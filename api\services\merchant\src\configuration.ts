import * as orm from '@midwayjs/typeorm';
import { Configuration, App } from '@midwayjs/decorator';
import * as koa from '@midwayjs/koa';
import * as validate from '@midwayjs/validate';
import { join } from 'path';
import * as crossDomain from '@midwayjs/cross-domain';
import * as cool from '@cool-midway/core';
import { IMidwayApplication } from '@midwayjs/core';
import * as rpc from '@cool-midway/rpc';

@Configuration({
  imports: [
    // Koa框架
    koa,
    // 跨域支持
    crossDomain,
    // ORM数据库
    orm,
    // 参数验证
    validate,
    // Cool-Admin核心
    cool,
    // RPC微服务支持
    rpc,
  ],
  importConfigs: [join(__dirname, './config')]
})
export class MerchantServiceConfiguration {
  @App()
  app!: IMidwayApplication;

  async onReady() {
    console.log('🚀 商户微服务启动成功');
    console.log('📡 服务端口: 9802');
    console.log('💾 独立数据库: merchant_service_db');
  }
} 