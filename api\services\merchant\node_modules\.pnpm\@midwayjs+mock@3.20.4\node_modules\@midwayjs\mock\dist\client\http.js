"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createHttpRequest = void 0;
const request = require("supertest");
function createHttpRequest(app) {
    if (app.callback2) {
        return request(app.callback2());
    }
    else if (app.callback) {
        return request(app.callback());
    }
    else {
        return request(app);
    }
}
exports.createHttpRequest = createHttpRequest;
//# sourceMappingURL=http.js.map