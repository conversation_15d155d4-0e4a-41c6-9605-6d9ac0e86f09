{"version": 3, "sources": ["../browser/src/driver/capacitor/CapacitorDriver.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,yCAAyC,CAAA;AAE9E,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAA;AAG7D,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAA;AAG5D,MAAM,OAAO,eAAgB,SAAQ,oBAAoB;IAIrD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAEjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAEjC,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;IACrC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAA;QACxD,OAAO,kBAAkB,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YACxC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;QACvC,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAA;QAExE,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,eAAe,CAAA;QACzD,MAAM,kBAAkB,GAAG,YAAY,KAAK,eAAe,CAAA;QAC3D,MAAM,eAAe,GACjB,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,WAAW;YACvC,CAAC,CAAC,CAAC;YACH,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAA;QAC9B,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,CACjD,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,kBAAkB,EAClB,YAAY,EACZ,eAAe,CAClB,CAAA;QACD,MAAM,UAAU,CAAC,IAAI,EAAE,CAAA;QAEvB,yFAAyF;QACzF,kEAAkE;QAClE,MAAM,UAAU,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAA;QAEpD,IACI,IAAI,CAAC,OAAO,CAAC,WAAW;YACxB,CAAC,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAC7D,IAAI,CAAC,OAAO,CAAC,WAAW,CAC3B,KAAK,CAAC,CAAC,EACV,CAAC;YACC,MAAM,UAAU,CAAC,OAAO,CACpB,yBAAyB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CACtD,CAAA;QACL,CAAC;QAED,OAAO,UAAU,CAAA;IACrB,CAAC;IAES,gBAAgB;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,8BAA8B,CACpC,WAAW,EACX,6BAA6B,CAChC,CAAA;QACL,CAAC;IACL,CAAC;CACJ", "file": "CapacitorDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { CapacitorConnectionOptions } from \"./CapacitorConnectionOptions\"\nimport { CapacitorQueryRunner } from \"./CapacitorQueryRunner\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { DriverPackageNotInstalledError } from \"../../error\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\n\nexport class CapacitorDriver extends AbstractSqliteDriver {\n    driver: any\n    options: CapacitorConnectionOptions\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        this.database = this.options.database\n        this.driver = this.options.driver\n\n        // load sqlite package\n        this.sqlite = this.options.driver\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        this.databaseConnection = this.createDatabaseConnection()\n        await this.databaseConnection\n    }\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        this.queryRunner = undefined\n        const databaseConnection = await this.databaseConnection\n        return databaseConnection.close().then(() => {\n            this.databaseConnection = undefined\n        })\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner) this.queryRunner = new CapacitorQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected async createDatabaseConnection() {\n        const databaseMode = this.options.mode || \"no-encryption\"\n        const isDatabaseEncryted = databaseMode !== \"no-encryption\"\n        const databaseVersion =\n            typeof this.options.version === \"undefined\"\n                ? 1\n                : this.options.version\n        const connection = await this.sqlite.createConnection(\n            this.options.database,\n            isDatabaseEncryted,\n            databaseMode,\n            databaseVersion,\n        )\n        await connection.open()\n\n        // we need to enable foreign keys in sqlite to make sure all foreign key related features\n        // working properly. this also makes onDelete to work with sqlite.\n        await connection.execute(`PRAGMA foreign_keys = ON`)\n\n        if (\n            this.options.journalMode &&\n            [\"DELETE\", \"TRUNCATE\", \"PERSIST\", \"MEMORY\", \"WAL\", \"OFF\"].indexOf(\n                this.options.journalMode,\n            ) !== -1\n        ) {\n            await connection.execute(\n                `PRAGMA journal_mode = ${this.options.journalMode}`,\n            )\n        }\n\n        return connection\n    }\n\n    protected loadDependencies(): void {\n        this.sqlite = this.driver\n        if (!this.driver) {\n            throw new DriverPackageNotInstalledError(\n                \"Capacitor\",\n                \"@capacitor-community/sqlite\",\n            )\n        }\n    }\n}\n"], "sourceRoot": "../.."}