{"version": 3, "sources": ["../browser/src/error/TransactionNotStartedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,YAAY;IACxD;QACI,KAAK,CACD,yFAAyF,CAC5F,CAAA;IACL,CAAC;CACJ", "file": "TransactionNotStartedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when transaction is not started yet and user tries to run commit or rollback.\n */\nexport class TransactionNotStartedError extends TypeORMError {\n    constructor() {\n        super(\n            `Transaction is not started yet, start transaction before committing or rolling it back.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}