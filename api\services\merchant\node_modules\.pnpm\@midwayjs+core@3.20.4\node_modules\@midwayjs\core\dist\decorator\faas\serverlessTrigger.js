"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerlessTrigger = exports.ServerlessFunction = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function ServerlessFunction(options) {
    return (target, key, descriptor) => {
        (0, __1.savePropertyMetadata)(__1.SERVERLESS_FUNC_KEY, options, target, key);
    };
}
exports.ServerlessFunction = ServerlessFunction;
function ServerlessTrigger(type, metadata = {}) {
    return (target, functionName, descriptor) => {
        var _a;
        if (type === interface_1.ServerlessTriggerType.HTTP ||
            type === interface_1.ServerlessTriggerType.API_GATEWAY) {
            metadata['method'] = (_a = metadata['method']) !== null && _a !== void 0 ? _a : 'get';
        }
        (0, __1.saveModule)(__1.FUNC_KEY, target.constructor);
        // new method decorator
        metadata = metadata || {};
        (0, __1.attachClassMetadata)(__1.FUNC_KEY, {
            type,
            methodName: functionName,
            metadata,
        }, target);
    };
}
exports.ServerlessTrigger = ServerlessTrigger;
//# sourceMappingURL=serverlessTrigger.js.map