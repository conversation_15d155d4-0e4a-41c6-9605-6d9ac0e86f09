{"name": "json-stringify-safe", "version": "5.0.1", "description": "Like JSON.stringify, but doesn't blow up on circular refs.", "keywords": ["json", "stringify", "circular", "safe"], "homepage": "https://github.com/isaacs/json-stringify-safe", "bugs": "https://github.com/isaacs/json-stringify-safe/issues", "author": "<PERSON> <<EMAIL>> (http://blog.izs.me)", "contributors": ["<PERSON><PERSON> <<EMAIL>> (http://themoll.com)"], "license": "ISC", "repository": {"type": "git", "url": "git://github.com/isaacs/json-stringify-safe"}, "main": "stringify.js", "scripts": {"test": "node test.js"}, "devDependencies": {"mocha": ">= 2.1.0 < 3", "must": ">= 0.12 < 0.13", "sinon": ">= 1.12.2 < 2"}}