{"version": 3, "sources": ["../../src/commands/MigrationGenerateCommand.ts"], "names": [], "mappings": ";;;;AAAA,uEAA6D;AAC7D,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AACzD,qDAA+C;AAC/C,iDAA6C;AAE7C;;GAEG;AACH,MAAa,wBAAwB;IAArC;QACI,YAAO,GAAG,2BAA2B,CAAA;QACrC,aAAQ,GACJ,gFAAgF,CAAA;IA+RxF,CAAC;IA7RG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,UAAU,CAAC,MAAM,EAAE;YAChB,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,4BAA4B;YACtC,YAAY,EAAE,IAAI;SACrB,CAAC;aACD,MAAM,CAAC,YAAY,EAAE;YAClB,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,QAAQ;YACd,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC;aACD,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,4BAA4B;SACzC,CAAC;aACD,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,UAAU;YACjB,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,+DAA+D;SACtE,CAAC;aACD,MAAM,CAAC,KAAK,EAAE;YACX,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,sDAAsD;SAC7D,CAAC;aACD,MAAM,CAAC,IAAI,EAAE;YACV,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,0EAA0E;SACjF,CAAC;aACD,MAAM,CAAC,IAAI,EAAE;YACV,KAAK,EAAE,OAAO;YACd,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EACJ,kHAAkH;SACzH,CAAC;aACD,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,WAAW;YAClB,IAAI,EAAE,QAAQ;YACd,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,yCAAyC;SACtD,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAA6C;QACvD,MAAM,SAAS,GAAG,2BAAY,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAA;QAC/C,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC,IAAI;YACX,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAC5C,MAAM,QAAQ,GAAG,SAAS,GAAG,GAAG,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAA;QAEtE,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,KAAK;aACjB,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,MAAM,GAAa,EAAE,EACvB,QAAQ,GAAa,EAAE,CAAA;YAE3B,IAAI,CAAC;gBACD,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,MAAM;qBACtC,mBAAmB,EAAE;qBACrB,GAAG,EAAE,CAAA;gBAEV,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;oBACd,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;wBACtC,OAAO,CAAC,KAAK,GAAG,wBAAwB,CAAC,aAAa,CAClD,OAAO,CAAC,KAAK,CAChB,CAAA;oBACL,CAAC,CAAC,CAAA;oBACF,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;wBAC1C,SAAS,CAAC,KAAK;4BACX,wBAAwB,CAAC,aAAa,CAClC,SAAS,CAAC,KAAK,CAClB,CAAA;oBACT,CAAC,CAAC,CAAA;gBACN,CAAC;gBAED,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACtC,MAAM,CAAC,IAAI,CACP,mCAAmC;wBAC/B,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;wBACpC,GAAG;wBACH,wBAAwB,CAAC,WAAW,CAChC,OAAO,CAAC,UAAU,CACrB;wBACD,IAAI,CACX,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,WAAW,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC1C,QAAQ,CAAC,IAAI,CACT,mCAAmC;wBAC/B,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC;wBACtC,GAAG;wBACH,wBAAwB,CAAC,WAAW,CAChC,SAAS,CAAC,UAAU,CACvB;wBACD,IAAI,CACX,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;oBAAS,CAAC;gBACP,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAC9B,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACjB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,0CAA0C,CACvD,CAAA;oBACD,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACnB,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,MAAM,CAAA,gJAAgJ,CAC9J,CAAA;oBACD,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACnB,CAAC;YACL,CAAC;iBAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACpB,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,MAAM,CAAA,iCAAiC,CAAC,CAAA;gBACzD,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACnB,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;gBAC7B,CAAC,CAAC,wBAAwB,CAAC,qBAAqB,CAC1C,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACvB,SAAS,EACT,MAAM,EACN,QAAQ,CAAC,OAAO,EAAE,EAClB,IAAI,CAAC,GAAG,CACX;gBACH,CAAC,CAAC,wBAAwB,CAAC,WAAW,CAChC,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACvB,SAAS,EACT,MAAM,EACN,QAAQ,CAAC,OAAO,EAAE,CACrB,CAAA;YAEP,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,MAAM,CAAA,sEAAsE,eAAI,CAAC,KAAK,CACvF,WAAW,CACd,EAAE,CACN,CAAA;gBACD,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CACN,aAAa,eAAI,CAAC,IAAI,CAClB,QAAQ,GAAG,SAAS,CACvB,oBAAoB,eAAI,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE,CACjD,CACJ,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,iBAAiB,GACnB,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,GAAG,GAAG,QAAQ,CAAA;gBAC3C,MAAM,2BAAY,CAAC,UAAU,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAA;gBAE7D,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,aAAa,eAAI,CAAC,IAAI,CAC5B,iBAAiB,CACpB,mCAAmC,CACvC,CAAA;gBACD,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;oBAC7B,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBACnB,CAAC;YACL,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAA;YAClE,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,2BAA2B;IAC3B,4EAA4E;IAE5E;;OAEG;IACO,MAAM,CAAC,WAAW,CAAC,UAA6B;QACtD,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACpC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAA;IAC5C,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,WAAW,CACxB,IAAY,EACZ,SAAiB,EACjB,MAAgB,EAChB,QAAkB;QAElB,MAAM,aAAa,GAAG,GAAG,IAAA,uBAAS,EAAC,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,CAAA;QAE5D,OAAO;;eAEA,aAAa;cACd,aAAa;;;EAGzB,MAAM,CAAC,IAAI,CAAC;CACb,CAAC;;;;EAIA,QAAQ,CAAC,IAAI,CAAC;CACf,CAAC;;;;CAID,CAAA;IACG,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,qBAAqB,CAClC,IAAY,EACZ,SAAiB,EACjB,MAAgB,EAChB,QAAkB,EAClB,GAAY;QAEZ,MAAM,aAAa,GAAG,GAAG,IAAA,uBAAS,EAAC,IAAI,EAAE,IAAI,CAAC,GAAG,SAAS,EAAE,CAAA;QAE5D,MAAM,YAAY,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,kBAAkB,CAAA;QAExD,OAAO;;;;;;;;EAQb,YAAY,UAAU,aAAa;cACvB,aAAa;;;EAGzB,MAAM,CAAC,IAAI,CAAC;CACb,CAAC;;;;EAIA,QAAQ,CAAC,IAAI,CAAC;CACf,CAAC;;;CAGD,CAAA;IACG,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,aAAa,CAAC,KAAa;QACxC,MAAM,cAAc,GAAG,IAAA,qBAAM,EAAC,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,CAAA;QACxD,OAAO,CACH,IAAI,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,CAAC,GAAG,YAAY,CACtE,CAAA;IACL,CAAC;CACJ;AAlSD,4DAkSC", "file": "MigrationGenerateCommand.js", "sourcesContent": ["import { format } from \"@sqltools/formatter/lib/sqlFormatter\"\nimport ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { camelCase } from \"../util/StringUtils\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Generates a new migration file with sql needs to be executed to update schema.\n */\nexport class MigrationGenerateCommand implements yargs.CommandModule {\n    command = \"migration:generate <path>\"\n    describe =\n        \"Generates a new migration file with sql needs to be executed to update schema.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .positional(\"path\", {\n                type: \"string\",\n                describe: \"Path of the migration file\",\n                demandOption: true,\n            })\n            .option(\"dataSource\", {\n                alias: \"d\",\n                type: \"string\",\n                describe:\n                    \"Path to the file where your DataSource instance is defined.\",\n                demandOption: true,\n            })\n            .option(\"p\", {\n                alias: \"pretty\",\n                type: \"boolean\",\n                default: false,\n                describe: \"Pretty-print generated SQL\",\n            })\n            .option(\"o\", {\n                alias: \"outputJs\",\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Generate a migration file on Javascript instead of Typescript\",\n            })\n            .option(\"esm\", {\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Generate a migration file on ESM instead of CommonJS\",\n            })\n            .option(\"dr\", {\n                alias: \"dryrun\",\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Prints out the contents of the migration instead of writing it to a file\",\n            })\n            .option(\"ch\", {\n                alias: \"check\",\n                type: \"boolean\",\n                default: false,\n                describe:\n                    \"Verifies that the current database is up to date and that no migrations are needed. Otherwise exits with code 1.\",\n            })\n            .option(\"t\", {\n                alias: \"timestamp\",\n                type: \"number\",\n                default: false,\n                describe: \"Custom timestamp for the migration name\",\n            })\n    }\n\n    async handler(args: yargs.Arguments<any & { path: string }>) {\n        const timestamp = CommandUtils.getTimestamp(args.timestamp)\n        const extension = args.outputJs ? \".js\" : \".ts\"\n        const fullPath = args.path.startsWith(\"/\")\n            ? args.path\n            : path.resolve(process.cwd(), args.path)\n        const filename = timestamp + \"-\" + path.basename(fullPath) + extension\n\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: false,\n            })\n            await dataSource.initialize()\n\n            const upSqls: string[] = [],\n                downSqls: string[] = []\n\n            try {\n                const sqlInMemory = await dataSource.driver\n                    .createSchemaBuilder()\n                    .log()\n\n                if (args.pretty) {\n                    sqlInMemory.upQueries.forEach((upQuery) => {\n                        upQuery.query = MigrationGenerateCommand.prettifyQuery(\n                            upQuery.query,\n                        )\n                    })\n                    sqlInMemory.downQueries.forEach((downQuery) => {\n                        downQuery.query =\n                            MigrationGenerateCommand.prettifyQuery(\n                                downQuery.query,\n                            )\n                    })\n                }\n\n                sqlInMemory.upQueries.forEach((upQuery) => {\n                    upSqls.push(\n                        \"        await queryRunner.query(`\" +\n                            upQuery.query.replaceAll(\"`\", \"\\\\`\") +\n                            \"`\" +\n                            MigrationGenerateCommand.queryParams(\n                                upQuery.parameters,\n                            ) +\n                            \");\",\n                    )\n                })\n                sqlInMemory.downQueries.forEach((downQuery) => {\n                    downSqls.push(\n                        \"        await queryRunner.query(`\" +\n                            downQuery.query.replaceAll(\"`\", \"\\\\`\") +\n                            \"`\" +\n                            MigrationGenerateCommand.queryParams(\n                                downQuery.parameters,\n                            ) +\n                            \");\",\n                    )\n                })\n            } finally {\n                await dataSource.destroy()\n            }\n\n            if (!upSqls.length) {\n                if (args.check) {\n                    console.log(\n                        ansi.green`No changes in database schema were found`,\n                    )\n                    process.exit(0)\n                } else {\n                    console.log(\n                        ansi.yellow`No changes in database schema were found - cannot generate a migration. To create a new empty migration use \"typeorm migration:create\" command`,\n                    )\n                    process.exit(1)\n                }\n            } else if (!args.path) {\n                console.log(ansi.yellow`Please specify a migration path`)\n                process.exit(1)\n            }\n\n            const fileContent = args.outputJs\n                ? MigrationGenerateCommand.getJavascriptTemplate(\n                      path.basename(fullPath),\n                      timestamp,\n                      upSqls,\n                      downSqls.reverse(),\n                      args.esm,\n                  )\n                : MigrationGenerateCommand.getTemplate(\n                      path.basename(fullPath),\n                      timestamp,\n                      upSqls,\n                      downSqls.reverse(),\n                  )\n\n            if (args.check) {\n                console.log(\n                    ansi.yellow`Unexpected changes in database schema were found in check mode:\\n\\n${ansi.white(\n                        fileContent,\n                    )}`,\n                )\n                process.exit(1)\n            }\n\n            if (args.dryrun) {\n                console.log(\n                    ansi.green(\n                        `Migration ${ansi.blue(\n                            fullPath + extension,\n                        )} has content:\\n\\n${ansi.white(fileContent)}`,\n                    ),\n                )\n            } else {\n                const migrationFileName =\n                    path.dirname(fullPath) + \"/\" + filename\n                await CommandUtils.createFile(migrationFileName, fileContent)\n\n                console.log(\n                    ansi.green`Migration ${ansi.blue(\n                        migrationFileName,\n                    )} has been generated successfully.`,\n                )\n                if (args.exitProcess !== false) {\n                    process.exit(0)\n                }\n            }\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during migration generation:\", err)\n            process.exit(1)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Formats query parameters for migration queries if parameters actually exist\n     */\n    protected static queryParams(parameters: any[] | undefined): string {\n        if (!parameters || !parameters.length) {\n            return \"\"\n        }\n\n        return `, ${JSON.stringify(parameters)}`\n    }\n\n    /**\n     * Gets contents of the migration file.\n     */\n    protected static getTemplate(\n        name: string,\n        timestamp: number,\n        upSqls: string[],\n        downSqls: string[],\n    ): string {\n        const migrationName = `${camelCase(name, true)}${timestamp}`\n\n        return `import { MigrationInterface, QueryRunner } from \"typeorm\";\n\nexport class ${migrationName} implements MigrationInterface {\n    name = '${migrationName}'\n\n    public async up(queryRunner: QueryRunner): Promise<void> {\n${upSqls.join(`\n`)}\n    }\n\n    public async down(queryRunner: QueryRunner): Promise<void> {\n${downSqls.join(`\n`)}\n    }\n\n}\n`\n    }\n\n    /**\n     * Gets contents of the migration file in Javascript.\n     */\n    protected static getJavascriptTemplate(\n        name: string,\n        timestamp: number,\n        upSqls: string[],\n        downSqls: string[],\n        esm: boolean,\n    ): string {\n        const migrationName = `${camelCase(name, true)}${timestamp}`\n\n        const exportMethod = esm ? \"export\" : \"module.exports =\"\n\n        return `/**\n * @typedef {import('typeorm').MigrationInterface} MigrationInterface\n */\n\n/**\n * @class\n * @implements {MigrationInterface}\n */\n${exportMethod} class ${migrationName} {\n    name = '${migrationName}'\n\n    async up(queryRunner) {\n${upSqls.join(`\n`)}\n    }\n\n    async down(queryRunner) {\n${downSqls.join(`\n`)}\n    }\n}\n`\n    }\n\n    /**\n     *\n     */\n    protected static prettifyQuery(query: string) {\n        const formattedQuery = format(query, { indent: \"    \" })\n        return (\n            \"\\n\" + formattedQuery.replace(/^/gm, \"            \") + \"\\n        \"\n        )\n    }\n}\n"], "sourceRoot": ".."}