#!/usr/bin/env node
export interface Logger {
    log: (...args: unknown[]) => void;
    error: (...args: unknown[]) => void;
    dir: (obj: unknown, options?: unknown) => void;
}
export interface Options {
    dryRun: boolean;
    mwtsRootDir: string;
    targetRootDir: string;
    yes: boolean;
    no: boolean;
    logger: Logger;
    yarn?: boolean;
}
export declare type VerbFilesFunction = (options: Options, files: string[], fix?: boolean) => Promise<boolean>;
/**
 * Get the current version of node.js being run.
 * Exported purely for stubbing purposes.
 * @private
 */
export declare function getNodeVersion(): string;
export declare function getEslintVersion(): string;
export declare function getPrettierVersion(): string;
export declare function run(verb: string, files: string[]): Promise<boolean>;
//# sourceMappingURL=cli.d.ts.map