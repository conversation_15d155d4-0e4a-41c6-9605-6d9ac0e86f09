{"version": 3, "sources": ["../../src/error/TypeORMError.ts"], "names": [], "mappings": ";;;AAAA,MAAa,YAAa,SAAQ,KAAK;IACnC,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;IAChC,CAAC;IAED,YAAY,OAAgB;QACxB,KAAK,CAAC,OAAO,CAAC,CAAA;QAEd,wDAAwD;QACxD,0CAA0C;QAC1C,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;QACrD,CAAC;aAAM,CAAC;YACJ,CAAC;YAAC,IAAY,CAAC,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAA;QACnD,CAAC;IACL,CAAC;CACJ;AAhBD,oCAgBC", "file": "TypeORMError.js", "sourcesContent": ["export class TypeORMError extends <PERSON>rror {\n    get name() {\n        return this.constructor.name\n    }\n\n    constructor(message?: string) {\n        super(message)\n\n        // restore prototype chain because the base `Error` type\n        // will break the prototype chain a little\n        if (Object.setPrototypeOf) {\n            Object.setPrototypeOf(this, new.target.prototype)\n        } else {\n            ;(this as any).__proto__ = new.target.prototype\n        }\n    }\n}\n"], "sourceRoot": ".."}