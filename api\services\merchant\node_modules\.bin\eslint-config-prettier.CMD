@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules\eslint-config-prettier\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules\eslint-config-prettier\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules\eslint-config-prettier\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules\eslint-config-prettier\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\eslint-config-prettier@8.10.0_eslint@7.32.0\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
)
