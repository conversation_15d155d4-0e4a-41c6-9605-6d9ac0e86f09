"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const session_1 = require("./middleware/session");
let SessionConfiguration = class SessionConfiguration {
    async onReady() {
        if (this.sessionConfig.enable) {
            this.applicationManager.getApplications(['koa', 'faas']).forEach(app => {
                if (app.on) {
                    // listen on session's events
                    app.on('session:missed', ({ ctx, key }) => {
                        this.logger.warn('[session][missed] key(%s)', key);
                    });
                    app.on('session:expired', ({ ctx, key, value }) => {
                        this.logger.warn('[session][expired] key(%s) value(%j)', key, this.sessionConfig.logValue ? value : '');
                    });
                    app.on('session:invalid', ({ ctx, key, value }) => {
                        this.logger.warn('[session][invalid] key(%s) value(%j)', key, this.sessionConfig.logValue ? value : '');
                    });
                }
                app.useMiddleware(session_1.SessionMiddleware);
            });
        }
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayApplicationManager)
], SessionConfiguration.prototype, "applicationManager", void 0);
__decorate([
    (0, core_1.Logger)('coreLogger'),
    __metadata("design:type", Object)
], SessionConfiguration.prototype, "logger", void 0);
__decorate([
    (0, core_1.Config)('session'),
    __metadata("design:type", Object)
], SessionConfiguration.prototype, "sessionConfig", void 0);
SessionConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'session',
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], SessionConfiguration);
exports.SessionConfiguration = SessionConfiguration;
//# sourceMappingURL=configuration.js.map