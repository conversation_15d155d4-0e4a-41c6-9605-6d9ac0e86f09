{"alternatives.all": "{{#label}} 不能匹配上所有可选项", "alternatives.any": "{{#label}} 不能匹配上任何可选项", "alternatives.match": "{{#label}} 不能匹配上任何可选项", "alternatives.one": "{{#label}} 匹配超过一个可选项", "alternatives.types": "{{#label}} 必须是 {{#types}}", "any.custom": "{{#label}} 自定义校验失败，原因为 {{#error.message}}", "any.default": "{{#label}} 运行默认函数时出错", "any.failover": "{{#label}} 运行 failover 函数时出错", "any.invalid": "{{#label}} 不是可选值", "any.only": "{{#label}} 必须是 {if(#valids.length == 1, \"\", \"one of \")}{{#valids}}", "any.ref": "{{#label}} {{#arg}} 引用 {{:#ref}} 其中 {{#reason}}", "any.required": "{{#label}} 是必须的", "any.unknown": "{{#label}} 不被允许", "array.base": "{{#label}} 必须是数组", "array.excludes": "{{#label}} 包含排除的值", "array.hasKnown": "{{#label}} 不包含至少一个类型为 {:#patternLabel} 的必选项", "array.hasUnknown": "{{#label}} 不包含至少一个必选项", "array.includes": "{{#label}} 不能匹配上任何可选项", "array.includesRequiredBoth": "{{#label}} 不包含 {{#knownMisses}} 及 {{#unknownMisses}} 个必选项", "array.includesRequiredKnowns": "{{#label}} 不包含 {{#knownMisses}} 等必选项", "array.includesRequiredUnknowns": "{{#label}} 不包含 {{#unknownMisses}} 个必选项", "array.length": "{{#label}} 必须正好包含 {{#limit}} 个成员", "array.max": "{{#label}} 必须最多包含 {{#limit}} 个成员", "array.min": "{{#label}} 必须至少包含 {{#limit}} 个成员", "array.orderedLength": "{{#label}} 必须至少包含 {{#limit}} 个成员", "array.sort": "{{#label}} 必须按 {#order} 顺序按 {{#by}} 排序", "array.sort.mismatching": "{{#label}} 由于类型不匹配而无法排序", "array.sort.unsupported": "{{#label}} 类型 {#type} 不支持排序", "array.sparse": "{{#label}} 不能包含未定义成员", "array.unique": "{{#label}} 是重复的成员", "binary.base": "{{#label}} 必须是一段内容", "binary.length": "{{#label}} 必须至少包含 {{#limit}} 个字节", "binary.max": "{{#label}} 必须最多包含 {{#limit}} 个字节", "binary.min": "{{#label}} 必须至少包含 {{#limit}} 个字节", "boolean.base": "{{#label}} 必须是 bool 值", "date.base": "{{#label}} 必须是毫秒数或者有效的时间字符串", "date.format": "{{#label}} 必须是以下格式的字符串 {msg(\"date.format.\" + #format) || #format}", "date.greater": "{{#label}} 必须晚于 {{:#limit}}", "date.less": "{{#label}} 必须早于 {{:#limit}}", "date.max": "{{#label}} 必须早于 {{:#limit}}", "date.min": "{{#label}} 必须晚于 {{:#limit}}", "date.format.iso": "必须是有效的\"ISO 8601\"格式的时间", "date.format.javascript": "必须是有效的时间戳或者毫秒数", "date.format.unix": "必须是有效的时间戳或者秒数", "function.arity": "{{#label}} 必须正好拥有 {{#n}} 个参数", "function.class": "{{#label}} 必须是对象类", "function.maxArity": "{{#label}} 必须最多拥有 {{#n}} 个参数", "function.minArity": "{{#label}} 必须至少拥有 {{#n}} 个参数", "object.and": "{{#label}} 当前 {{#presentWithLabels}} 需要有对应的 {{#missingWithLabels}}", "object.assert": "{{#label}} 无效，原因为 {if(#subject.key, `\"` + #subject.key + `\" failed to ` + (#message || \"pass the assertion test\"), #message || \"the assertion failed\")}", "object.base": "{{#label}} 必须是一个对象 {{#type}}", "object.instance": "{{#label}} 必须是一个 {{:#type}} 的实例", "object.length": "{{#label}} 必须正好包含 {{#limit}} 个子对象", "object.max": "{{#label}} 必须最多包含 {{#limit}} 个子对象", "object.min": "{{#label}} 必须至少包含 {{#limit}} 个子对象", "object.missing": "{{#label}} 没有对应的 {{#peersWithLabels}}", "object.nand": "{{:#mainWithLabel}} 不得与 {{#peersWithLabels}} 同时存在", "object.oxor": "{{#label}} 和 {{#peerWithLabels}} 有冲突", "object.pattern.match": "{{#label}} 键匹配失败", "object.refType": "{{#label}} 必须是 Joi 引用", "object.regex": "{{#label}} 必须是正则对象", "object.rename.multiple": "{{#label}} 无法重命名 {{:#from}} 因为多次重命名被禁用并且另一个键已经重命名为 {{:#to}}", "object.rename.override": "{{#label}} 无法重命名 {{:#from}} 因为覆盖被禁用并且目标 {{:#to}} 存在", "object.schema": "{{#label}} 必须是 {{#type}} 类型的 <PERSON><PERSON>a", "object.unknown": "{{#label}} 不被允许", "object.with": "{{:#mainWithLabel}} 缺少必需的对等方 {{:#peerWithLabel}}", "object.without": "{{:#mainWithLabel}} 与禁止的节点冲突 {{:#peerWithLabel}}", "object.xor": "{{#label}} 包含独占对等体之间的冲突 {{#peersWithLabels}}", "number.base": "{{#label}} 必须是数值", "number.greater": "{{#label}} 必须大于 {{#limit}}", "number.infinity": "{{#label}} 不能是无穷大", "number.integer": "{{#label}} 必须是整数", "number.less": "{{#label}} 必须小于 {{#limit}}", "number.max": "{{#label}} 必须小于或等于 {{#limit}}", "number.min": "{{#label}} 必须大于或等于 {{#limit}}", "number.multiple": "{{#label}} 必须是 {{#multiple}} 的倍数", "number.negative": "{{#label}} 必须是负数", "number.port": "{{#label}} 必须是有效的端口号", "number.positive": "{{#label}} 必须是正数", "number.precision": "{{#label}} 必须有 {{#limit}} 位小数", "number.unsafe": "{{#label}} 必须是安全号码", "string.alphanum": "{{#label}} must only contain alpha-numeric characters", "string.base": "{{#label}} 必须是字符串", "string.base64": "{{#label}} 必须是有效的 base64 字符串", "string.creditCard": "{{#label}} 必须是有效的信用卡号", "string.dataUri": "{{#label}} 必须是有效的 dataUri 字符串", "string.domain": "{{#label}} 必须包含有效的域名", "string.email": "{{#label}} 必须是有效的电子邮件", "string.empty": "{{#label}} 不允许为空", "string.guid": "{{#label}} 必须是有效的 GUID", "string.hex": "{{#label}} 只能包含十六进制字符", "string.hexAlign": "{{#label}} 十六进制解码表示必须字节对齐", "string.hostname": "{{#label}} 必须是有效的主机名", "string.ip": "{{#label}} 必须是具有 {{#cidr}} CIDR 的有效 IP 地址", "string.ipVersion": "{{#label}} 必须是以下版本之一的有效 IP 地址 {{#version}} 和 {{#cidr}} CIDR", "string.isoDate": "{{#label}} 必须是 iso 格式", "string.isoDuration": "{{#label}} 必须是有效的 ISO 8601 持续时间", "string.length": "{{#label}} 长度必须为 {{#limit}} 个字符长", "string.lowercase": "{{#label}} 只能包含小写字符", "string.max": "{{#label}} 长度必须小于或等于 {{#limit}} 个字符长", "string.min": "{{#label}} 长度必须至少为 {{#limit}} 个字符长", "string.normalize": "{{#label}} 必须以 {{#form}} 形式进行 unicode 规范化", "string.token": "{{#label}} 只能包含字母数字和下划线字符", "string.pattern.base": "{{#label}} 的值为 {:[.]} 未能匹配所需的模式：{{#regex}}", "string.pattern.name": "{{#label}} 值为 {:[.]} 未能匹配 {{#name}} 模式", "string.pattern.invert.base": "{{#label}} 与值 {:[.]} 匹配反转模式：{{#regex}}", "string.pattern.invert.name": "{{#label}} 与值 {:[.]} 匹配倒置的 {{#name}} 模式", "string.trim": "{{#label}} 不得前后空格", "string.uri": "{{#label}} 必须是有效的 uri", "string.uriCustomScheme": "{{#label}} 必须是有效的 uri，其方案与 {{#scheme}} 模式匹配", "string.uriRelativeOnly": "{{#label}} 必须是有效的相对 uri", "string.uppercase": "{{#label}} 只能包含大写字符", "symbol.base": "{{#label}} 必须是符号", "symbol.map": "{{#label}} 必须是 {{#map}} 之一"}