{"version": 3, "sources": ["../../src/error/OffsetWithoutLimitNotSupportedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,mCAAoC,SAAQ,2BAAY;IACjE;QACI,KAAK,CACD,0FAA0F;YACtF,yFAAyF;YACzF,oBAAoB,CAC3B,CAAA;IACL,CAAC;CACJ;AARD,kFAQC", "file": "OffsetWithoutLimitNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build SELECT query using OFFSET without LIMIT applied but database does not support it.\n */\nexport class OffsetWithoutLimitNotSupportedError extends TypeORMError {\n    constructor() {\n        super(\n            `RDBMS does not support OFFSET without LIMIT in SELECT statements. You must use limit in ` +\n                `conjunction with offset function (or take in conjunction with skip function if you are ` +\n                `using pagination).`,\n        )\n    }\n}\n"], "sourceRoot": ".."}