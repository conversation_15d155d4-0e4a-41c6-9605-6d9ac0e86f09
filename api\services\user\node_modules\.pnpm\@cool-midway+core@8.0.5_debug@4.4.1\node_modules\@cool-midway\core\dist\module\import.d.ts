import { ILogger, IMidwayApplication } from '@midwayjs/core';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
import { DataSource } from 'typeorm';
import { CoolEventManager } from '../event';
import { CoolModuleConfig } from './config';
import { CoolModuleMenu } from './menu';
/**
 * 模块sql
 */
export declare class CoolModuleImport {
    ormConfig: any;
    defaultDataSource: DataSource;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    coolConfig: any;
    coreLogger: ILogger;
    coolModuleConfig: CoolModuleConfig;
    coolEventManager: CoolEventManager;
    app: IMidwayApplication;
    coolModuleMenu: CoolModuleMenu;
    initJudge: 'file' | 'db';
    /**
     * 初始化
     */
    init(): Promise<void>;
    /**
     * 获取数据库元数据
     */
    getDbMetadatas(): Promise<any>;
    /**
     * 检查数据是否存在
     * @param module
     * @param metadatas
     */
    checkDbExist(module: string, metadatas: any): Promise<boolean>;
    /**
     * 检查文件是否存在
     * @param module
     */
    checkFileExist(module: string): {
        exist: boolean;
        lockPath: string;
    };
    /**
     * 导入数据库
     * @param module
     * @param lockPath 锁定导入
     */
    initDataBase(module: string, metadatas: any, lockPath?: string): Promise<void>;
    /**
     * 锁定导入
     * @param module
     * @param metadatas
     * @param lockPath
     * @param time
     */
    lockImportData(module: string, metadatas: any, lockPath: string, time: number): Promise<void>;
    /**
     * 导入数据
     * @param metadatas
     * @param datas
     * @param tableName
     */
    importData(metadatas: any[], item: any, tableName: string, parentItem?: any): Promise<void>;
}
