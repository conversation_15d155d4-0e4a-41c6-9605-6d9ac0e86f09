<template>
  <div class="risk-control-center">

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <ArtLineChartCard
            :isMiniChart="true"
            :value="riskScore"
            label="风险评分"
            date="实时更新"
            :percentage="-2"
            :height="9.5"
            :chartData="[85, 82, 88, 85, 87, 84, riskScore]"
            :showAreaColor="true"
            :color="getRiskScoreColor()"
          />
        </el-col>
        <el-col :span="6">
          <ArtBarChartCard
            :isMiniChart="true"
            :value="getThreatLevelValue()"
            label="威胁等级"
            date="过去7天"
            :percentage="-2"
            :height="9.5"
            :chartData="[3, 2, 3, 2, 2, 1, getThreatLevelValue()]"
            :color="getThreatLevelColor()"
          />
        </el-col>
        <el-col :span="6">
          <ArtLineChartCard
            :isMiniChart="true"
            :value="98.5"
            label="合规率 (%)"
            date="过去30天"
            :percentage="0.3"
            :height="9.5"
            :chartData="[96.2, 97.1, 97.8, 98.1, 98.3, 98.2, 98.5]"
            :showAreaColor="true"
            color="#67c23a"
          />
        </el-col>
        <el-col :span="6">
          <ArtBarChartCard
            :isMiniChart="true"
            :value="23"
            label="待处理事项"
            date="今日"
            :percentage="-18"
            :height="9.5"
            :chartData="[35, 28, 32, 25, 30, 28, 23]"
            color="#e6a23c"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 智能配置面板 -->
    <div class="config-panels">
      <el-row :gutter="24">
        <!-- 风险控制面板 -->
        <el-col :span="14">
          <div class="control-panel">
            <div class="panel-header">
              <div class="panel-title">
                <h3>风险控制</h3>
                <p>设置风险等级和限额管理</p>
              </div>
              <div class="panel-status">
                <el-tag :type="getRiskTagType()" size="small">{{ getRiskStatusText() }}</el-tag>
              </div>
            </div>

            <div class="panel-content">
              <!-- 风险等级滑块 -->
              <div class="risk-level-control">
                <div class="control-label">
                  <span>风险防护等级</span>
                  <el-tag :type="getRiskTagType()" size="small">{{ currentRiskLevel }}</el-tag>
                </div>
                <div class="level-slider">
                  <div class="slider-track" @click="handleTrackClick">
                    <div class="slider-fill" :style="{ width: getRiskLevelPercentage() + '%' }"></div>
                    <div class="slider-thumb"
                         :style="{ left: getRiskLevelPercentage() + '%' }"
                         @mousedown="startDrag"
                    ></div>
                  </div>
                  <div class="level-labels">
                    <span @click="setRiskLevel('low')" :class="{ active: riskSettings.riskLevel === 'low' }">低风险</span>
                    <span @click="setRiskLevel('medium')" :class="{ active: riskSettings.riskLevel === 'medium' }">中风险</span>
                    <span @click="setRiskLevel('high')" :class="{ active: riskSettings.riskLevel === 'high' }">高风险</span>
                  </div>
                </div>
              </div>

              <!-- 限额控制器 -->
              <div class="limit-controllers">
                <div class="limit-item">
                  <div class="limit-header">
                    <span class="limit-title">每日交易限额</span>
                    <span class="limit-value">{{ formatCurrency(riskSettings.dailyLimit) }}</span>
                  </div>
                  <div class="limit-visual">
                    <div class="limit-bar">
                      <div class="used-amount" :style="{ width: getDailyUsagePercentage() + '%' }"></div>
                      <div class="usage-indicator">
                        <span class="used">已用: {{ formatCurrency(156000) }}</span>
                        <span class="remaining">剩余: {{ formatCurrency(riskSettings.dailyLimit - 156000) }}</span>
                      </div>
                    </div>
                    <el-input-number
                      v-model="riskSettings.dailyLimit"
                      :min="0"
                      :max="10000000"
                      :step="10000"
                      class="limit-input"
                      size="small"
                    />
                  </div>
                </div>

                <div class="limit-item">
                  <div class="limit-header">
                    <span class="limit-title">每月交易限额</span>
                    <span class="limit-value">{{ formatCurrency(riskSettings.monthlyLimit) }}</span>
                  </div>
                  <div class="limit-visual">
                    <div class="limit-bar">
                      <div class="used-amount" :style="{ width: getMonthlyUsagePercentage() + '%' }"></div>
                      <div class="usage-indicator">
                        <span class="used">已用: {{ formatCurrency(2300000) }}</span>
                        <span class="remaining">剩余: {{ formatCurrency(riskSettings.monthlyLimit - 2300000) }}</span>
                      </div>
                    </div>
                    <el-input-number
                      v-model="riskSettings.monthlyLimit"
                      :min="0"
                      :max="100000000"
                      :step="100000"
                      class="limit-input"
                      size="small"
                    />
                  </div>
                </div>
              </div>

              <!-- 功能开关 -->
              <div class="feature-switches">
                <div class="switch-item">
                  <div class="switch-content">
                    <div class="switch-title">反洗钱监测</div>
                    <div class="switch-desc">自动检测可疑交易行为</div>
                  </div>
                  <el-switch v-model="riskSettings.enableAML" />
                </div>

                <div class="switch-item">
                  <div class="switch-content">
                    <div class="switch-title">资金托管</div>
                    <div class="switch-desc">启用第三方资金托管服务</div>
                  </div>
                  <el-switch v-model="riskSettings.enableEscrow" />
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 合规监控面板 -->
        <el-col :span="10">
          <div class="control-panel">
            <div class="panel-header">
              <div class="panel-title">
                <h3>合规要求</h3>
                <p>配置KYC认证和监管报告</p>
              </div>
              <div class="compliance-score">
                <span class="score-text">98.5%</span>
              </div>
            </div>

            <div class="panel-content">
              <!-- 合规检查清单 -->
              <div class="compliance-checklist">
                <div class="checklist-header">
                  <span>合规检查项目</span>
                  <span class="check-status">8/9 通过</span>
                </div>

                <div class="check-items">
                  <div class="check-item completed">
                    <div class="check-icon">✅</div>
                    <div class="check-content">
                      <div class="check-title">KYC身份认证</div>
                      <div class="check-desc">{{ getKycStatusText() }}</div>
                    </div>
                  </div>

                  <div class="check-item completed">
                    <div class="check-icon">✅</div>
                    <div class="check-content">
                      <div class="check-title">反洗钱检查</div>
                      <div class="check-desc">AML规则已启用</div>
                    </div>
                  </div>

                  <div class="check-item completed">
                    <div class="check-icon">✅</div>
                    <div class="check-content">
                      <div class="check-title">数据保护</div>
                      <div class="check-desc">GDPR合规</div>
                    </div>
                  </div>

                  <div class="check-item pending">
                    <div class="check-icon">⏳</div>
                    <div class="check-content">
                      <div class="check-title">税务申报</div>
                      <div class="check-desc">待完善税务信息</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 快速配置 -->
              <div class="quick-config">
                <div class="config-group">
                  <label class="config-label">KYC认证等级</label>
                  <el-select v-model="complianceSettings.kycLevel" class="config-select">
                    <el-option label="基础认证" value="basic" />
                    <el-option label="标准认证" value="standard" />
                    <el-option label="高级认证" value="advanced" />
                  </el-select>
                </div>

                <div class="config-group">
                  <label class="config-label">监管报告</label>
                  <el-select v-model="complianceSettings.reportingLevel" class="config-select">
                    <el-option label="标准报告" value="standard" />
                    <el-option label="详细报告" value="detailed" />
                    <el-option label="实时报告" value="realtime" />
                  </el-select>
                </div>

                <div class="config-toggles">
                  <div class="toggle-item">
                    <span class="toggle-label">数据保护</span>
                    <el-switch v-model="complianceSettings.dataProtection" />
                  </div>

                  <div class="toggle-item">
                    <span class="toggle-label">审计日志</span>
                    <el-switch v-model="complianceSettings.auditLog" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 实时警报 -->
    <div class="alert-stream">
      <div class="stream-header">
        <div class="stream-title">实时风险警报</div>
        <div class="stream-controls">
          <el-button size="small" @click="refreshAlerts">刷新</el-button>
          <el-button size="small" @click="clearAlerts">清空</el-button>
        </div>
      </div>

      <div class="alert-list">
        <div v-for="alert in recentAlerts" :key="alert.id" :class="['alert-item', alert.level]">
          <div class="alert-time">{{ alert.time }}</div>
          <div class="alert-content">
            <div class="alert-title">{{ alert.title }}</div>
            <div class="alert-desc">{{ alert.description }}</div>
          </div>
          <div class="alert-action">
            <el-button size="small" type="text">处理</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { Check, Close, Warning } from '@element-plus/icons-vue'
import { useSettlementStore } from '@/stores/settlement'
import ArtLineChartCard from '@/components/core/cards/art-line-chart-card/index.vue'
import ArtBarChartCard from '@/components/core/cards/art-bar-chart-card/index.vue'

// 使用共享的结算配置状态
const settlementStore = useSettlementStore()
const { riskSettings, complianceSettings } = settlementStore

const lastUpdateTime = ref('2025-01-21 14:30:25')

// 风险评分计算
const riskScore = computed(() => {
  let score = 85 // 基础分数
  if (riskSettings.riskLevel === 'high') score -= 20
  if (riskSettings.riskLevel === 'medium') score -= 10
  if (!riskSettings.enableAML) score -= 15
  if (!riskSettings.enableEscrow) score -= 10
  return Math.max(score, 0)
})

// 当前风险等级显示
const currentRiskLevel = computed(() => {
  const levels = {
    low: '低风险',
    medium: '中风险',
    high: '高风险'
  }
  return levels[riskSettings.riskLevel] || '未知'
})

// KYC等级配置
const kycLevels = [
  { value: 'basic', label: '基础', icon: '🆔' },
  { value: 'standard', label: '标准', icon: '📄' },
  { value: 'advanced', label: '高级', icon: '🏆' }
]

// 实时警报数据
const recentAlerts = ref([
  {
    id: 1,
    time: '14:25',
    title: '异常交易检测',
    description: '检测到单笔交易金额超过日常平均值300%',
    level: 'warning'
  },
  {
    id: 2,
    time: '14:20',
    title: '合规检查通过',
    description: '商户 M001234 完成KYC高级认证',
    level: 'success'
  },
  {
    id: 3,
    time: '14:15',
    title: '风险等级调整',
    description: '系统自动将商户 M005678 调整为中风险等级',
    level: 'info'
  }
])



// 获取风险评分等级类
const getRiskScoreClass = () => {
  if (riskScore.value >= 80) return 'excellent'
  if (riskScore.value >= 60) return 'good'
  if (riskScore.value >= 40) return 'warning'
  return 'danger'
}

// 获取趋势类
const getTrendClass = (value: number) => {
  return value > 0 ? 'trend-up' : 'trend-down'
}

// 获取风险状态类
const getRiskStatusClass = () => {
  const classes = {
    low: 'status-safe',
    medium: 'status-warning',
    high: 'status-danger'
  }
  return classes[riskSettings.riskLevel] || 'status-unknown'
}

// 获取风险状态文本
const getRiskStatusText = () => {
  const texts = {
    low: '安全运行',
    medium: '需要关注',
    high: '高度警戒'
  }
  return texts[riskSettings.riskLevel] || '状态未知'
}

// 获取风险标签类型
const getRiskTagType = () => {
  const types = {
    low: 'success',
    medium: 'warning',
    high: 'danger'
  }
  return types[riskSettings.riskLevel] || 'info'
}

// 获取风险评分颜色
const getRiskScoreColor = () => {
  if (riskScore.value >= 80) return '#67c23a'
  if (riskScore.value >= 60) return '#409eff'
  if (riskScore.value >= 40) return '#e6a23c'
  return '#f56c6c'
}

// 获取威胁等级数值
const getThreatLevelValue = () => {
  const levels = { low: 1, medium: 2, high: 3 }
  return levels[riskSettings.riskLevel] || 1
}

// 获取威胁等级颜色
const getThreatLevelColor = () => {
  const colors = {
    low: '#67c23a',
    medium: '#e6a23c',
    high: '#f56c6c'
  }
  return colors[riskSettings.riskLevel] || '#67c23a'
}

// 获取风险等级百分比
const getRiskLevelPercentage = () => {
  const levels = { low: 25, medium: 60, high: 90 }
  return levels[riskSettings.riskLevel] || 0
}

// 设置风险等级
const setRiskLevel = (level: string) => {
  riskSettings.riskLevel = level
}

// 点击轨道跳转
const handleTrackClick = (e: MouseEvent) => {
  if ((e.target as HTMLElement).classList.contains('slider-thumb')) {
    return // 如果点击的是滑块本身，不处理
  }

  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  const percentage = ((e.clientX - rect.left) / rect.width) * 100

  // 根据百分比设置风险等级
  if (percentage <= 33) {
    setRiskLevel('low')
  } else if (percentage <= 66) {
    setRiskLevel('medium')
  } else {
    setRiskLevel('high')
  }
}

// 拖拽相关
const isDragging = ref(false)

const startDrag = (e: MouseEvent) => {
  isDragging.value = true
  e.preventDefault()

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!isDragging.value) return

    const sliderTrack = (e.target as HTMLElement).parentElement
    if (!sliderTrack) return

    const rect = sliderTrack.getBoundingClientRect()
    const percentage = Math.max(0, Math.min(100, ((moveEvent.clientX - rect.left) / rect.width) * 100))

    // 根据百分比设置风险等级
    if (percentage <= 33) {
      setRiskLevel('low')
    } else if (percentage <= 66) {
      setRiskLevel('medium')
    } else {
      setRiskLevel('high')
    }
  }

  const handleMouseUp = () => {
    isDragging.value = false
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

// 计算每日使用百分比
const getDailyUsagePercentage = () => {
  const used = 156000
  return Math.min((used / riskSettings.dailyLimit) * 100, 100)
}

// 计算每月使用百分比
const getMonthlyUsagePercentage = () => {
  const used = 2300000
  return Math.min((used / riskSettings.monthlyLimit) * 100, 100)
}

// 格式化货币
const formatCurrency = (amount: number) => {
  return `¥${amount.toLocaleString()}`
}

// 获取KYC状态文本
const getKycStatusText = () => {
  const texts = {
    basic: '基础认证已完成',
    standard: '标准认证已完成',
    advanced: '高级认证已完成'
  }
  return texts[complianceSettings.kycLevel] || '未认证'
}

// 自动保存功能
const autoSave = async () => {
  try {
    await settlementStore.saveSettings()
    lastUpdateTime.value = new Date().toLocaleString()
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

// 监听配置变化，自动保存
watch(() => riskSettings.riskLevel, autoSave)
watch(() => riskSettings.dailyLimit, autoSave)
watch(() => riskSettings.monthlyLimit, autoSave)
watch(() => riskSettings.enableAML, autoSave)
watch(() => riskSettings.enableEscrow, autoSave)
watch(() => complianceSettings.kycLevel, autoSave)
watch(() => complianceSettings.reportingLevel, autoSave)
watch(() => complianceSettings.dataProtection, autoSave)
watch(() => complianceSettings.auditLog, autoSave)



// 刷新警报
const refreshAlerts = () => {
  lastUpdateTime.value = new Date().toLocaleString()
}

// 清空警报
const clearAlerts = () => {
  recentAlerts.value = []
}

// 页面加载时获取配置
onMounted(async () => {
  await settlementStore.loadSettings()
})
</script>

<style scoped lang="scss">
.risk-control-center {
  padding-bottom: 20px;
}



// 核心指标概览
.metrics-overview {
  margin-bottom: 24px;
}

// 配置面板
.config-panels {
  margin-bottom: 24px;

  .control-panel {
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 4px);

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 20px 20px 16px 20px;
      border-bottom: 1px solid var(--art-border-color);

      .panel-title {
        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: var(--art-text-gray-900);
        }

        p {
          margin: 0;
          font-size: 13px;
          color: var(--art-text-gray-600);
        }
      }

      .panel-status {
        display: flex;
        align-items: center;
      }

      .compliance-score {
        .score-text {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-color-success);
        }
      }
    }

    .panel-content {
      padding: 20px;
    }
  }
}

// 风险等级控制器
.risk-level-control {
  margin-bottom: 24px;

  .control-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    span {
      font-size: 14px;
      font-weight: 500;
      color: var(--art-text-gray-900);
    }
  }

  .level-slider {
    .slider-track {
      position: relative;
      height: 6px;
      background: var(--art-border-color);
      border-radius: 3px;
      margin-bottom: 12px;
      cursor: pointer;

      &:hover {
        background: var(--art-bg-gray-200);
      }

      .slider-fill {
        height: 100%;
        background: linear-gradient(90deg, #67c23a, #e6a23c, #f56c6c);
        border-radius: 3px;
        transition: width 0.2s ease;
      }

      .slider-thumb {
        position: absolute;
        top: -5px;
        width: 16px;
        height: 16px;
        background: var(--art-main-bg-color);
        border: 2px solid var(--el-color-primary);
        border-radius: 50%;
        cursor: grab;
        transition: all 0.2s ease;
        transform: translateX(-50%);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateX(-50%) scale(1.1);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        &:active {
          cursor: grabbing;
          transform: translateX(-50%) scale(1.15);
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
        }
      }
    }

    .level-labels {
      display: flex;
      justify-content: space-between;

      span {
        font-size: 12px;
        color: var(--art-text-gray-600);
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: var(--art-bg-gray-100);
          color: var(--art-text-gray-900);
        }

        &.active {
          background: var(--el-color-primary);
          color: white;
          font-weight: 500;
        }
      }
    }
  }
}

// 限额控制器
.limit-controllers {
  margin-bottom: 24px;

  .limit-item {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--art-bg-gray-50);
    border-radius: calc(var(--custom-radius));
    border: 1px solid var(--art-border-color);

    .limit-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .limit-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--art-text-gray-900);
      }

      .limit-value {
        font-size: 14px;
        font-weight: 600;
        color: var(--el-color-primary);
      }
    }

    .limit-visual {
      .limit-bar {
        position: relative;
        height: 8px;
        background: var(--art-border-color);
        border-radius: 4px;
        margin-bottom: 8px;
        overflow: hidden;

        .used-amount {
          height: 100%;
          background: var(--el-color-primary);
          border-radius: 4px;
          transition: width 0.2s ease;
        }

        .usage-indicator {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          display: flex;
          justify-content: space-between;
          margin-top: 4px;

          span {
            font-size: 11px;
            color: var(--art-text-gray-500);
          }
        }
      }

      .limit-input {
        margin-top: 12px;

        :deep(.el-input-number) {
          width: 100%;
        }
      }
    }
  }
}

// 功能开关
.feature-switches {
  .switch-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid var(--art-border-color);

    &:last-child {
      border-bottom: none;
    }

    .switch-content {
      flex: 1;

      .switch-title {
        font-size: 14px;
        font-weight: 500;
        color: var(--art-text-gray-900);
        margin-bottom: 4px;
      }

      .switch-desc {
        font-size: 12px;
        color: var(--art-text-gray-600);
      }
    }
  }
}

// 合规检查清单
.compliance-checklist {
  margin-bottom: 20px;

  .checklist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    span:first-child {
      font-size: 14px;
      font-weight: 500;
      color: var(--art-text-gray-900);
    }

    .check-status {
      font-size: 12px;
      color: var(--el-color-primary);
      font-weight: 500;
    }
  }

  .check-items {
    .check-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid var(--art-border-color);

      &:last-child {
        border-bottom: none;
      }

      .check-icon {
        margin-right: 10px;
        font-size: 14px;
      }

      .check-content {
        flex: 1;

        .check-title {
          font-size: 13px;
          font-weight: 500;
          color: var(--art-text-gray-900);
          margin-bottom: 2px;
        }

        .check-desc {
          font-size: 11px;
          color: var(--art-text-gray-600);
        }
      }

      &.completed {
        opacity: 0.7;
      }

      &.pending {
        .check-title {
          color: var(--el-color-warning);
        }
      }
    }
  }
}

// 快速配置
.quick-config {
  .config-group {
    margin-bottom: 20px;

    .config-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      color: var(--art-text-gray-900);
      margin-bottom: 8px;
    }

    .config-select {
      width: 100%;
    }
  }

  .config-toggles {
    .toggle-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid var(--art-border-color);

      &:last-child {
        border-bottom: none;
      }

      .toggle-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--art-text-gray-900);
      }
    }
  }
}

// 实时警报
.alert-stream {
  background: var(--art-main-bg-color);
  border: 1px solid var(--art-border-color);
  border-radius: calc(var(--custom-radius) + 4px);

  .stream-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 16px 20px;
    border-bottom: 1px solid var(--art-border-color);

    .stream-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--art-text-gray-900);
    }

    .stream-controls {
      display: flex;
      gap: 8px;
    }
  }

  .alert-list {
    max-height: 300px;
    overflow-y: auto;

    .alert-item {
      display: flex;
      align-items: flex-start;
      padding: 16px 20px;
      border-bottom: 1px solid var(--art-border-color);

      &:hover {
        background: var(--art-bg-gray-50);
      }

      &:last-child {
        border-bottom: none;
      }

      .alert-time {
        font-size: 12px;
        color: var(--art-text-gray-500);
        margin-right: 16px;
        min-width: 40px;
        margin-top: 2px;
      }

      .alert-content {
        flex: 1;

        .alert-title {
          font-size: 14px;
          font-weight: 500;
          color: var(--art-text-gray-900);
          margin-bottom: 4px;
        }

        .alert-desc {
          font-size: 12px;
          color: var(--art-text-gray-600);
          line-height: 1.4;
        }
      }

      .alert-action {
        margin-left: 16px;
        margin-top: 2px;
      }

      &.warning {
        border-left: 3px solid var(--el-color-warning);
      }

      &.success {
        border-left: 3px solid var(--el-color-success);
      }

      &.info {
        border-left: 3px solid var(--el-color-info);
      }
    }
  }
}
</style>
