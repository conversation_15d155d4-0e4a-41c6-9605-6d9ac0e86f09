{"version": 3, "sources": ["../browser/src/error/OffsetWithoutLimitNotSupportedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,mCAAoC,SAAQ,YAAY;IACjE;QACI,KAAK,CACD,0FAA0F;YACtF,yFAAyF;YACzF,oBAAoB,CAC3B,CAAA;IACL,CAAC;CACJ", "file": "OffsetWithoutLimitNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build SELECT query using OFFSET without LIMIT applied but database does not support it.\n */\nexport class OffsetWithoutLimitNotSupportedError extends TypeORMError {\n    constructor() {\n        super(\n            `RDBMS does not support OFFSET without LIMIT in SELECT statements. You must use limit in ` +\n                `conjunction with offset function (or take in conjunction with skip function if you are ` +\n                `using pagination).`,\n        )\n    }\n}\n"], "sourceRoot": ".."}