{"version": 3, "sources": ["../browser/src/error/PrimaryColumnCannotBeNullableError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,kCAAmC,SAAQ,YAAY;IAChE,YAAY,MAAc,EAAE,YAAoB;QAC5C,KAAK,CACD,kBACU,MAAM,CAAC,WAAY,CAAC,IAC9B,IAAI,YAAY,uBAAuB;YACnC,kEAAkE,CACzE,CAAA;IACL,CAAC;CACJ", "file": "PrimaryColumnCannotBeNullableError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class PrimaryColumnCannotBeNullableError extends TypeORMError {\n    constructor(object: Object, propertyName: string) {\n        super(\n            `Primary column ${\n                (<any>object.constructor).name\n            }#${propertyName} cannot be nullable. ` +\n                `Its not allowed for primary keys. Try to remove nullable option.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}