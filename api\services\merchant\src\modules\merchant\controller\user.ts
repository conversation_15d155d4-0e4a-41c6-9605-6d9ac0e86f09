import { BaseController, CoolCommException } from '@cool-midway/core';
import { Body, Controller, Inject, Post } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MerchantSysUserEntity } from '../entity/sys/user';

/**
 * 商户系统用户
 */
@Controller('/merchant/sys/user')
export class MerchantSysUserController extends BaseController {
  @InjectEntityModel(MerchantSysUserEntity)
  userEntity: Repository<MerchantSysUserEntity>;

  @Inject()
  ctx: Context;

  /**
   * 分页查询
   */
  @Post('/page')
  async page(@Body() body) {
    const { keyWord, status } = body;
    const query = this.userEntity
      .createQueryBuilder('a')
      .where('1 = 1');

    // 关键字搜索
    if (keyWord) {
      query.andWhere('(a.userName LIKE :keyWord OR a.phone LIKE :keyWord OR a.email LIKE :keyWord)', {
        keyWord: `%${keyWord}%`
      });
    }

    // 状态筛选
    if (status !== undefined && status !== null) {
      query.andWhere('a.status = :status', { status });
    }

    const result = await query
      .orderBy('a.createTime', 'DESC')
      .skip((body.page - 1) * body.size)
      .take(body.size)
      .getManyAndCount();

    return {
      list: result[0],
      pagination: {
        page: body.page,
        size: body.size,
        total: result[1]
      }
    };
  }
} 