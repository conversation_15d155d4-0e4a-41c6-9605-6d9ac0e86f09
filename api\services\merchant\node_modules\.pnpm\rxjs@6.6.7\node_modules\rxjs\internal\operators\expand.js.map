{"version": 3, "file": "expand.js", "sources": ["../../src/internal/operators/expand.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAKA,oDAAiG;AA2DjG,SAAgB,MAAM,CAAO,OAAwD,EACxD,UAA6C,EAC7C,SAAyB;IADzB,2BAAA,EAAA,aAAqB,MAAM,CAAC,iBAAiB;IAExE,UAAU,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,UAAU,CAAC;IAE3E,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,EAA/D,CAA+D,CAAC;AACpG,CAAC;AAND,wBAMC;AAED;IACE,wBAAoB,OAAwD,EACxD,UAAkB,EAClB,SAAyB;QAFzB,YAAO,GAAP,OAAO,CAAiD;QACxD,eAAU,GAAV,UAAU,CAAQ;QAClB,cAAS,GAAT,SAAS,CAAgB;IAC7C,CAAC;IAED,6BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3G,CAAC;IACH,qBAAC;AAAD,CAAC,AATD,IASC;AATY,wCAAc;AAuB3B;IAA4C,oCAA2B;IAMrE,0BAAY,WAA0B,EAClB,OAAwD,EACxD,UAAkB,EAClB,SAAyB;QAH7C,YAIE,kBAAM,WAAW,CAAC,SAInB;QAPmB,aAAO,GAAP,OAAO,CAAiD;QACxD,gBAAU,GAAV,UAAU,CAAQ;QAClB,eAAS,GAAT,SAAS,CAAgB;QARrC,WAAK,GAAW,CAAC,CAAC;QAClB,YAAM,GAAW,CAAC,CAAC;QACnB,kBAAY,GAAY,KAAK,CAAC;QAQpC,IAAI,UAAU,GAAG,MAAM,CAAC,iBAAiB,EAAE;YACzC,KAAI,CAAC,MAAM,GAAG,EAAE,CAAC;SAClB;;IACH,CAAC;IAEc,yBAAQ,GAAvB,UAA8B,GAAsB;QAC3C,IAAA,2BAAU,EAAE,mBAAM,EAAE,iBAAK,EAAE,iBAAK,CAAQ;QAC/C,UAAU,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAES,gCAAK,GAAf,UAAgB,KAAU;QACxB,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QAErC,IAAI,WAAW,CAAC,MAAM,EAAE;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,OAAO;SACR;QAED,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;YACjC,WAAW,CAAC,IAAK,CAAC,KAAK,CAAC,CAAC;YACzB,IAAI;gBACM,IAAA,sBAAO,CAAU;gBACzB,IAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACnB,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;iBAClD;qBAAM;oBACL,IAAM,KAAK,GAAsB,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,QAAA,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC;oBAC5E,IAAM,aAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;oBACrD,aAAW,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAoB,gBAAgB,CAAC,QAAe,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;iBACzG;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,WAAW,CAAC,KAAM,CAAC,CAAC,CAAC,CAAC;aACvB;SACF;aAAM;YACL,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1B;IACH,CAAC;IAEO,gDAAqB,GAA7B,UAA8B,MAAW,EAAE,KAAQ,EAAE,KAAa;QAChE,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,+BAAc,CAAC,MAAM,EAAE,IAAI,sCAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC;IAES,oCAAS,GAAnB;QACE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,qCAAU,GAAV,UAAW,UAAa;QACtB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;IACzB,CAAC;IAED,yCAAc,GAAd;QACE,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;QACd,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;SAC5B;QACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;SAC9B;IACH,CAAC;IACH,uBAAC;AAAD,CAAC,AA9ED,CAA4C,sCAAqB,GA8EhE;AA9EY,4CAAgB"}