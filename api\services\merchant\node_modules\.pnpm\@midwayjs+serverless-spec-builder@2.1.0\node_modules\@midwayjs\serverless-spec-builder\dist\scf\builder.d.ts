import { SpecBuilder } from '../index';
import { SCFHTTPMethod } from './interface';
export declare const nodejsVersion: {
    nodejs6: string;
    nodejs8: string;
    nodejs10: string;
    nodejs12: string;
};
export declare class SCFServerlessSpecBuilder extends SpecBuilder {
    toJSON(): {};
}
export declare function convertMethods(method: string | string[]): SCFHTTPMethod;
//# sourceMappingURL=builder.d.ts.map