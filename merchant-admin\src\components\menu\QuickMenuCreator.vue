<template>
  <div class="quick-menu-creator">
    <el-button type="primary" @click="showDialog = true">
      <el-icon><Magic /></el-icon>
      快速创建菜单
    </el-button>

    <el-dialog
      v-model="showDialog"
      title="快速创建菜单"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="creator-content">
        <el-alert
          title="提示"
          description="选择预设模板快速创建菜单，包括权限配置"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />

        <el-tabs v-model="activeTab">
          <!-- 结算模块 -->
          <el-tab-pane label="结算模块" name="settlement">
            <div class="template-grid">
              <div 
                v-for="template in settlementTemplates" 
                :key="template.path"
                class="template-card"
                :class="{ active: selectedTemplates.includes(template.path) }"
                @click="toggleTemplate(template.path)"
              >
                <div class="template-header">
                  <el-icon class="template-icon">
                    <component :is="getIconComponent(template.icon)" />
                  </el-icon>
                  <h4>{{ template.name }}</h4>
                </div>
                <p class="template-desc">{{ template.description }}</p>
                <div class="template-info">
                  <el-tag size="small">{{ template.path }}</el-tag>
                  <el-tag size="small" type="info">{{ template.permissions?.length || 0 }} 权限</el-tag>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <!-- 自定义 -->
          <el-tab-pane label="自定义" name="custom">
            <el-form :model="customForm" label-width="100px">
              <el-form-item label="菜单名称">
                <el-input v-model="customForm.name" placeholder="请输入菜单名称" />
              </el-form-item>
              <el-form-item label="路由路径">
                <el-input v-model="customForm.path" placeholder="如: /settlement/custom" />
              </el-form-item>
              <el-form-item label="图标">
                <el-input v-model="customForm.icon" placeholder="如: icon-custom" />
              </el-form-item>
              <el-form-item label="父菜单">
                <el-select v-model="customForm.parentPath" placeholder="选择父菜单">
                  <el-option label="财务结算" value="/settlement" />
                  <el-option label="商户管理" value="/merchant" />
                  <el-option label="系统管理" value="/system" />
                </el-select>
              </el-form-item>
              <el-form-item label="描述">
                <el-input 
                  v-model="customForm.description" 
                  type="textarea" 
                  placeholder="菜单功能描述"
                />
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="createMenus"
            :loading="creating"
            :disabled="activeTab === 'settlement' && selectedTemplates.length === 0"
          >
            <el-icon><Check /></el-icon>
            创建菜单 ({{ activeTab === 'settlement' ? selectedTemplates.length : 1 }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Magic, Check, Shield, Globe } from '@element-plus/icons-vue'
import { useMenuStore } from '@/store/modules/menu'
import { 
  settlementMenuTemplates, 
  MenuCreationHelper,
  type MenuTemplate 
} from '@/utils/menu-templates'

const menuStore = useMenuStore()

// 对话框状态
const showDialog = ref(false)
const activeTab = ref('settlement')
const creating = ref(false)

// 选中的模板
const selectedTemplates = ref<string[]>([])

// 结算模块模板
const settlementTemplates = settlementMenuTemplates

// 自定义表单
const customForm = reactive({
  name: '',
  path: '',
  icon: '',
  parentPath: '/settlement',
  description: ''
})

// 获取图标组件
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    'icon-shield': Shield,
    'icon-global': Globe
  }
  return iconMap[iconName] || Shield
}

// 切换模板选择
const toggleTemplate = (templatePath: string) => {
  const index = selectedTemplates.value.indexOf(templatePath)
  if (index > -1) {
    selectedTemplates.value.splice(index, 1)
  } else {
    selectedTemplates.value.push(templatePath)
  }
}

// 创建菜单
const createMenus = async () => {
  creating.value = true
  
  try {
    if (activeTab.value === 'settlement') {
      // 创建选中的结算模板菜单
      const selectedMenus = settlementTemplates.filter(t => 
        selectedTemplates.value.includes(t.path)
      )
      
      for (const template of selectedMenus) {
        await createMenuFromTemplate(template)
      }
      
      ElMessage.success(`成功创建 ${selectedMenus.length} 个菜单`)
    } else {
      // 创建自定义菜单
      await createCustomMenu()
      ElMessage.success('自定义菜单创建成功')
    }
    
    showDialog.value = false
    selectedTemplates.value = []
    resetCustomForm()
    
    // 刷新菜单列表
    emit('refresh')
    
  } catch (error) {
    ElMessage.error(`菜单创建失败: ${error.message}`)
  } finally {
    creating.value = false
  }
}

// 从模板创建菜单
const createMenuFromTemplate = async (template: MenuTemplate) => {
  // 确保父菜单存在
  const parentMenu = await MenuCreationHelper.ensureParentMenu(menuStore)
  
  // 创建菜单数据
  const menuData = {
    name: template.name,
    path: template.path,
    component: template.component,
    parentId: parentMenu.id,
    meta: {
      title: template.name,
      icon: template.icon,
      keepAlive: true,
      isHide: false,
      isMenu: true,
      sort: 1
    }
  }
  
  // 添加菜单
  const newMenu = await menuStore.addMenu(menuData, parentMenu.id)
  
  // 添加权限按钮（如果有）
  if (template.permissions && template.permissions.length > 0) {
    for (let i = 0; i < template.permissions.length; i++) {
      const perm = template.permissions[i]
      const buttonData = {
        name: getPermissionName(perm),
        parentId: newMenu.id,
        perms: perm,
        type: 2, // 按钮类型
        orderNum: i + 1,
        isShow: false
      }
      await menuStore.addMenu(buttonData, newMenu.id)
    }
  }
}

// 创建自定义菜单
const createCustomMenu = async () => {
  if (!customForm.name || !customForm.path) {
    throw new Error('菜单名称和路由路径不能为空')
  }
  
  // 查找父菜单
  const parentMenu = await findParentMenu(customForm.parentPath)
  
  const menuData = {
    name: customForm.name,
    path: customForm.path,
    component: customForm.path,
    parentId: parentMenu?.id || 0,
    meta: {
      title: customForm.name,
      icon: customForm.icon || 'icon-menu',
      keepAlive: true,
      isHide: false,
      isMenu: true,
      sort: 1
    }
  }
  
  await menuStore.addMenu(menuData, parentMenu?.id || 0)
}

// 查找父菜单
const findParentMenu = async (parentPath: string) => {
  const menuList = await menuStore.getMenuList()
  return menuList.find((menu: any) => menu.path === parentPath)
}

// 获取权限名称
const getPermissionName = (perm: string) => {
  const permMap: Record<string, string> = {
    'settlement:risk:view': '查看风险设置',
    'settlement:risk:edit': '修改风险设置',
    'settlement:compliance:view': '查看合规设置',
    'settlement:compliance:edit': '修改合规设置',
    'settlement:currency:view': '查看币种设置',
    'settlement:currency:edit': '修改币种设置',
    'settlement:region:view': '查看地区设置',
    'settlement:region:edit': '修改地区设置',
    'settlement:exchange:view': '查看汇率信息',
    'settlement:exchange:refresh': '刷新汇率'
  }
  return permMap[perm] || perm
}

// 重置自定义表单
const resetCustomForm = () => {
  Object.assign(customForm, {
    name: '',
    path: '',
    icon: '',
    parentPath: '/settlement',
    description: ''
  })
}

// 事件
const emit = defineEmits<{
  refresh: []
}>()
</script>

<style scoped lang="scss">
.quick-menu-creator {
  display: inline-block;
}

.creator-content {
  .template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
    margin-top: 16px;
  }
  
  .template-card {
    border: 2px solid var(--el-border-color);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.3s;
    
    &:hover {
      border-color: var(--el-color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    &.active {
      border-color: var(--el-color-primary);
      background-color: var(--el-color-primary-light-9);
    }
    
    .template-header {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      
      .template-icon {
        font-size: 20px;
        color: var(--el-color-primary);
        margin-right: 8px;
      }
      
      h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }
    }
    
    .template-desc {
      color: var(--el-text-color-regular);
      font-size: 14px;
      margin: 8px 0;
      line-height: 1.4;
    }
    
    .template-info {
      display: flex;
      gap: 8px;
      margin-top: 12px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
