{"version": 3, "sources": ["../browser/src/find-options/operator/MoreThan.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAI,KAA0B;IAClD,OAAO,IAAI,YAAY,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;AAC9C,CAAC", "file": "MoreThan.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: MoreThan(10) }\n */\nexport function MoreThan<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"moreThan\", value)\n}\n"], "sourceRoot": "../.."}