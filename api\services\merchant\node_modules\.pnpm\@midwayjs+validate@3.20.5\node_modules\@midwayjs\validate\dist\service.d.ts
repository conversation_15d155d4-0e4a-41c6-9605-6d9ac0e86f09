import * as DefaultConfig from './config/config.default';
import * as <PERSON><PERSON> from 'joi';
import { MidwayI18nServiceSingleton } from '@midwayjs/i18n';
import { ObjectSchema, AnySchema } from 'joi';
export declare class ValidateService {
    protected validateConfig: typeof DefaultConfig.validate;
    protected i18nConfig: any;
    protected i18nService: MidwayI18nServiceSingleton;
    protected messages: {};
    protected init(): Promise<void>;
    validate<T extends new (...args: any[]) => any>(ClzType: T, value: any, options?: {
        errorStatus?: number;
        locale?: string;
        validationOptions?: Joi.ValidationOptions;
    }): Jo<PERSON>.ValidationResult<T> | undefined;
    validateWithSchema<T>(schema: AnySchema<T>, value: any, options?: {
        errorStatus?: number;
        locale?: string;
        validationOptions?: Joi.ValidationOptions;
    }): Jo<PERSON>.ValidationResult<T> | undefined;
    getSchema<T extends new (...args: any[]) => any>(ClzType: T): ObjectSchema<any>;
}
export declare function getSchema<T extends new (...args: any[]) => any>(ClzType: T): ObjectSchema<any>;
//# sourceMappingURL=service.d.ts.map