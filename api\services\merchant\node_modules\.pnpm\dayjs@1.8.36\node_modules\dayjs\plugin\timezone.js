!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.dayjs_plugin_timezone=e()}(this,function(){"use strict";var t={year:0,month:1,day:2,hour:3,minute:4,second:5};return function(e,n,i){var o,r=i().utcOffset(),u=function(e,n){for(var o=new Date(e),r=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:n,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).formatToParts(o),u=[],a=0;a<r.length;a+=1){var f=r[a],s=f.type,d=f.value,c=t[s];c>=0&&(u[c]=parseInt(d,10))}var m=u[3],v=24===m?0:m,h=u[0]+"-"+u[1]+"-"+u[2]+" "+v+":"+u[4]+":"+u[5]+":000",l=+o;return(i.utc(h).valueOf()-(l-=l%1e3))/6e4};n.prototype.tz=function(t){void 0===t&&(t=o);var e=this.toDate().toLocaleString("en-US",{timeZone:t}),n=Math.round((this.toDate()-new Date(e))/1e3/60);return i(e).utcOffset(r-n,!0).$set("ms",this.$ms)},i.tz=function(t,e){void 0===e&&(e=o);var n,r=u(+i(),e);"string"!=typeof t&&(n=i(t)+60*r*1e3);var a=function(t,e,n){var i=t-60*e*1e3,o=u(i,n);if(e===o)return[i,e];var r=u(i-=60*(o-e)*1e3,n);return o===r?[i,o]:[t-60*Math.min(o,r)*1e3,Math.max(o,r)]}(n=n||i.utc(t).valueOf(),r,e),f=a[0],s=a[1];return i(f).utcOffset(s)},i.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},i.tz.setDefault=function(t){o=t}}});
