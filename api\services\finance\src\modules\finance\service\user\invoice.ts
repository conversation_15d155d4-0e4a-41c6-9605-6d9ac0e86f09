import { Init, Inject, Provide } from '@midwayjs/decorator';
import { BaseService, CoolCommException } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, In, Repository } from 'typeorm';
import { FinanceUserInvoiceEntity } from '../../entity/user/invoice';

/**
 * 用户发票
 */
@Provide()
export class FinanceUserInvoiceService extends BaseService {
  @InjectEntityModel(FinanceUserInvoiceEntity)
  financeUserInvoiceEntity: Repository<FinanceUserInvoiceEntity>;

  @Inject()
  ctx;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeUserInvoiceEntity);
  }

  /**
   * 获得信息
   * @param userId
   * @param invoiceId
   * @returns
   */
  async getInvoice(userId: number, invoiceId: number) {
    return await this.financeUserInvoiceEntity.findOneBy({
      userId: Equal(userId),
      id: Equal(invoiceId),
    });
  }

  /**
   * 修改之前
   * @param data
   * @param type
   */
  async modifyBefore(data: any, type: 'add' | 'update' | 'delete') {
    const userId = this.ctx.user?.id;
    // 如果是app用户，只能操作自己的发票信息
    if (userId) {
      if (type == 'update') {
        const info = await this.financeUserInvoiceEntity.findOneBy({
          id: Equal(data.id),
        });
        if (info.userId != userId) {
          throw new CoolCommException('无权限操作');
        }
      }
      if (type == 'delete') {
        const infos = await this.financeUserInvoiceEntity.findBy({
          id: In(data),
        });
        if (infos.some(info => info.userId != userId)) {
          throw new CoolCommException('无权限操作');
        }
      }
    }
  }
}
