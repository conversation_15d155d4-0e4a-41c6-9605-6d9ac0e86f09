import { Options } from './cli';
import { PackageJson } from '@npm/types';
export declare function addScripts(packageJson: PackageJson, options: Options): Promise<boolean>;
export declare function addDependencies(packageJson: PackageJson, options: Options): Promise<boolean>;
export declare const ESLINT_CONFIG: {
    extends: string;
};
export declare const ESLINT_IGNORE = "dist/\n";
export declare function installDefaultTemplate(options: Options): Promise<boolean>;
export declare function init(options: Options): Promise<boolean>;
//# sourceMappingURL=init.d.ts.map