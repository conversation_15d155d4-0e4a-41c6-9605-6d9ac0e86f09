{"version": 3, "sources": ["../../src/driver/types/CteCapabilities.ts"], "names": [], "mappings": "", "file": "CteCapabilities.js", "sourcesContent": ["export interface CteCapabilities {\n    /**\n     * Are CTEs supported at all?\n     */\n    enabled: boolean\n\n    /**\n     * Are RETURNING clauses supported in CTEs?\n     */\n    writable?: boolean\n    /**\n     * Is RECURSIVE clause required for recursive CTEs?\n     */\n    requiresRecursiveHint?: boolean\n\n    /**\n     * Is MATERIALIZED clause supported?\n     */\n    materializedHint?: boolean\n}\n"], "sourceRoot": "../.."}