{"version": 3, "sources": ["../browser/src/driver/cockroachdb/CockroachDriver.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1C,OAAO,EAAE,uBAAuB,EAAE,MAAM,qCAAqC,CAAA;AAC7E,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAA;AAG3F,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAE5D,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAK5E,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAA;AAC1E,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAChD,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AACpD,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAE9C,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAS5C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAA;AAE7D;;GAEG;AACH,MAAM,OAAO,eAAe;IA0NxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QA1MlC;;;WAGG;QACH,WAAM,GAAU,EAAE,CAAA;QAElB;;WAEG;QACH,0BAAqB,GAAkB,EAAE,CAAA;QA+BzC;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;WAIG;QACH,uBAAkB,GAAiB;YAC/B,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,MAAM;YACN,MAAM;YACN,MAAM;YACN,UAAU;YACV,WAAW;YACX,SAAS;YACT,SAAS;YACT,KAAK;YACL,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,kBAAkB;YAClB,MAAM;YACN,MAAM;YACN,KAAK;YACL,MAAM;YACN,SAAS;YACT,MAAM;YACN,MAAM;YACN,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;YACV,QAAQ;YACR,mBAAmB;YACnB,WAAW;YACX,MAAM;YACN,cAAc;YACd,SAAS;YACT,MAAM;YACN,MAAM;YACN,wBAAwB;YACxB,WAAW;YACX,aAAa;YACb,6BAA6B;YAC7B,0BAA0B;YAC1B,MAAM;YACN,OAAO;YACP,MAAM;SACT,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB;YACjC,uBAAuB;YACvB,aAAa;SAChB,CAAA;QAED;;WAEG;QACH,iBAAY,GAAiB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;QAEtD;;WAEG;QACH,0BAAqB,GAAiB;YAClC,mBAAmB;YACnB,cAAc;YACd,SAAS;YACT,WAAW;YACX,MAAM;YACN,QAAQ;SACX,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;QAEtE;;WAEG;QACH,yBAAoB,GAAiB,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;QAElE;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,aAAa;YACzB,iBAAiB,EAAE,OAAO;YAC1B,UAAU,EAAE,aAAa;YACzB,iBAAiB,EAAE,OAAO;YAC1B,UAAU,EAAE,aAAa;YACzB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,MAAM;YACf,SAAS,EAAE,MAAM;YACjB,WAAW,EAAE,MAAM;YACnB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,MAAM;YAC1B,OAAO,EAAE,MAAM;YACf,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,MAAM;YACjB,aAAa,EAAE,MAAM;YACrB,UAAU,EAAE,QAAQ;YACpB,WAAW,EAAE,QAAQ;YACrB,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;YACzB,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,QAAQ;SAC1B,CAAA;QAED;;WAEG;QACH,qBAAgB,GAAW,GAAG,CAAA;QAE9B;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;SACtB,CAAA;QAQD,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI;YACd,gBAAgB,EAAE,IAAI;YACtB,qBAAqB,EAAE,IAAI;SAC9B,CAAA;QAOG,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAqC,CAAA;QAC/D,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAE3D,wBAAwB;QACxB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAC1C,IAAI,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CACrB,CAAC,QAAQ,CAAA;QACV,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;QAEjE,kHAAkH;QAClH,kDAAkD;QAClD,oDAAoD;QACpD,0BAA0B;QAC1B,iDAAiD;QACjD,8BAA8B;QAC9B,qDAAqD;QACrD,8BAA8B;QAC9B,qDAAqD;IACzD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC/C,CAAC,CAAC,CACL,CAAA;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAC/B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAClC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YAEpD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAA;YAC1D,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;gBACrB,IAAI,CAAC,YAAY,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;YAC5D,CAAC;YAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,CAAA;QACnC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,6BAA6B;QAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAC;YACjC,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvB,oDAAoD,CACvD,CAAA;QACL,CAAC;QAED,iFAAiF;QACjF,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvB,0DAA0D,CAC7D,CAAA;QAED,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,uBAAuB,CAAC,aAAa,CAAC,CAAA;QACpD,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,aAAa;YACrC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,6BAA6B,EACvD,CAAC;YACC,OAAO,SAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IACH,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAC3C,cAAc,CAAC,IAAI,CACtB,IAAI,CAAC,EACR,CAAC;YACC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAChC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,8EAA8E;QAC9E,IACI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,IAAI,CACtC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,cAAc,CAAC,IAAI,CACnC;YACG,CAAC,cAAc,CAAC,OAAO,CAAC;YAC5B,cAAc,CAAC,kBAAkB,KAAK,WAAW,EACnD,CAAC;YACC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACzC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,aAAa;YACrC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,6BAA6B,EACvD,CAAC;YACC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,MAAM;YAC9B,cAAc,CAAC,IAAI,KAAK,aAAa,EACvC,CAAC;YACC,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,EAAE,CAAA;gBAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;oBAAE,OAAO,KAAK,CAAA;gBAEtC,8HAA8H;gBAC9H,KAAK,GAAI,KAAgB;qBACpB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;qBACZ,KAAK,CAAC,GAAG,CAAC;qBACV,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;oBACT,4DAA4D;oBAC5D,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACxC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;oBAC1B,8CAA8C;oBAC9C,OAAO,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAA;gBACzC,CAAC,CAAC,CAAA;gBAEN,4DAA4D;gBAC5D,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAW,EAAE,EAAE;oBAC9B,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;wBACf,cAAc,CAAC,IAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBAChD,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;wBACf,CAAC,CAAC,GAAG,CAAA;gBACb,CAAC,CAAC,CAAA;YACN,CAAC;iBAAM,CAAC;gBACJ,4DAA4D;gBAC5D,KAAK;oBACD,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;wBACd,cAAc,CAAC,IAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;wBAC9C,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;wBACjB,CAAC,CAAC,KAAK,CAAA;YACnB,CAAC;QACL,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,CAAC,CACjC,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7D,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;YACpD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,GAAG,GAAG,UAAU,GAAG,GAAG,CAAA;IACjC,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,SAAiB,EAAE,MAAe;QAC7C,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAA;QAEhC,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,2BAA2B;YAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,0CAA0C;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,OAAO;YACH,QAAQ,EAAE,cAAc;YACxB,MAAM,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,YAAY;YACjE,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;SACpD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAQb;QACG,IACI,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,KAAK;YACrB,MAAM,CAAC,IAAI,KAAK,QAAQ;YACxB,MAAM,CAAC,IAAI,KAAK,OAAO,EACzB,CAAC;YACC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,MAAM,CAAC,IAAI,KAAK,mBAAmB;YACnC,MAAM,CAAC,IAAI,KAAK,cAAc,EAChC,CAAC;YACC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,IAAI;YACpB,MAAM,CAAC,IAAI,KAAK,6BAA6B,EAC/C,CAAC;YACC,OAAO,WAAW,CAAA;QACtB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,0BAA0B,EAAE,CAAC;YACpD,OAAO,aAAa,CAAA;QACxB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,wBAAwB,EAAE,CAAC;YAClD,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC9D,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,cAAc;YAC9B,MAAM,CAAC,IAAI,KAAK,aAAa;YAC7B,MAAM,CAAC,IAAI,KAAK,MAAM,EACxB,CAAC;YACC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC3D,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACpC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAC5D,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,kBAAkB;YAClC,MAAM,CAAC,IAAI,KAAK,OAAO,EACzB,CAAC;YACC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YACrC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,IACI,CAAC,cAAc,CAAC,IAAI,KAAK,MAAM;YAC3B,cAAc,CAAC,IAAI,KAAK,aAAa,CAAC;YAC1C,YAAY,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAA;gBACnD,IAAI,UAAU,GAAG,YAAY,CAAA;gBAC7B,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;oBACnC,IAAI,YAAY,KAAK,IAAI;wBAAE,OAAO,YAAY,QAAQ,IAAI,CAAA;oBAC1D,UAAU,GAAG,YAAY;yBACpB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;yBAChB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;yBAChB,KAAK,CAAC,GAAG,CAAC,CAAA;gBACnB,CAAC;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC5B,MAAM,IAAI,GAAG,SAAS,UAAU;yBAC3B,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;yBACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAA;oBACjB,OAAO,GAAG,IAAI,KAAK,QAAQ,IAAI,CAAA;gBACnC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,OAAO,IAAI,YAAY,GAAG,CAAA;YAC9B,CAAC;QACL,CAAC;aAAM,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YAC1C,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAA;QAC1C,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,YAAY,EAAE,CAAA;YAC5B,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,mBAAmB,EAAE,CAAC;gBAC9C,OAAO,qBAAqB,CAAA;YAChC,CAAC;iBAAM,IAAI,KAAK,CAAC,WAAW,EAAE,KAAK,cAAc,EAAE,CAAC;gBAChD,OAAO,gBAAgB,CAAA;YAC3B,CAAC;YACD,OAAO,KAAK,CAAA;QAChB,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO;gBACpC,CAAC,CAAC,KAAK,cAAc,CAAC,IAAI,IAAI;gBAC9B,CAAC,CAAC,EAAE,CAAA;YACR,OAAO,IAAI,YAAY,IAAI,SAAS,EAAE,CAAA;QAC1C,CAAC;QAED,IAAI,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,YAAY,KAAK,IAAI,EAAE,CAAC;YAC9D,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAA;QAC9C,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAsB;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;QACrC,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAA;QAC7D,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QACxC,CAAC;aAAM,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,IAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBAC3D,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,IAAI,GAAG,CAAA;YACxE,CAAC;iBAAM,IAAI,MAAM,CAAC,kBAAkB,IAAI,IAAI,EAAE,CAAC;gBAC3C,IAAI,GAAG,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,kBAAkB,GAAG,CAAA;YACzD,CAAC;iBAAM,CAAC;gBACJ,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;YACtB,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,sBAAsB;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAA;QAClD,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAY,EAAE,EAAE;gBAC5D,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;YAC/C,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,qBAAqB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE7D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAE7D,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,OAAO,CACvB,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAY,EAAE,EAAE;gBACxC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC,CAAA;YAC/C,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAA2B;QACpE,IAAI,CAAC,YAAY;YAAE,OAAO,SAAS,CAAA;QAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,MAAM,EAAE,CAAC;gBACT,QAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CACjB,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CACvD,CACJ,CAAA;YACL,CAAC;YACD,OAAO,GAAG,CAAA;QACd,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,iEAAiE;YACjE,yBAAyB;YACzB,qCAAqC;YACrC,mDAAmD;YACnD,KAAK;YACL,yBAAyB;YACzB,qCAAqC;YACrC,0DAA0D;YAC1D,KAAK;YACL,2BAA2B;YAC3B,uCAAuC;YACvC,6CAA6C;YAC7C,KAAK;YACL,kEAAkE;YAClE,8EAA8E;YAC9E,kEAAkE;YAClE,4FAA4F;YAC5F,wEAAwE;YACxE,2HAA2H;YAC3H,8EAA8E;YAC9E,iFAAiF;YACjF,0FAA0F;YAC1F,iCAAiC;YACjC,4DAA4D;YAC5D,kEAAkE;YAClE,KAAK;YACL,6DAA6D;YAE7D,OAAO,CACH,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM;gBAC5C,WAAW,CAAC,OAAO,KAAK,cAAc,CAAC,OAAO;gBAC9C,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,CAAC,cAAc,CAAC,KAAK,KAAK,SAAS;oBAC/B,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK,CAAC;gBAC/C,WAAW,CAAC,OAAO;oBACf,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC;gBAC9C,CAAC,CAAC,WAAW,CAAC,WAAW;oBACrB,IAAI,CAAC,4BAA4B,CAC7B,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,CACxC,KAAK,WAAW,CAAC,OAAO,CAAC,IAAI,kGAAkG;gBACpI,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,WAAW,CAAC,QAAQ,KAAK,cAAc,CAAC,QAAQ;gBAChD,CAAC,WAAW,CAAC,IAAI;oBACb,cAAc,CAAC,IAAI;oBACnB,CAAC,QAAQ,CAAC,aAAa,CACnB,WAAW,CAAC,IAAI,EAChB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAC7C,CAAC,IAAI,uCAAuC;gBACjD,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW;gBACtD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,CAAC,WAAW,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;oBACnC,CAAC,cAAc,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;gBAC9C,CAAC,WAAW,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;oBAChD,CAAC,cAAc,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;gBAC3D,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CAC3C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAEO,4BAA4B,CAAC,KAAyB;QAC1D,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,KAAK,CAAA;QAChB,CAAC;QACD,OAAO,KAAK;aACP,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACV,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAA;QAC5C,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IACD;;OAEG;IACH,uBAAuB;QACnB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;IAC9C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,oBAAoB;QAChB,IAAI,CAAC;YACD,OAAO,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAChD,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,YAAY,CAClB,6GAA6G,CAChH,CAAA;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAChE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;YACxB,IAAI,CAAC;gBACD,MAAM,QAAQ,GACV,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAChE,IAAI,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;oBAChC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;YAC5C,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;QAClB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,qCAAqC;YACrC,MAAM,IAAI,8BAA8B,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CACtB,OAAmC,EACnC,WAAkD;QAElD,WAAW,GAAG,MAAM,CAAC,MAAM,CACvB,EAAE,EACF,WAAW,EACX,WAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAC9C,CAAA,CAAC,yBAAyB;QAE3B,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,EACF;YACI,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,GAAG,EAAE,WAAW,CAAC,GAAG;YACpB,gBAAgB,EAAE,OAAO,CAAC,eAAe;YACzC,GAAG,EAAE,OAAO,CAAC,QAAQ;SACxB,EACD,OAAO,CAAC,KAAK,IAAI,EAAE,CACtB,CAAA;QAED,2BAA2B;QAC3B,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QACtD,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,gBAAgB,GAClB,OAAO,CAAC,gBAAgB;YACxB,CAAC,CAAC,KAAU,EAAE,EAAE,CACZ,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,kCAAkC,KAAK,EAAE,CAAC,CAAC,CAAA;QAEtE;;;WAGG;QACH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;QAElC,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC5B,IAAI,CAAC,OAAO,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAiB,EAAE,EAAE;gBAC1D,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,OAAO,EAAE,CAAA;gBACT,EAAE,CAAC,IAAI,CAAC,CAAA;YACZ,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,IAAS;QAC/B,MAAM,OAAO,CAAC,GAAG,CACb,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAC3C,WAAW,CAAC,OAAO,EAAE,CACxB,CACJ,CAAA;QACD,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;QACpD,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO;YAAE,OAAO,OAAO,CAAA;QAE5B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAErG,OAAO,OAAO,CAAA;IAClB,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,MAAsB;QAC1C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;QACxE,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ;YAC1B,CAAC,CAAC,MAAM,CAAC,QAAQ;YACjB,CAAC,CAAC,GAAG,SAAS,IAAI,MAAM,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAA;QAC9D,IAAI,MAAM;YAAE,QAAQ,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAA;QAC9C,OAAO,QAAQ;aACV,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,OAAO,IAAI,CAAC,GAAG,CAAA;QACnB,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;CACJ", "file": "CockroachDriver.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { TypeORMError } from \"../../error\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Driver } from \"../Driver\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { UpsertType } from \"../types/UpsertType\"\nimport { CockroachConnectionCredentialsOptions } from \"./CockroachConnectionCredentialsOptions\"\nimport { CockroachConnectionOptions } from \"./CockroachConnectionOptions\"\nimport { CockroachQueryRunner } from \"./CockroachQueryRunner\"\n\n/**\n * Organizes communication with Cockroach DBMS.\n */\nexport class CockroachDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Cockroach underlying library.\n     */\n    postgres: any\n\n    /**\n     * Pool for master database.\n     */\n    master: any\n\n    /**\n     * Pool for slave databases.\n     * Used in replication.\n     */\n    slaves: any[] = []\n\n    /**\n     * We store all created query runners because we need to release them.\n     */\n    connectedQueryRunners: QueryRunner[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: CockroachConnectionOptions\n\n    /**\n     * Database name used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Schema that's used internally by Postgres for object resolution.\n     *\n     * Because we never set this we have to track it in separately from the `schema` so\n     * we know when we have to specify the full schema or not.\n     *\n     * In most cases this will be `public`.\n     */\n    searchSchema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"nested\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://www.cockroachlabs.com/docs/stable/data-types.html\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"array\",\n        \"bool\",\n        \"boolean\",\n        \"bytes\",\n        \"bytea\",\n        \"blob\",\n        \"date\",\n        \"enum\",\n        \"geometry\",\n        \"geography\",\n        \"numeric\",\n        \"decimal\",\n        \"dec\",\n        \"float\",\n        \"float4\",\n        \"float8\",\n        \"double precision\",\n        \"real\",\n        \"inet\",\n        \"int\",\n        \"int4\",\n        \"integer\",\n        \"int2\",\n        \"int8\",\n        \"int64\",\n        \"smallint\",\n        \"bigint\",\n        \"interval\",\n        \"string\",\n        \"character varying\",\n        \"character\",\n        \"char\",\n        \"char varying\",\n        \"varchar\",\n        \"text\",\n        \"time\",\n        \"time without time zone\",\n        \"timestamp\",\n        \"timestamptz\",\n        \"timestamp without time zone\",\n        \"timestamp with time zone\",\n        \"json\",\n        \"jsonb\",\n        \"uuid\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\n        \"on-conflict-do-update\",\n        \"primary-key\",\n    ]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = [\"geometry\", \"geography\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"character varying\",\n        \"char varying\",\n        \"varchar\",\n        \"character\",\n        \"char\",\n        \"string\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\"numeric\", \"decimal\", \"dec\"]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\"numeric\", \"decimal\", \"dec\"]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"timestamptz\",\n        createDateDefault: \"now()\",\n        updateDate: \"timestamptz\",\n        updateDateDefault: \"now()\",\n        deleteDate: \"timestamptz\",\n        deleteDateNullable: true,\n        version: Number,\n        treeLevel: Number,\n        migrationId: Number,\n        migrationName: \"varchar\",\n        migrationTimestamp: \"int8\",\n        cacheId: Number,\n        cacheIdentifier: \"varchar\",\n        cacheTime: \"int8\",\n        cacheDuration: Number,\n        cacheQuery: \"string\",\n        cacheResult: \"string\",\n        metadataType: \"varchar\",\n        metadataDatabase: \"varchar\",\n        metadataSchema: \"varchar\",\n        metadataTable: \"varchar\",\n        metadataName: \"varchar\",\n        metadataValue: \"string\",\n    }\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix: string = \"$\"\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        char: { length: 1 },\n    }\n\n    /**\n     * No documentation specifying a maximum length for identifiers could be found\n     * for CockroarchDb.\n     */\n    maxAliasLength?: number\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n        writable: true,\n        materializedHint: true,\n        requiresRecursiveHint: true,\n    }\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = connection.options as CockroachConnectionOptions\n        this.isReplicated = this.options.replication ? true : false\n\n        // load postgres package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(\n            this.options.replication\n                ? this.options.replication.master\n                : this.options,\n        ).database\n        this.schema = DriverUtils.buildDriverOptions(this.options).schema\n\n        // ObjectUtils.assign(this.options, DriverUtils.buildDriverOptions(connection.options)); // todo: do it better way\n        // validate options to make sure everything is set\n        // todo: revisit validation with replication in mind\n        // if (!this.options.host)\n        //     throw new DriverOptionNotSetError(\"host\");\n        // if (!this.options.username)\n        //     throw new DriverOptionNotSetError(\"username\");\n        // if (!this.options.database)\n        //     throw new DriverOptionNotSetError(\"database\");\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {\n        if (this.options.replication) {\n            this.slaves = await Promise.all(\n                this.options.replication.slaves.map((slave) => {\n                    return this.createPool(this.options, slave)\n                }),\n            )\n            this.master = await this.createPool(\n                this.options,\n                this.options.replication.master,\n            )\n        } else {\n            this.master = await this.createPool(this.options, this.options)\n        }\n\n        if (!this.database || !this.searchSchema) {\n            const queryRunner = this.createQueryRunner(\"master\")\n\n            if (!this.database) {\n                this.database = await queryRunner.getCurrentDatabase()\n            }\n\n            if (!this.searchSchema) {\n                this.searchSchema = await queryRunner.getCurrentSchema()\n            }\n\n            await queryRunner.release()\n        }\n\n        if (!this.schema) {\n            this.schema = this.searchSchema\n        }\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    async afterConnect(): Promise<void> {\n        // enable time travel queries\n        if (this.options.timeTravelQueries) {\n            await this.connection.query(\n                `SET default_transaction_use_follower_reads = 'on';`,\n            )\n        }\n\n        // enable experimental alter column type support (we need it to alter enum types)\n        await this.connection.query(\n            \"SET enable_experimental_alter_column_type_general = true\",\n        )\n\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        if (!this.master) {\n            throw new ConnectionIsNotSetError(\"cockroachdb\")\n        }\n\n        await this.closePool(this.master)\n        await Promise.all(this.slaves.map((slave) => this.closePool(slave)))\n        this.master = undefined\n        this.slaves = []\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new CockroachQueryRunner(this, mode)\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedDateToTimeString(value)\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamptz\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp without time zone\"\n        ) {\n            return DateUtils.mixedDateToDate(value)\n        } else if (\n            [\"json\", \"jsonb\", ...this.spatialTypes].indexOf(\n                columnMetadata.type,\n            ) >= 0\n        ) {\n            return JSON.stringify(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        // unique_rowid() generates bigint value and should not be converted to number\n        if (\n            ([Number, \"int4\", \"smallint\", \"int2\"].some(\n                (v) => v === columnMetadata.type,\n            ) &&\n                !columnMetadata.isArray) ||\n            columnMetadata.generationStrategy === \"increment\"\n        ) {\n            value = parseInt(value)\n        } else if (columnMetadata.type === Boolean) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamptz\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp without time zone\"\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (\n            columnMetadata.type === \"enum\" ||\n            columnMetadata.type === \"simple-enum\"\n        ) {\n            if (columnMetadata.isArray) {\n                if (value === \"{}\") return []\n                if (Array.isArray(value)) return value\n\n                // manually convert enum array to array of values (pg does not support, see https://github.com/brianc/node-pg-types/issues/56)\n                value = (value as string)\n                    .slice(1, -1)\n                    .split(\",\")\n                    .map((val) => {\n                        // replace double quotes from the beginning and from the end\n                        if (val.startsWith(`\"`) && val.endsWith(`\"`))\n                            val = val.slice(1, -1)\n                        // replace escaped backslash and double quotes\n                        return val.replace(/\\\\(\\\\|\")/g, \"$1\")\n                    })\n\n                // convert to number if that exists in possible enum options\n                value = value.map((val: string) => {\n                    return !isNaN(+val) &&\n                        columnMetadata.enum!.indexOf(parseInt(val)) >= 0\n                        ? parseInt(val)\n                        : val\n                })\n            } else {\n                // convert to number if that exists in possible enum options\n                value =\n                    !isNaN(+value) &&\n                    columnMetadata.enum!.indexOf(parseInt(value)) >= 0\n                        ? parseInt(value)\n                        : value\n            }\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => nativeParameters[key],\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        const parameterIndexMap = new Map<string, number>()\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                if (parameterIndexMap.has(key)) {\n                    return this.parametersPrefix + parameterIndexMap.get(key)\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                escapedParameters.push(value)\n                parameterIndexMap.set(key, escapedParameters.length)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return '\"' + columnName + '\"'\n    }\n\n    /**\n     * Build full table name with schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(tableName: string, schema?: string): string {\n        const tablePath = [tableName]\n\n        if (schema) {\n            tablePath.unshift(schema)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = this.schema\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            // name is sometimes a path\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            // referencedTableName is sometimes a path\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        return {\n            database: driverDatabase,\n            schema: (parts.length > 1 ? parts[0] : undefined) || driverSchema,\n            tableName: parts.length > 1 ? parts[1] : parts[0],\n        }\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n        isArray?: boolean\n        isGenerated?: boolean\n        generationStrategy?: \"increment\" | \"uuid\" | \"rowid\"\n    }): string {\n        if (\n            column.type === Number ||\n            column.type === \"integer\" ||\n            column.type === \"int\" ||\n            column.type === \"bigint\" ||\n            column.type === \"int64\"\n        ) {\n            return \"int8\"\n        } else if (\n            column.type === String ||\n            column.type === \"character varying\" ||\n            column.type === \"char varying\"\n        ) {\n            return \"varchar\"\n        } else if (\n            column.type === Date ||\n            column.type === \"timestamp without time zone\"\n        ) {\n            return \"timestamp\"\n        } else if (column.type === \"timestamp with time zone\") {\n            return \"timestamptz\"\n        } else if (column.type === \"time without time zone\") {\n            return \"time\"\n        } else if (column.type === Boolean || column.type === \"boolean\") {\n            return \"bool\"\n        } else if (\n            column.type === \"simple-array\" ||\n            column.type === \"simple-json\" ||\n            column.type === \"text\"\n        ) {\n            return \"string\"\n        } else if (column.type === \"bytea\" || column.type === \"blob\") {\n            return \"bytes\"\n        } else if (column.type === \"smallint\") {\n            return \"int2\"\n        } else if (column.type === \"numeric\" || column.type === \"dec\") {\n            return \"decimal\"\n        } else if (\n            column.type === \"double precision\" ||\n            column.type === \"float\"\n        ) {\n            return \"float8\"\n        } else if (column.type === \"real\") {\n            return \"float4\"\n        } else if (column.type === \"character\") {\n            return \"char\"\n        } else if (column.type === \"simple-enum\") {\n            return \"enum\"\n        } else if (column.type === \"json\") {\n            return \"jsonb\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (defaultValue === undefined || defaultValue === null) {\n            return undefined\n        }\n\n        if (\n            (columnMetadata.type === \"enum\" ||\n                columnMetadata.type === \"simple-enum\") &&\n            defaultValue !== undefined\n        ) {\n            if (columnMetadata.isArray) {\n                const enumName = this.buildEnumName(columnMetadata)\n                let arrayValue = defaultValue\n                if (typeof defaultValue === \"string\") {\n                    if (defaultValue === \"{}\") return `ARRAY[]::${enumName}[]`\n                    arrayValue = defaultValue\n                        .replace(\"{\", \"\")\n                        .replace(\"}\", \"\")\n                        .split(\",\")\n                }\n                if (Array.isArray(arrayValue)) {\n                    const expr = `ARRAY[${arrayValue\n                        .map((it) => `'${it}'`)\n                        .join(\",\")}]`\n                    return `${expr}::${enumName}[]`\n                }\n            } else {\n                return `'${defaultValue}'`\n            }\n        } else if (typeof defaultValue === \"number\") {\n            return `(${defaultValue})`\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"true\" : \"false\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            const value = defaultValue()\n            if (value.toUpperCase() === \"CURRENT_TIMESTAMP\") {\n                return \"current_timestamp()\"\n            } else if (value.toUpperCase() === \"CURRENT_DATE\") {\n                return \"current_date()\"\n            }\n            return value\n        }\n\n        if (typeof defaultValue === \"string\") {\n            const arrayCast = columnMetadata.isArray\n                ? `::${columnMetadata.type}[]`\n                : \"\"\n            return `'${defaultValue}'${arrayCast}`\n        }\n\n        if (ObjectUtils.isObject(defaultValue) && defaultValue !== null) {\n            return `'${JSON.stringify(defaultValue)}'`\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.uniques.some(\n            (uq) => uq.columns.length === 1 && uq.columns[0] === column,\n        )\n    }\n\n    /**\n     * Returns default column lengths, which is required on column creation.\n     */\n    getColumnLength(column: ColumnMetadata): string {\n        return column.length ? column.length.toString() : \"\"\n    }\n\n    /**\n     * Creates column type definition including length, precision and scale\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        if (column.length) {\n            type += \"(\" + column.length + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += \"(\" + column.precision + \",\" + column.scale + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += \"(\" + column.precision + \")\"\n        } else if (this.spatialTypes.indexOf(column.type as ColumnType) >= 0) {\n            if (column.spatialFeatureType != null && column.srid != null) {\n                type = `${column.type}(${column.spatialFeatureType},${column.srid})`\n            } else if (column.spatialFeatureType != null) {\n                type = `${column.type}(${column.spatialFeatureType})`\n            } else {\n                type = column.type\n            }\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    async obtainMasterConnection(): Promise<any> {\n        if (!this.master) {\n            throw new TypeORMError(\"Driver not Connected\")\n        }\n\n        return new Promise((ok, fail) => {\n            this.master.connect((err: any, connection: any, release: any) => {\n                err ? fail(err) : ok([connection, release])\n            })\n        })\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    async obtainSlaveConnection(): Promise<any> {\n        if (!this.slaves.length) return this.obtainMasterConnection()\n\n        const random = Math.floor(Math.random() * this.slaves.length)\n\n        return new Promise((ok, fail) => {\n            this.slaves[random].connect(\n                (err: any, connection: any, release: any) => {\n                    err ? fail(err) : ok([connection, release])\n                },\n            )\n        })\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     *\n     * todo: slow. optimize Object.keys(), OrmUtils.mergeDeep and column.createValueMap parts\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: ObjectLiteral) {\n        if (!insertResult) return undefined\n\n        return Object.keys(insertResult).reduce((map, key) => {\n            const column = metadata.findColumnWithDatabaseName(key)\n            if (column) {\n                OrmUtils.mergeDeep(\n                    map,\n                    column.createValueMap(\n                        this.prepareHydratedValue(insertResult[key], column),\n                    ),\n                )\n            }\n            return map\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            // console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            // console.log(\"name:\", {\n            //     tableColumn: tableColumn.name,\n            //     columnMetadata: columnMetadata.databaseName,\n            // })\n            // console.log(\"type:\", {\n            //     tableColumn: tableColumn.type,\n            //     columnMetadata: this.normalizeType(columnMetadata),\n            // })\n            // console.log(\"length:\", {\n            //     tableColumn: tableColumn.length,\n            //     columnMetadata: columnMetadata.length,\n            // })\n            // console.log(\"width:\", tableColumn.width, columnMetadata.width);\n            // console.log(\"precision:\", tableColumn.precision, columnMetadata.precision);\n            // console.log(\"scale:\", tableColumn.scale, columnMetadata.scale);\n            // console.log(\"comment:\", tableColumn.comment, this.escapeComment(columnMetadata.comment));\n            // console.log(\"default:\", tableColumn.default, columnMetadata.default);\n            // console.log(\"default changed:\", !this.compareDefaultValues(this.normalizeDefault(columnMetadata), tableColumn.default));\n            // console.log(\"isPrimary:\", tableColumn.isPrimary, columnMetadata.isPrimary);\n            // console.log(\"isNullable:\", tableColumn.isNullable, columnMetadata.isNullable);\n            // console.log(\"isUnique:\", tableColumn.isUnique, this.normalizeIsUnique(columnMetadata));\n            // console.log(\"asExpression:\", {\n            //     tableColumn: (tableColumn.asExpression || \"\").trim(),\n            //     columnMetadata: (columnMetadata.asExpression || \"\").trim(),\n            // })\n            // console.log(\"==========================================\");\n\n            return (\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                tableColumn.length !== columnMetadata.length ||\n                tableColumn.isArray !== columnMetadata.isArray ||\n                tableColumn.precision !== columnMetadata.precision ||\n                (columnMetadata.scale !== undefined &&\n                    tableColumn.scale !== columnMetadata.scale) ||\n                tableColumn.comment !==\n                    this.escapeComment(columnMetadata.comment) ||\n                (!tableColumn.isGenerated &&\n                    this.lowerDefaultValueIfNecessary(\n                        this.normalizeDefault(columnMetadata),\n                    ) !== tableColumn.default) || // we included check for generated here, because generated columns already can have default values\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                tableColumn.enumName !== columnMetadata.enumName ||\n                (tableColumn.enum &&\n                    columnMetadata.enum &&\n                    !OrmUtils.isArraysEqual(\n                        tableColumn.enum,\n                        columnMetadata.enum.map((val) => val + \"\"),\n                    )) || // enums in postgres are always strings\n                tableColumn.isGenerated !== columnMetadata.isGenerated ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                (tableColumn.asExpression || \"\").trim() !==\n                    (columnMetadata.asExpression || \"\").trim() ||\n                (tableColumn.spatialFeatureType || \"\").toLowerCase() !==\n                    (columnMetadata.spatialFeatureType || \"\").toLowerCase() ||\n                tableColumn.srid !== columnMetadata.srid\n            )\n        })\n    }\n\n    private lowerDefaultValueIfNecessary(value: string | undefined) {\n        if (!value) {\n            return value\n        }\n        return value\n            .split(`'`)\n            .map((v, i) => {\n                return i % 2 === 1 ? v : v.toLowerCase()\n            })\n            .join(`'`)\n    }\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return this.parametersPrefix + (index + 1)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads postgres query stream package.\n     */\n    loadStreamDependency() {\n        try {\n            return PlatformTools.load(\"pg-query-stream\")\n        } catch (e) {\n            // todo: better error for browser env\n            throw new TypeORMError(\n                `To use streams you should install pg-query-stream package. Please run npm i pg-query-stream --save command.`,\n            )\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const postgres = this.options.driver || PlatformTools.load(\"pg\")\n            this.postgres = postgres\n            try {\n                const pgNative =\n                    this.options.nativeDriver || PlatformTools.load(\"pg-native\")\n                if (pgNative && this.postgres.native)\n                    this.postgres = this.postgres.native\n            } catch (e) {}\n        } catch (e) {\n            // todo: better error for browser env\n            throw new DriverPackageNotInstalledError(\"Postgres\", \"pg\")\n        }\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected async createPool(\n        options: CockroachConnectionOptions,\n        credentials: CockroachConnectionCredentialsOptions,\n    ): Promise<any> {\n        credentials = Object.assign(\n            {},\n            credentials,\n            DriverUtils.buildDriverOptions(credentials),\n        ) // todo: do it better way\n\n        // build connection options for the driver\n        const connectionOptions = Object.assign(\n            {},\n            {\n                host: credentials.host,\n                user: credentials.username,\n                password: credentials.password,\n                database: credentials.database,\n                port: credentials.port,\n                ssl: credentials.ssl,\n                application_name: options.applicationName,\n                max: options.poolSize,\n            },\n            options.extra || {},\n        )\n\n        // create a connection pool\n        const pool = new this.postgres.Pool(connectionOptions)\n        const { logger } = this.connection\n\n        const poolErrorHandler =\n            options.poolErrorHandler ||\n            ((error: any) =>\n                logger.log(\"warn\", `Postgres pool raised an error. ${error}`))\n\n        /*\n          Attaching an error handler to pool errors is essential, as, otherwise, errors raised will go unhandled and\n          cause the hosting app to crash.\n         */\n        pool.on(\"error\", poolErrorHandler)\n\n        return new Promise((ok, fail) => {\n            pool.connect((err: any, connection: any, release: Function) => {\n                if (err) return fail(err)\n                release()\n                ok(pool)\n            })\n        })\n    }\n\n    /**\n     * Closes connection pool.\n     */\n    protected async closePool(pool: any): Promise<void> {\n        await Promise.all(\n            this.connectedQueryRunners.map((queryRunner) =>\n                queryRunner.release(),\n            ),\n        )\n        return new Promise<void>((ok, fail) => {\n            pool.end((err: any) => (err ? fail(err) : ok()))\n        })\n    }\n\n    /**\n     * Escapes a given comment.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment) return comment\n\n        comment = comment.replace(/'/g, \"''\").replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return comment\n    }\n\n    /**\n     * Builds ENUM type name from given table and column.\n     */\n    protected buildEnumName(column: ColumnMetadata): string {\n        const { schema, tableName } = this.parseTableName(column.entityMetadata)\n        let enumName = column.enumName\n            ? column.enumName\n            : `${tableName}_${column.databaseName.toLowerCase()}_enum`\n        if (schema) enumName = `${schema}.${enumName}`\n        return enumName\n            .split(\".\")\n            .map((i) => {\n                return `\"${i}\"`\n            })\n            .join(\".\")\n    }\n}\n"], "sourceRoot": "../.."}