{"name": "vizion", "version": "2.2.1", "engines": {"node": ">=4.0"}, "author": {"name": "Keymetrics", "email": "<EMAIL>"}, "keywords": ["git", "svn", "hg", "subversion", "mercurial", "repository", "parser", "versioning", "revision"], "description": "Git/Subversion/Mercurial repository metadata parser", "repository": {"type": "git", "url": "https://github.com/keymetrics/vizion"}, "main": "index.js", "scripts": {"cover": "nyc --reporter=html npm test", "test": "mocha"}, "dependencies": {"async": "^2.6.3", "git-node-fs": "^1.0.0", "ini": "^1.3.5", "js-git": "^0.7.8"}, "devDependencies": {"chai": "4.1.2", "mocha": "^6.0.0", "nyc": "~15", "shelljs": "~0.8.4", "should": "~13.2.3", "sinon": "~9.2.4"}, "bugs": {"url": "https://github.com/keymetrics/vizion/issues"}, "license": "Apache-2.0"}