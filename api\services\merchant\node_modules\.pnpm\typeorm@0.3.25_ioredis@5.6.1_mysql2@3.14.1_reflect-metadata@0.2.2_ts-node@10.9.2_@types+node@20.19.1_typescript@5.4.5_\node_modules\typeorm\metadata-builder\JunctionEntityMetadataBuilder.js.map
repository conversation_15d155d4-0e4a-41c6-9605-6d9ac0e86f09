{"version": 3, "sources": ["../../src/metadata-builder/JunctionEntityMetadataBuilder.ts"], "names": [], "mappings": ";;;AAAA,+DAA2D;AAE3D,+DAA2D;AAC3D,uEAAmE;AACnE,6DAAyD;AAGzD,oCAAuC;AACvC,uDAAmD;AAEnD;;;GAGG;AACH,MAAa,6BAA6B;IACtC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAE9C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CACD,QAA0B,EAC1B,SAAgC;QAEhC,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CACnD,QAAQ,EACR,SAAS,CACZ,CAAA;QACD,MAAM,wBAAwB,GAAG,IAAI,CAAC,+BAA+B,CACjE,QAAQ,EACR,SAAS,CACZ,CAAA;QAED,MAAM,aAAa,GACf,SAAS,CAAC,IAAI;YACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,aAAa,CACxC,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAC9C,QAAQ,CAAC,qBAAqB,CAAC,sBAAsB,EACrD,QAAQ,CAAC,YAAY,EACrB,QAAQ,CAAC,eAAe;gBACpB,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY;gBACvC,CAAC,CAAC,EAAE,CACX,CAAA;QAEL,MAAM,cAAc,GAAG,IAAI,+BAAc,CAAC;YACtC,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE;gBACF,MAAM,EAAE,EAAE;gBACV,IAAI,EAAE,aAAa;gBACnB,IAAI,EAAE,UAAU;gBAChB,QAAQ,EACJ,SAAS,CAAC,QAAQ,IAAI,QAAQ,CAAC,cAAc,CAAC,QAAQ;gBAC1D,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,QAAQ,CAAC,cAAc,CAAC,MAAM;gBAC1D,WAAW,EAAE,SAAS,CAAC,WAAW;aACrC;SACJ,CAAC,CAAA;QACF,cAAc,CAAC,KAAK,EAAE,CAAA;QAEtB,wCAAwC;QACxC,MAAM,eAAe,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE;YAC/D,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW;gBACpC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;oBAC1C,OAAO,CACH,CAAC,CAAC,cAAc,CAAC,oBAAoB;wBACjC,cAAc,CAAC,oBAAoB;4BAC/B,gBAAgB,CAAC,YAAY,CAAC;wBACtC,CAAC,CAAC,cAAc,CAAC,IAAI,CACxB,CAAA;gBACL,CAAC,CAAC;gBACJ,CAAC,CAAC,SAAS,CAAA;YACf,MAAM,UAAU,GACZ,UAAU,IAAI,UAAU,CAAC,IAAI;gBACzB,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,QAAQ,CAAC,cAAc,CAAC,sBAAsB,EAC9C,gBAAgB,CAAC,YAAY,EAC7B,gBAAgB,CAAC,YAAY,CAChC,CAAA;YAEX,OAAO,IAAI,+BAAc,CAAC;gBACtB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,gBAAgB,EAAE,gBAAgB;gBAClC,IAAI,EAAE;oBACF,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,UAAU;oBACxB,OAAO,EAAE;wBACL,IAAI,EAAE,UAAU;wBAChB,MAAM,EACF,CAAC,gBAAgB,CAAC,MAAM;4BACxB,CAAC,yBAAW,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;gCACG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;oCAC/B,cAAc,CAAC;4BACvB,qGAAqG;4BACrG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAChC,gBAAgB,CACnB,KAAK,MAAM;4BACZ,CAAC,gBAAgB,CAAC,kBAAkB,KAAK,MAAM;gCAC3C,gBAAgB,CAAC,IAAI,KAAK,MAAM,CAAC;4BACjC,CAAC,CAAC,IAAI;4BACN,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,qDAAqD;wBACxF,KAAK,EAAE,gBAAgB,CAAC,KAAK;wBAC7B,IAAI,EAAE,gBAAgB,CAAC,IAAI;wBAC3B,SAAS,EAAE,gBAAgB,CAAC,SAAS;wBACrC,KAAK,EAAE,gBAAgB,CAAC,KAAK;wBAC7B,OAAO,EAAE,gBAAgB,CAAC,OAAO;wBACjC,SAAS,EAAE,gBAAgB,CAAC,SAAS;wBACrC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;wBACnC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;4BAC/B,CAAC,CAAC,IAAI;4BACN,CAAC,CAAC,gBAAgB,CAAC,QAAQ;wBAC/B,IAAI,EAAE,gBAAgB,CAAC,IAAI;wBAC3B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;wBACnC,wBAAwB,EACpB,UAAU,EAAE,wBAAwB;wBACxC,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,IAAI;qBAChB;iBACJ;aACJ,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QAEF,uCAAuC;QACvC,MAAM,sBAAsB,GAAG,wBAAwB,CAAC,GAAG,CACvD,CAAC,uBAAuB,EAAE,EAAE;YACxB,MAAM,UAAU,GAAG,SAAS,CAAC,kBAAkB;gBAC3C,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;oBACjD,OAAO,CACH,CAAC,CAAC,cAAc,CAAC,oBAAoB;wBACjC,cAAc,CAAC,oBAAoB;4BAC/B,uBAAuB,CAAC,YAAY,CAAC;wBAC7C,CAAC,CAAC,cAAc,CAAC,IAAI,CACxB,CAAA;gBACL,CAAC,CAAC;gBACJ,CAAC,CAAC,SAAS,CAAA;YACf,MAAM,UAAU,GACZ,UAAU,IAAI,UAAU,CAAC,IAAI;gBACzB,CAAC,CAAC,UAAU,CAAC,IAAI;gBACjB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,0BAA0B,CACrD,QAAQ,CAAC,qBAAqB;qBACzB,sBAAsB,EAC3B,uBAAuB,CAAC,YAAY,EACpC,uBAAuB,CAAC,YAAY,CACvC,CAAA;YAEX,OAAO,IAAI,+BAAc,CAAC;gBACtB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,gBAAgB,EAAE,uBAAuB;gBACzC,IAAI,EAAE;oBACF,MAAM,EAAE,EAAE;oBACV,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,UAAU;oBACxB,OAAO,EAAE;wBACL,MAAM,EACF,CAAC,uBAAuB,CAAC,MAAM;4BAC/B,CAAC,yBAAW,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;gCACG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;oCAC/B,cAAc,CAAC;4BACvB,qGAAqG;4BACrG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAChC,uBAAuB,CAC1B,KAAK,MAAM;4BACZ,CAAC,uBAAuB,CAAC,kBAAkB;gCACvC,MAAM;gCACN,uBAAuB,CAAC,IAAI,KAAK,MAAM,CAAC;4BACxC,CAAC,CAAC,IAAI;4BACN,CAAC,CAAC,uBAAuB,CAAC,MAAM,EAAE,qDAAqD;wBAC/F,KAAK,EAAE,uBAAuB,CAAC,KAAK,EAAE,qDAAqD;wBAC3F,IAAI,EAAE,uBAAuB,CAAC,IAAI;wBAClC,SAAS,EAAE,uBAAuB,CAAC,SAAS;wBAC5C,KAAK,EAAE,uBAAuB,CAAC,KAAK;wBACpC,OAAO,EAAE,uBAAuB,CAAC,OAAO;wBACxC,SAAS,EAAE,uBAAuB,CAAC,SAAS;wBAC5C,QAAQ,EAAE,uBAAuB,CAAC,QAAQ;wBAC1C,QAAQ,EAAE,uBAAuB,CAAC,QAAQ;4BACtC,CAAC,CAAC,IAAI;4BACN,CAAC,CAAC,uBAAuB,CAAC,QAAQ;wBACtC,IAAI,EAAE,uBAAuB,CAAC,IAAI;wBAClC,QAAQ,EAAE,uBAAuB,CAAC,QAAQ;wBAC1C,wBAAwB,EACpB,UAAU,EAAE,wBAAwB;wBACxC,IAAI,EAAE,UAAU;wBAChB,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,IAAI;qBAChB;iBACJ;aACJ,CAAC,CAAA;QACN,CAAC,CACJ,CAAA;QAED,IAAI,CAAC,2BAA2B,CAC5B,eAAe,EACf,sBAAsB,CACzB,CAAA;QAED,6BAA6B;QAC7B,cAAc,CAAC,YAAY,GAAG,eAAe,CAAA;QAC7C,cAAc,CAAC,cAAc,GAAG,sBAAsB,CAAA;QACtD,cAAc,CAAC,UAAU,GAAG;YACxB,GAAG,eAAe;YAClB,GAAG,sBAAsB;SAC5B,CAAA;QACD,cAAc,CAAC,UAAU,CAAC,OAAO,CAC7B,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,gBAAgB,GAAG,QAAQ,CAAC,CACnD,CAAA;QAED,qCAAqC;QACrC,0DAA0D;QAC1D,oEAAoE;QACpE,cAAc,CAAC,WAAW,GAAG,QAAQ,CAAC,2BAA2B;YAC7D,CAAC,CAAC;gBACI,IAAI,uCAAkB,CAAC;oBACnB,cAAc,EAAE,cAAc;oBAC9B,wBAAwB,EAAE,QAAQ,CAAC,cAAc;oBACjD,OAAO,EAAE,eAAe;oBACxB,iBAAiB,EAAE,iBAAiB;oBACpC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,wBAAwB;oBAClD,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;wBAC7C,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,SAAS;oBACxC,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;wBAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;wBAC7C,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,QAAQ,CAAC,QAAQ,IAAI,SAAS;iBAC3C,CAAC;gBACF,IAAI,uCAAkB,CAAC;oBACnB,cAAc,EAAE,cAAc;oBAC9B,wBAAwB,EAAE,QAAQ,CAAC,qBAAqB;oBACxD,OAAO,EAAE,sBAAsB;oBAC/B,iBAAiB,EAAE,wBAAwB;oBAC3C,IAAI,EAAE,sBAAsB,CAAC,CAAC,CAAC,EAAE,wBAAwB;oBACzD,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;wBAC7C,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,QAAQ,CAAC,eAAe;4BAC1B,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ;4BACnC,CAAC,CAAC,SAAS;oBACnB,QAAQ,EACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;wBAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;wBAC7C,CAAC,CAAC,WAAW;wBACb,CAAC,CAAC,QAAQ,CAAC,eAAe;4BAC1B,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ;4BACnC,CAAC,CAAC,SAAS;iBACtB,CAAC;aACL;YACH,CAAC,CAAC,EAAE,CAAA;QAER,gCAAgC;QAChC,cAAc,CAAC,UAAU,GAAG;YACxB,IAAI,6BAAa,CAAC;gBACd,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,WAAW,EAAE,IAAI;iBACpB;aACJ,CAAC;YAEF,IAAI,6BAAa,CAAC;gBACd,cAAc,EAAE,cAAc;gBAC9B,OAAO,EAAE,sBAAsB;gBAC/B,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,WAAW,EAAE,IAAI;iBACpB;aACJ,CAAC;SACL,CAAA;QAED,iCAAiC;QACjC,OAAO,cAAc,CAAA;IACzB,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,wBAAwB,CAC9B,QAA0B,EAC1B,SAAgC;QAEhC,MAAM,0BAA0B,GAAG,SAAS,CAAC,WAAW;YACpD,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CACtB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CACpD;YACH,CAAC,CAAC,KAAK,CAAA;QACX,IACI,CAAC,SAAS,CAAC,WAAW;YACtB,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC,0BAA0B,CAAC,EACxD,CAAC;YACC,OAAO,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC5C,MAAM,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACzD,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY,KAAK,UAAU,CAAC,oBAAoB,CAC9D,CAAA;gBACD,IAAI,CAAC,gBAAgB;oBACjB,MAAM,IAAI,oBAAY,CAClB,qBAAqB,UAAU,CAAC,oBAAoB,4BAA4B,QAAQ,CAAC,cAAc,CAAC,IAAI,EAAE,CACjH,CAAA;gBAEL,OAAO,gBAAgB,CAAA;YAC3B,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACO,+BAA+B,CACrC,QAA0B,EAC1B,SAAgC;QAEhC,MAAM,qBAAqB,GAAG,CAAC,CAAC,SAAS,CAAC,kBAAkB,CAAA;QAC5D,MAAM,iCAAiC,GAAG,qBAAqB;YAC3D,CAAC,CAAC,SAAS,CAAC,kBAAmB,CAAC,IAAI,CAC9B,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,oBAAoB,CACpD;YACH,CAAC,CAAC,KAAK,CAAA;QACX,IACI,CAAC,qBAAqB;YACtB,CAAC,qBAAqB,IAAI,CAAC,iCAAiC,CAAC,EAC/D,CAAC;YACC,OAAO,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC,kBAAmB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,gBAAgB,GAClB,QAAQ,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAC1C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY;oBACnB,UAAU,CAAC,oBAAoB,CACtC,CAAA;gBACL,IAAI,CAAC,gBAAgB;oBACjB,MAAM,IAAI,oBAAY,CAClB,qBAAqB,UAAU,CAAC,oBAAoB,4BAA4B,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,CACxH,CAAA;gBAEL,OAAO,gBAAgB,CAAA;YAC3B,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAES,2BAA2B,CACjC,eAAiC,EACjC,sBAAwC;QAExC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,sBAAsB,CAAC,OAAO,CAAC,CAAC,qBAAqB,EAAE,EAAE;gBACrD,IACI,cAAc,CAAC,iBAAiB;oBAChC,qBAAqB,CAAC,iBAAiB,EACzC,CAAC;oBACC,MAAM,kBAAkB,GACpB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,gCAAgC,CAC3D,cAAc,CAAC,YAAY,EAC3B,CAAC,CACJ,CAAA;oBACL,cAAc,CAAC,YAAY,GAAG,kBAAkB,CAAA;oBAChD,cAAc,CAAC,iBAAiB,GAAG,kBAAkB,CAAA;oBAErD,MAAM,yBAAyB,GAC3B,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,gCAAgC,CAC3D,qBAAqB,CAAC,YAAY,EAClC,CAAC,CACJ,CAAA;oBACL,qBAAqB,CAAC,YAAY;wBAC9B,yBAAyB,CAAA;oBAC7B,qBAAqB,CAAC,iBAAiB;wBACnC,yBAAyB,CAAA;gBACjC,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AA7XD,sEA6XC", "file": "JunctionEntityMetadataBuilder.js", "sourcesContent": ["import { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ForeignKeyMetadata } from \"../metadata/ForeignKeyMetadata\"\nimport { IndexMetadata } from \"../metadata/IndexMetadata\"\nimport { JoinTableMetadataArgs } from \"../metadata-args/JoinTableMetadataArgs\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\n\n/**\n * Creates EntityMetadata for junction tables.\n * Junction tables are tables generated by many-to-many relations.\n */\nexport class JunctionEntityMetadataBuilder {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(private connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Builds EntityMetadata for the junction of the given many-to-many relation.\n     */\n    build(\n        relation: RelationMetadata,\n        joinTable: JoinTableMetadataArgs,\n    ): EntityMetadata {\n        const referencedColumns = this.collectReferencedColumns(\n            relation,\n            joinTable,\n        )\n        const inverseReferencedColumns = this.collectInverseReferencedColumns(\n            relation,\n            joinTable,\n        )\n\n        const joinTableName =\n            joinTable.name ||\n            this.connection.namingStrategy.joinTableName(\n                relation.entityMetadata.tableNameWithoutPrefix,\n                relation.inverseEntityMetadata.tableNameWithoutPrefix,\n                relation.propertyPath,\n                relation.inverseRelation\n                    ? relation.inverseRelation.propertyName\n                    : \"\",\n            )\n\n        const entityMetadata = new EntityMetadata({\n            connection: this.connection,\n            args: {\n                target: \"\",\n                name: joinTableName,\n                type: \"junction\",\n                database:\n                    joinTable.database || relation.entityMetadata.database,\n                schema: joinTable.schema || relation.entityMetadata.schema,\n                synchronize: joinTable.synchronize,\n            },\n        })\n        entityMetadata.build()\n\n        // create original side junction columns\n        const junctionColumns = referencedColumns.map((referencedColumn) => {\n            const joinColumn = joinTable.joinColumns\n                ? joinTable.joinColumns.find((joinColumnArgs) => {\n                      return (\n                          (!joinColumnArgs.referencedColumnName ||\n                              joinColumnArgs.referencedColumnName ===\n                                  referencedColumn.propertyName) &&\n                          !!joinColumnArgs.name\n                      )\n                  })\n                : undefined\n            const columnName =\n                joinColumn && joinColumn.name\n                    ? joinColumn.name\n                    : this.connection.namingStrategy.joinTableColumnName(\n                          relation.entityMetadata.tableNameWithoutPrefix,\n                          referencedColumn.propertyName,\n                          referencedColumn.databaseName,\n                      )\n\n            return new ColumnMetadata({\n                connection: this.connection,\n                entityMetadata: entityMetadata,\n                referencedColumn: referencedColumn,\n                args: {\n                    target: \"\",\n                    mode: \"virtual\",\n                    propertyName: columnName,\n                    options: {\n                        name: columnName,\n                        length:\n                            !referencedColumn.length &&\n                            (DriverUtils.isMySQLFamily(\n                                this.connection.driver,\n                            ) ||\n                                this.connection.driver.options.type ===\n                                    \"aurora-mysql\") &&\n                            // some versions of mariadb support the column type and should not try to provide the length property\n                            this.connection.driver.normalizeType(\n                                referencedColumn,\n                            ) !== \"uuid\" &&\n                            (referencedColumn.generationStrategy === \"uuid\" ||\n                                referencedColumn.type === \"uuid\")\n                                ? \"36\"\n                                : referencedColumn.length, // fix https://github.com/typeorm/typeorm/issues/3604\n                        width: referencedColumn.width,\n                        type: referencedColumn.type,\n                        precision: referencedColumn.precision,\n                        scale: referencedColumn.scale,\n                        charset: referencedColumn.charset,\n                        collation: referencedColumn.collation,\n                        zerofill: referencedColumn.zerofill,\n                        unsigned: referencedColumn.zerofill\n                            ? true\n                            : referencedColumn.unsigned,\n                        enum: referencedColumn.enum,\n                        enumName: referencedColumn.enumName,\n                        foreignKeyConstraintName:\n                            joinColumn?.foreignKeyConstraintName,\n                        nullable: false,\n                        primary: true,\n                    },\n                },\n            })\n        })\n\n        // create inverse side junction columns\n        const inverseJunctionColumns = inverseReferencedColumns.map(\n            (inverseReferencedColumn) => {\n                const joinColumn = joinTable.inverseJoinColumns\n                    ? joinTable.inverseJoinColumns.find((joinColumnArgs) => {\n                          return (\n                              (!joinColumnArgs.referencedColumnName ||\n                                  joinColumnArgs.referencedColumnName ===\n                                      inverseReferencedColumn.propertyName) &&\n                              !!joinColumnArgs.name\n                          )\n                      })\n                    : undefined\n                const columnName =\n                    joinColumn && joinColumn.name\n                        ? joinColumn.name\n                        : this.connection.namingStrategy.joinTableInverseColumnName(\n                              relation.inverseEntityMetadata\n                                  .tableNameWithoutPrefix,\n                              inverseReferencedColumn.propertyName,\n                              inverseReferencedColumn.databaseName,\n                          )\n\n                return new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    referencedColumn: inverseReferencedColumn,\n                    args: {\n                        target: \"\",\n                        mode: \"virtual\",\n                        propertyName: columnName,\n                        options: {\n                            length:\n                                !inverseReferencedColumn.length &&\n                                (DriverUtils.isMySQLFamily(\n                                    this.connection.driver,\n                                ) ||\n                                    this.connection.driver.options.type ===\n                                        \"aurora-mysql\") &&\n                                // some versions of mariadb support the column type and should not try to provide the length property\n                                this.connection.driver.normalizeType(\n                                    inverseReferencedColumn,\n                                ) !== \"uuid\" &&\n                                (inverseReferencedColumn.generationStrategy ===\n                                    \"uuid\" ||\n                                    inverseReferencedColumn.type === \"uuid\")\n                                    ? \"36\"\n                                    : inverseReferencedColumn.length, // fix https://github.com/typeorm/typeorm/issues/3604\n                            width: inverseReferencedColumn.width, // fix https://github.com/typeorm/typeorm/issues/6442\n                            type: inverseReferencedColumn.type,\n                            precision: inverseReferencedColumn.precision,\n                            scale: inverseReferencedColumn.scale,\n                            charset: inverseReferencedColumn.charset,\n                            collation: inverseReferencedColumn.collation,\n                            zerofill: inverseReferencedColumn.zerofill,\n                            unsigned: inverseReferencedColumn.zerofill\n                                ? true\n                                : inverseReferencedColumn.unsigned,\n                            enum: inverseReferencedColumn.enum,\n                            enumName: inverseReferencedColumn.enumName,\n                            foreignKeyConstraintName:\n                                joinColumn?.foreignKeyConstraintName,\n                            name: columnName,\n                            nullable: false,\n                            primary: true,\n                        },\n                    },\n                })\n            },\n        )\n\n        this.changeDuplicatedColumnNames(\n            junctionColumns,\n            inverseJunctionColumns,\n        )\n\n        // set junction table columns\n        entityMetadata.ownerColumns = junctionColumns\n        entityMetadata.inverseColumns = inverseJunctionColumns\n        entityMetadata.ownColumns = [\n            ...junctionColumns,\n            ...inverseJunctionColumns,\n        ]\n        entityMetadata.ownColumns.forEach(\n            (column) => (column.relationMetadata = relation),\n        )\n\n        // create junction table foreign keys\n        // Note: UPDATE CASCADE clause is not supported in Oracle.\n        // Note: UPDATE/DELETE CASCADE clauses are not supported in Spanner.\n        entityMetadata.foreignKeys = relation.createForeignKeyConstraints\n            ? [\n                  new ForeignKeyMetadata({\n                      entityMetadata: entityMetadata,\n                      referencedEntityMetadata: relation.entityMetadata,\n                      columns: junctionColumns,\n                      referencedColumns: referencedColumns,\n                      name: junctionColumns[0]?.foreignKeyConstraintName,\n                      onDelete:\n                          this.connection.driver.options.type === \"spanner\"\n                              ? \"NO ACTION\"\n                              : relation.onDelete || \"CASCADE\",\n                      onUpdate:\n                          this.connection.driver.options.type === \"oracle\" ||\n                          this.connection.driver.options.type === \"spanner\"\n                              ? \"NO ACTION\"\n                              : relation.onUpdate || \"CASCADE\",\n                  }),\n                  new ForeignKeyMetadata({\n                      entityMetadata: entityMetadata,\n                      referencedEntityMetadata: relation.inverseEntityMetadata,\n                      columns: inverseJunctionColumns,\n                      referencedColumns: inverseReferencedColumns,\n                      name: inverseJunctionColumns[0]?.foreignKeyConstraintName,\n                      onDelete:\n                          this.connection.driver.options.type === \"spanner\"\n                              ? \"NO ACTION\"\n                              : relation.inverseRelation\n                              ? relation.inverseRelation.onDelete\n                              : \"CASCADE\",\n                      onUpdate:\n                          this.connection.driver.options.type === \"oracle\" ||\n                          this.connection.driver.options.type === \"spanner\"\n                              ? \"NO ACTION\"\n                              : relation.inverseRelation\n                              ? relation.inverseRelation.onUpdate\n                              : \"CASCADE\",\n                  }),\n              ]\n            : []\n\n        // create junction table indices\n        entityMetadata.ownIndices = [\n            new IndexMetadata({\n                entityMetadata: entityMetadata,\n                columns: junctionColumns,\n                args: {\n                    target: entityMetadata.target,\n                    synchronize: true,\n                },\n            }),\n\n            new IndexMetadata({\n                entityMetadata: entityMetadata,\n                columns: inverseJunctionColumns,\n                args: {\n                    target: entityMetadata.target,\n                    synchronize: true,\n                },\n            }),\n        ]\n\n        // finally return entity metadata\n        return entityMetadata\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Collects referenced columns from the given join column args.\n     */\n    protected collectReferencedColumns(\n        relation: RelationMetadata,\n        joinTable: JoinTableMetadataArgs,\n    ): ColumnMetadata[] {\n        const hasAnyReferencedColumnName = joinTable.joinColumns\n            ? joinTable.joinColumns.find(\n                  (joinColumn) => !!joinColumn.referencedColumnName,\n              )\n            : false\n        if (\n            !joinTable.joinColumns ||\n            (joinTable.joinColumns && !hasAnyReferencedColumnName)\n        ) {\n            return relation.entityMetadata.columns.filter(\n                (column) => column.isPrimary,\n            )\n        } else {\n            return joinTable.joinColumns.map((joinColumn) => {\n                const referencedColumn = relation.entityMetadata.columns.find(\n                    (column) =>\n                        column.propertyName === joinColumn.referencedColumnName,\n                )\n                if (!referencedColumn)\n                    throw new TypeORMError(\n                        `Referenced column ${joinColumn.referencedColumnName} was not found in entity ${relation.entityMetadata.name}`,\n                    )\n\n                return referencedColumn\n            })\n        }\n    }\n\n    /**\n     * Collects inverse referenced columns from the given join column args.\n     */\n    protected collectInverseReferencedColumns(\n        relation: RelationMetadata,\n        joinTable: JoinTableMetadataArgs,\n    ): ColumnMetadata[] {\n        const hasInverseJoinColumns = !!joinTable.inverseJoinColumns\n        const hasAnyInverseReferencedColumnName = hasInverseJoinColumns\n            ? joinTable.inverseJoinColumns!.find(\n                  (joinColumn) => !!joinColumn.referencedColumnName,\n              )\n            : false\n        if (\n            !hasInverseJoinColumns ||\n            (hasInverseJoinColumns && !hasAnyInverseReferencedColumnName)\n        ) {\n            return relation.inverseEntityMetadata.primaryColumns\n        } else {\n            return joinTable.inverseJoinColumns!.map((joinColumn) => {\n                const referencedColumn =\n                    relation.inverseEntityMetadata.ownColumns.find(\n                        (column) =>\n                            column.propertyName ===\n                            joinColumn.referencedColumnName,\n                    )\n                if (!referencedColumn)\n                    throw new TypeORMError(\n                        `Referenced column ${joinColumn.referencedColumnName} was not found in entity ${relation.inverseEntityMetadata.name}`,\n                    )\n\n                return referencedColumn\n            })\n        }\n    }\n\n    protected changeDuplicatedColumnNames(\n        junctionColumns: ColumnMetadata[],\n        inverseJunctionColumns: ColumnMetadata[],\n    ) {\n        junctionColumns.forEach((junctionColumn) => {\n            inverseJunctionColumns.forEach((inverseJunctionColumn) => {\n                if (\n                    junctionColumn.givenDatabaseName ===\n                    inverseJunctionColumn.givenDatabaseName\n                ) {\n                    const junctionColumnName =\n                        this.connection.namingStrategy.joinTableColumnDuplicationPrefix(\n                            junctionColumn.propertyName,\n                            1,\n                        )\n                    junctionColumn.propertyName = junctionColumnName\n                    junctionColumn.givenDatabaseName = junctionColumnName\n\n                    const inverseJunctionColumnName =\n                        this.connection.namingStrategy.joinTableColumnDuplicationPrefix(\n                            inverseJunctionColumn.propertyName,\n                            2,\n                        )\n                    inverseJunctionColumn.propertyName =\n                        inverseJunctionColumnName\n                    inverseJunctionColumn.givenDatabaseName =\n                        inverseJunctionColumnName\n                }\n            })\n        })\n    }\n}\n"], "sourceRoot": ".."}