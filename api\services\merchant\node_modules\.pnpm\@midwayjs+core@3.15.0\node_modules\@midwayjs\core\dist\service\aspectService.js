"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayAspectService = void 0;
const pm = require("picomatch");
const interface_1 = require("../interface");
const decorator_1 = require("../decorator");
const types_1 = require("../util/types");
let MidwayAspectService = class MidwayAspectService {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
    }
    /**
     * load aspect method for container
     */
    async loadAspect() {
        // for aop implementation
        const aspectModules = (0, decorator_1.listModule)(decorator_1.ASPECT_KEY);
        // sort for aspect target
        let aspectDataList = [];
        for (const module of aspectModules) {
            const data = (0, decorator_1.getClassMetadata)(decorator_1.ASPECT_KEY, module);
            aspectDataList = aspectDataList.concat(data.map(el => {
                el.aspectModule = module;
                return el;
            }));
        }
        // sort priority
        aspectDataList.sort((pre, next) => {
            return (next.priority || 0) - (pre.priority || 0);
        });
        for (const aspectData of aspectDataList) {
            // aspect instance init
            const aspectIns = await this.applicationContext.getAsync(aspectData.aspectModule);
            await this.addAspect(aspectIns, aspectData);
        }
    }
    async addAspect(aspectIns, aspectData) {
        const module = aspectData.aspectTarget;
        const names = Object.getOwnPropertyNames(module.prototype);
        const isMatch = aspectData.match ? pm(aspectData.match) : () => true;
        for (const name of names) {
            if (name === 'constructor' || !isMatch(name)) {
                continue;
            }
            const descriptor = Object.getOwnPropertyDescriptor(module.prototype, name);
            if (!descriptor || descriptor.writable === false) {
                continue;
            }
            this.interceptPrototypeMethod(module, name, aspectIns);
        }
    }
    /**
     * intercept class method in prototype
     * @param Clz class you want to intercept
     * @param methodName method name you want to intercept
     * @param aspectObject aspect object, before, round, etc.
     */
    interceptPrototypeMethod(Clz, methodName, aspectObject) {
        const originMethod = Clz.prototype[methodName];
        if (types_1.Types.isAsyncFunction(Clz.prototype[methodName])) {
            Clz.prototype[methodName] = async function (...args) {
                var _a, _b, _c;
                let error, result;
                const newProceed = (...args) => {
                    return originMethod.apply(this, args);
                };
                const joinPoint = {
                    methodName,
                    target: this,
                    args: args,
                    proceed: newProceed,
                    proceedIsAsyncFunction: true,
                };
                if (typeof aspectObject === 'function') {
                    aspectObject = aspectObject();
                }
                try {
                    await ((_a = aspectObject.before) === null || _a === void 0 ? void 0 : _a.call(aspectObject, joinPoint));
                    if (aspectObject.around) {
                        result = await aspectObject.around(joinPoint);
                    }
                    else {
                        result = await originMethod.call(this, ...joinPoint.args);
                    }
                    joinPoint.proceed = undefined;
                    const resultTemp = await ((_b = aspectObject.afterReturn) === null || _b === void 0 ? void 0 : _b.call(aspectObject, joinPoint, result));
                    result = typeof resultTemp === 'undefined' ? result : resultTemp;
                    return result;
                }
                catch (err) {
                    joinPoint.proceed = undefined;
                    error = err;
                    if (aspectObject.afterThrow) {
                        await aspectObject.afterThrow(joinPoint, error);
                    }
                    else {
                        throw err;
                    }
                }
                finally {
                    await ((_c = aspectObject.after) === null || _c === void 0 ? void 0 : _c.call(aspectObject, joinPoint, result, error));
                }
            };
        }
        else {
            Clz.prototype[methodName] = function (...args) {
                var _a, _b, _c;
                let error, result;
                const newProceed = (...args) => {
                    return originMethod.apply(this, args);
                };
                const joinPoint = {
                    methodName,
                    target: this,
                    args: args,
                    proceed: newProceed,
                    proceedIsAsyncFunction: false,
                };
                if (typeof aspectObject === 'function') {
                    aspectObject = aspectObject();
                }
                try {
                    (_a = aspectObject.before) === null || _a === void 0 ? void 0 : _a.call(aspectObject, joinPoint);
                    if (aspectObject.around) {
                        result = aspectObject.around(joinPoint);
                    }
                    else {
                        result = originMethod.call(this, ...joinPoint.args);
                    }
                    joinPoint.proceed = undefined;
                    const resultTemp = (_b = aspectObject.afterReturn) === null || _b === void 0 ? void 0 : _b.call(aspectObject, joinPoint, result);
                    result = typeof resultTemp === 'undefined' ? result : resultTemp;
                    return result;
                }
                catch (err) {
                    joinPoint.proceed = undefined;
                    error = err;
                    if (aspectObject.afterThrow) {
                        aspectObject.afterThrow(joinPoint, error);
                    }
                    else {
                        throw err;
                    }
                }
                finally {
                    (_c = aspectObject.after) === null || _c === void 0 ? void 0 : _c.call(aspectObject, joinPoint, result, error);
                }
            };
        }
    }
};
MidwayAspectService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayAspectService);
exports.MidwayAspectService = MidwayAspectService;
//# sourceMappingURL=aspectService.js.map