import { MidwayConfigService } from './configService';
import { ServiceFactory } from '../common/serviceFactory';
import { ILogger, IMidwayContainer, IMidwayContext, MidwayLoggerOptions } from '../interface';
import { LoggerFactory } from '../common/loggerFactory';
export declare class MidwayLoggerService extends ServiceFactory<ILogger> {
    readonly applicationContext: IMidwayContainer;
    readonly globalOptions: {};
    configService: MidwayConfigService;
    private loggerFactory;
    private lazyLoggerConfigMap;
    private aliasLoggerMap;
    constructor(applicationContext: IMidwayContainer, globalOptions?: {});
    protected init(): void;
    protected createClient(config: any, name?: string): void;
    getName(): string;
    createLogger(name: string, config: MidwayLoggerOptions): any;
    getLogger(name: string): any;
    getCurrentLoggerFactory(): LoggerFactory<any, any>;
    createContextLogger(ctx: IMidwayContext, appLogger: ILogger, contextOptions?: any): ILogger;
    getClients(): Map<string, ILogger>;
    getClientKeys(): string[];
}
//# sourceMappingURL=loggerService.d.ts.map