{"version": 3, "file": "onErrorResumeNext.js", "sources": ["../../../src/internal/operators/onErrorResumeNext.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAI1C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAE1C,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAmFjG,MAAM,UAAU,iBAAiB,CAAO,GAAG,WACwC;IACjF,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAA2B,WAAW,CAAC,CAAC,CAAC,CAAC;KACtD;IAED,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,CAAC;AAClG,CAAC;AAaD,MAAM,UAAU,uBAAuB,CAAO,GAAG,WAEhB;IAC/B,IAAI,MAAM,GAAmC,SAAS,CAAC;IAEvD,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE;QACvD,WAAW,GAAG,WAAW,CAAC,CAAC,CAA2B,CAAC;KACxD;IAED,MAAM,GAAG,WAAW,CAAC,KAAK,EAAG,CAAC;IAE9B,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,IAAI,yBAAyB,CAAO,WAAW,CAAC,CAAC,CAAC;AAC7E,CAAC;AAED,MAAM,yBAAyB;IAC7B,YAAoB,WAAwC;QAAxC,gBAAW,GAAX,WAAW,CAA6B;IAC5D,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IACzF,CAAC;CACF;AAED,MAAM,2BAAkC,SAAQ,qBAA2B;IACzE,YAAsB,WAA0B,EAC5B,WAAwC;QAC1D,KAAK,CAAC,WAAW,CAAC,CAAC;QAFC,gBAAW,GAAX,WAAW,CAAe;QAC5B,gBAAW,GAAX,WAAW,CAA6B;IAE5D,CAAC;IAED,WAAW;QACT,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,qBAAqB,EAAE,CAAC;IAC/B,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,qBAAqB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,CAAC,IAAI,EAAE;YACV,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;YACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;YACjC,MAAM,iBAAiB,GAAG,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;YAIhE,IAAI,iBAAiB,KAAK,eAAe,EAAE;gBACzC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;aACpC;SACF;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;SAC7B;IACH,CAAC;CACF"}