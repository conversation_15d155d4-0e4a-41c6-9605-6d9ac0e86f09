import { BasePlugin } from '@midwayjs/command-core';
export declare class TestPlugin extends BasePlugin {
    commands: {
        test: {
            usage: string;
            lifecycleEvents: string[];
            options: {
                cov: {
                    usage: string;
                    shortcut: string;
                };
                watch: {
                    usage: string;
                    shortcut: string;
                };
                file: {
                    usage: string;
                    shortcut: string;
                };
                forceExit: {
                    usage: string;
                };
                runInBand: {
                    usage: string;
                };
                mocha: {
                    usage: string;
                };
                ignoreTypeCheck: {
                    usage: string;
                };
            };
        };
        cov: {
            usage: string;
            lifecycleEvents: string[];
            options: {
                watch: {
                    usage: string;
                    shortcut: string;
                };
                file: {
                    usage: string;
                    shortcut: string;
                };
                forceExit: {
                    usage: string;
                };
            };
        };
    };
    hooks: {
        'test:test': any;
        'cov:test': () => Promise<void>;
    };
    run(): Promise<unknown>;
    formatJestTestArgs(isTs: any, testFiles: any): Promise<any[]>;
    formatMochaTestArgs(isTs: any, testFiles: any): Promise<any[]>;
    runTypeCheck(): void;
}
//# sourceMappingURL=index.d.ts.map