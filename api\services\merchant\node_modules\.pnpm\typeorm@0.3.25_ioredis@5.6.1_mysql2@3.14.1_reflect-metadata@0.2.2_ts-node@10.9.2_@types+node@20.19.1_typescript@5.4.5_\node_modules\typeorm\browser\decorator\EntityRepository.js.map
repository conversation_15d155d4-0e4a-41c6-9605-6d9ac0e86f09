{"version": 3, "sources": ["../browser/src/decorator/EntityRepository.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AAInD;;;;;;GAMG;AACH,MAAM,UAAU,gBAAgB,CAC5B,MAAqC;IAErC,OAAO,UAAU,MAAgB;QAC7B,sBAAsB,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;YAC7C,MAAM,EAAE,MAAM;YACd,MAAM,EAAE,MAAM;SACe,CAAC,CAAA;IACtC,CAAC,CAAA;AACL,CAAC", "file": "EntityRepository.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../globals\"\nimport { EntityRepositoryMetadataArgs } from \"../metadata-args/EntityRepositoryMetadataArgs\"\nimport { EntitySchema } from \"../entity-schema/EntitySchema\"\n\n/**\n * Used to declare a class as a custom repository.\n * Custom repository can manage some specific entity or just be generic.\n * Custom repository optionally can extend AbstractRepository, Repository or TreeRepository.\n *\n * @deprecated use Repository.extend function to create a custom repository\n */\nexport function EntityRepository(\n    entity?: Function | EntitySchema<any>,\n): ClassDecorator {\n    return function (target: Function) {\n        getMetadataArgsStorage().entityRepositories.push({\n            target: target,\n            entity: entity,\n        } as EntityRepositoryMetadataArgs)\n    }\n}\n"], "sourceRoot": ".."}