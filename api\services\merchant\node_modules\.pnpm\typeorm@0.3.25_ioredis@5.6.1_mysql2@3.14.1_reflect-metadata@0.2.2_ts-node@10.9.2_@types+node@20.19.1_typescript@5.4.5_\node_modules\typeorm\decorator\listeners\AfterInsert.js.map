{"version": 3, "sources": ["../../src/decorator/listeners/AfterInsert.ts"], "names": [], "mappings": ";;AAOA,kCAQC;AAfD,2CAAsD;AACtD,gFAA4E;AAG5E;;GAEG;AACH,SAAgB,WAAW;IACvB,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,uCAAkB,CAAC,YAAY;SACV,CAAC,CAAA;IACpC,CAAC,CAAA;AACL,CAAC", "file": "AfterInsert.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { EventListenerTypes } from \"../../metadata/types/EventListenerTypes\"\nimport { EntityListenerMetadataArgs } from \"../../metadata-args/EntityListenerMetadataArgs\"\n\n/**\n * Calls a method on which this decorator is applied after this entity insertion.\n */\nexport function AfterInsert(): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().entityListeners.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            type: EventListenerTypes.AFTER_INSERT,\n        } as EntityListenerMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}