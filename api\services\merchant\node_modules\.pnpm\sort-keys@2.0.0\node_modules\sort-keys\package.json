{"name": "sort-keys", "version": "2.0.0", "description": "Sort the keys of an object", "license": "MIT", "repository": "sindresorhus/sort-keys", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["sort", "object", "keys", "obj", "key", "stable", "deterministic", "deep", "recursive", "recursively"], "dependencies": {"is-plain-obj": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}