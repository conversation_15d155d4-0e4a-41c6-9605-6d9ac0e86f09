import { IMidwayApplication } from "@midwayjs/core";
/**
 * 模块配置
 */
export declare class CoolModuleConfig {
    app: IMidwayApplication;
    allConfig: any;
    modules: any;
    init(): Promise<void>;
    /**
     * 模块配置
     * @param module 模块
     * @param config 配置
     */
    moduleConfig(module: any, config: any): Promise<void>;
    /**
     * 全局中间件
     * @param middleware 中间件
     */
    globalMiddlewareArr(middlewares: any[]): Promise<void>;
}
