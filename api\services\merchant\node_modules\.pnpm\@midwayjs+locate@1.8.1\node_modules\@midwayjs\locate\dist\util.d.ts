export declare const exists: (file: string) => Promise<any>;
export declare const safeReadJSON: (file: string) => Promise<any>;
export declare const safeGetProperty: (json: object, property: string | string[]) => any;
export declare const propertyExists: (json: object, properties: string[]) => boolean;
export declare const filterModule: (module: string, modules: Set<string>) => void;
export declare const findDependenciesByAST: (source: string, jsx?: boolean) => string[];
export declare const findFile: (files: string[]) => Promise<string>;
export declare const findCommonDir: (files: string[]) => string;
