-- 修复 merchant_sys_menu 表缺少 component 字段的问题
-- 这是导致404错误的根本原因

USE `merchant_service_db`;

-- 第一步：添加 component 字段
ALTER TABLE merchant_sys_menu 
ADD COLUMN component VARCHAR(200) NULL COMMENT '组件路径' 
AFTER router;

-- 第二步：为现有菜单设置正确的 component 值
-- 根据 router 路径设置对应的 RoutesAlias key

-- 仪表盘相关
UPDATE merchant_sys_menu SET component = 'Dashboard' WHERE router = '/dashboard/console';
UPDATE merchant_sys_menu SET component = 'Analysis' WHERE router = '/dashboard/analysis';
UPDATE merchant_sys_menu SET component = 'Ecommerce' WHERE router = '/dashboard/ecommerce';

-- 商户管理相关
UPDATE merchant_sys_menu SET component = 'MerchantSettleIn' WHERE router = '/merchant/settle-in';
UPDATE merchant_sys_menu SET component = 'CreatorHeritageAuth' WHERE router = '/merchant/heritage-auth';
UPDATE merchant_sys_menu SET component = 'MerchantPersonal' WHERE router = '/merchant/personal';
UPDATE merchant_sys_menu SET component = 'MerchantCompany' WHERE router = '/merchant/company';

-- 监控相关（重点修复）
UPDATE merchant_sys_menu SET component = 'MonitorDashboard' WHERE router = '/monitor/dashboard';
UPDATE merchant_sys_menu SET component = 'MonitorRiskAlert' WHERE router = '/monitor/risk-alert';

-- 数据分析相关
UPDATE merchant_sys_menu SET component = 'AnalyticsOverview' WHERE router = '/analytics/overview';

-- 财务结算相关
UPDATE merchant_sys_menu SET component = 'SettlementRules' WHERE router = '/settlement/rules';
UPDATE merchant_sys_menu SET component = 'SettlementBatch' WHERE router = '/settlement/batch-settlement';

-- 系统管理相关
UPDATE merchant_sys_menu SET component = 'SystemUser' WHERE router = '/system/user';
UPDATE merchant_sys_menu SET component = 'SystemRole' WHERE router = '/system/role';
UPDATE merchant_sys_menu SET component = 'SystemMenu' WHERE router = '/system/menu';

-- 异常页面相关
UPDATE merchant_sys_menu SET component = 'Exception403' WHERE router = '/exception/403';
UPDATE merchant_sys_menu SET component = 'Exception404' WHERE router = '/exception/404';
UPDATE merchant_sys_menu SET component = 'Exception500' WHERE router = '/exception/500';

-- 第三步：验证修复结果
SELECT 
    id,
    name,
    router,
    component,
    type,
    parentId,
    isShow
FROM merchant_sys_menu 
WHERE router IN (
    '/monitor/dashboard',
    '/monitor/risk-alert',
    '/analytics/overview',
    '/settlement/rules',
    '/settlement/batch-settlement'
)
ORDER BY id;

-- 显示所有菜单的组件映射情况
SELECT 
    CASE 
        WHEN parentId = 0 THEN CONCAT('├── ', name)
        ELSE CONCAT('│   ├── ', name)
    END AS menu_tree,
    router,
    component,
    CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' WHEN 2 THEN '按钮' END AS type_name
FROM merchant_sys_menu 
WHERE isShow = 1
ORDER BY parentId, orderNum, id;
