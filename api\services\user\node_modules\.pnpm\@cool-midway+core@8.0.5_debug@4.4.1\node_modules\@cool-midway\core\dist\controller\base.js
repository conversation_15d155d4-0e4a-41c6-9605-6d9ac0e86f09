"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseController = void 0;
const core_1 = require("@midwayjs/core");
const global_1 = require("../constant/global");
const typeorm_1 = require("@midwayjs/typeorm");
const validate_1 = require("../exception/validate");
/**
 * 控制器基类
 */
let BaseController = class BaseController {
    async init() {
        const option = (0, core_1.getClassMetadata)(core_1.CONTROLLER_KEY, this);
        this.service = await this.baseCtx.requestContext.getAsync('baseService');
        const curdOption = option.curdOption;
        this.curdOption = curdOption;
        if (!this.curdOption) {
            return;
        }
        // 操作之前
        await this.before(curdOption);
        // 设置service
        await this.setService(curdOption);
        // 设置实体
        await this.setEntity(curdOption);
        // 创建动态方法
        await this.createDynamicMethods(curdOption);
    }
    /**
     * 获取用户ID
     * @param type 类型
     * @returns
     */
    getUserId(type = 'admin') {
        var _a, _b;
        return type === 'admin'
            ? (_a = this.baseCtx.admin) === null || _a === void 0 ? void 0 : _a.userId
            : (_b = this.baseCtx.user) === null || _b === void 0 ? void 0 : _b.id;
    }
    /**
     * 创建动态方法
     * @param curdOption 配置
     */
    async createDynamicMethods(curdOption) {
        if (!curdOption.serviceApis) {
            return;
        }
        // 过滤出非标准方法
        const customMethods = curdOption.serviceApis;
        // 为每个自定义方法创建对应的控制器方法
        for (const api of customMethods) {
            const methodName = typeof api === 'string' ? api : api.method;
            if (this[methodName]) {
                continue; // 如果方法已存在则跳过
            }
            this[methodName] = async function () {
                const { body } = this.baseCtx.request;
                const serviceMethod = this.service[methodName];
                if (typeof serviceMethod !== 'function') {
                    throw new validate_1.CoolValidateException(`Service method ${methodName} not found`);
                }
                return this.ok(await serviceMethod.call(this.service, body));
            };
        }
    }
    async before(curdOption) {
        if (!(curdOption === null || curdOption === void 0 ? void 0 : curdOption.before)) {
            return;
        }
        await curdOption.before(this.baseCtx, this.baseApp);
    }
    /**
     * 插入参数值
     * @param curdOption 配置
     */
    async insertParam(curdOption) {
        if (!(curdOption === null || curdOption === void 0 ? void 0 : curdOption.insertParam)) {
            return;
        }
        const body = this.baseCtx.request.body;
        if (body) {
            // 判断body是否是数组
            if (Array.isArray(body)) {
                for (let i = 0; i < body.length; i++) {
                    body[i] = {
                        ...body[i],
                        ...(await curdOption.insertParam(this.baseCtx, this.baseApp)),
                    };
                }
                this.baseCtx.request.body = body;
                return;
            }
            this.baseCtx.request.body = {
                // @ts-ignore
                ...this.baseCtx.request.body,
                ...(await curdOption.insertParam(this.baseCtx, this.baseApp)),
            };
        }
    }
    /**
     * 设置实体
     * @param curdOption 配置
     */
    async setEntity(curdOption) {
        const entity = curdOption === null || curdOption === void 0 ? void 0 : curdOption.entity;
        if (entity) {
            const dataSourceName = this.typeORMDataSourceManager.getDataSourceNameByModel(entity);
            this.connectionName = dataSourceName;
            const entityModel = this.typeORMDataSourceManager
                .getDataSource(dataSourceName)
                .getRepository(entity);
            this.service.setEntity(entityModel);
        }
    }
    /**
     * 设置service
     * @param curdOption
     */
    async setService(curdOption) {
        if (curdOption.service) {
            this.service = await this.baseCtx.requestContext.getAsync(curdOption.service);
        }
    }
    /**
     * 新增
     * @returns
     */
    async add() {
        // 插入参数
        await this.insertParam(this.curdOption);
        const { body } = this.baseCtx.request;
        return this.ok(await this.service.add(body));
    }
    /**
     * 删除
     * @returns
     */
    async delete() {
        // @ts-ignore
        const { ids } = this.baseCtx.request.body;
        return this.ok(await this.service.delete(ids));
    }
    /**
     * 更新
     * @returns
     */
    async update() {
        const { body } = this.baseCtx.request;
        return this.ok(await this.service.update(body));
    }
    /**
     * 分页查询
     * @returns
     */
    async page() {
        const { body } = this.baseCtx.request;
        return this.ok(await this.service.page(body, this.curdOption.pageQueryOp, this.connectionName));
    }
    /**
     * 列表查询
     * @returns
     */
    async list() {
        const { body } = this.baseCtx.request;
        return this.ok(await this.service.list(body, this.curdOption.listQueryOp, this.connectionName));
    }
    /**
     * 根据ID查询信息
     * @returns
     */
    async info() {
        const { id } = this.baseCtx.query;
        return this.ok(await this.service.info(id, this.curdOption.infoIgnoreProperty));
    }
    /**
     * 成功返回
     * @param data 返回数据
     */
    ok(data) {
        const { RESCODE, RESMESSAGE } = global_1.GlobalConfig.getInstance();
        const res = {
            code: RESCODE.SUCCESS,
            message: RESMESSAGE.SUCCESS,
        };
        if (data || data == 0) {
            res['data'] = data;
        }
        return res;
    }
    /**
     * 失败返回
     * @param message
     */
    fail(message, code) {
        const { RESCODE, RESMESSAGE } = global_1.GlobalConfig.getInstance();
        return {
            code: code ? code : RESCODE.COMMFAIL,
            message: message
                ? message
                : code == RESCODE.VALIDATEFAIL
                    ? RESMESSAGE.VALIDATEFAIL
                    : RESMESSAGE.COMMFAIL,
        };
    }
};
exports.BaseController = BaseController;
__decorate([
    (0, core_1.Inject)('ctx'),
    __metadata("design:type", Object)
], BaseController.prototype, "baseCtx", void 0);
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], BaseController.prototype, "baseApp", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], BaseController.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BaseController.prototype, "init", null);
exports.BaseController = BaseController = __decorate([
    (0, core_1.Provide)()
], BaseController);
