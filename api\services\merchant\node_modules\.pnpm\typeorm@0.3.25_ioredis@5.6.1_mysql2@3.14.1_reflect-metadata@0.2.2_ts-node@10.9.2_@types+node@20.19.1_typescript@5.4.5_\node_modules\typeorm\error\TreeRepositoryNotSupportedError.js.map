{"version": 3, "sources": ["../../src/error/TreeRepositoryNotSupportedError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C,MAAa,+BAAgC,SAAQ,2BAAY;IAC7D,YAAY,MAAc;QACtB,KAAK,CACD,0CAA0C,MAAM,CAAC,OAAO,CAAC,IAAI,UAAU,CAC1E,CAAA;IACL,CAAC;CACJ;AAND,0EAMC", "file": "TreeRepositoryNotSupportedError.js", "sourcesContent": ["import { Driver } from \"../driver/Driver\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class TreeRepositoryNotSupportedError extends TypeORMError {\n    constructor(driver: Driver) {\n        super(\n            `Tree repositories are not supported in ${driver.options.type} driver.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}