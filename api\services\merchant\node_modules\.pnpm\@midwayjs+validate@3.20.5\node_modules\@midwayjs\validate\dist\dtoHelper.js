"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OmitDto = exports.PickDto = void 0;
const core_1 = require("@midwayjs/core");
const constants_1 = require("./constants");
function PickDto(dto, keys) {
    const pickedDto = function () { };
    pickedDto.prototype = dto.prototype;
    const fatherRule = (0, core_1.getClassExtendedMetadata)(constants_1.RULES_KEY, dto);
    const pickedRule = {};
    for (const key of keys) {
        if (fatherRule[key]) {
            pickedRule[key] = fatherRule[key];
        }
    }
    (0, core_1.saveClassMetadata)(constants_1.RULES_KEY, pickedRule, pickedDto);
    return pickedDto;
}
exports.PickDto = PickDto;
function OmitDto(dto, keys) {
    const pickedDto = function () { };
    pickedDto.prototype = dto.prototype;
    const fatherRule = (0, core_1.getClassExtendedMetadata)(constants_1.RULES_KEY, dto);
    const pickedRule = Object.assign({}, fatherRule);
    for (const key of keys) {
        delete pickedRule[key];
    }
    (0, core_1.saveClassMetadata)(constants_1.RULES_KEY, pickedRule, pickedDto);
    return pickedDto;
}
exports.OmitDto = OmitDto;
//# sourceMappingURL=dtoHelper.js.map