{"name": "mwts", "version": "1.3.0", "description": "MidwayJS TypeScript Style", "repository": "midwayjs/mwts", "main": "dist/src/index.js", "bin": {"mwts": "dist/src/cli.js"}, "files": ["CHANGELOG.md", "dist/src", "dist/template", "dist/.eslintrc.json", ".prettierrc.json", ".eslintrc.json", "LICENSE", "tsconfig-midway.json", "tsconfig.json"], "scripts": {"build": "tsc", "clean": "rimraf ./dist/", "codecov": "c8 report --reporter=json && codecov -f coverage/*.json", "postbuild": "ncp template dist/template", "lint": "eslint '**/*.ts'", "prepare": "npm run build", "test": "c8 mocha dist/test/test-*.js", "system-test": "c8 mocha dist/test/kitchen.js", "pretest": "npm run build", "presystem-test": "npm run build", "fix": "eslint --fix '**/*.ts'"}, "engines": {"node": ">=10"}, "keywords": [], "license": "Apache-2.0", "dependencies": {"@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "chalk": "^4.1.0", "eslint": "^7.10.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "execa": "^5.0.0", "inquirer": "^7.3.3", "json5": "^2.1.3", "meow": "^9.0.0", "ncp": "^2.0.0", "prettier": "^2.1.2", "rimraf": "^3.0.2", "update-notifier": "^5.0.0", "write-file-atomic": "^3.0.3"}, "devDependencies": {"@npm/types": "^1.0.1", "@types/cross-spawn": "^6.0.2", "@types/eslint": "^7.2.3", "@types/fs-extra": "^9.0.1", "@types/inquirer": "^7.3.1", "@types/json5": "0.0.30", "@types/mocha": "^8.0.3", "@types/ncp": "^2.0.4", "@types/node": "^14.11.2", "@types/prettier": "^2.1.1", "@types/rimraf": "^3.0.0", "@types/sinon": "^9.0.6", "@types/tmp": "^0.2.0", "@types/update-notifier": "^5.0.0", "@types/write-file-atomic": "^3.0.1", "c8": "^7.3.1", "cross-spawn": "^7.0.3", "fs-extra": "^9.0.1", "inline-fixtures": "^1.1.0", "js-green-licenses": "^3.0.0", "mocha": "^8.1.3", "sinon": "^9.0.3", "tmp": "0.2.1", "typescript": "~4.5.2"}, "peerDependencies": {"typescript": ">=3"}}