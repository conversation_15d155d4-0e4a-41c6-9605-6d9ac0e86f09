export * from './interface';
export * from './builder';
export * from './wrapper';
export { filterUserDefinedEnv, getFaaSPackageVersion } from './utils';
export declare const transform: (sourcefilePathOrJson: any, builderCls?: any) => any;
export { saveYaml, parse } from './parse';
export declare const generate: (sourceFilePathOrJson: any, targetFilePath: string, builderCls?: any) => void;
export declare const getSpecFile: (baseDir: any) => {
    type: string;
    path: string;
} | {
    type?: undefined;
    path?: undefined;
};
export declare const loadSpec: (baseDir: any, specFileInfo?: any) => any;
export declare const writeToSpec: (baseDir: any, specResult: any, specFileInfo?: any) => void | {};
//# sourceMappingURL=index.d.ts.map