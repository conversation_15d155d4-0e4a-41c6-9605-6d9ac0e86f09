"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolModuleImport = void 0;
const core_1 = require("@midwayjs/core");
const decorator_1 = require("@midwayjs/decorator");
const typeorm_1 = require("@midwayjs/typeorm");
const fs = require("fs");
const _ = require("lodash");
const path = require("path");
const typeorm_2 = require("typeorm");
const event_1 = require("../event");
const config_1 = require("./config");
const menu_1 = require("./menu");
/**
 * 模块sql
 */
let CoolModuleImport = class CoolModuleImport {
    /**
     * 初始化
     */
    async init() {
        this.initJudge = this.coolConfig.initJudge;
        if (!this.initJudge) {
            this.initJudge = "file";
        }
        // 是否需要导入
        if (this.coolConfig.initDB) {
            const modules = this.coolModuleConfig.modules;
            const metadatas = await this.getDbMetadatas();
            setTimeout(async () => {
                for (const module of modules) {
                    if (this.initJudge == "file") {
                        const { exist, lockPath } = this.checkFileExist(module);
                        if (!exist) {
                            await this.initDataBase(module, metadatas, lockPath);
                        }
                    }
                    if (this.initJudge == "db") {
                        const exist = await this.checkDbExist(module, metadatas);
                        if (!exist) {
                            await this.initDataBase(module, metadatas);
                        }
                    }
                }
                this.coolEventManager.emit("onDBInit", {});
                this.coolModuleMenu.init();
            }, 2000);
        }
    }
    /**
     * 获取数据库元数据
     */
    async getDbMetadatas() {
        // 获得所有的实体
        const entityMetadatas = this.defaultDataSource.entityMetadatas;
        const metadatas = _.mapValues(_.keyBy(entityMetadatas, "tableName"), "target");
        return metadatas;
    }
    /**
     * 检查数据是否存在
     * @param module
     * @param metadatas
     */
    async checkDbExist(module, metadatas) {
        const cKey = `init_db_${module}`;
        const repository = this.defaultDataSource.getRepository(metadatas["base_sys_conf"]);
        const data = await repository.findOneBy({ cKey: (0, typeorm_2.Equal)(cKey) });
        return !!data;
    }
    /**
     * 检查文件是否存在
     * @param module
     */
    checkFileExist(module) {
        const importLockPath = path.join(`${this.app.getBaseDir()}`, "..", "lock", "db");
        if (!fs.existsSync(importLockPath)) {
            fs.mkdirSync(importLockPath, { recursive: true });
        }
        const lockPath = path.join(importLockPath, module + ".db.lock");
        return {
            exist: fs.existsSync(lockPath),
            lockPath,
        };
    }
    /**
     * 导入数据库
     * @param module
     * @param lockPath 锁定导入
     */
    async initDataBase(module, metadatas, lockPath) {
        // 计算耗时
        const startTime = new Date().getTime();
        // 模块路径
        const modulePath = `${this.app.getBaseDir()}/modules/${module}`;
        // 数据路径
        const dataPath = `${modulePath}/db.json`;
        // 判断文件是否存在
        if (fs.existsSync(dataPath)) {
            // 读取数据
            const data = JSON.parse(fs.readFileSync(dataPath).toString() || "{}");
            // 导入数据
            for (const key in data) {
                try {
                    for (const item of data[key]) {
                        await this.importData(metadatas, item, key);
                    }
                }
                catch (e) {
                    this.coreLogger.error("\x1B[36m [cool:core] midwayjs cool core init " +
                        module +
                        " database err \x1B[0m");
                    continue;
                }
            }
            const endTime = new Date().getTime();
            await this.lockImportData(module, metadatas, lockPath, endTime - startTime);
            this.coreLogger.info("\x1B[36m [cool:core] midwayjs cool core init " +
                module +
                " database complete \x1B[0m");
        }
    }
    /**
     * 锁定导入
     * @param module
     * @param metadatas
     * @param lockPath
     * @param time
     */
    async lockImportData(module, metadatas, lockPath, time) {
        if (this.initJudge == "file") {
            fs.writeFileSync(lockPath, `time consuming：${time}ms`);
        }
        if (this.initJudge == "db") {
            const repository = this.defaultDataSource.getRepository(metadatas["base_sys_conf"]);
            if (this.ormConfig.default.type == "postgres") {
                await repository.save(repository.create({
                    cKey: `init_db_${module}`,
                    cValue: `time consuming：${time}ms`,
                }));
            }
            else {
                await repository.insert({
                    cKey: `init_db_${module}`,
                    cValue: `time consuming：${time}ms`,
                });
            }
        }
    }
    /**
     * 导入数据
     * @param metadatas
     * @param datas
     * @param tableName
     */
    async importData(metadatas, item, tableName, parentItem = null) {
        const repository = this.defaultDataSource.getRepository(metadatas[tableName]);
        // 处理当前项中的引用
        if (parentItem) {
            for (const key in item) {
                if (typeof item[key] === "string" && item[key].startsWith("@")) {
                    const parentKey = item[key].substring(1); // 移除"@"符号
                    if (parentItem.hasOwnProperty(parentKey)) {
                        item[key] = parentItem[parentKey];
                    }
                }
            }
        }
        // 插入当前项到数据库
        let insertedItem;
        if (this.ormConfig.default.type == "postgres") {
            insertedItem = await repository.save(repository.create(item));
            if (item.id) {
                await repository.update(insertedItem.id, { id: item.id });
                await this.defaultDataSource.query(`SELECT setval('${tableName}_id_seq', (SELECT MAX(id) FROM ${tableName}));`);
            }
        }
        else {
            insertedItem = await repository.insert(item);
        }
        // 递归处理@childDatas
        if (!_.isEmpty(item["@childDatas"])) {
            const childDatas = item["@childDatas"];
            delete item["@childDatas"];
            for (const childKey in childDatas) {
                for (const childItem of childDatas[childKey]) {
                    await this.importData(metadatas, childItem, childKey, item);
                }
            }
        }
    }
};
exports.CoolModuleImport = CoolModuleImport;
__decorate([
    (0, decorator_1.Config)("typeorm.dataSource"),
    __metadata("design:type", Object)
], CoolModuleImport.prototype, "ormConfig", void 0);
__decorate([
    (0, typeorm_1.InjectDataSource)("default"),
    __metadata("design:type", typeorm_2.DataSource)
], CoolModuleImport.prototype, "defaultDataSource", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolModuleImport.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, decorator_1.Config)("cool"),
    __metadata("design:type", Object)
], CoolModuleImport.prototype, "coolConfig", void 0);
__decorate([
    (0, decorator_1.Logger)(),
    __metadata("design:type", Object)
], CoolModuleImport.prototype, "coreLogger", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", config_1.CoolModuleConfig)
], CoolModuleImport.prototype, "coolModuleConfig", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", event_1.CoolEventManager)
], CoolModuleImport.prototype, "coolEventManager", void 0);
__decorate([
    (0, decorator_1.App)(),
    __metadata("design:type", Object)
], CoolModuleImport.prototype, "app", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", menu_1.CoolModuleMenu)
], CoolModuleImport.prototype, "coolModuleMenu", void 0);
exports.CoolModuleImport = CoolModuleImport = __decorate([
    (0, decorator_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolModuleImport);
