{"name": "@midwayjs/validate", "version": "3.20.5", "description": "Midway Component for mongoose", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test"}, "keywords": [], "author": "<EMAIL>", "files": ["dist/**/*.js", "dist/**/*.d.ts", "locales", "index.d.ts"], "engines": {"node": ">=12"}, "license": "MIT", "dependencies": {"@midwayjs/i18n": "^3.20.5", "joi": "17.13.3"}, "devDependencies": {"@midwayjs/core": "^3.20.4", "@midwayjs/express": "^3.20.4", "@midwayjs/koa": "^3.20.5", "@midwayjs/mock": "^3.20.4"}, "gitHead": "7ce57281bd3ef5d18dc50b47ff9bffb8a27c071e"}