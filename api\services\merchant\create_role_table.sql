-- 商户微服务角色表创建脚本
-- 根据前端 Role 接口和后端 MerchantSysRoleEntity 实体类设计
-- 解决角色列表API失败问题

USE `merchant_service_db`;

-- ======================================
-- 创建角色表结构
-- ======================================
DROP TABLE IF EXISTS `merchant_sys_role`;
CREATE TABLE `merchant_sys_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(100) NOT NULL COMMENT '角色名称 (对应前端 roleName)',
  `label` varchar(100) NOT NULL COMMENT '角色标识 (对应前端 roleCode)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注描述 (对应前端 des)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用 (对应前端 enable)',
  `relevance` int(11) NOT NULL DEFAULT '1' COMMENT '数据权限',
  `menuIdList` text COMMENT '菜单权限ID列表 (JSON数组字符串)',
  `departmentIdList` text COMMENT '部门权限ID列表 (JSON数组字符串)',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_label` (`label`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统角色表';

-- ======================================
-- 插入基础角色数据 (与前端接口格式匹配)
-- ======================================

-- 超级管理员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  1, 
  '超级管理员', 
  'admin', 
  '系统超级管理员，拥有所有权限', 
  1, 
  1, 
  '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]', 
  '[]'
);

-- 商户管理员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  2, 
  '商户管理员', 
  'merchant_admin', 
  '商户管理员，负责商户相关业务管理', 
  1, 
  2, 
  '[1,5,6,7,8,9]', 
  '[]'
);

-- 系统管理员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  3, 
  '系统管理员', 
  'system_admin', 
  '系统管理员，负责系统配置和用户管理', 
  1, 
  2, 
  '[1,10,11,12,13,14,15]', 
  '[]'
);

-- 普通操作员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  4, 
  '普通操作员', 
  'operator', 
  '普通操作员，只有基础查看和操作权限', 
  1, 
  3, 
  '[1,2,3,4]', 
  '[]'
);

-- 客服人员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  5, 
  '客服人员', 
  'customer_service', 
  '客服人员，负责商户咨询和基础服务', 
  1, 
  3, 
  '[1,5,6]', 
  '[]'
);

-- 财务人员角色
INSERT INTO `merchant_sys_role` (
  `id`, `name`, `label`, `remark`, `status`, `relevance`, 
  `menuIdList`, `departmentIdList`
) VALUES (
  6, 
  '财务人员', 
  'finance', 
  '财务人员，负责财务相关功能', 
  1, 
  3, 
  '[1,16,17,18]', 
  '[]'
);

-- ======================================
-- 验证数据和显示结果
-- ======================================
SELECT '======== 角色表创建完成 ========' AS message;

-- 统计角色数量
SELECT 
    '角色数据统计' AS info,
    COUNT(*) AS total_roles,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) AS enabled_roles,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS disabled_roles
FROM merchant_sys_role;

-- 显示所有角色数据
SELECT 
    id,
    name AS '角色名称',
    label AS '角色标识', 
    remark AS '描述',
    CASE status WHEN 1 THEN '启用' ELSE '禁用' END AS '状态',
    relevance AS '数据权限级别',
    createTime AS '创建时间'
FROM merchant_sys_role
ORDER BY id;

SELECT '======== 角色表初始化成功 ========' AS message;
SELECT '前端调用格式：' AS info;
SELECT 'POST /api/admin/merchant/role/page' AS api_endpoint;
SELECT '数据格式兼容：roleName, roleCode, des, enable, menuIdList' AS data_format; 