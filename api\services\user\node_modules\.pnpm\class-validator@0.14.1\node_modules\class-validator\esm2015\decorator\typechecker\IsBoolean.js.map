{"version": 3, "file": "IsBoolean.js", "sourceRoot": "", "sources": ["../../../../src/decorator/typechecker/IsBoolean.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,MAAM,UAAU,GAAG,WAAW,CAAC;AAEtC;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACtC,OAAO,KAAK,YAAY,OAAO,IAAI,OAAO,KAAK,KAAK,SAAS,CAAC;AAChE,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;YACpD,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,mCAAmC,EAAE,iBAAiB,CAAC;SAChH;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_BOOLEAN = 'isBoolean';\n\n/**\n * Checks if a given value is a boolean.\n */\nexport function isBoolean(value: unknown): value is boolean {\n  return value instanceof Boolean || typeof value === 'boolean';\n}\n\n/**\n * Checks if a value is a boolean.\n */\nexport function IsBoolean(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BOOLEAN,\n      validator: {\n        validate: (value, args): boolean => isBoolean(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a boolean value', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}