"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Controller = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Controller(prefix = '/', routerOptions = { middleware: [], sensitive: true }) {
    return (target) => {
        (0, __1.saveModule)(__1.CONTROLLER_KEY, target);
        if (prefix)
            (0, __1.saveClassMetadata)(__1.CONTROLLER_KEY, {
                prefix,
                routerOptions,
            }, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Controller = Controller;
//# sourceMappingURL=controller.js.map