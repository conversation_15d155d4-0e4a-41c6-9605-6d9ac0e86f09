import { BasePlugin } from '@midwayjs/command-core';
export declare class BuildPlugin extends BasePlugin {
    isMidwayHooks: boolean;
    private midwayBinBuild;
    private midwayCliConfig;
    commands: {
        build: {
            lifecycleEvents: string[];
            options: {
                clean: {
                    usage: string;
                    shortcut: string;
                };
                project: {
                    usage: string;
                    shortcut: string;
                };
                srcDir: {
                    usage: string;
                };
                outDir: {
                    usage: string;
                };
                tsConfig: {
                    usage: string;
                };
                buildCache: {
                    usage: string;
                };
                exclude: {
                    usage: string;
                };
                include: {
                    usage: string;
                };
                bundle: {
                    usage: string;
                };
            };
        };
    };
    hooks: {
        'build:formatOptions': any;
        'build:clean': any;
        'build:copyFile': any;
        'build:compile': any;
        'build:bundle': any;
        'build:complete': any;
    };
    formatOptions(): Promise<void>;
    clean(): Promise<void>;
    private getOutDir;
    copyFile(): Promise<void>;
    compile(): Promise<void>;
    private getCompilerOptions;
    private getProjectFile;
    private getTsCodeRoot;
    private getTsConfig;
    bundle(): Promise<void>;
    complete(): Promise<void>;
}
//# sourceMappingURL=index.d.ts.map