{"version": 3, "file": "builder.js", "sourceRoot": "", "sources": ["../../src/fc/builder.ts"], "names": [], "mappings": ";;;AAAA,wCAAyC;AAQzC,oCAKkB;AAWlB,MAAa,aAAc,SAAQ,qBAAW;IAC5C,MAAM;;QACJ,MAAM,YAAY,GAAwB,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,MAAM,aAAa,GAAyB,IAAI,CAAC,YAAY,EAAE,CAAC;QAChE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;QACrC,MAAM,cAAc,GAAG,IAAA,4BAAoB,GAAE,CAAC;QAE9C,MAAM,QAAQ,GAAW;YACvB,wBAAwB,EAAE,YAAY;YACtC,SAAS,EAAE,+BAA+B;YAC1C,SAAS,EAAE;gBACT,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE;oBAClB,IAAI,EAAE,6BAA6B;oBACnC,UAAU,EAAE;wBACV,WAAW,EAAE,WAAW,CAAC,WAAW;wBACpC,IAAI,EAAE,YAAY,CAAC,IAAI;wBACvB,cAAc,EAAE,YAAY,CAAC,cAAc;wBAC3C,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;wBACrD,QAAQ,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,QAAQ,CAAC;wBACnD,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;wBACrD,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;wBACrD,kBAAkB,EAAE,IAAA,0BAAkB,EACpC,YAAY,CAAC,kBAAkB,CAChC;wBACD,aAAa,EAAE,YAAY,CAAC,aAAa;qBAC1C;iBACF;aACF;SACF,CAAC;QAEF,IAAI,gBAAgB,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;YACnC,MAAM,OAAO,GAAwB,aAAa,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,eAAe,CAAC;YACnD,MAAM,gBAAgB,GAAmB;gBACvC,IAAI,EAAE,8BAA8B;gBACpC,UAAU,EAAE;oBACV,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;oBACtC,WAAW,EACT,OAAO,CAAC,WAAW;wBACnB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc;oBAC5D,OAAO,EAAE,OAAO;oBAChB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,IAAI,UAAU;oBAC9D,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,GAAG;oBAC/B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,IAAI,CAAC;oBACrD,qBAAqB,EACnB,OAAO,CAAC,WAAW,IAAI,YAAY,CAAC,WAAW,IAAI,CAAC;oBACtD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU,IAAI,GAAG;oBAChE,oBAAoB,EAAE;wBACpB,GAAG,YAAY,CAAC,WAAW;wBAC3B,GAAG,OAAO,CAAC,WAAW;wBACtB,GAAG,cAAc;qBAClB;oBACD,mBAAmB,EAAE,OAAO,CAAC,WAAW,IAAI,CAAC;iBAC9C;gBACD,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,QAAQ,CAAC,mCAAI,EAAE,EAAE;gBAC7C,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;oBACjB,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAc,CAAC;oBACvC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG;wBACvD,IAAI,EAAE,MAAM;wBACZ,UAAU,EAAE;4BACV,QAAQ,EACN,OAAO,CAAC,QAAQ,IAAI,YAAY,CAAC,QAAQ,IAAI,WAAW;4BAC1D,OAAO,EAAE,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;4BACnC,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC;oBAEF,IAAI,CAAC,gBAAgB,EAAE;wBACrB,gBAAgB,GAAG,EAAE,CAAC;qBACvB;oBACD,gBAAgB,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG;wBACnC,WAAW;wBACX,YAAY,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO;qBACtC,CAAC;iBACH;gBAED,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAe,CAAC;oBAEzC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,OAAO,CAAC,GAAG;wBAC7C,IAAI,EAAE,OAAO;wBACb,UAAU,EAAE;4BACV,cAAc,EACZ,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;4BAC1D,MAAM,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;4BAC3C,OAAO,EAAE,GAAG,CAAC,OAAO;4BACpB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC;iBACH;gBAED,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;oBAChB,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAa,CAAC;oBACrC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG;wBAC3C,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE;4BACV,YAAY,EAAE;gCACZ,QAAQ,EAAE,GAAG,CAAC,MAAM;6BACrB;4BACD,SAAS,EAAE;gCACT,YAAY,EAAE,GAAG,CAAC,SAAS,IAAI,CAAC;gCAChC,eAAe,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;6BACpC;4BACD,SAAS,EAAE;gCACT,OAAO,EAAE,GAAG,CAAC,OAAO;gCACpB,QAAQ,EAAE,GAAG,CAAC,GAAG;6BAClB;4BACD,MAAM,EAAE,IAAI;4BACZ,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC;iBACH;gBAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE5D,IAAI,OAAO,EAAE;oBACX,MAAM,GAAG,GAAG,OAAkB,CAAC;oBAC/B,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG;wBAC3C,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE;4BACV,UAAU,EAAE,GAAG,CAAC,MAAM;4BACtB,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;4BAC7B,MAAM,EAAE;gCACN,GAAG,EAAE;oCACH,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;oCACzB,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;iCAC1B;6BACF;4BACD,MAAM,EAAE,IAAI;4BACZ,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC;iBACH;gBAED,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;oBACf,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAY,CAAC;oBACnC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG;wBAC1C,IAAI,EAAE,UAAU;wBAChB,UAAU,EAAE;4BACV,SAAS,EAAE,GAAG,CAAC,KAAK;4BACpB,mBAAmB,EAAE,MAAM;4BAC3B,cAAc,EAAE,GAAG,CAAC,QAAQ,IAAI,eAAe;4BAC/C,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,SAAS,EAAE,GAAG,CAAC,IAAI;4BACnB,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC;iBACH;aACF;YAED,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC;gBACtD,gBAAgB,CAAC;SACpB;QAED,IAAI,gBAAgB,EAAE;YACpB,MAAM,YAAY,GAAG,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAG,QAAQ,CAAC,0CAAG,cAAc,CAAC,CAAC;YACnE,IAAI,YAAY,EAAE;gBAChB,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;gBACpC,IAAI,UAAU,KAAK,MAAM,EAAE;oBACzB,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,GAAG;wBACzC,IAAI,EAAE,kCAAkC;wBACxC,UAAU,EAAE;4BACV,UAAU,EAAE,MAAM;4BAClB,QAAQ,EAAE,MAAM;4BAChB,WAAW,EAAE;gCACX,MAAM,EAAE,gBAAgB;6BACzB;yBACF;qBACoB,CAAC;iBACzB;qBAAM;oBACL,QAAQ,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG;wBAC/B,IAAI,EAAE,kCAAkC;wBACxC,UAAU,EAAE;4BACV,QAAQ,EAAE,MAAM;4BAChB,WAAW,EAAE;gCACX,MAAM,EAAE,gBAAgB;6BACzB;yBACF;qBACoB,CAAC;iBACzB;aACF;iBAAM,IAAI,YAAY,KAAK,KAAK,EAAE;gBACjC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;gBAClE,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAClD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAClB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBACvB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;gBAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;gBACpC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;gBAClE,QAAQ,CAAC,SAAS,CAAC,oBAAoB,CAAC,GAAG;oBACzC,IAAI,EAAE,kCAAkC;oBACxC,UAAU,EAAE;wBACV,UAAU,EAAE,MAAM;wBAClB,QAAQ,EAAE,MAAM;wBAChB,WAAW,EAAE;4BACX,MAAM,EAAE,gBAAgB;yBACzB;qBACF;iBACoB,CAAC;aACzB;SACF;QAED,OAAO,IAAA,mCAA2B,EAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;CACF;AAvND,sCAuNC;AAED,SAAgB,cAAc,CAAC,OAA0B;IACvD,0DAA0D;IAC1D,MAAM,GAAG,GAAoB;QAC3B,KAAK;QACL,KAAK;QACL,MAAM;QACN,QAAQ;QACR,MAAM;QACN,OAAO;KACR,CAAC;IACF,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE;YAC1C,OAAO,GAAG,CAAC;SACZ;QAED,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;KACrB;SAAM,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE;QAC1B,YAAY;KACb;SAAM;QACL,QAAQ;QACR,OAAO,GAAG,CAAC;KACZ;IAED,OAAO,OAAO;SACX,GAAG,CAAC,MAAM,CAAC,EAAE;QACZ,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;IAC9B,CAAC,CAAC;SACD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAuB,CAAC,CAAoB,CAAC;AAChF,CAAC;AA5BD,wCA4BC;AAED,MAAa,sBAAuB,SAAQ,qBAAW;IACrD,MAAM;;QACJ,MAAM,YAAY,GAAwB,IAAI,CAAC,WAAW,EAAE,CAAC;QAC7D,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,MAAM,aAAa,GAAyB,IAAI,CAAC,YAAY,EAAE,CAAC;QAChE,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;QACrC,MAAM,cAAc,GAAG,IAAA,4BAAoB,GAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,EAAE,CAAC;QACpB,MAAM,OAAO,GAAG;YACd,IAAI,EAAE,WAAW;YACjB,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;YACrD,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;YACrD,SAAS,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC,SAAS,CAAC;YACrD,aAAa,EAAE,YAAY,CAAC,aAAa;SAC1C,CAAC;QACF,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QACnC,MAAM,MAAM,GAAI,YAAoB,CAAC,MAAM,IAAI,SAAS,CAAC;QAEzD,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;YACnC,IAAI,gBAAgB,CAAC;YACrB,MAAM,OAAO,GAAwB,aAAa,CAAC,OAAO,CAAC,CAAC;YAC5D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,eAAe,CAAC;YACnD,MAAM,OAAO,GAAG;gBACd,OAAO,EAAE;oBACP,QAAQ,EAAE,SAAS;oBACnB,MAAM;oBACN,WAAW,EAAE,WAAW;iBACzB;gBACD,KAAK,EAAE;oBACL,OAAO;oBACP,MAAM;oBACN,QAAQ,EAAE;wBACR,IAAI,EAAE,OAAO;wBACb,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;wBACtC,0CAA0C;wBAC1C,sEAAsE;wBACtE,OAAO,EAAE,OAAO;wBAChB,WAAW,EACT,OAAO,CAAC,WAAW;4BACnB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,cAAc;wBAC5D,qBAAqB,EACnB,OAAO,CAAC,WAAW,IAAI,YAAY,CAAC,WAAW,IAAI,CAAC;wBACtD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,YAAY,CAAC,UAAU,IAAI,GAAG;wBAChE,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,IAAI,UAAU;wBAC9D,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,YAAY,CAAC,OAAO,IAAI,CAAC;wBACrD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,GAAG;wBAC/B,mBAAmB,EACjB,OAAO,CAAC,WAAW,IAAI,YAAY,CAAC,WAAW,IAAI,CAAC;wBACtD,oBAAoB,EAAE;4BACpB,GAAG,YAAY,CAAC,WAAW;4BAC3B,GAAG,OAAO,CAAC,WAAW;4BACtB,GAAG,cAAc;yBAClB;wBACD,kBAAkB,EAAE,YAAY,CAAC,kBAAkB,IAAI,EAAE;wBACzD,MAAM,EAAE,YAAY,CAAC,MAAM,IAAI,EAAE;qBAClC;oBACD,QAAQ,EAAE,EAAE;oBACZ,aAAa,EAAE,EAAE;iBAClB;aACF,CAAC;YACF,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEvB,KAAK,MAAM,KAAK,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,QAAQ,CAAC,mCAAI,EAAE,EAAE;gBAC7C,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;oBACjB,MAAM,GAAG,GAAG,KAAK,CAAC,MAAM,CAAc,CAAC;oBACvC,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBAC3C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,OAAO;wBACnC,IAAI,EAAE,MAAM;wBACZ,MAAM,EAAE;4BACN,QAAQ,EAAE,WAAW;4BACrB,OAAO,EAAE,OAAO;4BAChB,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC,CAAC;oBAEH,sGAAsG;oBACtG,IAAI,CAAC,gBAAgB,EAAE;wBACrB,gBAAgB,GAAG,EAAE,CAAC;qBACvB;oBACD,gBAAgB,CAAC,IAAI,CAAC;wBACpB,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,IAAI;wBACtB,WAAW;wBACX,YAAY,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO;wBACrC,OAAO;qBACR,CAAC,CAAC;iBACJ;gBAED,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAe,CAAC;oBACzC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,QAAQ,GAAG,OAAO;wBACpC,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE;4BACN,cAAc,EACZ,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK;4BAC1D,MAAM,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;4BAC3C,OAAO,EAAE,GAAG,CAAC,OAAO;4BACpB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC,CAAC;iBACJ;gBAED,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;oBAChB,MAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAa,CAAC;oBACrC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO;wBAClC,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE;4BACN,YAAY,EAAE;gCACZ,QAAQ,EAAE,GAAG,CAAC,MAAM;6BACrB;4BACD,SAAS,EAAE;gCACT,YAAY,EAAE,GAAG,CAAC,SAAS,IAAI,CAAC;gCAChC,eAAe,EAAE,GAAG,CAAC,QAAQ,IAAI,EAAE;6BACpC;4BACD,SAAS,EAAE;gCACT,OAAO,EAAE,GAAG,CAAC,OAAO;gCACpB,QAAQ,EAAE,GAAG,CAAC,GAAG;6BAClB;4BACD,MAAM,EAAE,IAAI;4BACZ,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC,CAAC;iBACJ;gBAED,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;gBAE5D,IAAI,OAAO,EAAE;oBACX,MAAM,GAAG,GAAG,OAAkB,CAAC;oBAC/B,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC1B,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,GAAG,OAAO;wBAClC,IAAI,EAAE,KAAK;wBACX,MAAM,EAAE;4BACN,UAAU,EAAE,GAAG,CAAC,MAAM;4BACtB,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC;4BAC7B,MAAM,EAAE;gCACN,GAAG,EAAE;oCACH,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;oCACzB,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,MAAM;iCAC1B;6BACF;4BACD,MAAM,EAAE,IAAI;4BACZ,cAAc,EAAE,GAAG,CAAC,IAAI;4BACxB,SAAS,EAAE,GAAG,CAAC,OAAO;yBACvB;qBACF,CAAC,CAAC;iBACJ;aACF;YAED,IAAI,gBAAgB,aAAhB,gBAAgB,uBAAhB,gBAAgB,CAAE,MAAM,EAAE;gBAC5B,MAAM,YAAY,GAAG,MAAA,MAAA,IAAI,CAAC,UAAU,0CAAG,QAAQ,CAAC,0CAAG,cAAc,CAAC,CAAC;gBACnE,IAAI,YAAY,EAAE;oBAChB,MAAM,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;oBACpC,IAAI,UAAU,KAAK,MAAM,EAAE;wBACzB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;4BAC/B,UAAU,EAAE,MAAM;4BAClB,QAAQ,EAAE,MAAM;4BAChB,YAAY,EAAE,gBAAgB;yBAC/B,CAAC,CAAC;qBACJ;yBAAM;wBACL,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;4BAC/B,UAAU;4BACV,QAAQ,EAAE,MAAM;4BAChB,YAAY,EAAE,gBAAgB;yBAC/B,CAAC,CAAC;qBACJ;iBACF;qBAAM,IAAI,YAAY,KAAK,KAAK,EAAE;oBACjC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;oBAClE,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;oBACnD,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;oBAC7C,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBAClB,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;oBACvB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;oBAC/B,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;oBACpC,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;oBAClE,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC;wBAC/B,UAAU,EAAE,MAAM;wBAClB,QAAQ,EAAE,MAAM;wBAChB,YAAY,EAAE,gBAAgB;qBAC/B,CAAC,CAAC;iBACJ;aACF;SACF;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AAhMD,wDAgMC"}