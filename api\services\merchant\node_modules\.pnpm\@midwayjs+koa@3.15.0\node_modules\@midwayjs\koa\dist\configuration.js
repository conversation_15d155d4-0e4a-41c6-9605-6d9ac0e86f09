"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.KoaConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const session = require("@midwayjs/session");
const framework_1 = require("./framework");
const DefaultConfig = require("./config/config.default");
const bodyparser_middleware_1 = require("./middleware/bodyparser.middleware");
const fav_middleware_1 = require("./middleware/fav.middleware");
let KoaConfiguration = class KoaConfiguration {
    init() {
        // register param decorator
        this.decoratorService.registerParameterHandler(core_1.WEB_ROUTER_PARAM_KEY, options => {
            return (0, core_1.extractKoaLikeValue)(options.metadata.type, options.metadata.propertyData, options.originParamType)(options.originArgs[0], options.originArgs[1]);
        });
    }
    async onReady() {
        this.koaFramework.useMiddleware([fav_middleware_1.SiteFileMiddleware, bodyparser_middleware_1.BodyParserMiddleware]);
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayDecoratorService)
], KoaConfiguration.prototype, "decoratorService", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", framework_1.MidwayKoaFramework)
], KoaConfiguration.prototype, "koaFramework", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayConfigService)
], KoaConfiguration.prototype, "configService", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], KoaConfiguration.prototype, "init", null);
KoaConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'koa',
        imports: [session],
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], KoaConfiguration);
exports.KoaConfiguration = KoaConfiguration;
//# sourceMappingURL=configuration.js.map