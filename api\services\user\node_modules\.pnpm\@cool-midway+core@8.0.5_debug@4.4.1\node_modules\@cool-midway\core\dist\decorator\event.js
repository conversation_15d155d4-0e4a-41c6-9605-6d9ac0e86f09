"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.COOL_EVENT_KEY = exports.COOL_CLS_EVENT_KEY = void 0;
exports.CoolEvent = CoolEvent;
exports.Event = Event;
const core_1 = require("@midwayjs/core");
const core_2 = require("@midwayjs/core");
exports.COOL_CLS_EVENT_KEY = 'decorator:cool:cls:event';
/**
 * 事件
 * @param options
 * @returns
 */
function CoolEvent(options = {}) {
    return (target) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, core_1.saveModule)(exports.COOL_CLS_EVENT_KEY, target);
        // 保存一些元数据信息，任意你希望存的东西
        (0, core_1.saveClassMetadata)(exports.COOL_CLS_EVENT_KEY, options, target);
        // 指定 IoC 容器创建实例的作用域，这里注册为请求作用域，这样能取到 ctx
        (0, core_2.Scope)(core_2.ScopeEnum.Singleton)(target);
    };
}
exports.COOL_EVENT_KEY = 'decorator:cool:event';
/**
 * 事件
 * @param eventName
 * @returns
 */
function Event(eventName) {
    return (target, propertyKey, descriptor) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, core_1.attachClassMetadata)(exports.COOL_EVENT_KEY, {
            eventName,
            propertyKey,
            descriptor,
        }, target);
    };
}
