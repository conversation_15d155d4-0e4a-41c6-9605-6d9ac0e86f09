import { Init, Inject, Provide } from '@midwayjs/core';
import {
  BaseService,
  CoolCommException,
  CoolTransaction,
} from '@cool-midway/core';
import { CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, QueryRunner, Repository } from 'typeorm';
import { FinanceApplyDrawEntity } from '../../entity/apply/draw';
import { FinanceWalletUserService } from '../wallet/user';
import { FinanceWalletRecordEntity } from '../../entity/wallet/record';
import { FinanceUserDrawService } from '../user/draw';
import { FinanceWalletRecordService } from '../wallet/record';

/**
 * 提现申请微服务
 */
@Provide()
@CoolRpcService({
  entity: FinanceApplyDrawEntity,
  method: ["add", "delete", "update", "info", "list", "page"],
})
export class FinanceApplyDrawService extends BaseService {
  @InjectEntityModel(FinanceApplyDrawEntity)
  financeApplyDrawEntity!: Repository<FinanceApplyDrawEntity>;

  @Inject()
  financeWalletUserService: FinanceWalletUserService;

  @Inject()
  financeUserDrawService: FinanceUserDrawService;

  @Inject()
  financeWalletRecordService: FinanceWalletRecordService;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeApplyDrawEntity);
  }

  /**
   * 提现申请（RPC暴露）
   * @param userId 用户ID
   * @param amount 提现金额
   * @param type 提现方式 0-银行卡 1-支付宝 2-微信
   */
  @CoolTransaction({ isolation: 'SERIALIZABLE' })
  async submitDrawApplication(
    userId: number,
    amount: number,
    type: number,
    queryRunner?: QueryRunner
  ) {
    // 判断余额是否足够
    const wallet = await this.financeWalletUserService.getWalletDetail(userId);
    if (wallet.balance < amount) {
      throw new CoolCommException('余额不足');
    }

    // 检查最小提现金额
    if (amount < 1) {
      throw new CoolCommException('提现金额不能少于1元');
    }

    const account = await this.financeUserDrawService.getAccount(userId);
    if (!account) {
      throw new CoolCommException('请先设置提现账户');
    }

    // 提现信息
    const apply = new FinanceApplyDrawEntity();
    apply.userId = userId;
    apply.amount = amount;
    apply.type = type;
    apply.account = account;
    apply.status = 0; // 待审核
    
    const result = await queryRunner.manager.insert<FinanceApplyDrawEntity>(
      FinanceApplyDrawEntity,
      apply
    );
    
    const applyId = result.identifiers[0].id;

    // 记录提现（冻结金额）
    const record = new FinanceWalletRecordEntity();
    record.userId = userId;
    record.amount = -amount;
    record.type = 1; // 提现
    record.title = '提现申请';
    record.objectId = applyId;
    record.objectType = 1;
    record.objectInfo = apply;
    
    await this.financeWalletUserService.addWalletRecord(record, queryRunner);

    return { applyId, message: '提现申请提交成功，请等待审核' };
  }

  /**
   * 审核提现申请（RPC暴露）
   * @param id 申请ID
   * @param status 状态 1-通过 2-拒绝
   * @param remark 备注
   * @param fee 手续费
   */
  async reviewDrawApplication(id: number, status: number, remark: string, fee: number = 0) {
    const draw = await this.financeApplyDrawEntity.findOneBy({ id: Equal(id) });
    if (!draw) throw new CoolCommException('提现申请不存在');
    if (draw.status !== 0) throw new CoolCommException('提现申请已处理，无法重复操作');

    draw.status = status;
    draw.remark = remark || '';
    draw.fee = fee;
    
    await this.financeApplyDrawEntity.save(draw);

    // 更新相关的钱包记录
    const record = await this.financeWalletRecordService.getByObject(
      draw.userId,
      draw.id,
      1
    );
    
    if (record) {
      await this.financeWalletRecordService.updateObject(record.id, {
        ...draw,
        statusText: status === 1 ? '审核通过' : '审核拒绝'
      });
    }

    // 如果审核不通过，回流金额
    if (status === 2) {
      await this.financeWalletUserService.changeBalance(
        draw.userId,
        draw.amount
      );
      
      // 添加退款记录
      const refundRecord = new FinanceWalletRecordEntity();
      refundRecord.userId = draw.userId;
      refundRecord.amount = draw.amount;
      refundRecord.type = 0; // 收入
      refundRecord.title = '提现失败退款';
      refundRecord.objectId = draw.id;
      refundRecord.objectType = 1;
      refundRecord.objectInfo = { reason: remark };
      
      await this.financeWalletRecordService.add(refundRecord);
    }

    return {
      status: status === 1 ? 'approved' : 'rejected',
      message: status === 1 ? '提现申请审核通过' : '提现申请被拒绝'
    };
  }

  /**
   * 获取用户提现申请列表（RPC暴露）
   * @param userId 用户ID
   * @param status 状态筛选
   */
  async getUserDrawApplications(userId: number, status?: number) {
    const where: any = { userId };
    if (status !== undefined) {
      where.status = status;
    }

    return this.financeApplyDrawEntity.find({
      where,
      order: { createTime: 'DESC' }
    });
  }

  /**
   * 获取提现统计信息（RPC暴露）
   */
  async getDrawStats() {
    const stats = await this.financeApplyDrawEntity
      .createQueryBuilder('draw')
      .select([
        'COUNT(draw.id) as totalApplications',
        'SUM(CASE WHEN draw.status = 0 THEN 1 ELSE 0 END) as pendingCount',
        'SUM(CASE WHEN draw.status = 1 THEN 1 ELSE 0 END) as approvedCount', 
        'SUM(CASE WHEN draw.status = 2 THEN 1 ELSE 0 END) as rejectedCount',
        'SUM(CASE WHEN draw.status = 1 THEN draw.amount ELSE 0 END) as totalApprovedAmount',
        'SUM(draw.fee) as totalFee'
      ])
      .getRawOne();

    return {
      totalApplications: parseInt(stats.totalApplications) || 0,
      pendingCount: parseInt(stats.pendingCount) || 0,
      approvedCount: parseInt(stats.approvedCount) || 0,
      rejectedCount: parseInt(stats.rejectedCount) || 0,
      totalApprovedAmount: parseFloat(stats.totalApprovedAmount) || 0,
      totalFee: parseFloat(stats.totalFee) || 0
    };
  }
}
