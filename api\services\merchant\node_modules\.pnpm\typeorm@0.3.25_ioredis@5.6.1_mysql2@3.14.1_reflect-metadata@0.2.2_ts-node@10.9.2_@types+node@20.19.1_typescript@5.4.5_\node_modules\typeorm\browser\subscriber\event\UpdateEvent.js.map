{"version": 3, "sources": ["../browser/src/subscriber/event/UpdateEvent.ts"], "names": [], "mappings": "", "file": "UpdateEvent.js", "sourcesContent": ["import { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { RelationMetadata } from \"../../metadata/RelationMetadata\"\nimport { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\n\n/**\n * UpdateEvent is an object that broadcaster sends to the entity subscriber when entity is being updated in the database.\n */\nexport interface UpdateEvent<Entity> {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n\n    /**\n     * Updating entity.\n     *\n     * Contains the same data that was passed to the updating method, be it the instance of an entity or the partial entity.\n     */\n    entity: ObjectLiteral | undefined\n\n    /**\n     * Metadata of the entity.\n     */\n    metadata: EntityMetadata\n\n    /**\n     * Updating entity in the database.\n     *\n     * Is set only when one of the following methods are used: .save(), .remove(), .softRemove(), and .recover()\n     */\n    databaseEntity: Entity\n\n    /**\n     * List of updated columns. In query builder has no affected\n     */\n    updatedColumns: ColumnMetadata[]\n\n    /**\n     * List of updated relations. In query builder has no affected\n     */\n    updatedRelations: RelationMetadata[]\n}\n"], "sourceRoot": "../.."}