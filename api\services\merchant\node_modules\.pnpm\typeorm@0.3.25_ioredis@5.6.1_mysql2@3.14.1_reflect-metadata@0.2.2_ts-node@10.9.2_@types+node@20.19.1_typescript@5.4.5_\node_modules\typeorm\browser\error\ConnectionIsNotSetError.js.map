{"version": 3, "sources": ["../browser/src/error/ConnectionIsNotSetError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,YAAY;IACrD,YAAY,MAAc;QACtB,KAAK,CACD,mBAAmB,MAAM,+DAA+D,CAC3F,CAAA;IACL,CAAC;CACJ", "file": "ConnectionIsNotSetError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to execute operation that requires connection to be established.\n */\nexport class ConnectionIsNotSetError extends TypeORMError {\n    constructor(dbType: string) {\n        super(\n            `Connection with ${dbType} database is not established. Check connection configuration.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}