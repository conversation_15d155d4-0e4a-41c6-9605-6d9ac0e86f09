export declare const COOL_CLS_EVENT_KEY = "decorator:cool:cls:event";
/**
 * 事件配置
 */
export interface CoolEventOptions {
    /** 是否全局 */
    isGlobal: boolean;
}
/**
 * 事件
 * @param options
 * @returns
 */
export declare function CoolEvent(options?: CoolEventOptions): ClassDecorator;
export declare const COOL_EVENT_KEY = "decorator:cool:event";
/**
 * 事件
 * @param eventName
 * @returns
 */
export declare function Event(eventName?: string): MethodDecorator;
