"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpc = void 0;
const core_1 = require("@midwayjs/core");
const decorator_1 = require("@midwayjs/decorator");
const moleculer_1 = require("moleculer");
const core_2 = require("@cool-midway/core");
const uuid_1 = require("uuid");
const rpc_1 = require("./decorator/rpc");
const event_1 = require("./decorator/event/event");
const handler_1 = require("./decorator/event/handler");
const _ = require("lodash");
const typeorm_1 = require("@midwayjs/typeorm");
const camelCase_1 = require("@midwayjs/core/dist/util/camelCase");
// import { AgentService } from '@moleculer/lab';
/**
 * 微服务
 */
let CoolRpc = class CoolRpc {
    async init() {
        var _a, _b, _c, _d, _e, _f;
        if (!((_a = this.rpcConfig) === null || _a === void 0 ? void 0 : _a.name)) {
            throw new core_2.CoolCoreException('cool.rpc.name config is require and every service name must be unique');
        }
        let redisConfig;
        if (!((_b = this.rpcConfig) === null || _b === void 0 ? void 0 : _b.redis) && !((_c = this.coolConfig) === null || _c === void 0 ? void 0 : _c.redis)) {
            throw new core_2.CoolCoreException('cool.rpc.redis or cool.redis is require');
        }
        redisConfig = ((_d = this.rpcConfig) === null || _d === void 0 ? void 0 : _d.redis)
            ? (_e = this.rpcConfig) === null || _e === void 0 ? void 0 : _e.redis
            : (_f = this.coolConfig) === null || _f === void 0 ? void 0 : _f.redis;
        const transporter = {
            type: 'Redis',
            options: {},
        };
        if (redisConfig instanceof Array) {
            transporter.options = {
                cluster: {
                    nodes: redisConfig,
                },
            };
        }
        else {
            transporter.options = redisConfig;
        }
        this.broker = new moleculer_1.ServiceBroker({
            nodeID: `${this.rpcConfig.name}-${(0, uuid_1.v1)()}`,
            transporter,
            //   metrics: {
            //     enabled: true,
            //     reporter: 'Laboratory',
            //   },
            //   tracing: {
            //     enabled: true,
            //     exporter: 'Laboratory',
            //   },
            ...this.rpcConfig,
        });
        // this.broker.createService({
        //   name: this.rpcConfig.name,
        //   mixins: [],
        //   //   settings: {
        //   //     name: 'test',
        //   //     port: 3210,
        //   //     token: '123123',
        //   //     apiKey: '92C18ZR-ERM45EG-HT8GQGQ-4MHCXAT',
        //   //   },
        // });
        global['moleculer:broker'] = this.broker;
        await this.initService();
        await this.createService();
    }
    /**
     * 获得事件
     * @returns
     */
    async getEvents() {
        const allEvents = {};
        const modules = (0, decorator_1.listModule)(event_1.COOL_RPC_EVENT_KEY);
        for (const module of modules) {
            const moduleInstance = await this.app
                .getApplicationContext()
                .getAsync(module);
            moduleInstance['broker'] = this.broker;
            const events = (0, decorator_1.getClassMetadata)(handler_1.COOL_RPC_EVENT_HANDLER_KEY, module);
            for (const event of events) {
                allEvents[event.eventName ? event.eventName : event.propertyKey] = {
                    handler(ctx) {
                        moduleInstance[event.propertyKey](ctx.params);
                    },
                };
            }
        }
        return allEvents;
    }
    /**
     * 创建服务
     */
    async createService() {
        const _this = this;
        this.broker.createService({
            name: this.rpcConfig.name,
            events: await this.getEvents(),
            actions: {
                async call(ctx) {
                    const { service, method, params } = ctx.params;
                    const targetName = _.upperFirst(service);
                    const target = _.find(_this.cruds, { name: targetName });
                    if (!target) {
                        throw new core_2.CoolValidateException('找不到服务');
                    }
                    const curdOption = (0, decorator_1.getClassMetadata)(rpc_1.MOLECYLER_KEY, target);
                    const cls = await _this.app
                        .getApplicationContext()
                        .getAsync((0, camelCase_1.camelCase)(service));
                    const serviceInstance = new target();
                    Object.assign(serviceInstance, cls);
                    serviceInstance.setModel(_this.getModel(curdOption));
                    serviceInstance.setApp(_this.app);
                    serviceInstance.init();
                    // 如果是通用crud方法 注入参数
                    if (['add', 'delete', 'update', 'page', 'info', 'list'].includes(method)) {
                        if (!curdOption.method.includes(method)) {
                            throw new core_2.CoolValidateException('方法不存在');
                        }
                    }
                    return serviceInstance[method](params);
                },
            },
        });
        this.broker.start();
    }
    /**
     * 初始化service，设置entity
     */
    async initService() {
        // 获得所有的service
        this.cruds = (0, decorator_1.listModule)(rpc_1.MOLECYLER_KEY);
        for (const crud of this.cruds) {
            const curdOption = (0, decorator_1.getClassMetadata)(rpc_1.MOLECYLER_KEY, crud);
            const serviceInstance = await this.app
                .getApplicationContext()
                .getAsync(crud);
            serviceInstance.setModel(this.getModel(curdOption));
            serviceInstance.setCurdOption(curdOption);
        }
    }
    /**
     * 获得Model
     * @param curdOption
     */
    getModel(curdOption) {
        // 获得到model
        let entityModel;
        const { entity } = curdOption || {};
        if (entity) {
            const dataSourceName = this.typeORMDataSourceManager.getDataSourceNameByModel(entity);
            entityModel = this.typeORMDataSourceManager
                .getDataSource(dataSourceName)
                .getRepository(entity);
        }
        return entityModel;
    }
    /**
     * 调用服务
     * @param name 服务名称
     * @param controller 接口服务
     * @param method 方法
     * @param params 参数
     * @returns
     */
    async call(name, service, method, params) {
        return this.broker.call(`${name}.call`, { service, method, params });
    }
    /**
     * 发送事件
     * @param name 事件名称
     * @param params 事件参数
     * @param node 节点名称
     */
    async event(name, params, node) {
        this.broker.emit(name, params);
    }
    /**
     * 发送广播事件
     * @param name
     * @param params
     * @param node 节点名称
     */
    async broadcastEvent(name, params, node) {
        this.broker.broadcast(name, params);
    }
    /**
     * 发送本地广播事件
     * @param name
     * @param params
     * @param node 节点名称
     */
    async broadcastLocalEvent(name, params, node) {
        this.broker.broadcastLocal(name, params);
    }
    /**
     * 获得原始的broker对象
     * @returns
     */
    getBroker() {
        return this.broker;
    }
    /**
     * 停止
     */
    stop() {
        this.broker.stop();
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolRpc.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, decorator_1.Logger)(),
    __metadata("design:type", Object)
], CoolRpc.prototype, "coreLogger", void 0);
__decorate([
    (0, decorator_1.Config)('cool.rpc'),
    __metadata("design:type", Object)
], CoolRpc.prototype, "rpcConfig", void 0);
__decorate([
    (0, decorator_1.Config)('cool'),
    __metadata("design:type", Object)
], CoolRpc.prototype, "coolConfig", void 0);
__decorate([
    (0, decorator_1.App)(),
    __metadata("design:type", Object)
], CoolRpc.prototype, "app", void 0);
CoolRpc = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(decorator_1.ScopeEnum.Singleton)
], CoolRpc);
exports.CoolRpc = CoolRpc;
