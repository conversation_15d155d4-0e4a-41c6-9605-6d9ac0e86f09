{"version": 3, "sources": ["../../src/decorator/relations/OneToMany.ts"], "names": [], "mappings": ";;AAUA,8BAoCC;AA9CD,2CAAsD;AAKtD;;;;GAIG;AACH,SAAgB,SAAS,CACrB,oBAA8D,EAC9D,WAA0C,EAC1C,OAAyB;IAEzB,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAqB,CAAA;QAE7C,iDAAiD;QACjD,IAAI,MAAM,GAAG,OAAO,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAA;QAC7C,IAAI,CAAC,MAAM,IAAI,OAAO,IAAK,OAAe,CAAC,WAAW,EAAE,CAAC;YACrD,0BAA0B;YAC1B,MAAM,aAAa,GAAI,OAAe,CAAC,WAAW,CAC9C,aAAa,EACb,MAAM,EACN,YAAY,CACf,CAAA;YACD,IACI,aAAa;gBACb,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ;gBACtC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS;gBAE9C,MAAM,GAAG,IAAI,CAAA;QACrB,CAAC;QAED,IAAA,gCAAsB,GAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACpC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,+BAA+B;YAC/B,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,aAAa;YAC3B,IAAI,EAAE,oBAAoB;YAC1B,mBAAmB,EAAE,WAAW;YAChC,OAAO,EAAE,OAAO;SACK,CAAC,CAAA;IAC9B,CAAC,CAAA;AACL,CAAC", "file": "OneToMany.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationMetadataArgs } from \"../../metadata-args/RelationMetadataArgs\"\nimport { ObjectType } from \"../../common/ObjectType\"\nimport { RelationOptions } from \"../options/RelationOptions\"\n\n/**\n * A one-to-many relation allows creating the type of relation where Entity1 can have multiple instances of Entity2,\n * but Entity2 has only one Entity1. Entity2 is the owner of the relationship, and stores the id of Entity1 on its\n * side of the relation.\n */\nexport function OneToMany<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSide: string | ((object: T) => any),\n    options?: RelationOptions,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        if (!options) options = {} as RelationOptions\n\n        // Now try to determine if it is a lazy relation.\n        let isLazy = options && options.lazy === true\n        if (!isLazy && Reflect && (Reflect as any).getMetadata) {\n            // automatic determination\n            const reflectedType = (Reflect as any).getMetadata(\n                \"design:type\",\n                object,\n                propertyName,\n            )\n            if (\n                reflectedType &&\n                typeof reflectedType.name === \"string\" &&\n                reflectedType.name.toLowerCase() === \"promise\"\n            )\n                isLazy = true\n        }\n\n        getMetadataArgsStorage().relations.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            // propertyType: reflectedType,\n            isLazy: isLazy,\n            relationType: \"one-to-many\",\n            type: typeFunctionOrTarget,\n            inverseSideProperty: inverseSide,\n            options: options,\n        } as RelationMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}