"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.readJSON = exports.getTSConfig = exports.getPkgManagerCommand = exports.isYarnUsed = exports.safeError = exports.nop = exports.readJsonp = exports.ncpp = exports.writeFileAtomicp = exports.rimrafp = exports.readFilep = void 0;
const fs = require("fs");
const path = require("path");
const rimraf = require("rimraf");
const util_1 = require("util");
const ncp = require("ncp");
const writeFileAtomic = require("write-file-atomic");
const JSON5 = require("json5");
exports.readFilep = (0, util_1.promisify)(fs.readFile);
exports.rimrafp = (0, util_1.promisify)(rimraf);
exports.writeFileAtomicp = (0, util_1.promisify)(writeFileAtomic);
exports.ncpp = (0, util_1.promisify)(ncp.ncp);
async function readJsonp(jsonPath) {
    const contents = await (0, exports.readFilep)(jsonPath, 'utf8');
    return JSON5.parse(contents);
}
exports.readJsonp = readJsonp;
function nop() {
    /* empty */
}
exports.nop = nop;
function safeError(err) {
    if (err == null) {
        return new Error(`(${err})`);
    }
    if (err instanceof Error) {
        return err;
    }
    return new Error(`${err}`);
}
exports.safeError = safeError;
/**
 * Recursively iterate through the dependency chain until we reach the end of
 * the dependency chain or encounter a circular reference
 * @param filePath Filepath of file currently being read
 * @param customReadFilep The file reading function being used
 * @param readFiles an array of the previously read files so we can check for
 * circular references
 * returns a ConfigFile object containing the data from all the dependencies
 */
async function getBase(filePath, customReadFilep, readFiles, currentDir) {
    customReadFilep = customReadFilep || exports.readFilep;
    filePath = path.resolve(currentDir, filePath);
    // An error is thrown if there is a circular reference as specified by the
    // TypeScript doc
    if (readFiles.has(filePath)) {
        throw new Error(`Circular reference in ${filePath}`);
    }
    readFiles.add(filePath);
    try {
        const json = await customReadFilep(filePath, 'utf8');
        let contents;
        try {
            contents = JSON5.parse(json);
        }
        catch (exc) {
            const err = safeError(exc);
            err.message = `Unable to parse ${filePath}!\n${err.message}`;
            throw err;
        }
        if (typeof contents.extends === 'string') {
            const nextFile = await getBase(contents.extends, customReadFilep, readFiles, path.dirname(filePath));
            contents = combineTSConfig(nextFile, contents);
        }
        else if (Array.isArray(contents.extends)) {
            for (const extend of contents.extends) {
                const nextFile = await getBase(extend, customReadFilep, readFiles, path.dirname(filePath));
                contents = combineTSConfig(nextFile, contents);
            }
        }
        return contents;
    }
    catch (exc) {
        const err = safeError(exc);
        err.message = `Error: ${filePath}\n${err.message}`;
        throw err;
    }
}
/**
 * Takes in 2 config files
 * @param base is loaded first
 * @param inherited is then loaded and overwrites base
 */
function combineTSConfig(base, inherited) {
    const result = { compilerOptions: {} };
    Object.assign(result, base, inherited);
    Object.assign(result.compilerOptions, base.compilerOptions, inherited.compilerOptions);
    delete result.extends;
    return result;
}
/**
 * Automatically defines npm or yarn is going to be used:
 * - If only yarn.lock exists, use yarn
 * - If only package-lock.json or both exist, use npm
 */
function isYarnUsed(existsSync = fs.existsSync) {
    if (existsSync('package-lock.json')) {
        return false;
    }
    return existsSync('yarn.lock');
}
exports.isYarnUsed = isYarnUsed;
function getPkgManagerCommand(isYarnUsed) {
    return ((isYarnUsed ? 'yarn' : 'npm') + (process.platform === 'win32' ? '.cmd' : ''));
}
exports.getPkgManagerCommand = getPkgManagerCommand;
/**
 * Find the tsconfig.json, read it, and return parsed contents.
 * @param rootDir Directory where the tsconfig.json should be found.
 * If the tsconfig.json file has an "extends" field hop down the dependency tree
 * until it ends or a circular reference is found in which case an error will be
 * thrown
 */
async function getTSConfig(rootDir, customReadFilep) {
    const readArr = new Set();
    return getBase('tsconfig.json', customReadFilep, readArr, rootDir);
}
exports.getTSConfig = getTSConfig;
function readJSON(filepath) {
    const content = fs.readFileSync(filepath, 'utf8');
    try {
        return JSON.parse(content);
    }
    catch (exc) {
        const err = safeError(exc);
        throw new Error(`Failed to parse JSON file '${content}' for: ${err.message}`);
    }
}
exports.readJSON = readJSON;
//# sourceMappingURL=util.js.map