{"name": "is-npm", "version": "5.0.0", "description": "Check if your code is running as an npm script", "license": "MIT", "repository": "sindresorhus/is-npm", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["npm", "yarn", "is", "check", "detect", "env", "environment", "run", "script"], "devDependencies": {"ava": "^2.4.0", "tsd": "^0.11.0", "xo": "^0.30.0"}}