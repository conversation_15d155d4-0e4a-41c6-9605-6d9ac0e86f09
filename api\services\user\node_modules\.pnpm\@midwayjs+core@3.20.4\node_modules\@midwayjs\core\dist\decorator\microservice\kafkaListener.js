"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.KafkaListener = void 0;
const __1 = require("../");
function KafkaListener(topic, options = {}) {
    return (target, propertyKey) => {
        options.topic = topic;
        options.propertyKey = propertyKey;
        (0, __1.attachPropertyDataToClass)(__1.MS_CONSUMER_KEY, options, target, propertyKey);
    };
}
exports.KafkaListener = KafkaListener;
//# sourceMappingURL=kafkaListener.js.map