import { ILifeCycle, IMidwayApplication, IMidwayContainer, MidwayDecoratorService } from '@midwayjs/core';
import { TypeORMDataSourceManager } from './dataSourceManager';
export declare class OrmConfiguration implements ILifeCycle {
    app: IMidwayApplication;
    decoratorService: MidwayDecoratorService;
    dataSourceManager: TypeORMDataSourceManager;
    init(): Promise<void>;
    onReady(container: IMidwayContainer): Promise<void>;
    onStop(container: IMidwayContainer): Promise<void>;
}
//# sourceMappingURL=configuration.d.ts.map