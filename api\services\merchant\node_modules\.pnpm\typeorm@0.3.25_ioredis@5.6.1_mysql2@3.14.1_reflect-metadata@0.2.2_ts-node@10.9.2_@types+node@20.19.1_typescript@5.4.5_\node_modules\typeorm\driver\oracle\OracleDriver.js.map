{"version": 3, "sources": ["../../src/driver/oracle/OracleDriver.ts"], "names": [], "mappings": ";;;AACA,iFAA6E;AAC7E,+FAA2F;AAE3F,2DAAuD;AAGvD,oDAAgD;AAChD,gEAA4D;AAE5D,gFAA4E;AAO5E,gDAA4C;AAE5C,kDAA8C;AAC9C,8EAA0E;AAK1E,uCAA0C;AAC1C,gEAA4D;AAK5D;;GAEG;AACH,MAAa,YAAY;IAmOrB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAnNlC;;;WAGG;QACH,WAAM,GAAU,EAAE,CAAA;QAqBlB;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAG,QAAiB,CAAA;QAEtC;;;;;WAKG;QACH,uBAAkB,GAAiB;YAC/B,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;YACV,MAAM;YACN,KAAK;YACL,UAAU;YACV,QAAQ;YACR,SAAS;YACT,OAAO;YACP,KAAK;YACL,SAAS;YACT,SAAS;YACT,KAAK;YACL,UAAU;YACV,MAAM;YACN,kBAAkB;YAClB,MAAM;YACN,WAAW;YACX,0BAA0B;YAC1B,gCAAgC;YAChC,wBAAwB;YACxB,wBAAwB;YACxB,OAAO;YACP,MAAM;YACN,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,aAAa;YACb,MAAM;SACT,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,YAAY,CAAC,CAAA;QAEnD;;;;WAIG;QACH,2BAAsB,GAAmB;YACrC,SAAS;YACT,UAAU;YACV,WAAW;SACd,CAAA;QAED;;;WAGG;QACH,2BAAsB,GAAmB,CAAC,WAAW,CAAC,CAAA;QAEtD;;WAEG;QACH,iBAAY,GAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,0BAAqB,GAAiB;YAClC,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;YACV,SAAS;YACT,KAAK;SACR,CAAA;QAED;;WAEG;QACH,6BAAwB,GAAiB;YACrC,QAAQ;YACR,OAAO;YACP,WAAW;YACX,0BAA0B;YAC1B,gCAAgC;SACnC,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,QAAQ,CAAC,CAAA;QAE/C;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,mBAAmB;YACtC,UAAU,EAAE,WAAW;YACvB,iBAAiB,EAAE,mBAAmB;YACtC,UAAU,EAAE,WAAW;YACvB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,QAAQ;YACnB,WAAW,EAAE,QAAQ;YACrB,aAAa,EAAE,UAAU;YACzB,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EAAE,QAAQ;YACjB,eAAe,EAAE,UAAU;YAC3B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,QAAQ;YACvB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,UAAU;YACxB,gBAAgB,EAAE,UAAU;YAC5B,cAAc,EAAE,UAAU;YAC1B,aAAa,EAAE,UAAU;YACzB,YAAY,EAAE,UAAU;YACxB,aAAa,EAAE,MAAM;SACxB,CAAA;QAED;;WAEG;QACH,qBAAgB,GAAW,GAAG,CAAA;QAE9B;;;WAGG;QACH,qBAAgB,GAAqB;YACjC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACnB,KAAK,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpB,OAAO,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACxB,QAAQ,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YACzB,SAAS,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE;YAC1B,GAAG,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;YACrB,KAAK,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE;YACzB,SAAS,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC3B,0BAA0B,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;YAC5C,gCAAgC,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;SACrD,CAAA;QAED;;;;;;;;;;;WAWG;QACH,mBAAc,GAAG,EAAE,CAAA;QAEnB,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;SAChB,CAAA;QAED,mBAAc,GAAG,MAAM,CAAA;QAOnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAkC,CAAA;QAE5D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAA;QAChC,CAAC;QACD,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEvB,IAAI,CAAC,QAAQ,GAAG,yBAAW,CAAC,kBAAkB,CAC1C,IAAI,CAAC,OAAO,CAAC,WAAW;YACpB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM;YACjC,CAAC,CAAC,IAAI,CAAC,OAAO,CACrB,CAAC,QAAQ,CAAA;QACV,IAAI,CAAC,MAAM,GAAG,yBAAW,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAA;QAEjE,mHAAmH;QACnH,kDAAkD;QAClD,0BAA0B;QAC1B,iDAAiD;QACjD,8BAA8B;QAC9B,qDAAqD;QACrD,yBAAyB;QACzB,gDAAgD;QAChD,EAAE;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACtD,IAAI,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACtD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC;YAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,CAC3B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC1C,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;YAC/C,CAAC,CAAC,CACL,CAAA;YACD,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAC/B,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAClC,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACnE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAA;YAEpD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACjB,IAAI,CAAC,QAAQ,GAAG,MAAM,WAAW,CAAC,kBAAkB,EAAE,CAAA;YAC1D,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;YACtD,CAAC;YAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QAC/B,CAAC;IACL,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,IAAI,iDAAuB,CAAC,QAAQ,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACjC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpE,IAAI,CAAC,MAAM,GAAG,SAAS,CAAA;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,uCAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,OAAO,IAAI,qCAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE;YACJ,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,SAAS;gBAC1C,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACxC,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAChC,CAAC,CACJ,CAAA;QACD,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAkB,CAAA;QACnD,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YAC7D,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;YAC5B,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;YACpD,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,IAAI,UAAU,GAAG,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC,CAAA;QAE7B,IAAI,MAAM,EAAE,CAAC;YACT,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;QAED,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAA;QAEhC,IAAI,iCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAE/C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO;gBACH,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc;gBACpC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY;gBAChC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAO;gBACH,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY;gBAChC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,MAAM;aACpB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACxB,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,IAAI,OAAO,KAAK,KAAK,QAAQ;gBAAE,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAA;YACpE,OAAO,GAAG,EAAE,CACR,YAAY,qBAAS,CAAC,qBAAqB,CACvC,KAAK,CACR,kBAAkB,CAAA;QAC3B,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,gCAAgC,EAC1D,CAAC;YACC,OAAO,qBAAS,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;QAC3C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,+CAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IAAI,cAAc,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAA;QACnB,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,qBAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,IAAI;YAC5B,cAAc,CAAC,IAAI,KAAK,WAAW;YACnC,cAAc,CAAC,IAAI,KAAK,0BAA0B;YAClD,cAAc,CAAC,IAAI,KAAK,gCAAgC,EAC1D,CAAC;YACC,KAAK,GAAG,qBAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,qBAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,qBAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,+CAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAMb;QACG,IACI,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,MAAM,CAAC,IAAI,KAAK,OAAO;YACvB,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,KAAK;YACrB,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,KAAK;YACrB,MAAM,CAAC,IAAI,KAAK,SAAS;YACzB,MAAM,CAAC,IAAI,KAAK,UAAU,EAC5B,CAAC;YACC,OAAO,QAAQ,CAAA;QACnB,CAAC;aAAM,IACH,MAAM,CAAC,IAAI,KAAK,MAAM;YACtB,MAAM,CAAC,IAAI,KAAK,kBAAkB,EACpC,CAAC;YACC,OAAO,OAAO,CAAA;QAClB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YAC7D,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,WAAW,CAAA;QACtB,CAAC;aAAM,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YACzC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YACxC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,EAAE,GAAG,YAAY,CAAA;QAC5B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,OAAO,YAAY,EAAE,CAAA;QACzB,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAoC;QAChD,IAAI,MAAM,CAAC,MAAM;YAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAA;QAElD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,MAAM,CAAC;YACZ,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,WAAW;gBACZ,OAAO,KAAK,CAAA;YAChB,KAAK,KAAK;gBACN,OAAO,MAAM,CAAA;YACjB,KAAK,MAAM;gBACP,OAAO,IAAI,CAAA;YACf;gBACI,OAAO,EAAE,CAAA;QACjB,CAAC;IACL,CAAC;IAED,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QAEtB,oGAAoG;QACpG,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAA;QAC/C,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAA;QAC7D,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QACxC,CAAC;QAED,IAAI,MAAM,CAAC,IAAI,KAAK,0BAA0B,EAAE,CAAC;YAC7C,IAAI;gBACA,WAAW;oBACX,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC;oBACT,iBAAiB,CAAA;QACzB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,gCAAgC,EAAE,CAAC;YAC1D,IAAI;gBACA,WAAW;oBACX,CAAC,MAAM,CAAC,SAAS,KAAK,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS;wBACxD,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG;wBAC9B,CAAC,CAAC,EAAE,CAAC;oBACT,uBAAuB,CAAA;QAC/B,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,OAAO,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACf,OAAO,IAAI,CAAC,IAAI,oBAAY,CAAC,sBAAsB,CAAC,CAAC,CAAA;YACzD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,aAAa,CACrB,CAAC,GAAQ,EAAE,UAAe,EAAE,OAAiB,EAAE,EAAE;gBAC7C,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,CAAC,UAAU,CAAC,CAAA;YAClB,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;YAAE,OAAO,IAAI,CAAC,sBAAsB,EAAE,CAAA;QAE7D,OAAO,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACjC,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;YAE7D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,aAAa,CAAC,CAAC,GAAQ,EAAE,UAAe,EAAE,EAAE;gBAC5D,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,CAAC,UAAU,CAAC,CAAA;YAClB,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAwB,EAAE,YAA2B;QACpE,IAAI,CAAC,YAAY;YAAE,OAAO,SAAS,CAAA;QAEnC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YACjD,MAAM,MAAM,GAAG,QAAQ,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAA;YACvD,IAAI,MAAM,EAAE,CAAC;gBACT,mBAAQ,CAAC,SAAS,CACd,GAAG,EACH,MAAM,CAAC,cAAc,CACjB,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CACvD,CACJ,CAAA;YACL,CAAC;YACD,OAAO,GAAG,CAAA;QACd,CAAC,EAAE,EAAmB,CAAC,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC;gBAC3D,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK;gBAC1C,oDAAoD;gBACpD,WAAW,CAAC,OAAO,KAAK,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;gBAC7D,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,YAAY,KAAK,cAAc,CAAC,YAAY;gBACxD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,CAAC,cAAc,CAAC,kBAAkB,KAAK,MAAM;oBACzC,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW,CAAC,CAAA;YAE/D,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,iCAAiC;YACjC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,kCAAkC;YAClC,QAAQ;YACR,mBAAmB;YACnB,sBAAsB;YACtB,+BAA+B;YAC/B,iDAAiD;YACjD,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,8BAA8B;YAC9B,qCAAqC;YACrC,uCAAuC;YACvC,oCAAoC;YACpC,8DAA8D;YAC9D,iBAAiB;YACjB,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,mCAAmC;YACnC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,yBAAyB;YACzB,kCAAkC;YAClC,qCAAqC;YACrC,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,mBAAmB;YACnB,0BAA0B;YAC1B,mCAAmC;YACnC,sCAAsC;YACtC,QAAQ;YACR,gEAAgE;YAChE,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,OAAO,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC,CAAA;IAC9C,CAAC;IAED;;OAEG;IACH,2BAA2B,CAAC,IAAgB;QACxC,QAAQ,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,IAAW,EAAE,CAAC,EAAE,CAAC;YAChD,KAAK,QAAQ,CAAC;YACd,KAAK,SAAS,CAAC;YACf,KAAK,KAAK,CAAC;YACX,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,KAAK,CAAC;YACX,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,cAAc,CAAA;YACrC,KAAK,MAAM,CAAC;YACZ,KAAK,OAAO,CAAC;YACb,KAAK,WAAW,CAAC;YACjB,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,MAAM,CAAC,eAAe,CAAA;YACtC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;YACnC,KAAK,aAAa,CAAC;YACnB,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;YACnC,KAAK,MAAM,CAAC;YACZ,KAAK,WAAW,CAAC;YACjB,KAAK,0BAA0B,CAAC;YAChC,KAAK,gCAAgC;gBACjC,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAA;YACxC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;QACvC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,6BAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACpE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,+DAA8B,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAClE,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAA;QACxC,IAAI,SAAS,EAAE,CAAC;YACZ,OAAO,SAAS,KAAK,QAAQ;gBACzB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;gBACzC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CACtB,OAAgC,EAChC,WAA+C;QAE/C,WAAW,GAAG,MAAM,CAAC,MAAM,CACvB,EAAE,EACF,WAAW,EACX,yBAAW,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAC9C,CAAA,CAAC,yBAAyB;QAE3B,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC;YAC7B,IAAI,OAAO,GAAG,gBAAgB,CAAA;YAE9B,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,CAAA;YAC3C,CAAC;YAED,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC;gBACnB,OAAO,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,CAAA;YAC3C,CAAC;YAED,IAAI,WAAW,GAAG,oBAAoB,CAAA;YAEtC,IAAI,WAAW,CAAC,GAAG,EAAE,CAAC;gBAClB,WAAW,IAAI,QAAQ,WAAW,CAAC,GAAG,GAAG,CAAA;YAC7C,CAAC;YAED,IAAI,WAAW,CAAC,WAAW,EAAE,CAAC;gBAC1B,WAAW,IAAI,iBAAiB,WAAW,CAAC,WAAW,GAAG,CAAA;YAC9D,CAAC;YAED,MAAM,aAAa,GAAG,yBAAyB,OAAO,kBAAkB,WAAW,IAAI,CAAA;YACvF,MAAM,CAAC,MAAM,CAAC,WAAW,EAAE,EAAE,aAAa,EAAE,CAAC,CAAA;QACjD,CAAC;QAED,0CAA0C;QAC1C,MAAM,iBAAiB,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,EACF;YACI,IAAI,EAAE,WAAW,CAAC,QAAQ;YAC1B,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,aAAa,EAAE,WAAW,CAAC,aAAa;SAC3C,EACD;YACI,OAAO,EAAE,OAAO,CAAC,QAAQ;SAC5B,EACD,OAAO,CAAC,KAAK,IAAI,EAAE,CACtB,CAAA;QAED,6DAA6D;QAC7D,+DAA+D;QAC/D,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAC,GAAQ,EAAE,IAAS,EAAE,EAAE;gBAC9D,IAAI,GAAG;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACzB,EAAE,CAAC,IAAI,CAAC,CAAA;YACZ,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,SAAS,CAAC,IAAS;QAC/B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;YAClD,IAAI,GAAG,SAAS,CAAA;QACpB,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AAjhCD,oCAihCC", "file": "OracleDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { ConnectionIsNotSetError } from \"../../error/ConnectionIsNotSetError\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { OracleQueryRunner } from \"./OracleQueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { OracleConnectionOptions } from \"./OracleConnectionOptions\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { OracleConnectionCredentialsOptions } from \"./OracleConnectionCredentialsOptions\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TypeORMError } from \"../../error\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\nimport { OnDeleteType } from \"../../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../../metadata/types/OnUpdateType\"\n\n/**\n * Organizes communication with Oracle RDBMS.\n */\nexport class OracleDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Underlying oracle library.\n     */\n    oracle: any\n\n    /**\n     * Pool for master database.\n     */\n    master: any\n\n    /**\n     * Pool for slave databases.\n     * Used in replication.\n     */\n    slaves: any[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: OracleConnectionOptions\n\n    /**\n     * Database name used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport = \"nested\" as const\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://www.techonthenet.com/oracle/datatypes.php\n     * @see https://docs.oracle.com/cd/B28359_01/server.111/b28318/datatype.htm#CNCPT012\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"char\",\n        \"nchar\",\n        \"nvarchar2\",\n        \"varchar2\",\n        \"long\",\n        \"raw\",\n        \"long raw\",\n        \"number\",\n        \"numeric\",\n        \"float\",\n        \"dec\",\n        \"decimal\",\n        \"integer\",\n        \"int\",\n        \"smallint\",\n        \"real\",\n        \"double precision\",\n        \"date\",\n        \"timestamp\",\n        \"timestamp with time zone\",\n        \"timestamp with local time zone\",\n        \"interval year to month\",\n        \"interval day to second\",\n        \"bfile\",\n        \"blob\",\n        \"clob\",\n        \"nclob\",\n        \"rowid\",\n        \"urowid\",\n        \"simple-json\",\n        \"json\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"merge-into\"]\n\n    /**\n     * Returns list of supported onDelete types by driver.\n     * https://docs.oracle.com/en/database/oracle/oracle-database/21/sqlrf/sql-language-reference.pdf\n     * Oracle does not support NO ACTION, but NO ACTION is set by default in EntityMetadata\n     */\n    supportedOnDeleteTypes: OnDeleteType[] = [\n        \"CASCADE\",\n        \"SET NULL\",\n        \"NO ACTION\",\n    ]\n\n    /**\n     * Returns list of supported onUpdate types by driver.\n     * Oracle does not have onUpdate option, but we allow NO ACTION since it is set by default in EntityMetadata\n     */\n    supportedOnUpdateTypes: OnUpdateType[] = [\"NO ACTION\"]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"char\",\n        \"nchar\",\n        \"nvarchar2\",\n        \"varchar2\",\n        \"varchar\",\n        \"raw\",\n    ]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\n        \"number\",\n        \"float\",\n        \"timestamp\",\n        \"timestamp with time zone\",\n        \"timestamp with local time zone\",\n    ]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\"number\"]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"timestamp\",\n        createDateDefault: \"CURRENT_TIMESTAMP\",\n        updateDate: \"timestamp\",\n        updateDateDefault: \"CURRENT_TIMESTAMP\",\n        deleteDate: \"timestamp\",\n        deleteDateNullable: true,\n        version: \"number\",\n        treeLevel: \"number\",\n        migrationId: \"number\",\n        migrationName: \"varchar2\",\n        migrationTimestamp: \"number\",\n        cacheId: \"number\",\n        cacheIdentifier: \"varchar2\",\n        cacheTime: \"number\",\n        cacheDuration: \"number\",\n        cacheQuery: \"clob\",\n        cacheResult: \"clob\",\n        metadataType: \"varchar2\",\n        metadataDatabase: \"varchar2\",\n        metadataSchema: \"varchar2\",\n        metadataTable: \"varchar2\",\n        metadataName: \"varchar2\",\n        metadataValue: \"clob\",\n    }\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix: string = \":\"\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults = {\n        char: { length: 1 },\n        nchar: { length: 1 },\n        varchar: { length: 255 },\n        varchar2: { length: 255 },\n        nvarchar2: { length: 255 },\n        raw: { length: 2000 },\n        float: { precision: 126 },\n        timestamp: { precision: 6 },\n        \"timestamp with time zone\": { precision: 6 },\n        \"timestamp with local time zone\": { precision: 6 },\n    }\n\n    /**\n     * Max length allowed by Oracle for aliases.\n     * @see https://docs.oracle.com/database/121/SQLRF/sql_elements008.htm#SQLRF51129\n     * > The following list of rules applies to both quoted and nonquoted identifiers unless otherwise indicated\n     * > Names must be from 1 to 30 bytes long with these exceptions:\n     * > [...]\n     *\n     * Since Oracle 12.2 (with a compatible driver/client), the limit has been set to 128.\n     * @see https://docs.oracle.com/en/database/oracle/oracle-database/12.2/sqlrf/Database-Object-Names-and-Qualifiers.html\n     *\n     * > If COMPATIBLE is set to a value of 12.2 or higher, then names must be from 1 to 128 bytes long with these exceptions\n     */\n    maxAliasLength = 29\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n    }\n\n    dummyTableName = \"DUAL\"\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = connection.options as OracleConnectionOptions\n\n        if (this.options.useUTC === true) {\n            process.env.ORA_SDTZ = \"UTC\"\n        }\n        // load oracle package\n        this.loadDependencies()\n\n        this.database = DriverUtils.buildDriverOptions(\n            this.options.replication\n                ? this.options.replication.master\n                : this.options,\n        ).database\n        this.schema = DriverUtils.buildDriverOptions(this.options).schema\n\n        // Object.assign(connection.options, DriverUtils.buildDriverOptions(connection.options)); // todo: do it better way\n        // validate options to make sure everything is set\n        // if (!this.options.host)\n        //     throw new DriverOptionNotSetError(\"host\");\n        // if (!this.options.username)\n        //     throw new DriverOptionNotSetError(\"username\");\n        // if (!this.options.sid)\n        //     throw new DriverOptionNotSetError(\"sid\");\n        //\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     * Based on pooling options, it can either create connection immediately,\n     * either create a pool and create connection when needed.\n     */\n    async connect(): Promise<void> {\n        this.oracle.fetchAsString = [this.oracle.DB_TYPE_CLOB]\n        this.oracle.fetchAsBuffer = [this.oracle.DB_TYPE_BLOB]\n        if (this.options.replication) {\n            this.slaves = await Promise.all(\n                this.options.replication.slaves.map((slave) => {\n                    return this.createPool(this.options, slave)\n                }),\n            )\n            this.master = await this.createPool(\n                this.options,\n                this.options.replication.master,\n            )\n        } else {\n            this.master = await this.createPool(this.options, this.options)\n        }\n\n        if (!this.database || !this.schema) {\n            const queryRunner = this.createQueryRunner(\"master\")\n\n            if (!this.database) {\n                this.database = await queryRunner.getCurrentDatabase()\n            }\n\n            if (!this.schema) {\n                this.schema = await queryRunner.getCurrentSchema()\n            }\n\n            await queryRunner.release()\n        }\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with the database.\n     */\n    async disconnect(): Promise<void> {\n        if (!this.master) {\n            throw new ConnectionIsNotSetError(\"oracle\")\n        }\n\n        await this.closePool(this.master)\n        await Promise.all(this.slaves.map((slave) => this.closePool(slave)))\n        this.master = undefined\n        this.slaves = []\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode) {\n        return new OracleQueryRunner(this, mode)\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => {\n                if (typeof nativeParameters[key] === \"boolean\")\n                    return nativeParameters[key] ? 1 : 0\n                return nativeParameters[key]\n            },\n        )\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        const parameterIndexMap = new Map<string, number>()\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                if (parameterIndexMap.has(key)) {\n                    return this.parametersPrefix + parameterIndexMap.get(key)\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                }\n\n                if (typeof value === \"boolean\") {\n                    return value ? \"1\" : \"0\"\n                }\n\n                escapedParameters.push(value)\n                parameterIndexMap.set(key, escapedParameters.length)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return `\"${columnName}\"`\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * Oracle does not support table schemas. One user can have only one schema.\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        const tablePath = [tableName]\n\n        if (schema) {\n            tablePath.unshift(schema)\n        }\n\n        return tablePath.join(\".\")\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = this.schema\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(target.name)\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        if (parts.length === 3) {\n            return {\n                database: parts[0] || driverDatabase,\n                schema: parts[1] || driverSchema,\n                tableName: parts[2],\n            }\n        } else if (parts.length === 2) {\n            return {\n                database: driverDatabase,\n                schema: parts[0] || driverSchema,\n                tableName: parts[1],\n            }\n        } else {\n            return {\n                database: driverDatabase,\n                schema: driverSchema,\n                tableName: target,\n            }\n        }\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (columnMetadata.type === Boolean) {\n            return value ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            if (typeof value === \"string\") value = value.replace(/[^0-9-]/g, \"\")\n            return () =>\n                `TO_DATE('${DateUtils.mixedDateToDateString(\n                    value,\n                )}', 'YYYY-MM-DD')`\n        } else if (\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp with local time zone\"\n        ) {\n            return DateUtils.mixedDateToDate(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (columnMetadata.type === \"json\") {\n            return DateUtils.simpleJsonToString(value)\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (columnMetadata.type === Boolean) {\n            value = !!value\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (\n            columnMetadata.type === Date ||\n            columnMetadata.type === \"timestamp\" ||\n            columnMetadata.type === \"timestamp with time zone\" ||\n            columnMetadata.type === \"timestamp with local time zone\"\n        ) {\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n        isArray?: boolean\n    }): string {\n        if (\n            column.type === Number ||\n            column.type === Boolean ||\n            column.type === \"numeric\" ||\n            column.type === \"dec\" ||\n            column.type === \"decimal\" ||\n            column.type === \"int\" ||\n            column.type === \"integer\" ||\n            column.type === \"smallint\"\n        ) {\n            return \"number\"\n        } else if (\n            column.type === \"real\" ||\n            column.type === \"double precision\"\n        ) {\n            return \"float\"\n        } else if (column.type === String || column.type === \"varchar\") {\n            return \"varchar2\"\n        } else if (column.type === Date) {\n            return \"timestamp\"\n        } else if ((column.type as any) === Buffer) {\n            return \"blob\"\n        } else if (column.type === \"uuid\") {\n            return \"varchar2\"\n        } else if (column.type === \"simple-array\") {\n            return \"clob\"\n        } else if (column.type === \"simple-json\") {\n            return \"clob\"\n        } else if (column.type === \"json\") {\n            return \"json\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (typeof defaultValue === \"number\") {\n            return \"\" + defaultValue\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"1\" : \"0\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            return defaultValue()\n        }\n\n        if (typeof defaultValue === \"string\") {\n            return `'${defaultValue}'`\n        }\n\n        if (defaultValue === null || defaultValue === undefined) {\n            return undefined\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.uniques.some(\n            (uq) => uq.columns.length === 1 && uq.columns[0] === column,\n        )\n    }\n\n    /**\n     * Calculates column length taking into account the default length values.\n     */\n    getColumnLength(column: ColumnMetadata | TableColumn): string {\n        if (column.length) return column.length.toString()\n\n        switch (column.type) {\n            case String:\n            case \"varchar\":\n            case \"varchar2\":\n            case \"nvarchar2\":\n                return \"255\"\n            case \"raw\":\n                return \"2000\"\n            case \"uuid\":\n                return \"36\"\n            default:\n                return \"\"\n        }\n    }\n\n    createFullType(column: TableColumn): string {\n        let type = column.type\n\n        // used 'getColumnLength()' method, because in Oracle column length is required for some data types.\n        if (this.getColumnLength(column)) {\n            type += `(${this.getColumnLength(column)})`\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += \"(\" + column.precision + \",\" + column.scale + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += \"(\" + column.precision + \")\"\n        }\n\n        if (column.type === \"timestamp with time zone\") {\n            type =\n                \"TIMESTAMP\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\") +\n                \" WITH TIME ZONE\"\n        } else if (column.type === \"timestamp with local time zone\") {\n            type =\n                \"TIMESTAMP\" +\n                (column.precision !== null && column.precision !== undefined\n                    ? \"(\" + column.precision + \")\"\n                    : \"\") +\n                \" WITH LOCAL TIME ZONE\"\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        return new Promise<any>((ok, fail) => {\n            if (!this.master) {\n                return fail(new TypeORMError(\"Driver not Connected\"))\n            }\n\n            this.master.getConnection(\n                (err: any, connection: any, release: Function) => {\n                    if (err) return fail(err)\n                    ok(connection)\n                },\n            )\n        })\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        if (!this.slaves.length) return this.obtainMasterConnection()\n\n        return new Promise<any>((ok, fail) => {\n            const random = Math.floor(Math.random() * this.slaves.length)\n\n            this.slaves[random].getConnection((err: any, connection: any) => {\n                if (err) return fail(err)\n                ok(connection)\n            })\n        })\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(metadata: EntityMetadata, insertResult: ObjectLiteral) {\n        if (!insertResult) return undefined\n\n        return Object.keys(insertResult).reduce((map, key) => {\n            const column = metadata.findColumnWithDatabaseName(key)\n            if (column) {\n                OrmUtils.mergeDeep(\n                    map,\n                    column.createValueMap(\n                        this.prepareHydratedValue(insertResult[key], column),\n                    ),\n                )\n            }\n            return map\n        }, {} as ObjectLiteral)\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                tableColumn.length !== this.getColumnLength(columnMetadata) ||\n                tableColumn.precision !== columnMetadata.precision ||\n                tableColumn.scale !== columnMetadata.scale ||\n                // || tableColumn.comment !== columnMetadata.comment\n                tableColumn.default !== this.normalizeDefault(columnMetadata) ||\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.asExpression !== columnMetadata.asExpression ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                (columnMetadata.generationStrategy !== \"uuid\" &&\n                    tableColumn.isGenerated !== columnMetadata.isGenerated)\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         columnMetadata.length,\n            //     )\n            //     console.log(\n            //         \"precision:\",\n            //         tableColumn.precision,\n            //         columnMetadata.precision,\n            //     )\n            //     console.log(\"scale:\", tableColumn.scale, columnMetadata.scale)\n            //     console.log(\n            //         \"comment:\",\n            //         tableColumn.comment,\n            //         columnMetadata.comment,\n            //     )\n            //     console.log(\n            //         \"default:\",\n            //         tableColumn.default,\n            //         this.normalizeDefault(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"enum:\",\n            //         tableColumn.enum &&\n            //             columnMetadata.enum &&\n            //             !OrmUtils.isArraysEqual(\n            //                 tableColumn.enum,\n            //                 columnMetadata.enum.map((val) => val + \"\"),\n            //             ),\n            //     )\n            //     console.log(\n            //         \"onUpdate:\",\n            //         tableColumn.onUpdate,\n            //         columnMetadata.onUpdate,\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable:\",\n            //         tableColumn.isNullable,\n            //         columnMetadata.isNullable,\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         tableColumn.asExpression,\n            //         columnMetadata.asExpression,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"isGenerated:\",\n            //         tableColumn.isGenerated,\n            //         columnMetadata.isGenerated,\n            //     )\n            //     console.log(\"==========================================\")\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return true\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        return this.parametersPrefix + (index + 1)\n    }\n\n    /**\n     * Converts column type in to native oracle type.\n     */\n    columnTypeToNativeParameter(type: ColumnType): any {\n        switch (this.normalizeType({ type: type as any })) {\n            case \"number\":\n            case \"numeric\":\n            case \"int\":\n            case \"integer\":\n            case \"smallint\":\n            case \"dec\":\n            case \"decimal\":\n                return this.oracle.DB_TYPE_NUMBER\n            case \"char\":\n            case \"nchar\":\n            case \"nvarchar2\":\n            case \"varchar2\":\n                return this.oracle.DB_TYPE_VARCHAR\n            case \"blob\":\n                return this.oracle.DB_TYPE_BLOB\n            case \"simple-json\":\n            case \"clob\":\n                return this.oracle.DB_TYPE_CLOB\n            case \"date\":\n            case \"timestamp\":\n            case \"timestamp with time zone\":\n            case \"timestamp with local time zone\":\n                return this.oracle.DB_TYPE_TIMESTAMP\n            case \"json\":\n                return this.oracle.DB_TYPE_JSON\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Loads all driver dependencies.\n     */\n    protected loadDependencies(): void {\n        try {\n            const oracle = this.options.driver || PlatformTools.load(\"oracledb\")\n            this.oracle = oracle\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\"Oracle\", \"oracledb\")\n        }\n        const thickMode = this.options.thickMode\n        if (thickMode) {\n            typeof thickMode === \"object\"\n                ? this.oracle.initOracleClient(thickMode)\n                : this.oracle.initOracleClient()\n        }\n    }\n\n    /**\n     * Creates a new connection pool for a given database credentials.\n     */\n    protected async createPool(\n        options: OracleConnectionOptions,\n        credentials: OracleConnectionCredentialsOptions,\n    ): Promise<any> {\n        credentials = Object.assign(\n            {},\n            credentials,\n            DriverUtils.buildDriverOptions(credentials),\n        ) // todo: do it better way\n\n        if (!credentials.connectString) {\n            let address = `(PROTOCOL=TCP)`\n\n            if (credentials.host) {\n                address += `(HOST=${credentials.host})`\n            }\n\n            if (credentials.port) {\n                address += `(PORT=${credentials.port})`\n            }\n\n            let connectData = `(SERVER=DEDICATED)`\n\n            if (credentials.sid) {\n                connectData += `(SID=${credentials.sid})`\n            }\n\n            if (credentials.serviceName) {\n                connectData += `(SERVICE_NAME=${credentials.serviceName})`\n            }\n\n            const connectString = `(DESCRIPTION=(ADDRESS=${address})(CONNECT_DATA=${connectData}))`\n            Object.assign(credentials, { connectString })\n        }\n\n        // build connection options for the driver\n        const connectionOptions = Object.assign(\n            {},\n            {\n                user: credentials.username,\n                password: credentials.password,\n                connectString: credentials.connectString,\n            },\n            {\n                poolMax: options.poolSize,\n            },\n            options.extra || {},\n        )\n\n        // pooling is enabled either when its set explicitly to true,\n        // either when its not defined at all (e.g. enabled by default)\n        return new Promise<void>((ok, fail) => {\n            this.oracle.createPool(connectionOptions, (err: any, pool: any) => {\n                if (err) return fail(err)\n                ok(pool)\n            })\n        })\n    }\n\n    /**\n     * Closes connection pool.\n     */\n    protected async closePool(pool: any): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            pool.close((err: any) => (err ? fail(err) : ok()))\n            pool = undefined\n        })\n    }\n}\n"], "sourceRoot": "../.."}