{"version": 3, "file": "expose-metadata.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/metadata/expose-metadata.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ExposeOptions } from '..';\n\n/**\n * This object represents metadata assigned to a property via the @Expose decorator.\n */\nexport interface ExposeMetadata {\n  target: Function;\n\n  /**\n   * The property name this metadata belongs to on the target (class or property).\n   *\n   * Note: If the decorator is applied to a class the propertyName will be undefined.\n   */\n  propertyName: string | undefined;\n\n  /**\n   * Options passed to the @Expose operator for this property.\n   */\n  options: ExposeOptions;\n}\n"]}