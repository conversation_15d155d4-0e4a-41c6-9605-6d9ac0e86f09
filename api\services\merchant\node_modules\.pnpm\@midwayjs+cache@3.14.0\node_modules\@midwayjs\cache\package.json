{"name": "@midwayjs/cache", "version": "3.14.0", "description": "midway cache manager", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit"}, "author": "", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "license": "MIT", "repository": {"type": "git", "url": "**************:midwayjs/midway.git"}, "keywords": ["midway", "cache"], "engines": {"node": ">=12"}, "devDependencies": {"@midwayjs/core": "^3.14.0", "@midwayjs/mock": "^3.14.0"}, "dependencies": {"cache-manager": "3.6.3"}}