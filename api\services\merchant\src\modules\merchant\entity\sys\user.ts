import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 商户系统用户
 */
@Entity('merchant_sys_user')
export class MerchantSysUserEntity extends BaseEntity {
  @Column({ comment: '用户名' })
  userName: string;

  @Column({ comment: '密码' })
  password: string;

  @Column({ comment: '密码版本', default: 1 })
  passwordV: number;

  @Column({ comment: '昵称', nullable: true })
  nickName: string;

  @Column({ comment: '头像', nullable: true })
  avatar: string;

  @Column({ comment: '状态 0:禁用 1：启用', default: 1 })
  status: number;

  @Column({ comment: '手机', nullable: true })
  phone: string;

  @Column({ comment: '邮箱', nullable: true })
  email: string;

  @Column({ comment: '角色', type: 'simple-array' })
  roles: string[];

  @Column({ comment: '按钮权限', type: 'simple-array' })
  buttons: string[];

  @Column({ comment: '备注', nullable: true })
  remark: string;
} 