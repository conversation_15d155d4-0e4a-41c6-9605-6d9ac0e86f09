USE `merchant_service_db`;

-- ======================================
-- 第一步：重建菜单表 (对应 AppRouteRecord)
-- ======================================
DROP TABLE IF EXISTS `merchant_sys_menu`;
CREATE TABLE `merchant_sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID (对应前端 AppRouteRecord.id)',
  `parentId` int(11) NOT NULL DEFAULT '0' COMMENT '父级菜单ID',
  `name` varchar(100) NOT NULL COMMENT '路由名称 (对应前端 AppRouteRecord.name)',
  `router` varchar(200) DEFAULT NULL COMMENT '路由路径 (对应前端 AppRouteRecord.path)',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径 (对应前端 AppRouteRecord.component)',
  `title` varchar(100) NOT NULL COMMENT '菜单标题 (对应前端 RouteMeta.title)',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标 (对应前端 RouteMeta.icon)',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '菜单类型 0-目录 1-菜单 2-按钮',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `isHide` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏 (对应前端 RouteMeta.isHide)',
  `keepAlive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否缓存 (对应前端 RouteMeta.keepAlive)',
  `authList` text COMMENT '按钮权限列表 (JSON格式, 对应前端 RouteMeta.authList)',
  `roles` text COMMENT '角色权限 (JSON格式, 对应前端 RouteMeta.roles)',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parentId`),
  KEY `idx_type` (`type`),
  KEY `idx_order` (`orderNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统菜单表 (严格对应前端 AppRouteRecord)';

-- ======================================
-- 第二步：插入基础菜单数据
-- ======================================

-- 一级菜单：首页
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(1, 0, 'Dashboard', '/dashboard', '/dashboard/index', '首页', '&#xe7ae;', 1, 1, 0, 1);

-- 一级菜单：商户管理
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(100, 0, 'Merchant', '/merchant', '/index/index', '商户管理', '&#xe7b4;', 0, 2, 0, 0);

-- 商户管理子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(101, 100, 'MerchantSettleIn', '/merchant/settle-in', '/merchant/settle-in', '商户入驻', '', 1, 1, 0, 1),
(102, 100, 'MerchantHeritageAuth', '/merchant/heritage-auth', '/merchant/heritage-auth', '非遗人认证', '', 1, 2, 0, 1),
(103, 100, 'MerchantPersonal', '/merchant/personal', '/merchant/personal', '个人商户', '', 1, 3, 0, 1),
(104, 100, 'MerchantCompany', '/merchant/company', '/merchant/company', '企业商户', '', 1, 4, 0, 1);

-- 一级菜单：系统管理
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(200, 0, 'System', '/system', '/index/index', '系统管理', '&#xe7ca;', 0, 3, 0, 0);

-- 系统管理子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`, `authList`) VALUES
(201, 200, 'SystemUser', '/system/user', '/system/user/index', '用户管理', '', 1, 1, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]'),
(202, 200, 'SystemRole', '/system/role', '/system/role/index', '角色管理', '', 1, 2, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]'),
(203, 200, 'SystemMenu', '/system/menu', '/system/menu/index', '菜单管理', '', 1, 3, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]');

-- ======================================
-- 第三步：插入模板中心和组件中心菜单
-- ======================================

-- 一级菜单：模板中心
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(50, 0, 'Template', '/template', '/index/index', '模板中心', '&#xe860;', 0, 4, 0, 0);

-- 模板中心子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(51, 50, 'TemplateCards', '/template/cards', '/template/cards', '卡片', '', 1, 1, 0, 1),
(52, 50, 'TemplateBanners', '/template/banners', '/template/banners', '横幅', '', 1, 2, 0, 1),
(53, 50, 'TemplateCharts', '/template/charts', '/template/charts', '图表', '', 1, 3, 0, 1),
(54, 50, 'TemplateMap', '/template/map', '/template/map', '地图', '', 1, 4, 0, 1),
(55, 50, 'TemplateChat', '/template/chat', '/template/chat', '聊天', '', 1, 5, 0, 1),
(56, 50, 'TemplateCalendar', '/template/calendar', '/template/calendar', '日历', '', 1, 6, 0, 1),
(57, 50, 'TemplatePricing', '/template/pricing', '/template/pricing', '定价', '', 1, 7, 0, 1);

-- 一级菜单：组件中心
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(60, 0, 'Widgets', '/widgets', '/index/index', '组件中心', '&#xe81a;', 0, 5, 0, 0);

-- 组件中心子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(61, 60, 'WidgetsIconList', '/widgets/icon-list', '/widgets/icon-list', '图标列表', '', 1, 1, 0, 1),
(62, 60, 'WidgetsIconSelector', '/widgets/icon-selector', '/widgets/icon-selector', '图标选择器', '', 1, 2, 0, 1),
(63, 60, 'WidgetsImageCrop', '/widgets/image-crop', '/widgets/image-crop', '图片裁剪', '', 1, 3, 0, 1),
(64, 60, 'WidgetsExcel', '/widgets/excel', '/widgets/excel', 'Excel', '', 1, 4, 0, 1),
(65, 60, 'WidgetsVideo', '/widgets/video', '/widgets/video', '视频', '', 1, 5, 0, 1),
(66, 60, 'WidgetsCountTo', '/widgets/count-to', '/widgets/count-to', '数字动画', '', 1, 6, 0, 1),
(67, 60, 'WidgetsWangEditor', '/widgets/wang-editor', '/widgets/wang-editor', '富文本编辑器', '', 1, 7, 0, 1),
(68, 60, 'WidgetsWatermark', '/widgets/watermark', '/widgets/watermark', '水印', '', 1, 8, 0, 1),
(69, 60, 'WidgetsContextMenu', '/widgets/context-menu', '/widgets/context-menu', '右键菜单', '', 1, 9, 0, 1),
(70, 60, 'WidgetsQrcode', '/widgets/qrcode', '/widgets/qrcode', '二维码', '', 1, 10, 0, 1),
(71, 60, 'WidgetsDrag', '/widgets/drag', '/widgets/drag', '拖拽', '', 1, 11, 0, 1),
(72, 60, 'WidgetsTextScroll', '/widgets/text-scroll', '/widgets/text-scroll', '文字滚动', '', 1, 12, 0, 1),
(73, 60, 'WidgetsFireworks', '/widgets/fireworks', '/widgets/fireworks', '烟花效果', '', 1, 13, 0, 1);

-- ======================================
-- 第四步：验证菜单数据
-- ======================================
SELECT '========== 菜单恢复完成 ==========' AS message;

-- 显示菜单统计
SELECT 
  'menu_summary' as type,
  COUNT(*) as total_menus,
  SUM(CASE WHEN parentId = 0 THEN 1 ELSE 0 END) as top_level_menus,
  SUM(CASE WHEN type = 0 THEN 1 ELSE 0 END) as directories,
  SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) as pages,
  SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) as buttons
FROM merchant_sys_menu;

-- 显示菜单树结构
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', title)
    WHEN type = 1 THEN CONCAT('  📄 ', title)
    WHEN type = 2 THEN CONCAT('    🔘 ', title)
  END as menu_tree,
  id,
  parentId,
  router,
  name
FROM merchant_sys_menu 
ORDER BY 
  CASE WHEN parentId = 0 THEN id ELSE parentId END,
  CASE WHEN parentId = 0 THEN 0 ELSE 1 END,
  orderNum; 