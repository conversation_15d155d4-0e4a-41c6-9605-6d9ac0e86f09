import * as FsStore from "@cool-midway/cache-manager-fs-hash";
/**
 * cool 基于磁盘的缓存
 */
declare class FsCacheStore {
    options: any;
    store: FsStore;
    constructor(options?: {});
    /**
     * 获得
     * @param key
     * @returns
     */
    get<T>(key: string): Promise<T>;
    /**
     * 设置
     * @param key
     * @param value
     * @param ttl
     */
    set<T>(key: string, value: T, ttl: number): Promise<void>;
    /**
     * 删除
     * @param key
     */
    del(key: string): Promise<void>;
    /**
     * 重置
     */
    reset(): Promise<void>;
}
export declare const CoolCacheStore: (options?: {}) => FsCacheStore;
export {};
