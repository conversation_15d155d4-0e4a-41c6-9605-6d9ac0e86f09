{"version": 3, "sources": ["../../src/driver/mongodb/MongoQueryRunner.ts"], "names": [], "mappings": ";;;AAWA,8DAA0D;AAG1D,uCAA0C;AA8C1C;;GAEG;AACH,MAAa,gBAAgB;IAsDzB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB,EAAE,kBAA+B;QAtCnE;;;;WAIG;QACH,eAAU,GAAG,KAAK,CAAA;QAElB;;;WAGG;QACH,wBAAmB,GAAG,KAAK,CAAA;QAE3B;;;WAGG;QACH,SAAI,GAAG,EAAE,CAAA;QAsBL,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;QAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,aAAa;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,aAAa;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAsB,EAAE,MAAwB;QACnD,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,SAAS,CACL,cAAsB,EACtB,QAAoB,EACpB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CAC/C,QAAQ,EACR,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,cAAsB,EACtB,UAA6C,EAC7C,OAA0B;QAE1B,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CACrD,UAAU,EACV,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,cAAsB,EACtB,MAAwB,EACxB,OAAsB;QAEtB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,KAAK,CAC3C,MAAM,IAAI,EAAE,EACZ,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,cAAsB,EACtB,MAAwB,EACxB,OAA+B;QAE/B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,cAAc,CACpD,MAAM,IAAI,EAAE,EACZ,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,cAAsB,EACtB,SAA6B,EAC7B,OAA8B;QAE9B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,WAAW,CACjD,SAAS,EACT,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,uBAAuB,CACzB,cAAsB,EACtB,UAA8B;QAE9B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,cAAsB,EACtB,MAAwB,EACxB,OAAsB;QAEtB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,UAAU,CAChD,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,cAAsB,EACtB,MAAwB,EACxB,OAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CAC/C,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CACV,cAAsB,EACtB,GAAQ,EACR,MAAwB,EACxB,OAAiC;QAEjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,CAC9C,GAAG,EACH,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,cAAsB,EACtB,SAAiB,EACjB,OAAiC;QAEjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CAC/C,SAAS,EACT,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,cAAsB;QAC9C,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,WAAW,EAAE,CAAA;IAC3D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,cAAsB,EACtB,MAAwB,EACxB,OAAiC;QAEjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,gBAAgB,CACtD,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,cAAsB,EACtB,MAAwB,EACxB,WAAqB,EACrB,OAAkC;QAElC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,iBAAiB,CACvD,MAAM,EACN,WAAW,EACX,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,cAAsB,EACtB,MAAwB,EACxB,MAA8B,EAC9B,OAAiC;QAEjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,gBAAgB,CACtD,MAAM,EACN,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,cAAsB;QAC1C,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,OAAO,EAAE,CAAA;IACvD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,cAAsB,EACtB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,cAAsB,EACtB,OAAiC;QAEjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,gBAAgB,CACtD,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,uBAAuB,CACnB,cAAsB,EACtB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,uBAAuB,CAC7D,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,yBAAyB,CACrB,cAAsB,EACtB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,yBAAyB,CAC/D,OAAO,CACV,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,cAAsB,EACtB,IAA4B,EAC5B,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,UAAU,CAChD,IAAI,EACJ,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,cAAsB,EACtB,GAAyB,EACzB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,IAAI,EAAE,CAAC,CAAA;IAC3E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,cAAsB;QACjC,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,qBAAqB,CACjB,cAAsB,EACtB,OAA4B;QAE5B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,cAAsB,EACtB,OAAe,EACf,OAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC,CAAA;IAC5E,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,cAAsB,EACtB,MAAwB,EACxB,WAAqB,EACrB,OAAwB;QAExB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,UAAU,CAChD,MAAM,EACN,WAAW,EACX,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,cAAsB,EACtB,OAA0B;QAE1B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACH,KAAK,CACD,cAAsB,EACtB,QAAqB,EACrB,OAA6B;QAE7B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,cAAsB,EACtB,MAAwB,EACxB,MAA8B,EAC9B,OAAuB;QAEvB,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,UAAU,CAChD,MAAM,EACN,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,cAAsB,EACtB,MAAwB,EACxB,MAA8B,EAC9B,OAAuB;QAEvB,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC,SAAS,CACrD,MAAM,EACN,MAAM,EACN,OAAO,IAAI,EAAE,CAChB,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,gDAAgD;IAChD,4EAA4E;IAE5E;;;;OAIG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,IAAI,CAAC,kBAAkB;aACxB,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAS,CAAC;aACpC,YAAY,EAAE,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,KAAkB,CAAC;IAEhC;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,6FAA6F;IACjG,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,qFAAqF;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACnB,qFAAqF;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACrB,qFAAqF;IACzF,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAa,EAAE,UAAkB;QACnC,MAAM,IAAI,oBAAY,CAClB,yDAAyD,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG,CACL,OAA6B,EAC7B,GAAG,MAAiB;QAEpB,MAAM,IAAI,oBAAY,CAClB,yDAAyD,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CACF,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,MAAM,IAAI,oBAAY,CAClB,+DAA+D,CAClE,CAAA;IACL,CAAC;IAED;;;;;;;;;;;;;OAaG;IAEH;;;;;;;OAOG;IAEH;;;;;;;;;;OAUG;IAEH;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,cAAsB;QACjC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,eAAyB;QACrC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,cAAsB;QAChC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,eAAyB;QACpC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED,kBAAkB;QACd,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,IAAI,oBAAY,CAClB,6DAA6D,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,IAAI,oBAAY,CAClB,6DAA6D,CAChE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,IAAI,oBAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,IAAI,oBAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,cAAsB;QACjC,MAAM,IAAI,oBAAY,CAClB,2DAA2D,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,QAAgB;QACjC,MAAM,IAAI,oBAAY,CAClB,8DAA8D,CACjE,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAiB;QAClD,MAAM,IAAI,oBAAY,CAClB,0DAA0D,CAC7D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,KAAY;QAC1B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,SAAyB;QACrC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,IAAU;QACvB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,cAA8B;QAE9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB;QAErB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAAC,WAA2B;QAC5C,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,cAAsB,EAAE,SAAiB;QACrD,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,IAAI,oBAAY,CAClB,4DAA4D,CAC/D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,cAAsB;QACnC,MAAM,IAAI,CAAC,kBAAkB;aACxB,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAS,CAAC;aACpC,cAAc,CAAC,cAAc,CAAC,CAAA;IACvC,CAAC;IAED;;;;OAIG;IACH,eAAe;QACX,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;;;;OAKG;IACH,gBAAgB;QACZ,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,cAAc;QACV,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,YAAY;QACR,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB;QACtB,MAAM,IAAI,oBAAY,CAClB,oDAAoD,CACvD,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,aAAa,CAAC,cAAsB;QAC1C,OAAO,IAAI,CAAC,kBAAkB;aACzB,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAS,CAAC;aACpC,UAAU,CAAC,cAAc,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,oBAAY,CAClB,uDAAuD,CAC1D,CAAA;IACL,CAAC;CACJ;AA1sCD,4CA0sCC", "file": "MongoQueryRunner.js", "sourcesContent": ["import { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { View } from \"../../schema-builder/view/View\"\n// import {Connection} from \"../../connection/Connection\";\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { MongoEntityManager } from \"../../entity-manager/MongoEntityManager\"\nimport { SqlInMemory } from \"../SqlInMemory\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TypeORMError } from \"../../error\"\n\nimport {\n    BulkWriteResult,\n    AggregationCursor,\n    MongoClient,\n    Collection,\n    FindCursor,\n    Document,\n    AggregateOptions,\n    AnyBulkWriteOperation,\n    BulkWriteOptions,\n    Filter,\n    CountOptions,\n    CountDocumentsOptions,\n    IndexSpecification,\n    CreateIndexesOptions,\n    IndexDescription,\n    DeleteResult,\n    DeleteOptions,\n    CommandOperationOptions,\n    FindOneAndDeleteOptions,\n    FindOneAndReplaceOptions,\n    UpdateFilter,\n    FindOneAndUpdateOptions,\n    RenameOptions,\n    ReplaceOptions,\n    UpdateResult,\n    CollStats,\n    CollStatsOptions,\n    ChangeStreamOptions,\n    ChangeStream,\n    UpdateOptions,\n    ListIndexesOptions,\n    ListIndexesCursor,\n    OptionalId,\n    InsertOneOptions,\n    InsertOneResult,\n    InsertManyResult,\n    UnorderedBulkOperation,\n    OrderedBulkOperation,\n    IndexInformationOptions,\n} from \"../../driver/mongodb/typings\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\n\n/**\n * Runs queries on a single MongoDB connection.\n */\nexport class MongoQueryRunner implements QueryRunner {\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by this query runner.\n     */\n    connection: DataSource\n\n    /**\n     * Broadcaster used on this query runner to broadcast entity events.\n     */\n    broadcaster: Broadcaster\n\n    /**\n     * Entity manager working only with current query runner.\n     */\n    manager: MongoEntityManager\n\n    /**\n     * Indicates if connection for this query runner is released.\n     * Once its released, query runner cannot run queries anymore.\n     * Always false for mongodb since mongodb has a single query executor instance.\n     */\n    isReleased = false\n\n    /**\n     * Indicates if transaction is active in this query executor.\n     * Always false for mongodb since mongodb does not support transactions.\n     */\n    isTransactionActive = false\n\n    /**\n     * Stores temporarily user data.\n     * Useful for sharing data with subscribers.\n     */\n    data = {}\n\n    /**\n     * All synchronized tables in the database.\n     */\n    loadedTables: Table[]\n\n    /**\n     * All synchronized views in the database.\n     */\n    loadedViews: View[]\n\n    /**\n     * Real database connection from a connection pool used to perform queries.\n     */\n    databaseConnection: MongoClient\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource, databaseConnection: MongoClient) {\n        this.connection = connection\n        this.databaseConnection = databaseConnection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        // Do nothing\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        // Do nothing\n    }\n\n    /**\n     * Creates a cursor for a query that can be used to iterate over results from MongoDB.\n     */\n    cursor(collectionName: string, filter: Filter<Document>): FindCursor<any> {\n        return this.getCollection(collectionName).find(filter || {})\n    }\n\n    /**\n     * Execute an aggregation framework pipeline against the collection.\n     */\n    aggregate(\n        collectionName: string,\n        pipeline: Document[],\n        options?: AggregateOptions,\n    ): AggregationCursor<any> {\n        return this.getCollection(collectionName).aggregate(\n            pipeline,\n            options || {},\n        )\n    }\n\n    /**\n     * Perform a bulkWrite operation without a fluent API.\n     */\n    async bulkWrite(\n        collectionName: string,\n        operations: AnyBulkWriteOperation<Document>[],\n        options?: BulkWriteOptions,\n    ): Promise<BulkWriteResult> {\n        return await this.getCollection(collectionName).bulkWrite(\n            operations,\n            options || {},\n        )\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    async count(\n        collectionName: string,\n        filter: Filter<Document>,\n        options?: CountOptions,\n    ): Promise<number> {\n        return this.getCollection(collectionName).count(\n            filter || {},\n            options || {},\n        )\n    }\n\n    /**\n     * Count number of matching documents in the db to a query.\n     */\n    async countDocuments(\n        collectionName: string,\n        filter: Filter<Document>,\n        options?: CountDocumentsOptions,\n    ): Promise<any> {\n        return this.getCollection(collectionName).countDocuments(\n            filter || {},\n            options || {},\n        )\n    }\n\n    /**\n     * Creates an index on the db and collection.\n     */\n    async createCollectionIndex(\n        collectionName: string,\n        indexSpec: IndexSpecification,\n        options?: CreateIndexesOptions,\n    ): Promise<string> {\n        return this.getCollection(collectionName).createIndex(\n            indexSpec,\n            options || {},\n        )\n    }\n\n    /**\n     * Creates multiple indexes in the collection, this method is only supported for MongoDB 2.6 or higher.\n     * Earlier version of MongoDB will throw a command not supported error. Index specifications are defined at http://docs.mongodb.org/manual/reference/command/createIndexes/.\n     */\n    async createCollectionIndexes(\n        collectionName: string,\n        indexSpecs: IndexDescription[],\n    ): Promise<string[]> {\n        return this.getCollection(collectionName).createIndexes(indexSpecs)\n    }\n\n    /**\n     * Delete multiple documents on MongoDB.\n     */\n    async deleteMany(\n        collectionName: string,\n        filter: Filter<Document>,\n        options: DeleteOptions,\n    ): Promise<DeleteResult> {\n        return this.getCollection(collectionName).deleteMany(\n            filter,\n            options || {},\n        )\n    }\n\n    /**\n     * Delete a document on MongoDB.\n     */\n    async deleteOne(\n        collectionName: string,\n        filter: Filter<Document>,\n        options?: DeleteOptions,\n    ): Promise<DeleteResult> {\n        return this.getCollection(collectionName).deleteOne(\n            filter,\n            options || {},\n        )\n    }\n\n    /**\n     * The distinct command returns returns a list of distinct values for the given key across a collection.\n     */\n    async distinct(\n        collectionName: string,\n        key: any,\n        filter: Filter<Document>,\n        options?: CommandOperationOptions,\n    ): Promise<any> {\n        return this.getCollection(collectionName).distinct(\n            key,\n            filter,\n            options || {},\n        )\n    }\n\n    /**\n     * Drops an index from this collection.\n     */\n    async dropCollectionIndex(\n        collectionName: string,\n        indexName: string,\n        options?: CommandOperationOptions,\n    ): Promise<Document> {\n        return this.getCollection(collectionName).dropIndex(\n            indexName,\n            options || {},\n        )\n    }\n\n    /**\n     * Drops all indexes from the collection.\n     */\n    async dropCollectionIndexes(collectionName: string): Promise<Document> {\n        return this.getCollection(collectionName).dropIndexes()\n    }\n\n    /**\n     * Find a document and delete it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    async findOneAndDelete(\n        collectionName: string,\n        filter: Filter<Document>,\n        options?: FindOneAndDeleteOptions,\n    ): Promise<Document | null> {\n        return this.getCollection(collectionName).findOneAndDelete(\n            filter,\n            options || {},\n        )\n    }\n\n    /**\n     * Find a document and replace it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    async findOneAndReplace(\n        collectionName: string,\n        filter: Filter<Document>,\n        replacement: Document,\n        options?: FindOneAndReplaceOptions,\n    ): Promise<Document | null> {\n        return this.getCollection(collectionName).findOneAndReplace(\n            filter,\n            replacement,\n            options || {},\n        )\n    }\n\n    /**\n     * Find a document and update it in one atomic operation, requires a write lock for the duration of the operation.\n     */\n    async findOneAndUpdate(\n        collectionName: string,\n        filter: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: FindOneAndUpdateOptions,\n    ): Promise<Document | null> {\n        return this.getCollection(collectionName).findOneAndUpdate(\n            filter,\n            update,\n            options || {},\n        )\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    async collectionIndexes(collectionName: string): Promise<Document> {\n        return this.getCollection(collectionName).indexes()\n    }\n\n    /**\n     * Retrieve all the indexes on the collection.\n     */\n    async collectionIndexExists(\n        collectionName: string,\n        indexes: string | string[],\n    ): Promise<boolean> {\n        return this.getCollection(collectionName).indexExists(indexes)\n    }\n\n    /**\n     * Retrieves this collections index info.\n     */\n    async collectionIndexInformation(\n        collectionName: string,\n        options?: IndexInformationOptions,\n    ): Promise<any> {\n        return this.getCollection(collectionName).indexInformation(\n            options || {},\n        )\n    }\n\n    /**\n     * Initiate an In order bulk write operation, operations will be serially executed in the order they are added, creating a new operation for each switch in types.\n     */\n    initializeOrderedBulkOp(\n        collectionName: string,\n        options?: BulkWriteOptions,\n    ): OrderedBulkOperation {\n        return this.getCollection(collectionName).initializeOrderedBulkOp(\n            options,\n        )\n    }\n\n    /**\n     * Initiate a Out of order batch write operation. All operations will be buffered into insert/update/remove commands executed out of order.\n     */\n    initializeUnorderedBulkOp(\n        collectionName: string,\n        options?: BulkWriteOptions,\n    ): UnorderedBulkOperation {\n        return this.getCollection(collectionName).initializeUnorderedBulkOp(\n            options,\n        )\n    }\n\n    /**\n     * Inserts an array of documents into MongoDB.\n     */\n    async insertMany(\n        collectionName: string,\n        docs: OptionalId<Document>[],\n        options?: BulkWriteOptions,\n    ): Promise<InsertManyResult> {\n        return this.getCollection(collectionName).insertMany(\n            docs,\n            options || {},\n        )\n    }\n\n    /**\n     * Inserts a single document into MongoDB.\n     */\n    async insertOne(\n        collectionName: string,\n        doc: OptionalId<Document>,\n        options?: InsertOneOptions,\n    ): Promise<InsertOneResult> {\n        return this.getCollection(collectionName).insertOne(doc, options || {})\n    }\n\n    /**\n     * Returns if the collection is a capped collection.\n     */\n    async isCapped(collectionName: string): Promise<boolean> {\n        return this.getCollection(collectionName).isCapped()\n    }\n\n    /**\n     * Get the list of all indexes information for the collection.\n     */\n    listCollectionIndexes(\n        collectionName: string,\n        options?: ListIndexesOptions,\n    ): ListIndexesCursor {\n        return this.getCollection(collectionName).listIndexes(options)\n    }\n\n    /**\n     * Reindex all indexes on the collection Warning: reIndex is a blocking operation (indexes are rebuilt in the foreground) and will be slow for large collections.\n     */\n    async rename(\n        collectionName: string,\n        newName: string,\n        options?: RenameOptions,\n    ): Promise<Collection<Document>> {\n        return this.getCollection(collectionName).rename(newName, options || {})\n    }\n\n    /**\n     * Replace a document on MongoDB.\n     */\n    async replaceOne(\n        collectionName: string,\n        filter: Filter<Document>,\n        replacement: Document,\n        options?: ReplaceOptions,\n    ): Promise<Document | UpdateResult> {\n        return this.getCollection(collectionName).replaceOne(\n            filter,\n            replacement,\n            options || {},\n        )\n    }\n\n    /**\n     * Get all the collection statistics.\n     */\n    async stats(\n        collectionName: string,\n        options?: CollStatsOptions,\n    ): Promise<CollStats> {\n        return this.getCollection(collectionName).stats(options || {})\n    }\n\n    /**\n     * Watching new changes as stream.\n     */\n    watch(\n        collectionName: string,\n        pipeline?: Document[],\n        options?: ChangeStreamOptions,\n    ): ChangeStream {\n        return this.getCollection(collectionName).watch(pipeline, options)\n    }\n\n    /**\n     * Update multiple documents on MongoDB.\n     */\n    async updateMany(\n        collectionName: string,\n        filter: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResult> {\n        return this.getCollection(collectionName).updateMany(\n            filter,\n            update,\n            options || {},\n        )\n    }\n\n    /**\n     * Update a single document on MongoDB.\n     */\n    async updateOne(\n        collectionName: string,\n        filter: Filter<Document>,\n        update: UpdateFilter<Document>,\n        options?: UpdateOptions,\n    ): Promise<Document | UpdateResult> {\n        return await this.getCollection(collectionName).updateOne(\n            filter,\n            update,\n            options || {},\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods (from QueryRunner)\n    // -------------------------------------------------------------------------\n\n    /**\n     * Removes all collections from the currently connected database.\n     * Be careful with using this method and avoid using it in production or migrations\n     * (because it can clear all your database).\n     */\n    async clearDatabase(): Promise<void> {\n        await this.databaseConnection\n            .db(this.connection.driver.database!)\n            .dropDatabase()\n    }\n\n    /**\n     * For MongoDB database we don't create connection, because its single connection already created by a driver.\n     */\n    async connect(): Promise<any> {}\n\n    /**\n     * For MongoDB database we don't release connection, because its single connection.\n     */\n    async release(): Promise<void> {\n        // releasing connection are not supported by mongodb driver, so simply don't do anything here\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(): Promise<void> {\n        // transactions are not supported by mongodb driver, so simply don't do anything here\n    }\n\n    /**\n     * Commits transaction.\n     */\n    async commitTransaction(): Promise<void> {\n        // transactions are not supported by mongodb driver, so simply don't do anything here\n    }\n\n    /**\n     * Rollbacks transaction.\n     */\n    async rollbackTransaction(): Promise<void> {\n        // transactions are not supported by mongodb driver, so simply don't do anything here\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    query(query: string, parameters?: any[]): Promise<any> {\n        throw new TypeORMError(\n            `Executing SQL query is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Unsupported - Executing SQL query is not supported by MongoDB driver.\n     */\n    async sql(\n        strings: TemplateStringsArray,\n        ...values: unknown[]\n    ): Promise<any> {\n        throw new TypeORMError(\n            `Executing SQL query is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        throw new TypeORMError(\n            `Stream is not supported by MongoDB driver. Use watch instead.`,\n        )\n    }\n\n    /**\n     * Insert a new row with given values into the given table.\n     * Returns value of inserted object id.\n\n    async insert(collectionName: string, keyValues: ObjectLiteral): Promise<any> { // todo: fix any\n        const results = await this.databaseConnection\n            .collection(collectionName)\n            .insertOne(keyValues);\n        const generatedMap = this.connection.getMetadata(collectionName).objectIdColumn!.createValueMap(results.insertedId);\n        return {\n            result: results,\n            generatedMap: generatedMap\n        };\n    }*/\n\n    /**\n     * Updates rows that match given conditions in the given table.\n\n    async update(collectionName: string, valuesMap: ObjectLiteral, conditions: ObjectLiteral): Promise<any> { // todo: fix any\n        await this.databaseConnection\n            .collection(collectionName)\n            .updateOne(conditions, valuesMap);\n    }*/\n\n    /**\n     * Deletes from the given table by a given conditions.\n\n    async delete(collectionName: string, conditions: ObjectLiteral|ObjectLiteral[]|string, maybeParameters?: any[]): Promise<any> { // todo: fix any\n        if (typeof conditions === \"string\")\n            throw new TypeORMError(`String condition is not supported by MongoDB driver.`);\n\n        await this.databaseConnection\n            .collection(collectionName)\n            .deleteOne(conditions);\n    }*/\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads given table's data from the database.\n     */\n    async getTable(collectionName: string): Promise<Table | undefined> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    async getTables(collectionNames: string[]): Promise<Table[]> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads given views's data from the database.\n     */\n    async getView(collectionName: string): Promise<View | undefined> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads all views (with given names) from the database and creates a Table from them.\n     */\n    async getViews(collectionNames: string[]): Promise<View[]> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    getReplicationMode(): ReplicationMode {\n        return \"master\"\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        throw new TypeORMError(\n            `Check database queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<undefined> {\n        throw new TypeORMError(\n            `Check database queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        throw new TypeORMError(\n            `Check schema queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<undefined> {\n        throw new TypeORMError(\n            `Check schema queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(collectionName: string): Promise<boolean> {\n        throw new TypeORMError(\n            `Check schema queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a database if it's not created.\n     */\n    async createDatabase(database: string): Promise<void> {\n        throw new TypeORMError(\n            `Database create queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        throw new TypeORMError(\n            `Database drop queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema create queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(schemaPath: string, ifExist?: boolean): Promise<void> {\n        throw new TypeORMError(\n            `Schema drop queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new table from the given table and columns inside it.\n     */\n    async createTable(table: Table): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(tableName: Table | string): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(view: View): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableOrName: Table | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(tableOrName: Table | string): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops an unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(collectionName: string, indexName: string): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `Schema update queries are not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Drops collection.\n     */\n    async clearTable(collectionName: string): Promise<void> {\n        await this.databaseConnection\n            .db(this.connection.driver.database!)\n            .dropCollection(collectionName)\n    }\n\n    /**\n     * Enables special query runner mode in which sql queries won't be executed,\n     * instead they will be memorized into a special variable inside query runner.\n     * You can get memorized sql using getMemorySql() method.\n     */\n    enableSqlMemory(): void {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Disables special query runner mode in which sql queries won't be executed\n     * started by calling enableSqlMemory() method.\n     *\n     * Previously memorized sql will be flushed.\n     */\n    disableSqlMemory(): void {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Flushes all memorized sqls.\n     */\n    clearSqlMemory(): void {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Gets sql stored in the memory. Parameters in the sql are already replaced.\n     */\n    getMemorySql(): SqlInMemory {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Executes up sql queries.\n     */\n    async executeMemoryUpSql(): Promise<void> {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    /**\n     * Executes down sql queries.\n     */\n    async executeMemoryDownSql(): Promise<void> {\n        throw new TypeORMError(\n            `This operation is not supported by MongoDB driver.`,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets collection from the database with a given name.\n     */\n    protected getCollection(collectionName: string): Collection<any> {\n        return this.databaseConnection\n            .db(this.connection.driver.database!)\n            .collection(collectionName)\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `mongodb driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}