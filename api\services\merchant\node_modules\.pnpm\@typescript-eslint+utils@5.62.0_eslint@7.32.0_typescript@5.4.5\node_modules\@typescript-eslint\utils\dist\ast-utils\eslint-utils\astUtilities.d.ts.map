{"version": 3, "file": "astUtilities.d.ts", "sourceRoot": "", "sources": ["../../../src/ast-utils/eslint-utils/astUtilities.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,KAAK,QAAQ,MAAM,iBAAiB,CAAC;AACjD,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;;GAIG;AACH,QAAA,MAAM,uBAAuB,SAEvB,SAAS,mBAAmB,GAC5B,SAAS,kBAAkB,GAC3B,SAAS,uBAAuB,cACxB,SAAS,UAAU,KAC5B,SAAS,cAAc,CAAC;AAE7B;;;;GAIG;AACH,QAAA,MAAM,uBAAuB,SAEvB,SAAS,mBAAmB,GAC5B,SAAS,kBAAkB,GAC3B,SAAS,uBAAuB,eACvB,SAAS,UAAU,KAC7B,MAAM,CAAC;AAEZ;;;;;;GAMG;AACH,QAAA,MAAM,eAAe,SAEf,SAAS,gBAAgB,GACzB,SAAS,QAAQ,GACjB,SAAS,gBAAgB,GACzB,SAAS,kBAAkB,iBAChB,SAAS,KAAK,CAAC,KAAK,KAChC,MAAM,GAAG,IAAI,CAAC;AAEnB;;;;;;;;;;GAUG;AACH,QAAA,MAAM,cAAc,SACZ,SAAS,IAAI,iBACJ,SAAS,KAAK,CAAC,KAAK,KAChC;IAAE,KAAK,EAAE,OAAO,CAAA;CAAE,GAAG,IAAI,CAAC;AAE/B;;;;;GAKG;AACH,QAAA,MAAM,mBAAmB,SACjB,SAAS,IAAI,iBACJ,SAAS,KAAK,CAAC,KAAK,KAChC,MAAM,GAAG,IAAI,CAAC;AAEnB;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,QAAA,MAAM,aAAa,SACX,SAAS,IAAI,cACP,SAAS,UAAU,YACrB;IACR,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,8BAA8B,CAAC,EAAE,OAAO,CAAC;CAC1C,KACE,OAAO,CAAC;AAEb,QAAA,MAAM,eAAe;WAUZ,SAAS,IAAI,cAAc,SAAS,UAAU,GAAG,OAAO;YAEtD,MAAM,QACP,SAAS,IAAI,cACP,SAAS,UAAU,GAC9B,OAAO;CACX,CAAC;AAEF,OAAO,EACL,uBAAuB,EACvB,uBAAuB,EACvB,eAAe,EACf,cAAc,EACd,mBAAmB,EACnB,aAAa,EACb,eAAe,GAChB,CAAC"}