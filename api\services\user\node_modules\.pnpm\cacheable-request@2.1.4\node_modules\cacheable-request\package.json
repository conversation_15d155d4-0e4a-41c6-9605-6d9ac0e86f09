{"name": "cacheable-request", "version": "2.1.4", "description": "Wrap native HTTP requests with RFC compliant cache support", "main": "src/index.js", "scripts": {"test": "xo && nyc ava", "coverage": "nyc report --reporter=text-lcov | coveralls"}, "xo": {"extends": "xo-lukechilds"}, "repository": {"type": "git", "url": "git+https://github.com/lukechilds/cacheable-request.git"}, "keywords": ["HTTP", "HTTPS", "cache", "caching", "layer", "cacheable", "RFC 7234", "RFC", "7234", "compliant"], "author": "<PERSON> <<EMAIL>> (http://lukechilds.co.uk)", "license": "MIT", "bugs": {"url": "https://github.com/lukechilds/cacheable-request/issues"}, "homepage": "https://github.com/lukechilds/cacheable-request", "dependencies": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}, "devDependencies": {"@keyv/sqlite": "^1.2.6", "ava": "^0.24.0", "coveralls": "^3.0.0", "create-test-server": "^2.0.0", "delay": "^2.0.0", "eslint-config-xo-lukechilds": "^1.0.0", "nyc": "^11.0.2", "pify": "^3.0.0", "sqlite3": "^3.1.9", "this": "^1.0.2", "xo": "^0.19.0"}}