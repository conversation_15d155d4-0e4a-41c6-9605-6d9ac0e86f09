.markdown-body a {
    color: #c0392b;
}

.menu {
    background-color: #c0392b;
}

.-menu-visible .menu-toggle {
    background-color: #c0392b;
}

.menu-toggle {
    color: #fff;
    background-color: #c0392b;
}

.toc-menu .link.-active, .toc-menu .hlink.-active {
    box-shadow: inset -2px 0 #fff;
}

.toc-menu .-level-1.-parent > .title {
    color: #fff;
}

.toc-menu .-level-1:first-child > .title {
    color: #fff;
}

ul.heading-list .hlink, ul.heading-list .hlink:visited {
    color: #fff;
}

.toc-menu .link, .toc-menu .link:visited {
    color: #fff;
}

ul.heading-list .hlink::before {
    background: #fff;
}

.footer-nav a {
    color: #c0392b;
}

.footer-nav .label, .footer-nav .left .title {
    color: #c0392b;
}

.header-nav.-expanded .icon {
    color: #fff;
}

.pullquote {
    display: block;
    float: right;
    padding: 0 0 0 10px;
    margin: 0 0 10px 10px;
    width: 15.0em;
    font-size: 1.0em;
    line-height: 1.4em;
    color: #666;
    border-left: 3px solid #ccc;
}

.markdown-body pre {
    background-color: #fdf6e3;
    border-radius: 12px;
}

.markdown-body pre code {
    background-color: #fdf6e3;
}

a.download {
  -webkit-user-select: none;
  background: #fff;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,32,0.01)), to(rgba(0,0,32,0.08)));
  background-image: -moz-linear-gradient(rgba(0,0,32,0.01), rgba(0,0,32,0.08));
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  border-radius: 6px;
  color: #202028;
  padding: 4px 10px;
  border: 1px solid #a8a8af;
  text-decoration: none !important;
  text-shadow: 0 1px 1px rgba(255,255,255,0.9);
  font-size: 14px;
  line-height: 19px;
  font-family: helvetica, arial, sans-serif;
  display: inline-block;
  cursor: pointer;
  font-weight: bold;
}

a.download:hover {
  color: #202028;
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,32,0.01)), to(rgba(0,0,32,0.16)));
  background-image: -moz-linear-gradient(rgba(0,0,32,0.01), rgba(0,0,32,0.16));
  border-color: #707078;
}

a.download:active {
  background-color: rgba(0,0,32,0.05);
  background-image: -webkit-gradient(linear, left top, left bottom, from(rgba(0,0,32,0.16)), to(rgba(0,0,32,0.01)));
  background-image: -moz-linear-gradient(rgba(0,0,32,0.16), rgba(0,0,32,0.01));
  box-shadow: inset 0 2px 2px rgba(0,0,0,0.1);
}
