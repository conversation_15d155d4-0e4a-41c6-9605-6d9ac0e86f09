/**
 * fork from https://github.com/node-cache-manager/node-cache-manager/tree/master
 * reason: Support node.js v18 version below and add some features
 * for example: add methodWrap
 */
import { Cache, Config, FactoryConfig, FactoryStore, MemoryCache, MemoryConfig, MemoryStore, MultiCache, Store } from './types';
export declare function caching(name: 'memory', args?: MemoryConfig): Promise<MemoryCache>;
export declare function caching<S extends Store>(store: S): Promise<Cache<S>>;
export declare function caching<S extends Store, T extends object = never>(factory: FactoryStore<S, T>, args?: FactoryConfig<T>): Promise<Cache<S>>;
export declare function createCache(store: MemoryStore, args?: MemoryConfig): MemoryCache;
export declare function createCache(store: Store, args?: Config): Cache<Store>;
/**
 * Module that lets you specify a hierarchy of caches.
 */
export declare function multiCaching<Caches extends Cache[]>(caches: Caches): MultiCache;
//# sourceMappingURL=cacheManager.d.ts.map