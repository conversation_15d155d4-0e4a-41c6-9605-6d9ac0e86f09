{"version": 3, "sources": ["../../src/find-options/mongodb/MongoFindOneOptions.ts"], "names": [], "mappings": "", "file": "MongoFindOneOptions.js", "sourcesContent": ["import { FindOneOptions } from \"../FindOneOptions\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\n\n/**\n * Defines a special criteria to find specific entity.\n */\nexport type MongoFindOneOptions<Entity = any> = Omit<\n    FindOneOptions<Entity>,\n    \"where\"\n> & {\n    /**\n     * Simple condition that should be applied to match entities.\n     */\n    where?: FindOneOptions<Entity>[\"where\"] | ObjectLiteral\n}\n"], "sourceRoot": "../.."}