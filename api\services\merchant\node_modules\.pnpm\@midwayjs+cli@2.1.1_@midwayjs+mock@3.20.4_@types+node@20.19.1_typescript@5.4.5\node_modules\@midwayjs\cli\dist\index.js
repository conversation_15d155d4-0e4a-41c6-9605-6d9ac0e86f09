"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLI = exports.findNpm = void 0;
const command_core_1 = require("@midwayjs/command-core");
const child_process_1 = require("child_process");
const path_1 = require("path");
const plugins_1 = require("./plugins");
__exportStar(require("./utils"), exports);
const enquirer = require('enquirer');
var command_core_2 = require("@midwayjs/command-core");
Object.defineProperty(exports, "findNpm", { enumerable: true, get: function () { return command_core_2.findNpm; } });
process.env.MIDWAY_CLI_PATH = (0, path_1.join)(__dirname, '../');
class CLI extends command_core_1.CoreBaseCLI {
    async loadDefaultPlugin() {
        const command = this.commands && this.commands[0];
        // // version not load plugin
        if (this.argv.v || this.argv.version) {
            return;
        }
        await super.loadDefaultPlugin();
        let needLoad = plugins_1.PluginList;
        const req = this.argv.require || require;
        if (!this.argv.h && command) {
            needLoad = (0, command_core_1.filterPluginByCommand)(plugins_1.PluginList, {
                command,
                cwd: this.core.cwd,
                load: name => req(name),
            });
        }
        if (this.argv.isFaaS) {
            delete this.argv.isFaaS;
            needLoad.push({ mod: '@midwayjs/cli-plugin-faas', name: 'FaaSPlugin' });
        }
        this.debug('Plugin load list', needLoad);
        const allPluginClass = await (0, command_core_1.getPluginClass)(needLoad, {
            cwd: this.core.cwd,
            load: name => req(name),
            npm: this.argv.npm,
            notAutoInstall: this.argv.h,
        });
        allPluginClass.forEach(pluginClass => {
            this.core.addPlugin(pluginClass);
        });
    }
    // cli 扩展
    loadExtensions() {
        return {
            debug: this.debug.bind(this),
            enquirer,
        };
    }
    error(err) {
        if (err && err.message) {
            console.log(err.message);
            throw err;
        }
        else {
            console.log(err);
            throw new Error(err);
        }
    }
    async loadPlugins() {
        this.debug('command & options', this.argv);
        await super.loadPlugins();
        await this.loadDefaultOptions();
    }
    async loadDefaultOptions() {
        if (this.commands.length) {
            return;
        }
        if (this.argv.v || this.argv.version) {
            this.displayVersion();
        }
        else {
            // 默认没有command的时候展示帮助
            this.argv.h = true;
        }
    }
    debug(...args) {
        if (!this.argv.V && !this.argv.verbose && !process.env.FAAS_CLI_VERBOSE) {
            return;
        }
        const log = this.loadLog();
        log.log('[Verbose] ', ...args);
    }
    displayVersion() {
        const log = this.loadLog();
        try {
            const nodeVersion = (0, child_process_1.execSync)('node -v').toString().replace('\n', '');
            log.log('Node.js'.padEnd(20) + nodeVersion);
        }
        catch (_a) {
            //
        }
        try {
            // midway-faas version
            const cliVersion = require('../package.json').version;
            log.log('@midwayjs/cli'.padEnd(20) + `v${cliVersion}`);
        }
        catch (_b) {
            //
        }
    }
    displayUsage(commandsArray, usage, coreInstance, commandInfo) {
        this.displayVersion();
        super.displayUsage(commandsArray, usage, coreInstance, commandInfo);
    }
}
exports.CLI = CLI;
//# sourceMappingURL=index.js.map