-- 添加风险与合规和国际化管理菜单
-- 为财务结算模块添加两个新的子菜单

-- 首先查看当前的财务结算菜单结构
SELECT id, parentId, name, router, type, orderNum 
FROM merchant_sys_menu 
WHERE name LIKE '%结算%' OR name LIKE '%财务%'
ORDER BY parentId, orderNum;

-- 查找财务结算的父菜单ID（假设是 "财务结算" 或 "Settlement"）
SET @settlement_parent_id = (
    SELECT id FROM merchant_sys_menu 
    WHERE (name = '财务结算' OR name = 'Settlement' OR router = '/settlement') 
    AND type = 0 
    LIMIT 1
);

-- 如果没有找到财务结算父菜单，创建一个
INSERT IGNORE INTO merchant_sys_menu (
    parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime
) VALUES (
    0, '财务结算', '/settlement', '', 0, 'icon-money', 5, 1, 1, NOW(), NOW()
);

-- 重新获取财务结算父菜单ID
SET @settlement_parent_id = (
    SELECT id FROM merchant_sys_menu 
    WHERE (name = '财务结算' OR router = '/settlement') 
    AND type = 0 
    LIMIT 1
);

-- 获取当前最大的菜单ID，用于生成新的ID
SET @max_menu_id = (SELECT COALESCE(MAX(id), 0) FROM merchant_sys_menu);

-- 添加风险与合规菜单
INSERT INTO merchant_sys_menu (
    id,
    parentId, 
    name, 
    router, 
    component,
    perms, 
    type, 
    icon, 
    orderNum, 
    keepAlive, 
    isShow, 
    createTime, 
    updateTime
) VALUES (
    @max_menu_id + 1,
    @settlement_parent_id,
    '风险与合规',
    '/settlement/risk-compliance',
    '/settlement/risk-compliance',
    'settlement:risk:compliance',
    1,
    'icon-shield',
    3,
    1,
    1,
    NOW(),
    NOW()
);

-- 添加国际化管理菜单
INSERT INTO merchant_sys_menu (
    id,
    parentId, 
    name, 
    router, 
    component,
    perms, 
    type, 
    icon, 
    orderNum, 
    keepAlive, 
    isShow, 
    createTime, 
    updateTime
) VALUES (
    @max_menu_id + 2,
    @settlement_parent_id,
    '国际化管理',
    '/settlement/internationalization',
    '/settlement/internationalization',
    'settlement:international',
    1,
    'icon-global',
    4,
    1,
    1,
    NOW(),
    NOW()
);

-- 为风险与合规菜单添加权限按钮
INSERT INTO merchant_sys_menu (
    id,
    parentId, 
    name, 
    router, 
    perms, 
    type, 
    icon, 
    orderNum, 
    keepAlive, 
    isShow, 
    createTime, 
    updateTime
) VALUES 
-- 风险与合规权限
(@max_menu_id + 10, @max_menu_id + 1, '查看风险设置', '', 'settlement:risk:view', 2, '', 1, 0, 1, NOW(), NOW()),
(@max_menu_id + 11, @max_menu_id + 1, '修改风险设置', '', 'settlement:risk:edit', 2, '', 2, 0, 1, NOW(), NOW()),
(@max_menu_id + 12, @max_menu_id + 1, '查看合规设置', '', 'settlement:compliance:view', 2, '', 3, 0, 1, NOW(), NOW()),
(@max_menu_id + 13, @max_menu_id + 1, '修改合规设置', '', 'settlement:compliance:edit', 2, '', 4, 0, 1, NOW(), NOW()),

-- 国际化管理权限
(@max_menu_id + 20, @max_menu_id + 2, '查看币种设置', '', 'settlement:currency:view', 2, '', 1, 0, 1, NOW(), NOW()),
(@max_menu_id + 21, @max_menu_id + 2, '修改币种设置', '', 'settlement:currency:edit', 2, '', 2, 0, 1, NOW(), NOW()),
(@max_menu_id + 22, @max_menu_id + 2, '查看地区设置', '', 'settlement:region:view', 2, '', 3, 0, 1, NOW(), NOW()),
(@max_menu_id + 23, @max_menu_id + 2, '修改地区设置', '', 'settlement:region:edit', 2, '', 4, 0, 1, NOW(), NOW()),
(@max_menu_id + 24, @max_menu_id + 2, '查看汇率信息', '', 'settlement:exchange:view', 2, '', 5, 0, 1, NOW(), NOW()),
(@max_menu_id + 25, @max_menu_id + 2, '刷新汇率', '', 'settlement:exchange:refresh', 2, '', 6, 0, 1, NOW(), NOW());

-- 验证插入结果
SELECT 
    CASE 
        WHEN parentId = 0 THEN CONCAT('📁 ', name)
        WHEN type = 0 THEN CONCAT('  📂 ', name)
        WHEN type = 1 THEN CONCAT('    📄 ', name)
        ELSE CONCAT('      🔘 ', name)
    END AS menu_tree,
    router,
    CASE type 
        WHEN 0 THEN '目录' 
        WHEN 1 THEN '菜单' 
        WHEN 2 THEN '按钮'
    END AS type_name,
    perms,
    orderNum
FROM merchant_sys_menu 
WHERE parentId = @settlement_parent_id 
   OR id = @settlement_parent_id
   OR parentId IN (
       SELECT id FROM merchant_sys_menu 
       WHERE parentId = @settlement_parent_id
   )
ORDER BY 
    CASE WHEN id = @settlement_parent_id THEN 0 ELSE 1 END,
    parentId, 
    orderNum;

-- 显示添加的菜单信息
SELECT 
    '✅ 菜单添加完成' AS status,
    COUNT(*) AS new_menus_count
FROM merchant_sys_menu 
WHERE name IN ('风险与合规', '国际化管理');

-- 显示权限按钮添加情况
SELECT 
    '✅ 权限按钮添加完成' AS status,
    COUNT(*) AS permission_buttons_count
FROM merchant_sys_menu 
WHERE parentId IN (
    SELECT id FROM merchant_sys_menu 
    WHERE name IN ('风险与合规', '国际化管理')
) AND type = 2;
