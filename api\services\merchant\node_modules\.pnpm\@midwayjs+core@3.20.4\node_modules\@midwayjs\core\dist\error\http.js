"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.httpError = exports.GatewayTimeoutError = exports.ServiceUnavailableError = exports.BadGatewayError = exports.NotImplementedError = exports.InternalServerErrorError = exports.TooManyRequestsError = exports.UnprocessableEntityError = exports.UnsupportedMediaTypeError = exports.PayloadTooLargeError = exports.GoneError = exports.ConflictError = exports.RequestTimeoutError = exports.NotAcceptableError = exports.ForbiddenError = exports.NotFoundError = exports.UnauthorizedError = exports.BadRequestError = exports.HttpStatus = void 0;
const base_1 = require("./base");
var HttpStatus;
(function (HttpStatus) {
    HttpStatus[HttpStatus["CONTINUE"] = 100] = "CONTINUE";
    HttpStatus[HttpStatus["SWITCHING_PROTOCOLS"] = 101] = "SWITCHING_PROTOCOLS";
    HttpStatus[HttpStatus["PROCESSING"] = 102] = "PROCESSING";
    HttpStatus[HttpStatus["EARLYHINTS"] = 103] = "EARLYHINTS";
    HttpStatus[HttpStatus["OK"] = 200] = "OK";
    HttpStatus[HttpStatus["CREATED"] = 201] = "CREATED";
    HttpStatus[HttpStatus["ACCEPTED"] = 202] = "ACCEPTED";
    HttpStatus[HttpStatus["NON_AUTHORITATIVE_INFORMATION"] = 203] = "NON_AUTHORITATIVE_INFORMATION";
    HttpStatus[HttpStatus["NO_CONTENT"] = 204] = "NO_CONTENT";
    HttpStatus[HttpStatus["RESET_CONTENT"] = 205] = "RESET_CONTENT";
    HttpStatus[HttpStatus["PARTIAL_CONTENT"] = 206] = "PARTIAL_CONTENT";
    HttpStatus[HttpStatus["AMBIGUOUS"] = 300] = "AMBIGUOUS";
    HttpStatus[HttpStatus["MOVED_PERMANENTLY"] = 301] = "MOVED_PERMANENTLY";
    HttpStatus[HttpStatus["FOUND"] = 302] = "FOUND";
    HttpStatus[HttpStatus["SEE_OTHER"] = 303] = "SEE_OTHER";
    HttpStatus[HttpStatus["NOT_MODIFIED"] = 304] = "NOT_MODIFIED";
    HttpStatus[HttpStatus["TEMPORARY_REDIRECT"] = 307] = "TEMPORARY_REDIRECT";
    HttpStatus[HttpStatus["PERMANENT_REDIRECT"] = 308] = "PERMANENT_REDIRECT";
    HttpStatus[HttpStatus["BAD_REQUEST"] = 400] = "BAD_REQUEST";
    HttpStatus[HttpStatus["UNAUTHORIZED"] = 401] = "UNAUTHORIZED";
    HttpStatus[HttpStatus["PAYMENT_REQUIRED"] = 402] = "PAYMENT_REQUIRED";
    HttpStatus[HttpStatus["FORBIDDEN"] = 403] = "FORBIDDEN";
    HttpStatus[HttpStatus["NOT_FOUND"] = 404] = "NOT_FOUND";
    HttpStatus[HttpStatus["METHOD_NOT_ALLOWED"] = 405] = "METHOD_NOT_ALLOWED";
    HttpStatus[HttpStatus["NOT_ACCEPTABLE"] = 406] = "NOT_ACCEPTABLE";
    HttpStatus[HttpStatus["PROXY_AUTHENTICATION_REQUIRED"] = 407] = "PROXY_AUTHENTICATION_REQUIRED";
    HttpStatus[HttpStatus["REQUEST_TIMEOUT"] = 408] = "REQUEST_TIMEOUT";
    HttpStatus[HttpStatus["CONFLICT"] = 409] = "CONFLICT";
    HttpStatus[HttpStatus["GONE"] = 410] = "GONE";
    HttpStatus[HttpStatus["LENGTH_REQUIRED"] = 411] = "LENGTH_REQUIRED";
    HttpStatus[HttpStatus["PRECONDITION_FAILED"] = 412] = "PRECONDITION_FAILED";
    HttpStatus[HttpStatus["PAYLOAD_TOO_LARGE"] = 413] = "PAYLOAD_TOO_LARGE";
    HttpStatus[HttpStatus["URI_TOO_LONG"] = 414] = "URI_TOO_LONG";
    HttpStatus[HttpStatus["UNSUPPORTED_MEDIA_TYPE"] = 415] = "UNSUPPORTED_MEDIA_TYPE";
    HttpStatus[HttpStatus["REQUESTED_RANGE_NOT_SATISFIABLE"] = 416] = "REQUESTED_RANGE_NOT_SATISFIABLE";
    HttpStatus[HttpStatus["EXPECTATION_FAILED"] = 417] = "EXPECTATION_FAILED";
    HttpStatus[HttpStatus["I_AM_A_TEAPOT"] = 418] = "I_AM_A_TEAPOT";
    HttpStatus[HttpStatus["MISDIRECTED"] = 421] = "MISDIRECTED";
    HttpStatus[HttpStatus["UNPROCESSABLE_ENTITY"] = 422] = "UNPROCESSABLE_ENTITY";
    HttpStatus[HttpStatus["FAILED_DEPENDENCY"] = 424] = "FAILED_DEPENDENCY";
    HttpStatus[HttpStatus["PRECONDITION_REQUIRED"] = 428] = "PRECONDITION_REQUIRED";
    HttpStatus[HttpStatus["TOO_MANY_REQUESTS"] = 429] = "TOO_MANY_REQUESTS";
    HttpStatus[HttpStatus["INTERNAL_SERVER_ERROR"] = 500] = "INTERNAL_SERVER_ERROR";
    HttpStatus[HttpStatus["NOT_IMPLEMENTED"] = 501] = "NOT_IMPLEMENTED";
    HttpStatus[HttpStatus["BAD_GATEWAY"] = 502] = "BAD_GATEWAY";
    HttpStatus[HttpStatus["SERVICE_UNAVAILABLE"] = 503] = "SERVICE_UNAVAILABLE";
    HttpStatus[HttpStatus["GATEWAY_TIMEOUT"] = 504] = "GATEWAY_TIMEOUT";
    HttpStatus[HttpStatus["HTTP_VERSION_NOT_SUPPORTED"] = 505] = "HTTP_VERSION_NOT_SUPPORTED";
})(HttpStatus = exports.HttpStatus || (exports.HttpStatus = {}));
/**
 * 400 http error, Means that the request can be fulfilled because of the bad syntax.
 */
class BadRequestError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.BAD_REQUEST);
    }
}
exports.BadRequestError = BadRequestError;
/**
 * 401 http error, Means that the request was legal, but the server is rejecting to answer it. For the use when authentication is required and has failed or has not yet been provided.
 */
class UnauthorizedError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.UNAUTHORIZED);
    }
}
exports.UnauthorizedError = UnauthorizedError;
/**
 * 	4o4 http error, Means that the requested page cannot be found at the moment, but it may be available again in the future.
 */
class NotFoundError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.NOT_FOUND);
    }
}
exports.NotFoundError = NotFoundError;
/**
 * 403 http error, Means that the request is legal, but the server is rejecting to answer it.
 */
class ForbiddenError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.FORBIDDEN);
    }
}
exports.ForbiddenError = ForbiddenError;
/**
 * 406 http error, Means that the server can only generate an answer which the client doesn't accept.
 */
class NotAcceptableError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.NOT_ACCEPTABLE);
    }
}
exports.NotAcceptableError = NotAcceptableError;
/**
 * 408 http error, Means that the server timed out waiting for the request.
 */
class RequestTimeoutError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.REQUEST_TIMEOUT);
    }
}
exports.RequestTimeoutError = RequestTimeoutError;
/**
 * 409 http error, Means that the request cannot be completed, because of a conflict in the request.
 */
class ConflictError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.CONFLICT);
    }
}
exports.ConflictError = ConflictError;
/**
 * 410 http error, Means that the requested page is not available anymore.
 */
class GoneError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.GONE);
    }
}
exports.GoneError = GoneError;
/**
 * 413 http error, Means that the request entity is too large and that's why the server won't accept the request.
 */
class PayloadTooLargeError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.PAYLOAD_TOO_LARGE);
    }
}
exports.PayloadTooLargeError = PayloadTooLargeError;
/**
 * 415 http error, Means that the media type is not supported and that's why the server won't accept the request.
 */
class UnsupportedMediaTypeError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.UNSUPPORTED_MEDIA_TYPE);
    }
}
exports.UnsupportedMediaTypeError = UnsupportedMediaTypeError;
class UnprocessableEntityError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.UNPROCESSABLE_ENTITY);
    }
}
exports.UnprocessableEntityError = UnprocessableEntityError;
/**
 * 429 http error, Means that client has sent too many requests in a given amount of time and that's why the server won't accept the request.
 */
class TooManyRequestsError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.TOO_MANY_REQUESTS);
    }
}
exports.TooManyRequestsError = TooManyRequestsError;
/**
 * 500 http error, Is a generic error and users receive this error message when there is no more suitable specific message.
 */
class InternalServerErrorError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.INTERNAL_SERVER_ERROR);
    }
}
exports.InternalServerErrorError = InternalServerErrorError;
/**
 * 501 http error, Means that the server doesn't recognize the request method or it lacks the ability to fulfill the request.
 */
class NotImplementedError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.NOT_IMPLEMENTED);
    }
}
exports.NotImplementedError = NotImplementedError;
/**
 * 502 http error, Means that the server was acting as a gateway or proxy and it received an invalid answer from the upstream server.
 */
class BadGatewayError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.BAD_GATEWAY);
    }
}
exports.BadGatewayError = BadGatewayError;
/**
 * 503 http error, Means that the server is not available now (It may be overloaded or down).
 */
class ServiceUnavailableError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.SERVICE_UNAVAILABLE);
    }
}
exports.ServiceUnavailableError = ServiceUnavailableError;
/**
 * 504 http error, Means that the server was acting as a gateway or proxy and it didn't get answer on time from the upstream server.
 */
class GatewayTimeoutError extends base_1.MidwayHttpError {
    constructor(resOrMessage) {
        super(resOrMessage, HttpStatus.GATEWAY_TIMEOUT);
    }
}
exports.GatewayTimeoutError = GatewayTimeoutError;
exports.httpError = {
    BadRequestError,
    UnauthorizedError,
    NotFoundError,
    ForbiddenError,
    NotAcceptableError,
    RequestTimeoutError,
    ConflictError,
    GoneError,
    PayloadTooLargeError,
    UnsupportedMediaTypeError,
    UnprocessableEntityError,
    TooManyRequestsError,
    InternalServerErrorError,
    NotImplementedError,
    BadGatewayError,
    ServiceUnavailableError,
    GatewayTimeoutError,
};
//# sourceMappingURL=http.js.map