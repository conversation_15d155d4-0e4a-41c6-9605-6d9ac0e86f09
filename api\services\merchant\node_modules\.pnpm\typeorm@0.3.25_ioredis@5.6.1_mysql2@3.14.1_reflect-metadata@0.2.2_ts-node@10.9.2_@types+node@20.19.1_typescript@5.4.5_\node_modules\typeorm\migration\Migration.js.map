{"version": 3, "sources": ["../../src/migration/Migration.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,SAAS;IA+BlB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,EAAsB,EACtB,SAAiB,EACjB,IAAY,EACZ,QAA6B,EAC7B,WAAqB;QAErB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAA;QACZ,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;IAClC,CAAC;CACJ;AAhDD,8BAgDC", "file": "Migration.js", "sourcesContent": ["import { MigrationInterface } from \"./MigrationInterface\"\n\n/**\n * Represents entity of the migration in the database.\n */\nexport class Migration {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Migration id.\n     * Indicates order of the executed migrations.\n     */\n    id: number | undefined\n\n    /**\n     * Timestamp of the migration.\n     */\n    timestamp: number\n\n    /**\n     * Name of the migration (class name).\n     */\n    name: string\n\n    /**\n     * Migration instance that needs to be run.\n     */\n    instance?: MigrationInterface\n\n    /**\n     * Whether to run this migration within a transaction\n     */\n    transaction?: boolean\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        id: number | undefined,\n        timestamp: number,\n        name: string,\n        instance?: MigrationInterface,\n        transaction?: boolean,\n    ) {\n        this.id = id\n        this.timestamp = timestamp\n        this.name = name\n        this.instance = instance\n        this.transaction = transaction\n    }\n}\n"], "sourceRoot": ".."}