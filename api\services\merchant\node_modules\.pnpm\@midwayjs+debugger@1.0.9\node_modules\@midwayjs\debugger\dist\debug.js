"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const utils_1 = require("./utils");
const config = JSON.parse(process.argv[2]);
const fun = utils_1.getFun(config);
process.on('exit', () => {
    utils_1.sendData(process, { type: 'childExit', exitCode: process.exitCode });
});
const funcMap = {};
(async () => {
    if (config.debug) {
        await utils_1.waitDebug(config.port || 9229);
    }
    utils_1.onMessage(process, async (msg) => {
        let funcArgs = [];
        let func = () => { };
        if (msg.type === 'invoke') {
            funcArgs = msg.data;
            func = fun;
        }
        else if (msg.type === 'invokeInnerFunc') {
            const { funcId, args } = msg.data;
            funcArgs = args;
            func = funcMap[funcId];
        }
        try {
            const result = await func(...funcArgs);
            const functions = [];
            if (utils_1.getType(result) === 'object') {
                Object.keys(result).map(key => {
                    const item = result[key];
                    if (utils_1.getType(item) === 'function' || utils_1.getType(item) === 'asyncfunction') {
                        const funcId = utils_1.getRandomId('func' + key);
                        functions.push({
                            name: key,
                            id: funcId
                        });
                        funcMap[funcId] = async (...args) => {
                            return item(...args);
                        };
                        result[key] = 'this is function';
                    }
                });
            }
            utils_1.sendData(process, { type: 'response', success: true, id: msg.id, result, function: functions });
        }
        catch (e) {
            utils_1.sendData(process, { type: 'response', id: msg.id, error: e.message });
        }
    });
    utils_1.sendData(process, { type: 'ready' });
})();
//# sourceMappingURL=data:application/json;base64,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