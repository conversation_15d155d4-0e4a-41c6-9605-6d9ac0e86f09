import { SpecStructure, Builder } from './interface';
export declare class Spec<PERSON>uilder implements Builder {
    originData: SpecStructure;
    constructor(originData: SpecStructure);
    validate(): boolean;
    transform(): void;
    getProvider(): import("./interface").ProviderStructure;
    getCustom(): any;
    getFunctions(): import("./interface").FunctionsStructure;
    getResources(): import("./interface").ResourcesStructure;
    getPackage(): import("./interface").PackageStructure;
    getPlugins(): import("./interface").PluginsStructure;
    getService(): import("./interface").ServiceStructure;
    getLayers(): import("./interface").LayersStructure;
    getAggregation(): import("./interface").AggregationStructure;
    getFunctionsRule(): import("./interface").FunctionsRuleItem[];
    toJSON(): any;
}
//# sourceMappingURL=builder.d.ts.map