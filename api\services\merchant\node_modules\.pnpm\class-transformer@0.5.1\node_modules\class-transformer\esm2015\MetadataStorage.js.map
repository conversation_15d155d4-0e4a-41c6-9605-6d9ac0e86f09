{"version": 3, "file": "MetadataStorage.js", "sourceRoot": "", "sources": ["../../src/MetadataStorage.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAE7C;;GAEG;AACH,MAAM,OAAO,eAAe;IAA5B;QACE,4EAA4E;QAC5E,aAAa;QACb,4EAA4E;QAEpE,mBAAc,GAAG,IAAI,GAAG,EAAuC,CAAC;QAChE,wBAAmB,GAAG,IAAI,GAAG,EAA8C,CAAC;QAC5E,qBAAgB,GAAG,IAAI,GAAG,EAAyC,CAAC;QACpE,sBAAiB,GAAG,IAAI,GAAG,EAA0C,CAAC;QACtE,kBAAa,GAAG,IAAI,GAAG,EAAwB,CAAC;IA8O1D,CAAC;IA5OC,4EAA4E;IAC5E,gBAAgB;IAChB,4EAA4E;IAE5E,eAAe,CAAC,QAAsB;QACpC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC7C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,EAAwB,CAAC,CAAC;SAC3E;QACD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAChF,CAAC;IAED,oBAAoB,CAAC,QAA2B;QAC9C,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAClD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,EAA+B,CAAC,CAAC;SACvF;QACD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;YAC7E,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;SAC9E;QACD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1F,CAAC;IAED,iBAAiB,CAAC,QAAwB;QACxC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC/C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,EAA0B,CAAC,CAAC;SAC/E;QACD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED,kBAAkB,CAAC,QAAyB;QAC1C,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAChD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,GAAG,EAA2B,CAAC,CAAC;SACjF;QACD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACnF,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,sBAAsB,CACpB,MAAgB,EAChB,YAAoB,EACpB,kBAAsC;QAEtC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAC1F,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEhG,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,CACL,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;oBACxD,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CACzD,CAAC;aACH;YACD,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC;aACjE;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mBAAmB,CAAC,MAAgB,EAAE,YAAoB;QACxD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAED,kBAAkB,CAAC,MAAgB,EAAE,YAAoB;QACvD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACxE,CAAC;IAED,8BAA8B,CAAC,MAAgB,EAAE,IAAY;QAC3D,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtD,OAAO,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,MAAgB,EAAE,YAAoB;QACrD,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;IACtE,CAAC;IAED,WAAW,CAAC,MAAgB;QAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACtD,MAAM,OAAO,GAAG,UAAU,IAAI,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,SAAS,IAAI,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACrD,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC;YAAE,OAAO,MAAM,CAAC;QAChE,OAAO,OAAO,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;IAC9C,CAAC;IAED,mBAAmB,CAAC,MAAgB;QAClC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED,oBAAoB,CAAC,MAAgB;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IAC1D,CAAC;IAED,oBAAoB,CAAC,MAAgB,EAAE,kBAAsC;QAC3E,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;aACpC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEhG,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,CACL,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;oBACxD,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CACzD,CAAC;aACH;YACD,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC;aACjE;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,qBAAqB,CAAC,MAAgB,EAAE,kBAAsC;QAC5E,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC;aACrC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACjB,IAAI,CAAC,QAAQ,CAAC,OAAO;gBAAE,OAAO,IAAI,CAAC;YACnC,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEhG,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,CACL,kBAAkB,KAAK,kBAAkB,CAAC,cAAc;oBACxD,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CACzD,CAAC;aACH;YACD,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE;gBACzC,OAAO,kBAAkB,KAAK,kBAAkB,CAAC,cAAc,CAAC;aACjE;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;aACD,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK;QACH,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;QAC5B,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC/B,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;IAC7B,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAEpE,WAAW,CACjB,SAAwC,EACxC,MAAgB;QAEhB,MAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,kBAAuB,CAAC;QAC5B,IAAI,qBAAqB,EAAE;YACzB,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,KAAK,SAAS,CAAC,CAAC;SACjH;QACD,MAAM,qBAAqB,GAAQ,EAAE,CAAC;QACtC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;YAChD,MAAM,mBAAmB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,mBAAmB,EAAE;gBACvB,MAAM,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAC1E,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,KAAK,SAAS,CACxC,CAAC;gBACF,qBAAqB,CAAC,IAAI,CAAC,GAAG,oBAAoB,CAAC,CAAC;aACrD;SACF;QACD,OAAO,qBAAqB,CAAC,MAAM,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC;IAChE,CAAC;IAEO,YAAY,CAClB,SAAwC,EACxC,MAAgB,EAChB,YAAoB;QAEpB,MAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,qBAAqB,EAAE;YACzB,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACnE,IAAI,kBAAkB,EAAE;gBACtB,OAAO,kBAAkB,CAAC;aAC3B;SACF;QACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;YAChD,MAAM,mBAAmB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,mBAAmB,EAAE;gBACvB,MAAM,cAAc,GAAG,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAC7D,IAAI,cAAc,EAAE;oBAClB,OAAO,cAAc,CAAC;iBACvB;aACF;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,aAAa,CACnB,SAA0C,EAC1C,MAAgB,EAChB,YAAoB;QAEpB,MAAM,qBAAqB,GAAG,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,kBAAuB,CAAC;QAC5B,IAAI,qBAAqB,EAAE;YACzB,kBAAkB,GAAG,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;SAC9D;QACD,MAAM,2BAA2B,GAAQ,EAAE,CAAC;QAC5C,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE;YAChD,MAAM,mBAAmB,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,mBAAmB,EAAE;gBACvB,IAAI,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE;oBACzC,2BAA2B,CAAC,IAAI,CAAC,GAAG,mBAAmB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;iBAC5E;aACF;SACF;QACD,OAAO,2BAA2B;aAC/B,KAAK,EAAE;aACP,OAAO,EAAE;aACT,MAAM,CAAC,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;IAC1D,CAAC;IAEO,YAAY,CAAC,MAAgB;QACnC,IAAI,CAAC,MAAM;YAAE,OAAO,EAAE,CAAC;QACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACnC,MAAM,SAAS,GAAe,EAAE,CAAC;YACjC,KACE,IAAI,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,EACnE,OAAO,SAAS,CAAC,SAAS,KAAK,WAAW,EAC1C,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,EAClE;gBACA,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IACxC,CAAC;CACF", "sourcesContent": ["import { TypeMetadata, ExposeMetadata, ExcludeMetadata, TransformMetadata } from './interfaces';\nimport { TransformationType } from './enums';\n\n/**\n * Storage all library metadata.\n */\nexport class MetadataStorage {\n  // -------------------------------------------------------------------------\n  // Properties\n  // -------------------------------------------------------------------------\n\n  private _typeMetadatas = new Map<Function, Map<string, TypeMetadata>>();\n  private _transformMetadatas = new Map<Function, Map<string, TransformMetadata[]>>();\n  private _exposeMetadatas = new Map<Function, Map<string, ExposeMetadata>>();\n  private _excludeMetadatas = new Map<Function, Map<string, ExcludeMetadata>>();\n  private _ancestorsMap = new Map<Function, Function[]>();\n\n  // -------------------------------------------------------------------------\n  // Adder Methods\n  // -------------------------------------------------------------------------\n\n  addTypeMetadata(metadata: TypeMetadata): void {\n    if (!this._typeMetadatas.has(metadata.target)) {\n      this._typeMetadatas.set(metadata.target, new Map<string, TypeMetadata>());\n    }\n    this._typeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n  }\n\n  addTransformMetadata(metadata: TransformMetadata): void {\n    if (!this._transformMetadatas.has(metadata.target)) {\n      this._transformMetadatas.set(metadata.target, new Map<string, TransformMetadata[]>());\n    }\n    if (!this._transformMetadatas.get(metadata.target).has(metadata.propertyName)) {\n      this._transformMetadatas.get(metadata.target).set(metadata.propertyName, []);\n    }\n    this._transformMetadatas.get(metadata.target).get(metadata.propertyName).push(metadata);\n  }\n\n  addExposeMetadata(metadata: ExposeMetadata): void {\n    if (!this._exposeMetadatas.has(metadata.target)) {\n      this._exposeMetadatas.set(metadata.target, new Map<string, ExposeMetadata>());\n    }\n    this._exposeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n  }\n\n  addExcludeMetadata(metadata: ExcludeMetadata): void {\n    if (!this._excludeMetadatas.has(metadata.target)) {\n      this._excludeMetadatas.set(metadata.target, new Map<string, ExcludeMetadata>());\n    }\n    this._excludeMetadatas.get(metadata.target).set(metadata.propertyName, metadata);\n  }\n\n  // -------------------------------------------------------------------------\n  // Public Methods\n  // -------------------------------------------------------------------------\n\n  findTransformMetadatas(\n    target: Function,\n    propertyName: string,\n    transformationType: TransformationType\n  ): TransformMetadata[] {\n    return this.findMetadatas(this._transformMetadatas, target, propertyName).filter(metadata => {\n      if (!metadata.options) return true;\n      if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true) return true;\n\n      if (metadata.options.toClassOnly === true) {\n        return (\n          transformationType === TransformationType.CLASS_TO_CLASS ||\n          transformationType === TransformationType.PLAIN_TO_CLASS\n        );\n      }\n      if (metadata.options.toPlainOnly === true) {\n        return transformationType === TransformationType.CLASS_TO_PLAIN;\n      }\n\n      return true;\n    });\n  }\n\n  findExcludeMetadata(target: Function, propertyName: string): ExcludeMetadata {\n    return this.findMetadata(this._excludeMetadatas, target, propertyName);\n  }\n\n  findExposeMetadata(target: Function, propertyName: string): ExposeMetadata {\n    return this.findMetadata(this._exposeMetadatas, target, propertyName);\n  }\n\n  findExposeMetadataByCustomName(target: Function, name: string): ExposeMetadata {\n    return this.getExposedMetadatas(target).find(metadata => {\n      return metadata.options && metadata.options.name === name;\n    });\n  }\n\n  findTypeMetadata(target: Function, propertyName: string): TypeMetadata {\n    return this.findMetadata(this._typeMetadatas, target, propertyName);\n  }\n\n  getStrategy(target: Function): 'excludeAll' | 'exposeAll' | 'none' {\n    const excludeMap = this._excludeMetadatas.get(target);\n    const exclude = excludeMap && excludeMap.get(undefined);\n    const exposeMap = this._exposeMetadatas.get(target);\n    const expose = exposeMap && exposeMap.get(undefined);\n    if ((exclude && expose) || (!exclude && !expose)) return 'none';\n    return exclude ? 'excludeAll' : 'exposeAll';\n  }\n\n  getExposedMetadatas(target: Function): ExposeMetadata[] {\n    return this.getMetadata(this._exposeMetadatas, target);\n  }\n\n  getExcludedMetadatas(target: Function): ExcludeMetadata[] {\n    return this.getMetadata(this._excludeMetadatas, target);\n  }\n\n  getExposedProperties(target: Function, transformationType: TransformationType): string[] {\n    return this.getExposedMetadatas(target)\n      .filter(metadata => {\n        if (!metadata.options) return true;\n        if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true) return true;\n\n        if (metadata.options.toClassOnly === true) {\n          return (\n            transformationType === TransformationType.CLASS_TO_CLASS ||\n            transformationType === TransformationType.PLAIN_TO_CLASS\n          );\n        }\n        if (metadata.options.toPlainOnly === true) {\n          return transformationType === TransformationType.CLASS_TO_PLAIN;\n        }\n\n        return true;\n      })\n      .map(metadata => metadata.propertyName);\n  }\n\n  getExcludedProperties(target: Function, transformationType: TransformationType): string[] {\n    return this.getExcludedMetadatas(target)\n      .filter(metadata => {\n        if (!metadata.options) return true;\n        if (metadata.options.toClassOnly === true && metadata.options.toPlainOnly === true) return true;\n\n        if (metadata.options.toClassOnly === true) {\n          return (\n            transformationType === TransformationType.CLASS_TO_CLASS ||\n            transformationType === TransformationType.PLAIN_TO_CLASS\n          );\n        }\n        if (metadata.options.toPlainOnly === true) {\n          return transformationType === TransformationType.CLASS_TO_PLAIN;\n        }\n\n        return true;\n      })\n      .map(metadata => metadata.propertyName);\n  }\n\n  clear(): void {\n    this._typeMetadatas.clear();\n    this._exposeMetadatas.clear();\n    this._excludeMetadatas.clear();\n    this._ancestorsMap.clear();\n  }\n\n  // -------------------------------------------------------------------------\n  // Private Methods\n  // -------------------------------------------------------------------------\n\n  private getMetadata<T extends { target: Function; propertyName: string }>(\n    metadatas: Map<Function, Map<string, T>>,\n    target: Function\n  ): T[] {\n    const metadataFromTargetMap = metadatas.get(target);\n    let metadataFromTarget: T[];\n    if (metadataFromTargetMap) {\n      metadataFromTarget = Array.from(metadataFromTargetMap.values()).filter(meta => meta.propertyName !== undefined);\n    }\n    const metadataFromAncestors: T[] = [];\n    for (const ancestor of this.getAncestors(target)) {\n      const ancestorMetadataMap = metadatas.get(ancestor);\n      if (ancestorMetadataMap) {\n        const metadataFromAncestor = Array.from(ancestorMetadataMap.values()).filter(\n          meta => meta.propertyName !== undefined\n        );\n        metadataFromAncestors.push(...metadataFromAncestor);\n      }\n    }\n    return metadataFromAncestors.concat(metadataFromTarget || []);\n  }\n\n  private findMetadata<T extends { target: Function; propertyName: string }>(\n    metadatas: Map<Function, Map<string, T>>,\n    target: Function,\n    propertyName: string\n  ): T {\n    const metadataFromTargetMap = metadatas.get(target);\n    if (metadataFromTargetMap) {\n      const metadataFromTarget = metadataFromTargetMap.get(propertyName);\n      if (metadataFromTarget) {\n        return metadataFromTarget;\n      }\n    }\n    for (const ancestor of this.getAncestors(target)) {\n      const ancestorMetadataMap = metadatas.get(ancestor);\n      if (ancestorMetadataMap) {\n        const ancestorResult = ancestorMetadataMap.get(propertyName);\n        if (ancestorResult) {\n          return ancestorResult;\n        }\n      }\n    }\n    return undefined;\n  }\n\n  private findMetadatas<T extends { target: Function; propertyName: string }>(\n    metadatas: Map<Function, Map<string, T[]>>,\n    target: Function,\n    propertyName: string\n  ): T[] {\n    const metadataFromTargetMap = metadatas.get(target);\n    let metadataFromTarget: T[];\n    if (metadataFromTargetMap) {\n      metadataFromTarget = metadataFromTargetMap.get(propertyName);\n    }\n    const metadataFromAncestorsTarget: T[] = [];\n    for (const ancestor of this.getAncestors(target)) {\n      const ancestorMetadataMap = metadatas.get(ancestor);\n      if (ancestorMetadataMap) {\n        if (ancestorMetadataMap.has(propertyName)) {\n          metadataFromAncestorsTarget.push(...ancestorMetadataMap.get(propertyName));\n        }\n      }\n    }\n    return metadataFromAncestorsTarget\n      .slice()\n      .reverse()\n      .concat((metadataFromTarget || []).slice().reverse());\n  }\n\n  private getAncestors(target: Function): Function[] {\n    if (!target) return [];\n    if (!this._ancestorsMap.has(target)) {\n      const ancestors: Function[] = [];\n      for (\n        let baseClass = Object.getPrototypeOf(target.prototype.constructor);\n        typeof baseClass.prototype !== 'undefined';\n        baseClass = Object.getPrototypeOf(baseClass.prototype.constructor)\n      ) {\n        ancestors.push(baseClass);\n      }\n      this._ancestorsMap.set(target, ancestors);\n    }\n    return this._ancestorsMap.get(target);\n  }\n}\n"]}