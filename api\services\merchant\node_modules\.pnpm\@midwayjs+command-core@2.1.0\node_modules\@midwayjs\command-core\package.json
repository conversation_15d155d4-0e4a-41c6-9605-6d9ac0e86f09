{"name": "@midwayjs/command-core", "version": "2.1.0", "main": "dist/index.js", "typings": "dist/index.d.ts", "dependencies": {"fs-extra": "^8.1.0", "globby": "^10.0.1", "light-spinner": "^1.0.0", "minimist": "^1.2.5", "p-limit": "^3.1.0"}, "devDependencies": {"benchmark": "^2.1.4", "cross-env": "^7.0.2", "eslint": "^7.9.0", "mm": "^3.2.0", "typescript": "^4.1.0"}, "engines": {"node": ">= 10"}, "files": ["dist", "src"], "scripts": {"lint": "../../node_modules/.bin/eslint .", "build": "tsc --build", "test": "../../node_modules/.bin/jest ./test/cli.test.ts ./test/formatTsError.test.ts", "cov": "cross-env ../../node_modules/.bin/jest --coverage", "cov-win": "npm run cov", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "publishConfig": {"access": "public"}, "license": "MIT", "gitHead": "8b6ce5b6bebd4d31140af0e9a51871ab12692b14"}