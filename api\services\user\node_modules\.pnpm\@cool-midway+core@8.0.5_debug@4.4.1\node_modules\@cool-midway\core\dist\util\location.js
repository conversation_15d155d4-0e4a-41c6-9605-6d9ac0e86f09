"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationUtil = void 0;
const core_1 = require("@midwayjs/core");
const fs = require("fs");
const path = require("path");
/**
 * Location 工具类
 */
let LocationUtil = class LocationUtil {
    constructor() {
        this.locationCache = new Map();
    }
    async init() {
        this.distPath = this.getRunPath();
    }
    /**
     * 获取编译后的文件路径
     * @returns 编译后的文件路径
     */
    getDistPath() {
        return this.distPath;
    }
    /**
     * 获取目标类的定义位置
     * @param target 目标类
     * @returns 目标类的定义位置
     */
    async scriptPath(target) {
        const targetName = target.name;
        // 检查缓存
        if (this.locationCache.has(targetName)) {
            return this.locationCache.get(targetName);
        }
        const originalPrepareStackTrace = Error.prepareStackTrace;
        let targetFile;
        try {
            Error.prepareStackTrace = (error, stack) => stack;
            const stack = new Error().stack;
            for (const site of stack) {
                const fileName = site.getFileName();
                // 如果文件名不存在，则跳过
                if (!fileName || !fs.existsSync(fileName))
                    continue;
                if (!fileName.includes('/modules/') &&
                    !fileName.includes('\\modules\\'))
                    continue;
                targetFile = {
                    path: fileName,
                    line: site.getLineNumber(),
                    column: site.getColumnNumber(),
                    source: `file://${fileName}`,
                };
                this.locationCache.set(targetName, targetFile);
                break;
            }
        }
        finally {
            Error.prepareStackTrace = originalPrepareStackTrace;
        }
        return targetFile;
    }
    /**
     * 获取使用此包的项目的真实根目录路径
     * @returns 项目根目录的绝对路径
     */
    getRunPath() {
        const err = new Error();
        const callerfile = err.stack.split('\n')[2].match(/\(([^)]+)\)/)[1];
        const dirPath = path.dirname(callerfile);
        const nodeModulesIndex = dirPath.indexOf('/node_modules/') !== -1
            ? dirPath.indexOf('/node_modules/')
            : dirPath.indexOf('\\node_modules\\');
        if (nodeModulesIndex !== -1) {
            return path.join(dirPath.substring(0, nodeModulesIndex), 'dist');
        }
        return dirPath;
    }
};
exports.LocationUtil = LocationUtil;
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LocationUtil.prototype, "init", null);
exports.LocationUtil = LocationUtil = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], LocationUtil);
// 不再需要单例模式，直接导出实例即可
exports.default = new LocationUtil();
