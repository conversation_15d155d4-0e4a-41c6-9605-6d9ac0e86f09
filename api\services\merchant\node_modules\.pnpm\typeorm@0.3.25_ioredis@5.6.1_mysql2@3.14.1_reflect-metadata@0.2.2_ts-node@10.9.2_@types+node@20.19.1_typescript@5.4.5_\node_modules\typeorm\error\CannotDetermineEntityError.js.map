{"version": 3, "sources": ["../../src/error/CannotDetermineEntityError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,0BAA2B,SAAQ,2BAAY;IACxD,YAAY,SAAiB;QACzB,KAAK,CACD,UAAU,SAAS,kDAAkD;YACjE,uFAAuF,CAC9F,CAAA;IACL,CAAC;CACJ;AAPD,gEAOC", "file": "CannotDetermineEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to save/remove/etc. constructor-less object (object literal) instead of entity.\n */\nexport class CannotDetermineEntityError extends TypeORMError {\n    constructor(operation: string) {\n        super(\n            `Cannot ${operation}, given value must be instance of entity class, ` +\n                `instead object literal is given. Or you must specify an entity target to method call.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}