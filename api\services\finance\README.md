# Finance 财务微服务

本服务为财务（finance）相关业务的独立微服务，基于 Midway + cool-admin 架构。

## 🏗️ 业务范围

财务微服务负责处理所有与资金相关的业务逻辑，包括：

### 核心功能
- **用户钱包管理** - 余额、冻结金额、钱包记录
- **提现申请处理** - 申请提交、审核、统计
- **发票管理** - 开票申请、发票记录
- **资金流水记录** - 详细的资金变动记录

### 业务特色
- **🔒 数据安全** - 独立数据库，敏感数据隔离
- **💰 事务一致性** - 支持分布式事务处理
- **📊 实时统计** - 提供财务数据统计分析
- **🚀 高性能** - 支持批量操作和并发处理

## 📁 目录结构

```
services/finance/
├── src/
│   ├── config/                    # 配置文件
│   │   ├── config.default.ts      # 默认配置
│   │   ├── config.local.ts        # 本地开发配置
│   │   └── config.prod.ts         # 生产环境配置
│   ├── configuration.ts           # 服务配置
│   └── modules/finance/            # 财务业务模块
│       ├── entity/                 # 数据实体
│       │   ├── wallet/             # 钱包相关实体
│       │   ├── apply/              # 申请相关实体
│       │   └── user/               # 用户相关实体
│       ├── service/                # 业务逻辑服务
│       │   ├── wallet/             # 钱包服务
│       │   ├── apply/              # 申请服务
│       │   └── user/               # 用户服务
│       └── controller/             # 控制器
│           ├── admin/              # 管理端接口
│           └── app/                # 应用端接口
├── main.ts                        # 启动入口
├── bootstrap.js                   # 启动脚本
├── package.json                   # 依赖配置
└── README.md                      # 本文档
```

## 🚀 启动方法

### 1. 安装依赖

```bash
cd services/finance
npm install
# 或
yarn install
# 或
pnpm install
```

### 2. 配置数据库

修改 `src/config/config.local.ts` 中的数据库配置：

```typescript
database: 'cool', // 本地开发使用共享数据库
// 或生产环境使用独立数据库
database: 'finance_service_db', // 独立数据库
```

### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

### 4. 验证启动

服务启动后会显示：
```
✅ 财务微服务启动成功！
🚀 服务端口: 9803
📋 服务名称: finance-service
💰 财务数据独立管理
🔗 RPC通信已启用
```

## 🔧 服务配置

### 端口配置
- **开发环境**: 9803
- **生产环境**: 9803

### 数据库配置
- **本地开发**: 共享数据库 `cool`
- **生产环境**: 独立数据库 `finance_service_db`

### Redis配置
- **数据库**: db: 12 (独立Redis数据库)

## 📡 RPC接口

财务微服务通过RPC暴露以下主要接口：

### 钱包服务 (FinanceWalletUserService)

```typescript
// 创建钱包
createWallet(userId: number)

// 获取钱包详情
getWalletDetail(userId: number)

// 获取可用余额
getValidBalance(userId: number)

// 批量获取用户余额
batchGetBalances(userIds: number[])

// 变更余额
changeBalance(userId: number, amount: number)

// 变更冻结金额
changeFreeze(userId: number, amount: number)

// 添加钱包记录
addWalletRecord(record: FinanceWalletRecordEntity)

// 获取钱包统计
getWalletStats()
```

### 提现申请服务 (FinanceApplyDrawService)

```typescript
// 提交提现申请
submitDrawApplication(userId: number, amount: number, type: number)

// 审核提现申请
reviewDrawApplication(id: number, status: number, remark: string, fee?: number)

// 获取用户提现记录
getUserDrawApplications(userId: number, status?: number)

// 获取提现统计
getDrawStats()
```

## 💻 调用示例

### 在主服务中调用财务微服务

```typescript
import { CoolRpc } from '@cool-midway/rpc';

@Provide()
export class SomeService {
  @Inject()
  rpc: CoolRpc;

  // 获取用户钱包信息
  async getUserWallet(userId: number) {
    return await this.rpc.call(
      'finance-service',
      'financeWalletUserService',
      'getWalletDetail',
      userId
    );
  }

  // 钱包支付
  async walletPay(userId: number, amount: number) {
    // 检查余额
    const balance = await this.rpc.call(
      'finance-service',
      'financeWalletUserService',
      'getValidBalance',
      userId
    );

    if (balance < amount) {
      throw new Error('余额不足');
    }

    // 扣除余额
    await this.rpc.call(
      'finance-service',
      'financeWalletUserService',
      'changeBalance',
      [userId, -amount]
    );
  }
}
```

## 🛡️ 安全特性

### 数据隔离
- 独立数据库存储敏感财务数据
- Redis缓存使用独立db避免数据混淆

### 事务保障
- 使用 `@CoolTransaction` 确保事务一致性
- 支持分布式事务处理

### 权限控制
- RPC接口只对内部服务开放
- 敏感操作需要多重验证

## 📊 监控指标

### 钱包统计
- 总用户数
- 总余额
- 总冻结金额
- 平均余额

### 提现统计
- 申请总数
- 待审核数量
- 通过数量
- 拒绝数量
- 总提现金额
- 总手续费

## 🔄 架构特点

### 微服务优势
- **独立部署** - 可单独扩容和发布
- **技术栈灵活** - 可选择最适合的技术
- **故障隔离** - 不影响其他业务模块
- **团队分工** - 专门团队负责财务功能

### 通信方式
- **RPC调用** - 基于Moleculer的服务间通信
- **事件驱动** - 支持异步事件通知
- **降级处理** - 服务不可用时的降级策略

## 🎯 未来规划

1. **支付集成** - 集成多种支付方式
2. **风控系统** - 实时风险监控和预警
3. **报表系统** - 完整的财务报表生成
4. **国际化** - 支持多币种和汇率转换
5. **API网关** - 统一的API管理和限流

---

**服务端口**: 9803  
**服务名称**: finance-service  
**数据库**: finance_service_db (生产) / cool (开发)  
**Redis DB**: 12 