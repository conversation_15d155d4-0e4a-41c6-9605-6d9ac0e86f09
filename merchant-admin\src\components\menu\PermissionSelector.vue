<template>
  <div class="permission-selector">
    <ElFormItem label="权限标识" prop="label" required>
      <ElSelect
        v-model="permissionLabel"
        placeholder="请选择或输入权限标识"
        filterable
        clearable
        allow-create
        default-first-option
        @change="handlePermissionChange"
        class="permission-selector-input"
        popper-class="permission-selector-dropdown"
      >
        <ElOptionGroup
          v-for="group in groupedPermissions"
          :key="group.label"
          :label="group.label"
        >
          <ElOption
            v-for="permission in group.options"
            :key="permission.value"
            :label="permission.label"
            :value="permission.value"
          >
            <div class="permission-option">
              <span class="permission-name">{{ permission.label }}</span>
              <span class="permission-value">{{ permission.value }}</span>
            </div>
          </ElOption>
        </ElOptionGroup>
      </ElSelect>
    </ElFormItem>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElFormItem, ElSelect, ElOption, ElOptionGroup } from 'element-plus'

interface PermissionOption {
  label: string
  value: string
  description?: string
}

interface PermissionGroup {
  label: string
  options: PermissionOption[]
}

interface Props {
  modelValue?: string
  routePath?: string // 当前选择的路由路径，用于智能推荐
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const permissionLabel = ref(props.modelValue || '')

// 预定义的权限标识模板
const permissionTemplates: PermissionGroup[] = [
  {
    label: '商户管理',
    options: [
      { label: '商户入驻', value: 'merchant:settle_in' },
      { label: '个人商户', value: 'merchant:personal' },
      { label: '企业商户', value: 'merchant:company' },
      { label: '商户审核', value: 'merchant:audit' },
      { label: '商户列表', value: 'merchant:list' }
    ]
  },
  {
    label: '用户管理',
    options: [
      { label: '用户列表', value: 'user:list' },
      { label: '用户详情', value: 'user:detail' },
      { label: '用户编辑', value: 'user:edit' },
      { label: '用户删除', value: 'user:delete' }
    ]
  },
  {
    label: '订单管理',
    options: [
      { label: '订单列表', value: 'order:list' },
      { label: '订单详情', value: 'order:detail' },
      { label: '订单处理', value: 'order:process' },
      { label: '订单退款', value: 'order:refund' }
    ]
  },
  {
    label: '产品管理',
    options: [
      { label: '产品列表', value: 'product:list' },
      { label: '产品添加', value: 'product:add' },
      { label: '产品编辑', value: 'product:edit' },
      { label: '产品删除', value: 'product:delete' }
    ]
  },
  {
    label: '系统管理',
    options: [
      { label: '菜单管理', value: 'system:menu' },
      { label: '角色管理', value: 'system:role' },
      { label: '权限管理', value: 'system:permission' },
      { label: '系统设置', value: 'system:setting' }
    ]
  },
  {
    label: '财务结算',
    options: [
      { label: '结算规则', value: 'settlement:rules:view' },
      { label: '结算规则-新增', value: 'settlement:rules:add' },
      { label: '结算规则-编辑', value: 'settlement:rules:edit' },
      { label: '结算规则-删除', value: 'settlement:rules:delete' },
      { label: '批量结算', value: 'settlement:batch:view' },
      { label: '对账管理', value: 'settlement:reconciliation:view' },
      { label: '提现审核', value: 'settlement:withdraw:view' }
    ]
  },
  {
    label: '数据分析',
    options: [
      { label: '仪表盘', value: 'dashboard:index' },
      { label: '数据分析', value: 'dashboard:analysis' },
      { label: '报表统计', value: 'dashboard:report' }
    ]
  }
]

// 根据路由路径智能推荐权限标识
const getRecommendedPermission = (routePath: string): PermissionOption | null => {
  if (!routePath) return null
  
  const pathParts = routePath.split('/').filter(p => p)
  if (pathParts.length === 0) return null
  
  // 生成推荐的权限标识
  const recommendedValue = pathParts.map(part => 
    part.replace(/-/g, '_')
  ).join(':')
  
  // 生成推荐的显示名称
  const nameMap: Record<string, string> = {
    'settle_in': '商户入驻',
    'personal': '个人商户',
    'company': '企业商户',
    'category': '类目管理',
    'heritage_auth': '非遗认证',
    'dashboard': '仪表盘',
    'console': '控制台',
    'analysis': '数据分析',
    'user': '用户管理',
    'role': '角色管理',
    'menu': '菜单管理',
    'order': '订单管理',
    'product': '产品管理',
    'finance': '财务管理',
    'settlement': '财务结算',
    'rules': '结算规则',
    'batch': '批量结算',
    'reconciliation': '对账管理',
    'withdraw': '提现审核',
    'report': '报表统计',
    'setting': '系统设置'
  }
  
  const lastName = pathParts[pathParts.length - 1].replace(/-/g, '_')
  // 权限标识使用英文标识符，不使用中文显示名称
  const recommendedLabel = recommendedValue

  return {
    label: recommendedLabel,
    value: recommendedValue
  }
}

// 组合权限选项（模板 + 智能推荐）
const groupedPermissions = computed(() => {
  const groups = [...permissionTemplates]
  
  // 如果有路由路径，添加智能推荐
  if (props.routePath) {
    const recommended = getRecommendedPermission(props.routePath)
    if (recommended) {
      // 检查是否已存在相同的权限标识
      const exists = groups.some(group => 
        group.options.some(option => option.value === recommended.value)
      )
      
      if (!exists) {
        groups.unshift({
          label: '智能推荐',
          options: [recommended]
        })
      }
    }
  }
  
  return groups
})

// 处理权限标识变化
const handlePermissionChange = () => {
  emit('update:modelValue', permissionLabel.value)
  emit('change', permissionLabel.value)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  permissionLabel.value = newVal || ''
})

// 监听路由路径变化，自动更新推荐
watch(() => props.routePath, (newPath) => {
  // 当路由路径改变时，自动更新权限标识为对应的推荐值
  if (newPath) {
    const recommended = getRecommendedPermission(newPath)
    if (recommended) {
      permissionLabel.value = recommended.value
      handlePermissionChange()
    }
  }
})
</script>

<style scoped>
.permission-selector-input {
  width: 100%;
}

.permission-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.permission-name {
  font-weight: 500;
  color: #303133;
}

.permission-value {
  font-size: 12px;
  color: #909399;
  margin-left: 8px;
}

/* 控制下拉选项的宽度 */
:global(.permission-selector-dropdown) {
  min-width: 250px !important;
  max-width: 400px !important;
}

:global(.permission-selector-dropdown .el-select-dropdown__item) {
  padding: 8px 12px;
}

:global(.permission-selector-dropdown .el-select-group__title) {
  padding: 8px 12px;
  font-weight: 600;
  color: #409eff;
  background-color: #f5f7fa;
}
</style>
