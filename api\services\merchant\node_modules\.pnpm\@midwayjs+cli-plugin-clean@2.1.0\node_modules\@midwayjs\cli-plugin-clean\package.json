{"name": "@midwayjs/cli-plugin-clean", "version": "2.1.0", "main": "dist/index", "typings": "dist/index.d.ts", "dependencies": {"@midwayjs/command-core": "^2.1.0", "@midwayjs/serverless-spec-builder": "^2.1.0", "fs-extra": "^8.1.0"}, "devDependencies": {"typescript": "^4.1.0"}, "engines": {"node": ">= 10"}, "files": ["plugin.json", "dist", "src"], "scripts": {"build": "tsc --build", "lint": "../../node_modules/.bin/eslint .", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "publishConfig": {"access": "public"}, "license": "MIT", "gitHead": "8b6ce5b6bebd4d31140af0e9a51871ab12692b14"}