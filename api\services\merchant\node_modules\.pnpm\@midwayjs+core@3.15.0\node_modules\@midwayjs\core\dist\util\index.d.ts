import { FunctionMiddleware, IgnoreMatcher } from '../interface';
import { camelCase, pascalCase } from './camelCase';
import { randomUUID } from './uuid';
import { safeParse, safeStringify } from './flatted';
/**
 * @since 2.0.0
 * @param env
 */
export declare const isDevelopmentEnvironment: (env: any) => boolean;
/**
 * @since 2.0.0
 */
export declare const getCurrentEnvironment: () => string;
/**
 * @param p
 * @param enabledCache
 * @since 2.0.0
 */
export declare const safeRequire: (p: any, enabledCache?: boolean) => any;
/**
 * load module, and it can be chosen commonjs or esm mode
 * @param p
 * @param options
 * @since 3.12.0
 */
export declare const loadModule: (p: string, options?: {
    enableCache?: boolean;
    loadMode?: 'commonjs' | 'esm';
    safeLoad?: boolean;
}) => Promise<any>;
/**
 *  @example
 *  safelyGet(['a','b'],{a: {b: 2}})  // => 2
 *  safelyGet(['a','b'],{c: {b: 2}})  // => undefined
 *  safelyGet(['a','1'],{a: {"1": 2}})  // => 2
 *  safelyGet(['a','1'],{a: {b: 2}})  // => undefined
 *  safelyGet('a.b',{a: {b: 2}})  // => 2
 *  safelyGet('a.b',{c: {b: 2}})  // => undefined
 *  @since 2.0.0
 */
export declare function safelyGet(list: string | string[], obj?: Record<string, unknown>): any;
/**
 * 剔除 @ 符号
 * @param provideId provideId
 * @since 2.0.0
 */
export declare function parsePrefix(provideId: string): string;
export declare function getUserHome(): string;
export declare function joinURLPath(...strArray: any[]): string;
/**
 * 代理目标所有的原型方法，不包括构造器和内部隐藏方法
 * @param derivedCtor
 * @param constructors
 * @param otherMethods
 * @since 2.0.0
 */
export declare function delegateTargetPrototypeMethod(derivedCtor: any, constructors: any[], otherMethods?: string[]): void;
/**
 * 代理目标所有的原型方法，包括原型链，不包括构造器和内部隐藏方法
 * @param derivedCtor
 * @param constructor
 * @since 3.0.0
 */
export declare function delegateTargetAllPrototypeMethod(derivedCtor: any, constructor: any): void;
/**
 * 代理目标原型上的特定方法
 * @param derivedCtor
 * @param methods
 * @since 2.0.0
 */
export declare function delegateTargetMethod(derivedCtor: any, methods: string[]): void;
/**
 * 代理目标原型属性
 * @param derivedCtor
 * @param properties
 * @since 2.0.0
 */
export declare function delegateTargetProperties(derivedCtor: any, properties: string[]): void;
/**
 * 获取当前的时间戳
 * @since 2.0.0
 * @param timestamp
 */
export declare const getCurrentDateString: (timestamp?: number) => string;
/**
 *
 * @param message
 * @since 3.0.0
 */
export declare const deprecatedOutput: (message: string) => void;
/**
 * transform request object to definition type
 *
 * @param originValue
 * @param targetType
 * @since 3.0.0
 */
export declare const transformRequestObjectByType: (originValue: any, targetType?: any) => any;
export declare function toPathMatch(pattern: any): any;
export declare function pathMatching(options: {
    match?: IgnoreMatcher<any> | IgnoreMatcher<any>[];
    ignore?: IgnoreMatcher<any> | IgnoreMatcher<any>[];
    thisResolver?: any;
}): (ctx?: any) => boolean;
/**
 * wrap function middleware with match and ignore
 * @param mw
 * @param options
 */
export declare function wrapMiddleware(mw: FunctionMiddleware<any, any>, options: any): (context: any, next: any, options?: any) => any;
export declare function isIncludeProperty(obj: any, prop: string): boolean;
export declare function wrapAsync(handler: any): (...args: any[]) => any;
export declare function sleep(sleepTime?: number): Promise<void>;
/**
 * get parameter name from function
 * @param func
 */
export declare function getParamNames(func: any): string[];
/**
 * generate a lightweight 32 bit random id, enough for ioc container
 */
export declare function generateRandomId(): string;
export declare function merge(target: any, src: any): any;
export declare function toAsyncFunction<T extends (...args: any[]) => any>(method: T): (...args: Parameters<T>) => Promise<ReturnType<T>>;
export declare function isTypeScriptEnvironment(): boolean;
/**
 * Create a Promise that resolves after the specified time
 * @param options
 */
export declare function createPromiseTimeoutInvokeChain<Result>(options: {
    promiseItems: Array<Promise<any> | {
        item: Promise<any>;
        meta?: any;
        timeout?: number;
    }>;
    timeout: number;
    methodName: string;
    onSuccess?: (result: any, meta: any) => Result | Promise<Result>;
    onFail: (err: Error, meta: any) => Result | Promise<Result>;
    isConcurrent?: boolean;
}): Promise<Result[]>;
export declare const Utils: {
    sleep: typeof sleep;
    getParamNames: typeof getParamNames;
    camelCase: typeof camelCase;
    pascalCase: typeof pascalCase;
    randomUUID: typeof randomUUID;
    generateRandomId: typeof generateRandomId;
    toAsyncFunction: typeof toAsyncFunction;
    safeStringify: typeof safeStringify;
    safeParse: typeof safeParse;
    isTypeScriptEnvironment: typeof isTypeScriptEnvironment;
};
//# sourceMappingURL=index.d.ts.map