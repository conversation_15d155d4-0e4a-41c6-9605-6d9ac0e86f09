{"version": 3, "sources": ["../browser/src/util/escapeRegExp.ts"], "names": [], "mappings": "AAAA,mDAAmD;AACnD,iGAAiG;AACjG,MAAM,aAAa,GAAG,uBAAuB,CAAA;AAC7C,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,MAAM,CAAC,CAAA", "file": "escapeRegExp.js", "sourcesContent": ["// Escape special characters in regular expressions\n// Per https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#Escaping\nconst ESCAPE_REGEXP = /[.*+\\-?^${}()|[\\]\\\\]/g\nexport const escapeRegExp = (s: string) => s.replace(ESCAPE_REGEXP, \"\\\\$&\")\n"], "sourceRoot": ".."}