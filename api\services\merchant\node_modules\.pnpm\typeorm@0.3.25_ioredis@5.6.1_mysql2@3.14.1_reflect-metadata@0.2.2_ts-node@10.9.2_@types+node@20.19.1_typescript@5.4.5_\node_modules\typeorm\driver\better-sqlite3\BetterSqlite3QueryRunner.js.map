{"version": 3, "sources": ["../../src/driver/better-sqlite3/BetterSqlite3QueryRunner.ts"], "names": [], "mappings": ";;;AAAA,iGAA6F;AAC7F,mEAA+D;AAC/D,4FAAwF;AACxF,8DAA0D;AAE1D,gEAA4D;AAC5D,0EAAsE;AAEtE;;;;;GAKG;AACH,MAAa,wBAAyB,SAAQ,qDAAyB;IAMnE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAA2B;QACnC,KAAK,EAAE,CAAA;QAYH,cAAS,GAAG,IAAI,GAAG,EAAe,CAAA;QAXtC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,KAAK,QAAQ,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,kBAAkB,CAAA;QAC3D,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,SAAS,GAAG,GAAG,CAAA;QACxB,CAAC;IACL,CAAC;IAKO,KAAK,CAAC,OAAO,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;YACpC,IAAI,CAAC,IAAI,EAAE,CAAC;gBACR,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;gBAC/C,IAAI,GAAG,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;gBACxC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;gBAC/B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;oBAC1C,2CAA2C;oBAC3C,4BAA4B;oBAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,KAAM,CAAA;oBAC/C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBAC9B,CAAC;YACL,CAAC;YACD,OAAO,IAAI,CAAA;QACf,CAAC;aAAM,CAAC;YACJ,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;YAC/C,OAAO,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAC5C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA;QAEzC,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,IAAI,CAAC,WAAW,CAAC,yBAAyB,CACtC,iBAAiB,EACjB,KAAK,EACL,UAAU,CACb,CAAA;QACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAEtC,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAEhC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;gBAE5C,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;gBAEhB,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACrB,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;gBACxB,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;gBAC5C,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAA;gBAC7B,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,eAAe,CAAA;YACpC,CAAC;YAED,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YACxD,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,UAAU,CAAC,MAAM,CAAC,YAAY,CAC1B,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAEL,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,MAAM,CAAC,GAAG,EACV,SAAS,CACZ,CAAA;YAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAC7D,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,gBAAgB,CAC5B,SAAiB,EACjB,YAA+B;QAE/B,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAC5D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CACxB,UACI,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC,CAAC,CAAC,IACjC,wBAAwB,IAAI,CAAC,UAAU,CACnC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,eAAe,CACnD,oBAAoB,YAAY,UAC7B,YAAY,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,UACxC,UAAU,SAAS,IAAI,CAC1B,CAAA;QACD,OAAO,GAAG,CAAA;IACd,CAAC;IACS,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,MAAc;QAC/D,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAC5D,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,KAAK,CACxB,UACI,QAAQ,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAC,EAClC,GAAG,MAAM,KAAK,SAAS,IAAI,CAC9B,CAAA;QACD,OAAO,GAAG,CAAA;IACd,CAAC;CACJ;AAvKD,4DAuKC", "file": "BetterSqlite3QueryRunner.js", "sourcesContent": ["import { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BetterSqlite3Driver } from \"./BetterSqlite3Driver\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\n\n/**\n * Runs queries on a single sqlite database connection.\n *\n * Does not support compose primary keys with autoincrement field.\n * todo: need to throw exception for this case.\n */\nexport class BetterSqlite3QueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: BetterSqlite3Driver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: BetterSqlite3Driver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n        if (typeof this.driver.options.statementCacheSize === \"number\") {\n            this.cacheSize = this.driver.options.statementCacheSize\n        } else {\n            this.cacheSize = 100\n        }\n    }\n\n    private cacheSize: number\n    private stmtCache = new Map<string, any>()\n\n    private async getStmt(query: string) {\n        if (this.cacheSize > 0) {\n            let stmt = this.stmtCache.get(query)\n            if (!stmt) {\n                const databaseConnection = await this.connect()\n                stmt = databaseConnection.prepare(query)\n                this.stmtCache.set(query, stmt)\n                while (this.stmtCache.size > this.cacheSize) {\n                    // since es6 map keeps the insertion order,\n                    // it comes to be FIFO cache\n                    const key = this.stmtCache.keys().next().value!\n                    this.stmtCache.delete(key)\n                }\n            }\n            return stmt\n        } else {\n            const databaseConnection = await this.connect()\n            return databaseConnection.prepare(query)\n        }\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const connection = this.driver.connection\n\n        const broadcasterResult = new BroadcasterResult()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        this.broadcaster.broadcastBeforeQueryEvent(\n            broadcasterResult,\n            query,\n            parameters,\n        )\n        const queryStartTime = Date.now()\n\n        const stmt = await this.getStmt(query)\n\n        try {\n            const result = new QueryResult()\n\n            if (stmt.reader) {\n                const raw = stmt.all.apply(stmt, parameters)\n\n                result.raw = raw\n\n                if (Array.isArray(raw)) {\n                    result.records = raw\n                }\n            } else {\n                const raw = stmt.run.apply(stmt, parameters)\n                result.affected = raw.changes\n                result.raw = raw.lastInsertRowid\n            }\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                result.raw,\n                undefined,\n            )\n\n            if (!useStructuredResult) {\n                return result.raw\n            }\n\n            return result\n        } catch (err) {\n            connection.logger.logQueryError(err, query, parameters, this)\n            throw new QueryFailedError(query, parameters, err)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadTableRecords(\n        tablePath: string,\n        tableOrIndex: \"table\" | \"index\",\n    ) {\n        const [database, tableName] = this.splitTablePath(tablePath)\n        const res = await this.query(\n            `SELECT ${\n                database ? `'${database}'` : null\n            } as database, * FROM ${this.escapePath(\n                `${database ? `${database}.` : \"\"}sqlite_master`,\n            )} WHERE \"type\" = '${tableOrIndex}' AND \"${\n                tableOrIndex === \"table\" ? \"name\" : \"tbl_name\"\n            }\" IN ('${tableName}')`,\n        )\n        return res\n    }\n    protected async loadPragmaRecords(tablePath: string, pragma: string) {\n        const [database, tableName] = this.splitTablePath(tablePath)\n        const res = await this.query(\n            `PRAGMA ${\n                database ? `\"${database}\".` : \"\"\n            }${pragma}(\"${tableName}\")`,\n        )\n        return res\n    }\n}\n"], "sourceRoot": "../.."}