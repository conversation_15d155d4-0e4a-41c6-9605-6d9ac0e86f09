{"name": "registry-url", "version": "5.1.0", "description": "Get the set npm registry URL", "license": "MIT", "repository": "sindresorhus/registry-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["npm", "conf", "config", "npmconf", "registry", "url", "uri", "scope"], "dependencies": {"rc": "^1.2.8"}, "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "ava": {"serial": true}}