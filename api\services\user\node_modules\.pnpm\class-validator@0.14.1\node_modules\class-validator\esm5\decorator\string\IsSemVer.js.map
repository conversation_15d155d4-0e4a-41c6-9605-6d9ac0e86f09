{"version": 3, "file": "IsSemVer.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsSemVer.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,IAAM,UAAU,GAAG,UAAU,CAAC;AAErC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,UAAU;QAChB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YACnD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,uDAAuD,EAApE,CAAoE,EAClF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isSemVerValidator from 'validator/lib/isSemVer';\n\nexport const IS_SEM_VER = 'isSemVer';\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function isSemVer(value: unknown): boolean {\n  return typeof value === 'string' && isSemVerValidator(value);\n}\n\n/**\n * Check if the string is a Semantic Versioning Specification (SemVer).\n * If given value is not a string, then it returns false.\n */\nexport function IsSemVer(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_SEM_VER,\n      validator: {\n        validate: (value, args): boolean => isSemVer(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a Semantic Versioning Specification',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}