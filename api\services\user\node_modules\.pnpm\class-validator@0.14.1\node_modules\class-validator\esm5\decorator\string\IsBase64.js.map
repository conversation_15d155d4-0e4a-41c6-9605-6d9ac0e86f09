{"version": 3, "file": "IsBase64.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBase64.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAGvD,MAAM,CAAC,IAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc,EAAE,OAAqC;IAC5E,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACxE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CACtB,OAAqC,EACrC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,QAAQ,CAAC,KAAK,CAAC,EAAf,CAAe;YACnD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,kCAAkC,EAA/C,CAA+C,EAAE,iBAAiB,CAAC;SAC/G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBase64Validator from 'validator/lib/isBase64';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_BASE64 = 'isBase64';\n\n/**\n * Checks if a string is base64 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function isBase64(value: unknown, options?: ValidatorJS.IsBase64Options): boolean {\n  return typeof value === 'string' && isBase64Validator(value, options);\n}\n\n/**\n * Checks if a string is base64 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function IsBase64(\n  options?: ValidatorJS.IsBase64Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BASE64,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isBase64(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be base64 encoded', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}