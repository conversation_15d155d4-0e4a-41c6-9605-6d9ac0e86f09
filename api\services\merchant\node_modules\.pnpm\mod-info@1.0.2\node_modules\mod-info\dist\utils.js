var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
__export(exports, {
  exec: () => exec,
  formatVersion: () => formatVersion,
  matchVersion: () => matchVersion
});
var import_child_process = __toModule(require("child_process"));
const exec = (options) => {
  const { cmd, baseDir, timeout } = options;
  return new Promise((resolved, rejected) => {
    let timeoutHandler;
    let execProcess;
    if (timeout) {
      timeoutHandler = setTimeout(() => {
        if (execProcess == null ? void 0 : execProcess.kill) {
          execProcess.kill();
        }
        rejected("timeout");
      }, timeout);
    }
    execProcess = (0, import_child_process.exec)(cmd, {
      cwd: baseDir
    }, (err, result) => {
      clearTimeout(timeoutHandler);
      if (err) {
        return rejected(err);
      }
      resolved(result);
    });
    execProcess.stdout.on("data", (data) => {
    });
  });
};
const formatVersion = (version) => {
  const versionParts = version.replace(/[^\d\.]/g, "").split(".");
  const versionInfo = {
    version,
    major: +(versionParts[0] || 0),
    minor: +(versionParts[1] || 0),
    pacth: +(versionParts[2] || 0),
    tag: ["beta", "alpha"].find((tag) => version.includes(tag)),
    score: 0
  };
  versionInfo.score = versionInfo.major * 1e12 + versionInfo.minor * 1e6 + versionInfo.pacth;
  return versionInfo;
};
const matchVersion = (version, matchRule) => {
  const rule = matchRule.replace(/[^\d\.]/g, "").split(".");
  if (rule[0] && version.major !== +rule[0]) {
    return false;
  }
  if (rule[1] && version.minor !== +rule[1]) {
    return false;
  }
  if (rule[2] && version.pacth !== +rule[2]) {
    return false;
  }
  return true;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  exec,
  formatVersion,
  matchVersion
});
