{"version": 3, "file": "isUnsafeAssignment.js", "sourceRoot": "", "sources": ["../src/isUnsafeAssignment.ts"], "names": [], "mappings": ";;;AACA,oDAA0D;AAC1D,qCAA0C;AAG1C,6CAAgE;AAEhE;;;;;;;;;GASG;AACH,SAAgB,kBAAkB,CAChC,IAAa,EACb,QAAiB,EACjB,OAAuB,EACvB,UAAgC;;IAEhC,IAAI,IAAA,0BAAa,EAAC,IAAI,CAAC,EAAE;QACvB,uCAAuC;QACvC,IAAI,IAAA,8BAAiB,EAAC,QAAQ,CAAC,EAAE;YAC/B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,IAAA,0BAAa,EAAC,QAAQ,CAAC,EAAE;YAC5B,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;SACnC;KACF;IAED,IAAI,IAAA,yBAAe,EAAC,IAAI,CAAC,IAAI,IAAA,yBAAe,EAAC,QAAQ,CAAC,EAAE;QACtD,mDAAmD;QACnD,wDAAwD;QACxD;;;;;;;;;UASE;QAEF,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,EAAE;YACnC,mGAAmG;YACnG,+DAA+D;YAC/D,OAAO,KAAK,CAAC;SACd;QAED,IACE,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,MAAK,sBAAc,CAAC,aAAa;YACjD,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;YACpD,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK;YAChC,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;YACjC,UAAU,CAAC,cAAc,IAAI,IAAI,EACjC;YACA,qCAAqC;YACrC,sFAAsF;YACtF,4FAA4F;YAC5F,OAAO,KAAK,CAAC;SACd;QAED,MAAM,aAAa,GAAG,MAAA,IAAI,CAAC,aAAa,mCAAI,EAAE,CAAC;QAC/C,MAAM,qBAAqB,GAAG,MAAA,QAAQ,CAAC,aAAa,mCAAI,EAAE,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YAChD,MAAM,GAAG,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAC7B,MAAM,WAAW,GAAG,qBAAqB,CAAC,CAAC,CAAC,CAAC;YAE7C,MAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;YACzE,IAAI,MAAM,EAAE;gBACV,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC;aACnC;SACF;QAED,OAAO,KAAK,CAAC;KACd;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAnED,gDAmEC"}