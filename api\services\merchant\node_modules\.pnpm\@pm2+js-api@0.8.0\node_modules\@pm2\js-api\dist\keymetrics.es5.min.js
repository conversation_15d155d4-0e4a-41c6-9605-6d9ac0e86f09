!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Keymetrics=e()}}(function(){return function(){return function e(t,n,o){function i(r,s){if(!n[r]){if(!t[r]){var l="function"==typeof require&&require;if(!s&&l)return l(r,!0);if(a)return a(r,!0);var u=new Error("Cannot find module '"+r+"'");throw u.code="MODULE_NOT_FOUND",u}var p=n[r]={exports:{}};t[r][0].call(p.exports,function(e){return i(t[r][1][e]||e)},p,p.exports,e,t,n,o)}return n[r].exports}for(var a="function"==typeof require&&require,r=0;r<o.length;r++)i(o[r]);return i}}()({1:[function(e,t,n){(function(n){(function(){"use strict";var o=e("./package.json"),i={headers:{"X-JS-API-Version":o.version},services:{API:"https://api.keymetrics.io",OAUTH:"https://id.keymetrics.io"},OAUTH_AUTHORIZE_ENDPOINT:"/api/oauth/authorize",OAUTH_CLIENT_ID:"795984050",ENVIRONNEMENT:n&&n.versions&&n.versions.node?"node":"browser",VERSION:o.version,IS_DEBUG:"undefined"!=typeof window&&window.location.host.match(/km.(io|local)/)||void 0!==n&&"true"===n.env.DEBUG};t.exports=Object.assign({},i)}).call(this)}).call(this,e("_process"))},{"./package.json":39,_process:37}],2:[function(e,t,n){(function(e,o,i){(function(){!function(e,o){o("object"==typeof n&&void 0!==t?n:e.async=e.async||{})}(this,function(n){"use strict";function a(e,t){t|=0;for(var n=Math.max(e.length-t,0),o=Array(n),i=0;i<n;i++)o[i]=e[t+i];return o}var r=function(e){var t=a(arguments,1);return function(){var n=a(arguments);return e.apply(null,t.concat(n))}},s=function(e){return function(){var t=a(arguments),n=t.pop();e.call(this,t,n)}};function l(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}var u="function"==typeof i&&i,p="object"==typeof e&&"function"==typeof e.nextTick;function c(e){setTimeout(e,0)}function d(e){return function(t){var n=a(arguments,1);e(function(){t.apply(null,n)})}}var f=d(u?i:p?e.nextTick:c);function m(e){return s(function(t,n){var o;try{o=e.apply(this,t)}catch(e){return n(e)}l(o)&&"function"==typeof o.then?o.then(function(e){y(n,null,e)},function(e){y(n,e.message?e:new Error(e))}):n(null,o)})}function y(e,t,n){try{e(t,n)}catch(e){f(h,e)}}function h(e){throw e}var v="function"==typeof Symbol;function b(e){return v&&"AsyncFunction"===e[Symbol.toStringTag]}function g(e){return b(e)?m(e):e}function k(e){return function(t){var n=a(arguments,1),o=s(function(n,o){var i=this;return e(t,function(e,t){g(e).apply(i,n.concat(t))},o)});return n.length?o.apply(this,n):o}}var w="object"==typeof o&&o&&o.Object===Object&&o,_="object"==typeof self&&self&&self.Object===Object&&self,T=w||_||Function("return this")(),j=T.Symbol,E=Object.prototype,A=E.hasOwnProperty,S=E.toString,O=j?j.toStringTag:void 0;var x=Object.prototype.toString;var C="[object Null]",P="[object Undefined]",L=j?j.toStringTag:void 0;function z(e){return null==e?void 0===e?P:C:L&&L in Object(e)?function(e){var t=A.call(e,O),n=e[O];try{e[O]=void 0;var o=!0}catch(e){}var i=S.call(e);return o&&(t?e[O]=n:delete e[O]),i}(e):function(e){return x.call(e)}(e)}var B="[object AsyncFunction]",D="[object Function]",F="[object GeneratorFunction]",I="[object Proxy]";var R=9007199254740991;function U(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=R}function N(e){return null!=e&&U(e.length)&&!function(e){if(!l(e))return!1;var t=z(e);return t==D||t==F||t==B||t==I}(e)}var q={};function M(){}function G(e){return function(){if(null!==e){var t=e;e=null,t.apply(this,arguments)}}}var H="function"==typeof Symbol&&Symbol.iterator,W=function(e){return H&&e[H]&&e[H]()};function V(e){return null!=e&&"object"==typeof e}var J="[object Arguments]";function K(e){return V(e)&&z(e)==J}var $=Object.prototype,Q=$.hasOwnProperty,X=$.propertyIsEnumerable,Y=K(function(){return arguments}())?K:function(e){return V(e)&&Q.call(e,"callee")&&!X.call(e,"callee")},Z=Array.isArray;var ee="object"==typeof n&&n&&!n.nodeType&&n,te=ee&&"object"==typeof t&&t&&!t.nodeType&&t,ne=te&&te.exports===ee?T.Buffer:void 0,oe=(ne?ne.isBuffer:void 0)||function(){return!1},ie=9007199254740991,ae=/^(?:0|[1-9]\d*)$/;function re(e,t){var n=typeof e;return!!(t=null==t?ie:t)&&("number"==n||"symbol"!=n&&ae.test(e))&&e>-1&&e%1==0&&e<t}var se={};se["[object Float32Array]"]=se["[object Float64Array]"]=se["[object Int8Array]"]=se["[object Int16Array]"]=se["[object Int32Array]"]=se["[object Uint8Array]"]=se["[object Uint8ClampedArray]"]=se["[object Uint16Array]"]=se["[object Uint32Array]"]=!0,se["[object Arguments]"]=se["[object Array]"]=se["[object ArrayBuffer]"]=se["[object Boolean]"]=se["[object DataView]"]=se["[object Date]"]=se["[object Error]"]=se["[object Function]"]=se["[object Map]"]=se["[object Number]"]=se["[object Object]"]=se["[object RegExp]"]=se["[object Set]"]=se["[object String]"]=se["[object WeakMap]"]=!1;var le,ue="object"==typeof n&&n&&!n.nodeType&&n,pe=ue&&"object"==typeof t&&t&&!t.nodeType&&t,ce=pe&&pe.exports===ue&&w.process,de=function(){try{var e=pe&&pe.require&&pe.require("util").types;return e||ce&&ce.binding&&ce.binding("util")}catch(e){}}(),fe=de&&de.isTypedArray,me=fe?(le=fe,function(e){return le(e)}):function(e){return V(e)&&U(e.length)&&!!se[z(e)]},ye=Object.prototype.hasOwnProperty;function he(e,t){var n=Z(e),o=!n&&Y(e),i=!n&&!o&&oe(e),a=!n&&!o&&!i&&me(e),r=n||o||i||a,s=r?function(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}(e.length,String):[],l=s.length;for(var u in e)!t&&!ye.call(e,u)||r&&("length"==u||i&&("offset"==u||"parent"==u)||a&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||re(u,l))||s.push(u);return s}var ve=Object.prototype;var be=function(e,t){return function(n){return e(t(n))}}(Object.keys,Object),ge=Object.prototype.hasOwnProperty;function ke(e){if(n=(t=e)&&t.constructor,t!==("function"==typeof n&&n.prototype||ve))return be(e);var t,n,o=[];for(var i in Object(e))ge.call(e,i)&&"constructor"!=i&&o.push(i);return o}function we(e){return N(e)?he(e):ke(e)}function _e(e){if(N(e))return function(e){var t=-1,n=e.length;return function(){return++t<n?{value:e[t],key:t}:null}}(e);var t,n,o,i,a=W(e);return a?function(e){var t=-1;return function(){var n=e.next();return n.done?null:(t++,{value:n.value,key:t})}}(a):(n=we(t=e),o=-1,i=n.length,function e(){var a=n[++o];return"__proto__"===a?e():o<i?{value:t[a],key:a}:null})}function Te(e){return function(){if(null===e)throw new Error("Callback was already called.");var t=e;e=null,t.apply(this,arguments)}}function je(e){return function(t,n,o){if(o=G(o||M),e<=0||!t)return o(null);var i=_e(t),a=!1,r=0,s=!1;function l(e,t){if(r-=1,e)a=!0,o(e);else{if(t===q||a&&r<=0)return a=!0,o(null);s||u()}}function u(){for(s=!0;r<e&&!a;){var t=i();if(null===t)return a=!0,void(r<=0&&o(null));r+=1,n(t.value,t.key,Te(l))}s=!1}u()}}function Ee(e,t,n,o){je(t)(e,g(n),o)}function Ae(e,t){return function(n,o,i){return e(n,t,o,i)}}function Se(e,t,n){n=G(n||M);var o=0,i=0,a=e.length;function r(e,t){e?n(e):++i!==a&&t!==q||n(null)}for(0===a&&n(null);o<a;o++)t(e[o],o,Te(r))}var Oe=Ae(Ee,1/0),xe=function(e,t,n){(N(e)?Se:Oe)(e,g(t),n)};function Ce(e){return function(t,n,o){return e(xe,t,g(n),o)}}function Pe(e,t,n,o){o=o||M,t=t||[];var i=[],a=0,r=g(n);e(t,function(e,t,n){var o=a++;r(e,function(e,t){i[o]=t,n(e)})},function(e){o(e,i)})}var Le=Ce(Pe),ze=k(Le);function Be(e){return function(t,n,o,i){return e(je(n),t,g(o),i)}}var De=Be(Pe),Fe=Ae(De,1),Ie=k(Fe);function Re(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}var Ue,Ne=function(e,t,n){for(var o=-1,i=Object(e),a=n(e),r=a.length;r--;){var s=a[Ue?r:++o];if(!1===t(i[s],s,i))break}return e};function qe(e,t){return e&&Ne(e,t,we)}function Me(e){return e!=e}function Ge(e,t,n){return t==t?function(e,t,n){for(var o=n-1,i=e.length;++o<i;)if(e[o]===t)return o;return-1}(e,t,n):function(e,t,n,o){for(var i=e.length,a=n+(o?1:-1);o?a--:++a<i;)if(t(e[a],a,e))return a;return-1}(e,Me,n)}var He=function(e,t,n){"function"==typeof t&&(n=t,t=null),n=G(n||M);var o=we(e).length;if(!o)return n(null);t||(t=o);var i={},r=0,s=!1,l=Object.create(null),u=[],p=[],c={};function d(e,t){u.push(function(){!function(e,t){if(s)return;var o=Te(function(t,o){if(r--,arguments.length>2&&(o=a(arguments,1)),t){var u={};qe(i,function(e,t){u[t]=e}),u[e]=o,s=!0,l=Object.create(null),n(t,u)}else i[e]=o,Re(l[e]||[],function(e){e()}),f()});r++;var u=g(t[t.length-1]);t.length>1?u(i,o):u(o)}(e,t)})}function f(){if(0===u.length&&0===r)return n(null,i);for(;u.length&&r<t;){u.shift()()}}function m(t){var n=[];return qe(e,function(e,o){Z(e)&&Ge(e,t,0)>=0&&n.push(o)}),n}qe(e,function(t,n){if(!Z(t))return d(n,[t]),void p.push(n);var o=t.slice(0,t.length-1),i=o.length;if(0===i)return d(n,t),void p.push(n);c[n]=i,Re(o,function(a){if(!e[a])throw new Error("async.auto task `"+n+"` has a non-existent dependency `"+a+"` in "+o.join(", "));!function(e,t){var n=l[e];n||(n=l[e]=[]);n.push(t)}(a,function(){0===--i&&d(n,t)})})}),function(){var e,t=0;for(;p.length;)e=p.pop(),t++,Re(m(e),function(e){0==--c[e]&&p.push(e)});if(t!==o)throw new Error("async.auto cannot execute tasks due to a recursive dependency")}(),f()};function We(e,t){for(var n=-1,o=null==e?0:e.length,i=Array(o);++n<o;)i[n]=t(e[n],n,e);return i}var Ve="[object Symbol]";var Je=1/0,Ke=j?j.prototype:void 0,$e=Ke?Ke.toString:void 0;function Qe(e){if("string"==typeof e)return e;if(Z(e))return We(e,Qe)+"";if(function(e){return"symbol"==typeof e||V(e)&&z(e)==Ve}(e))return $e?$e.call(e):"";var t=e+"";return"0"==t&&1/e==-Je?"-0":t}function Xe(e,t,n){var o=e.length;return n=void 0===n?o:n,!t&&n>=o?e:function(e,t,n){var o=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var a=Array(i);++o<i;)a[o]=e[o+t];return a}(e,t,n)}var Ye=RegExp("[\\u200d\\ud800-\\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");var Ze="[\\ud800-\\udfff]",et="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",tt="\\ud83c[\\udffb-\\udfff]",nt="[^\\ud800-\\udfff]",ot="(?:\\ud83c[\\udde6-\\uddff]){2}",it="[\\ud800-\\udbff][\\udc00-\\udfff]",at="(?:"+et+"|"+tt+")"+"?",rt="[\\ufe0e\\ufe0f]?"+at+("(?:\\u200d(?:"+[nt,ot,it].join("|")+")[\\ufe0e\\ufe0f]?"+at+")*"),st="(?:"+[nt+et+"?",et,ot,it,Ze].join("|")+")",lt=RegExp(tt+"(?="+tt+")|"+st+rt,"g");function ut(e){return function(e){return Ye.test(e)}(e)?function(e){return e.match(lt)||[]}(e):function(e){return e.split("")}(e)}var pt=/^\s+|\s+$/g;function ct(e,t,n){var o;if((e=null==(o=e)?"":Qe(o))&&(n||void 0===t))return e.replace(pt,"");if(!e||!(t=Qe(t)))return e;var i=ut(e),a=ut(t);return Xe(i,function(e,t){for(var n=-1,o=e.length;++n<o&&Ge(t,e[n],0)>-1;);return n}(i,a),function(e,t){for(var n=e.length;n--&&Ge(t,e[n],0)>-1;);return n}(i,a)+1).join("")}var dt=/^(?:async\s+)?(function)?\s*[^\(]*\(\s*([^\)]*)\)/m,ft=/,/,mt=/(=.+)?(\s*)$/,yt=/((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm;function ht(e,t){var n={};qe(e,function(e,t){var o,i,a=b(e),r=!a&&1===e.length||a&&0===e.length;if(Z(e))o=e.slice(0,-1),e=e[e.length-1],n[t]=o.concat(o.length>0?s:e);else if(r)n[t]=e;else{if(o=i=(i=(i=(i=(i=e).toString().replace(yt,"")).match(dt)[2].replace(" ",""))?i.split(ft):[]).map(function(e){return ct(e.replace(mt,""))}),0===e.length&&!a&&0===o.length)throw new Error("autoInject task functions require explicit parameters.");a||o.pop(),n[t]=o.concat(s)}function s(t,n){var i=We(o,function(e){return t[e]});i.push(n),g(e).apply(null,i)}}),He(n,t)}function vt(){this.head=this.tail=null,this.length=0}function bt(e,t){e.length=1,e.head=e.tail=t}function gt(e,t,n){if(null==t)t=1;else if(0===t)throw new Error("Concurrency must not be zero");var o=g(e),i=0,a=[],r=!1;function s(e,t,n){if(null!=n&&"function"!=typeof n)throw new Error("task callback must be a function");if(p.started=!0,Z(e)||(e=[e]),0===e.length&&p.idle())return f(function(){p.drain()});for(var o=0,i=e.length;o<i;o++){var a={data:e[o],callback:n||M};t?p._tasks.unshift(a):p._tasks.push(a)}r||(r=!0,f(function(){r=!1,p.process()}))}function l(e){return function(t){i-=1;for(var n=0,o=e.length;n<o;n++){var r=e[n],s=Ge(a,r,0);0===s?a.shift():s>0&&a.splice(s,1),r.callback.apply(r,arguments),null!=t&&p.error(t,r.data)}i<=p.concurrency-p.buffer&&p.unsaturated(),p.idle()&&p.drain(),p.process()}}var u=!1,p={_tasks:new vt,concurrency:t,payload:n,saturated:M,unsaturated:M,buffer:t/4,empty:M,drain:M,error:M,started:!1,paused:!1,push:function(e,t){s(e,!1,t)},kill:function(){p.drain=M,p._tasks.empty()},unshift:function(e,t){s(e,!0,t)},remove:function(e){p._tasks.remove(e)},process:function(){if(!u){for(u=!0;!p.paused&&i<p.concurrency&&p._tasks.length;){var e=[],t=[],n=p._tasks.length;p.payload&&(n=Math.min(n,p.payload));for(var r=0;r<n;r++){var s=p._tasks.shift();e.push(s),a.push(s),t.push(s.data)}i+=1,0===p._tasks.length&&p.empty(),i===p.concurrency&&p.saturated();var c=Te(l(e));o(t,c)}u=!1}},length:function(){return p._tasks.length},running:function(){return i},workersList:function(){return a},idle:function(){return p._tasks.length+i===0},pause:function(){p.paused=!0},resume:function(){!1!==p.paused&&(p.paused=!1,f(p.process))}};return p}function kt(e,t){return gt(e,1,t)}vt.prototype.removeLink=function(e){return e.prev?e.prev.next=e.next:this.head=e.next,e.next?e.next.prev=e.prev:this.tail=e.prev,e.prev=e.next=null,this.length-=1,e},vt.prototype.empty=function(){for(;this.head;)this.shift();return this},vt.prototype.insertAfter=function(e,t){t.prev=e,t.next=e.next,e.next?e.next.prev=t:this.tail=t,e.next=t,this.length+=1},vt.prototype.insertBefore=function(e,t){t.prev=e.prev,t.next=e,e.prev?e.prev.next=t:this.head=t,e.prev=t,this.length+=1},vt.prototype.unshift=function(e){this.head?this.insertBefore(this.head,e):bt(this,e)},vt.prototype.push=function(e){this.tail?this.insertAfter(this.tail,e):bt(this,e)},vt.prototype.shift=function(){return this.head&&this.removeLink(this.head)},vt.prototype.pop=function(){return this.tail&&this.removeLink(this.tail)},vt.prototype.toArray=function(){for(var e=Array(this.length),t=this.head,n=0;n<this.length;n++)e[n]=t.data,t=t.next;return e},vt.prototype.remove=function(e){for(var t=this.head;t;){var n=t.next;e(t)&&this.removeLink(t),t=n}return this};var wt=Ae(Ee,1);function _t(e,t,n,o){o=G(o||M);var i=g(n);wt(e,function(e,n,o){i(t,e,function(e,n){t=n,o(e)})},function(e){o(e,t)})}function Tt(){var e=We(arguments,g);return function(){var t=a(arguments),n=this,o=t[t.length-1];"function"==typeof o?t.pop():o=M,_t(e,t,function(e,t,o){t.apply(n,e.concat(function(e){var t=a(arguments,1);o(e,t)}))},function(e,t){o.apply(n,[e].concat(t))})}}var jt=function(){return Tt.apply(null,a(arguments).reverse())},Et=Array.prototype.concat,At=function(e,t,n,o){o=o||M;var i=g(n);De(e,t,function(e,t){i(e,function(e){return e?t(e):t(null,a(arguments,1))})},function(e,t){for(var n=[],i=0;i<t.length;i++)t[i]&&(n=Et.apply(n,t[i]));return o(e,n)})},St=Ae(At,1/0),Ot=Ae(At,1),xt=function(){var e=a(arguments),t=[null].concat(e);return function(){return arguments[arguments.length-1].apply(this,t)}};function Ct(e){return e}function Pt(e,t){return function(n,o,i,a){a=a||M;var r,s=!1;n(o,function(n,o,a){i(n,function(o,i){o?a(o):e(i)&&!r?(s=!0,r=t(!0,n),a(null,q)):a()})},function(e){e?a(e):a(null,s?r:t(!1))})}}function Lt(e,t){return t}var zt=Ce(Pt(Ct,Lt)),Bt=Be(Pt(Ct,Lt)),Dt=Ae(Bt,1);function Ft(e){return function(t){var n=a(arguments,1);n.push(function(t){var n=a(arguments,1);"object"==typeof console&&(t?console.error&&console.error(t):console[e]&&Re(n,function(t){console[e](t)}))}),g(t).apply(null,n)}}var It=Ft("dir");function Rt(e,t,n){n=Te(n||M);var o=g(e),i=g(t);function r(e){if(e)return n(e);var t=a(arguments,1);t.push(s),i.apply(this,t)}function s(e,t){return e?n(e):t?void o(r):n(null)}s(null,!0)}function Ut(e,t,n){n=Te(n||M);var o=g(e),i=function(e){if(e)return n(e);var r=a(arguments,1);if(t.apply(this,r))return o(i);n.apply(null,[null].concat(r))};o(i)}function Nt(e,t,n){Ut(e,function(){return!t.apply(this,arguments)},n)}function qt(e,t,n){n=Te(n||M);var o=g(t),i=g(e);function a(e){if(e)return n(e);i(r)}function r(e,t){return e?n(e):t?void o(a):n(null)}i(r)}function Mt(e){return function(t,n,o){return e(t,o)}}function Gt(e,t,n){xe(e,Mt(g(t)),n)}function Ht(e,t,n,o){je(t)(e,Mt(g(n)),o)}var Wt=Ae(Ht,1);function Vt(e){return b(e)?e:s(function(t,n){var o=!0;t.push(function(){var e=arguments;o?f(function(){n.apply(null,e)}):n.apply(null,e)}),e.apply(this,t),o=!1})}function Jt(e){return!e}var Kt=Ce(Pt(Jt,Jt)),$t=Be(Pt(Jt,Jt)),Qt=Ae($t,1);function Xt(e){return function(t){return null==t?void 0:t[e]}}function Yt(e,t,n,o){var i=new Array(t.length);e(t,function(e,t,o){n(e,function(e,n){i[t]=!!n,o(e)})},function(e){if(e)return o(e);for(var n=[],a=0;a<t.length;a++)i[a]&&n.push(t[a]);o(null,n)})}function Zt(e,t,n,o){var i=[];e(t,function(e,t,o){n(e,function(n,a){n?o(n):(a&&i.push({index:t,value:e}),o())})},function(e){e?o(e):o(null,We(i.sort(function(e,t){return e.index-t.index}),Xt("value")))})}function en(e,t,n,o){(N(t)?Yt:Zt)(e,t,g(n),o||M)}var tn=Ce(en),nn=Be(en),on=Ae(nn,1);function an(e,t){var n=Te(t||M),o=g(Vt(e));!function e(t){if(t)return n(t);o(e)}()}var rn=function(e,t,n,o){o=o||M;var i=g(n);De(e,t,function(e,t){i(e,function(n,o){return n?t(n):t(null,{key:o,val:e})})},function(e,t){for(var n={},i=Object.prototype.hasOwnProperty,a=0;a<t.length;a++)if(t[a]){var r=t[a].key,s=t[a].val;i.call(n,r)?n[r].push(s):n[r]=[s]}return o(e,n)})},sn=Ae(rn,1/0),ln=Ae(rn,1),un=Ft("log");function pn(e,t,n,o){o=G(o||M);var i={},a=g(n);Ee(e,t,function(e,t,n){a(e,t,function(e,o){if(e)return n(e);i[t]=o,n()})},function(e){o(e,i)})}var cn=Ae(pn,1/0),dn=Ae(pn,1);function fn(e,t){return t in e}function mn(e,t){var n=Object.create(null),o=Object.create(null);t=t||Ct;var i=g(e),r=s(function(e,r){var s=t.apply(null,e);fn(n,s)?f(function(){r.apply(null,n[s])}):fn(o,s)?o[s].push(r):(o[s]=[r],i.apply(null,e.concat(function(){var e=a(arguments);n[s]=e;var t=o[s];delete o[s];for(var i=0,r=t.length;i<r;i++)t[i].apply(null,e)})))});return r.memo=n,r.unmemoized=e,r}var yn=d(p?e.nextTick:u?i:c);function hn(e,t,n){n=n||M;var o=N(t)?[]:{};e(t,function(e,t,n){g(e)(function(e,i){arguments.length>2&&(i=a(arguments,1)),o[t]=i,n(e)})},function(e){n(e,o)})}function vn(e,t){hn(xe,e,t)}function bn(e,t,n){hn(je(t),e,n)}var gn=function(e,t){var n=g(e);return gt(function(e,t){n(e[0],t)},t,1)},kn=function(e,t){var n=gn(e,t);return n.push=function(e,t,o){if(null==o&&(o=M),"function"!=typeof o)throw new Error("task callback must be a function");if(n.started=!0,Z(e)||(e=[e]),0===e.length)return f(function(){n.drain()});t=t||0;for(var i=n._tasks.head;i&&t>=i.priority;)i=i.next;for(var a=0,r=e.length;a<r;a++){var s={data:e[a],priority:t,callback:o};i?n._tasks.insertBefore(i,s):n._tasks.push(s)}f(n.process)},delete n.unshift,n};function wn(e,t){if(t=G(t||M),!Z(e))return t(new TypeError("First argument to race must be an array of functions"));if(!e.length)return t();for(var n=0,o=e.length;n<o;n++)g(e[n])(t)}function _n(e,t,n,o){_t(a(e).reverse(),t,n,o)}function Tn(e){var t=g(e);return s(function(e,n){return e.push(function(e,t){var o;e?n(null,{error:e}):(o=arguments.length<=2?t:a(arguments,1),n(null,{value:o}))}),t.apply(this,e)})}function jn(e){var t;return Z(e)?t=We(e,Tn):(t={},qe(e,function(e,n){t[n]=Tn.call(this,e)})),t}function En(e,t,n,o){en(e,t,function(e,t){n(e,function(e,n){t(e,!n)})},o)}var An=Ce(En),Sn=Be(En),On=Ae(Sn,1);function xn(e){return function(){return e}}function Cn(e,t,n){var o=5,i=0,a={times:o,intervalFunc:xn(i)};if(arguments.length<3&&"function"==typeof e?(n=t||M,t=e):(!function(e,t){if("object"==typeof t)e.times=+t.times||o,e.intervalFunc="function"==typeof t.interval?t.interval:xn(+t.interval||i),e.errorFilter=t.errorFilter;else{if("number"!=typeof t&&"string"!=typeof t)throw new Error("Invalid arguments for async.retry");e.times=+t||o}}(a,e),n=n||M),"function"!=typeof t)throw new Error("Invalid arguments for async.retry");var r=g(t),s=1;!function e(){r(function(t){t&&s++<a.times&&("function"!=typeof a.errorFilter||a.errorFilter(t))?setTimeout(e,a.intervalFunc(s)):n.apply(null,arguments)})}()}var Pn=function(e,t){t||(t=e,e=null);var n=g(t);return s(function(t,o){function i(e){n.apply(null,t.concat(e))}e?Cn(e,i,o):Cn(i,o)})};function Ln(e,t){hn(wt,e,t)}var zn=Ce(Pt(Boolean,Ct)),Bn=Be(Pt(Boolean,Ct)),Dn=Ae(Bn,1);function Fn(e,t,n){var o=g(t);function i(e,t){var n=e.criteria,o=t.criteria;return n<o?-1:n>o?1:0}Le(e,function(e,t){o(e,function(n,o){if(n)return t(n);t(null,{value:e,criteria:o})})},function(e,t){if(e)return n(e);n(null,We(t.sort(i),Xt("value")))})}function In(e,t,n){var o=g(e);return s(function(i,a){var r,s=!1;i.push(function(){s||(a.apply(null,arguments),clearTimeout(r))}),r=setTimeout(function(){var t=e.name||"anonymous",o=new Error('Callback function "'+t+'" timed out.');o.code="ETIMEDOUT",n&&(o.info=n),s=!0,a(o)},t),o.apply(null,i)})}var Rn=Math.ceil,Un=Math.max;function Nn(e,t,n,o){var i=g(n);De(function(e,t,n,o){for(var i=-1,a=Un(Rn((t-e)/(n||1)),0),r=Array(a);a--;)r[o?a:++i]=e,e+=n;return r}(0,e,1),t,i,o)}var qn=Ae(Nn,1/0),Mn=Ae(Nn,1);function Gn(e,t,n,o){arguments.length<=3&&(o=n,n=t,t=Z(e)?[]:{}),o=G(o||M);var i=g(n);xe(e,function(e,n,o){i(t,e,n,o)},function(e){o(e,t)})}function Hn(e,t){var n,o=null;t=t||M,Wt(e,function(e,t){g(e)(function(e,i){n=arguments.length>2?a(arguments,1):i,o=e,t(!e)})},function(){t(o,n)})}function Wn(e){return function(){return(e.unmemoized||e).apply(null,arguments)}}function Vn(e,t,n){n=Te(n||M);var o=g(t);if(!e())return n(null);var i=function(t){if(t)return n(t);if(e())return o(i);var r=a(arguments,1);n.apply(null,[null].concat(r))};o(i)}function Jn(e,t,n){Vn(function(){return!e.apply(this,arguments)},t,n)}var Kn=function(e,t){if(t=G(t||M),!Z(e))return t(new Error("First argument to waterfall must be an array of functions"));if(!e.length)return t();var n=0;function o(t){var o=g(e[n++]);t.push(Te(i)),o.apply(null,t)}function i(i){if(i||n===e.length)return t.apply(null,arguments);o(a(arguments,1))}o([])},$n={apply:r,applyEach:ze,applyEachSeries:Ie,asyncify:m,auto:He,autoInject:ht,cargo:kt,compose:jt,concat:St,concatLimit:At,concatSeries:Ot,constant:xt,detect:zt,detectLimit:Bt,detectSeries:Dt,dir:It,doDuring:Rt,doUntil:Nt,doWhilst:Ut,during:qt,each:Gt,eachLimit:Ht,eachOf:xe,eachOfLimit:Ee,eachOfSeries:wt,eachSeries:Wt,ensureAsync:Vt,every:Kt,everyLimit:$t,everySeries:Qt,filter:tn,filterLimit:nn,filterSeries:on,forever:an,groupBy:sn,groupByLimit:rn,groupBySeries:ln,log:un,map:Le,mapLimit:De,mapSeries:Fe,mapValues:cn,mapValuesLimit:pn,mapValuesSeries:dn,memoize:mn,nextTick:yn,parallel:vn,parallelLimit:bn,priorityQueue:kn,queue:gn,race:wn,reduce:_t,reduceRight:_n,reflect:Tn,reflectAll:jn,reject:An,rejectLimit:Sn,rejectSeries:On,retry:Cn,retryable:Pn,seq:Tt,series:Ln,setImmediate:f,some:zn,someLimit:Bn,someSeries:Dn,sortBy:Fn,timeout:In,times:qn,timesLimit:Nn,timesSeries:Mn,transform:Gn,tryEach:Hn,unmemoize:Wn,until:Jn,waterfall:Kn,whilst:Vn,all:Kt,allLimit:$t,allSeries:Qt,any:zn,anyLimit:Bn,anySeries:Dn,find:zt,findLimit:Bt,findSeries:Dt,forEach:Gt,forEachSeries:Wt,forEachLimit:Ht,forEachOf:xe,forEachOfSeries:wt,forEachOfLimit:Ee,inject:_t,foldl:_t,foldr:_n,select:tn,selectLimit:nn,selectSeries:on,wrapSync:m};n.default=$n,n.apply=r,n.applyEach=ze,n.applyEachSeries=Ie,n.asyncify=m,n.auto=He,n.autoInject=ht,n.cargo=kt,n.compose=jt,n.concat=St,n.concatLimit=At,n.concatSeries=Ot,n.constant=xt,n.detect=zt,n.detectLimit=Bt,n.detectSeries=Dt,n.dir=It,n.doDuring=Rt,n.doUntil=Nt,n.doWhilst=Ut,n.during=qt,n.each=Gt,n.eachLimit=Ht,n.eachOf=xe,n.eachOfLimit=Ee,n.eachOfSeries=wt,n.eachSeries=Wt,n.ensureAsync=Vt,n.every=Kt,n.everyLimit=$t,n.everySeries=Qt,n.filter=tn,n.filterLimit=nn,n.filterSeries=on,n.forever=an,n.groupBy=sn,n.groupByLimit=rn,n.groupBySeries=ln,n.log=un,n.map=Le,n.mapLimit=De,n.mapSeries=Fe,n.mapValues=cn,n.mapValuesLimit=pn,n.mapValuesSeries=dn,n.memoize=mn,n.nextTick=yn,n.parallel=vn,n.parallelLimit=bn,n.priorityQueue=kn,n.queue=gn,n.race=wn,n.reduce=_t,n.reduceRight=_n,n.reflect=Tn,n.reflectAll=jn,n.reject=An,n.rejectLimit=Sn,n.rejectSeries=On,n.retry=Cn,n.retryable=Pn,n.seq=Tt,n.series=Ln,n.setImmediate=f,n.some=zn,n.someLimit=Bn,n.someSeries=Dn,n.sortBy=Fn,n.timeout=In,n.times=qn,n.timesLimit=Nn,n.timesSeries=Mn,n.transform=Gn,n.tryEach=Hn,n.unmemoize=Wn,n.until=Jn,n.waterfall=Kn,n.whilst=Vn,n.all=Kt,n.allLimit=$t,n.allSeries=Qt,n.any=zn,n.anyLimit=Bn,n.anySeries=Dn,n.find=zt,n.findLimit=Bt,n.findSeries=Dt,n.forEach=Gt,n.forEachSeries=Wt,n.forEachLimit=Ht,n.forEachOf=xe,n.forEachOfSeries=wt,n.forEachOfLimit=Ee,n.inject=_t,n.foldl=_t,n.foldr=_n,n.select=tn,n.selectLimit=nn,n.selectSeries=on,n.wrapSync=m,Object.defineProperty(n,"__esModule",{value:!0})})}).call(this)}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},e("timers").setImmediate)},{_process:37,timers:38}],3:[function(e,t,n){},{}],4:[function(e,t,n){(function(o){(function(){n.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;e.splice(1,0,n,"color: inherit");let o=0,i=0;e[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&"%c"===e&&(i=++o)}),e.splice(i,0,n)},n.save=function(e){try{e?n.storage.setItem("debug",e):n.storage.removeItem("debug")}catch(e){}},n.load=function(){let e;try{e=n.storage.getItem("debug")}catch(e){}!e&&void 0!==o&&"env"in o&&(e=o.env.DEBUG);return e},n.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},n.storage=function(){try{return localStorage}catch(e){}}(),n.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),n.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],n.log=console.debug||console.log||(()=>{}),t.exports=e("./common")(n);const{formatters:i}=t.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this)}).call(this,e("_process"))},{"./common":5,_process:37}],5:[function(e,t,n){t.exports=function(t){function n(e){let t,i,a,r=null;function s(...e){if(!s.enabled)return;const o=s,i=Number(new Date),a=i-(t||i);o.diff=a,o.prev=t,o.curr=i,t=i,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let r=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,i)=>{if("%%"===t)return"%";r++;const a=n.formatters[i];if("function"==typeof a){const n=e[r];t=a.call(o,n),e.splice(r,1),r--}return t}),n.formatArgs.call(o,e),(o.log||n.log).apply(o,e)}return s.namespace=e,s.useColors=n.useColors(),s.color=n.selectColor(e),s.extend=o,s.destroy=n.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==r?r:(i!==n.namespaces&&(i=n.namespaces,a=n.enabled(e)),a),set:e=>{r=e}}),"function"==typeof n.init&&n.init(s),s}function o(e,t){const o=n(this.namespace+(void 0===t?":":t)+e);return o.log=this.log,o}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return n.debug=n,n.default=n,n.coerce=function(e){return e instanceof Error?e.stack||e.message:e},n.disable=function(){const e=[...n.names.map(i),...n.skips.map(i).map(e=>"-"+e)].join(",");return n.enable(""),e},n.enable=function(e){let t;n.save(e),n.namespaces=e,n.names=[],n.skips=[];const o=("string"==typeof e?e:"").split(/[\s,]+/),i=o.length;for(t=0;t<i;t++)o[t]&&("-"===(e=o[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.slice(1)+"$")):n.names.push(new RegExp("^"+e+"$")))},n.enabled=function(e){if("*"===e[e.length-1])return!0;let t,o;for(t=0,o=n.skips.length;t<o;t++)if(n.skips[t].test(e))return!1;for(t=0,o=n.names.length;t<o;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=e("ms"),n.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(t).forEach(e=>{n[e]=t[e]}),n.names=[],n.skips=[],n.formatters={},n.selectColor=function(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return n.colors[Math.abs(t)%n.colors.length]},n.enable(n.load()),n}},{ms:36}],6:[function(e,t,n){(function(e,o){(function(){!function(i){var a=Object.hasOwnProperty,r=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},s="object"==typeof e&&"function"==typeof e.nextTick,l="function"==typeof Symbol,u="object"==typeof Reflect,p="function"==typeof o?o:setTimeout,c=l?u&&"function"==typeof Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return t.push.apply(t,Object.getOwnPropertySymbols(e)),t}:Object.keys;function d(){this._events={},this._conf&&f.call(this,this._conf)}function f(e){e&&(this._conf=e,e.delimiter&&(this.delimiter=e.delimiter),e.maxListeners!==i&&(this._maxListeners=e.maxListeners),e.wildcard&&(this.wildcard=e.wildcard),e.newListener&&(this._newListener=e.newListener),e.removeListener&&(this._removeListener=e.removeListener),e.verboseMemoryLeak&&(this.verboseMemoryLeak=e.verboseMemoryLeak),e.ignoreErrors&&(this.ignoreErrors=e.ignoreErrors),this.wildcard&&(this.listenerTree={}))}function m(t,n){var o="(node) warning: possible EventEmitter memory leak detected. "+t+" listeners added. Use emitter.setMaxListeners() to increase limit.";if(this.verboseMemoryLeak&&(o+=" Event name: "+n+"."),void 0!==e&&e.emitWarning){var i=new Error(o);i.name="MaxListenersExceededWarning",i.emitter=this,i.count=t,e.emitWarning(i)}else console.error(o),console.trace&&console.trace()}var y=function(e,t,n){var o=arguments.length;switch(o){case 0:return[];case 1:return[e];case 2:return[e,t];case 3:return[e,t,n];default:for(var i=new Array(o);o--;)i[o]=arguments[o];return i}};function h(e,t){for(var n={},o=e.length,a=t?t.length:0,r=0;r<o;r++)n[e[r]]=r<a?t[r]:i;return n}function v(e,t,n){var o,i;if(this._emitter=e,this._target=t,this._listeners={},this._listenersCount=0,(n.on||n.off)&&(o=n.on,i=n.off),t.addEventListener?(o=t.addEventListener,i=t.removeEventListener):t.addListener?(o=t.addListener,i=t.removeListener):t.on&&(o=t.on,i=t.off),!o&&!i)throw Error("target does not implement any known event API");if("function"!=typeof o)throw TypeError("on method must be a function");if("function"!=typeof i)throw TypeError("off method must be a function");this._on=o,this._off=i;var a=e._observers;a?a.push(this):e._observers=[this]}function b(e,t,n,o){var r=Object.assign({},t);if(!e)return r;if("object"!=typeof e)throw TypeError("options must be an object");var s,l,u,p=Object.keys(e),c=p.length;function d(e){throw Error('Invalid "'+s+'" option value'+(e?". Reason: "+e:""))}for(var f=0;f<c;f++){if(s=p[f],!o&&!a.call(t,s))throw Error('Unknown "'+s+'" option');(l=e[s])!==i&&(u=n[s],r[s]=u?u(l,d):l)}return r}function g(e,t){return"function"==typeof e&&e.hasOwnProperty("prototype")||t("value must be a constructor"),e}function k(e){var t="value must be type of "+e.join("|"),n=e.length,o=e[0],i=e[1];return 1===n?function(e,n){if(typeof e===o)return e;n(t)}:2===n?function(e,n){var a=typeof e;if(a===o||a===i)return e;n(t)}:function(o,i){for(var a=typeof o,r=n;r-- >0;)if(a===e[r])return o;i(t)}}Object.assign(v.prototype,{subscribe:function(e,t,n){var o=this,i=this._target,a=this._emitter,r=this._listeners,s=function(){var o=y.apply(null,arguments),r={data:o,name:t,original:e};n?!1!==n.call(i,r)&&a.emit.apply(a,[r.name].concat(o)):a.emit.apply(a,[t].concat(o))};if(r[e])throw Error("Event '"+e+"' is already listening");this._listenersCount++,a._newListener&&a._removeListener&&!o._onNewListener?(this._onNewListener=function(n){n===t&&null===r[e]&&(r[e]=s,o._on.call(i,e,s))},a.on("newListener",this._onNewListener),this._onRemoveListener=function(n){n===t&&!a.hasListeners(n)&&r[e]&&(r[e]=null,o._off.call(i,e,s))},r[e]=null,a.on("removeListener",this._onRemoveListener)):(r[e]=s,o._on.call(i,e,s))},unsubscribe:function(e){var t,n,o,i=this,a=this._listeners,r=this._emitter,s=this._off,l=this._target;if(e&&"string"!=typeof e)throw TypeError("event must be a string");function u(){i._onNewListener&&(r.off("newListener",i._onNewListener),r.off("removeListener",i._onRemoveListener),i._onNewListener=null,i._onRemoveListener=null);var e=j.call(r,i);r._observers.splice(e,1)}if(e){if(!(t=a[e]))return;s.call(l,e,t),delete a[e],--this._listenersCount||u()}else{for(o=(n=c(a)).length;o-- >0;)e=n[o],s.call(l,e,a[e]);this._listeners={},this._listenersCount=0,u()}}});var w=k(["function"]),_=k(["object","function"]);function T(e,t,n){var o,i,a,r=0,s=new e(function(l,u,p){function c(){i&&(i=null),r&&(clearTimeout(r),r=0)}n=b(n,{timeout:0,overload:!1},{timeout:function(e,t){return("number"!=typeof(e*=1)||e<0||!Number.isFinite(e))&&t("timeout must be a positive number"),e}});var d=function(e){c(),l(e)},f=function(e){c(),u(e)};(o=!n.overload&&"function"==typeof e.prototype.cancel&&"function"==typeof p)?t(d,f,p):(i=[function(e){f(e||Error("canceled"))}],t(d,f,function(e){if(a)throw Error("Unable to subscribe on cancel event asynchronously");if("function"!=typeof e)throw TypeError("onCancel callback must be a function");i.push(e)}),a=!0),n.timeout>0&&(r=setTimeout(function(){var e=Error("timeout");e.code="ETIMEDOUT",r=0,s.cancel(e),u(e)},n.timeout))});return o||(s.cancel=function(e){if(i){for(var t=i.length,n=1;n<t;n++)i[n](e);i[0](e),i=null}}),s}function j(e){var t=this._observers;if(!t)return-1;for(var n=t.length,o=0;o<n;o++)if(t[o]._target===e)return o;return-1}function E(e,t,n,o,i){if(!n)return null;if(0===o){var a=typeof t;if("string"===a){var r,s,l=0,u=0,p=this.delimiter,d=p.length;if(-1!==(s=t.indexOf(p))){r=new Array(5);do{r[l++]=t.slice(u,s),u=s+d}while(-1!==(s=t.indexOf(p,u)));r[l++]=t.slice(u),t=r,i=l}else t=[t],i=1}else"object"===a?i=t.length:(t=[t],i=1)}var f,m,y,h,v,b,g,k=null,w=t[o],_=t[o+1];if(o===i)n._listeners&&("function"==typeof n._listeners?(e&&e.push(n._listeners),k=[n]):(e&&e.push.apply(e,n._listeners),k=[n]));else{if("*"===w){for(s=(b=c(n)).length;s-- >0;)"_listeners"!==(f=b[s])&&(g=E(e,t,n[f],o+1,i))&&(k?k.push.apply(k,g):k=g);return k}if("**"===w){for((v=o+1===i||o+2===i&&"*"===_)&&n._listeners&&(k=E(e,t,n,i,i)),s=(b=c(n)).length;s-- >0;)"_listeners"!==(f=b[s])&&("*"===f||"**"===f?(n[f]._listeners&&!v&&(g=E(e,t,n[f],i,i))&&(k?k.push.apply(k,g):k=g),g=E(e,t,n[f],o,i)):g=E(e,t,n[f],f===_?o+2:o,i),g&&(k?k.push.apply(k,g):k=g));return k}n[w]&&(k=E(e,t,n[w],o+1,i))}if((m=n["*"])&&E(e,t,m,o+1,i),y=n["**"])if(o<i)for(y._listeners&&E(e,t,y,i,i),s=(b=c(y)).length;s-- >0;)"_listeners"!==(f=b[s])&&(f===_?E(e,t,y[f],o+2,i):f===w?E(e,t,y[f],o+1,i):((h={})[f]=y[f],E(e,t,{"**":h},o+1,i)));else y._listeners?E(e,t,y,i,i):y["*"]&&y["*"]._listeners&&E(e,t,y["*"],i,i);return k}function A(e){for(var t,n,o,i=c(e),a=i.length;a-- >0;)(t=e[n=i[a]])&&(o=!0,"_listeners"===n||A(t)||delete e[n]);return o}function S(e,t,n){this.emitter=e,this.event=t,this.listener=n}function O(e){this._events={},this._newListener=!1,this._removeListener=!1,this.verboseMemoryLeak=!1,f.call(this,e)}S.prototype.off=function(){return this.emitter.off(this.event,this.listener),this},O.EventEmitter2=O,O.prototype.listenTo=function(e,t,n){if("object"!=typeof e)throw TypeError("target musts be an object");var o=this;function a(t){if("object"!=typeof t)throw TypeError("events must be an object");var i,a=n.reducers,r=j.call(o,e);i=-1===r?new v(o,e,n):o._observers[r];for(var s,l=c(t),u=l.length,p="function"==typeof a,d=0;d<u;d++)s=l[d],i.subscribe(s,t[s]||s,p?a:a&&a[s])}return n=b(n,{on:i,off:i,reducers:i},{on:w,off:w,reducers:_}),r(t)?a(h(t)):a("string"==typeof t?h(t.split(/\s+/)):t),this},O.prototype.stopListeningTo=function(e,t){var n=this._observers;if(!n)return!1;var o,i=n.length,a=!1;if(e&&"object"!=typeof e)throw TypeError("target should be an object");for(;i-- >0;)o=n[i],e&&o._target!==e||(o.unsubscribe(t),a=!0);return a},O.prototype.delimiter=".",O.prototype.setMaxListeners=function(e){e!==i&&(this._maxListeners=e,this._conf||(this._conf={}),this._conf.maxListeners=e)},O.prototype.getMaxListeners=function(){return this._maxListeners},O.prototype.event="",O.prototype.once=function(e,t,n){return this._once(e,t,!1,n)},O.prototype.prependOnceListener=function(e,t,n){return this._once(e,t,!0,n)},O.prototype._once=function(e,t,n,o){return this._many(e,1,t,n,o)},O.prototype.many=function(e,t,n,o){return this._many(e,t,n,!1,o)},O.prototype.prependMany=function(e,t,n,o){return this._many(e,t,n,!0,o)},O.prototype._many=function(e,t,n,o,i){var a=this;if("function"!=typeof n)throw new Error("many only accepts instances of Function");function r(){return 0==--t&&a.off(e,r),n.apply(this,arguments)}return r._origin=n,this._on(e,r,o,i)},O.prototype.emit=function(){if(!this._events&&!this._all)return!1;this._events||d.call(this);var e,t,n,o,i,a,r=arguments[0],s=this.wildcard;if("newListener"===r&&!this._newListener&&!this._events.newListener)return!1;if(s&&(e=r,"newListener"!==r&&"removeListener"!==r&&"object"==typeof r)){if(n=r.length,l)for(o=0;o<n;o++)if("symbol"==typeof r[o]){a=!0;break}a||(r=r.join(this.delimiter))}var u,p=arguments.length;if(this._all&&this._all.length)for(o=0,n=(u=this._all.slice()).length;o<n;o++)switch(this.event=r,p){case 1:u[o].call(this,r);break;case 2:u[o].call(this,r,arguments[1]);break;case 3:u[o].call(this,r,arguments[1],arguments[2]);break;default:u[o].apply(this,arguments)}if(s)u=[],E.call(this,u,e,this.listenerTree,0,n);else{if("function"==typeof(u=this._events[r])){switch(this.event=r,p){case 1:u.call(this);break;case 2:u.call(this,arguments[1]);break;case 3:u.call(this,arguments[1],arguments[2]);break;default:for(t=new Array(p-1),i=1;i<p;i++)t[i-1]=arguments[i];u.apply(this,t)}return!0}u&&(u=u.slice())}if(u&&u.length){if(p>3)for(t=new Array(p-1),i=1;i<p;i++)t[i-1]=arguments[i];for(o=0,n=u.length;o<n;o++)switch(this.event=r,p){case 1:u[o].call(this);break;case 2:u[o].call(this,arguments[1]);break;case 3:u[o].call(this,arguments[1],arguments[2]);break;default:u[o].apply(this,t)}return!0}if(!this.ignoreErrors&&!this._all&&"error"===r)throw arguments[1]instanceof Error?arguments[1]:new Error("Uncaught, unspecified 'error' event.");return!!this._all},O.prototype.emitAsync=function(){if(!this._events&&!this._all)return!1;this._events||d.call(this);var e,t,n,o,i,a,r=arguments[0],s=this.wildcard;if("newListener"===r&&!this._newListener&&!this._events.newListener)return Promise.resolve([!1]);if(s&&(e=r,"newListener"!==r&&"removeListener"!==r&&"object"==typeof r)){if(o=r.length,l)for(i=0;i<o;i++)if("symbol"==typeof r[i]){t=!0;break}t||(r=r.join(this.delimiter))}var u,p=[],c=arguments.length;if(this._all)for(i=0,o=this._all.length;i<o;i++)switch(this.event=r,c){case 1:p.push(this._all[i].call(this,r));break;case 2:p.push(this._all[i].call(this,r,arguments[1]));break;case 3:p.push(this._all[i].call(this,r,arguments[1],arguments[2]));break;default:p.push(this._all[i].apply(this,arguments))}if(s?(u=[],E.call(this,u,e,this.listenerTree,0)):u=this._events[r],"function"==typeof u)switch(this.event=r,c){case 1:p.push(u.call(this));break;case 2:p.push(u.call(this,arguments[1]));break;case 3:p.push(u.call(this,arguments[1],arguments[2]));break;default:for(n=new Array(c-1),a=1;a<c;a++)n[a-1]=arguments[a];p.push(u.apply(this,n))}else if(u&&u.length){if(u=u.slice(),c>3)for(n=new Array(c-1),a=1;a<c;a++)n[a-1]=arguments[a];for(i=0,o=u.length;i<o;i++)switch(this.event=r,c){case 1:p.push(u[i].call(this));break;case 2:p.push(u[i].call(this,arguments[1]));break;case 3:p.push(u[i].call(this,arguments[1],arguments[2]));break;default:p.push(u[i].apply(this,n))}}else if(!this.ignoreErrors&&!this._all&&"error"===r)return arguments[1]instanceof Error?Promise.reject(arguments[1]):Promise.reject("Uncaught, unspecified 'error' event.");return Promise.all(p)},O.prototype.on=function(e,t,n){return this._on(e,t,!1,n)},O.prototype.prependListener=function(e,t,n){return this._on(e,t,!0,n)},O.prototype.onAny=function(e){return this._onAny(e,!1)},O.prototype.prependAny=function(e){return this._onAny(e,!0)},O.prototype.addListener=O.prototype.on,O.prototype._onAny=function(e,t){if("function"!=typeof e)throw new Error("onAny only accepts instances of Function");return this._all||(this._all=[]),t?this._all.unshift(e):this._all.push(e),this},O.prototype._on=function(t,n,o,a){if("function"==typeof t)return this._onAny(t,n),this;if("function"!=typeof n)throw new Error("on only accepts instances of Function");this._events||d.call(this);var r,l=this;return a!==i&&(r=function(t,n,o){if(!0===o)r=!0;else if(!1===o)a=!0;else{if(!o||"object"!=typeof o)throw TypeError("options should be an object or true");var a=o.async,r=o.promisify,l=o.nextTick,u=o.objectify}if(a||l||r){var c=n,d=n._origin||n;if(l&&!s)throw Error("process.nextTick is not supported");r===i&&(r="AsyncFunction"===n.constructor.name),(n=function(){var t=arguments,n=this,o=this.event;return r?l?Promise.resolve():new Promise(function(e){p(e)}).then(function(){return n.event=o,c.apply(n,t)}):(l?e.nextTick:p)(function(){n.event=o,c.apply(n,t)})})._async=!0,n._origin=d}return[n,u?new S(this,t,n):this]}.call(this,t,n,a),n=r[0],l=r[1]),this._newListener&&this.emit("newListener",t,n),this.wildcard?(function(e,t,n){var o,i,a=0,r=0,s=this.delimiter,l=s.length;if("string"==typeof e)if(-1!==(o=e.indexOf(s))){i=new Array(5);do{i[a++]=e.slice(r,o),r=o+l}while(-1!==(o=e.indexOf(s,r)));i[a++]=e.slice(r)}else i=[e],a=1;else i=e,a=e.length;if(a>1)for(o=0;o+1<a;o++)if("**"===i[o]&&"**"===i[o+1])return;var u,p=this.listenerTree;for(o=0;o<a;o++)if(p=p[u=i[o]]||(p[u]={}),o===a-1)return p._listeners?("function"==typeof p._listeners&&(p._listeners=[p._listeners]),n?p._listeners.unshift(t):p._listeners.push(t),!p._listeners.warned&&this._maxListeners>0&&p._listeners.length>this._maxListeners&&(p._listeners.warned=!0,m.call(this,p._listeners.length,u))):p._listeners=t,!0;return!0}.call(this,t,n,o),l):(this._events[t]?("function"==typeof this._events[t]&&(this._events[t]=[this._events[t]]),o?this._events[t].unshift(n):this._events[t].push(n),!this._events[t].warned&&this._maxListeners>0&&this._events[t].length>this._maxListeners&&(this._events[t].warned=!0,m.call(this,this._events[t].length,t))):this._events[t]=n,l)},O.prototype.off=function(e,t){if("function"!=typeof t)throw new Error("removeListener only takes instances of Function");var n,o=[];if(this.wildcard){var i="string"==typeof e?e.split(this.delimiter):e.slice();if(!(o=E.call(this,null,i,this.listenerTree,0)))return this}else{if(!this._events[e])return this;n=this._events[e],o.push({_listeners:n})}for(var a=0;a<o.length;a++){var s=o[a];if(n=s._listeners,r(n)){for(var l=-1,u=0,p=n.length;u<p;u++)if(n[u]===t||n[u].listener&&n[u].listener===t||n[u]._origin&&n[u]._origin===t){l=u;break}if(l<0)continue;return this.wildcard?s._listeners.splice(l,1):this._events[e].splice(l,1),0===n.length&&(this.wildcard?delete s._listeners:delete this._events[e]),this._removeListener&&this.emit("removeListener",e,t),this}(n===t||n.listener&&n.listener===t||n._origin&&n._origin===t)&&(this.wildcard?delete s._listeners:delete this._events[e],this._removeListener&&this.emit("removeListener",e,t))}return this.listenerTree&&A(this.listenerTree),this},O.prototype.offAny=function(e){var t,n=0,o=0;if(e&&this._all&&this._all.length>0){for(n=0,o=(t=this._all).length;n<o;n++)if(e===t[n])return t.splice(n,1),this._removeListener&&this.emit("removeListenerAny",e),this}else{if(t=this._all,this._removeListener)for(n=0,o=t.length;n<o;n++)this.emit("removeListenerAny",t[n]);this._all=[]}return this},O.prototype.removeListener=O.prototype.off,O.prototype.removeAllListeners=function(e){if(e===i)return!this._events||d.call(this),this;if(this.wildcard){var t,n=E.call(this,null,e,this.listenerTree,0);if(!n)return this;for(t=0;t<n.length;t++)n[t]._listeners=null;this.listenerTree&&A(this.listenerTree)}else this._events&&(this._events[e]=null);return this},O.prototype.listeners=function(e){var t,n,o,a,r,s=this._events;if(e===i){if(this.wildcard)throw Error("event name required for wildcard emitter");if(!s)return[];for(a=(t=c(s)).length,o=[];a-- >0;)"function"==typeof(n=s[t[a]])?o.push(n):o.push.apply(o,n);return o}if(this.wildcard){if(!(r=this.listenerTree))return[];var l=[],u="string"==typeof e?e.split(this.delimiter):e.slice();return E.call(this,l,u,r,0),l}return s&&(n=s[e])?"function"==typeof n?[n]:n:[]},O.prototype.eventNames=function(e){var t=this._events;return this.wildcard?function e(t,n,o,i){for(var a,r,s,l,u=c(t),p=u.length,d=t._listeners;p-- >0;)a=t[r=u[p]],s="_listeners"===r?o:o?o.concat(r):[r],l=i||"symbol"==typeof r,d&&n.push(l?s:s.join(this.delimiter)),"object"==typeof a&&e.call(this,a,n,s,l);return n}.call(this,this.listenerTree,[],null,e):t?c(t):[]},O.prototype.listenerCount=function(e){return this.listeners(e).length},O.prototype.hasListeners=function(e){if(this.wildcard){var t=[],n="string"==typeof e?e.split(this.delimiter):e.slice();return E.call(this,t,n,this.listenerTree,0),t.length>0}var o=this._events,a=this._all;return!!(a&&a.length||o&&(e===i?c(o).length:o[e]))},O.prototype.listenersAny=function(){return this._all?this._all:[]},O.prototype.waitFor=function(e,t){var n=this,o=typeof t;return"number"===o?t={timeout:t}:"function"===o&&(t={filter:t}),T((t=b(t,{timeout:0,filter:i,handleError:!1,Promise:Promise,overload:!1},{filter:w,Promise:g})).Promise,function(o,i,a){function r(){var a=t.filter;if(!a||a.apply(n,arguments))if(n.off(e,r),t.handleError){var s=arguments[0];s?i(s):o(y.apply(null,arguments).slice(1))}else o(y.apply(null,arguments))}a(function(){n.off(e,r)}),n._on(e,r,!1)},{timeout:t.timeout,overload:t.overload})};var x=O.prototype;Object.defineProperties(O,{defaultMaxListeners:{get:function(){return x._maxListeners},set:function(e){if("number"!=typeof e||e<0||Number.isNaN(e))throw TypeError("n must be a non-negative number");x._maxListeners=e},enumerable:!0},once:{value:function(e,t,n){return T((n=b(n,{Promise:Promise,timeout:0,overload:!1},{Promise:g})).Promise,function(n,o,i){var a;if("function"==typeof e.addEventListener)return a=function(){n(y.apply(null,arguments))},i(function(){e.removeEventListener(t,a)}),void e.addEventListener(t,a,{once:!0});var r,s=function(){r&&e.removeListener("error",r),n(y.apply(null,arguments))};"error"!==t&&(r=function(n){e.removeListener(t,s),o(n)},e.once("error",r)),i(function(){r&&e.removeListener("error",r),e.removeListener(t,s)}),e.once(t,s)},{timeout:n.timeout,overload:n.overload})},writable:!0,configurable:!0}}),Object.defineProperties(x,{_maxListeners:{value:10,writable:!0,configurable:!0},_observers:{value:null,writable:!0,configurable:!0}}),"object"==typeof n?t.exports=O:new Function("","return this")().EventEmitter2=O}()}).call(this)}).call(this,e("_process"),e("timers").setImmediate)},{_process:37,timers:38}],7:[function(e,t,n){t.exports=e("./lib/axios")},{"./lib/axios":9}],8:[function(e,t,n){"use strict";var o=e("./../utils"),i=e("./../core/settle"),a=e("./../helpers/cookies"),r=e("./../helpers/buildURL"),s=e("../core/buildFullPath"),l=e("./../helpers/parseHeaders"),u=e("./../helpers/isURLSameOrigin"),p=e("../core/createError");t.exports=function(e){return new Promise(function(t,n){var c=e.data,d=e.headers,f=e.responseType;o.isFormData(c)&&delete d["Content-Type"];var m=new XMLHttpRequest;if(e.auth){var y=e.auth.username||"",h=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";d.Authorization="Basic "+btoa(y+":"+h)}var v=s(e.baseURL,e.url);function b(){if(m){var o="getAllResponseHeaders"in m?l(m.getAllResponseHeaders()):null,a={data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:o,config:e,request:m};i(t,n,a),m=null}}if(m.open(e.method.toUpperCase(),r(v,e.params,e.paramsSerializer),!0),m.timeout=e.timeout,"onloadend"in m?m.onloadend=b:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(b)},m.onabort=function(){m&&(n(p("Request aborted",e,"ECONNABORTED",m)),m=null)},m.onerror=function(){n(p("Network Error",e,null,m)),m=null},m.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(p(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",m)),m=null},o.isStandardBrowserEnv()){var g=(e.withCredentials||u(v))&&e.xsrfCookieName?a.read(e.xsrfCookieName):void 0;g&&(d[e.xsrfHeaderName]=g)}"setRequestHeader"in m&&o.forEach(d,function(e,t){void 0===c&&"content-type"===t.toLowerCase()?delete d[t]:m.setRequestHeader(t,e)}),o.isUndefined(e.withCredentials)||(m.withCredentials=!!e.withCredentials),f&&"json"!==f&&(m.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&m.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&m.upload&&m.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then(function(e){m&&(m.abort(),n(e),m=null)}),c||(c=null),m.send(c)})}},{"../core/buildFullPath":15,"../core/createError":16,"./../core/settle":20,"./../helpers/buildURL":24,"./../helpers/cookies":26,"./../helpers/isURLSameOrigin":29,"./../helpers/parseHeaders":31,"./../utils":34}],9:[function(e,t,n){"use strict";var o=e("./utils"),i=e("./helpers/bind"),a=e("./core/Axios"),r=e("./core/mergeConfig");function s(e){var t=new a(e),n=i(a.prototype.request,t);return o.extend(n,a.prototype,t),o.extend(n,t),n}var l=s(e("./defaults"));l.Axios=a,l.create=function(e){return s(r(l.defaults,e))},l.Cancel=e("./cancel/Cancel"),l.CancelToken=e("./cancel/CancelToken"),l.isCancel=e("./cancel/isCancel"),l.all=function(e){return Promise.all(e)},l.spread=e("./helpers/spread"),l.isAxiosError=e("./helpers/isAxiosError"),t.exports=l,t.exports.default=l},{"./cancel/Cancel":10,"./cancel/CancelToken":11,"./cancel/isCancel":12,"./core/Axios":13,"./core/mergeConfig":19,"./defaults":22,"./helpers/bind":23,"./helpers/isAxiosError":28,"./helpers/spread":32,"./utils":34}],10:[function(e,t,n){"use strict";function o(e){this.message=e}o.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},o.prototype.__CANCEL__=!0,t.exports=o},{}],11:[function(e,t,n){"use strict";var o=e("./Cancel");function i(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise(function(e){t=e});var n=this;e(function(e){n.reason||(n.reason=new o(e),t(n.reason))})}i.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},i.source=function(){var e;return{token:new i(function(t){e=t}),cancel:e}},t.exports=i},{"./Cancel":10}],12:[function(e,t,n){"use strict";t.exports=function(e){return!(!e||!e.__CANCEL__)}},{}],13:[function(e,t,n){"use strict";var o=e("./../utils"),i=e("../helpers/buildURL"),a=e("./InterceptorManager"),r=e("./dispatchRequest"),s=e("./mergeConfig"),l=e("../helpers/validator"),u=l.validators;function p(e){this.defaults=e,this.interceptors={request:new a,response:new a}}p.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=s(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&l.assertOptions(t,{silentJSONParsing:u.transitional(u.boolean,"1.0.0"),forcedJSONParsing:u.transitional(u.boolean,"1.0.0"),clarifyTimeoutError:u.transitional(u.boolean,"1.0.0")},!1);var n=[],o=!0;this.interceptors.request.forEach(function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(o=o&&t.synchronous,n.unshift(t.fulfilled,t.rejected))});var i,a=[];if(this.interceptors.response.forEach(function(e){a.push(e.fulfilled,e.rejected)}),!o){var p=[r,void 0];for(Array.prototype.unshift.apply(p,n),p=p.concat(a),i=Promise.resolve(e);p.length;)i=i.then(p.shift(),p.shift());return i}for(var c=e;n.length;){var d=n.shift(),f=n.shift();try{c=d(c)}catch(e){f(e);break}}try{i=r(c)}catch(e){return Promise.reject(e)}for(;a.length;)i=i.then(a.shift(),a.shift());return i},p.prototype.getUri=function(e){return e=s(this.defaults,e),i(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},o.forEach(["delete","get","head","options"],function(e){p.prototype[e]=function(t,n){return this.request(s(n||{},{method:e,url:t,data:(n||{}).data}))}}),o.forEach(["post","put","patch"],function(e){p.prototype[e]=function(t,n,o){return this.request(s(o||{},{method:e,url:t,data:n}))}}),t.exports=p},{"../helpers/buildURL":24,"../helpers/validator":33,"./../utils":34,"./InterceptorManager":14,"./dispatchRequest":17,"./mergeConfig":19}],14:[function(e,t,n){"use strict";var o=e("./../utils");function i(){this.handlers=[]}i.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},i.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},i.prototype.forEach=function(e){o.forEach(this.handlers,function(t){null!==t&&e(t)})},t.exports=i},{"./../utils":34}],15:[function(e,t,n){"use strict";var o=e("../helpers/isAbsoluteURL"),i=e("../helpers/combineURLs");t.exports=function(e,t){return e&&!o(t)?i(e,t):t}},{"../helpers/combineURLs":25,"../helpers/isAbsoluteURL":27}],16:[function(e,t,n){"use strict";var o=e("./enhanceError");t.exports=function(e,t,n,i,a){var r=new Error(e);return o(r,t,n,i,a)}},{"./enhanceError":18}],17:[function(e,t,n){"use strict";var o=e("./../utils"),i=e("./transformData"),a=e("../cancel/isCancel"),r=e("../defaults");function s(e){e.cancelToken&&e.cancelToken.throwIfRequested()}t.exports=function(e){return s(e),e.headers=e.headers||{},e.data=i.call(e,e.data,e.headers,e.transformRequest),e.headers=o.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),o.forEach(["delete","get","head","post","put","patch","common"],function(t){delete e.headers[t]}),(e.adapter||r.adapter)(e).then(function(t){return s(e),t.data=i.call(e,t.data,t.headers,e.transformResponse),t},function(t){return a(t)||(s(e),t&&t.response&&(t.response.data=i.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},{"../cancel/isCancel":12,"../defaults":22,"./../utils":34,"./transformData":21}],18:[function(e,t,n){"use strict";t.exports=function(e,t,n,o,i){return e.config=t,n&&(e.code=n),e.request=o,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},{}],19:[function(e,t,n){"use strict";var o=e("../utils");t.exports=function(e,t){t=t||{};var n={},i=["url","method","data"],a=["headers","auth","proxy","params"],r=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],s=["validateStatus"];function l(e,t){return o.isPlainObject(e)&&o.isPlainObject(t)?o.merge(e,t):o.isPlainObject(t)?o.merge({},t):o.isArray(t)?t.slice():t}function u(i){o.isUndefined(t[i])?o.isUndefined(e[i])||(n[i]=l(void 0,e[i])):n[i]=l(e[i],t[i])}o.forEach(i,function(e){o.isUndefined(t[e])||(n[e]=l(void 0,t[e]))}),o.forEach(a,u),o.forEach(r,function(i){o.isUndefined(t[i])?o.isUndefined(e[i])||(n[i]=l(void 0,e[i])):n[i]=l(void 0,t[i])}),o.forEach(s,function(o){o in t?n[o]=l(e[o],t[o]):o in e&&(n[o]=l(void 0,e[o]))});var p=i.concat(a).concat(r).concat(s),c=Object.keys(e).concat(Object.keys(t)).filter(function(e){return-1===p.indexOf(e)});return o.forEach(c,u),n}},{"../utils":34}],20:[function(e,t,n){"use strict";var o=e("./createError");t.exports=function(e,t,n){var i=n.config.validateStatus;n.status&&i&&!i(n.status)?t(o("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},{"./createError":16}],21:[function(e,t,n){"use strict";var o=e("./../utils"),i=e("./../defaults");t.exports=function(e,t,n){var a=this||i;return o.forEach(n,function(n){e=n.call(a,e,t)}),e}},{"./../defaults":22,"./../utils":34}],22:[function(e,t,n){(function(n){(function(){"use strict";var o=e("./utils"),i=e("./helpers/normalizeHeaderName"),a=e("./core/enhanceError"),r={"Content-Type":"application/x-www-form-urlencoded"};function s(e,t){!o.isUndefined(e)&&o.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var l,u={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:("undefined"!=typeof XMLHttpRequest?l=e("./adapters/xhr"):void 0!==n&&"[object process]"===Object.prototype.toString.call(n)&&(l=e("./adapters/http")),l),transformRequest:[function(e,t){return i(t,"Accept"),i(t,"Content-Type"),o.isFormData(e)||o.isArrayBuffer(e)||o.isBuffer(e)||o.isStream(e)||o.isFile(e)||o.isBlob(e)?e:o.isArrayBufferView(e)?e.buffer:o.isURLSearchParams(e)?(s(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):o.isObject(e)||t&&"application/json"===t["Content-Type"]?(s(t,"application/json"),function(e,t,n){if(o.isString(e))try{return(t||JSON.parse)(e),o.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,n=t&&t.silentJSONParsing,i=t&&t.forcedJSONParsing,r=!n&&"json"===this.responseType;if(r||i&&o.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(r){if("SyntaxError"===e.name)throw a(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300}};u.headers={common:{Accept:"application/json, text/plain, */*"}},o.forEach(["delete","get","head"],function(e){u.headers[e]={}}),o.forEach(["post","put","patch"],function(e){u.headers[e]=o.merge(r)}),t.exports=u}).call(this)}).call(this,e("_process"))},{"./adapters/http":8,"./adapters/xhr":8,"./core/enhanceError":18,"./helpers/normalizeHeaderName":30,"./utils":34,_process:37}],23:[function(e,t,n){"use strict";t.exports=function(e,t){return function(){for(var n=new Array(arguments.length),o=0;o<n.length;o++)n[o]=arguments[o];return e.apply(t,n)}}},{}],24:[function(e,t,n){"use strict";var o=e("./../utils");function i(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}t.exports=function(e,t,n){if(!t)return e;var a;if(n)a=n(t);else if(o.isURLSearchParams(t))a=t.toString();else{var r=[];o.forEach(t,function(e,t){null!==e&&void 0!==e&&(o.isArray(e)?t+="[]":e=[e],o.forEach(e,function(e){o.isDate(e)?e=e.toISOString():o.isObject(e)&&(e=JSON.stringify(e)),r.push(i(t)+"="+i(e))}))}),a=r.join("&")}if(a){var s=e.indexOf("#");-1!==s&&(e=e.slice(0,s)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}},{"./../utils":34}],25:[function(e,t,n){"use strict";t.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},{}],26:[function(e,t,n){"use strict";var o=e("./../utils");t.exports=o.isStandardBrowserEnv()?{write:function(e,t,n,i,a,r){var s=[];s.push(e+"="+encodeURIComponent(t)),o.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),o.isString(i)&&s.push("path="+i),o.isString(a)&&s.push("domain="+a),!0===r&&s.push("secure"),document.cookie=s.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},{"./../utils":34}],27:[function(e,t,n){"use strict";t.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},{}],28:[function(e,t,n){"use strict";t.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},{}],29:[function(e,t,n){"use strict";var o=e("./../utils");t.exports=o.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function i(e){var o=e;return t&&(n.setAttribute("href",o),o=n.href),n.setAttribute("href",o),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=i(window.location.href),function(t){var n=o.isString(t)?i(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},{"./../utils":34}],30:[function(e,t,n){"use strict";var o=e("../utils");t.exports=function(e,t){o.forEach(e,function(n,o){o!==t&&o.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[o])})}},{"../utils":34}],31:[function(e,t,n){"use strict";var o=e("./../utils"),i=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];t.exports=function(e){var t,n,a,r={};return e?(o.forEach(e.split("\n"),function(e){if(a=e.indexOf(":"),t=o.trim(e.substr(0,a)).toLowerCase(),n=o.trim(e.substr(a+1)),t){if(r[t]&&i.indexOf(t)>=0)return;r[t]="set-cookie"===t?(r[t]?r[t]:[]).concat([n]):r[t]?r[t]+", "+n:n}}),r):r}},{"./../utils":34}],32:[function(e,t,n){"use strict";t.exports=function(e){return function(t){return e.apply(null,t)}}},{}],33:[function(e,t,n){"use strict";var o=e("./../../package.json"),i={};["object","boolean","number","function","string","symbol"].forEach(function(e,t){i[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});var a={},r=o.version.split(".");function s(e,t){for(var n=t?t.split("."):r,o=e.split("."),i=0;i<3;i++){if(n[i]>o[i])return!0;if(n[i]<o[i])return!1}return!1}i.transitional=function(e,t,n){var i=t&&s(t);function r(e,t){return"[Axios v"+o.version+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,o,s){if(!1===e)throw new Error(r(o," has been removed in "+t));return i&&!a[o]&&(a[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,s)}},t.exports={isOlderVersion:s,assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var o=Object.keys(e),i=o.length;i-- >0;){var a=o[i],r=t[a];if(r){var s=e[a],l=void 0===s||r(s,a,e);if(!0!==l)throw new TypeError("option "+a+" must be "+l)}else if(!0!==n)throw Error("Unknown option "+a)}},validators:i}},{"./../../package.json":35}],34:[function(e,t,n){"use strict";var o=e("./helpers/bind"),i=Object.prototype.toString;function a(e){return"[object Array]"===i.call(e)}function r(e){return void 0===e}function s(e){return null!==e&&"object"==typeof e}function l(e){if("[object Object]"!==i.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function u(e){return"[object Function]"===i.call(e)}function p(e,t){if(null!==e&&void 0!==e)if("object"!=typeof e&&(e=[e]),a(e))for(var n=0,o=e.length;n<o;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}t.exports={isArray:a,isArrayBuffer:function(e){return"[object ArrayBuffer]"===i.call(e)},isBuffer:function(e){return null!==e&&!r(e)&&null!==e.constructor&&!r(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:s,isPlainObject:l,isUndefined:r,isDate:function(e){return"[object Date]"===i.call(e)},isFile:function(e){return"[object File]"===i.call(e)},isBlob:function(e){return"[object Blob]"===i.call(e)},isFunction:u,isStream:function(e){return s(e)&&u(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:p,merge:function e(){var t={};function n(n,o){l(t[o])&&l(n)?t[o]=e(t[o],n):l(n)?t[o]=e({},n):a(n)?t[o]=n.slice():t[o]=n}for(var o=0,i=arguments.length;o<i;o++)p(arguments[o],n);return t},extend:function(e,t,n){return p(t,function(t,i){e[i]=n&&"function"==typeof t?o(t,n):t}),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}},{"./helpers/bind":23}],35:[function(e,t,n){t.exports={name:"extrareqp2",version:"1.0.0",description:"Promise based HTTP client for the browser and node.js",main:"index.js",scripts:{test:"grunt test",start:"node ./sandbox/server.js",build:"NODE_ENV=production grunt build",preversion:"npm test",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json",postversion:"git push && git push --tags",examples:"node ./examples/server.js",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",fix:"eslint --fix lib/**/*.js"},repository:{type:"git",url:"https://github.com/keymetrics/extrareqp2"},keywords:["xhr","http","ajax","promise","node"],author:"Matt Zabriskie",license:"MIT",homepage:"https://axios-http.com",devDependencies:{coveralls:"^3.0.0","es6-promise":"^4.2.4",grunt:"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",mocha:"^8.2.1",sinon:"^4.5.0","terser-webpack-plugin":"^4.2.3",typescript:"^4.0.5","url-search-params":"^0.10.0",webpack:"^4.44.2","webpack-dev-server":"^3.11.0"},browser:{"./lib/adapters/http.js":"./lib/adapters/xhr.js"},jsdelivr:"dist/axios.min.js",unpkg:"dist/axios.min.js",typings:"./index.d.ts",dependencies:{"follow-redirects":"^1.14.0"},bundlesize:[{path:"./dist/axios.min.js",threshold:"5kB"}]}},{}],36:[function(e,t,n){var o=1e3,i=60*o,a=60*i,r=24*a,s=7*r,l=365.25*r;function u(e,t,n,o){var i=t>=1.5*n;return Math.round(e/n)+" "+o+(i?"s":"")}t.exports=function(e,t){t=t||{};var n=typeof e;if("string"===n&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var n=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return n*l;case"weeks":case"week":case"w":return n*s;case"days":case"day":case"d":return n*r;case"hours":case"hour":case"hrs":case"hr":case"h":return n*a;case"minutes":case"minute":case"mins":case"min":case"m":return n*i;case"seconds":case"second":case"secs":case"sec":case"s":return n*o;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return n;default:return}}(e);if("number"===n&&isFinite(e))return t.long?function(e){var t=Math.abs(e);if(t>=r)return u(e,t,r,"day");if(t>=a)return u(e,t,a,"hour");if(t>=i)return u(e,t,i,"minute");if(t>=o)return u(e,t,o,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=r)return Math.round(e/r)+"d";if(t>=a)return Math.round(e/a)+"h";if(t>=i)return Math.round(e/i)+"m";if(t>=o)return Math.round(e/o)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},{}],37:[function(e,t,n){var o,i,a=t.exports={};function r(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}function l(e){if(o===setTimeout)return setTimeout(e,0);if((o===r||!o)&&setTimeout)return o=setTimeout,setTimeout(e,0);try{return o(e,0)}catch(t){try{return o.call(null,e,0)}catch(t){return o.call(this,e,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:r}catch(e){o=r}try{i="function"==typeof clearTimeout?clearTimeout:s}catch(e){i=s}}();var u,p=[],c=!1,d=-1;function f(){c&&u&&(c=!1,u.length?p=u.concat(p):d=-1,p.length&&m())}function m(){if(!c){var e=l(f);c=!0;for(var t=p.length;t;){for(u=p,p=[];++d<t;)u&&u[d].run();d=-1,t=p.length}u=null,c=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===s||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function y(e,t){this.fun=e,this.array=t}function h(){}a.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];p.push(new y(e,t)),1!==p.length||c||l(m)},y.prototype.run=function(){this.fun.apply(null,this.array)},a.title="browser",a.browser=!0,a.env={},a.argv=[],a.version="",a.versions={},a.on=h,a.addListener=h,a.once=h,a.off=h,a.removeListener=h,a.removeAllListeners=h,a.emit=h,a.prependListener=h,a.prependOnceListener=h,a.listeners=function(e){return[]},a.binding=function(e){throw new Error("process.binding is not supported")},a.cwd=function(){return"/"},a.chdir=function(e){throw new Error("process.chdir is not supported")},a.umask=function(){return 0}},{}],38:[function(e,t,n){(function(t,o){(function(){var i=e("process/browser.js").nextTick,a=Function.prototype.apply,r=Array.prototype.slice,s={},l=0;function u(e,t){this._id=e,this._clearFn=t}n.setTimeout=function(){return new u(a.call(setTimeout,window,arguments),clearTimeout)},n.setInterval=function(){return new u(a.call(setInterval,window,arguments),clearInterval)},n.clearTimeout=n.clearInterval=function(e){e.close()},u.prototype.unref=u.prototype.ref=function(){},u.prototype.close=function(){this._clearFn.call(window,this._id)},n.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},n.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},n._unrefActive=n.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n.setImmediate="function"==typeof t?t:function(e){var t=l++,o=!(arguments.length<2)&&r.call(arguments,1);return s[t]=!0,i(function(){s[t]&&(o?e.apply(null,o):e.call(null),n.clearImmediate(t))}),t},n.clearImmediate="function"==typeof o?o:function(e){delete s[e]}}).call(this)}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":37,timers:38}],39:[function(e,t,n){t.exports={name:"@pm2/js-api",version:"0.8.0",description:"PM2.io API Client for Javascript",main:"index.js",unpkg:"dist/keymetrics.es5.min.js",engines:{node:">=4.0"},scripts:{test:"mocha test/*",build:"mkdir -p dist; browserify -s Keymetrics -r ./ > ./dist/keymetrics.es5.js",dist:"mkdir -p dist; browserify -s Keymetrics -r ./ | uglifyjs -c warnings=false -m > ./dist/keymetrics.es5.min.js",doc:"jsdoc -r ./src --readme ./README.md -d doc -t ./node_modules/minami",clean:"rm dist/*",prepare:"npm run build && npm run dist"},repository:{type:"git",url:"git+https://github.com/keymetrics/km.js.git"},keywords:["keymetrics","api","dashboard","monitoring","wrapper"],author:"Keymetrics Team",license:"Apache-2",bugs:{url:"https://github.com/keymetrics/km.js/issues"},homepage:"https://github.com/keymetrics/km.js#readme",dependencies:{async:"^2.6.3",extrareqp2:"^1.0.0",debug:"~4.3.1",eventemitter2:"^6.3.1",ws:"^7.0.0"},devDependencies:{"@babel/core":"^7.0.0","@babel/preset-env":"^7.0.0",babelify:"10.0.0",browserify:"^17.0.0",jsdoc:"^3.4.2",minami:"^1.1.1",mocha:"^7.0.0",should:"*","uglify-es":"~3.3.9"},browserify:{debug:!0,transform:[["babelify",{presets:[["@babel/preset-env",{debug:!1,forceAllTransforms:!0}]],sourceMaps:!0}]]},browser:{"./src/auth_strategies/embed_strategy.js":!1,ws:!1}}},{}],40:[function(e,t,n){t.exports={actions:[{route:{name:"/api/bucket/:id/actions/trigger",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"process_id",type:"number",description:"the id of the process",optional:!0,defaultvalue:null},{name:"app_name",type:"number",description:"the name of the process",optional:!0,defaultvalue:null},{name:"action_name",type:"string",description:"the name of the action to trigger",optional:!1,defaultvalue:null},{name:"opts",type:"object",description:"any specific options to be passed to the function",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:"success",type:"boolean",description:"succesully sended the action to PM2",optional:!1,defaultvalue:null}],name:"triggerAction",longname:"Actions.triggerAction",scope:"route"},{route:{name:"/api/bucket/:id/actions/triggerPM2",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"method_name",type:"string",description:"the name of the pm2 method to trigger",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"the name of the application",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"failed action",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:"success",type:"boolean",description:"succesully sended the action to PM2",optional:!1,defaultvalue:null}],name:"triggerPM2Action",longname:"Actions.triggerPM2Action",scope:"route"},{route:{name:"/api/bucket/:id/actions/triggerScopedAction",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of the server",optional:!1,defaultvalue:null},{name:"action_name",type:"string",description:"the name of the pm2 method to trigger",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"the name of the application",optional:!1,defaultvalue:null},{name:"pm_id",type:"number",description:"the id of the process",optional:!1,defaultvalue:null},{name:"opts",type:"object",description:"custom parameters to give to the action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully run the action",optional:!1}],response:[{name:".",type:"object",description:"the action sended to the process",optional:!1,defaultvalue:null}],name:"triggerScopedAction",longname:"Actions.triggerScopedAction",scope:"route"}],bucket:{alert:{analyzer:[{route:{name:"/api/bucket/:id/alerts/analyzer",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"size",type:"integer",description:"line limit, default to 20",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"list all alerts",optional:!1}],name:"list",longname:"Bucket.alert.analyzer.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/alerts/analyzer/:alert",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],body:[{name:"useful",type:"boolean",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"content modified",optional:!1}],name:"editState",longname:"Bucket.alert.analyzer.editState",scope:"route"},{route:{name:"/api/bucket/:id/alerts/analyzer/:analyzer/config",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":analyzer",type:"string",description:"analyzer name",optional:!1}],body:[{name:"blacklist",type:"object",description:"",optional:!1,defaultvalue:null},{name:"blacklist.apps",type:"array",description:"",optional:!0,defaultvalue:null},{name:"blacklist.servers",type:"array",description:"",optional:!0,defaultvalue:null},{name:"blacklist.metrics",type:"array",description:"",optional:!0,defaultvalue:null},{name:"threshold",type:"number",description:"",optional:!1,defaultvalue:null},{name:"window",type:"number",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"content modified",optional:!1}],name:"updateConfig",longname:"Bucket.alert.analyzer.updateConfig",scope:"route"}],default:[{route:{name:"/api/bucket/:id/alerts",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"Alert name",optional:!1,defaultvalue:null},{name:"enabled",type:"boolean",description:"Alert's state",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"Should be `metric`, `event` or `webcheck`",optional:!1,defaultvalue:null},{name:"initiator",type:"string",description:"Should be metric name or event name",optional:!1,defaultvalue:null},{name:"options",type:"object",description:"",optional:!1,defaultvalue:null},{name:"options.operator",type:"string",description:"Should be `>`, `<`, `=`, `>=` or `<=`",optional:!0,defaultvalue:null},{name:"options.threshold",type:"number",description:"Value to reach to send an alert",optional:!0,defaultvalue:null},{name:"options.act",type:"string",description:"Should be `always`, `opposite`, `first` or `diff`",optional:!0,defaultvalue:null},{name:"options.timerange",type:"number",description:"Timerange to check, in seconds",optional:!0,defaultvalue:null},{name:"scope",type:"object",description:"",optional:!1,defaultvalue:null},{name:"scope.apps",type:"object",description:"Array of strings with apps name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.servers",type:"object",description:"Array of strings with servers name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.initiators",type:"object",description:"Array of strings with initiators name (need to be set if no apps or servers)",optional:!0,defaultvalue:null},{name:"scope.sources",type:"object",description:"Array of strings with sources name (can be empty)",optional:!0,defaultvalue:null},{name:"actions",type:"object",description:"List of actions to trigger",optional:!1,defaultvalue:null},{name:"actions[].type",type:"string",description:"Type of action",optional:!0,defaultvalue:null},{name:"actions[].params",type:"object",description:"Params for action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created alert",optional:!1}],name:"create",longname:"Bucket.alert.create",scope:"route"},{route:{name:"/api/bucket/:id/alerts/:alert",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted alert",optional:!1}],name:"delete",longname:"Bucket.alert.delete",scope:"route",authentication:!1},{route:{name:"/api/bucket/:id/alerts/",type:"GET"},params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"list all alerts",optional:!1}],name:"list",longname:"Bucket.alert.list",scope:"route",authentication:!1},{route:{name:"/api/bucket/:id/alerts/:alert",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],body:[{name:"name",type:"string",description:"Alert name",optional:!0,defaultvalue:null},{name:"enabled",type:"boolean",description:"Alert's state",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"Should be `metric`, `event` or `webcheck`",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"Should be metric name or event name",optional:!0,defaultvalue:null},{name:"options",type:"object",description:"",optional:!0,defaultvalue:null},{name:"options.operator",type:"string",description:"Should be `>`, `<`, `=`, `<=` or `>=`",optional:!0,defaultvalue:null},{name:"options.threshold",type:"number",description:"Value to reach to send an alert",optional:!0,defaultvalue:null},{name:"options.act",type:"string",description:"Should be `always`, `opposite`, `first` or `diff`",optional:!0,defaultvalue:null},{name:"options.timerange",type:"number",description:"Timerange to check, in seconds",optional:!0,defaultvalue:null},{name:"scope",type:"object",description:"",optional:!0,defaultvalue:null},{name:"scope.apps",type:"array",description:"Array of strings with apps name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.servers",type:"array",description:"Array of strings with servers name (can be empty)",optional:!0,defaultvalue:null},{name:"scope.initiators",type:"object",description:"Array of strings with initiators name (need to be set if no apps or servers)",optional:!0,defaultvalue:null},{name:"scope.sources",type:"object",description:"Array of strings with sources name (can be empty)",optional:!0,defaultvalue:null},{name:"actions",type:"array",description:"List of actions to trigger",optional:!0,defaultvalue:null},{name:"actions[].type",type:"string",description:"Type of action",optional:!0,defaultvalue:null},{name:"actions[].params",type:"object",description:"Params for action",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"200",description:"successfuly created alert",optional:!1}],name:"updateAlert",longname:"Bucket.alert.updateAlert",scope:"route"},{route:{name:"/api/bucket/:id/alerts/:alert",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"200",description:"successfuly returned alert",optional:!1}],name:"get",longname:"Bucket.alert.get",scope:"route",async:!0},{route:{name:"/api/bucket/:id/alerts/:alert/sample",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":alert",type:"string",description:"alert id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"alert not found",optional:!1},{type:"202",description:"successfuly sended alert actions",optional:!1}],name:"triggerSample",longname:"Bucket.alert.triggerSample",scope:"route"},{route:{name:"/api/bucket/:id/alerts/update",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"triggers",type:"object",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"triggers",type:"object",description:"new triggers object",optional:!1,defaultvalue:null}],name:"update",longname:"Bucket.alert.update",scope:"route"},{route:{name:"/api/bucket/:id/alerts/updateSlack",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"slack",type:"object",description:"",optional:!1,defaultvalue:null},{name:"slack.active",type:"boolean",description:"",optional:!0,defaultvalue:null},{name:"slack.url",type:"boolean",description:"needed if active is set to true",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateSlack",longname:"Bucket.alert.updateSlack",scope:"route"},{route:{name:"/api/bucket/:id/alerts/updateWebhooks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"webhooks",type:"object",description:"",optional:!1,defaultvalue:null},{name:"webhooks.active",type:"boolean",description:"",optional:!0,defaultvalue:null},{name:"webhooks.url",type:"boolean",description:"needed if active is set to true",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing triggers parameter",optional:!1},{type:"200",description:"succesfully update triggers",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateWebhooks",longname:"Bucket.alert.updateWebhooks",scope:"route"}]},application:[{route:{name:"/api/bucket/:id/applications",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved applications",optional:!1}],name:"list",longname:"Bucket.application.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"application not found",optional:!1},{type:"200",description:"successfuly retrieved application",optional:!1}],name:"get",longname:"Bucket.application.get",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!1,defaultvalue:null},{name:"domains",type:"object",description:"Array of string with domains",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created application",optional:!1}],name:"create",longname:"Bucket.application.create",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"domains",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly updated application",optional:!1}],name:"update",longname:"Bucket.application.update",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted application",optional:!1}],name:"delete",longname:"Bucket.application.delete",scope:"route",async:!0,authentication:!1},{route:{name:"/api/bucket/:id/applications/:application/preview",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"preview not found",optional:!1},{type:"200",description:"successfuly retrieved application screenshot",optional:!1}],name:"getPreview",longname:"Bucket.application.getPreview",scope:"route",async:!0},{route:{name:"/api/bucket/:id/applications/:application/report",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":application",type:"string",description:"application id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"report not found",optional:!1},{type:"200",description:"successfuly retrieved application report",optional:!1}],name:"getReports",longname:"Bucket.application.getReports",scope:"route",async:!0}],billing:[{route:{name:"/api/bucket/:id/payment/subscribe",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null},{name:"stripe_token",type:"string",description:"a card token created by stripe",optional:!0,defaultvalue:null},{name:"coupon_id",type:"string",description:"the id of the stripe coupon",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"need a credit card OR not allowed to subscribe to the plan",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null},{name:"subscription",type:"object",description:"the subscription object attached to the subscription",optional:!1,defaultvalue:null}],name:"subscribe",longname:"Bucket.billing.subscribe",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/subscribe/:paymentIntent",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":paymentIntent",type:"string",description:"paymentIntent id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null}],name:"paymentIntentSucceed",longname:"Bucket.billing.paymentIntentSucceed",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/trial",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"Plan to trial",optional:!1,defaultvalue:null}],code:[{type:"400",description:"can't claim trial",optional:!1},{type:"200",description:"trial launched",optional:!1}],response:[{name:"duration",type:"string",description:"the duration of the trial",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"the plan of the trial",optional:!1,defaultvalue:null}],name:"startTrial",longname:"Bucket.billing.startTrial",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/invoices",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"This bucket hasn't invoices",optional:!1},{type:"200",description:"succesfully returns invoices",optional:!1}],response:[{name:".",type:"array",description:"array of invoices",optional:!1,defaultvalue:null}],name:"getInvoices",longname:"Bucket.billing.getInvoices",scope:"route"},{route:{name:"/api/bucket/:id/payment/receipts",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"This bucket hasn't receipts",optional:!1},{type:"200",description:"succesfully returns receipts",optional:!1}],response:[{name:".",type:"array",description:"array of receipts",optional:!1,defaultvalue:null}],name:"getReceipts",longname:"Bucket.billing.getReceipts",scope:"route"},{route:{name:"/api/bucket/:id/payment/subscription",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"404",description:"the bucket doesnt have any subscription",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the subscription",optional:!1}],response:[{name:".",type:"object",description:"subscription object",optional:!1,defaultvalue:null}],name:"getSubcription",longname:"Bucket.billing.getSubcription",scope:"route"},{route:{name:"/api/bucket/:id/payment/subscription/state",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"404",description:"the bucket doesnt have any subscription",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the subscription",optional:!1}],response:[{name:"status",type:"string",description:"stripe state of the subscription",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"stripe plan name of the subscription",optional:!1,defaultvalue:null},{name:"canceled_at",type:"string",description:"if he sub has been cancelled, add the date",optional:!1,defaultvalue:null}],name:"getSubcriptionState",longname:"Bucket.billing.getSubcriptionState",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/cards",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"token",type:"string",description:"card token generated by stripe",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully added the card",optional:!1}],response:[{name:"data",type:"object",description:"stripe credit card Object",optional:!1,defaultvalue:null}],name:"attachCreditCard",longname:"Bucket.billing.attachCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/cards",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the charges",optional:!1}],response:[{name:"data",type:"array",description:"list of stripe cards object",optional:!1,defaultvalue:null}],name:"fetchCreditCards",longname:"Bucket.billing.fetchCreditCards",scope:"route"},{route:{name:"/api/bucket/:id/payment/card/:card_id",type:"GET"},authentication:!0,params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"fetchCreditCard",longname:"Bucket.billing.fetchCreditCard",scope:"route"},{route:{name:"/api/bucket/:id/payment/card",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default card",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"fetchDefaultCreditCard",longname:"Bucket.billing.fetchDefaultCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/card",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"id",type:"string",description:"stripe card id",optional:!1,defaultvalue:null},{name:"address_line1",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_country",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_zip",type:"string",description:"",optional:!0,defaultvalue:null},{name:"address_city",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters, you need to specify a card",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe card object",optional:!1,defaultvalue:null}],name:"updateCreditCard",longname:"Bucket.billing.updateCreditCard",scope:"route"},{route:{name:"/api/bucket/:id/payment/card/:card_id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1},{type:"403",description:"the user must have one card active when having a subscription",optional:!1}],response:[{name:".",type:"object",description:"stripe card object",optional:!1,defaultvalue:null}],name:"deleteCreditCard",longname:"Bucket.billing.deleteCreditCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/card/:card_id/default",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":card_id",type:"string",description:"the stripe id of the card",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully set the card as default",optional:!1}],response:[{name:"data",type:"object",description:"stripe card object",optional:!1,defaultvalue:null}],name:"setDefaultCard",longname:"Bucket.billing.setDefaultCard",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters card_id",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:".",type:"object",description:"stripe metadata object",optional:!1,defaultvalue:null}],name:"fetchMetadata",longname:"Bucket.billing.fetchMetadata",scope:"route"},{route:{name:"/api/bucket/:id/payment",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"metadata",type:"object",description:"the metadata you can update",optional:!1,defaultvalue:null},{name:"metadata.vat_number",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metadata.company_name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metadata.receipt_email",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"400",description:"missing parameters, you need to specify a card",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:"data",type:"array",description:"stripe customer metadata object",optional:!1,defaultvalue:null}],name:"updateMetadata",longname:"Bucket.billing.updateMetadata",scope:"route",async:!0},{route:{name:"/api/bucket/:id/payment/banks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"iban",type:"string",description:"the iban used to recognize the account",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"the type of the bank account (currently only sepa is available)",optional:!1,defaultvalue:null},{name:"name",type:"string",description:"name of the bank account owner",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully added the account",optional:!1}],response:[{name:"data",type:"object",description:"stripe credit card Object",optional:!1,defaultvalue:null}],name:"attachBankAccount",longname:"Bucket.billing.attachBankAccount",scope:"route"},{route:{name:"/api/bucket/:id/payment/banks",type:"GET"},authentication:!0,params:[{name:":id",type:"string",description:"bucket id",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"404",description:"the user doesn't have any default account",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1}],response:[{name:"data",type:"object",description:"stripe source object",optional:!1,defaultvalue:null}],name:"fetchBankAccount",longname:"Bucket.billing.fetchBankAccount",scope:"route"},{route:{name:"/api/bucket/:id/payment/banks",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retieved the card",optional:!1},{type:"404",description:"the user doesn't have any default account",optional:!1}],response:[{name:".",type:"object",description:"stripe source object",optional:!1,defaultvalue:null}],name:"deleteBankAccount",longname:"Bucket.billing.deleteBankAccount",scope:"route"}],dashboardschema:[{route:{name:"/api/bucket/:id/dashboardSchema/",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"data",type:"object",description:"the list of component that compose the dashboard",optional:!1,defaultvalue:null},{name:"mode",type:"string",description:"the dashboard mode",optional:!1,defaultvalue:null},{name:"image",type:"object",description:"background image for the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully created dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"dashboard",description:"complete dashboard object from database",optional:!1,defaultvalue:null}],name:"create",longname:"Bucket.dashboardschema.create",scope:"route"},{route:{name:"/api/bucket/:id/dashboardSchema/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"retrieveAll",longname:"Bucket.dashboardschema.retrieveAll",scope:"route"},{route:{name:"/api/bucket/:id/dashboardSchema/:dashid/visualization",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"visualization",longname:"Bucket.dashboardschema.visualization",scope:"route"},{route:{name:"/api/bucket/:id/dashboardSchema/:dashid",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Bucket.dashboardschema.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/dashboardSchema/:dashid",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1},{type:"404",description:"dashboard not found",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"remove",longname:"Bucket.dashboardschema.remove",scope:"route"},{route:{name:"/api/bucket/:id/dashboardSchema/:dashId",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashId",type:"string",description:"dashboard id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"data",type:"object",description:"the data to populate the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"update",longname:"Bucket.dashboardschema.update",scope:"route"}],server:[{route:{name:"/api/bucket/:id/data/deleteServer",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"the name of server",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"406",description:"require an action before delete",optional:!1},{type:"400",description:"missing or invalid parameters",optional:!1},{type:"200",description:"successfully deleted",optional:!1}],response:[{name:"success",type:"boolean",description:"can be true or false",optional:!1,defaultvalue:null},{name:"msg",type:"string",description:"response",optional:!1,defaultvalue:null}],name:"deleteServer",longname:"Bucket.server.deleteServer",scope:"route"}],webcheck:[{route:{name:"/api/bucket/:id/webchecks/metrics",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks metrics",optional:!1}],name:"listMetrics",longname:"Bucket.webcheck.listMetrics",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/regions",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks regions",optional:!1}],name:"listRegions",longname:"Bucket.webcheck.listRegions",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck/metrics",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],body:[{name:"start",type:"string",description:"",optional:!0,defaultvalue:null},{name:"metrics",type:"array",description:"",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks regions",optional:!1}],name:"getMetrics",longname:"Bucket.webcheck.getMetrics",scope:"route",async:!0},{route:{name:"/api/bucket/:id/webchecks",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"application",type:"string",description:"Application's id to filter",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfuly retrieved webchecks",optional:!1}],name:"list",longname:"Bucket.webcheck.list",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"webcheck not found",optional:!1},{type:"200",description:"successfuly retrieved webcheck",optional:!1}],name:"get",longname:"Bucket.webcheck.get",scope:"route"},{route:{name:"/api/bucket/:id/webchecks",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"Webcheck name",optional:!1,defaultvalue:null},{name:"enabled",type:"boolean",description:"Webcheck's state",optional:!0,defaultvalue:null},{name:"target",type:"object",description:"",optional:!1,defaultvalue:null},{name:"target.type",type:"string",description:"Should be `http`, `https` or `tcp`",optional:!0,defaultvalue:null},{name:"target.port",type:"number",description:"Target's port",optional:!0,defaultvalue:null},{name:"target.address",type:"string",description:"Target's IP/domain",optional:!0,defaultvalue:null},{name:"target.path",type:"string",description:"HTTP Path (only for http/https)",optional:!0,defaultvalue:null},{name:"target.is_frontend",type:"boolean",description:"Enable or disable frontend metrics (via puppeteer)",optional:!0,defaultvalue:null},{name:"body",type:"string",description:"Body need to match this regex to succeed webcheck (only for http/https)",optional:!0,defaultvalue:null},{name:"interval",type:"number",description:"Webcheck's interval check (ms)",optional:!1,defaultvalue:null},{name:"timeout",type:"number",description:"Webcheck's timeout (ms)",optional:!1,defaultvalue:null},{name:"source",type:"object",description:"",optional:!1,defaultvalue:null},{name:"source.region",type:"string",description:"Webcheck's worker region",optional:!0,defaultvalue:null},{name:"retry",type:"object",description:"",optional:!1,defaultvalue:null},{name:"retry.max",type:"number",description:"Max webcheck's retry before mark as failed",optional:!0,defaultvalue:null},{name:"retry.interval",type:"number",description:"Webcheck's retry interval (ms)",optional:!0,defaultvalue:null},{name:"alerts",type:"object",description:"List of alerts (cf. Alert type)",optional:!1,defaultvalue:null},{name:"application",type:"string",description:"Application's id",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly created webcheck",optional:!1}],name:"create",longname:"Bucket.webcheck.create",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],body:[{name:"name",type:"string",description:"Webcheck name",optional:!0,defaultvalue:null},{name:"enabled",type:"boolean",description:"Webcheck's state",optional:!0,defaultvalue:null},{name:"target",type:"object",description:"",optional:!0,defaultvalue:null},{name:"target.type",type:"string",description:"Should be `http`, `https` or `tcp`",optional:!0,defaultvalue:null},{name:"target.port",type:"number",description:"Target's port",optional:!0,defaultvalue:null},{name:"target.address",type:"string",description:"Target's IP/domain",optional:!0,defaultvalue:null},{name:"target.path",type:"string",description:"HTTP Path (only for http/https)",optional:!0,defaultvalue:null},{name:"target.is_frontend",type:"boolean",description:"Enable or disable frontend metrics (via puppeteer)",optional:!0,defaultvalue:null},{name:"body",type:"string",description:"Body need to match this regex to succeed webcheck (only for http/https)",optional:!0,defaultvalue:null},{name:"interval",type:"number",description:"Webcheck's interval check (ms)",optional:!0,defaultvalue:null},{name:"timeout",type:"number",description:"Webcheck's timeout (ms)",optional:!0,defaultvalue:null},{name:"source",type:"object",description:"",optional:!0,defaultvalue:null},{name:"source.region",type:"string",description:"Webcheck's worker region",optional:!0,defaultvalue:null},{name:"retry",type:"object",description:"",optional:!0,defaultvalue:null},{name:"retry.max",type:"number",description:"Max webcheck's retry before mark as failed",optional:!0,defaultvalue:null},{name:"retry.interval",type:"number",description:"Webcheck's retry interval (ms)",optional:!0,defaultvalue:null},{name:"alerts",type:"object",description:"List of alerts (cf. Alert type)",optional:!0,defaultvalue:null},{name:"application",type:"string",description:"Application's id",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"200",description:"successfuly updated webcheck",optional:!1}],name:"update",longname:"Bucket.webcheck.update",scope:"route"},{route:{name:"/api/bucket/:id/webchecks/:webcheck",type:"DELETE"},params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":webcheck",type:"string",description:"webcheck id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"204",description:"successfuly deleted webcheck",optional:!1}],name:"delete",longname:"Bucket.webcheck.delete",scope:"route",authentication:!1}],default:[{route:{name:"/api/bucket/:id/feedback",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"feedback",type:"string",description:"the feedback text",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing feedback field",optional:!1},{type:"200",description:"succesfully registered the feedback",optional:!1}],response:[{name:"feedback",type:"string",description:"the feedback that hasn't been registered",optional:!1,defaultvalue:null}],name:"sendFeedback",longname:"Bucket.sendFeedback",scope:"route"},{name:"retrieveUsers",route:{name:"/api/bucket/:id/users_authorized",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved bucket's members",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveUsers",scope:"route"},{name:"currentRole",route:{name:"/api/bucket/:id/current_role",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"200",description:"succesfully retrieved the use role",optional:!1}],response:[{name:"role",type:"string",description:"the user role",optional:!1,defaultvalue:null}],longname:"Bucket.currentRole",scope:"route"},{route:{name:"/api/bucket/:id/manage_notif",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the user email",optional:!1,defaultvalue:null},{name:"state",type:"string",description:"the notification state you want to set for that user\n (either 'email' or 'nonde)",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"user not found",optional:!1}],response:[{name:".",type:"array",description:"array of state for each user",optional:!1,defaultvalue:null}],name:"setNotificationState",longname:"Bucket.setNotificationState",scope:"route"},{name:"inviteUser",route:{name:"/api/bucket/:id/add_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"you cant invit more users because you hit the bucket limit",optional:!1},{type:"200",description:"succesfully invited the user (either directly or by email)",optional:!1}],response:[{name:"invitations",type:"array",description:"the list of invitations actually active",optional:!1,defaultvalue:null}],longname:"Bucket.inviteUser",scope:"route"},{route:{name:"/api/bucket/:id/invitation",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"email",type:"string",description:"the email of the invitation you want to delete",optional:!0,defaultvalue:null}],code:[{type:"400",description:"invalid/missing parameters",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted the invitation",optional:!1}],response:[{name:"invitations",type:"array",description:"the list of invitations actually active",optional:!1,defaultvalue:null}],name:"removeInvitation",longname:"Bucket.removeInvitation",scope:"route"},{route:{name:"/api/bucket/:id/remove_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user you want to remove",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"impossible to remove the owner from the bucket",optional:!1},{type:"500",description:"database error",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],name:"removeUser",longname:"Bucket.removeUser",scope:"route"},{route:{name:"/api/bucket/:id/promote_user",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"email",type:"string",description:"the email of the user you want to change the role",optional:!1,defaultvalue:null},{name:"role",type:"string",description:"the role you want to set",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid/missing parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"impossible to set the role of the owner",optional:!1}],response:[{name:".",type:"array",description:"a array of user containing their email, username and roles",optional:!1,defaultvalue:null}],name:"setUserRole",longname:"Bucket.setUserRole",scope:"route"},{name:"retrieveAll",route:{name:"/api/bucket/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully fetched bucket",optional:!1}],response:[{name:".",type:"array",description:"array of buckets",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveAll",scope:"route"},{name:"create",route:{name:"/api/bucket/create_classic",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"name",type:"string",description:"the name of the bucket",optional:!1,defaultvalue:null},{name:"comment",type:"string",description:"any comments that will be written under the bucket name",optional:!0,defaultvalue:null},{name:"app_url",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"you cant create any more bucket",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully created a bucket",optional:!1}],response:[{name:"bucket",type:"object",description:"the created bucket",optional:!1,defaultvalue:null}],longname:"Bucket.create",scope:"route"},{deprecated:!0,route:{name:"/api/bucket/:id/start_trial",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"400",description:"can't claim trial",optional:!1},{type:"200",description:"trial launched",optional:!1}],response:[{name:"duration",type:"string",description:"the duration of the trial",optional:!1,defaultvalue:null},{name:"plan",type:"string",description:"the plan of the trial",optional:!1,defaultvalue:null}],name:"claimTrial",longname:"Bucket.claimTrial",scope:"route"},{deprecated:!0,name:"upgrade",route:{name:"/api/bucket/:id/upgrade",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"plan",type:"string",description:"name of the plan to upgrade to",optional:!1,defaultvalue:null},{name:"stripe_token",type:"string",description:"a card token created by stripe",optional:!0,defaultvalue:null},{name:"coupon_id",type:"string",description:"the id of the stripe coupon",optional:!0,defaultvalue:null}],code:[{type:"400",description:"missing/invalid parameters",optional:!1},{type:"403",description:"need a credit card OR not allowed to subscribe to the plan",optional:!1},{type:"500",description:"stripe/database error",optional:!1},{type:"200",description:"succesfully upgraded",optional:!1}],response:[{name:"bucket",type:"object",description:"the bucket object",optional:!1,defaultvalue:null},{name:"subscription",type:"object",description:"the subscription object attached to the subscription",optional:!1,defaultvalue:null}],longname:"Bucket.upgrade",scope:"route"},{name:"retrieve",route:{name:"/api/bucket/:id",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"200",description:"succesfully retrieved the bucket",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],longname:"Bucket.retrieve",scope:"route"},{route:{name:"/api/bucket/:id",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"",optional:!0,defaultvalue:null},{name:"comment",type:"string",description:"",optional:!0,defaultvalue:null},{name:"app_url",type:"string",description:"",optional:!0,defaultvalue:null},{name:"configuration",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],name:"update",longname:"Bucket.update",scope:"route",async:!0},{name:"retrieveServers",route:{name:"/api/bucket/:id/meta_servers",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved the server's metadata",optional:!1}],response:[{name:".",type:"array",description:"servers metadata",optional:!1,defaultvalue:null}],longname:"Bucket.retrieveServers",scope:"route"},{route:{name:"/api/bucket/:id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted the bucket",optional:!1}],response:[{name:".",type:"object",description:"the deleted bucket",optional:!1,defaultvalue:null}],name:"destroy",longname:"Bucket.destroy",scope:"route"},{route:{name:"/api/bucket/:id/transfer_ownership",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"new_owner",type:"string",description:"the wanted owner's email",optional:!1,defaultvalue:null}],code:[{type:"400",description:"Missing/invalid parameters",optional:!1},{type:"404",description:"user not found",optional:!1},{type:"403",description:"the new owner need to have a active credit card",optional:!1},{type:"200",description:"succesfully transfered the bucket, old owner is now admin",optional:!1}],response:[{name:".",type:"object",description:"bucket object",optional:!1,defaultvalue:null}],name:"transferOwnership",longname:"Bucket.transferOwnership",scope:"route"},{route:{name:"/api/bucket/:id/user_options",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"options",type:"object",description:"user options",optional:!1,defaultvalue:null}],code:[{type:"200",description:"succesfully update user options",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:"bucket",type:"object",description:"",optional:!1,defaultvalue:null}],name:"updateUserOptions",longname:"Bucket.updateUserOptions",scope:"route"}]},dashboard:[{route:{name:"/api/bucket/:id/dashboard/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"retrieveAll",longname:"Dashboard.retrieveAll",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashid",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Dashboard.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashid",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashid",type:"string",description:"dashboard id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1},{type:"404",description:"dashboard not found",optional:!1}],response:[{name:".",type:"array",description:"array of dashboards",optional:!1,defaultvalue:null}],name:"remove",longname:"Dashboard.remove",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/:dashId",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":dashId",type:"string",description:"dashboard id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"children",type:"object",description:"the list of component that compose the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"404",description:"dashboard not found",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of servers status",optional:!1,defaultvalue:null}],name:"update",longname:"Dashboard.update",scope:"route"},{route:{name:"/api/bucket/:id/dashboard/",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"name",type:"string",description:"the name of the dashboard",optional:!1,defaultvalue:null},{name:"children",type:"object",description:"the list of component that compose the dashboard",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully created dashboard",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"dashboard",description:"complete dashboard object from database",optional:!1,defaultvalue:null}],name:"create",longname:"Dashboard.create",scope:"route"}],data:{dependencies:[{route:{name:"/api/bucket/:id/data/dependencies/",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!1,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.dependencies.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.dependencies.retrieve",scope:"route"}],events:[{route:{name:"/api/bucket/:id/data/events",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"event_name",type:"string",description:"the event name to retrieve",optional:!1,defaultvalue:null},{name:"start",type:"date",description:"from which date to retrieve events",optional:!0,defaultvalue:null},{name:"end",type:"date",description:"to which date to retrieve events",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"limit",type:"number",description:"limit the number of events to retrieve",optional:!0,defaultvalue:100},{name:"offset",type:"number",description:"offset research by X",optional:!0,defaultvalue:0}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of events",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.events.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/eventsKeysByApp",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object representing events emitted for each application name",optional:!1,defaultvalue:null}],name:"retrieveMetadatas",longname:"Data.events.retrieveMetadatas",scope:"route"},{route:{name:"/api/bucket/:id/data/events/stats",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"event_name",type:"string",description:"the event name to retrieve",optional:!1,defaultvalue:null},{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"days",type:"number",description:"limit the number of days of data",optional:!0,defaultvalue:2},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of point (each point is one dimensional array, X are at 0 and Y at 1)",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.events.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/events/delete_all",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully deleted data",optional:!1}],response:[{name:".",type:"array",description:"array of object representing events emitted for each application name",optional:!1,defaultvalue:null}],name:"deleteAll",longname:"Data.events.deleteAll",scope:"route"}],exceptions:[{route:{name:"/api/bucket/:id/data/exceptions",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"filter exceptions by server source",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"filter exceptions by app source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out exceptions older than X (in minutes)",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of exceptions",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.exceptions.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/summary",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveSummary",longname:"Data.exceptions.retrieveSummary",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/delete_all",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"deleteAll",longname:"Data.exceptions.deleteAll",scope:"route"},{route:{name:"/api/bucket/:id/data/exceptions/delete",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"identifier",type:"string",description:"exception identifier",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"the application on which exception happened",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"the server on which exception happened",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing/invalid parameters",optional:!1}],response:[{name:".",type:"array",description:"array of deleted exceptions",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.exceptions.delete",scope:"route"}],issues:[{route:{name:"/api/bucket/:id/data/issues/list",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"server_name",type:"string",description:"filter exceptions by server source",optional:!0,defaultvalue:null},{name:"app_name",type:"string",description:"filter exceptions by app source needed if initiator+source not provided",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"exclude exceptions older than 'before' minutes",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"filter exceptions by initiator (node/golang/browser/webcheck...) needed with source",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter exceptions by source (browser app id, webcheck id...)",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"array of string to filter tags",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of exceptions",optional:!1,defaultvalue:null}],name:"list",longname:"Data.issues.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/occurrences/:identifier",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":identifier",type:"string",description:"issue identifier",optional:!1}],query:[{name:"from",type:"number",description:"",optional:!0,defaultvalue:null},{name:"search_after",type:"number",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of occurrences id",optional:!1,defaultvalue:null}],name:"listOccurencesForIdentifier",longname:"Data.issues.listOccurencesForIdentifier",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/replay/:uuid",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":uuid",type:"string",description:"replay id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"replay",type:"string",description:"",optional:!1,defaultvalue:null}],name:"getReplay",longname:"Data.issues.getReplay",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"a specific app name",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"ignore issue before this date",optional:!0,defaultvalue:null},{name:"identifier",type:"string",description:"a specific issue identifier",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"ignore issue before this date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"ignore issue after this date",optional:!0,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1},{name:"initiator",type:"string",description:"filter exceptions by initiator (node/golang/browser/webcheck...) needed with source",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter exceptions by source (browser app id, webcheck id...)",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"array of string to filter tags",optional:!0,defaultvalue:null},{name:"includeEmptyDocs",type:"boolean",description:"add empty docs",optional:!0,defaultvalue:!1},{name:"invertedTags",type:"boolean",description:"filter issue without tags",optional:!0,defaultvalue:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.issues.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/ocurrences",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"identifier",type:"object",description:"find occurence by using an issue identifier",optional:!0,defaultvalue:null},{name:"occurrence_id",type:"object",description:"find ocurrence by his id",optional:!0,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1},{name:"limit",type:"number",description:"limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing ocurrences",optional:!1,defaultvalue:null}],name:"findOccurences",longname:"Data.issues.findOccurences",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues/search",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"message",type:"string",description:"find occurence that match this message",optional:!1,defaultvalue:null},{name:"includeFixed",type:"boolean",description:"choose to ignore or not the fixed occurences",optional:!0,defaultvalue:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"search",longname:"Data.issues.search",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/summary/:aggregateBy",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":aggregateBy",type:"string",description:"servers, apps, initiators or sources",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"issues count aggregated",optional:!1,defaultvalue:null}],name:"summary",longname:"Data.issues.summary",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/issues",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"app_name",type:"string",description:"an specific application to delete application",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"deleteAll",longname:"Data.issues.deleteAll",scope:"route"},{route:{name:"/api/bucket/:id/data/issues/:identifier",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":identifier",type:"string",description:"the identifier of issue that you want to delete",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing/invalid parameters",optional:!1}],response:[{name:".",type:"array",description:"array of deleted exceptions",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.issues.delete",scope:"route"}],logs:[{route:{name:"/api/bucket/:id/data/logs",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"only search log oldest than <before>",optional:!0,defaultvalue:null},{name:"after",type:"string",description:"only search log newer than <after>",optional:!0,defaultvalue:null},{name:"size",type:"integer",description:"line limit, default to 100",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.logs.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.logs.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/logs/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"object",description:"a specific app name",optional:!0,defaultvalue:null},{name:"start",type:"object",description:"ignore log before this date",optional:!0,defaultvalue:null},{name:"interval",type:"object",description:"ignore log before this date",optional:!0,defaultvalue:null},{name:"end",type:"object",description:"ignore log after this date",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing exceptions for each application for each server",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.logs.retrieveHistogram",scope:"route"}],metrics:[{route:{name:"/api/bucket/:id/data/metrics/aggregations",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"aggregations",type:"object",description:"array of aggregations to compute",optional:!1,defaultvalue:null},{name:"aggregations[].name",type:"string",description:"the name of metric to compute the graph",optional:!1,defaultvalue:null},{name:"aggregations[].types",type:"array",description:"type of aggregation (e.g. ['histogram', 'servers'])",optional:!1,defaultvalue:null},{name:"aggregations[].start",type:"date",description:"oldest documents to aggregate on",optional:!1,defaultvalue:null},{name:"aggregations[].end",type:"date",description:"newest documents to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].apps",type:"array",description:"filter source applications to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].interval",type:"number",description:"interval between two points",optional:!0,defaultvalue:null},{name:"aggregations[].servers",type:"array",description:"filter source server to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].initiator",type:"string",description:"filter source initiator to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].webcheck",type:"string",description:"filter source webcheck to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].collector",type:"string",description:"filter source collector to aggregate on",optional:!0,defaultvalue:null},{name:"aggregations[].tags",type:"array",description:"filter tags to aggregate on",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"aggregations",optional:!1,defaultvalue:null}],examples:["// Example #1: Retrieve HTTP metrics of app INTERACTION, WEB-API, WORKER from all servers\n km.data.metrics.retrieveAggregations(bucket._id, {\n  aggregations: [\n    {\n     'start': 'now-5m',\n     'apps': ['INTERACTION', 'WEB-API', 'WORKER'],\n     'types': ['histogram', 'apps', 'servers'],\n     'name': 'HTTP'\n    }\n  ]\n})\n\n // Example #2: Retrieve HTTP metrics of ALL apps from all servers\n km.data.metrics.retrieveAggregations(bucket._id, {\n  aggregations: [\n    {\n     'start': 'now-1d',\n     'types': ['histogram', 'apps', 'servers'],\n     'name': 'HTTP'\n    }\n  ]\n})"],name:"retrieveAggregations",longname:"Data.metrics.retrieveAggregations",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter probes by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter probes by server source",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"},{name:"before",type:"string",description:"filter out probes that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics.agg_type",type:"string",description:"the type of aggregation for this probe",optional:!1,defaultvalue:null},{name:"server_name.app_name.metrics_name.timestamps_and_stats",type:"array",description:"array of point",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.metrics.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics/histogramPrecise",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"probe",type:"string",description:"probe name",optional:!1,defaultvalue:null},{name:"app",type:"string",description:"filter probes by app source",optional:!1,defaultvalue:null},{name:"server",type:"string",description:"filter probes by server source",optional:!1,defaultvalue:null},{name:"after",type:"string",description:"interval of time between two point (now-5d, now-5m...)",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"Array",type:"array",description:"of points",optional:!1,defaultvalue:null}],name:"retrieveHistogramPrecise",longname:"Data.metrics.retrieveHistogramPrecise",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics/list",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"servers",type:"object",description:"filter metrics by app name",optional:!0,defaultvalue:null},{name:"apps",type:"object",description:"filter metrics by server name",optional:!0,defaultvalue:null},{name:"initiator",type:"string",description:"filter metrics by a specific initiator",optional:!0,defaultvalue:null},{name:"source",type:"string",description:"filter metrics by a specific source",optional:!0,defaultvalue:null},{name:"collector",type:"string",description:"filter metrics by a specific collector",optional:!0,defaultvalue:null},{name:"webcheck",type:"string",description:"filter metrics by a specific webcheck",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"retrieveList",longname:"Data.metrics.retrieveList",scope:"route"},{route:{name:"/api/bucket/:id/data/metrics",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter metrics by app source",optional:!1,defaultvalue:null},{name:"server_name",type:"string",description:"filter metrics by server source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out metrics that are after X minute",optional:!0,defaultvalue:720}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],name:"retrieveMetadatas",longname:"Data.metrics.retrieveMetadatas",scope:"route"}],notifications:[{route:{name:"/api/bucket/:id/data/notifications",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"type",type:"string",description:"Type of notification",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"we search logs before this date (lower than)",optional:!0,defaultvalue:null},{name:"after",type:"string",description:"we search logs after this date (greater than)",optional:!0,defaultvalue:null},{name:"size",type:"number",description:"",optional:!0,defaultvalue:null},{name:"from",type:"number",description:"",optional:!0,defaultvalue:null},{name:"type",type:"string",description:"type of notification",optional:!0,defaultvalue:null},{name:"providers",type:"array",description:"find notifications with this providers",optional:!0,defaultvalue:null},{name:"contacts",type:"array",description:"find notifications with this contact",optional:!0,defaultvalue:null},{name:"size",type:"integer",description:"line limit, default to 20",optional:!0,defaultvalue:null},{name:"from",type:"integer",description:"offset limit",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of traces",value:"{Array} . array of traces",optional:!1,type:null}],name:"list",longname:"Data.notifications.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/notifications/:notification",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":notification",type:"string",description:"notification id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Object} . notification",value:"{Object} . notification",optional:!1,type:null}],name:"retrieve",longname:"Data.notifications.retrieve",scope:"route",async:!0}],outliers:[{route:{name:"/api/bucket/:id/data/outliers/",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"the application name",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter by server name",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"only search outlier newer than <start>",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"only search outlier older than <end>",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"missing parameters",optional:!1}],response:[{name:".",type:"array",description:"recorded dependencies",optional:!1,defaultvalue:null}],examples:["km.data.outliers.retrieve(bucket._id, {\n   app_name: 'my_api'\n })"],name:"retrieve",longname:"Data.outliers.retrieve",scope:"route"}],processes:[{route:{name:"/api/bucket/:id/data/processEvents",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null},{name:"before",type:"string",description:"filter out events that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of process events",optional:!1,defaultvalue:null}],name:"retrieveEvents",longname:"Data.processes.retrieveEvents",scope:"route"},{route:{name:"/api/bucket/:id/data/processEvents/deployments",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter events by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter events by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of deployments",optional:!1,defaultvalue:null}],name:"retrieveDeployments",longname:"Data.processes.retrieveDeployments",scope:"route"}],profiling:[{route:{name:"/api/bucket/:id/data/profilings/:filename",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"object",description:"return profile data",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Data.profiling.retrieve",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings/:filename/download",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"file",description:"return a file",optional:!1,defaultvalue:null}],name:"download",longname:"Data.profiling.download",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"apps",type:"object",description:"",optional:!0,defaultvalue:null},{name:"servers",type:"object",description:"",optional:!0,defaultvalue:null},{name:"from",type:"object",description:"",optional:!0,defaultvalue:null},{name:"size",type:"object",description:"",optional:!0,defaultvalue:null},{name:"type",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of object containing profilings",optional:!1,defaultvalue:null}],name:"list",longname:"Data.profiling.list",scope:"route"},{route:{name:"/api/bucket/:id/data/profilings/:filename",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":filename",type:"string",description:"filename",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters",optional:!1}],response:[{name:".",type:"file",description:"return a file",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.profiling.delete",scope:"route"}],status:[{route:{name:"/api/bucket/:id/data/status",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of servers status",value:"{Array} . array of servers status",optional:!1,type:null}],name:"retrieve",longname:"Data.status.retrieve",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/status/blacklisted",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of servers status",value:"{Array} . array of servers status",optional:!1,type:null}],name:"retrieveBlacklisted",longname:"Data.status.retrieveBlacklisted",scope:"route",async:!0}],traces:[{route:{name:"/api/bucket/:id/data/traces",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"includeSpans",type:"boolean",description:"",optional:!0,defaultvalue:!0},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"limit",type:"string",description:"default: 10, max: 100",optional:!0,defaultvalue:null},{name:"kind",type:"string",description:"",optional:!0,defaultvalue:null},{name:"minDuration",type:"number",description:"",optional:!0,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"Query string array like [error=500, error, ...]",optional:!0,defaultvalue:null},{name:"orderBy",type:"string",description:"Default: newest, enum: oldest, newest, shortest, longest",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Array} . array of traces",value:"{Array} . array of traces",optional:!1,type:null}],name:"list",longname:"Data.traces.list",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/:trace",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1},{name:":trace",type:"string",description:"trace id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],tags:[{originalTitle:"reponse",title:"reponse",text:"{Object} . trace",value:"{Object} . trace",optional:!1,type:null}],name:"retrieve",longname:"Data.traces.retrieve",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/services",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"services,",type:"object",description:"spans names",optional:!1,defaultvalue:null}],name:"getServices",longname:"Data.traces.getServices",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/tags",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"tags",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getTags",longname:"Data.traces.getTags",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/histogram/tag",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"tag",type:"string",description:"",optional:!1,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getHistogramByTag",longname:"Data.traces.getHistogramByTag",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/aggregation/tag",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"tag",type:"string",description:"",optional:!1,defaultvalue:null},{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getTagsValue",longname:"Data.traces.getTagsValue",scope:"route",async:!0},{route:{name:"/api/bucket/:id/data/traces/aggregation/duration",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"start",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"end",type:"string",description:"date",optional:!0,defaultvalue:null},{name:"serviceName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"spanName",type:"string",description:"",optional:!0,defaultvalue:null},{name:"tags",type:"array",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"aggregation",type:"array",description:"",optional:!1,defaultvalue:null}],name:"getDurationAvg",longname:"Data.traces.getDurationAvg",scope:"route",async:!0}],transactions:[{route:{name:"/api/bucket/:id/data/transactions/v2/histogram",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null},{name:"interval",type:"string",description:"interval of time between two point",optional:!0,defaultvalue:"minute"},{name:"before",type:"string",description:"filter out transactions that are after X minute",optional:!0,defaultvalue:60}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:".",type:"array",description:"array of times series containing points",optional:!1,defaultvalue:null}],name:"retrieveHistogram",longname:"Data.transactions.retrieveHistogram",scope:"route"},{route:{name:"/api/bucket/:id/data/transactions/v2/summary",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],body:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"transaction object",optional:!1,defaultvalue:null}],name:"retrieveSummary",longname:"Data.transactions.retrieveSummary",scope:"route"},{route:{name:"/api/bucket/:id/data/transactions/v2/delete",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"bucket id",optional:!1}],query:[{name:"app_name",type:"string",description:"filter transactions by app source",optional:!0,defaultvalue:null},{name:"server_name",type:"string",description:"filter transactions by server source",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1}],response:[{name:"server_name",type:"object",description:"",optional:!1,defaultvalue:null},{name:"server_name.app_name",type:"object",description:"transaction object",optional:!1,defaultvalue:null}],name:"delete",longname:"Data.transactions.delete",scope:"route"}]},misc:[{route:{name:"/api/misc/changelog",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:"changelog",type:"array",description:"articles",optional:!1,defaultvalue:null}],name:"listChangelogArticles",longname:"Misc.listChangelogArticles",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/release/pm2",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:"pm2_version",type:"string",description:"latest version",optional:!1,defaultvalue:null}],name:"retrievePM2Version",longname:"Misc.retrievePM2Version",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/release/nodejs/:version",type:"GET"},params:[{name:":version",type:"string",description:"semver version range",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"array",description:"array of releases matching the range requested",optional:!1,defaultvalue:null}],name:"retrieveNodeRelease",longname:"Misc.retrieveNodeRelease",scope:"route",authentication:!1},{route:{name:"/api/misc/plans",type:"GET"},code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"succesfully retrieved data",optional:!1},{type:"400",description:"Invalid params",optional:!1}],response:[{name:".",type:"object",description:"list of plans keyed by plan name",optional:!1,defaultvalue:null}],name:"retrievePlans",longname:"Misc.retrievePlans",scope:"route",params:[],authentication:!1},{route:{name:"/api/misc/stripe/retrieveCoupon",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"coupon",type:"string",description:"the coupon name",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:"coupon",type:"object",description:"the coupon object",optional:!1,defaultvalue:null}],name:"retrieveCoupon",longname:"Misc.retrieveCoupon",scope:"route",params:[]},{route:{name:"/api/misc/stripe/retrieveCompany",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"vat_id",type:"string",description:"the vat id of the company",optional:!1,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:".",type:"object",description:"metadata about company",optional:!1,defaultvalue:null}],name:"retrieveCompany",longname:"Misc.retrieveCompany",scope:"route",params:[]},{route:{name:"/api/misc/stripe/retrieveVat",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"country",type:"string",description:"country code of the user",optional:!0,defaultvalue:null}],code:[{type:"500",description:"stripe error",optional:!1},{type:"200",description:"succesfully retrieved the metadata",optional:!1}],response:[{name:"coupon",type:"object",description:"the coupon object",optional:!1,defaultvalue:null}],name:"retrieveVAT",longname:"Misc.retrieveVAT",scope:"route",params:[]}],node:[{route:{name:"/api/node/default",type:"GET"},response:[{name:"node",type:"object",description:"Return node object",optional:!1,defaultvalue:null}],name:"getDefaultNode",longname:"Node.getDefaultNode",scope:"route",params:[],authentication:!1}],orchestration:[{route:{name:"/api/bucket/:id/balance",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"balancing error",optional:!1},{type:"403",description:"already on new node or not premium",optional:!1},{type:"200",description:"succesfully balanced the bucket",optional:!1}],response:[{name:"migration",type:"object",description:"is equal true if succesfull",optional:!1,defaultvalue:null}],name:"selfSend",longname:"Orchestration.selfSend",scope:"route",params:[]}],tokens:[{route:{name:"/api/users/token/",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"200",description:"successfully retrieved",optional:!1}],response:[{name:".",type:"object",description:"array of tokens",optional:!1,defaultvalue:null}],name:"retrieve",longname:"Tokens.retrieve",scope:"route",params:[]},{route:{name:"/api/users/token/:id",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],params:[{name:":id",type:"string",description:"token id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"404",description:"token not found",optional:!1},{type:"200",description:"refresh token has been deleted and all access token that have been created with it",optional:!1}],response:[{name:".",type:"object",description:"array of tokens",optional:!1,defaultvalue:null}],name:"remove",longname:"Tokens.remove",scope:"route"},{route:{name:"/api/users/token/",type:"PUT"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"scope",type:"object",description:"a valid oauth scope",optional:!1,defaultvalue:null}],code:[{type:"409",description:"the otp is already enabled for the user, you can only delete it",optional:!1},{type:"200",description:"the otp can be registered for the account, return the full response",optional:!1}],response:[{name:".",type:"object",description:"generated token",optional:!1,defaultvalue:null}],name:"create",longname:"Tokens.create",scope:"route",params:[]}],user:{otp:[{route:{name:"/api/users/otp",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"409",description:"the otp is already enabled for the user, you can only delete it",optional:!1},{type:"200",description:"the otp can be registered for the account, return the full response",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null},{name:"key",type:"string",description:"otp secret key",optional:!1,defaultvalue:null},{name:"qrImage",type:"string",description:"url to the QrCode",optional:!1,defaultvalue:null}],name:"retrieve",longname:"User.otp.retrieve",scope:"route",params:[]},{route:{name:"/api/users/otp",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"otpKey",type:"string",description:"secret key used to generate OTP code",optional:!1,defaultvalue:null},{name:"otpToken",type:"string",description:"a currently valid OTP code generated with the otpKey",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"the code asked to add the OTP from user account is invalid",optional:!1},{type:"500",description:"error from database",optional:!1},{type:"200",description:"the otp has been registered for the user",optional:!1}],name:"enable",longname:"User.otp.enable",scope:"route",params:[]},{route:{name:"/api/users/otp",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],query:[{name:"otpToken",type:"string",description:"a currently valid OTP code",optional:!1,defaultvalue:null}],code:[{type:"400",description:"missing parameters",optional:!1},{type:"403",description:"the code asked to remove the OTP from user account is invalid",optional:!1},{type:"500",description:"error from database",optional:!1},{type:"200",description:"the otp has been deleted for the user",optional:!1}],name:"disable",longname:"User.otp.disable",scope:"route",params:[]}],providers:[{route:{name:"/api/users/integrations",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"200",description:"succesfully retrieved providers",optional:!1}],response:[{name:".",type:"array",description:"array of providers for user account",optional:!1,defaultvalue:null}],name:"retrieve",longname:"User.providers.retrieve",scope:"route",params:[]},{route:{name:"/api/users/integrations",type:"POST"},authentication:!0,body:[{name:"name",type:"string",description:"the provider name",optional:!1,defaultvalue:null}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid parameters",optional:!1},{type:"403",description:"the user already have this provider",optional:!1},{type:"200",description:"succesfully added the provider",optional:!1}],name:"add",longname:"User.providers.add",scope:"route",params:[]},{route:{name:"/api/users/integrations/:name",type:"DELETE"},authentication:!0,params:[{name:":name",type:"string",description:"the provider name",optional:!1}],header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"400",description:"invalid parameters or provider isn't implemented",optional:!1},{type:"403",description:"the provider isn't enabled",optional:!1},{type:"200",description:"succesfully removed the provider",optional:!1}],name:"remove",longname:"User.providers.remove",scope:"route"}],default:[{name:"retrieve",route:{name:"/api/users/isLogged",type:"GET"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"200",description:"the user has been retrieved",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null}],longname:"User.retrieve",scope:"route"},{route:{name:"/api/users/show/:id",type:"GET"},params:[{name:":id",type:"string",description:"user id",optional:!1}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"invalid parameters (no id provided)",optional:!1},{type:"404",description:"no user account where found",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],response:[{name:"String",type:"",description:"email user email",optional:!1,defaultvalue:null},{name:"String",type:"",description:"username user pseudo",optional:!1,defaultvalue:null}],name:"show",longname:"User.show",scope:"route",authentication:!1},{route:{name:"/api/users/update",type:"POST"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],body:[{name:"username",type:"string",description:"",optional:!0,defaultvalue:null},{name:"email",type:"string",description:"",optional:!0,defaultvalue:null},{name:"old_password",type:"string",description:"",optional:!0,defaultvalue:null},{name:"new_password",type:"string",description:"",optional:!0,defaultvalue:null},{name:"info",type:"object",description:"",optional:!0,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"400",description:"missing parameters, no data to update",optional:!1},{type:"403",description:"when updating the password, it need a new one",optional:!1},{type:"406",description:"when updating the password, the old one is false",optional:!1},{type:"409",description:"when updating email or username\n another user already have one of those two",optional:!1},{type:"200",description:"succesfully updated the card",optional:!1}],response:[{name:".",type:"object",description:"user object",optional:!1,defaultvalue:null}],name:"update",longname:"User.update",scope:"route",params:[]},{route:{name:"/api/users/delete",type:"DELETE"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"500",description:"database error",optional:!1},{type:"403",description:"permission denied (hold buckets)",optional:!1},{type:"200",description:"succesfully deleted the user",optional:!1}],response:[{name:".",type:"object",description:"user object",optional:!1,defaultvalue:null}],name:"delete",longname:"User.delete",scope:"route",params:[]}]},auth:[{name:"retrieveToken",route:{name:"/api/oauth/token",type:"POST"},service:{name:"OAUTH"},body:[{name:"client_id",type:"string",description:"the public id of your oauth application",optional:!1,defaultvalue:null},{name:"refresh_token",type:"string",description:"refresh token you retrieved via authorize endpoint",optional:!1,defaultvalue:null},{name:"grant_type",type:"string",description:"",optional:!1,defaultvalue:"refresh_token"}],code:[{type:"400",description:"invalid parameters (missing or not correct)",optional:!1}],response:[{name:"access_token",type:"string",description:"a fresh access_token",optional:!1,defaultvalue:null},{name:"refresh_token",type:"string",description:"the refresh token you used",optional:!1,defaultvalue:null},{name:"expire_at",type:"string",description:"UTC date at which the token will be considered\n as invalid",optional:!1,defaultvalue:null},{name:"token_type",type:"string",description:"the type of token to use, for now its always Bearer",optional:!1,defaultvalue:null}],longname:"Auth.retrieveToken",scope:"route",authentication:!1},{name:"requestNewPassword",route:{name:"/api/oauth/reset_password",type:"POST"},service:{name:"OAUTH"},body:[{name:"email",type:"string",description:"email of the account that want a password reset",optional:!1,defaultvalue:null}],code:[{type:"500",description:"the database failed to register the token to reset the mail",optional:!1},{type:"400",description:"missing parameters",optional:!1},{type:"404",description:"no user account where found with the provided email",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],longname:"Auth.requestNewPassword",scope:"route",authentication:!1},{name:"sendEmailLink",route:{name:"/api/oauth/send_email_link",type:"POST"},service:{name:"OAUTH"},code:[{type:"500",description:"the database failed to register the token to reset the mail",optional:!1},{type:"401",description:"need to authenticated",optional:!1},{type:"200",description:"the mail has been sent to the provided email",optional:!1}],longname:"Auth.sendEmailLink",scope:"route",authentication:!1},{name:"validEmail",route:{name:"/api/oauth/valid_email/:token",type:"GET"},params:[{description:"the token to validate the account",name:":token",optional:!1,type:null}],service:{name:"OAUTH"},code:[{type:"500",description:"the database failed to valid email",optional:!1},{type:"404",description:"need to authenticated",optional:!1},{type:"301",description:"the email has been valided",optional:!1}],longname:"Auth.validEmail",scope:"route",authentication:!1},{route:{name:"/api/oauth/register",type:"GET"},service:{name:"OAUTH"},body:[{name:"username",type:"string",description:"",optional:!1,defaultvalue:null},{name:"email",type:"string",description:"",optional:!1,defaultvalue:null},{name:"password",type:"string",description:"",optional:!1,defaultvalue:null},{name:"role",type:"string",description:"job title in user company",optional:!0,defaultvalue:null},{name:"company",type:"string",description:"company name",optional:!0,defaultvalue:null},{name:"accept_terms",type:"integer",description:"",optional:!1,defaultvalue:null}],code:[{type:"500",description:"either the registeration of new user is disabled or\nthe database failed to register the user",optional:!1},{type:"409",description:"the user field are already used by another user",optional:!1},{type:"200",description:"the user has been created",optional:!1}],response:[{name:"user",type:"object",description:"user model",optional:!1,defaultvalue:null},{name:"access_token",type:"object",description:"access token issued for the user",optional:!1,defaultvalue:null},{name:"refreshToken",type:"object",description:"refresh token issued for the user",optional:!1,defaultvalue:null}],name:"register",longname:"Auth.register",scope:"route",authentication:!1},{route:{name:"/api/oauth/revoke",type:"POST"},service:{name:"OAUTH"},authentication:!0,header:[{name:"Authorization",type:"string",description:"bearer access token issued for the user",optional:!1,defaultvalue:null}],code:[{type:"404",description:"token not found",optional:!1},{type:"500",description:"database error",optional:!1},{type:"200",description:"the token has been succesfully deleted,\n if there was access token generated with this token, they\n have been deleted too",optional:!1}],name:"revoke",longname:"Auth.revoke",scope:"route"}]}},{}],41:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}function r(e,t,n){return t=s(t),function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],s(e).constructor):t.apply(e,n))}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var u=e("./strategy");t.exports=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r(this,t,arguments)}var n,o,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,u),n=t,(o=[{key:"removeUrlToken",value:function(e){var t=window.location.href,n="?access_token=".concat(e,"&token_type=refresh_token"),o=t.replace(n,"");window.history.pushState("","",o)}},{key:"retrieveTokens",value:function(e,t){var n=this,o=function(t){return e.auth.retrieveToken({client_id:n.client_id,refresh_token:t})},i=new URL(window.location);this.response_mode="query"===this.response_mode?"search":this.response_mode;var a=new URLSearchParams(i[this.response_mode]);null!==a.get("access_token")?o(a.get("access_token")).then(function(e){n.removeUrlToken(e.data.refresh_token),localStorage.setItem("km_refresh_token",a.get("access_token"));var o=e.data;return t(null,o)}).catch(t):"undefined"!=typeof localStorage&&null!==localStorage.getItem("km_refresh_token")?o(localStorage.getItem("km_refresh_token")).then(function(e){n.removeUrlToken(e.data.refresh_token);var o=e.data;return t(null,o)}).catch(t):window.location="".concat(this.oauth_endpoint).concat(this.oauth_query,"&redirect_uri=").concat(window.location)}},{key:"deleteTokens",value:function(e){var t=this;return new Promise(function(n,o){return e.auth.revoke().then(function(e){return console.log("Token successfuly revoked!")}).catch(function(e){return console.error("Error when trying to revoke token: ".concat(e.message))}),localStorage.removeItem("km_refresh_token"),setTimeout(function(e){window.location="".concat(t.oauth_endpoint).concat(t.oauth_query)},500),n()})}}])&&i(n.prototype,o),a&&i(n,a),Object.defineProperty(n,"prototype",{writable:!1}),t}()},{"./strategy":43}],42:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}function r(e,t,n){return t=s(t),function(e,t){if(t&&("object"===o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,function(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return function(){return!!e}()}()?Reflect.construct(t,n||[],s(e).constructor):t.apply(e,n))}function s(e){return(s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function l(e,t){return(l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}var u=e("./strategy");t.exports=function(e){function t(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),r(this,t,arguments)}var n,o,a;return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(t,u),n=t,(o=[{key:"retrieveTokens",value:function(e,t){if(this._opts.refresh_token&&this._opts.access_token)return t(null,{access_token:this._opts.access_token,refresh_token:this._opts.refresh_token});if(!this._opts.refresh_token||!this._opts.client_id)throw new Error("If you want to use the standalone flow you need to provide either \n        a refresh and access token OR a refresh token and a client id");e.auth.retrieveToken({client_id:this._opts.client_id,refresh_token:this._opts.refresh_token}).then(function(e){var n=e.data;return t(null,n)}).catch(t)}},{key:"deleteTokens",value:function(e){return e.auth.revoke}}])&&i(n.prototype,o),a&&i(n,a),Object.defineProperty(n,"prototype",{writable:!1}),t}()},{"./strategy":43}],43:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}var r=e("../../constants.js"),s=function(){function t(e){if(function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this._opts=e,this.client_id=e.client_id||e.OAUTH_CLIENT_ID,!this.client_id)throw new Error("You must always provide a application id for any of the strategies");this.scope=e.scope||"all",this.response_mode=e.reponse_mode||"query";var n=null;e&&e.services&&(n=e.services.OAUTH||e.services.API);var o=r.services.OAUTH||r.services.API;this.oauth_endpoint="".concat(n||o),"/"===this.oauth_endpoint[this.oauth_endpoint.length-1]&&"/"===r.OAUTH_AUTHORIZE_ENDPOINT[0]&&(this.oauth_endpoint=this.oauth_endpoint.substr(0,this.oauth_endpoint.length-1)),this.oauth_endpoint+=r.OAUTH_AUTHORIZE_ENDPOINT,this.oauth_query="?client_id=".concat(e.client_id,"&response_mode=").concat(this.response_mode)+"&response_type=token&scope=".concat(this.scope)}var n,o,a;return n=t,a=[{key:"implementations",value:function(t){var n={embed:{nodule:e("./embed_strategy"),condition:"node"},browser:{nodule:e("./browser_strategy"),condition:"browser"},standalone:{nodule:e("./standalone_strategy"),condition:null}};return t?n[t]:null}}],(o=[{key:"retrieveTokens",value:function(){throw new Error("You need to implement a retrieveTokens function inside your strategy")}},{key:"deleteTokens",value:function(){throw new Error("You need to implement a deleteTokens function inside your strategy")}}])&&i(n.prototype,o),a&&i(n,a),Object.defineProperty(n,"prototype",{writable:!1}),t}();t.exports=s},{"../../constants.js":1,"./browser_strategy":41,"./embed_strategy":3,"./standalone_strategy":42}],44:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}var r=e("./utils/validator"),s=e("debug")("kmjs:endpoint");t.exports=function(){function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Object.assign(this,t)}var t,n,o;return t=e,(n=[{key:"build",value:function(e){var t=this;return function(){var n=arguments,o=(new Error).stack.split("\n")[2];return o&&o.length>0&&s("Call to '".concat(t.route.name,"' from ").concat(o.replace("    at ",""))),new Promise(function(o,i){r.extract(t,Array.prototype.slice.call(n)).then(function(n){if(t.service&&t.service.baseURL){var a=t.service.baseURL;a="/"===a[a.length-1]?a.substr(0,a.length-1):a,n.url=a+n.url}e.request(n).then(o,i)}).catch(i)})}}}])&&i(t.prototype,n),o&&i(t,o),Object.defineProperty(t,"prototype",{writable:!1}),e}()},{"./utils/validator":48,debug:4}],45:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}var r=e("./namespace"),s=e("../constants"),l=e("./network"),u=e("debug")("kmjs"),p=function(){function t(n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),u("init keymetrics instance"),this.opts=Object.assign(s,n),u("init network client (http/ws)"),this._network=new l(this,this.opts);var o=n&&n.mappings?n.mappings:e("./api_mappings.json");u("Using mappings provided in ".concat(n&&n.mappings?"options":"package")),u("building namespaces");var i=new r(o,{name:"root",http:this._network,services:this.opts.services});for(var a in u("exposing namespaces"),i)"name"!==a&&"opts"!==a&&(this[a]=i[a]);u("attached namespaces : ".concat(Object.keys(this))),this.realtime=this._network.realtime}var n,o,a;return n=t,(o=[{key:"use",value:function(e,t){var n=this;return u("using ".concat(e," authentication strategy")),this._network.useStrategy(e,t),this.auth.logout=function(){return n._network.oauth_flow.deleteTokens(n)},this}},{key:"apiDateLag",get:function(){return this._network.apiDateLag}}])&&i(n.prototype,o),a&&i(n,a),Object.defineProperty(n,"prototype",{writable:!1}),t}();t.exports=p},{"../constants":1,"./api_mappings.json":40,"./namespace":46,"./network":47,debug:4}],46:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}var r=e("./endpoint"),s=e("debug")("kmjs:namespace");t.exports=function(){function e(t,n){for(var i in function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),s("initialization namespace ".concat(n.name)),this.name=n.name,this.http=n.http,this.endpoints=[],this.namespaces=[],s("building namespace ".concat(n.name)),t){var a=t[i];if("object"!==o(t)||a.route)a.service&&n.services&&n.services[a.service.name]&&(a.service.baseURL=n.services[a.service.name]),this.addEndpoint(new r(a));else{if("default"===i){var l=new e(a,{name:i,http:this.http,services:n.services});for(var u in this.namespaces.push(l),l)"name"!==u&&"opts"!==u&&(this[u]=l[u]);continue}this.addNamespace(new e(a,{name:i,http:this.http,services:n.services}))}}this.namespaces.length>0&&s("namespace ".concat(this.name," contains namespaces : \n").concat(this.namespaces.map(function(e){return e.name}).join("\n"),"\n")),this.endpoints.length>0&&s("Namespace ".concat(this.name," contains endpoints : \n").concat(this.endpoints.map(function(e){return e.route.name}).join("\n"),"\n"))}var t,n,a;return t=e,(n=[{key:"addNamespace",value:function(t){if(!t||t.name===this.name)throw new Error("A namespace must not have the same name as the parent namespace");if(!(t instanceof e))throw new Error("addNamespace only accept Namespace instance");this.namespaces.push(t),this[t.name]=t}},{key:"addEndpoint",value:function(e){if(!e||e.name===this.name)throw new Error("A endpoint must not have the same name as a namespace");if(!(e instanceof r))throw new Error("addNamespace only accept Namespace instance");this.endpoints.push(e),this[e.name]=e.build(this.http)}}])&&i(t.prototype,n),a&&i(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}()},{"./endpoint":44,debug:4}],47:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,a(o.key),o)}}function a(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}var r=e("extrareqp2"),s=e("./auth_strategies/strategy"),l=e("../constants"),u=e("debug")("kmjs:network"),p=e("debug")("kmjs:network:http"),c=e("debug")("kmjs:network:ws"),d=e("./utils/websocket"),f=e("eventemitter2"),m=e("async");t.exports=function(){function e(t,n){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),u("init network manager"),n.baseURL=n.services.API,this.opts=n,this.opts.maxRedirects=0,this.tokens={refresh_token:null,access_token:null},this.km=t,this._queue=[],this._extrareqp2=r.create(n),this._websockets=[],this._endpoints=new Map,this._bucketFilters=new Map,this.apiDateLag=0,this.realtime=new f({wildcard:!0,delimiter:":",newListener:!1,maxListeners:100});var o=this,i=this.realtime.on;this.realtime.on=function(){return o.editSocketFilters("push",arguments[0]),i.apply(o.realtime,arguments)};var a=this.realtime.off;this.realtime.off=function(){return o.editSocketFilters("remove",arguments[0]),a.apply(o.realtime,arguments)},this.realtime.subscribe=this.subscribe.bind(this),this.realtime.unsubscribe=this.unsubscribe.bind(this),this.authenticated=!1,this._setupDateLag()}var t,n,a;return t=e,(n=[{key:"_setupDateLag",value:function(){var e=this,t=function(t){if(t&&t.headers&&t.headers.date){var n=new Date(t.headers.date),o=new Date;n.setMilliseconds(0),o.setMilliseconds(0),e.apiDateLag=n-o}};this._extrareqp2.interceptors.response.use(function(e){return t(e),e},function(e){return t(e.response),Promise.reject(e)})}},{key:"_queueUpdater",value:function(){if(!1!==this.authenticated)for(this._queue.length>0&&u("Emptying requests queue (size: ".concat(this._queue.length,")"));this._queue.length>0;){var e=this._queue.shift();this.request(e.request).then(e.resolve,e.reject)}}},{key:"_resolveBucketEndpoint",value:function(e){var t=this;if(!e)return Promise.reject(new Error("Missing argument : bucketID"));if(!this._endpoints.has(e)){var n=this._extrareqp2.request({url:"/api/bucket/".concat(e),method:"GET",headers:{Authorization:"Bearer ".concat(this.tokens.access_token)}}).then(function(e){return e.data.node.endpoints.web}).catch(function(n){throw t._endpoints.delete(e),n});this._endpoints.set(e,n)}return this._endpoints.get(e)}},{key:"request",value:function(e){var t=this;return new Promise(function(n,o){m.series([function(i){return!0===t.authenticated||!1===e.authentication?i():(p("Queued request to ".concat(e.url)),t._queue.push({resolve:n,reject:o,request:e}),i(-1))},function(n){if(!e.url.match(/bucket\/[0-9a-fA-F]{24}/))return n();var o=e.url.split("/")[3];t._resolveBucketEndpoint(o).then(function(t){return e.baseURL=t,n()}).catch(n)},function(n){var o=function(e){return n(null,e)};p("Making request to ".concat(e.url)),e.headers||(e.headers={}),e.headers.Authorization="Bearer ".concat(t.tokens.access_token),t._extrareqp2.request(e).then(o).catch(function(i){var a=i.response;if(a&&401!==a.status)return n(a);p("Got unautenticated response, buffering request from now ..."),t.authenticated=!1,p("Asking to the oauth flow to retrieve new tokens");var r=function(){t.oauth_flow.retrieveTokens(t.km,function(i,r){if(i)return p("Failed to retrieve new tokens : ".concat(i.message||i)),n(a);p("Succesfully retrieved new tokens"),t._updateTokens(null,r,function(i,r){return i?n(a):(p("Re-buffering call to ".concat(e.url," since authenticated now")),e.headers.Authorization="Bearer ".concat(t.tokens.access_token),t._extrareqp2.request(e).then(o).catch(n))})})};if(e.url==t.opts.services.OAUTH+"/api/oauth/token")return setTimeout(r.bind(t),500);r()})}],function(e,t){if(-1!==e)return e?o(e):n(t[2])})})}},{key:"_updateTokens",value:function(e,t,n){var o=this;if(e)return console.error("Error while retrieving tokens:",e),this.oauth_flow.deleteTokens(this.km),console.error(e.response?e.response.data:e.stack);if(!t||!t.access_token||!t.refresh_token)throw new Error("Invalid tokens");this.tokens=t,p("Registered new access_token : ".concat(t.access_token)),this._websockets.forEach(function(e){return e.updateAuthorization(t.access_token)}),this._extrareqp2.defaults.headers.common.Authorization="Bearer ".concat(t.access_token),this._extrareqp2.request({url:"/api/bucket",method:"GET",headers:{Authorization:"Bearer ".concat(t.access_token)}}).then(function(e){return p("Cached ".concat(e.data.length," buckets for current user")),o.authenticated=!0,o._queueUpdater(),"function"==typeof n?n(null,!0):null}).catch(function(e){return console.error("Error while retrieving buckets"),console.error(e.response?e.response.data:e),"function"==typeof n?n(e):null})}},{key:"useStrategy",value:function(e,t){if(t||(t={}),t.client_id||(t.client_id=this.opts.OAUTH_CLIENT_ID),"object"===o(e)){if(this.oauth_flow=e,!this.oauth_flow.retrieveTokens||!this.oauth_flow.deleteTokens)throw new Error("You must implement the Strategy interface to use it");return this.oauth_flow.retrieveTokens(this.km,this._updateTokens.bind(this))}if(void 0===s.implementations(e))throw new Error("The flow named ".concat(e," doesn't exist"));var n=s.implementations(e);if(n.condition&&l.ENVIRONNEMENT!==n.condition)throw new Error("The flow ".concat(e," is reserved for ").concat(n.condition," environment"));var i=n.nodule;return this.oauth_flow=new i(t),this.oauth_flow.retrieveTokens(this.km,this._updateTokens.bind(this))}},{key:"editSocketFilters",value:function(e,t){if(0===t.indexOf("**"))throw new Error("You need to provide a bucket public id.");var n=(t=t.split(":"))[0],o=t.slice(2).join(":"),i=this._websockets.find(function(e){return e.bucketPublic===n});this._bucketFilters.has(n)||this._bucketFilters.set(n,[]);var a=this._bucketFilters.get(n);"push"===e?a.push(o):a.splice(a.indexOf(o),1),i&&i.send(JSON.stringify({action:"sub",public_id:n,filters:Array.from(new Set(a))}))}},{key:"subscribe",value:function(e,t){var n=this;return new Promise(function(t,o){u("Request endpoints for ".concat(e)),n.km.bucket.retrieve(e).then(function(i){var a=i.data,r=!1,s=a.node.endpoints,l=s.realtime||s.web;l=l.replace("http","ws"),n.opts.IS_DEBUG&&(l=l.replace(":3000",":4020")),c("Found endpoint for ".concat(e," : ").concat(l));var p=new d("".concat(l,"/primus"),n.tokens.access_token);p.bucketPublic=a.public_id,p.connected=!1,p.bucket=e;var f=null;p.onmaxreconnect=function(e){if(!r)return r=!0,o(new Error("Connection timeout"))},p.onopen=function(){if(u("Connected to ws endpoint : ".concat(l," (bucket: ").concat(e,")")),p.connected=!0,n.realtime.emit("".concat(a.public_id,":connected")),p.send(JSON.stringify({action:"sub",public_id:a.public_id,filters:Array.from(new Set(n._bucketFilters.get(a.public_id)))})),null!==f&&(clearInterval(f),f=null),f=setInterval(function(){p.ping()}.bind(n),5e3),!r)return r=!0,t(p)},p.onunexpectedresponse=function(e,t){return 401===t.statusCode?n.oauth_flow.retrieveTokens(n.km,function(e,t){if(e)return u("Failed to retrieve tokens for ws: ".concat(e.message));u("Succesfully retrieved new tokens for ws"),n._updateTokens(null,t,function(e,t){return e?u("Failed to update tokens for ws: ".concat(e.message)):p._tryReconnect()})}):p._tryReconnect()},p.onerror=function(t){c("Error on ".concat(l," (bucket: ").concat(e,")")),c(t),n.realtime.emit("".concat(a.public_id,":error"),t)},p.onclose=function(){u("Closing ws connection ".concat(l," (bucket: ").concat(e,")")),p.connected=!1,n.realtime.emit("".concat(a.public_id,":disconnected")),null!==f&&(clearInterval(f),f=null)},p.onmessage=function(t){c("Received message for bucket ".concat(e," (").concat((t.data.length/1e3).toFixed(1)," Kb)"));var o=null;try{o=JSON.parse(t.data)}catch(t){return c("Receive not json message for bucket ".concat(e))}var i=o.data[1];Object.keys(i).forEach(function(e){"server_name"!==e&&n.realtime.emit("".concat(a.public_id,":").concat(i.server_name||"none",":").concat(e),i[e])})},n._websockets.push(p)}).catch(o)})}},{key:"unsubscribe",value:function(e,t){var n=this;return new Promise(function(t,o){u("Unsubscribe from realtime for ".concat(e));var i=n._websockets.find(function(t){return t.bucket===e});return i?(i.close(1e3,"Disconnecting"),u("Succesfully unsubscribed from realtime for ".concat(e)),t()):o(new Error("Realtime wasn't connected to ".concat(e)))})}}])&&i(t.prototype,n),a&&i(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}()},{"../constants":1,"./auth_strategies/strategy":43,"./utils/websocket":49,async:2,debug:4,eventemitter2:6,extrareqp2:7}],48:[function(e,t,n){"use strict";function o(e){"@babel/helpers - typeof";return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var r,s=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){l=!0,r=e},f:function(){try{s||null==n.return||n.return()}finally{if(l)throw r}}}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=new Array(t);n<t;n++)o[n]=e[n];return o}function r(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,s(o.key),o)}}function s(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=o(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:String(t)}t.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n,a;return t=e,a=[{key:"extract",value:function(e,t){var n=function(e){return null!==e&&void 0!==e};return new Promise(function(a,r){var s={params:{},data:{},url:e.route.name+"",method:e.route.type,authentication:e.authentication||!1};switch(e.route.type){case"GET":var l,u=i(e.params||[]);try{for(u.s();!(l=u.n()).done;){var p=l.value,c=t.shift();if("string"!=typeof c&&!1===p.optional)return r(new Error("Expected to receive string argument for ".concat(p.name," to match but got ").concat(c)));c?s.url=s.url.replace(p.name,c):!1===p.optional&&null!==p.defaultvalue&&(s.url=s.url.replace(p.name,p.defaultvalue))}}catch(e){u.e(e)}finally{u.f()}var d,f=i(e.query||[]);try{for(f.s();!(d=f.n()).done;){var m=d.value,y=t.shift();if("string"!=typeof y&&!1===m.optional)return r(new Error("Expected to receive string argument for ".concat(m.name," query but got ").concat(y)));y?s.params[m.name]=y:!1===m.optional&&null!==m.defaultvalue&&(s.params[m.name]=m.defaultvalue)}}catch(e){f.e(e)}finally{f.f()}break;case"PUT":case"POST":case"PATCH":var h,v=i(e.params||[]);try{for(v.s();!(h=v.n()).done;){var b=h.value,g=t.shift();if("string"!=typeof g&&!1===b.optional)return r(new Error("Expected to receive string argument for ".concat(b.name," to match but got ").concat(g)));g?s.url=s.url.replace(b.name,g):!1===b.optional&&null!==b.defaultvalue&&(s.url=s.url.replace(b.name,b.defaultvalue))}}catch(e){v.e(e)}finally{v.f()}var k,w=i(e.query||[]);try{for(w.s();!(k=w.n()).done;){var _=k.value,T=t.shift();if("string"!=typeof T&&!1===_.optional)return r(new Error("Expected to receive string argument for ".concat(_.name," query but got ").concat(T)));T?s.params[_.name]=T:!1===_.optional&&null!==_.defaultvalue&&(s.params[_.name]=_.defaultvalue)}}catch(e){w.e(e)}finally{w.f()}if(0===t.length)break;var j=t[0];if("object"!==o(j)&&e.body.length>0)return r(new Error("Expected to receive an object for post data but received ".concat(o(j))));var E,A=i(e.body||[]);try{for(A.s();!(E=A.n()).done;){var S=E.value,O=!0===S.name.includes("[]");if(!n(j[S.name])&&!1===O&&!1===S.optional&&null===S.defaultvalue)return r(new Error("Missing mandatory field ".concat(S.name," to make a POST request on ").concat(e.route.name)));if(o(j[S.name])!==S.type&&!1===O&&!1===S.optional&&null===S.defaultvalue)return r(new Error("Invalid type for field ".concat(S.name,", expected ").concat(S.type," but got ").concat(o(j[S.name]))));n(j[S.name])&&(s.data[S.name]=j[S.name]),!1===S.optional&&null!==S.defaultvalue&&(s.data[S.name]=S.defaultvalue)}}catch(e){A.e(e)}finally{A.f()}break;case"DELETE":var x,C=i(e.params||[]);try{for(C.s();!(x=C.n()).done;){var P=x.value,L=t.shift();if("string"!=typeof L&&!1===P.optional)return r(new Error("Expected to receive string argument for ".concat(P.name," to match but got ").concat(L)));L?s.url=s.url.replace(P.name,L):!1===P.optional&&null!==P.defaultvalue&&(s.url=s.url.replace(P.name,P.defaultvalue))}}catch(e){C.e(e)}finally{C.f()}var z,B=i(e.query||[]);try{for(B.s();!(z=B.n()).done;){var D=z.value,F=t.shift();if("string"!=typeof F&&!1===D.optional)return r(new Error("Expected to receive string argument for ".concat(D.name," query but got ").concat(F)));F?s.params[D.name]=F:!1===D.optional&&null!==D.defaultvalue&&(s.params[D.name]=D.defaultvalue)}}catch(e){B.e(e)}finally{B.f()}break;default:return r(new Error("Invalid endpoint declaration, invalid method ".concat(e.route.type," found")))}return a(s)})}}],(n=null)&&r(t.prototype,n),a&&r(t,a),Object.defineProperty(t,"prototype",{writable:!1}),e}()},{}],49:[function(e,t,n){"use strict";var o=e("ws"),i=e("debug")("kmjs:network:_ws"),a="function"!=typeof o?WebSocket:o,r={debug:!1,automaticOpen:!0,reconnectOnError:!0,reconnectInterval:1e3,maxReconnectInterval:1e4,reconnectDecay:1,timeoutInterval:2e3,maxReconnectAttempts:1/0,randomRatio:3,reconnectOnCleanClose:!1},s=function(e,t,n,o){n||(n=[]),o||(o=[]),this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this._url=e,this._token=t,this._protocols=n,this._options=Object.assign({},r,o),this._messageQueue=[],this._reconnectAttempts=0,this.readyState=this.CONNECTING,"function"==typeof this._options.debug?this._debug=this._options.debug:this._options.debug?this._debug=console.log.bind(console):this._debug=function(){},this._options.automaticOpen&&this.open()};s.prototype.updateAuthorization=function(e){this._token=e},s.prototype.open=function(){i("open");var e=this._socket=new a("".concat(this._url,"?token=").concat(this._token),this._protocols);if(this._options.binaryType&&(e.binaryType=this._options.binaryType),this._options.maxReconnectAttempts&&this._options.maxReconnectAttempts<this._reconnectAttempts)return this.onmaxreconnect();this._syncState(),e.on&&e.on("unexpected-response",this._onunexpectedresponse),e.onmessage=this._onmessage.bind(this),e.onopen=this._onopen.bind(this),e.onclose=this._onclose.bind(this),e.onerror=this._onerror.bind(this)},s.prototype.send=function(e){i("send"),this._socket&&this._socket.readyState===a.OPEN&&0===this._messageQueue.length?this._socket.send(e):this._messageQueue.push(e)},s.prototype.ping=function(){i("ping"),this._socket.ping&&this._socket&&this._socket.readyState===a.OPEN&&0===this._messageQueue.length&&this._socket.ping()},s.prototype.close=function(e,t){i("close"),void 0===e&&(e=1e3),this._socket&&this._socket.close(e,t)},s.prototype._onunexpectedresponse=function(e,t){i("unexpected-response"),this.onunexpectedresponse&&this.onunexpectedresponse(e,t)},s.prototype._onmessage=function(e){i("onmessage"),this.onmessage&&this.onmessage(e)},s.prototype._onopen=function(e){i("onopen"),this._syncState(),this._flushQueue(),0!==this._reconnectAttempts&&this.onreconnect&&this.onreconnect(),this._reconnectAttempts=0,this.onopen&&this.onopen(e)},s.prototype._onclose=function(e){i("onclose",e),this._syncState(),this._debug("WebSocket: connection is broken",e),this.onclose&&this.onclose(e),this._tryReconnect(e)},s.prototype._onerror=function(e){i("onerror",e),this._socket.close(),this._syncState(),this._debug("WebSocket: error",e),this.onerror&&this.onerror(e),this._options.reconnectOnError&&this._tryReconnect(e)},s.prototype._tryReconnect=function(e){var t=this;i("Trying to reconnect"),e.wasClean&&!this._options.reconnectOnCleanClose||setTimeout(function(){t.readyState!==t.CLOSING&&t.readyState!==t.CLOSED||(t._reconnectAttempts++,t.open())},this._getTimeout())},s.prototype._flushQueue=function(){for(;0!==this._messageQueue.length;){var e=this._messageQueue.shift();this._socket.send(e)}},s.prototype._getTimeout=function(){var e,t,n=this._options.reconnectInterval*Math.pow(this._options.reconnectDecay,this._reconnectAttempts);return n=n>this._options.maxReconnectInterval?this._options.maxReconnectInterval:n,this._options.randomRatio?(e=n/this._options.randomRatio,t=n,Math.random()*(t-e)+e):n},s.prototype._syncState=function(){this.readyState=this._socket.readyState},t.exports=s},{debug:4,ws:3}],"/":[function(e,t,n){"use strict";t.exports=e("./src/keymetrics.js")},{"./src/keymetrics.js":45}]},{},[])("/")});
