# cf

```js
// usage
chance.cf()
```
Generate a random Italian social security number (Codice Fiscale).

```js
chance.cf();
=> '****************'
```

Optionally specify any or all components: first name, last name, gender, birth date, place of birth (using ISTAT geocodes for Italian cities).

```js
chance.cf({first: 'Sergio', last: 'Leone'});
=> '****************'

chance.cf({first: '<PERSON>', last: '<PERSON><PERSON>', gender: 'Female', birthday: new Date(1934,8,20), city: 'h501'});

=> '****************'
```
