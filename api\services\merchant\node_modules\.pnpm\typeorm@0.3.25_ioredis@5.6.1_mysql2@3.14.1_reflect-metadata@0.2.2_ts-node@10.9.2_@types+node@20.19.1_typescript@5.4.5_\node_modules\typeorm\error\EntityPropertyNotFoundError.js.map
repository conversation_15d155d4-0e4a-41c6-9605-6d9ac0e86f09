{"version": 3, "sources": ["../../src/error/EntityPropertyNotFoundError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAG7C;;GAEG;AACH,MAAa,2BAA4B,SAAQ,2BAAY;IACzD,YAAY,YAAoB,EAAE,QAAwB;QACtD,KAAK,CAAC,YAAY,CAAC,CAAA;QACnB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,2BAA2B,CAAC,SAAS,CAAC,CAAA;QAClE,IAAI,CAAC,OAAO,GAAG,aAAa,YAAY,uBAAuB,QAAQ,CAAC,UAAU,qCAAqC,CAAA;IAC3H,CAAC;CACJ;AAND,kEAMC", "file": "EntityPropertyNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\n\n/**\n * Thrown when specified entity property was not found.\n */\nexport class EntityPropertyNotFoundError extends TypeORMError {\n    constructor(propertyPath: string, metadata: EntityMetadata) {\n        super(propertyPath)\n        Object.setPrototypeOf(this, EntityPropertyNotFoundError.prototype)\n        this.message = `Property \"${propertyPath}\" was not found in \"${metadata.targetName}\". Make sure your query is correct.`\n    }\n}\n"], "sourceRoot": ".."}