import { IMidwayApplication, IMidwayContainer, IMidwayContext, ISimulation } from '../interface';
export declare class MidwayMockService {
    readonly applicationContext: IMidwayContainer;
    protected mocks: any[];
    protected contextMocks: Array<{
        app: IMidwayApplication;
        key: string | ((ctx: IMidwayContext) => void);
        value: any;
    }>;
    protected cache: Map<any, any>;
    protected simulatorList: Array<ISimulation>;
    constructor(applicationContext: IMidwayContainer);
    init(): Promise<void>;
    static prepareMocks: any[];
    static mockClassProperty(clzz: new (...args: any[]) => any, propertyName: string, value: any): void;
    static mockProperty(obj: new (...args: any[]) => any, key: string, value: any): void;
    mockClassProperty(clzz: new (...args: any[]) => any, propertyName: string, value: any): void;
    mockProperty(obj: any, key: string, value: any): void;
    mockContext(app: IMidwayApplication, key: string | ((ctx: IMidwayContext) => void), value?: PropertyDescriptor | any): void;
    restore(): void;
    isMocked(obj: any, key: any): any;
    applyContextMocks(app: IMidwayApplication, ctx: IMidwayContext): void;
    getContextMocksSize(): number;
    private overridePropertyDescriptor;
    initSimulation(): Promise<void>;
    runSimulatorSetup(): Promise<void>;
    runSimulatorTearDown(): Promise<void>;
    runSimulatorAppSetup(app: IMidwayApplication): Promise<void>;
    runSimulatorAppTearDown(app: IMidwayApplication): Promise<void>;
    runSimulatorContextSetup(ctx: IMidwayContext, app: IMidwayApplication): Promise<void>;
    runSimulatorContextTearDown(ctx: IMidwayContext, app: IMidwayApplication): Promise<void>;
}
//# sourceMappingURL=mockService.d.ts.map