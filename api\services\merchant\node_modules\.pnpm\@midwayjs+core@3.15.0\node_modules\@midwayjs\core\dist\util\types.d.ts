export declare function isString(value: any): boolean;
export declare function isClass(fn: any): boolean;
export declare function isAsyncFunction(value: any): boolean;
export declare function isGeneratorFunction(value: any): boolean;
export declare function isPromise(value: any): boolean;
export declare function isFunction(value: any): boolean;
export declare function isObject(value: any): boolean;
export declare function isPlainObject(obj: any): any;
export declare function isNumber(value: any): boolean;
export declare function isProxy(value: any): boolean;
export declare function isMap(value: any): boolean;
export declare function isSet(value: any): boolean;
export declare function isRegExp(value: any): boolean;
export declare function isUndefined(value: any): boolean;
export declare function isNull(value: any): boolean;
export declare function isNullOrUndefined(value: any): boolean;
export declare const Types: {
    isClass: typeof isClass;
    isAsyncFunction: typeof isAsyncFunction;
    isGeneratorFunction: typeof isGeneratorFunction;
    isString: typeof isString;
    isPromise: typeof isPromise;
    isFunction: typeof isFunction;
    isObject: typeof isObject;
    isPlainObject: typeof isPlainObject;
    isNumber: typeof isNumber;
    isProxy: typeof isProxy;
    isMap: typeof isMap;
    isSet: typeof isSet;
    isRegExp: typeof isRegExp;
    isUndefined: typeof isUndefined;
    isNull: typeof isNull;
    isNullOrUndefined: typeof isNullOrUndefined;
};
//# sourceMappingURL=types.d.ts.map