<template>
  <div class="risk-alert-page">
    <!-- 筛选区域 -->
    <div class="custom-card art-custom-card filter-card">
      <div class="custom-card-content">
        <el-form :model="filters" :inline="true">
          <el-form-item label="风险等级">
            <el-select
              v-model="filters.riskLevel"
              placeholder="选择风险等级"
              clearable
              style="width: 140px"
              @change="handleSearch"
            >
              <el-option label="全部" value="" />
              <el-option label="高风险" value="high" />
              <el-option label="中风险" value="medium" />
              <el-option label="低风险" value="low" />
            </el-select>
          </el-form-item>

          <el-form-item label="预警类型">
            <el-select
              v-model="filters.alertType"
              placeholder="选择预警类型"
              clearable
              style="width: 140px"
              @change="handleSearch"
            >
              <el-option label="全部" value="" />
              <el-option label="交易异常" value="transaction" />
              <el-option label="退款异常" value="refund" />
              <el-option label="合规风险" value="compliance" />
            </el-select>
          </el-form-item>

          <el-form-item label="商户名称">
            <el-input
              v-model="filters.merchantName"
              placeholder="搜索商户名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <el-table
          :data="dataSource"
          v-loading="loading"
          row-key="id"
        >
          <el-table-column prop="merchantName" label="商户名称" min-width="150" />
          <el-table-column prop="alertType" label="预警类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getAlertTypeTag(row.alertType)">
                {{ getAlertTypeName(row.alertType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="riskLevel" label="风险等级" width="100">
            <template #default="{ row }">
              <el-tag :type="getRiskLevelTag(row.riskLevel)">
                {{ getRiskLevelName(row.riskLevel) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="预警描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="createTime" label="预警时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleProcess(row)">
                处理
              </el-button>
              <el-button type="info" size="small" @click="viewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchData"
            @current-change="fetchData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 筛选条件
const filters = reactive({
  riskLevel: '',
  alertType: '',
  merchantName: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 数据源
const dataSource = ref([
  {
    id: 1,
    merchantName: '张三手工艺品店',
    alertType: 'transaction',
    riskLevel: 'high',
    description: '24小时内交易金额异常增长300%，疑似刷单行为',
    createTime: '2025-01-18 14:30:00'
  },
  {
    id: 2,
    merchantName: '李四非遗工坊',
    alertType: 'refund',
    riskLevel: 'medium',
    description: '退款率超过平台平均水平50%，客户投诉较多',
    createTime: '2025-01-18 13:45:00'
  },
  {
    id: 3,
    merchantName: '王五文创店',
    alertType: 'compliance',
    riskLevel: 'low',
    description: '商品描述涉嫌违规内容，需要审核',
    createTime: '2025-01-18 12:20:00'
  }
])

const loading = ref(false)

// 获取预警类型标签和名称
const getAlertTypeTag = (type: string) => {
  const tags = { transaction: 'danger', refund: 'warning', compliance: 'info' }
  return tags[type] || 'info'
}

const getAlertTypeName = (type: string) => {
  const names = { transaction: '交易异常', refund: '退款异常', compliance: '合规风险' }
  return names[type] || '未知'
}

// 获取风险等级标签和名称
const getRiskLevelTag = (level: string) => {
  const tags = { high: 'danger', medium: 'warning', low: 'success' }
  return tags[level] || 'info'
}

const getRiskLevelName = (level: string) => {
  const names = { high: '高风险', medium: '中风险', low: '低风险' }
  return names[level] || '未知'
}

// 格式化时间
const formatTime = (time: string) => {
  return time
}

// 查询数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    pagination.total = 50
    loading.value = false
  }, 1000)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    riskLevel: '',
    alertType: '',
    merchantName: ''
  })
  handleSearch()
}

// 处理预警
const handleProcess = (row: any) => {
  ElMessage.info(`处理预警：${row.merchantName}`)
}

// 查看详情
const viewDetail = (row: any) => {
  ElMessage.info(`查看详情：${row.merchantName}`)
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
.risk-alert-page {
  padding-bottom: 20px;

  // 使用系统统一的卡片样式
  :deep(.custom-card) {
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 4px);
    box-shadow: none;
    margin-bottom: 20px;
  }

  // 卡片内容样式
  .custom-card-content {
    padding: 20px;
  }

  // 筛选卡片
  .filter-card {
    .custom-card-content {
      padding: 16px 20px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
