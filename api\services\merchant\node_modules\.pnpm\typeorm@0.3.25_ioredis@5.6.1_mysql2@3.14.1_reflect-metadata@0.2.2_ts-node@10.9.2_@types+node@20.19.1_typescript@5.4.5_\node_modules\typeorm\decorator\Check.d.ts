/**
 * Creates a database check.
 * Can be used on entity property or on entity.
 * Can create checks with composite columns when used on entity.
 */
export declare function Check(expression: string): ClassDecorator & PropertyDecorator;
/**
 * Creates a database check.
 * Can be used on entity property or on entity.
 * Can create checks with composite columns when used on entity.
 */
export declare function Check(name: string, expression: string): ClassDecorator & PropertyDecorator;
