/// <reference types="node" />
import { IFileDetector, IIdentifierRelationShip, IMidwayContainer, IModuleStore, IObjectDefinition, IObjectDefinitionRegistry, ObjectContext, ObjectIdentifier, ScopeEnum } from '../interface';
import { ManagedResolverFactory } from './managedResolverFactory';
import * as EventEmitter from 'events';
export declare class MidwayContainer implements IMidwayContainer, IModuleStore {
    private _resolverFactory;
    private _registry;
    private _identifierMapping;
    private moduleMap;
    private _objectCreateEventTarget;
    parent: IMidwayContainer;
    protected ctx: {
        _MAIN_CTX_: boolean;
    };
    private fileDetector;
    private attrMap;
    private _namespaceSet;
    constructor(parent?: IMidwayContainer);
    protected init(): void;
    get objectCreateEventTarget(): EventEmitter;
    get registry(): IObjectDefinitionRegistry;
    set registry(registry: IObjectDefinitionRegistry);
    get managedResolverFactory(): ManagedResolverFactory;
    get identifierMapping(): IIdentifierRelationShip;
    get namespaceSet(): Set<string>;
    load(module: any): void;
    protected loadDefinitions(): void | Promise<void>;
    bindClass(exports: any, options?: Partial<IObjectDefinition>): void;
    bind<T>(target: T, options?: Partial<IObjectDefinition>): void;
    bind<T>(identifier: ObjectIdentifier, target: T, options?: Partial<IObjectDefinition>): void;
    protected bindModule(module: any, options: Partial<IObjectDefinition>): void;
    setFileDetector(fileDetector: IFileDetector): void;
    createChild(): IMidwayContainer;
    setAttr(key: string, value: any): void;
    getAttr<T>(key: string): T;
    protected getIdentifier(target: any): string;
    protected getManagedResolverFactory(): ManagedResolverFactory;
    stop(): Promise<void>;
    ready(): void | Promise<void>;
    get<T>(identifier: {
        new (...args: any[]): T;
    }, args?: any[], objectContext?: ObjectContext): T;
    get<T>(identifier: ObjectIdentifier, args?: any[], objectContext?: ObjectContext): T;
    getAsync<T>(identifier: {
        new (...args: any[]): T;
    }, args?: any[], objectContext?: ObjectContext): Promise<T>;
    getAsync<T>(identifier: ObjectIdentifier, args?: any[], objectContext?: ObjectContext): Promise<T>;
    /**
     * proxy registry.registerObject
     * @param {ObjectIdentifier} identifier
     * @param target
     */
    registerObject(identifier: ObjectIdentifier, target: any): void;
    onBeforeBind(fn: (Clzz: any, options: {
        context: IMidwayContainer;
        definition: IObjectDefinition;
        replaceCallback: (newDefinition: IObjectDefinition) => void;
    }) => void): void;
    onBeforeObjectCreated(fn: (Clzz: any, options: {
        context: IMidwayContainer;
        definition: IObjectDefinition;
        constructorArgs: any[];
    }) => void): void;
    onObjectCreated<T>(fn: (ins: T, options: {
        context: IMidwayContainer;
        definition: IObjectDefinition;
        replaceCallback: (ins: T) => void;
    }) => void): void;
    onObjectInit<T>(fn: (ins: T, options: {
        context: IMidwayContainer;
        definition: IObjectDefinition;
    }) => void): void;
    onBeforeObjectDestroy<T>(fn: (ins: T, options: {
        context: IMidwayContainer;
        definition: IObjectDefinition;
    }) => void): void;
    saveModule(key: any, module: any): void;
    listModule(key: string): unknown[];
    transformModule(moduleMap: Map<string, Set<any>>): void;
    hasNamespace(ns: string): boolean;
    getNamespaceList(): string[];
    hasDefinition(identifier: ObjectIdentifier): boolean;
    hasObject(identifier: ObjectIdentifier): boolean;
    getInstanceScope(instance: any): ScopeEnum | undefined;
}
//# sourceMappingURL=container.d.ts.map