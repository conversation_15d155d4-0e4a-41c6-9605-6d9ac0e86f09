"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayValidationError = void 0;
const core_1 = require("@midwayjs/core");
const ValidateErrorCode = (0, core_1.registerErrorCode)('validate', {
    VALIDATE_FAIL: 10000,
});
class MidwayValidationError extends core_1.MidwayHttpError {
    constructor(message, status, cause) {
        super(message, status, ValidateErrorCode.VALIDATE_FAIL, {
            cause,
        });
    }
}
exports.MidwayValidationError = MidwayValidationError;
//# sourceMappingURL=error.js.map