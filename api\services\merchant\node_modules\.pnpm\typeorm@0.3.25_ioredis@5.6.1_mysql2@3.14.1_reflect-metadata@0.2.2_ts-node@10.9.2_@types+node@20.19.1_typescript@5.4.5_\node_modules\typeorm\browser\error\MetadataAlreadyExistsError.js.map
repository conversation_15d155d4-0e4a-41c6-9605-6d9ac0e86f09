{"version": 3, "sources": ["../browser/src/error/MetadataAlreadyExistsError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,0BAA2B,SAAQ,YAAY;IACxD,YACI,YAAoB,EACpB,WAAqB,EACrB,YAAqB;QAErB,KAAK,CACD,YAAY;YACR,qDAAqD;YACrD,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;YAC3B,CAAC,YAAY;gBACT,CAAC,CAAC,eAAe,GAAG,YAAY;gBAChC,CAAC,CAAC,8DAA8D;oBAC9D,iGAAiG,CAAC,CAC/G,CAAA;IACL,CAAC;CACJ", "file": "MetadataAlreadyExistsError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class MetadataAlreadyExistsError extends TypeORMError {\n    constructor(\n        metadataType: string,\n        constructor: Function,\n        propertyName?: string,\n    ) {\n        super(\n            metadataType +\n                \" metadata already exists for the class constructor \" +\n                JSON.stringify(constructor) +\n                (propertyName\n                    ? \" on property \" + propertyName\n                    : \". If you previously renamed or moved entity class, make sure\" +\n                      \" that compiled version of old entity class source wasn't left in the compiler output directory.\"),\n        )\n    }\n}\n"], "sourceRoot": ".."}