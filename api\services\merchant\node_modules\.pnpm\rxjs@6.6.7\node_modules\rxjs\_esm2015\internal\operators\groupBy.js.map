{"version": 3, "file": "groupBy.js", "sources": ["../../../src/internal/operators/groupBy.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAoGrC,MAAM,UAAU,OAAO,CAAU,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;IACjE,OAAO,CAAC,MAAqB,EAAE,EAAE,CAC/B,MAAM,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC,CAAC;AACtG,CAAC;AASD,MAAM,eAAe;IACnB,YAAoB,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QAHlC,gBAAW,GAAX,WAAW,CAAiB;QAC5B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,oBAAe,GAAf,eAAe,CAAmB;IACtD,CAAC;IAED,IAAI,CAAC,UAA+C,EAAE,MAAW;QAC/D,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,iBAAiB,CAC3C,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,eAAe,CAChG,CAAC,CAAC;IACL,CAAC;CACF;AAOD,MAAM,iBAA2B,SAAQ,UAAa;IAKpD,YAAY,WAAgD,EACxC,WAA4B,EAC5B,eAA0C,EAC1C,gBAAwE,EACxE,eAAkC;QACpD,KAAK,CAAC,WAAW,CAAC,CAAC;QAJD,gBAAW,GAAX,WAAW,CAAiB;QAC5B,oBAAe,GAAf,eAAe,CAA2B;QAC1C,qBAAgB,GAAhB,gBAAgB,CAAwD;QACxE,oBAAe,GAAf,eAAe,CAAmB;QAR9C,WAAM,GAA2B,IAAI,CAAC;QACvC,2BAAsB,GAAY,KAAK,CAAC;QACxC,UAAK,GAAW,CAAC,CAAC;IAQzB,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,GAAM,CAAC;QACX,IAAI;YACF,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChB,OAAO;SACR;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC1B,CAAC;IAEO,MAAM,CAAC,KAAQ,EAAE,GAAM;QAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAEzB,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,GAAG,EAAqB,CAAC;SACrD;QAED,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE5B,IAAI,OAAU,CAAC;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI;gBACF,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;aACvC;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aACjB;SACF;aAAM;YACL,OAAO,GAAQ,KAAK,CAAC;SACtB;QAED,IAAI,CAAC,KAAK,EAAE;YACV,KAAK,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,OAAO,EAAK,CAAmB,CAAC;YAC7F,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;YACvB,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;YAClE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YACzC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,IAAI,QAAa,CAAC;gBAClB,IAAI;oBACF,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,iBAAiB,CAAO,GAAG,EAAc,KAAK,CAAC,CAAC,CAAC;iBACvF;gBAAC,OAAO,GAAG,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBAChB,OAAO;iBACR;gBACD,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;aAC7E;SACF;QAED,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACrB;IACH,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC5B,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC9B,CAAC;IAES,SAAS;QACjB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC5B,KAAK,CAAC,QAAQ,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,EAAE,CAAC;SAChB;QACD,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC;IAED,WAAW,CAAC,GAAM;QAChB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,WAAW;QACT,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;YACnC,IAAI,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE;gBACpB,KAAK,CAAC,WAAW,EAAE,CAAC;aACrB;SACF;IACH,CAAC;CACF;AAOD,MAAM,uBAA8B,SAAQ,UAAa;IACvD,YAAoB,GAAM,EACN,KAAiB,EACjB,MAA0C;QAC5D,KAAK,CAAC,KAAK,CAAC,CAAC;QAHK,QAAG,GAAH,GAAG,CAAG;QACN,UAAK,GAAL,KAAK,CAAY;QACjB,WAAM,GAAN,MAAM,CAAoC;IAE9D,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAGD,YAAY;QACV,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAC9B,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SACzB;IACH,CAAC;CACF;AAUD,MAAM,OAAO,iBAAwB,SAAQ,UAAa;IAExD,YAAmB,GAAM,EACL,YAAwB,EACxB,oBAA2C;QAC7D,KAAK,EAAE,CAAC;QAHS,QAAG,GAAH,GAAG,CAAG;QACL,iBAAY,GAAZ,YAAY,CAAY;QACxB,yBAAoB,GAApB,oBAAoB,CAAuB;IAE/D,CAAC;IAGD,UAAU,CAAC,UAAyB;QAClC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACxC,MAAM,EAAE,oBAAoB,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;QACpD,IAAI,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE;YACxD,YAAY,CAAC,GAAG,CAAC,IAAI,yBAAyB,CAAC,oBAAoB,CAAC,CAAC,CAAC;SACvE;QACD,YAAY,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;QACrD,OAAO,YAAY,CAAC;IACtB,CAAC;CACF;AAOD,MAAM,yBAA0B,SAAQ,YAAY;IAClD,YAAoB,MAA4B;QAC9C,KAAK,EAAE,CAAC;QADU,WAAM,GAAN,MAAM,CAAsB;QAE9C,MAAM,CAAC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,WAAW;QACT,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAClC,KAAK,CAAC,WAAW,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC;YAClB,IAAI,MAAM,CAAC,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,sBAAsB,EAAE;gBACvD,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;SACF;IACH,CAAC;CACF"}