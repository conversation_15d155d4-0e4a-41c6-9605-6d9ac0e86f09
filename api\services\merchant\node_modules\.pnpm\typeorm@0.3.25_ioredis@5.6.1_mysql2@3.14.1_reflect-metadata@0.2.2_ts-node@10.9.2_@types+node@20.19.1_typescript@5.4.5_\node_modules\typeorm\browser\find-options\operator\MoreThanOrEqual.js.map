{"version": 3, "sources": ["../browser/src/find-options/operator/MoreThanOrEqual.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAI,KAA0B;IACzD,OAAO,IAAI,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;AACrD,CAAC", "file": "MoreThanOrEqual.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: MoreThanOrEqual(10) }\n */\nexport function MoreThanOrEqual<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"moreThanOrEqual\", value)\n}\n"], "sourceRoot": "../.."}