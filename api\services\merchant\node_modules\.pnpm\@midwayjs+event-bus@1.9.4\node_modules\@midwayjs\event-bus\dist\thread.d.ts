/// <reference types="node" />
import { Message, ThreadEventBusOptions } from './interface';
import { Worker } from 'worker_threads';
import { AbstractEventBus } from './base';
export declare class ThreadEventBus extends AbstractEventBus<Worker> {
    protected options: ThreadEventBusOptions;
    constructor(options?: ThreadEventBusOptions);
    protected workerSubscribeMessage(subscribeMessageHandler: (message: Message) => void): void;
    protected workerListenMessage(worker: Worker, subscribeMessageHandler: (message: Message) => void): void;
    protected workerSendMessage(message: Message): void;
    protected mainSendMessage(worker: Worker, message: Message): void;
    isMain(): boolean;
    isWorker(): boolean;
    getWorkerId(worker?: Worker): string;
}
//# sourceMappingURL=thread.d.ts.map