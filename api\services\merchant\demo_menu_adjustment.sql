-- 演示菜单结构调整：将"批量结算"从"财务结算"移动到"商户管理"
USE `merchant_service_db`;

-- 🎯 演示场景：电商平台决定将批量结算功能整合到商户管理模块中

-- 1. 查看调整前的菜单结构
SELECT '=== 调整前的菜单结构 ===' as info;
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', name)
    ELSE CONCAT('  ├── ', name)
  END AS menu_tree,
  router,
  routeKey,
  CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' END AS type_name
FROM merchant_sys_menu 
WHERE name IN ('商户管理', '财务结算', '批量结算') 
   OR parentId IN (
     SELECT id FROM merchant_sys_menu WHERE name IN ('商户管理', '财务结算')
   )
ORDER BY parentId, orderNum;

-- 2. 模拟菜单结构调整
-- 假设"批量结算"原来在"财务结算"(id=50)下，现在要移动到"商户管理"(id=2)下

-- 首先找到相关菜单的ID
SET @merchant_mgmt_id = (SELECT id FROM merchant_sys_menu WHERE name = '商户管理' AND parentId = 0);
SET @settlement_batch_id = (SELECT id FROM merchant_sys_menu WHERE routeKey = 'settlement.batch');

-- 显示找到的ID
SELECT 
  '商户管理ID' as item, @merchant_mgmt_id as id
UNION ALL
SELECT 
  '批量结算ID' as item, @settlement_batch_id as id;

-- 3. 执行菜单调整
-- 将"批量结算"的父级改为"商户管理"，并调整路由路径
UPDATE merchant_sys_menu 
SET 
  parentId = @merchant_mgmt_id,
  router = '/merchant/batch-settlement',  -- 新的路由路径
  orderNum = 6  -- 调整排序，放在商户管理的最后
WHERE routeKey = 'settlement.batch';

-- 4. 查看调整后的菜单结构
SELECT '=== 调整后的菜单结构 ===' as info;
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', name)
    ELSE CONCAT('  ├── ', name)
  END AS menu_tree,
  router,
  routeKey,
  CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' END AS type_name
FROM merchant_sys_menu 
WHERE name IN ('商户管理', '财务结算', '批量结算') 
   OR parentId IN (
     SELECT id FROM merchant_sys_menu WHERE name IN ('商户管理', '财务结算')
   )
ORDER BY parentId, orderNum;

-- 5. 验证路由映射系统的效果
SELECT '=== 路由映射验证 ===' as info;
SELECT 
  name,
  routeKey,
  router as current_path,
  '/settlement/batch-settlement' as old_path,
  '✅ 旧路径会自动重定向到新路径' as mapping_status
FROM merchant_sys_menu 
WHERE routeKey = 'settlement.batch';

-- 6. 模拟更多调整场景
SELECT '=== 更多调整示例 ===' as info;

-- 场景1：将"创作者认证"从"商户管理"移动到新的"创作者中心"
-- 首先创建"创作者中心"目录
INSERT INTO merchant_sys_menu (parentId, name, router, routeKey, type, icon, orderNum, keepAlive, isShow) 
VALUES (0, '创作者中心', '/creator', 'creator', 0, '&#xe7b5;', 7, 1, 1)
ON DUPLICATE KEY UPDATE name = name;  -- 如果已存在则不插入

SET @creator_center_id = (SELECT id FROM merchant_sys_menu WHERE name = '创作者中心' AND parentId = 0);

-- 移动"创作者认证"到"创作者中心"
UPDATE merchant_sys_menu 
SET 
  parentId = @creator_center_id,
  router = '/creator/heritage-auth',
  orderNum = 1
WHERE routeKey = 'creator.heritage-auth';

-- 7. 显示最终的菜单结构
SELECT '=== 最终菜单结构 ===' as info;
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', name)
    WHEN (SELECT parentId FROM merchant_sys_menu p WHERE p.id = merchant_sys_menu.parentId) = 0 
      THEN CONCAT('  ├── ', name)
    ELSE CONCAT('    └── ', name)
  END AS menu_tree,
  router,
  routeKey
FROM merchant_sys_menu 
WHERE isShow = 1 AND type IN (0, 1)
ORDER BY 
  CASE WHEN parentId = 0 THEN id ELSE parentId END,
  CASE WHEN parentId = 0 THEN 0 ELSE 1 END,
  orderNum;

-- 8. 路由映射效果总结
SELECT '=== 路由映射系统效果总结 ===' as info;
SELECT 
  '原始访问路径' as path_type,
  '/settlement/batch-settlement' as path,
  '自动重定向到 → /merchant/batch-settlement' as result
UNION ALL
SELECT 
  '新访问路径' as path_type,
  '/merchant/batch-settlement' as path,
  '✅ 正常访问' as result
UNION ALL
SELECT 
  '创作者认证原路径' as path_type,
  '/merchant/heritage-auth' as path,
  '自动重定向到 → /creator/heritage-auth' as result
UNION ALL
SELECT 
  '创作者认证新路径' as path_type,
  '/creator/heritage-auth' as path,
  '✅ 正常访问' as result;

-- 9. 清理演示数据（可选，如果需要恢复原状态）
-- ROLLBACK; -- 如果在事务中执行，可以回滚

SELECT '🎉 菜单调整演示完成！' as result;
SELECT '现在用户访问旧路径时会自动重定向到新路径，不会出现404错误！' as benefit;
