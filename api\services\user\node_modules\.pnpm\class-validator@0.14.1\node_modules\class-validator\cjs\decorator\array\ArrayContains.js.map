{"version": 3, "file": "ArrayContains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/array/ArrayContains.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,cAAc,GAAG,eAAe,CAAC;AAE9C;;;GAGG;AACH,SAAgB,aAAa,CAAC,KAAc,EAAE,MAAa;IACzD,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAExC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC;AAJD,sCAIC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,MAAa,EAAE,iBAAqC;IAChF,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC9E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,4CAA4C,EACvE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,sCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const ARRAY_CONTAINS = 'arrayContains';\n\n/**\n * Checks if array contains all values from the given array of values.\n * If null or undefined is given then this function returns false.\n */\nexport function arrayContains(array: unknown, values: any[]): boolean {\n  if (!Array.isArray(array)) return false;\n\n  return values.every(value => array.indexOf(value) !== -1);\n}\n\n/**\n * Checks if array contains all values from the given array of values.\n * If null or undefined is given then this function returns false.\n */\nexport function ArrayContains(values: any[], validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: ARRAY_CONTAINS,\n      constraints: [values],\n      validator: {\n        validate: (value, args): boolean => arrayContains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain $constraint1 values',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}