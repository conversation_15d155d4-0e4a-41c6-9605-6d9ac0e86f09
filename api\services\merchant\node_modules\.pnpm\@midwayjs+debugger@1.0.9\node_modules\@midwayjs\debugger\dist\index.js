"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !exports.hasOwnProperty(p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.clearDebug = exports.debugWrapper = void 0;
const child_process_1 = require("child_process");
const utils_1 = require("./utils");
__exportStar(require("./utils"), exports);
let child;
exports.debugWrapper = (options) => {
    if (options.debug || options.ts || options.child) {
        if (!child) {
            child = {
                isReady: false,
                invoke: (data) => {
                    return new Promise((resolve, reject) => {
                        const id = utils_1.getRandomId();
                        child.invokeMap[id] = { resolve, reject };
                        utils_1.sendData(child.process, { type: 'invoke', id, data });
                    });
                },
                process: null,
                invokeMap: {}
            };
            ;
            (async () => {
                const port = options.port || '9229';
                const execArgv = [];
                if (options.debug) {
                    const portIsUse = await utils_1.checkPort(port);
                    if (portIsUse) {
                        console.log('\n\n');
                        console.log(`Debug port ${port} is in use`);
                        console.log('\n\n');
                    }
                    utils_1.vscodeSupport(options);
                    execArgv.push(`--inspect=${port}`);
                }
                if (options.ts) {
                    execArgv.push('-r', 'ts-node/register');
                }
                const debugInfo = utils_1.getDebugPath();
                if (debugInfo.extensions) {
                    execArgv.push(...debugInfo.extensions);
                }
                child.process = child_process_1.fork(debugInfo.path, [
                    JSON.stringify({
                        export: options.export,
                        file: options.file,
                        port,
                        debug: options.debug
                    }),
                ], {
                    cwd: process.cwd(),
                    env: process.env,
                    execArgv
                });
                utils_1.onMessage(child.process, async (msg) => {
                    if (msg.type === 'ready') {
                        child.isReady = true;
                    }
                    else if (msg.type === 'response') {
                        const full = child.invokeMap[msg.id];
                        delete child.invokeMap[msg.id];
                        if (msg.success) {
                            if (msg.function) {
                                msg.function.forEach(functionInfo => {
                                    msg.result[functionInfo.name] = (...args) => {
                                        return new Promise((resolve, reject) => {
                                            const id = utils_1.getRandomId();
                                            child.invokeMap[id] = { resolve, reject };
                                            utils_1.sendData(child.process, {
                                                type: 'invokeInnerFunc',
                                                id,
                                                data: {
                                                    funcId: functionInfo.id,
                                                    args
                                                }
                                            });
                                        });
                                    };
                                });
                            }
                            full.resolve(msg.result);
                        }
                        else {
                            full.reject(msg.error);
                        }
                    }
                    else if (msg.type === 'childExit') {
                        exports.clearDebug(msg.exitCode);
                    }
                });
                process.on('SIGINT', () => {
                    exports.clearDebug(process.exitCode);
                });
            })();
        }
        return (...args) => {
            return waitChildReady().then((child) => {
                return child.invoke(args);
            });
        };
    }
    else {
        return utils_1.getFun(options);
    }
};
exports.clearDebug = (exitCode) => {
    if (child && child.process && child.process.pid > 0) {
        const pid = child.process.pid;
        try {
            child.process.kill(0);
            child.process.kill();
            try {
                child.process.kill(0);
                child_process_1.execSync('kill -9 ' + pid);
            }
            catch (ex) {
                void 0;
            }
        }
        catch (ex) {
            void 0;
        }
        child = null;
    }
    // 若子进程异常退出则本进程相同异常退出，以确保外层调用（jest，mocha等）能正确感知执行结果
    if (exitCode > 0) {
        process.exit(exitCode);
    }
};
// 等待子进程ready，避免在同时调用多次的时候创建多个debug子进程
const waitChildReady = () => {
    return new Promise(resolve => {
        if (child && child.isReady) {
            resolve(child);
        }
        else {
            setTimeout(() => {
                waitChildReady().then(resolve);
            }, 50);
        }
    });
};
//# sourceMappingURL=data:application/json;base64,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