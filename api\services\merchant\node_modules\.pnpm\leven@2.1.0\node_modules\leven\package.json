{"name": "leven", "version": "2.1.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": "sindresorhus/leven", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava", "bench": "matcha bench.js"}, "files": ["index.js"], "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^0.17.0", "fast-levenshtein": "^2.0.5", "ld": "^0.1.0", "levdist": "^2.0.0", "levenshtein": "^1.0.4", "levenshtein-component": "0.0.1", "levenshtein-edit-distance": "^2.0.0", "matcha": "^0.7.0", "natural": "^0.4.0", "talisman": "^0.18.0", "xo": "^0.16.0"}}