declare const widestLine: {
	/**
	Get the visual width of the widest line in a string - the number of columns required to display it.

	@example
	```
	import widestLine = require('widest-line');

	widestLine('古\n\u001B[1m@\u001B[22m');
	//=> 2
	```
	*/
	(input: string): number;

	// TODO: remove this in the next major version, refactor definition to:
	// declare function widestLine(input: string): number;
	// export = widestLine;
	default: typeof widestLine;
};

export = widestLine;
