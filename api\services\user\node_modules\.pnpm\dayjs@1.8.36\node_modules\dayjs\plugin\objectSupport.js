!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):t.dayjs_plugin_objectSupport=n()}(this,function(){"use strict";return function(t,n){var e=n.prototype,i=function(t){var n,i=t.date,r=t.utc,o={};if(!((n=i)instanceof Date)&&!(n instanceof Array)&&n instanceof Object){Object.keys(i).forEach(function(t){var n,r;o[(n=t,r=e.$utils().p(n),"date"===r?"day":r)]=i[t]});var u=o.year||1970,a=o.month-1||0,c=o.day||1,d=o.hour||0,f=o.minute||0,s=o.second||0,b=o.millisecond||0;return r?new Date(Date.UTC(u,a,c,d,f,s,b)):new Date(u,a,c,d,f,s,b)}return i},r=e.parse;e.parse=function(t){t.date=i.bind(this)(t),r.bind(this)(t)};var o=e.set,u=e.add,a=function(t,n,e,i){if(void 0===i&&(i=1),n instanceof Object){var r=this;return Object.keys(n).forEach(function(e){r=t.bind(r)(n[e]*i,e)}),r}return t.bind(this)(n*i,e)};e.set=function(t,n){return n=void 0===n?t:n,a.bind(this)(function(t,n){return o.bind(this)(n,t)},n,t)},e.add=function(t,n){return a.bind(this)(u,t,n)},e.subtract=function(t,n){return a.bind(this)(u,t,n,-1)}}});
