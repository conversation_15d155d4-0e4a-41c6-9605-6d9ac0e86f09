"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Configuration = exports.ORM_MODEL_KEY = exports.EVENT_SUBSCRIBER_KEY = exports.ENTITY_MODEL_KEY = void 0;
var decorator_1 = require("./decorator");
Object.defineProperty(exports, "ENTITY_MODEL_KEY", { enumerable: true, get: function () { return decorator_1.ENTITY_MODEL_KEY; } });
Object.defineProperty(exports, "EVENT_SUBSCRIBER_KEY", { enumerable: true, get: function () { return decorator_1.EVENT_SUBSCRIBER_KEY; } });
Object.defineProperty(exports, "ORM_MODEL_KEY", { enumerable: true, get: function () { return decorator_1.ORM_MODEL_KEY; } });
var configuration_1 = require("./configuration");
Object.defineProperty(exports, "Configuration", { enumerable: true, get: function () { return configuration_1.OrmConfiguration; } });
__exportStar(require("./decorator"), exports);
__exportStar(require("./interface"), exports);
__exportStar(require("./dataSourceManager"), exports);
//# sourceMappingURL=index.js.map