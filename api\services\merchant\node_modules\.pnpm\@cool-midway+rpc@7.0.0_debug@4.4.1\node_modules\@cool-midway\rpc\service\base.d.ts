import { IMidwayApplication } from '@midwayjs/core';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
/**
 * 服务基类
 */
export declare abstract class BaseRpcService {
    private conf;
    protected entity: any;
    protected sqlParams: any;
    protected curdOption: any;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    setModel(entity: any): void;
    setCurdOption(curdOption: any): void;
    app: IMidwayApplication;
    setApp(app: any): void;
    init(): void;
    /**
     * 检查排序
     * @param sort 排序
     * @returns
     */
    private checkSort;
    /**
     * 获得单个ID
     * @param params 参数
     */
    info(params: any): Promise<any>;
    /**
     * 执行SQL并获得分页数据
     * @param sql 执行的sql语句
     * @param query 分页查询条件
     * @param autoSort 是否自动排序
     * @param connectionName 连接名称
     */
    sqlRenderPage(sql: any, query: any, autoSort?: boolean, connectionName?: any): Promise<{
        list: any;
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    /**
     * 设置sql
     * @param condition 条件是否成立
     * @param sql sql语句
     * @param params 参数
     */
    setSql(condition: any, sql: any, params: any): any;
    /**
     * 获得查询个数的SQL
     * @param sql
     */
    getCountSql(sql: any): string;
    /**
     * 参数安全性检查
     * @param params
     */
    paramSafetyCheck(params: any): Promise<boolean>;
    /**
     * 原生查询
     * @param sql
     * @param params
     * @param connectionName
     */
    nativeQuery(sql: any, params?: any, connectionName?: any): Promise<any>;
    /**
     * 获得ORM管理
     *  @param connectionName 连接名称
     */
    getOrmManager(connectionName?: string): import("typeorm").DataSource;
    /**
     * 非分页查询
     * @param params 查询条件
     */
    list(params?: any): Promise<any>;
    /**
     * 删除
     * @param params 参数
     */
    delete(params: any | string): Promise<void>;
    /**
     * 新增|修改
     * @param params 数据
     */
    addOrUpdate(params: any): Promise<void>;
    /**
     * 新增
     * @param param 数据
     */
    add(params: any): Promise<Object>;
    /**
     * 修改
     * @param param 数据
     */
    update(params: any): Promise<void>;
    /**
     * 新增|修改|删除 之后的操作
     * @param data 对应数据
     */
    modifyAfter(data: any): Promise<void>;
    /**
     * 分页查询
     * @param params 查询条件
     */
    page(params?: any): Promise<{
        list: any;
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    /**
     * query
     * @param data
     * @param query
     */
    renderPage(data: any, query: any): {
        list: any;
        pagination: {
            page: number;
            size: number;
            total: any;
        };
    };
    /**
     * 构建查询配置
     * @param query 前端查询
     * @param option
     */
    private getOptionFind;
}
