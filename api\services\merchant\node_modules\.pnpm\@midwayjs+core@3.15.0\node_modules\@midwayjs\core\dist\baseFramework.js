"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseFramework = void 0;
const interface_1 = require("./interface");
const constants_1 = require("./constants");
const decorator_1 = require("./decorator");
const requestContainer_1 = require("./context/requestContainer");
const environmentService_1 = require("./service/environmentService");
const configService_1 = require("./service/configService");
const informationService_1 = require("./service/informationService");
const loggerService_1 = require("./service/loggerService");
const middlewareManager_1 = require("./common/middlewareManager");
const middlewareService_1 = require("./service/middlewareService");
const filterManager_1 = require("./common/filterManager");
const mockService_1 = require("./service/mockService");
const util = require("util");
const asyncContextManager_1 = require("./common/asyncContextManager");
const guardManager_1 = require("./common/guardManager");
const debug = util.debuglog('midway:debug');
class BaseFramework {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
        this.defaultContext = {};
        this.middlewareManager = this.createMiddlewareManager();
        this.filterManager = this.createFilterManager();
        this.guardManager = this.createGuardManager();
        this.composeMiddleware = null;
    }
    async init() {
        var _a, _b;
        this.configurationOptions = (_a = this.configure()) !== null && _a !== void 0 ? _a : {};
        this.contextLoggerApplyLogger =
            (_b = this.configurationOptions.contextLoggerApplyLogger) !== null && _b !== void 0 ? _b : 'appLogger';
        this.contextLoggerFormat = this.configurationOptions.contextLoggerFormat;
        this.logger = this.loggerService.getLogger('coreLogger');
        this.appLogger = this.loggerService.getLogger('appLogger');
        return this;
    }
    isEnable() {
        return true;
    }
    async initialize(options) {
        this.bootstrapOptions = options;
        await this.beforeContainerInitialize(options);
        await this.containerInitialize(options);
        await this.afterContainerInitialize(options);
        await this.containerDirectoryLoad(options);
        await this.afterContainerDirectoryLoad(options);
        /**
         * Third party application initialization
         */
        await this.applicationInitialize(options);
        await this.containerReady(options);
        await this.afterContainerReady(options);
        await this.mockService.runSimulatorAppSetup(this.app);
    }
    /**
     * @deprecated
     */
    async containerInitialize(options) { }
    /**
     * @deprecated
     */
    async containerDirectoryLoad(options) { }
    /**
     * @deprecated
     */
    async containerReady(options) {
        if (!this.app.getApplicationContext) {
            this.defineApplicationProperties();
        }
    }
    getApplicationContext() {
        return this.applicationContext;
    }
    getConfiguration(key) {
        return this.configService.getConfiguration(key);
    }
    getCurrentEnvironment() {
        return this.environmentService.getCurrentEnvironment();
    }
    getApplication() {
        return this.app;
    }
    createContextLogger(ctx, name) {
        if (name && name !== 'appLogger') {
            const appLogger = this.getLogger(name);
            let ctxLoggerCache = ctx.getAttr(constants_1.REQUEST_CTX_LOGGER_CACHE_KEY);
            if (!ctxLoggerCache) {
                ctxLoggerCache = new Map();
                ctx.setAttr(constants_1.REQUEST_CTX_LOGGER_CACHE_KEY, ctxLoggerCache);
            }
            // if logger exists
            if (ctxLoggerCache.has(name)) {
                return ctxLoggerCache.get(name);
            }
            // create new context logger
            const ctxLogger = this.loggerService.createContextLogger(ctx, appLogger);
            ctxLoggerCache.set(name, ctxLogger);
            return ctxLogger;
        }
        else {
            const appLogger = this.getLogger(name !== null && name !== void 0 ? name : this.contextLoggerApplyLogger);
            // avoid maximum call stack size exceeded
            if (ctx['_logger']) {
                return ctx['_logger'];
            }
            ctx['_logger'] = this.loggerService.createContextLogger(ctx, appLogger, {
                contextFormat: this.contextLoggerFormat,
            });
            return ctx['_logger'];
        }
    }
    async stop() {
        await this.mockService.runSimulatorAppTearDown(this.app);
        await this.beforeStop();
    }
    getAppDir() {
        return this.informationService.getAppDir();
    }
    getBaseDir() {
        return this.informationService.getBaseDir();
    }
    defineApplicationProperties(applicationProperties = {}, whiteList = []) {
        const defaultApplicationProperties = {
            getBaseDir: () => {
                return this.getBaseDir();
            },
            getAppDir: () => {
                return this.getAppDir();
            },
            getEnv: () => {
                return this.getCurrentEnvironment();
            },
            getApplicationContext: () => {
                return this.getApplicationContext();
            },
            getConfig: (key) => {
                return this.getConfiguration(key);
            },
            getFrameworkType: () => {
                if (this['getFrameworkType']) {
                    return this['getFrameworkType']();
                }
            },
            getProcessType: () => {
                return interface_1.MidwayProcessTypeEnum.APPLICATION;
            },
            getCoreLogger: () => {
                return this.getCoreLogger();
            },
            getLogger: (name) => {
                return this.getLogger(name);
            },
            createLogger: (name, options = {}) => {
                return this.createLogger(name, options);
            },
            getFramework: () => {
                return this;
            },
            getProjectName: () => {
                return this.getProjectName();
            },
            createAnonymousContext: (extendCtx) => {
                const ctx = extendCtx || Object.create(this.defaultContext);
                if (!ctx.startTime) {
                    ctx.startTime = Date.now();
                }
                if (!ctx.logger) {
                    ctx.logger = this.createContextLogger(ctx);
                }
                if (!ctx.requestContext) {
                    ctx.requestContext = new requestContainer_1.MidwayRequestContainer(ctx, this.getApplicationContext());
                    ctx.requestContext.ready();
                }
                if (!ctx.getLogger) {
                    ctx.getLogger = name => {
                        return this.createContextLogger(ctx, name);
                    };
                }
                ctx.setAttr = (key, value) => {
                    ctx.requestContext.setAttr(key, value);
                };
                ctx.getAttr = (key) => {
                    return ctx.requestContext.getAttr(key);
                };
                ctx.getApp = () => {
                    return this.getApplication();
                };
                return ctx;
            },
            addConfigObject: (obj) => {
                this.configService.addObject(obj);
            },
            setAttr: (key, value) => {
                this.getApplicationContext().setAttr(key, value);
            },
            getAttr: (key) => {
                return this.getApplicationContext().getAttr(key);
            },
            useMiddleware: (middleware) => {
                return this.useMiddleware(middleware);
            },
            getMiddleware: () => {
                return this.getMiddleware();
            },
            useFilter: (Filter) => {
                return this.useFilter(Filter);
            },
            useGuard: (guard) => {
                return this.useGuard(guard);
            },
            getNamespace: () => {
                return this.getNamespace();
            },
        };
        for (const method of whiteList) {
            delete defaultApplicationProperties[method];
        }
        Object.assign(this.app, defaultApplicationProperties, applicationProperties);
    }
    async beforeStop() { }
    /**
     * @deprecated
     */
    async beforeContainerInitialize(options) { }
    /**
     * @deprecated
     */
    async afterContainerInitialize(options) { }
    /**
     * @deprecated
     */
    async afterContainerDirectoryLoad(options) { }
    /**
     * @deprecated
     */
    async afterContainerReady(options) { }
    async applyMiddleware(lastMiddleware) {
        var _a;
        if (!this.composeMiddleware) {
            if (!this.applicationContext.hasObject(constants_1.ASYNC_CONTEXT_MANAGER_KEY)) {
                const asyncContextManagerEnabled = this.configService.getConfiguration('asyncContextManager.enable') ||
                    false;
                const contextManager = asyncContextManagerEnabled
                    ? ((_a = this.bootstrapOptions) === null || _a === void 0 ? void 0 : _a.asyncContextManager) ||
                        new asyncContextManager_1.NoopContextManager()
                    : new asyncContextManager_1.NoopContextManager();
                if (asyncContextManagerEnabled) {
                    contextManager.enable();
                }
                this.applicationContext.registerObject(constants_1.ASYNC_CONTEXT_MANAGER_KEY, contextManager);
            }
            this.middlewareManager.insertFirst((async (ctx, next) => {
                // warp with context manager
                const rootContext = asyncContextManager_1.ASYNC_ROOT_CONTEXT.setValue(constants_1.ASYNC_CONTEXT_KEY, ctx);
                const contextManager = this.applicationContext.get(constants_1.ASYNC_CONTEXT_MANAGER_KEY);
                return await contextManager.with(rootContext, async () => {
                    // run simulator context setup
                    await this.mockService.runSimulatorContextSetup(ctx, this.app);
                    this.mockService.applyContextMocks(this.app, ctx);
                    let returnResult = undefined;
                    try {
                        const result = await next();
                        returnResult = await this.filterManager.runResultFilter(result, ctx);
                    }
                    catch (err) {
                        returnResult = await this.filterManager.runErrorFilter(err, ctx);
                    }
                    finally {
                        // run simulator context teardown
                        await this.mockService.runSimulatorContextTearDown(ctx, this.app);
                    }
                    if (returnResult.error) {
                        throw returnResult.error;
                    }
                    return returnResult.result;
                });
            }));
            debug(`[core]: Compose middleware = [${this.middlewareManager.getNames()}]`);
            this.composeMiddleware = await this.middlewareService.compose(this.middlewareManager, this.app);
            await this.filterManager.init(this.applicationContext);
        }
        if (lastMiddleware) {
            lastMiddleware = Array.isArray(lastMiddleware)
                ? lastMiddleware
                : [lastMiddleware];
            return await this.middlewareService.compose([this.composeMiddleware, ...lastMiddleware], this.app);
        }
        return this.composeMiddleware;
    }
    getLogger(name) {
        var _a;
        return (_a = this.loggerService.getLogger(name)) !== null && _a !== void 0 ? _a : this.appLogger;
    }
    getCoreLogger() {
        return this.logger;
    }
    createLogger(name, option = {}) {
        return this.loggerService.createLogger(name, option);
    }
    getProjectName() {
        return this.informationService.getProjectName();
    }
    getFrameworkName() {
        return this.constructor.name;
    }
    useMiddleware(middleware) {
        this.middlewareManager.insertLast(middleware);
    }
    getMiddleware() {
        return this.middlewareManager;
    }
    useFilter(filter) {
        return this.filterManager.useFilter(filter);
    }
    useGuard(guards) {
        return this.guardManager.addGlobalGuard(guards);
    }
    async runGuard(ctx, supplierClz, methodName) {
        return this.guardManager.runGuard(ctx, supplierClz, methodName);
    }
    createMiddlewareManager() {
        return new middlewareManager_1.ContextMiddlewareManager();
    }
    createFilterManager() {
        return new filterManager_1.FilterManager();
    }
    createGuardManager() {
        return new guardManager_1.GuardManager();
    }
    setNamespace(namespace) {
        this.namespace = namespace;
    }
    getNamespace() {
        return this.namespace;
    }
}
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", loggerService_1.MidwayLoggerService)
], BaseFramework.prototype, "loggerService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", environmentService_1.MidwayEnvironmentService)
], BaseFramework.prototype, "environmentService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", configService_1.MidwayConfigService)
], BaseFramework.prototype, "configService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", informationService_1.MidwayInformationService)
], BaseFramework.prototype, "informationService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", middlewareService_1.MidwayMiddlewareService)
], BaseFramework.prototype, "middlewareService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", mockService_1.MidwayMockService)
], BaseFramework.prototype, "mockService", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BaseFramework.prototype, "init", null);
exports.BaseFramework = BaseFramework;
//# sourceMappingURL=baseFramework.js.map