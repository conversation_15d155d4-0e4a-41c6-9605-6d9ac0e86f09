import { Provide } from '@midwayjs/core';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MerchantEntity } from '../entity/merchant';
import { In } from 'typeorm';

@Provide()
@CoolRpcService({
  entity: MerchantEntity,
  method: ["add", "delete", "update", "info", "list", "page"]
})
export class MerchantService extends BaseRpcService {
  @InjectEntityModel(MerchantEntity)
  merchantEntity!: Repository<MerchantEntity>;

  /**
   * 重写分页方法，修复分页参数处理问题
   */
  async page(query: any, option?: any) {
    console.log('🔍 [MerchantService.page] 接收参数:', { query, option });
    
    // 若 query 被包装成 [query, option] 的数组，解包处理
    if (Array.isArray(query) && query.length === 2 && option === undefined) {
      option = query[1];
      query = query[0];
    }
    
    // 提取分页参数
    const page = query.page || 1;
    const size = query.size || 10;
    
    console.log('📋 [MerchantService.page] 分页参数:', { page, size });
    
    // 构建查询条件
    const queryBuilder = this.merchantEntity.createQueryBuilder('merchant');
    
    // ✅ 修复：处理status条件
    if (query.status !== undefined) {
      // 如果是数组，说明可能是默认查询或多状态查询
      if (Array.isArray(query.status)) {
        queryBuilder.andWhere('merchant.status IN (:...status)', { status: query.status });

        // ✅ 修复：只有在入驻列表查询时才限制包含status=1的查询
        // 通过检查是否有type参数来判断：入驻列表查询不会传type参数，商户列表查询会传type参数
        if (query.status.includes(1) && query.type === undefined) {
          throw new Error('已通过的商户请在商户列表中查看');
        }
      } else {
        // 如果是单个值，说明是前端指定了具体状态
        const status = Number(query.status);

        // ✅ 修复：只有在入驻列表查询时才限制status=1的查询
        // 通过检查是否有type参数来判断是否为商户列表查询
        // 入驻列表查询不会传type参数，商户列表查询会传type参数
        if (status === 1 && query.type === undefined) {
          throw new Error('已通过的商户请在商户列表中查看');
        }

        queryBuilder.andWhere('merchant.status = :status', { status });
      }
    }

    // ✅ 修复：强制处理type条件
    if (query.type !== undefined) {
      queryBuilder.andWhere('merchant.type = :type', { type: query.type });
    }
    
    // 处理其他等值条件
    if (option?.fieldEq && Array.isArray(option.fieldEq)) {
      option.fieldEq.forEach((field: string) => {
        // 跳过已处理的status和type字段
        if (field !== 'status' && field !== 'type' && query[field] !== undefined && query[field] !== '') {
          if (Array.isArray(query[field])) {
            queryBuilder.andWhere(`merchant.${field} IN (:...${field})`, { [field]: query[field] });
          } else {
            queryBuilder.andWhere(`merchant.${field} = :${field}`, { [field]: query[field] });
          }
        }
      });
    }
    
    // 处理模糊搜索
    if (option?.keyWordLikeFields && Array.isArray(option.keyWordLikeFields) && query.searchKey) {
      const likeConditions = option.keyWordLikeFields.map((field: string) => 
        `merchant.${field} LIKE :searchKey`
      ).join(' OR ');
      queryBuilder.andWhere(`(${likeConditions})`, { searchKey: `%${query.searchKey}%` });
    }
    
    // 处理排序
    if (option?.addOrderBy) {
      Object.entries(option.addOrderBy).forEach(([field, direction]) => {
        queryBuilder.addOrderBy(`merchant.${field}`, direction as any);
      });
    } else {
      queryBuilder.orderBy('merchant.createTime', 'DESC');
    }
    
    // 执行分页查询
    const [list, total] = await queryBuilder
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();
    
    // 打印SQL和结果
    console.log('🔍 [MerchantService.page] 生成的SQL:', queryBuilder.getSql());
    console.log('🔍 [MerchantService.page] SQL参数:', queryBuilder.getParameters());
    console.log('✅ [MerchantService.page] 查询结果:', { total, listCount: list.length });
    
    return {
      list,
      pagination: {
        page: Number(page),
        size: Number(size),
        total
      }
    };
  }

  /**
   * 通过ID获取商户信息（RPC暴露）
   */
  async getMerchantById(id: number) {
    return this.merchantEntity.findOneBy({ id });
  }

  /**
   * 获取商户统计信息
   */
  async getMerchantStats() {
    const total = await this.merchantEntity.count();
    const pending = await this.merchantEntity.countBy({ status: 0 }); // 待审核
    const approved = await this.merchantEntity.countBy({ status: 1 }); // 已通过
    const rejected = await this.merchantEntity.countBy({ status: 2 }); // 已拒绝

    return {
      total,
      pending,
      approved,
      rejected,
    };
  }

  /**
   * 商户审核
   */
  async reviewMerchant(id: number, status: number, remark?: string) {
    return await this.merchantEntity.update(id, {
      status,
      remark,
      updateTime: new Date(),
    });
  }

  /**
   * 审核商户入驻申请
   */
  async auditMerchant(...args: any[]) {
    console.log('🔍 [商户微服务] 审核商户原始参数:', args);
    
    // 处理RPC参数传递 - 可能是数组格式 [id, status, remark]
    let id: any, status: any, remark: any;
    
    if (args.length === 1 && Array.isArray(args[0])) {
      // 如果参数被包装成数组：[[id, status, remark]]
      [id, status, remark] = args[0];
    } else if (args.length === 3) {
      // 如果是直接传递的三个参数：(id, status, remark)
      [id, status, remark] = args;
    } else {
      // 如果是对象格式或其他格式
      const param = args[0];
      if (typeof param === 'object' && param !== null) {
        id = param.id;
        status = param.status;
        remark = param.remark;
      } else {
        id = args[0];
        status = args[1];
        remark = args[2];
      }
    }
    
    console.log('🔍 [商户微服务] 解析后的参数:', { id, status, remark });
    console.log('🔍 [商户微服务] 参数类型检查:', {
      id: { value: id, type: typeof id },
      status: { value: status, type: typeof status },
      remark: { value: remark, type: typeof remark }
    });
    
    // 参数验证 - 更宽松的验证
    if (id === undefined || id === null || (typeof id !== 'number' && typeof id !== 'string')) {
      console.error('❌ [商户微服务] 无效的商户ID:', { id, type: typeof id });
      throw new Error('无效的商户ID');
    }
    if (status === undefined || status === null || ![1, 2].includes(Number(status))) {
      console.error('❌ [商户微服务] 无效的审核状态:', { status, type: typeof status });
      throw new Error('无效的审核状态');
    }
    
    // 确保参数都是正确的类型
    const merchantId = Number(id);
    const auditStatus = Number(status);
    const auditRemark = remark || '';
    
    console.log('🔍 [商户微服务] 转换后的参数:', {
      merchantId,
      auditStatus,
      auditRemark
    });
    
    const merchant = await this.merchantEntity.findOneBy({ id: merchantId });
    if (!merchant) {
      console.error('❌ [商户微服务] 商户不存在:', { merchantId });
      throw new Error('商户不存在');
    }

    console.log('🔍 [商户微服务] 找到商户:', {
      id: merchant.id,
      name: merchant.name || merchant.companyName,
      currentStatus: merchant.status,
      type: merchant.type
    });

    if (merchant.status !== 0) {
      console.error('❌ [商户微服务] 商户已经审核过了:', {
        merchantId,
        currentStatus: merchant.status
      });
      throw new Error('该商户已经审核过了');
    }

    try {
      // 开启事务
      const queryRunner = this.merchantEntity.manager.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
    // 更新审核状态
        const result = await queryRunner.manager.update(
          MerchantEntity,
          { id: merchantId },
          {
            status: auditStatus,
            remark: auditRemark,
            updateTime: new Date()
          }
        );

        console.log('🔍 [商户微服务] 数据库更新结果:', result);

    // 如果审核通过，可以在这里添加额外的业务逻辑
        if (auditStatus === 1) {
          console.log(`✅ 商户 ${merchant.type === 0 ? merchant.name : merchant.companyName} 审核通过`);
      // TODO: 可以在这里调用其他服务，比如创建商户登录账户等
    }

        // 提交事务
        await queryRunner.commitTransaction();
        console.log('✅ [商户微服务] 审核完成:', result);
    return { success: true, message: '审核完成' };
      } catch (error) {
        // 回滚事务
        await queryRunner.rollbackTransaction();
        console.error('❌ [商户微服务] 审核失败:', error);
        throw error;
      } finally {
        // 释放连接
        await queryRunner.release();
      }
    } catch (error) {
      console.error('❌ [商户微服务] 审核失败:', error);
      throw error;
    }
  }

  /**
   * 批量审核商户入驻申请
   */
  async batchAuditMerchant(ids: number[], status: number, remark?: string) {
    console.log('🔍 [商户微服务] 批量审核商户:', { ids, status, remark });
    
    // 参数验证
    if (!Array.isArray(ids) || ids.length === 0) {
      throw new Error('无效的商户ID列表');
    }
    if (![1, 2].includes(status)) {
      throw new Error('无效的审核状态');
    }

    const merchants = await this.merchantEntity.findByIds(ids);
    if (!merchants || merchants.length !== ids.length) {
      throw new Error('部分商户不存在');
    }
    
    // 检查所有商户是否都存在且状态为待审核
    const pendingMerchants = merchants.filter(m => m.status === 0);
    if (pendingMerchants.length !== ids.length) {
      throw new Error('部分商户不存在或已经审核过了');
    }

    try {
      // 开启事务
      const queryRunner = this.merchantEntity.manager.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
    // 批量更新
        const result = await queryRunner.manager.update(
          MerchantEntity,
          { id: In(ids) },
          {
      status,
      remark: remark || '',
            updateTime: new Date()
          }
        );

        // 提交事务
        await queryRunner.commitTransaction();
        console.log('✅ [商户微服务] 批量审核完成:', result);
    return { success: true, message: `批量审核完成，共处理${ids.length}个商户` };
      } catch (error) {
        // 回滚事务
        await queryRunner.rollbackTransaction();
        console.error('❌ [商户微服务] 批量审核失败:', error);
        throw error;
      } finally {
        // 释放连接
        await queryRunner.release();
      }
    } catch (error) {
      console.error('❌ [商户微服务] 批量审核失败:', error);
      throw error;
    }
  }







  /**
   * 更新商户状态
   */
  async updateMerchantStatus(params: { id: number; status: number; remark?: string }) {
    const { id, status, remark } = params;
    const merchant = await this.merchantEntity.findOneBy({ id });
    if (!merchant) {
      throw new Error('商户不存在');
    }

    // ✅ 修复：正确的状态验证
    if (![1, 3, 4].includes(status)) {
      throw new Error('无效的商户状态');
    }

    // ✅ 修复：只有已审核通过的商户（status=1）才能进行状态管理
    if (merchant.status === 0) {
      throw new Error('请先完成商户审核');
    }

    // ✅ 修复：已拒绝的商户（status=2）不能直接更改状态
    if (merchant.status === 2) {
      throw new Error('已拒绝的商户不能更改状态，请重新审核');
    }

    await this.merchantEntity.update(id, {
      status,
      remark: remark || `状态更新为${status === 1 ? '营业' : status === 3 ? '注销' : '封禁'}`,
      updateTime: new Date(),
    });

    return { success: true, message: '商户状态更新成功' };
  }

  /**
   * 删除商户
   */
  async delete(params: any): Promise<void> {
    console.log('🔍 [商户微服务] 删除商户:', params);
    
    const id = params.id || params;
    
    // 参数验证
    if (!id || typeof id !== 'number') {
      throw new Error('无效的商户ID');
    }
    
    const merchant = await this.merchantEntity.findOneBy({ id });
    if (!merchant) {
      throw new Error('商户不存在');
    }

    try {
      // 开启事务
      const queryRunner = this.merchantEntity.manager.connection.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        // 删除商户
        const result = await queryRunner.manager.delete(MerchantEntity, { id });

        // TODO: 可以在这里添加额外的清理逻辑，比如：
        // - 删除关联的订单记录
        // - 删除关联的商品记录
        // - 删除商户账户
        // - 清理商户文件等

        // 提交事务
        await queryRunner.commitTransaction();
        console.log('✅ [商户微服务] 删除完成:', result);
      } catch (error) {
        // 回滚事务
        await queryRunner.rollbackTransaction();
        console.error('❌ [商户微服务] 删除失败:', error);
        throw error;
      } finally {
        // 释放连接
        await queryRunner.release();
      }
    } catch (error) {
      console.error('❌ [商户微服务] 删除失败:', error);
      throw error;
    }
  }
} 