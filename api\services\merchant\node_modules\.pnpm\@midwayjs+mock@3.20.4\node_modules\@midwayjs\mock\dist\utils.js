"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processArgsParser = exports.mergeGlobalConfig = exports.removeFile = exports.transformFrameworkToConfiguration = exports.findFirstExistModule = exports.isWin32 = exports.isTestEnvironment = void 0;
const core_1 = require("@midwayjs/core");
const os = require("os");
const assert = require("assert");
const fs = require("fs");
function isTestEnvironment() {
    const testEnv = ['test', 'unittest'];
    return (testEnv.includes(process.env.MIDWAY_SERVER_ENV) ||
        testEnv.includes(process.env.EGG_SERVER_ENV) ||
        testEnv.includes(process.env.NODE_ENV));
}
exports.isTestEnvironment = isTestEnvironment;
function isWin32() {
    return os.platform() === 'win32';
}
exports.isWin32 = isWin32;
function findFirstExistModule(moduleList) {
    for (const name of moduleList) {
        if (!name)
            continue;
        try {
            return require(name);
        }
        catch (e) {
            // ignore
        }
    }
}
exports.findFirstExistModule = findFirstExistModule;
/**
 * transform a framework component or framework module to configuration class
 * @param Framework
 */
async function transformFrameworkToConfiguration(Framework, loadMode) {
    if (!Framework)
        return null;
    let CustomFramework = Framework;
    if (typeof Framework === 'string') {
        Framework = await (0, core_1.loadModule)(Framework, {
            loadMode,
            safeLoad: true,
        });
    }
    if (Framework.Configuration) {
        return Framework;
    }
    if (Framework.Framework) {
        CustomFramework = Framework.Framework;
    }
    else {
        CustomFramework = Framework;
    }
    assert(CustomFramework, `can't found custom framework ${Framework}`);
    let CustomConfiguration = class CustomConfiguration {
        async onServerReady(container) {
            const customFramework = (await container.getAsync(CustomFramework));
            await customFramework.run();
        }
    };
    CustomConfiguration = __decorate([
        (0, core_1.Configuration)()
    ], CustomConfiguration);
    return {
        Configuration: CustomConfiguration,
        Framework,
    };
}
exports.transformFrameworkToConfiguration = transformFrameworkToConfiguration;
async function removeFile(file) {
    try {
        await fs.promises.access(file, fs.constants.W_OK);
        await fs.promises.unlink(file);
    }
    catch (_a) {
        // ignore
    }
}
exports.removeFile = removeFile;
function mergeGlobalConfig(globalConfig, newConfigObject) {
    if (globalConfig) {
        if (Array.isArray(globalConfig)) {
            globalConfig.push({
                default: {
                    ...newConfigObject,
                },
            });
        }
        else {
            globalConfig = {
                ...newConfigObject,
                ...globalConfig,
            };
        }
    }
    else {
        globalConfig = {
            ...newConfigObject,
        };
    }
    return globalConfig;
}
exports.mergeGlobalConfig = mergeGlobalConfig;
/**
 * 解析命令行参数的函数。
 * 它接受一个字符串数组作为输入，然后解析这个数组，
 * 将形如 `--key value` 或 `--key=value` 的参数转换为对象的键值对，
 * 形如 `--key` 的参数转换为 `{ key: true }`。
 * @param argv 命令行参数数组
 * @returns 解析后的参数对象
 */
function processArgsParser(argv) {
    const args = argv.slice(2);
    const result = {};
    args.forEach((arg, index) => {
        if (arg.startsWith('--')) {
            let value;
            let key = arg.slice(2);
            if (key.includes('=')) {
                [key, value] = key.split('=');
            }
            else if (args[index + 1] && !args[index + 1].startsWith('--')) {
                value = args[index + 1];
            }
            else {
                value = true;
            }
            result[key] = value;
        }
    });
    return result;
}
exports.processArgsParser = processArgsParser;
//# sourceMappingURL=utils.js.map