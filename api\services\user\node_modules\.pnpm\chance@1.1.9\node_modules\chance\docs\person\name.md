# name

```js
// usage
chance.name()
chance.name({ middle: true })
chance.name({ middle_initial: true })
chance.name({ prefix: true })
chance.name({ nationality: 'en' })
```

Generate a random name

```js
  chance.name();
  => '<PERSON><PERSON>'
```

Optionally include the middle name

```js
  chance.name({ middle: true });
  => '<PERSON><PERSON><PERSON><PERSON>'
```


Optionally include the middle initial

```js
  chance.name({ middle_initial: true });
  => '<PERSON><PERSON><PERSON>'
```

Optionally include the prefix

```js
  chance.name({ prefix: true });
  => 'Doctor <PERSON>'
```

Optionally include the suffix

```js
  chance.name({ suffix: true });
  => '<PERSON>'
```

Optionally specify a gender

```js
  chance.name({ gender: 'male' });
  => "<PERSON>"
```

Optionally specify a nationality

```js
  chance.name({ nationality: 'it' });
  => "<PERSON>"
```

Note, currently support for nationality is limited to: `'en', 'it'`.

