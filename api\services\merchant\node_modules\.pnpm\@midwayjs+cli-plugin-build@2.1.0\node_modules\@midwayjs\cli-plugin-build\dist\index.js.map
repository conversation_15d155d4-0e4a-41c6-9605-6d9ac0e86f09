{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,yDAMgC;AAChC,+BAAwD;AACxD,uCAMkB;AAClB,iCAAiC;AACjC,2BAA4B;AAC5B,MAAa,WAAY,SAAQ,yBAAU;IAA3C;;QACE,kBAAa,GAAG,KAAK,CAAC;QACd,mBAAc,GAA2B,EAAE,CAAC;QAC5C,oBAAe,GAAQ,EAAE,CAAC;QAClC,aAAQ,GAAG;YACT,KAAK,EAAE;gBACL,eAAe,EAAE;oBACf,eAAe;oBACf,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,QAAQ;oBACR,UAAU;iBACX;gBACD,OAAO,EAAE;oBACP,KAAK,EAAE;wBACL,KAAK,EAAE,wBAAwB;wBAC/B,QAAQ,EAAE,GAAG;qBACd;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE,uBAAuB;wBAC9B,QAAQ,EAAE,GAAG;qBACd;oBACD,MAAM,EAAE;wBACN,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,MAAM,EAAE;wBACN,KAAK,EAAE,gBAAgB;qBACxB;oBACD,QAAQ,EAAE;wBACR,KAAK,EAAE,kCAAkC;qBAC1C;oBACD,UAAU,EAAE;wBACV,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE,mBAAmB;qBAC3B;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE,mBAAmB;qBAC3B;oBACD,MAAM,EAAE;wBACN,KAAK,EAAE,oBAAoB;qBAC5B;iBACF;aACF;SACF,CAAC;QAEF,UAAK,GAAG;YACN,qBAAqB,EAAE,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;YACpD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YACpC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1C,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;YACxC,cAAc,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YACtC,gBAAgB,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;SAC3C,CAAC;IA8OJ,CAAC;IA5OC,KAAK,CAAC,aAAa;QACjB,MAAM,MAAM,GAAG,IAAA,kCAAmB,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SACrC;QAED,MAAM,eAAe,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAC5D,IAAI,IAAA,qBAAU,EAAC,eAAe,CAAC,EAAE;YAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,uBAAY,EAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC;YACxD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;SACpD;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACvB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,IAAA,qBAAU,EAAC,MAAM,CAAC,EAAE;YACtB,MAAM,IAAA,iBAAM,EAAC,MAAM,CAAC,CAAC;SACtB;IACH,CAAC;IAEO,SAAS;QACf,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACvB,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;SAC5B;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QAC5C,OAAO,CACL,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAA,cAAO,EAAC,WAAW,CAAC,CAAC;YACjE,MAAM,CACP,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QAC5E,MAAM,SAAS,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC;QACpE,MAAM,SAAS,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,IAAA,wBAAS,EAAC;YACd,SAAS;YACT,SAAS;YACT,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,OAAO;gBACzC,CAAC,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO;gBAC7B,CAAC,CAAC,CAAC,MAAM,CAAC;YACZ,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;YAC/C,GAAG,EAAE,IAAI,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACzC,CAAC;SACF,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,OAAO,GAAG;YACZ,mBAAmB;YACnB,iBAAiB;YACjB,kBAAkB;YAClB,uBAAuB;SACxB,CAAC;QACF,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,SAAS,EAAE;YACtC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;SACvE;QAED,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,MAAM,IAAA,wBAAS,EAAC;gBACd,SAAS;gBACT,SAAS;gBACT,cAAc,EAAE,OAAO;gBACvB,OAAO;gBACP,GAAG,EAAE,IAAI,CAAC,EAAE;oBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;gBACzC,CAAC;aACF,CAAC,CAAC;SACJ;QACD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;IAC1C,CAAC;IAED,KAAK,CAAC,OAAO;;QACX,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACxD,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,MAAM,IAAA,gCAAiB,EAAC;YAC1D,OAAO,EAAE,GAAG;YACZ,SAAS,EAAE,IAAI,CAAC,WAAW,EAAE;YAC7B,SAAS,EAAE,IAAI,CAAC,aAAa,EAAE;SAChC,CAAC,CAAC;QACH,IAAI,MAAM,CAAC,MAAM,EAAE;YACjB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gBAAgB,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC;aACtE;YACD,IACE,eAAe,CAAC,MAAM;gBACtB,CAAC,CAAA,MAAA,MAAA,IAAI,CAAC,eAAe,0CAAE,oBAAoB,0CAAE,aAAa,CAAA,EAC1D;gBACA,MAAM,IAAI,KAAK,CACb,UAAU,eAAe,CAAC,MAAM,+BAA+B,CAChE,CAAC;aACH;SACF;IACH,CAAC;IAEO,kBAAkB,CAAC,QAAQ,EAAE,aAAa,EAAE,UAAU;;QAC5D,kDAAkD;QAClD,8BAA8B;QAC9B,IAAI,QAAQ,IAAI,QAAQ,CAAC,OAAO,EAAE;YAChC,IACE,CAAC,QAAQ,CAAC,eAAe;gBACzB,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,EACtE;gBACA,OAAO,IAAI,CAAC,kBAAkB,CAC5B,OAAO,CAAC,IAAA,WAAI,EAAC,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,EAC3C,aAAa,EACb,IAAA,cAAO,EAAC,IAAA,WAAI,EAAC,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAC5C,CAAC;aACH;SACF;QAED,OAAO,MAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,eAAe,0CAAG,aAAa,CAAC,CAAC;IACpD,CAAC;IAEO,cAAc;QACpB,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QACjC,OAAO,IAAA,cAAO,EAAC,GAAG,EAAE,OAAO,IAAI,eAAe,CAAC,CAAC;IAClD,CAAC;IAEO,aAAa;QACnB,OAAO,IAAA,cAAO,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,CAAC;IAC9D,CAAC;IAEO,WAAW;QACjB,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QAC5B,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,cAAc,CAAC;QACnB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YAChC,uBAAuB;YACvB,IAAI,IAAA,qBAAU,EAAC,QAAQ,CAAC,EAAE;gBACxB,QAAQ,GAAG,IAAA,uBAAY,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;aAC9C;YACD,IAAI;gBACF,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;aACvC;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CACjB,uDAAuD,CACxD,CAAC;gBACF,MAAM,CAAC,CAAC;aACT;SACF;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;QAC1C,IAAI,CAAC,cAAc,EAAE;YACnB,IAAI,CAAC,IAAA,qBAAU,EAAC,WAAW,CAAC,EAAE;gBAC5B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,yCAAyC,GAAG,IAAI,CAAC,CAAC;gBACtE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;aAC5C;YACD,IAAI;gBACF,cAAc,GAAG,IAAI,CAAC,KAAK,CACzB,IAAA,uBAAY,EAAC,WAAW,EAAE,OAAO,CAAC,CAAC,QAAQ,EAAE,CAC9C,CAAC;aACH;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,gCAAgC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;gBACjE,MAAM,CAAC,CAAC;aACT;SACF;QACD,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,KAAK,CAAC,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACxB,OAAO;SACR;QACD,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,uBAAY,EAAC,cAAc,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvE,MAAM,MAAM,GAAG,IAAA,WAAI,EAAC,cAAc,EAAE,KAAK,EAAE,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAA,WAAI,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QAErD,IAAI,WAAW,GAAG,kBAAkB,CAAC;QACrC,MAAM,WAAW,GAAG,wBAAwB,CAAC;QAC7C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE;YAC5C,GAAG,EAAE,MAAM;SACZ,CAAC,CAAC;QAEH,WAAW,IAAI,WAAW;aACvB,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnB,OAAO,cAAc,IAAI,KAAK,CAAC;QACjC,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;QAEd,MAAM,qBAAqB,GAAG,IAAA,WAAI,EAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC;QAC/D,IAAI,IAAA,qBAAU,EAAC,qBAAqB,CAAC,EAAE;YACrC,WAAW,IAAI;;;;;;;;;;OAUd,CAAC;SACH;QACD,IAAA,wBAAa,EAAC,IAAA,WAAI,EAAC,MAAM,EAAE,WAAW,CAAC,EAAE,WAAW,CAAC,CAAC;QAEtD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACrC,MAAM,IAAA,uBAAQ,EACZ,MAAM,EACN,CAAC,OAAO,EAAE,WAAW,EAAE,IAAI,EAAE,eAAe,EAAE,IAAI,CAAC,EACnD;YACE,GAAG,EAAE,MAAM;SACZ,CACF,CAAC;QACF,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,IAAA,WAAM,GAAE,EAAE,iBAAiB,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACjE,MAAM,IAAA,eAAI,EAAC,IAAA,WAAI,EAAC,MAAM,EAAE,wBAAwB,CAAC,EAAE,OAAO,CAAC,CAAC;QAC5D,MAAM,IAAA,iBAAM,EAAC,MAAM,CAAC,CAAC;QACrB,MAAM,IAAA,eAAI,EAAC,OAAO,EAAE,IAAA,WAAI,EAAC,MAAM,EAAE,WAAW,CAAC,CAAC,CAAC;QAC/C,MAAM,IAAA,iBAAM,EAAC,OAAO,CAAC,CAAC;QACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CACf,sBAAsB,IAAA,eAAQ,EAC5B,OAAO,CAAC,GAAG,EAAE,EACb,IAAA,WAAI,EAAC,MAAM,EAAE,WAAW,CAAC,CAC1B,GAAG,CACL,CAAC;QACF,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CACf,iFAAiF,CAClF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ;QACZ,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;IACvC,CAAC;CACF;AArSD,kCAqSC"}