"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrmConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const decorator_1 = require("./decorator");
const dataSourceManager_1 = require("./dataSourceManager");
const typeorm_1 = require("typeorm");
let OrmConfiguration = class OrmConfiguration {
    async init() {
        this.decoratorService.registerPropertyHandler(decorator_1.ORM_MODEL_KEY, (propertyName, meta, instance) => {
            const dataSource = this.dataSourceManager.getDataSource(meta.connectionName ||
                this.dataSourceManager.getDataSourceNameByModel(meta.modelKey) ||
                this.dataSourceManager.getDefaultDataSourceName());
            if (!dataSource) {
                throw new core_1.MidwayCommonError(`DataSource ${meta.connectionName} not found with current model ${meta.modelKey}, please check it.`);
            }
            const type = (0, core_1.getPropertyType)(instance, propertyName);
            if (type.originDesign === typeorm_1.Repository) {
                return dataSource.getRepository(meta.modelKey);
            }
            else if (type.originDesign === typeorm_1.TreeRepository) {
                return dataSource.getTreeRepository(meta.modelKey);
            }
            else if (type.originDesign === typeorm_1.MongoRepository) {
                return dataSource.getMongoRepository(meta.modelKey);
            }
            else {
                return dataSource.getRepository(meta.modelKey);
            }
        });
        this.decoratorService.registerPropertyHandler(decorator_1.ORM_DATA_SOURCE_KEY, (propertyName, meta) => {
            return this.dataSourceManager.getDataSource(meta.dataSourceName ||
                this.dataSourceManager.getDefaultDataSourceName());
        });
    }
    async onReady(container) {
        (0, typeorm_1.useContainer)(container);
        this.dataSourceManager = await container.getAsync(dataSourceManager_1.TypeORMDataSourceManager);
    }
    async onStop(container) {
        const dataSourceManager = await container.getAsync(dataSourceManager_1.TypeORMDataSourceManager);
        await dataSourceManager.stop();
    }
};
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], OrmConfiguration.prototype, "app", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayDecoratorService)
], OrmConfiguration.prototype, "decoratorService", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], OrmConfiguration.prototype, "init", null);
OrmConfiguration = __decorate([
    (0, core_1.Configuration)({
        importConfigs: [
            {
                default: {
                    typeorm: {},
                    midwayLogger: {
                        clients: {
                            typeormLogger: {
                                lazyLoad: true,
                                fileLogName: 'midway-typeorm.log',
                                enableError: false,
                                level: 'info',
                            },
                        },
                    },
                },
            },
        ],
        namespace: 'typeorm',
    })
], OrmConfiguration);
exports.OrmConfiguration = OrmConfiguration;
//# sourceMappingURL=configuration.js.map