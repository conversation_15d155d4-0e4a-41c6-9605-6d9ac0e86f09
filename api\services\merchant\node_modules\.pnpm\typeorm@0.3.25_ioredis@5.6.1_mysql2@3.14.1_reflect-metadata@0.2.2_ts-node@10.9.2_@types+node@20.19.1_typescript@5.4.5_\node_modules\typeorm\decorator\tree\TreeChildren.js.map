{"version": 3, "sources": ["../../src/decorator/tree/TreeChildren.ts"], "names": [], "mappings": ";;AAQA,oCA8BC;AAtCD,2CAAsD;AAItD;;;GAGG;AACH,SAAgB,YAAY,CAAC,OAI5B;IACG,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAqB,CAAA;QAE7C,4CAA4C;QAC5C,MAAM,aAAa,GACf,OAAO,IAAK,OAAe,CAAC,WAAW;YACnC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC;YAC1D,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,MAAM,GACR,CAAC,aAAa;YACV,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ;YACtC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC;YACnD,KAAK,CAAA;QAET,oCAAoC;QACpC,IAAA,gCAAsB,GAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACpC,cAAc,EAAE,IAAI;YACpB,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,MAAM,EAAE,MAAM;YACd,YAAY,EAAE,aAAa;YAC3B,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,WAAW;YAC9B,OAAO,EAAE,OAAO;SACK,CAAC,CAAA;IAC9B,CAAC,CAAA;AACL,CAAC", "file": "TreeChildren.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationMetadataArgs } from \"../../metadata-args/RelationMetadataArgs\"\nimport { RelationOptions } from \"../options/RelationOptions\"\n\n/**\n * Marks a entity property as a children of the tree.\n * \"Tree children\" will contain all children (bind) of this entity.\n */\nexport function TreeChildren(options?: {\n    cascade?:\n        | boolean\n        | (\"insert\" | \"update\" | \"remove\" | \"soft-remove\" | \"recover\")[]\n}): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        if (!options) options = {} as RelationOptions\n\n        // now try to determine it its lazy relation\n        const reflectedType =\n            Reflect && (Reflect as any).getMetadata\n                ? Reflect.getMetadata(\"design:type\", object, propertyName)\n                : undefined\n        const isLazy =\n            (reflectedType &&\n                typeof reflectedType.name === \"string\" &&\n                reflectedType.name.toLowerCase() === \"promise\") ||\n            false\n\n        // add one-to-many relation for this\n        getMetadataArgsStorage().relations.push({\n            isTreeChildren: true,\n            target: object.constructor,\n            propertyName: propertyName,\n            isLazy: isLazy,\n            relationType: \"one-to-many\",\n            type: () => object.constructor,\n            options: options,\n        } as RelationMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}