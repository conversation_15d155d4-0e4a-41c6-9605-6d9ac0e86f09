"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayInformationService = void 0;
const interface_1 = require("../interface");
const util_1 = require("../util");
const path_1 = require("path");
const decorator_1 = require("../decorator");
const fs_1 = require("fs");
let MidwayInformationService = class MidwayInformationService {
    init() {
        if (this.baseDir) {
            if (!this.appDir) {
                this.appDir = (0, path_1.dirname)(this.baseDir);
            }
            const pkgPath = (0, path_1.join)(this.appDir, 'package.json');
            if ((0, fs_1.existsSync)(pkgPath)) {
                const content = (0, fs_1.readFileSync)(pkgPath, {
                    encoding: 'utf-8',
                });
                this.pkg = JSON.parse(content);
            }
            else {
                this.pkg = {};
            }
        }
        else {
            this.pkg = {};
        }
    }
    getAppDir() {
        return this.appDir;
    }
    getBaseDir() {
        return this.baseDir;
    }
    getHome() {
        return (0, util_1.getUserHome)();
    }
    getPkg() {
        return this.pkg;
    }
    getProjectName() {
        var _a;
        return ((_a = this.pkg) === null || _a === void 0 ? void 0 : _a['name']) || '';
    }
    getRoot() {
        const isDevelopmentEnv = (0, util_1.isDevelopmentEnvironment)((0, util_1.getCurrentEnvironment)());
        return isDevelopmentEnv ? this.getAppDir() : this.getHome();
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", String)
], MidwayInformationService.prototype, "appDir", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", String)
], MidwayInformationService.prototype, "baseDir", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MidwayInformationService.prototype, "init", null);
MidwayInformationService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayInformationService);
exports.MidwayInformationService = MidwayInformationService;
//# sourceMappingURL=informationService.js.map