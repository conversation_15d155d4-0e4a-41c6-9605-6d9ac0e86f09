import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';
import { FinanceUserDrawEntity } from '../user/draw';

/**
 * 提现申请
 */
@Entity('finance_apply_draw')
export class FinanceApplyDrawEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '提现金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '提现方式 0-银行卡 1-微信 2-支付宝' })
  type: number;

  @Column({
    comment: '手续费',
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
  })
  fee: number;

  @Column({ comment: '提现状态 0-申请中 1-成功 2-失败', default: 0 })
  status: number;

  @Column({ comment: '备注', nullable: true })
  remark: string;

  @Column({ comment: '提现账号', type: 'json' })
  account: FinanceUserDrawEntity;
}
