import { IMidwayContext } from '@midwayjs/core';
import { TranslateOptions } from './interface';
export declare class MidwayI18nServiceSingleton {
    private i18nConfig;
    private localeTextMap;
    private defaultLocale;
    private fallbackMatch;
    private localeMatchCache;
    protected init(): Promise<void>;
    /**
     * add a language text mapping
     * @param locale
     * @param localeTextMapping
     */
    addLocale(locale: string, localeTextMapping: Record<string, any>): void;
    /**
     * translate a message
     * @param message
     * @param options
     */
    translate(message: string, options?: TranslateOptions): any;
    /**
     * get locale string by find fallback and default, ignore match message
     * @param locale
     * @param group
     */
    getAvailableLocale(locale: string, group?: string): any;
    /**
     * get available local in locale text map, include fallbacks
     * @param locale
     */
    hasAvailableLocale(locale: string): boolean;
    /**
     * get mapping by locale
     * @param locale
     * @param group
     */
    getLocaleMapping(locale: string, group?: string): any;
    /**
     * get current default language
     */
    getDefaultLocale(): string;
    private getLocaleMappingText;
}
export declare class MidwayI18nService {
    protected i18nServiceSingleton: MidwayI18nServiceSingleton;
    ctx: IMidwayContext;
    translate(message: string, options?: TranslateOptions): any;
    /**
     * add a language text mapping
     * @param locale
     * @param localeTextMapping
     */
    addLocale(locale: string, localeTextMapping: Record<string, any>): void;
    /**
     * get mapping by lang
     * @param locale
     * @param group
     */
    getLocaleMapping(locale: any, group?: string): any;
    /**
     * get current default language
     */
    getDefaultLocale(): string;
    /**
     * save current context lang to flag, middleware will be set it to cookie
     */
    saveRequestLocale(locale?: string): void;
    /**
     * get locale string by find fallback and default, ignore match message
     * @param locale
     * @param group
     */
    getAvailableLocale(locale: string, group?: string): any;
    /**
     * get available local in locale text map, include fallbacks
     * @param locale
     */
    hasAvailableLocale(locale: string): boolean;
}
//# sourceMappingURL=i18nService.d.ts.map