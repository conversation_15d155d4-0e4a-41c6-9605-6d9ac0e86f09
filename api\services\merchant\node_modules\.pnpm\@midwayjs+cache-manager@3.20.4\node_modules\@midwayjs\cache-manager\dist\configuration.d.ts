import { ILifeCycle, IMidwayContainer, MidwayDecoratorService } from '@midwayjs/core';
import { CachingFactory } from './factory';
export declare function getClassMethodDefaultCacheKey(target: any, methodName: string): string;
export declare class CacheConfiguration implements ILifeCycle {
    decoratorService: MidwayDecoratorService;
    cacheService: CachingFactory;
    onReady(container: IMidwayContainer): Promise<void>;
}
//# sourceMappingURL=configuration.d.ts.map