@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules\midway-version\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules\midway-version\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules\midway-version\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules\midway-version\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\midway-version@1.4.0\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\midway-version\bin\midway-version.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\midway-version\bin\midway-version.js" %*
)
