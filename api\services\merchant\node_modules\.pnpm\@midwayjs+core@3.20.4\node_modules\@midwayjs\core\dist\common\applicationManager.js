"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayApplicationManager = void 0;
const interface_1 = require("../interface");
const decorator_1 = require("../decorator");
let MidwayApplicationManager = class MidwayApplicationManager {
    constructor() {
        this.globalFrameworkMap = new Map();
        this.globalFrameworkTypeMap = new WeakMap();
    }
    addFramework(namespace, framework) {
        this.globalFrameworkMap.set(namespace, framework);
        if (framework['getFrameworkType']) {
            this.globalFrameworkTypeMap.set(framework['getFrameworkType'](), framework);
        }
    }
    getFramework(namespaceOrFrameworkType) {
        if (typeof namespaceOrFrameworkType === 'string') {
            if (this.globalFrameworkMap.has(namespaceOrFrameworkType)) {
                return this.globalFrameworkMap.get(namespaceOrFrameworkType);
            }
        }
        else {
            if (this.globalFrameworkTypeMap.has(namespaceOrFrameworkType)) {
                return this.globalFrameworkTypeMap.get(namespaceOrFrameworkType);
            }
        }
    }
    getApplication(namespaceOrFrameworkType) {
        if (typeof namespaceOrFrameworkType === 'string') {
            if (this.globalFrameworkMap.has(namespaceOrFrameworkType)) {
                return this.globalFrameworkMap
                    .get(namespaceOrFrameworkType)
                    .getApplication();
            }
        }
        else {
            if (this.globalFrameworkTypeMap.has(namespaceOrFrameworkType)) {
                return this.globalFrameworkTypeMap
                    .get(namespaceOrFrameworkType)
                    .getApplication();
            }
        }
    }
    getApplications(namespaces) {
        if (!namespaces) {
            return Array.from(this.globalFrameworkMap.values())
                .map(framework => {
                return framework.getApplication();
            })
                .filter(app => {
                return !!app;
            });
        }
        else {
            return namespaces
                .map(namespace => {
                return this.getApplication(namespace);
            })
                .filter(app => {
                return !!app;
            });
        }
    }
};
MidwayApplicationManager = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayApplicationManager);
exports.MidwayApplicationManager = MidwayApplicationManager;
//# sourceMappingURL=applicationManager.js.map