export declare class ContextSession {
    private ctx;
    private app;
    private opts;
    private session;
    private externalKey;
    private prevHash;
    store: any;
    /**
     * context session constructor
     * @api public
     */
    constructor(ctx: any, opts: any);
    /**
     * internal logic of `ctx.session`
     * @return {Session} session object
     *
     * @api public
     */
    get(): any;
    /**
     * internal logic of `ctx.session=`
     * @param {Object} val session object
     *
     * @api public
     */
    set(val: any): void;
    /**
     * init session from external store
     * will be called in the front of session middleware
     *
     * @api public
     */
    initFromExternal(): Promise<void>;
    /**
     * init session from cookie
     * @api private
     */
    initFromCookie(): void;
    /**
     * verify session(expired or )
     * @param  {Object} value session object
     * @param  {Object} key session externalKey(optional)
     * @return {Boolean} valid
     * @api private
     */
    valid(value: any, key?: any): boolean;
    /**
     * @param {String} event event name
     * @param {Object} data event data
     * @api private
     */
    emit(event: any, data: any): void;
    /**
     * create a new session and attach to ctx.sess
     *
     * @param {Object} [val] session data
     * @param {String} [externalKey] session external key
     * @api private
     */
    create(val?: any, externalKey?: any): void;
    /**
     * Commit the session changes or removal.
     *
     * @api public
     */
    commit({ save, regenerate }?: {
        save?: boolean;
        regenerate?: boolean;
    }): Promise<void>;
    _shouldSaveSession(): "" | "changed" | "rolling" | "renew";
    /**
     * remove session
     * @api private
     */
    remove(): Promise<void>;
    /**
     * save session
     * @api private
     */
    save(changed: any): Promise<void>;
}
//# sourceMappingURL=context.d.ts.map