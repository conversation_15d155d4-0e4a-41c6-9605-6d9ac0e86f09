function convertTZ(date,tzString){return new Date(date.toLocaleString("en-US",{timeZone:tzString}))}function CronDate(date,timezone){this.timezone=timezone;if(date&&date instanceof Date){this.fromDate(date)}else if(date===void 0){this.fromDate(new Date)}else if(date&&typeof date==="string"){this.fromString(date)}else if(date instanceof CronDate){this.fromCronDate(date)}else{throw new TypeError("CronDate: Invalid type ("+typeof date+") passed as parameter to CronDate constructor")}}CronDate.prototype.fromDate=function(date){if(this.timezone){date=convertTZ(date,this.timezone)}this.milliseconds=date.getMilliseconds();this.seconds=date.getSeconds();this.minutes=date.getMinutes();this.hours=date.getHours();this.days=date.getDate();this.months=date.getMonth();this.years=date.getFullYear()};CronDate.prototype.fromCronDate=function(date){this.timezone=date.timezone;this.milliseconds=date.milliseconds;this.seconds=date.seconds;this.minutes=date.minutes;this.hours=date.hours;this.days=date.days;this.months=date.months;this.years=date.years};CronDate.prototype.apply=function(){const newDate=new Date(this.years,this.months,this.days,this.hours,this.minutes,this.seconds,this.milliseconds);this.milliseconds=newDate.getMilliseconds();this.seconds=newDate.getSeconds();this.minutes=newDate.getMinutes();this.hours=newDate.getHours();this.days=newDate.getDate();this.months=newDate.getMonth();this.years=newDate.getFullYear()};CronDate.prototype.fromString=function(str){const parsedDate=this.parseISOLocal(str);if(isNaN(parsedDate)){throw new TypeError("CronDate: Provided string value for CronDate could not be parsed as date.")}this.fromDate(parsedDate)};CronDate.prototype.increment=function(pattern,rerun){if(!rerun){this.seconds+=1}this.milliseconds=0;const origTime=this.getTime(),findNext=(target,pattern,offset,override)=>{const startPos=override===void 0?this[target]+offset:0+offset;for(let i=startPos;i<pattern[target].length;i++){if(pattern[target][i]){if(target==="days"&&pattern.lastDayOfMonth){let baseDate=this.getDate(true);baseDate.setDate(i-offset+1);if(baseDate.getMonth()!==this["months"]){this[target]=i-offset;return true}}else{this[target]=i-offset;return true}}}return false},resetPrevious=offset=>{while(doing+offset>=0){findNext(toDo[doing+offset][0],pattern,toDo[doing+offset][2],0);doing--}};const toDo=[["seconds","minutes",0],["minutes","hours",0],["hours","days",0],["days","months",-1],["months","years",0]];let doing=0;while(doing<5){let currentValue=this[toDo[doing][0]];if(!findNext(toDo[doing][0],pattern,toDo[doing][2])){this[toDo[doing][1]]++;resetPrevious(0)}else if(currentValue!==this[toDo[doing][0]]){resetPrevious(-1)}if(this.years>=4e3){return null}doing++}while(!pattern.daysOfWeek[this.getDate(true).getDay()]){this.days+=1;doing=2;resetPrevious()}if(origTime!=this.getTime()){this.apply();return this.increment(pattern,true)}else{return this}};CronDate.prototype.getDate=function(internal){const targetDate=new Date(this.years,this.months,this.days,this.hours,this.minutes,this.seconds,this.milliseconds);if(internal||!this.timezone){return targetDate}else{const offset=convertTZ(targetDate,this.timezone).getTime()-targetDate.getTime();return new Date(targetDate.getTime()-offset)}};CronDate.prototype.getTime=function(internal){return this.getDate(internal).getTime()};CronDate.prototype.parseISOLocal=function(dateTimeString){const dateTimeStringSplit=dateTimeString.split(/\D/);if(dateTimeStringSplit.length<6){return NaN}const year=parseInt(dateTimeStringSplit[0],10),month=parseInt(dateTimeStringSplit[1],10),day=parseInt(dateTimeStringSplit[2],10),hour=parseInt(dateTimeStringSplit[3],10),minute=parseInt(dateTimeStringSplit[4],10),second=parseInt(dateTimeStringSplit[5],10);if(isNaN(year)||isNaN(month)||isNaN(day)||isNaN(hour)||isNaN(minute)||isNaN(second)){return NaN}else{let generatedDate;if(dateTimeString.indexOf("Z")>0){generatedDate=new Date(Date.UTC(year,month-1,day,hour,minute,second));if(year==generatedDate.getUTCFullYear()&&month==generatedDate.getUTCMonth()+1&&day==generatedDate.getUTCDate()&&hour==generatedDate.getUTCHours()&&minute==generatedDate.getUTCMinutes()&&second==generatedDate.getUTCSeconds()){return generatedDate}else{return NaN}}else{generatedDate=new Date(year,month-1,day,hour,minute,second);if(year==generatedDate.getFullYear()&&month==generatedDate.getMonth()+1&&day==generatedDate.getDate()&&hour==generatedDate.getHours()&&minute==generatedDate.getMinutes()&&second==generatedDate.getSeconds()){return generatedDate}else{return NaN}}}};function CronPattern(pattern,timezone){this.pattern=pattern;this.timezone=timezone;this.seconds=Array(60).fill(0);this.minutes=Array(60).fill(0);this.hours=Array(24).fill(0);this.days=Array(31).fill(0);this.months=Array(12).fill(0);this.daysOfWeek=Array(8).fill(0);this.lastDayOfMonth=false;this.parse()}CronPattern.prototype.parse=function(){if(!(typeof this.pattern==="string"||this.pattern.constructor===String)){throw new TypeError("CronPattern: Pattern has to be of type string.")}const parts=this.pattern.trim().replace(/\s+/g," ").split(" ");if(parts.length<5||parts.length>6){throw new TypeError("CronPattern: invalid configuration format ('"+this.pattern+"'), exacly five or six space separated parts required.")}if(parts.length===5){parts.unshift("0")}if(parts[3].toUpperCase()=="L"){parts[3]="28,29,30,31";this.lastDayOfMonth=true}parts[4]=this.replaceAlphaMonths(parts[4]);parts[5]=this.replaceAlphaDays(parts[5]);let initDate=new CronDate(new Date,this.timezone).getDate(true);parts[0]=parts[0].replace("?",initDate.getSeconds());parts[1]=parts[1].replace("?",initDate.getMinutes());parts[2]=parts[2].replace("?",initDate.getHours());parts[3]=parts[3].replace("?",initDate.getDate());parts[4]=parts[4].replace("?",initDate.getMonth()+1);parts[5]=parts[5].replace("?",initDate.getDay());this.throwAtIllegalCharacters(parts);this.partToArray("seconds",parts[0],0);this.partToArray("minutes",parts[1],0);this.partToArray("hours",parts[2],0);this.partToArray("days",parts[3],-1);this.partToArray("months",parts[4],-1);this.partToArray("daysOfWeek",parts[5],0);if(this.daysOfWeek[7]){this.daysOfWeek[0]=1}};CronPattern.prototype.partToArray=function(type,conf,valueIndexOffset,recursed){const arr=this[type];if(conf==="*"){for(let i=0;i<arr.length;i++){arr[i]=1}return}const split=conf.split(",");if(split.length>1){for(let i=0;i<split.length;i++){this.partToArray(type,split[i],valueIndexOffset,true)}}else if(conf.indexOf("-")!==-1&&conf.indexOf("/")!==-1){if(recursed)throw new Error("CronPattern: Range with stepping cannot coexist with ,");this.handleRangeWithStepping(conf,type,valueIndexOffset)}else if(conf.indexOf("-")!==-1){if(recursed)throw new Error("CronPattern: Range with stepping cannot coexist with ,");this.handleRange(conf,type,valueIndexOffset)}else if(conf.indexOf("/")!==-1){if(recursed)throw new Error("CronPattern: Range with stepping cannot coexist with ,");this.handleStepping(conf,type,valueIndexOffset)}else{this.handleNumber(conf,type,valueIndexOffset)}};CronPattern.prototype.throwAtIllegalCharacters=function(parts){const reValidCron=/[^/*0-9,-]+/;for(let i=0;i<parts.length;i++){if(reValidCron.test(parts[i])){throw new TypeError("CronPattern: configuration entry "+i+" ("+parts[i]+") contains illegal characters.")}}};CronPattern.prototype.handleNumber=function(conf,type,valueIndexOffset){const i=parseInt(conf,10)+valueIndexOffset;if(i<0||i>=this[type].length){throw new TypeError("CronPattern: "+type+" value out of range: '"+conf+"'")}this[type][i]=1};CronPattern.prototype.handleRangeWithStepping=function(conf,type,valueIndexOffset){const matches=conf.match(/^(\d+)-(\d+)\/(\d+)$/);if(matches===null)throw new TypeError("CronPattern: Syntax error, illegal range with stepping: '"+conf+"'");let[,lower,upper,steps]=matches;lower=parseInt(lower,10)+valueIndexOffset;upper=parseInt(upper,10)+valueIndexOffset;steps=parseInt(steps,10);if(isNaN(lower))throw new TypeError("CronPattern: Syntax error, illegal lower range (NaN)");if(isNaN(upper))throw new TypeError("CronPattern: Syntax error, illegal upper range (NaN)");if(isNaN(steps))throw new TypeError("CronPattern: Syntax error, illegal stepping: (NaN)");if(steps===0)throw new TypeError("CronPattern: Syntax error, illegal stepping: 0");if(steps>this[type].length)throw new TypeError("CronPattern: Syntax error, steps cannot be greater than maximum value of part ("+this[type].length+")");if(lower<0||upper>=this[type].length)throw new TypeError("CronPattern: Value out of range: '"+conf+"'");if(lower>upper)throw new TypeError("CronPattern: From value is larger than to value: '"+conf+"'");for(let i=lower;i<=upper;i+=steps){this[type][i]=1}};CronPattern.prototype.handleRange=function(conf,type,valueIndexOffset){const split=conf.split("-");if(split.length!==2){throw new TypeError("CronPattern: Syntax error, illegal range: '"+conf+"'")}const lower=parseInt(split[0],10)+valueIndexOffset,upper=parseInt(split[1],10)+valueIndexOffset;if(isNaN(lower)){throw new TypeError("CronPattern: Syntax error, illegal lower range (NaN)")}else if(isNaN(upper)){throw new TypeError("CronPattern: Syntax error, illegal upper range (NaN)")}if(lower<0||upper>=this[type].length){throw new TypeError("CronPattern: Value out of range: '"+conf+"'")}if(lower>upper){throw new TypeError("CronPattern: From value is larger than to value: '"+conf+"'")}for(let i=lower;i<=upper;i++){this[type][i]=1}};CronPattern.prototype.handleStepping=function(conf,type){const split=conf.split("/");if(split.length!==2){throw new TypeError("CronPattern: Syntax error, illegal stepping: '"+conf+"'")}let start=0;if(split[0]!=="*"){start=parseInt(split[0],10)}const steps=parseInt(split[1],10);if(isNaN(steps))throw new TypeError("CronPattern: Syntax error, illegal stepping: (NaN)");if(steps===0)throw new TypeError("CronPattern: Syntax error, illegal stepping: 0");if(steps>this[type].length)throw new TypeError("CronPattern: Syntax error, steps cannot be greater than maximum value of part ("+this[type].length+")");for(let i=start;i<this[type].length;i+=steps){this[type][i]=1}};CronPattern.prototype.replaceAlphaDays=function(conf){return conf.replace(/sun/gi,"0").replace(/mon/gi,"1").replace(/tue/gi,"2").replace(/wed/gi,"3").replace(/thu/gi,"4").replace(/fri/gi,"5").replace(/sat/gi,"6")};CronPattern.prototype.replaceAlphaMonths=function(conf){return conf.replace(/jan/gi,"1").replace(/feb/gi,"2").replace(/mar/gi,"3").replace(/apr/gi,"4").replace(/may/gi,"5").replace(/jun/gi,"6").replace(/jul/gi,"7").replace(/aug/gi,"8").replace(/sep/gi,"9").replace(/oct/gi,"10").replace(/nov/gi,"11").replace(/dec/gi,"12")};const maxDelay=Math.pow(2,32-1)-1;function Cron(pattern,options,func){if(!(this instanceof Cron)){return new Cron(pattern,options,func)}if(typeof options==="function"){func=options;options=void 0}this.options=this.processOptions(options);if(pattern&&pattern instanceof Date){this.once=new CronDate(pattern,this.options.timezone)}else if(pattern&&typeof pattern==="string"&&pattern.indexOf(":")>0){this.once=new CronDate(pattern,this.options.timezone)}else{this.pattern=new CronPattern(pattern,this.options.timezone)}if(func!==void 0){this.fn=func;this.schedule()}return this}Cron.prototype.processOptions=function(options){if(options===void 0){options={}}options.paused=options.paused===void 0?false:options.paused;options.maxRuns=options.maxRuns===void 0?Infinity:options.maxRuns;options.catch=options.catch===void 0?false:options.catch;options.kill=false;if(options.startAt){options.startAt=new CronDate(options.startAt,options.timezone)}if(options.stopAt){options.stopAt=new CronDate(options.stopAt,options.timezone)}return options};Cron.prototype.next=function(prev){prev=new CronDate(prev,this.options.timezone);const next=this._next(prev);return next?next.getDate():null};Cron.prototype.enumerate=function(n,previous){let enumeration=[];while(n--&&(previous=this.next(previous))){enumeration.push(previous)}return enumeration};Cron.prototype.running=function(){const msLeft=this.msToNext(this.previousrun);const running=!this.options.paused&&this.fn!==void 0;return msLeft!==null&&running};Cron.prototype.previous=function(){return this.previousrun?this.previousrun.getDate():null};Cron.prototype._next=function(prev){if(this.options.startAt&&prev&&prev.getTime(true)<this.options.startAt.getTime(true)){prev=this.options.startAt}const nextRun=this.once||new CronDate(prev,this.options.timezone).increment(this.pattern);if(this.once&&this.once.getTime(true)<=prev.getTime(true)){return null}else if(nextRun===null||this.options.maxRuns<=0||this.options.kill||this.options.stopAt&&nextRun.getTime(true)>=this.options.stopAt.getTime(true)){return null}else{return nextRun}};Cron.prototype.msToNext=function(prev){prev=new CronDate(prev,this.options.timezone);const next=this._next(prev);if(next){return next.getTime(true)-prev.getTime(true)}else{return null}};Cron.prototype.stop=function(){this.options.kill=true;if(this.currentTimeout){clearTimeout(this.currentTimeout)}};Cron.prototype.pause=function(){return(this.options.paused=true)&&!this.options.kill};Cron.prototype.resume=function(){return!(this.options.paused=false)&&!this.options.kill};Cron.prototype.schedule=function(func){if(func&&this.fn){throw new Error("Cron: It is not allowed to schedule two functions using the same Croner instance.")}else if(func){this.fn=func}let waitMs=this.msToNext(this.previousrun);if(waitMs===null)return this;if(waitMs>maxDelay){waitMs=maxDelay}this.currentTimeout=setTimeout(()=>{if(waitMs!==maxDelay&&!this.options.paused){this.options.maxRuns--;if(this.options.catch){try{this.fn(this,this.options.context)}catch(_e){}}else{this.fn(this,this.options.context)}this.previousrun=new CronDate(void 0,this.options.timezone)}this.schedule()},waitMs);return this};export{Cron,Cron as default};