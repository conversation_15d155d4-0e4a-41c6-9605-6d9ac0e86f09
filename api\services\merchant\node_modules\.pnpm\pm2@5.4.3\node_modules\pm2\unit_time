[V] test/programmatic/path_resolution.mocha.js succeeded and took 2 seconds
[V] test/programmatic/modules.mocha.js succeeded and took 4 seconds
[V] test/programmatic/instances.mocha.js succeeded and took 6 seconds
[V] test/programmatic/reload-locker.mocha.js succeeded and took 4 seconds
[V] test/programmatic/filter_env.mocha.js succeeded and took 2 seconds
[V] test/programmatic/resurect_state.mocha.js succeeded and took 1 seconds
[V] test/programmatic/programmatic.js succeeded and took 12 seconds
[V] test/programmatic/namespace.mocha.js succeeded and took 3 seconds
[V] test/programmatic/auto_restart.mocha.js succeeded and took 5 seconds
[V] test/programmatic/containerizer.mocha.js succeeded and took 1 seconds
[V] test/programmatic/api.mocha.js succeeded and took 9 seconds
[V] test/programmatic/lazy_api.mocha.js succeeded and took 2 seconds
[V] test/programmatic/exp_backoff_restart_delay.mocha.js succeeded and took 7 seconds
[V] test/programmatic/api.backward.compatibility.mocha.js succeeded and took 3 seconds
[V] test/programmatic/custom_action.mocha.js succeeded and took 4 seconds
[V] test/programmatic/logs.js succeeded and took 7 seconds
[V] test/programmatic/watcher.js succeeded and took 2 seconds
[V] test/programmatic/max_memory_limit.js succeeded and took 14 seconds
[V] test/programmatic/cluster.mocha.js succeeded and took 17 seconds
[V] test/programmatic/graceful.mocha.js succeeded and took 10 seconds
[V] test/programmatic/inside.mocha.js succeeded and took 6 seconds
[V] test/programmatic/misc_commands.js succeeded and took 4 seconds
[V] test/programmatic/signals.js succeeded and took 5 seconds
[V] test/programmatic/send_data_process.mocha.js succeeded and took 1 seconds
[V] test/programmatic/json_validation.mocha.js succeeded and took 1 seconds
[V] test/programmatic/env_switching.js succeeded and took 12 seconds
[V] test/programmatic/configuration.mocha.js succeeded and took 1 seconds
[V] test/programmatic/id.mocha.js succeeded and took 2 seconds
[V] test/programmatic/god.mocha.js succeeded and took 5 seconds
[V] test/programmatic/dump.mocha.js succeeded and took 2 seconds
[V] test/programmatic/common.mocha.js succeeded and took 1 seconds
[V] test/programmatic/issues/json_env_passing_4080.mocha.js succeeded and took 2 seconds
[V] test/interface/bus.spec.mocha.js succeeded and took 3 seconds
[V] test/interface/bus.fork.spec.mocha.js succeeded and took 5 seconds
[V] test/interface/utility.mocha.js succeeded and took 1 seconds
