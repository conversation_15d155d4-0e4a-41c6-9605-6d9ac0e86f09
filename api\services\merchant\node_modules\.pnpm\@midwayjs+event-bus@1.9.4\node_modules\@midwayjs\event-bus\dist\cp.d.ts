/// <reference types="node" />
import { ChildProcessEventBusOptions, Message } from './interface';
import { ChildProcess } from 'child_process';
import { AbstractEventBus } from './base';
export declare class ChildProcessEventBus extends AbstractEventBus<ChildProcess> {
    protected options: ChildProcessEventBusOptions;
    protected workerSubscribeMessage(subscribeMessageHandler: (message: Message) => void): void;
    protected workerListenMessage(worker: ChildProcess, subscribeMessageHandler: (message: Message) => void): void;
    protected workerSendMessage(message: Message): void;
    protected mainSendMessage(worker: ChildProcess, message: Message): void;
    isMain(): boolean;
    isWorker(): boolean;
    getWorkerId(worker?: ChildProcess): string;
}
//# sourceMappingURL=cp.d.ts.map