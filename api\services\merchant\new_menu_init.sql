-- Art Design Pro 商户微服务菜单初始化脚本
-- 基于前端 asyncRoutes.ts 的菜单结构设计
-- 创建时间: 2024-12-19

USE `merchant_service_db`;

-- ======================================
-- 第一步：清空现有菜单数据
-- ======================================
DELETE FROM merchant_sys_menu;

-- 重置自增ID
ALTER TABLE merchant_sys_menu AUTO_INCREMENT = 1;

-- ======================================
-- 第二步：插入基础菜单数据
-- ======================================

-- 一级菜单（目录）
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
-- 仪表盘模块
(1, 0, '仪表盘', '/dashboard', '', 0, '&#xe721;', 1, 1, 1, NOW(), NOW()),
-- 商户管理模块（核心业务）
(2, 0, '商户管理', '/merchant', '', 0, '&#xe7b4;', 2, 1, 1, NOW(), NOW()),
-- 系统管理模块
(3, 0, '系统管理', '/system', '', 0, '&#xe7b9;', 3, 1, 1, NOW(), NOW()),
-- 异常页面模块
(4, 0, '异常页面', '/exception', '', 0, '&#xe820;', 4, 1, 1, NOW(), NOW());

-- ======================================
-- 第三步：插入二级菜单数据
-- ======================================

-- 仪表盘子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(10, 1, '控制台', '/dashboard/console', 'dashboard:console', 1, '', 1, 0, 1, NOW(), NOW()),
(11, 1, '分析页', '/dashboard/analysis', 'dashboard:analysis', 1, '', 2, 0, 1, NOW(), NOW()),
(12, 1, '电商数据', '/dashboard/ecommerce', 'dashboard:ecommerce', 1, '', 3, 0, 1, NOW(), NOW());

-- 商户管理子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(20, 2, '商户入驻', '/merchant/settle-in', 'merchant:settle-in', 1, '', 1, 1, 1, NOW(), NOW()),
(21, 2, '非遗人认证', '/merchant/heritage-auth', 'merchant:heritage-auth', 1, '', 2, 1, 1, NOW(), NOW()),
(22, 2, '个人商户', '/merchant/personal', 'merchant:personal', 1, '', 3, 1, 1, NOW(), NOW()),
(23, 2, '企业商户', '/merchant/company', 'merchant:company', 1, '', 4, 1, 1, NOW(), NOW());

-- 系统管理子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(30, 3, '用户管理', '/system/user', 'system:user', 1, '', 1, 1, 1, NOW(), NOW()),
(31, 3, '角色管理', '/system/role', 'system:role', 1, '', 2, 1, 1, NOW(), NOW()),
(32, 3, '菜单管理', '/system/menu', 'system:menu', 1, '', 3, 1, 1, NOW(), NOW()),
(33, 3, '个人中心', '/system/user-center', 'system:user-center', 1, '', 4, 1, 0, NOW(), NOW());

-- 异常页面子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(40, 4, '403页面', '/exception/403', 'exception:403', 1, '', 1, 1, 1, NOW(), NOW()),
(41, 4, '404页面', '/exception/404', 'exception:404', 1, '', 2, 1, 1, NOW(), NOW()),
(42, 4, '500页面', '/exception/500', 'exception:500', 1, '', 3, 1, 1, NOW(), NOW());

-- ======================================
-- 第四步：插入按钮权限数据（type=2）
-- ======================================

-- 商户入驻管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(200, 20, '查看', '', 'merchant:settle-in:list', 2, '', 1, 1, 0, NOW(), NOW()),
(201, 20, '审核', '', 'merchant:settle-in:audit', 2, '', 2, 1, 0, NOW(), NOW()),
(202, 20, '编辑', '', 'merchant:settle-in:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(203, 20, '删除', '', 'merchant:settle-in:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- 非遗人认证管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(210, 21, '查看', '', 'merchant:heritage-auth:list', 2, '', 1, 1, 0, NOW(), NOW()),
(211, 21, '审核', '', 'merchant:heritage-auth:audit', 2, '', 2, 1, 0, NOW(), NOW()),
(212, 21, '编辑', '', 'merchant:heritage-auth:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(213, 21, '删除', '', 'merchant:heritage-auth:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- 个人商户管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(220, 22, '查看', '', 'merchant:personal:list', 2, '', 1, 1, 0, NOW(), NOW()),
(221, 22, '审核', '', 'merchant:personal:audit', 2, '', 2, 1, 0, NOW(), NOW()),
(222, 22, '编辑', '', 'merchant:personal:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(223, 22, '删除', '', 'merchant:personal:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- 企业商户管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(230, 23, '查看', '', 'merchant:company:list', 2, '', 1, 1, 0, NOW(), NOW()),
(231, 23, '审核', '', 'merchant:company:audit', 2, '', 2, 1, 0, NOW(), NOW()),
(232, 23, '编辑', '', 'merchant:company:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(233, 23, '删除', '', 'merchant:company:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- 用户管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(300, 30, '查看', '', 'system:user:list', 2, '', 1, 1, 0, NOW(), NOW()),
(301, 30, '新增', '', 'system:user:add', 2, '', 2, 1, 0, NOW(), NOW()),
(302, 30, '编辑', '', 'system:user:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(303, 30, '删除', '', 'system:user:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- 角色管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(310, 31, '查看', '', 'system:role:list', 2, '', 1, 1, 0, NOW(), NOW()),
(311, 31, '新增', '', 'system:role:add', 2, '', 2, 1, 0, NOW(), NOW()),
(312, 31, '编辑', '', 'system:role:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(313, 31, '删除', '', 'system:role:delete', 2, '', 4, 1, 0, NOW(), NOW()),
(314, 31, '分配权限', '', 'system:role:updatePerms', 2, '', 5, 1, 0, NOW(), NOW());

-- 菜单管理权限
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(320, 32, '查看', '', 'system:menu:list', 2, '', 1, 1, 0, NOW(), NOW()),
(321, 32, '新增', '', 'system:menu:add', 2, '', 2, 1, 0, NOW(), NOW()),
(322, 32, '编辑', '', 'system:menu:edit', 2, '', 3, 1, 0, NOW(), NOW()),
(323, 32, '删除', '', 'system:menu:delete', 2, '', 4, 1, 0, NOW(), NOW());

-- ======================================
-- 完成菜单初始化
-- ======================================

-- 查询验证：显示菜单层级结构
SELECT 
    CASE 
        WHEN parentId = 0 THEN CONCAT('├── ', name)
        ELSE CONCAT('│   ├── ', name)
    END AS menu_tree,
    id,
    router,
    perms,
    CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' WHEN 2 THEN '按钮' END AS type_name,
    CASE isShow WHEN 1 THEN '显示' ELSE '隐藏' END AS show_status
FROM merchant_sys_menu 
ORDER BY parentId, orderNum, id;

-- 统计信息
SELECT 
    '菜单初始化完成' AS status,
    COUNT(*) AS total_count,
    SUM(CASE WHEN type = 0 THEN 1 ELSE 0 END) AS directory_count,
    SUM(CASE WHEN type = 1 THEN 1 ELSE 0 END) AS menu_count,
    SUM(CASE WHEN type = 2 THEN 1 ELSE 0 END) AS button_count
FROM merchant_sys_menu; 