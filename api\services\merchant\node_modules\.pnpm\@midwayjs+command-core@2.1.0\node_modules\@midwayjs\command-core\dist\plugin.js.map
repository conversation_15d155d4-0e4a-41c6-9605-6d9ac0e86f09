{"version": 3, "file": "plugin.js", "sourceRoot": "", "sources": ["../src/plugin.ts"], "names": [], "mappings": ";;;AAMA,+BAA+B;AAC/B,2BAA8C;AAC9C,iDAAiC;AACjC,+BAAmC;AACnC,MAAa,UAAU;IAOrB,YAAY,IAAmB,EAAE,OAAY;QAFrC,SAAI,GAAW,IAAI,CAAC,OAAO,EAAE,CAAC;QAGpC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;IAClB,CAAC;IAEM,OAAO;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC/B,CAAC;IAEM,QAAQ,CAAC,GAAW,EAAE,KAAU,EAAE,aAAuB;QAC9D,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACnD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC;IAChD,CAAC;IAEM,QAAQ,CAAC,GAAW,EAAE,KAAc;QACzC,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAC7D,CAAC;IAED,qBAAqB,CAAC,IAAY,EAAE,OAAgB;QAClD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE;YACzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,GAAG,EAAE,CAAC;SAC3C;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,OAAO,IAAI,GAAG,CAAC;IAC9D,CAAC;CACF;AAjCD,gCAiCC;AAED,WAAW;AACJ,MAAM,qBAAqB,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE;IAC3D,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,GAAG,OAAO,IAAI,EAAE,CAAC;IACvE,OAAO,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QACD,WAAW;QACX,IAAI,MAAM,CAAC,OAAO,EAAE;YAClB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;gBACjC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC1C,OAAO,KAAK,CAAC;iBACd;aACF;iBAAM,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;SACF;QACD,QAAQ;QACR,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnB,IAAI,MAAM,CAAC,QAAQ,KAAK,QAAQ,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;SACF;QACD,IAAI;YACF,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,GAAG,cAAc,CAAC,CAAC;YACrD,IAAI,UAAU,CAAC,KAAK,EAAE;gBACpB,WAAW;gBACX,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE;oBAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;wBAC3C,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;4BACpD,OAAO,KAAK,CAAC;yBACd;qBACF;yBAAM,IAAI,UAAU,CAAC,KAAK,CAAC,OAAO,KAAK,OAAO,EAAE;wBAC/C,OAAO,KAAK,CAAC;qBACd;iBACF;gBACD,WAAW;gBACX,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE;oBACzB,MAAM,QAAQ,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,CAAC,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE;wBACzB,OAAO,KAAK,CAAC;qBACd;iBACF;gBACD,OAAO,IAAI,CAAC;aACb;SACF;QAAC,WAAM;YACN,EAAE;SACH;QACD,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAjDW,QAAA,qBAAqB,yBAiDhC;AAEF,eAAe;AACR,MAAM,cAAc,GAAG,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,EAAE;IAC1D,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;IACnD,MAAM,SAAS,GAAG,EAAE,CAAC;IACrB,KAAK,MAAM,UAAU,IAAI,UAAU,EAAE;QACnC,IAAI,GAAG,CAAC;QACR,IAAI;YACF,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC5B;QAAC,WAAM;YACN,IAAI,cAAc,EAAE;gBAClB,SAAS;aACV;YACD,IAAI,WAAW,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;YAC/D,qCAAqC;YACrC,IAAI,CAAC,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE;gBAC5B,MAAM,cAAc,CAAC,UAAU,CAAC,GAAG,EAAE;oBACnC,GAAG;oBACH,GAAG;iBACJ,CAAC,CAAC;aACJ;YACD,qBAAqB;YACrB,MAAM,UAAU,GAAG,IAAA,cAAO,EAAC,WAAW,EAAE,cAAc,CAAC,CAAC;YACxD,IAAI,CAAC,IAAA,eAAU,EAAC,UAAU,CAAC,EAAE;gBAC3B,SAAS;aACV;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5D,IAAI,GAAG,CAAC,IAAI,EAAE;gBACZ,WAAW,GAAG,IAAA,cAAO,EAAC,WAAW,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;aAC9C;YACD,IAAI;gBACF,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACV,eAAe;aAChB;SACF;QACD,IAAI,CAAC,GAAG,EAAE;YACR,SAAS;SACV;QACD,IAAI,UAAU,CAAC,IAAI,EAAE;YACnB,IAAI,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBACxB,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;aACtC;SACF;aAAM;YACL,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACrB;KACF;IACD,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AA9CW,QAAA,cAAc,kBA8CzB;AAEF,MAAM,cAAc,GAAG,KAAK,EAC1B,OAAe,EACf,OAGC,EACD,EAAE;IACF,OAAO,CAAC,GAAG,CACT,0BAA0B,OAAO,0DAA0D,CAC5F,CAAC;IACF,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;QAChB,OAAO,CAAC,GAAG,CACT,qFAAqF,CACtF,CAAC;KACH;IACD,MAAM,IAAI,GAAG,IAAI,uBAAI,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;IAC9C,IAAI,CAAC,KAAK,EAAE,CAAC;IACb,IAAI;QACF,MAAM,IAAA,gBAAU,EAAC;YACf,UAAU,EAAE,OAAO;YACnB,QAAQ,EAAE,OAAO,CAAC,GAAG;YACrB,OAAO,EAAE,OAAO,CAAC,GAAG;YACpB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;KACJ;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,CAAC,KAAK,CACX,0BAA0B,OAAO,oBAAoB,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,OAAO,EAAE,CAClE,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,qCAAqC,OAAO,GAAG,CAAC,CAAC;KAC9D;IACD,IAAI,CAAC,IAAI,EAAE,CAAC;AACd,CAAC,CAAC"}