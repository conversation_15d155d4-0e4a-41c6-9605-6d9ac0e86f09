module.exports = [
  'ADD CONSTRAINT',
  'ADD',
  'ALL',
  'ALTER COLUMN',
  'ALTER TABLE',
  'ALTER',
  'AND',
  'ANY',
  'AS',
  'ASC',
  'AUTO_INCREMENT',
  '<PERSON><PERSON><PERSON><PERSON> DATABASE',
  '<PERSON><PERSON><PERSON>',
  'BETWEEN',
  'BINARY',
  'BLOB',
  'BY',
  'CASCADE',
  'CASE',
  'CHAR',
  'CHECK',
  'COLUMN',
  'COMMIT',
  'CONSTRAINT',
  'CREATE DATABASE',
  'CREATE INDEX',
  'CREATE OR REPLACE VIEW',
  'CREATE PROCEDURE',
  'CREATE TABLE',
  'CREATE UNIQUE INDEX',
  'CREATE VIEW',
  'CREATE',
  'CURRENT_DATE',
  'CURRENT_TIME',
  'DATABASE',
  'DATETIME',
  'DECIMAL',
  'DECLARE',
  'DEFAULT',
  'DELETE',
  'DESC',
  'DISTINCT',
  'DROP COLUMN',
  'DROP CONSTRAINT',
  'DROP DATABASE',
  'DROP DEFAULT',
  'DROP INDEX',
  'DROP TABLE',
  'DROP VIEW',
  'DROP',
  'EACH',
  'ELSE',
  'ELSEIF',
  'END',
  'ENGINE',
  'EXEC',
  'EXISTS',
  'FALSE',
  'FOR',
  'FOREIGN KEY',
  'FROM',
  'FULL OUTER JOIN',
  'GROUP BY',
  'GROUP',
  'HAVING',
  'IF',
  'IFNULL',
  'ILIKE',
  'IN',
  'INDEX_LIST',
  'INDEX',
  'INNER JOIN',
  'INSERT INTO SELECT',
  'INSERT INTO',
  'INSERT',
  'INTEGER',
  'INTERVAL',
  'INTO',
  'IS NOT NULL',
  'IS NULL',
  'IS',
  'JOIN',
  'KEY',
  'KEYS',
  'LEADING',
  'LEFT JOIN',
  'LEFT',
  'LIKE',
  'LIMIT',
  'LONGTEXT',
  'MATCH',
  'NOT NULL',
  'NOT',
  'NULL',
  'ON',
  'OPTION',
  'OR',
  'ORDER BY',
  'ORDER',
  'OUT',
  'OUTER JOIN',
  'OUTER',
  'OVERLAPS',
  'PRAGMA',
  'PRIMARY KEY',
  'PRIMARY',
  'PRINT',
  'PROCEDURE',
  'REFERENCES',
  'REPLACE',
  'RETURNING',
  'RIGHT JOIN',
  'RIGHT',
  'ROWNUM',
  'SELECT DISTINCT',
  'SELECT INTO',
  'SELECT TOP',
  'SELECT',
  'SET',
  'SHOW',
  'TABLE',
  'TEXT',
  'THEN',
  'TIMESTAMP',
  'TINYBLOB',
  'TINYINT',
  'TINYTEXT',
  'TO',
  'TOP',
  'TRAILING',
  'TRUE',
  'TRUNCATE TABLE',
  'UNION ALL',
  'UNION',
  'UNIQUE',
  'UNSIGNED',
  'UPDATE',
  'VALUES',
  'VARBINARY',
  'VARCHAR',
  'VIEW',
  'WHEN',
  'WHERE',
  'WITH'
];
