{"version": 3, "file": "ftp.js", "sourceRoot": "", "sources": ["../src/ftp.ts"], "names": [], "mappings": ";;;;;;AAAA,yCAAkD;AAClD,mCAA+C;AAC/C,+BAAyC;AACzC,kDAAgC;AAChC,0DAAuC;AACvC,gEAA6C;AAG7C,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,aAAa,CAAC,CAAC;AAUzC;;GAEG;AACI,MAAM,GAAG,GAA+B,KAAK,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,EAAE;IACvE,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;IACvB,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAClD,IAAI,YAA8B,CAAC;IAEnC,IAAI,CAAC,QAAQ,EAAE;QACd,MAAM,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC;KACtC;IAED,MAAM,MAAM,GAAG,IAAI,kBAAM,EAAE,CAAC;IAE5B,IAAI;QACH,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,IAAI,IAAI,WAAW,CAAC;QACrD,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;QACjD,MAAM,IAAI,GAAG,GAAG,CAAC,QAAQ;YACxB,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;YAClC,CAAC,CAAC,SAAS,CAAC;QACb,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ;YAC5B,CAAC,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,CAAC;YAClC,CAAC,CAAC,SAAS,CAAC;QAEb,MAAM,MAAM,CAAC,MAAM,CAAC;YACnB,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,QAAQ;YACR,GAAG,IAAI;SACP,CAAC,CAAC;QAEH,sDAAsD;QACtD,sEAAsE;QACtE,IAAI;YACH,YAAY,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;SAC9C;QAAC,OAAO,GAAY,EAAE;YACtB,yCAAyC;YACzC,IAAK,GAAwB,CAAC,IAAI,KAAK,GAAG,EAAE;gBAC3C,MAAM,IAAI,kBAAa,EAAE,CAAC;aAC1B;SACD;QAED,IAAI,CAAC,YAAY,EAAE;YAClB,+DAA+D;YAC/D,gEAAgE;YAChE,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,IAAA,cAAO,EAAC,QAAQ,CAAC,CAAC,CAAC;YAElD,qDAAqD;YACrD,MAAM,IAAI,GAAG,IAAA,eAAQ,EAAC,QAAQ,CAAC,CAAC;YAChC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;YAChD,IAAI,KAAK,EAAE;gBACV,YAAY,GAAG,KAAK,CAAC,UAAU,CAAC;aAChC;SACD;QAED,IAAI,YAAY,EAAE;YACjB,IAAI,aAAa,EAAE,EAAE;gBACpB,MAAM,IAAI,qBAAgB,EAAE,CAAC;aAC7B;SACD;aAAM;YACN,MAAM,IAAI,kBAAa,EAAE,CAAC;SAC1B;QAED,MAAM,MAAM,GAAG,IAAI,oBAAW,EAAE,CAAC;QACjC,MAAM,EAAE,GAAG,MAAqB,CAAC;QACjC,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;YACnD,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,CAAC,KAAK,EAAE,CAAC;QAChB,CAAC,CAAC,CAAC;QACH,EAAE,CAAC,YAAY,GAAG,YAAY,CAAC;QAC/B,OAAO,EAAE,CAAC;KACV;IAAC,OAAO,GAAG,EAAE;QACb,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,GAAG,CAAC;KACV;IAED,uEAAuE;IACvE,SAAS,aAAa;QACrB,IAAI,KAAK,EAAE,YAAY,IAAI,YAAY,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,YAAY,KAAK,CAAC,YAAY,CAAC;SAC7C;QACD,OAAO,KAAK,CAAC;IACd,CAAC;AACF,CAAC,CAAC;AAjFW,QAAA,GAAG,OAiFd"}