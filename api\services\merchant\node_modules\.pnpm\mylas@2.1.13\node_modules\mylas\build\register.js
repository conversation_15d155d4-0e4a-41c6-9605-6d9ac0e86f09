"use strict";var _chunkM3H36CX2js = require('./chunk-M3H36CX2.js');String.load=_chunkM3H36CX2js.e.load;String.save=_chunkM3H36CX2js.e.save;String.loadS=_chunkM3H36CX2js.e.loadS;String.saveS=_chunkM3H36CX2js.e.saveS;String.loadW=_chunkM3H36CX2js.e.loadW;String.saveW=_chunkM3H36CX2js.e.saveW;String.prototype.save=async function(i,l){await _chunkM3H36CX2js.e.save(i,String(this),l)};String.prototype.saveS=function(i){_chunkM3H36CX2js.e.saveS(i,String(this))};String.prototype.saveW=async function(i,l){await _chunkM3H36CX2js.e.saveW(i,String(this),l)};JSON.load=_chunkM3H36CX2js.f.load;JSON.save=_chunkM3H36CX2js.f.save;JSON.loadS=_chunkM3H36CX2js.f.loadS;JSON.saveS=_chunkM3H36CX2js.f.saveS;JSON.loadW=_chunkM3H36CX2js.f.loadW;JSON.saveW=_chunkM3H36CX2js.f.saveW;
