var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
define(["require", "exports", "../bindings/binding", "../constants/error_msgs", "../constants/literal_types", "../constants/metadata_keys", "../planning/metadata_reader", "../planning/planner", "../resolution/resolver", "../syntax/binding_to_syntax", "../utils/async", "../utils/id", "../utils/serialization", "./container_snapshot", "./lookup", "./module_activation_store"], function (require, exports, binding_1, ERROR_MSGS, literal_types_1, METADATA_KEY, metadata_reader_1, planner_1, resolver_1, binding_to_syntax_1, async_1, id_1, serialization_1, container_snapshot_1, lookup_1, module_activation_store_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Container = void 0;
    ERROR_MSGS = __importStar(ERROR_MSGS);
    METADATA_KEY = __importStar(METADATA_KEY);
    var Container = (function () {
        function Container(containerOptions) {
            var options = containerOptions || {};
            if (typeof options !== "object") {
                throw new Error("" + ERROR_MSGS.CONTAINER_OPTIONS_MUST_BE_AN_OBJECT);
            }
            if (options.defaultScope === undefined) {
                options.defaultScope = literal_types_1.BindingScopeEnum.Transient;
            }
            else if (options.defaultScope !== literal_types_1.BindingScopeEnum.Singleton &&
                options.defaultScope !== literal_types_1.BindingScopeEnum.Transient &&
                options.defaultScope !== literal_types_1.BindingScopeEnum.Request) {
                throw new Error("" + ERROR_MSGS.CONTAINER_OPTIONS_INVALID_DEFAULT_SCOPE);
            }
            if (options.autoBindInjectable === undefined) {
                options.autoBindInjectable = false;
            }
            else if (typeof options.autoBindInjectable !== "boolean") {
                throw new Error("" + ERROR_MSGS.CONTAINER_OPTIONS_INVALID_AUTO_BIND_INJECTABLE);
            }
            if (options.skipBaseClassChecks === undefined) {
                options.skipBaseClassChecks = false;
            }
            else if (typeof options.skipBaseClassChecks !== "boolean") {
                throw new Error("" + ERROR_MSGS.CONTAINER_OPTIONS_INVALID_SKIP_BASE_CHECK);
            }
            this.options = {
                autoBindInjectable: options.autoBindInjectable,
                defaultScope: options.defaultScope,
                skipBaseClassChecks: options.skipBaseClassChecks
            };
            this.id = (0, id_1.id)();
            this._bindingDictionary = new lookup_1.Lookup();
            this._snapshots = [];
            this._middleware = null;
            this._activations = new lookup_1.Lookup();
            this._deactivations = new lookup_1.Lookup();
            this.parent = null;
            this._metadataReader = new metadata_reader_1.MetadataReader();
            this._moduleActivationStore = new module_activation_store_1.ModuleActivationStore();
        }
        Container.merge = function (container1, container2) {
            var containers = [];
            for (var _i = 2; _i < arguments.length; _i++) {
                containers[_i - 2] = arguments[_i];
            }
            var container = new Container();
            var targetContainers = __spreadArray([container1, container2], containers, true).map(function (targetContainer) { return (0, planner_1.getBindingDictionary)(targetContainer); });
            var bindingDictionary = (0, planner_1.getBindingDictionary)(container);
            function copyDictionary(origin, destination) {
                origin.traverse(function (_key, value) {
                    value.forEach(function (binding) {
                        destination.add(binding.serviceIdentifier, binding.clone());
                    });
                });
            }
            targetContainers.forEach(function (targetBindingDictionary) {
                copyDictionary(targetBindingDictionary, bindingDictionary);
            });
            return container;
        };
        Container.prototype.load = function () {
            var modules = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                modules[_i] = arguments[_i];
            }
            var getHelpers = this._getContainerModuleHelpersFactory();
            for (var _a = 0, modules_1 = modules; _a < modules_1.length; _a++) {
                var currentModule = modules_1[_a];
                var containerModuleHelpers = getHelpers(currentModule.id);
                currentModule.registry(containerModuleHelpers.bindFunction, containerModuleHelpers.unbindFunction, containerModuleHelpers.isboundFunction, containerModuleHelpers.rebindFunction, containerModuleHelpers.unbindAsyncFunction, containerModuleHelpers.onActivationFunction, containerModuleHelpers.onDeactivationFunction);
            }
        };
        Container.prototype.loadAsync = function () {
            var modules = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                modules[_i] = arguments[_i];
            }
            return __awaiter(this, void 0, void 0, function () {
                var getHelpers, _a, modules_2, currentModule, containerModuleHelpers;
                return __generator(this, function (_b) {
                    switch (_b.label) {
                        case 0:
                            getHelpers = this._getContainerModuleHelpersFactory();
                            _a = 0, modules_2 = modules;
                            _b.label = 1;
                        case 1:
                            if (!(_a < modules_2.length)) return [3, 4];
                            currentModule = modules_2[_a];
                            containerModuleHelpers = getHelpers(currentModule.id);
                            return [4, currentModule.registry(containerModuleHelpers.bindFunction, containerModuleHelpers.unbindFunction, containerModuleHelpers.isboundFunction, containerModuleHelpers.rebindFunction, containerModuleHelpers.unbindAsyncFunction, containerModuleHelpers.onActivationFunction, containerModuleHelpers.onDeactivationFunction)];
                        case 2:
                            _b.sent();
                            _b.label = 3;
                        case 3:
                            _a++;
                            return [3, 1];
                        case 4: return [2];
                    }
                });
            });
        };
        Container.prototype.unload = function () {
            var _this = this;
            var modules = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                modules[_i] = arguments[_i];
            }
            modules.forEach(function (module) {
                var deactivations = _this._removeModuleBindings(module.id);
                _this._deactivateSingletons(deactivations);
                _this._removeModuleHandlers(module.id);
            });
        };
        Container.prototype.unloadAsync = function () {
            var modules = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                modules[_i] = arguments[_i];
            }
            return __awaiter(this, void 0, void 0, function () {
                var _a, modules_3, module_1, deactivations;
                return __generator(this, function (_b) {
                    switch (_b.label) {
                        case 0:
                            _a = 0, modules_3 = modules;
                            _b.label = 1;
                        case 1:
                            if (!(_a < modules_3.length)) return [3, 4];
                            module_1 = modules_3[_a];
                            deactivations = this._removeModuleBindings(module_1.id);
                            return [4, this._deactivateSingletonsAsync(deactivations)];
                        case 2:
                            _b.sent();
                            this._removeModuleHandlers(module_1.id);
                            _b.label = 3;
                        case 3:
                            _a++;
                            return [3, 1];
                        case 4: return [2];
                    }
                });
            });
        };
        Container.prototype.bind = function (serviceIdentifier) {
            var scope = this.options.defaultScope || literal_types_1.BindingScopeEnum.Transient;
            var binding = new binding_1.Binding(serviceIdentifier, scope);
            this._bindingDictionary.add(serviceIdentifier, binding);
            return new binding_to_syntax_1.BindingToSyntax(binding);
        };
        Container.prototype.rebind = function (serviceIdentifier) {
            this.unbind(serviceIdentifier);
            return this.bind(serviceIdentifier);
        };
        Container.prototype.rebindAsync = function (serviceIdentifier) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4, this.unbindAsync(serviceIdentifier)];
                        case 1:
                            _a.sent();
                            return [2, this.bind(serviceIdentifier)];
                    }
                });
            });
        };
        Container.prototype.unbind = function (serviceIdentifier) {
            if (this._bindingDictionary.hasKey(serviceIdentifier)) {
                var bindings = this._bindingDictionary.get(serviceIdentifier);
                this._deactivateSingletons(bindings);
            }
            this._removeServiceFromDictionary(serviceIdentifier);
        };
        Container.prototype.unbindAsync = function (serviceIdentifier) {
            return __awaiter(this, void 0, void 0, function () {
                var bindings;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (!this._bindingDictionary.hasKey(serviceIdentifier)) return [3, 2];
                            bindings = this._bindingDictionary.get(serviceIdentifier);
                            return [4, this._deactivateSingletonsAsync(bindings)];
                        case 1:
                            _a.sent();
                            _a.label = 2;
                        case 2:
                            this._removeServiceFromDictionary(serviceIdentifier);
                            return [2];
                    }
                });
            });
        };
        Container.prototype.unbindAll = function () {
            var _this = this;
            this._bindingDictionary.traverse(function (_key, value) {
                _this._deactivateSingletons(value);
            });
            this._bindingDictionary = new lookup_1.Lookup();
        };
        Container.prototype.unbindAllAsync = function () {
            return __awaiter(this, void 0, void 0, function () {
                var promises;
                var _this = this;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            promises = [];
                            this._bindingDictionary.traverse(function (_key, value) {
                                promises.push(_this._deactivateSingletonsAsync(value));
                            });
                            return [4, Promise.all(promises)];
                        case 1:
                            _a.sent();
                            this._bindingDictionary = new lookup_1.Lookup();
                            return [2];
                    }
                });
            });
        };
        Container.prototype.onActivation = function (serviceIdentifier, onActivation) {
            this._activations.add(serviceIdentifier, onActivation);
        };
        Container.prototype.onDeactivation = function (serviceIdentifier, onDeactivation) {
            this._deactivations.add(serviceIdentifier, onDeactivation);
        };
        Container.prototype.isBound = function (serviceIdentifier) {
            var bound = this._bindingDictionary.hasKey(serviceIdentifier);
            if (!bound && this.parent) {
                bound = this.parent.isBound(serviceIdentifier);
            }
            return bound;
        };
        Container.prototype.isCurrentBound = function (serviceIdentifier) {
            return this._bindingDictionary.hasKey(serviceIdentifier);
        };
        Container.prototype.isBoundNamed = function (serviceIdentifier, named) {
            return this.isBoundTagged(serviceIdentifier, METADATA_KEY.NAMED_TAG, named);
        };
        Container.prototype.isBoundTagged = function (serviceIdentifier, key, value) {
            var bound = false;
            if (this._bindingDictionary.hasKey(serviceIdentifier)) {
                var bindings = this._bindingDictionary.get(serviceIdentifier);
                var request_1 = (0, planner_1.createMockRequest)(this, serviceIdentifier, key, value);
                bound = bindings.some(function (b) { return b.constraint(request_1); });
            }
            if (!bound && this.parent) {
                bound = this.parent.isBoundTagged(serviceIdentifier, key, value);
            }
            return bound;
        };
        Container.prototype.snapshot = function () {
            this._snapshots.push(container_snapshot_1.ContainerSnapshot.of(this._bindingDictionary.clone(), this._middleware, this._activations.clone(), this._deactivations.clone(), this._moduleActivationStore.clone()));
        };
        Container.prototype.restore = function () {
            var snapshot = this._snapshots.pop();
            if (snapshot === undefined) {
                throw new Error(ERROR_MSGS.NO_MORE_SNAPSHOTS_AVAILABLE);
            }
            this._bindingDictionary = snapshot.bindings;
            this._activations = snapshot.activations;
            this._deactivations = snapshot.deactivations;
            this._middleware = snapshot.middleware;
            this._moduleActivationStore = snapshot.moduleActivationStore;
        };
        Container.prototype.createChild = function (containerOptions) {
            var child = new Container(containerOptions || this.options);
            child.parent = this;
            return child;
        };
        Container.prototype.applyMiddleware = function () {
            var middlewares = [];
            for (var _i = 0; _i < arguments.length; _i++) {
                middlewares[_i] = arguments[_i];
            }
            var initial = (this._middleware) ? this._middleware : this._planAndResolve();
            this._middleware = middlewares.reduce(function (prev, curr) { return curr(prev); }, initial);
        };
        Container.prototype.applyCustomMetadataReader = function (metadataReader) {
            this._metadataReader = metadataReader;
        };
        Container.prototype.get = function (serviceIdentifier) {
            var getArgs = this._getNotAllArgs(serviceIdentifier, false);
            return this._getButThrowIfAsync(getArgs);
        };
        Container.prototype.getAsync = function (serviceIdentifier) {
            return __awaiter(this, void 0, void 0, function () {
                var getArgs;
                return __generator(this, function (_a) {
                    getArgs = this._getNotAllArgs(serviceIdentifier, false);
                    return [2, this._get(getArgs)];
                });
            });
        };
        Container.prototype.getTagged = function (serviceIdentifier, key, value) {
            var getArgs = this._getNotAllArgs(serviceIdentifier, false, key, value);
            return this._getButThrowIfAsync(getArgs);
        };
        Container.prototype.getTaggedAsync = function (serviceIdentifier, key, value) {
            return __awaiter(this, void 0, void 0, function () {
                var getArgs;
                return __generator(this, function (_a) {
                    getArgs = this._getNotAllArgs(serviceIdentifier, false, key, value);
                    return [2, this._get(getArgs)];
                });
            });
        };
        Container.prototype.getNamed = function (serviceIdentifier, named) {
            return this.getTagged(serviceIdentifier, METADATA_KEY.NAMED_TAG, named);
        };
        Container.prototype.getNamedAsync = function (serviceIdentifier, named) {
            return this.getTaggedAsync(serviceIdentifier, METADATA_KEY.NAMED_TAG, named);
        };
        Container.prototype.getAll = function (serviceIdentifier) {
            var getArgs = this._getAllArgs(serviceIdentifier);
            return this._getButThrowIfAsync(getArgs);
        };
        Container.prototype.getAllAsync = function (serviceIdentifier) {
            var getArgs = this._getAllArgs(serviceIdentifier);
            return this._getAll(getArgs);
        };
        Container.prototype.getAllTagged = function (serviceIdentifier, key, value) {
            var getArgs = this._getNotAllArgs(serviceIdentifier, true, key, value);
            return this._getButThrowIfAsync(getArgs);
        };
        Container.prototype.getAllTaggedAsync = function (serviceIdentifier, key, value) {
            var getArgs = this._getNotAllArgs(serviceIdentifier, true, key, value);
            return this._getAll(getArgs);
        };
        Container.prototype.getAllNamed = function (serviceIdentifier, named) {
            return this.getAllTagged(serviceIdentifier, METADATA_KEY.NAMED_TAG, named);
        };
        Container.prototype.getAllNamedAsync = function (serviceIdentifier, named) {
            return this.getAllTaggedAsync(serviceIdentifier, METADATA_KEY.NAMED_TAG, named);
        };
        Container.prototype.resolve = function (constructorFunction) {
            var isBound = this.isBound(constructorFunction);
            if (!isBound) {
                this.bind(constructorFunction).toSelf();
            }
            var resolved = this.get(constructorFunction);
            if (!isBound) {
                this.unbind(constructorFunction);
            }
            return resolved;
        };
        Container.prototype._preDestroy = function (constructor, instance) {
            if (Reflect.hasMetadata(METADATA_KEY.PRE_DESTROY, constructor)) {
                var data = Reflect.getMetadata(METADATA_KEY.PRE_DESTROY, constructor);
                return instance[data.value]();
            }
        };
        Container.prototype._removeModuleHandlers = function (moduleId) {
            var moduleActivationsHandlers = this._moduleActivationStore.remove(moduleId);
            this._activations.removeIntersection(moduleActivationsHandlers.onActivations);
            this._deactivations.removeIntersection(moduleActivationsHandlers.onDeactivations);
        };
        Container.prototype._removeModuleBindings = function (moduleId) {
            return this._bindingDictionary.removeByCondition(function (binding) { return binding.moduleId === moduleId; });
        };
        Container.prototype._deactivate = function (binding, instance) {
            var _this = this;
            var constructor = Object.getPrototypeOf(instance).constructor;
            try {
                if (this._deactivations.hasKey(binding.serviceIdentifier)) {
                    var result = this._deactivateContainer(instance, this._deactivations.get(binding.serviceIdentifier).values());
                    if ((0, async_1.isPromise)(result)) {
                        return this._handleDeactivationError(result.then(function () { return _this._propagateContainerDeactivationThenBindingAndPreDestroyAsync(binding, instance, constructor); }), constructor);
                    }
                }
                var propagateDeactivationResult = this._propagateContainerDeactivationThenBindingAndPreDestroy(binding, instance, constructor);
                if ((0, async_1.isPromise)(propagateDeactivationResult)) {
                    return this._handleDeactivationError(propagateDeactivationResult, constructor);
                }
            }
            catch (ex) {
                throw new Error(ERROR_MSGS.ON_DEACTIVATION_ERROR(constructor.name, ex.message));
            }
        };
        Container.prototype._handleDeactivationError = function (asyncResult, constructor) {
            return __awaiter(this, void 0, void 0, function () {
                var ex_1;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            _a.trys.push([0, 2, , 3]);
                            return [4, asyncResult];
                        case 1:
                            _a.sent();
                            return [3, 3];
                        case 2:
                            ex_1 = _a.sent();
                            throw new Error(ERROR_MSGS.ON_DEACTIVATION_ERROR(constructor.name, ex_1.message));
                        case 3: return [2];
                    }
                });
            });
        };
        Container.prototype._deactivateContainer = function (instance, deactivationsIterator) {
            var _this = this;
            var deactivation = deactivationsIterator.next();
            while (deactivation.value) {
                var result = deactivation.value(instance);
                if ((0, async_1.isPromise)(result)) {
                    return result.then(function () {
                        return _this._deactivateContainerAsync(instance, deactivationsIterator);
                    });
                }
                deactivation = deactivationsIterator.next();
            }
        };
        Container.prototype._deactivateContainerAsync = function (instance, deactivationsIterator) {
            return __awaiter(this, void 0, void 0, function () {
                var deactivation;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            deactivation = deactivationsIterator.next();
                            _a.label = 1;
                        case 1:
                            if (!deactivation.value) return [3, 3];
                            return [4, deactivation.value(instance)];
                        case 2:
                            _a.sent();
                            deactivation = deactivationsIterator.next();
                            return [3, 1];
                        case 3: return [2];
                    }
                });
            });
        };
        Container.prototype._getContainerModuleHelpersFactory = function () {
            var _this = this;
            var setModuleId = function (bindingToSyntax, moduleId) {
                bindingToSyntax._binding.moduleId = moduleId;
            };
            var getBindFunction = function (moduleId) {
                return function (serviceIdentifier) {
                    var bindingToSyntax = _this.bind(serviceIdentifier);
                    setModuleId(bindingToSyntax, moduleId);
                    return bindingToSyntax;
                };
            };
            var getUnbindFunction = function () {
                return function (serviceIdentifier) {
                    return _this.unbind(serviceIdentifier);
                };
            };
            var getUnbindAsyncFunction = function () {
                return function (serviceIdentifier) {
                    return _this.unbindAsync(serviceIdentifier);
                };
            };
            var getIsboundFunction = function () {
                return function (serviceIdentifier) {
                    return _this.isBound(serviceIdentifier);
                };
            };
            var getRebindFunction = function (moduleId) {
                return function (serviceIdentifier) {
                    var bindingToSyntax = _this.rebind(serviceIdentifier);
                    setModuleId(bindingToSyntax, moduleId);
                    return bindingToSyntax;
                };
            };
            var getOnActivationFunction = function (moduleId) {
                return function (serviceIdentifier, onActivation) {
                    _this._moduleActivationStore.addActivation(moduleId, serviceIdentifier, onActivation);
                    _this.onActivation(serviceIdentifier, onActivation);
                };
            };
            var getOnDeactivationFunction = function (moduleId) {
                return function (serviceIdentifier, onDeactivation) {
                    _this._moduleActivationStore.addDeactivation(moduleId, serviceIdentifier, onDeactivation);
                    _this.onDeactivation(serviceIdentifier, onDeactivation);
                };
            };
            return function (mId) { return ({
                bindFunction: getBindFunction(mId),
                isboundFunction: getIsboundFunction(),
                onActivationFunction: getOnActivationFunction(mId),
                onDeactivationFunction: getOnDeactivationFunction(mId),
                rebindFunction: getRebindFunction(mId),
                unbindFunction: getUnbindFunction(),
                unbindAsyncFunction: getUnbindAsyncFunction()
            }); };
        };
        Container.prototype._getAll = function (getArgs) {
            return Promise.all(this._get(getArgs));
        };
        Container.prototype._get = function (getArgs) {
            var planAndResolveArgs = __assign(__assign({}, getArgs), { contextInterceptor: function (context) { return context; }, targetType: literal_types_1.TargetTypeEnum.Variable });
            if (this._middleware) {
                var middlewareResult = this._middleware(planAndResolveArgs);
                if (middlewareResult === undefined || middlewareResult === null) {
                    throw new Error(ERROR_MSGS.INVALID_MIDDLEWARE_RETURN);
                }
                return middlewareResult;
            }
            return this._planAndResolve()(planAndResolveArgs);
        };
        Container.prototype._getButThrowIfAsync = function (getArgs) {
            var result = this._get(getArgs);
            if ((0, async_1.isPromiseOrContainsPromise)(result)) {
                throw new Error(ERROR_MSGS.LAZY_IN_SYNC(getArgs.serviceIdentifier));
            }
            return result;
        };
        Container.prototype._getAllArgs = function (serviceIdentifier) {
            var getAllArgs = {
                avoidConstraints: true,
                isMultiInject: true,
                serviceIdentifier: serviceIdentifier,
            };
            return getAllArgs;
        };
        Container.prototype._getNotAllArgs = function (serviceIdentifier, isMultiInject, key, value) {
            var getNotAllArgs = {
                avoidConstraints: false,
                isMultiInject: isMultiInject,
                serviceIdentifier: serviceIdentifier,
                key: key,
                value: value,
            };
            return getNotAllArgs;
        };
        Container.prototype._planAndResolve = function () {
            var _this = this;
            return function (args) {
                var context = (0, planner_1.plan)(_this._metadataReader, _this, args.isMultiInject, args.targetType, args.serviceIdentifier, args.key, args.value, args.avoidConstraints);
                context = args.contextInterceptor(context);
                var result = (0, resolver_1.resolve)(context);
                return result;
            };
        };
        Container.prototype._deactivateIfSingleton = function (binding) {
            var _this = this;
            if (!binding.activated) {
                return;
            }
            if ((0, async_1.isPromise)(binding.cache)) {
                return binding.cache.then(function (resolved) { return _this._deactivate(binding, resolved); });
            }
            return this._deactivate(binding, binding.cache);
        };
        Container.prototype._deactivateSingletons = function (bindings) {
            for (var _i = 0, bindings_1 = bindings; _i < bindings_1.length; _i++) {
                var binding = bindings_1[_i];
                var result = this._deactivateIfSingleton(binding);
                if ((0, async_1.isPromise)(result)) {
                    throw new Error(ERROR_MSGS.ASYNC_UNBIND_REQUIRED);
                }
            }
        };
        Container.prototype._deactivateSingletonsAsync = function (bindings) {
            return __awaiter(this, void 0, void 0, function () {
                var _this = this;
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4, Promise.all(bindings.map(function (b) { return _this._deactivateIfSingleton(b); }))];
                        case 1:
                            _a.sent();
                            return [2];
                    }
                });
            });
        };
        Container.prototype._propagateContainerDeactivationThenBindingAndPreDestroy = function (binding, instance, constructor) {
            if (this.parent) {
                return this._deactivate.bind(this.parent)(binding, instance);
            }
            else {
                return this._bindingDeactivationAndPreDestroy(binding, instance, constructor);
            }
        };
        Container.prototype._propagateContainerDeactivationThenBindingAndPreDestroyAsync = function (binding, instance, constructor) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (!this.parent) return [3, 2];
                            return [4, this._deactivate.bind(this.parent)(binding, instance)];
                        case 1:
                            _a.sent();
                            return [3, 4];
                        case 2: return [4, this._bindingDeactivationAndPreDestroyAsync(binding, instance, constructor)];
                        case 3:
                            _a.sent();
                            _a.label = 4;
                        case 4: return [2];
                    }
                });
            });
        };
        Container.prototype._removeServiceFromDictionary = function (serviceIdentifier) {
            try {
                this._bindingDictionary.remove(serviceIdentifier);
            }
            catch (e) {
                throw new Error(ERROR_MSGS.CANNOT_UNBIND + " " + (0, serialization_1.getServiceIdentifierAsString)(serviceIdentifier));
            }
        };
        Container.prototype._bindingDeactivationAndPreDestroy = function (binding, instance, constructor) {
            var _this = this;
            if (typeof binding.onDeactivation === "function") {
                var result = binding.onDeactivation(instance);
                if ((0, async_1.isPromise)(result)) {
                    return result.then(function () { return _this._preDestroy(constructor, instance); });
                }
            }
            return this._preDestroy(constructor, instance);
        };
        Container.prototype._bindingDeactivationAndPreDestroyAsync = function (binding, instance, constructor) {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0:
                            if (!(typeof binding.onDeactivation === "function")) return [3, 2];
                            return [4, binding.onDeactivation(instance)];
                        case 1:
                            _a.sent();
                            _a.label = 2;
                        case 2: return [4, this._preDestroy(constructor, instance)];
                        case 3:
                            _a.sent();
                            return [2];
                    }
                });
            });
        };
        return Container;
    }());
    exports.Container = Container;
});
//# sourceMappingURL=container.js.map