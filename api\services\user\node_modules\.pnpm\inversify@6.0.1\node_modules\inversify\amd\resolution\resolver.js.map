{"version": 3, "file": "resolver.js", "sourceRoot": "", "sources": ["../../src/resolution/resolver.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUA,IAAM,eAAe,GAAG,UAAI,YAAqC;QAC/D,OAAA,UAAC,OAA2B;YAE1B,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAEjD,IAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;YAE5C,IAAM,eAAe,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;YAEnE,IAAM,wBAAwB,GAAG,CAAC,OAAO,CAAC,aAAa;gBACrD,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM;gBAC7B,CAAC,OAAO,CAAC,MAAM;gBACf,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAE/E,IAAI,eAAe,IAAI,wBAAwB,EAAE;gBAG/C,OAAO,aAAa,CAAC,GAAG,CAAC,UAAC,YAAgC;oBACxD,IAAM,EAAE,GAAG,eAAe,CAAC,YAAY,CAAC,CAAC;oBACzC,OAAO,EAAE,CAAC,YAAY,CAAmB,CAAC;gBAC5C,CAAC,CAAC,CAAC;aAEJ;iBAAM;gBACL,IAAI,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;oBACxD,OAAO,SAAS,CAAC;iBAClB;gBAED,IAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAE5B,OAAO,eAAe,CAAI,YAAY,EAAE,OAAO,EAAE,OAA2C,CAAC,CAAC;aAC/F;QACH,CAAC;IA/BD,CA+BC,CAAC;IAEJ,IAAM,0BAA0B,GAAG,UACjC,OAA8B,EAC9B,OAA2B;QAE3B,IAAM,cAAc,GAAG,IAAA,iCAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,OAAO,IAAA,4CAA+B,EACpC,cAAM,OAAC,cAAc,CAAC,OAA6C,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAApF,CAAoF,EAC1F,cAAM,OAAA,IAAI,KAAK,CACb,UAAU,CAAC,8BAA8B,CAAC,cAAc,CAAC,WAAW,EAAE,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CACxH,CACF,EAHK,CAGL,CAAC,CAAC;IACP,CAAC,CAAA;IAED,IAAM,uBAAuB,GAAG,UAC9B,YAAqC,EACrC,OAA2B,EAC3B,OAA8B;QAE9B,IAAI,MAAkC,CAAC;QACvC,IAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;QAE5C,IAAA,gCAAgB,EAAC,OAAO,CAAC,CAAC;QAE1B,QAAQ,OAAO,CAAC,IAAI,EAAE;YACpB,KAAK,+BAAe,CAAC,aAAa,CAAC;YACnC,KAAK,+BAAe,CAAC,QAAQ;gBAC3B,MAAM,GAAG,OAAO,CAAC,KAAuB,CAAC;gBACzC,MAAM;YACR,KAAK,+BAAe,CAAC,WAAW;gBAC9B,MAAM,GAAG,OAAO,CAAC,kBAAuB,CAAC;gBACzC,MAAM;YACR,KAAK,+BAAe,CAAC,QAAQ;gBAC3B,MAAM,GAAG,IAAA,+BAAe,EACtB,OAAO,EACP,OAAO,CAAC,kBAA2C,EACnD,aAAa,EACb,eAAe,CAAI,YAAY,CAAC,CACjC,CAAC;gBACF,MAAM;YACR;gBACE,MAAM,GAAG,0BAA0B,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;SACvE;QAED,OAAO,MAAwB,CAAC;IAClC,CAAC,CAAA;IAED,IAAM,eAAe,GAAG,UACtB,YAAqC,EACrC,OAA8B,EAC9B,kBAAwC;QAExC,IAAI,MAAM,GAAG,IAAA,uBAAe,EAAI,YAAY,EAAE,OAAO,CAAC,CAAC;QACvD,IAAI,MAAM,KAAK,IAAI,EAAE;YACnB,OAAO,MAAM,CAAC;SACf;QACD,MAAM,GAAG,kBAAkB,EAAE,CAAC;QAC9B,IAAA,mBAAW,EAAC,YAAY,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3C,OAAO,MAAM,CAAC;IAChB,CAAC,CAAA;IAED,IAAM,eAAe,GAAG,UACtB,YAAqC,EACrC,OAA2B,EAC3B,OAA8B;QAE9B,OAAO,eAAe,CAAI,YAAY,EAAE,OAAO,EAAE;YAC/C,IAAI,MAAM,GAAG,uBAAuB,CAAC,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;YACrE,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;gBACrB,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,UAAC,QAAQ,IAAK,OAAA,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAzC,CAAyC,CAAC,CAAC;aAC/E;iBAAM;gBACL,MAAM,GAAG,aAAa,CAAI,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACrD;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAA;IACJ,CAAC,CAAA;IAED,SAAS,aAAa,CAAI,OAA2B,EAAE,OAA8B,EAAE,QAAW;QAChG,IAAI,MAAM,GAAG,kBAAkB,CAAC,OAAO,CAAC,aAAa,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE1E,IAAM,kBAAkB,GAAG,sBAAsB,CAAC,OAAO,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;QAEnF,IAAI,SAA+B,CAAC;QACpC,IAAI,wBAAwB,GAAG,kBAAkB,CAAC,IAAI,EAAE,CAAC;QAEzD,GAAG;YACD,SAAS,GAAG,wBAAwB,CAAC,KAAK,CAAC;YAC3C,IAAM,SAAO,GAAG,OAAO,CAAC,aAAa,CAAC;YACtC,IAAM,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;YACpD,IAAM,mBAAmB,GAAG,kCAAkC,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;YAE7F,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;gBACrB,MAAM,GAAG,uBAAuB,CAAI,mBAAgE,EAAE,SAAO,EAAE,MAAM,CAAC,CAAC;aACxH;iBAAM;gBACL,MAAM,GAAG,kBAAkB,CAAI,mBAAgE,EAAE,SAAO,EAAE,MAAM,CAAC,CAAC;aACnH;YAED,wBAAwB,GAAG,kBAAkB,CAAC,IAAI,EAAE,CAAC;SAGtD,QAAQ,wBAAwB,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,IAAA,8BAAoB,EAAC,SAAS,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;QAEvH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,IAAM,kBAAkB,GAAG,UAAI,OAA2B,EAAE,OAA8B,EAAE,cAAiB;QAC3G,IAAI,MAAsB,CAAC;QAG3B,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,UAAU,EAAE;YAC9C,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;SACxD;aAAM;YACL,MAAM,GAAG,cAAc,CAAC;SACzB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAA;IAED,IAAM,kBAAkB,GAAG,UACzB,mBAA8D,EAC9D,OAA2B,EAC3B,MAAS;QAET,IAAI,UAAU,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC;QAE5C,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE;YACvB,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAM,CAAC;YAEhD,IAAI,IAAA,iBAAS,EAAI,MAAM,CAAC,EAAE;gBACxB,OAAO,uBAAuB,CAAC,mBAAmB,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;aACtE;YAED,UAAU,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC;SACzC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC,CAAA;IAED,IAAM,uBAAuB,GAAG,UAC9B,mBAA8D,EAC9D,OAA2B,EAC3B,aAAyB;;;;wBAEZ,WAAM,aAAa,EAAA;;oBAA5B,MAAM,GAAG,SAAmB;oBAC5B,UAAU,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC;;;yBAErC,CAAC,UAAU,CAAC,IAAI;oBACZ,WAAM,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,EAAA;;oBAAhD,MAAM,GAAG,SAAuC,CAAC;oBAEjD,UAAU,GAAG,mBAAmB,CAAC,IAAI,EAAE,CAAC;;wBAG1C,WAAO,MAAM,EAAC;;;SACf,CAAA;IAED,IAAM,kCAAkC,GAAG,UAAI,SAA+B,EAAE,iBAAkD;QAEhI,IAAM,WAAW,GAAI,SAAmG,CAAC,YAAY,CAAC;QAEtI,OAAO,WAAW,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC;IAC3G,CAAC,CAAA;IAED,IAAM,sBAAsB,GAAG,UAAC,SAA+B;QAC7D,IAAM,eAAe,GAA2B,CAAC,SAAS,CAAC,CAAC;QAE5D,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;QAE9B,OAAO,MAAM,KAAK,IAAI,EAAE;YACtB,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAE7B,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;SACxB;QAED,IAAM,gBAAgB,GAA+C;YACnE,IAAM,aAAa,GAAG,eAAe,CAAC,GAAG,EAAE,CAAC;YAE5C,IAAI,aAAa,KAAK,SAAS,EAAE;gBAC/B,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,CAAC;aAC9C;iBAAM;gBACL,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;aACzC;QACH,CAAC,CAAC;QAEF,IAAM,kBAAkB,GAAmC;YACzD,IAAI,EAAE,gBAAgB;SACvB,CAAC;QAEF,OAAO,kBAAkB,CAAC;IAC5B,CAAC,CAAA;IAED,SAAS,OAAO,CAAI,OAA2B;QAC7C,IAAM,EAAE,GAAG,eAAe,CAAI,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,YAAuC,CAAC,CAAC;QAChG,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAwC,CAAC;IAC7E,CAAC;IAEQ,0BAAO"}