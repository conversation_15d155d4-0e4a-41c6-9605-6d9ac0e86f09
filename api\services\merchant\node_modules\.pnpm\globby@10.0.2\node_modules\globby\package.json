{"name": "globby", "version": "10.0.2", "description": "Extends `glob` with support for multiple patterns and exposes a Promise API", "license": "MIT", "repository": "sindresorhus/globby", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"bench": "npm update glob-stream fast-glob && matcha bench.js", "test": "xo && ava && tsd"}, "files": ["index.js", "gitignore.js", "index.d.ts", "stream-utils.js"], "keywords": ["all", "array", "directories", "expand", "files", "filesystem", "filter", "find", "fnmatch", "folders", "fs", "glob", "globbing", "globs", "gulpfriendly", "match", "matcher", "minimatch", "multi", "multiple", "paths", "pattern", "patterns", "traverse", "util", "utility", "wildcard", "wildcards", "promise", "gitignore", "git"], "dependencies": {"@types/glob": "^7.1.1", "array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.0.3", "glob": "^7.1.3", "ignore": "^5.1.1", "merge2": "^1.2.3", "slash": "^3.0.0"}, "devDependencies": {"ava": "^2.1.0", "get-stream": "^5.1.0", "glob-stream": "^6.1.0", "globby": "sindresorhus/globby#master", "matcha": "^0.7.0", "rimraf": "^2.6.3", "tsd": "^0.7.3", "xo": "^0.24.0"}, "xo": {"ignores": ["fixtures"]}}