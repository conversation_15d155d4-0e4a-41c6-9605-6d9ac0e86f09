{"version": 3, "sources": ["../../src/error/CannotExecuteNotConnectedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,8BAA+B,SAAQ,2BAAY;IAC5D,YAAY,cAAsB;QAC9B,KAAK,CACD,gCAAgC,cAAc,yDAAyD,CAC1G,CAAA;IACL,CAAC;CACJ;AAND,wEAMC", "file": "CannotExecuteNotConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to execute operation allowed only if connection is opened.\n */\nexport class CannotExecuteNotConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot execute operation on \"${connectionName}\" connection because connection is not yet established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}