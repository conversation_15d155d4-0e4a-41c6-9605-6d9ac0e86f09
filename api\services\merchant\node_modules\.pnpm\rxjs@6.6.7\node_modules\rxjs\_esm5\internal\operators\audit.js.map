{"version": 3, "file": "audit.js", "sources": ["../../../src/internal/operators/audit.ts"], "names": [], "mappings": ";AAKA,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAgDjG,MAAM,UAAU,KAAK,CAAI,gBAA0D;IACjF,OAAO,SAAS,qBAAqB,CAAC,MAAqB;QACzD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;AACJ,CAAC;AAED;IACE,uBAAoB,gBAA0D;QAA1D,qBAAgB,GAAhB,gBAAgB,CAA0C;IAC9E,CAAC;IAED,4BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,eAAe,CAAO,UAAU,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACxF,CAAC;IACH,oBAAC;AAAD,CAAC,AAPD,IAOC;AAOD;IAAoC,2CAA2B;IAM7D,yBAAY,WAA0B,EAClB,gBAA0D;QAD9E,YAEE,kBAAM,WAAW,CAAC,SACnB;QAFmB,sBAAgB,GAAhB,gBAAgB,CAA0C;QAJtE,cAAQ,GAAY,KAAK,CAAC;;IAMlC,CAAC;IAES,+BAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,QAAQ,SAAA,CAAC;YACb,IAAI;gBACM,IAAA,wCAAgB,CAAU;gBAClC,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;aACpC;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;aACrC;YACD,IAAM,iBAAiB,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;YACpF,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAClD,IAAI,CAAC,aAAa,EAAE,CAAC;aACtB;iBAAM;gBACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,CAAC;aAC9C;SACF;IACH,CAAC;IAED,uCAAa,GAAb;QACQ,IAAA,SAAqC,EAAnC,gBAAK,EAAE,sBAAQ,EAAE,wBAAS,CAAU;QAC5C,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACvB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;YAC3B,SAAS,CAAC,WAAW,EAAE,CAAC;SACzB;QACD,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC;IAED,oCAAU,GAAV;QACE,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,wCAAc,GAAd;QACE,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IACH,sBAAC;AAAD,CAAC,AApDD,CAAoC,qBAAqB,GAoDxD"}