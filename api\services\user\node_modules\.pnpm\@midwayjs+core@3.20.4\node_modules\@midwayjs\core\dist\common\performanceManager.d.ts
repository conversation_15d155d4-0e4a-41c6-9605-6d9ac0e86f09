/// <reference types="node" />
import { PerformanceObserver, PerformanceObserverEntryList } from 'perf_hooks';
export declare class MidwayPerformanceManager {
    private readonly group;
    private static instances;
    static DEFAULT_GROUP: {
        INITIALIZE: string;
    };
    private observer;
    private marks;
    private measures;
    private constructor();
    static getInstance(group: string): MidwayPerformanceManager;
    private formatKey;
    markStart(key: string): void;
    markEnd(key: string): void;
    observeMeasure(callback: (list: PerformanceObserverEntryList) => void): PerformanceObserver;
    disconnect(): void;
    clean(): void;
    static cleanAll(): void;
    static getInitialPerformanceEntries(): any[];
}
export declare class MidwayInitializerPerformanceManager {
    static MEASURE_KEYS: {
        INITIALIZE: string;
        METADATA_PREPARE: string;
        DETECTOR_PREPARE: string;
        DEFINITION_PREPARE: string;
        CONFIG_LOAD: string;
        LOGGER_PREPARE: string;
        FRAMEWORK_PREPARE: string;
        FRAMEWORK_INITIALIZE: string;
        FRAMEWORK_RUN: string;
        LIFECYCLE_PREPARE: string;
        PRELOAD_MODULE_PREPARE: string;
    };
    static markStart(key: string): void;
    static markEnd(key: string): void;
    static frameworkInitializeStart(frameworkName: string): void;
    static frameworkInitializeEnd(frameworkName: string): void;
    static frameworkRunStart(frameworkName: string): void;
    static frameworkRunEnd(frameworkName: string): void;
    static lifecycleStart(namespace: string, lifecycleName: string): void;
    static lifecycleEnd(namespace: string, lifecycleName: string): void;
}
//# sourceMappingURL=performanceManager.d.ts.map