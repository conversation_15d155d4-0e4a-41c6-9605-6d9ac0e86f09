<template>
  <div class="file-selector">
    <ElFormItem label="路由地址" prop="path" required>
      <ElSelect
        v-model="routePath"
        placeholder="路由地址"
        filterable
        clearable
        allow-create
        default-first-option
        @change="handleRouteChange"
        class="route-selector"
        popper-class="route-selector-dropdown"
      >
        <ElOption
          v-for="file in viewFiles"
          :key="file.path"
          :label="file.path"
          :value="file.path"
        />
      </ElSelect>
    </ElFormItem>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElFormItem, ElSelect, ElOption } from 'element-plus'

interface FileInfo {
  name: string
  path: string
  fullPath: string
}

interface Props {
  modelValue?: string
}

interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const routePath = ref(props.modelValue || '')
const viewFiles = ref<FileInfo[]>([])

// 扫描 views 目录下的所有 .vue 文件
const scanViewFiles = () => {
  try {
    // 使用 Vite 的 import.meta.glob 扫描文件
    const modules = import.meta.glob('../../views/**/*.vue')
    const files: FileInfo[] = []

    for (const path in modules) {
      // 提取相对路径：../../views/merchant/settle-in.vue -> /merchant/settle-in
      const relativePath = path
        .replace('../../views', '')
        .replace('.vue', '')
        .replace('/index', '') // 处理 index.vue 文件

      // 跳过空路径
      if (!relativePath || relativePath === '/') {
        continue
      }

      // 提取文件名
      const pathParts = relativePath.split('/').filter(p => p)
      const fileName = pathParts[pathParts.length - 1] || pathParts[pathParts.length - 2]

      if (fileName) {
        files.push({
          name: fileName,
          path: relativePath,
          fullPath: path
        })
      }
    }

    return files.sort((a, b) => a.path.localeCompare(b.path))
  } catch (error) {
    console.warn('扫描视图文件失败:', error)
    return []
  }
}

// 处理路由变化
const handleRouteChange = () => {
  emit('update:modelValue', routePath.value)
  emit('change', routePath.value)
}

// 初始化
onMounted(() => {
  viewFiles.value = scanViewFiles()
})

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  routePath.value = newVal || ''
})
</script>

<style scoped>
.route-selector {
  width: 100%;
}

/* 控制下拉选项的宽度 */
:global(.route-selector-dropdown) {
  min-width: 200px !important;
  max-width: 300px !important;
}

:global(.route-selector-dropdown .el-select-dropdown__item) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 12px;
}
</style>
