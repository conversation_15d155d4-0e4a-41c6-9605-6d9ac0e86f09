{"name": "filenamify", "version": "3.0.0", "description": "Convert a string to a valid safe filename", "license": "MIT", "repository": "sindresorhus/filenamify", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["filename", "safe", "sanitize", "file", "name", "string", "path", "filepath", "convert", "valid", "dirname"], "dependencies": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}, "devDependencies": {"ava": "^1.3.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}}