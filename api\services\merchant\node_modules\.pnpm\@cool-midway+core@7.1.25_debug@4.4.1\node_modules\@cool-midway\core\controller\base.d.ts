import { CurdOption } from "../decorator/controller";
import { BaseService } from "../service/base";
import { IMidwayApplication } from "@midwayjs/core";
import { Context } from "@midwayjs/koa";
import { TypeORMDataSourceManager } from "@midwayjs/typeorm";
/**
 * 控制器基类
 */
export declare abstract class BaseController {
    baseCtx: Context;
    service: BaseService;
    baseApp: IMidwayApplication;
    curdOption: CurdOption;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    connectionName: any;
    init(): Promise<void>;
    private before;
    /**
     * 插入参数值
     * @param curdOption 配置
     */
    private insertParam;
    /**
     * 设置实体
     * @param curdOption 配置
     */
    private setEntity;
    /**
     * 设置service
     * @param curdOption
     */
    private setService;
    /**
     * 新增
     * @returns
     */
    add(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 删除
     * @returns
     */
    delete(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 更新
     * @returns
     */
    update(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 分页查询
     * @returns
     */
    page(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 列表查询
     * @returns
     */
    list(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 根据ID查询信息
     * @returns
     */
    info(): Promise<{
        code: number;
        message: string;
    }>;
    /**
     * 成功返回
     * @param data 返回数据
     */
    ok(data?: any): {
        code: number;
        message: string;
    };
    /**
     * 失败返回
     * @param message
     */
    fail(message?: string, code?: number): {
        code: number;
        message: string;
    };
}
