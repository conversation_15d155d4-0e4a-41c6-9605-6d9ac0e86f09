body {
    display: flex;
    height: 100vh;
    justify-content: center;
    align-items: center;
    text-align: center;
    background: #ee3f4d;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.footer-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    color: #ffffff;
    padding: 10px 0 20px 0;
    text-align: center;
    opacity: 0; /* 开始时隐藏 */
    animation: fadeIn 5s forwards;  /* 应用动画 */
}

.link {
    color: #ffffff;
}

.reveal {
    position: relative;
    display: flex;
    color: #ffffff;
    font-size: 2em;
    font-family: Raleway, sans-serif;
    letter-spacing: 3px;
    text-transform: uppercase;
    white-space: pre;
}
.reveal span {
    opacity: 0;
    transform: scale(0);
    animation: fadeIn 2.4s forwards;
}
.reveal::before, .reveal::after {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    width: 2px;
    height: 100%;
    background: white;
    opacity: 0;
    transform: scale(0);
}
.reveal::before {
    left: 50%;
    animation: slideLeft 1.5s cubic-bezier(0.7, -0.6, 0.3, 1.5) forwards;
}
.reveal::after {
    right: 50%;
    animation: slideRight 1.5s cubic-bezier(0.7, -0.6, 0.3, 1.5) forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
        transform: scale(1);
    }
}
@keyframes slideLeft {
    to {
        left: -6%;
        opacity: 1;
        transform: scale(0.9);
    }
}
@keyframes slideRight {
    to {
        right: -6%;
        opacity: 1;
        transform: scale(0.9);
    }
}
