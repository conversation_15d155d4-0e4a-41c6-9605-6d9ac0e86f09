{"name": "@midwayjs/cli-plugin-test", "version": "2.1.0", "main": "dist/index", "typings": "dist/index.d.ts", "dependencies": {"@midwayjs/command-core": "^2.1.0", "globby": "^10.0.1", "ts-node": "^10.0.0", "typescript": "^4.1.0"}, "devDependencies": {"mocha": "^7.1.2", "typescript": "^4.1.0"}, "engines": {"node": ">= 10"}, "files": ["plugin.json", "config", "dist", "src"], "scripts": {"build": "tsc --build", "lint": "../../node_modules/.bin/eslint .", "test": "../../node_modules/.bin/jest", "cov": "../../node_modules/.bin/jest --coverage", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "publishConfig": {"access": "public"}, "license": "MIT", "gitHead": "8b6ce5b6bebd4d31140af0e9a51871ab12692b14"}