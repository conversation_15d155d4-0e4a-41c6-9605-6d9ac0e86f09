{"version": 3, "sources": ["../../src/find-options/operator/In.ts"], "names": [], "mappings": ";;AAMA,gBAIC;AAVD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,EAAE,CACd,KAAqC;IAErC,OAAO,IAAI,2BAAY,CAAC,IAAI,EAAE,KAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC3D,CAAC", "file": "In.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: In([...]) }\n */\nexport function In<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"in\", value as any, true, true)\n}\n"], "sourceRoot": "../.."}