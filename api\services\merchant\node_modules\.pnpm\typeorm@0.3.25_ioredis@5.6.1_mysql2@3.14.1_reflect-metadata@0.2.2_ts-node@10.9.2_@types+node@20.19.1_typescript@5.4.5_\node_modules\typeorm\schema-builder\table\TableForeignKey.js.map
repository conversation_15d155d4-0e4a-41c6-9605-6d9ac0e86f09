{"version": 3, "sources": ["../../src/schema-builder/table/TableForeignKey.ts"], "names": [], "mappings": ";;;AAIA;;GAEG;AACH,MAAa,eAAe;IAuDxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,OAA+B;QA1DlC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAA;QAWtD;;WAEG;QACH,gBAAW,GAAa,EAAE,CAAA;QAiB1B;;WAEG;QACH,0BAAqB,GAAa,EAAE,CAAA;QAyBhC,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACxB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACtC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAA;QAC1D,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAA;QACpD,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAChD,IAAI,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAA;QACtD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAChC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QAChC,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;IACxC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK;QACD,OAAO,IAAI,eAAe,CAAyB;YAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;YAClC,qBAAqB,EAAE,CAAC,GAAG,IAAI,CAAC,qBAAqB,CAAC;YACtD,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;YAC3C,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;YACvC,mBAAmB,EAAE,IAAI,CAAC,mBAAmB;YAC7C,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,UAAU,EAAE,IAAI,CAAC,UAAU;SAC9B,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,MAAM,CACT,QAA4B,EAC5B,MAAc;QAEd,OAAO,IAAI,eAAe,CAAyB;YAC/C,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,WAAW,EAAE,QAAQ,CAAC,WAAW;YACjC,qBAAqB,EAAE,QAAQ,CAAC,qBAAqB;YACrD,kBAAkB,EAAE,QAAQ,CAAC,wBAAwB,CAAC,QAAQ;YAC9D,gBAAgB,EAAE,QAAQ,CAAC,wBAAwB,CAAC,MAAM;YAC1D,mBAAmB,EAAE,QAAQ,CAAC,mBAAmB;YACjD,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;SAClC,CAAC,CAAA;IACN,CAAC;CACJ;AAnHD,0CAmHC", "file": "TableForeignKey.js", "sourcesContent": ["import { ForeignKeyMetadata } from \"../../metadata/ForeignKeyMetadata\"\nimport { TableForeignKeyOptions } from \"../options/TableForeignKeyOptions\"\nimport { Driver } from \"../../driver/Driver\"\n\n/**\n * Foreign key from the database stored in this class.\n */\nexport class TableForeignKey {\n    readonly \"@instanceof\" = Symbol.for(\"TableForeignKey\")\n\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Name of the foreign key constraint.\n     */\n    name?: string\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    columnNames: string[] = []\n\n    /**\n     * Database of Table referenced in the foreign key.\n     */\n    referencedDatabase?: string\n\n    /**\n     * Database of Table referenced in the foreign key.\n     */\n    referencedSchema?: string\n\n    /**\n     * Table referenced in the foreign key.\n     */\n    referencedTableName: string\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    referencedColumnNames: string[] = []\n\n    /**\n     * \"ON DELETE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being deleted.\n     */\n    onDelete?: string\n\n    /**\n     * \"ON UPDATE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being updated.\n     */\n    onUpdate?: string\n\n    /**\n     * Set this foreign key constraint as \"DEFERRABLE\" e.g. check constraints at start\n     * or at the end of a transaction\n     */\n    deferrable?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(options: TableForeignKeyOptions) {\n        this.name = options.name\n        this.columnNames = options.columnNames\n        this.referencedColumnNames = options.referencedColumnNames\n        this.referencedDatabase = options.referencedDatabase\n        this.referencedSchema = options.referencedSchema\n        this.referencedTableName = options.referencedTableName\n        this.onDelete = options.onDelete\n        this.onUpdate = options.onUpdate\n        this.deferrable = options.deferrable\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new copy of this foreign key with exactly same properties.\n     */\n    clone(): TableForeignKey {\n        return new TableForeignKey(<TableForeignKeyOptions>{\n            name: this.name,\n            columnNames: [...this.columnNames],\n            referencedColumnNames: [...this.referencedColumnNames],\n            referencedDatabase: this.referencedDatabase,\n            referencedSchema: this.referencedSchema,\n            referencedTableName: this.referencedTableName,\n            onDelete: this.onDelete,\n            onUpdate: this.onUpdate,\n            deferrable: this.deferrable,\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new table foreign key from the given foreign key metadata.\n     */\n    static create(\n        metadata: ForeignKeyMetadata,\n        driver: Driver,\n    ): TableForeignKey {\n        return new TableForeignKey(<TableForeignKeyOptions>{\n            name: metadata.name,\n            columnNames: metadata.columnNames,\n            referencedColumnNames: metadata.referencedColumnNames,\n            referencedDatabase: metadata.referencedEntityMetadata.database,\n            referencedSchema: metadata.referencedEntityMetadata.schema,\n            referencedTableName: metadata.referencedTablePath,\n            onDelete: metadata.onDelete,\n            onUpdate: metadata.onUpdate,\n            deferrable: metadata.deferrable,\n        })\n    }\n}\n"], "sourceRoot": "../.."}