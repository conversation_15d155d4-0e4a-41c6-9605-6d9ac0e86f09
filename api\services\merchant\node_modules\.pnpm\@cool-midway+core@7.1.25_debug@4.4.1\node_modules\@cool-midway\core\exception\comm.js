"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolCommException = void 0;
const global_1 = require("../constant/global");
const base_1 = require("./base");
/**
 * 通用异常
 */
class CoolCommException extends base_1.BaseException {
    constructor(message) {
        const { RESCODE, RESMESSAGE } = global_1.GlobalConfig.getInstance();
        super('CoolCommException', RESCODE.COMMFAIL, message ? message : RESMESSAGE.COMMFAIL);
    }
}
exports.CoolCommException = CoolCommException;
