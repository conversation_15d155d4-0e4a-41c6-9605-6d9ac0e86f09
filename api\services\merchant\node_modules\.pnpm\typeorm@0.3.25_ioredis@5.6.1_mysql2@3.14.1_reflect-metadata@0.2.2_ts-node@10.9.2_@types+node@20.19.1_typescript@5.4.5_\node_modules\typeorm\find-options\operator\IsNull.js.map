{"version": 3, "sources": ["../../src/find-options/operator/IsNull.ts"], "names": [], "mappings": ";;AAMA,wBAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,MAAM;IAClB,OAAO,IAAI,2BAAY,CAAC,QAAQ,EAAE,SAAgB,EAAE,KAAK,CAAC,CAAA;AAC9D,CAAC", "file": "IsNull.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: IsNull() }\n */\nexport function IsNull() {\n    return new FindOperator(\"isNull\", undefined as any, false)\n}\n"], "sourceRoot": "../.."}