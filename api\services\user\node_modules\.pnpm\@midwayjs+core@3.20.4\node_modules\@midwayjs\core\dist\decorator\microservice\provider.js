"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DubboMethod = exports.GrpcMethod = exports.GrpcStreamTypeEnum = exports.Provider = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Provider(type, metadata = {}) {
    return (target) => {
        (0, __1.saveModule)(__1.MS_PROVIDER_KEY, target);
        (0, __1.saveClassMetadata)(__1.MS_PROVIDER_KEY, {
            type,
            metadata,
        }, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Provider = Provider;
var GrpcStreamTypeEnum;
(function (GrpcStreamTypeEnum) {
    GrpcStreamTypeEnum["BASE"] = "base";
    GrpcStreamTypeEnum["DUPLEX"] = "ServerDuplexStream";
    GrpcStreamTypeEnum["READABLE"] = "ServerReadableStream";
    GrpcStreamTypeEnum["WRITEABLE"] = "ServerWritableStream";
})(GrpcStreamTypeEnum = exports.GrpcStreamTypeEnum || (exports.GrpcStreamTypeEnum = {}));
function GrpcMethod(methodOptions = {}) {
    return (target, propertyName, descriptor) => {
        if (!methodOptions.type) {
            methodOptions.type = GrpcStreamTypeEnum.BASE;
        }
        (0, __1.savePropertyMetadata)(__1.MS_GRPC_METHOD_KEY, {
            methodName: methodOptions.methodName || propertyName,
            type: methodOptions.type,
            onEnd: methodOptions.onEnd,
        }, target, propertyName);
        return descriptor;
    };
}
exports.GrpcMethod = GrpcMethod;
function DubboMethod(methodName) {
    return (target, propertyName, descriptor) => {
        (0, __1.attachClassMetadata)(__1.MS_DUBBO_METHOD_KEY, {
            methodName: methodName || propertyName,
        }, target);
        return descriptor;
    };
}
exports.DubboMethod = DubboMethod;
//# sourceMappingURL=provider.js.map