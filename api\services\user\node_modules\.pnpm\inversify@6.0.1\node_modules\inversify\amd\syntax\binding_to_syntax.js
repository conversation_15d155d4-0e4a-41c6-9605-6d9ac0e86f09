var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
define(["require", "exports", "../constants/error_msgs", "../constants/literal_types", "./binding_in_when_on_syntax", "./binding_when_on_syntax"], function (require, exports, ERROR_MSGS, literal_types_1, binding_in_when_on_syntax_1, binding_when_on_syntax_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BindingToSyntax = void 0;
    ERROR_MSGS = __importStar(ERROR_MSGS);
    var BindingToSyntax = (function () {
        function BindingToSyntax(binding) {
            this._binding = binding;
        }
        BindingToSyntax.prototype.to = function (constructor) {
            this._binding.type = literal_types_1.BindingTypeEnum.Instance;
            this._binding.implementationType = constructor;
            return new binding_in_when_on_syntax_1.BindingInWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toSelf = function () {
            if (typeof this._binding.serviceIdentifier !== "function") {
                throw new Error("" + ERROR_MSGS.INVALID_TO_SELF_VALUE);
            }
            var self = this._binding.serviceIdentifier;
            return this.to(self);
        };
        BindingToSyntax.prototype.toConstantValue = function (value) {
            this._binding.type = literal_types_1.BindingTypeEnum.ConstantValue;
            this._binding.cache = value;
            this._binding.dynamicValue = null;
            this._binding.implementationType = null;
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toDynamicValue = function (func) {
            this._binding.type = literal_types_1.BindingTypeEnum.DynamicValue;
            this._binding.cache = null;
            this._binding.dynamicValue = func;
            this._binding.implementationType = null;
            return new binding_in_when_on_syntax_1.BindingInWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toConstructor = function (constructor) {
            this._binding.type = literal_types_1.BindingTypeEnum.Constructor;
            this._binding.implementationType = constructor;
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toFactory = function (factory) {
            this._binding.type = literal_types_1.BindingTypeEnum.Factory;
            this._binding.factory = factory;
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toFunction = function (func) {
            if (typeof func !== "function") {
                throw new Error(ERROR_MSGS.INVALID_FUNCTION_BINDING);
            }
            var bindingWhenOnSyntax = this.toConstantValue(func);
            this._binding.type = literal_types_1.BindingTypeEnum.Function;
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return bindingWhenOnSyntax;
        };
        BindingToSyntax.prototype.toAutoFactory = function (serviceIdentifier) {
            this._binding.type = literal_types_1.BindingTypeEnum.Factory;
            this._binding.factory = function (context) {
                var autofactory = function () { return context.container.get(serviceIdentifier); };
                return autofactory;
            };
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toAutoNamedFactory = function (serviceIdentifier) {
            this._binding.type = literal_types_1.BindingTypeEnum.Factory;
            this._binding.factory = function (context) {
                return function (named) { return context.container.getNamed(serviceIdentifier, named); };
            };
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toProvider = function (provider) {
            this._binding.type = literal_types_1.BindingTypeEnum.Provider;
            this._binding.provider = provider;
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingToSyntax.prototype.toService = function (service) {
            this.toDynamicValue(function (context) { return context.container.get(service); });
        };
        return BindingToSyntax;
    }());
    exports.BindingToSyntax = BindingToSyntax;
});
//# sourceMappingURL=binding_to_syntax.js.map