{"version": 3, "file": "transformation-type.enum.js", "sourceRoot": "", "sources": ["../../../src/enums/transformation-type.enum.ts"], "names": [], "mappings": ";;;AAAA,IAAY,kBAIX;AAJD,WAAY,kBAAkB;IAC5B,+EAAc,CAAA;IACd,+EAAc,CAAA;IACd,+EAAc,CAAA;AAChB,CAAC,EAJW,kBAAkB,GAAlB,0BAAkB,KAAlB,0BAAkB,QAI7B", "sourcesContent": ["export enum TransformationType {\n  PLAIN_TO_CLASS,\n  CLASS_TO_PLAIN,\n  CLASS_TO_CLASS,\n}\n"]}