{"version": 3, "sources": ["../../src/metadata/IndexMetadata.ts"], "names": [], "mappings": ";;;AAKA,oCAAuC;AAEvC;;GAEG;AACH,MAAa,aAAa;IAqHtB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAKX;QA/GD;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;;WAGG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;;WAGG;QACH,eAAU,GAAY,KAAK,CAAA;QAE3B;;;;;;WAMG;QACH,mBAAc,GAAY,KAAK,CAAA;QAQ/B;;WAEG;QACH,gBAAW,GAAY,IAAI,CAAA;QAgC3B;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QA0B9B;;;WAGG;QACH,+BAA0B,GAA8B,EAAE,CAAA;QAYtD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAChD,IAAI,OAAO,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAEnD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACjC,IACI,OAAO,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI;gBACjC,OAAO,CAAC,IAAI,CAAC,WAAW,KAAK,SAAS;gBAEtC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,WAAW,CAAA;YAC/C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACrC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAA;YACvC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAA;YACzC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;YACjD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACjC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAA;YAC/B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACnC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAA;YAC3C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAA;YAC3C,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAA;YACzD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA;YAClC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAA;QAChD,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,uBAAuB;IACvB,wEAAwE;IAExE;;;OAGG;IACH,KAAK,CAAC,cAAuC;QACzC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAU,CAAA;YAC3B,OAAO,IAAI,CAAA;QACf,CAAC;QAED,MAAM,GAAG,GAA8B,EAAE,CAAA;QAEzC,8DAA8D;QAC9D,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,mBAAmB,GAAa,EAAE,CAAA;YACtC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAC3C,CAAC,UAAU,EAAE,EAAE;oBACX,IAAI,IAAI,CAAC,gBAAgB;wBACrB,OAAO,CACH,IAAI,CAAC,gBAAgB,CAAC,YAAY;4BAClC,GAAG;4BACH,UAAU,CACb,CAAA;oBAEL,OAAO,UAAU,CAAC,IAAI,EAAE,CAAA;gBAC5B,CAAC,CACJ,CAAA;gBACD,mBAAmB,CAAC,OAAO,CACvB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAC5C,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,qFAAqF;gBACrF,2GAA2G;gBAC3G,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CACzC,IAAI,CAAC,cAAc,CAAC,aAAa,CACpC,CAAA;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;oBACjC,mBAAmB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CACjD,MAAM,CAAC,CAAC,CAAC,CACZ,CAAA;oBACD,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBAC1D,CAAC;qBAAM,CAAC;oBACJ,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAClD,CAAC,CAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACxB,CAAA;oBACD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAChC,CAAC,UAAU,EAAE,EAAE,CACX,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CACtD,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,OAAO,GAAG,mBAAmB;iBAC7B,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACvD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;gBACD,IAAI,kBAAkB,EAAE,CAAC;oBACrB,OAAO,CAAC,kBAAkB,CAAC,CAAA;gBAC/B,CAAC;gBACD,MAAM,oBAAoB,GACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAC9B,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,gBAAgB;oBACzB,QAAQ,CAAC,YAAY,KAAK,YAAY,CAC7C,CAAA;gBACL,IAAI,oBAAoB,EAAE,CAAC;oBACvB,OAAO,oBAAoB,CAAC,WAAW,CAAA;gBAC3C,CAAC;gBACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;oBAC5B,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI;oBAC7B,CAAC,CAAC,EAAE,CAAA;gBACR,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAA;gBACjD,MAAM,IAAI,oBAAY,CAClB,SAAS,SAAS,kDAAkD,UAAU,KAAK;oBAC/E,YAAY,CACnB,CAAA;YACL,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,CAAC,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CACrD,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,CAC1C,CAAA;YACD,IAAI,MAAM;gBAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;YAEtD,OAAO,UAAU,CAAA;QACrB,CAAC,EACD,EAA+B,CAClC,CAAA;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;YACtB,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,CAAC,CAAC,cAAc,CAAC,SAAS,CACpB,IAAI,CAAC,cAAc,CAAC,SAAS,EAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,EACjD,IAAI,CAAC,KAAK,CACb,CAAA;QACP,OAAO,IAAI,CAAA;IACf,CAAC;CACJ;AApQD,sCAoQC", "file": "IndexMetadata.js", "sourcesContent": ["import { EntityMetadata } from \"./EntityMetadata\"\nimport { IndexMetadataArgs } from \"../metadata-args/IndexMetadataArgs\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { ColumnMetadata } from \"./ColumnMetadata\"\nimport { EmbeddedMetadata } from \"./EmbeddedMetadata\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Index metadata contains all information about table's index.\n */\nexport class IndexMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the class to which this index is applied.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Embedded metadata if this index was applied on embedded.\n     */\n    embeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * Indicates if this index must be unique.\n     */\n    isUnique: boolean = false\n\n    /**\n     * The SPATIAL modifier indexes the entire column and does not allow indexed columns to contain NULL values.\n     * Works only in MySQL.\n     */\n    isSpatial: boolean = false\n\n    /**\n     * The FULLTEXT modifier indexes the entire column and does not allow prefixing.\n     * Works only in MySQL.\n     */\n    isFulltext: boolean = false\n\n    /**\n     * NULL_FILTERED indexes are particularly useful for indexing sparse columns, where most rows contain a NULL value.\n     * In these cases, the NULL_FILTERED index can be considerably smaller and more efficient to maintain than\n     * a normal index that includes NULL values.\n     *\n     * Works only in Spanner.\n     */\n    isNullFiltered: boolean = false\n\n    /**\n     * Fulltext parser.\n     * Works only in MySQL.\n     */\n    parser?: string\n\n    /**\n     * Indicates if this index must synchronize with database index.\n     */\n    synchronize: boolean = true\n\n    /**\n     * If true, the index only references documents with the specified field.\n     * These indexes use less space but behave differently in some situations (particularly sorts).\n     * This option is only supported for mongodb database.\n     */\n    isSparse?: boolean\n\n    /**\n     * Builds the index in the background so that building an index an does not block other database activities.\n     * This option is only supported for mongodb database.\n     */\n    isBackground?: boolean\n\n    /**\n     * Builds the index using the concurrently option.\n     * This options is only supported for postgres database.\n     */\n    isConcurrent?: boolean\n\n    /**\n     * Specifies a time to live, in seconds.\n     * This option is only supported for mongodb database.\n     */\n    expireAfterSeconds?: number\n\n    /**\n     * Target class to which metadata is applied.\n     */\n    target?: Function | string\n\n    /**\n     * Indexed columns.\n     */\n    columns: ColumnMetadata[] = []\n\n    /**\n     * User specified index name.\n     */\n    givenName?: string\n\n    /**\n     * User specified column names.\n     */\n    givenColumnNames?:\n        | ((object?: any) => any[] | { [key: string]: number })\n        | string[]\n\n    /**\n     * Final index name.\n     * If index name was given by a user then it stores normalized (by naming strategy) givenName.\n     * If index name was not given then its generated.\n     */\n    name: string\n\n    /**\n     * Index filter condition.\n     */\n    where?: string\n\n    /**\n     * Map of column names with order set.\n     * Used only by MongoDB driver.\n     */\n    columnNamesWithOrderingMap: { [key: string]: number } = {}\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        embeddedMetadata?: EmbeddedMetadata\n        columns?: ColumnMetadata[]\n        args?: IndexMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.embeddedMetadata = options.embeddedMetadata\n        if (options.columns) this.columns = options.columns\n\n        if (options.args) {\n            this.target = options.args.target\n            if (\n                options.args.synchronize !== null &&\n                options.args.synchronize !== undefined\n            )\n                this.synchronize = options.args.synchronize\n            this.isUnique = !!options.args.unique\n            this.isSpatial = !!options.args.spatial\n            this.isFulltext = !!options.args.fulltext\n            this.isNullFiltered = !!options.args.nullFiltered\n            this.parser = options.args.parser\n            this.where = options.args.where\n            this.isSparse = options.args.sparse\n            this.isBackground = options.args.background\n            this.isConcurrent = options.args.concurrent\n            this.expireAfterSeconds = options.args.expireAfterSeconds\n            this.givenName = options.args.name\n            this.givenColumnNames = options.args.columns\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Build Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend index properties.\n     * Must be called after all entity metadata's properties map, columns and relations are built.\n     */\n    build(namingStrategy: NamingStrategyInterface): this {\n        if (this.synchronize === false) {\n            this.name = this.givenName!\n            return this\n        }\n\n        const map: { [key: string]: number } = {}\n\n        // if columns already an array of string then simply return it\n        if (this.givenColumnNames) {\n            let columnPropertyPaths: string[] = []\n            if (Array.isArray(this.givenColumnNames)) {\n                columnPropertyPaths = this.givenColumnNames.map(\n                    (columnName) => {\n                        if (this.embeddedMetadata)\n                            return (\n                                this.embeddedMetadata.propertyPath +\n                                \".\" +\n                                columnName\n                            )\n\n                        return columnName.trim()\n                    },\n                )\n                columnPropertyPaths.forEach(\n                    (propertyPath) => (map[propertyPath] = 1),\n                )\n            } else {\n                // todo: indices in embeds are not implemented in this syntax. deprecate this syntax?\n                // if columns is a function that returns array of field names then execute it and get columns names from it\n                const columnsFnResult = this.givenColumnNames(\n                    this.entityMetadata.propertiesMap,\n                )\n                if (Array.isArray(columnsFnResult)) {\n                    columnPropertyPaths = columnsFnResult.map((i: any) =>\n                        String(i),\n                    )\n                    columnPropertyPaths.forEach((name) => (map[name] = 1))\n                } else {\n                    columnPropertyPaths = Object.keys(columnsFnResult).map(\n                        (i: any) => String(i),\n                    )\n                    Object.keys(columnsFnResult).forEach(\n                        (columnName) =>\n                            (map[columnName] = columnsFnResult[columnName]),\n                    )\n                }\n            }\n\n            this.columns = columnPropertyPaths\n                .map((propertyPath) => {\n                    const columnWithSameName = this.entityMetadata.columns.find(\n                        (column) => column.propertyPath === propertyPath,\n                    )\n                    if (columnWithSameName) {\n                        return [columnWithSameName]\n                    }\n                    const relationWithSameName =\n                        this.entityMetadata.relations.find(\n                            (relation) =>\n                                relation.isWithJoinColumn &&\n                                relation.propertyName === propertyPath,\n                        )\n                    if (relationWithSameName) {\n                        return relationWithSameName.joinColumns\n                    }\n                    const indexName = this.givenName\n                        ? '\"' + this.givenName + '\" '\n                        : \"\"\n                    const entityName = this.entityMetadata.targetName\n                    throw new TypeORMError(\n                        `Index ${indexName}contains column that is missing in the entity (${entityName}): ` +\n                            propertyPath,\n                    )\n                })\n                .reduce((a, b) => a.concat(b))\n        }\n\n        this.columnNamesWithOrderingMap = Object.keys(map).reduce(\n            (updatedMap, key) => {\n                const column = this.entityMetadata.columns.find(\n                    (column) => column.propertyPath === key,\n                )\n                if (column) updatedMap[column.databasePath] = map[key]\n\n                return updatedMap\n            },\n            {} as { [key: string]: number },\n        )\n\n        this.name = this.givenName\n            ? this.givenName\n            : namingStrategy.indexName(\n                  this.entityMetadata.tableName,\n                  this.columns.map((column) => column.databaseName),\n                  this.where,\n              )\n        return this\n    }\n}\n"], "sourceRoot": ".."}