/**
 * @deprecated Replaced by ConsumerSubscribeTopics
 */
export type ConsumerSubscribeTopic = {
    fromBeginning?: boolean;
};
export type ConsumerSubscribeTopics = {
    fromBeginning?: boolean;
};
export type ConsumerRunConfig = {
    autoCommit?: boolean;
    autoCommitInterval?: number | null;
    autoCommitThreshold?: number | null;
    eachBatchAutoResolve?: boolean;
    partitionsConsumedConcurrently?: number;
};
export interface KafkaListenerOptions {
    propertyKey?: string;
    topic?: string;
    subscription?: ConsumerSubscribeTopics | ConsumerSubscribeTopic;
    runConfig?: ConsumerRunConfig;
}
export declare function KafkaListener(topic: string, options?: KafkaListenerOptions): MethodDecorator;
//# sourceMappingURL=kafkaListener.d.ts.map