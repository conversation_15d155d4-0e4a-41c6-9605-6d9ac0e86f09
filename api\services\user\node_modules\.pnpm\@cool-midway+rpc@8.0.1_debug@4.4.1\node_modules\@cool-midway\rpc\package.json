{"name": "@cool-midway/rpc", "version": "8.0.1", "description": "cool-admin midway rpc", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "mwtsc --cleanOutDir", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix"}, "keywords": ["cool", "cool-admin", "cooljs"], "author": "COOL", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "readme": "README.md", "license": "MIT", "repository": {"type": "git", "url": "https://cool-js.com"}, "devDependencies": {"@cool-midway/core": "8.0.1", "@midwayjs/core": "^3.20.0", "@midwayjs/koa": "^3.20.0", "@midwayjs/logger": "^3.4.2", "@midwayjs/mock": "^3.20.0", "@midwayjs/redis": "^3.20.0", "@midwayjs/typeorm": "^3.20.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "cross-env": "^7.0.3", "jest": "^29.7.0", "lodash": "^4.17.21", "moment": "^2.30.1", "mwts": "^1.3.0", "mwtsc": "^1.15.1", "sqlstring": "^2.3.3", "ts-jest": "^29.2.5", "typeorm": "^0.3.20", "typescript": "^5.7.3", "uuid": "^11.0.5"}, "dependencies": {"ioredis": "^5.4.2", "moleculer": "^0.14.35"}}