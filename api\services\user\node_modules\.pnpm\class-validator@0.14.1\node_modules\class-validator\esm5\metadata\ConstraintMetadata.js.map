{"version": 3, "file": "ConstraintMetadata.js", "sourceRoot": "", "sources": ["../../../src/metadata/ConstraintMetadata.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,cAAc,CAAC;AAEhD;;GAEG;AACH;IAoBE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,4BAAY,MAAgB,EAAE,IAAa,EAAE,KAAsB;QAAtB,sBAAA,EAAA,aAAsB;QACjE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IASD,sBAAI,wCAAQ;QAPZ,4EAA4E;QAC5E,YAAY;QACZ,4EAA4E;QAE5E;;WAEG;aACH;YACE,OAAO,gBAAgB,CAA+B,IAAI,CAAC,MAAM,CAAC,CAAC;QACrE,CAAC;;;OAAA;IACH,yBAAC;AAAD,CAAC,AAxCD,IAwCC", "sourcesContent": ["import { ValidatorConstraintInterface } from '../validation/ValidatorConstraintInterface';\nimport { getFromContainer } from '../container';\n\n/**\n * This metadata interface contains information for custom validators.\n */\nexport class ConstraintMetadata {\n  // -------------------------------------------------------------------------\n  // Properties\n  // -------------------------------------------------------------------------\n\n  /**\n   * Target class which performs validation.\n   */\n  target: Function;\n\n  /**\n   * Custom validation's name, that will be used as validation error type.\n   */\n  name: string;\n\n  /**\n   * Indicates if this validation is asynchronous or not.\n   */\n  async: boolean;\n\n  // -------------------------------------------------------------------------\n  // Constructor\n  // -------------------------------------------------------------------------\n\n  constructor(target: Function, name?: string, async: boolean = false) {\n    this.target = target;\n    this.name = name;\n    this.async = async;\n  }\n\n  // -------------------------------------------------------------------------\n  // Accessors\n  // -------------------------------------------------------------------------\n\n  /**\n   * Instance of the target custom validation class which performs validation.\n   */\n  get instance(): ValidatorConstraintInterface {\n    return getFromContainer<ValidatorConstraintInterface>(this.target);\n  }\n}\n"]}