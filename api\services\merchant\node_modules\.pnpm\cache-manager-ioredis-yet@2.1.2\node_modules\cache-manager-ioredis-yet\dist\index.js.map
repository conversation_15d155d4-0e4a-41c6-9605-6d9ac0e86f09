{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8GA,gCAaC;AAED,sCAOC;AApID,sDAKiB;AAEjB,mDAAqC;AAErC,MAAM,SAAS,GAAG,CAAC,KAAc,EAAE,EAAE,CACnC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAExE,MAAM,KAAK,GAAG,CAAC,KAAa,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,CAAC,CAAC;AAW1E,MAAM,MAAM,GAAG,CAAC,KAAc,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAEpD,MAAa,gBAAgB;IAER;IADnB,IAAI,GAAG,kBAAkB,CAAC;IAC1B,YAAmB,OAAe;QAAf,YAAO,GAAP,OAAO,CAAQ;IAAG,CAAC;CACvC;AAHD,4CAGC;AAEM,MAAM,gBAAgB,GAAG,KAAK,EAAK,CAAa,EAAE,EAAE;IACzD,IAAI,CAAC;QACH,OAAO,MAAM,CAAC,CAAC;IACjB,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,IAAI,CAAC,CAAC,CAAC,YAAY,gBAAgB,CAAC;YAAE,MAAM,CAAC,CAAC;IAChD,CAAC;AACH,CAAC,CAAC;AANW,QAAA,gBAAgB,oBAM3B;AACF,SAAS,OAAO,CACd,UAA2B,EAC3B,KAA0B,EAC1B,IAA4C,EAC5C,OAAgB;IAEhB,MAAM,WAAW,GACf,OAAO,EAAE,WAAW,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,IAAI,CAAC,CAAC;IAE7E,OAAO;QACL,KAAK,CAAC,GAAG,CAAI,GAAW;YACtB,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACtC,IAAI,GAAG,KAAK,SAAS,IAAI,GAAG,KAAK,IAAI;gBAAE,OAAO,SAAS,CAAC;;gBACnD,OAAO,KAAK,CAAC,GAAG,CAAM,CAAC;QAC9B,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG;YACvB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;gBACrB,MAAM,IAAI,gBAAgB,CAAC,IAAI,KAAK,4BAA4B,CAAC,CAAC;YACpE,MAAM,CAAC,GAAG,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACjD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC;gBAC5B,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;gBAC/C,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;QAChD,CAAC;QACD,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;YAClB,MAAM,CAAC,GAAG,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YACjD,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;gBACjC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,EAAE,CAAC;oBAChC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;wBACrB,MAAM,IAAI,gBAAgB,CACxB,IAAI,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAC9C,CAAC;oBACJ,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;gBACzC,CAAC;gBACD,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC;YACrB,CAAC;;gBACC,MAAM,UAAU,CAAC,IAAI,CACnB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;oBAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;wBACrB,MAAM,IAAI,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;oBACjE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAqB,CAAC;gBAClD,CAAC,CAAC,CACH,CAAC;QACN,CAAC;QACD,IAAI,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAChB,UAAU;aACP,IAAI,CAAC,IAAI,CAAC;aACV,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CACV,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACV,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAE,KAAK,CAAC,CAAC,CAAa,CAClE,CACF;QACL,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI;YAChB,MAAM,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,CAAC;QACD,KAAK,CAAC,GAAG,CAAC,GAAG;YACX,MAAM,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5B,CAAC;QACD,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC;QACxC,IAAI,EAAE,CAAC,OAAO,GAAG,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;QACtC,KAAK;QACL,WAAW;QACX,IAAI,MAAM;YACR,OAAO,UAAU,CAAC;QACpB,CAAC;KACY,CAAC;AAClB,CAAC;AAOM,KAAK,UAAU,UAAU,CAC9B,OAAyE;IAEzE,OAAO,KAAK,EAAE,CAAC;IACf,MAAM,UAAU,GACd,eAAe,IAAI,OAAO;QACxB,CAAC,CAAC,IAAI,iBAAK,CAAC,OAAO,CACf,OAAO,CAAC,aAAa,CAAC,KAAK,EAC3B,OAAO,CAAC,aAAa,CAAC,OAAO,CAC9B;QACH,CAAC,CAAC,IAAI,iBAAK,CAAC,OAAO,CAAC,CAAC;IAEzB,OAAO,aAAa,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AAC5C,CAAC;AAED,SAAgB,aAAa,CAAC,UAA2B,EAAE,OAAgB;IACzE,MAAM,KAAK,GAAG,KAAK,IAAI,EAAE;QACvB,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC;IAC7B,CAAC,CAAC;IACF,MAAM,IAAI,GAAG,CAAC,OAAe,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE3D,OAAO,OAAO,CAAC,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACnD,CAAC"}