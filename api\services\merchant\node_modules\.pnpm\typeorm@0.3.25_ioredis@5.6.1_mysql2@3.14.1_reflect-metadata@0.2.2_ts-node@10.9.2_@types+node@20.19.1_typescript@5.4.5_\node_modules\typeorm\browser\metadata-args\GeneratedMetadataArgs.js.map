{"version": 3, "sources": ["../browser/src/metadata-args/GeneratedMetadataArgs.ts"], "names": [], "mappings": "", "file": "GeneratedMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for Generated decorator class.\n */\nexport interface GeneratedMetadataArgs {\n    /**\n     * Class to which decorator is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * Class's property name to which decorator is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * Generation strategy.\n     */\n    readonly strategy: \"uuid\" | \"increment\" | \"rowid\"\n}\n"], "sourceRoot": ".."}