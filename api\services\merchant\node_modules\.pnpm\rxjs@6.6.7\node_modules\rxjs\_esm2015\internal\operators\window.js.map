{"version": 3, "file": "window.js", "sources": ["../../../src/internal/operators/window.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGrC,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AA6CjG,MAAM,UAAU,MAAM,CAAI,gBAAiC;IACzD,OAAO,SAAS,sBAAsB,CAAC,MAAqB;QAC1D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,cAAc;IAElB,YAAoB,gBAAiC;QAAjC,qBAAgB,GAAhB,gBAAgB,CAAiB;IACrD,CAAC;IAED,IAAI,CAAC,UAAqC,EAAE,MAAW;QACrD,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC1D,MAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAC9B,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC1G;QACD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;CACF;AAOD,MAAM,gBAAoB,SAAQ,qBAA6B;IAI7D,YAAY,WAAsC;QAChD,KAAK,CAAC,WAAW,CAAC,CAAC;QAHb,WAAM,GAAe,IAAI,OAAO,EAAK,CAAC;QAI5C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,UAAU;QACR,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,WAAW,CAAC,KAAU;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;IAC/B,CAAC;IAGD,YAAY;QACV,IAAI,CAAC,MAAM,GAAG,IAAK,CAAC;IACtB,CAAC;IAEO,UAAU;QAChB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;QACjD,WAAW,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;CACF"}