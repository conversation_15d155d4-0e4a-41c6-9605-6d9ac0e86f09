{"version": 3, "sources": ["../browser/src/error/CustomRepositoryNotFoundError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,6BAA8B,SAAQ,YAAY;IAC3D,YAAY,UAAe;QACvB,KAAK,CACD,qBACI,OAAO,UAAU,KAAK,UAAU;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IACjC,kBAAkB;YACd,0DAA0D,CACjE,CAAA;IACL,CAAC;CACJ", "file": "CustomRepositoryNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if custom repository was not found.\n */\nexport class CustomRepositoryNotFoundError extends TypeORMError {\n    constructor(repository: any) {\n        super(\n            `Custom repository ${\n                typeof repository === \"function\"\n                    ? repository.name\n                    : repository.constructor.name\n            } was not found. ` +\n                `Did you forgot to put @EntityRepository decorator on it?`,\n        )\n    }\n}\n"], "sourceRoot": ".."}