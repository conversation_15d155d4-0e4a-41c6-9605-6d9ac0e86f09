#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.run = exports.getPrettierVersion = exports.getEslintVersion = exports.getNodeVersion = void 0;
const path = require("path");
const meow = require("meow");
const updateNotifier = require("update-notifier");
const init_1 = require("./init");
const clean_1 = require("./clean");
const util_1 = require("./util");
const execa = require("execa");
// eslint-disable-next-line @typescript-eslint/no-var-requires
const packageJson = require('../../package.json');
const eslint = require.resolve('eslint/bin/eslint');
const logger = console;
const cli = meow({
    help: `
	Usage
	  $ mwts <verb> [<file>...] [options]

    Verb can be:
      init        Adds default npm scripts to your package.json.
      lint        Checks code for formatting and lint issues.
      check       Alias for lint. Kept for backward compatibility.
      fix         Fixes formatting and linting issues (if possible).
      clean       Removes all files generated by the build.

  Options
    --help        Prints this help message.
    -y, --yes     Assume a yes answer for every prompt.
    -n, --no      Assume a no answer for every prompt.
    --dry-run     Don't make any actual changes.
    --yarn        Use yarn instead of npm.

	Examples
    $ mwts init -y
    $ mwts lint
    $ mwts fix
    $ mwts fix src/file1.ts src/file2.ts
    $ mwts clean`,
    flags: {
        help: { type: 'boolean' },
        yes: { type: 'boolean', alias: 'y' },
        no: { type: 'boolean', alias: 'n' },
        dryRun: { type: 'boolean' },
        yarn: { type: 'boolean' },
    },
});
/**
 * Get the current version of node.js being run.
 * Exported purely for stubbing purposes.
 * @private
 */
function getNodeVersion() {
    return process.version;
}
exports.getNodeVersion = getNodeVersion;
function getEslintVersion() {
    const packageJson = (0, util_1.readJSON)(require.resolve('eslint/package.json'));
    return packageJson.version;
}
exports.getEslintVersion = getEslintVersion;
function getPrettierVersion() {
    const packageJson = (0, util_1.readJSON)(require.resolve('prettier/package.json'));
    return packageJson.version;
}
exports.getPrettierVersion = getPrettierVersion;
function usage(msg) {
    if (msg) {
        logger.error(msg);
    }
    cli.showHelp(1);
}
async function run(verb, files) {
    // throw if running on an old version of nodejs
    const nodeMajorVersion = Number(getNodeVersion().slice(1).split('.')[0]);
    console.log(`Node.js Version: ${nodeMajorVersion}`);
    console.log(`ESLint Version: ${getEslintVersion()}`);
    console.log(`Pretteir Version: ${getPrettierVersion()}`);
    if (nodeMajorVersion < 10) {
        throw new Error(`mwts requires node.js 10.x or up. You are currently running
      ${process.version}, which is not supported. Please upgrade to
      a safe, secure version of nodejs!`);
    }
    const options = {
        dryRun: cli.flags.dryRun || false,
        // Paths are relative to the transpiled output files.
        mwtsRootDir: path.resolve(__dirname, '../..'),
        targetRootDir: process.cwd(),
        yes: cli.flags.yes || cli.flags.y || false,
        no: cli.flags.no || cli.flags.n || false,
        logger,
        yarn: cli.flags.yarn || (0, util_1.isYarnUsed)(),
    };
    // Linting/formatting depend on typescript. We don't want to load the
    // typescript module during init, since it might not exist.
    // See: https://github.com/google/gts/issues/48
    if (verb === 'init') {
        return (0, init_1.init)(options);
    }
    const flags = Object.assign([], files);
    if (flags.length === 0) {
        flags.push('**/*.ts', '**/*.js', '**/*.tsx', '**/*.jsx', '--no-error-on-unmatched-pattern');
    }
    switch (verb) {
        case 'lint':
        case 'check': {
            try {
                await execa('node', [eslint, ...flags], {
                    stdio: 'inherit',
                });
                return true;
            }
            catch (e) {
                return false;
            }
        }
        case 'fix': {
            const fixFlag = options.dryRun ? '--fix-dry-run' : '--fix';
            try {
                await execa('node', [eslint, fixFlag, ...flags], {
                    stdio: 'inherit',
                });
                return true;
            }
            catch (e) {
                console.error(e);
                return false;
            }
        }
        case 'clean':
            return (0, clean_1.clean)(options);
        default:
            usage(`Unknown verb: ${verb}`);
            return false;
    }
}
exports.run = run;
updateNotifier({ pkg: packageJson }).notify();
if (cli.input.length < 1) {
    usage();
}
run(cli.input[0], cli.input.slice(1)).then(success => {
    if (!success) {
        // eslint-disable-next-line no-process-exit
        process.exit(1);
    }
});
//# sourceMappingURL=cli.js.map