{"version": 3, "file": "builder.js", "sourceRoot": "", "sources": ["../src/builder.ts"], "names": [], "mappings": ";;;AAEA,MAAa,WAAW;IAGtB,YAAY,UAAyB;QACnC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChB,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAED,QAAQ;QACN,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS;QACP,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG;gBAC3B,IAAI,EAAE,WAAW;aAClB,CAAC;SACH;IACH,CAAC;IACD,WAAW;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IAC3C,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;IAC1C,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IAED,cAAc;QACZ,OAAO,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IACxC,CAAC;IAED,gBAAgB;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;IAC1C,CAAC;IAED,MAAM;QACJ,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG;gBAC3B,IAAI,EAAE,WAAW;aAClB,CAAC;SACH;QAED,OAAO;YACL,GAAG,IAAI,CAAC,UAAU;YAClB,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE;YAC5B,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,SAAS,EAAE,IAAI,CAAC,YAAY,EAAE;YAC9B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;YAC1B,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE;YACxB,WAAW,EAAE,IAAI,CAAC,cAAc,EAAE;YAClC,aAAa,EAAE,IAAI,CAAC,gBAAgB,EAAE;SACvC,CAAC;IACJ,CAAC;CACF;AAnFD,kCAmFC"}