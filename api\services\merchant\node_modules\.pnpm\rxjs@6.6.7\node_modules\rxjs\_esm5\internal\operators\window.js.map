{"version": 3, "file": "window.js", "sources": ["../../../src/internal/operators/window.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAGrC,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AA6CjG,MAAM,UAAU,MAAM,CAAI,gBAAiC;IACzD,OAAO,SAAS,sBAAsB,CAAC,MAAqB;QAC1D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAC3D,CAAC,CAAC;AACJ,CAAC;AAED;IAEE,wBAAoB,gBAAiC;QAAjC,qBAAgB,GAAhB,gBAAgB,CAAiB;IACrD,CAAC;IAED,6BAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,IAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC1D,IAAM,kBAAkB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;YAC9B,gBAAgB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,qBAAqB,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;SAC1G;QACD,OAAO,kBAAkB,CAAC;IAC5B,CAAC;IACH,qBAAC;AAAD,CAAC,AAbD,IAaC;AAOD;IAAkC,4CAA6B;IAI7D,0BAAY,WAAsC;QAAlD,YACE,kBAAM,WAAW,CAAC,SAEnB;QALO,YAAM,GAAe,IAAI,OAAO,EAAK,CAAC;QAI5C,WAAW,CAAC,IAAI,CAAC,KAAI,CAAC,MAAM,CAAC,CAAC;;IAChC,CAAC;IAED,qCAAU,GAAV;QACE,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED,sCAAW,GAAX,UAAY,KAAU;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,yCAAc,GAAd;QACE,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAES,gCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAES,iCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;IAC/B,CAAC;IAES,oCAAS,GAAnB;QACE,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,CAAC,QAAS,EAAE,CAAC;IAC/B,CAAC;IAGD,uCAAY,GAAZ;QACE,IAAI,CAAC,MAAM,GAAG,IAAK,CAAC;IACtB,CAAC;IAEO,qCAAU,GAAlB;QACE,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;QACD,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;QACjD,WAAW,CAAC,IAAK,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IACH,uBAAC;AAAD,CAAC,AAjDD,CAAkC,qBAAqB,GAiDtD"}