/// <reference types="node" />
/// <reference types="node" />
import type { Channel, Options } from 'amqplib';
declare class ChannelManager {
    private connection;
    private channel;
    private channelList;
    constructor(connection: any);
    assertQueue(queue: string, options?: Options.AssertQueue): Promise<import("amqplib").Replies.AssertQueue>;
    createConfirmChannel(queueName: any): Promise<Channel>;
    createChannel(queueName: any): Promise<Channel>;
    sendToQueue(queueName: string, content: Buffer, options?: Options.Publish): boolean;
    sendToExchange(exchange: string, routingKey: string, content: Buffer, options?: Options.Publish): boolean;
    assertExchange(exchange: string, type: 'direct' | 'topic' | 'headers' | 'fanout' | 'match' | string, options?: Options.AssertExchange): Promise<import("amqplib").Replies.AssertExchange>;
    close(): Promise<void>;
}
export declare function createRabbitMQProducer(options: {
    url?: string;
}): Promise<ChannelManager>;
export declare function createRabbitMQProducer(queueName: string, options: {
    url?: string;
    isConfirmChannel?: boolean;
    mock?: boolean;
}): Promise<ChannelManager>;
export {};
//# sourceMappingURL=rabbitMQ.d.ts.map