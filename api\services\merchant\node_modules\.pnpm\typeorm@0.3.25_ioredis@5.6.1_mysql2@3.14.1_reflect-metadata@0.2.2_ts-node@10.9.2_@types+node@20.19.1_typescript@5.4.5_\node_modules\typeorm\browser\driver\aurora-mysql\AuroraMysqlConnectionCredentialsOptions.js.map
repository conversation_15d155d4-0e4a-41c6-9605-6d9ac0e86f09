{"version": 3, "sources": ["../browser/src/driver/aurora-mysql/AuroraMysqlConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "AuroraMysqlConnectionCredentialsOptions.js", "sourcesContent": ["/**\n * MySQL specific connection credential options.\n *\n * @see https://github.com/mysqljs/mysql#connection-options\n */\nexport interface AuroraMysqlConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly url?: string\n\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * Object with ssl parameters or a string containing name of ssl profile.\n     */\n    readonly ssl?: any\n}\n"], "sourceRoot": "../.."}