{"version": 3, "sources": ["../browser/src/metadata/types/EventListenerTypes.ts"], "names": [], "mappings": "AAgBA;;GAEG;AACH,MAAM,OAAO,kBAAkB;;AACpB,6BAAU,GAAG,YAAqB,CAAA;AAClC,gCAAa,GAAG,eAAwB,CAAA;AACxC,+BAAY,GAAG,cAAuB,CAAA;AACtC,gCAAa,GAAG,eAAwB,CAAA;AACxC,+BAAY,GAAG,cAAuB,CAAA;AACtC,gCAAa,GAAG,eAAwB,CAAA;AACxC,+BAAY,GAAG,cAAuB,CAAA;AACtC,qCAAkB,GAAG,oBAA6B,CAAA;AAClD,oCAAiB,GAAG,mBAA4B,CAAA;AAChD,iCAAc,GAAG,gBAAyB,CAAA;AAC1C,gCAAa,GAAG,eAAwB,CAAA", "file": "EventListenerTypes.js", "sourcesContent": ["/**\n * All types that entity listener can be.\n */\nexport type EventListenerType =\n    | \"after-load\"\n    | \"before-insert\"\n    | \"after-insert\"\n    | \"before-update\"\n    | \"after-update\"\n    | \"before-remove\"\n    | \"after-remove\"\n    | \"before-soft-remove\"\n    | \"after-soft-remove\"\n    | \"before-recover\"\n    | \"after-recover\"\n\n/**\n * Provides a constants for each entity listener type.\n */\nexport class EventListenerTypes {\n    static AFTER_LOAD = \"after-load\" as const\n    static BEFORE_INSERT = \"before-insert\" as const\n    static AFTER_INSERT = \"after-insert\" as const\n    static BEFORE_UPDATE = \"before-update\" as const\n    static AFTER_UPDATE = \"after-update\" as const\n    static BEFORE_REMOVE = \"before-remove\" as const\n    static AFTER_REMOVE = \"after-remove\" as const\n    static BEFORE_SOFT_REMOVE = \"before-soft-remove\" as const\n    static AFTER_SOFT_REMOVE = \"after-soft-remove\" as const\n    static BEFORE_RECOVER = \"before-recover\" as const\n    static AFTER_RECOVER = \"after-recover\" as const\n}\n"], "sourceRoot": "../.."}