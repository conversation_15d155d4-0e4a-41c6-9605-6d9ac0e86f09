import { IMidwayApplication, IMidwayContext } from '@midwayjs/core';
export declare function mockSession(app: IMidwayApplication<any>, key: string, value: any, group?: string): void;
export declare function mockHeader(app: IMidwayApplication<any>, headerKey: string, headerValue: string, group?: string): void;
export declare function mockClassProperty(clzz: new (...args: any[]) => any, propertyName: string, value: any, group?: string): void;
export declare function mockProperty(obj: any, key: string, value: any, group?: string): void;
export declare function restoreAllMocks(): void;
export declare function restoreMocks(group?: string): void;
export declare function mockContext(app: IMidwayApplication<any>, key: string | ((ctx: IMidwayContext) => void), value?: PropertyDescriptor | any, group?: string): void;
//# sourceMappingURL=mock.d.ts.map