"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagTypes = exports.COOL_METHOD_TAG_KEY = exports.COOL_URL_TAG_KEY = void 0;
exports.CoolUrlTag = CoolUrlTag;
exports.CoolTag = CoolTag;
const core_1 = require("@midwayjs/core");
exports.COOL_URL_TAG_KEY = 'decorator:cool:url:tag';
exports.COOL_METHOD_TAG_KEY = 'decorator:cool:method:tag';
var TagTypes;
(function (TagTypes) {
    TagTypes["IGNORE_TOKEN"] = "ignoreToken";
    TagTypes["IGNORE_SIGN"] = "ignoreSign";
})(TagTypes || (exports.TagTypes = TagTypes = {}));
/**
 * 打标记
 * @param data
 * @returns
 */
function CoolUrlTag(data) {
    return (target) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, core_1.saveModule)(exports.COOL_URL_TAG_KEY, target);
        // 保存一些元数据信息，任意你希望存的东西
        (0, core_1.saveClassMetadata)(exports.COOL_URL_TAG_KEY, data, target);
    };
}
/**
 * 方法打标记
 * @param data
 * @returns
 */
function CoolTag(tag) {
    return (target, key, descriptor) => {
        (0, core_1.savePropertyDataToClass)(exports.COOL_METHOD_TAG_KEY, {
            key,
            tag,
        }, target, key);
        return descriptor;
    };
}
