"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolDecorator = void 0;
const cache_1 = require("./cache");
const cache_manager_1 = require("@midwayjs/cache-manager");
const core_1 = require("@midwayjs/core");
const typeorm_1 = require("@midwayjs/typeorm");
const comm_1 = require("../exception/comm");
const transaction_1 = require("./transaction");
const md5 = require("md5");
const data_1 = require("../tag/data");
/**
 * 装饰器
 */
let CoolDecorator = class CoolDecorator {
    async init() {
        // 事务
        await this.transaction();
        // 缓存
        await this.cache();
        // URL标签
        await this.coolUrlTagData.init();
    }
    /**
     * 缓存
     */
    async cache() {
        this.decoratorService.registerMethodHandler(cache_1.COOL_CACHE, (options) => {
            return {
                around: async (joinPoint) => {
                    const key = md5(joinPoint.target.constructor.name +
                        joinPoint.methodName +
                        JSON.stringify(joinPoint.args));
                    // 缓存有数据就返回
                    let data = await this.midwayCache.get(key);
                    if (data) {
                        return JSON.parse(data);
                    }
                    else {
                        // 执行原始方法
                        data = await joinPoint.proceed(...joinPoint.args);
                        await this.midwayCache.set(key, JSON.stringify(data), options.metadata);
                    }
                    return data;
                },
            };
        });
    }
    /**
     * 事务
     */
    async transaction() {
        this.decoratorService.registerMethodHandler(transaction_1.COOL_TRANSACTION, (options) => {
            return {
                around: async (joinPoint) => {
                    const option = options.metadata;
                    const dataSource = this.typeORMDataSourceManager.getDataSource((option === null || option === void 0 ? void 0 : option.connectionName) || "default");
                    const queryRunner = dataSource.createQueryRunner();
                    await queryRunner.connect();
                    if (option && option.isolation) {
                        await queryRunner.startTransaction(option.isolation);
                    }
                    else {
                        await queryRunner.startTransaction();
                    }
                    let data;
                    try {
                        joinPoint.args.push(queryRunner);
                        data = await joinPoint.proceed(...joinPoint.args);
                        await queryRunner.commitTransaction();
                    }
                    catch (error) {
                        await queryRunner.rollbackTransaction();
                        throw new comm_1.CoolCommException(error.message);
                    }
                    finally {
                        await queryRunner.release();
                    }
                    return data;
                },
            };
        });
    }
};
exports.CoolDecorator = CoolDecorator;
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolDecorator.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayDecoratorService)
], CoolDecorator.prototype, "decoratorService", void 0);
__decorate([
    (0, core_1.InjectClient)(cache_manager_1.CachingFactory, "default"),
    __metadata("design:type", Object)
], CoolDecorator.prototype, "midwayCache", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", data_1.CoolUrlTagData)
], CoolDecorator.prototype, "coolUrlTagData", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoolDecorator.prototype, "init", null);
exports.CoolDecorator = CoolDecorator = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolDecorator);
