{"version": 3, "file": "method-signature-style.js", "sourceRoot": "", "sources": ["../../src/rules/method-signature-style.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAKhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,WAAW,EACT,2EAA2E;YAC7E,aAAa,EACX,2EAA2E;SAC9E;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;aAC7B;SACF;KACF;IACD,cAAc,EAAE,CAAC,UAAU,CAAC;IAE5B,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,YAAY,CACnB,IAA+D;YAE/D,IAAI,GAAG,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACvC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;aAClB;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;aACjB;YACD,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,GAAG,GAAG,YAAY,GAAG,EAAE,CAAC;aACzB;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,SAAS,eAAe,CACtB,IAA0D;YAE1D,IAAI,MAAM,GAAG,IAAI,CAAC;YAClB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAClC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,EACnE,8CAA8C,CAC/C,CAAC;gBACF,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAClC,UAAU,CAAC,aAAa,CACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,EACnC,IAAI,CAAC,mBAAmB,CACzB,EACD,4CAA4C,CAC7C,CAAC;gBAEF,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,SAAS,CAChC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EACrB,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CACtB,CAAC;aACH;YACD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;gBAC/B,MAAM,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC3D,MAAM,GAAG,GAAG,UAAU,GAAG,MAAM,EAAE,CAAC;aACnC;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,SAAS,mBAAmB,CAC1B,IAA0D;YAE1D,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI;gBAC5B,CAAC,CAAC,2EAA2E;oBAC3E,qDAAqD;oBACrD,KAAK;gBACP,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;QACzD,CAAC;QAED,SAAS,YAAY,CAAC,IAAmB;YACvC,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChD,IACE,SAAS;gBACT,CAAC,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,EAClE;gBACA,OAAO,SAAS,CAAC,KAAK,CAAC;aACxB;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,SAAS,6BAA6B,CAAC,IAAmB;YACxD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAChB,OAAO,KAAK,CAAC;aACd;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE;gBAC3D,OAAO,IAAI,CAAC;aACb;YAED,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE;gBAC/C,OAAO,KAAK,CAAC;aACd;YACD,OAAO,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;QAED,uCACK,CAAC,IAAI,KAAK,UAAU,IAAI;YACzB,iBAAiB,CAAC,UAAU;gBAC1B,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,EAAE;oBAChC,OAAO;iBACR;gBAED,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;gBACjC,MAAM,OAAO,GACX,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;oBAC7C,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,MAAK,sBAAc,CAAC,aAAa;wBAC/C,CAAC,CAAC,MAAM,CAAC,OAAO;wBAChB,CAAC,CAAC,EAAE,CAAC;gBAET,MAAM,wBAAwB,GAC5B,OAAO,CAAC,MAAM,CACZ,CAAC,OAAO,EAAyC,EAAE,CACjD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACjD,OAAO,KAAK,UAAU;oBACtB,YAAY,CAAC,OAAO,CAAC,KAAK,YAAY,CAAC,UAAU,CAAC,CACrD,CAAC;gBACJ,MAAM,cAAc,GAAG,6BAA6B,CAAC,UAAU,CAAC,CAAC;gBAEjE,IAAI,wBAAwB,CAAC,MAAM,GAAG,CAAC,EAAE;oBACvC,IAAI,cAAc,EAAE;wBAClB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;yBACzB,CAAC,CAAC;qBACJ;yBAAM;wBACL,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,UAAU;4BAChB,SAAS,EAAE,aAAa;4BACxB,CAAC,GAAG,CAAC,KAAK;gCACR,MAAM,WAAW,GAAG;oCAClB,UAAU;oCACV,GAAG,wBAAwB;iCAC5B,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gCACrD,MAAM,UAAU,GAAG,WAAW;qCAC3B,GAAG,CAAC,IAAI,CAAC,EAAE;oCACV,MAAM,MAAM,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;oCACrC,MAAM,UAAU,GAAG,mBAAmB,CAAC,IAAI,CAAC,CAAC;oCAC7C,OAAO,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;gCACxC,CAAC,CAAC;qCACD,IAAI,CAAC,KAAK,CAAC,CAAC;gCACf,MAAM,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gCACrC,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;gCAC3C,MAAM,KAAK,CAAC,WAAW,CACrB,UAAU,EACV,GAAG,GAAG,KAAK,UAAU,GAAG,SAAS,EAAE,CACpC,CAAC;gCACF,KAAK,MAAM,IAAI,IAAI,wBAAwB,EAAE;oCAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;oCAChD,IAAI,SAAS,EAAE;wCACb,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;wCACtD,IAAI,SAAS,EAAE;4CACb,MAAM,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;4CACzB,MAAM,KAAK,CAAC,gBAAgB,CAC1B,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACxC,EAAE,CACH,CAAC;yCACH;qCACF;iCACF;4BACH,CAAC;yBACF,CAAC,CAAC;qBACJ;oBACD,OAAO;iBACR;gBAED,IAAI,cAAc,EAAE;oBAClB,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,aAAa;qBACzB,CAAC,CAAC;iBACJ;qBAAM;oBACL,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,UAAU;wBAChB,SAAS,EAAE,aAAa;wBACxB,GAAG,EAAE,KAAK,CAAC,EAAE;4BACX,MAAM,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;4BACrC,MAAM,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;4BAC3C,MAAM,UAAU,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;4BACnD,MAAM,SAAS,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;4BAC3C,OAAO,KAAK,CAAC,WAAW,CACtB,UAAU,EACV,GAAG,GAAG,KAAK,MAAM,OAAO,UAAU,GAAG,SAAS,EAAE,CACjD,CAAC;wBACJ,CAAC;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC,GACC,CAAC,IAAI,KAAK,QAAQ,IAAI;YACvB,mBAAmB,CAAC,YAAY;;gBAC9B,MAAM,QAAQ,GAAG,MAAA,YAAY,CAAC,cAAc,0CAAE,cAAc,CAAC;gBAC7D,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,EAAE;oBACpD,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,YAAY;oBAClB,SAAS,EAAE,eAAe;oBAC1B,GAAG,EAAE,KAAK,CAAC,EAAE;wBACX,MAAM,GAAG,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;wBACvC,MAAM,MAAM,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;wBACzC,MAAM,UAAU,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;wBACjD,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;wBAC7C,OAAO,KAAK,CAAC,WAAW,CACtB,YAAY,EACZ,GAAG,GAAG,GAAG,MAAM,KAAK,UAAU,GAAG,SAAS,EAAE,CAC7C,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC,EACF;IACJ,CAAC;CACF,CAAC,CAAC"}