"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SpecBuilder = void 0;
class SpecBuilder {
    constructor(originData) {
        this.originData = originData;
        this.validate();
        this.transform();
    }
    validate() {
        return true;
    }
    transform() {
        const serviceData = this.originData['service'];
        if (typeof serviceData === 'string') {
            this.originData['service'] = {
                name: serviceData,
            };
        }
    }
    getProvider() {
        return this.originData['provider'] || {};
    }
    getCustom() {
        return this.originData['custom'];
    }
    getFunctions() {
        return this.originData['functions'];
    }
    getResources() {
        return this.originData['resources'];
    }
    getPackage() {
        return this.originData['package'] || {};
    }
    getPlugins() {
        return this.originData['plugins'];
    }
    getService() {
        return this.originData['service'];
    }
    getLayers() {
        return this.originData['layers'];
    }
    getAggregation() {
        return this.originData['aggregation'];
    }
    getFunctionsRule() {
        return this.originData['functionsRule'];
    }
    toJSON() {
        const serviceData = this.originData['service'];
        if (typeof serviceData === 'string') {
            this.originData['service'] = {
                name: serviceData,
            };
        }
        return {
            ...this.originData,
            service: this.getService(),
            provider: this.getProvider(),
            custom: this.getCustom(),
            functions: this.getFunctions(),
            resources: this.getResources(),
            package: this.getPackage(),
            plugins: this.getPlugins(),
            layers: this.getLayers(),
            aggregation: this.getAggregation(),
            functionsRule: this.getFunctionsRule(),
        };
    }
}
exports.SpecBuilder = SpecBuilder;
//# sourceMappingURL=builder.js.map