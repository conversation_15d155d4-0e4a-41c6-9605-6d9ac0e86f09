import { IMidwayContext } from '@midwayjs/core';
export declare const CACHE_DECORATOR_KEY = "cache-manager:caching";
export type CachingDecoratorKeyOptions = string | ((options: {
    methodArgs: any[];
    ctx?: IMidwayContext;
    target: any;
}) => string);
export declare function Caching(cacheInstanceName: string, cacheKeyOrTTL?: CachingDecoratorKeyOptions | number, ttl?: number): MethodDecorator;
//# sourceMappingURL=cacheKey.d.ts.map