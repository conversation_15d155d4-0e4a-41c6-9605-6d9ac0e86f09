"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractExpressLikeValue = exports.extractKoaLikeValue = void 0;
const decorator_1 = require("../decorator");
const index_1 = require("./index");
const extractKoaLikeValue = (key, data, paramType) => {
    if (decorator_1.ALL === data) {
        data = undefined;
    }
    return function (ctx, next) {
        switch (key) {
            case decorator_1.RouteParamTypes.NEXT:
                return next;
            case decorator_1.RouteParamTypes.BODY:
                return (0, index_1.transformRequestObjectByType)(data && ctx.request.body ? ctx.request.body[data] : ctx.request.body, paramType);
            case decorator_1.RouteParamTypes.PARAM:
                return (0, index_1.transformRequestObjectByType)(data ? ctx.params[data] : ctx.params, paramType);
            case decorator_1.RouteParamTypes.QUERY:
                return (0, index_1.transformRequestObjectByType)(data ? ctx.query[data] : ctx.query, paramType);
            case decorator_1.RouteParamTypes.HEADERS:
                return (0, index_1.transformRequestObjectByType)(data ? ctx.get(data) : ctx.headers, paramType);
            case decorator_1.RouteParamTypes.SESSION:
                return (0, index_1.transformRequestObjectByType)(data ? ctx.session[data] : ctx.session, paramType);
            case decorator_1.RouteParamTypes.FILESTREAM:
                if (ctx.getFileStream) {
                    return ctx.getFileStream(data);
                }
                else if (ctx.files) {
                    return ctx.files[0];
                }
                else {
                    return undefined;
                }
            case decorator_1.RouteParamTypes.FILESSTREAM:
                if (ctx.multipart) {
                    return ctx.multipart(data);
                }
                else if (ctx.files) {
                    return ctx.files;
                }
                else {
                    return undefined;
                }
            case decorator_1.RouteParamTypes.REQUEST_PATH:
                return ctx['path'];
            case decorator_1.RouteParamTypes.REQUEST_IP:
                return ctx['ip'];
            case decorator_1.RouteParamTypes.QUERIES:
                if (ctx.queries) {
                    return (0, index_1.transformRequestObjectByType)(data ? ctx.queries[data] : ctx.queries, paramType);
                }
                else {
                    return (0, index_1.transformRequestObjectByType)(data ? ctx.query[data] : ctx.query, paramType);
                }
            case decorator_1.RouteParamTypes.FIELDS:
                return data ? ctx.fields[data] : ctx.fields;
            case decorator_1.RouteParamTypes.CUSTOM:
                return data ? data(ctx) : undefined;
            default:
                return null;
        }
    };
};
exports.extractKoaLikeValue = extractKoaLikeValue;
const extractExpressLikeValue = (key, data, paramType) => {
    if (decorator_1.ALL === data) {
        data = undefined;
    }
    return function (req, res, next) {
        switch (key) {
            case decorator_1.RouteParamTypes.NEXT:
                return next;
            case decorator_1.RouteParamTypes.BODY:
                return (0, index_1.transformRequestObjectByType)(data && req.body ? req.body[data] : req.body, paramType);
            case decorator_1.RouteParamTypes.PARAM:
                return (0, index_1.transformRequestObjectByType)(data ? req.params[data] : req.params, paramType);
            case decorator_1.RouteParamTypes.QUERY:
                return (0, index_1.transformRequestObjectByType)(data ? req.query[data] : req.query, paramType);
            case decorator_1.RouteParamTypes.HEADERS:
                return (0, index_1.transformRequestObjectByType)(data ? req.get(data) : req.headers, paramType);
            case decorator_1.RouteParamTypes.SESSION:
                return (0, index_1.transformRequestObjectByType)(data ? req.session[data] : req.session, paramType);
            case decorator_1.RouteParamTypes.FILESTREAM:
                return req.files ? req.files[0] : undefined;
            case decorator_1.RouteParamTypes.FILESSTREAM:
                return req.files;
            case decorator_1.RouteParamTypes.REQUEST_PATH:
                return req['baseUrl'];
            case decorator_1.RouteParamTypes.REQUEST_IP:
                return req['ip'];
            case decorator_1.RouteParamTypes.QUERIES:
                if (req.queries) {
                    return (0, index_1.transformRequestObjectByType)(data ? req.queries[data] : req.queries, paramType);
                }
                else {
                    return (0, index_1.transformRequestObjectByType)(data ? req.query[data] : req.query, paramType);
                }
            case decorator_1.RouteParamTypes.FIELDS:
                return data ? req.fields[data] : req.fields;
            case decorator_1.RouteParamTypes.CUSTOM:
                return data ? data(req, res) : undefined;
            default:
                return null;
        }
    };
};
exports.extractExpressLikeValue = extractExpressLikeValue;
//# sourceMappingURL=webRouterParam.js.map