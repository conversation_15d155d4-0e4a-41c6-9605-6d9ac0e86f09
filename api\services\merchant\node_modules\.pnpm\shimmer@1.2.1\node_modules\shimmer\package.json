{"name": "shimmer", "version": "1.2.1", "description": "Safe(r) monkeypatching for JavaScript.", "main": "index.js", "scripts": {"test": "standard && tap test/*.tap.js --coverage"}, "repository": {"type": "git", "url": "https://github.com/othiym23/shimmer.git"}, "keywords": ["monkeypatch", "swizzle", "wrapping", "danger", "hmm", "shim"], "author": "<PERSON> <<EMAIL>>", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"sinon": "^7.2.2", "standard": "^12.0.1", "tap": "^12.1.1"}}