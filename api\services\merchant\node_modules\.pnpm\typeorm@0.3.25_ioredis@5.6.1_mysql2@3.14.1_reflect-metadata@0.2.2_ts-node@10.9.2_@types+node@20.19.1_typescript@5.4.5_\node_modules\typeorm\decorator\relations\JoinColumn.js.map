{"version": 3, "sources": ["../../src/decorator/relations/JoinColumn.ts"], "names": [], "mappings": ";;AA8BA,gCAiBC;AA/CD,2CAAsD;AAyBtD;;;;GAIG;AACH,SAAgB,UAAU,CACtB,qBAA+D;IAE/D,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,qBAAqB,CAAC;YAChD,CAAC,CAAC,qBAAqB;YACvB,CAAC,CAAC,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAA;QACnC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACxB,IAAA,gCAAsB,GAAE,CAAC,WAAW,CAAC,IAAI,CAAC;gBACtC,MAAM,EAAE,MAAM,CAAC,WAAW;gBAC1B,YAAY,EAAE,YAAY;gBAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,oBAAoB,EAAE,OAAO,CAAC,oBAAoB;gBAClD,wBAAwB,EAAE,OAAO,CAAC,wBAAwB;aACnC,CAAC,CAAA;QAChC,CAAC,CAAC,CAAA;IACN,CAAC,CAAA;AACL,CAAC", "file": "JoinColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { JoinColumnMetadataArgs } from \"../../metadata-args/JoinColumnMetadataArgs\"\nimport { JoinColumnOptions } from \"../options/JoinColumnOptions\"\n\n/**\n * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.\n * It also can be used on both one-to-one and many-to-one relations to specify custom column name\n * or custom referenced column.\n */\nexport function JoinColumn(): PropertyDecorator\n\n/**\n * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.\n * It also can be used on both one-to-one and many-to-one relations to specify custom column name\n * or custom referenced column.\n */\nexport function JoinColumn(options: JoinColumnOptions): PropertyDecorator\n\n/**\n * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.\n * It also can be used on both one-to-one and many-to-one relations to specify custom column name\n * or custom referenced column.\n */\nexport function JoinColumn(options: JoinColumnOptions[]): PropertyDecorator\n\n/**\n * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.\n * It also can be used on both one-to-one and many-to-one relations to specify custom column name\n * or custom referenced column.\n */\nexport function JoinColumn(\n    optionsOrOptionsArray?: JoinColumnOptions | JoinColumnOptions[],\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        const options = Array.isArray(optionsOrOptionsArray)\n            ? optionsOrOptionsArray\n            : [optionsOrOptionsArray || {}]\n        options.forEach((options) => {\n            getMetadataArgsStorage().joinColumns.push({\n                target: object.constructor,\n                propertyName: propertyName,\n                name: options.name,\n                referencedColumnName: options.referencedColumnName,\n                foreignKeyConstraintName: options.foreignKeyConstraintName,\n            } as JoinColumnMetadataArgs)\n        })\n    }\n}\n"], "sourceRoot": "../.."}