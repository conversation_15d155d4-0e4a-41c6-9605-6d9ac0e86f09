const { <%=faasStarterName %> } = require('<%=faasModName %>');
const { asyncWrapper, start } = require('<%=starter %>');
<% if (clearCache) { %>
if (process.env.CLEAR_CACHE === 'true') {
  process.env.CLEAR_CACHE = 'false';
  try {
    const { clearContainerCache } = require('@midwayjs/core');
    clearContainerCache();
  } catch (e) {}
  try {
    const { clearAllModule } = require('@midwayjs/decorator');
    clearAllModule();
  } catch (e) {}
  try {
    const { clearAllLoggers } = require('@midwayjs/logger');
    clearAllLoggers();
  } catch (e) {}
}
<% } %>
<% if (functionMap) { %>
const { registerFunctionToIocByConfig } = require('./registerFunction.js');
const { join } = require('path');
<% } %>
const picomatch = require('picomatch');
const layers = [];
<% layerDeps.forEach(function(layer){ %>
try {
  const <%=layer.name%> = require('<%=layer.path%>');
  layers.push(<%=layer.name%>);
} catch(e) { }
<% }); %>

let starter;
let runtime;
let inited = false;
let initError;
<% if (advancePreventMultiInit) { %>
let isInitialing = false;
<% } %>

const initializeMethod = async (initializeContext = {}) => {
  <% if (advancePreventMultiInit) { %>
  inited = true;
  isInitialing = true;
  <% } %>
  layers.unshift(engine => {
    engine.addRuntimeExtension({
      async beforeFunctionStart(runtime) {
        starter = new <%=faasStarterName %>({ baseDir: __dirname, initializeContext, applicationAdapter: runtime, middleware: <%-JSON.stringify(middleware, null, 2)%>, preloadModules: <%-JSON.stringify(preloadModules, null, 2)%> });
        <% loadDirectory.forEach(function(dirName){ %>
          starter.loader.loadDirectory({ baseDir: '<%=dirName%>'});<% }) %>
        <% if (functionMap) { %>
        registerFunctionToIocByConfig(<%-JSON.stringify(functionMap, null, 2)%>, {
          baseDir: join(__dirname, 'dist'),
          context: starter.loader.getApplicationContext()
        });
        <% } %>
        await starter.start();
      }
    });
  })
  runtime = await start({
    layers: layers,
    getHandler: getHandler,
    initContext: initializeContext,
    runtimeConfig: <%-JSON.stringify(runtimeConfig)%>,
  });
  
  <% if (!advancePreventMultiInit) { %> inited = true; <% } %>
  <% if (advancePreventMultiInit) { %>
  isInitialing = false;
  <% } %>
};

const getHandler = (hanlderName) => {
  <% handlers.forEach(function(handlerData){ %>
    if (hanlderName === '<%=handlerData.name%>') {
      return <% if (handlerData.handler) {
      %> starter.handleInvokeWrapper('<%=handlerData.handler%>'); <% } else {
      %> async (ctx<% if (moreArgs) { %>, ...args<% } %>) => {
        const allHandlers = <%-JSON.stringify(handlerData.handlers, null, 2)%>;
        let handler = null;
        let ctxPath = ctx && ctx.path || '';
        if (ctxPath) {
          handler = allHandlers.find(handler => {
            return picomatch.isMatch(ctxPath, handler.router, { dot: true });
          });
        }

        if (handler) {
          return starter.handleInvokeWrapper(handler.handler)(ctx<% if (moreArgs) { %>, ...args<% } %>);
        }
        ctx.status = 404;
        ctx.set('Content-Type', 'text/html');
        return '<h1>404 Page Not Found</h1>';
      }; <% } %>
    }
  <% }); %>
}

<% if (advancePreventMultiInit) { %>
const waitInitial = () => {
  return new Promise(resolve => {
    if (!isInitialing) {
      return resolve();
    }
    setTimeout(() => {
      waitInitial().then(resolve);
    }, 50);
  });
};
<% } %>


exports.<%=initializer%> = asyncWrapper(async (...args) => {
  try {
    if (!inited) {
      await initializeMethod(...args);
    }
  } catch (e) {
    initError = e;
    throw e;
  }
});

<% handlers.forEach(function(handlerData){ %>
exports.<%=handlerData.name%> = asyncWrapper(async (...args) => {
  try {
    if (!inited) {
      await initializeMethod();
    }
  } catch (e) {
    initError = e;
    throw e;
  }
  if (initError) {
    throw initError;
  }
  <% if (advancePreventMultiInit) { %>
  if (isInitialing) {
    await waitInitial();
  }
  <% } %>

  const handler = getHandler('<%=handlerData.name%>');
  return runtime.asyncEvent(handler)(...args);
});
<% }); %>
