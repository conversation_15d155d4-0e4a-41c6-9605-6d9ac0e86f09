{"name": "trim-newlines", "version": "3.0.1", "description": "Trim newlines from the start and/or end of a string", "license": "MIT", "repository": "sindresorhus/trim-newlines", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["trim", "newline", "newlines", "linebreak", "lf", "crlf", "left", "right", "start", "end", "string", "remove", "delete", "strip"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}