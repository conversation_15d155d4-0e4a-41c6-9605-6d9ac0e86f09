{"name": "restore-cursor", "version": "3.1.0", "description": "Gracefully restore the CLI cursor on exit", "license": "MIT", "repository": "sindresorhus/restore-cursor", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["exit", "quit", "process", "graceful", "shutdown", "sigterm", "sigint", "terminate", "kill", "stop", "cli", "cursor", "ansi", "show", "term", "terminal", "console", "tty", "shell", "command-line"], "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "devDependencies": {"tsd": "^0.7.2", "xo": "^0.24.0"}}