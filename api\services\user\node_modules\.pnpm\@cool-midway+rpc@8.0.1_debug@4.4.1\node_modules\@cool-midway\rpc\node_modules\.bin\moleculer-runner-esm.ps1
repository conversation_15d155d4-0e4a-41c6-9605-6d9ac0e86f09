#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\jiangrenjie\api\services\user\node_modules\.pnpm\moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1\node_modules\moleculer\bin\node_modules;D:\jiangrenjie\api\services\user\node_modules\.pnpm\moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1\node_modules\moleculer\node_modules;D:\jiangrenjie\api\services\user\node_modules\.pnpm\moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1\node_modules;D:\jiangrenjie\api\services\user\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../../moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/moleculer-runner.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../../moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/moleculer-runner.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../../moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/moleculer-runner.mjs" $args
  } else {
    & "node$exe"  "$basedir/../../../../../../moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/moleculer-runner.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
