{"name": "prepend-http", "version": "2.0.0", "description": "Prepend `http://` to humanized URLs like todomvc.com and localhost", "license": "MIT", "repository": "sindresorhus/prepend-http", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["prepend", "protocol", "scheme", "url", "uri", "http", "https", "humanized"], "devDependencies": {"ava": "*", "xo": "*"}}