const mysql = require('mysql2/promise');

async function connectDB() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'wap.336101',
      database: 'merchant_service_db'
    });

    console.log('✅ 数据库连接成功！');
    
    // 查看菜单表结构
    console.log('\n📋 菜单表结构:');
    const [tableInfo] = await connection.execute('DESCRIBE merchant_sys_menu');
    console.table(tableInfo);
    
    // 查看菜单数据
    console.log('\n📊 菜单数据:');
    const [menuData] = await connection.execute('SELECT id, parentId, name, router, type, isShow FROM merchant_sys_menu ORDER BY parentId, id');
    console.table(menuData);
    
    // 查看菜单层级关系
    console.log('\n🌳 菜单层级关系:');
    const [hierarchyData] = await connection.execute(`
      SELECT 
        m1.id,
        m1.name,
        m1.parentId,
        m2.name as parentName,
        m1.router,
        m1.type,
        m1.isShow
      FROM merchant_sys_menu m1
      LEFT JOIN merchant_sys_menu m2 ON m1.parentId = m2.id
      ORDER BY m1.parentId, m1.id
    `);
    console.table(hierarchyData);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
  }
}

connectDB();
