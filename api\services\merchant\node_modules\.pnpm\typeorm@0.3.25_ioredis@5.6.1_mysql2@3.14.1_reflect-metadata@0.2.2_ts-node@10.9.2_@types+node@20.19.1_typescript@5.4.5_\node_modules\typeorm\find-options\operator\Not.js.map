{"version": 3, "sources": ["../../src/find-options/operator/Not.ts"], "names": [], "mappings": ";;AAOA,kBAEC;AATD,kDAA8C;AAE9C;;;;GAIG;AACH,SAAgB,GAAG,CAAI,KAA0B;IAC7C,OAAO,IAAI,2BAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AACzC,CAAC", "file": "Not.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Used to negate expression.\n * Example: { title: not(\"hello\") } will return entities where title not equal to \"hello\".\n */\nexport function Not<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"not\", value)\n}\n"], "sourceRoot": "../.."}