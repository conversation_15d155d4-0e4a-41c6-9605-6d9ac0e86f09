interface ErrorOption {
    cause?: Error;
    status?: number;
}
interface Convertable {
    [key: string]: string | number;
}
type ConvertString<T extends Convertable, Group extends string> = {
    [P in keyof T]: P extends string ? T[P] extends number ? `${Uppercase<Group>}_${T[P]}` : never : never;
};
/**
 * Register error group and code, return the standard ErrorCode
 * @param errorGroup
 * @param errorCodeMapping
 */
export declare function registerErrorCode<T extends Convertable, G extends string>(errorGroup: G, errorCodeMapping: T): ConvertString<T, G>;
export declare class MidwayError extends Error {
    code: number | string;
    cause: Error;
    constructor(message: string, options?: ErrorOption);
    constructor(message: string, code: string, options?: ErrorOption);
}
export type ResOrMessage = string | {
    message: string;
} | undefined;
export declare class MidwayHttpError extends MidwayError {
    status: number;
    constructor(resOrMessage: ResOrMessage, status: number);
    constructor(resOrMessage: ResOrMessage, status: number, code: string, options?: ErrorOption);
}
export {};
//# sourceMappingURL=base.d.ts.map