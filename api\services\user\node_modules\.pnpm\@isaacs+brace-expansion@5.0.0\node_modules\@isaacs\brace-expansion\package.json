{"name": "@isaacs/brace-expansion", "description": "Brace expansion as known from sh/bash", "version": "5.0.0", "files": ["dist"], "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "type": "module", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "prepare": "tshy", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "tap", "snap": "tap", "format": "prettier --write . --loglevel warn", "benchmark": "node benchmark/index.js", "typedoc": "typedoc --tsconfig .tshy/esm.json ./src/*.ts"}, "prettier": {"semi": false, "printWidth": 80, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "devDependencies": {"@types/brace-expansion": "^1.1.2", "@types/node": "^24.0.0", "mkdirp": "^3.0.1", "prettier": "^3.3.2", "tap": "^21.1.0", "tshy": "^3.0.2", "typedoc": "^0.28.5"}, "dependencies": {"@isaacs/balanced-match": "^4.0.1"}, "license": "MIT", "engines": {"node": "20 || >=22"}, "tshy": {"exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "main": "./dist/commonjs/index.js", "types": "./dist/commonjs/index.d.ts", "module": "./dist/esm/index.js"}