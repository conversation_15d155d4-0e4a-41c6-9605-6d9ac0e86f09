{"version": 3, "sources": ["../../src/data-source/DataSourceOptions.ts"], "names": [], "mappings": "", "file": "DataSourceOptions.js", "sourcesContent": ["import { CockroachConnectionOptions } from \"../driver/cockroachdb/CockroachConnectionOptions\"\nimport { MysqlConnectionOptions } from \"../driver/mysql/MysqlConnectionOptions\"\nimport { PostgresConnectionOptions } from \"../driver/postgres/PostgresConnectionOptions\"\nimport { SqliteConnectionOptions } from \"../driver/sqlite/SqliteConnectionOptions\"\nimport { SqlServerConnectionOptions } from \"../driver/sqlserver/SqlServerConnectionOptions\"\nimport { OracleConnectionOptions } from \"../driver/oracle/OracleConnectionOptions\"\nimport { MongoConnectionOptions } from \"../driver/mongodb/MongoConnectionOptions\"\nimport { CordovaConnectionOptions } from \"../driver/cordova/CordovaConnectionOptions\"\nimport { SqljsConnectionOptions } from \"../driver/sqljs/SqljsConnectionOptions\"\nimport { ReactNativeConnectionOptions } from \"../driver/react-native/ReactNativeConnectionOptions\"\nimport { NativescriptConnectionOptions } from \"../driver/nativescript/NativescriptConnectionOptions\"\nimport { ExpoConnectionOptions } from \"../driver/expo/ExpoConnectionOptions\"\nimport { AuroraMysqlConnectionOptions } from \"../driver/aurora-mysql/AuroraMysqlConnectionOptions\"\nimport { SapConnectionOptions } from \"../driver/sap/SapConnectionOptions\"\nimport { AuroraPostgresConnectionOptions } from \"../driver/aurora-postgres/AuroraPostgresConnectionOptions\"\nimport { BetterSqlite3ConnectionOptions } from \"../driver/better-sqlite3/BetterSqlite3ConnectionOptions\"\nimport { CapacitorConnectionOptions } from \"../driver/capacitor/CapacitorConnectionOptions\"\nimport { SpannerConnectionOptions } from \"../driver/spanner/SpannerConnectionOptions\"\n\n/**\n * DataSourceOptions is an interface with settings and options for specific DataSource.\n */\nexport type DataSourceOptions =\n    | MysqlConnectionOptions\n    | PostgresConnectionOptions\n    | CockroachConnectionOptions\n    | SqliteConnectionOptions\n    | SqlServerConnectionOptions\n    | SapConnectionOptions\n    | OracleConnectionOptions\n    | CordovaConnectionOptions\n    | NativescriptConnectionOptions\n    | ReactNativeConnectionOptions\n    | SqljsConnectionOptions\n    | MongoConnectionOptions\n    | AuroraMysqlConnectionOptions\n    | AuroraPostgresConnectionOptions\n    | ExpoConnectionOptions\n    | BetterSqlite3ConnectionOptions\n    | CapacitorConnectionOptions\n    | SpannerConnectionOptions\n"], "sourceRoot": ".."}