import { IMidwayApplication } from '../interface';
import { MidwayWebRouterService, RouterInfo } from '../service/webRouterService';
export declare abstract class WebControllerGenerator<Router extends {
    use: (...args: any[]) => void;
}> {
    readonly app: IMidwayApplication;
    readonly midwayWebRouterService: MidwayWebRouterService;
    protected constructor(app: IMidwayApplication, midwayWebRouterService: MidwayWebRouterService);
    /**
     * wrap controller string to middleware function
     * @param routeInfo
     */
    generateKoaController(routeInfo: RouterInfo): (ctx: any, next: any) => Promise<void>;
    loadMidwayController(routerHandler?: (newRouter: Router) => void): Promise<void>;
    abstract createRouter(routerOptions: any): Router;
    abstract generateController(routeInfo: RouterInfo): any;
}
//# sourceMappingURL=webGenerator.d.ts.map