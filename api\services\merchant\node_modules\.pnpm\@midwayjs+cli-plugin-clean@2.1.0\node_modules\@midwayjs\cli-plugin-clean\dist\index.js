"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CleanPlugin = void 0;
const command_core_1 = require("@midwayjs/command-core");
const path_1 = require("path");
const fs_extra_1 = require("fs-extra");
class CleanPlugin extends command_core_1.BasePlugin {
    constructor() {
        super(...arguments);
        this.commands = {
            clean: {
                usage: 'clean local log and cache',
                lifecycleEvents: ['clean'],
            },
        };
        this.hooks = {
            'clean:clean': this.clean.bind(this),
        };
    }
    async clean() {
        const { cwd } = this.core;
        if (!(0, fs_extra_1.existsSync)((0, path_1.join)(cwd, 'package.json'))) {
            this.core.cli.error(`[ Midway ] package.json not found in ${cwd}\n`);
            return;
        }
        await this.rmDir();
        const pkg = require((0, path_1.join)(cwd, 'package.json'));
        if (pkg['midway-bin-clean'] && pkg['midway-bin-clean'].length) {
            for (const file of pkg['midway-bin-clean']) {
                await this.safeRemove((0, path_1.join)(cwd, file));
                this.core.cli.log(`[ Midway ] clean ${file} success!`);
            }
            this.core.cli.log('[ Midway ] clean complete!');
        }
    }
    async rmDir() {
        const { cwd } = this.core;
        const rmDirName = ['logs', 'run', '.nodejs-cache'];
        for (const name of rmDirName) {
            await this.safeRemove((0, path_1.join)(cwd, name));
        }
        this.core.cli.log('[ Midway ] clean midway temporary files complete!');
    }
    safeRemove(path) {
        if (!(0, fs_extra_1.existsSync)(path)) {
            return;
        }
        return (0, fs_extra_1.remove)(path);
    }
}
exports.CleanPlugin = CleanPlugin;
//# sourceMappingURL=index.js.map