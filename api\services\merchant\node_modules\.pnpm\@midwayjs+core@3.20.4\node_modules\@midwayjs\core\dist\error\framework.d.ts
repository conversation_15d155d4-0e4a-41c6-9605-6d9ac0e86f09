import { MidwayError } from './base';
import { ObjectIdentifier } from '../interface';
export declare const FrameworkErrorEnum: {
    readonly UNKNOWN: "MIDWAY_10000";
    readonly COMMON: "MIDWAY_10001";
    readonly PARAM_TYPE: "MIDWAY_10002";
    readonly DEFINITION_NOT_FOUND: "MIDWAY_10003";
    readonly FEATURE_NO_LONGER_SUPPORTED: "MIDWAY_10004";
    readonly FEATURE_NOT_IMPLEMENTED: "MIDWAY_10004";
    readonly MISSING_CONFIG: "MIDWAY_10006";
    readonly MISSING_RESOLVER: "MIDWAY_10007";
    readonly DUPLICATE_ROUTER: "MIDWAY_10008";
    readonly USE_WRONG_METHOD: "MIDWAY_10009";
    readonly SINGLETON_INJECT_REQUEST: "MIDWAY_10010";
    readonly MISSING_IMPORTS: "MIDWAY_10011";
    readonly UTIL_HTTP_TIMEOUT: "MIDWAY_10012";
    readonly INCONSISTENT_VERSION: "MIDWAY_10013";
    readonly INVALID_CONFIG: "MIDWAY_10014";
    readonly DUPLICATE_CLASS_NAME: "MIDWAY_10015";
    readonly DUPLICATE_CONTROLLER_PREFIX_OPTIONS: "MIDWAY_10016";
    readonly RETRY_OVER_MAX_TIME: "MIDWAY_10017";
    readonly INVOKE_METHOD_FORBIDDEN: "MIDWAY_10018";
    readonly CODE_INVOKE_TIMEOUT: "MIDWAY_10019";
    readonly MAIN_FRAMEWORK_MISSING: "MIDWAY_10020";
    readonly INVALID_CONFIG_PROPERTY: "MIDWAY_10021";
    readonly EMPTY_VALUE: "MIDWAY_10022";
};
export declare class MidwayCommonError extends MidwayError {
    constructor(message: string);
}
export declare class MidwayParameterError extends MidwayError {
    constructor(message?: string);
}
export declare class MidwayDefinitionNotFoundError extends MidwayError {
    static readonly type: unique symbol;
    static isClosePrototypeOf(ins: MidwayDefinitionNotFoundError): boolean;
    constructor(identifier: ObjectIdentifier);
    updateErrorMsg(className: string): void;
}
export declare class MidwayFeatureNoLongerSupportedError extends MidwayError {
    constructor(message?: string);
}
export declare class MidwayFeatureNotImplementedError extends MidwayError {
    constructor(message?: string);
}
export declare class MidwayConfigMissingError extends MidwayError {
    constructor(configKey: string);
}
export declare class MidwayInvalidConfigError extends MidwayError {
    constructor(message?: string);
}
export declare class MidwayResolverMissingError extends MidwayError {
    constructor(type: string);
}
export declare class MidwayDuplicateRouteError extends MidwayError {
    constructor(routerUrl: string, existPos: string, existPosOther: string);
}
export declare class MidwayUseWrongMethodError extends MidwayError {
    constructor(wrongMethod: string, replacedMethod: string, describeKey?: string);
}
export declare class MidwaySingletonInjectRequestError extends MidwayError {
    constructor(singletonScopeName: string, requestScopeName: string);
}
export declare class MidwayMissingImportComponentError extends MidwayError {
    constructor(originName: string);
}
export declare class MidwayUtilHttpClientTimeoutError extends MidwayError {
    constructor(message: string);
}
export declare class MidwayInconsistentVersionError extends MidwayError {
    constructor();
}
export declare class MidwayDuplicateClassNameError extends MidwayError {
    constructor(className: string, existPath: string, existPathOther: string);
}
export declare class MidwayDuplicateControllerOptionsError extends MidwayError {
    constructor(prefix: string, existController: string, existControllerOther: string);
}
export declare class MidwayRetryExceededMaxTimesError extends MidwayError {
    constructor(methodName: any, times: number, err: Error);
}
export declare class MidwayInvokeForbiddenError extends MidwayError {
    constructor(methodName: string, module?: any);
}
export declare class MidwayCodeInvokeTimeoutError extends MidwayError {
    constructor(methodName: string, timeout: number);
}
export declare class MidwayMainFrameworkMissingError extends MidwayError {
    constructor();
}
export declare class MidwayInvalidConfigPropertyError extends MidwayError {
    constructor(propertyName: string, allowTypes?: string[]);
}
export declare class MidwayEmptyValueError extends MidwayError {
    constructor(msg: string);
}
//# sourceMappingURL=framework.d.ts.map