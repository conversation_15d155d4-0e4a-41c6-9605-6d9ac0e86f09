export declare function detectStatus(err: any): any;
export declare function accepts(ctx: any): "json" | "html";
export declare function sendToWormhole(stream: any): Promise<void>;
export declare function isProduction(app: any): boolean;
export declare const tpl = "\n<!DOCTYPE html>\n<html>\n  <head>\n    <title>Error - {{status}}</title>\n    <meta name=\"viewport\" content=\"user-scalable=no, width=device-width, initial-scale=1.0, maximum-scale=1.0\">\n\n    <style>\n      body {\n        padding: 50px 80px;\n        font: 14px \"Helvetica Neue\", Helvetica, sans-serif;\n      }\n\n      h1 {\n        font-size: 2em;\n        margin-bottom: 5px;\n      }\n\n      pre {\n        font-size: .8em;\n      }\n    </style>\n  </head>\n  <body>\n    <div id=\"error\">\n      <h1>Error</h1>\n      <p>Looks like something broke!</p>\n      <p><h2>Stack</h2></p>\n      <pre>\n        <code>\n{{stack}}\n        </code>\n      </pre>\n      <p><h2>Error Code</h2></p>\n      <pre>\n        <code>\n{{errorCode}}\n        </code>\n      </pre>\n    </div>\n  </body>\n</html>";
/**
 * Escape special characters in the given string of text.
 *
 * @param  {string} string The string to escape for inserting into HTML
 * @return {string}
 * @public
 */
export declare function escapeHtml(string: any): string;
//# sourceMappingURL=utils.d.ts.map