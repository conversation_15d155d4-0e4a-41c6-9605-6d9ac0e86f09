import { Init, Inject, Provide } from '@midwayjs/decorator';
import { BaseService, CoolCommException } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, Repository } from 'typeorm';
import { FinanceApplyInvoiceEntity } from '../../entity/apply/invoice';
import { FinanceUserInvoiceService } from '../user/invoice';

/**
 * 发票
 */
@Provide()
export class FinanceApplyInvoiceService extends BaseService {
  @InjectEntityModel(FinanceApplyInvoiceEntity)
  financeApplyInvoiceEntity: Repository<FinanceApplyInvoiceEntity>;

  @Inject()
  financeUserInvoiceService: FinanceUserInvoiceService;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeApplyInvoiceEntity);
  }

  /**
   * 提交申请
   * @param userId
   * @param amount
   * @param invoiceId
   * @param orderIds
   */
  async submit(
    userId: number,
    amount: number,
    invoiceId: number,
    orderIds: number[]
  ) {
    const invoice = await this.financeUserInvoiceService.getInvoice(
      userId,
      invoiceId
    );
    if (!invoice) throw new CoolCommException('发票信息不存在');
    await this.financeApplyInvoiceEntity.save({
      userId,
      amount,
      invoice,
      orderIds,
    });
  }

  /**
   * 审核
   * @param id
   * @param status
   * @param remark
   */
  async auth(id: number, status: number, remark: string) {
    const draw = await this.financeApplyInvoiceEntity.findOneBy({
      id: Equal(id),
    });
    if (!draw) throw new CoolCommException('开票申请不存在');
    if (draw.status !== 0) throw new CoolCommException('开票申请状态错误');
    draw.status = status;
    draw.remark = remark || '';
    await this.financeApplyInvoiceEntity.save(draw);
  }
}
