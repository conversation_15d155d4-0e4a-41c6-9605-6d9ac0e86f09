"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONPMiddleware = exports.JSONPFilter = void 0;
const core_1 = require("@midwayjs/core");
const jsonp_1 = require("../jsonp");
const error_1 = require("../error");
let JSONPFilter = class JSONPFilter {
    async match(value, req) {
        const jsonpService = await req.requestContext.getAsync(jsonp_1.JSONPService);
        return jsonpService.jsonp(value);
    }
};
JSONPFilter = __decorate([
    (0, core_1.Match)()
], JSONPFilter);
exports.JSONPFilter = JSONPFilter;
let JSONPMiddleware = class JSONPMiddleware {
    resolve(app) {
        if (app.getFrameworkType() === core_1.MidwayFrameworkType.WEB_EXPRESS) {
            app.useFilter(JSONPFilter);
            return async (req, res, next) => {
                return this.compatibleMiddleware(req, next);
            };
        }
        else {
            return async (ctx, next) => {
                const result = await this.compatibleMiddleware(ctx, next);
                const jsonpService = await ctx.requestContext.getAsync(jsonp_1.JSONPService);
                return jsonpService.jsonp(result);
            };
        }
    }
    async compatibleMiddleware(context, next) {
        const { csrf } = this.jsonp;
        // midway security
        if (csrf && context.assertCsrf) {
            try {
                context.assertCsrf();
            }
            catch (_) {
                throw new error_1.JSONPCSRFError();
            }
        }
        return await next();
    }
    static getName() {
        return 'jsonp';
    }
};
__decorate([
    (0, core_1.Config)('jsonp'),
    __metadata("design:type", Object)
], JSONPMiddleware.prototype, "jsonp", void 0);
JSONPMiddleware = __decorate([
    (0, core_1.Middleware)()
], JSONPMiddleware);
exports.JSONPMiddleware = JSONPMiddleware;
//# sourceMappingURL=jsonp.js.map