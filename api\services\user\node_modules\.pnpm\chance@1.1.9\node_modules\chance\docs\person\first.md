# first

```js
// usage
chance.first()
chance.first({ nationality: 'en' })
```

Generate a random first name

```js
<PERSON>.first();
=> '<PERSON><PERSON>'
```

Optionally specify a gender to limit first names to that gender

```js
<PERSON>.first({ gender: "female" });
=> '<PERSON>'
```

Optionally specify a nationality to limit first names to those most common of that nationality

```js
Chance.first({ nationality: "it" });
=> '<PERSON>'
```

Note, currently support for nationality is limited to: `'en', 'it', 'nl', 'fr'`.

