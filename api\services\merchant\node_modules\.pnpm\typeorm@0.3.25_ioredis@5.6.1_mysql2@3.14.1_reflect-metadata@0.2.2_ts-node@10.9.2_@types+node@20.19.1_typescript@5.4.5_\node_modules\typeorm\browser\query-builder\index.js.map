{"version": 3, "sources": ["../browser/src/query-builder/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AACzD,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AACzD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAA;AAC7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AACzD,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAA;AACjE,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AAEzD,MAAM,UAAU,qBAAqB;IACjC,YAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,YAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,YAAY,CAAC,yBAAyB,CAClC,sBAAsB,EACtB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,oBAAoB,CAAC,EAAE,CAAC,CAC1D,CAAA;IACD,YAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;IACD,YAAY,CAAC,yBAAyB,CAClC,wBAAwB,EACxB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,sBAAsB,CAAC,EAAE,CAAC,CAC5D,CAAA;IACD,YAAY,CAAC,yBAAyB,CAClC,oBAAoB,EACpB,CAAC,EAAqB,EAAE,EAAE,CAAC,IAAI,kBAAkB,CAAC,EAAE,CAAC,CACxD,CAAA;AACL,CAAC", "file": "index.js", "sourcesContent": ["import { DeleteQueryBuilder } from \"./DeleteQueryBuilder\"\nimport { InsertQueryBuilder } from \"./InsertQueryBuilder\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { RelationQueryBuilder } from \"./RelationQueryBuilder\"\nimport { SelectQueryBuilder } from \"./SelectQueryBuilder\"\nimport { SoftDeleteQueryBuilder } from \"./SoftDeleteQueryBuilder\"\nimport { UpdateQueryBuilder } from \"./UpdateQueryBuilder\"\n\nexport function registerQueryBuilders() {\n    QueryBuilder.registerQueryBuilderClass(\n        \"DeleteQueryBuilder\",\n        (qb: QueryBuilder<any>) => new DeleteQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"InsertQueryBuilder\",\n        (qb: QueryBuilder<any>) => new InsertQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"RelationQueryBuilder\",\n        (qb: QueryBuilder<any>) => new RelationQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"SelectQueryBuilder\",\n        (qb: QueryBuilder<any>) => new SelectQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"SoftDeleteQueryBuilder\",\n        (qb: QueryBuilder<any>) => new SoftDeleteQueryBuilder(qb),\n    )\n    QueryBuilder.registerQueryBuilderClass(\n        \"UpdateQueryBuilder\",\n        (qb: QueryBuilder<any>) => new UpdateQueryBuilder(qb),\n    )\n}\n"], "sourceRoot": ".."}