{"version": 3, "sources": ["../browser/src/driver/types/ColumnTypes.ts"], "names": [], "mappings": "", "file": "ColumnTypes.js", "sourcesContent": ["/**\n * Column types used for @PrimaryGeneratedColumn() decorator.\n */\nexport type PrimaryGeneratedColumnType =\n    | \"int\" // mysql, mssql, oracle, sqlite, sap\n    | \"int2\" // postgres, sqlite, cockroachdb\n    | \"int4\" // postgres, cockroachdb\n    | \"int8\" // postgres, sqlite, cockroachdb\n    | \"integer\" // postgres, oracle, sqlite, mysql, cockroachdb, sap\n    | \"tinyint\" // mysql, mssql, sqlite, sap\n    | \"smallint\" // mysql, postgres, mssql, oracle, sqlite, cockroachdb, sap\n    | \"mediumint\" // mysql, sqlite\n    | \"bigint\" // mysql, postgres, mssql, sqlite, cockroachdb, sap\n    | \"dec\" // oracle, mssql, sap\n    | \"decimal\" // mysql, postgres, mssql, sqlite, sap\n    | \"smalldecimal\" // sap\n    | \"fixed\" // mysql\n    | \"numeric\" // postgres, mssql, sqlite, spanner\n    | \"number\" // oracle\n\n/**\n * Column types where spatial properties are used.\n */\nexport type SpatialColumnType =\n    | \"geometry\" // postgres\n    | \"geography\" // postgres\n    | \"st_geometry\" // sap\n    | \"st_point\" // sap\n\n/**\n * Column types where precision and scale properties are used.\n */\nexport type WithPrecisionColumnType =\n    | \"float\" // mysql, mssql, oracle, sqlite\n    | \"double\" // mysql, sqlite\n    | \"dec\" // oracle, mssql, mysql\n    | \"decimal\" // mysql, postgres, mssql, sqlite\n    | \"smalldecimal\" // sap\n    | \"fixed\" // mysql\n    | \"numeric\" // postgres, mssql, sqlite, mysql\n    | \"real\" // mysql, postgres, mssql, oracle, sqlite, cockroachdb, sap\n    | \"double precision\" // postgres, oracle, sqlite, mysql, cockroachdb\n    | \"number\" // oracle\n    | \"datetime\" // mssql, mysql, sqlite\n    | \"datetime2\" // mssql\n    | \"datetimeoffset\" // mssql\n    | \"time\" // mysql, postgres, mssql, cockroachdb\n    | \"time with time zone\" // postgres, cockroachdb\n    | \"time without time zone\" // postgres\n    | \"timestamp\" // mysql, postgres, mssql, oracle, cockroachdb, spanner\n    | \"timestamp without time zone\" // postgres, cockroachdb\n    | \"timestamp with time zone\" // postgres, oracle, cockroachdb\n    | \"timestamp with local time zone\" // oracle\n\n/**\n * Column types where column length is used.\n */\nexport type WithLengthColumnType =\n    | \"character varying\" // postgres, cockroachdb\n    | \"varying character\" // sqlite\n    | \"char varying\" // cockroachdb\n    | \"nvarchar\" // mssql, mysql\n    | \"national varchar\" // mysql\n    | \"character\" // mysql, postgres, sqlite, cockroachdb\n    | \"native character\" // sqlite\n    | \"varchar\" // mysql, postgres, mssql, sqlite, cockroachdb\n    | \"char\" // mysql, postgres, mssql, oracle, cockroachdb, sap\n    | \"nchar\" // mssql, oracle, sqlite, mysql, sap\n    | \"national char\" // mysql\n    | \"varchar2\" // oracle\n    | \"nvarchar2\" // oracle, sqlite\n    | \"alphanum\" // sap\n    | \"shorttext\" // sap\n    | \"raw\" // oracle\n    | \"binary\" // mssql\n    | \"varbinary\" // mssql, sap\n    | \"string\" // cockroachdb, spanner\n\nexport type WithWidthColumnType =\n    | \"tinyint\" // mysql\n    | \"smallint\" // mysql\n    | \"mediumint\" // mysql\n    | \"int\" // mysql\n    | \"bigint\" // mysql\n\n/**\n * All other regular column types.\n */\nexport type SimpleColumnType =\n    | \"simple-array\" // typeorm-specific, automatically mapped to string\n    // |\"string\" // typeorm-specific, automatically mapped to varchar depend on platform\n    | \"simple-json\" // typeorm-specific, automatically mapped to string\n    | \"simple-enum\" // typeorm-specific, automatically mapped to string\n\n    // numeric types\n    | \"int2\" // postgres, sqlite, cockroachdb\n    | \"integer\" // postgres, oracle, sqlite, cockroachdb\n    | \"int4\" // postgres, cockroachdb\n    | \"int8\" // postgres, sqlite, cockroachdb\n    | \"int64\" // cockroachdb, spanner\n    | \"unsigned big int\" // sqlite\n    | \"float\" // mysql, mssql, oracle, sqlite, sap\n    | \"float4\" // postgres, cockroachdb\n    | \"float8\" // postgres, cockroachdb\n    | \"float64\" // spanner\n    | \"smallmoney\" // mssql\n    | \"money\" // postgres, mssql\n\n    // boolean types\n    | \"boolean\" // postgres, sqlite, mysql, cockroachdb\n    | \"bool\" // postgres, mysql, cockroachdb, spanner\n\n    // text/binary types\n    | \"tinyblob\" // mysql\n    | \"tinytext\" // mysql\n    | \"mediumblob\" // mysql\n    | \"mediumtext\" // mysql\n    | \"blob\" // mysql, oracle, sqlite, cockroachdb, sap\n    | \"text\" // mysql, postgres, mssql, sqlite, cockroachdb, sap\n    | \"ntext\" // mssql\n    | \"citext\" // postgres\n    | \"hstore\" // postgres\n    | \"longblob\" // mysql\n    | \"longtext\" // mysql\n    | \"alphanum\" // sap\n    | \"shorttext\" // sap\n    | \"bytes\" // cockroachdb, spanner\n    | \"bytea\" // postgres, cockroachdb\n    | \"long\" // oracle\n    | \"raw\" // oracle\n    | \"long raw\" // oracle\n    | \"bfile\" // oracle\n    | \"clob\" // oracle, sqlite, sap\n    | \"nclob\" // oracle, sap\n    | \"image\" // mssql\n\n    // date types\n    | \"timetz\" // postgres\n    | \"timestamptz\" // postgres, cockroachdb\n    | \"timestamp with local time zone\" // oracle\n    | \"smalldatetime\" // mssql\n    | \"date\" // mysql, postgres, mssql, oracle, sqlite, spanner\n    | \"interval year to month\" // oracle\n    | \"interval day to second\" // oracle\n    | \"interval\" // postgres, cockroachdb\n    | \"year\" // mysql\n    | \"seconddate\" // sap\n\n    // geometric types\n    | \"point\" // postgres, mysql\n    | \"line\" // postgres\n    | \"lseg\" // postgres\n    | \"box\" // postgres\n    | \"circle\" // postgres\n    | \"path\" // postgres\n    | \"polygon\" // postgres, mysql\n    | \"geography\" // mssql\n    | \"geometry\" // mysql\n    | \"linestring\" // mysql\n    | \"multipoint\" // mysql\n    | \"multilinestring\" // mysql\n    | \"multipolygon\" // mysql\n    | \"geometrycollection\" // mysql\n    | \"st_geometry\" // sap\n    | \"st_point\" // sap\n\n    // range types\n    | \"int4range\" // postgres\n    | \"int8range\" // postgres\n    | \"numrange\" // postgres\n    | \"tsrange\" // postgres\n    | \"tstzrange\" // postgres\n    | \"daterange\" // postgres\n\n    // multirange types\n    | \"int4multirange\" // postgres\n    | \"int8multirange\" // postgres\n    | \"nummultirange\" // postgres\n    | \"tsmultirange\" // postgres\n    | \"tstzmultirange\" // postgres\n    | \"datemultirange\" // postgres\n\n    // other types\n    | \"enum\" // mysql, postgres\n    | \"set\" // mysql\n    | \"cidr\" // postgres\n    | \"inet\" // postgres, cockroachdb\n    | \"inet4\" // mariadb\n    | \"inet6\" // mariadb\n    | \"macaddr\" // postgres\n    | \"macaddr8\" // postgres\n    | \"bit\" // postgres, mssql\n    | \"bit varying\" // postgres\n    | \"varbit\" // postgres\n    | \"tsvector\" // postgres\n    | \"tsquery\" // postgres\n    | \"uuid\" // postgres, cockroachdb, mariadb\n    | \"xml\" // mssql, postgres\n    | \"json\" // mysql, postgres, cockroachdb, spanner\n    | \"jsonb\" // postgres, cockroachdb\n    | \"varbinary\" // mssql, sap\n    | \"hierarchyid\" // mssql\n    | \"sql_variant\" // mssql\n    | \"rowid\" // oracle\n    | \"urowid\" // oracle\n    | \"uniqueidentifier\" // mssql\n    | \"rowversion\" // mssql\n    | \"array\" // cockroachdb, sap, spanner\n    | \"cube\" // postgres\n    | \"ltree\" // postgres\n\n/**\n * Any column type column can be.\n */\nexport type ColumnType =\n    | WithPrecisionColumnType\n    | WithLengthColumnType\n    | WithWidthColumnType\n    | SpatialColumnType\n    | SimpleColumnType\n    | BooleanConstructor\n    | DateConstructor\n    | NumberConstructor\n    | StringConstructor\n"], "sourceRoot": "../.."}