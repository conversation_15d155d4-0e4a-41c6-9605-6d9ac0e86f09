{"version": 3, "sources": ["../browser/src/error/InsertValuesMissingError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,wBAAyB,SAAQ,YAAY;IACtD;QACI,KAAK,CACD,8DAA8D;YAC1D,0DAA0D,CACjE,CAAA;IACL,CAAC;CACJ", "file": "InsertValuesMissingError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to insert using QueryBuilder but do not specify what to insert.\n */\nexport class InsertValuesMissingError extends TypeORMError {\n    constructor() {\n        super(\n            `Cannot perform insert query because values are not defined. ` +\n                `Call \"qb.values(...)\" method to specify inserted values.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}