{"version": 3, "sources": ["../../src/commands/SchemaSyncCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,iBAAiB;IAA9B;QACI,YAAO,GAAG,aAAa,CAAA;QACvB,aAAQ,GACJ,8GAA8G;YAC9G,+DAA+D,CAAA;IAuCvE,CAAC;IArCG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;aAC/B,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAC7B,MAAM,UAAU,CAAC,WAAW,EAAE,CAAA;YAC9B,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE1B,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,+CAA+C,CAC5D,CAAA;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAA;YAEpE,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AA3CD,8CA2CC", "file": "SchemaSyncCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Synchronizes database schema with entities.\n */\nexport class SchemaSyncCommand implements yargs.CommandModule {\n    command = \"schema:sync\"\n    describe =\n        \"Synchronizes your entities with database schema. It runs schema update queries on all connections you have. \" +\n        \"To run update queries on a concrete connection use -c option.\"\n\n    builder(args: yargs.Argv) {\n        return args.option(\"dataSource\", {\n            alias: \"d\",\n            describe:\n                \"Path to the file where your DataSource instance is defined.\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"query\", \"schema\"],\n            })\n            await dataSource.initialize()\n            await dataSource.synchronize()\n            await dataSource.destroy()\n\n            console.log(\n                ansi.green`Schema synchronization finished successfully.`,\n            )\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during schema synchronization:\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}