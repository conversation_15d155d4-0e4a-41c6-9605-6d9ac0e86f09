-- 为菜单表添加 routeKey 字段，支持路由映射系统
USE `merchant_service_db`;

-- 1. 添加 routeKey 字段
ALTER TABLE merchant_sys_menu 
ADD COLUMN routeKey VARCHAR(100) NULL COMMENT '路由标识符，用于路由映射' 
AFTER router;

-- 2. 为现有菜单数据添加 routeKey
-- 商户管理相关
UPDATE merchant_sys_menu SET routeKey = 'merchant.settle-in' WHERE name = '商户入驻' AND router = '/merchant/settle-in';
UPDATE merchant_sys_menu SET routeKey = 'merchant.heritage-auth' WHERE name = '创作者认证' AND router = '/merchant/heritage-auth';
UPDATE merchant_sys_menu SET routeKey = 'merchant.creators' WHERE name = '创作者管理' AND router = '/merchant/creators';
UPDATE merchant_sys_menu SET routeKey = 'merchant.personal' WHERE name = '个人商户' AND router = '/merchant/personal';
UPDATE merchant_sys_menu SET routeKey = 'merchant.company' WHERE name = '企业商户' AND router = '/merchant/company';

-- 财务结算相关
UPDATE merchant_sys_menu SET routeKey = 'settlement.rules' WHERE name = '结算规则' AND router LIKE '%settlement%rules%';
UPDATE merchant_sys_menu SET routeKey = 'settlement.batch' WHERE name = '批量结算' AND router LIKE '%settlement%batch%';
UPDATE merchant_sys_menu SET routeKey = 'settlement.risk-compliance' WHERE name = '风险与合规' AND router LIKE '%settlement%risk%';
UPDATE merchant_sys_menu SET routeKey = 'settlement.internationalization' WHERE name = '国际化管理' AND router LIKE '%settlement%international%';

-- 创作者管理相关
UPDATE merchant_sys_menu SET routeKey = 'creator.heritage-auth' WHERE name = '创作者认证' AND router LIKE '%creator%heritage%';
UPDATE merchant_sys_menu SET routeKey = 'creator.management' WHERE name = '创作者管理' AND router LIKE '%creator%management%';
UPDATE merchant_sys_menu SET routeKey = 'creator.category' WHERE name = '认证分类' AND router LIKE '%creator%category%';

-- 商户监控相关
UPDATE merchant_sys_menu SET routeKey = 'monitor.dashboard' WHERE name = '监控总览' AND router LIKE '%monitor%dashboard%';
UPDATE merchant_sys_menu SET routeKey = 'monitor.risk-alert' WHERE name = '风险预警' AND router LIKE '%monitor%risk%';

-- 数据分析相关
UPDATE merchant_sys_menu SET routeKey = 'analytics.overview' WHERE name = '数据概览' AND router LIKE '%analytics%overview%';

-- 系统管理相关
UPDATE merchant_sys_menu SET routeKey = 'system.user' WHERE name = '用户管理' AND router LIKE '%system%user%';
UPDATE merchant_sys_menu SET routeKey = 'system.role' WHERE name = '角色管理' AND router LIKE '%system%role%';
UPDATE merchant_sys_menu SET routeKey = 'system.menu' WHERE name = '菜单管理' AND router LIKE '%system%menu%';
UPDATE merchant_sys_menu SET routeKey = 'system.dict' WHERE name = '字典管理' AND router LIKE '%system%dict%';

-- 仪表盘相关
UPDATE merchant_sys_menu SET routeKey = 'dashboard.console' WHERE name = '控制台' AND router = '/dashboard/console';
UPDATE merchant_sys_menu SET routeKey = 'dashboard.analysis' WHERE name = '分析页' AND router = '/dashboard/analysis';
UPDATE merchant_sys_menu SET routeKey = 'dashboard.ecommerce' WHERE name = '电商' AND router = '/dashboard/ecommerce';

-- 3. 创建索引提高查询性能
CREATE INDEX idx_route_key ON merchant_sys_menu(routeKey);

-- 4. 验证更新结果
SELECT 
  name,
  router,
  routeKey,
  CASE 
    WHEN routeKey IS NOT NULL THEN '✅ 已设置'
    ELSE '❌ 未设置'
  END AS status
FROM merchant_sys_menu 
WHERE type = 1  -- 只显示菜单类型
ORDER BY parentId, orderNum;

-- 5. 显示统计信息
SELECT 
  '总菜单数' as item,
  COUNT(*) as count
FROM merchant_sys_menu WHERE type = 1
UNION ALL
SELECT 
  '已设置routeKey' as item,
  COUNT(*) as count
FROM merchant_sys_menu WHERE type = 1 AND routeKey IS NOT NULL
UNION ALL
SELECT 
  '未设置routeKey' as item,
  COUNT(*) as count
FROM merchant_sys_menu WHERE type = 1 AND routeKey IS NULL;

-- 6. 显示需要手动设置的菜单
SELECT 
  '需要手动设置routeKey的菜单:' as notice,
  name,
  router
FROM merchant_sys_menu 
WHERE type = 1 AND routeKey IS NULL
ORDER BY parentId, orderNum;
