{"version": 3, "sources": ["../browser/src/driver/sqlserver/authentication/AzureActiveDirectoryServicePrincipalSecret.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryServicePrincipalSecret.js", "sourcesContent": ["export interface AzureActiveDirectoryServicePrincipalSecret {\n    type: \"azure-active-directory-service-principal-secret\"\n    options: {\n        /**\n         * Application (`client`) ID from your registered Azure application\n         */\n        clientId: string\n        /**\n         * The created `client secret` for this registered Azure application\n         */\n        clientSecret: string\n        /**\n         * Directory (`tenant`) ID from your registered Azure application\n         */\n        tenantId: string\n    }\n}\n"], "sourceRoot": "../../.."}