{"version": 3, "sources": ["../browser/src/driver/types/GeoJsonTypes.ts"], "names": [], "mappings": "", "file": "GeoJsonTypes.js", "sourcesContent": ["/**\n * Position object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.1\n */\nexport type Position = number[]\n\n/**\n * Point geometry object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.2\n */\nexport type Point = {\n    type: \"Point\"\n    coordinates: Position\n}\n\n/**\n * LineString geometry object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.4\n */\nexport type LineString = {\n    type: \"LineString\"\n    coordinates: Position[]\n}\n\n/**\n * Polygon geometry object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.6\n */\nexport type Polygon = {\n    type: \"Polygon\"\n    coordinates: Position[][]\n}\n\n/**\n * MultiPoint geometry object.\n *  https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.3\n */\nexport type MultiPoint = {\n    type: \"MultiPoint\"\n    coordinates: Position[]\n}\n\n/**\n * MultiLineString geometry object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.5\n */\nexport type MultiLineString = {\n    type: \"MultiLineString\"\n    coordinates: Position[][]\n}\n\n/**\n * MultiPolygon geometry object.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.7\n */\nexport type MultiPolygon = {\n    type: \"MultiPolygon\"\n    coordinates: Position[][][]\n}\n\n/**\n * Geometry Collection\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.1.8\n */\nexport type GeometryCollection = {\n    type: \"GeometryCollection\"\n    geometries: (\n        | Point\n        | LineString\n        | Polygon\n        | MultiPoint\n        | MultiLineString\n        | MultiPolygon\n    )[]\n}\n\n/**\n * Union of Geometry objects.\n */\nexport type Geometry =\n    | Point\n    | LineString\n    | Polygon\n    | MultiPoint\n    | MultiLineString\n    | MultiPolygon\n    | GeometryCollection\nexport type Geography = Geometry\n\n/**\n * A feature object which contains a geometry and associated properties.\n * https://datatracker.ietf.org/doc/html/rfc7946#section-3.2\n */\nexport type Feature = {\n    type: \"Feature\"\n    geometry: Geometry\n    id?: string | number\n    bbox?: number[]\n    properties: { [name: string]: any } | null\n}\n\n/**\n * A collection of feature objects.\n *  https://datatracker.ietf.org/doc/html/rfc7946#section-3.3\n */\nexport type FeatureCollection = {\n    type: \"FeatureCollection\"\n    bbox?: number[]\n    features: Feature[]\n}\n\n/**\n * Union of GeoJSON objects.\n */\nexport type GeoJSON = Geometry | Feature | FeatureCollection\n"], "sourceRoot": "../.."}