/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import { IMidwayContext, ServerSendEventMessage, ServerSendEventStreamOptions, ServerStreamOptions } from '../interface';
import { ServerResponse } from './base';
import { ServerSendEventStream } from './sse';
import { Readable } from 'stream';
import { HttpStreamResponse } from './stream';
export declare class HttpServerResponse<CTX extends IMidwayContext> extends ServerResponse<CTX> {
    constructor(ctx: CTX);
    static FILE_TPL: <CTX_1 extends import("../interface").Context>(data: Readable, isSuccess: boolean, ctx: CTX_1) => Readable;
    static SSE_TPL: <CTX_1 extends import("../interface").Context>(data: ServerSendEventMessage, ctx: CTX_1) => ServerSendEventMessage;
    static STREAM_TPL: <CTX_1 extends import("../interface").Context>(data: unknown, ctx: CTX_1) => unknown;
    static HTML_TPL: <CTX_1 extends import("../interface").Context>(data: string, isSuccess: boolean, ctx: CTX_1) => unknown;
    status(code: number): this;
    header(key: string, value: string): this;
    headers(headers: Record<string, string>): this;
    json(data: Record<any, any>): any;
    text(data: string): any;
    file(filePath: string, mimeType?: string): any;
    blob(data: Buffer, mimeType?: string): any;
    html(data: string): any;
    redirect(url: string, status?: number): any;
    sse(options?: ServerSendEventStreamOptions<CTX>): ServerSendEventStream<import("../interface").Context>;
    stream(options?: ServerStreamOptions<CTX>): HttpStreamResponse<import("../interface").Context>;
}
//# sourceMappingURL=http.d.ts.map