const { Framework } = require('@midwayjs/koa');
const { bootstrap } = require('@midwayjs/core');

// 消息微服务启动文件
bootstrap(Framework, {
  globalPrefix: '/api',
})
  .then(() => {
    console.log('✅ 消息微服务启动成功！');
    console.log('🚀 服务端口: 9804');
    console.log('📋 服务名称: message-service');
    console.log('🔔 消息推送独立管理');
    console.log('🔗 RPC通信已启用');
  })
  .catch(err => {
    console.error('❌ 消息微服务启动失败:', err);
    process.exit(1);
  }); 