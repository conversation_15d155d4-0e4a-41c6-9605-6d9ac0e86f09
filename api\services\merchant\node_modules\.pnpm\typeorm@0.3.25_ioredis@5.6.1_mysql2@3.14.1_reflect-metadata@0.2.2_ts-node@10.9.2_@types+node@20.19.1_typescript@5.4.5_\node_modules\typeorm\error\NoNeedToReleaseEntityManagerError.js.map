{"version": 3, "sources": ["../../src/error/NoNeedToReleaseEntityManagerError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,iCAAkC,SAAQ,2BAAY;IAC/D;QACI,KAAK,CACD,iFAAiF;YAC7E,6FAA6F;YAC7F,wEAAwE,CAC/E,CAAA;IACL,CAAC;CACJ;AARD,8EAQC", "file": "NoNeedToReleaseEntityManagerError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to release entity manager that does not use single database connection.\n */\nexport class NoNeedToReleaseEntityManagerError extends TypeORMError {\n    constructor() {\n        super(\n            `Entity manager is not using single database connection and cannot be released. ` +\n                `Only entity managers created by connection#createEntityManagerWithSingleDatabaseConnection ` +\n                `methods have a single database connection and they should be released.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}