# Installation
> `npm install --save @types/koa`

# Summary
This package contains type definitions for koa (http://koajs.com).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/koa.

### Additional Details
 * Last updated: Thu, 22 Feb 2024 23:07:30 GMT
 * Dependencies: [@types/accepts](https://npmjs.com/package/@types/accepts), [@types/content-disposition](https://npmjs.com/package/@types/content-disposition), [@types/cookies](https://npmjs.com/package/@types/cookies), [@types/http-assert](https://npmjs.com/package/@types/http-assert), [@types/http-errors](https://npmjs.com/package/@types/http-errors), [@types/keygrip](https://npmjs.com/package/@types/keygrip), [@types/koa-compose](https://npmjs.com/package/@types/koa-compose), [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [j<PERSON><PERSON>](https://github.com/jkeylu), [<PERSON><PERSON>](https://github.com/brikou), [harryparkdotio](https://github.com/harryparkdotio), [Wooram Jun](https://github.com/chatoo2412), [Christian Vaagland Tellnes](https://github.com/tellnes), [Piotr Kuczynski](https://github.com/pkuczynski), and [vnoder](https://github.com/vnoder).
