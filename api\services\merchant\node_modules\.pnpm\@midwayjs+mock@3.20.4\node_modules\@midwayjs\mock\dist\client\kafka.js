"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createKafkaProducer = void 0;
const connect = async () => ({
    producer: () => { },
    cousumer: () => { },
});
async function createKafkaProducer(options = {
    mock: true,
}) {
    let { Kafka } = require('kafkajs');
    if (options.mock) {
        Kafka = connect;
    }
    const kafka = new Kafka(options.kafkaConfig);
    return kafka.producer(options.producerConfig);
}
exports.createKafkaProducer = createKafkaProducer;
//# sourceMappingURL=kafka.js.map