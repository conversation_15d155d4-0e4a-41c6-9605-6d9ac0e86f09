import { IServiceFactory } from '../interface';
import { MidwayPriorityManager } from './priorityManager';
/**
 * 多客户端工厂实现
 */
export declare abstract class ServiceFactory<T> implements IServiceFactory<T> {
    protected clients: Map<string, T>;
    protected clientPriority: Record<string, string>;
    protected options: {};
    protected priorityManager: MidwayPriorityManager;
    protected initClients(options?: any): Promise<void>;
    get<U = T>(id?: string): U;
    has(id: string): boolean;
    createInstance(config: any, clientName?: any): Promise<T | undefined>;
    abstract getName(): string;
    protected abstract createClient(config: any, clientName: any): Promise<T | void> | (T | void);
    protected destroyClient(client: T, clientName?: string): Promise<void>;
    stop(): Promise<void>;
    getDefaultClientName(): string;
    getClients(): Map<string, T>;
    getClientKeys(): string[];
    getClientPriority(name: string): string;
    isHighPriority(name: string): boolean;
    isMediumPriority(name: string): boolean;
    isLowPriority(name: string): boolean;
}
//# sourceMappingURL=serviceFactory.d.ts.map