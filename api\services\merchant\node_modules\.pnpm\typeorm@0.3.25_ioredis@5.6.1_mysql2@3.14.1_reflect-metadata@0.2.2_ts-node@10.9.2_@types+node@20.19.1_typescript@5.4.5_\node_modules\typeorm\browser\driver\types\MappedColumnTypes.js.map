{"version": 3, "sources": ["../browser/src/driver/types/MappedColumnTypes.ts"], "names": [], "mappings": "", "file": "MappedColumnTypes.js", "sourcesContent": ["import { ColumnType } from \"./ColumnTypes\"\n\n/**\n * <PERSON><PERSON> has special columns and we need to know what database column types should be for those types.\n * Column types are driver dependant.\n */\nexport interface MappedColumnTypes {\n    /**\n     * Column type for the create date column.\n     */\n    createDate: ColumnType\n\n    /**\n     * Precision of datetime column. Used in MySql to define milliseconds.\n     */\n    createDatePrecision?: number\n\n    /**\n     * Default value should be used by a database for \"created date\" column.\n     */\n    createDateDefault: string\n\n    /**\n     * Column type for the update date column.\n     */\n    updateDate: ColumnType\n\n    /**\n     * Precision of datetime column. Used in MySql to define milliseconds.\n     */\n    updateDatePrecision?: number\n\n    /**\n     * Default value should be used by a database for \"updated date\" column.\n     */\n    updateDateDefault: string\n\n    /**\n     * Column type for the delete date column.\n     */\n    deleteDate: ColumnType\n\n    /**\n     * Precision of datetime column. Used in MySql to define milliseconds.\n     */\n    deleteDatePrecision?: number\n\n    /**\n     * Nullable value should be used by a database for \"deleted date\" column.\n     */\n    deleteDateNullable: boolean\n\n    /**\n     * Column type for the version column.\n     */\n    version: ColumnType\n\n    /**\n     * Column type for the tree level column.\n     */\n    treeLevel: ColumnType\n\n    /**\n     * Column type of id column used for migrations table.\n     */\n    migrationId: ColumnType\n\n    /**\n     * Column type of timestamp column used for migrations table.\n     */\n    migrationTimestamp: ColumnType\n\n    /**\n     * Column type for migration name column used for migrations table.\n     */\n    migrationName: ColumnType\n\n    /**\n     * Column type for identifier column in query result cache table.\n     */\n    cacheId: ColumnType\n\n    /**\n     * Column type for identifier column in query result cache table.\n     */\n    cacheIdentifier: ColumnType\n\n    /**\n     * Column type for time column in query result cache table.\n     */\n    cacheTime: ColumnType\n\n    /**\n     * Column type for duration column in query result cache table.\n     */\n    cacheDuration: ColumnType\n\n    /**\n     * Column type for query column in query result cache table.\n     */\n    cacheQuery: ColumnType\n\n    /**\n     * Column type for result column in query result cache table.\n     */\n    cacheResult: ColumnType\n\n    /**\n     * Column type for metadata type column in typeorm metadata table.\n     * Stores type of metadata. E.g. 'VIEW' or 'CHECK'\n     */\n    metadataType: ColumnType\n\n    /**\n     * Column type for metadata database name column in typeorm metadata table.\n     */\n    metadataDatabase: ColumnType\n\n    /**\n     * Column type for metadata schema name column in typeorm metadata table.\n     */\n    metadataSchema: ColumnType\n\n    /**\n     * Column type for metadata table name column in typeorm metadata table.\n     */\n    metadataTable: ColumnType\n\n    /**\n     * Column type for metadata name column in typeorm metadata table.\n     */\n    metadataName: ColumnType\n\n    /**\n     * Column type for metadata value column in typeorm metadata table.\n     */\n    metadataValue: ColumnType\n}\n"], "sourceRoot": "../.."}