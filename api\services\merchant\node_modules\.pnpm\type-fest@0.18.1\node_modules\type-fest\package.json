{"name": "type-fest", "version": "0.18.1", "description": "A collection of essential TypeScript types", "license": "(MIT OR CC0-1.0)", "repository": "sindresorhus/type-fest", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && tsd"}, "files": ["index.d.ts", "source"], "keywords": ["typescript", "ts", "types", "utility", "util", "utilities", "omit", "merge", "json"], "devDependencies": {"tsd": "^0.13.1", "xo": "^0.28.2"}, "types": "index.d.ts", "xo": {"rules": {"@typescript-eslint/indent": "off", "func-call-spacing": "off"}}}