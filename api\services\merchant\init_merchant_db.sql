-- 商户数据库快速初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `merchant_service_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `merchant_service_db`;

-- 创建用户表
DROP TABLE IF EXISTS `merchant_sys_user`;
CREATE TABLE `merchant_sys_user` (
  `userId` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `userName` varchar(100) NOT NULL COMMENT '用户名', 
  `password` varchar(255) NOT NULL COMMENT '密码',
  `passwordV` int(11) NOT NULL DEFAULT '1' COMMENT '密码版本',
  `roles` text COMMENT '角色权限数组',
  `buttons` text COMMENT '按钮权限数组', 
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_userName` (`userName`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入测试用户 (密码已MD5加密)
-- admin/admin -> 21232f297a57a5a743894a0e4a801fc3
INSERT INTO `merchant_sys_user` (`userId`, `userName`, `password`, `passwordV`, `roles`, `buttons`, `avatar`, `email`, `phone`, `status`) VALUES
(1, 'admin', '21232f297a57a5a743894a0e4a801fc3', 1, '["admin","super"]', '["add","edit","delete","view"]', NULL, '<EMAIL>', '13800138000', 1);

-- 创建菜单表
DROP TABLE IF EXISTS `merchant_sys_menu`;
CREATE TABLE `merchant_sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parentId` int(11) NOT NULL DEFAULT '0',
  `name` varchar(100) NOT NULL,
  `router` varchar(200) DEFAULT NULL,
  `title` varchar(100) NOT NULL,
  `icon` varchar(100) DEFAULT NULL,
  `type` tinyint(1) NOT NULL DEFAULT '0',
  `orderNum` int(11) NOT NULL DEFAULT '0',
  `isShow` tinyint(1) NOT NULL DEFAULT '1',
  `keepAlive` tinyint(1) NOT NULL DEFAULT '1',
  `perms` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入基础菜单
INSERT INTO `merchant_sys_menu` VALUES (1, 0, 'Dashboard', '/dashboard', '首页', 'home', 1, 1, 1, 1, 'dashboard:view');

-- 验证数据
SELECT '=== 用户数据 ===' as info;
SELECT userId, userName, roles, buttons FROM merchant_sys_user;
SELECT '=== 菜单数据 ===' as info;  
SELECT id, name, title FROM merchant_sys_menu; 