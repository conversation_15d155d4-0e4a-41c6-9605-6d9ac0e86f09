"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.providerWrapper = void 0;
const constants_1 = require("../constants");
function providerWrapper(wrapperInfo) {
    for (const info of wrapperInfo) {
        Object.defineProperty(info.provider, constants_1.FUNCTION_INJECT_KEY, {
            value: info,
            writable: false,
        });
    }
}
exports.providerWrapper = providerWrapper;
//# sourceMappingURL=providerWrapper.js.map