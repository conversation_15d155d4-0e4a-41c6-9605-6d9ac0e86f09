{"version": 3, "file": "satisfies.js", "sourceRoot": "", "sources": ["../../src/satisfies.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAmB,eAAe,EAAE,gBAAgB,EAAE,MAAM,YAAY,CAAC;AAEhF;;;;;;;;;;;;GAYG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,CAAC,OAAe,EAAE,KAAa,EAAW,EAAE;IACnE,cAAc;IACd,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAE5C,8BAA8B;IAC9B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9D,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;QACjC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QACrC,OAAO,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7C,CAAC;SAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;QAC/B,OAAO,KAAK;aACT,IAAI,EAAE;aACN,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;aACvB,KAAK,CAAC,GAAG,CAAC;aACV,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,gCAAgC;IAChC,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACrC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;IAE1B,oCAAoC;IACpC,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG;QAC1B,OAAO,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,EAAqB,CAAC,CAAC;IAExD,6CAA6C;IAC7C,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,AAAD,EAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACrD,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,AAAD,EAAG,EAAE,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACnD,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,aAAF,EAAE,cAAF,EAAE,GAAI,GAAG,EAAE,EAAE,aAAF,EAAE,cAAF,EAAE,GAAI,GAAG,CAAC,CAAC;IAErC,uBAAuB;IACvB,IAAI,EAAE,EAAE,CAAC;QACP,IAAI,CAAC,EAAE;YAAE,OAAO,KAAK,CAAC;QACtB,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QAC9C,IAAI,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;IACzE,CAAC;IAED,wBAAwB;IACxB,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAElD,sCAAsC;IACtC,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAErD,+BAA+B;IAC/B,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;QAAE,OAAO,KAAK,CAAC;IAEtE,2BAA2B;IAC3B,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;QAAE,OAAO,KAAK,CAAC;IAEjE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC"}