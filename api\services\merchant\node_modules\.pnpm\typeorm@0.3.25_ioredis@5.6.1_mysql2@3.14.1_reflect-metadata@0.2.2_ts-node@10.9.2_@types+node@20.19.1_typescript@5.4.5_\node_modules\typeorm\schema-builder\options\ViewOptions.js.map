{"version": 3, "sources": ["../../src/schema-builder/options/ViewOptions.ts"], "names": [], "mappings": "", "file": "ViewOptions.js", "sourcesContent": ["import { DataSource, SelectQueryBuilder } from \"../..\"\n\n/**\n * View options.\n */\nexport interface ViewOptions {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database name that this table resides in if it applies.\n     */\n    database?: string\n\n    /**\n     * Schema name that this table resides in if it applies.\n     */\n    schema?: string\n\n    /**\n     * View name.\n     */\n    name: string\n\n    /**\n     * View expression.\n     */\n    expression: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    /**\n     * Indicates if view is materialized\n     */\n\n    materialized?: boolean\n}\n"], "sourceRoot": "../.."}