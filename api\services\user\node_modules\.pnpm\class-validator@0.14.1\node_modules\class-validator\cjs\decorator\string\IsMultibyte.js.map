{"version": 3, "file": "IsMultibyte.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsMultibyte.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,4EAA6D;AAEhD,QAAA,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,SAAgB,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,qBAAoB,EAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAFD,kCAEC;AAED;;;GAGG;AACH,SAAgB,WAAW,CAAC,iBAAqC;IAC/D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,oBAAY;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;YACtD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,oDAAoD,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,kCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isMultibyteValidator from 'validator/lib/isMultibyte';\n\nexport const IS_MULTIBYTE = 'isMultibyte';\n\n/**\n * Checks if the string contains one or more multibyte chars.\n * If given value is not a string, then it returns false.\n */\nexport function isMultibyte(value: unknown): boolean {\n  return typeof value === 'string' && isMultibyteValidator(value);\n}\n\n/**\n * Checks if the string contains one or more multibyte chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsMultibyte(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_MULTIBYTE,\n      validator: {\n        validate: (value, args): boolean => isMultibyte(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain one or more multibyte chars',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}