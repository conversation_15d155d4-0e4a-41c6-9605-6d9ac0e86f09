"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrossDomainConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const cors_1 = require("./middleware/cors");
let CrossDomainConfiguration = class CrossDomainConfiguration {
    async onReady() {
        this.applicationManager
            .getApplications(['koa', 'faas', 'express', 'egg'])
            .forEach(app => {
            app.useMiddleware(cors_1.CorsMiddleware);
        });
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayApplicationManager)
], CrossDomainConfiguration.prototype, "applicationManager", void 0);
CrossDomainConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'cross-domain',
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], CrossDomainConfiguration);
exports.CrossDomainConfiguration = CrossDomainConfiguration;
//# sourceMappingURL=configuration.js.map