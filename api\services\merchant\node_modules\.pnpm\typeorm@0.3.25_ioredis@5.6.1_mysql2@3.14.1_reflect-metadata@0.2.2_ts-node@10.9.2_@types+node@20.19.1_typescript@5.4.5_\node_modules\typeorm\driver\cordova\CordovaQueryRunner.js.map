{"version": 3, "sources": ["../../src/driver/cordova/CordovaQueryRunner.ts"], "names": [], "mappings": ";;;AACA,uCAA0C;AAC1C,mEAA+D;AAC/D,iGAA6F;AAC7F,gEAA4D;AAC5D,8DAA0D;AAC1D,0EAAsE;AACtE,4FAAwF;AAGxF;;GAEG;AACH,MAAa,kBAAmB,SAAQ,qDAAyB;IAM7D,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAqB;QAC7B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QACjD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,IAAI,CAAC;YACD,MAAM,GAAG,GAAG,MAAM,IAAI,OAAO,CAAM,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;gBAClD,kBAAkB,CAAC,UAAU,CACzB,KAAK,EACL,UAAU,EACV,CAAC,GAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,EACrB,CAAC,GAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAC1B,CAAA;YACL,CAAC,CAAC,CAAA;YAEF,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;YAED,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACL,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAEhC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,aAAa,EAAE,CAAC;gBACxC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,QAAQ,CAAA;YAC7B,CAAC;iBAAM,CAAC;gBACJ,MAAM,SAAS,GAAG,EAAE,CAAA;gBACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;gBACpC,CAAC;gBAED,MAAM,CAAC,OAAO,GAAG,SAAS,CAAA;gBAC1B,MAAM,CAAC,GAAG,GAAG,SAAS,CAAA;YAC1B,CAAC;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YAED,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IAEH;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,IAAI,oBAAY,CAClB,sDAAsD,CACzD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB;QACnB,MAAM,IAAI,oBAAY,CAClB,sDAAsD,CACzD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB;QACrB,MAAM,IAAI,oBAAY,CAClB,sDAAsD,CACzD,CAAA;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAC7C,IAAI,CAAC;YACD,MAAM,oBAAoB,GAAG,0FAA0F,CAAA;YACvH,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,oBAAoB,CACvB,CAAA;YAED,MAAM,qBAAqB,GAAG,4HAA4H,CAAA;YAC1J,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,qBAAqB,CACxB,CAAA;YAED,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACtD,CAAA;QACL,CAAC;gBAAS,CAAC;YACP,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAChD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,WAAW,CACjB,aAA4B,EAC5B,aAAqB,CAAC;QAEtB,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAA;IAC5E,CAAC;CACJ;AAxOD,gDAwOC", "file": "CordovaQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { CordovaDriver } from \"./CordovaDriver\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class CordovaQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: CordovaDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: CordovaDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n        const queryStartTime = Date.now()\n\n        try {\n            const raw = await new Promise<any>(async (ok, fail) => {\n                databaseConnection.executeSql(\n                    query,\n                    parameters,\n                    (raw: any) => ok(raw),\n                    (err: any) => fail(err),\n                )\n            })\n\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                raw,\n                undefined,\n            )\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            ) {\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n            }\n\n            const result = new QueryResult()\n\n            if (query.substr(0, 11) === \"INSERT INTO\") {\n                result.raw = raw.insertId\n            } else {\n                const resultSet = []\n                for (let i = 0; i < raw.rows.length; i++) {\n                    resultSet.push(raw.rows.item(i))\n                }\n\n                result.records = resultSet\n                result.raw = resultSet\n            }\n\n            if (useStructuredResult) {\n                return result\n            } else {\n                return result.raw\n            }\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n\n    /**\n     * Insert a new row with given values into the given table.\n     * Returns value of the generated column if given and generate column exist in the table.\n     // todo: implement new syntax\n    async insert(tableName: string, keyValues: ObjectLiteral): Promise<InsertResult> {\n        const keys = Object.keys(keyValues);\n        const columns = keys.map(key => `\"${key}\"`).join(\", \");\n        const values = keys.map(key => \"?\").join(\",\");\n        const generatedColumns = this.connection.hasMetadata(tableName) ? this.connection.getMetadata(tableName).generatedColumns : [];\n        const sql = columns.length > 0 ? (`INSERT INTO \"${tableName}\"(${columns}) VALUES (${values})`) : `INSERT INTO \"${tableName}\" DEFAULT VALUES`;\n        const parameters = keys.map(key => keyValues[key]);\n\n        return new Promise<InsertResult>(async (ok, fail) => {\n            this.driver.connection.logger.logQuery(sql, parameters, this);\n            const __this = this;\n            const databaseConnection = await this.connect();\n            databaseConnection.executeSql(sql, parameters, (resultSet: any) => {\n                const generatedMap = generatedColumns.reduce((map, generatedColumn) => {\n                    const value = generatedColumn.isPrimary && generatedColumn.generationStrategy === \"increment\" && resultSet.insertId ? resultSet.insertId : keyValues[generatedColumn.databaseName];\n                    if (!value) return map;\n                    return OrmUtils.mergeDeep(map, generatedColumn.createValueMap(value));\n                }, {} as ObjectLiteral);\n\n                ok({\n                    result: undefined,\n                    generatedMap: Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n                });\n            }, (err: any) => {\n                __this.driver.connection.logger.logQueryError(err, sql, parameters, this);\n                fail(err);\n            });\n        });\n    }*/\n\n    /**\n     * Would start a transaction but this driver does not support transactions.\n     */\n    async startTransaction(): Promise<void> {\n        throw new TypeORMError(\n            \"Transactions are not supported by the Cordova driver\",\n        )\n    }\n\n    /**\n     * Would start a transaction but this driver does not support transactions.\n     */\n    async commitTransaction(): Promise<void> {\n        throw new TypeORMError(\n            \"Transactions are not supported by the Cordova driver\",\n        )\n    }\n\n    /**\n     * Would start a transaction but this driver does not support transactions.\n     */\n    async rollbackTransaction(): Promise<void> {\n        throw new TypeORMError(\n            \"Transactions are not supported by the Cordova driver\",\n        )\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     * Be careful with using this method and avoid using it in production or migrations\n     * (because it can clear all your database).\n     */\n    async clearDatabase(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n        try {\n            const selectViewDropsQuery = `SELECT 'DROP VIEW \"' || name || '\";' as query FROM \"sqlite_master\" WHERE \"type\" = 'view'`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                selectViewDropsQuery,\n            )\n\n            const selectTableDropsQuery = `SELECT 'DROP TABLE \"' || name || '\";' as query FROM \"sqlite_master\" WHERE \"type\" = 'table' AND \"name\" != 'sqlite_sequence'`\n            const dropTableQueries: ObjectLiteral[] = await this.query(\n                selectTableDropsQuery,\n            )\n\n            await Promise.all(\n                dropViewQueries.map((q) => this.query(q[\"query\"])),\n            )\n            await Promise.all(\n                dropTableQueries.map((q) => this.query(q[\"query\"])),\n            )\n        } finally {\n            await this.query(`PRAGMA foreign_keys = ON`)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Parametrizes given object of values. Used to create column=value queries.\n     */\n    protected parametrize(\n        objectLiteral: ObjectLiteral,\n        startIndex: number = 0,\n    ): string[] {\n        return Object.keys(objectLiteral).map((key, index) => `\"${key}\"` + \"=?\")\n    }\n}\n"], "sourceRoot": "../.."}