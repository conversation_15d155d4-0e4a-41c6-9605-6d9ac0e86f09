{"parent": null, "pid": 31813, "argv": ["/Users/<USER>/.nvm/versions/node/v14.15.3/bin/node", "/Users/<USER>/jsProjects/inversify/InversifyJS/node_modules/.bin/mocha", "test/annotation/decorator_utils.test.ts", "test/annotation/inject.test.ts", "test/annotation/injectable.test.ts", "test/annotation/multi_inject.test.ts", "test/annotation/named.test.ts", "test/annotation/optional.test.ts", "test/annotation/post_construct.test.ts", "test/annotation/tagged.test.ts", "test/annotation/target_name.test.ts", "test/bindings/binding.test.ts", "test/bugs/bugs.test.ts", "test/bugs/issue_1190.test.ts", "test/bugs/issue_1297.test.ts", "test/bugs/issue_543.test.ts", "test/bugs/issue_549.test.ts", "test/bugs/issue_633.test.ts", "test/bugs/issue_706.test.ts", "test/bugs/issue_928.test.ts", "test/constants/error_message.test.ts", "test/container/container.test.ts", "test/container/container_module.test.ts", "test/container/lookup.test.ts", "test/container/module_activation_store.test.ts", "test/features/metadata_reader.test.ts", "test/features/named_default.test.ts", "test/features/property_injection.test.ts", "test/features/provider.test.ts", "test/features/request_scope.test.ts", "test/features/resolve_unbinded.test.ts", "test/features/transitive_bindings.test.ts", "test/middleware/middleware.test.ts", "test/node/error_messages.test.ts", "test/node/exceptions.test.ts", "test/node/performance.test.ts", "test/node/proxies.test.ts", "test/planning/context.test.ts", "test/planning/metadata.test.ts", "test/planning/plan.test.ts", "test/planning/planner.test.ts", "test/planning/queryable_string.test.ts", "test/planning/request.test.ts", "test/planning/target.test.ts", "test/resolution/resolver.test.ts", "test/syntax/binding_in_syntax.test.ts", "test/syntax/binding_in_when_on_syntax.test.ts", "test/syntax/binding_on_syntax.test.ts", "test/syntax/binding_to_syntax.test.ts", "test/syntax/binding_when_on_syntax.test.ts", "test/syntax/binding_when_syntax.test.ts", "test/syntax/constraint_helpers.test.ts", "test/utils/binding_utils.test.ts", "test/utils/id.test.ts", "test/utils/serialization.test.ts", "--reporter", "spec", "--retries", "3", "--require", "node_modules/reflect-metadata/Reflect.js", "--exit"], "execArgv": [], "cwd": "/Users/<USER>/jsProjects/inversify/InversifyJS", "time": 1634215802139, "ppid": 31812, "coverageFilename": "/Users/<USER>/jsProjects/inversify/InversifyJS/.nyc_output/c19faffc-964b-4370-81a6-9b4942f3c35e.json", "externalId": "", "uuid": "c19faffc-964b-4370-81a6-9b4942f3c35e", "files": ["/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/decorator_utils.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/error_msgs.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/metadata_keys.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/js.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/inversify.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/bindings/binding.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/literal_types.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/id.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/metadata_reader.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/planner.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/bindings/binding_count.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/exceptions.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/serialization.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/context.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/metadata.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/plan.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/reflection_utils.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/lazy_service_identifier.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/target.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/queryable_string.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/request.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/resolution/resolver.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/scope/scope.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/async.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/binding_utils.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/factory_type.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/resolution/instantiation.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_to_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_in_when_on_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_in_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_when_on_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_on_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_when_syntax.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/constraint_helpers.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container_snapshot.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/lookup.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/clonable.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/module_activation_store.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container_module.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/injectable.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/tagged.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/named.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/inject.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/inject_base.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/optional.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/unmanaged.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/multi_inject.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/target_name.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/post_construct.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/property_event_decorator.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/pre_destroy.ts", "/Users/<USER>/jsProjects/inversify/InversifyJS/src/interfaces/interfaces.ts"]}