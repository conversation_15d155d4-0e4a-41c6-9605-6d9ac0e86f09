{"version": 3, "sources": ["../../src/decorator/columns/ObjectIdColumn.ts"], "names": [], "mappings": ";;AAQA,wCAeC;AAvBD,2CAAsD;AAItD;;;GAGG;AACH,SAAgB,cAAc,CAAC,OAAuB;IAClD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,kEAAkE;QAClE,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAmB,CAAA;QAC3C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAA;QACtB,IAAI,CAAC,OAAO,CAAC,IAAI;YAAE,OAAO,CAAC,IAAI,GAAG,KAAK,CAAA;QAEvC,4CAA4C;QAC5C,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,OAAO;SACG,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "ObjectIdColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * Special type of column that is available only for MongoDB database.\n * Marks your entity's column to be an object id.\n */\nexport function ObjectIdColumn(options?: ColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        // if column options are not given then create a new empty options\n        if (!options) options = {} as ColumnOptions\n        options.primary = true\n        if (!options.name) options.name = \"_id\"\n\n        // create and register a new column metadata\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"objectId\",\n            options: options,\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}