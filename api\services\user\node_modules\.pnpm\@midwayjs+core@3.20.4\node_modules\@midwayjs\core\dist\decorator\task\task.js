"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Task = void 0;
const __1 = require("../");
const constant_1 = require("../constant");
function Task(options) {
    return function (target, propertyKey, descriptor) {
        (0, __1.saveModule)(constant_1.MODULE_TASK_KEY, target.constructor);
        (0, __1.attachClassMetadata)(constant_1.MODULE_TASK_METADATA, {
            options,
            propertyKey,
            value: descriptor.value,
            name: target.constructor.name,
        }, target);
    };
}
exports.Task = Task;
//# sourceMappingURL=task.js.map