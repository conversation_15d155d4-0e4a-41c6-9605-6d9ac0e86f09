{"version": 3, "file": "ipv4.js", "sourceRoot": "", "sources": ["../src/ipv4.ts"], "names": [], "mappings": ";AAAA,sCAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtC,iDAAmC;AACnC,0DAA4C;AAC5C,mDAA+C;AAC/C,+BAAkC;AAClC,2CAAqC;AAErC;;;;GAIG;AACH,MAAa,QAAQ;IAUnB,YAAY,OAAe;QAP3B,WAAM,GAAW,SAAS,CAAC,MAAM,CAAC;QAClC,kBAAa,GAAa,EAAE,CAAC;QAC7B,iBAAY,GAAW,EAAE,CAAC;QAC1B,WAAM,GAAW,KAAK,CAAC;QACvB,eAAU,GAAW,EAAE,CAAC;QACxB,OAAE,GAAY,IAAI,CAAC;QA0DnB;;;;;WAKG;QACH,cAAS,GAAG,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAyO7C;;;;;WAKG;QACH,eAAU,GAAG,MAAM,CAAC,UAAU,CAAC;QA5S7B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,MAAM,MAAM,GAAG,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAExD,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;YAC/C,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;YAClD,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAEpC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE;gBAC3D,MAAM,IAAI,4BAAY,CAAC,sBAAsB,CAAC,CAAC;aAChD;YAED,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;SAC3D;QAED,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC;QAElC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,OAAe;QAC5B,IAAI;YACF,kCAAkC;YAClC,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;YAEtB,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAe;QACnB,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAElC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YACxC,MAAM,IAAI,4BAAY,CAAC,uBAAuB,CAAC,CAAC;SACjD;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxE,CAAC;IAUD;;;;;;OAMG;IACH,MAAM,CAAC,OAAO,CAAC,GAAW;QACxB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACtD,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,CAAC;QAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;YACzB,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAEjC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;SAC9B;QAED,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,WAAW,CAAC,OAAe;QAChC,OAAO,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;;;OASG;IACH,MAAM,CAAC,QAAQ,CAAC,eAAuB;QACrC,6CAA6C;QAC7C,MAAM,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,uBAAuB,EAAE,EAAE,CAAC,CAAC;QAEpE,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEtD,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;IAC/B,CAAC;IAED;;;;;OAKG;IACH,KAAK;QACH,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAA,oBAAO,EAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzF,CAAC;IAED;;;;;OAKG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,CAAC;IAC9D,CAAC;IAED;;;;;OAKG;IACH,QAAQ;QACN,MAAM,MAAM,GAAG,EAAE,CAAC;QAClB,IAAI,CAAC,CAAC;QAEN,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACxC,MAAM,GAAG,GAAG,IAAA,oBAAO,EACjB,UAAU,EACV,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EACnC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CACxC,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAO,EAAC,IAAI,EAAE,QAAQ,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,UAAU;QACR,OAAO,IAAI,iBAAU,CACnB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,oBAAO,EAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EACxE,EAAE,CACH,CAAC;IACJ,CAAC;IAED;;;;;OAKG;IACH,aAAa;QACX,OAAO,IAAI,iBAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;OAMG;IACH,YAAY;QACV,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;OAMG;IACH,qBAAqB;QACnB,MAAM,MAAM,GAAG,IAAI,iBAAU,CAAC,GAAG,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;OAKG;IACH,WAAW;QACT,OAAO,IAAI,iBAAU,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;IACvF,CAAC;IAED;;;;;;OAMG;IACH,UAAU;QACR,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACH,mBAAmB;QACjB,MAAM,MAAM,GAAG,IAAI,iBAAU,CAAC,GAAG,CAAC,CAAC;QACnC,OAAO,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC;IACtE,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,cAAc,CAAC,UAAsB;QAC1C,OAAO,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACnE,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CAAC,IAAa;QAChB,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;SACxB;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,KAAa,EAAE,GAAW;QACrC,OAAO,IAAI,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAChD,CAAC;IAED;;;;;;;OAOG;IACH,WAAW,CAAC,OAAmC;QAC7C,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,EAAE,CAAC;SACd;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEnE,IAAI,OAAO,CAAC,UAAU,EAAE;YACtB,OAAO,QAAQ,CAAC;SACjB;QAED,OAAO,IAAA,oBAAO,EAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAUD;;;;;OAKG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC;IACtD,CAAC;IAED;;;;;OAKG;IACH,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IACrE,CAAC;IAED;;;OAGG;IACH,UAAU;QACR,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC;QAEpC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,CACzB,SAAS,CAAC,UAAU,EACpB,IAAA,oBAAO,EACL,2GAA2G,EAC3G,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAC9B,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAC/B,CACF,CAAC;IACJ,CAAC;CACF;AA7VD,4BA6VC"}