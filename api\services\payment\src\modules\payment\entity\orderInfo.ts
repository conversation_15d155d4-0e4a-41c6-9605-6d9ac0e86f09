import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('order_info')
export class OrderInfoEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 64 })
  orderNum: string;

  @Column({ type: 'int', default: 0 })
  status: number;

  @Column({ type: 'int', default: 0 })
  payType: number;

  @Column({ type: 'datetime', nullable: true })
  payTime: Date;

  // 可根据实际业务继续补充字段
} 