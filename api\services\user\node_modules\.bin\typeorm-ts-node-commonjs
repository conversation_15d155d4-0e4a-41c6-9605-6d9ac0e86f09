#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/typeorm@0.3.24_ioredis@5.6.1_reflect-metadata@0.2.2/node_modules/typeorm/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/typeorm@0.3.24_ioredis@5.6.1_reflect-metadata@0.2.2/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/typeorm@0.3.24_ioredis@5.6.1_reflect-metadata@0.2.2/node_modules/typeorm/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/typeorm@0.3.24_ioredis@5.6.1_reflect-metadata@0.2.2/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typeorm/cli-ts-node-commonjs.js" "$@"
else
  exec node  "$basedir/../typeorm/cli-ts-node-commonjs.js" "$@"
fi
