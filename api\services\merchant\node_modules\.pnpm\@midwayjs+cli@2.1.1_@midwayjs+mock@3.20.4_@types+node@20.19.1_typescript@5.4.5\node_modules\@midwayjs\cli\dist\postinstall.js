"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postinstall = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
const child_process_1 = require("child_process");
const plugins_1 = require("./plugins");
const command_core_1 = require("@midwayjs/command-core");
const matchReg = /(?:^|\s)(?:midway-bin|mw)\s+([a-z]+?)(?:\s|$)/i;
const postinstall = async (baseDir) => {
    const version = require((0, path_1.join)(__dirname, '../package.json')).version || '*';
    const pkgJson = getPkgJson(baseDir);
    const pkgJsonList = [];
    if (pkgJson) {
        pkgJsonList.push(pkgJson);
    }
    let isLerna = false;
    // lerna support
    if ((0, fs_1.existsSync)((0, path_1.join)(baseDir, 'lerna.json'))) {
        const lernaPackagesJson = getLernaPackagesJson();
        pkgJsonList.push(...lernaPackagesJson);
        isLerna = true;
    }
    const modMap = {};
    const installedModMap = {};
    pkgJsonList.forEach(pkgJson => {
        if (!pkgJson) {
            return;
        }
        Object.assign(installedModMap, pkgJson.dependencies, pkgJson.devDependencies);
        if (pkgJson.scripts) {
            Object.keys(pkgJson.scripts).forEach(script => {
                const cmd = pkgJson.scripts[script];
                cmdToMod(cmd, modMap, installedModMap);
            });
        }
    });
    const allMods = Object.keys(modMap);
    const npm = (0, command_core_1.findNpm)({ skipAutoFindNpm: true }).cmd;
    if (allMods.length) {
        console.log('[midway] auto install cli deps by ', npm);
    }
    for (const name of allMods) {
        console.log('[midway] auto install', name);
        await (0, command_core_1.installNpm)({
            baseDir,
            register: npm,
            moduleName: `${name}@${version.split('.')[0]}`,
            slience: true,
            isLerna,
        });
    }
    console.log('[midway] auto install complete');
};
exports.postinstall = postinstall;
const cmdToMod = (cmd, modMap, installedModMap) => {
    if (matchReg.test(cmd)) {
        const command = matchReg.exec(cmd)[1];
        const mod = plugins_1.PluginList.filter(plugin => {
            return !plugin.installed && plugin.command === command;
        });
        if (Array.isArray(mod) && mod.length) {
            mod.forEach(modInfo => {
                const modName = modInfo.mod;
                if (installedModMap[modName]) {
                    return;
                }
                if (!modMap[modName]) {
                    modMap[modName] = true;
                }
            });
        }
    }
};
const getLernaPackagesJson = () => {
    const pkgJsonList = [];
    try {
        const originData = (0, child_process_1.execSync)('npx lerna ls --json').toString();
        const packageInfoList = JSON.parse(originData);
        packageInfoList.forEach(packageInfo => {
            const pkgJson = getPkgJson(packageInfo.location);
            if (pkgJson) {
                pkgJsonList.push(pkgJson);
            }
        });
    }
    catch (_a) {
        // ignore
    }
    return pkgJsonList;
};
const getPkgJson = (dirPath) => {
    const pkgFile = (0, path_1.join)(dirPath, 'package.json');
    if (!(0, fs_1.existsSync)(pkgFile)) {
        return;
    }
    return JSON.parse((0, fs_1.readFileSync)(pkgFile).toString());
};
//# sourceMappingURL=postinstall.js.map