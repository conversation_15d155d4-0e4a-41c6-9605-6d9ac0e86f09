{"version": 3, "sources": ["../browser/src/find-options/operator/ArrayContains.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,aAAa,CACzB,KAAqC;IAErC,OAAO,IAAI,YAAY,CAAC,eAAe,EAAE,KAAY,CAAC,CAAA;AAC1D,CAAC", "file": "ArrayContains.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: ArrayContains([...]) }\n */\nexport function ArrayContains<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"arrayContains\", value as any)\n}\n"], "sourceRoot": "../.."}