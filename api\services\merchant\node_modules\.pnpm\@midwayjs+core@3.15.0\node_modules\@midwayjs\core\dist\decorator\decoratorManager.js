"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomParamDecorator = exports.createCustomMethodDecorator = exports.createCustomPropertyDecorator = exports.getMethodReturnTypes = exports.getPropertyType = exports.getMethodParamTypes = exports.BaseType = exports.isProvide = exports.getProviderUUId = exports.getProviderName = exports.getProviderId = exports.saveProviderId = exports.getObjectDefinition = exports.saveObjectDefinition = exports.getPropertyInject = exports.savePropertyInject = exports.transformTypeFromTSDesign = exports.clearAllModule = exports.resetModule = exports.listModule = exports.clearBindContainer = exports.bindContainer = exports.saveModule = exports.listPreloadModule = exports.savePreloadModule = exports.getPropertyMetadata = exports.attachPropertyMetadata = exports.savePropertyMetadata = exports.listPropertyDataFromClass = exports.getPropertyDataFromClass = exports.attachPropertyDataToClass = exports.savePropertyDataToClass = exports.getClassMetadata = exports.getClassExtendedMetadata = exports.attachClassMetadata = exports.saveClassMetadata = exports.DecoratorManager = exports.INJECT_CLASS_KEY_PREFIX = exports.PRELOAD_MODULE_KEY = void 0;
require("reflect-metadata");
const interface_1 = require("../interface");
const constant_1 = require("./constant");
const types_1 = require("../util/types");
const camelCase_1 = require("../util/camelCase");
const util_1 = require("../util");
const pathFileUtil_1 = require("../util/pathFileUtil");
const debug = require('util').debuglog('midway:core');
exports.PRELOAD_MODULE_KEY = 'INJECTION_PRELOAD_MODULE_KEY';
exports.INJECT_CLASS_KEY_PREFIX = 'INJECTION_CLASS_META_DATA';
class DecoratorManager extends Map {
    constructor() {
        super(...arguments);
        /**
         * the key for meta data store in class
         */
        this.injectClassKeyPrefix = exports.INJECT_CLASS_KEY_PREFIX;
        /**
         * the key for method meta data store in class
         */
        this.injectClassMethodKeyPrefix = 'INJECTION_CLASS_METHOD_META_DATA';
        /**
         * the key for method meta data store in method
         */
        this.injectMethodKeyPrefix = 'INJECTION_METHOD_META_DATA';
    }
    saveModule(key, module) {
        if (this.container) {
            return this.container.saveModule(key, module);
        }
        if (!this.has(key)) {
            this.set(key, new Set());
        }
        this.get(key).add(module);
    }
    listModule(key) {
        if (this.container) {
            return this.container.listModule(key);
        }
        return Array.from(this.get(key) || {});
    }
    resetModule(key) {
        this.set(key, new Set());
    }
    bindContainer(container) {
        this.container = container;
        this.container.transformModule(this);
    }
    static getDecoratorClassKey(decoratorNameKey) {
        return decoratorNameKey.toString() + '_CLS';
    }
    static removeDecoratorClassKeySuffix(decoratorNameKey) {
        return decoratorNameKey.toString().replace('_CLS', '');
    }
    static getDecoratorMethodKey(decoratorNameKey) {
        return decoratorNameKey.toString() + '_METHOD';
    }
    static getDecoratorClsExtendedKey(decoratorNameKey) {
        return decoratorNameKey.toString() + '_EXT';
    }
    static getDecoratorClsMethodPrefix(decoratorNameKey) {
        return decoratorNameKey.toString() + '_CLS_METHOD';
    }
    static getDecoratorClsMethodKey(decoratorNameKey, methodKey) {
        return (DecoratorManager.getDecoratorClsMethodPrefix(decoratorNameKey) +
            ':' +
            methodKey.toString());
    }
    static getDecoratorMethod(decoratorNameKey, methodKey) {
        return (DecoratorManager.getDecoratorMethodKey(decoratorNameKey) +
            '_' +
            methodKey.toString());
    }
    static saveMetadata(metaKey, target, dataKey, data) {
        // filter Object.create(null)
        if (typeof target === 'object' && target.constructor) {
            target = target.constructor;
        }
        let m;
        if (Reflect.hasOwnMetadata(metaKey, target)) {
            m = Reflect.getMetadata(metaKey, target);
        }
        else {
            m = new Map();
        }
        m.set(dataKey, data);
        Reflect.defineMetadata(metaKey, m, target);
    }
    static attachMetadata(metaKey, target, dataKey, data, groupBy, groupMode = 'one') {
        // filter Object.create(null)
        if (typeof target === 'object' && target.constructor) {
            target = target.constructor;
        }
        let m;
        if (Reflect.hasOwnMetadata(metaKey, target)) {
            m = Reflect.getMetadata(metaKey, target);
        }
        else {
            m = new Map();
        }
        if (!m.has(dataKey)) {
            if (groupBy) {
                m.set(dataKey, {});
            }
            else {
                m.set(dataKey, []);
            }
        }
        if (groupBy) {
            if (groupMode === 'one') {
                m.get(dataKey)[groupBy] = data;
            }
            else {
                if (m.get(dataKey)[groupBy]) {
                    m.get(dataKey)[groupBy].push(data);
                }
                else {
                    m.get(dataKey)[groupBy] = [data];
                }
            }
        }
        else {
            m.get(dataKey).push(data);
        }
        Reflect.defineMetadata(metaKey, m, target);
    }
    static getMetadata(metaKey, target, dataKey) {
        // filter Object.create(null)
        if (typeof target === 'object' && target.constructor) {
            target = target.constructor;
        }
        let m;
        if (!Reflect.hasOwnMetadata(metaKey, target)) {
            m = new Map();
            Reflect.defineMetadata(metaKey, m, target);
        }
        else {
            m = Reflect.getMetadata(metaKey, target);
        }
        if (!dataKey) {
            return m;
        }
        return m.get(dataKey);
    }
    /**
     * save meta data to class or property
     * @param decoratorNameKey the alias name for decorator
     * @param data the data you want to store
     * @param target target class
     * @param propertyName
     */
    saveMetadata(decoratorNameKey, data, target, propertyName) {
        if (propertyName) {
            const dataKey = DecoratorManager.getDecoratorMethod(decoratorNameKey, propertyName);
            DecoratorManager.saveMetadata(this.injectMethodKeyPrefix, target, dataKey, data);
        }
        else {
            const dataKey = DecoratorManager.getDecoratorClassKey(decoratorNameKey);
            DecoratorManager.saveMetadata(this.injectClassKeyPrefix, target, dataKey, data);
        }
    }
    /**
     * attach data to class or property
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     * @param groupBy
     */
    attachMetadata(decoratorNameKey, data, target, propertyName, groupBy, groupMode) {
        if (propertyName) {
            const dataKey = DecoratorManager.getDecoratorMethod(decoratorNameKey, propertyName);
            DecoratorManager.attachMetadata(this.injectMethodKeyPrefix, target, dataKey, data, groupBy, groupMode);
        }
        else {
            const dataKey = DecoratorManager.getDecoratorClassKey(decoratorNameKey);
            DecoratorManager.attachMetadata(this.injectClassKeyPrefix, target, dataKey, data, groupBy, groupMode);
        }
    }
    /**
     * get single data from class or property
     * @param decoratorNameKey
     * @param target
     * @param propertyName
     */
    getMetadata(decoratorNameKey, target, propertyName) {
        if (propertyName) {
            const dataKey = DecoratorManager.getDecoratorMethod(decoratorNameKey, propertyName);
            return DecoratorManager.getMetadata(this.injectMethodKeyPrefix, target, dataKey);
        }
        else {
            const dataKey = `${DecoratorManager.getDecoratorClassKey(decoratorNameKey)}`;
            return DecoratorManager.getMetadata(this.injectClassKeyPrefix, target, dataKey);
        }
    }
    /**
     * save property data to class
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     */
    savePropertyDataToClass(decoratorNameKey, data, target, propertyName) {
        const dataKey = DecoratorManager.getDecoratorClsMethodKey(decoratorNameKey, propertyName);
        DecoratorManager.saveMetadata(this.injectClassMethodKeyPrefix, target, dataKey, data);
    }
    /**
     * attach property data to class
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     * @param groupBy
     */
    attachPropertyDataToClass(decoratorNameKey, data, target, propertyName, groupBy) {
        const dataKey = DecoratorManager.getDecoratorClsMethodKey(decoratorNameKey, propertyName);
        DecoratorManager.attachMetadata(this.injectClassMethodKeyPrefix, target, dataKey, data, groupBy);
    }
    /**
     * get property data from class
     * @param decoratorNameKey
     * @param target
     * @param propertyName
     */
    getPropertyDataFromClass(decoratorNameKey, target, propertyName) {
        const dataKey = DecoratorManager.getDecoratorClsMethodKey(decoratorNameKey, propertyName);
        return DecoratorManager.getMetadata(this.injectClassMethodKeyPrefix, target, dataKey);
    }
    /**
     * list property data from class
     * @param decoratorNameKey
     * @param target
     */
    listPropertyDataFromClass(decoratorNameKey, target) {
        const originMap = DecoratorManager.getMetadata(this.injectClassMethodKeyPrefix, target);
        const res = [];
        for (const [key, value] of originMap) {
            if (key.indexOf(DecoratorManager.getDecoratorClsMethodPrefix(decoratorNameKey)) !== -1) {
                res.push(value);
            }
        }
        return res;
    }
}
exports.DecoratorManager = DecoratorManager;
let manager = new DecoratorManager();
if (typeof global === 'object') {
    if (global['MIDWAY_GLOBAL_DECORATOR_MANAGER']) {
        console.warn('DecoratorManager not singleton and please check @midwayjs/core version by "npm ls @midwayjs/core"');
        const coreModulePathList = (0, pathFileUtil_1.getModuleRequirePathList)('@midwayjs/core');
        if (coreModulePathList.length) {
            console.info('The module may be located in:');
            coreModulePathList.forEach((path, index) => {
                console.info(`${index + 1}. ${path}`);
            });
        }
        manager = global['MIDWAY_GLOBAL_DECORATOR_MANAGER'];
    }
    else {
        global['MIDWAY_GLOBAL_DECORATOR_MANAGER'] = manager;
    }
}
/**
 * save data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param mergeIfExist
 */
function saveClassMetadata(decoratorNameKey, data, target, mergeIfExist) {
    if (mergeIfExist && typeof data === 'object') {
        const originData = manager.getMetadata(decoratorNameKey, target);
        if (!originData) {
            return manager.saveMetadata(decoratorNameKey, data, target);
        }
        if (Array.isArray(originData)) {
            return manager.saveMetadata(decoratorNameKey, originData.concat(data), target);
        }
        else {
            return manager.saveMetadata(decoratorNameKey, Object.assign(originData, data), target);
        }
    }
    else {
        return manager.saveMetadata(decoratorNameKey, data, target);
    }
}
exports.saveClassMetadata = saveClassMetadata;
/**
 * attach data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param groupBy
 */
function attachClassMetadata(decoratorNameKey, data, target, groupBy, groupMode) {
    return manager.attachMetadata(decoratorNameKey, data, target, undefined, groupBy, groupMode);
}
exports.attachClassMetadata = attachClassMetadata;
/**
 * get data from class and proto
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 * @param useCache
 */
function getClassExtendedMetadata(decoratorNameKey, target, propertyName, useCache) {
    if (useCache === undefined) {
        useCache = true;
    }
    const extKey = DecoratorManager.getDecoratorClsExtendedKey(decoratorNameKey);
    let metadata = manager.getMetadata(extKey, target, propertyName);
    if (useCache && metadata !== undefined) {
        return metadata;
    }
    const father = Reflect.getPrototypeOf(target);
    if (father && father.constructor !== Object) {
        metadata = (0, util_1.merge)(getClassExtendedMetadata(decoratorNameKey, father, propertyName, useCache), manager.getMetadata(decoratorNameKey, target, propertyName));
    }
    manager.saveMetadata(extKey, metadata || null, target, propertyName);
    return metadata;
}
exports.getClassExtendedMetadata = getClassExtendedMetadata;
/**
 * get data from class
 * @param decoratorNameKey
 * @param target
 */
function getClassMetadata(decoratorNameKey, target) {
    return manager.getMetadata(decoratorNameKey, target);
}
exports.getClassMetadata = getClassMetadata;
/**
 * save property data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
function savePropertyDataToClass(decoratorNameKey, data, target, propertyName) {
    return manager.savePropertyDataToClass(decoratorNameKey, data, target, propertyName);
}
exports.savePropertyDataToClass = savePropertyDataToClass;
/**
 * attach property data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 * @param groupBy
 */
function attachPropertyDataToClass(decoratorNameKey, data, target, propertyName, groupBy) {
    return manager.attachPropertyDataToClass(decoratorNameKey, data, target, propertyName, groupBy);
}
exports.attachPropertyDataToClass = attachPropertyDataToClass;
/**
 * get property data from class
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 */
function getPropertyDataFromClass(decoratorNameKey, target, propertyName) {
    return manager.getPropertyDataFromClass(decoratorNameKey, target, propertyName);
}
exports.getPropertyDataFromClass = getPropertyDataFromClass;
/**
 * list property data from class
 * @param decoratorNameKey
 * @param target
 */
function listPropertyDataFromClass(decoratorNameKey, target) {
    return manager.listPropertyDataFromClass(decoratorNameKey, target);
}
exports.listPropertyDataFromClass = listPropertyDataFromClass;
/**
 * save property data
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
function savePropertyMetadata(decoratorNameKey, data, target, propertyName) {
    return manager.saveMetadata(decoratorNameKey, data, target, propertyName);
}
exports.savePropertyMetadata = savePropertyMetadata;
/**
 * attach property data
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
function attachPropertyMetadata(decoratorNameKey, data, target, propertyName) {
    return manager.attachMetadata(decoratorNameKey, data, target, propertyName);
}
exports.attachPropertyMetadata = attachPropertyMetadata;
/**
 * get property data
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 */
function getPropertyMetadata(decoratorNameKey, target, propertyName) {
    return manager.getMetadata(decoratorNameKey, target, propertyName);
}
exports.getPropertyMetadata = getPropertyMetadata;
/**
 * save preload module by target
 * @param target
 */
function savePreloadModule(target) {
    return saveModule(exports.PRELOAD_MODULE_KEY, target);
}
exports.savePreloadModule = savePreloadModule;
/**
 * list preload module
 */
function listPreloadModule() {
    return listModule(exports.PRELOAD_MODULE_KEY);
}
exports.listPreloadModule = listPreloadModule;
/**
 * save module to inner map
 * @param decoratorNameKey
 * @param target
 */
function saveModule(decoratorNameKey, target) {
    if ((0, types_1.isClass)(target)) {
        saveProviderId(undefined, target);
    }
    return manager.saveModule(decoratorNameKey, target);
}
exports.saveModule = saveModule;
function bindContainer(container) {
    return manager.bindContainer(container);
}
exports.bindContainer = bindContainer;
function clearBindContainer() {
    return (manager.container = null);
}
exports.clearBindContainer = clearBindContainer;
/**
 * list module from decorator key
 * @param decoratorNameKey
 * @param filter
 */
function listModule(decoratorNameKey, filter) {
    const modules = manager.listModule(decoratorNameKey);
    if (filter) {
        return modules.filter(filter);
    }
    else {
        return modules;
    }
}
exports.listModule = listModule;
/**
 * reset module
 * @param decoratorNameKey
 */
function resetModule(decoratorNameKey) {
    return manager.resetModule(decoratorNameKey);
}
exports.resetModule = resetModule;
/**
 * clear all module
 */
function clearAllModule() {
    debug('--- clear all module here ---');
    return manager.clear();
}
exports.clearAllModule = clearAllModule;
function transformTypeFromTSDesign(designFn) {
    if ((0, types_1.isNullOrUndefined)(designFn)) {
        return { name: 'undefined', isBaseType: true, originDesign: designFn };
    }
    switch (designFn.name) {
        case 'String':
            return { name: 'string', isBaseType: true, originDesign: designFn };
        case 'Number':
            return { name: 'number', isBaseType: true, originDesign: designFn };
        case 'Boolean':
            return { name: 'boolean', isBaseType: true, originDesign: designFn };
        case 'Symbol':
            return { name: 'symbol', isBaseType: true, originDesign: designFn };
        case 'Object':
            return { name: 'object', isBaseType: true, originDesign: designFn };
        case 'Function':
            return { name: 'function', isBaseType: true, originDesign: designFn };
        default:
            return {
                name: designFn.name,
                isBaseType: false,
                originDesign: designFn,
            };
    }
}
exports.transformTypeFromTSDesign = transformTypeFromTSDesign;
/**
 * save property inject args
 * @param opts 参数
 */
function savePropertyInject(opts) {
    // 1、use identifier by user
    let identifier = opts.identifier;
    let injectMode = interface_1.InjectModeEnum.Identifier;
    // 2、use identifier by class uuid
    if (!identifier) {
        const type = getPropertyType(opts.target, opts.targetKey);
        if (!type.isBaseType &&
            (0, types_1.isClass)(type.originDesign) &&
            isProvide(type.originDesign)) {
            identifier = getProviderUUId(type.originDesign);
            injectMode = interface_1.InjectModeEnum.Class;
        }
        if (!identifier) {
            // 3、use identifier by property name
            identifier = opts.targetKey;
            injectMode = interface_1.InjectModeEnum.PropertyName;
        }
    }
    attachClassMetadata(constant_1.INJECT_TAG, {
        targetKey: opts.targetKey,
        value: identifier,
        args: opts.args,
        injectMode,
    }, opts.target, opts.targetKey);
}
exports.savePropertyInject = savePropertyInject;
/**
 * get property inject args
 * @param target
 * @param useCache
 */
function getPropertyInject(target, useCache) {
    return getClassExtendedMetadata(constant_1.INJECT_TAG, target, undefined, useCache);
}
exports.getPropertyInject = getPropertyInject;
/**
 * save class object definition
 * @param target class
 * @param props property data
 */
function saveObjectDefinition(target, props = {}) {
    saveClassMetadata(constant_1.OBJ_DEF_CLS, props, target, true);
    return target;
}
exports.saveObjectDefinition = saveObjectDefinition;
/**
 * get class object definition from metadata
 * @param target
 */
function getObjectDefinition(target) {
    return getClassExtendedMetadata(constant_1.OBJ_DEF_CLS, target);
}
exports.getObjectDefinition = getObjectDefinition;
/**
 * class provider id
 * @param identifier id
 * @param target class
 */
function saveProviderId(identifier, target) {
    if (isProvide(target)) {
        if (identifier) {
            const meta = getClassMetadata(constant_1.TAGGED_CLS, target);
            if (meta.id !== identifier) {
                meta.id = identifier;
                // save class id and uuid
                saveClassMetadata(constant_1.TAGGED_CLS, meta, target);
                debug(`update provide: ${target.name} -> ${meta.uuid}`);
            }
        }
    }
    else {
        // save
        const uuid = (0, util_1.generateRandomId)();
        // save class id and uuid
        saveClassMetadata(constant_1.TAGGED_CLS, {
            id: identifier,
            originName: target.name,
            uuid,
            name: (0, camelCase_1.camelCase)(target.name),
        }, target);
        debug(`save provide: ${target.name} -> ${uuid}`);
    }
    return target;
}
exports.saveProviderId = saveProviderId;
/**
 * get provider id from module
 * @param module
 */
function getProviderId(module) {
    const metaData = getClassMetadata(constant_1.TAGGED_CLS, module);
    if (metaData && metaData.id) {
        return metaData.id;
    }
}
exports.getProviderId = getProviderId;
function getProviderName(module) {
    const metaData = getClassMetadata(constant_1.TAGGED_CLS, module);
    if (metaData && metaData.name) {
        return metaData.name;
    }
}
exports.getProviderName = getProviderName;
/**
 * get provider uuid from module
 * @param module
 */
function getProviderUUId(module) {
    const metaData = getClassMetadata(constant_1.TAGGED_CLS, module);
    if (metaData && metaData.uuid) {
        return metaData.uuid;
    }
}
exports.getProviderUUId = getProviderUUId;
/**
 * use @Provide decorator or not
 * @param target class
 */
function isProvide(target) {
    return !!getClassMetadata(constant_1.TAGGED_CLS, target);
}
exports.isProvide = isProvide;
var BaseType;
(function (BaseType) {
    BaseType["Boolean"] = "boolean";
    BaseType["Number"] = "number";
    BaseType["String"] = "string";
})(BaseType = exports.BaseType || (exports.BaseType = {}));
/**
 * get parameters type by reflect-metadata
 */
function getMethodParamTypes(target, methodName) {
    if ((0, types_1.isClass)(target)) {
        target = target.prototype;
    }
    return Reflect.getMetadata('design:paramtypes', target, methodName);
}
exports.getMethodParamTypes = getMethodParamTypes;
/**
 * get property(method) type from metadata
 * @param target
 * @param methodName
 */
function getPropertyType(target, methodName) {
    return transformTypeFromTSDesign(Reflect.getMetadata('design:type', target, methodName));
}
exports.getPropertyType = getPropertyType;
/**
 * get method return type from metadata
 * @param target
 * @param methodName
 */
function getMethodReturnTypes(target, methodName) {
    if ((0, types_1.isClass)(target)) {
        target = target.prototype;
    }
    return Reflect.getMetadata('design:returntype', target, methodName);
}
exports.getMethodReturnTypes = getMethodReturnTypes;
/**
 * create a custom property inject
 * @param decoratorKey
 * @param metadata
 * @param impl default true, configuration need decoratorService.registerMethodHandler
 */
function createCustomPropertyDecorator(decoratorKey, metadata, impl = true) {
    return function (target, propertyName) {
        attachClassMetadata(constant_1.INJECT_CUSTOM_PROPERTY, {
            propertyName,
            key: decoratorKey,
            metadata,
            impl,
        }, target, propertyName);
    };
}
exports.createCustomPropertyDecorator = createCustomPropertyDecorator;
/**
 *
 * @param decoratorKey
 * @param metadata
 * @param impl default true, configuration need decoratorService.registerMethodHandler
 */
function createCustomMethodDecorator(decoratorKey, metadata, implOrOptions = { impl: true }) {
    if (typeof implOrOptions === 'boolean') {
        implOrOptions = { impl: implOrOptions };
    }
    if (implOrOptions.impl === undefined) {
        implOrOptions.impl = true;
    }
    return function (target, propertyName, descriptor) {
        attachClassMetadata(constant_1.INJECT_CUSTOM_METHOD, {
            propertyName,
            key: decoratorKey,
            metadata,
            options: implOrOptions,
        }, target);
    };
}
exports.createCustomMethodDecorator = createCustomMethodDecorator;
/**
 *
 * @param decoratorKey
 * @param metadata
 * @param options
 */
function createCustomParamDecorator(decoratorKey, metadata, implOrOptions = { impl: true }) {
    if (typeof implOrOptions === 'boolean') {
        implOrOptions = { impl: implOrOptions };
    }
    if (implOrOptions.impl === undefined) {
        implOrOptions.impl = true;
    }
    return function (target, propertyName, parameterIndex) {
        attachClassMetadata(constant_1.INJECT_CUSTOM_PARAM, {
            key: decoratorKey,
            parameterIndex,
            propertyName,
            metadata,
            options: implOrOptions,
        }, target, propertyName, 'multi');
    };
}
exports.createCustomParamDecorator = createCustomParamDecorator;
//# sourceMappingURL=decoratorManager.js.map