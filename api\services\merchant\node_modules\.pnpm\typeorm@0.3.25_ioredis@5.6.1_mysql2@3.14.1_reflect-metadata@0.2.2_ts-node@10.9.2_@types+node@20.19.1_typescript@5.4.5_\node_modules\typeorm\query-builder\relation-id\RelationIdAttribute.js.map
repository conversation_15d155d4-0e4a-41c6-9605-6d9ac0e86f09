{"version": 3, "sources": ["../../src/query-builder/relation-id/RelationIdAttribute.ts"], "names": [], "mappings": ";;;AACA,4DAAwD;AAIxD,wDAAoD;AACpD,2DAAuD;AAEvD;;GAEG;AACH,MAAa,mBAAmB;IAgC5B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACY,kBAAsC,EAC9C,mBAAkD;QAD1C,uBAAkB,GAAlB,kBAAkB,CAAoB;QAVlD;;WAEG;QACH,oBAAe,GAAG,KAAK,CAAA;QAUnB,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,mBAAmB,IAAI,EAAE,CAAC,CAAA;IACvD,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,IAAI,uBAAuB;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAA;IAC9C,CAAC;IAED;;;;;OAKG;IACH,IAAI,WAAW;QACX,IAAI,CAAC,qCAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,2BAAY,CAClB,+DAA+D,CAClE,CAAA;QAEL,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;IACtE,CAAC;IAED;;;;;;OAMG;IACH,IAAI,oBAAoB;QACpB,IAAI,CAAC,qCAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,2BAAY,CAClB,+DAA+D,CAClE,CAAA;QAEL,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IACvE,CAAC;IAED;;;;OAIG;IACH,IAAI,QAAQ;QACR,IAAI,CAAC,qCAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,2BAAY,CAClB,+DAA+D,CAClE,CAAA;QAEL,MAAM,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAClE,IAAI,CAAC,WAAY,CACpB,CAAA;QACD,MAAM,QAAQ,GACV,sBAAsB,CAAC,QAAQ,CAAC,4BAA4B,CACxD,IAAI,CAAC,oBAAqB,CAC7B,CAAA;QACL,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,2BAAY,CAClB,+BAA+B,IAAI,CAAC,oBAAoB,2BAA2B,CACtF,CAAA;QACL,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACH,IAAI,aAAa;QACb,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpE,OAAO,WAAW,GAAG,GAAG,GAAG,gBAAgB,GAAG,MAAM,CAAA;IACxD,CAAC;IAED;;;OAGG;IACH,IAAI,gBAAgB;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAA;IAChD,CAAC;IAED,IAAI,wBAAwB;QACxB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;IACxE,CAAC;IAED,IAAI,yBAAyB;QACzB,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IACzE,CAAC;CACJ;AAlID,kDAkIC", "file": "RelationIdAttribute.js", "sourcesContent": ["import { RelationMetadata } from \"../../metadata/RelationMetadata\"\nimport { QueryBuilderUtils } from \"../QueryBuilderUtils\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { QueryExpressionMap } from \"../QueryExpressionMap\"\nimport { SelectQueryBuilder } from \"../SelectQueryBuilder\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\nimport { TypeORMError } from \"../../error/TypeORMError\"\n\n/**\n * Stores all join relation id attributes which will be used to build a JOIN query.\n */\nexport class RelationIdAttribute {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * <PERSON><PERSON> of the joined (destination) table.\n     */\n    alias?: string\n\n    /**\n     * Name of relation.\n     */\n    relationName: string\n\n    /**\n     * Property + alias of the object where to joined data should be mapped.\n     */\n    mapToProperty: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n\n    /**\n     * Indicates if relation id should NOT be loaded as id map.\n     */\n    disableMixedMap = false\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        private queryExpressionMap: QueryExpressionMap,\n        relationIdAttribute?: Partial<RelationIdAttribute>,\n    ) {\n        ObjectUtils.assign(this, relationIdAttribute || {})\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    get joinInverseSideMetadata(): EntityMetadata {\n        return this.relation.inverseEntityMetadata\n    }\n\n    /**\n     * Alias of the parent of this join.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"post\" is a parent alias.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get parentAlias(): string {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value must be a string representation of alias property`,\n            )\n\n        return this.relationName.substr(0, this.relationName.indexOf(\".\"))\n    }\n\n    /**\n     * Relation property name of the parent.\n     * This is used to understand what is joined.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"category\" is a relation property.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get relationPropertyPath(): string {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value must be a string representation of alias property`,\n            )\n\n        return this.relationName.substr(this.relationName.indexOf(\".\") + 1)\n    }\n\n    /**\n     * Relation of the parent.\n     * This is used to understand what is joined.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get relation(): RelationMetadata {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value must be a string representation of alias property`,\n            )\n\n        const relationOwnerSelection = this.queryExpressionMap.findAliasByName(\n            this.parentAlias!,\n        )\n        const relation =\n            relationOwnerSelection.metadata.findRelationWithPropertyPath(\n                this.relationPropertyPath!,\n            )\n        if (!relation)\n            throw new TypeORMError(\n                `Relation with property path ${this.relationPropertyPath} in entity was not found.`,\n            )\n        return relation\n    }\n\n    /**\n     * Generates alias of junction table, whose ids we get.\n     */\n    get junctionAlias(): string {\n        const [parentAlias, relationProperty] = this.relationName.split(\".\")\n        return parentAlias + \"_\" + relationProperty + \"_rid\"\n    }\n\n    /**\n     * Metadata of the joined entity.\n     * If extra condition without entity was joined, then it will return undefined.\n     */\n    get junctionMetadata(): EntityMetadata {\n        return this.relation.junctionEntityMetadata!\n    }\n\n    get mapToPropertyParentAlias(): string {\n        return this.mapToProperty.substr(0, this.mapToProperty.indexOf(\".\"))\n    }\n\n    get mapToPropertyPropertyPath(): string {\n        return this.mapToProperty.substr(this.mapToProperty.indexOf(\".\") + 1)\n    }\n}\n"], "sourceRoot": "../.."}