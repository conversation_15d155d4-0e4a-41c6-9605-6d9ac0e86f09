{"version": 3, "sources": ["../browser/src/connection/ConnectionManager.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,2BAA2B,CAAA;AACtD,OAAO,EAAE,uBAAuB,EAAE,MAAM,kCAAkC,CAAA;AAE1E,OAAO,EAAE,+BAA+B,EAAE,MAAM,0CAA0C,CAAA;AAE1F;;;;;GAKG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QAQI;;WAEG;QACc,kBAAa,GAA4B,IAAI,GAAG,EAAE,CAAA;IA+CvE,CAAC;IAzDG;;OAEG;IACH,IAAI,WAAW;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,CAAA;IAClD,CAAC;IAOD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,GAAG,CAAC,IAAY;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED;;;;OAIG;IACH,GAAG,CAAC,OAAe,SAAS;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;QAC/C,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,uBAAuB,CAAC,IAAI,CAAC,CAAA;QAExD,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,OAA0B;QAC7B,iDAAiD;QACjD,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC1C,OAAO,CAAC,IAAI,IAAI,SAAS,CAC5B,CAAA;QACD,IAAI,eAAe,EAAE,CAAC;YAClB,qEAAqE;YACrE,IAAI,eAAe,CAAC,aAAa;gBAC7B,MAAM,IAAI,+BAA+B,CACrC,OAAO,CAAC,IAAI,IAAI,SAAS,CAC5B,CAAA;QACT,CAAC;QAED,0BAA0B;QAC1B,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,CAAA;QAC1C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QACnD,OAAO,UAAU,CAAA;IACrB,CAAC;CACJ", "file": "ConnectionManager.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport { ConnectionNotFoundError } from \"../error/ConnectionNotFoundError\"\nimport { DataSourceOptions } from \"../data-source/DataSourceOptions\"\nimport { AlreadyHasActiveConnectionError } from \"../error/AlreadyHasActiveConnectionError\"\n\n/**\n * ConnectionManager is used to store and manage multiple orm connections.\n * It also provides useful factory methods to simplify connection creation.\n *\n * @deprecated\n */\nexport class ConnectionManager {\n    /**\n     * List of connections registered in this connection manager.\n     */\n    get connections(): DataSource[] {\n        return Array.from(this.connectionMap.values())\n    }\n\n    /**\n     * Internal lookup to quickly get from a connection name to the Connection object.\n     */\n    private readonly connectionMap: Map<string, DataSource> = new Map()\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Checks if connection with the given name exist in the manager.\n     */\n    has(name: string): boolean {\n        return this.connectionMap.has(name)\n    }\n\n    /**\n     * Gets registered connection with the given name.\n     * If connection name is not given then it will get a default connection.\n     * Throws error if connection with the given name was not found.\n     */\n    get(name: string = \"default\"): DataSource {\n        const connection = this.connectionMap.get(name)\n        if (!connection) throw new ConnectionNotFoundError(name)\n\n        return connection\n    }\n\n    /**\n     * Creates a new connection based on the given connection options and registers it in the manager.\n     * Connection won't be established, you'll need to manually call connect method to establish connection.\n     */\n    create(options: DataSourceOptions): DataSource {\n        // check if such connection is already registered\n        const existConnection = this.connectionMap.get(\n            options.name || \"default\",\n        )\n        if (existConnection) {\n            // if connection is registered and its not closed then throw an error\n            if (existConnection.isInitialized)\n                throw new AlreadyHasActiveConnectionError(\n                    options.name || \"default\",\n                )\n        }\n\n        // create a new connection\n        const connection = new DataSource(options)\n        this.connectionMap.set(connection.name, connection)\n        return connection\n    }\n}\n"], "sourceRoot": ".."}