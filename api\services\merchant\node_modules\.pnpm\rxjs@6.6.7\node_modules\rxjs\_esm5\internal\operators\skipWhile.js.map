{"version": 3, "file": "skipWhile.js", "sources": ["../../../src/internal/operators/skipWhile.ts"], "names": [], "mappings": ";AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAe3C,MAAM,UAAU,SAAS,CAAI,SAA+C;IAC1E,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,SAAS,CAAC,CAAC,EAA7C,CAA6C,CAAC;AAClF,CAAC;AAED;IACE,2BAAoB,SAA+C;QAA/C,cAAS,GAAT,SAAS,CAAsC;IACnE,CAAC;IAED,gCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC/E,CAAC;IACH,wBAAC;AAAD,CAAC,AAPD,IAOC;AAOD;IAAqC,+CAAa;IAIhD,6BAAY,WAA0B,EAClB,SAA+C;QADnE,YAEE,kBAAM,WAAW,CAAC,SACnB;QAFmB,eAAS,GAAT,SAAS,CAAsC;QAJ3D,cAAQ,GAAY,IAAI,CAAC;QACzB,WAAK,GAAW,CAAC,CAAC;;IAK1B,CAAC;IAES,mCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAC9B;QAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;IACH,CAAC;IAEO,8CAAgB,GAAxB,UAAyB,KAAQ;QAC/B,IAAI;YACF,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;SACjC;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SAC7B;IACH,CAAC;IACH,0BAAC;AAAD,CAAC,AA5BD,CAAqC,UAAU,GA4B9C"}