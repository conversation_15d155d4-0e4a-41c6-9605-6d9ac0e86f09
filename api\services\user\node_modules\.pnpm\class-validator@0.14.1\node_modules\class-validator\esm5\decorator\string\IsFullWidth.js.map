{"version": 3, "file": "IsFullWidth.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsFullWidth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,oBAAoB,MAAM,2BAA2B,CAAC;AAE7D,MAAM,CAAC,IAAM,aAAa,GAAG,aAAa,CAAC;AAE3C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc;IACxC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAClE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,iBAAqC;IAC/D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,aAAa;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,WAAW,CAAC,KAAK,CAAC,EAAlB,CAAkB;YACtD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,gDAAgD,EAA7D,CAA6D,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isFullWidthValidator from 'validator/lib/isFullWidth';\n\nexport const IS_FULL_WIDTH = 'isFullWidth';\n\n/**\n * Checks if the string contains any full-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function isFullWidth(value: unknown): boolean {\n  return typeof value === 'string' && isFullWidthValidator(value);\n}\n\n/**\n * Checks if the string contains any full-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsFullWidth(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_FULL_WIDTH,\n      validator: {\n        validate: (value, args): boolean => isFullWidth(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a full-width characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}