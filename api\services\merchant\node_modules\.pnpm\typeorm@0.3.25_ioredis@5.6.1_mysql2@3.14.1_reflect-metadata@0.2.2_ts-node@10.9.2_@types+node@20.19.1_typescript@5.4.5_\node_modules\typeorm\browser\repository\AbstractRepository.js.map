{"version": 3, "sources": ["../browser/src/repository/AbstractRepository.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,sCAAsC,EAAE,MAAM,iDAAiD,CAAA;AACxG,OAAO,EAAE,sBAAsB,EAAE,MAAM,YAAY,CAAA;AACnD,OAAO,EAAE,6BAA6B,EAAE,MAAM,wCAAwC,CAAA;AAGtF;;;;;;GAMG;AACH,MAAM,OAAO,kBAAkB;IAU3B,4EAA4E;IAC5E,sBAAsB;IACtB,4EAA4E;IAE5E;;;OAGG;IACH,IAAc,UAAU;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAW,CAAC,CAAA;QAC1D,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,sCAAsC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEtE,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAS,MAAM,CAAC,CAAA;IACrD,CAAC;IAED;;;OAGG;IACH,IAAc,cAAc;QACxB,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAW,CAAC,CAAA;QAC1D,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,sCAAsC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEtE,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAS,MAAM,CAAC,CAAA;IACzD,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;OAGG;IACO,kBAAkB,CAAC,KAAa;QACtC,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC/D,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,sCAAsC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAEtE,OAAO,IAAI,CAAC,OAAO;aACd,aAAa,CAAS,MAAM,CAAC;aAC7B,kBAAkB,CAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;OAEG;IACO,qBAAqB,CAC3B,MAAqB,EACrB,KAAa;QAEb,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;IAClE,CAAC;IAED;;OAEG;IACO,gBAAgB,CACtB,MAAqB;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,MAAqB;QAErB,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;IACjD,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAE5E;;;OAGG;IACK,yBAAyB,CAC7B,gBAAqB;QAErB,MAAM,4BAA4B,GAC9B,sBAAsB,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;YAC5D,OAAO,CACH,UAAU,CAAC,MAAM;gBACjB,CAAC,OAAO,gBAAgB,KAAK,UAAU;oBACnC,CAAC,CAAC,gBAAgB;oBAClB,CAAC,CAAE,gBAAwB,CAAC,WAAW,CAAC,CAC/C,CAAA;QACL,CAAC,CAAC,CAAA;QACN,IAAI,CAAC,4BAA4B;YAC7B,MAAM,IAAI,6BAA6B,CAAC,gBAAgB,CAAC,CAAA;QAE7D,OAAO,4BAA4B,CAAC,MAAM,CAAA;IAC9C,CAAC;CACJ", "file": "AbstractRepository.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { EntityManager } from \"../entity-manager/EntityManager\"\nimport { Repository } from \"./Repository\"\nimport { TreeRepository } from \"./TreeRepository\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { ObjectType } from \"../common/ObjectType\"\nimport { CustomRepositoryDoesNotHaveEntityError } from \"../error/CustomRepositoryDoesNotHaveEntityError\"\nimport { getMetadataArgsStorage } from \"../globals\"\nimport { CustomRepositoryNotFoundError } from \"../error/CustomRepositoryNotFoundError\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\n\n/**\n * Provides abstract class for custom repositories that do not inherit from original orm Repository.\n * Contains all most-necessary methods to simplify code in the custom repository.\n * All methods are protected thus not exposed and it allows to create encapsulated custom repository.\n *\n * @deprecated use Repository.extend function to create a custom repository\n */\nexport class AbstractRepository<Entity extends ObjectLiteral> {\n    // -------------------------------------------------------------------------\n    // Protected Methods Set Dynamically\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets entity manager that allows to perform repository operations with any entity.\n     */\n    protected manager: EntityManager\n\n    // -------------------------------------------------------------------------\n    // Protected Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets the original ORM repository for the entity that is managed by this repository.\n     * If current repository does not manage any entity, then exception will be thrown.\n     */\n    protected get repository(): Repository<Entity> {\n        const target = this.getCustomRepositoryTarget(this as any)\n        if (!target)\n            throw new CustomRepositoryDoesNotHaveEntityError(this.constructor)\n\n        return this.manager.getRepository<Entity>(target)\n    }\n\n    /**\n     * Gets the original ORM tree repository for the entity that is managed by this repository.\n     * If current repository does not manage any entity, then exception will be thrown.\n     */\n    protected get treeRepository(): TreeRepository<Entity> {\n        const target = this.getCustomRepositoryTarget(this as any)\n        if (!target)\n            throw new CustomRepositoryDoesNotHaveEntityError(this.constructor)\n\n        return this.manager.getTreeRepository<Entity>(target)\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a new query builder for the repository's entity that can be used to build a SQL query.\n     * If current repository does not manage any entity, then exception will be thrown.\n     */\n    protected createQueryBuilder(alias: string): SelectQueryBuilder<Entity> {\n        const target = this.getCustomRepositoryTarget(this.constructor)\n        if (!target)\n            throw new CustomRepositoryDoesNotHaveEntityError(this.constructor)\n\n        return this.manager\n            .getRepository<Entity>(target)\n            .createQueryBuilder(alias)\n    }\n\n    /**\n     * Creates a new query builder for the given entity that can be used to build a SQL query.\n     */\n    protected createQueryBuilderFor<T extends ObjectLiteral>(\n        entity: ObjectType<T>,\n        alias: string,\n    ): SelectQueryBuilder<T> {\n        return this.getRepositoryFor(entity).createQueryBuilder(alias)\n    }\n\n    /**\n     * Gets the original ORM repository for the given entity class.\n     */\n    protected getRepositoryFor<T extends ObjectLiteral>(\n        entity: ObjectType<T>,\n    ): Repository<T> {\n        return this.manager.getRepository(entity)\n    }\n\n    /**\n     * Gets the original ORM tree repository for the given entity class.\n     */\n    protected getTreeRepositoryFor<T extends ObjectLiteral>(\n        entity: ObjectType<T>,\n    ): TreeRepository<T> {\n        return this.manager.getTreeRepository(entity)\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets custom repository's managed entity.\n     * If given custom repository does not manage any entity then undefined will be returned.\n     */\n    private getCustomRepositoryTarget(\n        customRepository: any,\n    ): EntityTarget<any> | undefined {\n        const entityRepositoryMetadataArgs =\n            getMetadataArgsStorage().entityRepositories.find((repository) => {\n                return (\n                    repository.target ===\n                    (typeof customRepository === \"function\"\n                        ? customRepository\n                        : (customRepository as any).constructor)\n                )\n            })\n        if (!entityRepositoryMetadataArgs)\n            throw new CustomRepositoryNotFoundError(customRepository)\n\n        return entityRepositoryMetadataArgs.entity\n    }\n}\n"], "sourceRoot": ".."}