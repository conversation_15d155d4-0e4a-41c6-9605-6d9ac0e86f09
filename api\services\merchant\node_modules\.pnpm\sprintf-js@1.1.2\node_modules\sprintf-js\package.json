{"name": "sprintf-js", "version": "1.1.2", "description": "JavaScript sprintf implementation", "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "main": "src/sprintf.js", "scripts": {"test": "mocha test/*.js", "pretest": "npm run lint", "lint": "eslint .", "lint:fix": "eslint --fix ."}, "repository": {"type": "git", "url": "https://github.com/alexei/sprintf.js.git"}, "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "devDependencies": {"benchmark": "^2.1.4", "eslint": "^5.10.0", "gulp": "^3.9.1", "gulp-benchmark": "^1.1.1", "gulp-eslint": "^5.0.0", "gulp-header": "^2.0.5", "gulp-mocha": "^6.0.0", "gulp-rename": "^1.4.0", "gulp-sourcemaps": "^2.6.4", "gulp-uglify": "^3.0.1", "mocha": "^5.2.0"}}