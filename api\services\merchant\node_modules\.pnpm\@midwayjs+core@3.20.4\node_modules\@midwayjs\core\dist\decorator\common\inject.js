"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InjectClient = exports.Inject = void 0;
const decoratorManager_1 = require("../decoratorManager");
const constant_1 = require("../constant");
function Inject(identifier) {
    return function (target, targetKey) {
        (0, decoratorManager_1.savePropertyInject)({ target, targetKey, identifier });
    };
}
exports.Inject = Inject;
function InjectClient(serviceFactoryClz, clientName) {
    return (0, decoratorManager_1.createCustomPropertyDecorator)(constant_1.FACTORY_SERVICE_CLIENT_KEY, {
        serviceFactoryClz,
        clientName,
    });
}
exports.InjectClient = InjectClient;
//# sourceMappingURL=inject.js.map