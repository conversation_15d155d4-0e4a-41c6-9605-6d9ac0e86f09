{"version": 3, "sources": ["../../src/cli.ts"], "names": [], "mappings": ";;;;AACA,4BAAyB;AACzB,0DAAyB;AACzB,oEAAgE;AAChE,oEAAgE;AAChE,0DAAsD;AACtD,wEAAoE;AACpE,8EAA0E;AAC1E,wEAAoE;AACpE,8EAA0E;AAC1E,0EAAsE;AACtE,gFAA4E;AAC5E,kEAA8D;AAC9D,kFAA8E;AAC9E,8DAA0D;AAC1D,wDAAoD;AACpD,oEAAgE;AAEhE,eAAK;KACA,KAAK,CAAC,+BAA+B,CAAC;KACtC,OAAO,CAAC,IAAI,qCAAiB,EAAE,CAAC;KAChC,OAAO,CAAC,IAAI,mCAAgB,EAAE,CAAC;KAC/B,OAAO,CAAC,IAAI,qCAAiB,EAAE,CAAC;KAChC,OAAO,CAAC,IAAI,2BAAY,EAAE,CAAC;KAC3B,OAAO,CAAC,IAAI,yCAAmB,EAAE,CAAC;KAClC,OAAO,CAAC,IAAI,iDAAuB,EAAE,CAAC;KACtC,OAAO,CAAC,IAAI,+CAAsB,EAAE,CAAC;KACrC,OAAO,CAAC,IAAI,mDAAwB,EAAE,CAAC;KACvC,OAAO,CAAC,IAAI,yCAAmB,EAAE,CAAC;KAClC,OAAO,CAAC,IAAI,2CAAoB,EAAE,CAAC;KACnC,OAAO,CAAC,IAAI,+CAAsB,EAAE,CAAC;KACrC,OAAO,CAAC,IAAI,+BAAc,EAAE,CAAC;KAC7B,OAAO,CAAC,IAAI,qCAAiB,EAAE,CAAC;KAChC,OAAO,CAAC,IAAI,yBAAW,EAAE,CAAC;KAC1B,iBAAiB,EAAE;KACnB,aAAa,CAAC,CAAC,CAAC;KAChB,MAAM,EAAE;KACR,KAAK,CAAC,GAAG,EAAE,SAAS,CAAC;KACrB,IAAI,CAAC,GAAG,CAAC;KACT,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,IAAI,CAAA", "file": "cli.js", "sourcesContent": ["#!/usr/bin/env node\nimport \"reflect-metadata\"\nimport yargs from \"yargs\"\nimport { SchemaSyncCommand } from \"./commands/SchemaSyncCommand\"\nimport { SchemaDropCommand } from \"./commands/SchemaDropCommand\"\nimport { QueryCommand } from \"./commands/QueryCommand\"\nimport { EntityCreateCommand } from \"./commands/EntityCreateCommand\"\nimport { MigrationCreateCommand } from \"./commands/MigrationCreateCommand\"\nimport { MigrationRunCommand } from \"./commands/MigrationRunCommand\"\nimport { MigrationRevertCommand } from \"./commands/MigrationRevertCommand\"\nimport { MigrationShowCommand } from \"./commands/MigrationShowCommand\"\nimport { SubscriberCreateCommand } from \"./commands/SubscriberCreateCommand\"\nimport { SchemaLogCommand } from \"./commands/SchemaLogCommand\"\nimport { MigrationGenerateCommand } from \"./commands/MigrationGenerateCommand\"\nimport { VersionCommand } from \"./commands/VersionCommand\"\nimport { InitCommand } from \"./commands/InitCommand\"\nimport { Cache<PERSON>learCommand } from \"./commands/CacheClearCommand\"\n\nyargs\n    .usage(\"Usage: $0 <command> [options]\")\n    .command(new SchemaSyncCommand())\n    .command(new SchemaLogCommand())\n    .command(new SchemaDropCommand())\n    .command(new QueryCommand())\n    .command(new EntityCreateCommand())\n    .command(new SubscriberCreateCommand())\n    .command(new MigrationCreateCommand())\n    .command(new MigrationGenerateCommand())\n    .command(new MigrationRunCommand())\n    .command(new MigrationShowCommand())\n    .command(new MigrationRevertCommand())\n    .command(new VersionCommand())\n    .command(new CacheClearCommand())\n    .command(new InitCommand())\n    .recommendCommands()\n    .demandCommand(1)\n    .strict()\n    .alias(\"v\", \"version\")\n    .help(\"h\")\n    .alias(\"h\", \"help\").argv\n"], "sourceRoot": "."}