"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayContainer = void 0;
const decorator_1 = require("../decorator");
const configuration_1 = require("../functional/configuration");
const util = require("util");
const definitionRegistry_1 = require("./definitionRegistry");
const interface_1 = require("../interface");
const constants_1 = require("../constants");
const objectDefinition_1 = require("../definitions/objectDefinition");
const functionDefinition_1 = require("../definitions/functionDefinition");
const managedResolverFactory_1 = require("./managedResolverFactory");
const environmentService_1 = require("../service/environmentService");
const configService_1 = require("../service/configService");
const EventEmitter = require("events");
const error_1 = require("../error");
const types_1 = require("../util/types");
const util_1 = require("../util");
const debug = util.debuglog('midway:debug');
const debugBind = util.debuglog('midway:bind');
const debugSpaceLength = 9;
class ContainerConfiguration {
    constructor(container) {
        this.container = container;
        this.loadedMap = new WeakMap();
        this.namespaceList = [];
        this.configurationOptionsList = [];
    }
    load(module) {
        let namespace = decorator_1.MAIN_MODULE_KEY;
        // 可能导出多个
        const configurationExports = this.getConfigurationExport(module);
        if (!configurationExports.length)
            return;
        // 多个的情况，数据交给第一个保存
        for (let i = 0; i < configurationExports.length; i++) {
            const configurationExport = configurationExports[i];
            if (this.loadedMap.get(configurationExport)) {
                // 已经加载过就跳过循环
                continue;
            }
            let configurationOptions;
            if (configurationExport instanceof configuration_1.FunctionalConfiguration) {
                // 函数式写法
                configurationOptions = configurationExport.getConfigurationOptions();
            }
            else {
                // 普通类写法
                configurationOptions = (0, decorator_1.getClassMetadata)(decorator_1.CONFIGURATION_KEY, configurationExport);
            }
            // 已加载标记，防止死循环
            this.loadedMap.set(configurationExport, true);
            if (configurationOptions) {
                if (configurationOptions.namespace !== undefined) {
                    namespace = configurationOptions.namespace;
                    this.namespaceList.push(namespace);
                }
                this.configurationOptionsList.push(configurationOptions);
                debug(`[core]: load configuration in namespace="${namespace}"`);
                this.addImports(configurationOptions.imports);
                this.addImportObjects(configurationOptions.importObjects);
                this.addImportConfigs(configurationOptions.importConfigs);
                this.addImportConfigFilter(configurationOptions.importConfigFilter);
                this.bindConfigurationClass(configurationExport, namespace);
            }
        }
        // bind module
        this.container.bindClass(module, {
            namespace,
        });
    }
    addImportConfigs(importConfigs) {
        if (importConfigs) {
            if (Array.isArray(importConfigs)) {
                this.container.get(configService_1.MidwayConfigService).add(importConfigs);
            }
            else {
                this.container.get(configService_1.MidwayConfigService).addObject(importConfigs);
            }
        }
    }
    addImportConfigFilter(importConfigFilter) {
        if (importConfigFilter) {
            this.container.get(configService_1.MidwayConfigService).addFilter(importConfigFilter);
        }
    }
    addImports(imports = []) {
        var _a;
        // 处理 imports
        for (let importPackage of imports) {
            if (!importPackage)
                continue;
            if (typeof importPackage === 'string') {
                importPackage = require(importPackage);
            }
            if ('Configuration' in importPackage) {
                // component is object
                this.load(importPackage);
            }
            else if ('component' in importPackage) {
                if (importPackage === null || importPackage === void 0 ? void 0 : importPackage.enabledEnvironment) {
                    if ((_a = importPackage === null || importPackage === void 0 ? void 0 : importPackage.enabledEnvironment) === null || _a === void 0 ? void 0 : _a.includes(this.container
                        .get(environmentService_1.MidwayEnvironmentService)
                        .getCurrentEnvironment())) {
                        this.load(importPackage.component);
                    }
                }
                else {
                    this.load(importPackage.component);
                }
            }
            else {
                this.load(importPackage);
            }
        }
    }
    /**
     * 注册 importObjects
     * @param objs configuration 中的 importObjects
     */
    addImportObjects(objs) {
        if (objs) {
            const keys = Object.keys(objs);
            for (const key of keys) {
                if (typeof objs[key] !== undefined) {
                    this.container.registerObject(key, objs[key]);
                }
            }
        }
    }
    bindConfigurationClass(clzz, namespace) {
        if (clzz instanceof configuration_1.FunctionalConfiguration) {
            // 函数式写法不需要绑定到容器
        }
        else {
            // 普通类写法
            (0, decorator_1.saveProviderId)(undefined, clzz);
            const id = (0, decorator_1.getProviderUUId)(clzz);
            this.container.bind(id, clzz, {
                namespace: namespace,
                scope: interface_1.ScopeEnum.Singleton,
            });
        }
        // configuration 手动绑定去重
        const configurationMods = (0, decorator_1.listModule)(decorator_1.CONFIGURATION_KEY);
        const exists = configurationMods.find(mod => {
            return mod.target === clzz;
        });
        if (!exists) {
            (0, decorator_1.saveModule)(decorator_1.CONFIGURATION_KEY, {
                target: clzz,
                namespace: namespace,
            });
        }
    }
    getConfigurationExport(exports) {
        const mods = [];
        if (types_1.Types.isClass(exports) ||
            types_1.Types.isFunction(exports) ||
            exports instanceof configuration_1.FunctionalConfiguration) {
            mods.push(exports);
        }
        else {
            for (const m in exports) {
                const module = exports[m];
                if (types_1.Types.isClass(module) ||
                    types_1.Types.isFunction(module) ||
                    module instanceof configuration_1.FunctionalConfiguration) {
                    mods.push(module);
                }
            }
        }
        return mods;
    }
    getNamespaceList() {
        return this.namespaceList;
    }
    getConfigurationOptionsList() {
        return this.configurationOptionsList;
    }
}
class MidwayContainer {
    constructor(parent) {
        this._resolverFactory = null;
        this._registry = null;
        this._identifierMapping = null;
        this.moduleMap = null;
        this.parent = null;
        // 仅仅用于兼容 requestContainer 的 ctx
        this.ctx = constants_1.SINGLETON_CONTAINER_CTX;
        this.attrMap = new Map();
        this._namespaceSet = null;
        this.parent = parent;
        this.init();
    }
    init() {
        // 防止直接从applicationContext.getAsync or get对象实例时依赖当前上下文信息出错
        // ctx is in requestContainer
        this.registerObject(constants_1.REQUEST_CTX_KEY, this.ctx);
    }
    get objectCreateEventTarget() {
        if (!this._objectCreateEventTarget) {
            this._objectCreateEventTarget = new EventEmitter();
        }
        return this._objectCreateEventTarget;
    }
    get registry() {
        if (!this._registry) {
            this._registry = new definitionRegistry_1.ObjectDefinitionRegistry();
        }
        return this._registry;
    }
    set registry(registry) {
        this._registry = registry;
    }
    get managedResolverFactory() {
        if (!this._resolverFactory) {
            this._resolverFactory = new managedResolverFactory_1.ManagedResolverFactory(this);
        }
        return this._resolverFactory;
    }
    get identifierMapping() {
        if (!this._identifierMapping) {
            this._identifierMapping = this.registry.getIdentifierRelation();
        }
        return this._identifierMapping;
    }
    get namespaceSet() {
        if (!this._namespaceSet) {
            this._namespaceSet = new Set();
        }
        return this._namespaceSet;
    }
    load(module) {
        var _a, _b, _c;
        if (!Array.isArray(module)) {
            module = [module];
        }
        // load configuration
        const configuration = new ContainerConfiguration(this);
        for (const mod of module) {
            if (mod) {
                configuration.load(mod);
            }
        }
        for (const ns of configuration.getNamespaceList()) {
            this.namespaceSet.add(ns);
            debug(`[core]: load configuration in namespace="${ns}" complete`);
        }
        const configurationOptionsList = (_a = configuration.getConfigurationOptionsList()) !== null && _a !== void 0 ? _a : [];
        // find user code configuration it's without namespace
        const userCodeConfiguration = (_b = configurationOptionsList.find(options => !options.namespace)) !== null && _b !== void 0 ? _b : {};
        this.fileDetector = (_c = userCodeConfiguration.detector) !== null && _c !== void 0 ? _c : this.fileDetector;
        if (this.fileDetector) {
            this.fileDetector.setExtraDetectorOptions({
                conflictCheck: userCodeConfiguration.conflictCheck,
                ...userCodeConfiguration.detectorOptions,
            });
        }
    }
    loadDefinitions() {
        // load project file
        if (this.fileDetector) {
            return this.fileDetector.run(this);
        }
    }
    bindClass(exports, options) {
        if (types_1.Types.isClass(exports) || types_1.Types.isFunction(exports)) {
            this.bindModule(exports, options);
        }
        else {
            for (const m in exports) {
                const module = exports[m];
                if (types_1.Types.isClass(module) || types_1.Types.isFunction(module)) {
                    this.bindModule(module, options);
                }
            }
        }
    }
    bind(identifier, target, options) {
        var _a;
        if (types_1.Types.isClass(identifier) || types_1.Types.isFunction(identifier)) {
            return this.bindModule(identifier, target);
        }
        if (this.registry.hasDefinition(identifier)) {
            // 如果 definition 存在就不再重复 bind
            return;
        }
        if (options === null || options === void 0 ? void 0 : options.bindHook) {
            options.bindHook(target, options);
        }
        let definition;
        if (types_1.Types.isClass(target)) {
            definition = new objectDefinition_1.ObjectDefinition();
            definition.name = (0, decorator_1.getProviderName)(target);
        }
        else {
            definition = new functionDefinition_1.FunctionDefinition();
            if (!types_1.Types.isAsyncFunction(target)) {
                definition.asynchronous = false;
            }
            definition.name = definition.id;
        }
        definition.path = target;
        definition.id = identifier;
        definition.srcPath = (options === null || options === void 0 ? void 0 : options.srcPath) || null;
        definition.namespace = (options === null || options === void 0 ? void 0 : options.namespace) || '';
        definition.scope = (options === null || options === void 0 ? void 0 : options.scope) || interface_1.ScopeEnum.Request;
        definition.createFrom = options === null || options === void 0 ? void 0 : options.createFrom;
        if (definition.srcPath) {
            debug(`[core]: bind id "${definition.name} (${definition.srcPath}) ${identifier}"`);
        }
        else {
            debug(`[core]: bind id "${definition.name}" ${identifier}`);
        }
        // inject properties
        const props = (0, decorator_1.getPropertyInject)(target);
        for (const p in props) {
            const propertyMeta = props[p];
            debugBind(`${' '.repeat(debugSpaceLength)}inject properties => [${JSON.stringify(propertyMeta)}]`);
            const refManaged = new managedResolverFactory_1.ManagedReference();
            refManaged.args = propertyMeta.args;
            refManaged.name = propertyMeta.value;
            refManaged.injectMode = propertyMeta['injectMode'];
            definition.properties.set(propertyMeta['targetKey'], refManaged);
        }
        // inject custom properties
        const customProps = (0, decorator_1.getClassExtendedMetadata)(decorator_1.INJECT_CUSTOM_PROPERTY, target);
        for (const p in customProps) {
            const propertyMeta = customProps[p];
            definition.handlerProps.push(propertyMeta);
        }
        // @async, @init, @destroy @scope
        const objDefOptions = (_a = (0, decorator_1.getObjectDefinition)(target)) !== null && _a !== void 0 ? _a : {};
        if (objDefOptions.initMethod) {
            debugBind(`${' '.repeat(debugSpaceLength)}register initMethod = ${objDefOptions.initMethod}`);
            definition.initMethod = objDefOptions.initMethod;
        }
        if (objDefOptions.destroyMethod) {
            debugBind(`${' '.repeat(debugSpaceLength)}register destroyMethod = ${objDefOptions.destroyMethod}`);
            definition.destroyMethod = objDefOptions.destroyMethod;
        }
        if (objDefOptions.scope) {
            debugBind(`${' '.repeat(debugSpaceLength)}register scope = ${objDefOptions.scope}`);
            definition.scope = objDefOptions.scope;
        }
        if (objDefOptions.allowDowngrade) {
            debugBind(`${' '.repeat(debugSpaceLength)}register allowDowngrade = ${objDefOptions.allowDowngrade}`);
            definition.allowDowngrade = objDefOptions.allowDowngrade;
        }
        this.objectCreateEventTarget.emit(interface_1.ObjectLifeCycleEvent.BEFORE_BIND, target, {
            context: this,
            definition,
            replaceCallback: newDefinition => {
                definition = newDefinition;
            },
        });
        if (definition) {
            this.registry.registerDefinition(definition.id, definition);
        }
    }
    bindModule(module, options) {
        if (types_1.Types.isClass(module)) {
            const providerId = (0, decorator_1.getProviderUUId)(module);
            if (providerId) {
                this.identifierMapping.saveClassRelation(module, options === null || options === void 0 ? void 0 : options.namespace);
                this.bind(providerId, module, options);
            }
            else {
                // no provide or js class must be skip
            }
        }
        else {
            const info = module[constants_1.FUNCTION_INJECT_KEY];
            if (info && info.id) {
                if (!info.scope) {
                    info.scope = interface_1.ScopeEnum.Request;
                }
                const uuid = util_1.Utils.generateRandomId();
                this.identifierMapping.saveFunctionRelation(info.id, uuid);
                this.bind(uuid, module, {
                    scope: info.scope,
                    namespace: options.namespace,
                    srcPath: options.srcPath,
                    createFrom: options.createFrom,
                });
            }
        }
    }
    setFileDetector(fileDetector) {
        this.fileDetector = fileDetector;
    }
    createChild() {
        return new MidwayContainer(this);
    }
    setAttr(key, value) {
        this.attrMap.set(key, value);
    }
    getAttr(key) {
        return this.attrMap.get(key);
    }
    getIdentifier(target) {
        return (0, decorator_1.getProviderUUId)(target);
    }
    getManagedResolverFactory() {
        if (!this._resolverFactory) {
            this._resolverFactory = new managedResolverFactory_1.ManagedResolverFactory(this);
        }
        return this._resolverFactory;
    }
    async stop() {
        await this.getManagedResolverFactory().destroyCache();
        this.registry.clearAll();
    }
    ready() {
        return this.loadDefinitions();
    }
    get(identifier, args, objectContext) {
        var _a;
        args = args !== null && args !== void 0 ? args : [];
        objectContext = objectContext !== null && objectContext !== void 0 ? objectContext : { originName: identifier };
        if (typeof identifier !== 'string') {
            objectContext.originName = identifier.name;
            identifier = this.getIdentifier(identifier);
        }
        if (this.registry.hasObject(identifier)) {
            return this.registry.getObject(identifier);
        }
        const definition = this.registry.getDefinition(identifier);
        if (!definition && this.parent) {
            return this.parent.get(identifier, args);
        }
        if (!definition) {
            throw new error_1.MidwayDefinitionNotFoundError((_a = objectContext === null || objectContext === void 0 ? void 0 : objectContext.originName) !== null && _a !== void 0 ? _a : identifier);
        }
        return this.getManagedResolverFactory().create({ definition, args });
    }
    async getAsync(identifier, args, objectContext) {
        var _a;
        args = args !== null && args !== void 0 ? args : [];
        objectContext = objectContext !== null && objectContext !== void 0 ? objectContext : { originName: identifier };
        if (typeof identifier !== 'string') {
            objectContext.originName = identifier.name;
            identifier = this.getIdentifier(identifier);
        }
        if (this.registry.hasObject(identifier)) {
            return this.registry.getObject(identifier);
        }
        const definition = this.registry.getDefinition(identifier);
        if (!definition && this.parent) {
            return this.parent.getAsync(identifier, args);
        }
        if (!definition) {
            throw new error_1.MidwayDefinitionNotFoundError((_a = objectContext === null || objectContext === void 0 ? void 0 : objectContext.originName) !== null && _a !== void 0 ? _a : identifier);
        }
        return this.getManagedResolverFactory().createAsync({ definition, args });
    }
    /**
     * proxy registry.registerObject
     * @param {ObjectIdentifier} identifier
     * @param target
     */
    registerObject(identifier, target) {
        this.registry.registerObject(identifier, target);
    }
    onBeforeBind(fn) {
        this.objectCreateEventTarget.on(interface_1.ObjectLifeCycleEvent.BEFORE_BIND, fn);
    }
    onBeforeObjectCreated(fn) {
        this.objectCreateEventTarget.on(interface_1.ObjectLifeCycleEvent.BEFORE_CREATED, fn);
    }
    onObjectCreated(fn) {
        this.objectCreateEventTarget.on(interface_1.ObjectLifeCycleEvent.AFTER_CREATED, fn);
    }
    onObjectInit(fn) {
        this.objectCreateEventTarget.on(interface_1.ObjectLifeCycleEvent.AFTER_INIT, fn);
    }
    onBeforeObjectDestroy(fn) {
        this.objectCreateEventTarget.on(interface_1.ObjectLifeCycleEvent.BEFORE_DESTROY, fn);
    }
    saveModule(key, module) {
        if (!this.moduleMap.has(key)) {
            this.moduleMap.set(key, new Set());
        }
        this.moduleMap.get(key).add(module);
    }
    listModule(key) {
        return Array.from(this.moduleMap.get(key) || {});
    }
    transformModule(moduleMap) {
        this.moduleMap = new Map(moduleMap);
    }
    hasNamespace(ns) {
        return this.namespaceSet.has(ns);
    }
    getNamespaceList() {
        return Array.from(this.namespaceSet);
    }
    hasDefinition(identifier) {
        return this.registry.hasDefinition(identifier);
    }
    hasObject(identifier) {
        return this.registry.hasObject(identifier);
    }
    getInstanceScope(instance) {
        if (instance[constants_1.CONTAINER_OBJ_SCOPE]) {
            return instance[constants_1.CONTAINER_OBJ_SCOPE];
        }
        return undefined;
    }
}
exports.MidwayContainer = MidwayContainer;
//# sourceMappingURL=container.js.map