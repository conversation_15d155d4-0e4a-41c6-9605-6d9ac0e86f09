{"version": 3, "sources": ["../../src/metadata-args/ColumnMetadataArgs.ts"], "names": [], "mappings": "", "file": "ColumnMetadataArgs.js", "sourcesContent": ["import { ColumnOptions } from \"../decorator/options/ColumnOptions\"\nimport { ColumnMode } from \"./types/ColumnMode\"\n\n/**\n * Arguments for ColumnMetadata class.\n */\nexport interface ColumnMetadataArgs {\n    /**\n     * Class to which column is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * Class's property name to which column is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * Column mode in which column will work.\n     *\n     * todo: find name better then \"mode\".\n     */\n    readonly mode: ColumnMode\n\n    /**\n     * Extra column options.\n     */\n    readonly options: ColumnOptions\n}\n"], "sourceRoot": ".."}