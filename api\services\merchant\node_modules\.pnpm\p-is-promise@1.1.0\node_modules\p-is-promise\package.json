{"name": "p-is-promise", "version": "1.1.0", "description": "Check if something is a promise", "license": "MIT", "repository": "sindresorhus/p-is-promise", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "is", "detect", "check", "kind", "type", "thenable", "es2015", "async", "await", "promises", "bluebird"], "devDependencies": {"ava": "*", "bluebird": "^3.4.6", "xo": "*"}, "xo": {"esnext": true}}