{"version": 3, "file": "tryCatch.js", "sources": ["../../src/internal/util/tryCatch.ts"], "names": [], "mappings": ";;AAAA,6CAA4C;AAE5C,IAAI,cAAwB,CAAC;AAE7B,SAAS,UAAU;IACjB,yBAAW,CAAC,CAAC,GAAG,SAAS,CAAC;IAC1B,IAAI;QACF,OAAO,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;KAC9C;IAAC,OAAO,CAAC,EAAE;QACV,yBAAW,CAAC,CAAC,GAAG,CAAC,CAAC;QAClB,OAAO,yBAAW,CAAC;KACpB;YAAS;QACR,cAAc,GAAG,SAAS,CAAC;KAC5B;AACH,CAAC;AAED,SAAgB,QAAQ,CAAqB,EAAK;IAChD,cAAc,GAAG,EAAE,CAAC;IACpB,OAAY,UAAU,CAAC;AACzB,CAAC;AAHD,4BAGC"}