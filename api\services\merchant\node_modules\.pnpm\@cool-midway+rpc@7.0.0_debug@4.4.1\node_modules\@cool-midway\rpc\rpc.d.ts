import { ILogger, IMidwayApplication } from '@midwayjs/core';
import { ServiceBroker } from 'moleculer';
import { CoolRpcConfig } from '.';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
/**
 * 微服务
 */
export declare class CoolRpc {
    broker: ServiceBroker;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    coreLogger: ILogger;
    rpcConfig: CoolRpcConfig;
    coolConfig: any;
    app: IMidwayApplication;
    cruds: any;
    init(): Promise<void>;
    /**
     * 获得事件
     * @returns
     */
    getEvents(): Promise<{}>;
    /**
     * 创建服务
     */
    createService(): Promise<void>;
    /**
     * 初始化service，设置entity
     */
    initService(): Promise<void>;
    /**
     * 获得Model
     * @param curdOption
     */
    getModel(curdOption: any): any;
    /**
     * 调用服务
     * @param name 服务名称
     * @param controller 接口服务
     * @param method 方法
     * @param params 参数
     * @returns
     */
    call(name: string, service: string, method: string, params?: {}): Promise<unknown>;
    /**
     * 发送事件
     * @param name 事件名称
     * @param params 事件参数
     * @param node 节点名称
     */
    event(name: string, params: any, node?: string | string[]): Promise<void>;
    /**
     * 发送广播事件
     * @param name
     * @param params
     * @param node 节点名称
     */
    broadcastEvent(name: string, params: any, node?: string | string[]): Promise<void>;
    /**
     * 发送本地广播事件
     * @param name
     * @param params
     * @param node 节点名称
     */
    broadcastLocalEvent(name: string, params: any, node?: string | string[]): Promise<void>;
    /**
     * 获得原始的broker对象
     * @returns
     */
    getBroker(): ServiceBroker;
    /**
     * 停止
     */
    stop(): void;
}
