{"version": 3, "file": "fromEvent.js", "sources": ["../../../src/internal/observable/fromEvent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AAEvC,IAAM,QAAQ,GAAa,CAAC,cAAM,OAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAzB,CAAyB,CAAC,EAAE,CAAC;AA0K/D,MAAM,UAAU,SAAS,CACvB,MAA0B,EAC1B,SAAiB,EACjB,OAAwD,EACxD,cAAwC;IAGxC,IAAI,UAAU,CAAC,OAAO,CAAC,EAAE;QAEvB,cAAc,GAAG,OAAO,CAAC;QACzB,OAAO,GAAG,SAAS,CAAC;KACrB;IACD,IAAI,cAAc,EAAE;QAElB,OAAO,SAAS,CAAI,MAAM,EAAE,SAAS,EAAoC,OAAO,CAAC,CAAC,IAAI,CACpF,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,eAAI,IAAI,EAAE,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,EAA9D,CAA8D,CAAC,CAC5E,CAAC;KACH;IAED,OAAO,IAAI,UAAU,CAAI,UAAA,UAAU;QACjC,SAAS,OAAO,CAAC,CAAI;YACnB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBACxB,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACpB;QACH,CAAC;QACD,iBAAiB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAA+B,CAAC,CAAC;IAC7F,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAI,SAA6B,EAAE,SAAiB,EAChD,OAAiC,EAAE,UAAyB,EAC5D,OAA8B;IAC1D,IAAI,WAAuB,CAAC;IAC5B,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;QAC5B,IAAM,QAAM,GAAG,SAAS,CAAC;QACzB,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;QACxD,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAvD,CAAuD,CAAC;KAC7E;SAAM,IAAI,yBAAyB,CAAC,SAAS,CAAC,EAAE;QAC/C,IAAM,QAAM,GAAG,SAAS,CAAC;QACzB,SAAS,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjC,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,EAA9B,CAA8B,CAAC;KACpD;SAAM,IAAI,uBAAuB,CAAC,SAAS,CAAC,EAAE;QAC7C,IAAM,QAAM,GAAG,SAAS,CAAC;QACzB,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE,OAA2B,CAAC,CAAC;QAC9D,WAAW,GAAG,cAAM,OAAA,QAAM,CAAC,cAAc,CAAC,SAAS,EAAE,OAA2B,CAAC,EAA7D,CAA6D,CAAC;KACnF;SAAM,IAAI,SAAS,IAAK,SAAiB,CAAC,MAAM,EAAE;QACjD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAI,SAAiB,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC7D,iBAAiB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;SAC1E;KACF;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;KAC7C;IAED,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,uBAAuB,CAAC,SAAc;IAC7C,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,WAAW,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,cAAc,KAAK,UAAU,CAAC;AACpH,CAAC;AAED,SAAS,yBAAyB,CAAC,SAAc;IAC/C,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,EAAE,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,GAAG,KAAK,UAAU,CAAC;AAChG,CAAC;AAED,SAAS,aAAa,CAAC,SAAc;IACnC,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,gBAAgB,KAAK,UAAU,IAAI,OAAO,SAAS,CAAC,mBAAmB,KAAK,UAAU,CAAC;AAC9H,CAAC"}