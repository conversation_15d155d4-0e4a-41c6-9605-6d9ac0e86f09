"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const ErrorMap = {
    commandIsEntrypoint: (info) => {
        return {
            info,
            message: `command ${info.command} is entrypoint, cannot invoke`,
        };
    },
    commandNotFound: (info) => {
        return {
            info,
            message: `command ${info.command} not found`,
        };
    },
    localPlugin: (info) => {
        return {
            info,
            message: `load local plugin '${info.path}' error '${info.err.message}'`,
        };
    },
    npmPlugin: (info) => {
        return {
            info,
            message: `load npm plugin '${info.path}' error '${info.err.message}'`,
        };
    },
    pluginType: (info) => {
        return {
            info,
            message: 'only support npm / local / class plugin',
        };
    },
};
exports.default = (type, info) => {
    const error = ErrorMap[type];
    if (!error) {
        return {
            info,
            message: 'error',
        };
    }
    return error(info);
};
//# sourceMappingURL=errorMap.js.map