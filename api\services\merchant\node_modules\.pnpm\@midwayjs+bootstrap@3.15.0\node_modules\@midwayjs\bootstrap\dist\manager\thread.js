"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreadManager = void 0;
const base_1 = require("./base");
const worker_threads_1 = require("worker_threads");
const event_bus_1 = require("@midwayjs/event-bus");
const core_1 = require("@midwayjs/core");
class ThreadManager extends base_1.AbstractForkManager {
    constructor(options = {}) {
        super(options);
        this.options = options;
        options.argv = options.argv || [];
        options.execArgv = options.execArgv || [];
        process.env.MIDWAY_FORK_MODE = 'thread';
    }
    createWorker() {
        let w;
        let content = `
    require(${JSON.stringify(this.options.exec)});
  `;
        // 当前是 ts 环境
        if ((0, core_1.isTypeScriptEnvironment)()) {
            content = `
    require("ts-node/register/transpile-only");
    ${content}
    `;
            w = new worker_threads_1.Worker(content, { eval: true, env: worker_threads_1.SHARE_ENV });
            w['_originThreadId'] = String(w.threadId);
            this.options.logger.info('new worker thread with ts-node, threadId = %s.', this.getWorkerId(w));
        }
        else {
            w = new worker_threads_1.Worker(content, { eval: true, env: worker_threads_1.SHARE_ENV });
            this.options.logger.info('new worker thread, threadId = %s.', this.getWorkerId(w));
        }
        w.on('exit', code => {
            this.workerExitListener(w, code);
        });
        return w;
    }
    bindWorkerDisconnect(listener) {
        // this.disconnectListener = listener;
    }
    bindWorkerExit(listener) {
        this.workerExitListener = listener;
    }
    getWorkerId(worker) {
        return worker['_originThreadId'] || String(worker.threadId);
    }
    isWorkerDead(worker) {
        return false;
    }
    async closeWorker(worker) {
        await worker.terminate();
    }
    createEventBus(options) {
        return new event_bus_1.ThreadEventBus(options);
    }
    isPrimary() {
        return worker_threads_1.isMainThread;
    }
}
exports.ThreadManager = ThreadManager;
//# sourceMappingURL=thread.js.map