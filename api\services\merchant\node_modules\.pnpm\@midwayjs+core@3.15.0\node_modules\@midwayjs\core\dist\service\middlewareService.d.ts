import { CommonMiddleware, IMidwayContainer, IMidwayApplication } from '../interface';
export declare class MidwayMiddlewareService<T, R, N = unknown> {
    readonly applicationContext: IMidwayContainer;
    constructor(applicationContext: IMidwayContainer);
    compose(middleware: Array<CommonMiddleware<T, R, N> | string>, app: IMidwayApplication, name?: string): Promise<{
        (context: T, next?: any): Promise<any>;
        _name: string;
    }>;
}
//# sourceMappingURL=middlewareService.d.ts.map