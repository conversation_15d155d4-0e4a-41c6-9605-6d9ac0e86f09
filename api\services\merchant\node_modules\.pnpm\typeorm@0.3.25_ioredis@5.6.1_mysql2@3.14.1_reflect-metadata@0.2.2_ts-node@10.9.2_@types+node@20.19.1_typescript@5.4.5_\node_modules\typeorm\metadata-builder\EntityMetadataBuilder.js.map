{"version": 3, "sources": ["../../src/metadata-builder/EntityMetadataBuilder.ts"], "names": [], "mappings": ";;;AAAA,+DAA2D;AAC3D,+DAA2D;AAC3D,6DAAyD;AACzD,mEAA+D;AAC/D,mEAA+D;AAG/D,uEAAmE;AACnE,6EAAyE;AACzE,6EAAyE;AACzE,mDAA+C;AAE/C,mFAA+E;AAC/E,iGAA6F;AAC7F,2EAAuE;AAEvE,+EAA2E;AAC3E,+DAA2D;AAC3D,6DAAyD;AACzD,qEAAiE;AACjE,oCAAuC;AACvC,uDAAmD;AACnD,uEAAmE;AACnE,6DAAyD;AAEzD;;GAEG;AACH,MAAa,qBAAqB;IAoB9B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACY,UAAsB,EACtB,mBAAwC;QADxC,eAAU,GAAV,UAAU,CAAY;QACtB,wBAAmB,GAAnB,mBAAmB,CAAqB;QAEhD,IAAI,CAAC,6BAA6B,GAAG,IAAI,6DAA6B,CAClE,UAAU,CACb,CAAA;QACD,IAAI,CAAC,oCAAoC;YACrC,IAAI,2EAAoC,CAAC,UAAU,CAAC,CAAA;QACxD,IAAI,CAAC,yBAAyB,GAAG,IAAI,qDAAyB,CAC1D,UAAU,CACb,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,aAA0B;QAC5B,yFAAyF;QACzF,MAAM,SAAS,GAAG,aAAa;YAC3B,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,aAAa,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAA;QAErC,kGAAkG;QAClG,MAAM,UAAU,GAAG,SAAS,CAAC,MAAM,CAC/B,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,SAAS;YACxB,KAAK,CAAC,IAAI,KAAK,SAAS;YACxB,KAAK,CAAC,IAAI,KAAK,cAAc;YAC7B,KAAK,CAAC,IAAI,KAAK,MAAM,CAC5B,CAAA;QAED,oHAAoH;QACpH,MAAM,eAAe,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CACjD,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,CACvC,CAAA;QAED,wDAAwD;QACxD,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACvC,IAAI,CAAC,2BAA2B,CAAC,eAAe,EAAE,cAAc,CAAC,CACpE,CAAA;QAED,kFAAkF;QAClF,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YACjC,QAAQ,CAAC,oBAAoB,GAAG,eAAe,CAAC,MAAM,CAClD,CAAC,aAAa,EAAE,EAAE;gBACd,OAAO,CACH,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU;oBACrC,OAAO,aAAa,CAAC,MAAM,KAAK,UAAU;oBAC1C,6BAAa,CAAC,WAAW,CACrB,aAAa,CAAC,MAAM,EACpB,QAAQ,CAAC,MAAM,CAClB,CACJ,CAAA;YACL,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,mGAAmG;QACnG,eAAe;aACV,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAClE;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAA;QAExD,6FAA6F;QAC7F,eAAe;aACV,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAClE;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAA;QAExD,sHAAsH;QACtH,eAAe;aACV,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAClE;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACxB,IAAI,CAAC,0BAA0B,CAC3B,eAAe,EACf,cAAc,CACjB,CACJ,CAAA;QAEL,wGAAwG;QACxG,eAAe;aACV,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAClE;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACxB,IAAI,CAAC,0BAA0B,CAC3B,eAAe,EACf,cAAc,CACjB,CACJ,CAAA;QAEL,0EAA0E;QAC1E,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACvC,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAClD,CAAA;QAED,iDAAiD;QACjD,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACvC,IAAI,CAAC,wBAAwB,CAAC,cAAc,EAAE,eAAe,CAAC,CACjE,CAAA;QAED,0GAA0G;QAC1G,eAAe;aACV,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CAAC,cAAc,CAAC,SAAS,KAAK,cAAc,CAClE;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACxB,gFAAgF;YAChF,cAAc,CAAC,SAAS;iBACnB,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,WAAW,CAClD;iBACA,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAClB,MAAM,WAAW,GACb,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,CACtC,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,YAAY,CACxB,CAAA;gBACL,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAC3C,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAChC,WAAW,EACX,QAAQ,CACX,CAAA,CAAC,kDAAkD;gBACxD,IAAI,UAAU,EAAE,CAAC;oBACb,QAAQ,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA,CAAC,gEAAgE;oBACzG,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;gBAC/C,CAAC;gBACD,IAAI,OAAO,EAAE,CAAC;oBACV,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAA;gBACzC,CAAC;gBACD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IACI,yBAAW,CAAC,aAAa,CACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;wBACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC/B,cAAc;wBAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC/B,OAAO;wBACX,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;wBAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BAC/B,SAAS,EACf,CAAC;wBACC,MAAM,KAAK,GAAG,IAAI,6BAAa,CAAC;4BAC5B,cAAc,EACV,gBAAgB,CAAC,cAAc;4BACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;4BACjC,IAAI,EAAE;gCACF,MAAM,EAAE,gBAAgB,CAAC,MAAO;gCAChC,IAAI,EAAE,gBAAgB,CAAC,IAAI;gCAC3B,MAAM,EAAE,IAAI;gCACZ,WAAW,EAAE,IAAI;6BACpB;yBACJ,CAAC,CAAA;wBAEF,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BACnC,OAAO,EACT,CAAC;4BACC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO;iCACtB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gCACZ,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CACnC,MAAM,CAAC,YAAY,CACtB,cAAc,CAAA;4BACnB,CAAC,CAAC;iCACD,IAAI,CAAC,OAAO,CAAC,CAAA;wBACtB,CAAC;wBAED,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;4BACnC,SAAS,EACX,CAAC;4BACC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAA;wBAC/B,CAAC;wBAED,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAClC,KAAK,CACR,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CACnC,KAAK,CACR,CAAA;wBACL,CAAC;wBACD,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAA;oBACnD,CAAC;yBAAM,CAAC;wBACJ,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;4BAC5B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAClC,gBAAgB,CACnB,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CACnC,gBAAgB,CACnB,CAAA;wBACL,CAAC;wBACD,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAA;oBACnD,CAAC;gBACL,CAAC;gBAED,IACI,UAAU;oBACV,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;wBAC/B,aAAa,EACnB,CAAC;oBACC,MAAM,KAAK,GAAG,IAAI,6BAAa,CAAC;wBAC5B,cAAc,EAAE,QAAQ,CAAC,cAAc;wBACvC,OAAO,EAAE,UAAU,CAAC,OAAO;wBAC3B,IAAI,EAAE;4BACF,MAAM,EAAE,QAAQ,CAAC,cAAc,CAAC,MAAO;4BACvC,WAAW,EAAE,IAAI;yBACpB;qBACJ,CAAC,CAAA;oBACF,IAAI,QAAQ,CAAC,gBAAgB,EAAE,CAAC;wBAC5B,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBACjD,CAAC;yBAAM,CAAC;wBACJ,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;oBAClD,CAAC;oBACD,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAA;gBACnD,CAAC;YACL,CAAC,CAAC,CAAA;YAEN,qEAAqE;YACrE,cAAc,CAAC,SAAS;iBACnB,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC;iBAC3C,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAClB,MAAM,SAAS,GACX,IAAI,CAAC,mBAAmB,CAAC,aAAa,CAClC,QAAQ,CAAC,MAAM,EACf,QAAQ,CAAC,YAAY,CACvB,CAAA;gBACN,IAAI,CAAC,SAAS;oBAAE,OAAM,CAAC,0FAA0F;gBAEjH,8FAA8F;gBAC9F,MAAM,sBAAsB,GACxB,IAAI,CAAC,6BAA6B,CAAC,KAAK,CACpC,QAAQ,EACR,SAAS,CACZ,CAAA;gBACL,QAAQ,CAAC,mBAAmB,CACxB,GAAG,sBAAsB,CAAC,WAAW,CACxC,CAAA;gBACD,QAAQ,CAAC,mBAAmB,CACxB,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,EAC5C,sBAAsB,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAC/C,CAAA;gBACD,QAAQ,CAAC,8BAA8B,CACnC,sBAAsB,CACzB,CAAA;gBAED,8EAA8E;gBAC9E,IAAI,CAAC,0BAA0B,CAAC,sBAAsB,CAAC,CAAA;gBACvD,IAAI,CAAC,wBAAwB,CACzB,sBAAsB,EACtB,eAAe,CAClB,CAAA;gBACD,eAAe,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAA;YAChD,CAAC,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QAEN,2CAA2C;QAC3C,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,wBAAwB;gBACnC,cAAc,CAAC,SAAS,CAAC,MAAM,CAC3B,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,gBAAgB,CAC1C,CAAA;YACL,cAAc,CAAC,uBAAuB;gBAClC,cAAc,CAAC,wBAAwB,CAAC,IAAI,CACxC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,CAC3D,CAAA;QACT,CAAC,CAAC,CAAA;QAEF,0DAA0D;QAC1D,eAAe;aACV,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,KAAK,eAAe,CAAC;aAC3D,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACxB,MAAM,6BAA6B,GAC/B,IAAI,CAAC,oCAAoC,CAAC,KAAK,CAC3C,cAAc,CACjB,CAAA;YACL,cAAc,CAAC,oBAAoB;gBAC/B,6BAA6B,CAAA;YACjC,IAAI,CAAC,0BAA0B,CAAC,6BAA6B,CAAC,CAAA;YAC9D,IAAI,CAAC,wBAAwB,CACzB,6BAA6B,EAC7B,eAAe,CAClB,CAAA;YACD,eAAe,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAA;QACvD,CAAC,CAAC,CAAA;QAEN,yDAAyD;QACzD,eAAe;aACV,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,kBAAkB,KAAK,KAAK;YACrC,QAAQ,CAAC,mBAAmB,CACnC;aACA,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACxB,IAAI,CAAC,6BAA6B,CAAC,cAAc,CAAC,CACrD,CAAA;QAEL,qFAAqF;QACrF,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CACrC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAC9C,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,gGAAgG;QAChG,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CACtC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAC/C,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,8BAA8B;QAC9B,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CACpC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAC9C,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,kCAAkC;QAClC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE,CAC5C,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAClD,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,mCAAmC;QACnC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACvC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,eAAe,CAAC,CAC1D,CAAA;QAED,4CAA4C;QAC5C,eAAe;aACV,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,UAAU,CAAC;aAC3D,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACxB,cAAc,CAAC,SAAS;iBACnB,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;iBACrC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAClB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACP,cAAc,CAAC,MAAmB,CAAC,SAAS,CAChD,CAAA;YACL,CAAC,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QAEN,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACtC,yFAAyF;gBACzF,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,aAAa,CACpD,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,YAAY,CACtB,CAAA;gBACD,IAAI,SAAS,EAAE,CAAC;oBACZ,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;oBACzB,MAAM,CAAC,kBAAkB,GAAG,SAAS,CAAC,QAAQ,CAAA;oBAC9C,IAAI,SAAS,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAA;oBACxB,CAAC;yBAAM,IAAI,SAAS,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;wBACxC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAA;oBACvB,CAAC;yBAAM,CAAC;wBACJ,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,IAAI,MAAM,CAAA;oBACvC,CAAC;oBACD,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;oBAC7B,IAAI,CAAC,0BAA0B,CAAC,cAAc,CAAC,CAAA;gBACnD,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QAEF,OAAO,eAAe,CAAA;IAC1B,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;;OAGG;IACO,oBAAoB,CAC1B,SAA4B;QAE5B,0FAA0F;QAC1F,8FAA8F;QAC9F,2GAA2G;QAC3G,MAAM,eAAe,GACjB,OAAO,SAAS,CAAC,MAAM,KAAK,UAAU;YAClC,CAAC,CAAC,6BAAa,CAAC,kBAAkB,CAAC,SAAS,CAAC,MAAM,CAAC;YACpD,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA,CAAC,4DAA4D;QAEzF,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CACjE,SAAS,CAAC,MAAM,CACnB,CAAA;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAErE,4FAA4F;QAC5F,IAAI,0BAAiC,CAAA;QACrC,IACI,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,OAAO,KAAK,KAAK,CAAC;YACxD,SAAS,CAAC,IAAI,KAAK,cAAc,EACnC,CAAC;YACC,0BAA0B,GAAG,IAAI,CAAC,mBAAmB;iBAChD,yBAAyB,CAAC,SAAS,CAAC,MAAM,CAAC;iBAC3C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC1B,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,OAAO,MAAM,KAAK,UAAU,CAAC,CAAA;YAErD,eAAe,CAAC,IAAI,CAAC,GAAG,0BAA0B,CAAC,CAAA;QACvD,CAAC;QAED,OAAO,IAAI,+BAAc,CAAC;YACtB,UAAU,EAAE,IAAI,CAAC,UAAU;YAC3B,IAAI,EAAE,SAAS;YACf,eAAe,EAAE,eAAe;YAChC,SAAS,EAAE,SAAS;YACpB,kBAAkB,EAAE,gBAAgB;gBAChC,CAAC,CAAC,gBAAgB,CAAC,OAAO;gBAC1B,CAAC,CAAC,SAAS;SAClB,CAAC,CAAA;IACN,CAAC;IAES,2BAA2B,CACjC,kBAAoC,EACpC,cAA8B;QAE9B,kFAAkF;QAClF,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc,EAAE,CAAC;YAC9C,cAAc,CAAC,oBAAoB,GAAG,kBAAkB,CAAC,IAAI,CACzD,CAAC,iBAAiB,EAAE,EAAE;gBAClB,OAAO,CACH,iBAAiB,CAAC,eAAe,CAAC,OAAO,CACrC,cAAc,CAAC,MAAkB,CACpC,KAAK,CAAC,CAAC;oBACR,iBAAiB,CAAC,kBAAkB,KAAK,KAAK,CACjD,CAAA;YACL,CAAC,CACH,CAAA;QACN,CAAC;IACL,CAAC;IAES,0BAA0B,CAChC,kBAAoC,EACpC,cAA8B;QAE9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAClE,cAAc,CAAC,MAAM,CACxB,CAAA;QAED,MAAM,kBAAkB,GACpB,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,CAC3C,cAAc,CAAC,MAAM,CACxB,CAAA;QAEL,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE,CAAC;YAC5C,cAAc,CAAC,kBAAkB,GAAG,kBAAkB,CAAC,KAAK,CAAA;QAChE,CAAC;aAAM,CAAC;YACJ,cAAc,CAAC,kBAAkB,GAC7B,cAAc,CAAC,MAClB,CAAC,IAAI,CAAA;QACV,CAAC;QAED,wFAAwF;QACxF,cAAc,CAAC,SAAS,GAAG,IAAI,CAAC,0BAA0B,CACtD,cAAc,EACd,IAAI,CAAC,mBAAmB,CAAC,eAAe,CACpC,cAAc,CAAC,eAAe,CACjC,CACJ,CAAC,GAAG,CAAC,CAAC,QAA0B,EAAoB,EAAE;YACnD,IAAI,cAAc,CAAC,kBAAkB,KAAK,KAAK,EAAE,CAAC;gBAC9C,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CACnC,CAAC,MAAsB,EAAkB,EAAE;oBACvC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAA;oBACxB,OAAO,MAAM,CAAA;gBACjB,CAAC,CACJ,CAAA;YACL,CAAC;YACD,OAAO,QAAQ,CAAA;QACnB,CAAC,CAAC,CAAA;QAEF,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB;aAC/C,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;aAC7C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,uEAAuE;YACvE,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc;gBAC3C,OAAO,cAAc,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CACtD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CACvD,CAAA;YAEN,uEAAuE;YACvE,IACI,cAAc,CAAC,SAAS,KAAK,SAAS;gBACtC,IAAI,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,EACvC,CAAC;gBACC,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,IAAI,CACnD,CAAC,CAAC,EAAE,EAAE,CACF,CAAC,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY;oBACpC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM,CACzC,CAAA;gBACD,IAAI,SAAS,IAAI,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;oBACzC,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAA;gBACpD,CAAC;YACL,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,+BAAc,CAAC;gBAC9B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc;gBACd,IAAI;aACP,CAAC,CAAA;YAEF,0FAA0F;YAC1F,MAAM,iCAAiC,GACnC,kBAAkB,CAAC,IAAI,CACnB,CAAC,mBAAmB,EAAE,EAAE,CACpB,mBAAmB,CAAC,SAAS,KAAK,cAAc;gBAChD,mBAAmB,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CACjD,CAAA;YACL,IAAI,iCAAiC;gBAAE,MAAM,CAAC,UAAU,GAAG,IAAI,CAAA;YAC/D,OAAO,MAAM,CAAA;QACjB,CAAC,CAAC,CAAA;QAEN,8DAA8D;QAC9D,EAAE;QACF,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,uBAAuB,GACzB,iBAAiB,CAAC,MAAM,IAAI,iBAAiB,CAAC,MAAM,CAAC,IAAI;gBACrD,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,IAAI;gBAC/B,CAAC,CAAC,MAAM,CAAA;YAChB,IAAI,mBAAmB,GAAG,cAAc,CAAC,UAAU,CAAC,IAAI,CACpD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,uBAAuB,CAC9D,CAAA;YACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,mBAAmB,GAAG,IAAI,+BAAc,CAAC;oBACrC,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,cAAc,EAAE,cAAc;oBAC9B,IAAI,EAAE;wBACF,MAAM,EAAE,cAAc,CAAC,MAAM;wBAC7B,IAAI,EAAE,SAAS;wBACf,YAAY,EAAE,uBAAuB;wBACrC,OAAO,EAAE,iBAAiB,CAAC,MAAM,IAAI;4BACjC,IAAI,EAAE,uBAAuB;4BAC7B,IAAI,EAAE,SAAS;4BACf,QAAQ,EAAE,KAAK;yBAClB;qBACJ;iBACJ,CAAC,CAAA;gBACF,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAA;gBACpC,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAA;gBAC1C,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;YACvD,CAAC;iBAAM,CAAC;gBACJ,mBAAmB,CAAC,eAAe,GAAG,IAAI,CAAA;YAC9C,CAAC;QACL,CAAC;QAED,yDAAyD;QACzD,kGAAkG;QAClG,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc,EAAE,CAAC;YAC9C,MAAM,mBAAmB,GACrB,cAAc,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAC/C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,eAAe,CACrC,CAAA;YACL,IACI,mBAAmB;gBACnB,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAC3B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,mBAAmB,CAC7C,EACH,CAAC;gBACC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAA;YACvD,CAAC;YACD,oDAAoD;YACpD,mEAAmE;YACnE,cAAc,CAAC,kBAAkB;gBAC7B,cAAc,CAAC,oBAAoB,CAAC,kBAAkB,CAAA;YAC1D,IACI,CAAC,cAAc,CAAC,QAAQ;gBACxB,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAAC,QAAQ,EAChD,CAAC;gBACC,cAAc,CAAC,QAAQ;oBACnB,cAAc,CAAC,oBAAoB,CAAC,QAAQ,CAAA;gBAChD,cAAc,CAAC,WAAW;oBACtB,cAAc,CAAC,oBAAoB,CAAC,WAAW,CAAA;gBACnD,cAAc,CAAC,kBAAkB;oBAC7B,cAAc,CAAC,oBAAoB,CAAC,kBAAkB,CAAA;gBAC1D,cAAc,CAAC,eAAe;oBAC1B,cAAc,CAAC,oBAAoB,CAAC,eAAe,CAAA;YAC3D,CAAC;QACL,CAAC;QAED,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,UAAU,CAAA;QAE1C,kFAAkF;QAClF,IAAI,cAAc,CAAC,QAAQ,KAAK,mBAAmB,EAAE,CAAC;YAClD,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,+BAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,gBAAgB,EAAE,IAAI;gBACtB,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,OAAO;oBACrB,OAAO,EAAE,mBAAmB,CAAC;wBACzB,IAAI,EAAE,cAAc,CAAC,0BAA0B;wBAC/C,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,IAAI;wBACd,OAAO,EAAE,EAAE;qBACd;iBACJ;aACJ,CAAC,CACL,CAAA;QACL,CAAC;aAAM,IAAI,cAAc,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC,oBAAoB,CAAA;YAC3D,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,+BAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,aAAa,EAAE,IAAI;gBACnB,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,IAAI;oBAClB,OAAO,EAAE,mBAAmB,CAAC;wBACzB,IAAI,EAAE,IAAI;wBACV,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,CAAC;qBACb;iBACJ;aACJ,CAAC,CACL,CAAA;YACD,cAAc,CAAC,UAAU,CAAC,IAAI,CAC1B,IAAI,+BAAc,CAAC;gBACf,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,cAAc;gBAC9B,cAAc,EAAE,IAAI;gBACpB,IAAI,EAAE;oBACF,MAAM,EAAE,cAAc,CAAC,MAAM;oBAC7B,IAAI,EAAE,SAAS;oBACf,YAAY,EAAE,KAAK;oBACnB,OAAO,EAAE,mBAAmB,CAAC;wBACzB,IAAI,EAAE,KAAK;wBACX,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,KAAK;wBACf,OAAO,EAAE,CAAC;qBACb;iBACJ;aACJ,CAAC,CACL,CAAA;QACL,CAAC;QAED,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB;aACjD,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC;aAC/C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,yEAAyE;YACzE,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAChB,cAAc,CAAC,oBAAoB,CAAC,YAAY,CAAC,IAAI,CACjD,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CACjD,CAAA;gBACN,MAAM,IAAI,GACN,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU;oBAC3B,CAAC,CAAE,IAAI,CAAC,IAAkB,EAAE;oBAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;gBACnB,IAAI,cAAc,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;oBAC/B,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAA;oBAC3C,KAAK,CAAC,IAAI,GAAG,IAAI,CAAA;oBACjB,OAAO,KAAK,CAAA;gBAChB,CAAC;gBAED,OAAO,cAAc,CAAA;YACzB,CAAC;YAED,OAAO,IAAI,mCAAgB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;QACzD,CAAC,CAAC,CAAA;QACN,cAAc,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB;aAChD,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;aACjD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,4EAA4E;YAC5E,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc;gBAC3C,OAAO,cAAc,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CACvD,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CACnD,CAAA;YAEN,OAAO,IAAI,uCAAkB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;QAC3D,CAAC,CAAC,CAAA;QACN,cAAc,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB;aACnD,oBAAoB,CAAC,cAAc,CAAC,eAAe,CAAC;aACpD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,+EAA+E;YAC/E,IAAI,cAAc,CAAC,SAAS,KAAK,cAAc;gBAC3C,OAAO,cAAc,CAAC,oBAAoB,CAAC,cAAc,CAAC,IAAI,CAC1D,CAAC,aAAa,EAAE,EAAE,CACd,aAAa,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CACtD,CAAA;YAEN,OAAO,IAAI,6CAAqB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;QAC9D,CAAC,CAAC,CAAA;QACN,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB;aACjD,eAAe,CAAC,cAAc,CAAC,eAAe,CAAC;aAC/C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,OAAO,IAAI,+CAAsB,CAAC;gBAC9B,cAAc,EAAE,cAAc;gBAC9B,IAAI,EAAE,IAAI;aACb,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;QACN,cAAc,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB;aAC3C,YAAY,CAAC,cAAc,CAAC,eAAe,CAAC;aAC5C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,OAAO,IAAI,6BAAa,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QAEN,kDAAkD;QAClD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACrD,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB;iBAC/C,gBAAgB,CAAC,cAAc,CAAC,eAAe,CAAC;iBAChD,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,qCAAiB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YAC1D,CAAC,CAAC,CAAA;QACV,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACxD,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB;iBAC/C,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;iBAC7C,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC9B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,6BAAa,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YACtD,CAAC,CAAC,CAAA;YAEN,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBACnC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;iBAC7C,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC;iBAC7B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,+BAAc,CAAC;oBACtB,cAAc,EAAE,cAAc;oBAC9B,IAAI,EAAE;wBACF,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,OAAO,EAAE,IAAI,CAAC,OAAO;qBACxB;iBACJ,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAA;QAC9C,CAAC;aAAM,CAAC;YACJ,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC,mBAAmB;iBAC/C,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;iBAC7C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,6BAAa,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YACtD,CAAC,CAAC,CAAA;QACV,CAAC;QAED,4DAA4D;QAC5D,IACI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;YACtD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;YAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;YACC,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBACnC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;iBAC7C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,6BAAa,CAAC;oBACrB,cAAc,EAAE,cAAc;oBAC9B,IAAI,EAAE;wBACF,MAAM,EAAE,IAAI,CAAC,MAAM;wBACnB,IAAI,EAAE,IAAI,CAAC,IAAI;wBACf,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,MAAM,EAAE,IAAI;wBACZ,WAAW,EAAE,IAAI;qBACpB;iBACJ,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAA;QAC9C,CAAC;aAAM,CAAC;YACJ,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBACnC,aAAa,CAAC,cAAc,CAAC,eAAe,CAAC;iBAC7C,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,+BAAc,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;YACN,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;;OAGG;IACO,0BAA0B,CAChC,cAA8B,EAC9B,YAAoC;QAEpC,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;YACrC,MAAM,gBAAgB,GAAG,IAAI,mCAAgB,CAAC;gBAC1C,cAAc,EAAE,cAAc;gBAC9B,IAAI,EAAE,YAAY;aACrB,CAAC,CAAA;YACF,MAAM,OAAO,GACT,OAAO,gBAAgB,CAAC,IAAI,KAAK,UAAU;gBACvC,CAAC,CAAC,6BAAa,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,IAAI,CAAC;gBACzD,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA,CAAC,4DAA4D;YAE9F,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBAC9C,aAAa,CAAC,OAAO,CAAC;iBACtB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,+BAAc,CAAC;oBACtB,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,cAAc;oBACd,gBAAgB;oBAChB,IAAI;iBACP,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB;iBAChD,eAAe,CAAC,OAAO,CAAC;iBACxB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,mCAAgB,CAAC;oBACxB,cAAc;oBACd,gBAAgB;oBAChB,IAAI;iBACP,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,mBAAmB;iBAChD,eAAe,CAAC,OAAO,CAAC;iBACxB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,+CAAsB,CAAC;oBAC9B,cAAc;oBACd,gBAAgB;oBAChB,IAAI;iBACP,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBAC9C,aAAa,CAAC,OAAO,CAAC;iBACtB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,6BAAa,CAAC;oBACrB,cAAc;oBACd,gBAAgB;oBAChB,IAAI;iBACP,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,mBAAmB;iBAC9C,aAAa,CAAC,OAAO,CAAC;iBACtB,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,+BAAc,CAAC;oBACtB,cAAc;oBACd,gBAAgB;oBAChB,IAAI;iBACP,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,mBAAmB;iBAClD,iBAAiB,CAAC,OAAO,CAAC;iBAC1B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,uCAAkB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB;iBACrD,oBAAoB,CAAC,OAAO,CAAC;iBAC7B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,6CAAqB,CAAC,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAA;YAC9D,CAAC,CAAC,CAAA;YACN,gBAAgB,CAAC,SAAS,GAAG,IAAI,CAAC,0BAA0B,CACxD,cAAc,EACd,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,OAAO,CAAC,CACpD,CAAA;YACD,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAC9B,CAAC,WAAW,EAAE,EAAE,CACZ,CAAC,WAAW,CAAC,sBAAsB,GAAG,gBAAgB,CAAC,CAC9D,CAAA;YACD,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAClD,OAAO,gBAAgB,CAAA;QAC3B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,0BAA0B,CAAC,cAA8B;QAC/D,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC1C,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAClC,CAAA;QACD,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CACxC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAChC,CAAA;YACD,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;QACtE,CAAC,CAAC,CAAA;QACF,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CACzC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAChC,CAAA;QACD,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAA;QACnE,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACtD,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAChD,cAAc,CAAC,YAAY,CAC9B,CAAA;QACD,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC3D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,OAAO,CACjC,CAAA;QACD,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC1D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAChC,CAAA;QACD,cAAc,CAAC,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC9D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,UAAU,CACpC,CAAA;QACD,cAAc,CAAC,kBAAkB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC/D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,WAAW,CACrC,CAAA;QACD,cAAc,CAAC,kBAAkB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC/D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,WAAW,CACrC,CAAA;QACD,cAAc,CAAC,mBAAmB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAChE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CACtC,CAAA;QACD,cAAc,CAAC,sBAAsB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACnE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,eAAe,CACzC,CAAA;QACD,cAAc,CAAC,wBAAwB;YACnC,cAAc,CAAC,SAAS,CAAC,MAAM,CAC3B,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,iBAAiB,CAC3C,CAAA;QACL,cAAc,CAAC,kBAAkB,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAC7D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,YAAY,CACtC,CAAA;QACD,cAAc,CAAC,oBAAoB,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAC/D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,cAAc,CACxC,CAAA;QACD,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACpD,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAC/D,cAAc,CAAC,UAAU,CAC5B,CAAA;QACD,cAAc,CAAC,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACtD,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAChD,cAAc,CAAC,YAAY,CAC9B,CAAA;QACD,cAAc,CAAC,kBAAkB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAC/D,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,UAAU,CAChE,CAAA;QACD,cAAc,CAAC,oBAAoB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACjE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,YAAY,CAClE,CAAA;QACD,cAAc,CAAC,oBAAoB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACjE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,YAAY,CAClE,CAAA;QACD,cAAc,CAAC,oBAAoB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACjE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,YAAY,CAClE,CAAA;QACD,cAAc,CAAC,wBAAwB;YACnC,cAAc,CAAC,SAAS,CAAC,MAAM,CAC3B,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,iBAAiB,CAC7D,CAAA;QACL,cAAc,CAAC,qBAAqB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAClE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,aAAa,CACnE,CAAA;QACD,cAAc,CAAC,qBAAqB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAClE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,aAAa,CACnE,CAAA;QACD,cAAc,CAAC,qBAAqB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAClE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,aAAa,CACnE,CAAA;QACD,cAAc,CAAC,qBAAqB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CAClE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,aAAa,CACnE,CAAA;QACD,cAAc,CAAC,yBAAyB;YACpC,cAAc,CAAC,SAAS,CAAC,MAAM,CAC3B,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,kBAAkB,CAC9D,CAAA;QACL,cAAc,CAAC,sBAAsB,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACnE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,IAAI,KAAK,uCAAkB,CAAC,cAAc,CACpE,CAAA;QACD,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACpD,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAC/D,cAAc,CAAC,UAAU,CAC5B,CAAA;QACD,cAAc,CAAC,OAAO,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,CACpD,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,EAC/D,cAAc,CAAC,UAAU,CAC5B,CAAA;QACD,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CACzD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,cAAc,CAAC,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAC5D,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAChC,CAAA;QACD,cAAc,CAAC,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAC1D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,KAAK,UAAU,CAChD,CAAA;QACD,cAAc,CAAC,iBAAiB,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAC5D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,KAAK,YAAY,CAClD,CAAA;QACD,cAAc,CAAC,sBAAsB;YACjC,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAA;QAC5C,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAC3D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,UAAU,CACtD,CAAA;QACD,cAAc,CAAC,uBAAuB;YAClC,cAAc,CAAC,OAAO,CAAC,MAAM,CACzB,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM,CACjE,CAAC,MAAM,GAAG,CAAC,CAAA;QAChB,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACzD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC,CAAA;QACD,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACzD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC,CAAA;QACD,cAAc,CAAC,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACzD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC,CAAA;QACD,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACtD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,cAAc,CAAC,mBAAmB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC5D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,eAAe,CACrC,CAAA;QACD,cAAc,CAAC,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACxD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,WAAW,CACjC,CAAA;QACD,cAAc,CAAC,mBAAmB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC5D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,eAAe,CACrC,CAAA;QACD,cAAc,CAAC,oBAAoB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC7D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,gBAAgB,CACtC,CAAA;QACD,cAAc,CAAC,sBAAsB,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC/D,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,kBAAkB,CACxC,CAAA;QACD,cAAc,CAAC,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACvD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAChC,CAAA;QACD,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAC9C,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CACnD,CAAA;QACD,cAAc,CAAC,aAAa,GAAG,cAAc,CAAC,mBAAmB,EAAE,CAAA;QACnE,cAAc,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAA;QACtE,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE,CACpD,aAAa,CAAC,KAAK,EAAE,CACxB,CAAA;QACD,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,QAAQ,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CAChD,UAAU,CAAC,KAAK,EAAE,CACrB,CAAA;YACD,QAAQ,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE,CACtD,aAAa,CAAC,KAAK,EAAE,CACxB,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,cAA8B,EAC9B,eAAiC;QAEjC,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,6EAA6E;YAC7E,MAAM,qBAAqB,GAAG,eAAe,CAAC,IAAI,CAC9C,CAAC,CAAC,EAAE,EAAE,CACF,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,IAAI;gBAC1B,CAAC,OAAO,QAAQ,CAAC,IAAI,KAAK,QAAQ;oBAC9B,CAAC,CAAC,CAAC,UAAU,KAAK,QAAQ,CAAC,IAAI;wBAC3B,CAAC,CAAC,cAAc,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CACnD,CAAA;YACD,IAAI,CAAC,qBAAqB;gBACtB,MAAM,IAAI,oBAAY,CAClB,sBAAsB;oBAClB,cAAc,CAAC,IAAI;oBACnB,GAAG;oBACH,QAAQ,CAAC,YAAY;oBACrB,iHAAiH,CACxH,CAAA;YAEL,QAAQ,CAAC,qBAAqB,GAAG,qBAAqB,CAAA;YACtD,QAAQ,CAAC,uBAAuB;gBAC5B,QAAQ,CAAC,4BAA4B,EAAE,CAAA;YAE3C,uDAAuD;YACvD,QAAQ,CAAC,eAAe,GAAG,qBAAqB,CAAC,SAAS,CAAC,IAAI,CAC3D,CAAC,aAAa,EAAE,EAAE,CACd,aAAa,CAAC,YAAY;gBAC1B,QAAQ,CAAC,uBAAuB,CACvC,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,6BAA6B,CAAC,cAA8B;QAClE,MAAM,mCAAmC,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACnE,CAAC,EAAE,gBAAgB,EAAE,EAAE,EAAE,CACrB,CAAC,CAAC,gBAAgB;YAClB,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAC/B,gBAAgB,CAAC,MAAM,KAAK,CAAC;YAC7B,gBAAgB,CAAC,CAAC,CAAC;gBACf,cAAc,CAAC,mBAAmB,EAAE,YAAY,CAC3D,CAAA;QAED,sEAAsE;QACtE,kCAAkC;QAClC,IAAI,mCAAmC,EAAE,CAAC;YACtC,OAAM;QACV,CAAC;QAED,cAAc,CAAC,OAAO,CAAC,IAAI,CACvB,IAAI,6BAAa,CAAC;YACd,cAAc,EAAE,cAAc;YAC9B,OAAO,EAAE,CAAC,cAAc,CAAC,mBAAoB,CAAC;YAC9C,IAAI,EAAE;gBACF,MAAM,EAAE,cAAc,CAAC,MAAM;gBAC7B,MAAM,EAAE,KAAK;aAChB;SACJ,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,cAA8B,EAC9B,eAAiC;QAEjC,IAAI,CAAC,mBAAmB;aACnB,iBAAiB,CAAC,cAAc,CAAC,eAAe,CAAC;aACjD,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACxB,MAAM,cAAc,GAChB,OAAO,cAAc,CAAC,IAAI,KAAK,UAAU;gBACrC,CAAC,CAAE,cAAc,CAAC,IAAkB,EAAE;gBACtC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAA;YAE7B,MAAM,wBAAwB,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CACxD,OAAO,cAAc,KAAK,QAAQ;gBAC9B,CAAC,CAAC,CAAC,CAAC,UAAU,KAAK,cAAc;oBAC/B,CAAC,CAAC,cAAc,KAAK,cAAc;gBACrC,CAAC,CAAC,iCAAe,CAAC,cAAc,CAAC,cAAc,CAAC;oBAChD,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO,CAAC,IAAI;wBACxC,CAAC,CAAC,MAAM,KAAK,cAAc,CAAC,OAAO,CAAC,MAAM;oBAC5C,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,cAAc,CACpC,CAAA;YAED,IAAI,CAAC,wBAAwB,EAAE,CAAC;gBAC5B,MAAM,IAAI,oBAAY,CAClB,sBAAsB;oBAClB,cAAc,CAAC,IAAI;oBACnB,CAAC,cAAc,CAAC,YAAY;wBACxB,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,YAAY;wBACnC,CAAC,CAAC,EAAE,CAAC;oBACT,iHAAiH,CACxH,CAAA;YACL,CAAC;YAED,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,IAAI,EAAE,CAAA;YACpD,MAAM,qBAAqB,GACvB,cAAc,CAAC,qBAAqB,IAAI,EAAE,CAAA;YAE9C,MAAM,OAAO,GAAqB,EAAE,CAAA;YACpC,MAAM,iBAAiB,GAAqB,EAAE,CAAA;YAE9C,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAA;gBAE7C,IAAI,cAAc,CAAC,WAAW,EAAE,CAAC;oBAC7B,IAAI,OAAO,cAAc,CAAC,WAAW,KAAK,UAAU,EAAE,CAAC;wBACnD,qBAAqB,CAAC,IAAI,CACtB,cAAc,CAAC,WAAW,CACtB,wBAAwB,CAAC,aAAa,CACzC,CACJ,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,qBAAqB,CAAC,IAAI,CACtB,cAAc,CAAC,WAAW,CAC7B,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE,CAAC;gBAChC,iBAAiB,CAAC,IAAI,CAClB,GAAG,wBAAwB,CAAC,cAAc,CAC7C,CAAA;YACL,CAAC;YAED,MAAM,kBAAkB,GAAG,CACvB,UAAkB,EAClB,cAA8B,EAChB,EAAE;gBAChB,MAAM,MAAM,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CACtC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY,KAAK,UAAU;oBAClC,MAAM,CAAC,YAAY,KAAK,UAAU,CACzC,CAAA;gBAED,IAAI,MAAM;oBAAE,OAAO,MAAM,CAAA;gBAEzB,MAAM,cAAc,GAAG,cAAc,CAAC,IAAI;oBACtC,CAAC,CAAC,GAAG,GAAG,cAAc,CAAC,IAAI,GAAG,IAAI;oBAClC,CAAC,CAAC,EAAE,CAAA;gBACR,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,CAAA;gBAC5C,MAAM,IAAI,oBAAY,CAClB,0BAA0B,cAAc,kDAAkD,UAAU,MAAM,UAAU,EAAE,CACzH,CAAA;YACL,CAAC,CAAA;YAED,OAAO,CAAC,IAAI,CACR,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC9B,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC,CACjD,CACJ,CAAA;YAED,iBAAiB,CAAC,IAAI,CAClB,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CACxC,kBAAkB,CACd,UAAU,EACV,wBAAwB,CAC3B,CACJ,CACJ,CAAA;YAED,cAAc,CAAC,WAAW,CAAC,IAAI,CAC3B,IAAI,uCAAkB,CAAC;gBACnB,cAAc;gBACd,wBAAwB;gBACxB,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc;gBAC9C,OAAO;gBACP,iBAAiB;gBACjB,GAAG,cAAc;aACpB,CAAC,CACL,CAAA;QACL,CAAC,CAAC,CAAA;IACV,CAAC;CACJ;AAlvCD,sDAkvCC", "file": "EntityMetadataBuilder.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { IndexMetadata } from \"../metadata/IndexMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { EmbeddedMetadata } from \"../metadata/EmbeddedMetadata\"\nimport { MetadataArgsStorage } from \"../metadata-args/MetadataArgsStorage\"\nimport { EmbeddedMetadataArgs } from \"../metadata-args/EmbeddedMetadataArgs\"\nimport { RelationIdMetadata } from \"../metadata/RelationIdMetadata\"\nimport { RelationCountMetadata } from \"../metadata/RelationCountMetadata\"\nimport { EventListenerTypes } from \"../metadata/types/EventListenerTypes\"\nimport { MetadataUtils } from \"./MetadataUtils\"\nimport { TableMetadataArgs } from \"../metadata-args/TableMetadataArgs\"\nimport { JunctionEntityMetadataBuilder } from \"./JunctionEntityMetadataBuilder\"\nimport { ClosureJunctionEntityMetadataBuilder } from \"./ClosureJunctionEntityMetadataBuilder\"\nimport { RelationJoinColumnBuilder } from \"./RelationJoinColumnBuilder\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { EntityListenerMetadata } from \"../metadata/EntityListenerMetadata\"\nimport { UniqueMetadata } from \"../metadata/UniqueMetadata\"\nimport { CheckMetadata } from \"../metadata/CheckMetadata\"\nimport { ExclusionMetadata } from \"../metadata/ExclusionMetadata\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { ForeignKeyMetadata } from \"../metadata/ForeignKeyMetadata\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Builds EntityMetadata objects and all its sub-metadatas.\n */\nexport class EntityMetadataBuilder {\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Used to build entity metadatas of the junction entities.\n     */\n    protected junctionEntityMetadataBuilder: JunctionEntityMetadataBuilder\n\n    /**\n     * Used to build entity metadatas of the closure junction entities.\n     */\n    protected closureJunctionEntityMetadataBuilder: ClosureJunctionEntityMetadataBuilder\n\n    /**\n     * Used to build join columns of the relations.\n     */\n    protected relationJoinColumnBuilder: RelationJoinColumnBuilder\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        private connection: DataSource,\n        private metadataArgsStorage: MetadataArgsStorage,\n    ) {\n        this.junctionEntityMetadataBuilder = new JunctionEntityMetadataBuilder(\n            connection,\n        )\n        this.closureJunctionEntityMetadataBuilder =\n            new ClosureJunctionEntityMetadataBuilder(connection)\n        this.relationJoinColumnBuilder = new RelationJoinColumnBuilder(\n            connection,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Builds a complete entity metadatas for the given entity classes.\n     */\n    build(entityClasses?: Function[]): EntityMetadata[] {\n        // if entity classes to filter entities by are given then do filtering, otherwise use all\n        const allTables = entityClasses\n            ? this.metadataArgsStorage.filterTables(entityClasses)\n            : this.metadataArgsStorage.tables\n\n        // filter out table metadata args for those we really create entity metadatas and tables in the db\n        const realTables = allTables.filter(\n            (table) =>\n                table.type === \"regular\" ||\n                table.type === \"closure\" ||\n                table.type === \"entity-child\" ||\n                table.type === \"view\",\n        )\n\n        // create entity metadatas for a user defined entities (marked with @Entity decorator or loaded from entity schemas)\n        const entityMetadatas = realTables.map((tableArgs) =>\n            this.createEntityMetadata(tableArgs),\n        )\n\n        // compute parent entity metadatas for table inheritance\n        entityMetadatas.forEach((entityMetadata) =>\n            this.computeParentEntityMetadata(entityMetadatas, entityMetadata),\n        )\n\n        // after all metadatas created we set child entity metadatas for table inheritance\n        entityMetadatas.forEach((metadata) => {\n            metadata.childEntityMetadatas = entityMetadatas.filter(\n                (childMetadata) => {\n                    return (\n                        typeof metadata.target === \"function\" &&\n                        typeof childMetadata.target === \"function\" &&\n                        MetadataUtils.isInherited(\n                            childMetadata.target,\n                            metadata.target,\n                        )\n                    )\n                },\n            )\n        })\n\n        // build entity metadata (step0), first for non-single-table-inherited entity metadatas (dependant)\n        entityMetadatas\n            .filter(\n                (entityMetadata) => entityMetadata.tableType !== \"entity-child\",\n            )\n            .forEach((entityMetadata) => entityMetadata.build())\n\n        // build entity metadata (step0), now for single-table-inherited entity metadatas (dependant)\n        entityMetadatas\n            .filter(\n                (entityMetadata) => entityMetadata.tableType === \"entity-child\",\n            )\n            .forEach((entityMetadata) => entityMetadata.build())\n\n        // compute entity metadata columns, relations, etc. first for the regular, non-single-table-inherited entity metadatas\n        entityMetadatas\n            .filter(\n                (entityMetadata) => entityMetadata.tableType !== \"entity-child\",\n            )\n            .forEach((entityMetadata) =>\n                this.computeEntityMetadataStep1(\n                    entityMetadatas,\n                    entityMetadata,\n                ),\n            )\n\n        // then do it for single table inheritance children (since they are depend on their parents to be built)\n        entityMetadatas\n            .filter(\n                (entityMetadata) => entityMetadata.tableType === \"entity-child\",\n            )\n            .forEach((entityMetadata) =>\n                this.computeEntityMetadataStep1(\n                    entityMetadatas,\n                    entityMetadata,\n                ),\n            )\n\n        // calculate entity metadata computed properties and all its sub-metadatas\n        entityMetadatas.forEach((entityMetadata) =>\n            this.computeEntityMetadataStep2(entityMetadata),\n        )\n\n        // calculate entity metadata's inverse properties\n        entityMetadatas.forEach((entityMetadata) =>\n            this.computeInverseProperties(entityMetadata, entityMetadatas),\n        )\n\n        // go through all entity metadatas and create foreign keys / junction entity metadatas for their relations\n        entityMetadatas\n            .filter(\n                (entityMetadata) => entityMetadata.tableType !== \"entity-child\",\n            )\n            .forEach((entityMetadata) => {\n                // create entity's relations join columns (for many-to-one and one-to-one owner)\n                entityMetadata.relations\n                    .filter(\n                        (relation) =>\n                            relation.isOneToOne || relation.isManyToOne,\n                    )\n                    .forEach((relation) => {\n                        const joinColumns =\n                            this.metadataArgsStorage.filterJoinColumns(\n                                relation.target,\n                                relation.propertyName,\n                            )\n                        const { foreignKey, columns, uniqueConstraint } =\n                            this.relationJoinColumnBuilder.build(\n                                joinColumns,\n                                relation,\n                            ) // create a foreign key based on its metadata args\n                        if (foreignKey) {\n                            relation.registerForeignKeys(foreignKey) // push it to the relation and thus register there a join column\n                            entityMetadata.foreignKeys.push(foreignKey)\n                        }\n                        if (columns) {\n                            relation.registerJoinColumns(columns)\n                        }\n                        if (uniqueConstraint) {\n                            if (\n                                DriverUtils.isMySQLFamily(\n                                    this.connection.driver,\n                                ) ||\n                                this.connection.driver.options.type ===\n                                    \"aurora-mysql\" ||\n                                this.connection.driver.options.type ===\n                                    \"mssql\" ||\n                                this.connection.driver.options.type === \"sap\" ||\n                                this.connection.driver.options.type ===\n                                    \"spanner\"\n                            ) {\n                                const index = new IndexMetadata({\n                                    entityMetadata:\n                                        uniqueConstraint.entityMetadata,\n                                    columns: uniqueConstraint.columns,\n                                    args: {\n                                        target: uniqueConstraint.target!,\n                                        name: uniqueConstraint.name,\n                                        unique: true,\n                                        synchronize: true,\n                                    },\n                                })\n\n                                if (\n                                    this.connection.driver.options.type ===\n                                    \"mssql\"\n                                ) {\n                                    index.where = index.columns\n                                        .map((column) => {\n                                            return `${this.connection.driver.escape(\n                                                column.databaseName,\n                                            )} IS NOT NULL`\n                                        })\n                                        .join(\" AND \")\n                                }\n\n                                if (\n                                    this.connection.driver.options.type ===\n                                    \"spanner\"\n                                ) {\n                                    index.isNullFiltered = true\n                                }\n\n                                if (relation.embeddedMetadata) {\n                                    relation.embeddedMetadata.indices.push(\n                                        index,\n                                    )\n                                } else {\n                                    relation.entityMetadata.ownIndices.push(\n                                        index,\n                                    )\n                                }\n                                this.computeEntityMetadataStep2(entityMetadata)\n                            } else {\n                                if (relation.embeddedMetadata) {\n                                    relation.embeddedMetadata.uniques.push(\n                                        uniqueConstraint,\n                                    )\n                                } else {\n                                    relation.entityMetadata.ownUniques.push(\n                                        uniqueConstraint,\n                                    )\n                                }\n                                this.computeEntityMetadataStep2(entityMetadata)\n                            }\n                        }\n\n                        if (\n                            foreignKey &&\n                            this.connection.driver.options.type ===\n                                \"cockroachdb\"\n                        ) {\n                            const index = new IndexMetadata({\n                                entityMetadata: relation.entityMetadata,\n                                columns: foreignKey.columns,\n                                args: {\n                                    target: relation.entityMetadata.target!,\n                                    synchronize: true,\n                                },\n                            })\n                            if (relation.embeddedMetadata) {\n                                relation.embeddedMetadata.indices.push(index)\n                            } else {\n                                relation.entityMetadata.ownIndices.push(index)\n                            }\n                            this.computeEntityMetadataStep2(entityMetadata)\n                        }\n                    })\n\n                // create junction entity metadatas for entity many-to-many relations\n                entityMetadata.relations\n                    .filter((relation) => relation.isManyToMany)\n                    .forEach((relation) => {\n                        const joinTable =\n                            this.metadataArgsStorage.findJoinTable(\n                                relation.target,\n                                relation.propertyName,\n                            )!\n                        if (!joinTable) return // no join table set - no need to do anything (it means this is many-to-many inverse side)\n\n                        // here we create a junction entity metadata for a new junction table of many-to-many relation\n                        const junctionEntityMetadata =\n                            this.junctionEntityMetadataBuilder.build(\n                                relation,\n                                joinTable,\n                            )\n                        relation.registerForeignKeys(\n                            ...junctionEntityMetadata.foreignKeys,\n                        )\n                        relation.registerJoinColumns(\n                            junctionEntityMetadata.ownIndices[0].columns,\n                            junctionEntityMetadata.ownIndices[1].columns,\n                        )\n                        relation.registerJunctionEntityMetadata(\n                            junctionEntityMetadata,\n                        )\n\n                        // compute new entity metadata properties and push it to entity metadatas pool\n                        this.computeEntityMetadataStep2(junctionEntityMetadata)\n                        this.computeInverseProperties(\n                            junctionEntityMetadata,\n                            entityMetadatas,\n                        )\n                        entityMetadatas.push(junctionEntityMetadata)\n                    })\n            })\n\n        // update entity metadata depend properties\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.relationsWithJoinColumns =\n                entityMetadata.relations.filter(\n                    (relation) => relation.isWithJoinColumn,\n                )\n            entityMetadata.hasNonNullableRelations =\n                entityMetadata.relationsWithJoinColumns.some(\n                    (relation) => !relation.isNullable || relation.isPrimary,\n                )\n        })\n\n        // generate closure junction tables for all closure tables\n        entityMetadatas\n            .filter((metadata) => metadata.treeType === \"closure-table\")\n            .forEach((entityMetadata) => {\n                const closureJunctionEntityMetadata =\n                    this.closureJunctionEntityMetadataBuilder.build(\n                        entityMetadata,\n                    )\n                entityMetadata.closureJunctionTable =\n                    closureJunctionEntityMetadata\n                this.computeEntityMetadataStep2(closureJunctionEntityMetadata)\n                this.computeInverseProperties(\n                    closureJunctionEntityMetadata,\n                    entityMetadatas,\n                )\n                entityMetadatas.push(closureJunctionEntityMetadata)\n            })\n\n        // generate keys for tables with single-table inheritance\n        entityMetadatas\n            .filter(\n                (metadata) =>\n                    metadata.inheritancePattern === \"STI\" &&\n                    metadata.discriminatorColumn,\n            )\n            .forEach((entityMetadata) =>\n                this.createKeysForTableInheritance(entityMetadata),\n            )\n\n        // build all indices (need to do it after relations and their join columns are built)\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.indices.forEach((index) =>\n                index.build(this.connection.namingStrategy),\n            )\n        })\n\n        // build all unique constraints (need to do it after relations and their join columns are built)\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.uniques.forEach((unique) =>\n                unique.build(this.connection.namingStrategy),\n            )\n        })\n\n        // build all check constraints\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.checks.forEach((check) =>\n                check.build(this.connection.namingStrategy),\n            )\n        })\n\n        // build all exclusion constraints\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.exclusions.forEach((exclusion) =>\n                exclusion.build(this.connection.namingStrategy),\n            )\n        })\n\n        // generate foreign keys for tables\n        entityMetadatas.forEach((entityMetadata) =>\n            this.createForeignKeys(entityMetadata, entityMetadatas),\n        )\n\n        // add lazy initializer for entity relations\n        entityMetadatas\n            .filter((metadata) => typeof metadata.target === \"function\")\n            .forEach((entityMetadata) => {\n                entityMetadata.relations\n                    .filter((relation) => relation.isLazy)\n                    .forEach((relation) => {\n                        this.connection.relationLoader.enableLazyLoad(\n                            relation,\n                            (entityMetadata.target as Function).prototype,\n                        )\n                    })\n            })\n\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.columns.forEach((column) => {\n                // const target = column.embeddedMetadata ? column.embeddedMetadata.type : column.target;\n                const generated = this.metadataArgsStorage.findGenerated(\n                    column.target,\n                    column.propertyName,\n                )\n                if (generated) {\n                    column.isGenerated = true\n                    column.generationStrategy = generated.strategy\n                    if (generated.strategy === \"uuid\") {\n                        column.type = \"uuid\"\n                    } else if (generated.strategy === \"rowid\") {\n                        column.type = \"int\"\n                    } else {\n                        column.type = column.type || Number\n                    }\n                    column.build(this.connection)\n                    this.computeEntityMetadataStep2(entityMetadata)\n                }\n            })\n        })\n\n        return entityMetadatas\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates entity metadata from the given table args.\n     * Creates column, relation, etc. metadatas for everything this entity metadata owns.\n     */\n    protected createEntityMetadata(\n        tableArgs: TableMetadataArgs,\n    ): EntityMetadata {\n        // we take all \"inheritance tree\" from a target entity to collect all stored metadata args\n        // (by decorators or inside entity schemas). For example for target Post < ContentModel < Unit\n        // it will be an array of [Post, ContentModel, Unit] and we can then get all metadata args of those classes\n        const inheritanceTree: any[] =\n            typeof tableArgs.target === \"function\"\n                ? MetadataUtils.getInheritanceTree(tableArgs.target)\n                : [tableArgs.target] // todo: implement later here inheritance for string-targets\n\n        const tableInheritance = this.metadataArgsStorage.findInheritanceType(\n            tableArgs.target,\n        )\n        const tableTree = this.metadataArgsStorage.findTree(tableArgs.target)\n\n        // if single table inheritance used, we need to copy all children columns in to parent table\n        let singleTableChildrenTargets: any[]\n        if (\n            (tableInheritance && tableInheritance.pattern === \"STI\") ||\n            tableArgs.type === \"entity-child\"\n        ) {\n            singleTableChildrenTargets = this.metadataArgsStorage\n                .filterSingleTableChildren(tableArgs.target)\n                .map((args) => args.target)\n                .filter((target) => typeof target === \"function\")\n\n            inheritanceTree.push(...singleTableChildrenTargets)\n        }\n\n        return new EntityMetadata({\n            connection: this.connection,\n            args: tableArgs,\n            inheritanceTree: inheritanceTree,\n            tableTree: tableTree,\n            inheritancePattern: tableInheritance\n                ? tableInheritance.pattern\n                : undefined,\n        })\n    }\n\n    protected computeParentEntityMetadata(\n        allEntityMetadatas: EntityMetadata[],\n        entityMetadata: EntityMetadata,\n    ) {\n        // after all metadatas created we set parent entity metadata for table inheritance\n        if (entityMetadata.tableType === \"entity-child\") {\n            entityMetadata.parentEntityMetadata = allEntityMetadatas.find(\n                (allEntityMetadata) => {\n                    return (\n                        allEntityMetadata.inheritanceTree.indexOf(\n                            entityMetadata.target as Function,\n                        ) !== -1 &&\n                        allEntityMetadata.inheritancePattern === \"STI\"\n                    )\n                },\n            )!\n        }\n    }\n\n    protected computeEntityMetadataStep1(\n        allEntityMetadatas: EntityMetadata[],\n        entityMetadata: EntityMetadata,\n    ) {\n        const entityInheritance = this.metadataArgsStorage.findInheritanceType(\n            entityMetadata.target,\n        )\n\n        const discriminatorValue =\n            this.metadataArgsStorage.findDiscriminatorValue(\n                entityMetadata.target,\n            )\n\n        if (typeof discriminatorValue !== \"undefined\") {\n            entityMetadata.discriminatorValue = discriminatorValue.value\n        } else {\n            entityMetadata.discriminatorValue = (\n                entityMetadata.target as any\n            ).name\n        }\n\n        // if single table inheritance is used, we need to mark all embedded columns as nullable\n        entityMetadata.embeddeds = this.createEmbeddedsRecursively(\n            entityMetadata,\n            this.metadataArgsStorage.filterEmbeddeds(\n                entityMetadata.inheritanceTree,\n            ),\n        ).map((embedded: EmbeddedMetadata): EmbeddedMetadata => {\n            if (entityMetadata.inheritancePattern === \"STI\") {\n                embedded.columns = embedded.columns.map(\n                    (column: ColumnMetadata): ColumnMetadata => {\n                        column.isNullable = true\n                        return column\n                    },\n                )\n            }\n            return embedded\n        })\n\n        entityMetadata.ownColumns = this.metadataArgsStorage\n            .filterColumns(entityMetadata.inheritanceTree)\n            .map((args) => {\n                // for single table children we reuse columns created for their parents\n                if (entityMetadata.tableType === \"entity-child\")\n                    return entityMetadata.parentEntityMetadata.ownColumns.find(\n                        (column) => column.propertyName === args.propertyName,\n                    )!\n\n                // for multiple table inheritance we can override default column values\n                if (\n                    entityMetadata.tableType === \"regular\" &&\n                    args.target !== entityMetadata.target\n                ) {\n                    const childArgs = this.metadataArgsStorage.columns.find(\n                        (c) =>\n                            c.propertyName === args.propertyName &&\n                            c.target === entityMetadata.target,\n                    )\n                    if (childArgs && childArgs.options.default) {\n                        args.options.default = childArgs.options.default\n                    }\n                }\n\n                const column = new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata,\n                    args,\n                })\n\n                // if single table inheritance used, we need to mark all inherit table columns as nullable\n                const columnInSingleTableInheritedChild =\n                    allEntityMetadatas.find(\n                        (otherEntityMetadata) =>\n                            otherEntityMetadata.tableType === \"entity-child\" &&\n                            otherEntityMetadata.target === args.target,\n                    )\n                if (columnInSingleTableInheritedChild) column.isNullable = true\n                return column\n            })\n\n        // for table inheritance we need to add a discriminator column\n        //\n        if (entityInheritance && entityInheritance.column) {\n            const discriminatorColumnName =\n                entityInheritance.column && entityInheritance.column.name\n                    ? entityInheritance.column.name\n                    : \"type\"\n            let discriminatorColumn = entityMetadata.ownColumns.find(\n                (column) => column.propertyName === discriminatorColumnName,\n            )\n            if (!discriminatorColumn) {\n                discriminatorColumn = new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    args: {\n                        target: entityMetadata.target,\n                        mode: \"virtual\",\n                        propertyName: discriminatorColumnName,\n                        options: entityInheritance.column || {\n                            name: discriminatorColumnName,\n                            type: \"varchar\",\n                            nullable: false,\n                        },\n                    },\n                })\n                discriminatorColumn.isVirtual = true\n                discriminatorColumn.isDiscriminator = true\n                entityMetadata.ownColumns.push(discriminatorColumn)\n            } else {\n                discriminatorColumn.isDiscriminator = true\n            }\n        }\n\n        // add discriminator column to the child entity metadatas\n        // discriminator column will not be there automatically since we are creating it in the code above\n        if (entityMetadata.tableType === \"entity-child\") {\n            const discriminatorColumn =\n                entityMetadata.parentEntityMetadata.ownColumns.find(\n                    (column) => column.isDiscriminator,\n                )\n            if (\n                discriminatorColumn &&\n                !entityMetadata.ownColumns.find(\n                    (column) => column === discriminatorColumn,\n                )\n            ) {\n                entityMetadata.ownColumns.push(discriminatorColumn)\n            }\n            // also copy the inheritance pattern & tree metadata\n            // this comes in handy when inheritance and trees are used together\n            entityMetadata.inheritancePattern =\n                entityMetadata.parentEntityMetadata.inheritancePattern\n            if (\n                !entityMetadata.treeType &&\n                !!entityMetadata.parentEntityMetadata.treeType\n            ) {\n                entityMetadata.treeType =\n                    entityMetadata.parentEntityMetadata.treeType\n                entityMetadata.treeOptions =\n                    entityMetadata.parentEntityMetadata.treeOptions\n                entityMetadata.treeParentRelation =\n                    entityMetadata.parentEntityMetadata.treeParentRelation\n                entityMetadata.treeLevelColumn =\n                    entityMetadata.parentEntityMetadata.treeLevelColumn\n            }\n        }\n\n        const { namingStrategy } = this.connection\n\n        // check if tree is used then we need to add extra columns for specific tree types\n        if (entityMetadata.treeType === \"materialized-path\") {\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    materializedPath: true,\n                    args: {\n                        target: entityMetadata.target,\n                        mode: \"virtual\",\n                        propertyName: \"mpath\",\n                        options: /*tree.column || */ {\n                            name: namingStrategy.materializedPathColumnName,\n                            type: String,\n                            nullable: true,\n                            default: \"\",\n                        },\n                    },\n                }),\n            )\n        } else if (entityMetadata.treeType === \"nested-set\") {\n            const { left, right } = namingStrategy.nestedSetColumnNames\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    nestedSetLeft: true,\n                    args: {\n                        target: entityMetadata.target,\n                        mode: \"virtual\",\n                        propertyName: left,\n                        options: /*tree.column || */ {\n                            name: left,\n                            type: Number,\n                            nullable: false,\n                            default: 1,\n                        },\n                    },\n                }),\n            )\n            entityMetadata.ownColumns.push(\n                new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: entityMetadata,\n                    nestedSetRight: true,\n                    args: {\n                        target: entityMetadata.target,\n                        mode: \"virtual\",\n                        propertyName: right,\n                        options: /*tree.column || */ {\n                            name: right,\n                            type: Number,\n                            nullable: false,\n                            default: 2,\n                        },\n                    },\n                }),\n            )\n        }\n\n        entityMetadata.ownRelations = this.metadataArgsStorage\n            .filterRelations(entityMetadata.inheritanceTree)\n            .map((args) => {\n                // for single table children we reuse relations created for their parents\n                if (entityMetadata.tableType === \"entity-child\") {\n                    const parentRelation =\n                        entityMetadata.parentEntityMetadata.ownRelations.find(\n                            (relation) =>\n                                relation.propertyName === args.propertyName,\n                        )!\n                    const type =\n                        typeof args.type === \"function\"\n                            ? (args.type as () => any)()\n                            : args.type\n                    if (parentRelation.type !== type) {\n                        const clone = Object.create(parentRelation)\n                        clone.type = type\n                        return clone\n                    }\n\n                    return parentRelation\n                }\n\n                return new RelationMetadata({ entityMetadata, args })\n            })\n        entityMetadata.relationIds = this.metadataArgsStorage\n            .filterRelationIds(entityMetadata.inheritanceTree)\n            .map((args) => {\n                // for single table children we reuse relation ids created for their parents\n                if (entityMetadata.tableType === \"entity-child\")\n                    return entityMetadata.parentEntityMetadata.relationIds.find(\n                        (relationId) =>\n                            relationId.propertyName === args.propertyName,\n                    )!\n\n                return new RelationIdMetadata({ entityMetadata, args })\n            })\n        entityMetadata.relationCounts = this.metadataArgsStorage\n            .filterRelationCounts(entityMetadata.inheritanceTree)\n            .map((args) => {\n                // for single table children we reuse relation counts created for their parents\n                if (entityMetadata.tableType === \"entity-child\")\n                    return entityMetadata.parentEntityMetadata.relationCounts.find(\n                        (relationCount) =>\n                            relationCount.propertyName === args.propertyName,\n                    )!\n\n                return new RelationCountMetadata({ entityMetadata, args })\n            })\n        entityMetadata.ownListeners = this.metadataArgsStorage\n            .filterListeners(entityMetadata.inheritanceTree)\n            .map((args) => {\n                return new EntityListenerMetadata({\n                    entityMetadata: entityMetadata,\n                    args: args,\n                })\n            })\n        entityMetadata.checks = this.metadataArgsStorage\n            .filterChecks(entityMetadata.inheritanceTree)\n            .map((args) => {\n                return new CheckMetadata({ entityMetadata, args })\n            })\n\n        // Only PostgreSQL supports exclusion constraints.\n        if (this.connection.driver.options.type === \"postgres\") {\n            entityMetadata.exclusions = this.metadataArgsStorage\n                .filterExclusions(entityMetadata.inheritanceTree)\n                .map((args) => {\n                    return new ExclusionMetadata({ entityMetadata, args })\n                })\n        }\n\n        if (this.connection.driver.options.type === \"cockroachdb\") {\n            entityMetadata.ownIndices = this.metadataArgsStorage\n                .filterIndices(entityMetadata.inheritanceTree)\n                .filter((args) => !args.unique)\n                .map((args) => {\n                    return new IndexMetadata({ entityMetadata, args })\n                })\n\n            const uniques = this.metadataArgsStorage\n                .filterIndices(entityMetadata.inheritanceTree)\n                .filter((args) => args.unique)\n                .map((args) => {\n                    return new UniqueMetadata({\n                        entityMetadata: entityMetadata,\n                        args: {\n                            target: args.target,\n                            name: args.name,\n                            columns: args.columns,\n                        },\n                    })\n                })\n            entityMetadata.ownUniques.push(...uniques)\n        } else {\n            entityMetadata.ownIndices = this.metadataArgsStorage\n                .filterIndices(entityMetadata.inheritanceTree)\n                .map((args) => {\n                    return new IndexMetadata({ entityMetadata, args })\n                })\n        }\n\n        // This drivers stores unique constraints as unique indices.\n        if (\n            DriverUtils.isMySQLFamily(this.connection.driver) ||\n            this.connection.driver.options.type === \"aurora-mysql\" ||\n            this.connection.driver.options.type === \"sap\" ||\n            this.connection.driver.options.type === \"spanner\"\n        ) {\n            const indices = this.metadataArgsStorage\n                .filterUniques(entityMetadata.inheritanceTree)\n                .map((args) => {\n                    return new IndexMetadata({\n                        entityMetadata: entityMetadata,\n                        args: {\n                            target: args.target,\n                            name: args.name,\n                            columns: args.columns,\n                            unique: true,\n                            synchronize: true,\n                        },\n                    })\n                })\n            entityMetadata.ownIndices.push(...indices)\n        } else {\n            const uniques = this.metadataArgsStorage\n                .filterUniques(entityMetadata.inheritanceTree)\n                .map((args) => {\n                    return new UniqueMetadata({ entityMetadata, args })\n                })\n            entityMetadata.ownUniques.push(...uniques)\n        }\n    }\n\n    /**\n     * Creates from the given embedded metadata args real embedded metadatas with its columns and relations,\n     * and does the same for all its sub-embeddeds (goes recursively).\n     */\n    protected createEmbeddedsRecursively(\n        entityMetadata: EntityMetadata,\n        embeddedArgs: EmbeddedMetadataArgs[],\n    ): EmbeddedMetadata[] {\n        return embeddedArgs.map((embeddedArgs) => {\n            const embeddedMetadata = new EmbeddedMetadata({\n                entityMetadata: entityMetadata,\n                args: embeddedArgs,\n            })\n            const targets: any[] =\n                typeof embeddedMetadata.type === \"function\"\n                    ? MetadataUtils.getInheritanceTree(embeddedMetadata.type)\n                    : [embeddedMetadata.type] // todo: implement later here inheritance for string-targets\n\n            embeddedMetadata.columns = this.metadataArgsStorage\n                .filterColumns(targets)\n                .map((args) => {\n                    return new ColumnMetadata({\n                        connection: this.connection,\n                        entityMetadata,\n                        embeddedMetadata,\n                        args,\n                    })\n                })\n            embeddedMetadata.relations = this.metadataArgsStorage\n                .filterRelations(targets)\n                .map((args) => {\n                    return new RelationMetadata({\n                        entityMetadata,\n                        embeddedMetadata,\n                        args,\n                    })\n                })\n            embeddedMetadata.listeners = this.metadataArgsStorage\n                .filterListeners(targets)\n                .map((args) => {\n                    return new EntityListenerMetadata({\n                        entityMetadata,\n                        embeddedMetadata,\n                        args,\n                    })\n                })\n            embeddedMetadata.indices = this.metadataArgsStorage\n                .filterIndices(targets)\n                .map((args) => {\n                    return new IndexMetadata({\n                        entityMetadata,\n                        embeddedMetadata,\n                        args,\n                    })\n                })\n            embeddedMetadata.uniques = this.metadataArgsStorage\n                .filterUniques(targets)\n                .map((args) => {\n                    return new UniqueMetadata({\n                        entityMetadata,\n                        embeddedMetadata,\n                        args,\n                    })\n                })\n            embeddedMetadata.relationIds = this.metadataArgsStorage\n                .filterRelationIds(targets)\n                .map((args) => {\n                    return new RelationIdMetadata({ entityMetadata, args })\n                })\n            embeddedMetadata.relationCounts = this.metadataArgsStorage\n                .filterRelationCounts(targets)\n                .map((args) => {\n                    return new RelationCountMetadata({ entityMetadata, args })\n                })\n            embeddedMetadata.embeddeds = this.createEmbeddedsRecursively(\n                entityMetadata,\n                this.metadataArgsStorage.filterEmbeddeds(targets),\n            )\n            embeddedMetadata.embeddeds.forEach(\n                (subEmbedded) =>\n                    (subEmbedded.parentEmbeddedMetadata = embeddedMetadata),\n            )\n            entityMetadata.allEmbeddeds.push(embeddedMetadata)\n            return embeddedMetadata\n        })\n    }\n\n    /**\n     * Computes all entity metadata's computed properties, and all its sub-metadatas (relations, columns, embeds, etc).\n     */\n    protected computeEntityMetadataStep2(entityMetadata: EntityMetadata) {\n        entityMetadata.embeddeds.forEach((embedded) =>\n            embedded.build(this.connection),\n        )\n        entityMetadata.embeddeds.forEach((embedded) => {\n            embedded.columnsFromTree.forEach((column) =>\n                column.build(this.connection),\n            )\n            embedded.relationsFromTree.forEach((relation) => relation.build())\n        })\n        entityMetadata.ownColumns.forEach((column) =>\n            column.build(this.connection),\n        )\n        entityMetadata.ownRelations.forEach((relation) => relation.build())\n        entityMetadata.relations = entityMetadata.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.relationsFromTree),\n            entityMetadata.ownRelations,\n        )\n        entityMetadata.eagerRelations = entityMetadata.relations.filter(\n            (relation) => relation.isEager,\n        )\n        entityMetadata.lazyRelations = entityMetadata.relations.filter(\n            (relation) => relation.isLazy,\n        )\n        entityMetadata.oneToOneRelations = entityMetadata.relations.filter(\n            (relation) => relation.isOneToOne,\n        )\n        entityMetadata.oneToManyRelations = entityMetadata.relations.filter(\n            (relation) => relation.isOneToMany,\n        )\n        entityMetadata.manyToOneRelations = entityMetadata.relations.filter(\n            (relation) => relation.isManyToOne,\n        )\n        entityMetadata.manyToManyRelations = entityMetadata.relations.filter(\n            (relation) => relation.isManyToMany,\n        )\n        entityMetadata.ownerOneToOneRelations = entityMetadata.relations.filter(\n            (relation) => relation.isOneToOneOwner,\n        )\n        entityMetadata.ownerManyToManyRelations =\n            entityMetadata.relations.filter(\n                (relation) => relation.isManyToManyOwner,\n            )\n        entityMetadata.treeParentRelation = entityMetadata.relations.find(\n            (relation) => relation.isTreeParent,\n        )\n        entityMetadata.treeChildrenRelation = entityMetadata.relations.find(\n            (relation) => relation.isTreeChildren,\n        )\n        entityMetadata.columns = entityMetadata.embeddeds.reduce(\n            (columns, embedded) => columns.concat(embedded.columnsFromTree),\n            entityMetadata.ownColumns,\n        )\n        entityMetadata.listeners = entityMetadata.embeddeds.reduce(\n            (listeners, embedded) =>\n                listeners.concat(embedded.listenersFromTree),\n            entityMetadata.ownListeners,\n        )\n        entityMetadata.afterLoadListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.AFTER_LOAD,\n        )\n        entityMetadata.afterInsertListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.AFTER_INSERT,\n        )\n        entityMetadata.afterUpdateListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.AFTER_UPDATE,\n        )\n        entityMetadata.afterRemoveListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.AFTER_REMOVE,\n        )\n        entityMetadata.afterSoftRemoveListeners =\n            entityMetadata.listeners.filter(\n                (listener) =>\n                    listener.type === EventListenerTypes.AFTER_SOFT_REMOVE,\n            )\n        entityMetadata.afterRecoverListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.AFTER_RECOVER,\n        )\n        entityMetadata.beforeInsertListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.BEFORE_INSERT,\n        )\n        entityMetadata.beforeUpdateListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.BEFORE_UPDATE,\n        )\n        entityMetadata.beforeRemoveListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.BEFORE_REMOVE,\n        )\n        entityMetadata.beforeSoftRemoveListeners =\n            entityMetadata.listeners.filter(\n                (listener) =>\n                    listener.type === EventListenerTypes.BEFORE_SOFT_REMOVE,\n            )\n        entityMetadata.beforeRecoverListeners = entityMetadata.listeners.filter(\n            (listener) => listener.type === EventListenerTypes.BEFORE_RECOVER,\n        )\n        entityMetadata.indices = entityMetadata.embeddeds.reduce(\n            (indices, embedded) => indices.concat(embedded.indicesFromTree),\n            entityMetadata.ownIndices,\n        )\n        entityMetadata.uniques = entityMetadata.embeddeds.reduce(\n            (uniques, embedded) => uniques.concat(embedded.uniquesFromTree),\n            entityMetadata.ownUniques,\n        )\n        entityMetadata.primaryColumns = entityMetadata.columns.filter(\n            (column) => column.isPrimary,\n        )\n        entityMetadata.nonVirtualColumns = entityMetadata.columns.filter(\n            (column) => !column.isVirtual,\n        )\n        entityMetadata.ancestorColumns = entityMetadata.columns.filter(\n            (column) => column.closureType === \"ancestor\",\n        )\n        entityMetadata.descendantColumns = entityMetadata.columns.filter(\n            (column) => column.closureType === \"descendant\",\n        )\n        entityMetadata.hasMultiplePrimaryKeys =\n            entityMetadata.primaryColumns.length > 1\n        entityMetadata.generatedColumns = entityMetadata.columns.filter(\n            (column) => column.isGenerated || column.isObjectId,\n        )\n        entityMetadata.hasUUIDGeneratedColumns =\n            entityMetadata.columns.filter(\n                (column) =>\n                    column.isGenerated || column.generationStrategy === \"uuid\",\n            ).length > 0\n        entityMetadata.createDateColumn = entityMetadata.columns.find(\n            (column) => column.isCreateDate,\n        )\n        entityMetadata.updateDateColumn = entityMetadata.columns.find(\n            (column) => column.isUpdateDate,\n        )\n        entityMetadata.deleteDateColumn = entityMetadata.columns.find(\n            (column) => column.isDeleteDate,\n        )\n        entityMetadata.versionColumn = entityMetadata.columns.find(\n            (column) => column.isVersion,\n        )\n        entityMetadata.discriminatorColumn = entityMetadata.columns.find(\n            (column) => column.isDiscriminator,\n        )\n        entityMetadata.treeLevelColumn = entityMetadata.columns.find(\n            (column) => column.isTreeLevel,\n        )\n        entityMetadata.nestedSetLeftColumn = entityMetadata.columns.find(\n            (column) => column.isNestedSetLeft,\n        )\n        entityMetadata.nestedSetRightColumn = entityMetadata.columns.find(\n            (column) => column.isNestedSetRight,\n        )\n        entityMetadata.materializedPathColumn = entityMetadata.columns.find(\n            (column) => column.isMaterializedPath,\n        )\n        entityMetadata.objectIdColumn = entityMetadata.columns.find(\n            (column) => column.isObjectId,\n        )\n        entityMetadata.foreignKeys.forEach((foreignKey) =>\n            foreignKey.build(this.connection.namingStrategy),\n        )\n        entityMetadata.propertiesMap = entityMetadata.createPropertiesMap()\n        entityMetadata.relationIds.forEach((relationId) => relationId.build())\n        entityMetadata.relationCounts.forEach((relationCount) =>\n            relationCount.build(),\n        )\n        entityMetadata.embeddeds.forEach((embedded) => {\n            embedded.relationIdsFromTree.forEach((relationId) =>\n                relationId.build(),\n            )\n            embedded.relationCountsFromTree.forEach((relationCount) =>\n                relationCount.build(),\n            )\n        })\n    }\n\n    /**\n     * Computes entity metadata's relations inverse side properties.\n     */\n    protected computeInverseProperties(\n        entityMetadata: EntityMetadata,\n        entityMetadatas: EntityMetadata[],\n    ) {\n        entityMetadata.relations.forEach((relation) => {\n            // compute inverse side (related) entity metadatas for all relation metadatas\n            const inverseEntityMetadata = entityMetadatas.find(\n                (m) =>\n                    m.target === relation.type ||\n                    (typeof relation.type === \"string\" &&\n                        (m.targetName === relation.type ||\n                            m.givenTableName === relation.type)),\n            )\n            if (!inverseEntityMetadata)\n                throw new TypeORMError(\n                    \"Entity metadata for \" +\n                        entityMetadata.name +\n                        \"#\" +\n                        relation.propertyPath +\n                        \" was not found. Check if you specified a correct entity object and if it's connected in the connection options.\",\n                )\n\n            relation.inverseEntityMetadata = inverseEntityMetadata\n            relation.inverseSidePropertyPath =\n                relation.buildInverseSidePropertyPath()\n\n            // and compute inverse relation and mark if it has such\n            relation.inverseRelation = inverseEntityMetadata.relations.find(\n                (foundRelation) =>\n                    foundRelation.propertyPath ===\n                    relation.inverseSidePropertyPath,\n            )\n        })\n    }\n\n    /**\n     * Creates indices for the table of single table inheritance.\n     */\n    protected createKeysForTableInheritance(entityMetadata: EntityMetadata) {\n        const isDiscriminatorColumnAlreadyIndexed = entityMetadata.indices.some(\n            ({ givenColumnNames }) =>\n                !!givenColumnNames &&\n                Array.isArray(givenColumnNames) &&\n                givenColumnNames.length === 1 &&\n                givenColumnNames[0] ===\n                    entityMetadata.discriminatorColumn?.databaseName,\n        )\n\n        // If the discriminator column is already indexed, there is no need to\n        // add another index on top of it.\n        if (isDiscriminatorColumnAlreadyIndexed) {\n            return\n        }\n\n        entityMetadata.indices.push(\n            new IndexMetadata({\n                entityMetadata: entityMetadata,\n                columns: [entityMetadata.discriminatorColumn!],\n                args: {\n                    target: entityMetadata.target,\n                    unique: false,\n                },\n            }),\n        )\n    }\n\n    /**\n     * Creates from the given foreign key metadata args real foreign key metadatas.\n     */\n    protected createForeignKeys(\n        entityMetadata: EntityMetadata,\n        entityMetadatas: EntityMetadata[],\n    ) {\n        this.metadataArgsStorage\n            .filterForeignKeys(entityMetadata.inheritanceTree)\n            .forEach((foreignKeyArgs) => {\n                const foreignKeyType =\n                    typeof foreignKeyArgs.type === \"function\"\n                        ? (foreignKeyArgs.type as () => any)()\n                        : foreignKeyArgs.type\n\n                const referencedEntityMetadata = entityMetadatas.find((m) =>\n                    typeof foreignKeyType === \"string\"\n                        ? m.targetName === foreignKeyType ||\n                          m.givenTableName === foreignKeyType\n                        : InstanceChecker.isEntitySchema(foreignKeyType)\n                        ? m.target === foreignKeyType.options.name ||\n                          m.target === foreignKeyType.options.target\n                        : m.target === foreignKeyType,\n                )\n\n                if (!referencedEntityMetadata) {\n                    throw new TypeORMError(\n                        \"Entity metadata for \" +\n                            entityMetadata.name +\n                            (foreignKeyArgs.propertyName\n                                ? \"#\" + foreignKeyArgs.propertyName\n                                : \"\") +\n                            \" was not found. Check if you specified a correct entity object and if it's connected in the connection options.\",\n                    )\n                }\n\n                const columnNames = foreignKeyArgs.columnNames ?? []\n                const referencedColumnNames =\n                    foreignKeyArgs.referencedColumnNames ?? []\n\n                const columns: ColumnMetadata[] = []\n                const referencedColumns: ColumnMetadata[] = []\n\n                if (foreignKeyArgs.propertyName) {\n                    columnNames.push(foreignKeyArgs.propertyName)\n\n                    if (foreignKeyArgs.inverseSide) {\n                        if (typeof foreignKeyArgs.inverseSide === \"function\") {\n                            referencedColumnNames.push(\n                                foreignKeyArgs.inverseSide(\n                                    referencedEntityMetadata.propertiesMap,\n                                ),\n                            )\n                        } else {\n                            referencedColumnNames.push(\n                                foreignKeyArgs.inverseSide,\n                            )\n                        }\n                    }\n                }\n\n                if (!referencedColumnNames.length) {\n                    referencedColumns.push(\n                        ...referencedEntityMetadata.primaryColumns,\n                    )\n                }\n\n                const columnNameToColumn = (\n                    columnName: string,\n                    entityMetadata: EntityMetadata,\n                ): ColumnMetadata => {\n                    const column = entityMetadata.columns.find(\n                        (column) =>\n                            column.propertyName === columnName ||\n                            column.databaseName === columnName,\n                    )\n\n                    if (column) return column\n\n                    const foreignKeyName = foreignKeyArgs.name\n                        ? '\"' + foreignKeyArgs.name + '\" '\n                        : \"\"\n                    const entityName = entityMetadata.targetName\n                    throw new TypeORMError(\n                        `Foreign key constraint ${foreignKeyName}contains column that is missing in the entity (${entityName}): ${columnName}`,\n                    )\n                }\n\n                columns.push(\n                    ...columnNames.map((columnName) =>\n                        columnNameToColumn(columnName, entityMetadata),\n                    ),\n                )\n\n                referencedColumns.push(\n                    ...referencedColumnNames.map((columnName) =>\n                        columnNameToColumn(\n                            columnName,\n                            referencedEntityMetadata,\n                        ),\n                    ),\n                )\n\n                entityMetadata.foreignKeys.push(\n                    new ForeignKeyMetadata({\n                        entityMetadata,\n                        referencedEntityMetadata,\n                        namingStrategy: this.connection.namingStrategy,\n                        columns,\n                        referencedColumns,\n                        ...foreignKeyArgs,\n                    }),\n                )\n            })\n    }\n}\n"], "sourceRoot": ".."}