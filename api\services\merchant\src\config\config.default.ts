import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  // 必需的安全配置
  keys: 'merchant_service_keys_1234567890',
  koa: {
    port: 9802, // merchant 微服务端口（改为9802避免冲突）
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'wap.336101',
        database: 'merchant_service_db',
        synchronize: true,
        logging: true,
        charset: 'utf8mb4',
        cache: true,
        entities: ["**/modules/*/entity"],
      },
    },
  },
  cool: {
    // RPC微服务配置
    rpc: {
      name: "merchant-service", // 商户微服务名称
    },
    redis: {
      host: '127.0.0.1',
      password: '',
      port: 6379,
      db: 0, // 修改为与主服务相同的数据库
    },
    eps: false, // 关闭EPS，避免路由冲突
    initDB: true, // 启用自动初始化
    initMenu: true, // 启用菜单初始化
  } as CoolConfig,
  
  // 添加CORS配置，解决跨域问题
  cors: {
    origin: '*', // 允许所有来源
    allowMethods: 'GET,HEAD,PUT,POST,DELETE,PATCH,OPTIONS',
    credentials: true, // 允许携带凭证
  },
  
  // moleculer 配置移除，使用默认的 Redis RPC
} as MidwayConfig;