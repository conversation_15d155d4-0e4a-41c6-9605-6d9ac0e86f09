import { IMidwayContext, ParamDecoratorOptions, PipeUnionTransform } from '../../interface';
export declare enum RouteParamTypes {
    QUERY = "query",
    BODY = "body",
    PARAM = "param",
    HEADERS = "headers",
    SESSION = "session",
    FILESTREAM = "file_stream",
    FILESSTREAM = "files_stream",
    NEXT = "next",
    REQUEST_PATH = "request_path",
    REQUEST_IP = "request_ip",
    QUERIES = "queries",
    FIELDS = "fields",
    CUSTOM = "custom"
}
export interface RouterParamValue {
    index: number;
    type: RouteParamTypes;
    propertyData?: any;
}
export declare type KoaLikeCustomParamDecorator<T = unknown> = (ctx: IMidwayContext) => T | Promise<T>;
export declare type ExpressLikeCustomParamDecorator<T = unknown> = (req: any, res: any) => T | Promise<T>;
export declare type CustomParamDecorator<T = unknown> = KoaLikeCustomParamDecorator<T> | ExpressLikeCustomParamDecorator<T>;
export declare const createRequestParamDecorator: (transform: CustomParamDecorator, pipesOrOptions?: ParamDecoratorOptions | Array<PipeUnionTransform>) => ParameterDecorator;
export declare const Session: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Body: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Query: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Param: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Headers: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const File: (propertyOrPipes?: any, pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Files: (propertyOrPipes?: any, pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const RequestPath: (pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const RequestIP: (pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Queries: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
export declare const Fields: (propertyOrPipes?: string | PipeUnionTransform[], pipes?: PipeUnionTransform[]) => ParameterDecorator;
//# sourceMappingURL=paramMapping.d.ts.map