{"name": "cache-manager-ioredis-yet", "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Redis store for node-cache-manager updated", "version": "2.1.2", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/jaredwray/cache-manager-stores.git"}, "files": ["dist", "LICENSE", "README.md"], "keywords": ["cache-manager", "i<PERSON>is", "redis", "redis-cluster"], "dependencies": {"cache-manager": "*", "ioredis": "^5.4.1", "telejson": "^7.2.0"}, "devDependencies": {"@types/node": "^20.14.12", "@typescript-eslint/eslint-plugin": "7.17.0", "@typescript-eslint/parser": "7.17.0", "@vitest/coverage-v8": "2.0.4", "eslint": "^8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.2.1", "lint-staged": "15.2.7", "prettier": "3.3.3", "rimraf": "^6.0.1", "typescript": "5.5.4", "vitest": "2.0.4"}, "engines": {"node": ">= 18"}, "lint-staged": {"*.{ts,js}": "eslint --cache --fix", "*.{json,prisma,md,yml}": "prettier --write"}, "scripts": {"clean": "rimraf dist coverage node_modules yarn.lock pnpm-lock.yaml package-lock.json", "build": "tsc -p tsconfig.build.json", "test": "vitest run --coverage", "test:ci": "vitest run"}}