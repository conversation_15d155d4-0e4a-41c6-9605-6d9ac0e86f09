{"name": "run-async", "version": "2.4.1", "description": "Utility method to run function either synchronously or asynchronously using the common `this.async()` style.", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "engines": {"node": ">=0.12.0"}, "repository": "SBoudrias/run-async", "keywords": ["flow", "flow-control", "async"], "files": ["index.js"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {}, "devDependencies": {"mocha": "^7.1.0"}}