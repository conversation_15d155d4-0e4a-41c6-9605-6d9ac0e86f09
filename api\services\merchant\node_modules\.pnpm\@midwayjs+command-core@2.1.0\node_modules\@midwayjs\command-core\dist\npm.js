"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findMidwayVersion = exports.formatModuleVersion = exports.formatInstallNpmCommand = exports.findNpm = exports.resolveMidwayConfig = exports.findNpmModule = exports.findNpmModuleByResolve = exports.loadNpm = exports.installNpm = exports.getCoreBaseDir = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
const exec_1 = require("./utils/exec");
const child_process_1 = require("child_process");
const assert = require("assert");
const os_1 = require("os");
const getCoreBaseDir = () => {
    return (0, child_process_1.execSync)('npm root').toString().replace(/\n$/, '');
};
exports.getCoreBaseDir = getCoreBaseDir;
async function getNpmPath(scope, npmName, npmRegistry) {
    const cwd = scope.cwd || process.cwd();
    const findNmResult = (0, exports.findNpmModule)(cwd, npmName);
    if (findNmResult) {
        return findNmResult;
    }
    const currentNodeModules = (0, exports.getCoreBaseDir)();
    const localNpmPath = (0, path_1.join)(currentNodeModules, npmName);
    if ((0, fs_1.existsSync)(localNpmPath)) {
        return localNpmPath;
    }
    let baseDir = (0, path_1.join)(currentNodeModules, '../');
    if (!(0, fs_1.existsSync)(baseDir)) {
        baseDir = process.cwd();
    }
    const pkgJson = (0, path_1.join)(baseDir, 'package.json');
    if (!(0, fs_1.existsSync)(pkgJson)) {
        (0, fs_1.writeFileSync)(pkgJson, '{}');
    }
    scope.coreInstance.cli.log(`Installing ${npmName}`);
    await installNpm({
        baseDir,
        register: npmRegistry,
        moduleName: npmName,
        mode: ['production', 'no-save'],
        debugLog: scope.coreInstance.debug,
    });
    return (0, exports.findNpmModule)(cwd, npmName);
}
// yarn: yarn add mod --dev
// npm: npm i mod --no-save
// yarn + lerna: yarn add mod --ignore-workspace-root-check
// npm + lerna: npm i mod --no-save
async function installNpm(options) {
    const { baseDir, slience, debugLog } = options;
    const cmd = (0, exports.formatInstallNpmCommand)(options);
    if (debugLog) {
        debugLog('Install npm cmd', cmd);
    }
    return (0, exec_1.exec)({
        cmd,
        baseDir,
        slience,
    }).then((result) => {
        return result.replace(/\n$/, '').replace(/^\s*|\s*$/, '');
    });
}
exports.installNpm = installNpm;
async function loadNpm(scope, npmName, npmRegistry) {
    try {
        const npmPath = await getNpmPath(scope, npmName, npmRegistry);
        assert(npmPath, 'empty npm path');
        const plugin = require(npmPath);
        scope.addPlugin(plugin);
    }
    catch (e) {
        if (scope && scope.debug) {
            scope.debug('Load NPM Error', e);
        }
    }
}
exports.loadNpm = loadNpm;
const findNpmModuleByResolve = (cwd, modName) => {
    try {
        return (0, path_1.dirname)(require.resolve(`${modName}/package.json`, {
            paths: [cwd, process.env.MIDWAY_CLI_PATH || ''],
        }));
    }
    catch (e) {
        return;
    }
};
exports.findNpmModuleByResolve = findNpmModuleByResolve;
const findNpmModule = (cwd, modName) => {
    var _a;
    if ('pnp' in process.versions || ((_a = process.env.npm_execpath) === null || _a === void 0 ? void 0 : _a.includes('pnpm'))) {
        return (0, exports.findNpmModuleByResolve)(cwd, modName);
    }
    const modPath = (0, path_1.join)(cwd, 'node_modules', modName);
    if ((0, fs_1.existsSync)(modPath)) {
        return modPath;
    }
    const parentCwd = (0, path_1.join)(cwd, '../');
    if (parentCwd !== cwd) {
        return (0, exports.findNpmModule)(parentCwd, modName);
    }
};
exports.findNpmModule = findNpmModule;
const resolveMidwayConfig = (cwd) => {
    const midwayConfig = [
        (0, path_1.join)(cwd, 'midway.config.ts'),
        (0, path_1.join)(cwd, 'midway.config.js'),
    ].some(file => (0, fs_1.existsSync)(file));
    const result = {
        exist: midwayConfig,
        source: '',
    };
    if (midwayConfig) {
        const modInfo = (0, exports.findNpmModule)(cwd, '@midwayjs/hooks/internal') ||
            (0, exports.findNpmModule)(cwd, '@midwayjs/hooks-core');
        if (modInfo) {
            const { getConfig } = require(modInfo);
            const config = getConfig(cwd);
            if (config.source) {
                result.source = config.source;
            }
        }
    }
    return result;
};
exports.resolveMidwayConfig = resolveMidwayConfig;
// 从本地检索npm包
const findNpm = (argv) => {
    let npm = '';
    let registry = '';
    let ignoreRegistry = false;
    // 先找npm客户端
    if (argv === null || argv === void 0 ? void 0 : argv.npm) {
        npm = argv.npm;
    }
    else if (process.env.npm_config_user_agent &&
        /yarn/.test(process.env.npm_config_user_agent)) {
        npm = 'yarn';
    }
    else if (process.env.npm_execpath) {
        if (process.env.npm_execpath.includes('yarn')) {
            npm = 'yarn';
        }
        else {
            const npmClient = ['tnpm', 'cnpm', 'pnpm'].find(npm => process.env.npm_execpath.includes(npm));
            if (npmClient) {
                npm = npmClient;
                ignoreRegistry = true;
            }
        }
    }
    if (!npm && !(argv === null || argv === void 0 ? void 0 : argv.skipAutoFindNpm)) {
        const npmList = ['pnpm', 'cnpm'];
        const currentPlatform = (0, os_1.platform)();
        const cmd = npmList.find(cmd => {
            if (currentPlatform === 'win32') {
                // for windows
                try {
                    const find = (0, child_process_1.execSync)(`where ${cmd}`, { stdio: 'ignore' }).toString();
                    // windows的命令路径至少会有 C/D/E:\ 前缀
                    if (find.indexOf(':\\') !== -1) {
                        return cmd;
                    }
                }
                catch (_a) {
                    //
                }
            }
            else {
                // for mac/linux
                try {
                    const find = (0, child_process_1.execSync)(`which ${cmd}`).toString();
                    // 没有找到not found
                    if (find.indexOf('not found') === -1) {
                        return cmd;
                    }
                }
                catch (_b) {
                    //
                }
            }
        });
        if (cmd) {
            npm = cmd;
        }
    }
    if (!npm) {
        npm = 'npm';
    }
    // registry
    if ((argv === null || argv === void 0 ? void 0 : argv.registry) !== undefined) {
        registry = argv.registry || '';
    }
    else if (npm === 'yarn' && process.env.yarn_registry) {
        registry = process.env.yarn_registry;
    }
    else if (process.env.npm_config_registry) {
        registry = process.env.npm_config_registry;
    }
    else {
        // language is zh_CN
        if (process.env.LANG === 'zh_CN.UTF-8') {
            registry = 'https://registry.npmmirror.com';
        }
    }
    return {
        cmd: `${npm}${!ignoreRegistry && registry ? ` --registry=${registry}` : ''}`,
        npm,
        registry: !ignoreRegistry ? registry : '',
    };
};
exports.findNpm = findNpm;
// 转换npm安装命令
const formatInstallNpmCommand = (options) => {
    const { register = 'npm', moduleName, registerPath, isLerna, omitDev, } = options;
    let { installCmd = 'i', mode } = options;
    if (!(mode === null || mode === void 0 ? void 0 : mode.length)) {
        mode = ['no-save'];
    }
    if (/yarn/.test(register)) {
        // yarn add
        if (!moduleName) {
            installCmd = 'install';
            mode.push('no-lockfile');
            if (mode.includes('production')) {
                mode.push('ignore-optional');
            }
        }
        else {
            installCmd = 'add';
        }
        if (!(mode === null || mode === void 0 ? void 0 : mode.length)) {
            mode = [isLerna ? 'ignore-workspace-root-check' : 'dev'];
        }
        mode = mode.map(modeItem => {
            if (modeItem === 'no-save') {
                return 'optional';
            }
            if (modeItem === 'save-dev') {
                return 'dev';
            }
            return modeItem;
        });
    }
    else if (/^pnpm/.test(register)) {
        if (!moduleName) {
            installCmd = 'install';
            if (mode.includes('production')) {
                mode = ['prod', 'no-optional'];
            }
        }
        else {
            installCmd = 'add';
            mode = mode.map(modeItem => {
                if (modeItem === 'no-save') {
                    return 'save-optional';
                }
                if (modeItem === 'save-dev') {
                    return modeItem;
                }
                return '';
            });
        }
    }
    else {
        // npm
        const isProduction = mode.find(modeItem => {
            return modeItem === 'production';
        });
        if (!isProduction) {
            mode.push('legacy-peer-deps');
        }
        if (omitDev) {
            mode.push('omit=dev');
        }
    }
    const cmd = `${register} ${installCmd}${moduleName ? ` ${moduleName}` : ''}${mode
        .map(modeItem => {
        if (!modeItem)
            return '';
        return ` --${modeItem}`;
    })
        .join('')}${registerPath ? ` --registry=${registerPath}` : ''}`;
    return cmd;
};
exports.formatInstallNpmCommand = formatInstallNpmCommand;
const formatModuleVersion = (version) => {
    let major = '', minor = '', patch = '';
    if (typeof version === 'string') {
        if (['beta', 'latest', 'alpha'].includes(version)) {
            major = version;
        }
        else if (/^(\^)?(\d+)(\.|$)/.test(version)) {
            const versionList = version.replace(/^\^/, '').split('.');
            major = versionList[0];
            minor = versionList[1] || '';
            patch = versionList[2] || '';
        }
    }
    return {
        major,
        minor,
        patch,
    };
};
exports.formatModuleVersion = formatModuleVersion;
const findMidwayVersion = (cwd) => {
    let pkg = {};
    try {
        const pkgJsonPath = (0, path_1.join)(cwd, 'package.json');
        if ((0, fs_1.existsSync)(pkgJsonPath)) {
            pkg = JSON.parse((0, fs_1.readFileSync)(pkgJsonPath).toString());
        }
    }
    catch (_a) {
        //
    }
    const deps = Object.assign({}, pkg.dependencies, pkg.devDependencies);
    const modules = [
        '@midwayjs/faas',
        '@midwayjs/koa',
        '@midwayjs/express',
        '@midwayjs/web',
    ];
    const module = modules.find(module => deps[module]);
    return {
        module,
        version: (0, exports.formatModuleVersion)(deps[module]) || {},
    };
};
exports.findMidwayVersion = findMidwayVersion;
//# sourceMappingURL=npm.js.map