{"version": 3, "file": "npm.js", "sourceRoot": "", "sources": ["../src/npm.ts"], "names": [], "mappings": ";;;AAAA,+BAAqC;AACrC,2BAA6D;AAC7D,uCAAoC;AACpC,iDAAyC;AACzC,iCAAiC;AACjC,2BAA8B;AACvB,MAAM,cAAc,GAAG,GAAG,EAAE;IACjC,OAAO,IAAA,wBAAQ,EAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;AAC5D,CAAC,CAAC;AAFW,QAAA,cAAc,kBAEzB;AAEF,KAAK,UAAU,UAAU,CACvB,KAAU,EACV,OAAe,EACf,WAAoB;IAEpB,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IACvC,MAAM,YAAY,GAAG,IAAA,qBAAa,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACjD,IAAI,YAAY,EAAE;QAChB,OAAO,YAAY,CAAC;KACrB;IACD,MAAM,kBAAkB,GAAG,IAAA,sBAAc,GAAE,CAAC;IAC5C,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACvD,IAAI,IAAA,eAAU,EAAC,YAAY,CAAC,EAAE;QAC5B,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,OAAO,GAAG,IAAA,WAAI,EAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAC9C,IAAI,CAAC,IAAA,eAAU,EAAC,OAAO,CAAC,EAAE;QACxB,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;KACzB;IACD,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAC9C,IAAI,CAAC,IAAA,eAAU,EAAC,OAAO,CAAC,EAAE;QACxB,IAAA,kBAAa,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC;KAC9B;IACD,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,OAAO,EAAE,CAAC,CAAC;IACpD,MAAM,UAAU,CAAC;QACf,OAAO;QACP,QAAQ,EAAE,WAAW;QACrB,UAAU,EAAE,OAAO;QACnB,IAAI,EAAE,CAAC,YAAY,EAAE,SAAS,CAAC;QAC/B,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC,KAAK;KACnC,CAAC,CAAC;IACH,OAAO,IAAA,qBAAa,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AACrC,CAAC;AAcD,2BAA2B;AAC3B,2BAA2B;AAC3B,2DAA2D;AAC3D,mCAAmC;AAC5B,KAAK,UAAU,UAAU,CAAC,OAA2B;IAC1D,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;IAC/C,MAAM,GAAG,GAAG,IAAA,+BAAuB,EAAC,OAAO,CAAC,CAAC;IAC7C,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;KAClC;IACD,OAAO,IAAA,WAAI,EAAC;QACV,GAAG;QACH,OAAO;QACP,OAAO;KACR,CAAC,CAAC,IAAI,CAAC,CAAC,MAAc,EAAE,EAAE;QACzB,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC5D,CAAC,CAAC,CAAC;AACL,CAAC;AAbD,gCAaC;AAEM,KAAK,UAAU,OAAO,CAC3B,KAAU,EACV,OAAe,EACf,WAAoB;IAEpB,IAAI;QACF,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC;QAC9D,MAAM,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;QAClC,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAChC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACzB;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACxB,KAAK,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC;SAClC;KACF;AACH,CAAC;AAfD,0BAeC;AAEM,MAAM,sBAAsB,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;IACrD,IAAI;QACF,OAAO,IAAA,cAAO,EACZ,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,eAAe,EAAE;YACzC,KAAK,EAAE,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE,CAAC;SAChD,CAAC,CACH,CAAC;KACH;IAAC,OAAO,CAAC,EAAE;QACV,OAAO;KACR;AACH,CAAC,CAAC;AAVW,QAAA,sBAAsB,0BAUjC;AAEK,MAAM,aAAa,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;;IAC5C,IAAI,KAAK,IAAI,OAAO,CAAC,QAAQ,KAAI,MAAA,OAAO,CAAC,GAAG,CAAC,YAAY,0CAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,EAAE;QAC3E,OAAO,IAAA,8BAAsB,EAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC7C;IAED,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;IACnD,IAAI,IAAA,eAAU,EAAC,OAAO,CAAC,EAAE;QACvB,OAAO,OAAO,CAAC;KAChB;IACD,MAAM,SAAS,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACnC,IAAI,SAAS,KAAK,GAAG,EAAE;QACrB,OAAO,IAAA,qBAAa,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;KAC1C;AACH,CAAC,CAAC;AAbW,QAAA,aAAa,iBAaxB;AAEK,MAAM,mBAAmB,GAAG,CAAC,GAAW,EAAE,EAAE;IACjD,MAAM,YAAY,GAAG;QACnB,IAAA,WAAI,EAAC,GAAG,EAAE,kBAAkB,CAAC;QAC7B,IAAA,WAAI,EAAC,GAAG,EAAE,kBAAkB,CAAC;KAC9B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,eAAU,EAAC,IAAI,CAAC,CAAC,CAAC;IAEjC,MAAM,MAAM,GAAG;QACb,KAAK,EAAE,YAAY;QACnB,MAAM,EAAE,EAAE;KACX,CAAC;IAEF,IAAI,YAAY,EAAE;QAChB,MAAM,OAAO,GACX,IAAA,qBAAa,EAAC,GAAG,EAAE,0BAA0B,CAAC;YAC9C,IAAA,qBAAa,EAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;QAE7C,IAAI,OAAO,EAAE;YACX,MAAM,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAC9B,IAAI,MAAM,CAAC,MAAM,EAAE;gBACjB,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;aAC/B;SACF;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AA1BW,QAAA,mBAAmB,uBA0B9B;AAEF,YAAY;AACL,MAAM,OAAO,GAAG,CAAC,IAIvB,EAAE,EAAE;IACH,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,IAAI,QAAQ,GAAG,EAAE,CAAC;IAClB,IAAI,cAAc,GAAG,KAAK,CAAC;IAC3B,WAAW;IACX,IAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,GAAG,EAAE;QACb,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;KAChB;SAAM,IACL,OAAO,CAAC,GAAG,CAAC,qBAAqB;QACjC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,EAC9C;QACA,GAAG,GAAG,MAAM,CAAC;KACd;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE;QACnC,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC7C,GAAG,GAAG,MAAM,CAAC;SACd;aAAM;YACL,MAAM,SAAS,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CACpD,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,CACvC,CAAC;YACF,IAAI,SAAS,EAAE;gBACb,GAAG,GAAG,SAAS,CAAC;gBAChB,cAAc,GAAG,IAAI,CAAC;aACvB;SACF;KACF;IAED,IAAI,CAAC,GAAG,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,CAAA,EAAE;QAClC,MAAM,OAAO,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACjC,MAAM,eAAe,GAAG,IAAA,aAAQ,GAAE,CAAC;QACnC,MAAM,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YAC7B,IAAI,eAAe,KAAK,OAAO,EAAE;gBAC/B,cAAc;gBACd,IAAI;oBACF,MAAM,IAAI,GAAG,IAAA,wBAAQ,EAAC,SAAS,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACtE,8BAA8B;oBAC9B,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC9B,OAAO,GAAG,CAAC;qBACZ;iBACF;gBAAC,WAAM;oBACN,EAAE;iBACH;aACF;iBAAM;gBACL,gBAAgB;gBAChB,IAAI;oBACF,MAAM,IAAI,GAAG,IAAA,wBAAQ,EAAC,SAAS,GAAG,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC;oBACjD,gBAAgB;oBAChB,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;wBACpC,OAAO,GAAG,CAAC;qBACZ;iBACF;gBAAC,WAAM;oBACN,EAAE;iBACH;aACF;QACH,CAAC,CAAC,CAAC;QACH,IAAI,GAAG,EAAE;YACP,GAAG,GAAG,GAAG,CAAC;SACX;KACF;IAED,IAAI,CAAC,GAAG,EAAE;QACR,GAAG,GAAG,KAAK,CAAC;KACb;IAED,WAAW;IACX,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,QAAQ,MAAK,SAAS,EAAE;QAChC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,EAAE,CAAC;KAChC;SAAM,IAAI,GAAG,KAAK,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;QACtD,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;KACtC;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QAC1C,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC;KAC5C;SAAM;QACL,oBAAoB;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,KAAK,aAAa,EAAE;YACtC,QAAQ,GAAG,gCAAgC,CAAC;SAC7C;KACF;IAED,OAAO;QACL,GAAG,EAAE,GAAG,GAAG,GACT,CAAC,cAAc,IAAI,QAAQ,CAAC,CAAC,CAAC,eAAe,QAAQ,EAAE,CAAC,CAAC,CAAC,EAC5D,EAAE;QACF,GAAG;QACH,QAAQ,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;KAC1C,CAAC;AACJ,CAAC,CAAC;AAxFW,QAAA,OAAO,WAwFlB;AAEF,YAAY;AACL,MAAM,uBAAuB,GAAG,CAAC,OAA2B,EAAE,EAAE;IACrE,MAAM,EACJ,QAAQ,GAAG,KAAK,EAChB,UAAU,EACV,YAAY,EACZ,OAAO,EACP,OAAO,GACR,GAAG,OAAO,CAAC;IACZ,IAAI,EAAE,UAAU,GAAG,GAAG,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;IACzC,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,EAAE;QACjB,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;KACpB;IACD,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QACzB,WAAW;QACX,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,SAAS,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;gBAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;aAC9B;SACF;aAAM;YACL,UAAU,GAAG,KAAK,CAAC;SACpB;QACD,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,MAAM,CAAA,EAAE;YACjB,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,6BAA6B,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC1D;QACD,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACzB,IAAI,QAAQ,KAAK,SAAS,EAAE;gBAC1B,OAAO,UAAU,CAAC;aACnB;YACD,IAAI,QAAQ,KAAK,UAAU,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACd;YACD,OAAO,QAAQ,CAAC;QAClB,CAAC,CAAC,CAAC;KACJ;SAAM,IAAI,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;QACjC,IAAI,CAAC,UAAU,EAAE;YACf,UAAU,GAAG,SAAS,CAAC;YACvB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;gBAC/B,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;aAChC;SACF;aAAM;YACL,UAAU,GAAG,KAAK,CAAC;YACnB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACzB,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,OAAO,eAAe,CAAC;iBACxB;gBACD,IAAI,QAAQ,KAAK,UAAU,EAAE;oBAC3B,OAAO,QAAQ,CAAC;iBACjB;gBACD,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;SACJ;KACF;SAAM;QACL,MAAM;QACN,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACxC,OAAO,QAAQ,KAAK,YAAY,CAAC;QACnC,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC/B;QACD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACvB;KACF;IACD,MAAM,GAAG,GAAG,GAAG,QAAQ,IAAI,UAAU,GACnC,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,EAAE,CAAC,CAAC,CAAC,EAClC,GAAG,IAAI;SACJ,GAAG,CAAC,QAAQ,CAAC,EAAE;QACd,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,CAAC;QACzB,OAAO,MAAM,QAAQ,EAAE,CAAC;IAC1B,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,eAAe,YAAY,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAClE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AA1EW,QAAA,uBAAuB,2BA0ElC;AAEK,MAAM,mBAAmB,GAAG,CAAC,OAAQ,EAAE,EAAE;IAC9C,IAAI,KAAK,GAAG,EAAE,EACZ,KAAK,GAAG,EAAE,EACV,KAAK,GAAG,EAAE,CAAC;IACb,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;QAC/B,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACjD,KAAK,GAAG,OAAO,CAAC;SACjB;aAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAC5C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC1D,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACvB,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAC7B,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;SAC9B;KACF;IACD,OAAO;QACL,KAAK;QACL,KAAK;QACL,KAAK;KACN,CAAC;AACJ,CAAC,CAAC;AAnBW,QAAA,mBAAmB,uBAmB9B;AAEK,MAAM,iBAAiB,GAAG,CAAC,GAAG,EAAO,EAAE;IAC5C,IAAI,GAAG,GAAQ,EAAE,CAAC;IAClB,IAAI;QACF,MAAM,WAAW,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAC9C,IAAI,IAAA,eAAU,EAAC,WAAW,CAAC,EAAE;YAC3B,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;SACxD;KACF;IAAC,WAAM;QACN,EAAE;KACH;IACD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,eAAe,CAAC,CAAC;IACtE,MAAM,OAAO,GAAG;QACd,gBAAgB;QAChB,eAAe;QACf,mBAAmB;QACnB,eAAe;KAChB,CAAC;IACF,MAAM,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;IACpD,OAAO;QACL,MAAM;QACN,OAAO,EAAE,IAAA,2BAAmB,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE;KACjD,CAAC;AACJ,CAAC,CAAC;AAtBW,QAAA,iBAAiB,qBAsB5B"}