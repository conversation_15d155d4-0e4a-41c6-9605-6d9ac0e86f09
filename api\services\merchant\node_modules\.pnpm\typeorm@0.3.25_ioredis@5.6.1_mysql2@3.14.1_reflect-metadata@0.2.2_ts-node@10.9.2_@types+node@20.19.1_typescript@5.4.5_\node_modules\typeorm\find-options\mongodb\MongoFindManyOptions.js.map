{"version": 3, "sources": ["../../src/find-options/mongodb/MongoFindManyOptions.ts"], "names": [], "mappings": "", "file": "MongoFindManyOptions.js", "sourcesContent": ["import { MongoFindOneOptions } from \"./MongoFindOneOptions\"\n\n/**\n * Defines a special criteria to find specific entities.\n */\nexport interface MongoFindManyOptions<Entity = any>\n    extends MongoFindOneOptions<Entity> {\n    /**\n     * Offset (paginated) where from entities should be taken.\n     */\n    skip?: number\n\n    /**\n     * Limit (paginated) - max number of entities should be taken.\n     */\n    take?: number\n}\n"], "sourceRoot": "../.."}