"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTsConfig = exports.readJson = exports.formatTsError = exports.compileTypeScript = void 0;
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const npm_1 = require("../npm");
const copy_1 = require("./copy");
const compileTypeScript = async (options) => {
    var _a;
    let { tsOptions } = options;
    const { baseDir, coverOptions, sourceDir, outDir } = options;
    const tsMod = (0, npm_1.findNpmModule)(baseDir, 'typescript');
    const ts = require(tsMod);
    if (!tsOptions) {
        tsOptions = await (0, exports.getTsConfig)(baseDir);
    }
    if (!tsOptions.compilerOptions) {
        tsOptions.compilerOptions = {};
    }
    tsOptions.compilerOptions = Object.assign({
        experimentalDecorators: true,
        emitDecoratorMetadata: true,
    }, tsOptions.compilerOptions, coverOptions);
    if (outDir) {
        tsOptions.compilerOptions.outDir = outDir;
    }
    if (sourceDir) {
        tsOptions.compilerOptions.rootDir = sourceDir;
    }
    if (!((_a = tsOptions.include) === null || _a === void 0 ? void 0 : _a.length)) {
        tsOptions.include = [sourceDir || 'src'];
    }
    const parsedCommandLine = ts.parseJsonConfigFileContent(tsOptions, ts.sys, baseDir);
    const host = ts.createCompilerHost(parsedCommandLine.options);
    const fileNames = parsedCommandLine.fileNames;
    const program = ts.createProgram(fileNames, parsedCommandLine.options, host);
    const emitResult = program.emit();
    const allDiagnostics = ts
        .getPreEmitDiagnostics(program)
        .concat(emitResult.diagnostics);
    const errors = [];
    const necessaryErrors = [];
    for (const error of allDiagnostics) {
        if (error.category !== ts.DiagnosticCategory.Error) {
            continue;
        }
        const errorItem = (0, exports.formatTsError)(baseDir, error);
        // @ts-expect-error reportsUnnecessary is not in type
        if (!error.reportsUnnecessary) {
            necessaryErrors.push(errorItem);
        }
        errors.push(errorItem);
    }
    return {
        fileNames,
        options: parsedCommandLine.options,
        errors,
        necessaryErrors,
    };
};
exports.compileTypeScript = compileTypeScript;
const formatTsError = (baseDir, error) => {
    var _a;
    if (!error.messageText) {
        return { message: '', path: '' };
    }
    const message = pickMessageTextFromDiagnosticMessageChain(error.messageText);
    let errorPath = '';
    // tsconfig error, file is undefined
    if ((_a = error === null || error === void 0 ? void 0 : error.file) === null || _a === void 0 ? void 0 : _a.text) {
        const code = error.file.text.slice(0, error.start).split('\n');
        errorPath = `${(0, path_1.relative)(baseDir, error.file.fileName)}:${code.length}:${code[code.length - 1].length}`;
    }
    return {
        message,
        path: errorPath,
    };
};
exports.formatTsError = formatTsError;
function pickMessageTextFromDiagnosticMessageChain(input) {
    if (typeof input === 'string') {
        return input;
    }
    const arr = [];
    if (input.messageText) {
        arr.push(input.messageText);
    } // void else
    if (Array.isArray(input.next)) {
        arr.push(...input.next.map(pickMessageTextFromDiagnosticMessageChain));
    } // void else
    return arr.join('\n  ');
}
const readJson = async (path) => {
    if (await (0, copy_1.exists)(path)) {
        try {
            return JSON.parse(await (0, fs_extra_1.readFile)(path, 'utf-8'));
        }
        catch (_a) {
            return {};
        }
    }
    return {};
};
exports.readJson = readJson;
const getTsConfig = async (baseDir) => {
    const tsConfigPath = (0, path_1.join)(baseDir, 'tsconfig.json');
    return (0, exports.readJson)(tsConfigPath);
};
exports.getTsConfig = getTsConfig;
//# sourceMappingURL=compileTypeScript.js.map