{"name": "cache-manager", "version": "3.6.3", "description": "Cache module for Node.js", "main": "index.js", "files": ["index.js", "/examples", "/lib"], "scripts": {"test": "make"}, "repository": {"type": "git", "url": "https://github.com/BryanD<PERSON>van/node-cache-manager.git"}, "keywords": ["cache", "redis", "lru-cache", "memory cache", "multiple cache"], "author": "<PERSON>", "license": "MIT", "dependencies": {"async": "3.2.3", "lodash.clonedeep": "^4.5.0", "lru-cache": "6.0.0"}, "devDependencies": {"coveralls": "3.1.0", "es6-promise": "^4.2.8", "eslint": "7.7.0", "jsdoc": "3.6.5", "mocha": "8.1.1", "nyc": "15.1.0", "optimist": "0.6.1", "sinon": "9.0.3"}}