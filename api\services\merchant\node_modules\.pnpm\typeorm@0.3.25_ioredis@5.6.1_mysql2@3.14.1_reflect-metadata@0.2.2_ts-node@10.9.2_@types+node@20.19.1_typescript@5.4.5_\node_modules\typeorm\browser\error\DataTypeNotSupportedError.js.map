{"version": 3, "sources": ["../browser/src/error/DataTypeNotSupportedError.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,yBAA0B,SAAQ,YAAY;IACvD,YACI,MAAsB,EACtB,QAAoB,EACpB,QAAuB;QAEvB,KAAK,EAAE,CAAA;QAEP,MAAM,IAAI,GACN,OAAO,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAO,QAAS,CAAC,IAAI,CAAA;QAClE,IAAI,CAAC,OAAO,GAAG,cAAc,IAAI,SAAS,MAAM,CAAC,cAAc,CAAC,UAAU,IAAI,MAAM,CAAC,YAAY,0BAA0B,QAAQ,aAAa,CAAA;IACpJ,CAAC;CACJ", "file": "DataTypeNotSupportedError.js", "sourcesContent": ["import { ColumnType } from \"../driver/types/ColumnTypes\"\nimport { DatabaseType } from \"../driver/types/DatabaseType\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class DataTypeNotSupportedError extends TypeORMError {\n    constructor(\n        column: ColumnMetadata,\n        dataType: ColumnType,\n        database?: DatabaseType,\n    ) {\n        super()\n\n        const type =\n            typeof dataType === \"string\" ? dataType : (<any>dataType).name\n        this.message = `Data type \"${type}\" in \"${column.entityMetadata.targetName}.${column.propertyName}\" is not supported by \"${database}\" database.`\n    }\n}\n"], "sourceRoot": ".."}