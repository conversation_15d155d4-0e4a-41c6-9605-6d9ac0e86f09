<a href="https://travis-ci.org/Xotic750/has-symbol-support-x"
   title="Travis status">
<img
   src="https://travis-ci.org/Xotic750/has-symbol-support-x.svg?branch=master"
   alt="Travis status" height="18"/>
</a>
<a href="https://david-dm.org/Xotic750/has-symbol-support-x"
   title="Dependency status">
<img src="https://david-dm.org/Xotic750/has-symbol-support-x.svg"
   alt="Dependency status" height="18"/>
</a>
<a href="https://david-dm.org/Xotic750/has-symbol-support-x#info=devDependencies"
   title="devDependency status">
<img src="https://david-dm.org/Xotic750/has-symbol-support-x/dev-status.svg"
   alt="devDependency status" height="18"/>
</a>
<a href="https://badge.fury.io/js/has-symbol-support-x" title="npm version">
<img src="https://badge.fury.io/js/has-symbol-support-x.svg"
   alt="npm version" height="18"/>
</a>
<a name="module_has-symbol-support-x"></a>

## has-symbol-support-x
Tests if ES6 Symbol is supported.

**Version**: 1.4.2  
**Author**: Xotic750 <<EMAIL>>  
**License**: [MIT](&lt;https://opensource.org/licenses/MIT&gt;)  
**Copyright**: Xotic750  
<a name="exp_module_has-symbol-support-x--module.exports"></a>

### `module.exports` : <code>boolean</code> ⏏
Indicates if `Symbol`exists and creates the correct type.
`true`, if it exists and creates the correct type, otherwise `false`.

**Kind**: Exported member  
