{"version": 3, "sources": ["../../src/decorator/columns/Column.ts"], "names": [], "mappings": ";;AAqIA,wBAsFC;AA3ND,2CAAsD;AAiBtD,mFAA+E;AAgH/E;;;GAGG;AACH,SAAgB,MAAM,CAClB,aAG6C,EAC7C,OAA+C;IAE/C,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,uBAAuB;QACvB,IAAI,IAA4B,CAAA;QAChC,IACI,OAAO,aAAa,KAAK,QAAQ;YACjC,OAAO,aAAa,KAAK,UAAU,EACrC,CAAC;YACC,IAAI,GAAe,aAAa,CAAA;QACpC,CAAC;aAAM,IAAI,aAAa,EAAE,CAAC;YACvB,OAAO,GAAkB,aAAa,CAAA;YACtC,IAAI,GAAG,aAAa,CAAC,IAAI,CAAA;QAC7B,CAAC;QACD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAmB,CAAA;QAE3C,uDAAuD;QACvD,MAAM,mBAAmB,GACrB,OAAO,IAAK,OAAe,CAAC,WAAW;YACnC,CAAC,CAAE,OAAe,CAAC,WAAW,CACxB,aAAa,EACb,MAAM,EACN,YAAY,CACf;YACH,CAAC,CAAC,SAAS,CAAA;QACnB,IAAI,CAAC,IAAI,IAAI,mBAAmB;YAC5B,uDAAuD;YACvD,IAAI,GAAG,mBAAmB,CAAA;QAE9B,yGAAyG;QACzG,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI;YAAE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;QAE9C,0CAA0C;QAC1C,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU;YAChD,OAAO,CAAC,UAAU;gBACd,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAA;QAE5D,IAAI,OAAO,aAAa,KAAK,UAAU,EAAE,CAAC;YACtC,uBAAuB;YACvB,IAAA,gCAAsB,GAAE,CAAC,SAAS,CAAC,IAAI,CAAC;gBACpC,MAAM,EAAE,MAAM,CAAC,WAAW;gBAC1B,YAAY,EAAE,YAAY;gBAC1B,OAAO,EACH,mBAAmB,KAAK,KAAK,IAAI,OAAO,CAAC,KAAK,KAAK,IAAI;gBAC3D,MAAM,EACF,OAAO,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;gBAC7D,IAAI,EAAE,aAAyC;aAC1B,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACJ,4BAA4B;YAE5B,yFAAyF;YACzF,IAAI,CAAC,OAAO,CAAC,IAAI;gBACb,MAAM,IAAI,mDAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;YAE5D,gBAAgB;YAChB,IAAI,OAAO,CAAC,MAAM,KAAK,IAAI;gBACvB,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;oBAClC,MAAM,EAAE,MAAM,CAAC,WAAW;oBAC1B,OAAO,EAAE,CAAC,YAAY,CAAC;iBAC1B,CAAC,CAAA;YAEN,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;gBAClC,MAAM,EAAE,MAAM,CAAC,WAAW;gBAC1B,YAAY,EAAE,YAAY;gBAC1B,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,OAAO;aACG,CAAC,CAAA;YAExB,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACpB,IAAA,gCAAsB,GAAE,CAAC,WAAW,CAAC,IAAI,CAAC;oBACtC,MAAM,EAAE,MAAM,CAAC,WAAW;oBAC1B,YAAY,EAAE,YAAY;oBAC1B,QAAQ,EACJ,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;wBACjC,CAAC,CAAC,OAAO,CAAC,SAAS;wBACnB,CAAC,CAAC,WAAW;iBACC,CAAC,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC,CAAA;AACL,CAAC", "file": "Column.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport {\n    ColumnType,\n    SimpleColumnType,\n    SpatialColumnType,\n    WithLengthColumnType,\n    WithPrecisionColumnType,\n    WithWidthColumnType,\n} from \"../../driver/types/ColumnTypes\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnCommonOptions } from \"../options/ColumnCommonOptions\"\nimport { SpatialColumnOptions } from \"../options/SpatialColumnOptions\"\nimport { ColumnWithLengthOptions } from \"../options/ColumnWithLengthOptions\"\nimport { ColumnNumericOptions } from \"../options/ColumnNumericOptions\"\nimport { ColumnEnumOptions } from \"../options/ColumnEnumOptions\"\nimport { ColumnEmbeddedOptions } from \"../options/ColumnEmbeddedOptions\"\nimport { EmbeddedMetadataArgs } from \"../../metadata-args/EmbeddedMetadataArgs\"\nimport { ColumnTypeUndefinedError } from \"../../error/ColumnTypeUndefinedError\"\nimport { ColumnHstoreOptions } from \"../options/ColumnHstoreOptions\"\nimport { ColumnWithWidthOptions } from \"../options/ColumnWithWidthOptions\"\nimport { GeneratedMetadataArgs } from \"../../metadata-args/GeneratedMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * Column decorator is used to mark a specific class property as a table column. Only properties decorated with this\n * decorator will be persisted to the database when entity be saved.\n */\nexport function Column(): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(options: ColumnOptions): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: SimpleColumnType,\n    options?: ColumnCommonOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: SpatialColumnType,\n    options?: ColumnCommonOptions & SpatialColumnOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: WithLengthColumnType,\n    options?: ColumnCommonOptions & ColumnWithLengthOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: WithWidthColumnType,\n    options?: ColumnCommonOptions & ColumnWithWidthOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: WithPrecisionColumnType,\n    options?: ColumnCommonOptions & ColumnNumericOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: \"enum\",\n    options?: ColumnCommonOptions & ColumnEnumOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: \"simple-enum\",\n    options?: ColumnCommonOptions & ColumnEnumOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: \"set\",\n    options?: ColumnCommonOptions & ColumnEnumOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    type: \"hstore\",\n    options?: ColumnCommonOptions & ColumnHstoreOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n *\n * Property in entity can be marked as Embedded, and on persist all columns from the embedded are mapped to the\n * single table of the entity where Embedded is used. And on hydration all columns which supposed to be in the\n * embedded will be mapped to it from the single table.\n */\nexport function Column(\n    type: (type?: any) => Function,\n    options?: ColumnEmbeddedOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n */\nexport function Column(\n    typeOrOptions?:\n        | ((type?: any) => Function)\n        | ColumnType\n        | (ColumnOptions & ColumnEmbeddedOptions),\n    options?: ColumnOptions & ColumnEmbeddedOptions,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        // normalize parameters\n        let type: ColumnType | undefined\n        if (\n            typeof typeOrOptions === \"string\" ||\n            typeof typeOrOptions === \"function\"\n        ) {\n            type = <ColumnType>typeOrOptions\n        } else if (typeOrOptions) {\n            options = <ColumnOptions>typeOrOptions\n            type = typeOrOptions.type\n        }\n        if (!options) options = {} as ColumnOptions\n\n        // if type is not given explicitly then try to guess it\n        const reflectMetadataType =\n            Reflect && (Reflect as any).getMetadata\n                ? (Reflect as any).getMetadata(\n                      \"design:type\",\n                      object,\n                      propertyName,\n                  )\n                : undefined\n        if (!type && reflectMetadataType)\n            // if type is not given explicitly then try to guess it\n            type = reflectMetadataType\n\n        // check if there is no type in column options then set type from first function argument, or guessed one\n        if (!options.type && type) options.type = type\n\n        // specify HSTORE type if column is HSTORE\n        if (options.type === \"hstore\" && !options.hstoreType)\n            options.hstoreType =\n                reflectMetadataType === Object ? \"object\" : \"string\"\n\n        if (typeof typeOrOptions === \"function\") {\n            // register an embedded\n            getMetadataArgsStorage().embeddeds.push({\n                target: object.constructor,\n                propertyName: propertyName,\n                isArray:\n                    reflectMetadataType === Array || options.array === true,\n                prefix:\n                    options.prefix !== undefined ? options.prefix : undefined,\n                type: typeOrOptions as (type?: any) => Function,\n            } as EmbeddedMetadataArgs)\n        } else {\n            // register a regular column\n\n            // if we still don't have a type then we need to give error to user that type is required\n            if (!options.type)\n                throw new ColumnTypeUndefinedError(object, propertyName)\n\n            // create unique\n            if (options.unique === true)\n                getMetadataArgsStorage().uniques.push({\n                    target: object.constructor,\n                    columns: [propertyName],\n                })\n\n            getMetadataArgsStorage().columns.push({\n                target: object.constructor,\n                propertyName: propertyName,\n                mode: \"regular\",\n                options: options,\n            } as ColumnMetadataArgs)\n\n            if (options.generated) {\n                getMetadataArgsStorage().generations.push({\n                    target: object.constructor,\n                    propertyName: propertyName,\n                    strategy:\n                        typeof options.generated === \"string\"\n                            ? options.generated\n                            : \"increment\",\n                } as GeneratedMetadataArgs)\n            }\n        }\n    }\n}\n"], "sourceRoot": "../.."}