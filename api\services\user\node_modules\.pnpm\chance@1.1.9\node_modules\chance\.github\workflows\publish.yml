name: NPM Publish Package

on:
  pull_request:
    branches:
      - master
  push:
    branches:
      - master

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 12
      - name: install and test
        run: |
          yarn
          yarn run test

  publish-npm:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v1
      - uses: actions/setup-node@v1
        with:
          node-version: 12
          registry-url: https://registry.npmjs.org/
      - name: Publish to NPM
        if: github.event_name == 'pull_request' && github.event.pull_request.merged == 'true'
        run: npm publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  # Commented out until we have access to the Github Package Registry
  #
  # publish-gpr:
  #   needs: build
  #   runs-on: ubuntu-latest
  #   steps:
  #     - uses: actions/checkout@v1
  #     - uses: actions/setup-node@v1
  #       with:
  #         node-version: 12
  #         registry-url: https://npm.pkg.github.com/
  #         scope: '@victorquinn'
  #     - run: npm publish
  #       env:
  #         NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
