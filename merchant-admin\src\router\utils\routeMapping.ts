/**
 * 路由映射管理器
 * 解决菜单结构调整后路由访问失效的问题
 */
import type { AppRouteRecord } from '@/types/router'
import { RoutesAlias } from '../routesAlias'

/**
 * 路由映射配置
 * key: 路由标识符（唯一且不变）
 * value: 路由配置信息
 */
interface RouteMapping {
  /** 路由唯一标识符 */
  routeKey: string
  /** 组件路径 */
  component: string
  /** 默认访问路径（用于向后兼容） */
  defaultPath?: string
  /** 路由名称 */
  name: string
  /** 元数据 */
  meta?: any
}

/**
 * 系统路由映射表
 * 使用唯一标识符而不是路径来标识路由
 */
const ROUTE_MAPPINGS: Record<string, RouteMapping> = {
  // 商户管理
  'merchant.settle-in': {
    routeKey: 'merchant.settle-in',
    component: 'MerchantSettleIn',
    defaultPath: '/merchant/settle-in',
    name: 'MerchantSettleIn',
    meta: { title: '商户入驻', keepAlive: true }
  },
  'merchant.personal': {
    routeKey: 'merchant.personal',
    component: 'MerchantPersonal',
    defaultPath: '/merchant/personal',
    name: 'MerchantPersonal',
    meta: { title: '个人商户', keepAlive: true }
  },
  'merchant.company': {
    routeKey: 'merchant.company',
    component: 'MerchantCompany',
    defaultPath: '/merchant/company',
    name: 'MerchantCompany',
    meta: { title: '企业商户', keepAlive: true }
  },
  'merchant.category': {
    routeKey: 'merchant.category',
    component: 'MerchantCategory',
    defaultPath: '/merchant/category',
    name: 'MerchantCategory',
    meta: { title: '入驻类目管理', keepAlive: true }
  },
  'merchant.international': {
    routeKey: 'merchant.international',
    component: 'MerchantInternational',
    defaultPath: '/merchant/international',
    name: 'MerchantInternational',
    meta: { title: '国际商户', keepAlive: true }
  },

  // 财务结算
  'settlement.rules': {
    routeKey: 'settlement.rules',
    component: 'SettlementRules',
    defaultPath: '/settlement/rules',
    name: 'SettlementRules',
    meta: { title: '结算规则', keepAlive: true }
  },
  'settlement.batch': {
    routeKey: 'settlement.batch',
    component: 'SettlementBatch',
    defaultPath: '/settlement/batch-settlement',
    name: 'SettlementBatch',
    meta: { title: '批量结算', keepAlive: true }
  },
  'settlement.risk-compliance': {
    routeKey: 'settlement.risk-compliance',
    component: 'SettlementRiskCompliance',
    defaultPath: '/settlement/risk-compliance',
    name: 'SettlementRiskCompliance',
    meta: { title: '风险与合规', keepAlive: true }
  },
  'settlement.internationalization': {
    routeKey: 'settlement.internationalization',
    component: 'SettlementInternationalization',
    defaultPath: '/settlement/internationalization',
    name: 'SettlementInternationalization',
    meta: { title: '国际化管理', keepAlive: true }
  },

  // 创作者管理
  'creator.heritage-auth': {
    routeKey: 'creator.heritage-auth',
    component: 'CreatorHeritageAuth',
    defaultPath: '/creator/heritage-auth',
    name: 'CreatorHeritageAuth',
    meta: { title: '创作者认证', keepAlive: true }
  },
  'creator.management': {
    routeKey: 'creator.management',
    component: 'CreatorManagement',
    defaultPath: '/creator/management',
    name: 'CreatorManagement',
    meta: { title: '创作者管理', keepAlive: true }
  },
  'creator.category': {
    routeKey: 'creator.category',
    component: 'CreatorCategory',
    defaultPath: '/creator/category',
    name: 'CreatorCategory',
    meta: { title: '认证分类', keepAlive: true }
  },

  // 商户监控
  'monitor.dashboard': {
    routeKey: 'monitor.dashboard',
    component: 'MonitorDashboard',
    defaultPath: '/monitor/dashboard',
    name: 'MonitorDashboard',
    meta: { title: '监控总览', keepAlive: true }
  },
  'monitor.risk-alert': {
    routeKey: 'monitor.risk-alert',
    component: 'MonitorRiskAlert',
    defaultPath: '/monitor/risk-alert',
    name: 'MonitorRiskAlert',
    meta: { title: '风险预警', keepAlive: true }
  },

  // 数据分析
  'analytics.overview': {
    routeKey: 'analytics.overview',
    component: 'AnalyticsOverview',
    defaultPath: '/analytics/overview',
    name: 'AnalyticsOverview',
    meta: { title: '数据概览', keepAlive: true }
  }
}

/**
 * 路由映射管理器
 */
export class RouteMapper {
  private static instance: RouteMapper
  private routeKeyToPathMap = new Map<string, string>()
  private pathToRouteKeyMap = new Map<string, string>()

  private constructor() {
    this.initializeMappings()
  }

  public static getInstance(): RouteMapper {
    if (!RouteMapper.instance) {
      RouteMapper.instance = new RouteMapper()
    }
    return RouteMapper.instance
  }

  /**
   * 初始化路由映射
   */
  private initializeMappings(): void {
    Object.values(ROUTE_MAPPINGS).forEach(mapping => {
      if (mapping.defaultPath) {
        this.routeKeyToPathMap.set(mapping.routeKey, mapping.defaultPath)
        this.pathToRouteKeyMap.set(mapping.defaultPath, mapping.routeKey)
      }
    })
  }

  /**
   * 根据路由标识符获取路由配置
   */
  public getRouteByKey(routeKey: string): RouteMapping | null {
    return ROUTE_MAPPINGS[routeKey] || null
  }

  /**
   * 根据路径获取路由标识符
   */
  public getRouteKeyByPath(path: string): string | null {
    return this.pathToRouteKeyMap.get(path) || null
  }

  /**
   * 根据路由标识符获取当前访问路径
   */
  public getPathByRouteKey(routeKey: string): string | null {
    return this.routeKeyToPathMap.get(routeKey) || null
  }

  /**
   * 更新路由映射（当菜单结构调整时）
   */
  public updateRouteMapping(routeKey: string, newPath: string): void {
    const oldPath = this.routeKeyToPathMap.get(routeKey)
    if (oldPath) {
      this.pathToRouteKeyMap.delete(oldPath)
    }
    
    this.routeKeyToPathMap.set(routeKey, newPath)
    this.pathToRouteKeyMap.set(newPath, routeKey)
    
    console.log(`🔄 [路由映射] 更新路由映射: ${routeKey} -> ${newPath}`)
  }

  /**
   * 批量更新路由映射
   */
  public batchUpdateMappings(menuList: AppRouteRecord[]): void {
    console.log('🔄 [路由映射] 开始批量更新路由映射')
    this.traverseMenuAndUpdateMappings(menuList)
    console.log('✅ [路由映射] 路由映射更新完成')
  }

  /**
   * 遍历菜单并更新映射
   */
  private traverseMenuAndUpdateMappings(menuList: AppRouteRecord[], parentPath = ''): void {
    menuList.forEach(menu => {
      const fullPath = this.buildFullPath(parentPath, menu.path || '')
      
      // 如果菜单有路由标识符，更新映射
      if (menu.meta?.routeKey) {
        this.updateRouteMapping(menu.meta.routeKey, fullPath)
      }
      
      // 递归处理子菜单
      if (menu.children?.length) {
        this.traverseMenuAndUpdateMappings(menu.children, fullPath)
      }
    })
  }

  /**
   * 构建完整路径
   */
  private buildFullPath(parentPath: string, childPath: string): string {
    if (!childPath) return parentPath
    if (childPath.startsWith('/')) return childPath
    return parentPath ? `${parentPath}/${childPath}` : `/${childPath}`
  }

  /**
   * 获取所有路由映射
   */
  public getAllMappings(): Record<string, RouteMapping> {
    return { ...ROUTE_MAPPINGS }
  }

  /**
   * 检查路由是否存在
   */
  public hasRoute(routeKey: string): boolean {
    return routeKey in ROUTE_MAPPINGS
  }

  /**
   * 获取路由的默认路径（用于向后兼容）
   */
  public getDefaultPath(routeKey: string): string | null {
    const mapping = ROUTE_MAPPINGS[routeKey]
    return mapping?.defaultPath || null
  }
}

/**
 * 导出单例实例
 */
export const routeMapper = RouteMapper.getInstance()

/**
 * 路由映射工具函数
 */
export const routeMappingUtils = {
  /**
   * 根据路由标识符创建路由配置
   */
  createRouteFromKey(routeKey: string, customPath?: string): AppRouteRecord | null {
    const mapping = routeMapper.getRouteByKey(routeKey)
    if (!mapping) return null

    const component = RoutesAlias[mapping.component as keyof typeof RoutesAlias]
    
    return {
      name: mapping.name,
      path: customPath || mapping.defaultPath || '',
      component,
      meta: {
        ...mapping.meta,
        routeKey: mapping.routeKey
      }
    }
  },

  /**
   * 检查路径是否需要重定向
   */
  checkRedirect(path: string): string | null {
    const routeKey = routeMapper.getRouteKeyByPath(path)
    if (!routeKey) return null

    const currentPath = routeMapper.getPathByRouteKey(routeKey)
    return currentPath !== path ? currentPath : null
  }
}
