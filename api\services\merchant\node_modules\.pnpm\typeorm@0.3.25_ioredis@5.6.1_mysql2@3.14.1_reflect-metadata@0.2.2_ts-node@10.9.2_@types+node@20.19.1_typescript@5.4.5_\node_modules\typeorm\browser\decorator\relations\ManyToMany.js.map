{"version": 3, "sources": ["../browser/src/decorator/relations/ManyToMany.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AAuBpD;;;;GAIG;AACH,MAAM,UAAU,UAAU,CACtB,oBAA8D,EAC9D,oBAAsE,EACtE,OAAyB;IAEzB,uBAAuB;IACvB,IAAI,mBAAkD,CAAA;IACtD,IAAI,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;QAC7C,OAAO,GAAoB,oBAAoB,CAAA;IACnD,CAAC;SAAM,CAAC;QACJ,mBAAmB,GAAG,oBAA2B,CAAA;IACrD,CAAC;IAED,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAI,CAAC,OAAO;YAAE,OAAO,GAAG,EAAqB,CAAA;QAE7C,4CAA4C;QAC5C,IAAI,MAAM,GAAG,OAAO,CAAC,IAAI,KAAK,IAAI,CAAA;QAClC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAK,OAAe,CAAC,WAAW,EAAE,CAAC;YACrD,0BAA0B;YAC1B,MAAM,aAAa,GAAI,OAAe,CAAC,WAAW,CAC9C,aAAa,EACb,MAAM,EACN,YAAY,CACf,CAAA;YACD,IACI,aAAa;gBACb,OAAO,aAAa,CAAC,IAAI,KAAK,QAAQ;gBACtC,aAAa,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,SAAS;gBAE9C,MAAM,GAAG,IAAI,CAAA;QACrB,CAAC;QAED,sBAAsB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;YACpC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,+BAA+B;YAC/B,YAAY,EAAE,cAAc;YAC5B,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,oBAAoB;YAC1B,mBAAmB,EAAE,mBAAmB;YACxC,OAAO,EAAE,OAAO;SACK,CAAC,CAAA;IAC9B,CAAC,CAAA;AACL,CAAC", "file": "ManyToMany.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { RelationMetadataArgs } from \"../../metadata-args/RelationMetadataArgs\"\nimport { ObjectType } from \"../../common/ObjectType\"\nimport { RelationOptions } from \"../options/RelationOptions\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * Many-to-many is a type of relationship when Entity1 can have multiple instances of Entity2, and Entity2 can have\n * multiple instances of Entity1. To achieve it, this type of relation creates a junction table, where it storage\n * entity1 and entity2 ids. This is owner side of the relationship.\n */\nexport function ManyToMany<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    options?: RelationOptions,\n): PropertyDecorator\n\n/**\n * Many-to-many is a type of relationship when Entity1 can have multiple instances of Entity2, and Entity2 can have\n * multiple instances of Entity1. To achieve it, this type of relation creates a junction table, where it storage\n * entity1 and entity2 ids. This is owner side of the relationship.\n */\nexport function ManyToMany<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSide?: string | ((object: T) => any),\n    options?: RelationOptions,\n): PropertyDecorator\n\n/**\n * Many-to-many is a type of relationship when Entity1 can have multiple instances of Entity2, and Entity2 can have\n * multiple instances of Entity1. To achieve it, this type of relation creates a junction table, where it storage\n * entity1 and entity2 ids. This is owner side of the relationship.\n */\nexport function ManyToMany<T>(\n    typeFunctionOrTarget: string | ((type?: any) => ObjectType<T>),\n    inverseSideOrOptions?: string | ((object: T) => any) | RelationOptions,\n    options?: RelationOptions,\n): PropertyDecorator {\n    // normalize parameters\n    let inverseSideProperty: string | ((object: T) => any)\n    if (ObjectUtils.isObject(inverseSideOrOptions)) {\n        options = <RelationOptions>inverseSideOrOptions\n    } else {\n        inverseSideProperty = inverseSideOrOptions as any\n    }\n\n    return function (object: Object, propertyName: string) {\n        if (!options) options = {} as RelationOptions\n\n        // now try to determine it its lazy relation\n        let isLazy = options.lazy === true\n        if (!isLazy && Reflect && (Reflect as any).getMetadata) {\n            // automatic determination\n            const reflectedType = (Reflect as any).getMetadata(\n                \"design:type\",\n                object,\n                propertyName,\n            )\n            if (\n                reflectedType &&\n                typeof reflectedType.name === \"string\" &&\n                reflectedType.name.toLowerCase() === \"promise\"\n            )\n                isLazy = true\n        }\n\n        getMetadataArgsStorage().relations.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            // propertyType: reflectedType,\n            relationType: \"many-to-many\",\n            isLazy: isLazy,\n            type: typeFunctionOrTarget,\n            inverseSideProperty: inverseSideProperty,\n            options: options,\n        } as RelationMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}