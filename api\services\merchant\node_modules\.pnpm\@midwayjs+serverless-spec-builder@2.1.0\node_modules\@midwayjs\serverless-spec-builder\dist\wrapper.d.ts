export declare function writeWrapper(options: {
    service: any;
    baseDir: string;
    distDir: string;
    starter: string;
    cover?: boolean;
    loadDirectory?: string[];
    initializeName?: string;
    faasModName?: string;
    advancePreventMultiInit?: boolean;
    faasStarterName?: string;
    middleware?: string[];
    clearCache?: boolean;
    moreArgs?: boolean;
    specificStarterName?: string;
    preloadModules?: string[];
    templatePath?: string;
    preloadFile?: string;
    moreTemplateVariables?: any;
    isDefaultFunc?: boolean;
    aggregationBeforeExecScript?: string;
    initializeInHandler?: boolean;
}): void;
export declare function formatAggregationHandlers(handlers: any): any;
//# sourceMappingURL=wrapper.d.ts.map