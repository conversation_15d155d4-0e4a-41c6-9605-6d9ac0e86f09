#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/cli/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/cli/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/cli/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/cli/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules/@midwayjs/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@midwayjs/cli/bin/midway-bin.js" "$@"
else
  exec node  "$basedir/../@midwayjs/cli/bin/midway-bin.js" "$@"
fi
