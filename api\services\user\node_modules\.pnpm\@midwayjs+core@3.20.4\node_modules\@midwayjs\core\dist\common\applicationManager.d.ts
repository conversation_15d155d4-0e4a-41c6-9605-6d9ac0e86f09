import { IMidwayApplication, IMidwayFramework, FrameworkType } from '../interface';
export declare class MidwayApplicationManager {
    private globalFrameworkMap;
    private globalFrameworkTypeMap;
    addFramework(namespace: any, framework: IMidwayFramework<any, any, any>): void;
    getFramework(namespaceOrFrameworkType: string | FrameworkType): IMidwayFramework<any, any, any, unknown, unknown>;
    getApplication(namespaceOrFrameworkType: string | FrameworkType): IMidwayApplication;
    getApplications(namespaces?: Array<string | FrameworkType>): IMidwayApplication[];
}
//# sourceMappingURL=applicationManager.d.ts.map