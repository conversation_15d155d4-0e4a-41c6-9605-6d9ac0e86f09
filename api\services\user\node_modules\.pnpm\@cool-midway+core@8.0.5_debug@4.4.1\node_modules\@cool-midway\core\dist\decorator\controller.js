"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.apiDesc = void 0;
exports.CoolController = CoolController;
const core_1 = require("@midwayjs/core");
const fs = require("fs");
const _ = require("lodash");
const location_1 = require("../util/location");
// COOL的装饰器
function CoolController(curdOption, routerOptions = { middleware: [], sensitive: true }) {
    return (target) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, core_1.saveModule)(core_1.CONTROLLER_KEY, target);
        let prefix;
        if (curdOption) {
            // 判断 curdOption 的类型
            if (typeof curdOption === 'string') {
                prefix = curdOption;
            }
            else if (curdOption && 'api' in curdOption) {
                // curdOption 是 CurdOption 类型
                prefix = curdOption.prefix || '';
            }
            else {
                // curdOption 是 RouterOptions 类型 合并到 routerOptions
                routerOptions = { ...curdOption, ...routerOptions };
            }
        }
        // 如果不存在路由前缀，那么自动根据当前文件夹路径
        location_1.default.scriptPath(target).then(async (res) => {
            if (!(res === null || res === void 0 ? void 0 : res.path))
                return;
            const pathSps = res.path.split('.');
            const paths = pathSps[pathSps.length - 2].split(/[/\\]/);
            const pathArr = [];
            let module = null;
            for (const path of paths.reverse()) {
                if (path != 'controller' && !module) {
                    pathArr.push(path);
                }
                if (path == 'controller' && !paths.includes('modules')) {
                    break;
                }
                if (path == 'controller' && paths.includes('modules')) {
                    module = 'ready';
                }
                if (module && path != 'controller') {
                    module = `${path}`;
                    break;
                }
            }
            if (module) {
                pathArr.reverse();
                pathArr.splice(1, 0, module);
                // 追加模块中间件
                const path = `${res.path.split(new RegExp(`modules[/\\\\]${module}`))[0]}modules/${module}/config.${_.endsWith(res.path, 'ts') ? 'ts' : 'js'}`;
                if (fs.existsSync(path)) {
                    const config = require(path).default();
                    routerOptions.middleware = (config.middlewares || []).concat(routerOptions.middleware || []);
                }
            }
            if (!prefix) {
                prefix = `/${pathArr.join('/')}`;
            }
            saveMetadata(prefix, routerOptions, target, curdOption, module);
        });
    };
}
exports.apiDesc = {
    add: '新增',
    delete: '删除',
    update: '修改',
    page: '分页查询',
    list: '列表查询',
    info: '单个信息',
};
// 保存一些元数据信息，任意你希望存的东西
function saveMetadata(prefix, routerOptions, target, curdOption, module) {
    if (module && !routerOptions.tagName) {
        routerOptions = routerOptions || {};
        routerOptions.tagName = module;
    }
    (0, core_1.saveClassMetadata)(core_1.CONTROLLER_KEY, {
        prefix,
        routerOptions,
        curdOption,
        module,
    }, target);
    // 追加CRUD路由
    if (!_.isEmpty(curdOption === null || curdOption === void 0 ? void 0 : curdOption.api)) {
        // 获取已存在的路由
        const existingRoutes = (0, core_1.getClassMetadata)(core_1.WEB_ROUTER_KEY, target) || [];
        const existingPaths = existingRoutes.map(route => route.path);
        curdOption === null || curdOption === void 0 ? void 0 : curdOption.api.forEach(path => {
            const routePath = `/${path}`;
            // 检查路由是否已存在
            if (!existingPaths.includes(routePath)) {
                (0, core_1.attachClassMetadata)(core_1.WEB_ROUTER_KEY, {
                    path: routePath,
                    requestMethod: path == 'info' ? 'get' : 'post',
                    method: path,
                    summary: exports.apiDesc[path] || path,
                    description: '',
                }, target);
            }
        });
    }
    if (!_.isEmpty(curdOption === null || curdOption === void 0 ? void 0 : curdOption.serviceApis)) {
        // 获取已存在的路由
        const existingRoutes = (0, core_1.getClassMetadata)(core_1.WEB_ROUTER_KEY, target) || [];
        const existingPaths = existingRoutes.map(route => route.path);
        curdOption.serviceApis.forEach(api => {
            const methodName = typeof api === 'string' ? api : api.method;
            const routePath = `/${methodName}`;
            // 检查路由是否已存在
            if (!existingPaths.includes(routePath)) {
                (0, core_1.attachClassMetadata)(core_1.WEB_ROUTER_KEY, {
                    path: routePath,
                    requestMethod: 'post',
                    method: methodName,
                    summary: typeof api === 'string' ? api : api.summary,
                    description: '',
                }, target);
            }
        });
    }
    (0, core_1.Scope)(core_1.ScopeEnum.Request)(target);
}
