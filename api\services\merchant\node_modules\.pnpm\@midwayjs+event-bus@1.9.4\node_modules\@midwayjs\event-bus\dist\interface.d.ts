/// <reference types="node" />
/// <reference types="node" />
import type { Worker } from 'worker_threads';
import type { ChildProcess } from 'child_process';
export declare enum MessageType {
    /**
     * worker => main
     */
    Inited = "inited",
    /**
     * main => worker
     */
    Request = "request",
    /**
     * worker => main
     */
    Response = "response",
    /**
     * publish async: main => worker
     */
    Invoke = "invoke",
    /**
     * broadcast to all workers, or except the specified worker
     */
    Broadcast = "broadcast"
}
export declare enum ListenerType {
    Inited = "inited",
    Request = "request",
    Subscribe = "Subscribe",
    WorkerChanged = "worker_changed",
    Error = "error"
}
export declare enum MessageCategory {
    IN = "in",
    OUT = "out"
}
export type Message<BODY = any> = {
    messageId: string;
    workerId: string;
    type: MessageType;
    error?: {
        name: string;
        message: string;
        stack: string;
    };
    body: BODY;
    messageOptions?: PublishOptions | BroadcastOptions;
};
export type EventCenterMessage = {
    messageCategory: MessageCategory;
    message: Message;
};
export interface EventBusOptions<Worker> {
    initTimeout?: number;
    initTimeoutCheckInterval?: number;
    isWorker?: boolean;
    /**
     * custom worker dispatcher
     */
    dispatchStrategy?: (workers: Worker[], dispatchToken: any) => Worker | undefined;
}
export interface LocalEventBusOptions extends EventBusOptions<any> {
    waitWorkerTimeout?: number;
    waitWorkerCheckInterval?: number;
}
export interface ThreadEventBusOptions extends EventBusOptions<Worker> {
    encoder?: (message: Message) => any;
    decoder?: (serializedData: any) => Message;
}
export type ChildProcessEventBusOptions = EventBusOptions<ChildProcess>;
export interface WaitCheckOptions {
    timeout?: number;
    timeoutCheckInterval?: number;
    ErrorClz?: new (...args: any[]) => Error;
}
export interface PublishOptions {
    relatedMessageId?: string;
    targetWorkerId?: string;
    topic?: string;
    timeout?: number;
    isChunk?: boolean;
    /**
     * dispatch strategy will be selected according to this token
     */
    dispatchToken?: any;
}
export interface BroadcastOptions {
    /**
     * default false
     */
    includeSelfFromWorker?: boolean;
    /**
     * default false
     */
    includeMainFromWorker?: boolean;
    relatedMessageId?: string;
    relatedWorkerId?: string;
    topic?: string;
}
export interface SubscribeOptions {
    topic?: string;
    subscribeOnce?: boolean;
}
export type SubscribeTopicListener = (message: Message, responder?: IResponder) => void | Promise<void>;
export interface IEventBus<T> {
    addWorker(worker: T): any;
    start(err?: Error): Promise<void>;
    subscribe(callback: SubscribeTopicListener, options?: SubscribeOptions): void;
    subscribeOnce(callback: SubscribeTopicListener, options?: SubscribeOptions): void;
    publishAsync<ResData>(data: unknown, publishOptions?: PublishOptions): Promise<ResData>;
    publishChunk<ResData = unknown>(data: unknown, publishOptions?: PublishOptions): AsyncIterable<ResData>;
    publish(data: unknown, publishOptions?: PublishOptions): void;
    broadcast(data: unknown, options?: BroadcastOptions): void;
    isMain(): boolean;
    isWorker(): boolean;
    getWorkerId(worker: T): string;
    stop(): Promise<void>;
    onInited(listener: (message: Message) => void): any;
    onPublish(listener: (message: Message) => void): any;
    onSubscribe(listener: (message: Message) => void): any;
    onError(listener: (err: Error) => void): any;
}
export interface IResponder {
    end(data?: unknown): void;
    send(data: unknown): void;
    error(err: Error): void;
    isEnd(): boolean;
}
//# sourceMappingURL=interface.d.ts.map