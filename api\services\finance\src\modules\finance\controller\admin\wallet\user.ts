import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceWalletUserEntity } from '../../../entity/wallet/user';
import { UserInfoEntity } from '../../../../user/entity/info';
import { FinanceWalletUserService } from '../../../service/wallet/user';

/**
 * 用户钱包
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: FinanceWalletUserEntity,
  service: FinanceWalletUserService,
  pageQueryOp: {
    keyWordLikeFields: ['b.nickName'],
    select: ['a.*', 'b.nickName as userName', 'b.avatarUrl'],
    join: [
      {
        entity: UserInfoEntity,
        alias: 'b',
        condition: 'b.id = a.userId',
      },
    ],
  },
})
export class AdminFinanceWalletUserController extends BaseController {}
