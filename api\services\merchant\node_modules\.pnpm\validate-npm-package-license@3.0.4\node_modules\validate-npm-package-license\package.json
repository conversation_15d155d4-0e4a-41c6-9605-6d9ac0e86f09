{"name": "validate-npm-package-license", "description": "Give me a string and I'll tell you if it's a valid npm package license string", "version": "3.0.4", "author": "<PERSON> <<EMAIL>> (https://kemitchell.com)", "contributors": ["<PERSON> <markj<PERSON><PERSON>@gmail.com>"], "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}, "devDependencies": {"defence-cli": "^2.0.1", "replace-require-self": "^1.0.0"}, "keywords": ["license", "npm", "package", "validation"], "license": "Apache-2.0", "repository": "kemitchell/validate-npm-package-license.js", "scripts": {"test": "defence README.md | replace-require-self | node"}}