"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.typeCheck = void 0;
const ts = require("typescript");
const path_1 = require("path");
const typeCheck = cwd => {
    var _a;
    const tsconfigPath = ts.findConfigFile(cwd, ts.sys.fileExists);
    const { config } = ts.readConfigFile(tsconfigPath, ts.sys.readFile);
    const parsedCommandLine = ts.parseJsonConfigFileContent(config, ts.sys, cwd);
    const compilerOptions = {
        ...parsedCommandLine.options,
    };
    const host = ts.createCompilerHost(compilerOptions, true);
    const program = ts.createProgram(parsedCommandLine.fileNames, compilerOptions, host);
    const allDiagnostics = ts.getPreEmitDiagnostics(program);
    const errors = allDiagnostics.filter(diagnostic => {
        return diagnostic.category === ts.DiagnosticCategory.Error;
    });
    if (!Array.isArray(errors) || !errors.length) {
        return;
    }
    for (const error of errors) {
        const errorPath = error.file && error.file.fileName
            ? `(${(0, path_1.relative)(cwd, error.file.fileName)})`
            : '';
        const message = ((_a = error.messageText) === null || _a === void 0 ? void 0 : _a.messageText) || error.messageText;
        throw new Error(`TS Error: ${message || 'unknown error'}${errorPath}`);
    }
};
exports.typeCheck = typeCheck;
//# sourceMappingURL=typeCheck.js.map