{"version": 3, "file": "bindNodeCallback.js", "sources": ["../../../src/internal/observable/bindNodeCallback.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAG/C,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,cAAc,EAAE,MAAM,wBAAwB,CAAC;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAoJ1C,MAAM,UAAU,gBAAgB,CAC9B,YAAsB,EACtB,cAAsC,EACtC,SAAyB;IAGzB,IAAI,cAAc,EAAE;QAClB,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE;YAC/B,SAAS,GAAG,cAAc,CAAC;SAC5B;aAAM;YAEL,OAAO,CAAC,GAAG,IAAW,EAAE,EAAE,CAAC,gBAAgB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAChF,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAC5E,CAAC;SACH;KACF;IAED,OAAO,UAAoB,GAAG,IAAW;QACvC,MAAM,MAAM,GAAmB;YAC7B,OAAO,EAAE,SAAS;YAClB,IAAI;YACJ,YAAY;YACZ,SAAS;YACT,OAAO,EAAE,IAAI;SACd,CAAC;QACF,OAAO,IAAI,UAAU,CAAI,UAAU,CAAC,EAAE;YACpC,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;YAC3B,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;YACzB,IAAI,CAAC,SAAS,EAAE;gBACd,IAAI,CAAC,OAAO,EAAE;oBACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;oBACjD,MAAM,OAAO,GAAG,CAAC,GAAG,SAAgB,EAAE,EAAE;wBACtC,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;wBAE9B,IAAI,GAAG,EAAE;4BACP,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;4BACnB,OAAO;yBACR;wBAED,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;wBAC/D,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACrB,CAAC,CAAC;oBAEF,IAAI;wBACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;qBACjD;oBAAC,OAAO,GAAG,EAAE;wBACZ,IAAI,cAAc,CAAC,OAAO,CAAC,EAAE;4BAC3B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;yBACpB;6BAAM;4BACL,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;yBACnB;qBACF;iBACF;gBACD,OAAO,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;aACtC;iBAAM;gBACL,OAAO,SAAS,CAAC,QAAQ,CAAmB,QAAQ,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,CAAC,CAAC;aAC3F;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAgBD,SAAS,QAAQ,CAA6C,KAAuB;IACnF,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;IAC9C,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;IACjD,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;IAE7B,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,YAAY,EAAK,CAAC;QAEjD,MAAM,OAAO,GAAG,CAAC,GAAG,SAAgB,EAAE,EAAE;YACtC,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,EAAE,CAAC;YAC9B,IAAI,GAAG,EAAE;gBACP,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAsB,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aACvF;iBAAM;gBACL,MAAM,KAAK,GAAG,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC/D,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAqB,YAAY,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;aACvF;QACH,CAAC,CAAC;QAEF,IAAI;YACF,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;SACjD;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,QAAQ,CAAsB,aAAa,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;SACvF;KACF;IAED,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC;AAC1C,CAAC;AAOD,SAAS,YAAY,CAAI,GAAuB;IAC9C,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;IAC/B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACpB,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrB,CAAC;AAOD,SAAS,aAAa,CAAI,GAAwB;IAChD,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;IAC7B,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrB,CAAC"}