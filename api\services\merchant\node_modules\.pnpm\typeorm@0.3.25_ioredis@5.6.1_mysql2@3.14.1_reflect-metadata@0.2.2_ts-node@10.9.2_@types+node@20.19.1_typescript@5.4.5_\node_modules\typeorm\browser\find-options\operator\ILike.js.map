{"version": 3, "sources": ["../browser/src/find-options/operator/ILike.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,KAAK,CAAI,KAA0B;IAC/C,OAAO,IAAI,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;AAC3C,CAAC", "file": "ILike.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: ILike(\"%SOME string%\") }\n */\nexport function ILike<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"ilike\", value)\n}\n"], "sourceRoot": "../.."}