{"version": 3, "sources": ["../../src/driver/sqlserver/authentication/AzureActiveDirectoryMsiAppServiceAuthentication.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryMsiAppServiceAuthentication.js", "sourcesContent": ["export interface AzureActiveDirectoryMsiAppServiceAuthentication {\n    type: \"azure-active-directory-msi-app-service\"\n    options: {\n        /**\n         * If you user want to connect to an Azure app service using a specific client account\n         * they need to provide `clientId` associate to their created identity.\n         *\n         * This is optional for retrieve token from azure web app service\n         */\n        clientId?: string\n        /**\n         * A msi app service environment need to provide `msiEndpoint` for retriving the accesstoken.\n         */\n        msiEndpoint?: string\n        /**\n         * A msi app service environment need to provide `msiSecret` for retrieved the accesstoken.\n         */\n        msiSecret?: string\n    }\n}\n"], "sourceRoot": "../../.."}