/**
 * Location 工具类
 */
export declare class LocationUtil {
    private locationCache;
    distPath: string;
    init(): Promise<void>;
    /**
     * 获取编译后的文件路径
     * @returns 编译后的文件路径
     */
    getDistPath(): string;
    /**
     * 获取目标类的定义位置
     * @param target 目标类
     * @returns 目标类的定义位置
     */
    scriptPath(target: any): Promise<any>;
    /**
     * 获取使用此包的项目的真实根目录路径
     * @returns 项目根目录的绝对路径
     */
    getRunPath(): string;
}
declare const _default: LocationUtil;
export default _default;
