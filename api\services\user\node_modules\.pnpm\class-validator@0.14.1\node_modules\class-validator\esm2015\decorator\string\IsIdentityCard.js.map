{"version": 3, "file": "IsIdentityCard.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsIdentityCard.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,uBAAuB,MAAM,8BAA8B,CAAC;AAGnE,MAAM,CAAC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC;AAEjD;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAAC,KAAc,EAAE,MAAsC;IACnF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,cAAc,CAC5B,MAAuC,EACvC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,gBAAgB;QACtB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/E,cAAc,EAAE,YAAY,CAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,0CAA0C,EACrE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isIdentityCardValidator from 'validator/lib/isIdentityCard';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_IDENTITY_CARD = 'isIdentityCard';\n\n/**\n * Check if the string is a valid identity card code.\n * locale is one of ['ES', 'zh-TW', 'he-IL', 'ar-TN'] OR 'any'. If 'any' is used, function will check if any of the locals match.\n * Defaults to 'any'.\n * If given value is not a string, then it returns false.\n */\nexport function isIdentityCard(value: unknown, locale: ValidatorJS.IdentityCardLocale): boolean {\n  return typeof value === 'string' && isIdentityCardValidator(value, locale);\n}\n\n/**\n * Check if the string is a valid identity card code.\n * locale is one of ['ES', 'zh-TW', 'he-IL', 'ar-TN'] OR 'any'. If 'any' is used, function will check if any of the locals match.\n * Defaults to 'any'.\n * If given value is not a string, then it returns false.\n */\nexport function IsIdentityCard(\n  locale?: ValidatorJS.IdentityCardLocale,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_IDENTITY_CARD,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isIdentityCard(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a identity card number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}