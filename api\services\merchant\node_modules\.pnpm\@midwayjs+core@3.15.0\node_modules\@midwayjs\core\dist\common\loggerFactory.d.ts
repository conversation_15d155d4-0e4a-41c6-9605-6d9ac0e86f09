import { ILogger, MidwayAppInfo } from '../interface';
export declare abstract class LoggerFactory<Logger extends ILogger, LoggerOptions> {
    abstract createLogger(name: string, options: LoggerOptions): Logger;
    abstract getLogger(loggerName: string): Logger;
    abstract close(loggerName?: string): any;
    abstract removeLogger(loggerName: string): any;
    abstract getDefaultMidwayLoggerConfig(appInfo: MidwayAppInfo): {
        midwayLogger: {
            default?: LoggerOptions;
            clients?: {
                [loggerName: string]: LoggerOptions;
            };
        };
    };
    abstract createContextLogger(ctx: any, appLogger: ILogger, contextOptions?: any): ILogger;
}
export declare class DefaultConsoleLoggerFactory implements LoggerFactory<ILogger, any> {
    private instance;
    createLogger(name: string, options: any): ILogger;
    getLogger(loggerName: string): ILogger;
    close(loggerName?: string): void;
    removeLogger(loggerName: string): void;
    getDefaultMidwayLoggerConfig(): {
        midwayLogger: {
            default?: any;
            clients?: {
                [p: string]: any;
            };
        };
    };
    createContextLogger(ctx: any, appLogger: ILogger, contextOptions?: any): ILogger;
    getClients(): Map<string, ILogger>;
    getClientKeys(): string[];
}
//# sourceMappingURL=loggerFactory.d.ts.map