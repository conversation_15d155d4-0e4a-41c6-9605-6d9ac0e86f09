{"name": "download", "version": "8.0.0", "description": "Download and extract files", "license": "MIT", "repository": "kevva/download", "author": {"email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "name": "<PERSON>", "url": "github.com/kevva"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["download", "extract", "http", "request", "url"], "dependencies": {"archive-type": "^4.0.0", "content-disposition": "^0.5.2", "decompress": "^4.2.1", "ext-name": "^5.0.0", "file-type": "^11.1.0", "filenamify": "^3.0.0", "get-stream": "^4.1.0", "got": "^8.3.1", "make-dir": "^2.1.0", "p-event": "^2.1.0", "pify": "^4.0.1"}, "devDependencies": {"ava": "^1.4.1", "is-zip": "^1.0.0", "nock": "^10.0.6", "path-exists": "^3.0.0", "random-buffer": "^0.1.0", "rimraf": "^3.0.0", "xo": "^0.24.0"}}