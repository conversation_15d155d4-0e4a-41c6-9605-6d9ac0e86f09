"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Utils = exports.createPromiseTimeoutInvokeChain = exports.isTypeScriptEnvironment = exports.toAsyncFunction = exports.merge = exports.generateRandomId = exports.getParamNames = exports.sleep = exports.wrapAsync = exports.isIncludeProperty = exports.wrapMiddleware = exports.pathMatching = exports.toPathMatch = exports.transformRequestObjectByType = exports.deprecatedOutput = exports.getCurrentDateString = exports.delegateTargetProperties = exports.delegateTargetMethod = exports.delegateTargetAllPrototypeMethod = exports.delegateTargetPrototypeMethod = exports.joinURLPath = exports.getUserHome = exports.parsePrefix = exports.safelyGet = exports.loadModule = exports.safeRequire = exports.getCurrentEnvironment = exports.isDevelopmentEnvironment = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
const util_1 = require("util");
const transformer = require("class-transformer");
const pathToRegexp_1 = require("./pathToRegexp");
const error_1 = require("../error");
const camelCase_1 = require("./camelCase");
const uuid_1 = require("./uuid");
const flatted_1 = require("./flatted");
const crypto = require("crypto");
const types_1 = require("./types");
const url_1 = require("url");
const debug = (0, util_1.debuglog)('midway:debug');
/**
 * @since 2.0.0
 * @param env
 */
const isDevelopmentEnvironment = env => {
    return ['local', 'test', 'unittest'].includes(env);
};
exports.isDevelopmentEnvironment = isDevelopmentEnvironment;
/**
 * @since 2.0.0
 */
const getCurrentEnvironment = () => {
    return process.env['MIDWAY_SERVER_ENV'] || process.env['NODE_ENV'] || 'prod';
};
exports.getCurrentEnvironment = getCurrentEnvironment;
/**
 * @param p
 * @param enabledCache
 * @since 2.0.0
 */
const safeRequire = (p, enabledCache = true) => {
    if (p.startsWith(`.${path_1.sep}`) || p.startsWith(`..${path_1.sep}`)) {
        p = (0, path_1.resolve)((0, path_1.dirname)(module.parent.filename), p);
    }
    try {
        if (enabledCache) {
            return require(p);
        }
        else {
            const content = (0, fs_1.readFileSync)(p, {
                encoding: 'utf-8',
            });
            return JSON.parse(content);
        }
    }
    catch (err) {
        debug(`[core]: SafeRequire Warning\n\n${err.message}\n`);
        return undefined;
    }
};
exports.safeRequire = safeRequire;
const innerLoadModuleCache = {};
/**
 * load module, and it can be chosen commonjs or esm mode
 * @param p
 * @param options
 * @since 3.12.0
 */
const loadModule = async (p, options = {}) => {
    var _a, _b, _c;
    options.enableCache = (_a = options.enableCache) !== null && _a !== void 0 ? _a : true;
    options.safeLoad = (_b = options.safeLoad) !== null && _b !== void 0 ? _b : false;
    options.loadMode = (_c = options.loadMode) !== null && _c !== void 0 ? _c : 'commonjs';
    if (p.startsWith(`.${path_1.sep}`) || p.startsWith(`..${path_1.sep}`)) {
        p = (0, path_1.resolve)((0, path_1.dirname)(module.parent.filename), p);
    }
    debug(`[core]: load module ${p}, cache: ${options.enableCache}, mode: ${options.loadMode}, safeLoad: ${options.safeLoad}`);
    try {
        if (options.enableCache) {
            if (options.loadMode === 'commonjs') {
                return require(p);
            }
            else {
                // if json file, import need add options
                if (p.endsWith('.json')) {
                    /**
                     * attention: import json not support under nodejs 16
                     * use readFileSync instead
                     */
                    if (!innerLoadModuleCache[p]) {
                        // return (await import(p, { assert: { type: 'json' } })).default;
                        const content = (0, fs_1.readFileSync)(p, {
                            encoding: 'utf-8',
                        });
                        innerLoadModuleCache[p] = JSON.parse(content);
                    }
                    return innerLoadModuleCache[p];
                }
                else {
                    return await import((0, url_1.pathToFileURL)(p).href);
                }
            }
        }
        else {
            const content = (0, fs_1.readFileSync)(p, {
                encoding: 'utf-8',
            });
            return JSON.parse(content);
        }
    }
    catch (err) {
        if (!options.safeLoad) {
            throw err;
        }
        else {
            debug(`[core]: SafeLoadModule Warning\n\n${err.message}\n`);
            return undefined;
        }
    }
};
exports.loadModule = loadModule;
/**
 *  @example
 *  safelyGet(['a','b'],{a: {b: 2}})  // => 2
 *  safelyGet(['a','b'],{c: {b: 2}})  // => undefined
 *  safelyGet(['a','1'],{a: {"1": 2}})  // => 2
 *  safelyGet(['a','1'],{a: {b: 2}})  // => undefined
 *  safelyGet('a.b',{a: {b: 2}})  // => 2
 *  safelyGet('a.b',{c: {b: 2}})  // => undefined
 *  @since 2.0.0
 */
function safelyGet(list, obj) {
    if (arguments.length === 1) {
        return (_obj) => safelyGet(list, _obj);
    }
    if (typeof obj === 'undefined' || typeof obj !== 'object' || obj === null) {
        return void 0;
    }
    const pathArrValue = typeof list === 'string' ? list.split('.') : list;
    let willReturn = obj;
    for (const key of pathArrValue) {
        if (typeof willReturn === 'undefined' || willReturn === null) {
            return void 0;
        }
        else if (typeof willReturn !== 'object') {
            return void 0;
        }
        willReturn = willReturn[key];
    }
    return willReturn;
}
exports.safelyGet = safelyGet;
/**
 * 剔除 @ 符号
 * @param provideId provideId
 * @since 2.0.0
 */
function parsePrefix(provideId) {
    if (provideId.includes('@')) {
        return provideId.slice(1);
    }
    return provideId;
}
exports.parsePrefix = parsePrefix;
function getUserHome() {
    return process.env[process.platform === 'win32' ? 'USERPROFILE' : 'HOME'];
}
exports.getUserHome = getUserHome;
function joinURLPath(...strArray) {
    strArray = strArray.filter(item => !!item);
    if (strArray.length === 0) {
        return '';
    }
    let p = path_1.posix.join(...strArray);
    p = p.replace(/\/+$/, '');
    if (!/^\//.test(p)) {
        p = '/' + p;
    }
    return p;
}
exports.joinURLPath = joinURLPath;
/**
 * 代理目标所有的原型方法，不包括构造器和内部隐藏方法
 * @param derivedCtor
 * @param constructors
 * @param otherMethods
 * @since 2.0.0
 */
function delegateTargetPrototypeMethod(derivedCtor, constructors, otherMethods) {
    constructors.forEach(baseCtor => {
        if (baseCtor.prototype) {
            Object.getOwnPropertyNames(baseCtor.prototype).forEach(name => {
                if (name !== 'constructor' &&
                    !/^_/.test(name) &&
                    !derivedCtor.prototype[name]) {
                    derivedCtor.prototype[name] = function (...args) {
                        return this.instance[name](...args);
                    };
                }
            });
        }
    });
    if (otherMethods) {
        delegateTargetMethod(derivedCtor, otherMethods);
    }
}
exports.delegateTargetPrototypeMethod = delegateTargetPrototypeMethod;
/**
 * 代理目标所有的原型方法，包括原型链，不包括构造器和内部隐藏方法
 * @param derivedCtor
 * @param constructor
 * @since 3.0.0
 */
function delegateTargetAllPrototypeMethod(derivedCtor, constructor) {
    do {
        delegateTargetPrototypeMethod(derivedCtor, [constructor]);
        constructor = Object.getPrototypeOf(constructor);
    } while (constructor);
}
exports.delegateTargetAllPrototypeMethod = delegateTargetAllPrototypeMethod;
/**
 * 代理目标原型上的特定方法
 * @param derivedCtor
 * @param methods
 * @since 2.0.0
 */
function delegateTargetMethod(derivedCtor, methods) {
    methods.forEach(name => {
        derivedCtor.prototype[name] = function (...args) {
            return this.instance[name](...args);
        };
    });
}
exports.delegateTargetMethod = delegateTargetMethod;
/**
 * 代理目标原型属性
 * @param derivedCtor
 * @param properties
 * @since 2.0.0
 */
function delegateTargetProperties(derivedCtor, properties) {
    properties.forEach(name => {
        Object.defineProperty(derivedCtor.prototype, name, {
            get() {
                return this.instance[name];
            },
        });
    });
}
exports.delegateTargetProperties = delegateTargetProperties;
/**
 * 获取当前的时间戳
 * @since 2.0.0
 * @param timestamp
 */
const getCurrentDateString = (timestamp = Date.now()) => {
    const d = new Date(timestamp);
    return `${d.getFullYear()}-${(d.getMonth() + 1)
        .toString()
        .padStart(2, '0')}-${d.getDate().toString().padStart(2, '0')}`;
};
exports.getCurrentDateString = getCurrentDateString;
/**
 *
 * @param message
 * @since 3.0.0
 */
const deprecatedOutput = (message) => {
    console.warn('DeprecationWarning: ' + message);
};
exports.deprecatedOutput = deprecatedOutput;
/**
 * transform request object to definition type
 *
 * @param originValue
 * @param targetType
 * @since 3.0.0
 */
const transformRequestObjectByType = (originValue, targetType) => {
    if (targetType === undefined ||
        targetType === null ||
        targetType === Object ||
        typeof originValue === 'undefined') {
        return originValue;
    }
    switch (targetType) {
        case Number:
            return Number(originValue);
        case String:
            return String(originValue);
        case Boolean:
            if (originValue === '0' || originValue === 'false') {
                return false;
            }
            return Boolean(originValue);
        default:
            if (originValue instanceof targetType) {
                return originValue;
            }
            else {
                const transformToInstance = transformer['plainToClass'] || transformer['plainToInstance'];
                return transformToInstance(targetType, originValue);
            }
    }
};
exports.transformRequestObjectByType = transformRequestObjectByType;
function toPathMatch(pattern) {
    if (typeof pattern === 'boolean') {
        return ctx => pattern;
    }
    if (typeof pattern === 'string') {
        const reg = pathToRegexp_1.PathToRegexpUtil.toRegexp(pattern.replace('*', '(.*)'));
        if (reg.global)
            reg.lastIndex = 0;
        return ctx => reg.test(ctx.path);
    }
    if (pattern instanceof RegExp) {
        return ctx => {
            if (pattern.global)
                pattern.lastIndex = 0;
            return pattern.test(ctx.path);
        };
    }
    if (typeof pattern === 'function')
        return pattern;
    if (Array.isArray(pattern)) {
        const matchs = pattern.map(item => toPathMatch(item));
        return ctx => matchs.some(match => match(ctx));
    }
    throw new error_1.MidwayCommonError('match/ignore pattern must be RegExp, Array or String, but got ' + pattern);
}
exports.toPathMatch = toPathMatch;
function pathMatching(options) {
    options = options || {};
    if (options.match && options.ignore)
        throw new error_1.MidwayCommonError('options.match and options.ignore can not both present');
    if (!options.match && !options.ignore)
        return () => true;
    if (options.match && !Array.isArray(options.match)) {
        options.match = [options.match];
    }
    if (options.ignore && !Array.isArray(options.ignore)) {
        options.ignore = [options.ignore];
    }
    const createMatch = (ignoreMatcherArr) => {
        const matchedArr = ignoreMatcherArr.map(item => {
            if (options.thisResolver) {
                return toPathMatch(item).bind(options.thisResolver);
            }
            return toPathMatch(item);
        });
        return ctx => {
            for (let i = 0; i < matchedArr.length; i++) {
                const matched = matchedArr[i](ctx);
                if (matched) {
                    return true;
                }
            }
            return false;
        };
    };
    const matchFn = options.match
        ? createMatch(options.match)
        : createMatch(options.ignore);
    return function pathMatch(ctx) {
        const matched = matchFn(ctx);
        return options.match ? matched : !matched;
    };
}
exports.pathMatching = pathMatching;
/**
 * wrap function middleware with match and ignore
 * @param mw
 * @param options
 */
function wrapMiddleware(mw, options) {
    // support options.enable
    if (options.enable === false)
        return null;
    // support options.match and options.ignore
    if (!options.match && !options.ignore)
        return mw;
    const match = pathMatching(options);
    const fn = (ctx, next) => {
        if (!match(ctx))
            return next();
        return mw(ctx, next);
    };
    fn._name = mw._name + 'middlewareWrapper';
    return fn;
}
exports.wrapMiddleware = wrapMiddleware;
function isOwnPropertyWritable(obj, prop) {
    if (obj == null)
        return false;
    const type = typeof obj;
    if (type !== 'object' && type !== 'function')
        return false;
    return !!Object.getOwnPropertyDescriptor(obj, prop);
}
function isIncludeProperty(obj, prop) {
    while (obj) {
        if (isOwnPropertyWritable(obj, prop))
            return true;
        obj = Object.getPrototypeOf(obj);
    }
    return false;
}
exports.isIncludeProperty = isIncludeProperty;
function wrapAsync(handler) {
    return (...args) => {
        if (typeof args[args.length - 1] === 'function') {
            const callback = args.pop();
            if (handler.constructor.name !== 'AsyncFunction') {
                const err = new TypeError('Must be an AsyncFunction');
                return callback(err);
            }
            // 其他事件场景
            return handler.apply(handler, args).then(result => {
                callback(null, result);
            }, err => {
                callback(err);
            });
        }
        else {
            return handler.apply(handler, args);
        }
    };
}
exports.wrapAsync = wrapAsync;
function sleep(sleepTime = 1000) {
    return new Promise(resolve => {
        setTimeout(() => {
            resolve();
        }, sleepTime);
    });
}
exports.sleep = sleep;
const STRIP_COMMENTS = /((\/\/.*$)|(\/\*[\s\S]*?\*\/))/gm;
/**
 * get parameter name from function
 * @param func
 */
function getParamNames(func) {
    const fnStr = func.toString().replace(STRIP_COMMENTS, '');
    let result = fnStr
        .slice(fnStr.indexOf('(') + 1, fnStr.indexOf(')'))
        .split(',')
        .map(content => {
        return content.trim().replace(/\s?=.*$/, '');
    });
    if (result.length === 1 && result[0] === '') {
        result = [];
    }
    return result;
}
exports.getParamNames = getParamNames;
/**
 * generate a lightweight 32 bit random id, enough for ioc container
 */
function generateRandomId() {
    // => ********************************
    return crypto.randomBytes(16).toString('hex');
}
exports.generateRandomId = generateRandomId;
function merge(target, src) {
    if (!target) {
        target = src;
        src = null;
    }
    if (!target) {
        return null;
    }
    if (Array.isArray(target)) {
        return target.concat(src || []);
    }
    if (typeof target === 'object') {
        return Object.assign({}, target, src);
    }
    throw new Error('can not merge meta that type of ' + typeof target);
}
exports.merge = merge;
function toAsyncFunction(method) {
    if (types_1.Types.isAsyncFunction(method)) {
        return method;
    }
    else {
        return async function (...args) {
            return Promise.resolve(method.call(this, ...args));
        };
    }
}
exports.toAsyncFunction = toAsyncFunction;
function isTypeScriptEnvironment() {
    const TS_MODE_PROCESS_FLAG = process.env.MIDWAY_TS_MODE;
    if ('false' === TS_MODE_PROCESS_FLAG) {
        return false;
    }
    // eslint-disable-next-line node/no-deprecated-api
    return TS_MODE_PROCESS_FLAG === 'true' || !!require.extensions['.ts'];
}
exports.isTypeScriptEnvironment = isTypeScriptEnvironment;
/**
 * Create a Promise that resolves after the specified time
 * @param options
 */
async function createPromiseTimeoutInvokeChain(options) {
    var _a;
    if (!options.onSuccess) {
        options.onSuccess = async (result) => {
            return result;
        };
    }
    options.isConcurrent = (_a = options.isConcurrent) !== null && _a !== void 0 ? _a : true;
    options.promiseItems = options.promiseItems.map(item => {
        if (item instanceof Promise) {
            return { item };
        }
        else {
            return item;
        }
    });
    // filter promise
    options.promiseItems = options.promiseItems.filter(item => {
        return item['item'] instanceof Promise;
    });
    if (options.isConcurrent) {
        // For each check item, we create a timeout Promise
        const checkPromises = options.promiseItems.map(item => {
            const timeoutPromise = new Promise((_, reject) => {
                var _a;
                // The timeout Promise fails after the specified time
                setTimeout(() => {
                    var _a;
                    return reject(new error_1.MidwayCodeInvokeTimeoutError(options.methodName, (_a = item['timeout']) !== null && _a !== void 0 ? _a : options.timeout));
                }, (_a = item['timeout']) !== null && _a !== void 0 ? _a : options.timeout);
            });
            // We use Promise.race to wait for either the check item or the timeout Promise
            return (Promise.race([item['item'], timeoutPromise])
                // If the check item Promise resolves, we set the result to success
                .then(re => {
                return options.onSuccess(re, item['meta']);
            })
                // If the timeout Promise resolves (i.e., the check item Promise did not resolve in time), we set the result to failure
                .catch(err => {
                return options.onFail(err, item['meta']);
            }));
        });
        return Promise.all(checkPromises);
    }
    else {
        const results = [];
        for (const item of options.promiseItems) {
            const timeoutPromise = new Promise((_, reject) => {
                var _a;
                setTimeout(() => {
                    var _a;
                    return reject(new error_1.MidwayCodeInvokeTimeoutError(options.methodName, (_a = item['timeout']) !== null && _a !== void 0 ? _a : options.timeout));
                }, (_a = item['timeout']) !== null && _a !== void 0 ? _a : options.timeout);
            });
            try {
                const result = await Promise.race([item['item'], timeoutPromise]).then(re => {
                    return options.onSuccess(re, item['meta']);
                });
                results.push(result);
            }
            catch (error) {
                results.push(options.onFail(error, item['meta']));
                break;
            }
        }
        return results;
    }
}
exports.createPromiseTimeoutInvokeChain = createPromiseTimeoutInvokeChain;
exports.Utils = {
    sleep,
    getParamNames,
    camelCase: camelCase_1.camelCase,
    pascalCase: camelCase_1.pascalCase,
    randomUUID: uuid_1.randomUUID,
    generateRandomId,
    toAsyncFunction,
    safeStringify: flatted_1.safeStringify,
    safeParse: flatted_1.safeParse,
    isTypeScriptEnvironment,
};
//# sourceMappingURL=index.js.map