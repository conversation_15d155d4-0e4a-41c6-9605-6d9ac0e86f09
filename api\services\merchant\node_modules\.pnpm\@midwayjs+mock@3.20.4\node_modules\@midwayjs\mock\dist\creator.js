"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBootstrap = exports.createLightApp = exports.createFunctionApp = exports.close = exports.createApp = exports.create = void 0;
const core_1 = require("@midwayjs/core");
const path_1 = require("path");
const logger_1 = require("@midwayjs/logger");
const utils_1 = require("./utils");
const util_1 = require("util");
const fs_1 = require("fs");
const yaml = require("js-yaml");
const getRawBody = require("raw-body");
const async_hooks_context_manager_1 = require("@midwayjs/async-hooks-context-manager");
const debug = (0, util_1.debuglog)('midway:debug');
process.setMaxListeners(0);
function formatPath(baseDir, p) {
    if ((0, path_1.isAbsolute)(p)) {
        return p;
    }
    else {
        return (0, path_1.resolve)(baseDir, p);
    }
}
function getFileNameWithSuffix(fileName) {
    return (0, core_1.isTypeScriptEnvironment)() ? `${fileName}.ts` : `${fileName}.js`;
}
async function create(appDir, options = {}, customFramework) {
    var _a;
    process.env.MIDWAY_TS_MODE = (_a = process.env.MIDWAY_TS_MODE) !== null && _a !== void 0 ? _a : 'true';
    if (typeof appDir === 'object') {
        options = appDir;
        appDir = options.appDir || '';
    }
    debug(`[mock]: Create app, appDir="${appDir}"`);
    try {
        if (appDir) {
            // 处理测试的 fixtures
            if (!(0, path_1.isAbsolute)(appDir)) {
                appDir = (0, path_1.join)(process.cwd(), 'test', 'fixtures', appDir);
            }
            if (!(0, fs_1.existsSync)(appDir)) {
                throw new core_1.MidwayCommonError(`Path "${appDir}" not exists, please check it.`);
            }
        }
        else {
            appDir = process.cwd();
        }
        (0, logger_1.clearAllLoggers)();
        options = options || {};
        if (options.entryFile) {
            // start from entry file, like bootstrap.js
            options.entryFile = formatPath(appDir, options.entryFile);
            global['MIDWAY_BOOTSTRAP_APP_READY'] = false;
            // set app in @midwayjs/bootstrap
            require(options.entryFile);
            await new Promise((resolve, reject) => {
                const timeoutHandler = setTimeout(() => {
                    clearInterval(internalHandler);
                    reject(new Error('[midway]: bootstrap timeout'));
                }, options.bootstrapTimeout || 30 * 1000);
                const internalHandler = setInterval(() => {
                    if (global['MIDWAY_BOOTSTRAP_APP_READY'] === true) {
                        clearInterval(internalHandler);
                        clearTimeout(timeoutHandler);
                        resolve();
                    }
                    else {
                        debug('[mock]: bootstrap not ready and wait next check');
                    }
                }, 200);
            });
            return;
        }
        if (!options.moduleLoadType) {
            const pkgJSON = await (0, core_1.loadModule)((0, path_1.join)(appDir, 'package.json'), {
                safeLoad: true,
                enableCache: false,
            });
            options.moduleLoadType = (pkgJSON === null || pkgJSON === void 0 ? void 0 : pkgJSON.type) === 'module' ? 'esm' : 'commonjs';
        }
        if (options.baseDir) {
            if (!(0, path_1.isAbsolute)(options.baseDir)) {
                options.baseDir = (0, path_1.join)(appDir, options.baseDir);
            }
            await (0, core_1.loadModule)((0, path_1.join)(`${options.baseDir}`, getFileNameWithSuffix('interface')), {
                safeLoad: true,
                loadMode: options.moduleLoadType,
            });
        }
        else if (appDir) {
            options.baseDir = (0, path_1.join)(appDir, 'src');
            await (0, core_1.loadModule)((0, path_1.join)(`${options.baseDir}`, getFileNameWithSuffix('interface')), {
                safeLoad: true,
                loadMode: options.moduleLoadType,
            });
        }
        if (!options.imports && customFramework) {
            options.imports = await (0, utils_1.transformFrameworkToConfiguration)(customFramework, options.moduleLoadType);
        }
        if (customFramework === null || customFramework === void 0 ? void 0 : customFramework['Configuration']) {
            options.imports = customFramework;
            customFramework = customFramework['Framework'];
        }
        if (options.ssl) {
            const sslConfig = {
                koa: {
                    key: (0, path_1.join)(__dirname, '../ssl/ssl.key'),
                    cert: (0, path_1.join)(__dirname, '../ssl/ssl.pem'),
                },
                egg: {
                    key: (0, path_1.join)(__dirname, '../ssl/ssl.key'),
                    cert: (0, path_1.join)(__dirname, '../ssl/ssl.pem'),
                },
                express: {
                    key: (0, path_1.join)(__dirname, '../ssl/ssl.key'),
                    cert: (0, path_1.join)(__dirname, '../ssl/ssl.pem'),
                },
            };
            options.globalConfig = (0, utils_1.mergeGlobalConfig)(options.globalConfig, sslConfig);
        }
        const container = new core_1.MidwayContainer();
        const bindModuleMap = new WeakMap();
        // 这里设置是因为在 midway 单测中会不断的复用装饰器元信息，又不能清理缓存，所以在这里做一些过滤
        container.onBeforeBind(target => {
            bindModuleMap.set(target, true);
        });
        const originMethod = container.listModule;
        container.listModule = key => {
            const modules = originMethod.call(container, key);
            if (key === core_1.CONFIGURATION_KEY) {
                return modules;
            }
            return modules.filter((module) => {
                var _a;
                if (bindModuleMap.has(module)) {
                    return true;
                }
                else {
                    debug('[mock] Filter "%o" module without binding when list module %s.', (_a = module.name) !== null && _a !== void 0 ? _a : module, key);
                    return false;
                }
            });
        };
        options.applicationContext = container;
        await (0, core_1.initializeGlobalApplicationContext)({
            ...options,
            appDir,
            asyncContextManager: (0, async_hooks_context_manager_1.createContextManager)(),
            loggerFactory: logger_1.loggers,
            imports: [].concat(options.imports).concat(options.baseDir
                ? await (0, core_1.loadModule)((0, path_1.join)(options.baseDir, getFileNameWithSuffix('configuration')), {
                    safeLoad: true,
                    loadMode: options.moduleLoadType,
                })
                : []),
        });
        if (customFramework) {
            return container.getAsync(customFramework);
        }
        else {
            const frameworkService = await container.getAsync(core_1.MidwayFrameworkService);
            const mainFramework = frameworkService.getMainFramework();
            if (mainFramework) {
                return mainFramework;
            }
            else {
                throw new Error(`Can not get main framework, please check your ${getFileNameWithSuffix('configuration')}.`);
            }
        }
    }
    catch (err) {
        // catch for jest beforeAll can't throw error
        if (process.env.JEST_WORKER_ID) {
            console.error(err);
        }
        throw err;
    }
}
exports.create = create;
async function createApp(baseDir = process.cwd(), options, customFramework) {
    const framework = await create(baseDir, options, customFramework);
    return framework.getApplication();
}
exports.createApp = createApp;
async function close(app, options) {
    if (!app)
        return;
    if (app instanceof BootstrapAppStarter ||
        (typeof app['close'] === 'function' && !app['getConfig'])) {
        await app['close'](options);
    }
    else {
        app = app;
        debug(`[mock]: Closing app, appDir=${app.getAppDir()}`);
        options = options || {};
        await (0, core_1.destroyGlobalApplicationContext)(app.getApplicationContext());
        if ((0, utils_1.isTestEnvironment)()) {
            // clean first
            if (options.cleanLogsDir && !(0, utils_1.isWin32)()) {
                await (0, utils_1.removeFile)((0, path_1.join)(app.getAppDir(), 'logs'));
            }
            if (core_1.MidwayFrameworkType.WEB === app.getFrameworkType()) {
                if (options.cleanTempDir && !(0, utils_1.isWin32)()) {
                    await (0, utils_1.removeFile)((0, path_1.join)(app.getAppDir(), 'run'));
                }
            }
            if (options.sleep > 0) {
                await (0, core_1.sleep)(options.sleep);
            }
            else {
                await (0, core_1.sleep)(50);
            }
        }
    }
}
exports.close = close;
async function createFunctionApp(baseDir, options = {}, customFrameworkModule) {
    var _a, _b, _c, _d, _e;
    process.env.MIDWAY_TS_MODE = (_a = process.env.MIDWAY_TS_MODE) !== null && _a !== void 0 ? _a : 'true';
    if (typeof baseDir === 'object') {
        options = baseDir;
        baseDir = options.appDir || '';
    }
    // v3 新的处理 bootstrap 过来的 faas 入口
    if (options.entryFile) {
        const exportModules = require(formatPath(baseDir, options.entryFile));
        options.starter = exportModules.getStarter();
    }
    let starterName;
    // 老的 f.yml 逻辑
    if (!options.starter) {
        if (!baseDir) {
            baseDir = process.cwd();
        }
        // load yaml
        try {
            const doc = yaml.load((0, fs_1.readFileSync)((0, path_1.join)(baseDir, 'f.yml'), 'utf8'));
            starterName = (_b = doc === null || doc === void 0 ? void 0 : doc['provider']) === null || _b === void 0 ? void 0 : _b['starter'];
            if (starterName) {
                const m = await (0, core_1.loadModule)(starterName);
                if (m && m['BootstrapStarter']) {
                    options.starter = new m['BootstrapStarter']();
                }
            }
        }
        catch (e) {
            // ignore
            console.error('[mock]: get f.yml information fail, err = ' + e.stack);
        }
    }
    if (options.starter) {
        options.appDir = baseDir;
        debug(`[mock]: Create app, appDir="${options.appDir}"`);
        if (options.appDir) {
            // 处理测试的 fixtures
            if (!(0, path_1.isAbsolute)(options.appDir)) {
                options.appDir = (0, path_1.join)(process.cwd(), 'test', 'fixtures', options.appDir);
            }
            if (!(0, fs_1.existsSync)(options.appDir)) {
                throw new core_1.MidwayCommonError(`Path "${options.appDir}" not exists, please check it.`);
            }
        }
        (0, logger_1.clearAllLoggers)();
        const pkgJSON = await (0, core_1.loadModule)((0, path_1.join)(options.appDir, 'package.json'), {
            safeLoad: true,
            enableCache: false,
        });
        options.moduleLoadType = (pkgJSON === null || pkgJSON === void 0 ? void 0 : pkgJSON.type) === 'module' ? 'esm' : 'commonjs';
        options = options || {};
        if (options.baseDir) {
            await (0, core_1.loadModule)((0, path_1.join)(`${options.baseDir}`, getFileNameWithSuffix('interface')), {
                safeLoad: true,
                loadMode: options.moduleLoadType,
            });
        }
        else if (options.appDir) {
            options.baseDir = `${options.appDir}/src`;
            await (0, core_1.loadModule)((0, path_1.join)(`${options.baseDir}`, getFileNameWithSuffix('interface')), {
                safeLoad: true,
                loadMode: options.moduleLoadType,
            });
        }
        // new mode
        const exports = options.starter.start(options);
        await exports[options.initializeMethodName || 'initializer'](options['initializeContext']);
        const appCtx = options.starter.getApplicationContext();
        const configService = appCtx.get(core_1.MidwayConfigService);
        const frameworkService = appCtx.get(core_1.MidwayFrameworkService);
        const framework = frameworkService.getMainFramework();
        const appManager = appCtx.get(core_1.MidwayApplicationManager);
        const app = appManager.getApplication(core_1.MidwayFrameworkType.FAAS);
        const faasConfig = (_c = configService.getConfiguration('faas')) !== null && _c !== void 0 ? _c : {};
        const customPort = (_e = (_d = process.env.MIDWAY_HTTP_PORT) !== null && _d !== void 0 ? _d : faasConfig['port']) !== null && _e !== void 0 ? _e : options['port'];
        if (options.starter.callback2) {
            app.callback2 = options.starter.callback2.bind(options.starter);
        }
        else {
            app.callback2 = () => {
                // mock a real http server response for local dev
                return async (req, res) => {
                    const url = new URL(req.url, `http://${req.headers.host}`);
                    req.query = Object.fromEntries(url.searchParams);
                    req.path = url.pathname;
                    // 如果需要解析body并且body是个stream，函数网关不会接受比 10m 更大的文件了
                    if (['post', 'put', 'delete'].indexOf(req.method.toLowerCase()) !==
                        -1 &&
                        !req.body &&
                        typeof req.on === 'function') {
                        req.body = await getRawBody(req, {
                            limit: '10mb',
                        });
                    }
                    req.getOriginEvent = () => {
                        var _a;
                        return ((_a = options.starter) === null || _a === void 0 ? void 0 : _a.createDefaultMockHttpEvent()) || {};
                    };
                    req.getOriginContext = () => {
                        var _a;
                        return ((_a = options.starter) === null || _a === void 0 ? void 0 : _a.createDefaultMockContext()) || {};
                    };
                    try {
                        const ctx = await framework.wrapHttpRequest(req);
                        // create event and invoke
                        const result = await framework.invokeTriggerFunction(ctx, url.pathname, {
                            isHttpFunction: true,
                        });
                        const { statusCode, headers, body, isBase64Encoded } = result;
                        if (res.headersSent) {
                            return;
                        }
                        for (const key in headers) {
                            res.setHeader(key, headers[key]);
                        }
                        if (res.statusCode !== statusCode) {
                            res.statusCode = statusCode;
                        }
                        // http trigger only support `Buffer` or a `string` or a `stream.Readable`
                        if (isBase64Encoded && typeof body === 'string') {
                            res.end(Buffer.from(body, 'base64'));
                        }
                        else {
                            res.end(body);
                        }
                    }
                    catch (err) {
                        if (/favicon\.ico not found/.test(err.message)) {
                            res.statusCode = 404;
                            res.end();
                            return;
                        }
                        console.error(err);
                        res.statusCode = err.status || 500;
                        res.end(err.message);
                    }
                };
            };
        }
        app.getServerlessInstance = async (serviceClass, customContext = {}) => {
            const instance = new Proxy({}, {
                get: (target, prop) => {
                    let funcInfo;
                    if (typeof serviceClass === 'string') {
                        funcInfo = framework['funMappingStore'].get(`${serviceClass}.${prop}`);
                    }
                    else {
                        funcInfo = Array.from(framework['funMappingStore'].values()).find((item) => {
                            return (item.id === (0, core_1.getProviderUUId)(serviceClass) &&
                                item.method === prop);
                        });
                    }
                    if (funcInfo) {
                        return async (...args) => {
                            var _a, _b, _c, _d, _e;
                            const context = app.createAnonymousContext({
                                originContext: (_b = customContext !== null && customContext !== void 0 ? customContext : (_a = options.starter) === null || _a === void 0 ? void 0 : _a.createDefaultMockContext()) !== null && _b !== void 0 ? _b : {},
                                originEvent: (_e = (_c = args[0]) !== null && _c !== void 0 ? _c : (_d = options.starter) === null || _d === void 0 ? void 0 : _d.createDefaultMockEvent()) !== null && _e !== void 0 ? _e : {},
                            });
                            return framework.invokeTriggerFunction(context, funcInfo.funcHandlerName, {
                                isHttpFunction: false,
                            });
                        };
                    }
                },
            });
            return instance;
        };
        if (customPort) {
            await new Promise(resolve => {
                let server;
                if (options.ssl) {
                    server = require('https').createServer({
                        key: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../ssl/ssl.key'), 'utf8'),
                        cert: (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../ssl/ssl.pem'), 'utf8'),
                    }, app.callback2());
                }
                else {
                    server = require('http').createServer(app.callback2());
                }
                server.listen(customPort);
                process.env.MIDWAY_HTTP_PORT = String(customPort);
                app.server = server;
                resolve();
            });
        }
        return app;
    }
    else {
        const customFramework = customFrameworkModule !== null && customFrameworkModule !== void 0 ? customFrameworkModule : (0, utils_1.findFirstExistModule)([
            process.env.MIDWAY_SERVERLESS_APP_NAME,
            '@ali/serverless-app',
            '@midwayjs/serverless-app',
        ]);
        const serverlessModule = await (0, utils_1.transformFrameworkToConfiguration)(customFramework, options.moduleLoadType);
        if (serverlessModule) {
            if (options && options.imports) {
                options.imports.unshift(serverlessModule);
            }
            else {
                options = options || {};
                options.imports = [serverlessModule];
            }
        }
        const framework = await createApp(baseDir, options);
        const appCtx = framework.getApplicationContext();
        const appManager = appCtx.get(core_1.MidwayApplicationManager);
        return appManager.getApplication(core_1.MidwayFrameworkType.SERVERLESS_APP);
    }
}
exports.createFunctionApp = createFunctionApp;
/**
 * 一个全量的空框架
 */
class LightFramework extends core_1.BaseFramework {
    getFrameworkType() {
        return core_1.MidwayFrameworkType.LIGHT;
    }
    async run() { }
    async applicationInitialize(options) {
        this.app = {};
        this.defineApplicationProperties();
    }
    configure() {
        return {};
    }
    getFrameworkName() {
        return 'lightFramework';
    }
}
class BootstrapAppStarter {
    constructor(options) {
        this.options = options;
    }
    getApp(type) {
        const applicationContext = (0, core_1.getCurrentApplicationContext)();
        const applicationManager = applicationContext.get(core_1.MidwayApplicationManager);
        return applicationManager.getApplication(type);
    }
    async close(options = {}) {
        // eslint-disable-next-line node/no-extraneous-require
        const BootstrapModule = await (0, core_1.loadModule)('@midwayjs/bootstrap', {
            loadMode: this.options.moduleLoadType,
            safeLoad: true,
        });
        if (BootstrapModule === null || BootstrapModule === void 0 ? void 0 : BootstrapModule.Bootstrap) {
            await BootstrapModule.Bootstrap.stop();
        }
        if (options.sleep > 0) {
            await (0, core_1.sleep)(options.sleep);
        }
        else {
            await (0, core_1.sleep)(50);
        }
    }
}
/**
 * Create a real project but not ready or a virtual project
 * @param baseDirOrOptions
 * @param options
 */
async function createLightApp(baseDirOrOptions, options = {}) {
    var _a;
    if (baseDirOrOptions && typeof baseDirOrOptions === 'object') {
        options = baseDirOrOptions;
        baseDirOrOptions = options.baseDir || '';
    }
    (0, core_1.Framework)()(LightFramework);
    options.globalConfig = Object.assign({
        midwayLogger: {
            default: {
                disableFile: true,
                disableError: true,
            },
        },
    }, (_a = options.globalConfig) !== null && _a !== void 0 ? _a : {});
    if (!options.moduleLoadType) {
        const cwd = process.cwd();
        const pkgJSON = await (0, core_1.loadModule)((0, path_1.join)(cwd, 'package.json'), {
            safeLoad: true,
            enableCache: false,
        });
        options.moduleLoadType = (pkgJSON === null || pkgJSON === void 0 ? void 0 : pkgJSON.type) === 'module' ? 'esm' : 'commonjs';
    }
    return createApp(baseDirOrOptions, {
        ...options,
        imports: [
            await (0, utils_1.transformFrameworkToConfiguration)(LightFramework, options.moduleLoadType),
        ].concat(options === null || options === void 0 ? void 0 : options.imports),
    });
}
exports.createLightApp = createLightApp;
async function createBootstrap(entryFile, options = {}) {
    const cwd = process.cwd();
    if (!options.bootstrapMode) {
        options.bootstrapMode = (0, fs_1.existsSync)((0, path_1.join)(cwd, 'node_modules/@midwayjs/faas'))
            ? 'faas'
            : 'app';
    }
    if (options.bootstrapMode === 'faas') {
        options.entryFile = entryFile;
        const app = await createFunctionApp(cwd, options);
        return {
            close: async () => {
                return close(app);
            },
        };
    }
    else {
        await create(undefined, {
            entryFile,
        });
        return new BootstrapAppStarter(options);
    }
}
exports.createBootstrap = createBootstrap;
//# sourceMappingURL=creator.js.map