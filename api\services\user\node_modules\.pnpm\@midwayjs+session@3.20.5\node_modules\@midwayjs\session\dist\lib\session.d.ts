/**
 * Session model.
 */
import { ISession } from '../interface';
export declare class Session implements ISession {
    private _sessCtx;
    private _ctx;
    private _externalKey;
    _requireSave: any;
    isNew: boolean;
    /**
     * Session constructor
     * @param sessionContext
     * @param {Object} obj
     * @param externalKey
     */
    constructor(sessionContext: any, obj: any, externalKey: any);
    /**
     * JSON representation of the session.
     *
     * @return {Object}
     * @api public
     */
    toJSON(): {};
    /**
     * Return how many values there are in the session object.
     * Used to see if it's "populated".
     *
     * @return {Number}
     * @api public
     */
    get length(): number;
    /**
     * populated flag, which is just a boolean alias of .length.
     *
     * @return {Boolean}
     * @api public
     */
    get populated(): boolean;
    /**
     * get session maxAge
     *
     * @return {Number}
     * @api public
     */
    get maxAge(): any;
    /**
     * set session maxAge
     *
     * @api public
     * @param val
     */
    set maxAge(val: any);
    /**
     * get session external key
     * only exist if opts.store present
     */
    get externalKey(): any;
    /**
     * save this session no matter whether it is populated
     *
     * @api public
     */
    save(callback: any): any;
    /**
     * regenerate this session
     *
     * @param  {Function} callback the optional function to call after regenerating the session
     * @api public
     */
    regenerate(callback: any): any;
    /**
     * commit this session's headers if autoCommit is set to false
     *
     * @api public
     */
    manuallyCommit(): any;
    commit(options?: any, callback?: any): any;
}
//# sourceMappingURL=session.d.ts.map