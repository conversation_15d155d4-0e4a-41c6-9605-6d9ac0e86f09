{"version": 3, "file": "type-discriminator-descriptor.interface.js", "sourceRoot": "", "sources": ["../../../../src/interfaces/decorator-options/type-discriminator-descriptor.interface.ts"], "names": [], "mappings": "", "sourcesContent": ["import { ClassConstructor } from '..';\n\n/**\n * Discriminator object containing the type information to select a proper type\n * during transformation when a discriminator property is provided.\n */\nexport interface DiscriminatorDescriptor {\n  /**\n   * The name of the property which holds the type information in the received object.\n   */\n  property: string;\n  /**\n   * List of the available types. The transformer will try to lookup the object\n   * with the same key as the value received in the defined discriminator property\n   * and create an instance of the defined class.\n   */\n  subTypes: {\n    /**\n     * Name of the type.\n     */\n    name: string;\n\n    /**\n     * A class constructor which can be used to create the object.\n     */\n    value: ClassConstructor<any>;\n  }[];\n}\n"]}