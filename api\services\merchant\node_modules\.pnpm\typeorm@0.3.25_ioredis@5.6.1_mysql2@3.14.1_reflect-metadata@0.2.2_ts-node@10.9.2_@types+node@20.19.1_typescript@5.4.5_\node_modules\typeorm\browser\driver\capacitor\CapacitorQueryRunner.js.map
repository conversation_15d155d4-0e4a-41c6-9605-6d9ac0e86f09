{"version": 3, "sources": ["../browser/src/driver/capacitor/CapacitorQueryRunner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAC/D,OAAO,EAAE,yBAAyB,EAAE,MAAM,8CAA8C,CAAA;AAExF,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAE1D,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAE5D;;GAEG;AACH,MAAM,OAAO,oBAAqB,SAAQ,yBAAyB;IAM/D,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAuB;QAC/B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,GAA4C;QACzD,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,OAAO,kBAAkB,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAE/D,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAC3B,CAAC,EACD,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAC7D,CAAA;QAED,IAAI,CAAC;YACD,IAAI,GAAQ,CAAA;YAEZ,IACI;gBACI,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,OAAO;gBACP,MAAM;aACT,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC3B,CAAC;gBACC,GAAG,GAAG,MAAM,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;YACxD,CAAC;iBAAM,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAChE,GAAG,GAAG,MAAM,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;YAChE,CAAC;iBAAM,CAAC;gBACJ,GAAG,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,IAAI,EAAE,CAAC,CAAA;YACjE,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;YAEhC,IAAI,GAAG,EAAE,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,CAAA;gBACvB,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,MAAM,CAAA;YAC/B,CAAC;YAED,IAAI,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gBACjC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAA;gBACrC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,OAAO,CAAA;YAC1D,CAAC;YAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACvB,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAED,MAAM,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,WAAW,CAAC,aAA4B;QAC9C,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAA;IACrE,CAAC;CACJ", "file": "CapacitorQueryRunner.js", "sourcesContent": ["import { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { CapacitorDriver } from \"./CapacitorDriver\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class CapacitorQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: CapacitorDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: CapacitorDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    async executeSet(set: { statement: string; values?: any[] }[]) {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        return databaseConnection.executeSet(set, false)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n\n        const command = query.substring(\n            0,\n            query.indexOf(\" \") !== -1 ? query.indexOf(\" \") : undefined,\n        )\n\n        try {\n            let raw: any\n\n            if (\n                [\n                    \"BEGIN\",\n                    \"ROLLBACK\",\n                    \"COMMIT\",\n                    \"CREATE\",\n                    \"ALTER\",\n                    \"DROP\",\n                ].indexOf(command) !== -1\n            ) {\n                raw = await databaseConnection.execute(query, false)\n            } else if ([\"INSERT\", \"UPDATE\", \"DELETE\"].indexOf(command) !== -1) {\n                raw = await databaseConnection.run(query, parameters, false)\n            } else {\n                raw = await databaseConnection.query(query, parameters || [])\n            }\n\n            const result = new QueryResult()\n\n            if (raw?.hasOwnProperty(\"values\")) {\n                result.raw = raw.values\n                result.records = raw.values\n            }\n\n            if (raw?.hasOwnProperty(\"changes\")) {\n                result.affected = raw.changes.changes\n                result.raw = raw.changes.lastId || raw.changes.changes\n            }\n\n            if (!useStructuredResult) {\n                return result.raw\n            }\n\n            return result\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n\n            throw new QueryFailedError(query, parameters, err)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Parametrizes given object of values. Used to create column=value queries.\n     */\n    protected parametrize(objectLiteral: ObjectLiteral): string[] {\n        return Object.keys(objectLiteral).map((key) => `\"${key}\"` + \"=?\")\n    }\n}\n"], "sourceRoot": "../.."}