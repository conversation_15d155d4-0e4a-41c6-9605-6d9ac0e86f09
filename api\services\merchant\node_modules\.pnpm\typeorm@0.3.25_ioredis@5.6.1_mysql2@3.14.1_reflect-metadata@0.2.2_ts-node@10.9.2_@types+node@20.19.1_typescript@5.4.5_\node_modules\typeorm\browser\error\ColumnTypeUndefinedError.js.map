{"version": 3, "sources": ["../browser/src/error/ColumnTypeUndefinedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;;GAGG;AACH,MAAM,OAAO,wBAAyB,SAAQ,YAAY;IACtD,YAAY,MAAc,EAAE,YAAoB;QAC5C,KAAK,CACD,mBAAmB,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,YAAY,yCAAyC;YAC/F,yFAAyF;YACzF,qIAAqI;YACrI,8FAA8F,CACrG,CAAA;IACL,CAAC;CACJ", "file": "ColumnTypeUndefinedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when ORM cannot get column's type automatically.\n * Basically, when reflect-metadata is not available or tsconfig is not properly setup.\n */\nexport class ColumnTypeUndefinedError extends TypeORMError {\n    constructor(object: Object, propertyName: string) {\n        super(\n            `Column type for ${object.constructor.name}#${propertyName} is not defined and cannot be guessed. ` +\n                `Make sure you have turned on an \"emitDecoratorMetadata\": true option in tsconfig.json. ` +\n                `Also make sure you have imported \"reflect-metadata\" on top of the main entry file in your application (before any entity imported).` +\n                `If you are using JavaScript instead of TypeScript you must explicitly provide a column type.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}