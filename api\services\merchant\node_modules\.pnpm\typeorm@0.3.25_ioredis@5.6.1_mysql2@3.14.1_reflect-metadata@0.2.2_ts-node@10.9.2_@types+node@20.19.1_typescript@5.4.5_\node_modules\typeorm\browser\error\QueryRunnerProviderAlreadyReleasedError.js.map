{"version": 3, "sources": ["../browser/src/error/QueryRunnerProviderAlreadyReleasedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uCAAwC,SAAQ,YAAY;IACrE;QACI,KAAK,CACD,6DAA6D;YACzD,gEAAgE,CACvE,CAAA;IACL,CAAC;CACJ", "file": "QueryRunnerProviderAlreadyReleasedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to use query runner from query runner provider after it was released.\n */\nexport class QueryRunnerProviderAlreadyReleasedError extends TypeORMError {\n    constructor() {\n        super(\n            `Database connection provided by a query runner was already ` +\n                `released, cannot continue to use its querying methods anymore.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}