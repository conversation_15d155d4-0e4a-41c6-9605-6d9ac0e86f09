{"name": "i<PERSON>is", "version": "4.28.5", "description": "A robust, performance-focused and full-featured Redis client for Node.js.", "main": "built/index.js", "files": ["built/"], "scripts": {"test": "TS_NODE_TRANSPILE_ONLY=true TS_NODE_LOG_ERROR=true NODE_ENV=test mocha \"test/helpers/*.ts\" \"test/**/*.ts\"", "test-single": "TS_NODE_TRANSPILE_ONLY=true TS_NODE_LOG_ERROR=true NODE_ENV=test mocha \"test/helpers/*.ts\" $1", "lint": "eslint --ext .js,.ts .", "format": "prettier --write \"{,!(node_modules)/**/}*.{js,ts}\"", "format-check": "prettier --check \"{,!(node_modules)/**/}*.{js,ts}\"", "build": "rm -rf built && tsc", "prepublishOnly": "npm run build && npm test", "semantic-release": "semantic-release"}, "repository": {"type": "git", "url": "git://github.com/luin/ioredis.git"}, "keywords": ["redis", "cluster", "sentinel", "pipelining"], "author": "luin <<EMAIL>> (http://zihua.li)", "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/ioredis"}, "dependencies": {"cluster-key-slot": "^1.1.0", "debug": "^4.3.1", "denque": "^1.1.0", "lodash.defaults": "^4.2.0", "lodash.flatten": "^4.4.0", "lodash.isarguments": "^3.1.0", "p-map": "^2.1.0", "redis-commands": "1.7.0", "redis-errors": "^1.2.0", "redis-parser": "^3.0.0", "standard-as-callback": "^2.1.0"}, "devDependencies": {"@semantic-release/changelog": "^5.0.1", "@semantic-release/git": "^9.0.0", "@types/bluebird": "^3.5.30", "@types/chai": "^4.2.11", "@types/chai-as-promised": "^7.1.3", "@types/debug": "^4.1.5", "@types/lodash.defaults": "^4.2.6", "@types/lodash.flatten": "^4.4.6", "@types/lodash.isarguments": "^3.1.6", "@types/mocha": "^7.0.2", "@types/node": "^13.11.0", "@types/redis-errors": "1.2.0", "@types/sinon": "^9.0.0", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^1.13.0", "@typescript-eslint/parser": "^2.26.0", "bluebird": "^3.7.2", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "cronometro": "^0.6.0", "cz-conventional-changelog": "^3.1.0", "eslint": "^5.16.0", "eslint-config-prettier": "^6.10.1", "husky": "^4.2.3", "mocha": "^6.2.3", "prettier": "^2.0.2", "pretty-quick": "^2.0.1", "server-destroy": "^1.0.1", "sinon": "^9.0.1", "ts-node": "^8.8.1", "typescript": "3.8.3", "uuid": "^8.3.0"}, "engines": {"node": ">=6"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "mocha": {"exit": true, "timeout": 8000, "recursive": true, "require": "ts-node/register"}}