{"version": 3, "file": "ValidationOptions.js", "sourceRoot": "", "sources": ["../../../src/decorator/ValidationOptions.ts"], "names": [], "mappings": "AAiCA,MAAM,UAAU,mBAAmB,CAAC,GAAQ;IAC1C,IAAI,CAAC,GAAG,EAAE,CAAC;QACT,OAAO,KAAK,CAAC;IACf,CAAC;IACD,OAAO,MAAM,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,IAAI,QAAQ,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG,CAAC;AACrG,CAAC", "sourcesContent": ["import { ValidationArguments } from '../validation/ValidationArguments';\n\n/**\n * Options used to pass to validation decorators.\n */\nexport interface ValidationOptions {\n  /**\n   * Specifies if validated value is an array and each of its items must be validated.\n   */\n  each?: boolean;\n\n  /**\n   * Error message to be used on validation fail.\n   * Message can be either string or a function that returns a string.\n   */\n  message?: string | ((validationArguments: ValidationArguments) => string);\n\n  /**\n   * Validation groups used for this validation.\n   */\n  groups?: string[];\n\n  /**\n   * Indicates if validation must be performed always, no matter of validation groups used.\n   */\n  always?: boolean;\n\n  /*\n   * A transient set of data passed through to the validation result for response mapping\n   */\n  context?: any;\n}\n\nexport function isValidationOptions(val: any): val is ValidationOptions {\n  if (!val) {\n    return false;\n  }\n  return 'each' in val || 'message' in val || 'groups' in val || 'always' in val || 'context' in val;\n}\n"]}