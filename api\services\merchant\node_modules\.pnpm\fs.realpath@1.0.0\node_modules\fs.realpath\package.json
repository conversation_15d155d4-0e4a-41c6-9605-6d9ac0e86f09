{"name": "fs.realpath", "version": "1.0.0", "description": "Use node's fs.realpath, but fall back to the JS implementation if the native one fails", "main": "index.js", "dependencies": {}, "devDependencies": {}, "scripts": {"test": "tap test/*.js --cov"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/fs.realpath.git"}, "keywords": ["realpath", "fs", "polyfill"], "author": "<PERSON> <<EMAIL>> (http://blog.izs.me/)", "license": "ISC", "files": ["old.js", "index.js"]}