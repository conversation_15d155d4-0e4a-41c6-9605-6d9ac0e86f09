import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 用户钱包
 */
@Entity('finance_wallet_user')
export class FinanceWalletUserEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({
    comment: '总金额',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  balance: number;

  @Column({
    comment: '冻结金额',
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  freezeAmount: number;
}
