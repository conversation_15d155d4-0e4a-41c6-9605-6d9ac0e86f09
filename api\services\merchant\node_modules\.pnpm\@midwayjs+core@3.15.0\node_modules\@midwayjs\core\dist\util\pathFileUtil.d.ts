/// <reference types="node" />
export declare function isPath(p: any): boolean;
export declare function isPathEqual(one: string, two: string): boolean;
export declare function getFileContentSync(filePath: any, encoding?: BufferEncoding): any;
export declare const PathFileUtil: {
    isPath: typeof isPath;
    isPathEqual: typeof isPathEqual;
    getFileContentSync: typeof getFileContentSync;
};
export declare function getModuleRequirePathList(moduleName: string): string[];
//# sourceMappingURL=pathFileUtil.d.ts.map