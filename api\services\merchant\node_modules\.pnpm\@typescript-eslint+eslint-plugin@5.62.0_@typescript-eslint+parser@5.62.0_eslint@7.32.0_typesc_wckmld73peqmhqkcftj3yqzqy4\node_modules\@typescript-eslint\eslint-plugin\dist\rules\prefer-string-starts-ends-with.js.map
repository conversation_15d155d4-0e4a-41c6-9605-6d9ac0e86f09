{"version": 3, "file": "prefer-string-starts-ends-with.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-string-starts-ends-with.ts"], "names": [], "mappings": ";;AACA,uDAAyD;AAEzD,oDAA0D;AAE1D,kCASiB;AAEjB,MAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,MAAM,OAAO,GAAG,IAAI,sBAAY,EAAE,CAAC;AAEnC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,gCAAgC;IACtC,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,8GAA8G;YAChH,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,gBAAgB,EAAE,yCAAyC;YAC3D,cAAc,EAAE,2CAA2C;SAC5D;QACD,MAAM,EAAE,EAAE;QACV,OAAO,EAAE,MAAM;KAChB;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAErD;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAyB;YAC7C,MAAM,UAAU,GAAG,WAAW,CAAC,iBAAiB,CAC9C,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CACxC,CAAC;YACF,OAAO,IAAA,kBAAW,EAAC,WAAW,EAAE,UAAU,CAAC,KAAK,QAAQ,CAAC;QAC3D,CAAC;QAED;;;WAGG;QACH,SAAS,MAAM,CAAC,IAAmB;YACjC,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,IAAI,IAAI,CAAC;QACtD,CAAC;QAED;;;;WAIG;QACH,SAAS,QAAQ,CACf,IAAmB,EACnB,KAAa;YAEb,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;QACxD,CAAC;QAED;;;;WAIG;QACH,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,CACL,SAAS,IAAI,IAAI;gBACjB,OAAO,SAAS,CAAC,KAAK,KAAK,QAAQ;gBACnC,2CAA2C;gBAC3C,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,KAAK,CACvC,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,oBAAoB,CAC3B,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CACjC,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,YAAY,CAAC,KAAoB,EAAE,KAAoB;YAC9D,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAE5C,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACvC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE1B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,KAAK,EAAE;oBAChE,OAAO,KAAK,CAAC;iBACd;aACF;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;;;;;;WAUG;QACH,SAAS,kBAAkB,CACzB,IAAmB,EACnB,kBAAiC;YAEjC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;gBACjD,OAAO,CACL,IAAA,sBAAe,EAAC,IAAI,EAAE,WAAW,CAAC,KAAK,QAAQ;oBAC/C,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAC9C,CAAC;aACH;YAED,MAAM,eAAe,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YAC1D,MAAM,eAAe,GAAG,IAAA,qBAAc,EAAC,kBAAkB,EAAE,WAAW,CAAC,CAAC;YACxE,OAAO,CACL,eAAe,IAAI,IAAI;gBACvB,eAAe,IAAI,IAAI;gBACvB,OAAO,eAAe,CAAC,KAAK,KAAK,QAAQ;gBACzC,OAAO,eAAe,CAAC,KAAK,KAAK,QAAQ;gBACzC,eAAe,CAAC,KAAK,KAAK,eAAe,CAAC,KAAK,CAAC,MAAM,CACvD,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,yBAAyB,CAChC,IAAmB,EACnB,mBAAkC;YAElC,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC3C,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;gBACxB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBAC5C,IAAI,CAAC,QAAQ,KAAK,GAAG;oBACrB,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CACtD,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,qBAAqB,CAC5B,IAAmB,EACnB,kBAAiC;YAEjC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,QAAQ,KAAK,GAAG;gBACrB,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,kBAAkB,CAAC;gBACjD,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CACxB,CAAC;QACJ,CAAC;QAED;;;;;;;;WAQG;QACH,SAAS,gBAAgB,CACvB,IAA+B;YAE/B,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAC/C,IAAI,CAAC,MAAM,EACX,6BAAsB,CACtB,CAAC;YACH,OAAO,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,CAAC;QAED;;;;WAIG;QACH,SAAS,eAAe,CAAC,OAAe,EAAE,KAAc;YACtD,YAAY;YACZ,MAAM,GAAG,GAAG,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YACvE,IAAI,GAAG,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjC,OAAO,IAAI,CAAC;aACb;YAED,0BAA0B;YAC1B,MAAM,KAAK,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC3C,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACvB,IAAI,KAAK,CAAC,IAAI,KAAK,WAAW,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;gBACxD,KAAK,CAAC,KAAK,EAAE,CAAC;aACf;iBAAM;gBACL,KAAK,CAAC,GAAG,EAAE,CAAC;aACb;YAED,6CAA6C;YAC7C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE;gBAC7C,OAAO,IAAI,CAAC;aACb;YAED,aAAa;YACb,OAAO,MAAM,CAAC,aAAa,CACzB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAyB,CAAC,KAAK,CAAC,CACpD,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAClB,IAAmB;YAEnB,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,YAAY,MAAM,CAAC,EAAE;gBAC7D,OAAO,IAAI,CAAC;aACb;YAED,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;YAC1C,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;YAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACxC,IACE,YAAY,KAAK,UAAU;gBAC3B,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACnB,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EACnB;gBACA,OAAO,IAAI,CAAC;aACb;YAED,MAAM,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1D,IAAI,IAAI,IAAI,IAAI,EAAE;gBAChB,OAAO,IAAI,CAAC;aACb;YAED,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC;QAC5C,CAAC;QAED,SAAS,WAAW,CAClB,IAAsD;YAEtD,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;gBAChD,OAAO,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACrC;YAED,IAAI,QAAQ,CAAC;YACb,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAC/C,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;aACxB;iBAAM;gBACL,QAAQ,GAAG,IAAI,CAAC;aACjB;YAED,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,EAAE;gBACrD,MAAM,IAAI,KAAK,CAAC,oCAAoC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;aACtE;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAED;;;;;;;WAOG;QACH,QAAQ,CAAC,CAAC,mBAAmB,CAC3B,KAAyB,EACzB,IAA+B,EAC/B,IAAqB,EACrB,UAAmB,EACnB,UAAmB;YAEnB,8CAA8C;YAC9C,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;YAEjD,IAAI,UAAU,EAAE;gBACd,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACzC;YACD,MAAM,KAAK,CAAC,gBAAgB,CAC1B,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACvC,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,QAAQ,CAC1C,CAAC;YACF,MAAM,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QAC1E,CAAC;QAED;;;;;;;WAOG;QACH,QAAQ,CAAC,CAAC,eAAe,CACvB,KAAyB,EACzB,IAA+B,EAC/B,QAAiC,EACjC,UAAqC,EACrC,IAAqB,EACrB,QAAiB,EACjB,UAAmB;YAEnB,IAAI,QAAQ,EAAE;gBACZ,MAAM,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;aACzC;YACD,MAAM,KAAK,CAAC,gBAAgB,CAC1B,gBAAgB,CAAC,UAAU,CAAC,EAC5B,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,OAAO,CACzC,CAAC;YACF,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,SAAS,SAAS,CAAC,IAAmB;;YACpC,OAAO,IAAA,iBAAU,EACf,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,eAAe;gBAClD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;gBACpB,CAAC,CAAC,IAAI,CAAC,MAAM,EACf,wBAAiB,CAAC,aAAa,CAChC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,iBAAiB;YACjB,wBAAwB;YACxB,8BAA8B;YAC9B,qCAAqC;YACrC,CAAC;gBACC,yDAAyD;gBACzD,0GAA0G;gBAC1G,2EAA2E;gBAC3E,4HAA4H;aAC7H,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA+B;gBAC3C,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;gBAEjC,IAAI,SAAS,GAAyB,IAAI,CAAC;gBAC3C,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,EAAE;oBACtD,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;wBACrC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;qBACrC;oBACD,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC;iBACpC;qBAAM;oBACL,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC3B;gBAED,IACE,SAAS,IAAI,IAAI;oBACjB,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACjC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,MAAM,UAAU,GAAG,qBAAqB,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjE,MAAM,YAAY,GAAG,CAAC,UAAU,IAAI,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;gBAC3D,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE;oBAChC,OAAO;iBACR;gBAED,MAAM,MAAM,GAAG,UAAU,CAAC;gBAC1B,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB;oBAC/D,GAAG,CAAC,KAAK;wBACP,2CAA2C;wBAC3C,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;4BAC9B,OAAO,IAAI,CAAC;yBACb;wBACD,OAAO,mBAAmB,CACxB,KAAK,EACL,MAAM,EACN,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAC9B,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAC/B,IAAI,CAAC,QAAQ,CACd,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,2BAA2B;YAC3B,CAAC;gBACC,2GAA2G;gBAC3G,6HAA6H;aAC9H,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA+B;gBAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAA4B,CAAC;gBAC5D,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAEvC,IACE,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;oBAC/B,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACjC,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC9B,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,kBAAkB;oBAC7B,GAAG,CAAC,KAAK;wBACP,OAAO,eAAe,CACpB,KAAK,EACL,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EACnC,IAAI,CAAC,QAAQ,CACd,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,4CAA4C;YAC5C,mDAAmD;YACnD,CAAC;gBACC,+GAA+G;gBAC/G,iIAAiI;aAClI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA+B;gBAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAA4B,CAAC;gBAC5D,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAEvC,IACE,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;oBAC/B,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACjC,UAAU,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACzD,UAAU,CAAC,KAAK,CAAC,QAAQ,KAAK,GAAG;oBACjC,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;oBACvD,CAAC,kBAAkB,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBAClE,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,gBAAgB;oBAC3B,GAAG,CAAC,KAAK;wBACP,OAAO,eAAe,CACpB,KAAK,EACL,UAAU,EACV,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EACnC,IAAI,CAAC,QAAQ,CACd,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,6BAA6B;YAC7B,6BAA6B;YAC7B,CAAC;gBACC,yGAAyG;gBACzG,2HAA2H;aAC5H,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA+B;gBAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAA4B,CAAC;gBAC5D,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAA8B,CAAC;gBAEpE,IACE,CAAC,oBAAoB,CAAC,UAAU,CAAC;oBACjC,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;oBACzB,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAC1B;oBACA,OAAO;iBACR;gBAED,MAAM,MAAM,GACV,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;oBAC7B,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;oBACpC,CAAC,CAAC,IAAI,CAAC;gBACX,IAAI,MAAM,IAAI,IAAI,EAAE;oBAClB,OAAO;iBACR;gBAED,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;gBACtC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB;oBAC/D,CAAC,GAAG,CAAC,KAAK;wBACR,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;4BACxC,MAAM,KAAK,CAAC,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;yBAC/C;wBACD,MAAM,KAAK,CAAC,gBAAgB,CAC1B,gBAAgB,CAAC,IAAI,CAAC,EACtB,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAC3B,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAC3B,OAAO,CACR,CAAC;wBACF,MAAM,KAAK,CAAC,WAAW,CACrB,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CACrB,CAAC;wBACF,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACpE,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,4BAA4B;YAC5B,0BAA0B;YAC1B,sCAAsC;YACtC,gCAAgC;YAChC,0CAA0C;YAC1C,sDAAsD;YACtD,CAAC;gBACC,yGAAyG;gBACzG,6GAA6G;gBAC7G,2HAA2H;gBAC3H,+HAA+H;aAChI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAA+B;gBAC3C,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAA4B,CAAC;gBAC5D,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC;gBAEvC,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACnE,OAAO;iBACR;gBAED,MAAM,UAAU,GACd,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;oBAC9B,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;wBAC9B,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;oBAC5D,yBAAyB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBAChE,MAAM,YAAY,GAChB,CAAC,UAAU;oBACX,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;oBAC/B,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;oBAClC,CAAC,yBAAyB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;gBACjE,IAAI,CAAC,YAAY,IAAI,CAAC,UAAU,EAAE;oBAChC,OAAO;iBACR;gBAED,MAAM,MAAM,GAAG,UAAU,CAAC;gBAC1B,MAAM,sBAAsB,GACzB,IAAI,CAAC,QAAgC,CAAC,IAAI,KAAK,OAAO,CAAC;gBAC1D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU;oBAChB,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB;oBAC/D,GAAG,CAAC,KAAK;wBACP,2CAA2C;wBAC3C,IACE,MAAM,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC;4BAC5B,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gCAC3C,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,EACzC;4BACA,OAAO,IAAI,CAAC;yBACb;wBACD,wCAAwC;wBACxC,uDAAuD;wBACvD,0EAA0E;wBAC1E,IAAI,YAAY,EAAE;4BAChB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;gCAC5D,OAAO,IAAI,CAAC;6BACb;yBACF;6BAAM;4BACL,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;4BACtC,MAAM,wBAAwB,GAC5B,CAAC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gCAC/C,OAAO,CAAC,QAAQ,KAAK,GAAG;gCACxB,kBAAkB,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC;gCAC7C,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gCAClD,CAAC,sBAAsB;oCACrB,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oCAC/C,OAAO,CAAC,QAAQ,KAAK,GAAG;oCACxB,kBAAkB,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;4BACxD,IAAI,CAAC,wBAAwB,EAAE;gCAC7B,OAAO,IAAI,CAAC;6BACb;yBACF;wBAED,OAAO,mBAAmB,CACxB,KAAK,EACL,UAAU,EACV,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAC9B,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,EACnC,IAAI,CAAC,QAAQ,CACd,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,mBAAmB;YACnB,mBAAmB;YACnB,gFAAgF,CAC9E,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAA4B,CAAC;gBAC5D,MAAM,MAAM,GACV,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACpE,IAAI,MAAM,IAAI,IAAI,EAAE;oBAClB,OAAO;iBACR;gBAED,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;gBACtC,MAAM,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,gBAAgB,CAAC;gBACvE,MAAM,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,UAAU,CAAC;gBAC5D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,QAAQ;oBACd,SAAS;oBACT,CAAC,GAAG,CAAC,KAAK;wBACR,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM,UAAU,GACd,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;4BACvC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;4BAC/C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAC1C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BAChD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;wBAEjD,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,IAAI,UAAU,EAAE;4BACd,MAAM,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;4BAC3C,MAAM,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;yBAC3C;wBACD,MAAM,KAAK,CAAC,eAAe,CACzB,OAAO,EACP,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,UAAU,IAAI,IAAI,CAAC,SAAS,CAC1D,IAAI,CACL,EAAE,CACJ,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}