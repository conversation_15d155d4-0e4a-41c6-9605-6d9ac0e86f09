(function(e){if(typeof exports=="object"&&typeof module=="object")module.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var i=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this||{};i.prettierPlugins=i.prettierPlugins||{},i.prettierPlugins.markdown=e()}})(function(){"use strict";var $=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var Fe=$((nf,yu)=>{var tr=function(e){return e&&e.Math==Math&&e};yu.exports=tr(typeof globalThis=="object"&&globalThis)||tr(typeof window=="object"&&window)||tr(typeof self=="object"&&self)||tr(typeof global=="object"&&global)||function(){return this}()||Function("return this")()});var Ae=$((af,wu)=>{wu.exports=function(e){try{return!!e()}catch{return!0}}});var Be=$((of,Bu)=>{var fa=Ae();Bu.exports=!fa(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!=7})});var nr=$((sf,ku)=>{var pa=Ae();ku.exports=!pa(function(){var e=function(){}.bind();return typeof e!="function"||e.hasOwnProperty("prototype")})});var Oe=$((cf,qu)=>{var da=nr(),ir=Function.prototype.call;qu.exports=da?ir.bind(ir):function(){return ir.apply(ir,arguments)}});var Su=$(Iu=>{"use strict";var _u={}.propertyIsEnumerable,Ou=Object.getOwnPropertyDescriptor,ha=Ou&&!_u.call({1:2},1);Iu.f=ha?function(r){var u=Ou(this,r);return!!u&&u.enumerable}:_u});var ar=$((Df,Tu)=>{Tu.exports=function(e,r){return{enumerable:!(e&1),configurable:!(e&2),writable:!(e&4),value:r}}});var ve=$((ff,Ru)=>{var Nu=nr(),Lu=Function.prototype,wr=Lu.call,va=Nu&&Lu.bind.bind(wr,wr);Ru.exports=Nu?va:function(e){return function(){return wr.apply(e,arguments)}}});var Ve=$((pf,Pu)=>{var ju=ve(),ma=ju({}.toString),Ea=ju("".slice);Pu.exports=function(e){return Ea(ma(e),8,-1)}});var zu=$((df,Mu)=>{var Ca=ve(),ga=Ae(),Fa=Ve(),Br=Object,Aa=Ca("".split);Mu.exports=ga(function(){return!Br("z").propertyIsEnumerable(0)})?function(e){return Fa(e)=="String"?Aa(e,""):Br(e)}:Br});var or=$((hf,$u)=>{$u.exports=function(e){return e==null}});var kr=$((vf,Uu)=>{var xa=or(),ba=TypeError;Uu.exports=function(e){if(xa(e))throw ba("Can't call method on "+e);return e}});var sr=$((mf,Gu)=>{var ya=zu(),wa=kr();Gu.exports=function(e){return ya(wa(e))}});var _r=$((Ef,Vu)=>{var qr=typeof document=="object"&&document.all,Ba=typeof qr>"u"&&qr!==void 0;Vu.exports={all:qr,IS_HTMLDDA:Ba}});var de=$((Cf,Xu)=>{var Hu=_r(),ka=Hu.all;Xu.exports=Hu.IS_HTMLDDA?function(e){return typeof e=="function"||e===ka}:function(e){return typeof e=="function"}});var Ie=$((gf,Yu)=>{var Wu=de(),Ku=_r(),qa=Ku.all;Yu.exports=Ku.IS_HTMLDDA?function(e){return typeof e=="object"?e!==null:Wu(e)||e===qa}:function(e){return typeof e=="object"?e!==null:Wu(e)}});var He=$((Ff,Ju)=>{var Or=Fe(),_a=de(),Oa=function(e){return _a(e)?e:void 0};Ju.exports=function(e,r){return arguments.length<2?Oa(Or[e]):Or[e]&&Or[e][r]}});var Ir=$((Af,Zu)=>{var Ia=ve();Zu.exports=Ia({}.isPrototypeOf)});var et=$((xf,Qu)=>{var Sa=He();Qu.exports=Sa("navigator","userAgent")||""});var ot=$((bf,at)=>{var it=Fe(),Sr=et(),rt=it.process,ut=it.Deno,tt=rt&&rt.versions||ut&&ut.version,nt=tt&&tt.v8,me,cr;nt&&(me=nt.split("."),cr=me[0]>0&&me[0]<4?1:+(me[0]+me[1]));!cr&&Sr&&(me=Sr.match(/Edge\/(\d+)/),(!me||me[1]>=74)&&(me=Sr.match(/Chrome\/(\d+)/),me&&(cr=+me[1])));at.exports=cr});var Tr=$((yf,ct)=>{var st=ot(),Ta=Ae();ct.exports=!!Object.getOwnPropertySymbols&&!Ta(function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&st&&st<41})});var Nr=$((wf,lt)=>{var Na=Tr();lt.exports=Na&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Lr=$((Bf,Dt)=>{var La=He(),Ra=de(),ja=Ir(),Pa=Nr(),Ma=Object;Dt.exports=Pa?function(e){return typeof e=="symbol"}:function(e){var r=La("Symbol");return Ra(r)&&ja(r.prototype,Ma(e))}});var lr=$((kf,ft)=>{var za=String;ft.exports=function(e){try{return za(e)}catch{return"Object"}}});var Xe=$((qf,pt)=>{var $a=de(),Ua=lr(),Ga=TypeError;pt.exports=function(e){if($a(e))return e;throw Ga(Ua(e)+" is not a function")}});var Dr=$((_f,dt)=>{var Va=Xe(),Ha=or();dt.exports=function(e,r){var u=e[r];return Ha(u)?void 0:Va(u)}});var vt=$((Of,ht)=>{var Rr=Oe(),jr=de(),Pr=Ie(),Xa=TypeError;ht.exports=function(e,r){var u,t;if(r==="string"&&jr(u=e.toString)&&!Pr(t=Rr(u,e))||jr(u=e.valueOf)&&!Pr(t=Rr(u,e))||r!=="string"&&jr(u=e.toString)&&!Pr(t=Rr(u,e)))return t;throw Xa("Can't convert object to primitive value")}});var Et=$((If,mt)=>{mt.exports=!1});var fr=$((Sf,gt)=>{var Ct=Fe(),Wa=Object.defineProperty;gt.exports=function(e,r){try{Wa(Ct,e,{value:r,configurable:!0,writable:!0})}catch{Ct[e]=r}return r}});var pr=$((Tf,At)=>{var Ka=Fe(),Ya=fr(),Ft="__core-js_shared__",Ja=Ka[Ft]||Ya(Ft,{});At.exports=Ja});var Mr=$((Nf,bt)=>{var Za=Et(),xt=pr();(bt.exports=function(e,r){return xt[e]||(xt[e]=r!==void 0?r:{})})("versions",[]).push({version:"3.26.1",mode:Za?"pure":"global",copyright:"\xA9 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.26.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var zr=$((Lf,yt)=>{var Qa=kr(),eo=Object;yt.exports=function(e){return eo(Qa(e))}});var ke=$((Rf,wt)=>{var ro=ve(),uo=zr(),to=ro({}.hasOwnProperty);wt.exports=Object.hasOwn||function(r,u){return to(uo(r),u)}});var $r=$((jf,Bt)=>{var no=ve(),io=0,ao=Math.random(),oo=no(1 .toString);Bt.exports=function(e){return"Symbol("+(e===void 0?"":e)+")_"+oo(++io+ao,36)}});var Te=$((Pf,It)=>{var so=Fe(),co=Mr(),kt=ke(),lo=$r(),qt=Tr(),Ot=Nr(),Le=co("wks"),Se=so.Symbol,_t=Se&&Se.for,Do=Ot?Se:Se&&Se.withoutSetter||lo;It.exports=function(e){if(!kt(Le,e)||!(qt||typeof Le[e]=="string")){var r="Symbol."+e;qt&&kt(Se,e)?Le[e]=Se[e]:Ot&&_t?Le[e]=_t(r):Le[e]=Do(r)}return Le[e]}});var Lt=$((Mf,Nt)=>{var fo=Oe(),St=Ie(),Tt=Lr(),po=Dr(),ho=vt(),vo=Te(),mo=TypeError,Eo=vo("toPrimitive");Nt.exports=function(e,r){if(!St(e)||Tt(e))return e;var u=po(e,Eo),t;if(u){if(r===void 0&&(r="default"),t=fo(u,e,r),!St(t)||Tt(t))return t;throw mo("Can't convert object to primitive value")}return r===void 0&&(r="number"),ho(e,r)}});var dr=$((zf,Rt)=>{var Co=Lt(),go=Lr();Rt.exports=function(e){var r=Co(e,"string");return go(r)?r:r+""}});var Mt=$(($f,Pt)=>{var Fo=Fe(),jt=Ie(),Ur=Fo.document,Ao=jt(Ur)&&jt(Ur.createElement);Pt.exports=function(e){return Ao?Ur.createElement(e):{}}});var Gr=$((Uf,zt)=>{var xo=Be(),bo=Ae(),yo=Mt();zt.exports=!xo&&!bo(function(){return Object.defineProperty(yo("div"),"a",{get:function(){return 7}}).a!=7})});var Vr=$(Ut=>{var wo=Be(),Bo=Oe(),ko=Su(),qo=ar(),_o=sr(),Oo=dr(),Io=ke(),So=Gr(),$t=Object.getOwnPropertyDescriptor;Ut.f=wo?$t:function(r,u){if(r=_o(r),u=Oo(u),So)try{return $t(r,u)}catch{}if(Io(r,u))return qo(!Bo(ko.f,r,u),r[u])}});var Vt=$((Vf,Gt)=>{var To=Be(),No=Ae();Gt.exports=To&&No(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!=42})});var Re=$((Hf,Ht)=>{var Lo=Ie(),Ro=String,jo=TypeError;Ht.exports=function(e){if(Lo(e))return e;throw jo(Ro(e)+" is not an object")}});var We=$(Wt=>{var Po=Be(),Mo=Gr(),zo=Vt(),hr=Re(),Xt=dr(),$o=TypeError,Hr=Object.defineProperty,Uo=Object.getOwnPropertyDescriptor,Xr="enumerable",Wr="configurable",Kr="writable";Wt.f=Po?zo?function(r,u,t){if(hr(r),u=Xt(u),hr(t),typeof r=="function"&&u==="prototype"&&"value"in t&&Kr in t&&!t[Kr]){var a=Uo(r,u);a&&a[Kr]&&(r[u]=t.value,t={configurable:Wr in t?t[Wr]:a[Wr],enumerable:Xr in t?t[Xr]:a[Xr],writable:!1})}return Hr(r,u,t)}:Hr:function(r,u,t){if(hr(r),u=Xt(u),hr(t),Mo)try{return Hr(r,u,t)}catch{}if("get"in t||"set"in t)throw $o("Accessors not supported");return"value"in t&&(r[u]=t.value),r}});var Yr=$((Wf,Kt)=>{var Go=Be(),Vo=We(),Ho=ar();Kt.exports=Go?function(e,r,u){return Vo.f(e,r,Ho(1,u))}:function(e,r,u){return e[r]=u,e}});var Zt=$((Kf,Jt)=>{var Jr=Be(),Xo=ke(),Yt=Function.prototype,Wo=Jr&&Object.getOwnPropertyDescriptor,Zr=Xo(Yt,"name"),Ko=Zr&&function(){}.name==="something",Yo=Zr&&(!Jr||Jr&&Wo(Yt,"name").configurable);Jt.exports={EXISTS:Zr,PROPER:Ko,CONFIGURABLE:Yo}});var eu=$((Yf,Qt)=>{var Jo=ve(),Zo=de(),Qr=pr(),Qo=Jo(Function.toString);Zo(Qr.inspectSource)||(Qr.inspectSource=function(e){return Qo(e)});Qt.exports=Qr.inspectSource});var un=$((Jf,rn)=>{var es=Fe(),rs=de(),en=es.WeakMap;rn.exports=rs(en)&&/native code/.test(String(en))});var an=$((Zf,nn)=>{var us=Mr(),ts=$r(),tn=us("keys");nn.exports=function(e){return tn[e]||(tn[e]=ts(e))}});var ru=$((Qf,on)=>{on.exports={}});var Dn=$((ep,ln)=>{var ns=un(),cn=Fe(),is=Ie(),as=Yr(),uu=ke(),tu=pr(),os=an(),ss=ru(),sn="Object already initialized",nu=cn.TypeError,cs=cn.WeakMap,vr,Ke,mr,ls=function(e){return mr(e)?Ke(e):vr(e,{})},Ds=function(e){return function(r){var u;if(!is(r)||(u=Ke(r)).type!==e)throw nu("Incompatible receiver, "+e+" required");return u}};ns||tu.state?(Ee=tu.state||(tu.state=new cs),Ee.get=Ee.get,Ee.has=Ee.has,Ee.set=Ee.set,vr=function(e,r){if(Ee.has(e))throw nu(sn);return r.facade=e,Ee.set(e,r),r},Ke=function(e){return Ee.get(e)||{}},mr=function(e){return Ee.has(e)}):(Ne=os("state"),ss[Ne]=!0,vr=function(e,r){if(uu(e,Ne))throw nu(sn);return r.facade=e,as(e,Ne,r),r},Ke=function(e){return uu(e,Ne)?e[Ne]:{}},mr=function(e){return uu(e,Ne)});var Ee,Ne;ln.exports={set:vr,get:Ke,has:mr,enforce:ls,getterFor:Ds}});var dn=$((rp,pn)=>{var fs=Ae(),ps=de(),Er=ke(),iu=Be(),ds=Zt().CONFIGURABLE,hs=eu(),fn=Dn(),vs=fn.enforce,ms=fn.get,Cr=Object.defineProperty,Es=iu&&!fs(function(){return Cr(function(){},"length",{value:8}).length!==8}),Cs=String(String).split("String"),gs=pn.exports=function(e,r,u){String(r).slice(0,7)==="Symbol("&&(r="["+String(r).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),u&&u.getter&&(r="get "+r),u&&u.setter&&(r="set "+r),(!Er(e,"name")||ds&&e.name!==r)&&(iu?Cr(e,"name",{value:r,configurable:!0}):e.name=r),Es&&u&&Er(u,"arity")&&e.length!==u.arity&&Cr(e,"length",{value:u.arity});try{u&&Er(u,"constructor")&&u.constructor?iu&&Cr(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch{}var t=vs(e);return Er(t,"source")||(t.source=Cs.join(typeof r=="string"?r:"")),e};Function.prototype.toString=gs(function(){return ps(this)&&ms(this).source||hs(this)},"toString")});var vn=$((up,hn)=>{var Fs=de(),As=We(),xs=dn(),bs=fr();hn.exports=function(e,r,u,t){t||(t={});var a=t.enumerable,n=t.name!==void 0?t.name:r;if(Fs(u)&&xs(u,n,t),t.global)a?e[r]=u:bs(r,u);else{try{t.unsafe?e[r]&&(a=!0):delete e[r]}catch{}a?e[r]=u:As.f(e,r,{value:u,enumerable:!1,configurable:!t.nonConfigurable,writable:!t.nonWritable})}return e}});var En=$((tp,mn)=>{var ys=Math.ceil,ws=Math.floor;mn.exports=Math.trunc||function(r){var u=+r;return(u>0?ws:ys)(u)}});var au=$((np,Cn)=>{var Bs=En();Cn.exports=function(e){var r=+e;return r!==r||r===0?0:Bs(r)}});var Fn=$((ip,gn)=>{var ks=au(),qs=Math.max,_s=Math.min;gn.exports=function(e,r){var u=ks(e);return u<0?qs(u+r,0):_s(u,r)}});var xn=$((ap,An)=>{var Os=au(),Is=Math.min;An.exports=function(e){return e>0?Is(Os(e),9007199254740991):0}});var Ye=$((op,bn)=>{var Ss=xn();bn.exports=function(e){return Ss(e.length)}});var Bn=$((sp,wn)=>{var Ts=sr(),Ns=Fn(),Ls=Ye(),yn=function(e){return function(r,u,t){var a=Ts(r),n=Ls(a),s=Ns(t,n),c;if(e&&u!=u){for(;n>s;)if(c=a[s++],c!=c)return!0}else for(;n>s;s++)if((e||s in a)&&a[s]===u)return e||s||0;return!e&&-1}};wn.exports={includes:yn(!0),indexOf:yn(!1)}});var _n=$((cp,qn)=>{var Rs=ve(),ou=ke(),js=sr(),Ps=Bn().indexOf,Ms=ru(),kn=Rs([].push);qn.exports=function(e,r){var u=js(e),t=0,a=[],n;for(n in u)!ou(Ms,n)&&ou(u,n)&&kn(a,n);for(;r.length>t;)ou(u,n=r[t++])&&(~Ps(a,n)||kn(a,n));return a}});var In=$((lp,On)=>{On.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var Tn=$(Sn=>{var zs=_n(),$s=In(),Us=$s.concat("length","prototype");Sn.f=Object.getOwnPropertyNames||function(r){return zs(r,Us)}});var Ln=$(Nn=>{Nn.f=Object.getOwnPropertySymbols});var jn=$((pp,Rn)=>{var Gs=He(),Vs=ve(),Hs=Tn(),Xs=Ln(),Ws=Re(),Ks=Vs([].concat);Rn.exports=Gs("Reflect","ownKeys")||function(r){var u=Hs.f(Ws(r)),t=Xs.f;return t?Ks(u,t(r)):u}});var zn=$((dp,Mn)=>{var Pn=ke(),Ys=jn(),Js=Vr(),Zs=We();Mn.exports=function(e,r,u){for(var t=Ys(r),a=Zs.f,n=Js.f,s=0;s<t.length;s++){var c=t[s];!Pn(e,c)&&!(u&&Pn(u,c))&&a(e,c,n(r,c))}}});var Un=$((hp,$n)=>{var Qs=Ae(),ec=de(),rc=/#|\.prototype\./,Je=function(e,r){var u=tc[uc(e)];return u==ic?!0:u==nc?!1:ec(r)?Qs(r):!!r},uc=Je.normalize=function(e){return String(e).replace(rc,".").toLowerCase()},tc=Je.data={},nc=Je.NATIVE="N",ic=Je.POLYFILL="P";$n.exports=Je});var cu=$((vp,Gn)=>{var su=Fe(),ac=Vr().f,oc=Yr(),sc=vn(),cc=fr(),lc=zn(),Dc=Un();Gn.exports=function(e,r){var u=e.target,t=e.global,a=e.stat,n,s,c,i,D,o;if(t?s=su:a?s=su[u]||cc(u,{}):s=(su[u]||{}).prototype,s)for(c in r){if(D=r[c],e.dontCallGetSet?(o=ac(s,c),i=o&&o.value):i=s[c],n=Dc(t?c:u+(a?".":"#")+c,e.forced),!n&&i!==void 0){if(typeof D==typeof i)continue;lc(D,i)}(e.sham||i&&i.sham)&&oc(D,"sham",!0),sc(s,c,D,e)}}});var lu=$((mp,Vn)=>{var fc=Ve();Vn.exports=Array.isArray||function(r){return fc(r)=="Array"}});var Xn=$((Ep,Hn)=>{var pc=TypeError,dc=9007199254740991;Hn.exports=function(e){if(e>dc)throw pc("Maximum allowed index exceeded");return e}});var Kn=$((Cp,Wn)=>{var hc=Ve(),vc=ve();Wn.exports=function(e){if(hc(e)==="Function")return vc(e)}});var Du=$((gp,Jn)=>{var Yn=Kn(),mc=Xe(),Ec=nr(),Cc=Yn(Yn.bind);Jn.exports=function(e,r){return mc(e),r===void 0?e:Ec?Cc(e,r):function(){return e.apply(r,arguments)}}});var ei=$((Fp,Qn)=>{"use strict";var gc=lu(),Fc=Ye(),Ac=Xn(),xc=Du(),Zn=function(e,r,u,t,a,n,s,c){for(var i=a,D=0,o=s?xc(s,c):!1,l,d;D<t;)D in u&&(l=o?o(u[D],D,r):u[D],n>0&&gc(l)?(d=Fc(l),i=Zn(e,r,l,d,i,n-1)-1):(Ac(i+1),e[i]=l),i++),D++;return i};Qn.exports=Zn});var ti=$((Ap,ui)=>{var bc=Te(),yc=bc("toStringTag"),ri={};ri[yc]="z";ui.exports=String(ri)==="[object z]"});var fu=$((xp,ni)=>{var wc=ti(),Bc=de(),gr=Ve(),kc=Te(),qc=kc("toStringTag"),_c=Object,Oc=gr(function(){return arguments}())=="Arguments",Ic=function(e,r){try{return e[r]}catch{}};ni.exports=wc?gr:function(e){var r,u,t;return e===void 0?"Undefined":e===null?"Null":typeof(u=Ic(r=_c(e),qc))=="string"?u:Oc?gr(r):(t=gr(r))=="Object"&&Bc(r.callee)?"Arguments":t}});var li=$((bp,ci)=>{var Sc=ve(),Tc=Ae(),ii=de(),Nc=fu(),Lc=He(),Rc=eu(),ai=function(){},jc=[],oi=Lc("Reflect","construct"),pu=/^\s*(?:class|function)\b/,Pc=Sc(pu.exec),Mc=!pu.exec(ai),Ze=function(r){if(!ii(r))return!1;try{return oi(ai,jc,r),!0}catch{return!1}},si=function(r){if(!ii(r))return!1;switch(Nc(r)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Mc||!!Pc(pu,Rc(r))}catch{return!0}};si.sham=!0;ci.exports=!oi||Tc(function(){var e;return Ze(Ze.call)||!Ze(Object)||!Ze(function(){e=!0})||e})?si:Ze});var di=$((yp,pi)=>{var Di=lu(),zc=li(),$c=Ie(),Uc=Te(),Gc=Uc("species"),fi=Array;pi.exports=function(e){var r;return Di(e)&&(r=e.constructor,zc(r)&&(r===fi||Di(r.prototype))?r=void 0:$c(r)&&(r=r[Gc],r===null&&(r=void 0))),r===void 0?fi:r}});var vi=$((wp,hi)=>{var Vc=di();hi.exports=function(e,r){return new(Vc(e))(r===0?0:r)}});var mi=$(()=>{"use strict";var Hc=cu(),Xc=ei(),Wc=Xe(),Kc=zr(),Yc=Ye(),Jc=vi();Hc({target:"Array",proto:!0},{flatMap:function(r){var u=Kc(this),t=Yc(u),a;return Wc(r),a=Jc(u,0),a.length=Xc(a,u,u,t,0,1,r,arguments.length>1?arguments[1]:void 0),a}})});var du=$((qp,Ei)=>{Ei.exports={}});var gi=$((_p,Ci)=>{var Zc=Te(),Qc=du(),el=Zc("iterator"),rl=Array.prototype;Ci.exports=function(e){return e!==void 0&&(Qc.Array===e||rl[el]===e)}});var hu=$((Op,Ai)=>{var ul=fu(),Fi=Dr(),tl=or(),nl=du(),il=Te(),al=il("iterator");Ai.exports=function(e){if(!tl(e))return Fi(e,al)||Fi(e,"@@iterator")||nl[ul(e)]}});var bi=$((Ip,xi)=>{var ol=Oe(),sl=Xe(),cl=Re(),ll=lr(),Dl=hu(),fl=TypeError;xi.exports=function(e,r){var u=arguments.length<2?Dl(e):r;if(sl(u))return cl(ol(u,e));throw fl(ll(e)+" is not iterable")}});var Bi=$((Sp,wi)=>{var pl=Oe(),yi=Re(),dl=Dr();wi.exports=function(e,r,u){var t,a;yi(e);try{if(t=dl(e,"return"),!t){if(r==="throw")throw u;return u}t=pl(t,e)}catch(n){a=!0,t=n}if(r==="throw")throw u;if(a)throw t;return yi(t),u}});var Ii=$((Tp,Oi)=>{var hl=Du(),vl=Oe(),ml=Re(),El=lr(),Cl=gi(),gl=Ye(),ki=Ir(),Fl=bi(),Al=hu(),qi=Bi(),xl=TypeError,Fr=function(e,r){this.stopped=e,this.result=r},_i=Fr.prototype;Oi.exports=function(e,r,u){var t=u&&u.that,a=!!(u&&u.AS_ENTRIES),n=!!(u&&u.IS_RECORD),s=!!(u&&u.IS_ITERATOR),c=!!(u&&u.INTERRUPTED),i=hl(r,t),D,o,l,d,p,g,F,E=function(f){return D&&qi(D,"normal",f),new Fr(!0,f)},b=function(f){return a?(ml(f),c?i(f[0],f[1],E):i(f[0],f[1])):c?i(f,E):i(f)};if(n)D=e.iterator;else if(s)D=e;else{if(o=Al(e),!o)throw xl(El(e)+" is not iterable");if(Cl(o)){for(l=0,d=gl(e);d>l;l++)if(p=b(e[l]),p&&ki(_i,p))return p;return new Fr(!1)}D=Fl(e,o)}for(g=n?e.next:D.next;!(F=vl(g,D)).done;){try{p=b(F.value)}catch(f){qi(D,"throw",f)}if(typeof p=="object"&&p&&ki(_i,p))return p}return new Fr(!1)}});var Ti=$((Np,Si)=>{"use strict";var bl=dr(),yl=We(),wl=ar();Si.exports=function(e,r,u){var t=bl(r);t in e?yl.f(e,t,wl(0,u)):e[t]=u}});var Ni=$(()=>{var Bl=cu(),kl=Ii(),ql=Ti();Bl({target:"Object",stat:!0},{fromEntries:function(r){var u={};return kl(r,function(t,a){ql(u,t,a)},{AS_ENTRIES:!0}),u}})});var uf=$((jp,la)=>{var _l=["cliName","cliCategory","cliDescription"];function Ol(e,r){if(e==null)return{};var u=Il(e,r),t,a;if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(a=0;a<n.length;a++)t=n[a],!(r.indexOf(t)>=0)&&Object.prototype.propertyIsEnumerable.call(e,t)&&(u[t]=e[t])}return u}function Il(e,r){if(e==null)return{};var u={},t=Object.keys(e),a,n;for(n=0;n<t.length;n++)a=t[n],!(r.indexOf(a)>=0)&&(u[a]=e[a]);return u}mi();Ni();var Sl=Object.create,Ar=Object.defineProperty,Tl=Object.getOwnPropertyDescriptor,vu=Object.getOwnPropertyNames,Nl=Object.getPrototypeOf,Ll=Object.prototype.hasOwnProperty,je=(e,r)=>function(){return e&&(r=(0,e[vu(e)[0]])(e=0)),r},S=(e,r)=>function(){return r||(0,e[vu(e)[0]])((r={exports:{}}).exports,r),r.exports},Pi=(e,r)=>{for(var u in r)Ar(e,u,{get:r[u],enumerable:!0})},Mi=(e,r,u,t)=>{if(r&&typeof r=="object"||typeof r=="function")for(let a of vu(r))!Ll.call(e,a)&&a!==u&&Ar(e,a,{get:()=>r[a],enumerable:!(t=Tl(r,a))||t.enumerable});return e},Rl=(e,r,u)=>(u=e!=null?Sl(Nl(e)):{},Mi(r||!e||!e.__esModule?Ar(u,"default",{value:e,enumerable:!0}):u,e)),zi=e=>Mi(Ar({},"__esModule",{value:!0}),e),Qe,I=je({"<define:process>"(){Qe={env:{},argv:[]}}}),Pe=S({"node_modules/xtend/immutable.js"(e,r){I(),r.exports=t;var u=Object.prototype.hasOwnProperty;function t(){for(var a={},n=0;n<arguments.length;n++){var s=arguments[n];for(var c in s)u.call(s,c)&&(a[c]=s[c])}return a}}}),jl=S({"node_modules/inherits/inherits_browser.js"(e,r){I(),typeof Object.create=="function"?r.exports=function(t,a){a&&(t.super_=a,t.prototype=Object.create(a.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:r.exports=function(t,a){if(a){t.super_=a;var n=function(){};n.prototype=a.prototype,t.prototype=new n,t.prototype.constructor=t}}}}),Pl=S({"node_modules/unherit/index.js"(e,r){"use strict";I();var u=Pe(),t=jl();r.exports=a;function a(n){var s,c,i;t(o,n),t(D,o),s=o.prototype;for(c in s)i=s[c],i&&typeof i=="object"&&(s[c]="concat"in i?i.concat():u(i));return o;function D(l){return n.apply(this,l)}function o(){return this instanceof o?n.apply(this,arguments):new D(arguments)}}}}),Ml=S({"node_modules/state-toggle/index.js"(e,r){"use strict";I(),r.exports=u;function u(t,a,n){return s;function s(){var c=n||this,i=c[t];return c[t]=!a,D;function D(){c[t]=i}}}}}),zl=S({"node_modules/vfile-location/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){for(var a=String(t),n=[],s=/\r?\n|\r/g;s.exec(a);)n.push(s.lastIndex);return n.push(a.length+1),{toPoint:c,toPosition:c,toOffset:i};function c(D){var o=-1;if(D>-1&&D<n[n.length-1]){for(;++o<n.length;)if(n[o]>D)return{line:o+1,column:D-(n[o-1]||0)+1,offset:D}}return{}}function i(D){var o=D&&D.line,l=D&&D.column,d;return!isNaN(o)&&!isNaN(l)&&o-1 in n&&(d=(n[o-2]||0)+l-1||0),d>-1&&d<n[n.length-1]?d:-1}}}}),$l=S({"node_modules/remark-parse/lib/unescape.js"(e,r){"use strict";I(),r.exports=t;var u="\\";function t(a,n){return s;function s(c){for(var i=0,D=c.indexOf(u),o=a[n],l=[],d;D!==-1;)l.push(c.slice(i,D)),i=D+1,d=c.charAt(i),(!d||o.indexOf(d)===-1)&&l.push(u),D=c.indexOf(u,i+1);return l.push(c.slice(i)),l.join("")}}}}),Ul=S({"node_modules/character-entities-legacy/index.json"(e,r){r.exports={AElig:"\xC6",AMP:"&",Aacute:"\xC1",Acirc:"\xC2",Agrave:"\xC0",Aring:"\xC5",Atilde:"\xC3",Auml:"\xC4",COPY:"\xA9",Ccedil:"\xC7",ETH:"\xD0",Eacute:"\xC9",Ecirc:"\xCA",Egrave:"\xC8",Euml:"\xCB",GT:">",Iacute:"\xCD",Icirc:"\xCE",Igrave:"\xCC",Iuml:"\xCF",LT:"<",Ntilde:"\xD1",Oacute:"\xD3",Ocirc:"\xD4",Ograve:"\xD2",Oslash:"\xD8",Otilde:"\xD5",Ouml:"\xD6",QUOT:'"',REG:"\xAE",THORN:"\xDE",Uacute:"\xDA",Ucirc:"\xDB",Ugrave:"\xD9",Uuml:"\xDC",Yacute:"\xDD",aacute:"\xE1",acirc:"\xE2",acute:"\xB4",aelig:"\xE6",agrave:"\xE0",amp:"&",aring:"\xE5",atilde:"\xE3",auml:"\xE4",brvbar:"\xA6",ccedil:"\xE7",cedil:"\xB8",cent:"\xA2",copy:"\xA9",curren:"\xA4",deg:"\xB0",divide:"\xF7",eacute:"\xE9",ecirc:"\xEA",egrave:"\xE8",eth:"\xF0",euml:"\xEB",frac12:"\xBD",frac14:"\xBC",frac34:"\xBE",gt:">",iacute:"\xED",icirc:"\xEE",iexcl:"\xA1",igrave:"\xEC",iquest:"\xBF",iuml:"\xEF",laquo:"\xAB",lt:"<",macr:"\xAF",micro:"\xB5",middot:"\xB7",nbsp:"\xA0",not:"\xAC",ntilde:"\xF1",oacute:"\xF3",ocirc:"\xF4",ograve:"\xF2",ordf:"\xAA",ordm:"\xBA",oslash:"\xF8",otilde:"\xF5",ouml:"\xF6",para:"\xB6",plusmn:"\xB1",pound:"\xA3",quot:'"',raquo:"\xBB",reg:"\xAE",sect:"\xA7",shy:"\xAD",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",szlig:"\xDF",thorn:"\xFE",times:"\xD7",uacute:"\xFA",ucirc:"\xFB",ugrave:"\xF9",uml:"\xA8",uuml:"\xFC",yacute:"\xFD",yen:"\xA5",yuml:"\xFF"}}}),Gl=S({"node_modules/character-reference-invalid/index.json"(e,r){r.exports={0:"\uFFFD",128:"\u20AC",130:"\u201A",131:"\u0192",132:"\u201E",133:"\u2026",134:"\u2020",135:"\u2021",136:"\u02C6",137:"\u2030",138:"\u0160",139:"\u2039",140:"\u0152",142:"\u017D",145:"\u2018",146:"\u2019",147:"\u201C",148:"\u201D",149:"\u2022",150:"\u2013",151:"\u2014",152:"\u02DC",153:"\u2122",154:"\u0161",155:"\u203A",156:"\u0153",158:"\u017E",159:"\u0178"}}}),Me=S({"node_modules/is-decimal/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){var a=typeof t=="string"?t.charCodeAt(0):t;return a>=48&&a<=57}}}),Vl=S({"node_modules/is-hexadecimal/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){var a=typeof t=="string"?t.charCodeAt(0):t;return a>=97&&a<=102||a>=65&&a<=70||a>=48&&a<=57}}}),er=S({"node_modules/is-alphabetical/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){var a=typeof t=="string"?t.charCodeAt(0):t;return a>=97&&a<=122||a>=65&&a<=90}}}),Hl=S({"node_modules/is-alphanumerical/index.js"(e,r){"use strict";I();var u=er(),t=Me();r.exports=a;function a(n){return u(n)||t(n)}}}),Xl=S({"node_modules/character-entities/index.json"(e,r){r.exports={AEli:"\xC6",AElig:"\xC6",AM:"&",AMP:"&",Aacut:"\xC1",Aacute:"\xC1",Abreve:"\u0102",Acir:"\xC2",Acirc:"\xC2",Acy:"\u0410",Afr:"\u{1D504}",Agrav:"\xC0",Agrave:"\xC0",Alpha:"\u0391",Amacr:"\u0100",And:"\u2A53",Aogon:"\u0104",Aopf:"\u{1D538}",ApplyFunction:"\u2061",Arin:"\xC5",Aring:"\xC5",Ascr:"\u{1D49C}",Assign:"\u2254",Atild:"\xC3",Atilde:"\xC3",Aum:"\xC4",Auml:"\xC4",Backslash:"\u2216",Barv:"\u2AE7",Barwed:"\u2306",Bcy:"\u0411",Because:"\u2235",Bernoullis:"\u212C",Beta:"\u0392",Bfr:"\u{1D505}",Bopf:"\u{1D539}",Breve:"\u02D8",Bscr:"\u212C",Bumpeq:"\u224E",CHcy:"\u0427",COP:"\xA9",COPY:"\xA9",Cacute:"\u0106",Cap:"\u22D2",CapitalDifferentialD:"\u2145",Cayleys:"\u212D",Ccaron:"\u010C",Ccedi:"\xC7",Ccedil:"\xC7",Ccirc:"\u0108",Cconint:"\u2230",Cdot:"\u010A",Cedilla:"\xB8",CenterDot:"\xB7",Cfr:"\u212D",Chi:"\u03A7",CircleDot:"\u2299",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201D",CloseCurlyQuote:"\u2019",Colon:"\u2237",Colone:"\u2A74",Congruent:"\u2261",Conint:"\u222F",ContourIntegral:"\u222E",Copf:"\u2102",Coproduct:"\u2210",CounterClockwiseContourIntegral:"\u2233",Cross:"\u2A2F",Cscr:"\u{1D49E}",Cup:"\u22D3",CupCap:"\u224D",DD:"\u2145",DDotrahd:"\u2911",DJcy:"\u0402",DScy:"\u0405",DZcy:"\u040F",Dagger:"\u2021",Darr:"\u21A1",Dashv:"\u2AE4",Dcaron:"\u010E",Dcy:"\u0414",Del:"\u2207",Delta:"\u0394",Dfr:"\u{1D507}",DiacriticalAcute:"\xB4",DiacriticalDot:"\u02D9",DiacriticalDoubleAcute:"\u02DD",DiacriticalGrave:"`",DiacriticalTilde:"\u02DC",Diamond:"\u22C4",DifferentialD:"\u2146",Dopf:"\u{1D53B}",Dot:"\xA8",DotDot:"\u20DC",DotEqual:"\u2250",DoubleContourIntegral:"\u222F",DoubleDot:"\xA8",DoubleDownArrow:"\u21D3",DoubleLeftArrow:"\u21D0",DoubleLeftRightArrow:"\u21D4",DoubleLeftTee:"\u2AE4",DoubleLongLeftArrow:"\u27F8",DoubleLongLeftRightArrow:"\u27FA",DoubleLongRightArrow:"\u27F9",DoubleRightArrow:"\u21D2",DoubleRightTee:"\u22A8",DoubleUpArrow:"\u21D1",DoubleUpDownArrow:"\u21D5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21F5",DownBreve:"\u0311",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295E",DownLeftVector:"\u21BD",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295F",DownRightVector:"\u21C1",DownRightVectorBar:"\u2957",DownTee:"\u22A4",DownTeeArrow:"\u21A7",Downarrow:"\u21D3",Dscr:"\u{1D49F}",Dstrok:"\u0110",ENG:"\u014A",ET:"\xD0",ETH:"\xD0",Eacut:"\xC9",Eacute:"\xC9",Ecaron:"\u011A",Ecir:"\xCA",Ecirc:"\xCA",Ecy:"\u042D",Edot:"\u0116",Efr:"\u{1D508}",Egrav:"\xC8",Egrave:"\xC8",Element:"\u2208",Emacr:"\u0112",EmptySmallSquare:"\u25FB",EmptyVerySmallSquare:"\u25AB",Eogon:"\u0118",Eopf:"\u{1D53C}",Epsilon:"\u0395",Equal:"\u2A75",EqualTilde:"\u2242",Equilibrium:"\u21CC",Escr:"\u2130",Esim:"\u2A73",Eta:"\u0397",Eum:"\xCB",Euml:"\xCB",Exists:"\u2203",ExponentialE:"\u2147",Fcy:"\u0424",Ffr:"\u{1D509}",FilledSmallSquare:"\u25FC",FilledVerySmallSquare:"\u25AA",Fopf:"\u{1D53D}",ForAll:"\u2200",Fouriertrf:"\u2131",Fscr:"\u2131",GJcy:"\u0403",G:">",GT:">",Gamma:"\u0393",Gammad:"\u03DC",Gbreve:"\u011E",Gcedil:"\u0122",Gcirc:"\u011C",Gcy:"\u0413",Gdot:"\u0120",Gfr:"\u{1D50A}",Gg:"\u22D9",Gopf:"\u{1D53E}",GreaterEqual:"\u2265",GreaterEqualLess:"\u22DB",GreaterFullEqual:"\u2267",GreaterGreater:"\u2AA2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2A7E",GreaterTilde:"\u2273",Gscr:"\u{1D4A2}",Gt:"\u226B",HARDcy:"\u042A",Hacek:"\u02C7",Hat:"^",Hcirc:"\u0124",Hfr:"\u210C",HilbertSpace:"\u210B",Hopf:"\u210D",HorizontalLine:"\u2500",Hscr:"\u210B",Hstrok:"\u0126",HumpDownHump:"\u224E",HumpEqual:"\u224F",IEcy:"\u0415",IJlig:"\u0132",IOcy:"\u0401",Iacut:"\xCD",Iacute:"\xCD",Icir:"\xCE",Icirc:"\xCE",Icy:"\u0418",Idot:"\u0130",Ifr:"\u2111",Igrav:"\xCC",Igrave:"\xCC",Im:"\u2111",Imacr:"\u012A",ImaginaryI:"\u2148",Implies:"\u21D2",Int:"\u222C",Integral:"\u222B",Intersection:"\u22C2",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",Iogon:"\u012E",Iopf:"\u{1D540}",Iota:"\u0399",Iscr:"\u2110",Itilde:"\u0128",Iukcy:"\u0406",Ium:"\xCF",Iuml:"\xCF",Jcirc:"\u0134",Jcy:"\u0419",Jfr:"\u{1D50D}",Jopf:"\u{1D541}",Jscr:"\u{1D4A5}",Jsercy:"\u0408",Jukcy:"\u0404",KHcy:"\u0425",KJcy:"\u040C",Kappa:"\u039A",Kcedil:"\u0136",Kcy:"\u041A",Kfr:"\u{1D50E}",Kopf:"\u{1D542}",Kscr:"\u{1D4A6}",LJcy:"\u0409",L:"<",LT:"<",Lacute:"\u0139",Lambda:"\u039B",Lang:"\u27EA",Laplacetrf:"\u2112",Larr:"\u219E",Lcaron:"\u013D",Lcedil:"\u013B",Lcy:"\u041B",LeftAngleBracket:"\u27E8",LeftArrow:"\u2190",LeftArrowBar:"\u21E4",LeftArrowRightArrow:"\u21C6",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27E6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21C3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230A",LeftRightArrow:"\u2194",LeftRightVector:"\u294E",LeftTee:"\u22A3",LeftTeeArrow:"\u21A4",LeftTeeVector:"\u295A",LeftTriangle:"\u22B2",LeftTriangleBar:"\u29CF",LeftTriangleEqual:"\u22B4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21BF",LeftUpVectorBar:"\u2958",LeftVector:"\u21BC",LeftVectorBar:"\u2952",Leftarrow:"\u21D0",Leftrightarrow:"\u21D4",LessEqualGreater:"\u22DA",LessFullEqual:"\u2266",LessGreater:"\u2276",LessLess:"\u2AA1",LessSlantEqual:"\u2A7D",LessTilde:"\u2272",Lfr:"\u{1D50F}",Ll:"\u22D8",Lleftarrow:"\u21DA",Lmidot:"\u013F",LongLeftArrow:"\u27F5",LongLeftRightArrow:"\u27F7",LongRightArrow:"\u27F6",Longleftarrow:"\u27F8",Longleftrightarrow:"\u27FA",Longrightarrow:"\u27F9",Lopf:"\u{1D543}",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",Lscr:"\u2112",Lsh:"\u21B0",Lstrok:"\u0141",Lt:"\u226A",Map:"\u2905",Mcy:"\u041C",MediumSpace:"\u205F",Mellintrf:"\u2133",Mfr:"\u{1D510}",MinusPlus:"\u2213",Mopf:"\u{1D544}",Mscr:"\u2133",Mu:"\u039C",NJcy:"\u040A",Nacute:"\u0143",Ncaron:"\u0147",Ncedil:"\u0145",Ncy:"\u041D",NegativeMediumSpace:"\u200B",NegativeThickSpace:"\u200B",NegativeThinSpace:"\u200B",NegativeVeryThinSpace:"\u200B",NestedGreaterGreater:"\u226B",NestedLessLess:"\u226A",NewLine:`
`,Nfr:"\u{1D511}",NoBreak:"\u2060",NonBreakingSpace:"\xA0",Nopf:"\u2115",Not:"\u2AEC",NotCongruent:"\u2262",NotCupCap:"\u226D",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226F",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226B\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2A7E\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224E\u0338",NotHumpEqual:"\u224F\u0338",NotLeftTriangle:"\u22EA",NotLeftTriangleBar:"\u29CF\u0338",NotLeftTriangleEqual:"\u22EC",NotLess:"\u226E",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226A\u0338",NotLessSlantEqual:"\u2A7D\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2AA2\u0338",NotNestedLessLess:"\u2AA1\u0338",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2AAF\u0338",NotPrecedesSlantEqual:"\u22E0",NotReverseElement:"\u220C",NotRightTriangle:"\u22EB",NotRightTriangleBar:"\u29D0\u0338",NotRightTriangleEqual:"\u22ED",NotSquareSubset:"\u228F\u0338",NotSquareSubsetEqual:"\u22E2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22E3",NotSubset:"\u2282\u20D2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2AB0\u0338",NotSucceedsSlantEqual:"\u22E1",NotSucceedsTilde:"\u227F\u0338",NotSuperset:"\u2283\u20D2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",Nscr:"\u{1D4A9}",Ntild:"\xD1",Ntilde:"\xD1",Nu:"\u039D",OElig:"\u0152",Oacut:"\xD3",Oacute:"\xD3",Ocir:"\xD4",Ocirc:"\xD4",Ocy:"\u041E",Odblac:"\u0150",Ofr:"\u{1D512}",Ograv:"\xD2",Ograve:"\xD2",Omacr:"\u014C",Omega:"\u03A9",Omicron:"\u039F",Oopf:"\u{1D546}",OpenCurlyDoubleQuote:"\u201C",OpenCurlyQuote:"\u2018",Or:"\u2A54",Oscr:"\u{1D4AA}",Oslas:"\xD8",Oslash:"\xD8",Otild:"\xD5",Otilde:"\xD5",Otimes:"\u2A37",Oum:"\xD6",Ouml:"\xD6",OverBar:"\u203E",OverBrace:"\u23DE",OverBracket:"\u23B4",OverParenthesis:"\u23DC",PartialD:"\u2202",Pcy:"\u041F",Pfr:"\u{1D513}",Phi:"\u03A6",Pi:"\u03A0",PlusMinus:"\xB1",Poincareplane:"\u210C",Popf:"\u2119",Pr:"\u2ABB",Precedes:"\u227A",PrecedesEqual:"\u2AAF",PrecedesSlantEqual:"\u227C",PrecedesTilde:"\u227E",Prime:"\u2033",Product:"\u220F",Proportion:"\u2237",Proportional:"\u221D",Pscr:"\u{1D4AB}",Psi:"\u03A8",QUO:'"',QUOT:'"',Qfr:"\u{1D514}",Qopf:"\u211A",Qscr:"\u{1D4AC}",RBarr:"\u2910",RE:"\xAE",REG:"\xAE",Racute:"\u0154",Rang:"\u27EB",Rarr:"\u21A0",Rarrtl:"\u2916",Rcaron:"\u0158",Rcedil:"\u0156",Rcy:"\u0420",Re:"\u211C",ReverseElement:"\u220B",ReverseEquilibrium:"\u21CB",ReverseUpEquilibrium:"\u296F",Rfr:"\u211C",Rho:"\u03A1",RightAngleBracket:"\u27E9",RightArrow:"\u2192",RightArrowBar:"\u21E5",RightArrowLeftArrow:"\u21C4",RightCeiling:"\u2309",RightDoubleBracket:"\u27E7",RightDownTeeVector:"\u295D",RightDownVector:"\u21C2",RightDownVectorBar:"\u2955",RightFloor:"\u230B",RightTee:"\u22A2",RightTeeArrow:"\u21A6",RightTeeVector:"\u295B",RightTriangle:"\u22B3",RightTriangleBar:"\u29D0",RightTriangleEqual:"\u22B5",RightUpDownVector:"\u294F",RightUpTeeVector:"\u295C",RightUpVector:"\u21BE",RightUpVectorBar:"\u2954",RightVector:"\u21C0",RightVectorBar:"\u2953",Rightarrow:"\u21D2",Ropf:"\u211D",RoundImplies:"\u2970",Rrightarrow:"\u21DB",Rscr:"\u211B",Rsh:"\u21B1",RuleDelayed:"\u29F4",SHCHcy:"\u0429",SHcy:"\u0428",SOFTcy:"\u042C",Sacute:"\u015A",Sc:"\u2ABC",Scaron:"\u0160",Scedil:"\u015E",Scirc:"\u015C",Scy:"\u0421",Sfr:"\u{1D516}",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",Sigma:"\u03A3",SmallCircle:"\u2218",Sopf:"\u{1D54A}",Sqrt:"\u221A",Square:"\u25A1",SquareIntersection:"\u2293",SquareSubset:"\u228F",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",Sscr:"\u{1D4AE}",Star:"\u22C6",Sub:"\u22D0",Subset:"\u22D0",SubsetEqual:"\u2286",Succeeds:"\u227B",SucceedsEqual:"\u2AB0",SucceedsSlantEqual:"\u227D",SucceedsTilde:"\u227F",SuchThat:"\u220B",Sum:"\u2211",Sup:"\u22D1",Superset:"\u2283",SupersetEqual:"\u2287",Supset:"\u22D1",THOR:"\xDE",THORN:"\xDE",TRADE:"\u2122",TSHcy:"\u040B",TScy:"\u0426",Tab:"	",Tau:"\u03A4",Tcaron:"\u0164",Tcedil:"\u0162",Tcy:"\u0422",Tfr:"\u{1D517}",Therefore:"\u2234",Theta:"\u0398",ThickSpace:"\u205F\u200A",ThinSpace:"\u2009",Tilde:"\u223C",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",Topf:"\u{1D54B}",TripleDot:"\u20DB",Tscr:"\u{1D4AF}",Tstrok:"\u0166",Uacut:"\xDA",Uacute:"\xDA",Uarr:"\u219F",Uarrocir:"\u2949",Ubrcy:"\u040E",Ubreve:"\u016C",Ucir:"\xDB",Ucirc:"\xDB",Ucy:"\u0423",Udblac:"\u0170",Ufr:"\u{1D518}",Ugrav:"\xD9",Ugrave:"\xD9",Umacr:"\u016A",UnderBar:"_",UnderBrace:"\u23DF",UnderBracket:"\u23B5",UnderParenthesis:"\u23DD",Union:"\u22C3",UnionPlus:"\u228E",Uogon:"\u0172",Uopf:"\u{1D54C}",UpArrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21C5",UpDownArrow:"\u2195",UpEquilibrium:"\u296E",UpTee:"\u22A5",UpTeeArrow:"\u21A5",Uparrow:"\u21D1",Updownarrow:"\u21D5",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03D2",Upsilon:"\u03A5",Uring:"\u016E",Uscr:"\u{1D4B0}",Utilde:"\u0168",Uum:"\xDC",Uuml:"\xDC",VDash:"\u22AB",Vbar:"\u2AEB",Vcy:"\u0412",Vdash:"\u22A9",Vdashl:"\u2AE6",Vee:"\u22C1",Verbar:"\u2016",Vert:"\u2016",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200A",Vfr:"\u{1D519}",Vopf:"\u{1D54D}",Vscr:"\u{1D4B1}",Vvdash:"\u22AA",Wcirc:"\u0174",Wedge:"\u22C0",Wfr:"\u{1D51A}",Wopf:"\u{1D54E}",Wscr:"\u{1D4B2}",Xfr:"\u{1D51B}",Xi:"\u039E",Xopf:"\u{1D54F}",Xscr:"\u{1D4B3}",YAcy:"\u042F",YIcy:"\u0407",YUcy:"\u042E",Yacut:"\xDD",Yacute:"\xDD",Ycirc:"\u0176",Ycy:"\u042B",Yfr:"\u{1D51C}",Yopf:"\u{1D550}",Yscr:"\u{1D4B4}",Yuml:"\u0178",ZHcy:"\u0416",Zacute:"\u0179",Zcaron:"\u017D",Zcy:"\u0417",Zdot:"\u017B",ZeroWidthSpace:"\u200B",Zeta:"\u0396",Zfr:"\u2128",Zopf:"\u2124",Zscr:"\u{1D4B5}",aacut:"\xE1",aacute:"\xE1",abreve:"\u0103",ac:"\u223E",acE:"\u223E\u0333",acd:"\u223F",acir:"\xE2",acirc:"\xE2",acut:"\xB4",acute:"\xB4",acy:"\u0430",aeli:"\xE6",aelig:"\xE6",af:"\u2061",afr:"\u{1D51E}",agrav:"\xE0",agrave:"\xE0",alefsym:"\u2135",aleph:"\u2135",alpha:"\u03B1",amacr:"\u0101",amalg:"\u2A3F",am:"&",amp:"&",and:"\u2227",andand:"\u2A55",andd:"\u2A5C",andslope:"\u2A58",andv:"\u2A5A",ang:"\u2220",ange:"\u29A4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29A8",angmsdab:"\u29A9",angmsdac:"\u29AA",angmsdad:"\u29AB",angmsdae:"\u29AC",angmsdaf:"\u29AD",angmsdag:"\u29AE",angmsdah:"\u29AF",angrt:"\u221F",angrtvb:"\u22BE",angrtvbd:"\u299D",angsph:"\u2222",angst:"\xC5",angzarr:"\u237C",aogon:"\u0105",aopf:"\u{1D552}",ap:"\u2248",apE:"\u2A70",apacir:"\u2A6F",ape:"\u224A",apid:"\u224B",apos:"'",approx:"\u2248",approxeq:"\u224A",arin:"\xE5",aring:"\xE5",ascr:"\u{1D4B6}",ast:"*",asymp:"\u2248",asympeq:"\u224D",atild:"\xE3",atilde:"\xE3",aum:"\xE4",auml:"\xE4",awconint:"\u2233",awint:"\u2A11",bNot:"\u2AED",backcong:"\u224C",backepsilon:"\u03F6",backprime:"\u2035",backsim:"\u223D",backsimeq:"\u22CD",barvee:"\u22BD",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23B5",bbrktbrk:"\u23B6",bcong:"\u224C",bcy:"\u0431",bdquo:"\u201E",becaus:"\u2235",because:"\u2235",bemptyv:"\u29B0",bepsi:"\u03F6",bernou:"\u212C",beta:"\u03B2",beth:"\u2136",between:"\u226C",bfr:"\u{1D51F}",bigcap:"\u22C2",bigcirc:"\u25EF",bigcup:"\u22C3",bigodot:"\u2A00",bigoplus:"\u2A01",bigotimes:"\u2A02",bigsqcup:"\u2A06",bigstar:"\u2605",bigtriangledown:"\u25BD",bigtriangleup:"\u25B3",biguplus:"\u2A04",bigvee:"\u22C1",bigwedge:"\u22C0",bkarow:"\u290D",blacklozenge:"\u29EB",blacksquare:"\u25AA",blacktriangle:"\u25B4",blacktriangledown:"\u25BE",blacktriangleleft:"\u25C2",blacktriangleright:"\u25B8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20E5",bnequiv:"\u2261\u20E5",bnot:"\u2310",bopf:"\u{1D553}",bot:"\u22A5",bottom:"\u22A5",bowtie:"\u22C8",boxDL:"\u2557",boxDR:"\u2554",boxDl:"\u2556",boxDr:"\u2553",boxH:"\u2550",boxHD:"\u2566",boxHU:"\u2569",boxHd:"\u2564",boxHu:"\u2567",boxUL:"\u255D",boxUR:"\u255A",boxUl:"\u255C",boxUr:"\u2559",boxV:"\u2551",boxVH:"\u256C",boxVL:"\u2563",boxVR:"\u2560",boxVh:"\u256B",boxVl:"\u2562",boxVr:"\u255F",boxbox:"\u29C9",boxdL:"\u2555",boxdR:"\u2552",boxdl:"\u2510",boxdr:"\u250C",boxh:"\u2500",boxhD:"\u2565",boxhU:"\u2568",boxhd:"\u252C",boxhu:"\u2534",boxminus:"\u229F",boxplus:"\u229E",boxtimes:"\u22A0",boxuL:"\u255B",boxuR:"\u2558",boxul:"\u2518",boxur:"\u2514",boxv:"\u2502",boxvH:"\u256A",boxvL:"\u2561",boxvR:"\u255E",boxvh:"\u253C",boxvl:"\u2524",boxvr:"\u251C",bprime:"\u2035",breve:"\u02D8",brvba:"\xA6",brvbar:"\xA6",bscr:"\u{1D4B7}",bsemi:"\u204F",bsim:"\u223D",bsime:"\u22CD",bsol:"\\",bsolb:"\u29C5",bsolhsub:"\u27C8",bull:"\u2022",bullet:"\u2022",bump:"\u224E",bumpE:"\u2AAE",bumpe:"\u224F",bumpeq:"\u224F",cacute:"\u0107",cap:"\u2229",capand:"\u2A44",capbrcup:"\u2A49",capcap:"\u2A4B",capcup:"\u2A47",capdot:"\u2A40",caps:"\u2229\uFE00",caret:"\u2041",caron:"\u02C7",ccaps:"\u2A4D",ccaron:"\u010D",ccedi:"\xE7",ccedil:"\xE7",ccirc:"\u0109",ccups:"\u2A4C",ccupssm:"\u2A50",cdot:"\u010B",cedi:"\xB8",cedil:"\xB8",cemptyv:"\u29B2",cen:"\xA2",cent:"\xA2",centerdot:"\xB7",cfr:"\u{1D520}",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",chi:"\u03C7",cir:"\u25CB",cirE:"\u29C3",circ:"\u02C6",circeq:"\u2257",circlearrowleft:"\u21BA",circlearrowright:"\u21BB",circledR:"\xAE",circledS:"\u24C8",circledast:"\u229B",circledcirc:"\u229A",circleddash:"\u229D",cire:"\u2257",cirfnint:"\u2A10",cirmid:"\u2AEF",cirscir:"\u29C2",clubs:"\u2663",clubsuit:"\u2663",colon:":",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2A6D",conint:"\u222E",copf:"\u{1D554}",coprod:"\u2210",cop:"\xA9",copy:"\xA9",copysr:"\u2117",crarr:"\u21B5",cross:"\u2717",cscr:"\u{1D4B8}",csub:"\u2ACF",csube:"\u2AD1",csup:"\u2AD0",csupe:"\u2AD2",ctdot:"\u22EF",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22DE",cuesc:"\u22DF",cularr:"\u21B6",cularrp:"\u293D",cup:"\u222A",cupbrcap:"\u2A48",cupcap:"\u2A46",cupcup:"\u2A4A",cupdot:"\u228D",cupor:"\u2A45",cups:"\u222A\uFE00",curarr:"\u21B7",curarrm:"\u293C",curlyeqprec:"\u22DE",curlyeqsucc:"\u22DF",curlyvee:"\u22CE",curlywedge:"\u22CF",curre:"\xA4",curren:"\xA4",curvearrowleft:"\u21B6",curvearrowright:"\u21B7",cuvee:"\u22CE",cuwed:"\u22CF",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232D",dArr:"\u21D3",dHar:"\u2965",dagger:"\u2020",daleth:"\u2138",darr:"\u2193",dash:"\u2010",dashv:"\u22A3",dbkarow:"\u290F",dblac:"\u02DD",dcaron:"\u010F",dcy:"\u0434",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21CA",ddotseq:"\u2A77",de:"\xB0",deg:"\xB0",delta:"\u03B4",demptyv:"\u29B1",dfisht:"\u297F",dfr:"\u{1D521}",dharl:"\u21C3",dharr:"\u21C2",diam:"\u22C4",diamond:"\u22C4",diamondsuit:"\u2666",diams:"\u2666",die:"\xA8",digamma:"\u03DD",disin:"\u22F2",div:"\xF7",divid:"\xF7",divide:"\xF7",divideontimes:"\u22C7",divonx:"\u22C7",djcy:"\u0452",dlcorn:"\u231E",dlcrop:"\u230D",dollar:"$",dopf:"\u{1D555}",dot:"\u02D9",doteq:"\u2250",doteqdot:"\u2251",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22A1",doublebarwedge:"\u2306",downarrow:"\u2193",downdownarrows:"\u21CA",downharpoonleft:"\u21C3",downharpoonright:"\u21C2",drbkarow:"\u2910",drcorn:"\u231F",drcrop:"\u230C",dscr:"\u{1D4B9}",dscy:"\u0455",dsol:"\u29F6",dstrok:"\u0111",dtdot:"\u22F1",dtri:"\u25BF",dtrif:"\u25BE",duarr:"\u21F5",duhar:"\u296F",dwangle:"\u29A6",dzcy:"\u045F",dzigrarr:"\u27FF",eDDot:"\u2A77",eDot:"\u2251",eacut:"\xE9",eacute:"\xE9",easter:"\u2A6E",ecaron:"\u011B",ecir:"\xEA",ecirc:"\xEA",ecolon:"\u2255",ecy:"\u044D",edot:"\u0117",ee:"\u2147",efDot:"\u2252",efr:"\u{1D522}",eg:"\u2A9A",egrav:"\xE8",egrave:"\xE8",egs:"\u2A96",egsdot:"\u2A98",el:"\u2A99",elinters:"\u23E7",ell:"\u2113",els:"\u2A95",elsdot:"\u2A97",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",emptyv:"\u2205",emsp13:"\u2004",emsp14:"\u2005",emsp:"\u2003",eng:"\u014B",ensp:"\u2002",eogon:"\u0119",eopf:"\u{1D556}",epar:"\u22D5",eparsl:"\u29E3",eplus:"\u2A71",epsi:"\u03B5",epsilon:"\u03B5",epsiv:"\u03F5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2A96",eqslantless:"\u2A95",equals:"=",equest:"\u225F",equiv:"\u2261",equivDD:"\u2A78",eqvparsl:"\u29E5",erDot:"\u2253",erarr:"\u2971",escr:"\u212F",esdot:"\u2250",esim:"\u2242",eta:"\u03B7",et:"\xF0",eth:"\xF0",eum:"\xEB",euml:"\xEB",euro:"\u20AC",excl:"!",exist:"\u2203",expectation:"\u2130",exponentiale:"\u2147",fallingdotseq:"\u2252",fcy:"\u0444",female:"\u2640",ffilig:"\uFB03",fflig:"\uFB00",ffllig:"\uFB04",ffr:"\u{1D523}",filig:"\uFB01",fjlig:"fj",flat:"\u266D",fllig:"\uFB02",fltns:"\u25B1",fnof:"\u0192",fopf:"\u{1D557}",forall:"\u2200",fork:"\u22D4",forkv:"\u2AD9",fpartint:"\u2A0D",frac1:"\xBC",frac12:"\xBD",frac13:"\u2153",frac14:"\xBC",frac15:"\u2155",frac16:"\u2159",frac18:"\u215B",frac23:"\u2154",frac25:"\u2156",frac3:"\xBE",frac34:"\xBE",frac35:"\u2157",frac38:"\u215C",frac45:"\u2158",frac56:"\u215A",frac58:"\u215D",frac78:"\u215E",frasl:"\u2044",frown:"\u2322",fscr:"\u{1D4BB}",gE:"\u2267",gEl:"\u2A8C",gacute:"\u01F5",gamma:"\u03B3",gammad:"\u03DD",gap:"\u2A86",gbreve:"\u011F",gcirc:"\u011D",gcy:"\u0433",gdot:"\u0121",ge:"\u2265",gel:"\u22DB",geq:"\u2265",geqq:"\u2267",geqslant:"\u2A7E",ges:"\u2A7E",gescc:"\u2AA9",gesdot:"\u2A80",gesdoto:"\u2A82",gesdotol:"\u2A84",gesl:"\u22DB\uFE00",gesles:"\u2A94",gfr:"\u{1D524}",gg:"\u226B",ggg:"\u22D9",gimel:"\u2137",gjcy:"\u0453",gl:"\u2277",glE:"\u2A92",gla:"\u2AA5",glj:"\u2AA4",gnE:"\u2269",gnap:"\u2A8A",gnapprox:"\u2A8A",gne:"\u2A88",gneq:"\u2A88",gneqq:"\u2269",gnsim:"\u22E7",gopf:"\u{1D558}",grave:"`",gscr:"\u210A",gsim:"\u2273",gsime:"\u2A8E",gsiml:"\u2A90",g:">",gt:">",gtcc:"\u2AA7",gtcir:"\u2A7A",gtdot:"\u22D7",gtlPar:"\u2995",gtquest:"\u2A7C",gtrapprox:"\u2A86",gtrarr:"\u2978",gtrdot:"\u22D7",gtreqless:"\u22DB",gtreqqless:"\u2A8C",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\uFE00",gvnE:"\u2269\uFE00",hArr:"\u21D4",hairsp:"\u200A",half:"\xBD",hamilt:"\u210B",hardcy:"\u044A",harr:"\u2194",harrcir:"\u2948",harrw:"\u21AD",hbar:"\u210F",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22B9",hfr:"\u{1D525}",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21FF",homtht:"\u223B",hookleftarrow:"\u21A9",hookrightarrow:"\u21AA",hopf:"\u{1D559}",horbar:"\u2015",hscr:"\u{1D4BD}",hslash:"\u210F",hstrok:"\u0127",hybull:"\u2043",hyphen:"\u2010",iacut:"\xED",iacute:"\xED",ic:"\u2063",icir:"\xEE",icirc:"\xEE",icy:"\u0438",iecy:"\u0435",iexc:"\xA1",iexcl:"\xA1",iff:"\u21D4",ifr:"\u{1D526}",igrav:"\xEC",igrave:"\xEC",ii:"\u2148",iiiint:"\u2A0C",iiint:"\u222D",iinfin:"\u29DC",iiota:"\u2129",ijlig:"\u0133",imacr:"\u012B",image:"\u2111",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22B7",imped:"\u01B5",in:"\u2208",incare:"\u2105",infin:"\u221E",infintie:"\u29DD",inodot:"\u0131",int:"\u222B",intcal:"\u22BA",integers:"\u2124",intercal:"\u22BA",intlarhk:"\u2A17",intprod:"\u2A3C",iocy:"\u0451",iogon:"\u012F",iopf:"\u{1D55A}",iota:"\u03B9",iprod:"\u2A3C",iques:"\xBF",iquest:"\xBF",iscr:"\u{1D4BE}",isin:"\u2208",isinE:"\u22F9",isindot:"\u22F5",isins:"\u22F4",isinsv:"\u22F3",isinv:"\u2208",it:"\u2062",itilde:"\u0129",iukcy:"\u0456",ium:"\xEF",iuml:"\xEF",jcirc:"\u0135",jcy:"\u0439",jfr:"\u{1D527}",jmath:"\u0237",jopf:"\u{1D55B}",jscr:"\u{1D4BF}",jsercy:"\u0458",jukcy:"\u0454",kappa:"\u03BA",kappav:"\u03F0",kcedil:"\u0137",kcy:"\u043A",kfr:"\u{1D528}",kgreen:"\u0138",khcy:"\u0445",kjcy:"\u045C",kopf:"\u{1D55C}",kscr:"\u{1D4C0}",lAarr:"\u21DA",lArr:"\u21D0",lAtail:"\u291B",lBarr:"\u290E",lE:"\u2266",lEg:"\u2A8B",lHar:"\u2962",lacute:"\u013A",laemptyv:"\u29B4",lagran:"\u2112",lambda:"\u03BB",lang:"\u27E8",langd:"\u2991",langle:"\u27E8",lap:"\u2A85",laqu:"\xAB",laquo:"\xAB",larr:"\u2190",larrb:"\u21E4",larrbfs:"\u291F",larrfs:"\u291D",larrhk:"\u21A9",larrlp:"\u21AB",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21A2",lat:"\u2AAB",latail:"\u2919",late:"\u2AAD",lates:"\u2AAD\uFE00",lbarr:"\u290C",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298B",lbrksld:"\u298F",lbrkslu:"\u298D",lcaron:"\u013E",lcedil:"\u013C",lceil:"\u2308",lcub:"{",lcy:"\u043B",ldca:"\u2936",ldquo:"\u201C",ldquor:"\u201E",ldrdhar:"\u2967",ldrushar:"\u294B",ldsh:"\u21B2",le:"\u2264",leftarrow:"\u2190",leftarrowtail:"\u21A2",leftharpoondown:"\u21BD",leftharpoonup:"\u21BC",leftleftarrows:"\u21C7",leftrightarrow:"\u2194",leftrightarrows:"\u21C6",leftrightharpoons:"\u21CB",leftrightsquigarrow:"\u21AD",leftthreetimes:"\u22CB",leg:"\u22DA",leq:"\u2264",leqq:"\u2266",leqslant:"\u2A7D",les:"\u2A7D",lescc:"\u2AA8",lesdot:"\u2A7F",lesdoto:"\u2A81",lesdotor:"\u2A83",lesg:"\u22DA\uFE00",lesges:"\u2A93",lessapprox:"\u2A85",lessdot:"\u22D6",lesseqgtr:"\u22DA",lesseqqgtr:"\u2A8B",lessgtr:"\u2276",lesssim:"\u2272",lfisht:"\u297C",lfloor:"\u230A",lfr:"\u{1D529}",lg:"\u2276",lgE:"\u2A91",lhard:"\u21BD",lharu:"\u21BC",lharul:"\u296A",lhblk:"\u2584",ljcy:"\u0459",ll:"\u226A",llarr:"\u21C7",llcorner:"\u231E",llhard:"\u296B",lltri:"\u25FA",lmidot:"\u0140",lmoust:"\u23B0",lmoustache:"\u23B0",lnE:"\u2268",lnap:"\u2A89",lnapprox:"\u2A89",lne:"\u2A87",lneq:"\u2A87",lneqq:"\u2268",lnsim:"\u22E6",loang:"\u27EC",loarr:"\u21FD",lobrk:"\u27E6",longleftarrow:"\u27F5",longleftrightarrow:"\u27F7",longmapsto:"\u27FC",longrightarrow:"\u27F6",looparrowleft:"\u21AB",looparrowright:"\u21AC",lopar:"\u2985",lopf:"\u{1D55D}",loplus:"\u2A2D",lotimes:"\u2A34",lowast:"\u2217",lowbar:"_",loz:"\u25CA",lozenge:"\u25CA",lozf:"\u29EB",lpar:"(",lparlt:"\u2993",lrarr:"\u21C6",lrcorner:"\u231F",lrhar:"\u21CB",lrhard:"\u296D",lrm:"\u200E",lrtri:"\u22BF",lsaquo:"\u2039",lscr:"\u{1D4C1}",lsh:"\u21B0",lsim:"\u2272",lsime:"\u2A8D",lsimg:"\u2A8F",lsqb:"[",lsquo:"\u2018",lsquor:"\u201A",lstrok:"\u0142",l:"<",lt:"<",ltcc:"\u2AA6",ltcir:"\u2A79",ltdot:"\u22D6",lthree:"\u22CB",ltimes:"\u22C9",ltlarr:"\u2976",ltquest:"\u2A7B",ltrPar:"\u2996",ltri:"\u25C3",ltrie:"\u22B4",ltrif:"\u25C2",lurdshar:"\u294A",luruhar:"\u2966",lvertneqq:"\u2268\uFE00",lvnE:"\u2268\uFE00",mDDot:"\u223A",mac:"\xAF",macr:"\xAF",male:"\u2642",malt:"\u2720",maltese:"\u2720",map:"\u21A6",mapsto:"\u21A6",mapstodown:"\u21A7",mapstoleft:"\u21A4",mapstoup:"\u21A5",marker:"\u25AE",mcomma:"\u2A29",mcy:"\u043C",mdash:"\u2014",measuredangle:"\u2221",mfr:"\u{1D52A}",mho:"\u2127",micr:"\xB5",micro:"\xB5",mid:"\u2223",midast:"*",midcir:"\u2AF0",middo:"\xB7",middot:"\xB7",minus:"\u2212",minusb:"\u229F",minusd:"\u2238",minusdu:"\u2A2A",mlcp:"\u2ADB",mldr:"\u2026",mnplus:"\u2213",models:"\u22A7",mopf:"\u{1D55E}",mp:"\u2213",mscr:"\u{1D4C2}",mstpos:"\u223E",mu:"\u03BC",multimap:"\u22B8",mumap:"\u22B8",nGg:"\u22D9\u0338",nGt:"\u226B\u20D2",nGtv:"\u226B\u0338",nLeftarrow:"\u21CD",nLeftrightarrow:"\u21CE",nLl:"\u22D8\u0338",nLt:"\u226A\u20D2",nLtv:"\u226A\u0338",nRightarrow:"\u21CF",nVDash:"\u22AF",nVdash:"\u22AE",nabla:"\u2207",nacute:"\u0144",nang:"\u2220\u20D2",nap:"\u2249",napE:"\u2A70\u0338",napid:"\u224B\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266E",natural:"\u266E",naturals:"\u2115",nbs:"\xA0",nbsp:"\xA0",nbump:"\u224E\u0338",nbumpe:"\u224F\u0338",ncap:"\u2A43",ncaron:"\u0148",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2A6D\u0338",ncup:"\u2A42",ncy:"\u043D",ndash:"\u2013",ne:"\u2260",neArr:"\u21D7",nearhk:"\u2924",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",nexist:"\u2204",nexists:"\u2204",nfr:"\u{1D52B}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2A7E\u0338",nges:"\u2A7E\u0338",ngsim:"\u2275",ngt:"\u226F",ngtr:"\u226F",nhArr:"\u21CE",nharr:"\u21AE",nhpar:"\u2AF2",ni:"\u220B",nis:"\u22FC",nisd:"\u22FA",niv:"\u220B",njcy:"\u045A",nlArr:"\u21CD",nlE:"\u2266\u0338",nlarr:"\u219A",nldr:"\u2025",nle:"\u2270",nleftarrow:"\u219A",nleftrightarrow:"\u21AE",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2A7D\u0338",nles:"\u2A7D\u0338",nless:"\u226E",nlsim:"\u2274",nlt:"\u226E",nltri:"\u22EA",nltrie:"\u22EC",nmid:"\u2224",nopf:"\u{1D55F}",no:"\xAC",not:"\xAC",notin:"\u2209",notinE:"\u22F9\u0338",notindot:"\u22F5\u0338",notinva:"\u2209",notinvb:"\u22F7",notinvc:"\u22F6",notni:"\u220C",notniva:"\u220C",notnivb:"\u22FE",notnivc:"\u22FD",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2AFD\u20E5",npart:"\u2202\u0338",npolint:"\u2A14",npr:"\u2280",nprcue:"\u22E0",npre:"\u2AAF\u0338",nprec:"\u2280",npreceq:"\u2AAF\u0338",nrArr:"\u21CF",nrarr:"\u219B",nrarrc:"\u2933\u0338",nrarrw:"\u219D\u0338",nrightarrow:"\u219B",nrtri:"\u22EB",nrtrie:"\u22ED",nsc:"\u2281",nsccue:"\u22E1",nsce:"\u2AB0\u0338",nscr:"\u{1D4C3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22E2",nsqsupe:"\u22E3",nsub:"\u2284",nsubE:"\u2AC5\u0338",nsube:"\u2288",nsubset:"\u2282\u20D2",nsubseteq:"\u2288",nsubseteqq:"\u2AC5\u0338",nsucc:"\u2281",nsucceq:"\u2AB0\u0338",nsup:"\u2285",nsupE:"\u2AC6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20D2",nsupseteq:"\u2289",nsupseteqq:"\u2AC6\u0338",ntgl:"\u2279",ntild:"\xF1",ntilde:"\xF1",ntlg:"\u2278",ntriangleleft:"\u22EA",ntrianglelefteq:"\u22EC",ntriangleright:"\u22EB",ntrianglerighteq:"\u22ED",nu:"\u03BD",num:"#",numero:"\u2116",numsp:"\u2007",nvDash:"\u22AD",nvHarr:"\u2904",nvap:"\u224D\u20D2",nvdash:"\u22AC",nvge:"\u2265\u20D2",nvgt:">\u20D2",nvinfin:"\u29DE",nvlArr:"\u2902",nvle:"\u2264\u20D2",nvlt:"<\u20D2",nvltrie:"\u22B4\u20D2",nvrArr:"\u2903",nvrtrie:"\u22B5\u20D2",nvsim:"\u223C\u20D2",nwArr:"\u21D6",nwarhk:"\u2923",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",oS:"\u24C8",oacut:"\xF3",oacute:"\xF3",oast:"\u229B",ocir:"\xF4",ocirc:"\xF4",ocy:"\u043E",odash:"\u229D",odblac:"\u0151",odiv:"\u2A38",odot:"\u2299",odsold:"\u29BC",oelig:"\u0153",ofcir:"\u29BF",ofr:"\u{1D52C}",ogon:"\u02DB",ograv:"\xF2",ograve:"\xF2",ogt:"\u29C1",ohbar:"\u29B5",ohm:"\u03A9",oint:"\u222E",olarr:"\u21BA",olcir:"\u29BE",olcross:"\u29BB",oline:"\u203E",olt:"\u29C0",omacr:"\u014D",omega:"\u03C9",omicron:"\u03BF",omid:"\u29B6",ominus:"\u2296",oopf:"\u{1D560}",opar:"\u29B7",operp:"\u29B9",oplus:"\u2295",or:"\u2228",orarr:"\u21BB",ord:"\xBA",order:"\u2134",orderof:"\u2134",ordf:"\xAA",ordm:"\xBA",origof:"\u22B6",oror:"\u2A56",orslope:"\u2A57",orv:"\u2A5B",oscr:"\u2134",oslas:"\xF8",oslash:"\xF8",osol:"\u2298",otild:"\xF5",otilde:"\xF5",otimes:"\u2297",otimesas:"\u2A36",oum:"\xF6",ouml:"\xF6",ovbar:"\u233D",par:"\xB6",para:"\xB6",parallel:"\u2225",parsim:"\u2AF3",parsl:"\u2AFD",part:"\u2202",pcy:"\u043F",percnt:"%",period:".",permil:"\u2030",perp:"\u22A5",pertenk:"\u2031",pfr:"\u{1D52D}",phi:"\u03C6",phiv:"\u03D5",phmmat:"\u2133",phone:"\u260E",pi:"\u03C0",pitchfork:"\u22D4",piv:"\u03D6",planck:"\u210F",planckh:"\u210E",plankv:"\u210F",plus:"+",plusacir:"\u2A23",plusb:"\u229E",pluscir:"\u2A22",plusdo:"\u2214",plusdu:"\u2A25",pluse:"\u2A72",plusm:"\xB1",plusmn:"\xB1",plussim:"\u2A26",plustwo:"\u2A27",pm:"\xB1",pointint:"\u2A15",popf:"\u{1D561}",poun:"\xA3",pound:"\xA3",pr:"\u227A",prE:"\u2AB3",prap:"\u2AB7",prcue:"\u227C",pre:"\u2AAF",prec:"\u227A",precapprox:"\u2AB7",preccurlyeq:"\u227C",preceq:"\u2AAF",precnapprox:"\u2AB9",precneqq:"\u2AB5",precnsim:"\u22E8",precsim:"\u227E",prime:"\u2032",primes:"\u2119",prnE:"\u2AB5",prnap:"\u2AB9",prnsim:"\u22E8",prod:"\u220F",profalar:"\u232E",profline:"\u2312",profsurf:"\u2313",prop:"\u221D",propto:"\u221D",prsim:"\u227E",prurel:"\u22B0",pscr:"\u{1D4C5}",psi:"\u03C8",puncsp:"\u2008",qfr:"\u{1D52E}",qint:"\u2A0C",qopf:"\u{1D562}",qprime:"\u2057",qscr:"\u{1D4C6}",quaternions:"\u210D",quatint:"\u2A16",quest:"?",questeq:"\u225F",quo:'"',quot:'"',rAarr:"\u21DB",rArr:"\u21D2",rAtail:"\u291C",rBarr:"\u290F",rHar:"\u2964",race:"\u223D\u0331",racute:"\u0155",radic:"\u221A",raemptyv:"\u29B3",rang:"\u27E9",rangd:"\u2992",range:"\u29A5",rangle:"\u27E9",raqu:"\xBB",raquo:"\xBB",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21E5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291E",rarrhk:"\u21AA",rarrlp:"\u21AC",rarrpl:"\u2945",rarrsim:"\u2974",rarrtl:"\u21A3",rarrw:"\u219D",ratail:"\u291A",ratio:"\u2236",rationals:"\u211A",rbarr:"\u290D",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298C",rbrksld:"\u298E",rbrkslu:"\u2990",rcaron:"\u0159",rcedil:"\u0157",rceil:"\u2309",rcub:"}",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201D",rdquor:"\u201D",rdsh:"\u21B3",real:"\u211C",realine:"\u211B",realpart:"\u211C",reals:"\u211D",rect:"\u25AD",re:"\xAE",reg:"\xAE",rfisht:"\u297D",rfloor:"\u230B",rfr:"\u{1D52F}",rhard:"\u21C1",rharu:"\u21C0",rharul:"\u296C",rho:"\u03C1",rhov:"\u03F1",rightarrow:"\u2192",rightarrowtail:"\u21A3",rightharpoondown:"\u21C1",rightharpoonup:"\u21C0",rightleftarrows:"\u21C4",rightleftharpoons:"\u21CC",rightrightarrows:"\u21C9",rightsquigarrow:"\u219D",rightthreetimes:"\u22CC",ring:"\u02DA",risingdotseq:"\u2253",rlarr:"\u21C4",rlhar:"\u21CC",rlm:"\u200F",rmoust:"\u23B1",rmoustache:"\u23B1",rnmid:"\u2AEE",roang:"\u27ED",roarr:"\u21FE",robrk:"\u27E7",ropar:"\u2986",ropf:"\u{1D563}",roplus:"\u2A2E",rotimes:"\u2A35",rpar:")",rpargt:"\u2994",rppolint:"\u2A12",rrarr:"\u21C9",rsaquo:"\u203A",rscr:"\u{1D4C7}",rsh:"\u21B1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22CC",rtimes:"\u22CA",rtri:"\u25B9",rtrie:"\u22B5",rtrif:"\u25B8",rtriltri:"\u29CE",ruluhar:"\u2968",rx:"\u211E",sacute:"\u015B",sbquo:"\u201A",sc:"\u227B",scE:"\u2AB4",scap:"\u2AB8",scaron:"\u0161",sccue:"\u227D",sce:"\u2AB0",scedil:"\u015F",scirc:"\u015D",scnE:"\u2AB6",scnap:"\u2ABA",scnsim:"\u22E9",scpolint:"\u2A13",scsim:"\u227F",scy:"\u0441",sdot:"\u22C5",sdotb:"\u22A1",sdote:"\u2A66",seArr:"\u21D8",searhk:"\u2925",searr:"\u2198",searrow:"\u2198",sec:"\xA7",sect:"\xA7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",sfr:"\u{1D530}",sfrown:"\u2322",sharp:"\u266F",shchcy:"\u0449",shcy:"\u0448",shortmid:"\u2223",shortparallel:"\u2225",sh:"\xAD",shy:"\xAD",sigma:"\u03C3",sigmaf:"\u03C2",sigmav:"\u03C2",sim:"\u223C",simdot:"\u2A6A",sime:"\u2243",simeq:"\u2243",simg:"\u2A9E",simgE:"\u2AA0",siml:"\u2A9D",simlE:"\u2A9F",simne:"\u2246",simplus:"\u2A24",simrarr:"\u2972",slarr:"\u2190",smallsetminus:"\u2216",smashp:"\u2A33",smeparsl:"\u29E4",smid:"\u2223",smile:"\u2323",smt:"\u2AAA",smte:"\u2AAC",smtes:"\u2AAC\uFE00",softcy:"\u044C",sol:"/",solb:"\u29C4",solbar:"\u233F",sopf:"\u{1D564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\uFE00",sqcup:"\u2294",sqcups:"\u2294\uFE00",sqsub:"\u228F",sqsube:"\u2291",sqsubset:"\u228F",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25A1",square:"\u25A1",squarf:"\u25AA",squf:"\u25AA",srarr:"\u2192",sscr:"\u{1D4C8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22C6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03F5",straightphi:"\u03D5",strns:"\xAF",sub:"\u2282",subE:"\u2AC5",subdot:"\u2ABD",sube:"\u2286",subedot:"\u2AC3",submult:"\u2AC1",subnE:"\u2ACB",subne:"\u228A",subplus:"\u2ABF",subrarr:"\u2979",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2AC5",subsetneq:"\u228A",subsetneqq:"\u2ACB",subsim:"\u2AC7",subsub:"\u2AD5",subsup:"\u2AD3",succ:"\u227B",succapprox:"\u2AB8",succcurlyeq:"\u227D",succeq:"\u2AB0",succnapprox:"\u2ABA",succneqq:"\u2AB6",succnsim:"\u22E9",succsim:"\u227F",sum:"\u2211",sung:"\u266A",sup:"\u2283",sup1:"\xB9",sup2:"\xB2",sup3:"\xB3",supE:"\u2AC6",supdot:"\u2ABE",supdsub:"\u2AD8",supe:"\u2287",supedot:"\u2AC4",suphsol:"\u27C9",suphsub:"\u2AD7",suplarr:"\u297B",supmult:"\u2AC2",supnE:"\u2ACC",supne:"\u228B",supplus:"\u2AC0",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2AC6",supsetneq:"\u228B",supsetneqq:"\u2ACC",supsim:"\u2AC8",supsub:"\u2AD4",supsup:"\u2AD6",swArr:"\u21D9",swarhk:"\u2926",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292A",szli:"\xDF",szlig:"\xDF",target:"\u2316",tau:"\u03C4",tbrk:"\u23B4",tcaron:"\u0165",tcedil:"\u0163",tcy:"\u0442",tdot:"\u20DB",telrec:"\u2315",tfr:"\u{1D531}",there4:"\u2234",therefore:"\u2234",theta:"\u03B8",thetasym:"\u03D1",thetav:"\u03D1",thickapprox:"\u2248",thicksim:"\u223C",thinsp:"\u2009",thkap:"\u2248",thksim:"\u223C",thor:"\xFE",thorn:"\xFE",tilde:"\u02DC",time:"\xD7",times:"\xD7",timesb:"\u22A0",timesbar:"\u2A31",timesd:"\u2A30",tint:"\u222D",toea:"\u2928",top:"\u22A4",topbot:"\u2336",topcir:"\u2AF1",topf:"\u{1D565}",topfork:"\u2ADA",tosa:"\u2929",tprime:"\u2034",trade:"\u2122",triangle:"\u25B5",triangledown:"\u25BF",triangleleft:"\u25C3",trianglelefteq:"\u22B4",triangleq:"\u225C",triangleright:"\u25B9",trianglerighteq:"\u22B5",tridot:"\u25EC",trie:"\u225C",triminus:"\u2A3A",triplus:"\u2A39",trisb:"\u29CD",tritime:"\u2A3B",trpezium:"\u23E2",tscr:"\u{1D4C9}",tscy:"\u0446",tshcy:"\u045B",tstrok:"\u0167",twixt:"\u226C",twoheadleftarrow:"\u219E",twoheadrightarrow:"\u21A0",uArr:"\u21D1",uHar:"\u2963",uacut:"\xFA",uacute:"\xFA",uarr:"\u2191",ubrcy:"\u045E",ubreve:"\u016D",ucir:"\xFB",ucirc:"\xFB",ucy:"\u0443",udarr:"\u21C5",udblac:"\u0171",udhar:"\u296E",ufisht:"\u297E",ufr:"\u{1D532}",ugrav:"\xF9",ugrave:"\xF9",uharl:"\u21BF",uharr:"\u21BE",uhblk:"\u2580",ulcorn:"\u231C",ulcorner:"\u231C",ulcrop:"\u230F",ultri:"\u25F8",umacr:"\u016B",um:"\xA8",uml:"\xA8",uogon:"\u0173",uopf:"\u{1D566}",uparrow:"\u2191",updownarrow:"\u2195",upharpoonleft:"\u21BF",upharpoonright:"\u21BE",uplus:"\u228E",upsi:"\u03C5",upsih:"\u03D2",upsilon:"\u03C5",upuparrows:"\u21C8",urcorn:"\u231D",urcorner:"\u231D",urcrop:"\u230E",uring:"\u016F",urtri:"\u25F9",uscr:"\u{1D4CA}",utdot:"\u22F0",utilde:"\u0169",utri:"\u25B5",utrif:"\u25B4",uuarr:"\u21C8",uum:"\xFC",uuml:"\xFC",uwangle:"\u29A7",vArr:"\u21D5",vBar:"\u2AE8",vBarv:"\u2AE9",vDash:"\u22A8",vangrt:"\u299C",varepsilon:"\u03F5",varkappa:"\u03F0",varnothing:"\u2205",varphi:"\u03D5",varpi:"\u03D6",varpropto:"\u221D",varr:"\u2195",varrho:"\u03F1",varsigma:"\u03C2",varsubsetneq:"\u228A\uFE00",varsubsetneqq:"\u2ACB\uFE00",varsupsetneq:"\u228B\uFE00",varsupsetneqq:"\u2ACC\uFE00",vartheta:"\u03D1",vartriangleleft:"\u22B2",vartriangleright:"\u22B3",vcy:"\u0432",vdash:"\u22A2",vee:"\u2228",veebar:"\u22BB",veeeq:"\u225A",vellip:"\u22EE",verbar:"|",vert:"|",vfr:"\u{1D533}",vltri:"\u22B2",vnsub:"\u2282\u20D2",vnsup:"\u2283\u20D2",vopf:"\u{1D567}",vprop:"\u221D",vrtri:"\u22B3",vscr:"\u{1D4CB}",vsubnE:"\u2ACB\uFE00",vsubne:"\u228A\uFE00",vsupnE:"\u2ACC\uFE00",vsupne:"\u228B\uFE00",vzigzag:"\u299A",wcirc:"\u0175",wedbar:"\u2A5F",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",wfr:"\u{1D534}",wopf:"\u{1D568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",wscr:"\u{1D4CC}",xcap:"\u22C2",xcirc:"\u25EF",xcup:"\u22C3",xdtri:"\u25BD",xfr:"\u{1D535}",xhArr:"\u27FA",xharr:"\u27F7",xi:"\u03BE",xlArr:"\u27F8",xlarr:"\u27F5",xmap:"\u27FC",xnis:"\u22FB",xodot:"\u2A00",xopf:"\u{1D569}",xoplus:"\u2A01",xotime:"\u2A02",xrArr:"\u27F9",xrarr:"\u27F6",xscr:"\u{1D4CD}",xsqcup:"\u2A06",xuplus:"\u2A04",xutri:"\u25B3",xvee:"\u22C1",xwedge:"\u22C0",yacut:"\xFD",yacute:"\xFD",yacy:"\u044F",ycirc:"\u0177",ycy:"\u044B",ye:"\xA5",yen:"\xA5",yfr:"\u{1D536}",yicy:"\u0457",yopf:"\u{1D56A}",yscr:"\u{1D4CE}",yucy:"\u044E",yum:"\xFF",yuml:"\xFF",zacute:"\u017A",zcaron:"\u017E",zcy:"\u0437",zdot:"\u017C",zeetrf:"\u2128",zeta:"\u03B6",zfr:"\u{1D537}",zhcy:"\u0436",zigrarr:"\u21DD",zopf:"\u{1D56B}",zscr:"\u{1D4CF}",zwj:"\u200D",zwnj:"\u200C"}}}),Wl=S({"node_modules/parse-entities/decode-entity.js"(e,r){"use strict";I();var u=Xl();r.exports=a;var t={}.hasOwnProperty;function a(n){return t.call(u,n)?u[n]:!1}}}),xr=S({"node_modules/parse-entities/index.js"(e,r){"use strict";I();var u=Ul(),t=Gl(),a=Me(),n=Vl(),s=Hl(),c=Wl();r.exports=J;var i={}.hasOwnProperty,D=String.fromCharCode,o=Function.prototype,l={warning:null,reference:null,text:null,warningContext:null,referenceContext:null,textContext:null,position:{},additional:null,attribute:!1,nonTerminated:!0},d=9,p=10,g=12,F=32,E=38,b=59,f=60,x=61,v=35,h=88,m=120,C=65533,w="named",q="hexadecimal",L="decimal",B={};B[q]=16,B[L]=10;var O={};O[w]=s,O[L]=a,O[q]=n;var T=1,P=2,A=3,j=4,H=5,G=6,X=7,R={};R[T]="Named character references must be terminated by a semicolon",R[P]="Numeric character references must be terminated by a semicolon",R[A]="Named character references cannot be empty",R[j]="Numeric character references cannot be empty",R[H]="Named character references must be known",R[G]="Numeric character references cannot be disallowed",R[X]="Numeric character references cannot be outside the permissible Unicode range";function J(k,y){var _={},N,V;y||(y={});for(V in l)N=y[V],_[V]=N==null?l[V]:N;return(_.position.indent||_.position.start)&&(_.indent=_.position.indent||[],_.position=_.position.start),z(k,_)}function z(k,y){var _=y.additional,N=y.nonTerminated,V=y.text,W=y.reference,K=y.warning,ee=y.textContext,Y=y.referenceContext,ue=y.warningContext,le=y.position,ce=y.indent||[],te=k.length,Z=0,Q=-1,De=le.column||1,ye=le.line||1,fe="",he=[],ae,pe,ne,re,we,oe,ie,Ce,rr,br,qe,$e,_e,xe,Fu,Ue,ur,ge,se;for(typeof _=="string"&&(_=_.charCodeAt(0)),Ue=Ge(),Ce=K?Da:o,Z--,te++;++Z<te;)if(we===p&&(De=ce[Q]||1),we=k.charCodeAt(Z),we===E){if(ie=k.charCodeAt(Z+1),ie===d||ie===p||ie===g||ie===F||ie===E||ie===f||ie!==ie||_&&ie===_){fe+=D(we),De++;continue}for(_e=Z+1,$e=_e,se=_e,ie===v?(se=++$e,ie=k.charCodeAt(se),ie===h||ie===m?(xe=q,se=++$e):xe=L):xe=w,ae="",qe="",re="",Fu=O[xe],se--;++se<te&&(ie=k.charCodeAt(se),!!Fu(ie));)re+=D(ie),xe===w&&i.call(u,re)&&(ae=re,qe=u[re]);ne=k.charCodeAt(se)===b,ne&&(se++,pe=xe===w?c(re):!1,pe&&(ae=re,qe=pe)),ge=1+se-_e,!ne&&!N||(re?xe===w?(ne&&!qe?Ce(H,1):(ae!==re&&(se=$e+ae.length,ge=1+se-$e,ne=!1),ne||(rr=ae?T:A,y.attribute?(ie=k.charCodeAt(se),ie===x?(Ce(rr,ge),qe=null):s(ie)?qe=null:Ce(rr,ge)):Ce(rr,ge))),oe=qe):(ne||Ce(P,ge),oe=parseInt(re,B[xe]),M(oe)?(Ce(X,ge),oe=D(C)):oe in t?(Ce(G,ge),oe=t[oe]):(br="",U(oe)&&Ce(G,ge),oe>65535&&(oe-=65536,br+=D(oe>>>10|55296),oe=56320|oe&1023),oe=br+D(oe))):xe!==w&&Ce(j,ge)),oe?(Au(),Ue=Ge(),Z=se-1,De+=se-_e+1,he.push(oe),ur=Ge(),ur.offset++,W&&W.call(Y,oe,{start:Ue,end:ur},k.slice(_e-1,se)),Ue=ur):(re=k.slice(_e-1,se),fe+=re,De+=re.length,Z=se-1)}else we===10&&(ye++,Q++,De=0),we===we?(fe+=D(we),De++):Au();return he.join("");function Ge(){return{line:ye,column:De,offset:Z+(le.offset||0)}}function Da(xu,bu){var yr=Ge();yr.column+=bu,yr.offset+=bu,K.call(ue,R[xu],yr,xu)}function Au(){fe&&(he.push(fe),V&&V.call(ee,fe,{start:Ue,end:Ge()}),fe="")}}function M(k){return k>=55296&&k<=57343||k>1114111}function U(k){return k>=1&&k<=8||k===11||k>=13&&k<=31||k>=127&&k<=159||k>=64976&&k<=65007||(k&65535)===65535||(k&65535)===65534}}}),Kl=S({"node_modules/remark-parse/lib/decode.js"(e,r){"use strict";I();var u=Pe(),t=xr();r.exports=a;function a(n){return c.raw=i,c;function s(o){for(var l=n.offset,d=o.line,p=[];++d&&d in l;)p.push((l[d]||0)+1);return{start:o,indent:p}}function c(o,l,d){t(o,{position:s(l),warning:D,text:d,reference:d,textContext:n,referenceContext:n})}function i(o,l,d){return t(o,u(d,{position:s(l),warning:D}))}function D(o,l,d){d!==3&&n.file.message(o,l)}}}}),Yl=S({"node_modules/remark-parse/lib/tokenizer.js"(e,r){"use strict";I(),r.exports=u;function u(s){return c;function c(i,D){var o=this,l=o.offset,d=[],p=o[s+"Methods"],g=o[s+"Tokenizers"],F=D.line,E=D.column,b,f,x,v,h,m;if(!i)return d;for(P.now=q,P.file=o.file,C("");i;){for(b=-1,f=p.length,h=!1;++b<f&&(v=p[b],x=g[v],!(x&&(!x.onlyAtStart||o.atStart)&&(!x.notInList||!o.inList)&&(!x.notInBlock||!o.inBlock)&&(!x.notInLink||!o.inLink)&&(m=i.length,x.apply(o,[P,i]),h=m!==i.length,h))););h||o.file.fail(new Error("Infinite loop"),P.now())}return o.eof=q(),d;function C(A){for(var j=-1,H=A.indexOf(`
`);H!==-1;)F++,j=H,H=A.indexOf(`
`,H+1);j===-1?E+=A.length:E=A.length-j,F in l&&(j!==-1?E+=l[F]:E<=l[F]&&(E=l[F]+1))}function w(){var A=[],j=F+1;return function(){for(var H=F+1;j<H;)A.push((l[j]||0)+1),j++;return A}}function q(){var A={line:F,column:E};return A.offset=o.toOffset(A),A}function L(A){this.start=A,this.end=q()}function B(A){i.slice(0,A.length)!==A&&o.file.fail(new Error("Incorrectly eaten value: please report this warning on https://git.io/vg5Ft"),q())}function O(){var A=q();return j;function j(H,G){var X=H.position,R=X?X.start:A,J=[],z=X&&X.end.line,M=A.line;if(H.position=new L(R),X&&G&&X.indent){if(J=X.indent,z<M){for(;++z<M;)J.push((l[z]||0)+1);J.push(A.column)}G=J.concat(G)}return H.position.indent=G||[],H}}function T(A,j){var H=j?j.children:d,G=H[H.length-1],X;return G&&A.type===G.type&&(A.type==="text"||A.type==="blockquote")&&t(G)&&t(A)&&(X=A.type==="text"?a:n,A=X.call(o,G,A)),A!==G&&H.push(A),o.atStart&&d.length!==0&&o.exitStart(),A}function P(A){var j=w(),H=O(),G=q();return B(A),X.reset=R,R.test=J,X.test=J,i=i.slice(A.length),C(A),j=j(),X;function X(z,M){return H(T(H(z),M),j)}function R(){var z=X.apply(null,arguments);return F=G.line,E=G.column,i=A+i,z}function J(){var z=H({});return F=G.line,E=G.column,i=A+i,z.position}}}}function t(s){var c,i;return s.type!=="text"||!s.position?!0:(c=s.position.start,i=s.position.end,c.line!==i.line||i.column-c.column===s.value.length)}function a(s,c){return s.value+=c.value,s}function n(s,c){return this.options.commonmark||this.options.gfm?c:(s.children=s.children.concat(c.children),s)}}}),Jl=S({"node_modules/markdown-escapes/index.js"(e,r){"use strict";I(),r.exports=n;var u=["\\","`","*","{","}","[","]","(",")","#","+","-",".","!","_",">"],t=u.concat(["~","|"]),a=t.concat([`
`,'"',"$","%","&","'",",","/",":",";","<","=","?","@","^"]);n.default=u,n.gfm=t,n.commonmark=a;function n(s){var c=s||{};return c.commonmark?a:c.gfm?t:u}}}),Zl=S({"node_modules/remark-parse/lib/block-elements.js"(e,r){"use strict";I(),r.exports=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","iframe","legend","li","link","main","menu","menuitem","meta","nav","noframes","ol","optgroup","option","p","param","pre","section","source","title","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"]}}),$i=S({"node_modules/remark-parse/lib/defaults.js"(e,r){"use strict";I(),r.exports={position:!0,gfm:!0,commonmark:!1,pedantic:!1,blocks:Zl()}}}),Ql=S({"node_modules/remark-parse/lib/set-options.js"(e,r){"use strict";I();var u=Pe(),t=Jl(),a=$i();r.exports=n;function n(s){var c=this,i=c.options,D,o;if(s==null)s={};else if(typeof s=="object")s=u(s);else throw new Error("Invalid value `"+s+"` for setting `options`");for(D in a){if(o=s[D],o==null&&(o=i[D]),D!=="blocks"&&typeof o!="boolean"||D==="blocks"&&typeof o!="object")throw new Error("Invalid value `"+o+"` for setting `options."+D+"`");s[D]=o}return c.options=s,c.escape=t(s),c}}}),eD=S({"node_modules/unist-util-is/convert.js"(e,r){"use strict";I(),r.exports=u;function u(c){if(c==null)return s;if(typeof c=="string")return n(c);if(typeof c=="object")return"length"in c?a(c):t(c);if(typeof c=="function")return c;throw new Error("Expected function, string, or object as test")}function t(c){return i;function i(D){var o;for(o in c)if(D[o]!==c[o])return!1;return!0}}function a(c){for(var i=[],D=-1;++D<c.length;)i[D]=u(c[D]);return o;function o(){for(var l=-1;++l<i.length;)if(i[l].apply(this,arguments))return!0;return!1}}function n(c){return i;function i(D){return Boolean(D&&D.type===c)}}function s(){return!0}}}),rD=S({"node_modules/unist-util-visit-parents/color.browser.js"(e,r){I(),r.exports=u;function u(t){return t}}}),uD=S({"node_modules/unist-util-visit-parents/index.js"(e,r){"use strict";I(),r.exports=c;var u=eD(),t=rD(),a=!0,n="skip",s=!1;c.CONTINUE=a,c.SKIP=n,c.EXIT=s;function c(D,o,l,d){var p,g;typeof o=="function"&&typeof l!="function"&&(d=l,l=o,o=null),g=u(o),p=d?-1:1,F(D,null,[])();function F(E,b,f){var x=typeof E=="object"&&E!==null?E:{},v;return typeof x.type=="string"&&(v=typeof x.tagName=="string"?x.tagName:typeof x.name=="string"?x.name:void 0,h.displayName="node ("+t(x.type+(v?"<"+v+">":""))+")"),h;function h(){var m=f.concat(E),C=[],w,q;if((!o||g(E,b,f[f.length-1]||null))&&(C=i(l(E,f)),C[0]===s))return C;if(E.children&&C[0]!==n)for(q=(d?E.children.length:-1)+p;q>-1&&q<E.children.length;){if(w=F(E.children[q],q,m)(),w[0]===s)return w;q=typeof w[1]=="number"?w[1]:q+p}return C}}}function i(D){return D!==null&&typeof D=="object"&&"length"in D?D:typeof D=="number"?[a,D]:[D]}}}),tD=S({"node_modules/unist-util-visit/index.js"(e,r){"use strict";I(),r.exports=s;var u=uD(),t=u.CONTINUE,a=u.SKIP,n=u.EXIT;s.CONTINUE=t,s.SKIP=a,s.EXIT=n;function s(c,i,D,o){typeof i=="function"&&typeof D!="function"&&(o=D,D=i,i=null),u(c,i,l,o);function l(d,p){var g=p[p.length-1],F=g?g.children.indexOf(d):null;return D(d,F,g)}}}}),nD=S({"node_modules/unist-util-remove-position/index.js"(e,r){"use strict";I();var u=tD();r.exports=t;function t(s,c){return u(s,c?a:n),s}function a(s){delete s.position}function n(s){s.position=void 0}}}),iD=S({"node_modules/remark-parse/lib/parse.js"(e,r){"use strict";I();var u=Pe(),t=nD();r.exports=s;var a=`
`,n=/\r\n|\r/g;function s(){var c=this,i=String(c.file),D={line:1,column:1,offset:0},o=u(D),l;return i=i.replace(n,a),i.charCodeAt(0)===65279&&(i=i.slice(1),o.column++,o.offset++),l={type:"root",children:c.tokenizeBlock(i,o),position:{start:D,end:c.eof||u(D)}},c.options.position||t(l,!0),l}}}),aD=S({"node_modules/remark-parse/lib/tokenize/blank-line.js"(e,r){"use strict";I();var u=/^[ \t]*(\n|$)/;r.exports=t;function t(a,n,s){for(var c,i="",D=0,o=n.length;D<o&&(c=u.exec(n.slice(D)),c!=null);)D+=c[0].length,i+=c[0];if(i!==""){if(s)return!0;a(i)}}}}),mu=S({"node_modules/repeat-string/index.js"(e,r){"use strict";I();var u="",t;r.exports=a;function a(n,s){if(typeof n!="string")throw new TypeError("expected a string");if(s===1)return n;if(s===2)return n+n;var c=n.length*s;if(t!==n||typeof t>"u")t=n,u="";else if(u.length>=c)return u.substr(0,c);for(;c>u.length&&s>1;)s&1&&(u+=n),s>>=1,n+=n;return u+=n,u=u.substr(0,c),u}}}),Ui=S({"node_modules/trim-trailing-lines/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){return String(t).replace(/\n+$/,"")}}}),oD=S({"node_modules/remark-parse/lib/tokenize/code-indented.js"(e,r){"use strict";I();var u=mu(),t=Ui();r.exports=D;var a=`
`,n="	",s=" ",c=4,i=u(s,c);function D(o,l,d){for(var p=-1,g=l.length,F="",E="",b="",f="",x,v,h;++p<g;)if(x=l.charAt(p),h)if(h=!1,F+=b,E+=f,b="",f="",x===a)b=x,f=x;else for(F+=x,E+=x;++p<g;){if(x=l.charAt(p),!x||x===a){f=x,b=x;break}F+=x,E+=x}else if(x===s&&l.charAt(p+1)===x&&l.charAt(p+2)===x&&l.charAt(p+3)===x)b+=i,p+=3,h=!0;else if(x===n)b+=x,h=!0;else{for(v="";x===n||x===s;)v+=x,x=l.charAt(++p);if(x!==a)break;b+=v+x,f+=x}if(E)return d?!0:o(F)({type:"code",lang:null,meta:null,value:t(E)})}}}),sD=S({"node_modules/remark-parse/lib/tokenize/code-fenced.js"(e,r){"use strict";I(),r.exports=D;var u=`
`,t="	",a=" ",n="~",s="`",c=3,i=4;function D(o,l,d){var p=this,g=p.options.gfm,F=l.length+1,E=0,b="",f,x,v,h,m,C,w,q,L,B,O,T,P;if(g){for(;E<F&&(v=l.charAt(E),!(v!==a&&v!==t));)b+=v,E++;if(T=E,v=l.charAt(E),!(v!==n&&v!==s)){for(E++,x=v,f=1,b+=v;E<F&&(v=l.charAt(E),v===x);)b+=v,f++,E++;if(!(f<c)){for(;E<F&&(v=l.charAt(E),!(v!==a&&v!==t));)b+=v,E++;for(h="",w="";E<F&&(v=l.charAt(E),!(v===u||x===s&&v===x));)v===a||v===t?w+=v:(h+=w+v,w=""),E++;if(v=l.charAt(E),!(v&&v!==u)){if(d)return!0;P=o.now(),P.column+=b.length,P.offset+=b.length,b+=h,h=p.decode.raw(p.unescape(h),P),w&&(b+=w),w="",B="",O="",q="",L="";for(var A=!0;E<F;){if(v=l.charAt(E),q+=B,L+=O,B="",O="",v!==u){q+=v,O+=v,E++;continue}for(A?(b+=v,A=!1):(B+=v,O+=v),w="",E++;E<F&&(v=l.charAt(E),v===a);)w+=v,E++;if(B+=w,O+=w.slice(T),!(w.length>=i)){for(w="";E<F&&(v=l.charAt(E),v===x);)w+=v,E++;if(B+=w,O+=w,!(w.length<f)){for(w="";E<F&&(v=l.charAt(E),!(v!==a&&v!==t));)B+=v,O+=v,E++;if(!v||v===u)break}}}for(b+=q+B,E=-1,F=h.length;++E<F;)if(v=h.charAt(E),v===a||v===t)m||(m=h.slice(0,E));else if(m){C=h.slice(E);break}return o(b)({type:"code",lang:m||h||null,meta:C||null,value:L})}}}}}}}),ze=S({"node_modules/trim/index.js"(e,r){I(),e=r.exports=u;function u(t){return t.replace(/^\s*|\s*$/g,"")}e.left=function(t){return t.replace(/^\s*/,"")},e.right=function(t){return t.replace(/\s*$/,"")}}}),Eu=S({"node_modules/remark-parse/lib/util/interrupt.js"(e,r){"use strict";I(),r.exports=u;function u(t,a,n,s){for(var c=t.length,i=-1,D,o;++i<c;)if(D=t[i],o=D[1]||{},!(o.pedantic!==void 0&&o.pedantic!==n.options.pedantic)&&!(o.commonmark!==void 0&&o.commonmark!==n.options.commonmark)&&a[D[0]].apply(n,s))return!0;return!1}}}),cD=S({"node_modules/remark-parse/lib/tokenize/blockquote.js"(e,r){"use strict";I();var u=ze(),t=Eu();r.exports=i;var a=`
`,n="	",s=" ",c=">";function i(D,o,l){for(var d=this,p=d.offset,g=d.blockTokenizers,F=d.interruptBlockquote,E=D.now(),b=E.line,f=o.length,x=[],v=[],h=[],m,C=0,w,q,L,B,O,T,P,A;C<f&&(w=o.charAt(C),!(w!==s&&w!==n));)C++;if(o.charAt(C)===c){if(l)return!0;for(C=0;C<f;){for(L=o.indexOf(a,C),T=C,P=!1,L===-1&&(L=f);C<f&&(w=o.charAt(C),!(w!==s&&w!==n));)C++;if(o.charAt(C)===c?(C++,P=!0,o.charAt(C)===s&&C++):C=T,B=o.slice(C,L),!P&&!u(B)){C=T;break}if(!P&&(q=o.slice(C),t(F,g,d,[D,q,!0])))break;O=T===C?B:o.slice(T,L),h.push(C-T),x.push(O),v.push(B),C=L+1}for(C=-1,f=h.length,m=D(x.join(a));++C<f;)p[b]=(p[b]||0)+h[C],b++;return A=d.enterBlock(),v=d.tokenizeBlock(v.join(a),E),A(),m({type:"blockquote",children:v})}}}}),lD=S({"node_modules/remark-parse/lib/tokenize/heading-atx.js"(e,r){"use strict";I(),r.exports=c;var u=`
`,t="	",a=" ",n="#",s=6;function c(i,D,o){for(var l=this,d=l.options.pedantic,p=D.length+1,g=-1,F=i.now(),E="",b="",f,x,v;++g<p;){if(f=D.charAt(g),f!==a&&f!==t){g--;break}E+=f}for(v=0;++g<=p;){if(f=D.charAt(g),f!==n){g--;break}E+=f,v++}if(!(v>s)&&!(!v||!d&&D.charAt(g+1)===n)){for(p=D.length+1,x="";++g<p;){if(f=D.charAt(g),f!==a&&f!==t){g--;break}x+=f}if(!(!d&&x.length===0&&f&&f!==u)){if(o)return!0;for(E+=x,x="",b="";++g<p&&(f=D.charAt(g),!(!f||f===u));){if(f!==a&&f!==t&&f!==n){b+=x+f,x="";continue}for(;f===a||f===t;)x+=f,f=D.charAt(++g);if(!d&&b&&!x&&f===n){b+=f;continue}for(;f===n;)x+=f,f=D.charAt(++g);for(;f===a||f===t;)x+=f,f=D.charAt(++g);g--}return F.column+=E.length,F.offset+=E.length,E+=b+x,i(E)({type:"heading",depth:v,children:l.tokenizeInline(b,F)})}}}}}),DD=S({"node_modules/remark-parse/lib/tokenize/thematic-break.js"(e,r){"use strict";I(),r.exports=D;var u="	",t=`
`,a=" ",n="*",s="-",c="_",i=3;function D(o,l,d){for(var p=-1,g=l.length+1,F="",E,b,f,x;++p<g&&(E=l.charAt(p),!(E!==u&&E!==a));)F+=E;if(!(E!==n&&E!==s&&E!==c))for(b=E,F+=E,f=1,x="";++p<g;)if(E=l.charAt(p),E===b)f++,F+=x+b,x="";else if(E===a)x+=E;else return f>=i&&(!E||E===t)?(F+=x,d?!0:o(F)({type:"thematicBreak"})):void 0}}}),Gi=S({"node_modules/remark-parse/lib/util/get-indentation.js"(e,r){"use strict";I(),r.exports=s;var u="	",t=" ",a=1,n=4;function s(c){for(var i=0,D=0,o=c.charAt(i),l={},d,p=0;o===u||o===t;){for(d=o===u?n:a,D+=d,d>1&&(D=Math.floor(D/d)*d);p<D;)l[++p]=i;o=c.charAt(++i)}return{indent:D,stops:l}}}}),fD=S({"node_modules/remark-parse/lib/util/remove-indentation.js"(e,r){"use strict";I();var u=ze(),t=mu(),a=Gi();r.exports=i;var n=`
`,s=" ",c="!";function i(D,o){var l=D.split(n),d=l.length+1,p=1/0,g=[],F,E,b;for(l.unshift(t(s,o)+c);d--;)if(E=a(l[d]),g[d]=E.stops,u(l[d]).length!==0)if(E.indent)E.indent>0&&E.indent<p&&(p=E.indent);else{p=1/0;break}if(p!==1/0)for(d=l.length;d--;){for(b=g[d],F=p;F&&!(F in b);)F--;l[d]=l[d].slice(b[F]+1)}return l.shift(),l.join(n)}}}),pD=S({"node_modules/remark-parse/lib/tokenize/list.js"(e,r){"use strict";I();var u=ze(),t=mu(),a=Me(),n=Gi(),s=fD(),c=Eu();r.exports=w;var i="*",D="_",o="+",l="-",d=".",p=" ",g=`
`,F="	",E=")",b="x",f=4,x=/\n\n(?!\s*$)/,v=/^\[([ X\tx])][ \t]/,h=/^([ \t]*)([*+-]|\d+[.)])( {1,4}(?! )| |\t|$|(?=\n))([^\n]*)/,m=/^([ \t]*)([*+-]|\d+[.)])([ \t]+)/,C=/^( {1,4}|\t)?/gm;function w(O,T,P){for(var A=this,j=A.options.commonmark,H=A.options.pedantic,G=A.blockTokenizers,X=A.interruptList,R=0,J=T.length,z=null,M,U,k,y,_,N,V,W,K,ee,Y,ue,le,ce,te,Z,Q,De,ye,fe=!1,he,ae,pe,ne;R<J&&(y=T.charAt(R),!(y!==F&&y!==p));)R++;if(y=T.charAt(R),y===i||y===o||y===l)_=y,k=!1;else{for(k=!0,U="";R<J&&(y=T.charAt(R),!!a(y));)U+=y,R++;if(y=T.charAt(R),!U||!(y===d||j&&y===E)||P&&U!=="1")return;z=parseInt(U,10),_=y}if(y=T.charAt(++R),!(y!==p&&y!==F&&(H||y!==g&&y!==""))){if(P)return!0;for(R=0,ce=[],te=[],Z=[];R<J;){for(N=T.indexOf(g,R),V=R,W=!1,ne=!1,N===-1&&(N=J),M=0;R<J;){if(y=T.charAt(R),y===F)M+=f-M%f;else if(y===p)M++;else break;R++}if(Q&&M>=Q.indent&&(ne=!0),y=T.charAt(R),K=null,!ne){if(y===i||y===o||y===l)K=y,R++,M++;else{for(U="";R<J&&(y=T.charAt(R),!!a(y));)U+=y,R++;y=T.charAt(R),R++,U&&(y===d||j&&y===E)&&(K=y,M+=U.length+1)}if(K)if(y=T.charAt(R),y===F)M+=f-M%f,R++;else if(y===p){for(pe=R+f;R<pe&&T.charAt(R)===p;)R++,M++;R===pe&&T.charAt(R)===p&&(R-=f-1,M-=f-1)}else y!==g&&y!==""&&(K=null)}if(K){if(!H&&_!==K)break;W=!0}else!j&&!ne&&T.charAt(V)===p?ne=!0:j&&Q&&(ne=M>=Q.indent||M>f),W=!1,R=V;if(Y=T.slice(V,N),ee=V===R?Y:T.slice(R,N),(K===i||K===D||K===l)&&G.thematicBreak.call(A,O,Y,!0))break;if(ue=le,le=!W&&!u(ee).length,ne&&Q)Q.value=Q.value.concat(Z,Y),te=te.concat(Z,Y),Z=[];else if(W)Z.length!==0&&(fe=!0,Q.value.push(""),Q.trail=Z.concat()),Q={value:[Y],indent:M,trail:[]},ce.push(Q),te=te.concat(Z,Y),Z=[];else if(le){if(ue&&!j)break;Z.push(Y)}else{if(ue||c(X,G,A,[O,Y,!0]))break;Q.value=Q.value.concat(Z,Y),te=te.concat(Z,Y),Z=[]}R=N+1}for(he=O(te.join(g)).reset({type:"list",ordered:k,start:z,spread:fe,children:[]}),De=A.enterList(),ye=A.enterBlock(),R=-1,J=ce.length;++R<J;)Q=ce[R].value.join(g),ae=O.now(),O(Q)(q(A,Q,ae),he),Q=ce[R].trail.join(g),R!==J-1&&(Q+=g),O(Q);return De(),ye(),he}}function q(O,T,P){var A=O.offset,j=O.options.pedantic?L:B,H=null,G,X;return T=j.apply(null,arguments),O.options.gfm&&(G=T.match(v),G&&(X=G[0].length,H=G[1].toLowerCase()===b,A[P.line]+=X,T=T.slice(X))),{type:"listItem",spread:x.test(T),checked:H,children:O.tokenizeBlock(T,P)}}function L(O,T,P){var A=O.offset,j=P.line;return T=T.replace(m,H),j=P.line,T.replace(C,H);function H(G){return A[j]=(A[j]||0)+G.length,j++,""}}function B(O,T,P){var A=O.offset,j=P.line,H,G,X,R,J,z,M;for(T=T.replace(h,U),R=T.split(g),J=s(T,n(H).indent).split(g),J[0]=X,A[j]=(A[j]||0)+G.length,j++,z=0,M=R.length;++z<M;)A[j]=(A[j]||0)+R[z].length-J[z].length,j++;return J.join(g);function U(k,y,_,N,V){return G=y+_+N,X=V,Number(_)<10&&G.length%2===1&&(_=p+_),H=y+t(p,_.length)+N,H+X}}}}),dD=S({"node_modules/remark-parse/lib/tokenize/heading-setext.js"(e,r){"use strict";I(),r.exports=o;var u=`
`,t="	",a=" ",n="=",s="-",c=3,i=1,D=2;function o(l,d,p){for(var g=this,F=l.now(),E=d.length,b=-1,f="",x,v,h,m,C;++b<E;){if(h=d.charAt(b),h!==a||b>=c){b--;break}f+=h}for(x="",v="";++b<E;){if(h=d.charAt(b),h===u){b--;break}h===a||h===t?v+=h:(x+=v+h,v="")}if(F.column+=f.length,F.offset+=f.length,f+=x+v,h=d.charAt(++b),m=d.charAt(++b),!(h!==u||m!==n&&m!==s)){for(f+=h,v=m,C=m===n?i:D;++b<E;){if(h=d.charAt(b),h!==m){if(h!==u)return;b--;break}v+=h}return p?!0:l(f+v)({type:"heading",depth:C,children:g.tokenizeInline(x,F)})}}}}),Vi=S({"node_modules/remark-parse/lib/util/html.js"(e){"use strict";I();var r="[a-zA-Z_:][a-zA-Z0-9:._-]*",u="[^\"'=<>`\\u0000-\\u0020]+",t="'[^']*'",a='"[^"]*"',n="(?:"+u+"|"+t+"|"+a+")",s="(?:\\s+"+r+"(?:\\s*=\\s*"+n+")?)",c="<[A-Za-z][A-Za-z0-9\\-]*"+s+"*\\s*\\/?>",i="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",D="<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->",o="<[?].*?[?]>",l="<![A-Za-z]+\\s+[^>]*>",d="<!\\[CDATA\\[[\\s\\S]*?\\]\\]>";e.openCloseTag=new RegExp("^(?:"+c+"|"+i+")"),e.tag=new RegExp("^(?:"+c+"|"+i+"|"+D+"|"+o+"|"+l+"|"+d+")")}}),hD=S({"node_modules/remark-parse/lib/tokenize/html-block.js"(e,r){"use strict";I();var u=Vi().openCloseTag;r.exports=x;var t="	",a=" ",n=`
`,s="<",c=/^<(script|pre|style)(?=(\s|>|$))/i,i=/<\/(script|pre|style)>/i,D=/^<!--/,o=/-->/,l=/^<\?/,d=/\?>/,p=/^<![A-Za-z]/,g=/>/,F=/^<!\[CDATA\[/,E=/]]>/,b=/^$/,f=new RegExp(u.source+"\\s*$");function x(v,h,m){for(var C=this,w=C.options.blocks.join("|"),q=new RegExp("^</?("+w+")(?=(\\s|/?>|$))","i"),L=h.length,B=0,O,T,P,A,j,H,G,X=[[c,i,!0],[D,o,!0],[l,d,!0],[p,g,!0],[F,E,!0],[q,b,!0],[f,b,!1]];B<L&&(A=h.charAt(B),!(A!==t&&A!==a));)B++;if(h.charAt(B)===s){for(O=h.indexOf(n,B+1),O=O===-1?L:O,T=h.slice(B,O),P=-1,j=X.length;++P<j;)if(X[P][0].test(T)){H=X[P];break}if(H){if(m)return H[2];if(B=O,!H[1].test(T))for(;B<L;){if(O=h.indexOf(n,B+1),O=O===-1?L:O,T=h.slice(B+1,O),H[1].test(T)){T&&(B=O);break}B=O}return G=h.slice(0,B),v(G)({type:"html",value:G})}}}}}),be=S({"node_modules/is-whitespace-character/index.js"(e,r){"use strict";I(),r.exports=a;var u=String.fromCharCode,t=/\s/;function a(n){return t.test(typeof n=="number"?u(n):n.charAt(0))}}}),vD=S({"node_modules/collapse-white-space/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){return String(t).replace(/\s+/g," ")}}}),Hi=S({"node_modules/remark-parse/lib/util/normalize.js"(e,r){"use strict";I();var u=vD();r.exports=t;function t(a){return u(a).toLowerCase()}}}),mD=S({"node_modules/remark-parse/lib/tokenize/definition.js"(e,r){"use strict";I();var u=be(),t=Hi();r.exports=b;var a='"',n="'",s="\\",c=`
`,i="	",D=" ",o="[",l="]",d="(",p=")",g=":",F="<",E=">";function b(v,h,m){for(var C=this,w=C.options.commonmark,q=0,L=h.length,B="",O,T,P,A,j,H,G,X;q<L&&(A=h.charAt(q),!(A!==D&&A!==i));)B+=A,q++;if(A=h.charAt(q),A===o){for(q++,B+=A,P="";q<L&&(A=h.charAt(q),A!==l);)A===s&&(P+=A,q++,A=h.charAt(q)),P+=A,q++;if(!(!P||h.charAt(q)!==l||h.charAt(q+1)!==g)){for(H=P,B+=P+l+g,q=B.length,P="";q<L&&(A=h.charAt(q),!(A!==i&&A!==D&&A!==c));)B+=A,q++;if(A=h.charAt(q),P="",O=B,A===F){for(q++;q<L&&(A=h.charAt(q),!!f(A));)P+=A,q++;if(A=h.charAt(q),A===f.delimiter)B+=F+P+A,q++;else{if(w)return;q-=P.length+1,P=""}}if(!P){for(;q<L&&(A=h.charAt(q),!!x(A));)P+=A,q++;B+=P}if(P){for(G=P,P="";q<L&&(A=h.charAt(q),!(A!==i&&A!==D&&A!==c));)P+=A,q++;if(A=h.charAt(q),j=null,A===a?j=a:A===n?j=n:A===d&&(j=p),!j)P="",q=B.length;else if(P){for(B+=P+A,q=B.length,P="";q<L&&(A=h.charAt(q),A!==j);){if(A===c){if(q++,A=h.charAt(q),A===c||A===j)return;P+=c}P+=A,q++}if(A=h.charAt(q),A!==j)return;T=B,B+=P+A,q++,X=P,P=""}else return;for(;q<L&&(A=h.charAt(q),!(A!==i&&A!==D));)B+=A,q++;if(A=h.charAt(q),!A||A===c)return m?!0:(O=v(O).test().end,G=C.decode.raw(C.unescape(G),O,{nonTerminated:!1}),X&&(T=v(T).test().end,X=C.decode.raw(C.unescape(X),T)),v(B)({type:"definition",identifier:t(H),label:H,title:X||null,url:G}))}}}}function f(v){return v!==E&&v!==o&&v!==l}f.delimiter=E;function x(v){return v!==o&&v!==l&&!u(v)}}}),ED=S({"node_modules/remark-parse/lib/tokenize/table.js"(e,r){"use strict";I();var u=be();r.exports=F;var t="	",a=`
`,n=" ",s="-",c=":",i="\\",D="|",o=1,l=2,d="left",p="center",g="right";function F(E,b,f){var x=this,v,h,m,C,w,q,L,B,O,T,P,A,j,H,G,X,R,J,z,M,U,k;if(x.options.gfm){for(v=0,X=0,q=b.length+1,L=[];v<q;){if(M=b.indexOf(a,v),U=b.indexOf(D,v+1),M===-1&&(M=b.length),U===-1||U>M){if(X<l)return;break}L.push(b.slice(v,M)),X++,v=M+1}for(C=L.join(a),h=L.splice(1,1)[0]||[],v=0,q=h.length,X--,m=!1,P=[];v<q;){if(O=h.charAt(v),O===D){if(T=null,m===!1){if(k===!1)return}else P.push(m),m=!1;k=!1}else if(O===s)T=!0,m=m||null;else if(O===c)m===d?m=p:T&&m===null?m=g:m=d;else if(!u(O))return;v++}if(m!==!1&&P.push(m),!(P.length<o)){if(f)return!0;for(G=-1,J=[],z=E(C).reset({type:"table",align:P,children:J});++G<X;){for(R=L[G],w={type:"tableRow",children:[]},G&&E(a),E(R).reset(w,z),q=R.length+1,v=0,B="",A="",j=!0;v<q;){if(O=R.charAt(v),O===t||O===n){A?B+=O:E(O),v++;continue}O===""||O===D?j?E(O):((A||O)&&!j&&(C=A,B.length>1&&(O?(C+=B.slice(0,-1),B=B.charAt(B.length-1)):(C+=B,B="")),H=E.now(),E(C)({type:"tableCell",children:x.tokenizeInline(A,H)},w)),E(B+O),B="",A=""):(B&&(A+=B,B=""),A+=O,O===i&&v!==q-2&&(A+=R.charAt(v+1),v++)),j=!1,v++}G||E(a+h)}return z}}}}}),CD=S({"node_modules/remark-parse/lib/tokenize/paragraph.js"(e,r){"use strict";I();var u=ze(),t=Ui(),a=Eu();r.exports=D;var n="	",s=`
`,c=" ",i=4;function D(o,l,d){for(var p=this,g=p.options,F=g.commonmark,E=p.blockTokenizers,b=p.interruptParagraph,f=l.indexOf(s),x=l.length,v,h,m,C,w;f<x;){if(f===-1){f=x;break}if(l.charAt(f+1)===s)break;if(F){for(C=0,v=f+1;v<x;){if(m=l.charAt(v),m===n){C=i;break}else if(m===c)C++;else break;v++}if(C>=i&&m!==s){f=l.indexOf(s,f+1);continue}}if(h=l.slice(f+1),a(b,E,p,[o,h,!0]))break;if(v=f,f=l.indexOf(s,f+1),f!==-1&&u(l.slice(v,f))===""){f=v;break}}return h=l.slice(0,f),d?!0:(w=o.now(),h=t(h),o(h)({type:"paragraph",children:p.tokenizeInline(h,w)}))}}}),gD=S({"node_modules/remark-parse/lib/locate/escape.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){return t.indexOf("\\",a)}}}),FD=S({"node_modules/remark-parse/lib/tokenize/escape.js"(e,r){"use strict";I();var u=gD();r.exports=n,n.locator=u;var t=`
`,a="\\";function n(s,c,i){var D=this,o,l;if(c.charAt(0)===a&&(o=c.charAt(1),D.escape.indexOf(o)!==-1))return i?!0:(o===t?l={type:"break"}:l={type:"text",value:o},s(a+o)(l))}}}),Xi=S({"node_modules/remark-parse/lib/locate/tag.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){return t.indexOf("<",a)}}}),AD=S({"node_modules/remark-parse/lib/tokenize/auto-link.js"(e,r){"use strict";I();var u=be(),t=xr(),a=Xi();r.exports=l,l.locator=a,l.notInLink=!0;var n="<",s=">",c="@",i="/",D="mailto:",o=D.length;function l(d,p,g){var F=this,E="",b=p.length,f=0,x="",v=!1,h="",m,C,w,q,L;if(p.charAt(0)===n){for(f++,E=n;f<b&&(m=p.charAt(f),!(u(m)||m===s||m===c||m===":"&&p.charAt(f+1)===i));)x+=m,f++;if(x){if(h+=x,x="",m=p.charAt(f),h+=m,f++,m===c)v=!0;else{if(m!==":"||p.charAt(f+1)!==i)return;h+=i,f++}for(;f<b&&(m=p.charAt(f),!(u(m)||m===s));)x+=m,f++;if(m=p.charAt(f),!(!x||m!==s))return g?!0:(h+=x,w=h,E+=h+m,C=d.now(),C.column++,C.offset++,v&&(h.slice(0,o).toLowerCase()===D?(w=w.slice(o),C.column+=o,C.offset+=o):h=D+h),q=F.inlineTokenizers,F.inlineTokenizers={text:q.text},L=F.enterLink(),w=F.tokenizeInline(w,C),F.inlineTokenizers=q,L(),d(E)({type:"link",title:null,url:t(h,{nonTerminated:!1}),children:w}))}}}}}),xD=S({"node_modules/ccount/index.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){var n=String(t),s=0,c;if(typeof a!="string")throw new Error("Expected character");for(c=n.indexOf(a);c!==-1;)s++,c=n.indexOf(a,c+a.length);return s}}}),bD=S({"node_modules/remark-parse/lib/locate/url.js"(e,r){"use strict";I(),r.exports=t;var u=["www.","http://","https://"];function t(a,n){var s=-1,c,i,D;if(!this.options.gfm)return s;for(i=u.length,c=-1;++c<i;)D=a.indexOf(u[c],n),D!==-1&&(s===-1||D<s)&&(s=D);return s}}}),yD=S({"node_modules/remark-parse/lib/tokenize/url.js"(e,r){"use strict";I();var u=xD(),t=xr(),a=Me(),n=er(),s=be(),c=bD();r.exports=C,C.locator=c,C.notInLink=!0;var i=33,D=38,o=41,l=42,d=44,p=45,g=46,F=58,E=59,b=63,f=60,x=95,v=126,h="(",m=")";function C(w,q,L){var B=this,O=B.options.gfm,T=B.inlineTokenizers,P=q.length,A=-1,j=!1,H,G,X,R,J,z,M,U,k,y,_,N,V,W;if(O){if(q.slice(0,4)==="www.")j=!0,R=4;else if(q.slice(0,7).toLowerCase()==="http://")R=7;else if(q.slice(0,8).toLowerCase()==="https://")R=8;else return;for(A=R-1,X=R,H=[];R<P;){if(M=q.charCodeAt(R),M===g){if(A===R-1)break;H.push(R),A=R,R++;continue}if(a(M)||n(M)||M===p||M===x){R++;continue}break}if(M===g&&(H.pop(),R--),H[0]!==void 0&&(G=H.length<2?X:H[H.length-2]+1,q.slice(G,R).indexOf("_")===-1)){if(L)return!0;for(U=R,J=R;R<P&&(M=q.charCodeAt(R),!(s(M)||M===f));)R++,M===i||M===l||M===d||M===g||M===F||M===b||M===x||M===v||(U=R);if(R=U,q.charCodeAt(R-1)===o)for(z=q.slice(J,R),k=u(z,h),y=u(z,m);y>k;)R=J+z.lastIndexOf(m),z=q.slice(J,R),y--;if(q.charCodeAt(R-1)===E&&(R--,n(q.charCodeAt(R-1)))){for(U=R-2;n(q.charCodeAt(U));)U--;q.charCodeAt(U)===D&&(R=U)}return _=q.slice(0,R),V=t(_,{nonTerminated:!1}),j&&(V="http://"+V),W=B.enterLink(),B.inlineTokenizers={text:T.text},N=B.tokenizeInline(_,w.now()),B.inlineTokenizers=T,W(),w(_)({type:"link",title:null,url:V,children:N})}}}}}),wD=S({"node_modules/remark-parse/lib/locate/email.js"(e,r){"use strict";I();var u=Me(),t=er(),a=43,n=45,s=46,c=95;r.exports=i;function i(o,l){var d=this,p,g;if(!this.options.gfm||(p=o.indexOf("@",l),p===-1))return-1;if(g=p,g===l||!D(o.charCodeAt(g-1)))return i.call(d,o,p+1);for(;g>l&&D(o.charCodeAt(g-1));)g--;return g}function D(o){return u(o)||t(o)||o===a||o===n||o===s||o===c}}}),BD=S({"node_modules/remark-parse/lib/tokenize/email.js"(e,r){"use strict";I();var u=xr(),t=Me(),a=er(),n=wD();r.exports=l,l.locator=n,l.notInLink=!0;var s=43,c=45,i=46,D=64,o=95;function l(d,p,g){var F=this,E=F.options.gfm,b=F.inlineTokenizers,f=0,x=p.length,v=-1,h,m,C,w;if(E){for(h=p.charCodeAt(f);t(h)||a(h)||h===s||h===c||h===i||h===o;)h=p.charCodeAt(++f);if(f!==0&&h===D){for(f++;f<x;){if(h=p.charCodeAt(f),t(h)||a(h)||h===c||h===i||h===o){f++,v===-1&&h===i&&(v=f);continue}break}if(!(v===-1||v===f||h===c||h===o))return h===i&&f--,m=p.slice(0,f),g?!0:(w=F.enterLink(),F.inlineTokenizers={text:b.text},C=F.tokenizeInline(m,d.now()),F.inlineTokenizers=b,w(),d(m)({type:"link",title:null,url:"mailto:"+u(m,{nonTerminated:!1}),children:C}))}}}}}),kD=S({"node_modules/remark-parse/lib/tokenize/html-inline.js"(e,r){"use strict";I();var u=er(),t=Xi(),a=Vi().tag;r.exports=l,l.locator=t;var n="<",s="?",c="!",i="/",D=/^<a /i,o=/^<\/a>/i;function l(d,p,g){var F=this,E=p.length,b,f;if(!(p.charAt(0)!==n||E<3)&&(b=p.charAt(1),!(!u(b)&&b!==s&&b!==c&&b!==i)&&(f=p.match(a),!!f)))return g?!0:(f=f[0],!F.inLink&&D.test(f)?F.inLink=!0:F.inLink&&o.test(f)&&(F.inLink=!1),d(f)({type:"html",value:f}))}}}),Wi=S({"node_modules/remark-parse/lib/locate/link.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){var n=t.indexOf("[",a),s=t.indexOf("![",a);return s===-1||n<s?n:s}}}),qD=S({"node_modules/remark-parse/lib/tokenize/link.js"(e,r){"use strict";I();var u=be(),t=Wi();r.exports=E,E.locator=t;var a=`
`,n="!",s='"',c="'",i="(",D=")",o="<",l=">",d="[",p="\\",g="]",F="`";function E(b,f,x){var v=this,h="",m=0,C=f.charAt(0),w=v.options.pedantic,q=v.options.commonmark,L=v.options.gfm,B,O,T,P,A,j,H,G,X,R,J,z,M,U,k,y,_,N;if(C===n&&(G=!0,h=C,C=f.charAt(++m)),C===d&&!(!G&&v.inLink)){for(h+=C,U="",m++,J=f.length,y=b.now(),M=0,y.column+=m,y.offset+=m;m<J;){if(C=f.charAt(m),j=C,C===F){for(O=1;f.charAt(m+1)===F;)j+=C,m++,O++;T?O>=T&&(T=0):T=O}else if(C===p)m++,j+=f.charAt(m);else if((!T||L)&&C===d)M++;else if((!T||L)&&C===g)if(M)M--;else{if(f.charAt(m+1)!==i)return;j+=i,B=!0,m++;break}U+=j,j="",m++}if(B){for(X=U,h+=U+j,m++;m<J&&(C=f.charAt(m),!!u(C));)h+=C,m++;if(C=f.charAt(m),U="",P=h,C===o){for(m++,P+=o;m<J&&(C=f.charAt(m),C!==l);){if(q&&C===a)return;U+=C,m++}if(f.charAt(m)!==l)return;h+=o+U+l,k=U,m++}else{for(C=null,j="";m<J&&(C=f.charAt(m),!(j&&(C===s||C===c||q&&C===i)));){if(u(C)){if(!w)break;j+=C}else{if(C===i)M++;else if(C===D){if(M===0)break;M--}U+=j,j="",C===p&&(U+=p,C=f.charAt(++m)),U+=C}m++}h+=U,k=U,m=h.length}for(U="";m<J&&(C=f.charAt(m),!!u(C));)U+=C,m++;if(C=f.charAt(m),h+=U,U&&(C===s||C===c||q&&C===i))if(m++,h+=C,U="",R=C===i?D:C,A=h,q){for(;m<J&&(C=f.charAt(m),C!==R);)C===p&&(U+=p,C=f.charAt(++m)),m++,U+=C;if(C=f.charAt(m),C!==R)return;for(z=U,h+=U+C,m++;m<J&&(C=f.charAt(m),!!u(C));)h+=C,m++}else for(j="";m<J;){if(C=f.charAt(m),C===R)H&&(U+=R+j,j=""),H=!0;else if(!H)U+=C;else if(C===D){h+=U+R+j,z=U;break}else u(C)?j+=C:(U+=R+j+C,j="",H=!1);m++}if(f.charAt(m)===D)return x?!0:(h+=D,k=v.decode.raw(v.unescape(k),b(P).test().end,{nonTerminated:!1}),z&&(A=b(A).test().end,z=v.decode.raw(v.unescape(z),A)),N={type:G?"image":"link",title:z||null,url:k},G?N.alt=v.decode.raw(v.unescape(X),y)||null:(_=v.enterLink(),N.children=v.tokenizeInline(X,y),_()),b(h)(N))}}}}}),_D=S({"node_modules/remark-parse/lib/tokenize/reference.js"(e,r){"use strict";I();var u=be(),t=Wi(),a=Hi();r.exports=g,g.locator=t;var n="link",s="image",c="shortcut",i="collapsed",D="full",o="!",l="[",d="\\",p="]";function g(F,E,b){var f=this,x=f.options.commonmark,v=E.charAt(0),h=0,m=E.length,C="",w="",q=n,L=c,B,O,T,P,A,j,H,G;if(v===o&&(q=s,w=v,v=E.charAt(++h)),v===l){for(h++,w+=v,j="",G=0;h<m;){if(v=E.charAt(h),v===l)H=!0,G++;else if(v===p){if(!G)break;G--}v===d&&(j+=d,v=E.charAt(++h)),j+=v,h++}if(C=j,B=j,v=E.charAt(h),v===p){if(h++,C+=v,j="",!x)for(;h<m&&(v=E.charAt(h),!!u(v));)j+=v,h++;if(v=E.charAt(h),v===l){for(O="",j+=v,h++;h<m&&(v=E.charAt(h),!(v===l||v===p));)v===d&&(O+=d,v=E.charAt(++h)),O+=v,h++;v=E.charAt(h),v===p?(L=O?D:i,j+=O+v,h++):O="",C+=j,j=""}else{if(!B)return;O=B}if(!(L!==D&&H))return C=w+C,q===n&&f.inLink?null:b?!0:(T=F.now(),T.column+=w.length,T.offset+=w.length,O=L===D?O:B,P={type:q+"Reference",identifier:a(O),label:O,referenceType:L},q===n?(A=f.enterLink(),P.children=f.tokenizeInline(B,T),A()):P.alt=f.decode.raw(f.unescape(B),T)||null,F(C)(P))}}}}}),OD=S({"node_modules/remark-parse/lib/locate/strong.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){var n=t.indexOf("**",a),s=t.indexOf("__",a);return s===-1?n:n===-1||s<n?s:n}}}),ID=S({"node_modules/remark-parse/lib/tokenize/strong.js"(e,r){"use strict";I();var u=ze(),t=be(),a=OD();r.exports=i,i.locator=a;var n="\\",s="*",c="_";function i(D,o,l){var d=this,p=0,g=o.charAt(p),F,E,b,f,x,v,h;if(!(g!==s&&g!==c||o.charAt(++p)!==g)&&(E=d.options.pedantic,b=g,x=b+b,v=o.length,p++,f="",g="",!(E&&t(o.charAt(p)))))for(;p<v;){if(h=g,g=o.charAt(p),g===b&&o.charAt(p+1)===b&&(!E||!t(h))&&(g=o.charAt(p+2),g!==b))return u(f)?l?!0:(F=D.now(),F.column+=2,F.offset+=2,D(x+f+x)({type:"strong",children:d.tokenizeInline(f,F)})):void 0;!E&&g===n&&(f+=g,g=o.charAt(++p)),f+=g,p++}}}}),SD=S({"node_modules/is-word-character/index.js"(e,r){"use strict";I(),r.exports=a;var u=String.fromCharCode,t=/\w/;function a(n){return t.test(typeof n=="number"?u(n):n.charAt(0))}}}),TD=S({"node_modules/remark-parse/lib/locate/emphasis.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){var n=t.indexOf("*",a),s=t.indexOf("_",a);return s===-1?n:n===-1||s<n?s:n}}}),ND=S({"node_modules/remark-parse/lib/tokenize/emphasis.js"(e,r){"use strict";I();var u=ze(),t=SD(),a=be(),n=TD();r.exports=D,D.locator=n;var s="*",c="_",i="\\";function D(o,l,d){var p=this,g=0,F=l.charAt(g),E,b,f,x,v,h,m;if(!(F!==s&&F!==c)&&(b=p.options.pedantic,v=F,f=F,h=l.length,g++,x="",F="",!(b&&a(l.charAt(g)))))for(;g<h;){if(m=F,F=l.charAt(g),F===f&&(!b||!a(m))){if(F=l.charAt(++g),F!==f){if(!u(x)||m===f)return;if(!b&&f===c&&t(F)){x+=f;continue}return d?!0:(E=o.now(),E.column++,E.offset++,o(v+x+f)({type:"emphasis",children:p.tokenizeInline(x,E)}))}x+=f}!b&&F===i&&(x+=F,F=l.charAt(++g)),x+=F,g++}}}}),LD=S({"node_modules/remark-parse/lib/locate/delete.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){return t.indexOf("~~",a)}}}),RD=S({"node_modules/remark-parse/lib/tokenize/delete.js"(e,r){"use strict";I();var u=be(),t=LD();r.exports=s,s.locator=t;var a="~",n="~~";function s(c,i,D){var o=this,l="",d="",p="",g="",F,E,b;if(!(!o.options.gfm||i.charAt(0)!==a||i.charAt(1)!==a||u(i.charAt(2))))for(F=1,E=i.length,b=c.now(),b.column+=2,b.offset+=2;++F<E;){if(l=i.charAt(F),l===a&&d===a&&(!p||!u(p)))return D?!0:c(n+g+n)({type:"delete",children:o.tokenizeInline(g,b)});g+=d,p=d,d=l}}}}),jD=S({"node_modules/remark-parse/lib/locate/code-inline.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){return t.indexOf("`",a)}}}),PD=S({"node_modules/remark-parse/lib/tokenize/code-inline.js"(e,r){"use strict";I();var u=jD();r.exports=s,s.locator=u;var t=10,a=32,n=96;function s(c,i,D){for(var o=i.length,l=0,d,p,g,F,E,b;l<o&&i.charCodeAt(l)===n;)l++;if(!(l===0||l===o)){for(d=l,E=i.charCodeAt(l);l<o;){if(F=E,E=i.charCodeAt(l+1),F===n){if(p===void 0&&(p=l),g=l+1,E!==n&&g-p===d){b=!0;break}}else p!==void 0&&(p=void 0,g=void 0);l++}if(b){if(D)return!0;if(l=d,o=p,F=i.charCodeAt(l),E=i.charCodeAt(o-1),b=!1,o-l>2&&(F===a||F===t)&&(E===a||E===t)){for(l++,o--;l<o;){if(F=i.charCodeAt(l),F!==a&&F!==t){b=!0;break}l++}b===!0&&(d++,p--)}return c(i.slice(0,g))({type:"inlineCode",value:i.slice(d,p)})}}}}}),MD=S({"node_modules/remark-parse/lib/locate/break.js"(e,r){"use strict";I(),r.exports=u;function u(t,a){for(var n=t.indexOf(`
`,a);n>a&&t.charAt(n-1)===" ";)n--;return n}}}),zD=S({"node_modules/remark-parse/lib/tokenize/break.js"(e,r){"use strict";I();var u=MD();r.exports=s,s.locator=u;var t=" ",a=`
`,n=2;function s(c,i,D){for(var o=i.length,l=-1,d="",p;++l<o;){if(p=i.charAt(l),p===a)return l<n?void 0:D?!0:(d+=p,c(d)({type:"break"}));if(p!==t)return;d+=p}}}}),$D=S({"node_modules/remark-parse/lib/tokenize/text.js"(e,r){"use strict";I(),r.exports=u;function u(t,a,n){var s=this,c,i,D,o,l,d,p,g,F,E;if(n)return!0;for(c=s.inlineMethods,o=c.length,i=s.inlineTokenizers,D=-1,F=a.length;++D<o;)g=c[D],!(g==="text"||!i[g])&&(p=i[g].locator,p||t.file.fail("Missing locator: `"+g+"`"),d=p.call(s,a,1),d!==-1&&d<F&&(F=d));l=a.slice(0,F),E=t.now(),s.decode(l,E,b);function b(f,x,v){t(v||f)({type:"text",value:f})}}}}),UD=S({"node_modules/remark-parse/lib/parser.js"(e,r){"use strict";I();var u=Pe(),t=Ml(),a=zl(),n=$l(),s=Kl(),c=Yl();r.exports=i;function i(l,d){this.file=d,this.offset={},this.options=u(this.options),this.setOptions({}),this.inList=!1,this.inBlock=!1,this.inLink=!1,this.atStart=!0,this.toOffset=a(d).toOffset,this.unescape=n(this,"escape"),this.decode=s(this)}var D=i.prototype;D.setOptions=Ql(),D.parse=iD(),D.options=$i(),D.exitStart=t("atStart",!0),D.enterList=t("inList",!1),D.enterLink=t("inLink",!1),D.enterBlock=t("inBlock",!1),D.interruptParagraph=[["thematicBreak"],["list"],["atxHeading"],["fencedCode"],["blockquote"],["html"],["setextHeading",{commonmark:!1}],["definition",{commonmark:!1}]],D.interruptList=[["atxHeading",{pedantic:!1}],["fencedCode",{pedantic:!1}],["thematicBreak",{pedantic:!1}],["definition",{commonmark:!1}]],D.interruptBlockquote=[["indentedCode",{commonmark:!0}],["fencedCode",{commonmark:!0}],["atxHeading",{commonmark:!0}],["setextHeading",{commonmark:!0}],["thematicBreak",{commonmark:!0}],["html",{commonmark:!0}],["list",{commonmark:!0}],["definition",{commonmark:!1}]],D.blockTokenizers={blankLine:aD(),indentedCode:oD(),fencedCode:sD(),blockquote:cD(),atxHeading:lD(),thematicBreak:DD(),list:pD(),setextHeading:dD(),html:hD(),definition:mD(),table:ED(),paragraph:CD()},D.inlineTokenizers={escape:FD(),autoLink:AD(),url:yD(),email:BD(),html:kD(),link:qD(),reference:_D(),strong:ID(),emphasis:ND(),deletion:RD(),code:PD(),break:zD(),text:$D()},D.blockMethods=o(D.blockTokenizers),D.inlineMethods=o(D.inlineTokenizers),D.tokenizeBlock=c("block"),D.tokenizeInline=c("inline"),D.tokenizeFactory=c;function o(l){var d=[],p;for(p in l)d.push(p);return d}}}),GD=S({"node_modules/remark-parse/index.js"(e,r){"use strict";I();var u=Pl(),t=Pe(),a=UD();r.exports=n,n.Parser=a;function n(s){var c=this.data("settings"),i=u(a);i.prototype.options=t(i.prototype.options,c,s),this.Parser=i}}}),VD=S({"node_modules/bail/index.js"(e,r){"use strict";I(),r.exports=u;function u(t){if(t)throw t}}}),Ki=S({"node_modules/is-buffer/index.js"(e,r){I(),r.exports=function(t){return t!=null&&t.constructor!=null&&typeof t.constructor.isBuffer=="function"&&t.constructor.isBuffer(t)}}}),HD=S({"node_modules/extend/index.js"(e,r){"use strict";I();var u=Object.prototype.hasOwnProperty,t=Object.prototype.toString,a=Object.defineProperty,n=Object.getOwnPropertyDescriptor,s=function(l){return typeof Array.isArray=="function"?Array.isArray(l):t.call(l)==="[object Array]"},c=function(l){if(!l||t.call(l)!=="[object Object]")return!1;var d=u.call(l,"constructor"),p=l.constructor&&l.constructor.prototype&&u.call(l.constructor.prototype,"isPrototypeOf");if(l.constructor&&!d&&!p)return!1;var g;for(g in l);return typeof g>"u"||u.call(l,g)},i=function(l,d){a&&d.name==="__proto__"?a(l,d.name,{enumerable:!0,configurable:!0,value:d.newValue,writable:!0}):l[d.name]=d.newValue},D=function(l,d){if(d==="__proto__")if(u.call(l,d)){if(n)return n(l,d).value}else return;return l[d]};r.exports=function o(){var l,d,p,g,F,E,b=arguments[0],f=1,x=arguments.length,v=!1;for(typeof b=="boolean"&&(v=b,b=arguments[1]||{},f=2),(b==null||typeof b!="object"&&typeof b!="function")&&(b={});f<x;++f)if(l=arguments[f],l!=null)for(d in l)p=D(b,d),g=D(l,d),b!==g&&(v&&g&&(c(g)||(F=s(g)))?(F?(F=!1,E=p&&s(p)?p:[]):E=p&&c(p)?p:{},i(b,{name:d,newValue:o(v,E,g)})):typeof g<"u"&&i(b,{name:d,newValue:g}));return b}}}),XD=S({"node_modules/is-plain-obj/index.js"(e,r){"use strict";I(),r.exports=u=>{if(Object.prototype.toString.call(u)!=="[object Object]")return!1;let t=Object.getPrototypeOf(u);return t===null||t===Object.prototype}}}),WD=S({"node_modules/trough/wrap.js"(e,r){"use strict";I();var u=[].slice;r.exports=t;function t(a,n){var s;return c;function c(){var o=u.call(arguments,0),l=a.length>o.length,d;l&&o.push(i);try{d=a.apply(null,o)}catch(p){if(l&&s)throw p;return i(p)}l||(d&&typeof d.then=="function"?d.then(D,i):d instanceof Error?i(d):D(d))}function i(){s||(s=!0,n.apply(null,arguments))}function D(o){i(null,o)}}}}),KD=S({"node_modules/trough/index.js"(e,r){"use strict";I();var u=WD();r.exports=a,a.wrap=u;var t=[].slice;function a(){var n=[],s={};return s.run=c,s.use=i,s;function c(){var D=-1,o=t.call(arguments,0,-1),l=arguments[arguments.length-1];if(typeof l!="function")throw new Error("Expected function as last argument, not "+l);d.apply(null,[null].concat(o));function d(p){var g=n[++D],F=t.call(arguments,0),E=F.slice(1),b=o.length,f=-1;if(p){l(p);return}for(;++f<b;)(E[f]===null||E[f]===void 0)&&(E[f]=o[f]);o=E,g?u(g,d).apply(null,o):l.apply(null,[null].concat(o))}}function i(D){if(typeof D!="function")throw new Error("Expected `fn` to be a function, not "+D);return n.push(D),s}}}}),YD=S({"node_modules/unist-util-stringify-position/index.js"(e,r){"use strict";I();var u={}.hasOwnProperty;r.exports=t;function t(c){return!c||typeof c!="object"?"":u.call(c,"position")||u.call(c,"type")?n(c.position):u.call(c,"start")||u.call(c,"end")?n(c):u.call(c,"line")||u.call(c,"column")?a(c):""}function a(c){return(!c||typeof c!="object")&&(c={}),s(c.line)+":"+s(c.column)}function n(c){return(!c||typeof c!="object")&&(c={}),a(c.start)+"-"+a(c.end)}function s(c){return c&&typeof c=="number"?c:1}}}),JD=S({"node_modules/vfile-message/index.js"(e,r){"use strict";I();var u=YD();r.exports=n;function t(){}t.prototype=Error.prototype,n.prototype=new t;var a=n.prototype;a.file="",a.name="",a.reason="",a.message="",a.stack="",a.fatal=null,a.column=null,a.line=null;function n(c,i,D){var o,l,d;typeof i=="string"&&(D=i,i=null),o=s(D),l=u(i)||"1:1",d={start:{line:null,column:null},end:{line:null,column:null}},i&&i.position&&(i=i.position),i&&(i.start?(d=i,i=i.start):d.start=i),c.stack&&(this.stack=c.stack,c=c.message),this.message=c,this.name=l,this.reason=c,this.line=i?i.line:null,this.column=i?i.column:null,this.location=d,this.source=o[0],this.ruleId=o[1]}function s(c){var i=[null,null],D;return typeof c=="string"&&(D=c.indexOf(":"),D===-1?i[1]=c:(i[0]=c.slice(0,D),i[1]=c.slice(D+1))),i}}}),ZD=S({"node_modules/vfile/lib/minpath.browser.js"(e){"use strict";I(),e.basename=r,e.dirname=u,e.extname=t,e.join=a,e.sep="/";function r(i,D){var o=0,l=-1,d,p,g,F;if(D!==void 0&&typeof D!="string")throw new TypeError('"ext" argument must be a string');if(c(i),d=i.length,D===void 0||!D.length||D.length>i.length){for(;d--;)if(i.charCodeAt(d)===47){if(g){o=d+1;break}}else l<0&&(g=!0,l=d+1);return l<0?"":i.slice(o,l)}if(D===i)return"";for(p=-1,F=D.length-1;d--;)if(i.charCodeAt(d)===47){if(g){o=d+1;break}}else p<0&&(g=!0,p=d+1),F>-1&&(i.charCodeAt(d)===D.charCodeAt(F--)?F<0&&(l=d):(F=-1,l=p));return o===l?l=p:l<0&&(l=i.length),i.slice(o,l)}function u(i){var D,o,l;if(c(i),!i.length)return".";for(D=-1,l=i.length;--l;)if(i.charCodeAt(l)===47){if(o){D=l;break}}else o||(o=!0);return D<0?i.charCodeAt(0)===47?"/":".":D===1&&i.charCodeAt(0)===47?"//":i.slice(0,D)}function t(i){var D=-1,o=0,l=-1,d=0,p,g,F;for(c(i),F=i.length;F--;){if(g=i.charCodeAt(F),g===47){if(p){o=F+1;break}continue}l<0&&(p=!0,l=F+1),g===46?D<0?D=F:d!==1&&(d=1):D>-1&&(d=-1)}return D<0||l<0||d===0||d===1&&D===l-1&&D===o+1?"":i.slice(D,l)}function a(){for(var i=-1,D;++i<arguments.length;)c(arguments[i]),arguments[i]&&(D=D===void 0?arguments[i]:D+"/"+arguments[i]);return D===void 0?".":n(D)}function n(i){var D,o;return c(i),D=i.charCodeAt(0)===47,o=s(i,!D),!o.length&&!D&&(o="."),o.length&&i.charCodeAt(i.length-1)===47&&(o+="/"),D?"/"+o:o}function s(i,D){for(var o="",l=0,d=-1,p=0,g=-1,F,E;++g<=i.length;){if(g<i.length)F=i.charCodeAt(g);else{if(F===47)break;F=47}if(F===47){if(!(d===g-1||p===1))if(d!==g-1&&p===2){if(o.length<2||l!==2||o.charCodeAt(o.length-1)!==46||o.charCodeAt(o.length-2)!==46){if(o.length>2){if(E=o.lastIndexOf("/"),E!==o.length-1){E<0?(o="",l=0):(o=o.slice(0,E),l=o.length-1-o.lastIndexOf("/")),d=g,p=0;continue}}else if(o.length){o="",l=0,d=g,p=0;continue}}D&&(o=o.length?o+"/..":"..",l=2)}else o.length?o+="/"+i.slice(d+1,g):o=i.slice(d+1,g),l=g-d-1;d=g,p=0}else F===46&&p>-1?p++:p=-1}return o}function c(i){if(typeof i!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(i))}}}),QD=S({"node_modules/vfile/lib/minproc.browser.js"(e){"use strict";I(),e.cwd=r;function r(){return"/"}}}),e2=S({"node_modules/vfile/lib/core.js"(e,r){"use strict";I();var u=ZD(),t=QD(),a=Ki();r.exports=c;var n={}.hasOwnProperty,s=["history","path","basename","stem","extname","dirname"];c.prototype.toString=f,Object.defineProperty(c.prototype,"path",{get:i,set:D}),Object.defineProperty(c.prototype,"dirname",{get:o,set:l}),Object.defineProperty(c.prototype,"basename",{get:d,set:p}),Object.defineProperty(c.prototype,"extname",{get:g,set:F}),Object.defineProperty(c.prototype,"stem",{get:E,set:b});function c(m){var C,w;if(!m)m={};else if(typeof m=="string"||a(m))m={contents:m};else if("message"in m&&"messages"in m)return m;if(!(this instanceof c))return new c(m);for(this.data={},this.messages=[],this.history=[],this.cwd=t.cwd(),w=-1;++w<s.length;)C=s[w],n.call(m,C)&&(this[C]=m[C]);for(C in m)s.indexOf(C)<0&&(this[C]=m[C])}function i(){return this.history[this.history.length-1]}function D(m){v(m,"path"),this.path!==m&&this.history.push(m)}function o(){return typeof this.path=="string"?u.dirname(this.path):void 0}function l(m){h(this.path,"dirname"),this.path=u.join(m||"",this.basename)}function d(){return typeof this.path=="string"?u.basename(this.path):void 0}function p(m){v(m,"basename"),x(m,"basename"),this.path=u.join(this.dirname||"",m)}function g(){return typeof this.path=="string"?u.extname(this.path):void 0}function F(m){if(x(m,"extname"),h(this.path,"extname"),m){if(m.charCodeAt(0)!==46)throw new Error("`extname` must start with `.`");if(m.indexOf(".",1)>-1)throw new Error("`extname` cannot contain multiple dots")}this.path=u.join(this.dirname,this.stem+(m||""))}function E(){return typeof this.path=="string"?u.basename(this.path,this.extname):void 0}function b(m){v(m,"stem"),x(m,"stem"),this.path=u.join(this.dirname||"",m+(this.extname||""))}function f(m){return(this.contents||"").toString(m)}function x(m,C){if(m&&m.indexOf(u.sep)>-1)throw new Error("`"+C+"` cannot be a path: did not expect `"+u.sep+"`")}function v(m,C){if(!m)throw new Error("`"+C+"` cannot be empty")}function h(m,C){if(!m)throw new Error("Setting `"+C+"` requires `path` to be set too")}}}),r2=S({"node_modules/vfile/lib/index.js"(e,r){"use strict";I();var u=JD(),t=e2();r.exports=t,t.prototype.message=a,t.prototype.info=s,t.prototype.fail=n;function a(c,i,D){var o=new u(c,i,D);return this.path&&(o.name=this.path+":"+o.name,o.file=this.path),o.fatal=!1,this.messages.push(o),o}function n(){var c=this.message.apply(this,arguments);throw c.fatal=!0,c}function s(){var c=this.message.apply(this,arguments);return c.fatal=null,c}}}),u2=S({"node_modules/vfile/index.js"(e,r){"use strict";I(),r.exports=r2()}}),t2=S({"node_modules/unified/index.js"(e,r){"use strict";I();var u=VD(),t=Ki(),a=HD(),n=XD(),s=KD(),c=u2();r.exports=g().freeze();var i=[].slice,D={}.hasOwnProperty,o=s().use(l).use(d).use(p);function l(m,C){C.tree=m.parse(C.file)}function d(m,C,w){m.run(C.tree,C.file,q);function q(L,B,O){L?w(L):(C.tree=B,C.file=O,w())}}function p(m,C){var w=m.stringify(C.tree,C.file);w==null||(typeof w=="string"||t(w)?C.file.contents=w:C.file.result=w)}function g(){var m=[],C=s(),w={},q=-1,L;return B.data=T,B.freeze=O,B.attachers=m,B.use=P,B.parse=j,B.stringify=X,B.run=H,B.runSync=G,B.process=R,B.processSync=J,B;function B(){for(var z=g(),M=-1;++M<m.length;)z.use.apply(null,m[M]);return z.data(a(!0,{},w)),z}function O(){var z,M;if(L)return B;for(;++q<m.length;)z=m[q],z[1]!==!1&&(z[1]===!0&&(z[1]=void 0),M=z[0].apply(B,z.slice(1)),typeof M=="function"&&C.use(M));return L=!0,q=1/0,B}function T(z,M){return typeof z=="string"?arguments.length===2?(x("data",L),w[z]=M,B):D.call(w,z)&&w[z]||null:z?(x("data",L),w=z,B):w}function P(z){var M;if(x("use",L),z!=null)if(typeof z=="function")_.apply(null,arguments);else if(typeof z=="object")"length"in z?y(z):U(z);else throw new Error("Expected usable value, not `"+z+"`");return M&&(w.settings=a(w.settings||{},M)),B;function U(N){y(N.plugins),N.settings&&(M=a(M||{},N.settings))}function k(N){if(typeof N=="function")_(N);else if(typeof N=="object")"length"in N?_.apply(null,N):U(N);else throw new Error("Expected usable value, not `"+N+"`")}function y(N){var V=-1;if(N!=null)if(typeof N=="object"&&"length"in N)for(;++V<N.length;)k(N[V]);else throw new Error("Expected a list of plugins, not `"+N+"`")}function _(N,V){var W=A(N);W?(n(W[1])&&n(V)&&(V=a(!0,W[1],V)),W[1]=V):m.push(i.call(arguments))}}function A(z){for(var M=-1;++M<m.length;)if(m[M][0]===z)return m[M]}function j(z){var M=c(z),U;return O(),U=B.Parser,b("parse",U),F(U,"parse")?new U(String(M),M).parse():U(String(M),M)}function H(z,M,U){if(v(z),O(),!U&&typeof M=="function"&&(U=M,M=null),!U)return new Promise(k);k(null,U);function k(y,_){C.run(z,c(M),N);function N(V,W,K){W=W||z,V?_(V):y?y(W):U(null,W,K)}}}function G(z,M){var U,k;return H(z,M,y),h("runSync","run",k),U;function y(_,N){k=!0,U=N,u(_)}}function X(z,M){var U=c(M),k;return O(),k=B.Compiler,f("stringify",k),v(z),F(k,"compile")?new k(z,U).compile():k(z,U)}function R(z,M){if(O(),b("process",B.Parser),f("process",B.Compiler),!M)return new Promise(U);U(null,M);function U(k,y){var _=c(z);o.run(B,{file:_},N);function N(V){V?y(V):k?k(_):M(null,_)}}}function J(z){var M,U;return O(),b("processSync",B.Parser),f("processSync",B.Compiler),M=c(z),R(M,k),h("processSync","process",U),M;function k(y){U=!0,u(y)}}}function F(m,C){return typeof m=="function"&&m.prototype&&(E(m.prototype)||C in m.prototype)}function E(m){var C;for(C in m)return!0;return!1}function b(m,C){if(typeof C!="function")throw new Error("Cannot `"+m+"` without `Parser`")}function f(m,C){if(typeof C!="function")throw new Error("Cannot `"+m+"` without `Compiler`")}function x(m,C){if(C)throw new Error("Cannot invoke `"+m+"` on a frozen processor.\nCreate a new processor first, by invoking it: use `processor()` instead of `processor`.")}function v(m){if(!m||typeof m.type!="string")throw new Error("Expected node, got `"+m+"`")}function h(m,C,w){if(!w)throw new Error("`"+m+"` finished async. Use `"+C+"` instead")}}}),Yi=S({"node_modules/remark-math/util.js"(e){I(),e.isRemarkParser=r,e.isRemarkCompiler=u;function r(t){return Boolean(t&&t.prototype&&t.prototype.blockTokenizers)}function u(t){return Boolean(t&&t.prototype&&t.prototype.visitors)}}}),n2=S({"node_modules/remark-math/inline.js"(e,r){I();var u=Yi();r.exports=l;var t=9,a=32,n=36,s=48,c=57,i=92,D=["math","math-inline"],o="math-display";function l(g){let F=this.Parser,E=this.Compiler;u.isRemarkParser(F)&&d(F,g),u.isRemarkCompiler(E)&&p(E,g)}function d(g,F){let E=g.prototype,b=E.inlineMethods;x.locator=f,E.inlineTokenizers.math=x,b.splice(b.indexOf("text"),0,"math");function f(v,h){return v.indexOf("$",h)}function x(v,h,m){let C=h.length,w=!1,q=!1,L=0,B,O,T,P,A,j,H;if(h.charCodeAt(L)===i&&(q=!0,L++),h.charCodeAt(L)===n){if(L++,q)return m?!0:v(h.slice(0,L))({type:"text",value:"$"});if(h.charCodeAt(L)===n&&(w=!0,L++),T=h.charCodeAt(L),!(T===a||T===t)){for(P=L;L<C;){if(O=T,T=h.charCodeAt(L+1),O===n){if(B=h.charCodeAt(L-1),B!==a&&B!==t&&(T!==T||T<s||T>c)&&(!w||T===n)){A=L-1,L++,w&&L++,j=L;break}}else O===i&&(L++,T=h.charCodeAt(L+1));L++}if(j!==void 0)return m?!0:(H=h.slice(P,A+1),v(h.slice(0,j))({type:"inlineMath",value:H,data:{hName:"span",hProperties:{className:D.concat(w&&F.inlineMathDouble?[o]:[])},hChildren:[{type:"text",value:H}]}}))}}}}function p(g){let F=g.prototype;F.visitors.inlineMath=E;function E(b){let f="$";return(b.data&&b.data.hProperties&&b.data.hProperties.className||[]).includes(o)&&(f="$$"),f+b.value+f}}}}),i2=S({"node_modules/remark-math/block.js"(e,r){I();var u=Yi();r.exports=o;var t=10,a=32,n=36,s=`
`,c="$",i=2,D=["math","math-display"];function o(){let p=this.Parser,g=this.Compiler;u.isRemarkParser(p)&&l(p),u.isRemarkCompiler(g)&&d(g)}function l(p){let g=p.prototype,F=g.blockMethods,E=g.interruptParagraph,b=g.interruptList,f=g.interruptBlockquote;g.blockTokenizers.math=x,F.splice(F.indexOf("fencedCode")+1,0,"math"),E.splice(E.indexOf("fencedCode")+1,0,["math"]),b.splice(b.indexOf("fencedCode")+1,0,["math"]),f.splice(f.indexOf("fencedCode")+1,0,["math"]);function x(v,h,m){var C=h.length,w=0;let q,L,B,O,T,P,A,j,H,G,X;for(;w<C&&h.charCodeAt(w)===a;)w++;for(T=w;w<C&&h.charCodeAt(w)===n;)w++;if(P=w-T,!(P<i)){for(;w<C&&h.charCodeAt(w)===a;)w++;for(A=w;w<C;){if(q=h.charCodeAt(w),q===n)return;if(q===t)break;w++}if(h.charCodeAt(w)===t){if(m)return!0;for(L=[],A!==w&&L.push(h.slice(A,w)),w++,B=h.indexOf(s,w+1),B=B===-1?C:B;w<C;){for(j=!1,G=w,X=B,O=B,H=0;O>G&&h.charCodeAt(O-1)===a;)O--;for(;O>G&&h.charCodeAt(O-1)===n;)H++,O--;for(P<=H&&h.indexOf(c,G)===O&&(j=!0,X=O);G<=X&&G-w<T&&h.charCodeAt(G)===a;)G++;if(j)for(;X>G&&h.charCodeAt(X-1)===a;)X--;if((!j||G!==X)&&L.push(h.slice(G,X)),j)break;w=B+1,B=h.indexOf(s,w+1),B=B===-1?C:B}return L=L.join(`
`),v(h.slice(0,B))({type:"math",value:L,data:{hName:"div",hProperties:{className:D.concat()},hChildren:[{type:"text",value:L}]}})}}}}function d(p){let g=p.prototype;g.visitors.math=F;function F(E){return`$$
`+E.value+`
$$`}}}}),a2=S({"node_modules/remark-math/index.js"(e,r){I();var u=n2(),t=i2();r.exports=a;function a(n){var s=n||{};t.call(this,s),u.call(this,s)}}}),o2=S({"node_modules/remark-footnotes/index.js"(e,r){"use strict";I(),r.exports=g;var u=9,t=10,a=32,n=33,s=58,c=91,i=92,D=93,o=94,l=96,d=4,p=1024;function g(h){var m=this.Parser,C=this.Compiler;F(m)&&b(m,h),E(C)&&f(C)}function F(h){return Boolean(h&&h.prototype&&h.prototype.blockTokenizers)}function E(h){return Boolean(h&&h.prototype&&h.prototype.visitors)}function b(h,m){for(var C=m||{},w=h.prototype,q=w.blockTokenizers,L=w.inlineTokenizers,B=w.blockMethods,O=w.inlineMethods,T=q.definition,P=L.reference,A=[],j=-1,H=B.length,G;++j<H;)G=B[j],!(G==="newline"||G==="indentedCode"||G==="paragraph"||G==="footnoteDefinition")&&A.push([G]);A.push(["footnoteDefinition"]),C.inlineNotes&&(x(O,"reference","inlineNote"),L.inlineNote=J),x(B,"definition","footnoteDefinition"),x(O,"reference","footnoteCall"),q.definition=M,q.footnoteDefinition=X,L.footnoteCall=R,L.reference=z,w.interruptFootnoteDefinition=A,z.locator=P.locator,R.locator=U,J.locator=k;function X(y,_,N){for(var V=this,W=V.interruptFootnoteDefinition,K=V.offset,ee=_.length+1,Y=0,ue=[],le,ce,te,Z,Q,De,ye,fe,he,ae,pe,ne,re;Y<ee&&(Z=_.charCodeAt(Y),!(Z!==u&&Z!==a));)Y++;if(_.charCodeAt(Y++)===c&&_.charCodeAt(Y++)===o){for(ce=Y;Y<ee;){if(Z=_.charCodeAt(Y),Z!==Z||Z===t||Z===u||Z===a)return;if(Z===D){te=Y,Y++;break}Y++}if(!(te===void 0||ce===te||_.charCodeAt(Y++)!==s)){if(N)return!0;for(le=_.slice(ce,te),Q=y.now(),he=0,ae=0,pe=Y,ne=[];Y<ee;){if(Z=_.charCodeAt(Y),Z!==Z||Z===t)re={start:he,contentStart:pe||Y,contentEnd:Y,end:Y},ne.push(re),Z===t&&(he=Y+1,ae=0,pe=void 0,re.end=he);else if(ae!==void 0)if(Z===a||Z===u)ae+=Z===a?1:d-ae%d,ae>d&&(ae=void 0,pe=Y);else{if(ae<d&&re&&(re.contentStart===re.contentEnd||v(W,q,V,[y,_.slice(Y,p),!0])))break;ae=void 0,pe=Y}Y++}for(Y=-1,ee=ne.length;ee>0&&(re=ne[ee-1],re.contentStart===re.contentEnd);)ee--;for(De=y(_.slice(0,re.contentEnd));++Y<ee;)re=ne[Y],K[Q.line+Y]=(K[Q.line+Y]||0)+(re.contentStart-re.start),ue.push(_.slice(re.contentStart,re.end));return ye=V.enterBlock(),fe=V.tokenizeBlock(ue.join(""),Q),ye(),De({type:"footnoteDefinition",identifier:le.toLowerCase(),label:le,children:fe})}}}function R(y,_,N){var V=_.length+1,W=0,K,ee,Y,ue;if(_.charCodeAt(W++)===c&&_.charCodeAt(W++)===o){for(ee=W;W<V;){if(ue=_.charCodeAt(W),ue!==ue||ue===t||ue===u||ue===a)return;if(ue===D){Y=W,W++;break}W++}if(!(Y===void 0||ee===Y))return N?!0:(K=_.slice(ee,Y),y(_.slice(0,W))({type:"footnoteReference",identifier:K.toLowerCase(),label:K}))}}function J(y,_,N){var V=this,W=_.length+1,K=0,ee=0,Y,ue,le,ce,te,Z,Q;if(_.charCodeAt(K++)===o&&_.charCodeAt(K++)===c){for(le=K;K<W;){if(ue=_.charCodeAt(K),ue!==ue)return;if(Z===void 0)if(ue===i)K+=2;else if(ue===c)ee++,K++;else if(ue===D)if(ee===0){ce=K,K++;break}else ee--,K++;else if(ue===l){for(te=K,Z=1;_.charCodeAt(te+Z)===l;)Z++;K+=Z}else K++;else if(ue===l){for(te=K,Q=1;_.charCodeAt(te+Q)===l;)Q++;K+=Q,Z===Q&&(Z=void 0),Q=void 0}else K++}if(ce!==void 0)return N?!0:(Y=y.now(),Y.column+=2,Y.offset+=2,y(_.slice(0,K))({type:"footnote",children:V.tokenizeInline(_.slice(le,ce),Y)}))}}function z(y,_,N){var V=0;if(_.charCodeAt(V)===n&&V++,_.charCodeAt(V)===c&&_.charCodeAt(V+1)!==o)return P.call(this,y,_,N)}function M(y,_,N){for(var V=0,W=_.charCodeAt(V);W===a||W===u;)W=_.charCodeAt(++V);if(W===c&&_.charCodeAt(V+1)!==o)return T.call(this,y,_,N)}function U(y,_){return y.indexOf("[",_)}function k(y,_){return y.indexOf("^[",_)}}function f(h){var m=h.prototype.visitors,C="    ";m.footnote=w,m.footnoteReference=q,m.footnoteDefinition=L;function w(B){return"^["+this.all(B).join("")+"]"}function q(B){return"[^"+(B.label||B.identifier)+"]"}function L(B){for(var O=this.all(B).join(`

`).split(`
`),T=0,P=O.length,A;++T<P;)A=O[T],A!==""&&(O[T]=C+A);return"[^"+(B.label||B.identifier)+"]: "+O.join(`
`)}}function x(h,m,C){h.splice(h.indexOf(m),0,C)}function v(h,m,C,w){for(var q=h.length,L=-1;++L<q;)if(m[h[L][0]].apply(C,w))return!0;return!1}}}),Ji=S({"src/utils/front-matter/parse.js"(e,r){"use strict";I();var u=new RegExp("^(?<startDelimiter>-{3}|\\+{3})(?<language>[^\\n]*)\\n(?:|(?<value>.*?)\\n)(?<endDelimiter>\\k<startDelimiter>|\\.{3})[^\\S\\n]*(?:\\n|$)","s");function t(a){let n=a.match(u);if(!n)return{content:a};let{startDelimiter:s,language:c,value:i="",endDelimiter:D}=n.groups,o=c.trim()||"yaml";if(s==="+++"&&(o="toml"),o!=="yaml"&&s!==D)return{content:a};let[l]=n;return{frontMatter:{type:"front-matter",lang:o,value:i,startDelimiter:s,endDelimiter:D,raw:l.replace(/\n$/,"")},content:l.replace(/[^\n]/g," ")+a.slice(l.length)}}r.exports=t}}),s2=S({"src/language-markdown/pragma.js"(e,r){"use strict";I();var u=Ji(),t=["format","prettier"];function a(n){let s=`@(${t.join("|")})`,c=new RegExp([`<!--\\s*${s}\\s*-->`,`{\\s*\\/\\*\\s*${s}\\s*\\*\\/\\s*}`,`<!--.*\r?
[\\s\\S]*(^|
)[^\\S
]*${s}[^\\S
]*($|
)[\\s\\S]*
.*-->`].join("|"),"m"),i=n.match(c);return(i==null?void 0:i.index)===0}r.exports={startWithPragma:a,hasPragma:n=>a(u(n).content.trimStart()),insertPragma:n=>{let s=u(n),c=`<!-- @${t[0]} -->`;return s.frontMatter?`${s.frontMatter.raw}

${c}

${s.content}`:`${c}

${s.content}`}}}}),Zi=S({"src/language-markdown/loc.js"(e,r){"use strict";I();function u(a){return a.position.start.offset}function t(a){return a.position.end.offset}r.exports={locStart:u,locEnd:t}}}),Qi=S({"src/language-markdown/mdx.js"(e,r){"use strict";I();var u=/^import\s/,t=/^export\s/,a="[a-z][a-z0-9]*(\\.[a-z][a-z0-9]*)*|",n=/<!---->|<!---?[^>-](?:-?[^-])*-->/,s=/^{\s*\/\*(.*)\*\/\s*}/,c=`

`,i=p=>u.test(p),D=p=>t.test(p),o=(p,g)=>{let F=g.indexOf(c),E=g.slice(0,F);if(D(E)||i(E))return p(E)({type:D(E)?"export":"import",value:E})},l=(p,g)=>{let F=s.exec(g);if(F)return p(F[0])({type:"esComment",value:F[1].trim()})};o.locator=p=>D(p)||i(p)?-1:1,l.locator=(p,g)=>p.indexOf("{",g);function d(){let{Parser:p}=this,{blockTokenizers:g,blockMethods:F,inlineTokenizers:E,inlineMethods:b}=p.prototype;g.esSyntax=o,E.esComment=l,F.splice(F.indexOf("paragraph"),0,"esSyntax"),b.splice(b.indexOf("text"),0,"esComment")}r.exports={esSyntax:d,BLOCKS_REGEX:a,COMMENT_REGEX:n}}}),ea={};Pi(ea,{default:()=>c2});function c2(e){if(typeof e!="string")throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var l2=je({"node_modules/escape-string-regexp/index.js"(){I()}}),D2=S({"src/utils/get-last.js"(e,r){"use strict";I();var u=t=>t[t.length-1];r.exports=u}}),ra=S({"node_modules/semver/internal/debug.js"(e,r){I();var u=typeof Qe=="object"&&Qe.env&&Qe.env.NODE_DEBUG&&/\bsemver\b/i.test(Qe.env.NODE_DEBUG)?function(){for(var t=arguments.length,a=new Array(t),n=0;n<t;n++)a[n]=arguments[n];return console.error("SEMVER",...a)}:()=>{};r.exports=u}}),ua=S({"node_modules/semver/internal/constants.js"(e,r){I();var u="2.0.0",t=256,a=Number.MAX_SAFE_INTEGER||9007199254740991,n=16;r.exports={SEMVER_SPEC_VERSION:u,MAX_LENGTH:t,MAX_SAFE_INTEGER:a,MAX_SAFE_COMPONENT_LENGTH:n}}}),f2=S({"node_modules/semver/internal/re.js"(e,r){I();var{MAX_SAFE_COMPONENT_LENGTH:u}=ua(),t=ra();e=r.exports={};var a=e.re=[],n=e.src=[],s=e.t={},c=0,i=(D,o,l)=>{let d=c++;t(D,d,o),s[D]=d,n[d]=o,a[d]=new RegExp(o,l?"g":void 0)};i("NUMERICIDENTIFIER","0|[1-9]\\d*"),i("NUMERICIDENTIFIERLOOSE","[0-9]+"),i("NONNUMERICIDENTIFIER","\\d*[a-zA-Z-][a-zA-Z0-9-]*"),i("MAINVERSION",`(${n[s.NUMERICIDENTIFIER]})\\.(${n[s.NUMERICIDENTIFIER]})\\.(${n[s.NUMERICIDENTIFIER]})`),i("MAINVERSIONLOOSE",`(${n[s.NUMERICIDENTIFIERLOOSE]})\\.(${n[s.NUMERICIDENTIFIERLOOSE]})\\.(${n[s.NUMERICIDENTIFIERLOOSE]})`),i("PRERELEASEIDENTIFIER",`(?:${n[s.NUMERICIDENTIFIER]}|${n[s.NONNUMERICIDENTIFIER]})`),i("PRERELEASEIDENTIFIERLOOSE",`(?:${n[s.NUMERICIDENTIFIERLOOSE]}|${n[s.NONNUMERICIDENTIFIER]})`),i("PRERELEASE",`(?:-(${n[s.PRERELEASEIDENTIFIER]}(?:\\.${n[s.PRERELEASEIDENTIFIER]})*))`),i("PRERELEASELOOSE",`(?:-?(${n[s.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${n[s.PRERELEASEIDENTIFIERLOOSE]})*))`),i("BUILDIDENTIFIER","[0-9A-Za-z-]+"),i("BUILD",`(?:\\+(${n[s.BUILDIDENTIFIER]}(?:\\.${n[s.BUILDIDENTIFIER]})*))`),i("FULLPLAIN",`v?${n[s.MAINVERSION]}${n[s.PRERELEASE]}?${n[s.BUILD]}?`),i("FULL",`^${n[s.FULLPLAIN]}$`),i("LOOSEPLAIN",`[v=\\s]*${n[s.MAINVERSIONLOOSE]}${n[s.PRERELEASELOOSE]}?${n[s.BUILD]}?`),i("LOOSE",`^${n[s.LOOSEPLAIN]}$`),i("GTLT","((?:<|>)?=?)"),i("XRANGEIDENTIFIERLOOSE",`${n[s.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),i("XRANGEIDENTIFIER",`${n[s.NUMERICIDENTIFIER]}|x|X|\\*`),i("XRANGEPLAIN",`[v=\\s]*(${n[s.XRANGEIDENTIFIER]})(?:\\.(${n[s.XRANGEIDENTIFIER]})(?:\\.(${n[s.XRANGEIDENTIFIER]})(?:${n[s.PRERELEASE]})?${n[s.BUILD]}?)?)?`),i("XRANGEPLAINLOOSE",`[v=\\s]*(${n[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${n[s.XRANGEIDENTIFIERLOOSE]})(?:\\.(${n[s.XRANGEIDENTIFIERLOOSE]})(?:${n[s.PRERELEASELOOSE]})?${n[s.BUILD]}?)?)?`),i("XRANGE",`^${n[s.GTLT]}\\s*${n[s.XRANGEPLAIN]}$`),i("XRANGELOOSE",`^${n[s.GTLT]}\\s*${n[s.XRANGEPLAINLOOSE]}$`),i("COERCE",`(^|[^\\d])(\\d{1,${u}})(?:\\.(\\d{1,${u}}))?(?:\\.(\\d{1,${u}}))?(?:$|[^\\d])`),i("COERCERTL",n[s.COERCE],!0),i("LONETILDE","(?:~>?)"),i("TILDETRIM",`(\\s*)${n[s.LONETILDE]}\\s+`,!0),e.tildeTrimReplace="$1~",i("TILDE",`^${n[s.LONETILDE]}${n[s.XRANGEPLAIN]}$`),i("TILDELOOSE",`^${n[s.LONETILDE]}${n[s.XRANGEPLAINLOOSE]}$`),i("LONECARET","(?:\\^)"),i("CARETTRIM",`(\\s*)${n[s.LONECARET]}\\s+`,!0),e.caretTrimReplace="$1^",i("CARET",`^${n[s.LONECARET]}${n[s.XRANGEPLAIN]}$`),i("CARETLOOSE",`^${n[s.LONECARET]}${n[s.XRANGEPLAINLOOSE]}$`),i("COMPARATORLOOSE",`^${n[s.GTLT]}\\s*(${n[s.LOOSEPLAIN]})$|^$`),i("COMPARATOR",`^${n[s.GTLT]}\\s*(${n[s.FULLPLAIN]})$|^$`),i("COMPARATORTRIM",`(\\s*)${n[s.GTLT]}\\s*(${n[s.LOOSEPLAIN]}|${n[s.XRANGEPLAIN]})`,!0),e.comparatorTrimReplace="$1$2$3",i("HYPHENRANGE",`^\\s*(${n[s.XRANGEPLAIN]})\\s+-\\s+(${n[s.XRANGEPLAIN]})\\s*$`),i("HYPHENRANGELOOSE",`^\\s*(${n[s.XRANGEPLAINLOOSE]})\\s+-\\s+(${n[s.XRANGEPLAINLOOSE]})\\s*$`),i("STAR","(<|>)?=?\\s*\\*"),i("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),i("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")}}),p2=S({"node_modules/semver/internal/parse-options.js"(e,r){I();var u=["includePrerelease","loose","rtl"],t=a=>a?typeof a!="object"?{loose:!0}:u.filter(n=>a[n]).reduce((n,s)=>(n[s]=!0,n),{}):{};r.exports=t}}),d2=S({"node_modules/semver/internal/identifiers.js"(e,r){I();var u=/^[0-9]+$/,t=(n,s)=>{let c=u.test(n),i=u.test(s);return c&&i&&(n=+n,s=+s),n===s?0:c&&!i?-1:i&&!c?1:n<s?-1:1},a=(n,s)=>t(s,n);r.exports={compareIdentifiers:t,rcompareIdentifiers:a}}}),h2=S({"node_modules/semver/classes/semver.js"(e,r){I();var u=ra(),{MAX_LENGTH:t,MAX_SAFE_INTEGER:a}=ua(),{re:n,t:s}=f2(),c=p2(),{compareIdentifiers:i}=d2(),D=class{constructor(o,l){if(l=c(l),o instanceof D){if(o.loose===!!l.loose&&o.includePrerelease===!!l.includePrerelease)return o;o=o.version}else if(typeof o!="string")throw new TypeError(`Invalid Version: ${o}`);if(o.length>t)throw new TypeError(`version is longer than ${t} characters`);u("SemVer",o,l),this.options=l,this.loose=!!l.loose,this.includePrerelease=!!l.includePrerelease;let d=o.trim().match(l.loose?n[s.LOOSE]:n[s.FULL]);if(!d)throw new TypeError(`Invalid Version: ${o}`);if(this.raw=o,this.major=+d[1],this.minor=+d[2],this.patch=+d[3],this.major>a||this.major<0)throw new TypeError("Invalid major version");if(this.minor>a||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>a||this.patch<0)throw new TypeError("Invalid patch version");d[4]?this.prerelease=d[4].split(".").map(p=>{if(/^[0-9]+$/.test(p)){let g=+p;if(g>=0&&g<a)return g}return p}):this.prerelease=[],this.build=d[5]?d[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(o){if(u("SemVer.compare",this.version,this.options,o),!(o instanceof D)){if(typeof o=="string"&&o===this.version)return 0;o=new D(o,this.options)}return o.version===this.version?0:this.compareMain(o)||this.comparePre(o)}compareMain(o){return o instanceof D||(o=new D(o,this.options)),i(this.major,o.major)||i(this.minor,o.minor)||i(this.patch,o.patch)}comparePre(o){if(o instanceof D||(o=new D(o,this.options)),this.prerelease.length&&!o.prerelease.length)return-1;if(!this.prerelease.length&&o.prerelease.length)return 1;if(!this.prerelease.length&&!o.prerelease.length)return 0;let l=0;do{let d=this.prerelease[l],p=o.prerelease[l];if(u("prerelease compare",l,d,p),d===void 0&&p===void 0)return 0;if(p===void 0)return 1;if(d===void 0)return-1;if(d===p)continue;return i(d,p)}while(++l)}compareBuild(o){o instanceof D||(o=new D(o,this.options));let l=0;do{let d=this.build[l],p=o.build[l];if(u("prerelease compare",l,d,p),d===void 0&&p===void 0)return 0;if(p===void 0)return 1;if(d===void 0)return-1;if(d===p)continue;return i(d,p)}while(++l)}inc(o,l){switch(o){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",l);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",l);break;case"prepatch":this.prerelease.length=0,this.inc("patch",l),this.inc("pre",l);break;case"prerelease":this.prerelease.length===0&&this.inc("patch",l),this.inc("pre",l);break;case"major":(this.minor!==0||this.patch!==0||this.prerelease.length===0)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(this.patch!==0||this.prerelease.length===0)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":this.prerelease.length===0&&this.patch++,this.prerelease=[];break;case"pre":if(this.prerelease.length===0)this.prerelease=[0];else{let d=this.prerelease.length;for(;--d>=0;)typeof this.prerelease[d]=="number"&&(this.prerelease[d]++,d=-2);d===-1&&this.prerelease.push(0)}l&&(i(this.prerelease[0],l)===0?isNaN(this.prerelease[1])&&(this.prerelease=[l,0]):this.prerelease=[l,0]);break;default:throw new Error(`invalid increment argument: ${o}`)}return this.format(),this.raw=this.version,this}};r.exports=D}}),Cu=S({"node_modules/semver/functions/compare.js"(e,r){I();var u=h2(),t=(a,n,s)=>new u(a,s).compare(new u(n,s));r.exports=t}}),v2=S({"node_modules/semver/functions/lt.js"(e,r){I();var u=Cu(),t=(a,n,s)=>u(a,n,s)<0;r.exports=t}}),m2=S({"node_modules/semver/functions/gte.js"(e,r){I();var u=Cu(),t=(a,n,s)=>u(a,n,s)>=0;r.exports=t}}),E2=S({"src/utils/arrayify.js"(e,r){"use strict";I(),r.exports=(u,t)=>Object.entries(u).map(a=>{let[n,s]=a;return Object.assign({[t]:n},s)})}}),C2=S({"package.json"(e,r){r.exports={version:"2.8.8"}}}),g2=S({"node_modules/outdent/lib/index.js"(e,r){"use strict";I(),Object.defineProperty(e,"__esModule",{value:!0}),e.outdent=void 0;function u(){for(var f=[],x=0;x<arguments.length;x++)f[x]=arguments[x]}function t(){return typeof WeakMap<"u"?new WeakMap:a()}function a(){return{add:u,delete:u,get:u,set:u,has:function(f){return!1}}}var n=Object.prototype.hasOwnProperty,s=function(f,x){return n.call(f,x)};function c(f,x){for(var v in x)s(x,v)&&(f[v]=x[v]);return f}var i=/^[ \t]*(?:\r\n|\r|\n)/,D=/(?:\r\n|\r|\n)[ \t]*$/,o=/^(?:[\r\n]|$)/,l=/(?:\r\n|\r|\n)([ \t]*)(?:[^ \t\r\n]|$)/,d=/^[ \t]*[\r\n][ \t\r\n]*$/;function p(f,x,v){var h=0,m=f[0].match(l);m&&(h=m[1].length);var C="(\\r\\n|\\r|\\n).{0,"+h+"}",w=new RegExp(C,"g");x&&(f=f.slice(1));var q=v.newline,L=v.trimLeadingNewline,B=v.trimTrailingNewline,O=typeof q=="string",T=f.length,P=f.map(function(A,j){return A=A.replace(w,"$1"),j===0&&L&&(A=A.replace(i,"")),j===T-1&&B&&(A=A.replace(D,"")),O&&(A=A.replace(/\r\n|\n|\r/g,function(H){return q})),A});return P}function g(f,x){for(var v="",h=0,m=f.length;h<m;h++)v+=f[h],h<m-1&&(v+=x[h]);return v}function F(f){return s(f,"raw")&&s(f,"length")}function E(f){var x=t(),v=t();function h(C){for(var w=[],q=1;q<arguments.length;q++)w[q-1]=arguments[q];if(F(C)){var L=C,B=(w[0]===h||w[0]===b)&&d.test(L[0])&&o.test(L[1]),O=B?v:x,T=O.get(L);if(T||(T=p(L,B,f),O.set(L,T)),w.length===0)return T[0];var P=g(T,B?w.slice(1):w);return P}else return E(c(c({},f),C||{}))}var m=c(h,{string:function(C){return p([C],!1,f)[0]}});return m}var b=E({trimLeadingNewline:!0,trimTrailingNewline:!0});if(e.outdent=b,e.default=b,typeof r<"u")try{r.exports=b,Object.defineProperty(b,"__esModule",{value:!0}),b.default=b,b.outdent=b}catch{}}}),F2=S({"src/main/core-options.js"(e,r){"use strict";I();var{outdent:u}=g2(),t="Config",a="Editor",n="Format",s="Other",c="Output",i="Global",D="Special",o={cursorOffset:{since:"1.4.0",category:D,type:"int",default:-1,range:{start:-1,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Print (to stderr) where a cursor at the given position would move to after formatting.
      This option cannot be used with --range-start and --range-end.
    `,cliCategory:a},endOfLine:{since:"1.15.0",category:i,type:"choice",default:[{since:"1.15.0",value:"auto"},{since:"2.0.0",value:"lf"}],description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:u`
          Maintain existing
          (mixed values within one file are normalised by looking at what's used after the first line)
        `}]},filepath:{since:"1.4.0",category:D,type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:s,cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{since:"1.8.0",category:D,type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:s},parser:{since:"0.0.10",category:i,type:"choice",default:[{since:"0.0.10",value:"babylon"},{since:"1.13.0",value:void 0}],description:"Which parser to use.",exception:l=>typeof l=="string"||typeof l=="function",choices:[{value:"flow",description:"Flow"},{value:"babel",since:"1.16.0",description:"JavaScript"},{value:"babel-flow",since:"1.16.0",description:"Flow"},{value:"babel-ts",since:"2.0.0",description:"TypeScript"},{value:"typescript",since:"1.4.0",description:"TypeScript"},{value:"acorn",since:"2.6.0",description:"JavaScript"},{value:"espree",since:"2.2.0",description:"JavaScript"},{value:"meriyah",since:"2.2.0",description:"JavaScript"},{value:"css",since:"1.7.1",description:"CSS"},{value:"less",since:"1.7.1",description:"Less"},{value:"scss",since:"1.7.1",description:"SCSS"},{value:"json",since:"1.5.0",description:"JSON"},{value:"json5",since:"1.13.0",description:"JSON5"},{value:"json-stringify",since:"1.13.0",description:"JSON.stringify"},{value:"graphql",since:"1.5.0",description:"GraphQL"},{value:"markdown",since:"1.8.0",description:"Markdown"},{value:"mdx",since:"1.15.0",description:"MDX"},{value:"vue",since:"1.10.0",description:"Vue"},{value:"yaml",since:"1.14.0",description:"YAML"},{value:"glimmer",since:"2.3.0",description:"Ember / Handlebars"},{value:"html",since:"1.15.0",description:"HTML"},{value:"angular",since:"1.15.0",description:"Angular"},{value:"lwc",since:"1.17.0",description:"Lightning Web Components"}]},plugins:{since:"1.10.0",type:"path",array:!0,default:[{value:[]}],category:i,description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:l=>typeof l=="string"||typeof l=="object",cliName:"plugin",cliCategory:t},pluginSearchDirs:{since:"1.13.0",type:"path",array:!0,default:[{value:[]}],category:i,description:u`
      Custom directory that contains prettier plugins in node_modules subdirectory.
      Overrides default behavior when plugins are searched relatively to the location of Prettier.
      Multiple values are accepted.
    `,exception:l=>typeof l=="string"||typeof l=="object",cliName:"plugin-search-dir",cliCategory:t},printWidth:{since:"0.0.0",category:i,type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},rangeEnd:{since:"1.4.0",category:D,type:"int",default:Number.POSITIVE_INFINITY,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Format code ending at a given character offset (exclusive).
      The range will extend forwards to the end of the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:a},rangeStart:{since:"1.4.0",category:D,type:"int",default:0,range:{start:0,end:Number.POSITIVE_INFINITY,step:1},description:u`
      Format code starting at a given character offset.
      The range will extend backwards to the start of the first line containing the selected statement.
      This option cannot be used with --cursor-offset.
    `,cliCategory:a},requirePragma:{since:"1.7.0",category:D,type:"boolean",default:!1,description:u`
      Require either '@prettier' or '@format' to be present in the file's first docblock comment
      in order for it to be formatted.
    `,cliCategory:s},tabWidth:{type:"int",category:i,default:2,description:"Number of spaces per indentation level.",range:{start:0,end:Number.POSITIVE_INFINITY,step:1}},useTabs:{since:"1.0.0",category:i,type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{since:"2.1.0",category:i,type:"choice",default:[{since:"2.1.0",value:"auto"}],description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};r.exports={CATEGORY_CONFIG:t,CATEGORY_EDITOR:a,CATEGORY_FORMAT:n,CATEGORY_OTHER:s,CATEGORY_OUTPUT:c,CATEGORY_GLOBAL:i,CATEGORY_SPECIAL:D,options:o}}}),A2=S({"src/main/support.js"(e,r){"use strict";I();var u={compare:Cu(),lt:v2(),gte:m2()},t=E2(),a=C2().version,n=F2().options;function s(){let{plugins:i=[],showUnreleased:D=!1,showDeprecated:o=!1,showInternal:l=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},d=a.split("-",1)[0],p=i.flatMap(f=>f.languages||[]).filter(F),g=t(Object.assign({},...i.map(f=>{let{options:x}=f;return x}),n),"name").filter(f=>F(f)&&E(f)).sort((f,x)=>f.name===x.name?0:f.name<x.name?-1:1).map(b).map(f=>{f=Object.assign({},f),Array.isArray(f.default)&&(f.default=f.default.length===1?f.default[0].value:f.default.filter(F).sort((v,h)=>u.compare(h.since,v.since))[0].value),Array.isArray(f.choices)&&(f.choices=f.choices.filter(v=>F(v)&&E(v)),f.name==="parser"&&c(f,p,i));let x=Object.fromEntries(i.filter(v=>v.defaultOptions&&v.defaultOptions[f.name]!==void 0).map(v=>[v.name,v.defaultOptions[f.name]]));return Object.assign(Object.assign({},f),{},{pluginDefaults:x})});return{languages:p,options:g};function F(f){return D||!("since"in f)||f.since&&u.gte(d,f.since)}function E(f){return o||!("deprecated"in f)||f.deprecated&&u.lt(d,f.deprecated)}function b(f){if(l)return f;let{cliName:x,cliCategory:v,cliDescription:h}=f;return Ol(f,_l)}}function c(i,D,o){let l=new Set(i.choices.map(d=>d.value));for(let d of D)if(d.parsers){for(let p of d.parsers)if(!l.has(p)){l.add(p);let g=o.find(E=>E.parsers&&E.parsers[p]),F=d.name;g&&g.name&&(F+=` (plugin: ${g.name})`),i.choices.push({value:p,description:F})}}}r.exports={getSupportInfo:s}}}),x2=S({"src/utils/is-non-empty-array.js"(e,r){"use strict";I();function u(t){return Array.isArray(t)&&t.length>0}r.exports=u}});function b2(){let{onlyFirst:e=!1}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var y2=je({"node_modules/strip-ansi/node_modules/ansi-regex/index.js"(){I()}});function w2(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(b2(),"")}var B2=je({"node_modules/strip-ansi/index.js"(){I(),y2()}});function k2(e){return Number.isInteger(e)?e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141):!1}var q2=je({"node_modules/is-fullwidth-code-point/index.js"(){I()}}),_2=S({"node_modules/emoji-regex/index.js"(e,r){"use strict";I(),r.exports=function(){return/\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|(?:\uD83E\uDDD1\uD83C\uDFFF\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFF\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFE])|(?:\uD83E\uDDD1\uD83C\uDFFE\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFE\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFD\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFD\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFC\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFC\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|(?:\uD83E\uDDD1\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1|\uD83D\uDC69\uD83C\uDFFB\u200D\uD83E\uDD1D\u200D(?:\uD83D[\uDC68\uDC69]))(?:\uD83C[\uDFFC-\uDFFF])|\uD83D\uDC68(?:\uD83C\uDFFB(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFC-\uDFFF])|[\u2695\u2696\u2708]\uFE0F|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))?|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFF]))|\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFE])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB-\uDFFD\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83E\uDD1D\u200D\uD83D\uDC68(?:\uD83C[\uDFFB\uDFFD-\uDFFF])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])\uFE0F|\u200D(?:(?:\uD83D[\uDC68\uDC69])\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D[\uDC66\uDC67])|\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC)?|(?:\uD83D\uDC69(?:\uD83C\uDFFB\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|(?:\uD83C[\uDFFC-\uDFFF])\u200D\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83E\uDDD1(?:\uD83C[\uDFFB-\uDFFF])\u200D\uD83E\uDD1D\u200D\uD83E\uDDD1)(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67]))|\uD83D\uDC69(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69])|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83E\uDDD1(?:\u200D(?:\uD83E\uDD1D\u200D\uD83E\uDDD1|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFF\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFE\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFD\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFC\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C\uDFFB\u200D(?:\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD]))|\uD83D\uDC69\u200D\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D\uD83D\uDC69\u200D(?:\uD83D[\uDC66\uDC67])|\uD83D\uDC69\u200D\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83E\uDDD1(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDC69(?:\uD83C\uDFFF\u200D[\u2695\u2696\u2708]|\uD83C\uDFFE\u200D[\u2695\u2696\u2708]|\uD83C\uDFFD\u200D[\u2695\u2696\u2708]|\uD83C\uDFFC\u200D[\u2695\u2696\u2708]|\uD83C\uDFFB\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708])|\uD83D\uDE36\u200D\uD83C\uDF2B|\uD83C\uDFF3\uFE0F\u200D\u26A7|\uD83D\uDC3B\u200D\u2744|(?:(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF])\u200D[\u2640\u2642]|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]|\uD83C\uDFF4\u200D\u2620|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])\u200D[\u2640\u2642]|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u2600-\u2604\u260E\u2611\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26B0\u26B1\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0\u26F1\u26F4\u26F7\u26F8\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u3030\u303D\u3297\u3299]|\uD83C[\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]|\uD83D[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3])\uFE0F|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|\uD83D\uDC69\u200D\uD83D\uDC67|\uD83D\uDC69\u200D\uD83D\uDC66|\uD83D\uDE35\u200D\uD83D\uDCAB|\uD83D\uDE2E\u200D\uD83D\uDCA8|\uD83D\uDC15\u200D\uD83E\uDDBA|\uD83E\uDDD1(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83D\uDC69(?:\uD83C\uDFFF|\uD83C\uDFFE|\uD83C\uDFFD|\uD83C\uDFFC|\uD83C\uDFFB)?|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83D\uDC08\u200D\u2B1B|\u2764\uFE0F\u200D(?:\uD83D\uDD25|\uD83E\uDE79)|\uD83D\uDC41\uFE0F|\uD83C\uDFF3\uFE0F|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|[#\*0-9]\uFE0F\u20E3|\u2764\uFE0F|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|\uD83C\uDFF4|(?:[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270C\u270D]|\uD83D[\uDD74\uDD90])(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])|[\u270A\u270B]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC08\uDC15\uDC3B\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE2E\uDE35\uDE36\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5]|\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD]|\uD83D\uDC6F|\uD83E[\uDD3C\uDDDE\uDDDF]|[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0D\uDD0E\uDD10-\uDD17\uDD1D\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78\uDD7A-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCB\uDDD0\uDDE0-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6]|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDED5-\uDED7\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26A7\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDED5-\uDED7\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEFC\uDFE0-\uDFEB]|\uD83E[\uDD0C-\uDD3A\uDD3C-\uDD45\uDD47-\uDD78\uDD7A-\uDDCB\uDDCD-\uDDFF\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6])\uFE0F|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDC8F\uDC91\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD0C\uDD0F\uDD18-\uDD1F\uDD26\uDD30-\uDD39\uDD3C-\uDD3E\uDD77\uDDB5\uDDB6\uDDB8\uDDB9\uDDBB\uDDCD-\uDDCF\uDDD1-\uDDDD])/g}}}),ta={};Pi(ta,{default:()=>O2});function O2(e){if(typeof e!="string"||e.length===0||(e=w2(e),e.length===0))return 0;e=e.replace((0,na.default)(),"  ");let r=0;for(let u=0;u<e.length;u++){let t=e.codePointAt(u);t<=31||t>=127&&t<=159||t>=768&&t<=879||(t>65535&&u++,r+=k2(t)?2:1)}return r}var na,I2=je({"node_modules/string-width/index.js"(){I(),B2(),q2(),na=Rl(_2())}}),S2=S({"src/utils/get-string-width.js"(e,r){"use strict";I();var u=(I2(),zi(ta)).default,t=/[^\x20-\x7F]/;function a(n){return n?t.test(n)?u(n):n.length:0}r.exports=a}}),gu=S({"src/utils/text/skip.js"(e,r){"use strict";I();function u(c){return(i,D,o)=>{let l=o&&o.backwards;if(D===!1)return!1;let{length:d}=i,p=D;for(;p>=0&&p<d;){let g=i.charAt(p);if(c instanceof RegExp){if(!c.test(g))return p}else if(!c.includes(g))return p;l?p--:p++}return p===-1||p===d?p:!1}}var t=u(/\s/),a=u(" 	"),n=u(",; 	"),s=u(/[^\n\r]/);r.exports={skipWhitespace:t,skipSpaces:a,skipToLineEnd:n,skipEverythingButNewLine:s}}}),ia=S({"src/utils/text/skip-inline-comment.js"(e,r){"use strict";I();function u(t,a){if(a===!1)return!1;if(t.charAt(a)==="/"&&t.charAt(a+1)==="*"){for(let n=a+2;n<t.length;++n)if(t.charAt(n)==="*"&&t.charAt(n+1)==="/")return n+2}return a}r.exports=u}}),aa=S({"src/utils/text/skip-trailing-comment.js"(e,r){"use strict";I();var{skipEverythingButNewLine:u}=gu();function t(a,n){return n===!1?!1:a.charAt(n)==="/"&&a.charAt(n+1)==="/"?u(a,n):n}r.exports=t}}),oa=S({"src/utils/text/skip-newline.js"(e,r){"use strict";I();function u(t,a,n){let s=n&&n.backwards;if(a===!1)return!1;let c=t.charAt(a);if(s){if(t.charAt(a-1)==="\r"&&c===`
`)return a-2;if(c===`
`||c==="\r"||c==="\u2028"||c==="\u2029")return a-1}else{if(c==="\r"&&t.charAt(a+1)===`
`)return a+2;if(c===`
`||c==="\r"||c==="\u2028"||c==="\u2029")return a+1}return a}r.exports=u}}),T2=S({"src/utils/text/get-next-non-space-non-comment-character-index-with-start-index.js"(e,r){"use strict";I();var u=ia(),t=oa(),a=aa(),{skipSpaces:n}=gu();function s(c,i){let D=null,o=i;for(;o!==D;)D=o,o=n(c,o),o=u(c,o),o=a(c,o),o=t(c,o);return o}r.exports=s}}),N2=S({"src/common/util.js"(e,r){"use strict";I();var{default:u}=(l2(),zi(ea)),t=D2(),{getSupportInfo:a}=A2(),n=x2(),s=S2(),{skipWhitespace:c,skipSpaces:i,skipToLineEnd:D,skipEverythingButNewLine:o}=gu(),l=ia(),d=aa(),p=oa(),g=T2(),F=k=>k[k.length-2];function E(k){return(y,_,N)=>{let V=N&&N.backwards;if(_===!1)return!1;let{length:W}=y,K=_;for(;K>=0&&K<W;){let ee=y.charAt(K);if(k instanceof RegExp){if(!k.test(ee))return K}else if(!k.includes(ee))return K;V?K--:K++}return K===-1||K===W?K:!1}}function b(k,y){let _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},N=i(k,_.backwards?y-1:y,_),V=p(k,N,_);return N!==V}function f(k,y,_){for(let N=y;N<_;++N)if(k.charAt(N)===`
`)return!0;return!1}function x(k,y,_){let N=_(y)-1;N=i(k,N,{backwards:!0}),N=p(k,N,{backwards:!0}),N=i(k,N,{backwards:!0});let V=p(k,N,{backwards:!0});return N!==V}function v(k,y){let _=null,N=y;for(;N!==_;)_=N,N=D(k,N),N=l(k,N),N=i(k,N);return N=d(k,N),N=p(k,N),N!==!1&&b(k,N)}function h(k,y,_){return v(k,_(y))}function m(k,y,_){return g(k,_(y))}function C(k,y,_){return k.charAt(m(k,y,_))}function w(k,y){let _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return i(k,_.backwards?y-1:y,_)!==y}function q(k,y){let _=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0,N=0;for(let V=_;V<k.length;++V)k[V]==="	"?N=N+y-N%y:N++;return N}function L(k,y){let _=k.lastIndexOf(`
`);return _===-1?0:q(k.slice(_+1).match(/^[\t ]*/)[0],y)}function B(k,y){let _={quote:'"',regex:/"/g,escaped:"&quot;"},N={quote:"'",regex:/'/g,escaped:"&apos;"},V=y==="'"?N:_,W=V===N?_:N,K=V;if(k.includes(V.quote)||k.includes(W.quote)){let ee=(k.match(V.regex)||[]).length,Y=(k.match(W.regex)||[]).length;K=ee>Y?W:V}return K}function O(k,y){let _=k.slice(1,-1),N=y.parser==="json"||y.parser==="json5"&&y.quoteProps==="preserve"&&!y.singleQuote?'"':y.__isInHtmlAttribute?"'":B(_,y.singleQuote?"'":'"').quote;return T(_,N,!(y.parser==="css"||y.parser==="less"||y.parser==="scss"||y.__embeddedInHtml))}function T(k,y,_){let N=y==='"'?"'":'"',V=/\\(.)|(["'])/gs,W=k.replace(V,(K,ee,Y)=>ee===N?ee:Y===y?"\\"+Y:Y||(_&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/.test(ee)?ee:"\\"+ee));return y+W+y}function P(k){return k.toLowerCase().replace(/^([+-]?[\d.]+e)(?:\+|(-))?0*(\d)/,"$1$2$3").replace(/^([+-]?[\d.]+)e[+-]?0+$/,"$1").replace(/^([+-])?\./,"$10.").replace(/(\.\d+?)0+(?=e|$)/,"$1").replace(/\.(?=e|$)/,"")}function A(k,y){let _=k.match(new RegExp(`(${u(y)})+`,"g"));return _===null?0:_.reduce((N,V)=>Math.max(N,V.length/y.length),0)}function j(k,y){let _=k.match(new RegExp(`(${u(y)})+`,"g"));if(_===null)return 0;let N=new Map,V=0;for(let W of _){let K=W.length/y.length;N.set(K,!0),K>V&&(V=K)}for(let W=1;W<V;W++)if(!N.get(W))return W;return V+1}function H(k,y){(k.comments||(k.comments=[])).push(y),y.printed=!1,y.nodeDescription=U(k)}function G(k,y){y.leading=!0,y.trailing=!1,H(k,y)}function X(k,y,_){y.leading=!1,y.trailing=!1,_&&(y.marker=_),H(k,y)}function R(k,y){y.leading=!1,y.trailing=!0,H(k,y)}function J(k,y){let{languages:_}=a({plugins:y.plugins}),N=_.find(V=>{let{name:W}=V;return W.toLowerCase()===k})||_.find(V=>{let{aliases:W}=V;return Array.isArray(W)&&W.includes(k)})||_.find(V=>{let{extensions:W}=V;return Array.isArray(W)&&W.includes(`.${k}`)});return N&&N.parsers[0]}function z(k){return k&&k.type==="front-matter"}function M(k){let y=new WeakMap;return function(_){return y.has(_)||y.set(_,Symbol(k)),y.get(_)}}function U(k){let y=k.type||k.kind||"(unknown type)",_=String(k.name||k.id&&(typeof k.id=="object"?k.id.name:k.id)||k.key&&(typeof k.key=="object"?k.key.name:k.key)||k.value&&(typeof k.value=="object"?"":String(k.value))||k.operator||"");return _.length>20&&(_=_.slice(0,19)+"\u2026"),y+(_?" "+_:"")}r.exports={inferParserByLanguage:J,getStringWidth:s,getMaxContinuousCount:A,getMinNotPresentContinuousCount:j,getPenultimate:F,getLast:t,getNextNonSpaceNonCommentCharacterIndexWithStartIndex:g,getNextNonSpaceNonCommentCharacterIndex:m,getNextNonSpaceNonCommentCharacter:C,skip:E,skipWhitespace:c,skipSpaces:i,skipToLineEnd:D,skipEverythingButNewLine:o,skipInlineComment:l,skipTrailingComment:d,skipNewline:p,isNextLineEmptyAfterIndex:v,isNextLineEmpty:h,isPreviousLineEmpty:x,hasNewline:b,hasNewlineInRange:f,hasSpaces:w,getAlignmentSize:q,getIndentSize:L,getPreferredQuote:B,printString:O,printNumber:P,makeString:T,addLeadingComment:G,addDanglingComment:X,addTrailingComment:R,isFrontMatterNode:z,isNonEmptyArray:n,createGroupIdMapper:M}}}),L2=S({"src/language-markdown/constants.evaluate.js"(e,r){r.exports={cjkPattern:"(?:[\\u02ea-\\u02eb\\u1100-\\u11ff\\u2e80-\\u2e99\\u2e9b-\\u2ef3\\u2f00-\\u2fd5\\u2ff0-\\u303f\\u3041-\\u3096\\u3099-\\u309f\\u30a1-\\u30fa\\u30fc-\\u30ff\\u3105-\\u312f\\u3131-\\u318e\\u3190-\\u3191\\u3196-\\u31ba\\u31c0-\\u31e3\\u31f0-\\u321e\\u322a-\\u3247\\u3260-\\u327e\\u328a-\\u32b0\\u32c0-\\u32cb\\u32d0-\\u3370\\u337b-\\u337f\\u33e0-\\u33fe\\u3400-\\u4db5\\u4e00-\\u9fef\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\uf900-\\ufa6d\\ufa70-\\ufad9\\ufe10-\\ufe1f\\ufe30-\\ufe6f\\uff00-\\uffef]|[\\ud840-\\ud868\\ud86a-\\ud86c\\ud86f-\\ud872\\ud874-\\ud879][\\udc00-\\udfff]|\\ud82c[\\udc00-\\udd1e\\udd50-\\udd52\\udd64-\\udd67]|\\ud83c[\\ude00\\ude50-\\ude51]|\\ud869[\\udc00-\\uded6\\udf00-\\udfff]|\\ud86d[\\udc00-\\udf34\\udf40-\\udfff]|\\ud86e[\\udc00-\\udc1d\\udc20-\\udfff]|\\ud873[\\udc00-\\udea1\\udeb0-\\udfff]|\\ud87a[\\udc00-\\udfe0]|\\ud87e[\\udc00-\\ude1d])(?:[\\ufe00-\\ufe0f]|\\udb40[\\udd00-\\uddef])?",kPattern:"[\\u1100-\\u11ff\\u3001-\\u3003\\u3008-\\u3011\\u3013-\\u301f\\u302e-\\u3030\\u3037\\u30fb\\u3131-\\u318e\\u3200-\\u321e\\u3260-\\u327e\\ua960-\\ua97c\\uac00-\\ud7a3\\ud7b0-\\ud7c6\\ud7cb-\\ud7fb\\ufe45-\\ufe46\\uff61-\\uff65\\uffa0-\\uffbe\\uffc2-\\uffc7\\uffca-\\uffcf\\uffd2-\\uffd7\\uffda-\\uffdc]",punctuationPattern:"[\\u0021-\\u002f\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e\\u00a1\\u00a7\\u00ab\\u00b6-\\u00b7\\u00bb\\u00bf\\u037e\\u0387\\u055a-\\u055f\\u0589-\\u058a\\u05be\\u05c0\\u05c3\\u05c6\\u05f3-\\u05f4\\u0609-\\u060a\\u060c-\\u060d\\u061b\\u061e-\\u061f\\u066a-\\u066d\\u06d4\\u0700-\\u070d\\u07f7-\\u07f9\\u0830-\\u083e\\u085e\\u0964-\\u0965\\u0970\\u09fd\\u0a76\\u0af0\\u0c77\\u0c84\\u0df4\\u0e4f\\u0e5a-\\u0e5b\\u0f04-\\u0f12\\u0f14\\u0f3a-\\u0f3d\\u0f85\\u0fd0-\\u0fd4\\u0fd9-\\u0fda\\u104a-\\u104f\\u10fb\\u1360-\\u1368\\u1400\\u166e\\u169b-\\u169c\\u16eb-\\u16ed\\u1735-\\u1736\\u17d4-\\u17d6\\u17d8-\\u17da\\u1800-\\u180a\\u1944-\\u1945\\u1a1e-\\u1a1f\\u1aa0-\\u1aa6\\u1aa8-\\u1aad\\u1b5a-\\u1b60\\u1bfc-\\u1bff\\u1c3b-\\u1c3f\\u1c7e-\\u1c7f\\u1cc0-\\u1cc7\\u1cd3\\u2010-\\u2027\\u2030-\\u2043\\u2045-\\u2051\\u2053-\\u205e\\u207d-\\u207e\\u208d-\\u208e\\u2308-\\u230b\\u2329-\\u232a\\u2768-\\u2775\\u27c5-\\u27c6\\u27e6-\\u27ef\\u2983-\\u2998\\u29d8-\\u29db\\u29fc-\\u29fd\\u2cf9-\\u2cfc\\u2cfe-\\u2cff\\u2d70\\u2e00-\\u2e2e\\u2e30-\\u2e4f\\u3001-\\u3003\\u3008-\\u3011\\u3014-\\u301f\\u3030\\u303d\\u30a0\\u30fb\\ua4fe-\\ua4ff\\ua60d-\\ua60f\\ua673\\ua67e\\ua6f2-\\ua6f7\\ua874-\\ua877\\ua8ce-\\ua8cf\\ua8f8-\\ua8fa\\ua8fc\\ua92e-\\ua92f\\ua95f\\ua9c1-\\ua9cd\\ua9de-\\ua9df\\uaa5c-\\uaa5f\\uaade-\\uaadf\\uaaf0-\\uaaf1\\uabeb\\ufd3e-\\ufd3f\\ufe10-\\ufe19\\ufe30-\\ufe52\\ufe54-\\ufe61\\ufe63\\ufe68\\ufe6a-\\ufe6b\\uff01-\\uff03\\uff05-\\uff0a\\uff0c-\\uff0f\\uff1a-\\uff1b\\uff1f-\\uff20\\uff3b-\\uff3d\\uff3f\\uff5b\\uff5d\\uff5f-\\uff65]|\\ud800[\\udd00-\\udd02\\udf9f\\udfd0]|\\ud801[\\udd6f]|\\ud802[\\udc57\\udd1f\\udd3f\\ude50-\\ude58\\ude7f\\udef0-\\udef6\\udf39-\\udf3f\\udf99-\\udf9c]|\\ud803[\\udf55-\\udf59]|\\ud804[\\udc47-\\udc4d\\udcbb-\\udcbc\\udcbe-\\udcc1\\udd40-\\udd43\\udd74-\\udd75\\uddc5-\\uddc8\\uddcd\\udddb\\udddd-\\udddf\\ude38-\\ude3d\\udea9]|\\ud805[\\udc4b-\\udc4f\\udc5b\\udc5d\\udcc6\\uddc1-\\uddd7\\ude41-\\ude43\\ude60-\\ude6c\\udf3c-\\udf3e]|\\ud806[\\udc3b\\udde2\\ude3f-\\ude46\\ude9a-\\ude9c\\ude9e-\\udea2]|\\ud807[\\udc41-\\udc45\\udc70-\\udc71\\udef7-\\udef8\\udfff]|\\ud809[\\udc70-\\udc74]|\\ud81a[\\ude6e-\\ude6f\\udef5\\udf37-\\udf3b\\udf44]|\\ud81b[\\ude97-\\ude9a\\udfe2]|\\ud82f[\\udc9f]|\\ud836[\\ude87-\\ude8b]|\\ud83a[\\udd5e-\\udd5f]"}}}),R2=S({"src/language-markdown/utils.js"(e,r){"use strict";I();var{getLast:u}=N2(),{locStart:t,locEnd:a}=Zi(),{cjkPattern:n,kPattern:s,punctuationPattern:c}=L2(),i=["liquidNode","inlineCode","emphasis","esComment","strong","delete","wikiLink","link","linkReference","image","imageReference","footnote","footnoteReference","sentence","whitespace","word","break","inlineMath"],D=[...i,"tableCell","paragraph","heading"],o=new RegExp(s),l=new RegExp(c);function d(f,x){let v="non-cjk",h="cj-letter",m="k-letter",C="cjk-punctuation",w=[],q=(x.proseWrap==="preserve"?f:f.replace(new RegExp(`(${n})
(${n})`,"g"),"$1$2")).split(/([\t\n ]+)/);for(let[B,O]of q.entries()){if(B%2===1){w.push({type:"whitespace",value:/\n/.test(O)?`
`:" "});continue}if((B===0||B===q.length-1)&&O==="")continue;let T=O.split(new RegExp(`(${n})`));for(let[P,A]of T.entries())if(!((P===0||P===T.length-1)&&A==="")){if(P%2===0){A!==""&&L({type:"word",value:A,kind:v,hasLeadingPunctuation:l.test(A[0]),hasTrailingPunctuation:l.test(u(A))});continue}L(l.test(A)?{type:"word",value:A,kind:C,hasLeadingPunctuation:!0,hasTrailingPunctuation:!0}:{type:"word",value:A,kind:o.test(A)?m:h,hasLeadingPunctuation:!1,hasTrailingPunctuation:!1})}}return w;function L(B){let O=u(w);O&&O.type==="word"&&(O.kind===v&&B.kind===h&&!O.hasTrailingPunctuation||O.kind===h&&B.kind===v&&!B.hasLeadingPunctuation?w.push({type:"whitespace",value:" "}):!T(v,C)&&![O.value,B.value].some(P=>/\u3000/.test(P))&&w.push({type:"whitespace",value:""})),w.push(B);function T(P,A){return O.kind===P&&B.kind===A||O.kind===A&&B.kind===P}}}function p(f,x){let[,v,h,m]=x.slice(f.position.start.offset,f.position.end.offset).match(/^\s*(\d+)(\.|\))(\s*)/);return{numberText:v,marker:h,leadingSpaces:m}}function g(f,x){if(!f.ordered||f.children.length<2)return!1;let v=Number(p(f.children[0],x.originalText).numberText),h=Number(p(f.children[1],x.originalText).numberText);if(v===0&&f.children.length>2){let m=Number(p(f.children[2],x.originalText).numberText);return h===1&&m===1}return h===1}function F(f,x){let{value:v}=f;return f.position.end.offset===x.length&&v.endsWith(`
`)&&x.endsWith(`
`)?v.slice(0,-1):v}function E(f,x){return function v(h,m,C){let w=Object.assign({},x(h,m,C));return w.children&&(w.children=w.children.map((q,L)=>v(q,L,[w,...C]))),w}(f,null,[])}function b(f){if((f==null?void 0:f.type)!=="link"||f.children.length!==1)return!1;let[x]=f.children;return t(f)===t(x)&&a(f)===a(x)}r.exports={mapAst:E,splitText:d,punctuationPattern:c,getFencedCodeBlockValue:F,getOrderedListItemInfo:p,hasGitDiffFriendlyOrderedList:g,INLINE_NODE_TYPES:i,INLINE_NODE_WRAPPER_TYPES:D,isAutolink:b}}}),j2=S({"src/language-markdown/unified-plugins/html-to-jsx.js"(e,r){"use strict";I();var u=Qi(),{mapAst:t,INLINE_NODE_WRAPPER_TYPES:a}=R2();function n(){return s=>t(s,(c,i,D)=>{let[o]=D;return c.type!=="html"||u.COMMENT_REGEX.test(c.value)||a.includes(o.type)?c:Object.assign(Object.assign({},c),{},{type:"jsx"})})}r.exports=n}}),P2=S({"src/language-markdown/unified-plugins/front-matter.js"(e,r){"use strict";I();var u=Ji();function t(){let a=this.Parser.prototype;a.blockMethods=["frontMatter",...a.blockMethods],a.blockTokenizers.frontMatter=n;function n(s,c){let i=u(c);if(i.frontMatter)return s(i.frontMatter.raw)(i.frontMatter)}n.onlyAtStart=!0}r.exports=t}}),M2=S({"src/language-markdown/unified-plugins/liquid.js"(e,r){"use strict";I();function u(){let t=this.Parser.prototype,a=t.inlineMethods;a.splice(a.indexOf("text"),0,"liquid"),t.inlineTokenizers.liquid=n;function n(s,c){let i=c.match(/^({%.*?%}|{{.*?}})/s);if(i)return s(i[0])({type:"liquidNode",value:i[0]})}n.locator=function(s,c){return s.indexOf("{",c)}}r.exports=u}}),z2=S({"src/language-markdown/unified-plugins/wiki-link.js"(e,r){"use strict";I();function u(){let t="wikiLink",a=/^\[\[(?<linkContents>.+?)]]/s,n=this.Parser.prototype,s=n.inlineMethods;s.splice(s.indexOf("link"),0,t),n.inlineTokenizers.wikiLink=c;function c(i,D){let o=a.exec(D);if(o){let l=o.groups.linkContents.trim();return i(o[0])({type:t,value:l})}}c.locator=function(i,D){return i.indexOf("[",D)}}r.exports=u}}),$2=S({"src/language-markdown/unified-plugins/loose-items.js"(e,r){"use strict";I();function u(){let t=this.Parser.prototype,a=t.blockTokenizers.list;function n(s,c,i){return c.type==="listItem"&&(c.loose=c.spread||s.charAt(s.length-1)===`
`,c.loose&&(i.loose=!0)),c}t.blockTokenizers.list=function(c,i,D){function o(l){let d=c(l);function p(g,F){return d(n(l,g,F),F)}return p.reset=function(g,F){return d.reset(n(l,g,F),F)},p}return o.now=c.now,a.call(this,o,i,D)}}r.exports=u}});I();var U2=GD(),G2=t2(),V2=a2(),H2=o2(),X2=s2(),{locStart:W2,locEnd:K2}=Zi(),Li=Qi(),Y2=j2(),J2=P2(),Z2=M2(),Q2=z2(),ef=$2();function sa(e){let{isMDX:r}=e;return u=>{let t=G2().use(U2,Object.assign({commonmark:!0},r&&{blocks:[Li.BLOCKS_REGEX]})).use(H2).use(J2).use(V2).use(r?Li.esSyntax:Ri).use(Z2).use(r?Y2:Ri).use(Q2).use(ef);return t.runSync(t.parse(u))}}function Ri(e){return e}var ca={astFormat:"mdast",hasPragma:X2.hasPragma,locStart:W2,locEnd:K2},ji=Object.assign(Object.assign({},ca),{},{parse:sa({isMDX:!1})}),rf=Object.assign(Object.assign({},ca),{},{parse:sa({isMDX:!0})});la.exports={parsers:{remark:ji,markdown:ji,mdx:rf}}});return uf();});