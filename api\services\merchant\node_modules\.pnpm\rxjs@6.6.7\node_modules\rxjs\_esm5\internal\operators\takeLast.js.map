{"version": 3, "file": "takeLast.js", "sources": ["../../../src/internal/operators/takeLast.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AAC1E,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAC;AA8C5C,MAAM,UAAU,QAAQ,CAAI,KAAa;IACvC,OAAO,SAAS,wBAAwB,CAAC,MAAqB;QAC5D,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,KAAK,EAAE,CAAC;SAChB;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;IACH,CAAC,CAAC;AACJ,CAAC;AAED;IACE,0BAAoB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,uBAAuB,CAAC;SACnC;IACH,CAAC;IAED,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1E,CAAC;IACH,uBAAC;AAAD,CAAC,AAVD,IAUC;AAOD;IAAoC,8CAAa;IAI/C,4BAAY,WAA0B,EAAU,KAAa;QAA7D,YACE,kBAAM,WAAW,CAAC,SACnB;QAF+C,WAAK,GAAL,KAAK,CAAQ;QAHrD,UAAI,GAAa,IAAI,KAAK,EAAE,CAAC;QAC7B,WAAK,GAAW,CAAC,CAAC;;IAI1B,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACrB;IACH,CAAC;IAES,sCAAS,GAAnB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvB,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACjE,IAAM,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC;YAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9B,IAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IACH,yBAAC;AAAD,CAAC,AArCD,CAAoC,UAAU,GAqC7C"}