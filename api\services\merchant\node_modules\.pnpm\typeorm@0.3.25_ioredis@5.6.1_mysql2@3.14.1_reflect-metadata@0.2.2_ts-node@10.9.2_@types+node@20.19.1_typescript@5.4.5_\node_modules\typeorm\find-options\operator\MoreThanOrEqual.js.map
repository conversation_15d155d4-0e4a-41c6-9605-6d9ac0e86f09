{"version": 3, "sources": ["../../src/find-options/operator/MoreThanOrEqual.ts"], "names": [], "mappings": ";;AAMA,0CAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,eAAe,CAAI,KAA0B;IACzD,OAAO,IAAI,2BAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;AACrD,CAAC", "file": "MoreThanOrEqual.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: MoreThanOrEqual(10) }\n */\nexport function MoreThanOrEqual<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"moreThanOrEqual\", value)\n}\n"], "sourceRoot": "../.."}