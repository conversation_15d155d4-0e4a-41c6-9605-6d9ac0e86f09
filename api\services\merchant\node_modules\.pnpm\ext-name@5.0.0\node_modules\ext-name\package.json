{"name": "ext-name", "version": "5.0.0", "description": "Get the file extension and MIME type from a file", "license": "MIT", "repository": "kevva/ext-name", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["ext", "extname", "mime"], "dependencies": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}