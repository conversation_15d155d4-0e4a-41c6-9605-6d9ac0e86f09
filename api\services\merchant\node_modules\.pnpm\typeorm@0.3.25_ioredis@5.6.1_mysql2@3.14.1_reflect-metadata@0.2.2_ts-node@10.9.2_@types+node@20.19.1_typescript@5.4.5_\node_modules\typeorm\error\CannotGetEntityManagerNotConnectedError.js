"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CannotGetEntityManagerNotConnectedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when consumer tries to access entity manager before connection is established.
 */
class CannotGetEntityManagerNotConnectedError extends TypeORMError_1.TypeORMError {
    constructor(connectionName) {
        super(`Cannot get entity manager for "${connectionName}" connection because connection is not yet established.`);
    }
}
exports.CannotGetEntityManagerNotConnectedError = CannotGetEntityManagerNotConnectedError;

//# sourceMappingURL=CannotGetEntityManagerNotConnectedError.js.map
