"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const decorator_1 = require("@midwayjs/decorator");
const DefaultConfig = require("./config/config.default");
const filter_1 = require("./exception/filter");
const func_1 = require("./util/func");
const koa = require("@midwayjs/koa");
const config_1 = require("./module/config");
const import_1 = require("./module/import");
const event_1 = require("./event");
const eps_1 = require("./rest/eps");
const decorator_2 = require("./decorator");
const cache = require("@midwayjs/cache-manager");
const _cache = require("@midwayjs/cache");
let CoolConfiguration = class CoolConfiguration {
    async onReady(container) {
        this.coolEventManager.emit("onReady");
        // 处理模块配置
        await container.getAsync(config_1.CoolModuleConfig);
        // 常用函数处理
        await container.getAsync(func_1.FuncUtil);
        // 异常处理
        this.app.useFilter([filter_1.CoolExceptionFilter]);
        // 装饰器
        await container.getAsync(decorator_2.CoolDecorator);
        // 缓存设置为全局
        // global["COOL-CACHE"] = await container.getAsync(CacheManager);
        // // 清除 location
        // setTimeout(() => {
        //   location.clean();
        //   this.coreLogger.info("\x1B[36m [cool:core] location clean \x1B[0m");
        // }, 10000);
    }
    async onConfigLoad(container, mainApp) { }
    async onServerReady(container) {
        // 事件
        await (await container.getAsync(event_1.CoolEventManager)).init();
        // 导入模块数据
        (await container.getAsync(import_1.CoolModuleImport)).init();
        // 实体与路径
        const eps = await container.getAsync(eps_1.CoolEps);
        eps.init();
        this.coolEventManager.emit("onServerReady");
        // location.clean();
    }
};
exports.CoolConfiguration = CoolConfiguration;
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], CoolConfiguration.prototype, "coreLogger", void 0);
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], CoolConfiguration.prototype, "app", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", event_1.CoolEventManager)
], CoolConfiguration.prototype, "coolEventManager", void 0);
exports.CoolConfiguration = CoolConfiguration = __decorate([
    (0, decorator_1.Configuration)({
        namespace: "cool",
        imports: [_cache, cache],
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], CoolConfiguration);
