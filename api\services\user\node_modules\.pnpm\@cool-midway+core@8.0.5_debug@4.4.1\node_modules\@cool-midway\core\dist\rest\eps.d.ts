import { MidwayWebRouterService } from '@midwayjs/core';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
import { CoolUrlTagData } from '../tag/data';
import { CurdOption, QueryOp } from '../decorator/controller';
/**
 * 实体路径
 */
export declare class CoolEps {
    admin: {};
    app: {};
    module: {};
    midwayWebRouterService: MidwayWebRouterService;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    epsConfig: boolean;
    moduleConfig: any;
    coolUrlTagData: CoolUrlTagData;
    init(): Promise<void>;
    /**
     * 获取分页查询配置
     * @param curdOption
     * @returns
     */
    getPageOp(curdOption: CurdOption): Promise<QueryOp>;
    /**
     * 处理列
     * @param entitys
     * @param entityColumns
     * @param curdOption
     */
    pageColumns(entitys: Record<string, any[]>, curdOption: CurdOption): Promise<any[]>;
    /**
     * 模块信息
     * @param module
     */
    modules(module?: string): Promise<any>;
    /**
     * 所有controller
     * @returns
     */
    controller(): Promise<any[]>;
    /**
     * 所有路由
     * @returns
     */
    router(): Promise<any>;
    /**
     * 所有实体
     * @returns
     */
    entity(): Promise<{}>;
}
