{"@midwayjs/faas-typings": "3.3.5", "@midwayjs/fc-starter": "3.4.12", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.12", "@midwayjs/axios": "3.4.12", "@midwayjs/bootstrap": "3.4.12", "@midwayjs/cache": "3.4.12", "@midwayjs/code-dye": "3.4.12", "@midwayjs/consul": "3.4.12", "@midwayjs/core": "3.4.12", "@midwayjs/cos": "3.4.12", "@midwayjs/cross-domain": "3.4.12", "@midwayjs/decorator": "3.4.11", "@midwayjs/express-session": "3.4.12", "@midwayjs/faas": "3.4.12", "@midwayjs/grpc": "3.4.12", "@midwayjs/http-proxy": "3.4.12", "@midwayjs/i18n": "3.4.12", "@midwayjs/info": "3.4.12", "@midwayjs/jwt": "3.4.12", "@midwayjs/kafka": "3.4.12", "@midwayjs/mikro": "3.4.12", "@midwayjs/mock": "3.4.12", "@midwayjs/mongoose": "3.4.12", "@midwayjs/oss": "3.4.12", "@midwayjs/otel": "3.4.12", "@midwayjs/passport": "3.4.12", "@midwayjs/process-agent": "3.4.12", "@midwayjs/prometheus-socket-io": "3.4.12", "@midwayjs/prometheus": "3.4.12", "@midwayjs/rabbitmq": "3.4.12", "@midwayjs/redis": "3.4.12", "@midwayjs/security": "3.4.12", "@midwayjs/sequelize": "3.4.12", "@midwayjs/session": "3.4.12", "@midwayjs/socketio": "3.4.12", "@midwayjs/static-file": "3.4.12", "@midwayjs/swagger": "3.4.12", "@midwayjs/tablestore": "3.4.12", "@midwayjs/task": "3.4.12", "@midwayjs/typegoose": "3.4.12", "@midwayjs/typeorm": "3.4.12", "@midwayjs/upload": "3.4.12", "@midwayjs/validate": "3.4.12", "@midwayjs/version": "3.4.12", "@midwayjs/view-ejs": "3.4.12", "@midwayjs/view-nunjucks": "3.4.12", "@midwayjs/view": "3.4.12", "@midwayjs/express": "3.4.12", "@midwayjs/koa": "3.4.12", "@midwayjs/web": "3.4.12", "@midwayjs/ws": "3.4.12"}