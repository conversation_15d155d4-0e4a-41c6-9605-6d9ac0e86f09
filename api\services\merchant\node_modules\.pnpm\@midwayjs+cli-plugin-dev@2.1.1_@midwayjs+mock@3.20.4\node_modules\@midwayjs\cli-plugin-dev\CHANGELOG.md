# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [2.1.1](https://github.com/midwayjs/cli/compare/serverless-v2.1.0...serverless-v2.1.1) (2023-05-31)


### Bug Fixes

* function bootstrap entry will display port tip ([#332](https://github.com/midwayjs/cli/issues/332)) ([b7cc693](https://github.com/midwayjs/cli/commit/b7cc693784865d7a158db5505db624359753f3b0))





# [2.1.0](https://github.com/midwayjs/cli/compare/serverless-v1.2.39...serverless-v2.1.0) (2023-05-30)


### Features

* support new v3 serverless entry ([#331](https://github.com/midwayjs/cli/issues/331)) ([756edfd](https://github.com/midwayjs/cli/commit/756edfd21d7afd4e5f740ea2b69dbfb42ebaeb4b))



## 2.0.15 (2023-03-20)


### Features

* incremental dev ([#327](https://github.com/midwayjs/cli/issues/327)) ([78d5e9b](https://github.com/midwayjs/cli/commit/78d5e9bef78d15f169e3bfe9f364af4b668d96ba))



## 2.0.14 (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))



## 2.0.10 (2023-01-09)



## 2.0.9 (2022-12-30)



## 2.0.8 (2022-12-27)


### Bug Fixes

* dev random port ([#311](https://github.com/midwayjs/cli/issues/311)) ([f61c9ef](https://github.com/midwayjs/cli/commit/f61c9efede8055e110dce7a458da1d6c5dd304f8))



## 2.0.7 (2022-12-16)



## 2.0.7-beta.6 (2022-12-16)



## 2.0.7-beta.5 (2022-12-14)


### Bug Fixes

* child listen sigint ([3015f5b](https://github.com/midwayjs/cli/commit/3015f5be1b0a60fd7b76389ff4f893f709c661a9))



## 2.0.7-beta.4 (2022-12-14)


### Bug Fixes

* fix child process listen SIGINT make process exit slow ([61d22d6](https://github.com/midwayjs/cli/commit/61d22d6fc23b1a2d3d7039fce4af1d732a41d914))



## 2.0.7-beta.3 (2022-12-14)


### Bug Fixes

* resume MIDWAY_DEV_IS_SERVERLESS ([0814b1e](https://github.com/midwayjs/cli/commit/0814b1ea318db6cbf8b3ecbf888bfd3ba454d89a))



## 2.0.7-beta.2 (2022-12-14)


### Bug Fixes

* more stable close process ([2d0f1e7](https://github.com/midwayjs/cli/commit/2d0f1e72d912ce13afd1fb38ceb8a962a01f113d))



## 2.0.7-beta.1 (2022-12-14)


### Bug Fixes

* dev close process ([4aaaac8](https://github.com/midwayjs/cli/commit/4aaaac89d93aac8df51a76d5bb99944a8be71151))


### Features

* cherry pick master ([3584763](https://github.com/midwayjs/cli/commit/3584763c91aaa0256f96a7ff7a310f70c059a478))



## 2.0.6 (2022-12-09)



## 2.0.6-beta.3 (2022-12-06)



## 2.0.6-beta.2 (2022-12-06)



## 2.0.6-beta.1 (2022-12-05)


### Bug Fixes

* v3 serverless app ([51b298c](https://github.com/midwayjs/cli/commit/51b298cd6df3dbc6764edaa92d3c0c0a8345a7ac))



## 2.0.5 (2022-12-01)



## 2.0.5-beta.1 (2022-12-01)


### Features

* merga master ([c585444](https://github.com/midwayjs/cli/commit/c585444fb827702e2e3fd320891f340e969070b6))
* support watching files by glob patten in dev mode ([#305](https://github.com/midwayjs/cli/issues/305)) ([e4b97b6](https://github.com/midwayjs/cli/commit/e4b97b69b762252c5414aaad1ae06131d699df1b))



## 2.0.4 (2022-11-23)


### Bug Fixes

* find ts-node ([87944ea](https://github.com/midwayjs/cli/commit/87944eaa7dbe077b10ed907775000a982d2220e3))



## 2.0.3 (2022-11-21)



## 2.0.2 (2022-11-17)



## 2.0.1 (2022-10-28)



## 2.0.1-beta.1 (2022-10-26)



# 2.0.0 (2022-10-13)



## 1.3.15 (2022-10-13)


### Bug Fixes

* hooks old ([#299](https://github.com/midwayjs/cli/issues/299)) ([5b13c36](https://github.com/midwayjs/cli/commit/5b13c36ace3e2bfbfa582c5915455c34ecbfb104))



# 2.0.0-beta.1 (2022-10-10)



## 1.3.14 (2022-10-10)



## 1.3.13 (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))



## 1.3.13-beta.3 (2022-09-07)


### Bug Fixes

* 重复处理信号可能导致的异常 ([#293](https://github.com/midwayjs/cli/issues/293)) ([e693374](https://github.com/midwayjs/cli/commit/e6933745784a6e68c1eefb7f06ace73c8ec9172c))



## 1.3.11 (2022-08-04)



## 1.3.7 (2022-07-06)


### Bug Fixes

* cannot trigger "onStop" in dev mode ([#285](https://github.com/midwayjs/cli/issues/285)) ([5a04e20](https://github.com/midwayjs/cli/commit/5a04e2030be29a9fe08a509d339f56606e1e1d46))
* common http daya type ([#286](https://github.com/midwayjs/cli/issues/286)) ([9afe43c](https://github.com/midwayjs/cli/commit/9afe43c09a1c73edf9d7112aed0ea5271d7ab3f7))



## 1.3.6 (2022-06-02)


### Bug Fixes

* aggregationBeforeExecScript ([#283](https://github.com/midwayjs/cli/issues/283)) ([83528f4](https://github.com/midwayjs/cli/commit/83528f4d3b13500879e4b1ad68efa31654fbcb63))



## 1.3.5 (2022-05-25)



## 1.3.5-beta.3 (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))



## 1.3.4 (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))



## 1.3.3 (2022-04-20)



## 1.3.1 (2022-03-10)



## 1.3.1-beta.1 (2022-03-10)


### Bug Fixes

* emitDecoratorMetadata ([#269](https://github.com/midwayjs/cli/issues/269)) ([b08e6c6](https://github.com/midwayjs/cli/commit/b08e6c6ec581e5dfb1231962384afc035db516d6))



## 1.2.99 (2022-02-16)


### Bug Fixes

* fast dev ([#263](https://github.com/midwayjs/cli/issues/263)) ([89c5009](https://github.com/midwayjs/cli/commit/89c5009e1e6356ae9a4c58b39564211882594274))



## 1.2.98 (2022-02-10)



## 1.2.97 (2022-01-24)


### Bug Fixes

* auto select npm ([#258](https://github.com/midwayjs/cli/issues/258)) ([f6e497c](https://github.com/midwayjs/cli/commit/f6e497c331af2f51855f98fa880bccc8acbbdb4e))



## 1.2.95 (2022-01-20)


### Bug Fixes

* serverless devs cnpm ([#253](https://github.com/midwayjs/cli/issues/253)) ([93641a8](https://github.com/midwayjs/cli/commit/93641a8d76066ffe0016f297814070974b0f728a))



## 1.2.94 (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))



## 1.2.93 (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))



## 1.2.92 (2021-12-03)



## 1.2.91 (2021-11-26)


### Features

* fast install nm ([#231](https://github.com/midwayjs/cli/issues/231)) ([9974ed5](https://github.com/midwayjs/cli/commit/9974ed5d26dd4eca9627a709c72c2b677c0b49c1))



## 1.2.90 (2021-11-16)


### Bug Fixes

* remvoe hooks core ([#228](https://github.com/midwayjs/cli/issues/228)) ([d0c3fcc](https://github.com/midwayjs/cli/commit/d0c3fccbab299b15284f69f7574e84aec5cb7c5e))



## 1.2.89 (2021-11-16)


### Bug Fixes

* package check error ignore tsconfig error ([#226](https://github.com/midwayjs/cli/issues/226)) ([5d5468c](https://github.com/midwayjs/cli/commit/5d5468c7ac4fb45c73037fcd2ccc45a3494301b1))



## 1.2.87 (2021-11-05)


### Bug Fixes

* watch more file ([#223](https://github.com/midwayjs/cli/issues/223)) ([24180a1](https://github.com/midwayjs/cli/commit/24180a12989473a54c0af18ecfbce469b92599a1))



## 1.2.86 (2021-11-04)



## 1.2.85 (2021-10-21)


### Features

* dev support entry file port check ([#204](https://github.com/midwayjs/cli/issues/204)) ([7b174e3](https://github.com/midwayjs/cli/commit/7b174e35f6b2d084ac72e8c9b9d2bbbfa6303a95))
* support command alias ([#218](https://github.com/midwayjs/cli/issues/218)) ([380338c](https://github.com/midwayjs/cli/commit/380338c1b0a92d85874cba16ff37a1711e137027))



## 1.2.84 (2021-09-27)



## 1.2.83 (2021-09-13)



## 1.2.82 (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))



## 1.2.79 (2021-08-11)



## 1.2.78 (2021-08-09)


### Features

* support wechat cloud function ([#153](https://github.com/midwayjs/cli/issues/153)) ([f64744c](https://github.com/midwayjs/cli/commit/f64744c537f4050611705691bbffc240d283ae3c))



## 1.2.76 (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))



## 1.2.73 (2021-06-25)



## 1.2.72 (2021-06-18)



## 1.2.71 (2021-06-07)


### Bug Fixes

* midwayjs/midway[#1084](https://github.com/midwayjs/cli/issues/1084) ([#122](https://github.com/midwayjs/cli/issues/122)) ([460ac4a](https://github.com/midwayjs/cli/commit/460ac4ab74f0fd843695edacb91fc1e397ed382f))



## 1.2.70 (2021-06-02)


### Bug Fixes

* some bugs ([#119](https://github.com/midwayjs/cli/issues/119)) ([9cc24d2](https://github.com/midwayjs/cli/commit/9cc24d28b50676efca987466962e8caf13ef2d14))



## 1.2.69 (2021-06-01)


### Bug Fixes

* tsconfig path ([#103](https://github.com/midwayjs/cli/issues/103)) ([45e03ca](https://github.com/midwayjs/cli/commit/45e03caec022b9c8e4fe51e0f88014ef7c30b3f6))



## 1.2.68 (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))



## 1.2.65 (2021-04-23)



## 1.2.63 (2021-04-16)



## 1.2.62 (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))



## 1.2.61 (2021-04-12)



## 1.2.59 (2021-04-07)



## 1.2.58 (2021-04-07)


### Bug Fixes

* dev layers ([#89](https://github.com/midwayjs/cli/issues/89)) ([a5528fd](https://github.com/midwayjs/cli/commit/a5528fdf1d706e44882fe8fbcb611ad7cb8a0b70))



## 1.2.57 (2021-04-02)


### Bug Fixes

* use serverless trigger collector ([#87](https://github.com/midwayjs/cli/issues/87)) ([f956152](https://github.com/midwayjs/cli/commit/f956152879cd603d3cfc5b7475b92fd012ed06d6))



## 1.2.54 (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))


### Performance Improvements

* remove esbuild ([#75](https://github.com/midwayjs/cli/issues/75)) ([c7cbb5b](https://github.com/midwayjs/cli/commit/c7cbb5b467bc9b06a798e2a4decca2bbbb0ca0f8))



## 1.2.51 (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))



## 1.2.50 (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))



## 1.2.48 (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))



## 1.2.46 (2021-03-05)



## 1.2.45 (2021-03-04)



## 1.2.42 (2021-02-24)


### Bug Fixes

* dev framework check ([#52](https://github.com/midwayjs/cli/issues/52)) ([2a9e1a3](https://github.com/midwayjs/cli/commit/2a9e1a328c334f62d2b62a037a4db1ab9291c7ab))



## 1.2.41 (2021-02-17)



## 1.2.40 (2021-02-03)



## 1.2.39-beta.5 (2021-02-03)


### Features

* support dev entry ([#46](https://github.com/midwayjs/cli/issues/46)) ([35d3608](https://github.com/midwayjs/cli/commit/35d360866760a524ab7b99224ccdf835b4cf8c6b))





## [2.0.15](https://github.com/midwayjs/cli/compare/v2.0.14...v2.0.15) (2023-03-20)


### Features

* incremental dev ([#327](https://github.com/midwayjs/cli/issues/327)) ([78d5e9b](https://github.com/midwayjs/cli/commit/78d5e9bef78d15f169e3bfe9f364af4b668d96ba))





## [2.0.15-beta.3](https://github.com/midwayjs/cli/compare/v2.0.15-beta.2...v2.0.15-beta.3) (2023-03-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.15-beta.2](https://github.com/midwayjs/cli/compare/v2.0.15-beta.1...v2.0.15-beta.2) (2023-03-19)


### Bug Fixes

* dev js register ([8c2f9fa](https://github.com/midwayjs/cli/commit/8c2f9fa9ce2f7d4d39280de41e6083eead9ef972))





## [2.0.15-beta.1](https://github.com/midwayjs/cli/compare/v2.0.14...v2.0.15-beta.1) (2023-03-19)


### Features

* incremental dev ([f8f5608](https://github.com/midwayjs/cli/commit/f8f560816040068826ba25bfa969eec1726a27a0))





## [2.0.14](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))





## [2.0.14-beta.4](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.4) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))





## [2.0.14-beta.3](https://github.com/midwayjs/cli/compare/v2.0.14-beta.2...v2.0.14-beta.3) (2023-02-15)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.14-beta.2](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.2) (2023-02-14)


### Bug Fixes

* prevent log ([2a60bc6](https://github.com/midwayjs/cli/commit/2a60bc6e9a5f75edff826e817565cb6822faba0e))





## [2.0.10](https://github.com/midwayjs/cli/compare/v2.0.9...v2.0.10) (2023-01-09)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.9](https://github.com/midwayjs/cli/compare/v2.0.8...v2.0.9) (2022-12-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.9-beta.2](https://github.com/midwayjs/cli/compare/v2.0.8...v2.0.9-beta.2) (2022-12-29)


### Features

* auto clear terminal ([e37911c](https://github.com/midwayjs/cli/commit/e37911cccfba4ace714051c9ee04e76592b0d490))





## [2.0.8](https://github.com/midwayjs/cli/compare/v2.0.7...v2.0.8) (2022-12-27)


### Bug Fixes

* dev random port ([#311](https://github.com/midwayjs/cli/issues/311)) ([f61c9ef](https://github.com/midwayjs/cli/commit/f61c9efede8055e110dce7a458da1d6c5dd304f8))





## [2.0.8-beta.1](https://github.com/midwayjs/cli/compare/v2.0.7...v2.0.8-beta.1) (2022-12-26)


### Bug Fixes

* dev random port ([7571f17](https://github.com/midwayjs/cli/commit/7571f17e47b7548a7b8c582ee6fcced62f03230d))





## [2.0.7](https://github.com/midwayjs/cli/compare/v2.0.7-beta.6...v2.0.7) (2022-12-16)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.7-beta.6](https://github.com/midwayjs/cli/compare/v2.0.7-beta.5...v2.0.7-beta.6) (2022-12-16)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.7-beta.5](https://github.com/midwayjs/cli/compare/v2.0.7-beta.4...v2.0.7-beta.5) (2022-12-14)


### Bug Fixes

* child listen sigint ([3015f5b](https://github.com/midwayjs/cli/commit/3015f5be1b0a60fd7b76389ff4f893f709c661a9))





## [2.0.7-beta.4](https://github.com/midwayjs/cli/compare/v2.0.7-beta.3...v2.0.7-beta.4) (2022-12-14)


### Bug Fixes

* fix child process listen SIGINT make process exit slow ([61d22d6](https://github.com/midwayjs/cli/commit/61d22d6fc23b1a2d3d7039fce4af1d732a41d914))





## [2.0.7-beta.3](https://github.com/midwayjs/cli/compare/v2.0.7-beta.2...v2.0.7-beta.3) (2022-12-14)


### Bug Fixes

* resume MIDWAY_DEV_IS_SERVERLESS ([0814b1e](https://github.com/midwayjs/cli/commit/0814b1ea318db6cbf8b3ecbf888bfd3ba454d89a))





## [2.0.7-beta.2](https://github.com/midwayjs/cli/compare/v2.0.7-beta.1...v2.0.7-beta.2) (2022-12-14)


### Bug Fixes

* more stable close process ([2d0f1e7](https://github.com/midwayjs/cli/commit/2d0f1e72d912ce13afd1fb38ceb8a962a01f113d))





## [2.0.7-beta.1](https://github.com/midwayjs/cli/compare/v2.0.6...v2.0.7-beta.1) (2022-12-14)


### Bug Fixes

* dev close process ([4aaaac8](https://github.com/midwayjs/cli/commit/4aaaac89d93aac8df51a76d5bb99944a8be71151))


### Features

* cherry pick master ([3584763](https://github.com/midwayjs/cli/commit/3584763c91aaa0256f96a7ff7a310f70c059a478))





## [2.0.6](https://github.com/midwayjs/cli/compare/v2.0.6-beta.3...v2.0.6) (2022-12-09)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.6-beta.3](https://github.com/midwayjs/cli/compare/v2.0.6-beta.2...v2.0.6-beta.3) (2022-12-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.6-beta.2](https://github.com/midwayjs/cli/compare/v2.0.6-beta.1...v2.0.6-beta.2) (2022-12-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.6-beta.1](https://github.com/midwayjs/cli/compare/v2.0.5...v2.0.6-beta.1) (2022-12-05)


### Bug Fixes

* v3 serverless app ([51b298c](https://github.com/midwayjs/cli/commit/51b298cd6df3dbc6764edaa92d3c0c0a8345a7ac))





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.5-beta.1](https://github.com/midwayjs/cli/compare/v2.0.4...v2.0.5-beta.1) (2022-12-01)


### Features

* merga master ([c585444](https://github.com/midwayjs/cli/commit/c585444fb827702e2e3fd320891f340e969070b6))
* support watching files by glob patten in dev mode ([#305](https://github.com/midwayjs/cli/issues/305)) ([e4b97b6](https://github.com/midwayjs/cli/commit/e4b97b69b762252c5414aaad1ae06131d699df1b))





## [2.0.4](https://github.com/midwayjs/cli/compare/v2.0.3...v2.0.4) (2022-11-23)


### Bug Fixes

* find ts-node ([87944ea](https://github.com/midwayjs/cli/commit/87944eaa7dbe077b10ed907775000a982d2220e3))





## [2.0.3](https://github.com/midwayjs/cli/compare/v2.0.2...v2.0.3) (2022-11-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.2](https://github.com/midwayjs/cli/compare/v2.0.1...v2.0.2) (2022-11-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.1](https://github.com/midwayjs/cli/compare/v2.0.1-beta.1...v2.0.1) (2022-10-28)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [2.0.1-beta.1](https://github.com/midwayjs/cli/compare/v2.0.0...v2.0.1-beta.1) (2022-10-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





# [2.0.0](https://github.com/midwayjs/cli/compare/v1.3.15...v2.0.0) (2022-10-13)



# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





# [2.0.0](https://github.com/midwayjs/cli/compare/v2.0.0-beta.1...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14-beta.10](https://github.com/midwayjs/cli/compare/v1.3.14-beta.9...v1.3.14-beta.10) (2022-09-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14-beta.9](https://github.com/midwayjs/cli/compare/v1.3.14-beta.8...v1.3.14-beta.9) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14-beta.8](https://github.com/midwayjs/cli/compare/v1.3.14-beta.7...v1.3.14-beta.8) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14-beta.7](https://github.com/midwayjs/cli/compare/v1.3.14-beta.6...v1.3.14-beta.7) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.14-beta.6](https://github.com/midwayjs/cli/compare/v1.3.14-beta.5...v1.3.14-beta.6) (2022-09-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13-beta.4](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13-beta.4) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.13-beta.3](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.13-beta.3) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13) (2022-09-07)


### Bug Fixes

* 重复处理信号可能导致的异常 ([#293](https://github.com/midwayjs/cli/issues/293)) ([e693374](https://github.com/midwayjs/cli/commit/e6933745784a6e68c1eefb7f06ace73c8ec9172c))





## [1.3.13-beta.2](https://github.com/midwayjs/cli/compare/v1.3.13-beta.1...v1.3.13-beta.2) (2022-09-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.13-beta.1](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13-beta.1) (2022-08-29)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.12](https://github.com/midwayjs/cli/compare/v1.3.12-beta.3...v1.3.12) (2022-08-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.12-beta.3](https://github.com/midwayjs/cli/compare/v1.3.12-beta.2...v1.3.12-beta.3) (2022-08-22)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.12-beta.2](https://github.com/midwayjs/cli/compare/v1.3.12-beta.1...v1.3.12-beta.2) (2022-08-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.12-beta.1](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.12-beta.1) (2022-08-05)


### Bug Fixes

* dev handle error ([0bc3020](https://github.com/midwayjs/cli/commit/0bc3020f44e54a3a4966d33bf5c1851a404d6d08))





## [1.3.11](https://github.com/midwayjs/cli/compare/v1.3.9...v1.3.11) (2022-08-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.11-beta.1](https://github.com/midwayjs/cli/compare/v1.3.9...v1.3.11-beta.1) (2022-08-04)


### Bug Fixes

* dev alive ([8f06d66](https://github.com/midwayjs/cli/commit/8f06d6697ad5bb62730f538227056ad900f8b124))





## [1.3.7](https://github.com/midwayjs/cli/compare/v1.3.6...v1.3.7) (2022-07-06)


### Bug Fixes

* cannot trigger "onStop" in dev mode ([#285](https://github.com/midwayjs/cli/issues/285)) ([5a04e20](https://github.com/midwayjs/cli/commit/5a04e2030be29a9fe08a509d339f56606e1e1d46))
* common http daya type ([#286](https://github.com/midwayjs/cli/issues/286)) ([9afe43c](https://github.com/midwayjs/cli/commit/9afe43c09a1c73edf9d7112aed0ea5271d7ab3f7))





## [1.3.7-beta.1](https://github.com/midwayjs/cli/compare/v1.3.6...v1.3.7-beta.1) (2022-07-06)


### Bug Fixes

* cannot trigger "onStop" in dev mode ([#285](https://github.com/midwayjs/cli/issues/285)) ([5a04e20](https://github.com/midwayjs/cli/commit/5a04e2030be29a9fe08a509d339f56606e1e1d46))





## [1.3.6](https://github.com/midwayjs/cli/compare/v1.3.5...v1.3.6) (2022-06-02)


### Bug Fixes

* aggregationBeforeExecScript ([#283](https://github.com/midwayjs/cli/issues/283)) ([83528f4](https://github.com/midwayjs/cli/commit/83528f4d3b13500879e4b1ad68efa31654fbcb63))





## [1.3.6-beta.1](https://github.com/midwayjs/cli/compare/v1.3.5...v1.3.6-beta.1) (2022-06-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.5](https://github.com/midwayjs/cli/compare/v1.3.5-beta.3...v1.3.5) (2022-05-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.5-beta.3](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.3) (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))





## [1.3.5-beta.2](https://github.com/midwayjs/cli/compare/v1.3.5-beta.1...v1.3.5-beta.2) (2022-05-24)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.4](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4) (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))





## [1.3.4-beta.1](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4-beta.1) (2022-04-21)


### Bug Fixes

* cli time tick ([ca21feb](https://github.com/midwayjs/cli/commit/ca21feb39bb64e7f3af274a3ba6ae36143f113ab))





## [1.3.3](https://github.com/midwayjs/cli/compare/v1.3.2...v1.3.3) (2022-04-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.1](https://github.com/midwayjs/cli/compare/v1.3.1-beta.1...v1.3.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.3.1-beta.1](https://github.com/midwayjs/cli/compare/v1.3.0...v1.3.1-beta.1) (2022-03-10)


### Bug Fixes

* emitDecoratorMetadata ([#269](https://github.com/midwayjs/cli/issues/269)) ([b08e6c6](https://github.com/midwayjs/cli/commit/b08e6c6ec581e5dfb1231962384afc035db516d6))





## [1.2.99](https://github.com/midwayjs/cli/compare/v1.2.98...v1.2.99) (2022-02-16)


### Bug Fixes

* fast dev ([#263](https://github.com/midwayjs/cli/issues/263)) ([89c5009](https://github.com/midwayjs/cli/commit/89c5009e1e6356ae9a4c58b39564211882594274))





## [1.2.99-beta.2](https://github.com/midwayjs/cli/compare/v1.2.99-beta.1...v1.2.99-beta.2) (2022-02-16)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.99-beta.1](https://github.com/midwayjs/cli/compare/v1.2.98...v1.2.99-beta.1) (2022-02-15)


### Bug Fixes

* fast dev ([60407ac](https://github.com/midwayjs/cli/commit/60407acca89d4495173c64458687e5a6b03a8411))
* remove esbuild ([6bdfc7c](https://github.com/midwayjs/cli/commit/6bdfc7c3abdf94e1f5c3cb2f0f3fbc600be0a00f))


### Features

* swc ([3b0ab58](https://github.com/midwayjs/cli/commit/3b0ab580d8d030a3969495ecb650346710657af7))





## [1.2.98](https://github.com/midwayjs/cli/compare/v1.2.97...v1.2.98) (2022-02-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.98-beta.2](https://github.com/midwayjs/cli/compare/v1.2.98-beta.1...v1.2.98-beta.2) (2022-02-10)


### Bug Fixes

* esbuild version ([b193038](https://github.com/midwayjs/cli/commit/b19303870544d563b0a7a652320db55a6b951d03))





## [1.2.97](https://github.com/midwayjs/cli/compare/v1.2.96...v1.2.97) (2022-01-24)


### Bug Fixes

* auto select npm ([#258](https://github.com/midwayjs/cli/issues/258)) ([f6e497c](https://github.com/midwayjs/cli/commit/f6e497c331af2f51855f98fa880bccc8acbbdb4e))





## [1.2.95](https://github.com/midwayjs/cli/compare/v1.2.94...v1.2.95) (2022-01-20)


### Bug Fixes

* serverless devs cnpm ([#253](https://github.com/midwayjs/cli/issues/253)) ([93641a8](https://github.com/midwayjs/cli/commit/93641a8d76066ffe0016f297814070974b0f728a))





## [1.2.94](https://github.com/midwayjs/cli/compare/v1.2.93...v1.2.94) (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))





## [1.2.93](https://github.com/midwayjs/cli/compare/v1.2.92...v1.2.93) (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))





## [1.2.92](https://github.com/midwayjs/cli/compare/v1.2.91...v1.2.92) (2021-12-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.91](https://github.com/midwayjs/cli/compare/v1.2.90...v1.2.91) (2021-11-26)


### Features

* fast install nm ([#231](https://github.com/midwayjs/cli/issues/231)) ([9974ed5](https://github.com/midwayjs/cli/commit/9974ed5d26dd4eca9627a709c72c2b677c0b49c1))





## [1.2.90](https://github.com/midwayjs/cli/compare/v1.2.89...v1.2.90) (2021-11-16)


### Bug Fixes

* remvoe hooks core ([#228](https://github.com/midwayjs/cli/issues/228)) ([d0c3fcc](https://github.com/midwayjs/cli/commit/d0c3fccbab299b15284f69f7574e84aec5cb7c5e))





## [1.2.89](https://github.com/midwayjs/cli/compare/v1.2.87...v1.2.89) (2021-11-16)


### Bug Fixes

* package check error ignore tsconfig error ([#226](https://github.com/midwayjs/cli/issues/226)) ([5d5468c](https://github.com/midwayjs/cli/commit/5d5468c7ac4fb45c73037fcd2ccc45a3494301b1))





## [1.2.87](https://github.com/midwayjs/cli/compare/v1.2.86...v1.2.87) (2021-11-05)


### Bug Fixes

* watch more file ([#223](https://github.com/midwayjs/cli/issues/223)) ([24180a1](https://github.com/midwayjs/cli/commit/24180a12989473a54c0af18ecfbce469b92599a1))





## [1.2.86](https://github.com/midwayjs/cli/compare/v1.2.85...v1.2.86) (2021-11-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.85](https://github.com/midwayjs/cli/compare/v1.2.84...v1.2.85) (2021-10-21)


### Features

* dev support entry file port check ([#204](https://github.com/midwayjs/cli/issues/204)) ([7b174e3](https://github.com/midwayjs/cli/commit/7b174e35f6b2d084ac72e8c9b9d2bbbfa6303a95))
* support command alias ([#218](https://github.com/midwayjs/cli/issues/218)) ([380338c](https://github.com/midwayjs/cli/commit/380338c1b0a92d85874cba16ff37a1711e137027))





## [1.2.84](https://github.com/midwayjs/cli/compare/v1.2.83...v1.2.84) (2021-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.83](https://github.com/midwayjs/cli/compare/v1.2.82...v1.2.83) (2021-09-13)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.82](https://github.com/midwayjs/cli/compare/v1.2.81...v1.2.82) (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))





## [1.2.79](https://github.com/midwayjs/cli/compare/v1.2.78...v1.2.79) (2021-08-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.78](https://github.com/midwayjs/cli/compare/v1.2.77...v1.2.78) (2021-08-09)


### Features

* support wechat cloud function ([#153](https://github.com/midwayjs/cli/issues/153)) ([f64744c](https://github.com/midwayjs/cli/commit/f64744c537f4050611705691bbffc240d283ae3c))





## [1.2.76](https://github.com/midwayjs/cli/compare/v1.2.75...v1.2.76) (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))





## [1.2.73](https://github.com/midwayjs/cli/compare/v1.2.73-beta.1...v1.2.73) (2021-06-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.72](https://github.com/midwayjs/cli/compare/v1.2.71...v1.2.72) (2021-06-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.71](https://github.com/midwayjs/cli/compare/v1.2.70...v1.2.71) (2021-06-07)


### Bug Fixes

* midwayjs/midway[#1084](https://github.com/midwayjs/cli/issues/1084) ([#122](https://github.com/midwayjs/cli/issues/122)) ([460ac4a](https://github.com/midwayjs/cli/commit/460ac4ab74f0fd843695edacb91fc1e397ed382f))





## [1.2.70](https://github.com/midwayjs/cli/compare/v1.2.69...v1.2.70) (2021-06-02)


### Bug Fixes

* some bugs ([#119](https://github.com/midwayjs/cli/issues/119)) ([9cc24d2](https://github.com/midwayjs/cli/commit/9cc24d28b50676efca987466962e8caf13ef2d14))





## [1.2.69](https://github.com/midwayjs/cli/compare/v1.2.68...v1.2.69) (2021-06-01)


### Bug Fixes

* tsconfig path ([#103](https://github.com/midwayjs/cli/issues/103)) ([45e03ca](https://github.com/midwayjs/cli/commit/45e03caec022b9c8e4fe51e0f88014ef7c30b3f6))





## [1.2.68](https://github.com/midwayjs/cli/compare/v1.2.67...v1.2.68) (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))





## [1.2.65](https://github.com/midwayjs/cli/compare/v1.2.63...v1.2.65) (2021-04-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.63](https://github.com/midwayjs/cli/compare/v1.2.62...v1.2.63) (2021-04-16)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.62](https://github.com/midwayjs/cli/compare/v1.2.61...v1.2.62) (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))





## [1.2.61](https://github.com/midwayjs/cli/compare/v1.2.60...v1.2.61) (2021-04-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.59](https://github.com/midwayjs/cli/compare/v1.2.58...v1.2.59) (2021-04-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.58](https://github.com/midwayjs/cli/compare/v1.2.57...v1.2.58) (2021-04-07)


### Bug Fixes

* dev layers ([#89](https://github.com/midwayjs/cli/issues/89)) ([a5528fd](https://github.com/midwayjs/cli/commit/a5528fdf1d706e44882fe8fbcb611ad7cb8a0b70))





## [1.2.57](https://github.com/midwayjs/cli/compare/v1.2.56...v1.2.57) (2021-04-02)


### Bug Fixes

* use serverless trigger collector ([#87](https://github.com/midwayjs/cli/issues/87)) ([f956152](https://github.com/midwayjs/cli/commit/f956152879cd603d3cfc5b7475b92fd012ed06d6))





## [1.2.54](https://github.com/midwayjs/cli/compare/v1.2.53...v1.2.54) (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))


### Performance Improvements

* remove esbuild ([#75](https://github.com/midwayjs/cli/issues/75)) ([c7cbb5b](https://github.com/midwayjs/cli/commit/c7cbb5b467bc9b06a798e2a4decca2bbbb0ca0f8))





## [1.2.51](https://github.com/midwayjs/cli/compare/v1.2.50...v1.2.51) (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))





## [1.2.50](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.50) (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.49](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.49) (2021-03-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.48](https://github.com/midwayjs/cli/compare/v1.2.46...v1.2.48) (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))





## [1.2.46](https://github.com/midwayjs/cli/compare/v1.2.45...v1.2.46) (2021-03-05)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.45](https://github.com/midwayjs/cli/compare/v1.2.44...v1.2.45) (2021-03-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.42](https://github.com/midwayjs/cli/compare/v1.2.41...v1.2.42) (2021-02-24)


### Bug Fixes

* dev framework check ([#52](https://github.com/midwayjs/cli/issues/52)) ([2a9e1a3](https://github.com/midwayjs/cli/commit/2a9e1a328c334f62d2b62a037a4db1ab9291c7ab))





## [1.2.41](https://github.com/midwayjs/cli/compare/v1.2.40...v1.2.41) (2021-02-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.40](https://github.com/midwayjs/cli/compare/v1.2.39-beta.5...v1.2.40) (2021-02-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.37](https://github.com/midwayjs/cli/compare/v1.2.35...v1.2.37) (2021-01-08)


### Bug Fixes

* support package diagnostics & tsConfig config ([#38](https://github.com/midwayjs/cli/issues/38)) ([c499d14](https://github.com/midwayjs/cli/commit/c499d145f9cabf427877ec8ea65aea8ead42b9cd))





## [1.2.35](https://github.com/midwayjs/cli/compare/v1.2.33...v1.2.35) (2020-12-24)


### Bug Fixes

* copy file error catch ([8d2097c](https://github.com/midwayjs/cli/commit/8d2097c538f22ed6050c85d1c250436e0c2c71c1))





## [1.2.34](https://github.com/midwayjs/cli/compare/v1.2.34-beta.2...v1.2.34) (2020-12-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.32](https://github.com/midwayjs/cli/compare/v1.2.32-beta...v1.2.32) (2020-12-08)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.31](https://github.com/midwayjs/cli/compare/v1.2.30...v1.2.31) (2020-12-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.30](https://github.com/midwayjs/cli/compare/v1.2.30-beta...v1.2.30) (2020-11-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.29](https://github.com/midwayjs/cli/compare/serverless-v1.2.28...serverless-v1.2.29) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.28](https://github.com/midwayjs/cli/compare/serverless-v1.2.27...serverless-v1.2.28) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.27](https://github.com/midwayjs/cli/compare/serverless-v1.2.26...serverless-v1.2.27) (2020-11-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.26](https://github.com/midwayjs/cli/compare/serverless-v1.2.21...serverless-v1.2.26) (2020-11-17)



## 1.2.25 (2020-11-12)



## 1.2.25-beta.1 (2020-11-12)



## 1.2.23 (2020-11-11)



## 1.2.23-beta.3 (2020-11-10)



## 1.2.23-beta.2 (2020-10-30)



## 1.2.23-beta.1 (2020-10-26)


### Bug Fixes

* dev restart ([#16](https://github.com/midwayjs/cli/issues/16)) ([fd6aa75](https://github.com/midwayjs/cli/commit/fd6aa7584e291cbd9b0d7b99f17d32783901ff33))
* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.25](https://github.com/midwayjs/cli/compare/v1.2.25-beta.1...v1.2.25) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.25-beta.1](https://github.com/midwayjs/cli/compare/v1.2.24-beta.1...v1.2.25-beta.1) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.23](https://github.com/midwayjs/cli/compare/v1.2.23-beta.3...v1.2.23) (2020-11-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.23-beta.3](https://github.com/midwayjs/cli/compare/v1.2.23-beta.2...v1.2.23-beta.3) (2020-11-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.23-beta.2](https://github.com/midwayjs/cli/compare/v1.2.23-beta.1...v1.2.23-beta.2) (2020-10-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.23-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.23-beta.1) (2020-10-26)


### Bug Fixes

* dev restart ([#16](https://github.com/midwayjs/cli/issues/16)) ([fd6aa75](https://github.com/midwayjs/cli/commit/fd6aa7584e291cbd9b0d7b99f17d32783901ff33))
* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.22](https://github.com/midwayjs/cli/compare/v1.2.22-beta.1...v1.2.22) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.22-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.22-beta.1) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.21](https://github.com/midwayjs/cli/compare/serverless-v1.2.19...serverless-v1.2.21) (2020-10-20)



## 1.2.20 (2020-10-19)



## 1.2.20-beta.5 (2020-10-19)



## 1.2.20-beta.4 (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20](https://github.com/midwayjs/cli/compare/v1.2.20-beta.5...v1.2.20) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.20-beta.5](https://github.com/midwayjs/cli/compare/v1.2.20-beta.4...v1.2.20-beta.5) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.20-beta.4](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.4) (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20-beta.3](https://github.com/midwayjs/cli/compare/v1.2.20-beta.2...v1.2.20-beta.3) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.20-beta.2](https://github.com/midwayjs/cli/compare/v1.2.20-beta.1...v1.2.20-beta.2) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.20-beta.1](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.1) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.18](https://github.com/midwayjs/cli/compare/serverless-v1.2.17...serverless-v1.2.18) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.17](https://github.com/midwayjs/cli/compare/serverless-v1.2.16...serverless-v1.2.17) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.2.16](https://github.com/midwayjs/cli/compare/serverless-v1.2.15...serverless-v1.2.16) (2020-09-22)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## 1.2.15 (2020-09-22)



## 1.0.4 (2020-09-20)



## 1.0.3 (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))



## 1.0.1 (2020-09-17)


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))



# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





## [1.0.4](https://github.com/midwayjs/cli/compare/v1.0.3...v1.0.4) (2020-09-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))







**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))





## [1.0.1](https://github.com/midwayjs/cli/compare/v1.0.0...v1.0.1) (2020-09-17)


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))





# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





# [1.0.0](https://github.com/midwayjs/cli/compare/v1.1.0...v1.0.0) (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))







**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev







**Note:** Version bump only for package @midwayjs/cli-plugin-dev





# [1.0.0](https://github.com/midwayjs/bin/compare/v1.1.0...v1.0.0) (2020-09-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-dev





# 1.1.0 (2020-09-16)


### Features

* dev ([7e2dd87](https://github.com/midwayjs/bin/commit/7e2dd8773c2bd79de93a4aea7a41c0c74663b6bc))
