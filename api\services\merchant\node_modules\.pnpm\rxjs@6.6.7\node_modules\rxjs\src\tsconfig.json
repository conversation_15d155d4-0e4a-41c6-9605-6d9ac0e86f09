{"compilerOptions": {"removeComments": true, "preserveConstEnums": true, "sourceMap": true, "strictFunctionTypes": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "suppressImplicitAnyIndexErrors": true, "moduleResolution": "node", "stripInternal": false, "target": "es5", "outDir": "./.out", "lib": ["es5", "es2015.iterable", "es2015.collection", "es2015.promise", "es2015.symbol", "es2015.symbol.wellknown", "dom"]}, "formatCodeOptions": {"indentSize": 2, "tabSize": 2}, "bazelOptions": {"suppressTsconfigOverrideWarnings": true}}