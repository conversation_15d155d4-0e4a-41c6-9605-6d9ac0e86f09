"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WSController = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function WSController(namespace = '/', routerOptions = {
    middleware: [],
    connectionMiddleware: [],
}) {
    return (target) => {
        (0, __1.saveModule)(__1.WS_CONTROLLER_KEY, target);
        (0, __1.saveClassMetadata)(__1.WS_CONTROLLER_KEY, {
            namespace,
            routerOptions,
        }, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.WSController = WSController;
//# sourceMappingURL=webSocketController.js.map