#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules/mkdirp/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mkdirp@0.5.6/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../mkdirp@0.5.6/node_modules/mkdirp/bin/cmd.js" "$@"
else
  exec node  "$basedir/../../../../../../mkdirp@0.5.6/node_modules/mkdirp/bin/cmd.js" "$@"
fi
