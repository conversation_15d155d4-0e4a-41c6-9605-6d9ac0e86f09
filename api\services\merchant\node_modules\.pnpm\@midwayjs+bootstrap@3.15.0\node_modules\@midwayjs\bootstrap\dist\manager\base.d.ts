import { ForkOptions } from '../interface';
import type { IEventBus, EventBusOptions } from '@midwayjs/event-bus';
export declare abstract class AbstractForkManager<Worker, ClusterOptions extends ForkOptions> {
    readonly options: ClusterOptions;
    private reforks;
    private disconnectCount;
    private unexpectedCount;
    private disconnects;
    private hub;
    protected workers: Map<string, Worker>;
    protected eventBus: IEventBus<Worker>;
    private isClosing;
    private exitListener;
    protected constructor(options: ClusterOptions);
    start(): Promise<void>;
    protected tryToRefork(oldWorker: Worker): void;
    /**
     * allow refork
     */
    protected allowRefork(): boolean;
    /**
     * uncaughtException default handler
     */
    protected onerror(err: any): void;
    /**
     * unexpectedExit default handler
     */
    protected onUnexpected(worker: Worker, code: any, signal: any): void;
    /**
     * reachReforkLimit default handler
     */
    protected onReachReforkLimit(): void;
    protected killWorker(worker: any, timeout: any): Promise<void>;
    stop(timeout?: number): Promise<void>;
    hasWorker(workerId: string): boolean;
    getWorker(workerId: string): Worker;
    getWorkerIds(): string[];
    onStop(exitListener: any): void;
    protected bindClose(): void;
    /**
     * on bootstrap receive a exit signal
     * @param signal
     */
    private onSignal;
    /**
     * on bootstrap process exit
     * @param code
     */
    private onMasterExit;
    abstract createWorker(oldWorker?: Worker): Worker;
    abstract bindWorkerDisconnect(listener: (worker: Worker) => void): void;
    abstract bindWorkerExit(listener: (worker: Worker, code: any, signal: any) => void): void;
    abstract getWorkerId(worker: Worker): string;
    abstract isWorkerDead(worker: Worker): boolean;
    abstract closeWorker(worker: Worker): any;
    abstract createEventBus(eventBusOptions: EventBusOptions<Worker>): IEventBus<Worker>;
    abstract isPrimary(): boolean;
}
//# sourceMappingURL=base.d.ts.map