type AnsiStyles ='reset'|'inverse'|'hidden'|'visible'|'bold'|'dim'|'italic'|'underline'|'strikethrough'|'strike' ;type BC='black'|'red'|'green'|'yellow'|'blue'|'magenta'|'cyan'|'white';type BBC=`${BC}Bright`;type AnsiColors =BC|'gray'|'grey'|BBC|`bg${Capitalize<BC>}`|`bg${Capitalize<BBC>}`|'bgGray'|'bgGrey';type AC=AnsiColors;type AnsiColorsExtend<T extends string>=AC|(T & Record<never,never>);type DP={[K in AnsiStyles|AC]:A;};interface SP{(value:unknown):string;(strings:TemplateStringsArray,...values:any[]):string;ansi256(n:number):A;fg(n:number):A;bgAnsi256(n:number):A;bg(n:number):A;rgb(r:number,g:number,b:number):A;bgRgb(r:number,g:number,b:number):A;hex(hex:string):A;bgHex(hex:string):A;strip(s:string):string;extend<U extends string>(colors:Record<U,string|{open:string;close:string}>):asserts this is InstanceType<typeof Ansis> & Record<U,A>;isSupported():boolean;open:string;close:string;}type Ansis=SP & DP;type A=Ansis;declare const Ansis:new ()=>A,isSupported:()=>boolean,strip:SP["strip"],extend:SP["extend"],fg:SP["fg"],rgb:SP["rgb"],hex:SP["hex"],a:A;export{type AnsiColors,type AnsiStyles,type AnsiColorsExtend,a as default,Ansis,isSupported,strip,extend,fg,fg as bg,fg as ansi256,fg as bgAnsi256,rgb,rgb as bgRgb,hex,hex as bgHex,a as reset,a as inverse,a as hidden,a as visible,a as bold,a as dim,a as italic,a as underline,a as strikethrough,a as strike,a as black,a as red,a as green,a as yellow,a as blue,a as magenta,a as cyan,a as white,a as gray,a as grey,a as blackBright,a as redBright,a as greenBright,a as yellowBright,a as blueBright,a as magentaBright,a as cyanBright,a as whiteBright,a as bgBlack,a as bgGray,a as bgGrey,a as bgRed,a as bgGreen,a as bgYellow,a as bgBlue,a as bgMagenta,a as bgCyan,a as bgWhite,a as bgBlackBright,a as bgRedBright,a as bgGreenBright,a as bgYellowBright,a as bgBlueBright,a as bgMagentaBright,a as bgCyanBright,a as bgWhiteBright,};