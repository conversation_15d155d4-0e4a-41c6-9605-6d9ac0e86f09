"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpcEventHandler = exports.COOL_RPC_EVENT_HANDLER_KEY = void 0;
const decorator_1 = require("@midwayjs/decorator");
exports.COOL_RPC_EVENT_HANDLER_KEY = 'decorator:cool:rpc:event:handler';
/**
 * 事件
 * @param eventName 事件名称
 * @returns
 */
function CoolRpcEventHandler(eventName) {
    return (target, propertyKey, descriptor) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, decorator_1.attachClassMetadata)(exports.COOL_RPC_EVENT_HANDLER_KEY, {
            propertyKey,
            descriptor,
            eventName,
        }, target);
    };
}
exports.CoolRpcEventHandler = CoolRpcEventHandler;
