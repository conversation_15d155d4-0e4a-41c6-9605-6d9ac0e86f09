{"version": 3, "sources": ["../browser/src/decorator/options/JoinColumnOptions.ts"], "names": [], "mappings": "", "file": "JoinColumnOptions.js", "sourcesContent": ["/**\n * Describes join column options.\n */\nexport interface JoinColumnOptions {\n    /**\n     * Name of the column.\n     */\n    name?: string\n\n    /**\n     * Name of the column in the entity to which this column is referenced.\n     */\n    referencedColumnName?: string // TODO rename to referencedColumn\n\n    /**\n     * Name of the foreign key constraint.\n     */\n    foreignKeyConstraintName?: string\n}\n"], "sourceRoot": "../.."}