/// <reference types="node" />
import { Transform } from 'stream';
import { IMidwayContext, ServerStreamOptions } from '../interface';
export declare class HttpStreamResponse<CTX extends IMidwayContext> extends Transform {
    private ctx;
    private isActive;
    private options;
    constructor(ctx: any, options?: ServerStreamOptions<CTX>);
    _transform(chunk: any, encoding: any, callback: any): void;
    send(data: unknown): void;
    sendError(error: any): void;
    _flush(callback: any): void;
}
//# sourceMappingURL=stream.d.ts.map