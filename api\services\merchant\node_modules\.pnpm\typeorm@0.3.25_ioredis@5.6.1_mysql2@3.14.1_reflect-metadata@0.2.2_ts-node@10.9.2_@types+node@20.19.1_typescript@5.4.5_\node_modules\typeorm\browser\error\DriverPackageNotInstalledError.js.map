{"version": 3, "sources": ["../browser/src/error/DriverPackageNotInstalledError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,8BAA+B,SAAQ,YAAY;IAC5D,YAAY,UAAkB,EAAE,WAAmB;QAC/C,KAAK,CACD,GAAG,UAAU,yCAAyC;YAClD,kCAAkC,WAAW,SAAS,CAC7D,CAAA;IACL,CAAC;CACJ", "file": "DriverPackageNotInstalledError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when required driver's package is not installed.\n */\nexport class DriverPackageNotInstalledError extends TypeORMError {\n    constructor(driverName: string, packageName: string) {\n        super(\n            `${driverName} package has not been found installed. ` +\n                `Try to install it: npm install ${packageName} --save`,\n        )\n    }\n}\n"], "sourceRoot": ".."}