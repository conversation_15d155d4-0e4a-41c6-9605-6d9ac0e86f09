import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9804, // message 微服务端口
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'your_password',
        database: 'message_service_db', // 独立数据库！
        synchronize: true,
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ['src/modules/message/entity/**/*.ts'],
      },
    },
  },
  cool: {
    rpc: {
      name: "message-service", // 消息微服务名称
    },
    redis: {
      host: '127.0.0.1',
      password: '',
      port: 6379,
      db: 13, // 独立的Redis数据库
    },
    eps: true,
    initDB: true,
    initMenu: true,
  } as CoolConfig,
  moleculer: {
    namespace: 'cool',
    nodeID: 'message-service',
    transporter: 'NATS',
  },
} as MidwayConfig; 