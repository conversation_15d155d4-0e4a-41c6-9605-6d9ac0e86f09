{"version": 3, "sources": ["../browser/src/find-options/operator/In.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,EAAE,CACd,KAAqC;IAErC,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,KAAY,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC3D,CAAC", "file": "In.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: In([...]) }\n */\nexport function In<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"in\", value as any, true, true)\n}\n"], "sourceRoot": "../.."}