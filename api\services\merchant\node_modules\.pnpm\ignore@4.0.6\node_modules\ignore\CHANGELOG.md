# `node-ignore` 4 ChangeLog

# 4.x

## 2018-06-22, Version 4.0.0

- **SEMVER-MAJOR**: Drop support for node < 6 by default.
- **FEATURE**: supports the missing character ranges and sets, such as `*.[a-z]` and `*.[jJ][pP][gG]`
- **FEATURE**: new option: `ignorecase` to make `ignore` case sensitive.
- **FEATURE**: supports question mark which matches a single character.
- **PATCH**: fixes typescript declaration.

## ~ 2018-08-09, Version 4.0.1 - 4.0.5

- **PATCH**: updates README.md about frequent asked quesions from github issues.

## 2018-08-12, Version 4.0.6

- **PATCH**: `Object.prototype` methods will not ruin the result any more.
