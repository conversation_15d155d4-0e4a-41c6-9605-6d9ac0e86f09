{"version": 3, "sources": ["../browser/src/platform/BrowserConnectionOptionsReaderDummy.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AACH,MAAM,OAAO,0BAA0B;IACnC,KAAK,CAAC,IAAI;QACN,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED;;;;;GAKG;AACH,MAAM,OAAO,0BAA0B;IACnC,KAAK,CAAC,IAAI,CAAC,IAAY;QACnB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED;;;;;GAKG;AACH,MAAM,OAAO,0BAA0B;IACnC,KAAK,CAAC,IAAI,CAAC,IAAY;QACnB,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;CACJ;AAED;;;;;GAKG;AACH,MAAM,OAAO,uBAAuB;IAChC,KAAK,CAAC,GAAG;QACL,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,GAAG;QACL,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;IAED,KAAK,CAAC,GAAG;QACL,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAC;IAC5E,CAAC;CACJ", "file": "BrowserConnectionOptionsReaderDummy.js", "sourcesContent": ["/**\n * Dummy class for replacement via `package.json` in browser builds.\n *\n * If we don't include these functions typeorm will throw an error on runtime\n * as well as during webpack builds.\n */\nexport class ConnectionOptionsEnvReader {\n    async read() {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n}\n\n/**\n * Dummy class for replacement via `package.json` in browser builds.\n *\n * If we don't include these functions typeorm will throw an error on runtime\n * as well as during webpack builds.\n */\nexport class ConnectionOptionsXmlReader {\n    async read(path: string) {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n}\n\n/**\n * Dummy class for replacement via `package.json` in browser builds.\n *\n * If we don't include these functions typeorm will throw an error on runtime\n * as well as during webpack builds.\n */\nexport class ConnectionOptionsYmlReader {\n    async read(path: string) {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n}\n\n/**\n * Dummy class for replacement via `package.json` in browser builds.\n *\n * If we don't include these functions typeorm will throw an error on runtime\n * as well as during webpack builds.\n */\nexport class ConnectionOptionsReader {\n    async all() {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n\n    async get() {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n\n    async has() {\n        throw new Error(`Cannot read connection options in a browser context.`);\n    }\n}\n"], "sourceRoot": ".."}