{"version": 3, "sources": ["../browser/src/find-options/operator/Any.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,GAAG,CAAI,KAAqC;IACxD,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE,KAAY,CAAC,CAAA;AAChD,CAAC", "file": "Any.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Any([...]) }\n */\nexport function Any<T>(value: readonly T[] | FindOperator<T>): FindOperator<T> {\n    return new FindOperator(\"any\", value as any)\n}\n"], "sourceRoot": "../.."}