import { IProperties, IObjectCreator, IObjectDefinition, IManagedInstance, ObjectIdentifier, ScopeEnum } from '../interface';
export declare class FunctionDefinition implements IObjectDefinition {
    constructor();
    constructMethod: string;
    constructorArgs: IManagedInstance[];
    creator: IObjectCreator;
    dependsOn: ObjectIdentifier[];
    destroyMethod: string;
    export: string;
    id: string;
    name: string;
    initMethod: string;
    srcPath: string;
    path: any;
    properties: IProperties;
    namespace: string;
    asynchronous: boolean;
    handlerProps: any[];
    createFrom: any;
    allowDowngrade: boolean;
    protected innerAutowire: boolean;
    protected innerScope: ScopeEnum;
    getAttr(key: ObjectIdentifier): any;
    hasAttr(key: ObjectIdentifier): boolean;
    hasConstructorArgs(): boolean;
    hasDependsOn(): boolean;
    isAsync(): boolean;
    isDirect(): boolean;
    isExternal(): boolean;
    set scope(scope: ScopeEnum);
    isSingletonScope(): boolean;
    isRequestScope(): boolean;
    setAttr(key: ObjectIdentifier, value: any): void;
}
//# sourceMappingURL=functionDefinition.d.ts.map