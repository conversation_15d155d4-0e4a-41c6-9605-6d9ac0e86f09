"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JSONPService = void 0;
const core_1 = require("@midwayjs/core");
let JSONPService = class JSONPService {
    jsonp(body, config) {
        this.ctx.type = 'js';
        // https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Headers/X-Content-Type-Options
        if (this.ctx.set) {
            this.ctx.set('x-content-type-options', 'nosniff');
        }
        else if (this.res.set) {
            this.res.set('x-content-type-options', 'nosniff');
        }
        const { callback, limit } = Object.assign({}, this.jsonpConfig, config);
        // Only allow "[","]","a-zA-Z0123456789_", "$" and "." characters.
        let cb = (this.ctx.query[callback] || 'callback').replace(/[^[\]\w$.]+/g, '');
        if (cb.length > limit) {
            cb = cb.substring(0, limit);
        }
        const str = JSON.stringify(body === undefined ? null : body);
        // protect from jsonp xss
        return `/**/ typeof ${cb} === 'function' && ${cb}(${str});`;
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", Object)
], JSONPService.prototype, "ctx", void 0);
__decorate([
    (0, core_1.Config)('jsonp'),
    __metadata("design:type", Object)
], JSONPService.prototype, "jsonpConfig", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", Object)
], JSONPService.prototype, "res", void 0);
JSONPService = __decorate([
    (0, core_1.Provide)()
], JSONPService);
exports.JSONPService = JSONPService;
//# sourceMappingURL=jsonp.js.map