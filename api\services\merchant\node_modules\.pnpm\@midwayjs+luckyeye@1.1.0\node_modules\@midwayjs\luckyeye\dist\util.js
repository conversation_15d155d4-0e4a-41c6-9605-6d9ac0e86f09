"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.versionCompare = exports.getPackageVersion = exports.hasPackage = exports.getNpmList = exports.getDynamicPackageVersion = exports.color = exports.symbols = exports.colors = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const supportsColor = require("supports-color");
const semver_1 = require("semver");
/**
 * Enable coloring by default, except in the browser interface.
 */
exports.useColors = (supportsColor || (process.env.MOCHA_COLORS !== undefined));
/**
 * Default color map.
 */
exports.colors = {
    pass: 90,
    fail: 31,
    'bright pass': 92,
    'bright fail': 91,
    'bright yellow': 93,
    pending: 36,
    suite: 0,
    'error title': 0,
    'error message': 31,
    'error stack': 90,
    checkmark: 32,
    fast: 90,
    medium: 90,
    slow: 90,
    green: 32,
    light: 90,
    'diff gutter': 90,
    'diff added': 32,
    'diff removed': 31,
    info: 94,
    warn: 93,
};
/**
 * Default symbol map.
 */
exports.symbols = {
    ok: '✓',
    err: '✖',
    dot: '․',
    comma: ',',
    bang: '!',
    info: '¤ ',
    warn: '!️',
};
// With node.js on Windows: use symbols available in terminal default fonts
if (process.platform === 'win32') {
    exports.symbols.ok = '\u221A';
    exports.symbols.err = '\u00D7';
    exports.symbols.dot = '.';
}
const color = function (type, str) {
    if (!exports.useColors) {
        return String(str);
    }
    return '\u001b[' + exports.colors[type] + 'm' + str + '\u001b[0m';
};
exports.color = color;
function getDynamicPackageVersion(name) {
    try {
        return require(`${name}/package`).version;
    }
    catch (err) {
    }
    return '?';
}
exports.getDynamicPackageVersion = getDynamicPackageVersion;
async function getNpmList(dirPath, dataMap, level = 1) {
    if (level > 5 || !fs_1.existsSync(dirPath)) {
        return;
    }
    const fileList = fs_1.readdirSync(dirPath);
    return Promise.all(fileList.map(async (file) => {
        if (file[0] === '_') {
            // ignore tnpm module name
            return;
        }
        const filePath = path_1.join(dirPath, file);
        const fileStat = fs_1.statSync(filePath);
        if (!fileStat.isDirectory()) {
            return;
        }
        // npm group
        if (file[0] === '@') {
            return getNpmList(filePath, dataMap, level++);
        }
        const pkgJson = path_1.join(filePath, 'package.json');
        if (!fs_1.existsSync(pkgJson)) {
            return;
        }
        const { name, version } = JSON.parse(fs_1.readFileSync(pkgJson).toString());
        if (!dataMap[name]) {
            dataMap[name] = {};
        }
        if (!dataMap[name][version]) {
            dataMap[name][version] = 0;
        }
        dataMap[name][version]++;
        return getNpmList(path_1.join(filePath, 'node_modules'), dataMap, level++);
    }));
}
exports.getNpmList = getNpmList;
function hasPackage(baseDir, packageName, isDev = false) {
    try {
        const pkg = require(path_1.join(baseDir, 'package.json'));
        if (isDev) {
            return !!pkg['devDependencies'][packageName];
        }
        else {
            return !!pkg['dependencies'][packageName];
        }
    }
    catch (err) { }
    return false;
}
exports.hasPackage = hasPackage;
function getPackageVersion(baseDir, packageName, isDev = false) {
    try {
        const pkg = require(path_1.join(baseDir, 'package.json'));
        if (isDev) {
            return pkg['devDependencies'][packageName];
        }
        else {
            return pkg['dependencies'][packageName];
        }
    }
    catch (err) { }
    return undefined;
}
exports.getPackageVersion = getPackageVersion;
function versionCompare(current, target) {
    return semver_1.compare(current, target);
}
exports.versionCompare = versionCompare;
//# sourceMappingURL=util.js.map