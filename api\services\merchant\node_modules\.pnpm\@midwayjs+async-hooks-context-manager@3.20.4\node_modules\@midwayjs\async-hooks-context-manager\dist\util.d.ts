import { AsyncLocalStorageContextManager } from './asyncLocalStorageContextManager';
import { AsyncHooksContextManager } from './asyncHooksContextManager';
export declare function isSemverGreaterThanOrEqualTo(currentVersion: string, targetVersion: string): boolean;
export declare function createContextManager(): AsyncHooksContextManager | AsyncLocalStorageContextManager;
//# sourceMappingURL=util.d.ts.map