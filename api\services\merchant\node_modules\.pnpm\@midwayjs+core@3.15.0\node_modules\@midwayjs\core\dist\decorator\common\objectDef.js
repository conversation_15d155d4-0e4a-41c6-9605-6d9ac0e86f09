"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Singleton = exports.Scope = exports.Destroy = exports.Init = void 0;
const decoratorManager_1 = require("../decoratorManager");
const provide_1 = require("./provide");
const interface_1 = require("../../interface");
function Init() {
    return function (target, propertyKey) {
        (0, decoratorManager_1.saveObjectDefinition)(target, { initMethod: propertyKey });
    };
}
exports.Init = Init;
function Destroy() {
    return function (target, propertyKey) {
        (0, decoratorManager_1.saveObjectDefinition)(target, {
            destroyMethod: propertyKey,
        });
    };
}
exports.Destroy = Destroy;
function Scope(scope, scopeOptions) {
    return function (target) {
        (0, decoratorManager_1.saveObjectDefinition)(target, { scope, ...scopeOptions });
    };
}
exports.Scope = Scope;
function Singleton() {
    return function (target) {
        Scope(interface_1.ScopeEnum.Singleton)(target);
        (0, provide_1.Provide)()(target);
    };
}
exports.Singleton = Singleton;
//# sourceMappingURL=objectDef.js.map