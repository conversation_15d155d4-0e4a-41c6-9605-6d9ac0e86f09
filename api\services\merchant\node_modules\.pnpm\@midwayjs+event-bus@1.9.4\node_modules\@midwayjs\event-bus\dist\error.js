"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventBusDispatchStrategyError = exports.EventBusPublishSpecifyWorkerError = exports.EventBusWorkerPostError = exports.EventBusMainPostError = exports.EventBusTimeoutError = exports.EventBusPublishTimeoutError = void 0;
const util_1 = require("util");
class EventBusPublishTimeoutError extends Error {
    constructor(messageId) {
        super(`Message(${messageId}) publish timeout.`);
        this.name = 'EventBusPublishTimeoutError';
    }
}
exports.EventBusPublishTimeoutError = EventBusPublishTimeoutError;
class EventBusTimeoutError extends Error {
    constructor() {
        super('Waiting for ready timeout throws this error.');
        this.name = 'EventBusTimeoutError';
    }
}
exports.EventBusTimeoutError = EventBusTimeoutError;
class EventBusMainPostError extends Error {
    constructor(message, err) {
        super((0, util_1.format)('Main thread post message [%j] error => %s.', message, err.stack));
        this.name = 'EventBusMainPostError';
    }
}
exports.EventBusMainPostError = EventBusMainPostError;
class EventBusWorkerPostError extends Error {
    constructor(message, err) {
        super((0, util_1.format)('Worker post message [%j] error => %j.', message, err.stack));
        this.name = 'EventBusWorkerPostError';
    }
}
exports.EventBusWorkerPostError = EventBusWorkerPostError;
class EventBusPublishSpecifyWorkerError extends Error {
    constructor(workerId) {
        super((0, util_1.format)('Worker(%s) not find in ready map, maybe it is a wrong pid.', workerId));
        this.name = 'EventBusPublishSpecifyWorkerError';
    }
}
exports.EventBusPublishSpecifyWorkerError = EventBusPublishSpecifyWorkerError;
class EventBusDispatchStrategyError extends Error {
    constructor() {
        super('Dispatch strategy not found a worker and stop post.');
        this.name = 'EventBusDispatchStrategyError';
    }
}
exports.EventBusDispatchStrategyError = EventBusDispatchStrategyError;
//# sourceMappingURL=error.js.map