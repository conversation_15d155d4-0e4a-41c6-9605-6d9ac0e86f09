{"name": "moleculer", "version": "0.14.35", "description": "Fast & powerful microservices framework for Node.JS", "main": "index.js", "exports": {".": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.mjs"}}, "scripts": {"bench": "node benchmark/index.js", "ci": "jest --watch", "ci:leak": "jest --testMatch \"**/leak-detection/index.spc.js\" --runInBand --watch", "coverall": "cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "demo": "node examples/index.js", "deps": "npm-check -u", "postdeps": "npm test", "dev": "nodemon dev/index.js", "lint": "eslint --ext=.js benchmark bin examples src test", "lint:fix": "eslint --fix --ext=.js benchmark bin examples src test", "perf": "nodemon --allow-natives-syntax benchmark/perf-runner.js", "pperf": "node --inspect --expose-gc benchmark/perf-runner.js", "memleak": "node benchmark/memleak-test.js", "proto": "pbjs -t static-module -w commonjs -o src/serializers/proto/packets.proto.js src/serializers/proto/packets.proto", "thrift": "thrift -gen js:node -o src\\serializers\\thrift src\\serializers\\thrift\\packets.thrift", "test": "jest --coverage --forceExit", "test:unit": "jest --testMatch \"**/unit/**/*.spec.js\" --coverage", "test:int": "jest --testMatch \"**/integration/**/*.spec.js\" --coverage", "test:e2e": "cd test/e2e && ./start.sh", "test:leak": "jest --testMatch \"**/leak-detection/**/*.spc.js\" --runInBand", "test:ts": "tsd && tsc -p test/typescript/hello-world && ts-node -P test/typescript/hello-world/tsconfig.json test/typescript/hello-world/index.ts", "test:esm": "node bin/moleculer-runner.mjs -c test/esm/moleculer.config.mjs test/esm/*.service.*js", "esm-wrapper": "gen-esm-wrapper . ./index.mjs", "prerelease": "npm run esm-wrapper", "release": "npm publish --access public && git push --follow-tags", "release:beta": "npm publish --tag next --access public && git push --follow-tags"}, "keywords": ["microservice", "microservices", "framework", "backend", "messagebus", "rpc", "services", "micro", "pubsub", "scalable", "distributed"], "repository": {"type": "git", "url": "https://github.com/moleculerjs/moleculer.git"}, "funding": "https://github.com/moleculerjs/moleculer?sponsor=1", "bin": {"moleculer-runner": "./bin/moleculer-runner.js", "moleculer-runner-esm": "./bin/moleculer-runner.mjs"}, "author": "MoleculerJS", "license": "MIT", "devDependencies": {"@icebob/node-memwatch": "^2.1.0", "@sinonjs/fake-timers": "^9.1.2", "@types/bunyan": "^1.8.11", "@types/ioredis": "^4.28.10", "@types/node": "^18.11.18", "@types/pino": "^7.0.5", "amqplib": "^0.10.4", "avsc": "^5.7.7", "benchmarkify": "^3.0.0", "bunyan": "^1.8.15", "cbor-x": "1.5.0", "coveralls": "^3.1.1", "dd-trace": "^0.36.3", "debug": "^4.3.6", "dotenv": "^16.4.5", "eslint": "^8.53.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-security": "^1.7.1", "etcd3": "1.1.0", "event-loop-stats": "^1.4.1", "fakerator": "^0.3.6", "gc-stats": "^1.4.1", "gen-esm-wrapper": "^1.1.3", "ioredis": "^4.28.5", "jaeger-client": "^3.19.0", "jest": "^27.5.1", "jest-cli": "^27.5.1", "jest-diff": "^27.5.1", "joi": "^17.13.3", "kafka-node": "^5.0.0", "log4js": "^6.9.1", "moleculer-repl": "^0.7.4", "mqtt": "^4.3.7", "msgpack5": "^6.0.2", "nats": "^2.28.1", "node-nats-streaming": "^0.3.2", "nodemon": "^2.0.20", "notepack.io": "^3.0.1", "npm-check": "^6.0.1", "pino": "^6.14.0", "prettier": "^2.8.2", "protobufjs": "^7.3.2", "redlock": "^4.2.0", "rhea-promise": "^2.1.0", "supertest": "^6.3.3", "thrift": "^0.16.0", "ts-node": "^10.9.2", "tsd": "^0.22.0", "typescript": "^4.9.4", "v8-natives": "^1.2.5", "winston": "^3.13.1", "winston-context": "^0.0.7"}, "dependencies": {"args": "^5.0.3", "eventemitter2": "^6.4.9", "fastest-validator": "^1.19.0", "glob": "^7.2.0", "ipaddr.js": "^2.2.0", "kleur": "^4.1.5", "lodash": "^4.17.21", "lru-cache": "^6.0.0", "node-fetch": "^2.6.7", "recursive-watch": "^1.1.4"}, "peerDependencies": {"amqplib": "^0.7.0 || ^0.8.0 || ^0.9.0 || ^0.10.0", "avsc": "^5.0.0", "bunyan": "^1.0.0", "cbor-x": "^0.8.3 || ^0.9.0 || ^1.2.0", "dd-trace": "^0.33.0 || ^0.34.0 || ^0.35.0 || ^0.36.0 || >=1.0.0 <1.6.0", "debug": "^4.0.0", "etcd3": "^1.0.0", "ioredis": "^4.0.0 || ^5.0.0", "jaeger-client": "^3.0.0", "kafka-node": "^5.0.0", "log4js": "^6.0.0", "mqtt": "^4.0.0 || ^5.0.0", "msgpack5": "^5.0.0 || ^6.0.0", "nats": "^1.0.0 || ^2.0.0", "node-nats-streaming": "^0.0.51 || ^0.2.0 || ^0.3.0", "notepack.io": "^2.0.0 || ^3.0.0", "pino": "^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "protobufjs": "^6.0.0 || ^7.0.0", "redlock": "^4.0.0", "rhea-promise": "^1.0.0 || ^2.0.0", "thrift": "^0.12.0 || ^0.16.0", "winston": "^3.0.0"}, "peerDependenciesMeta": {"amqplib": {"optional": true}, "avsc": {"optional": true}, "bunyan": {"optional": true}, "cbor-x": {"optional": true}, "dd-trace": {"optional": true}, "debug": {"optional": true}, "etcd3": {"optional": true}, "ioredis": {"optional": true}, "jaeger-client": {"optional": true}, "kafka-node": {"optional": true}, "log4js": {"optional": true}, "mqtt": {"optional": true}, "msgpack5": {"optional": true}, "nats": {"optional": true}, "node-nats-streaming": {"optional": true}, "notepack.io": {"optional": true}, "pino": {"optional": true}, "protobufjs": {"optional": true}, "redlock": {"optional": true}, "rhea-promise": {"optional": true}, "thrift": {"optional": true}, "winston": {"optional": true}}, "engines": {"node": ">= 10.x.x"}, "types": "./index.d.ts", "tsd": {"directory": "test/typescript/tsd", "compilerOptions": {"noImplicitThis": true}}, "jest": {"coverageDirectory": "../coverage", "coveragePathIgnorePatterns": ["/node_modules/", "/test/services/", "/test/typescript/", "/test/unit/utils.js", "/src/serializers/proto/", "/src/serializers/thrift/"], "transform": {}, "testEnvironment": "node", "rootDir": "./src", "roots": ["../test"]}}