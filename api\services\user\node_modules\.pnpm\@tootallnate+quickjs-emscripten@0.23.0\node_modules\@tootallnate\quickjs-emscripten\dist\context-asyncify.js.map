{"version": 3, "file": "context-asyncify.js", "sourceRoot": "", "sources": ["../ts/context-asyncify.ts"], "names": [], "mappings": ";;;AAAA,uCAA0C;AAC1C,mCAAkC;AAOlC,mCAA+E;AAQ/E;;;;;;GAMG;AACH,MAAa,mBAAoB,SAAQ,wBAAc;IAWrD;;OAEG;IACH,KAAK,CAAC,aAAa,CACjB,IAAY,EACZ,WAAmB,SAAS;IAC5B,6CAA6C;IAC7C,OAAqC;QAErC,MAAM,YAAY,GAAG,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAqB,CAAA;QACxE,MAAM,KAAK,GAAG,IAAA,0BAAkB,EAAC,OAAO,CAAc,CAAA;QACtD,IAAI,SAAS,GAAG,CAAmB,CAAA;QACnC,IAAI;YACF,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM;iBAC1B,kBAAkB,CAAC,IAAI,CAAC;iBACxB,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACtB,IAAI,CAAC,GAAG,CAAC,mBAAmB,CAC1B,IAAI,CAAC,GAAG,CAAC,KAAK,EACd,UAAU,CAAC,KAAK,EAChB,QAAQ,EACR,YAAY,EACZ,KAAK,CACN,CACF,CAAA;SACJ;QAAC,OAAO,KAAK,EAAE;YACd,IAAA,gBAAQ,EAAC,2BAA2B,EAAE,KAAK,CAAC,CAAA;YAC5C,MAAM,KAAK,CAAA;SACZ;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACzE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACxD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAA;SACxD;QACD,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAA;IAC1D,CAAC;IAED;;;;;;;;;;;;OAYG;IACH,qBAAqB,CAAC,IAAY,EAAE,EAA+B;QACjE,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAS,CAAC,CAAA;IAC1C,CAAC;CACF;AA/DD,kDA+DC", "sourcesContent": ["import { <PERSON>JSContext } from \"./context\"\nimport { debug<PERSON><PERSON> } from \"./debug\"\nimport { QuickJSAsyncEmscriptenModule } from \"./emscripten-types\"\nimport { QuickJSAsyncFFI } from \"./variants\"\nimport { EvalDetectModule, <PERSON>l<PERSON><PERSON><PERSON>, J<PERSON><PERSON>imePointer, JSV<PERSON>uePointer } from \"./types-ffi\"\nimport { Lifetime } from \"./lifetime\"\nimport { QuickJSModuleCallbacks } from \"./module\"\nimport { QuickJSAsyncRuntime } from \"./runtime-asyncify\"\nimport { ContextEvalOptions, evalOptionsToFlags, QuickJSHandle } from \"./types\"\nimport { VmCallResult } from \"./vm-interface\"\n\nexport type AsyncFunctionImplementation = (\n  this: QuickJSHandle,\n  ...args: QuickJSHandle[]\n) => Promise<QuickJSHandle | VmCallResult<QuickJSHandle> | void>\n\n/**\n * Asyncified version of [[QuickJSContext]].\n *\n * *Asyncify* allows normally synchronous code to wait for asynchronous Promises\n * or callbacks. The asyncified version of QuickJSContext can wait for async\n * host functions as though they were synchronous.\n */\nexport class QuickJ<PERSON>syncContext extends QuickJSContext {\n  public declare runtime: QuickJSAsyncRuntime\n  /** @private */\n  protected declare module: QuickJSAsyncEmscriptenModule\n  /** @private */\n  protected declare ffi: QuickJSAsyncFFI\n  /** @private */\n  protected declare rt: Lifetime<JSRuntimePointer>\n  /** @private */\n  protected declare callbacks: QuickJSModuleCallbacks\n\n  /**\n   * Asyncified version of [[evalCode]].\n   */\n  async evalCodeAsync(\n    code: string,\n    filename: string = \"eval.js\",\n    /** See [[EvalFlags]] for number semantics */\n    options?: number | ContextEvalOptions\n  ): Promise<VmCallResult<QuickJSHandle>> {\n    const detectModule = (options === undefined ? 1 : 0) as EvalDetectModule\n    const flags = evalOptionsToFlags(options) as EvalFlags\n    let resultPtr = 0 as JSValuePointer\n    try {\n      resultPtr = await this.memory\n        .newHeapCharPointer(code)\n        .consume((charHandle) =>\n          this.ffi.QTS_Eval_MaybeAsync(\n            this.ctx.value,\n            charHandle.value,\n            filename,\n            detectModule,\n            flags\n          )\n        )\n    } catch (error) {\n      debugLog(\"QTS_Eval_MaybeAsync threw\", error)\n      throw error\n    }\n    const errorPtr = this.ffi.QTS_ResolveException(this.ctx.value, resultPtr)\n    if (errorPtr) {\n      this.ffi.QTS_FreeValuePointer(this.ctx.value, resultPtr)\n      return { error: this.memory.heapValueHandle(errorPtr) }\n    }\n    return { value: this.memory.heapValueHandle(resultPtr) }\n  }\n\n  /**\n   * Similar to [[newFunction]].\n   * Convert an async host Javascript function into a synchronous QuickJS function value.\n   *\n   * Whenever QuickJS calls this function, the VM's stack will be unwound while\n   * waiting the async function to complete, and then restored when the returned\n   * promise resolves.\n   *\n   * Asyncified functions must never call other asyncified functions or\n   * `import`, even indirectly, because the stack cannot be unwound twice.\n   *\n   * See [Emscripten's docs on Asyncify](https://emscripten.org/docs/porting/asyncify.html).\n   */\n  newAsyncifiedFunction(name: string, fn: AsyncFunctionImplementation): QuickJSHandle {\n    return this.newFunction(name, fn as any)\n  }\n}\n"]}