{"version": 3, "sources": ["../browser/src/util/StringUtils.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,QAAQ,CAAA;AAE1B;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW,EAAE,eAAwB,KAAK;IAChE,IAAI,YAAY;QAAE,GAAG,GAAG,GAAG,GAAG,GAAG,CAAA;IACjC,OAAO,GAAG,CAAC,OAAO,CAAC,sBAAsB,EAAE,UAAU,KAAK,EAAE,EAAE,EAAE,EAAE;QAC9D,IAAI,EAAE;YAAE,OAAO,EAAE,CAAC,WAAW,EAAE,CAAA;QAC/B,OAAO,EAAE,CAAC,WAAW,EAAE,CAAA;IAC3B,CAAC,CAAC,CAAA;AACN,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW;IACjC,OAAO,CACH,GAAG;QACC,cAAc;SACb,OAAO,CAAC,wBAAwB,EAAE,SAAS,CAAC;QAC7C,YAAY;SACX,OAAO,CAAC,oBAAoB,EAAE,OAAO,CAAC;SACtC,WAAW,EAAE,CACrB,CAAA;AACL,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,SAAS,CAAC,GAAW;IACjC,OAAO,GAAG,CAAC,OAAO,CACd,QAAQ,EACR,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CACrE,CAAA;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,GAAW,EAAE,mBAA2B,CAAC;IAChE,MAAM,KAAK,GAAG,GAAG;SACZ,OAAO,CAAC,kCAAkC,EAAE,OAAO,CAAC;SACpD,KAAK,CAAC,GAAG,CAAC,CAAA;IACf,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;QAC9B,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,gBAAgB,CAAC,CAAA;QACvC,OAAO,GAAG,CAAA;IACd,CAAC,EAAE,EAAE,CAAC,CAAA;AACV,CAAC;AAWD;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,OAAO,CAAC,KAAa,EAAE,UAA2B,EAAE;IAChE,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,SAAS,GAAG,IAAI,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,OAAO,CAAA;IAEvE,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,CAAA;IACvC,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,GAAW,EAAE,EAAE;QACjE,gFAAgF;QAChF,MAAM,YAAY,GAAG,GAAG;aACnB,OAAO,CAAC,mCAAmC,EAAE,OAAO,CAAC;aACrD,KAAK,CAAC,GAAG,CAAC,CAAA;QACf,mEAAmE;QACnE,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAA;QACnE,MAAM,YAAY,GAAG,YAAY;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;aACrC,IAAI,CAAC,EAAE,CAAC,CAAA;QAEb,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACtB,OAAO,GAAG,CAAA;IACd,CAAC,EAAE,EAAE,CAAC,CAAA;IAEN,OAAO,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;AACxC,CAAC;AAMD;;;;;GAKG;AACH,MAAM,UAAU,IAAI,CAAC,KAAa,EAAE,UAAwB,EAAE;IAC1D,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,CAAA;IAClC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IAClC,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;IAC9C,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;IAC/C,CAAC;IACD,OAAO,WAAW,CAAA;AACtB,CAAC", "file": "StringUtils.js", "sourcesContent": ["import shajs from \"sha.js\"\n\n/**\n * Converts string into camelCase.\n *\n * @see http://stackoverflow.com/questions/2970525/converting-any-string-into-camel-case\n */\nexport function camelCase(str: string, firstCapital: boolean = false): string {\n    if (firstCapital) str = \" \" + str\n    return str.replace(/^([A-Z])|[\\s-_](\\w)/g, function (match, p1, p2) {\n        if (p2) return p2.toUpperCase()\n        return p1.toLowerCase()\n    })\n}\n\n/**\n * Converts string into snake_case.\n *\n */\nexport function snakeCase(str: string): string {\n    return (\n        str\n            // ABc -> a_bc\n            .replace(/([A-Z])([A-Z])([a-z])/g, \"$1_$2$3\")\n            // aC -> a_c\n            .replace(/([a-z0-9])([A-Z])/g, \"$1_$2\")\n            .toLowerCase()\n    )\n}\n\n/**\n * Converts string into Title Case.\n *\n * @see http://stackoverflow.com/questions/196972/convert-string-to-title-case-with-javascript\n */\nexport function titleCase(str: string): string {\n    return str.replace(\n        /\\w\\S*/g,\n        (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(),\n    )\n}\n\n/**\n * Builds abbreviated string from given string;\n */\nexport function abbreviate(str: string, abbrLettersCount: number = 1): string {\n    const words = str\n        .replace(/([a-z\\xE0-\\xFF])([A-Z\\xC0\\xDF])/g, \"$1 $2\")\n        .split(\" \")\n    return words.reduce((res, word) => {\n        res += word.substr(0, abbrLettersCount)\n        return res\n    }, \"\")\n}\n\nexport interface IShortenOptions {\n    /** String used to split \"segments\" of the alias/column name */\n    separator?: string\n    /** Maximum length of any \"segment\" */\n    segmentLength?: number\n    /** Length of any \"term\" in a \"segment\"; \"OrderItem\" is a segment, \"Order\" and \"Items\" are terms */\n    termLength?: number\n}\n\n/**\n * Shorten a given `input`. Useful for RDBMS imposing a limit on the\n * maximum length of aliases and column names in SQL queries.\n *\n * @param input String to be shortened.\n * @param options Default to `4` for segments length, `2` for terms length, `'__'` as a separator.\n *\n * @return Shortened `input`.\n *\n * @example\n * // returns: \"UsShCa__orde__mark__dire\"\n * shorten('UserShoppingCart__order__market__director')\n *\n * // returns: \"cat_wit_ver_lon_nam_pos_wit_ver_lon_nam_pos_wit_ver_lon_nam\"\n * shorten(\n *   'category_with_very_long_name_posts_with_very_long_name_post_with_very_long_name',\n *   { separator: '_', segmentLength: 3 }\n * )\n *\n * // equals: UsShCa__orde__mark_market_id\n * `${shorten('UserShoppingCart__order__market')}_market_id`\n */\nexport function shorten(input: string, options: IShortenOptions = {}): string {\n    const { segmentLength = 4, separator = \"__\", termLength = 2 } = options\n\n    const segments = input.split(separator)\n    const shortSegments = segments.reduce((acc: string[], val: string) => {\n        // split the given segment into many terms based on an eventual camel cased name\n        const segmentTerms = val\n            .replace(/([a-z\\xE0-\\xFF])([A-Z\\xC0-\\xDF])/g, \"$1 $2\")\n            .split(\" \")\n        // \"OrderItemList\" becomes \"OrItLi\", while \"company\" becomes \"comp\"\n        const length = segmentTerms.length > 1 ? termLength : segmentLength\n        const shortSegment = segmentTerms\n            .map((term) => term.substr(0, length))\n            .join(\"\")\n\n        acc.push(shortSegment)\n        return acc\n    }, [])\n\n    return shortSegments.join(separator)\n}\n\ninterface IHashOptions {\n    length?: number\n}\n\n/**\n * Returns a hashed input.\n *\n * @param input String to be hashed.\n * @param options.length Optionally, shorten the output to desired length.\n */\nexport function hash(input: string, options: IHashOptions = {}): string {\n    const hashFunction = shajs(\"sha1\")\n    hashFunction.update(input, \"utf8\")\n    const hashedInput = hashFunction.digest(\"hex\")\n    if (options.length) {\n        return hashedInput.slice(0, options.length)\n    }\n    return hashedInput\n}\n"], "sourceRoot": ".."}