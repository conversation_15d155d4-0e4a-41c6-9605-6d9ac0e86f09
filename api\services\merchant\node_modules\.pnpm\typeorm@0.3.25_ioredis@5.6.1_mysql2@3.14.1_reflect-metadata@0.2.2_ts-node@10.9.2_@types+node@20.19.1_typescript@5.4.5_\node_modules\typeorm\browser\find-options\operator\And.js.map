{"version": 3, "sources": ["../browser/src/find-options/operator/And.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C,MAAM,UAAU,GAAG,CAAI,GAAG,MAAyB;IAC/C,OAAO,IAAI,YAAY,CAAC,KAAK,EAAE,MAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC7D,CAAC", "file": "And.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\nexport function And<T>(...values: FindOperator<T>[]): FindOperator<T> {\n    return new FindOperator(\"and\", values as any, true, true)\n}\n"], "sourceRoot": "../.."}