{"version": 3, "sources": ["../../src/driver/cockroachdb/CockroachConnectionOptions.ts"], "names": [], "mappings": "", "file": "CockroachConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { CockroachConnectionCredentialsOptions } from \"./CockroachConnectionCredentialsOptions\"\n\n/**\n * Cockroachdb-specific connection options.\n */\nexport interface CockroachConnectionOptions\n    extends BaseDataSourceOptions,\n        CockroachConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"cockroachdb\"\n\n    /**\n     * Enable time travel queries on cockroachdb.\n     * https://www.cockroachlabs.com/docs/stable/as-of-system-time.html\n     */\n    readonly timeTravelQueries: boolean\n\n    /**\n     * Schema name.\n     */\n    readonly schema?: string\n\n    /**\n     * The driver object\n     * This defaults to `require(\"pg\")`.\n     */\n    readonly driver?: any\n\n    /**\n     * The driver object\n     * This defaults to `require(\"pg-native\")`.\n     */\n    readonly nativeDriver?: any\n\n    /**\n     * Replication setup.\n     */\n    readonly replication?: {\n        /**\n         * Master server used by orm to perform writes.\n         */\n        readonly master: CockroachConnectionCredentialsOptions\n\n        /**\n         * List of read-from servers (slaves).\n         */\n        readonly slaves: CockroachConnectionCredentialsOptions[]\n\n        /**\n         * Default connection pool to use for SELECT queries\n         * @default \"slave\"\n         */\n        readonly defaultMode?: ReplicationMode\n    }\n\n    /**\n     * sets the application_name var to help db administrators identify\n     * the service using this connection. Defaults to 'undefined'\n     */\n    readonly applicationName?: string\n\n    /**\n     * Function handling errors thrown by drivers pool.\n     * Defaults to logging error with `warn` level.\n     */\n    readonly poolErrorHandler?: (err: any) => any\n\n    /**\n     * Max number of transaction retries in case of 40001 error.\n     */\n    readonly maxTransactionRetries?: number\n}\n"], "sourceRoot": "../.."}