{"version": 3, "sources": ["../browser/src/metadata/EntityListenerMetadata.ts"], "names": [], "mappings": "AAMA;;GAEG;AACH,MAAM,OAAO,sBAAsB;IA+B/B,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAIX;QACG,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAChD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;QACjC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;QAC7C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA;IACjC,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,SAAS,CAAC,MAAqB;QAC3B,2DAA2D;QAC3D,OAAO,CACH,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,IAAI,oIAAoI;YACzL,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,UAAU;gBAC7C,MAAM,CAAC,WAAW,CAAC,SAAS;oBACxB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CACtC,CAAA,CAAC,yDAAyD;IAC/D,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,MAAqB;QACzB,gDAAgD;QAChD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACzB,0BAA0B;YAC1B,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;YAE9C,4CAA4C;YAC5C,IAAI,CAAC,YAAY;gBACb,MAAM,IAAI,KAAK,CACX,2BAA2B,IAAI,CAAC,YAAY,+BAA+B,MAAM,CAAC,WAAW,CAAC,IAAI,IAAI,CACzG,CAAA;YAEL,+CAA+C;YAC/C,IAAI,OAAO,YAAY,KAAK,UAAU;gBAClC,MAAM,IAAI,KAAK,CACX,2BAA2B,IAAI,CAAC,YAAY,gBACxC,MAAM,CAAC,WAAW,CAAC,IACvB,iCAAiC,OAAO,YAAY,IAAI,CAC3D,CAAA;YAEL,oCAAoC;YACpC,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QACpC,CAAC;QAED,2BAA2B;QAC3B,IAAI,CAAC,wBAAwB,CACzB,MAAM,EACN,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAChD,CAAA;IACL,CAAC;IAED,wEAAwE;IACxE,oBAAoB;IACpB,wEAAwE;IAExE;;OAEG;IACO,wBAAwB,CAC9B,MAAqB,EACrB,aAAuB;QAEvB,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;QAC1C,IAAI,CAAC,YAAY,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;YAAE,OAAM;QAElD,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC7B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC,QAAuB,EAAE,EAAE,CACjD,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAChC,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAA;YAC7C,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,MAAM,CAAC,YAAY,CAAC;gBACpB,IAAI,CAAC,wBAAwB,CACzB,MAAM,CAAC,YAAY,CAAC,EACpB,aAAa,CAChB,CAAA;QACT,CAAC;IACL,CAAC;CACJ", "file": "EntityListenerMetadata.js", "sourcesContent": ["import { EventListenerType } from \"./types/EventListenerTypes\"\nimport { EntityListenerMetadataArgs } from \"../metadata-args/EntityListenerMetadataArgs\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { EmbeddedMetadata } from \"./EmbeddedMetadata\"\n\n/**\n * This metadata contains all information about entity's listeners.\n */\nexport class EntityListenerMetadata {\n    // ---------------------------------------------------------------------\n    // Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the listener.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Embedded metadata of the listener, in the case if listener is in embedded.\n     */\n    embeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * Target class to which metadata is applied.\n     * This can be different then entityMetadata.target in the case if listener is in the embedded.\n     */\n    target: Function | string\n\n    /**\n     * Target's property name to which this metadata is applied.\n     */\n    propertyName: string\n\n    /**\n     * The type of the listener.\n     */\n    type: EventListenerType\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        embeddedMetadata?: EmbeddedMetadata\n        args: EntityListenerMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.embeddedMetadata = options.embeddedMetadata\n        this.target = options.args.target\n        this.propertyName = options.args.propertyName\n        this.type = options.args.type\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Checks if entity listener is allowed to be executed on the given entity.\n     */\n    isAllowed(entity: ObjectLiteral) {\n        // todo: create in entity metadata method like isInherited?\n        return (\n            this.entityMetadata.target === entity.constructor || // todo: .constructor won't work for entity schemas, but there are no entity listeners in schemas since there are no objects, right?\n            (typeof this.entityMetadata.target === \"function\" &&\n                entity.constructor.prototype instanceof\n                    this.entityMetadata.target)\n        ) // todo: also need to implement entity schema inheritance\n    }\n\n    /**\n     * Executes listener method of the given entity.\n     */\n    execute(entity: ObjectLiteral) {\n        // Check if the Embedded Metadata does not exist\n        if (!this.embeddedMetadata) {\n            // Get the Entity's Method\n            const entityMethod = entity[this.propertyName]\n\n            // Check if the Entity Method does not exist\n            if (!entityMethod)\n                throw new Error(\n                    `Entity listener method \"${this.propertyName}\" does not exist in entity \"${entity.constructor.name}\".`,\n                )\n\n            // Check if the Entity Method is not a function\n            if (typeof entityMethod !== \"function\")\n                throw new Error(\n                    `Entity listener method \"${this.propertyName}\" in entity \"${\n                        entity.constructor.name\n                    }\" must be a function but got \"${typeof entityMethod}\".`,\n                )\n\n            // Call and return the Entity Method\n            return entityMethod.call(entity)\n        }\n\n        // Call the Embedded Method\n        this.callEntityEmbeddedMethod(\n            entity,\n            this.embeddedMetadata.propertyPath.split(\".\"),\n        )\n    }\n\n    // ---------------------------------------------------------------------\n    // Protected Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Calls embedded entity listener method no matter how nested it is.\n     */\n    protected callEntityEmbeddedMethod(\n        entity: ObjectLiteral,\n        propertyPaths: string[],\n    ): void {\n        const propertyPath = propertyPaths.shift()\n        if (!propertyPath || !entity[propertyPath]) return\n\n        if (propertyPaths.length === 0) {\n            if (Array.isArray(entity[propertyPath])) {\n                entity[propertyPath].map((embedded: ObjectLiteral) =>\n                    embedded[this.propertyName](),\n                )\n            } else {\n                entity[propertyPath][this.propertyName]()\n            }\n        } else {\n            if (entity[propertyPath])\n                this.callEntityEmbeddedMethod(\n                    entity[propertyPath],\n                    propertyPaths,\n                )\n        }\n    }\n}\n"], "sourceRoot": ".."}