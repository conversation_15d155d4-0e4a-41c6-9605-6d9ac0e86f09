{"version": 3, "sources": ["../../src/error/RepositoryNotTreeError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAC7C,qDAAiD;AACjD,6DAAyD;AAEzD;;GAEG;AACH,MAAa,sBAAuB,SAAQ,2BAAY;IACpD,YAAY,WAA8B;QACtC,KAAK,EAAE,CAAA;QAEP,IAAI,UAAkB,CAAA;QACtB,IAAI,iCAAe,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;YAC9C,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAA;QACzC,CAAC;aAAM,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;YAC3C,UAAU,GAAG,WAAW,CAAC,IAAI,CAAA;QACjC,CAAC;aAAM,IACH,yBAAW,CAAC,QAAQ,CAAC,WAAW,CAAC;YACjC,MAAM,IAAK,WAAmB,EAChC,CAAC;YACC,UAAU,GAAI,WAAmB,CAAC,IAAI,CAAA;QAC1C,CAAC;aAAM,CAAC;YACJ,UAAU,GAAG,WAAkB,CAAA;QACnC,CAAC;QACD,IAAI,CAAC,OAAO,GAAG,sBAAsB,UAAU,+EAA+E,CAAA;IAClI,CAAC;CACJ;AAnBD,wDAmBC", "file": "RepositoryNotTreeError.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\nimport { TypeORMError } from \"./TypeORMError\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Thrown when repository for the given class is not found.\n */\nexport class RepositoryNotTreeError extends TypeORMError {\n    constructor(entityClass: EntityTarget<any>) {\n        super()\n\n        let targetName: string\n        if (InstanceChecker.isEntitySchema(entityClass)) {\n            targetName = entityClass.options.name\n        } else if (typeof entityClass === \"function\") {\n            targetName = entityClass.name\n        } else if (\n            ObjectUtils.isObject(entityClass) &&\n            \"name\" in (entityClass as any)\n        ) {\n            targetName = (entityClass as any).name\n        } else {\n            targetName = entityClass as any\n        }\n        this.message = `Repository of the \"${targetName}\" class is not a TreeRepository. Try to apply @Tree decorator on your entity.`\n    }\n}\n"], "sourceRoot": ".."}