/**
 * wrap async function with retry
 * @param retryFn
 * @param retryTimes
 * @param options
 */
export declare function retryWithAsync<T extends (...args: any[]) => Promise<unknown>>(retryFn: T, retryTimes?: number, options?: {
    throwOriginError?: boolean;
    retryInterval?: number;
    receiver?: any;
}): (...args: Parameters<T>) => ReturnType<T>;
/**
 * wrap sync function with retry
 * @param retryFn
 * @param retryTimes
 * @param options
 */
export declare function retryWith<T extends (...args: any[]) => unknown>(retryFn: T, retryTimes?: number, options?: {
    throwOriginError?: boolean;
    receiver?: any;
}): (...args: Parameters<T>) => ReturnType<T>;
//# sourceMappingURL=retry.d.ts.map