"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultValuePipe = exports.ParseFloatPipe = exports.ParseBoolPipe = exports.ParseIntPipe = exports.DecoratorValidPipe = exports.ParsePipe = exports.ValidationPipe = exports.AbstractValidationPipe = void 0;
const core_1 = require("@midwayjs/core");
const service_1 = require("./service");
const i18n = require("@midwayjs/i18n");
const constants_1 = require("./constants");
const Joi = require("joi");
class AbstractValidationPipe {
    validateWithSchema(value, options, schema) {
        const validateOptions = this.parseValidationOptions(options);
        const result = this.validateService.validateWithSchema(schema, value, validateOptions);
        if (result && result.value !== undefined) {
            return result.value;
        }
        return value;
    }
    validate(value, options) {
        const validateOptions = this.parseValidationOptions(options);
        if (options.metaType.isBaseType) {
            return value;
        }
        const result = this.validateService.validate(options.metaType.originDesign, value, validateOptions);
        if (result && result.value) {
            return result.value;
        }
        return value;
    }
    parseValidationOptions(options) {
        const validateOptions = (0, core_1.getPropertyMetadata)(constants_1.VALIDATE_KEY, options.target, options.methodName) ||
            {};
        if (!validateOptions.locale) {
            const maybeCtx = options.target[core_1.REQUEST_OBJ_CTX_KEY];
            if (maybeCtx && maybeCtx.getAttr) {
                validateOptions.locale = maybeCtx.getAttr(i18n.I18N_ATTR_KEY);
            }
        }
        return validateOptions;
    }
    getSchema() {
        return undefined;
    }
}
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", service_1.ValidateService)
], AbstractValidationPipe.prototype, "validateService", void 0);
exports.AbstractValidationPipe = AbstractValidationPipe;
let ValidationPipe = class ValidationPipe extends AbstractValidationPipe {
    transform(value, options) {
        return this.validate(value, options);
    }
};
ValidationPipe = __decorate([
    (0, core_1.Pipe)()
], ValidationPipe);
exports.ValidationPipe = ValidationPipe;
class ParsePipe extends AbstractValidationPipe {
    transform(value, options) {
        return this.validateWithSchema(value, options, options.metadata['schema'] || this.getSchema());
    }
}
exports.ParsePipe = ParsePipe;
let DecoratorValidPipe = class DecoratorValidPipe extends ParsePipe {
};
DecoratorValidPipe = __decorate([
    (0, core_1.Pipe)()
], DecoratorValidPipe);
exports.DecoratorValidPipe = DecoratorValidPipe;
let ParseIntPipe = class ParseIntPipe extends ParsePipe {
    getSchema() {
        return Joi.number().integer().required();
    }
};
ParseIntPipe = __decorate([
    (0, core_1.Pipe)()
], ParseIntPipe);
exports.ParseIntPipe = ParseIntPipe;
let ParseBoolPipe = class ParseBoolPipe extends ParsePipe {
    getSchema() {
        return Joi.boolean().required();
    }
};
ParseBoolPipe = __decorate([
    (0, core_1.Pipe)()
], ParseBoolPipe);
exports.ParseBoolPipe = ParseBoolPipe;
let ParseFloatPipe = class ParseFloatPipe extends ParsePipe {
    getSchema() {
        return Joi.number().required();
    }
};
ParseFloatPipe = __decorate([
    (0, core_1.Pipe)()
], ParseFloatPipe);
exports.ParseFloatPipe = ParseFloatPipe;
class DefaultValuePipe {
    constructor(defaultValue) {
        this.defaultValue = defaultValue;
    }
    transform(value, options) {
        if (value === undefined ||
            value === null ||
            (typeof value === 'number' && isNaN(value))) {
            return this.defaultValue;
        }
        return value;
    }
}
exports.DefaultValuePipe = DefaultValuePipe;
//# sourceMappingURL=pipe.js.map