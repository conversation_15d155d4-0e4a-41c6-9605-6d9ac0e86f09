"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processArgsParser = exports.transformFrameworkToConfiguration = exports.createBootstrap = exports.createLightApp = exports.createFunctionApp = exports.createApp = exports.close = exports.create = void 0;
var creator_1 = require("./creator");
Object.defineProperty(exports, "create", { enumerable: true, get: function () { return creator_1.create; } });
Object.defineProperty(exports, "close", { enumerable: true, get: function () { return creator_1.close; } });
Object.defineProperty(exports, "createApp", { enumerable: true, get: function () { return creator_1.createApp; } });
Object.defineProperty(exports, "createFunctionApp", { enumerable: true, get: function () { return creator_1.createFunctionApp; } });
Object.defineProperty(exports, "createLightApp", { enumerable: true, get: function () { return creator_1.createLightApp; } });
Object.defineProperty(exports, "createBootstrap", { enumerable: true, get: function () { return creator_1.createBootstrap; } });
__exportStar(require("./client/index"), exports);
var utils_1 = require("./utils");
Object.defineProperty(exports, "transformFrameworkToConfiguration", { enumerable: true, get: function () { return utils_1.transformFrameworkToConfiguration; } });
Object.defineProperty(exports, "processArgsParser", { enumerable: true, get: function () { return utils_1.processArgsParser; } });
__exportStar(require("./mock"), exports);
//# sourceMappingURL=index.js.map