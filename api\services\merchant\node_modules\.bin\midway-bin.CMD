@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\cli\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\cli\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\cli\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\cli\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules\@midwayjs\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\@midwayjs+cli@2.1.1_@midwayjs+mock@3.20.4_@types+node@20.19.1_typescript@5.4.5\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@midwayjs\cli\bin\midway-bin.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@midwayjs\cli\bin\midway-bin.js" %*
)
