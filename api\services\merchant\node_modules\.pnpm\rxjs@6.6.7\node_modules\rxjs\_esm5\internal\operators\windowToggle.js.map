{"version": 3, "file": "windowToggle.js", "sources": ["../../../src/internal/operators/windowToggle.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAC/C,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAmD9D,MAAM,UAAU,YAAY,CAAO,QAAuB,EACvB,eAAkD;IACnF,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAO,QAAQ,EAAE,eAAe,CAAC,CAAC,EAAtE,CAAsE,CAAC;AAC3G,CAAC;AAED;IAEE,8BAAoB,QAAuB,EACvB,eAAkD;QADlD,aAAQ,GAAR,QAAQ,CAAe;QACvB,oBAAe,GAAf,eAAe,CAAmC;IACtE,CAAC;IAED,mCAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,sBAAsB,CAChD,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAChD,CAAC,CAAC;IACL,CAAC;IACH,2BAAC;AAAD,CAAC,AAXD,IAWC;AAYD;IAA2C,kDAAuB;IAIhE,gCAAY,WAAsC,EAC9B,QAAuB,EACvB,eAAkD;QAFtE,YAGE,kBAAM,WAAW,CAAC,SAEnB;QAJmB,cAAQ,GAAR,QAAQ,CAAe;QACvB,qBAAe,GAAf,eAAe,CAAmC;QAL9D,cAAQ,GAAuB,EAAE,CAAC;QAOxC,KAAI,CAAC,GAAG,CAAC,KAAI,CAAC,gBAAgB,GAAG,iBAAiB,CAAC,KAAI,EAAE,QAAQ,EAAE,QAAe,CAAC,CAAC,CAAC;;IACvF,CAAC;IAES,sCAAK,GAAf,UAAgB,KAAQ;QACd,IAAA,wBAAQ,CAAU;QAC1B,IAAI,QAAQ,EAAE;YACZ,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;gBAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC;SACF;IACH,CAAC;IAES,uCAAM,GAAhB,UAAiB,GAAQ;QAEf,IAAA,wBAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,IAAI,QAAQ,EAAE;YACZ,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YAEf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,SAAO,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBAC1B,SAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;QAED,iBAAM,MAAM,YAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAES,0CAAS,GAAnB;QACU,IAAA,wBAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,QAAQ,EAAE;YACZ,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,SAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC1B,SAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;QACD,iBAAM,SAAS,WAAE,CAAC;IACpB,CAAC;IAGD,6CAAY,GAAZ;QACU,IAAA,wBAAQ,CAAU;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,QAAQ,EAAE;YACZ,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC5B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,IAAM,SAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChC,SAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBAC7B,SAAO,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;aACpC;SACF;IACH,CAAC;IAED,2CAAU,GAAV,UAAW,UAAe,EAAE,UAAe,EAChC,UAAkB,EAAE,UAAkB,EACtC,QAAiC;QAE1C,IAAI,UAAU,KAAK,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,eAAe,SAAA,CAAC;YACpB,IAAI;gBACM,IAAA,sCAAe,CAAU;gBACjC,eAAe,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC;aAC/C;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aACtB;YAED,IAAM,QAAM,GAAG,IAAI,OAAO,EAAK,CAAC;YAChC,IAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;YACxC,IAAM,SAAO,GAAG,EAAE,MAAM,UAAA,EAAE,YAAY,cAAA,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAO,CAAC,CAAC;YAC5B,IAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,EAAE,eAAe,EAAE,SAAc,CAAC,CAAC;YAEnF,IAAI,iBAAiB,CAAC,MAAM,EAAE;gBAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC5C;iBAAM;gBACC,iBAAkB,CAAC,OAAO,GAAG,SAAO,CAAC;gBAC3C,YAAY,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;aACrC;YAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAM,CAAC,CAAC;SAC/B;aAAM;YACL,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;IACH,CAAC;IAED,4CAAW,GAAX,UAAY,GAAQ;QAClB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,CAAC;IAED,+CAAc,GAAd,UAAe,KAAmB;QAChC,IAAI,KAAK,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAQ,KAAM,CAAC,OAAO,CAAC,CAAC,CAAC;SAChE;IACH,CAAC;IAEO,4CAAW,GAAnB,UAAoB,KAAa;QAC/B,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,OAAO;SACR;QAEO,IAAA,wBAAQ,CAAU;QAC1B,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;QACxB,IAAA,uBAAM,EAAE,mCAAY,CAAa;QACzC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;QAC1B,MAAM,CAAC,QAAQ,EAAE,CAAC;QAClB,YAAY,CAAC,WAAW,EAAE,CAAC;IAC7B,CAAC;IACH,6BAAC;AAAD,CAAC,AA5HD,CAA2C,eAAe,GA4HzD"}