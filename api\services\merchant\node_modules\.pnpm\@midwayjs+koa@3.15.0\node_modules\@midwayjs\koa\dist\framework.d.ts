/// <reference types="node" />
import { BaseFramework, CommonFilterUnion, CommonMiddlewareUnion, IMidwayBootstrapOptions, MidwayFrameworkType, RouterInfo } from '@midwayjs/core';
import { IMidwayKoaApplication, IMidwayKoaConfigurationOptions, IMidwayKoaContext } from './interface';
import type { DefaultState, Middleware, Next } from 'koa';
import { Server } from 'http';
export declare class MidwayKoaFramework extends BaseFramework<IMidwayKoaApplication, IMidwayKoaContext, IMidwayKoaConfigurationOptions, Next> {
    private server;
    private generator;
    private webRouterService;
    configure(): IMidwayKoaConfigurationOptions;
    applicationInitialize(options: Partial<IMidwayBootstrapOptions>): Promise<void>;
    loadMidwayController(): Promise<void>;
    /**
     * wrap controller string to middleware function
     */
    generateController(routeInfo: RouterInfo): Middleware<DefaultState, IMidwayKoaContext>;
    /**
     * @deprecated
     * @param middlewareId
     */
    generateMiddleware(middlewareId: any): Promise<Middleware<DefaultState, IMidwayKoaContext, any>>;
    run(): Promise<void>;
    beforeStop(): Promise<void>;
    getFrameworkType(): MidwayFrameworkType;
    getFrameworkName(): string;
    getServer(): Server<typeof import("http").IncomingMessage, typeof import("http").ServerResponse>;
    getPort(): string;
    useMiddleware(Middleware: CommonMiddlewareUnion<IMidwayKoaContext, Next, unknown>): void;
    useFilter(Filter: CommonFilterUnion<IMidwayKoaContext, Next, unknown>): void;
}
//# sourceMappingURL=framework.d.ts.map