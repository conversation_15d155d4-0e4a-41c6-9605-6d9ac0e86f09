{"version": 3, "sources": ["../../src/data-source/BaseDataSourceOptions.ts"], "names": [], "mappings": "", "file": "BaseDataSourceOptions.js", "sourcesContent": ["import { EntitySchema } from \"../entity-schema/EntitySchema\"\nimport { LoggerOptions } from \"../logger/LoggerOptions\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { DatabaseType } from \"../driver/types/DatabaseType\"\nimport { Logger } from \"../logger/Logger\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryResultCache } from \"../cache/QueryResultCache\"\nimport { MixedList } from \"../common/MixedList\"\n\n/**\n * BaseDataSourceOptions is set of DataSourceOptions shared by all database types.\n */\nexport interface BaseDataSourceOptions {\n    /**\n     * Database type. This value is required.\n     */\n    readonly type: DatabaseType\n\n    /**\n     * Connection name. If connection name is not given then it will be called \"default\".\n     * Different connections must have different names.\n     *\n     * @deprecated\n     */\n    readonly name?: string\n\n    /**\n     * Entities to be loaded for this connection.\n     * Accepts both entity classes and directories where from entities need to be loaded.\n     * Directories support glob patterns.\n     */\n    readonly entities?: MixedList<Function | string | EntitySchema>\n\n    /**\n     * Subscribers to be loaded for this connection.\n     * Accepts both subscriber classes and directories where from subscribers need to be loaded.\n     * Directories support glob patterns.\n     */\n    readonly subscribers?: MixedList<Function | string>\n\n    /**\n     * Migrations to be loaded for this connection.\n     * Accepts both migration classes and glob patterns representing migration files.\n     */\n    readonly migrations?: MixedList<Function | string>\n\n    /**\n     * Migrations table name, in case of different name from \"migrations\".\n     * Accepts single string name.\n     */\n    readonly migrationsTableName?: string\n\n    /**\n     * Transaction mode for migrations to run in\n     */\n    readonly migrationsTransactionMode?: \"all\" | \"none\" | \"each\"\n\n    /**\n     * Typeorm metadata table name, in case of different name from \"typeorm_metadata\".\n     * Accepts single string name.\n     */\n    readonly metadataTableName?: string\n\n    /**\n     * Naming strategy to be used to name tables and columns in the database.\n     */\n    readonly namingStrategy?: NamingStrategyInterface\n\n    /**\n     * Logging options.\n     */\n    readonly logging?: LoggerOptions\n\n    /**\n     * Logger instance used to log queries and events in the ORM.\n     */\n    readonly logger?:\n        | \"advanced-console\"\n        | \"simple-console\"\n        | \"formatted-console\"\n        | \"file\"\n        | \"debug\"\n        | Logger\n\n    /**\n     * Maximum number of milliseconds query should be executed before logger log a warning.\n     */\n    readonly maxQueryExecutionTime?: number\n\n    /**\n     * Maximum number of clients the pool should contain.\n     */\n    readonly poolSize?: number\n\n    /**\n     * Indicates if database schema should be auto created on every application launch.\n     * Be careful with this option and don't use this in production - otherwise you can lose production data.\n     * This option is useful during debug and development.\n     * Alternative to it, you can use CLI and run schema:sync command.\n     *\n     * Note that for MongoDB database it does not create schema, because MongoDB is schemaless.\n     * Instead, it syncs just by creating indices.\n     */\n    readonly synchronize?: boolean\n\n    /**\n     * Indicates if migrations should be auto run on every application launch.\n     * Alternative to it, you can use CLI and run migrations:run command.\n     */\n    readonly migrationsRun?: boolean\n\n    /**\n     * Drops the schema each time connection is being established.\n     * Be careful with this option and don't use this in production - otherwise you'll lose all production data.\n     * This option is useful during debug and development.\n     */\n    readonly dropSchema?: boolean\n\n    /**\n     * Prefix to use on all tables (collections) of this connection in the database.\n     */\n    readonly entityPrefix?: string\n\n    /**\n     * When creating new Entity instances, skip all constructors when true.\n     */\n    readonly entitySkipConstructor?: boolean\n\n    /**\n     * Extra connection options to be passed to the underlying driver.\n     *\n     * todo: deprecate this and move all database-specific types into hts own connection options object.\n     */\n    readonly extra?: any\n\n    /**\n     * Specifies how relations must be loaded - using \"joins\" or separate queries.\n     * If you are loading too much data with nested joins it's better to load relations\n     * using separate queries.\n     *\n     * Default strategy is \"join\", but this default can be changed here.\n     * Also, strategy can be set per-query in FindOptions and QueryBuilder.\n     */\n    readonly relationLoadStrategy?: \"join\" | \"query\"\n\n    /**\n     * Optionally applied \"typename\" to the model.\n     * If set, then each hydrated model will have this property with the target model / entity name inside.\n     *\n     * (works like a discriminator property).\n     */\n    readonly typename?: string\n\n    /**\n     * Holds reference to the baseDirectory where configuration file are expected.\n     *\n     * @internal\n     */\n    baseDirectory?: string\n\n    /**\n     * Allows to setup cache options.\n     */\n    readonly cache?:\n        | boolean\n        | {\n              /**\n               * Type of caching.\n               *\n               * - \"database\" means cached values will be stored in the separate table in database. This is default value.\n               * - \"redis\" means cached values will be stored inside redis. You must provide redis connection options.\n               */\n              readonly type?:\n                  | \"database\"\n                  | \"redis\"\n                  | \"ioredis\"\n                  | \"ioredis/cluster\" // todo: add mongodb and other cache providers as well in the future\n\n              /**\n               * Factory function for custom cache providers that implement QueryResultCache.\n               */\n              readonly provider?: (connection: DataSource) => QueryResultCache\n\n              /**\n               * Configurable table name for \"database\" type cache.\n               * Default value is \"query-result-cache\"\n               */\n              readonly tableName?: string\n\n              /**\n               * Used to provide redis connection options.\n               */\n              readonly options?: any\n\n              /**\n               * If set to true then queries (using find methods and QueryBuilder's methods) will always be cached.\n               */\n              readonly alwaysEnabled?: boolean\n\n              /**\n               * Time in milliseconds in which cache will expire.\n               * This can be setup per-query.\n               * Default value is 1000 which is equivalent to 1 second.\n               */\n              readonly duration?: number\n\n              /**\n               * Used to specify if cache errors should be ignored, and pass through the call to the Database.\n               */\n              readonly ignoreErrors?: boolean\n          }\n\n    /**\n     * Allows automatic isolation of where clauses\n     */\n    readonly isolateWhereStatements?: boolean\n}\n"], "sourceRoot": ".."}