USE `merchant_service_db`;

-- 清空现有菜单数据
TRUNCATE TABLE merchant_sys_menu;

-- 插入菜单数据 (完全对应前端 asyncRoutes)
-- 一级菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(1, 0, '仪表盘', '/dashboard', '', 0, '&#xe721;', 1, 1, 1),
(2, 0, '商户管理', '/merchant', '', 0, '&#xe7b4;', 2, 1, 1),
(3, 0, '模板组件', '/template', '', 0, '&#xe860;', 3, 1, 1),
(4, 0, '小工具', '/widgets', '', 0, '&#xe81a;', 4, 1, 1),
(5, 0, '系统管理', '/system', '', 0, '&#xe7b9;', 5, 1, 1),
(6, 0, '异常页面', '/exception', '', 0, '&#xe820;', 6, 1, 1);

-- Dashboard 子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(11, 1, '控制台', '/dashboard/console', '', 1, '', 1, 0, 1),
(12, 1, '分析页', '/dashboard/analysis', '', 1, '', 2, 0, 1),
(13, 1, '电商', '/dashboard/ecommerce', '', 1, '', 3, 0, 1);

-- 商户管理子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(21, 2, '商户入驻', '/merchant/settle-in', '', 1, '', 1, 1, 1),
(22, 2, '创作者认证', '/merchant/heritage-auth', '', 1, '', 2, 1, 1),
(25, 2, '创作者管理', '/merchant/creators', '', 1, '', 3, 1, 1),
(23, 2, '个人商户', '/merchant/personal', '', 1, '', 4, 1, 1),
(24, 2, '企业商户', '/merchant/company', '', 1, '', 5, 1, 1);

-- 模板组件子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(31, 3, '卡片', '/template/cards', '', 1, '', 1, 0, 1),
(32, 3, '横幅', '/template/banners', '', 1, '', 2, 0, 1),
(33, 3, '图表', '/template/charts', '', 1, '', 3, 0, 1),
(34, 3, '地图', '/template/map', '', 1, '', 4, 1, 1),
(35, 3, '聊天', '/template/chat', '', 1, '', 5, 1, 1),
(36, 3, '日历', '/template/calendar', '', 1, '', 6, 1, 1),
(37, 3, '价格', '/template/pricing', '', 1, '', 7, 1, 1);

-- 小工具子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(41, 4, '图标列表', '/widgets/icon-list', '', 1, '', 1, 1, 1),
(42, 4, '图标选择器', '/widgets/icon-selector', '', 1, '', 2, 1, 1),
(43, 4, '图片裁剪', '/widgets/image-crop', '', 1, '', 3, 1, 1),
(44, 4, 'Excel', '/widgets/excel', '', 1, '', 4, 1, 1),
(45, 4, '视频', '/widgets/video', '', 1, '', 5, 1, 1),
(46, 4, '数字滚动', '/widgets/count-to', '', 1, '', 6, 0, 1),
(47, 4, '富文本编辑器', '/widgets/wang-editor', '', 1, '', 7, 1, 1),
(48, 4, '水印', '/widgets/watermark', '', 1, '', 8, 1, 1),
(49, 4, '右键菜单', '/widgets/context-menu', '', 1, '', 9, 1, 1),
(50, 4, '二维码', '/widgets/qrcode', '', 1, '', 10, 1, 1),
(51, 4, '拖拽', '/widgets/drag', '', 1, '', 11, 1, 1),
(52, 4, '文字滚动', '/widgets/text-scroll', '', 1, '', 12, 1, 1),
(53, 4, '烟花', '/widgets/fireworks', '', 1, '', 13, 1, 1);

-- 系统管理子菜单 (重要！菜单管理功能)
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(61, 5, '用户管理', '/system/user', '', 1, '', 1, 1, 1),
(62, 5, '角色管理', '/system/role', '', 1, '', 2, 1, 1),
(63, 5, '个人中心', '/system/user-center', '', 1, '', 3, 1, 0),
(64, 5, '菜单管理', '/system/menu', 'add,edit,delete', 1, '', 4, 1, 1);

-- 异常页面子菜单
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow) VALUES
(71, 6, '403', '/exception/403', '', 1, '', 1, 1, 1),
(72, 6, '404', '/exception/404', '', 1, '', 2, 1, 1),
(73, 6, '500', '/exception/500', '', 1, '', 3, 1, 1);

-- 验证菜单数据
SELECT '菜单数据恢复完成' AS result, COUNT(*) AS total_menus FROM merchant_sys_menu;

-- 显示菜单结构
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', name)
    ELSE CONCAT('  ├── ', name)
  END AS menu_tree,
  router,
  CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' END AS type_name
FROM merchant_sys_menu 
ORDER BY parentId, orderNum; 