
## 2.2.0

- remove lodash

## 2.1.1

- upgrade lodash

## 2.1.0

- Upgrade async
- Upgrade ini
- Merge PR security fix https://github.com/keymetrics/vizion/pull/27
- Fixed bug in SVN parsing https://github.com/keymetrics/vizion/pull/23

## 2.0.2 ( Fri Jul 20 2018 10:26:40 GMT+0200 (CEST) )


## Pull requests merged
  - Merge pull request #21 from medanat/master
  ([f9150b5f](https://github.com/keymetrics/vizion/commit/f9150b5fd38b1d65254b6416f9e7b70966592d68))




## 2.0.1 ( Mon Jul 09 2018 17:48:24 GMT+0200 (CEST) )


## Hot Fixes
  - return callback in case commit is null
  ([bcbaeba9](https://github.com/keymetrics/vizion/commit/bcbaeba9635f09c2c1f426eee9697d08b5b3942b))




## 2.0.0 ( Mon Jul 09 2018 17:48:24 GMT+0200 (CEST) )


## Bug Fixes
  - fix cliCommand test
  ([00e4d95c](https://github.com/keymetrics/vizion/commit/00e4d95c8232c1e214b9e9815bb6884eb4173ea5))
  - fix svn / hg test path issue
  ([6f0860cd](https://github.com/keymetrics/vizion/commit/6f0860cd9e46bc75d28a28396f4d27f0e38f84b9))
  - fix typo
  ([228470ea](https://github.com/keymetrics/vizion/commit/228470ea9e540c046311f36629f34e1b989a723a))
  - fix tests - upgrade async
  ([989a3ccb](https://github.com/keymetrics/vizion/commit/989a3ccbf44ed9538f9738dc09565f2df09cf631))
  - fix
  ([7486bdfd](https://github.com/keymetrics/vizion/commit/7486bdfd4c947a5a7c2d8ee17662b116a249ff25))
  - fix
  ([2c42c264](https://github.com/keymetrics/vizion/commit/2c42c264d13230abf25c4a0b605550aa2c903320))
  - fix
  ([31e1f81f](https://github.com/keymetrics/vizion/commit/31e1f81f0ca6d8654dfc0e88a6d267c6547797f8))




## Refactor
  - refactor isUpdated
  ([5f9b2fc5](https://github.com/keymetrics/vizion/commit/5f9b2fc5d29ee5f4a2cc949d61832a04747f84e9))
  - refactor tests + start refactoring git.js
  ([18112787](https://github.com/keymetrics/vizion/commit/1811278792bd03c911707d9694c06e5258a255e8))
  - refactored some code for clarity purposes
  ([10cf45cb](https://github.com/keymetrics/vizion/commit/10cf45cb46e44479ba9ba0e7772e441d1fa9ce7b))
  - refactoring code using now async
  ([0c8dad10](https://github.com/keymetrics/vizion/commit/0c8dad10ca87be646fd403136df134e6db859543))




## Test
  - remove unused test_git folder
  ([dfb3bb21](https://github.com/keymetrics/vizion/commit/dfb3bb21cb2c86d84dfacfd605742d50b8ea8d62))
  - tests hotfix
  ([7f699a0f](https://github.com/keymetrics/vizion/commit/7f699a0fc0f08a98004b17cdd64fb5429d3c50e9))
  - test
  ([fdbcc56f](https://github.com/keymetrics/vizion/commit/fdbcc56ff68a1a4aae077593b32ab7c89f8b9132))
  - tests
  ([0e335b1b](https://github.com/keymetrics/vizion/commit/0e335b1b34c72ebbfdfa9122fbcb5edcbcc58099))




## Chore
  - update package version to 2.0.0
  ([1c712900](https://github.com/keymetrics/vizion/commit/1c712900f761af75b64095ca9d64a2390b0bdd69))




## Pull requests merged
  - Merge pull request #19 from keymetrics/refacto
  ([70780da3](https://github.com/keymetrics/vizion/commit/70780da3bf7cc7a842866080734e6433acc3f7fb))
  - Merge pull request #18 from dthdyver/master
  ([4d96e6f2](https://github.com/keymetrics/vizion/commit/4d96e6f2aed995c7eac39a81b7aa276e8ca59399))
  - Merge pull request #14 from didil/master
  ([3942cd8a](https://github.com/keymetrics/vizion/commit/3942cd8a59b0f923f4ab91849b7534c3164fdf05))
  - Merge pull request #10 from keymetrics/cut_output
  ([9242d3e4](https://github.com/keymetrics/vizion/commit/9242d3e4160d07cb071a6a8c53b01aac283215da))
  - Merge pull request #9 from keymetrics/fix_local_branch
  ([8ead4616](https://github.com/keymetrics/vizion/commit/8ead461674c377b86f9e972c513678a924d96347))
  - Merge pull request #8 from keymetrics/git_timeout
  ([073c9e52](https://github.com/keymetrics/vizion/commit/073c9e52ab0ebddb08f8ece4f914081561eff6a2))
  - Merge pull request #5 from josser/master
  ([aefcc36b](https://github.com/keymetrics/vizion/commit/aefcc36bd3858d209d6ab84001b6cbca64e19e26))




# 0.2.12

- Increase buffer size
- Upgrade .travis.yml
