"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectCreator = void 0;
const error_1 = require("../error");
const types_1 = require("../util/types");
class ObjectCreator {
    constructor(definition) {
        this.definition = definition;
    }
    /**
     * 加载对象class
     * @returns {class} Clzz对象的Class
     */
    load() {
        let Clzz = null;
        if (typeof this.definition.path === 'string') {
            // 解析xml结果 默认 path = '' 需要兼容处理掉
            if (!this.definition.path) {
                return Clzz;
            }
            const path = this.definition.path;
            if (this.definition.export) {
                Clzz = require(path)[this.definition.export];
            }
            else {
                Clzz = require(path);
            }
        }
        else {
            // if it is class and return direct
            Clzz = this.definition.path;
        }
        return Clzz;
    }
    /**
     * 构建对象实例
     * @param Clzz 对象class，通过load加载
     * @param args 对象初始化参数
     * @returns {any} 返回创建的对象实例
     */
    doConstruct(Clzz, args) {
        if (!Clzz) {
            return Object.create(null);
        }
        let inst;
        if (this.definition.constructMethod) {
            // eslint-disable-next-line prefer-spread
            inst = Clzz[this.definition.constructMethod].apply(Clzz, args);
        }
        else {
            inst = Reflect.construct(Clzz, args);
        }
        return inst;
    }
    /**
     * 异步构造对象
     * @param Clzz 对象class，通过load加载
     * @param args 对象初始化参数
     * @returns {any} 返回创建的对象实例
     */
    async doConstructAsync(Clzz, args) {
        if (!Clzz) {
            return Object.create(null);
        }
        let inst;
        if (this.definition.constructMethod) {
            const fn = Clzz[this.definition.constructMethod];
            if (types_1.Types.isAsyncFunction(fn)) {
                inst = await fn.apply(Clzz, args);
            }
            else {
                inst = fn.apply(Clzz, args);
            }
        }
        else {
            inst = Reflect.construct(Clzz, args);
        }
        return inst;
    }
    /**
     * 调用对象初始化方法进行初始化
     * @param obj 对象，由doConstruct返回
     * @returns {void}
     */
    doInit(obj) {
        const inst = obj;
        // after properties set then do init
        if (this.definition.initMethod && inst[this.definition.initMethod]) {
            if (types_1.Types.isGeneratorFunction(inst[this.definition.initMethod]) ||
                types_1.Types.isAsyncFunction(inst[this.definition.initMethod])) {
                throw new error_1.MidwayUseWrongMethodError('context.get', 'context.getAsync', this.definition.id);
            }
            else {
                const rt = inst[this.definition.initMethod].call(inst);
                if (types_1.Types.isPromise(rt)) {
                    throw new error_1.MidwayUseWrongMethodError('context.get', 'context.getAsync', this.definition.id);
                }
            }
        }
    }
    /**
     * 调用对象初始化方法进行初始化
     * @param obj 对象，由doConstructAsync返回
     * @returns {void}
     */
    async doInitAsync(obj) {
        const inst = obj;
        if (this.definition.initMethod && inst[this.definition.initMethod]) {
            const initFn = inst[this.definition.initMethod];
            if (types_1.Types.isAsyncFunction(initFn)) {
                await initFn.call(inst);
            }
            else {
                if (initFn.length === 1) {
                    await new Promise(resolve => {
                        initFn.call(inst, resolve);
                    });
                }
                else {
                    initFn.call(inst);
                }
            }
        }
    }
    /**
     * 对象销毁
     * @param obj 对象，由doConstruct返回
     * @returns {void}
     */
    doDestroy(obj) {
        if (this.definition.destroyMethod && obj[this.definition.destroyMethod]) {
            obj[this.definition.destroyMethod].call(obj);
        }
    }
    /**
     * 对象销毁
     * @param obj 对象，由doConstructAsync返回
     * @returns {void}
     */
    async doDestroyAsync(obj) {
        if (this.definition.destroyMethod && obj[this.definition.destroyMethod]) {
            const fn = obj[this.definition.destroyMethod];
            if (types_1.Types.isAsyncFunction(fn)) {
                await fn.call(obj);
            }
            else {
                if (fn.length === 1) {
                    await new Promise(resolve => {
                        fn.call(obj, resolve);
                    });
                }
                else {
                    fn.call(obj);
                }
            }
        }
    }
}
exports.ObjectCreator = ObjectCreator;
//# sourceMappingURL=objectCreator.js.map