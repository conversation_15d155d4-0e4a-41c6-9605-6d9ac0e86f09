"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuildPlugin = void 0;
const command_core_1 = require("@midwayjs/command-core");
const path_1 = require("path");
const fs_extra_1 = require("fs-extra");
const globby = require("globby");
const os_1 = require("os");
class BuildPlugin extends command_core_1.BasePlugin {
    constructor() {
        super(...arguments);
        this.isMidwayHooks = false;
        this.midwayBinBuild = {};
        this.midwayCliConfig = {};
        this.commands = {
            build: {
                lifecycleEvents: [
                    'formatOptions',
                    'clean',
                    'copyFile',
                    'compile',
                    'bundle',
                    'complete',
                ],
                options: {
                    clean: {
                        usage: 'clean build target dir',
                        shortcut: 'c',
                    },
                    project: {
                        usage: 'project file location',
                        shortcut: 'p',
                    },
                    srcDir: {
                        usage: 'source code path',
                    },
                    outDir: {
                        usage: 'build out path',
                    },
                    tsConfig: {
                        usage: 'json string / file path / object',
                    },
                    buildCache: {
                        usage: 'save build cache',
                    },
                    exclude: {
                        usage: 'copy file exclude',
                    },
                    include: {
                        usage: 'copy file include',
                    },
                    bundle: {
                        usage: 'bundle to one file',
                    },
                },
            },
        };
        this.hooks = {
            'build:formatOptions': this.formatOptions.bind(this),
            'build:clean': this.clean.bind(this),
            'build:copyFile': this.copyFile.bind(this),
            'build:compile': this.compile.bind(this),
            'build:bundle': this.bundle.bind(this),
            'build:complete': this.complete.bind(this),
        };
    }
    async formatOptions() {
        const config = (0, command_core_1.resolveMidwayConfig)(this.core.cwd);
        if (config.exist) {
            this.isMidwayHooks = true;
            this.options.srcDir = config.source;
        }
        const packageJsonPath = (0, path_1.join)(this.core.cwd, 'package.json');
        if ((0, fs_extra_1.existsSync)(packageJsonPath)) {
            const pkgJson = JSON.parse((0, fs_extra_1.readFileSync)(packageJsonPath).toString());
            this.midwayBinBuild = pkgJson['midway-bin-build'] || {};
            this.midwayCliConfig = pkgJson['midway-cli'] || {};
        }
    }
    async clean() {
        if (!this.options.clean) {
            return;
        }
        const outdir = this.getOutDir();
        if ((0, fs_extra_1.existsSync)(outdir)) {
            await (0, fs_extra_1.remove)(outdir);
        }
    }
    getOutDir() {
        if (this.options.outDir) {
            return this.options.outDir;
        }
        const tsConfig = this.getTsConfig();
        this.core.debug('TSConfig', tsConfig);
        const projectFile = this.getProjectFile();
        this.core.debug('ProjectFile', projectFile);
        return (this.getCompilerOptions(tsConfig, 'outDir', (0, path_1.dirname)(projectFile)) ||
            'dist');
    }
    async copyFile() {
        const outDir = this.getOutDir();
        this.core.debug('CopyFile TargetDir', outDir);
        const exclude = this.options.exclude ? this.options.exclude.split(',') : [];
        const sourceDir = (0, path_1.join)(this.core.cwd, this.options.srcDir || 'src');
        const targetDir = (0, path_1.join)(this.core.cwd, outDir);
        await (0, command_core_1.copyFiles)({
            sourceDir,
            targetDir,
            defaultInclude: this.midwayBinBuild.include
                ? this.midwayBinBuild.include
                : ['**/*'],
            exclude: ['**/*.ts', '**/*.js'].concat(exclude),
            log: path => {
                this.core.cli.log(`   - Copy ${path}`);
            },
        });
        // midway core DEFAULT_IGNORE_PATTERN
        let include = [
            '**/public/**/*.js',
            '**/view/**/*.js',
            '**/views/**/*.js',
            '**/app/extend/**/*.js',
        ];
        if (this.options.include !== undefined) {
            include = this.options.include ? this.options.include.split(',') : [];
        }
        if (include.length) {
            await (0, command_core_1.copyFiles)({
                sourceDir,
                targetDir,
                defaultInclude: include,
                exclude,
                log: path => {
                    this.core.cli.log(`   - Copy ${path}`);
                },
            });
        }
        this.core.cli.log('   - Copy Complete');
    }
    async compile() {
        var _a, _b;
        const outDir = this.getOutDir();
        this.core.debug('outDir', outDir, this.midwayCliConfig);
        const { cwd } = this.core;
        const { errors, necessaryErrors } = await (0, command_core_1.compileTypeScript)({
            baseDir: cwd,
            tsOptions: this.getTsConfig(),
            sourceDir: this.getTsCodeRoot(),
        });
        if (errors.length) {
            for (const error of errors) {
                this.core.cli.error(`\n[TS Error] ${error.message} (${error.path})`);
            }
            if (necessaryErrors.length &&
                !((_b = (_a = this.midwayCliConfig) === null || _a === void 0 ? void 0 : _a.experimentalFeatures) === null || _b === void 0 ? void 0 : _b.ignoreTsError)) {
                throw new Error(`Error: ${necessaryErrors.length} ts error that must be fixed!`);
            }
        }
    }
    getCompilerOptions(tsConfig, optionKeyPath, projectDir) {
        var _a;
        // if projectFile extended and without the option,
        // get setting from its parent
        if (tsConfig && tsConfig.extends) {
            if (!tsConfig.compilerOptions ||
                (tsConfig.compilerOptions && !tsConfig.compilerOptions[optionKeyPath])) {
                return this.getCompilerOptions(require((0, path_1.join)(projectDir, tsConfig.extends)), optionKeyPath, (0, path_1.dirname)((0, path_1.join)(projectDir, tsConfig.extends)));
            }
        }
        return (_a = tsConfig === null || tsConfig === void 0 ? void 0 : tsConfig.compilerOptions) === null || _a === void 0 ? void 0 : _a[optionKeyPath];
    }
    getProjectFile() {
        const { cwd } = this.core;
        const { project } = this.options;
        return (0, path_1.resolve)(cwd, project || 'tsconfig.json');
    }
    getTsCodeRoot() {
        return (0, path_1.resolve)(this.core.cwd, this.options.srcDir || 'src');
    }
    getTsConfig() {
        const { cwd } = this.core;
        this.core.debug('CWD', cwd);
        let { tsConfig } = this.options;
        let tsConfigResult;
        if (typeof tsConfig === 'string') {
            // if ts config is file
            if ((0, fs_extra_1.existsSync)(tsConfig)) {
                tsConfig = (0, fs_extra_1.readFileSync)(tsConfig).toString();
            }
            try {
                tsConfigResult = JSON.parse(tsConfig);
            }
            catch (e) {
                this.core.cli.error('[midway-bin] tsConfig should be JSON string or Object');
                throw e;
            }
        }
        const projectFile = this.getProjectFile();
        if (!tsConfigResult) {
            if (!(0, fs_extra_1.existsSync)(projectFile)) {
                this.core.cli.error(`[ Midway ] tsconfig.json not found in ${cwd}\n`);
                throw new Error('tsconfig.json not found');
            }
            try {
                tsConfigResult = JSON.parse((0, fs_extra_1.readFileSync)(projectFile, 'utf-8').toString());
            }
            catch (e) {
                this.core.cli.error('[ Midway ] Read TsConfig Error', e.message);
                throw e;
            }
        }
        return tsConfigResult;
    }
    async bundle() {
        if (!this.options.bundle) {
            return;
        }
        const nccPkgJsonFile = require.resolve('@vercel/ncc/package');
        const nccPkgJson = JSON.parse((0, fs_extra_1.readFileSync)(nccPkgJsonFile).toString());
        const nccCli = (0, path_1.join)(nccPkgJsonFile, '../', nccPkgJson.bin.ncc);
        const outDir = (0, path_1.join)(this.core.cwd, this.getOutDir());
        let preloadCode = '// midway bundle';
        const preloadFile = 'midway_bundle_entry.js';
        const requireList = await globby(['**/*.js'], {
            cwd: outDir,
        });
        preloadCode += requireList
            .map((file, index) => {
            return `require('./${file}');`;
        })
            .join('\n');
        const configurationFilePath = (0, path_1.join)(outDir, 'configuration.js');
        if ((0, fs_extra_1.existsSync)(configurationFilePath)) {
            preloadCode += `
      const configuration = require('./configuration.js');
      if (typeof configuration === 'object') {
        const configurationKey = Object.keys(configuration).find(key => typeof configuration[key] === 'function');
        if (configurationKey) {
          exports.configuration = configuration[configurationKey];
        }
      } else {
        exports.configuration = configuration;
      }
      `;
        }
        (0, fs_extra_1.writeFileSync)((0, path_1.join)(outDir, preloadFile), preloadCode);
        this.core.cli.log('Build bundle...');
        await (0, command_core_1.forkNode)(nccCli, ['build', preloadFile, '-o', 'ncc_build_tmp', '-m'], {
            cwd: outDir,
        });
        const tmpFile = (0, path_1.join)((0, os_1.tmpdir)(), `midway_bundle_${Date.now()}.js`);
        await (0, fs_extra_1.move)((0, path_1.join)(outDir, 'ncc_build_tmp/index.js'), tmpFile);
        await (0, fs_extra_1.remove)(outDir);
        await (0, fs_extra_1.move)(tmpFile, (0, path_1.join)(outDir, 'bundle.js'));
        await (0, fs_extra_1.remove)(tmpFile);
        this.core.cli.log(`Success compile to ${(0, path_1.relative)(process.cwd(), (0, path_1.join)(outDir, 'bundle.js'))}.`);
        this.core.cli.log('You can use it through the configurationModule parameter in the bootstrap file.');
    }
    async complete() {
        this.core.cli.log('Build Complete!');
    }
}
exports.BuildPlugin = BuildPlugin;
//# sourceMappingURL=index.js.map