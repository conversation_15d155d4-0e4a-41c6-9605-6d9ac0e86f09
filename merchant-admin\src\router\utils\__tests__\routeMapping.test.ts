/**
 * 路由映射系统测试
 */
import { describe, it, expect, beforeEach } from 'vitest'
import { RouteMapper, routeMappingUtils } from '../routeMapping'
import type { AppRouteRecord } from '@/types/router'

describe('RouteMapper', () => {
  let routeMapper: RouteMapper

  beforeEach(() => {
    // 获取新的实例进行测试
    routeMapper = RouteMapper.getInstance()
  })

  describe('基础功能', () => {
    it('应该能够根据路由标识符获取路由配置', () => {
      const route = routeMapper.getRouteByKey('settlement.batch')
      
      expect(route).toBeDefined()
      expect(route?.routeKey).toBe('settlement.batch')
      expect(route?.name).toBe('SettlementBatch')
      expect(route?.defaultPath).toBe('/settlement/batch-settlement')
    })

    it('应该能够根据路径获取路由标识符', () => {
      const routeKey = routeMapper.getRouteKeyByPath('/settlement/batch-settlement')
      
      expect(routeKey).toBe('settlement.batch')
    })

    it('应该能够根据路由标识符获取当前路径', () => {
      const path = routeMapper.getPathByRouteKey('settlement.batch')
      
      expect(path).toBe('/settlement/batch-settlement')
    })

    it('应该能够检查路由是否存在', () => {
      expect(routeMapper.hasRoute('settlement.batch')).toBe(true)
      expect(routeMapper.hasRoute('non.existent')).toBe(false)
    })
  })

  describe('路由映射更新', () => {
    it('应该能够更新单个路由映射', () => {
      const newPath = '/merchant/batch-settlement'
      
      routeMapper.updateRouteMapping('settlement.batch', newPath)
      
      const currentPath = routeMapper.getPathByRouteKey('settlement.batch')
      expect(currentPath).toBe(newPath)
      
      const routeKey = routeMapper.getRouteKeyByPath(newPath)
      expect(routeKey).toBe('settlement.batch')
    })

    it('应该能够批量更新路由映射', () => {
      const menuList: AppRouteRecord[] = [
        {
          name: 'MerchantManagement',
          path: '/merchant',
          meta: { title: '商户管理' },
          children: [
            {
              name: 'SettlementBatch',
              path: '/merchant/batch-settlement',
              meta: { 
                title: '批量结算',
                routeKey: 'settlement.batch'
              }
            }
          ]
        }
      ]

      routeMapper.batchUpdateMappings(menuList)
      
      const currentPath = routeMapper.getPathByRouteKey('settlement.batch')
      expect(currentPath).toBe('/merchant/batch-settlement')
    })
  })

  describe('路径构建', () => {
    it('应该正确处理嵌套路径', () => {
      const menuList: AppRouteRecord[] = [
        {
          name: 'System',
          path: '/system',
          meta: { title: '系统管理' },
          children: [
            {
              name: 'UserManagement',
              path: '/system/user',
              meta: { 
                title: '用户管理',
                routeKey: 'system.user'
              },
              children: [
                {
                  name: 'UserList',
                  path: '/system/user/list',
                  meta: { 
                    title: '用户列表',
                    routeKey: 'system.user.list'
                  }
                }
              ]
            }
          ]
        }
      ]

      routeMapper.batchUpdateMappings(menuList)
      
      const userPath = routeMapper.getPathByRouteKey('system.user')
      const userListPath = routeMapper.getPathByRouteKey('system.user.list')
      
      expect(userPath).toBe('/system/user')
      expect(userListPath).toBe('/system/user/list')
    })

    it('应该正确处理绝对路径', () => {
      const menuList: AppRouteRecord[] = [
        {
          name: 'Dashboard',
          path: '/dashboard',
          meta: { 
            title: '仪表盘',
            routeKey: 'dashboard'
          }
        }
      ]

      routeMapper.batchUpdateMappings(menuList)
      
      const path = routeMapper.getPathByRouteKey('dashboard')
      expect(path).toBe('/dashboard')
    })
  })

  describe('错误处理', () => {
    it('应该正确处理不存在的路由标识符', () => {
      const route = routeMapper.getRouteByKey('non.existent')
      expect(route).toBeNull()
      
      const path = routeMapper.getPathByRouteKey('non.existent')
      expect(path).toBeNull()
    })

    it('应该正确处理不存在的路径', () => {
      const routeKey = routeMapper.getRouteKeyByPath('/non/existent/path')
      expect(routeKey).toBeNull()
    })
  })
})

describe('routeMappingUtils', () => {
  describe('createRouteFromKey', () => {
    it('应该能够根据路由标识符创建路由配置', () => {
      const route = routeMappingUtils.createRouteFromKey('settlement.batch')
      
      expect(route).toBeDefined()
      expect(route?.name).toBe('SettlementBatch')
      expect(route?.path).toBe('/settlement/batch-settlement')
      expect(route?.meta?.routeKey).toBe('settlement.batch')
    })

    it('应该能够使用自定义路径创建路由配置', () => {
      const customPath = '/custom/path'
      const route = routeMappingUtils.createRouteFromKey('settlement.batch', customPath)
      
      expect(route?.path).toBe(customPath)
    })

    it('应该正确处理不存在的路由标识符', () => {
      const route = routeMappingUtils.createRouteFromKey('non.existent')
      expect(route).toBeNull()
    })
  })

  describe('checkRedirect', () => {
    it('应该检测需要重定向的路径', () => {
      // 先更新映射，模拟菜单结构调整
      const routeMapper = RouteMapper.getInstance()
      routeMapper.updateRouteMapping('settlement.batch', '/merchant/batch-settlement')
      
      const redirectPath = routeMappingUtils.checkRedirect('/settlement/batch-settlement')
      expect(redirectPath).toBe('/merchant/batch-settlement')
    })

    it('应该正确处理不需要重定向的路径', () => {
      const redirectPath = routeMappingUtils.checkRedirect('/settlement/batch-settlement')
      // 如果当前路径就是映射的路径，不需要重定向
      expect(redirectPath).toBeNull()
    })

    it('应该正确处理不存在的路径', () => {
      const redirectPath = routeMappingUtils.checkRedirect('/non/existent/path')
      expect(redirectPath).toBeNull()
    })
  })
})

describe('集成测试', () => {
  it('应该完整支持菜单结构调整场景', () => {
    const routeMapper = RouteMapper.getInstance()
    
    // 1. 初始状态：批量结算在财务结算下
    expect(routeMapper.getPathByRouteKey('settlement.batch')).toBe('/settlement/batch-settlement')
    
    // 2. 模拟菜单结构调整：将批量结算移动到商户管理下
    const adjustedMenuList: AppRouteRecord[] = [
      {
        name: 'MerchantManagement',
        path: '/merchant',
        meta: { title: '商户管理' },
        children: [
          {
            name: 'SettlementBatch',
            path: '/merchant/batch-settlement',
            meta: { 
              title: '批量结算',
              routeKey: 'settlement.batch'  // 路由标识符不变
            }
          }
        ]
      }
    ]
    
    routeMapper.batchUpdateMappings(adjustedMenuList)
    
    // 3. 验证新路径
    expect(routeMapper.getPathByRouteKey('settlement.batch')).toBe('/merchant/batch-settlement')
    
    // 4. 验证重定向检查
    const redirectPath = routeMappingUtils.checkRedirect('/settlement/batch-settlement')
    expect(redirectPath).toBe('/merchant/batch-settlement')
    
    // 5. 验证新路径可以正常访问
    const routeKey = routeMapper.getRouteKeyByPath('/merchant/batch-settlement')
    expect(routeKey).toBe('settlement.batch')
  })
})
