"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getCurrentAsyncContextManager = exports.getCurrentMainApp = exports.getCurrentMainFramework = exports.getCurrentApplicationContext = void 0;
const constants_1 = require("../constants");
const getCurrentApplicationContext = () => {
    return global['MIDWAY_APPLICATION_CONTEXT'];
};
exports.getCurrentApplicationContext = getCurrentApplicationContext;
const getCurrentMainFramework = () => {
    return global['MIDWAY_MAIN_FRAMEWORK'];
};
exports.getCurrentMainFramework = getCurrentMainFramework;
const getCurrentMainApp = () => {
    const framework = (0, exports.getCurrentMainFramework)();
    if (framework) {
        return framework.getApplication();
    }
    return undefined;
};
exports.getCurrentMainApp = getCurrentMainApp;
const getCurrentAsyncContextManager = () => {
    return (0, exports.getCurrentApplicationContext)().get(constants_1.ASYNC_CONTEXT_MANAGER_KEY);
};
exports.getCurrentAsyncContextManager = getCurrentAsyncContextManager;
//# sourceMappingURL=contextUtil.js.map