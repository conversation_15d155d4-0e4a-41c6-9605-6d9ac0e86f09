<template>
  <div class="analytics-overview">
    <!-- 时间筛选 -->
    <div class="custom-card art-custom-card filter-card">
      <div class="custom-card-content">
        <el-form :inline="true">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 300px"
              @change="handleDateChange"
            />
          </el-form-item>
          <el-form-item label="商户类型">
            <el-select v-model="merchantType" placeholder="选择类型" style="width: 120px" @change="handleTypeChange">
              <el-option label="全部" value="" />
              <el-option label="个人商户" value="personal" />
              <el-option label="企业商户" value="enterprise" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="refreshData">
              <el-icon><Refresh /></el-icon>
              刷新数据
            </el-button>
            <el-button @click="exportReport">
              <el-icon><Download /></el-icon>
              导出报表
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="custom-card art-custom-card metric-card">
            <div class="custom-card-content">
              <div class="metric-item">
                <div class="metric-icon gmv">
                  <el-icon><Money /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">¥{{ formatNumber(totalGMV) }}</div>
                  <div class="metric-label">总GMV</div>
                  <div class="metric-trend up">
                    <el-icon><ArrowUp /></el-icon>
                    +12.5%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card metric-card">
            <div class="custom-card-content">
              <div class="metric-item">
                <div class="metric-icon orders">
                  <el-icon><Document /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ formatNumber(totalOrders) }}</div>
                  <div class="metric-label">总订单数</div>
                  <div class="metric-trend up">
                    <el-icon><ArrowUp /></el-icon>
                    +8.3%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card metric-card">
            <div class="custom-card-content">
              <div class="metric-item">
                <div class="metric-icon merchants">
                  <el-icon><Shop /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">{{ formatNumber(activeMerchants) }}</div>
                  <div class="metric-label">活跃商户</div>
                  <div class="metric-trend down">
                    <el-icon><ArrowDown /></el-icon>
                    -2.1%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card metric-card">
            <div class="custom-card-content">
              <div class="metric-item">
                <div class="metric-icon avg-order">
                  <el-icon><TrendCharts /></el-icon>
                </div>
                <div class="metric-content">
                  <div class="metric-value">¥{{ formatNumber(avgOrderValue) }}</div>
                  <div class="metric-label">客单价</div>
                  <div class="metric-trend up">
                    <el-icon><ArrowUp /></el-icon>
                    +5.7%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <div class="custom-card art-custom-card">
          <div class="custom-card-header">
            <h3>GMV趋势分析</h3>
            <el-radio-group v-model="gmvPeriod" size="small" @change="updateGMVChart">
              <el-radio-button label="7d">近7天</el-radio-button>
              <el-radio-button label="30d">近30天</el-radio-button>
              <el-radio-button label="90d">近90天</el-radio-button>
            </el-radio-group>
          </div>
          <div class="custom-card-content">
            <div ref="gmvChart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="custom-card art-custom-card">
          <div class="custom-card-header">
            <h3>商户类型分布</h3>
          </div>
          <div class="custom-card-content">
            <div ref="merchantTypeChart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <div class="custom-card art-custom-card">
          <div class="custom-card-header">
            <h3>商户活跃度分析</h3>
          </div>
          <div class="custom-card-content">
            <div ref="activityChart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="custom-card art-custom-card">
          <div class="custom-card-header">
            <h3>地域分布TOP10</h3>
          </div>
          <div class="custom-card-content">
            <div ref="regionChart" style="height: 300px;"></div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 商户排行榜 -->
    <div class="custom-card art-custom-card" style="margin-top: 20px;">
      <div class="custom-card-header">
        <h3>商户表现排行榜</h3>
        <el-radio-group v-model="rankingType" size="small" @change="updateRanking">
          <el-radio-button label="gmv">GMV排行</el-radio-button>
          <el-radio-button label="orders">订单量排行</el-radio-button>
          <el-radio-button label="growth">增长率排行</el-radio-button>
        </el-radio-group>
      </div>
      <div class="custom-card-content">
        <el-table :data="rankingData" v-loading="rankingLoading">
          <el-table-column label="排名" width="80">
            <template #default="{ $index }">
              <div class="ranking-number" :class="getRankingClass($index)">
                {{ $index + 1 }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="merchantName" label="商户名称" min-width="150" />
          <el-table-column prop="type" label="类型" width="80">
            <template #default="{ row }">
              <el-tag :type="row.type === 'personal' ? 'success' : 'primary'" size="small">
                {{ row.type === 'personal' ? '个人' : '企业' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="category" label="类目" width="120" />
          <el-table-column prop="gmv" label="GMV" width="120">
            <template #default="{ row }">
              ¥{{ formatNumber(row.gmv) }}
            </template>
          </el-table-column>
          <el-table-column prop="orders" label="订单数" width="100" />
          <el-table-column prop="growth" label="增长率" width="100">
            <template #default="{ row }">
              <span :class="row.growth >= 0 ? 'growth-positive' : 'growth-negative'">
                {{ row.growth >= 0 ? '+' : '' }}{{ row.growth }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="region" label="地区" width="100" />
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="viewMerchantDetail(row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Download, Money, Document, Shop, TrendCharts, ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 筛选条件
const dateRange = ref([])
const merchantType = ref('')
const gmvPeriod = ref('30d')
const rankingType = ref('gmv')

// 核心指标数据
const totalGMV = ref(12580000)
const totalOrders = ref(45680)
const activeMerchants = ref(1580)
const avgOrderValue = ref(275)

// 图表引用
const gmvChart = ref()
const merchantTypeChart = ref()
const activityChart = ref()
const regionChart = ref()

// 排行榜数据
const rankingData = ref([
  {
    merchantName: '张三手工艺品店',
    type: 'personal',
    category: '手工艺品',
    gmv: 580000,
    orders: 2340,
    growth: 25.6,
    region: '北京'
  },
  {
    merchantName: '李四非遗工坊',
    type: 'enterprise',
    category: '非遗传承',
    gmv: 520000,
    orders: 1890,
    growth: 18.3,
    region: '上海'
  },
  {
    merchantName: '王五文创店',
    type: 'personal',
    category: '文创产品',
    gmv: 480000,
    orders: 2100,
    growth: -5.2,
    region: '广州'
  }
])
const rankingLoading = ref(false)

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

// 获取排名样式
const getRankingClass = (index: number) => {
  if (index === 0) return 'rank-first'
  if (index === 1) return 'rank-second'
  if (index === 2) return 'rank-third'
  return ''
}

// 初始化GMV趋势图表
const initGMVChart = () => {
  const chart = echarts.init(gmvChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: ['1/12', '1/13', '1/14', '1/15', '1/16', '1/17', '1/18']
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => {
          return value >= 10000 ? (value / 10000) + '万' : value
        }
      }
    },
    series: [
      {
        name: 'GMV',
        type: 'line',
        smooth: true,
        data: [180000, 195000, 210000, 185000, 220000, 235000, 250000],
        itemStyle: { color: '#409EFF' },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0, y: 0, x2: 0, y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
              { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
            ]
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化商户类型分布图表
const initMerchantTypeChart = () => {
  const chart = echarts.init(merchantTypeChart.value)
  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        data: [
          { value: 980, name: '个人商户', itemStyle: { color: '#67C23A' } },
          { value: 600, name: '企业商户', itemStyle: { color: '#409EFF' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化活跃度分析图表
const initActivityChart = () => {
  const chart = echarts.init(activityChart.value)
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['日活跃', '周活跃', '月活跃']
    },
    xAxis: {
      type: 'category',
      data: ['1/12', '1/13', '1/14', '1/15', '1/16', '1/17', '1/18']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '日活跃',
        type: 'bar',
        data: [320, 332, 301, 334, 390, 330, 320],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '周活跃',
        type: 'bar',
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        itemStyle: { color: '#67C23A' }
      },
      {
        name: '月活跃',
        type: 'bar',
        data: [1200, 1320, 1180, 1340, 1580, 1560, 1580],
        itemStyle: { color: '#E6A23C' }
      }
    ]
  }
  chart.setOption(option)
}

// 初始化地域分布图表
const initRegionChart = () => {
  const chart = echarts.init(regionChart.value)
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'value'
    },
    yAxis: {
      type: 'category',
      data: ['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安', '南京', '重庆']
    },
    series: [
      {
        type: 'bar',
        data: [180, 165, 150, 135, 120, 110, 95, 85, 75, 65],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#188df0' }
          ])
        }
      }
    ]
  }
  chart.setOption(option)
}

// 更新GMV图表
const updateGMVChart = () => {
  ElMessage.info(`切换到${gmvPeriod.value}数据`)
}

// 更新排行榜
const updateRanking = () => {
  rankingLoading.value = true
  setTimeout(() => {
    rankingLoading.value = false
    ElMessage.success(`已切换到${rankingType.value}排行`)
  }, 1000)
}

// 时间变化
const handleDateChange = () => {
  refreshData()
}

// 类型变化
const handleTypeChange = () => {
  refreshData()
}

// 刷新数据
const refreshData = () => {
  ElMessage.success('数据已刷新')
}

// 导出报表
const exportReport = () => {
  ElMessage.info('正在导出报表...')
}

// 查看商户详情
const viewMerchantDetail = (row: any) => {
  ElMessage.info(`查看商户详情：${row.merchantName}`)
}

onMounted(() => {
  nextTick(() => {
    initGMVChart()
    initMerchantTypeChart()
    initActivityChart()
    initRegionChart()
  })
})
</script>

<style scoped lang="scss">
.analytics-overview {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.metrics-cards {
  margin-bottom: 20px;
}

.metric-card {
  .metric-item {
    display: flex;
    align-items: center;
    padding: 20px;

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;
      font-size: 24px;
      color: white;

      &.gmv {
        background: linear-gradient(135deg, #67c23a, #85ce61);
      }

      &.orders {
        background: linear-gradient(135deg, #409eff, #66b1ff);
      }

      &.merchants {
        background: linear-gradient(135deg, #e6a23c, #ebb563);
      }

      &.avg-order {
        background: linear-gradient(135deg, #f56c6c, #f78989);
      }
    }

    .metric-content {
      flex: 1;

      .metric-value {
        font-size: 28px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }

      .metric-label {
        font-size: 14px;
        color: #909399;
        margin: 8px 0;
      }

      .metric-trend {
        font-size: 12px;
        display: flex;
        align-items: center;

        &.up {
          color: #67c23a;
        }

        &.down {
          color: #f56c6c;
        }
      }
    }
  }
}

.ranking-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: white;
  background: #909399;

  &.rank-first {
    background: #ffd700;
  }

  &.rank-second {
    background: #c0c0c0;
  }

  &.rank-third {
    background: #cd7f32;
  }
}

.growth-positive {
  color: #67c23a;
  font-weight: bold;
}

.growth-negative {
  color: #f56c6c;
  font-weight: bold;
}
</style>
