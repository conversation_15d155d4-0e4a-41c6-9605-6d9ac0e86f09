/*!
 * is-natural-number.js | MIT (c) <PERSON><PERSON><PERSON>
 * https://github.com/shinnn/is-natural-number.js
*/
'use strict';

module.exports = function isNaturalNumber(val, option) {
  if (option) {
    if (typeof option !== 'object') {
      throw new TypeError(
        String(option) +
        ' is not an object. Expected an object that has boolean `includeZero` property.'
      );
    }

    if ('includeZero' in option) {
      if (typeof option.includeZero !== 'boolean') {
        throw new TypeError(
          String(option.includeZero) +
          ' is neither true nor false. `includeZero` option must be a Boolean value.'
        );
      }

      if (option.includeZero && val === 0) {
        return true;
      }
    }
  }

  return Number.isSafeInteger(val) && val >= 1;
};
