{"version": 3, "sources": ["../browser/src/decorator/options/ColumnEmbeddedOptions.ts"], "names": [], "mappings": "", "file": "ColumnEmbeddedOptions.js", "sourcesContent": ["/**\n * Column options specific to embedded column.\n */\nexport interface ColumnEmbeddedOptions {\n    /**\n     * Embedded column prefix.\n     * If set to empty string or false, then prefix is not set at all.\n     */\n    prefix?: string | boolean\n\n    /**\n     * Indicates if this embedded is in array mode.\n     *\n     * This option works only in mongodb.\n     */\n    array?: boolean\n}\n"], "sourceRoot": "../.."}