{"version": 3, "file": "serialization.js", "sourceRoot": "", "sources": ["../../src/utils/serialization.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAGA,SAAS,4BAA4B,CAAC,iBAA+C;QACnF,IAAI,OAAO,iBAAiB,KAAK,UAAU,EAAE;YAC3C,IAAM,kBAAkB,GAAG,iBAAiB,CAAC;YAC7C,OAAO,kBAAkB,CAAC,IAAI,CAAC;SAChC;aAAM,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE;YAChD,OAAO,iBAAiB,CAAC,QAAQ,EAAE,CAAC;SACrC;aAAM;YACL,IAAM,kBAAkB,GAAG,iBAAiB,CAAC;YAC7C,OAAO,kBAA4B,CAAC;SACrC;IACH,CAAC;IAkIC,oEAA4B;IAhI9B,SAAS,0CAA0C,CACjD,SAA+B,EAC/B,iBAAyB,EACzB,WAG4B;QAG5B,IAAI,sBAAsB,GAAG,EAAE,CAAC;QAChC,IAAM,kBAAkB,GAAG,WAAW,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;QAErE,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;YAEnC,sBAAsB,GAAG,wBAAwB,CAAC;YAElD,kBAAkB,CAAC,OAAO,CAAC,UAAC,OAAoC;gBAG9D,IAAI,IAAI,GAAG,QAAQ,CAAC;gBAGpB,IAAI,OAAO,CAAC,kBAAkB,KAAK,IAAI,EAAE;oBACvC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,kBAA6C,CAAC,CAAC;iBAC/E;gBAED,sBAAsB,GAAM,sBAAsB,WAAM,IAAM,CAAC;gBAE/D,IAAI,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;oBAC/B,sBAAsB,GAAM,sBAAsB,WAAM,OAAO,CAAC,UAAU,CAAC,QAAU,CAAC;iBACvF;YAEH,CAAC,CAAC,CAAC;SAEJ;QAED,OAAO,sBAAsB,CAAC;IAChC,CAAC;IA4FC,gGAA0C;IA1F5C,SAAS,sBAAsB,CAC7B,OAA2B,EAC3B,iBAA+C;QAE/C,IAAI,OAAO,CAAC,aAAa,KAAK,IAAI,EAAE;YAClC,OAAO,KAAK,CAAC;SACd;aAAM,IAAI,OAAO,CAAC,aAAa,CAAC,iBAAiB,KAAK,iBAAiB,EAAE;YACxE,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,sBAAsB,CAAC,OAAO,CAAC,aAAa,EAAE,iBAAiB,CAAC,CAAC;SACzE;IACH,CAAC;IAED,SAAS,uBAAuB,CAC9B,OAA2B;QAG3B,SAAS,gBAAgB,CACvB,GAAuB,EACvB,MAAqB;YAArB,uBAAA,EAAA,WAAqB;YAErB,IAAM,iBAAiB,GAAG,4BAA4B,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,aAAa,KAAK,IAAI,EAAE;gBAC9B,OAAO,gBAAgB,CAAC,GAAG,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;aACpD;YACD,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAC5C,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAE3C,CAAC;IAED,SAAS,6BAA6B,CACpC,OAA2B;QAE3B,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,UAAC,YAAY;YACzC,IAAI,sBAAsB,CAAC,YAAY,EAAE,YAAY,CAAC,iBAAiB,CAAC,EAAE;gBACxE,IAAM,QAAQ,GAAG,uBAAuB,CAAC,YAAY,CAAC,CAAC;gBACvD,MAAM,IAAI,KAAK,CAAI,UAAU,CAAC,mBAAmB,SAAI,QAAU,CAAC,CAAC;aAClE;iBAAM;gBACL,6BAA6B,CAAC,YAAY,CAAC,CAAC;aAC7C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IA+CC,sEAA6B;IA7C/B,SAAS,qBAAqB,CAAC,uBAA+B,EAAE,MAAyB;QACvF,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE;YAEzC,IAAI,GAAC,GAAG,EAAE,CAAC;YAEX,IAAM,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;YACtC,IAAM,SAAS,GAAG,MAAM,CAAC,aAAa,EAAE,CAAC;YAEzC,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACrB,GAAC,IAAI,QAAQ,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;aACjC;YAED,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,SAAS,CAAC,OAAO,CAAC,UAAC,GAAG;oBACpB,GAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC;gBAC7B,CAAC,CAAC,CAAC;aACJ;YAED,OAAO,MAAI,uBAAuB,WAAM,uBAAuB,WAAM,GAAG,CAAC;SAE1E;aAAM;YACL,OAAO,MAAI,uBAAyB,CAAC;SACtC;IACH,CAAC;IAqBC,sDAAqB;IAlBvB,SAAS,eAAe,CAAC,IAA6B;QACpD,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;aAAM;YACL,IAAM,MAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAM,KAAK,GAAG,MAAI,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC,CAAC,CAAE,KAAK,CAAC,CAAC,CAAY,CAAC,CAAC,CAAC,yBAAuB,MAAM,CAAC;SACrE;IACH,CAAC;IAOC,0CAAe;IALjB,SAAS,oBAAoB,CAAC,MAAc;QAC1C,OAAO,MAAM,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACxC,CAAC;IAQC,oDAAoB"}