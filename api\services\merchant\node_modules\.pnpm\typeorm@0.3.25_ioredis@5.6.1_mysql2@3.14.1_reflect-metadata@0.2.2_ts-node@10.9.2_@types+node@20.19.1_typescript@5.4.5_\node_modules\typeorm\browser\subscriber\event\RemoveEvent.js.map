{"version": 3, "sources": ["../browser/src/subscriber/event/RemoveEvent.ts"], "names": [], "mappings": "", "file": "RemoveEvent.js", "sourcesContent": ["import { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\n\n/**\n * RemoveEvent is an object that broadcaster sends to the entity subscriber when entity is being removed to the database.\n */\nexport interface RemoveEvent<Entity> {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n\n    /**\n     * Entity that is being removed.\n     * This may absent if entity is removed without being loaded (for examples by cascades).\n     */\n    entity?: Entity\n\n    /**\n     * Metadata of the entity.\n     */\n    metadata: EntityMetadata\n\n    /**\n     * Database representation of entity that is being removed.\n     */\n    databaseEntity: Entity\n\n    /**\n     * Id or ids of the entity that is being removed.\n     */\n    entityId?: any\n}\n"], "sourceRoot": "../.."}