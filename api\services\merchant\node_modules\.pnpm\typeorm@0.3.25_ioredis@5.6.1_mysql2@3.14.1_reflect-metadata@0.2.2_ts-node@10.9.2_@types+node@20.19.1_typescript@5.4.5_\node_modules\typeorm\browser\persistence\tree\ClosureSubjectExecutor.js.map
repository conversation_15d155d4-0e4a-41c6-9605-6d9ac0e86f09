{"version": 3, "sources": ["../browser/src/persistence/tree/ClosureSubjectExecutor.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,mCAAmC,EAAE,MAAM,iDAAiD,CAAA;AAErG,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAG9C;;GAEG;AACH,MAAM,OAAO,sBAAsB;IAC/B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAElD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,yDAAyD;QACzD,MAAM,wBAAwB,GAAkB,EAAE,CAAA;QAClD,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,eAAe,CAAC,OAAO,CACzD,CAAC,MAAM,EAAE,EAAE;YACP,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzC,OAAO,CAAC,UAAU,CAAA;QAC1B,CAAC,CACJ,CAAA;QACD,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,OAAO,CAC3D,CAAC,MAAM,EAAE,EAAE;YACP,wBAAwB,CAAC,MAAM,CAAC,YAAY,CAAC;gBACzC,OAAO,CAAC,UAAU,CAAA;QAC1B,CAAC,CACJ,CAAA;QAED,gDAAgD;QAChD,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;aACzB,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,CAAC;aACrD,MAAM,CAAC,wBAAwB,CAAC;aAChC,YAAY,CAAC,KAAK,CAAC;aACnB,aAAa,CAAC,KAAK,CAAC;aACpB,OAAO,EAAE,CAAA;QAEd,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC5D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YAChE,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC3C,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBACxC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QAEtC,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAC/B,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,CAClD,CAAA;YACD,MAAM,WAAW,GAAU,EAAE,CAAA;YAE7B,MAAM,mBAAmB,GACrB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,eAAe,CAAC,GAAG,CACrD,CAAC,MAAM,EAAE,EAAE;gBACP,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YACtC,CAAC,CACJ,CAAA;YACL,MAAM,qBAAqB,GACvB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,GAAG,CACvD,CAAC,MAAM,EAAE,EAAE;gBACP,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YACtC,CAAC,CACJ,CAAA;YACL,MAAM,eAAe,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CACvD,CAAC,MAAM,EAAE,EAAE;gBACP,WAAW,CAAC,IAAI,CACZ,MAAM,CAAC,cAAc,CACjB,OAAO,CAAC,gBAAgB;oBACpB,CAAC,CAAC,OAAO,CAAC,gBAAgB;oBAC1B,CAAC,CAAC,OAAO,CAAC,MAAO,CACxB,CACJ,CAAA;gBACD,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CACrD,eAAe,GAAG,MAAM,CAAC,YAAY,EACrC,WAAW,CAAC,MAAM,GAAG,CAAC,CACzB,CAAA;YACL,CAAC,CACJ,CAAA;YAED,MAAM,cAAc,GAChB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,GAAG,CACvD,CAAC,MAAM,EAAE,EAAE;gBACP,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;gBAC9C,MAAM,QAAQ,GACV,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAEnD,IAAI,CAAC,QAAQ;oBACT,MAAM,IAAI,mCAAmC,CACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,CACxB,CAAA;gBAEL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC1B,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAC9C,gBAAgB;oBACZ,MAAM,CAAC,gBAAiB,CAAC,YAAY,EACzC,WAAW,CAAC,MAAM,GAAG,CAAC,CACzB,CAAA;gBACL,OAAO,GAAG,UAAU,MAAM,aAAa,EAAE,CAAA;YAC7C,CAAC,CACJ,CAAA;YAEL,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,eAAe,SAAS,KAAK;gBACzB,GAAG,mBAAmB;gBACtB,GAAG,qBAAqB;aAC3B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;gBACZ,UAAU,mBAAmB,CAAC,IAAI,CAC9B,IAAI,CACP,KAAK,eAAe,CAAC,IAAI,CACtB,IAAI,CACP,SAAS,SAAS,UAAU,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAC/D,WAAW,CACd,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC5D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YAChE,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QAEzC,IAAI,MAAM,GAAG,OAAO,CAAC,cAAc,CAAA,CAAC,oCAAoC;QACxE,IAAI,CAAC,MAAM,IAAI,MAAM;YACjB,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,QAAQ;iBACpB,oBAAqB,CAAC,cAAc,CAAC,MAAM,CAAC;iBAC5C,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE;gBACjB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAW,CAAC,CAAC,KAAK,CAC5C,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,CACzC,CAAA;YACL,CAAC,CAAC,CAAA;QAEV,mDAAmD;QACnD,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC/C,OAAM;QACV,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CACjE,MAAO,CACV,CAAA;QACD,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAC9D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAExD,+CAA+C;QAC/C,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC7C,OAAM;QACV,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACpD,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAA;QAE1D,MAAM,mBAAmB,GAAG,YAAY,CAAC,eAAe,CAAC,GAAG,CACxD,CAAC,MAAM,EAAE,EAAE;YACP,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACtC,CAAC,CACJ,CAAA;QAED,MAAM,qBAAqB,GAAG,YAAY,CAAC,iBAAiB,CAAC,GAAG,CAC5D,CAAC,MAAM,EAAE,EAAE;YACP,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACtC,CAAC,CACJ,CAAA;QAED,eAAe;QACf,MAAM,cAAc,GAAG,CAAC,EAA2B,EAAE,KAAa,EAAE,EAAE;YAClE,MAAM,QAAQ,GAAG,MAAM,KAAK,EAAE,CAAA;YAE9B,MAAM,SAAS,GAAG,EAAE;iBACf,kBAAkB,EAAE;iBACpB,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxC,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAA;YAE3C,iFAAiF;YACjF,KAAK,MAAM,MAAM,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;gBAChD,SAAS,CAAC,QAAQ,CACd,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,MAAM,CACzB,MAAM,CAAC,YAAY,CACtB,aAAa,MAAM,CAAC,gBAAiB,CAAC,YAAY,EAAE,CACxD,CAAA;YACL,CAAC;YAED,OAAO,EAAE;iBACJ,kBAAkB,EAAE;iBACpB,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;iBACxC,IAAI,CAAC,IAAI,SAAS,CAAC,QAAQ,EAAE,GAAG,EAAE,KAAK,CAAC;iBACxC,aAAa,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;iBACxC,QAAQ,EAAE,CAAA;QACnB,CAAC,CAAA;QAED,MAAM,UAAU,GAAkB,EAAE,CAAA;QACpC,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,CAAC;YACnD,UAAU,CAAC,SAAS,MAAM,CAAC,YAAY,EAAE,CAAC;gBACtC,MAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;QACpC,CAAC;QAED,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;aACzB,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aAC5B,KAAK,CACF,CAAC,EAAE,EAAE,EAAE,CACH,IAAI,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,cAAc,CACvD,EAAE,EACF,YAAY,CACf,GAAG,CACX;aACA,QAAQ,CACL,CAAC,EAAE,EAAE,EAAE,CACH,IAAI,mBAAmB,CAAC,IAAI,CACxB,IAAI,CACP,aAAa,cAAc,CAAC,EAAE,EAAE,UAAU,CAAC,GAAG,CACtD;aACA,aAAa,CAAC,UAAU,CAAC;aACzB,OAAO,EAAE,CAAA;QAEd;;;;WAIG;QACH,IAAI,MAAM,EAAE,CAAC;YACT,eAAe;YACf,MAAM,WAAW,GAAU,EAAE,CAAA;YAE7B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;YAC3D,MAAM,UAAU,GAAG,MAAM,CAAC,WAAW,CAAC,CAAA;YACtC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;YAElC,MAAM,MAAM,GAAG;gBACX,GAAG,mBAAmB,CAAC,GAAG,CACtB,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,UAAU,IAAI,UAAU,EAAE,CAChD;gBACD,GAAG,qBAAqB,CAAC,GAAG,CACxB,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,QAAQ,IAAI,UAAU,EAAE,CAC9C;aACJ,CAAA;YAED,MAAM,oBAAoB,GACtB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,eAAe,CAAC,GAAG,CACrD,CAAC,MAAM,EAAE,EAAE;gBACP,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;gBAC9C,MAAM,QAAQ,GACV,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAO,CAAC,CAAA;gBAEpD,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC1B,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAC9C,SAAS;oBACL,MAAM,CAAC,gBAAiB,CAAC,YAAY,EACzC,WAAW,CAAC,MAAM,GAAG,CAAC,CACzB,CAAA;gBACL,OAAO,GAAG,QAAQ,IAAI,UAAU,MAAM,aAAa,EAAE,CAAA;YACzD,CAAC,CACJ,CAAA;YAEL,MAAM,oBAAoB,GACtB,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,GAAG,CACvD,CAAC,MAAM,EAAE,EAAE;gBACP,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;gBAC9C,MAAM,QAAQ,GACV,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBAEnD,IAAI,CAAC,QAAQ;oBACT,MAAM,IAAI,mCAAmC,CACzC,OAAO,CAAC,QAAQ,CAAC,IAAI,CACxB,CAAA;gBAEL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC1B,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAC9C,gBAAgB;oBACZ,MAAM,CAAC,gBAAiB,CAAC,YAAY,EACzC,WAAW,CAAC,MAAM,GAAG,CAAC,CACzB,CAAA;gBACL,OAAO,GAAG,UAAU,IAAI,UAAU,MAAM,aAAa,EAAE,CAAA;YAC3D,CAAC,CACJ,CAAA;YAEL,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,eAAe,SAAS,KAAK;gBACzB,GAAG,mBAAmB;gBACtB,GAAG,qBAAqB;aAC3B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI;gBACZ,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;gBAC9B,QAAQ,SAAS,OAAO,UAAU,KAAK,SAAS,OAAO,QAAQ,GAAG;gBAClE,SAAS;oBACL,GAAG,oBAAoB;oBACvB,GAAG,oBAAoB;iBAC1B,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EACrB,WAAW,CACd,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,QAA6B;QACtC,sGAAsG;QACtG,IAAI,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,CAAC;YACjE,OAAM;QACV,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;YAAE,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACpD,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QACjE,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAA;QAE9D,MAAM,cAAc,GAAG,CAAC,OAAyB,EAAE,EAAE;YACjD,OAAO,OAAO;iBACT,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,IAAI,GAAG,WAAW,CAAC,GAAG,CACxB,CAAC,UAAU,EAAE,EAAE,CACX,UAAW,CAAC,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,CACzD,CAAA;gBACD,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,CAClD,IAAI,CACP,GAAG,CAAA;YACR,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;QACtB,CAAC,CAAA;QAED,MAAM,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC,eAAe,CAAC,CAAA;QAClE,MAAM,eAAe,GAAG,cAAc,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAA;QAEtE,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;aACzB,kBAAkB,EAAE;aACpB,MAAM,EAAE;aACR,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC;aAC5B,KAAK,CAAC,aAAa,CAAC;aACpB,OAAO,CAAC,eAAe,CAAC;aACxB,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;;OAGG;IACO,YAAY,CAAC,SAAiB;QACpC,OAAO,SAAS;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,sMAAsM;YACtM,OAAO,CAAC,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACtD,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;CACJ", "file": "ClosureSubjectExecutor.js", "sourcesContent": ["import { Subject } from \"../Subject\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { CannotAttachTreeChildrenEntityError } from \"../../error/CannotAttachTreeChildrenEntityError\"\nimport { DeleteQueryBuilder } from \"../../query-builder/DeleteQueryBuilder\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\n\n/**\n * Executes subject operations for closure entities.\n */\nexport class ClosureSubjectExecutor {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected queryRunner: QueryRunner) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes operations when subject is being inserted.\n     */\n    async insert(subject: Subject): Promise<void> {\n        // create values to be inserted into the closure junction\n        const closureJunctionInsertMap: ObjectLiteral = {}\n        subject.metadata.closureJunctionTable.ancestorColumns.forEach(\n            (column) => {\n                closureJunctionInsertMap[column.databaseName] =\n                    subject.identifier\n            },\n        )\n        subject.metadata.closureJunctionTable.descendantColumns.forEach(\n            (column) => {\n                closureJunctionInsertMap[column.databaseName] =\n                    subject.identifier\n            },\n        )\n\n        // insert values into the closure junction table\n        await this.queryRunner.manager\n            .createQueryBuilder()\n            .insert()\n            .into(subject.metadata.closureJunctionTable.tablePath)\n            .values(closureJunctionInsertMap)\n            .updateEntity(false)\n            .callListeners(false)\n            .execute()\n\n        let parent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!parent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            parent = subject.parentSubject.insertedValueSet\n                ? subject.parentSubject.insertedValueSet\n                : subject.parentSubject.entity\n\n        if (parent) {\n            const escape = (alias: string) =>\n                this.queryRunner.connection.driver.escape(alias)\n            const tableName = this.getTableName(\n                subject.metadata.closureJunctionTable.tablePath,\n            )\n            const queryParams: any[] = []\n\n            const ancestorColumnNames =\n                subject.metadata.closureJunctionTable.ancestorColumns.map(\n                    (column) => {\n                        return escape(column.databaseName)\n                    },\n                )\n            const descendantColumnNames =\n                subject.metadata.closureJunctionTable.descendantColumns.map(\n                    (column) => {\n                        return escape(column.databaseName)\n                    },\n                )\n            const childEntityIds1 = subject.metadata.primaryColumns.map(\n                (column) => {\n                    queryParams.push(\n                        column.getEntityValue(\n                            subject.insertedValueSet\n                                ? subject.insertedValueSet\n                                : subject.entity!,\n                        ),\n                    )\n                    return this.queryRunner.connection.driver.createParameter(\n                        \"child_entity_\" + column.databaseName,\n                        queryParams.length - 1,\n                    )\n                },\n            )\n\n            const whereCondition =\n                subject.metadata.closureJunctionTable.descendantColumns.map(\n                    (column) => {\n                        const columnName = escape(column.databaseName)\n                        const parentId =\n                            column.referencedColumn!.getEntityValue(parent)\n\n                        if (!parentId)\n                            throw new CannotAttachTreeChildrenEntityError(\n                                subject.metadata.name,\n                            )\n\n                        queryParams.push(parentId)\n                        const parameterName =\n                            this.queryRunner.connection.driver.createParameter(\n                                \"parent_entity_\" +\n                                    column.referencedColumn!.databaseName,\n                                queryParams.length - 1,\n                            )\n                        return `${columnName} = ${parameterName}`\n                    },\n                )\n\n            await this.queryRunner.query(\n                `INSERT INTO ${tableName} (${[\n                    ...ancestorColumnNames,\n                    ...descendantColumnNames,\n                ].join(\", \")}) ` +\n                    `SELECT ${ancestorColumnNames.join(\n                        \", \",\n                    )}, ${childEntityIds1.join(\n                        \", \",\n                    )} FROM ${tableName} WHERE ${whereCondition.join(\" AND \")}`,\n                queryParams,\n            )\n        }\n    }\n\n    /**\n     * Executes operations when subject is being updated.\n     */\n    async update(subject: Subject): Promise<void> {\n        let parent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!parent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            parent = subject.parentSubject.entity\n\n        let entity = subject.databaseEntity // if entity was attached via parent\n        if (!entity && parent)\n            // if entity was attached via children\n            entity = subject.metadata\n                .treeChildrenRelation!.getEntityValue(parent)\n                .find((child: any) => {\n                    return Object.entries(subject.identifier!).every(\n                        ([key, value]) => child[key] === value,\n                    )\n                })\n\n        // Exit if the parent or the entity where never set\n        if (entity === undefined || parent === undefined) {\n            return\n        }\n\n        const oldParent = subject.metadata.treeParentRelation!.getEntityValue(\n            entity!,\n        )\n        const oldParentId = subject.metadata.getEntityIdMap(oldParent)\n        const parentId = subject.metadata.getEntityIdMap(parent)\n\n        // Exit if the new and old parents are the same\n        if (OrmUtils.compareIds(oldParentId, parentId)) {\n            return\n        }\n\n        const escape = (alias: string) =>\n            this.queryRunner.connection.driver.escape(alias)\n        const closureTable = subject.metadata.closureJunctionTable\n\n        const ancestorColumnNames = closureTable.ancestorColumns.map(\n            (column) => {\n                return escape(column.databaseName)\n            },\n        )\n\n        const descendantColumnNames = closureTable.descendantColumns.map(\n            (column) => {\n                return escape(column.databaseName)\n            },\n        )\n\n        // Delete logic\n        const createSubQuery = (qb: DeleteQueryBuilder<any>, alias: string) => {\n            const subAlias = `sub${alias}`\n\n            const subSelect = qb\n                .createQueryBuilder()\n                .select(descendantColumnNames.join(\", \"))\n                .from(closureTable.tablePath, subAlias)\n\n            // Create where conditions e.g. (WHERE \"subdescendant\".\"id_ancestor\" = :value_id)\n            for (const column of closureTable.ancestorColumns) {\n                subSelect.andWhere(\n                    `${escape(subAlias)}.${escape(\n                        column.databaseName,\n                    )} = :value_${column.referencedColumn!.databaseName}`,\n                )\n            }\n\n            return qb\n                .createQueryBuilder()\n                .select(descendantColumnNames.join(\", \"))\n                .from(`(${subSelect.getQuery()})`, alias)\n                .setParameters(subSelect.getParameters())\n                .getQuery()\n        }\n\n        const parameters: ObjectLiteral = {}\n        for (const column of subject.metadata.primaryColumns) {\n            parameters[`value_${column.databaseName}`] =\n                entity![column.databaseName]\n        }\n\n        await this.queryRunner.manager\n            .createQueryBuilder()\n            .delete()\n            .from(closureTable.tablePath)\n            .where(\n                (qb) =>\n                    `(${descendantColumnNames.join(\", \")}) IN (${createSubQuery(\n                        qb,\n                        \"descendant\",\n                    )})`,\n            )\n            .andWhere(\n                (qb) =>\n                    `(${ancestorColumnNames.join(\n                        \", \",\n                    )}) NOT IN (${createSubQuery(qb, \"ancestor\")})`,\n            )\n            .setParameters(parameters)\n            .execute()\n\n        /**\n         * Only insert new parent if it exits\n         *\n         * This only happens if the entity doesn't become a root entity\n         */\n        if (parent) {\n            // Insert logic\n            const queryParams: any[] = []\n\n            const tableName = this.getTableName(closureTable.tablePath)\n            const superAlias = escape(\"supertree\")\n            const subAlias = escape(\"subtree\")\n\n            const select = [\n                ...ancestorColumnNames.map(\n                    (columnName) => `${superAlias}.${columnName}`,\n                ),\n                ...descendantColumnNames.map(\n                    (columnName) => `${subAlias}.${columnName}`,\n                ),\n            ]\n\n            const entityWhereCondition =\n                subject.metadata.closureJunctionTable.ancestorColumns.map(\n                    (column) => {\n                        const columnName = escape(column.databaseName)\n                        const entityId =\n                            column.referencedColumn!.getEntityValue(entity!)\n\n                        queryParams.push(entityId)\n                        const parameterName =\n                            this.queryRunner.connection.driver.createParameter(\n                                \"entity_\" +\n                                    column.referencedColumn!.databaseName,\n                                queryParams.length - 1,\n                            )\n                        return `${subAlias}.${columnName} = ${parameterName}`\n                    },\n                )\n\n            const parentWhereCondition =\n                subject.metadata.closureJunctionTable.descendantColumns.map(\n                    (column) => {\n                        const columnName = escape(column.databaseName)\n                        const parentId =\n                            column.referencedColumn!.getEntityValue(parent)\n\n                        if (!parentId)\n                            throw new CannotAttachTreeChildrenEntityError(\n                                subject.metadata.name,\n                            )\n\n                        queryParams.push(parentId)\n                        const parameterName =\n                            this.queryRunner.connection.driver.createParameter(\n                                \"parent_entity_\" +\n                                    column.referencedColumn!.databaseName,\n                                queryParams.length - 1,\n                            )\n                        return `${superAlias}.${columnName} = ${parameterName}`\n                    },\n                )\n\n            await this.queryRunner.query(\n                `INSERT INTO ${tableName} (${[\n                    ...ancestorColumnNames,\n                    ...descendantColumnNames,\n                ].join(\", \")}) ` +\n                    `SELECT ${select.join(\", \")} ` +\n                    `FROM ${tableName} AS ${superAlias}, ${tableName} AS ${subAlias} ` +\n                    `WHERE ${[\n                        ...entityWhereCondition,\n                        ...parentWhereCondition,\n                    ].join(\" AND \")}`,\n                queryParams,\n            )\n        }\n    }\n\n    /**\n     * Executes operations when subject is being removed.\n     */\n    async remove(subjects: Subject | Subject[]): Promise<void> {\n        // Only mssql need to execute deletes for the juntion table as it doesn't support multi cascade paths.\n        if (!(this.queryRunner.connection.driver.options.type === \"mssql\")) {\n            return\n        }\n\n        if (!Array.isArray(subjects)) subjects = [subjects]\n\n        const escape = (alias: string) =>\n            this.queryRunner.connection.driver.escape(alias)\n        const identifiers = subjects.map((subject) => subject.identifier)\n        const closureTable = subjects[0].metadata.closureJunctionTable\n\n        const generateWheres = (columns: ColumnMetadata[]) => {\n            return columns\n                .map((column) => {\n                    const data = identifiers.map(\n                        (identifier) =>\n                            identifier![column.referencedColumn!.databaseName],\n                    )\n                    return `${escape(column.databaseName)} IN (${data.join(\n                        \", \",\n                    )})`\n                })\n                .join(\" AND \")\n        }\n\n        const ancestorWhere = generateWheres(closureTable.ancestorColumns)\n        const descendantWhere = generateWheres(closureTable.descendantColumns)\n\n        await this.queryRunner.manager\n            .createQueryBuilder()\n            .delete()\n            .from(closureTable.tablePath)\n            .where(ancestorWhere)\n            .orWhere(descendantWhere)\n            .execute()\n    }\n\n    /**\n     * Gets escaped table name with schema name if SqlServer or Postgres driver used with custom\n     * schema name, otherwise returns escaped table name.\n     */\n    protected getTableName(tablePath: string): string {\n        return tablePath\n            .split(\".\")\n            .map((i) => {\n                // this condition need because in SQL Server driver when custom database name was specified and schema name was not, we got `dbName..tableName` string, and doesn't need to escape middle empty string\n                return i === \"\"\n                    ? i\n                    : this.queryRunner.connection.driver.escape(i)\n            })\n            .join(\".\")\n    }\n}\n"], "sourceRoot": "../.."}