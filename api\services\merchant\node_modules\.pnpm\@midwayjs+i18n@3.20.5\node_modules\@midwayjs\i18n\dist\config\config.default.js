"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.i18n = void 0;
const core_1 = require("@midwayjs/core");
exports.i18n = {
    defaultLocale: 'en_US',
    localeTable: {
        en_US: {},
    },
    fallbacks: {
    //   'en_*': 'en_US',
    //   pt: 'pt-BR',
    },
    writeCookie: true,
    resolver: {
        queryField: 'locale',
        cookieField: {
            fieldName: 'locale',
            cookieDomain: '',
            cookieMaxAge: core_1.FORMAT.MS.ONE_YEAR,
        },
    },
    localsField: 'i18n',
};
//# sourceMappingURL=config.default.js.map