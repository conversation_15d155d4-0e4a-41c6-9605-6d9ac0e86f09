{"version": 3, "sources": ["../browser/src/entity-schema/EntitySchemaForeignKeyOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaForeignKeyOptions.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\nimport { ForeignKeyOptions } from \"../decorator/options/ForeignKeyOptions\"\n\nexport interface EntitySchemaForeignKeyOptions extends ForeignKeyOptions {\n    /**\n     * Indicates with which entity this relation is made.\n     */\n    target: EntityTarget<any>\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    columnNames: string[]\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    referencedColumnNames: string[]\n}\n"], "sourceRoot": ".."}