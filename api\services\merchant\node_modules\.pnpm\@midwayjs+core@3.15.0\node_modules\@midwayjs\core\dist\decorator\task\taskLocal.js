"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskLocal = void 0;
const __1 = require("../");
const constant_1 = require("../constant");
function TaskLocal(options) {
    return function (target, propertyKey, descriptor) {
        (0, __1.saveModule)(constant_1.MODULE_TASK_TASK_LOCAL_KEY, target.constructor);
        (0, __1.attachClassMetadata)(constant_1.MODULE_TASK_TASK_LOCAL_OPTIONS, {
            options,
            propertyKey,
            value: descriptor.value,
        }, target);
    };
}
exports.TaskLocal = TaskLocal;
//# sourceMappingURL=taskLocal.js.map