import { AsyncContext, AsyncContextManager } from '@midwayjs/core';
export declare class AsyncLocalStorageContextManager implements AsyncContextManager {
    private _asyncLocalStorage;
    constructor();
    active(): AsyncContext;
    with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(context: AsyncContext, fn: F, thisArg?: ThisParameterType<F>, ...args: A): ReturnType<F>;
    enable(): this;
    disable(): this;
    /**
     * Binds a the certain context or the active one to the target function and then returns the target
     * @param context A context (span) to be bind to target
     * @param target a function. When target or one of its callbacks is called,
     *  the provided context will be used as the active context for the duration of the call.
     */
    bind<T>(context: AsyncContext, target: T): T;
}
//# sourceMappingURL=asyncLocalStorageContextManager.d.ts.map