/**
 * 路由重定向守卫
 * 处理菜单结构调整后的路由访问问题
 */
import type { Router, RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { routeMapper, routeMappingUtils } from '../utils/routeMapping'

/**
 * 设置路由重定向守卫
 * @param router Vue Router 实例
 */
export function setupRouteRedirectGuard(router: Router): void {
  router.beforeEach((to, from, next) => {
    // 检查是否需要重定向
    const redirectPath = routeMappingUtils.checkRedirect(to.path)
    
    if (redirectPath && redirectPath !== to.path) {
      console.log(`🔀 [路由重定向] ${to.path} -> ${redirectPath}`)
      
      // 保持查询参数和hash
      next({
        path: redirectPath,
        query: to.query,
        hash: to.hash,
        replace: true
      })
      return
    }
    
    next()
  })
}

/**
 * 路由兼容性检查器
 * 在路由注册完成后检查是否有需要处理的兼容性问题
 */
export class RouteCompatibilityChecker {
  private router: Router
  private incompatibleRoutes: string[] = []

  constructor(router: Router) {
    this.router = router
  }

  /**
   * 检查路由兼容性
   */
  public checkCompatibility(): void {
    console.log('🔍 [兼容性检查] 开始检查路由兼容性')
    
    const mappings = routeMapper.getAllMappings()
    this.incompatibleRoutes = []

    Object.values(mappings).forEach(mapping => {
      const defaultPath = mapping.defaultPath
      const currentPath = routeMapper.getPathByRouteKey(mapping.routeKey)
      
      if (defaultPath && currentPath && defaultPath !== currentPath) {
        // 检查默认路径是否还能访问
        if (!this.router.hasRoute(mapping.name) && !this.hasRedirectRoute(defaultPath)) {
          this.incompatibleRoutes.push(defaultPath)
          console.warn(`⚠️ [兼容性警告] 路径 ${defaultPath} 可能无法访问，当前路径: ${currentPath}`)
        }
      }
    })

    if (this.incompatibleRoutes.length > 0) {
      console.warn(`⚠️ [兼容性检查] 发现 ${this.incompatibleRoutes.length} 个潜在的兼容性问题`)
      this.suggestSolutions()
    } else {
      console.log('✅ [兼容性检查] 所有路由兼容性良好')
    }
  }

  /**
   * 检查是否存在重定向路由
   */
  private hasRedirectRoute(path: string): boolean {
    return this.router.getRoutes().some(route => 
      route.path === path && route.meta?.isRedirect
    )
  }

  /**
   * 提供解决方案建议
   */
  private suggestSolutions(): void {
    console.log('💡 [解决方案] 建议的解决方案:')
    console.log('1. 确保在菜单配置中添加 routeKey 字段')
    console.log('2. 使用路由映射表中的标识符而不是硬编码路径')
    console.log('3. 考虑添加路由别名来保持向后兼容')
  }

  /**
   * 获取不兼容的路由列表
   */
  public getIncompatibleRoutes(): string[] {
    return [...this.incompatibleRoutes]
  }
}

/**
 * 路由健康检查工具
 */
export const routeHealthChecker = {
  /**
   * 检查路由健康状态
   */
  checkHealth(router: Router): {
    totalRoutes: number
    dynamicRoutes: number
    redirectRoutes: number
    issues: string[]
  } {
    const routes = router.getRoutes()
    const issues: string[] = []
    
    const totalRoutes = routes.length
    const dynamicRoutes = routes.filter(route => route.meta?.dynamic).length
    const redirectRoutes = routes.filter(route => route.meta?.isRedirect).length
    
    // 检查是否有重复的路由名称
    const routeNames = new Set<string>()
    routes.forEach(route => {
      if (route.name) {
        const name = String(route.name)
        if (routeNames.has(name)) {
          issues.push(`重复的路由名称: ${name}`)
        } else {
          routeNames.add(name)
        }
      }
    })
    
    // 检查是否有重复的路径
    const routePaths = new Set<string>()
    routes.forEach(route => {
      if (routePaths.has(route.path)) {
        issues.push(`重复的路由路径: ${route.path}`)
      } else {
        routePaths.add(route.path)
      }
    })

    return {
      totalRoutes,
      dynamicRoutes,
      redirectRoutes,
      issues
    }
  },

  /**
   * 打印路由健康报告
   */
  printHealthReport(router: Router): void {
    const health = this.checkHealth(router)
    
    console.log('📊 [路由健康报告]')
    console.log(`总路由数: ${health.totalRoutes}`)
    console.log(`动态路由数: ${health.dynamicRoutes}`)
    console.log(`重定向路由数: ${health.redirectRoutes}`)
    
    if (health.issues.length > 0) {
      console.warn('⚠️ 发现的问题:')
      health.issues.forEach(issue => console.warn(`  - ${issue}`))
    } else {
      console.log('✅ 路由健康状态良好')
    }
  }
}

/**
 * 路由调试工具
 */
export const routeDebugger = {
  /**
   * 查找路由
   */
  findRoute(router: Router, query: string): void {
    const routes = router.getRoutes()
    const matches = routes.filter(route => 
      route.path.includes(query) || 
      String(route.name || '').includes(query) ||
      String(route.meta?.title || '').includes(query)
    )
    
    console.log(`🔍 [路由查找] 查询: "${query}", 找到 ${matches.length} 个匹配项`)
    matches.forEach(route => {
      console.log(`  - ${route.name} (${route.path}) - ${route.meta?.title || ''}`)
    })
  },

  /**
   * 显示路由映射状态
   */
  showMappingStatus(): void {
    const mappings = routeMapper.getAllMappings()
    
    console.log('🗺️ [路由映射状态]')
    Object.values(mappings).forEach(mapping => {
      const currentPath = routeMapper.getPathByRouteKey(mapping.routeKey)
      const status = currentPath === mapping.defaultPath ? '✅' : '🔄'
      
      console.log(`  ${status} ${mapping.routeKey}:`)
      console.log(`    默认路径: ${mapping.defaultPath}`)
      console.log(`    当前路径: ${currentPath}`)
    })
  }
}
