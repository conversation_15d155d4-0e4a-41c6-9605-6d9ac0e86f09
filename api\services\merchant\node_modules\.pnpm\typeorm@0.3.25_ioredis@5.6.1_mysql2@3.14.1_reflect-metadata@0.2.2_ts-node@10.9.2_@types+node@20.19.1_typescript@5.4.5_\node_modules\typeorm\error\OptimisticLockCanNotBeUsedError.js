"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OptimisticLockCanNotBeUsedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when an optimistic lock cannot be used in query builder.
 */
class OptimisticLockCanNotBeUsedError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`The optimistic lock can be used only with getOne() method.`);
    }
}
exports.OptimisticLockCanNotBeUsedError = OptimisticLockCanNotBeUsedError;

//# sourceMappingURL=OptimisticLockCanNotBeUsedError.js.map
