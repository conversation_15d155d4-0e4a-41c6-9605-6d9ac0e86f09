{"@midwayjs/faas-typings": "3.3.5", "@midwayjs/fc-starter": "3.4.10", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.10", "@midwayjs/axios": "3.4.10", "@midwayjs/bootstrap": "3.4.10", "@midwayjs/cache": "3.4.10", "@midwayjs/code-dye": "3.4.10", "@midwayjs/consul": "3.4.10", "@midwayjs/core": "3.4.10", "@midwayjs/cos": "3.4.10", "@midwayjs/cross-domain": "3.4.10", "@midwayjs/decorator": "3.4.4", "@midwayjs/express-session": "3.4.10", "@midwayjs/faas": "3.4.10", "@midwayjs/grpc": "3.4.10", "@midwayjs/http-proxy": "3.4.10", "@midwayjs/i18n": "3.4.10", "@midwayjs/info": "3.4.10", "@midwayjs/jwt": "3.4.10", "@midwayjs/kafka": "3.4.10", "@midwayjs/mikro": "3.4.10", "@midwayjs/mock": "3.4.10", "@midwayjs/mongoose": "3.4.10", "@midwayjs/oss": "3.4.10", "@midwayjs/otel": "3.4.10", "@midwayjs/passport": "3.4.10", "@midwayjs/process-agent": "3.4.10", "@midwayjs/prometheus-socket-io": "3.4.10", "@midwayjs/prometheus": "3.4.10", "@midwayjs/rabbitmq": "3.4.10", "@midwayjs/redis": "3.4.10", "@midwayjs/security": "3.4.10", "@midwayjs/sequelize": "3.4.10", "@midwayjs/session": "3.4.10", "@midwayjs/socketio": "3.4.10", "@midwayjs/static-file": "3.4.10", "@midwayjs/swagger": "3.4.10", "@midwayjs/tablestore": "3.4.10", "@midwayjs/task": "3.4.10", "@midwayjs/typegoose": "3.4.10", "@midwayjs/typeorm": "3.4.10", "@midwayjs/upload": "3.4.10", "@midwayjs/validate": "3.4.10", "@midwayjs/version": "3.4.10", "@midwayjs/view-ejs": "3.4.10", "@midwayjs/view-nunjucks": "3.4.10", "@midwayjs/view": "3.4.10", "@midwayjs/express": "3.4.10", "@midwayjs/koa": "3.4.10", "@midwayjs/web": "3.4.10", "@midwayjs/ws": "3.4.10"}