import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9801, // merchant 微服务端口
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'your_password',
        database: 'cool',
        synchronize: false,
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ['src/modules/merchant/entity/*.ts'],
      },
    },
  },
  cool: {
    redis: {
      cluster: true,
      nodes: [
        { host: '*************', port: 6379 },
        { host: '*************', port: 6379 },
        { host: '*************', port: 6379 },
        // 更多节点可按需添加
      ],
      password: 'your_password', // 如有密码
    },
    eps: true,
    initDB: false,
    initMenu: false,
  } as CoolConfig,
} as MidwayConfig; 