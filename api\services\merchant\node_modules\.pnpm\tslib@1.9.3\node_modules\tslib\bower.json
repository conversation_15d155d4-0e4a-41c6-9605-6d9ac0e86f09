{"name": "tslib", "authors": ["Microsoft Corp."], "homepage": "http://typescriptlang.org/", "version": "1.9.3", "license": "Apache-2.0", "description": "Runtime library for TypeScript helper functions", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "repository": {"type": "git", "url": "https://github.com/Microsoft/tslib.git"}, "main": "tslib.js", "ignore": ["**/.*", "node_modules", "bower_components", "docs", "package.json", ".n<PERSON><PERSON><PERSON>", ".giti<PERSON>re", ".gitattributes"]}