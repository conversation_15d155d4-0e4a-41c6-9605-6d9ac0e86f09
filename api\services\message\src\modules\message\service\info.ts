import { Init, Inject, Provide } from '@midwayjs/core';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, In, Repository } from 'typeorm';
import { MsgInfoEntity } from '../entity/info';

/**
 * 消息
 */
@Provide()
@CoolRpcService({
  entity: MsgInfoEntity,
  method: ["add", "delete", "update", "info", "list", "page"],
})
export class MessageInfoService extends BaseRpcService {
  @InjectEntityModel(MsgInfoEntity)
  msgInfoEntity!: Repository<MsgInfoEntity>;

  @Inject()
  ctx;

  @Init()
  async init() {
    await super.init();
  }

  /**
   * 发送消息（RPC暴露）
   */
  async sendMessage(userId: number, title: string, content: string, obj?: any) {
    return this.msgInfoEntity.save({
      userId,
      title,
      content,
      obj,
      status: 0
    });
  }

  /**
   * 未读消息数量（RPC暴露）
   */
  async unreadCount(userId: number) {
    return this.msgInfoEntity.countBy({
      userId: Equal(userId),
      status: 0,
    });
  }

  /**
   * 批量标记已读（RPC暴露）
   */
  async markRead(userId: number, ids: number[]) {
    return this.msgInfoEntity.update(
      {
        id: In(ids),
        userId,
      },
      {
        status: 1,
      }
    );
  }

  /**
   * 分页查询（RPC暴露）
   */
  async page(params?: any) {
    return super.page(params);
  }
}
