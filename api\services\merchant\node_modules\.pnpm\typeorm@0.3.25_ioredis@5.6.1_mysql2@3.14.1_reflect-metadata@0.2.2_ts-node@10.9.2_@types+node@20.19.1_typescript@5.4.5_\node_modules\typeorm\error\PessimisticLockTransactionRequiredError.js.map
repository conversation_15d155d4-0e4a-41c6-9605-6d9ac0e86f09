{"version": 3, "sources": ["../../src/error/PessimisticLockTransactionRequiredError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uCAAwC,SAAQ,2BAAY;IACrE;QACI,KAAK,CAAC,uDAAuD,CAAC,CAAA;IAClE,CAAC;CACJ;AAJD,0FAIC", "file": "PessimisticLockTransactionRequiredError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when a transaction is required for the current operation, but there is none open.\n */\nexport class PessimisticLockTransactionRequiredError extends TypeORMError {\n    constructor() {\n        super(`An open transaction is required for pessimistic lock.`)\n    }\n}\n"], "sourceRoot": ".."}