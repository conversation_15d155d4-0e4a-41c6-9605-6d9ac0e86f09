{"version": 3, "sources": ["../../src/naming-strategy/NamingStrategyInterface.ts"], "names": [], "mappings": "", "file": "NamingStrategyInterface.js", "sourcesContent": ["import { Table } from \"../schema-builder/table/Table\"\nimport { View } from \"../schema-builder/view/View\"\n\n/**\n * Naming strategy defines how auto-generated names for such things like table name, or table column gonna be\n * generated.\n */\nexport interface NamingStrategyInterface {\n    /**\n     * Naming strategy name.\n     */\n    name?: string\n\n    /**\n     * Normalizes table name.\n     *\n     * @param targetName Name of the target entity that can be used to generate a table name.\n     * @param userSpecifiedName For example if user specified a table name in a decorator, e.g. @Entity(\"name\")\n     */\n    tableName(targetName: string, userSpecifiedName: string | undefined): string\n\n    /**\n     * Creates a table name for a junction table of a closure table.\n     *\n     * @param originalClosureTableName Name of the closure table which owns this junction table.\n     */\n    closureJunctionTableName(originalClosureTableName: string): string\n\n    /**\n     * Gets the table's column name from the given property name.\n     */\n    columnName(\n        propertyName: string,\n        customName: string | undefined,\n        embeddedPrefixes: string[],\n    ): string\n\n    /**\n     * Gets the table's relation name from the given property name.\n     */\n    relationName(propertyName: string): string\n\n    /**\n     * Gets the table's primary key name from the given table name and column names.\n     */\n    primaryKeyName(tableOrName: Table | string, columnNames: string[]): string\n\n    /**\n     * Gets the table's unique constraint name from the given table name and column names.\n     */\n    uniqueConstraintName(\n        tableOrName: Table | string,\n        columnNames: string[],\n    ): string\n\n    /**\n     * Gets the relation constraint (UNIQUE or UNIQUE INDEX) name from the given table name, column names\n     * and WHERE condition, if UNIQUE INDEX used.\n     */\n    relationConstraintName(\n        tableOrName: Table | string,\n        columnNames: string[],\n        where?: string,\n    ): string\n\n    /**\n     * Gets the table's default constraint name from the given table name and column name.\n     */\n    defaultConstraintName(\n        tableOrName: Table | string,\n        columnName: string,\n    ): string\n\n    /**\n     * Gets the name of the foreign key.\n     */\n    foreignKeyName(\n        tableOrName: Table | string,\n        columnNames: string[],\n        referencedTablePath?: string,\n        referencedColumnNames?: string[],\n    ): string\n\n    /**\n     * Gets the name of the index - simple and compose index.\n     */\n    indexName(\n        tableOrName: Table | View | string,\n        columns: string[],\n        where?: string,\n    ): string\n\n    /**\n     * Gets the name of the check constraint.\n     *\n     * \"isEnum\" parameter is used to indicate if this check constraint used\n     * to handle \"simple-enum\" type for databases that are not supporting \"enum\"\n     * type out of the box. If \"true\", constraint is ignored during CHECK constraints\n     * synchronization.\n     */\n    checkConstraintName(\n        tableOrName: Table | string,\n        expression: string,\n        isEnum?: boolean,\n    ): string\n\n    /**\n     * Gets the name of the exclusion constraint.\n     */\n    exclusionConstraintName(\n        tableOrName: Table | string,\n        expression: string,\n    ): string\n\n    /**\n     * Gets the name of the join column used in the one-to-one and many-to-one relations.\n     */\n    joinColumnName(relationName: string, referencedColumnName: string): string\n\n    /**\n     * Gets the name of the join table used in the many-to-many relations.\n     */\n    joinTableName(\n        firstTableName: string,\n        secondTableName: string,\n        firstPropertyName: string,\n        secondPropertyName: string,\n    ): string\n\n    /**\n     * Columns in join tables can have duplicate names in case of self-referencing.\n     * This method provide a resolution for such column names.\n     */\n    joinTableColumnDuplicationPrefix(columnName: string, index: number): string\n\n    /**\n     * Gets the name of the column used for columns in the junction tables.\n     *\n     * The reverse?:boolean parameter denotes if the joinTableColumnName is called for the junctionColumn (false)\n     * or the inverseJunctionColumns (true)\n     */\n    joinTableColumnName(\n        tableName: string,\n        propertyName: string,\n        columnName?: string,\n    ): string\n\n    /**\n     * Gets the name of the column used for columns in the junction tables from the invers side of the relationship.\n     */\n    joinTableInverseColumnName(\n        tableName: string,\n        propertyName: string,\n        columnName?: string,\n    ): string\n\n    /**\n     * Adds globally set prefix to the table name.\n     * This method is executed no matter if prefix was set or not.\n     * Table name is either user's given table name, either name generated from entity target.\n     * Note that table name comes here already normalized by #tableName method.\n     */\n    prefixTableName(prefix: string, tableName: string): string\n\n    /**\n     * Column names for nested sets.\n     */\n    nestedSetColumnNames: { left: string; right: string }\n\n    /**\n     * Column name for materialized paths.\n     */\n    materializedPathColumnName: string\n}\n"], "sourceRoot": ".."}