import { Provide, Inject } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { CoolRpc } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { 
  SettlementInternationalRuleEntity, 
  SettlementInternationalMerchantEntity,
  SettlementExchangeRateEntity 
} from '../entity/international-rule';
import BigNumber from 'bignumber.js';

/**
 * 国际化结算服务
 * 支持全球多国家/地区的商户入驻和结算
 */
@Provide()
export class SettlementInternationalService extends BaseService {
  @Inject()
  rpc: CoolRpc;

  @InjectEntityModel(SettlementInternationalRuleEntity)
  internationalRuleEntity: Repository<SettlementInternationalRuleEntity>;

  @InjectEntityModel(SettlementInternationalMerchantEntity)
  internationalMerchantEntity: Repository<SettlementInternationalMerchantEntity>;

  @InjectEntityModel(SettlementExchangeRateEntity)
  exchangeRateEntity: Repository<SettlementExchangeRateEntity>;

  /**
   * 为指定国家创建结算规则
   */
  async createCountryRule(params: {
    countryCode: string;
    countryName: string;
    localCurrency: string;
    merchantType: string;
    complianceLevel: string;
    baseCommissionRate: number;
    settlementCycle: string;
    supportedPaymentMethods: string[];
    kycLevel: string;
    createdBy: string;
  }) {
    const {
      countryCode,
      countryName,
      localCurrency,
      merchantType,
      complianceLevel,
      baseCommissionRate,
      settlementCycle,
      supportedPaymentMethods,
      kycLevel,
      createdBy
    } = params;

    // 检查是否已存在该国家的规则
    const existingRule = await this.internationalRuleEntity.findOne({
      where: { countryCode, merchantType }
    });

    if (existingRule) {
      throw new Error(`${countryName} 的 ${merchantType} 商户规则已存在`);
    }

    // 获取地区代码
    const regionCode = this.getRegionByCountry(countryCode);
    
    // 获取时区
    const timezone = this.getTimezoneByCountry(countryCode);

    // 创建规则
    const rule = this.internationalRuleEntity.create({
      name: `${countryName} ${merchantType === 'personal' ? '个人' : '企业'}商户结算规则`,
      description: `适用于${countryName}地区的${merchantType === 'personal' ? '个人' : '企业'}商户`,
      countryCode,
      countryName,
      regionCode,
      timezone,
      localCurrency,
      baseCurrency: 'USD', // 默认以美元为基础货币
      supportedCurrencies: [localCurrency, 'USD', 'EUR'],
      merchantType,
      settlementCycle,
      localSettlementCycle: this.calculateLocalSettlementCycle(countryCode, settlementCycle),
      crossBorderCycle: this.calculateCrossBorderCycle(countryCode),
      baseCommissionRate,
      localTransactionRate: baseCommissionRate,
      crossBorderRate: baseCommissionRate + 0.5, // 跨境交易额外收费
      complianceLevel,
      kycLevel,
      requireTaxReporting: this.requiresTaxReporting(countryCode),
      taxRate: this.getTaxRate(countryCode),
      dataProtectionLaw: this.getDataProtectionLaw(countryCode),
      amlRequired: this.requiresAML(countryCode),
      supportedPaymentMethods,
      minTransactionAmount: this.getMinTransactionAmount(countryCode, localCurrency),
      maxTransactionAmount: this.getMaxTransactionAmount(countryCode, localCurrency),
      dailyLimit: this.getDailyLimit(countryCode, localCurrency),
      monthlyLimit: this.getMonthlyLimit(countryCode, localCurrency),
      defaultLanguage: this.getDefaultLanguage(countryCode),
      supportedLanguages: this.getSupportedLanguages(countryCode),
      riskLevel: this.getRiskLevel(countryCode),
      effectiveDate: new Date(),
      createdBy,
      auditStatus: 'pending'
    });

    return await this.internationalRuleEntity.save(rule);
  }

  /**
   * 国际商户入驻申请
   */
  async applyInternationalMerchant(params: {
    merchantId: number;
    countryCode: string;
    merchantType: string;
    legalName: string;
    businessRegistrationNumber?: string;
    taxIdentificationNumber?: string;
    registeredAddress: string;
    bankName: string;
    bankCode: string;
    accountNumber: string;
    accountHolderName: string;
    preferredSettlementCurrency: string;
  }) {
    const {
      merchantId,
      countryCode,
      merchantType,
      legalName,
      businessRegistrationNumber,
      taxIdentificationNumber,
      registeredAddress,
      bankName,
      bankCode,
      accountNumber,
      accountHolderName,
      preferredSettlementCurrency
    } = params;

    // 检查是否已申请
    const existingApplication = await this.internationalMerchantEntity.findOne({
      where: { merchantId, countryCode }
    });

    if (existingApplication) {
      throw new Error('该商户已在此国家申请入驻');
    }

    // 获取适用的结算规则
    const rule = await this.internationalRuleEntity.findOne({
      where: { 
        countryCode, 
        merchantType,
        status: 1 
      }
    });

    if (!rule) {
      throw new Error(`暂不支持${countryCode}地区的${merchantType}商户入驻`);
    }

    // 创建国际商户记录
    const internationalMerchant = this.internationalMerchantEntity.create({
      merchantId,
      countryCode,
      merchantType,
      legalName,
      businessRegistrationNumber,
      taxIdentificationNumber,
      registeredAddress,
      kycStatus: 'pending',
      kycLevel: rule.kycLevel,
      bankName,
      bankCode,
      accountNumber,
      accountHolderName,
      complianceStatus: 'under_review',
      settlementRuleId: rule.id,
      localCurrency: rule.localCurrency,
      preferredSettlementCurrency,
      merchantStatus: 'inactive',
      riskScore: 50 // 默认风险评分
    });

    const result = await this.internationalMerchantEntity.save(internationalMerchant);

    // 触发KYC流程
    await this.initiateKYCProcess(result.id, rule.kycLevel);

    return result;
  }

  /**
   * 国际化结算计算
   */
  async calculateInternationalSettlement(params: {
    merchantId: number;
    countryCode: string;
    amount: number;
    currency: string;
    transactionType: string;
  }) {
    const { merchantId, countryCode, amount, currency, transactionType } = params;

    // 获取商户国际化配置
    const merchantConfig = await this.internationalMerchantEntity.findOne({
      where: { merchantId, countryCode }
    });

    if (!merchantConfig) {
      throw new Error('商户未在该国家开通服务');
    }

    // 获取结算规则
    const rule = await this.internationalRuleEntity.findOne({
      where: { id: merchantConfig.settlementRuleId }
    });

    if (!rule) {
      throw new Error('未找到适用的结算规则');
    }

    // 汇率转换
    const { convertedAmount, exchangeRate } = await this.convertCurrency(
      amount, 
      currency, 
      rule.localCurrency
    );

    // 确定费率
    let appliedRate = rule.baseCommissionRate;
    
    // 跨境交易额外费率
    if (currency !== rule.localCurrency) {
      appliedRate = rule.crossBorderRate;
    }

    // 计算费用
    const commission = new BigNumber(convertedAmount)
      .multipliedBy(appliedRate)
      .dividedBy(100);

    const currencyConversionFee = currency !== rule.localCurrency 
      ? new BigNumber(convertedAmount).multipliedBy(rule.currencyConversionFee).dividedBy(100)
      : new BigNumber(0);

    const bankProcessingFee = new BigNumber(rule.bankProcessingFee);
    
    const totalFees = commission
      .plus(currencyConversionFee)
      .plus(bankProcessingFee);

    const netAmount = new BigNumber(convertedAmount).minus(totalFees);

    // 税务计算
    const taxAmount = rule.requireTaxReporting 
      ? netAmount.multipliedBy(rule.taxRate).dividedBy(100)
      : new BigNumber(0);

    const finalNetAmount = netAmount.minus(taxAmount);

    // 计算结算日期
    const settlementDate = this.calculateInternationalSettlementDate(
      rule.countryCode,
      rule.localSettlementCycle,
      rule.timezone
    );

    return {
      originalAmount: amount,
      originalCurrency: currency,
      convertedAmount: convertedAmount,
      localCurrency: rule.localCurrency,
      exchangeRate,
      appliedRate,
      commission: commission.toNumber(),
      currencyConversionFee: currencyConversionFee.toNumber(),
      bankProcessingFee: bankProcessingFee.toNumber(),
      totalFees: totalFees.toNumber(),
      taxAmount: taxAmount.toNumber(),
      netAmount: finalNetAmount.toNumber(),
      settlementDate,
      complianceChecks: await this.performInternationalComplianceCheck(
        merchantConfig, 
        rule, 
        convertedAmount
      ),
      metadata: {
        ruleId: rule.id,
        countryCode: rule.countryCode,
        merchantType: rule.merchantType,
        calculatedAt: new Date()
      }
    };
  }

  /**
   * 更新汇率
   */
  async updateExchangeRates(source: string = 'api') {
    const currencies = ['USD', 'EUR', 'JPY', 'GBP', 'CNY', 'KRW', 'SGD', 'AUD', 'CAD'];
    const baseCurrency = 'USD';

    for (const targetCurrency of currencies) {
      if (targetCurrency === baseCurrency) continue;

      try {
        // 从外部API获取汇率（这里是模拟）
        const rateData = await this.fetchExchangeRateFromAPI(baseCurrency, targetCurrency);
        
        // 更新或创建汇率记录
        await this.exchangeRateEntity.save({
          baseCurrency,
          targetCurrency,
          rate: rateData.rate,
          bidRate: rateData.bidRate,
          askRate: rateData.askRate,
          source,
          updatedAt: new Date(),
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时有效
          status: 'active'
        });

      } catch (error) {
        console.error(`Failed to update exchange rate for ${targetCurrency}:`, error);
      }
    }
  }

  /**
   * 获取支持的国家列表
   */
  async getSupportedCountries() {
    const rules = await this.internationalRuleEntity.find({
      where: { status: 1 },
      select: ['countryCode', 'countryName', 'localCurrency', 'merchantType', 'complianceLevel']
    });

    // 按国家分组
    const countries = new Map();
    
    rules.forEach(rule => {
      if (!countries.has(rule.countryCode)) {
        countries.set(rule.countryCode, {
          code: rule.countryCode,
          name: rule.countryName,
          currency: rule.localCurrency,
          merchantTypes: [],
          complianceLevel: rule.complianceLevel
        });
      }
      
      const country = countries.get(rule.countryCode);
      if (!country.merchantTypes.includes(rule.merchantType)) {
        country.merchantTypes.push(rule.merchantType);
      }
    });

    return Array.from(countries.values());
  }

  // 私有辅助方法
  private getRegionByCountry(countryCode: string): string {
    const regionMap = {
      'CN': 'Asia', 'JP': 'Asia', 'KR': 'Asia', 'SG': 'Asia',
      'US': 'Americas', 'CA': 'Americas',
      'GB': 'Europe', 'DE': 'Europe', 'FR': 'Europe',
      'AU': 'Oceania'
    };
    return regionMap[countryCode] || 'Unknown';
  }

  private getTimezoneByCountry(countryCode: string): string {
    const timezoneMap = {
      'CN': 'Asia/Shanghai',
      'US': 'America/New_York',
      'JP': 'Asia/Tokyo',
      'KR': 'Asia/Seoul',
      'GB': 'Europe/London',
      'DE': 'Europe/Berlin',
      'SG': 'Asia/Singapore',
      'AU': 'Australia/Sydney',
      'CA': 'America/Toronto'
    };
    return timezoneMap[countryCode] || 'UTC';
  }

  private calculateLocalSettlementCycle(countryCode: string, cycle: string): string {
    // 考虑当地银行工作日和节假日
    const localFactors = {
      'US': { factor: 1, workdays: 'Mon-Fri' },
      'JP': { factor: 1, workdays: 'Mon-Fri' },
      'CN': { factor: 1, workdays: 'Mon-Fri' },
      'DE': { factor: 1, workdays: 'Mon-Fri' }
    };
    
    return cycle; // 简化实现，实际应考虑当地银行工作日
  }

  private calculateCrossBorderCycle(countryCode: string): string {
    // 跨境转账通常需要更长时间
    return 'T+3';
  }

  private requiresTaxReporting(countryCode: string): boolean {
    const taxRequiredCountries = ['US', 'DE', 'FR', 'GB', 'AU', 'CA'];
    return taxRequiredCountries.includes(countryCode);
  }

  private getTaxRate(countryCode: string): number {
    const taxRates = {
      'US': 0, // 平台不代扣，商户自行申报
      'DE': 19, // 德国VAT
      'FR': 20, // 法国VAT
      'GB': 20, // 英国VAT
      'CN': 6   // 中国增值税
    };
    return taxRates[countryCode] || 0;
  }

  private getDataProtectionLaw(countryCode: string): string {
    const laws = {
      'US': 'CCPA',
      'DE': 'GDPR',
      'FR': 'GDPR',
      'GB': 'UK-GDPR',
      'CA': 'PIPEDA',
      'AU': 'Privacy Act'
    };
    return laws[countryCode] || null;
  }

  private requiresAML(countryCode: string): boolean {
    // 大多数发达国家都需要反洗钱检查
    const amlRequiredCountries = ['US', 'GB', 'DE', 'FR', 'AU', 'CA', 'SG'];
    return amlRequiredCountries.includes(countryCode);
  }

  private getMinTransactionAmount(countryCode: string, currency: string): number {
    // 根据当地货币设置最小交易金额
    const minimums = {
      'USD': 1,
      'EUR': 1,
      'GBP': 1,
      'JPY': 100,
      'CNY': 10,
      'KRW': 1000
    };
    return minimums[currency] || 1;
  }

  private getMaxTransactionAmount(countryCode: string, currency: string): number {
    // 根据当地法规设置最大交易金额
    const maximums = {
      'USD': 50000,
      'EUR': 50000,
      'GBP': 50000,
      'JPY': 5000000,
      'CNY': 300000,
      'KRW': 50000000
    };
    return maximums[currency] || 50000;
  }

  private getDailyLimit(countryCode: string, currency: string): number {
    return this.getMaxTransactionAmount(countryCode, currency) * 10;
  }

  private getMonthlyLimit(countryCode: string, currency: string): number {
    return this.getDailyLimit(countryCode, currency) * 30;
  }

  private getDefaultLanguage(countryCode: string): string {
    const languages = {
      'CN': 'zh-CN',
      'US': 'en-US',
      'JP': 'ja-JP',
      'KR': 'ko-KR',
      'DE': 'de-DE',
      'FR': 'fr-FR',
      'GB': 'en-GB'
    };
    return languages[countryCode] || 'en-US';
  }

  private getSupportedLanguages(countryCode: string): string[] {
    const languages = {
      'CN': ['zh-CN', 'en-US'],
      'US': ['en-US', 'es-US'],
      'JP': ['ja-JP', 'en-US'],
      'KR': ['ko-KR', 'en-US'],
      'DE': ['de-DE', 'en-US'],
      'FR': ['fr-FR', 'en-US'],
      'GB': ['en-GB', 'en-US']
    };
    return languages[countryCode] || ['en-US'];
  }

  private getRiskLevel(countryCode: string): string {
    // 根据国家风险评级
    const riskLevels = {
      'US': 'low',
      'GB': 'low',
      'DE': 'low',
      'JP': 'low',
      'CN': 'medium',
      'KR': 'medium'
    };
    return riskLevels[countryCode] || 'medium';
  }

  private async convertCurrency(amount: number, fromCurrency: string, toCurrency: string) {
    if (fromCurrency === toCurrency) {
      return { convertedAmount: amount, exchangeRate: 1 };
    }

    // 获取汇率
    const rate = await this.exchangeRateEntity.findOne({
      where: {
        baseCurrency: 'USD',
        targetCurrency: toCurrency,
        status: 'active'
      },
      order: { updatedAt: 'DESC' }
    });

    if (!rate) {
      throw new Error(`未找到 ${fromCurrency} 到 ${toCurrency} 的汇率`);
    }

    const convertedAmount = new BigNumber(amount).multipliedBy(rate.rate).toNumber();
    return { convertedAmount, exchangeRate: rate.rate };
  }

  private calculateInternationalSettlementDate(countryCode: string, cycle: string, timezone: string): Date {
    // 根据当地时区和银行工作日计算结算日期
    const now = new Date();
    const days = cycle === 'T0' ? 0 : cycle === 'T1' ? 1 : parseInt(cycle.replace('T', ''));
    
    const settlementDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000);
    return settlementDate;
  }

  private async performInternationalComplianceCheck(merchant: any, rule: any, amount: number) {
    // 国际合规检查
    return {
      kycStatus: merchant.kycStatus,
      amlCheck: rule.amlRequired ? 'passed' : 'not_required',
      sanctionCheck: 'passed',
      taxCompliance: rule.requireTaxReporting ? 'required' : 'not_required',
      dataProtection: rule.dataProtectionLaw ? 'compliant' : 'not_applicable'
    };
  }

  private async initiateKYCProcess(merchantId: number, kycLevel: string) {
    // 启动KYC流程（这里是模拟）
    console.log(`Initiating ${kycLevel} KYC process for merchant ${merchantId}`);
  }

  private async fetchExchangeRateFromAPI(baseCurrency: string, targetCurrency: string) {
    // 模拟从外部API获取汇率
    const mockRates = {
      'USD-EUR': { rate: 0.85, bidRate: 0.849, askRate: 0.851 },
      'USD-JPY': { rate: 110.5, bidRate: 110.3, askRate: 110.7 },
      'USD-GBP': { rate: 0.73, bidRate: 0.729, askRate: 0.731 },
      'USD-CNY': { rate: 7.2, bidRate: 7.19, askRate: 7.21 },
      'USD-KRW': { rate: 1180, bidRate: 1179, askRate: 1181 }
    };
    
    const key = `${baseCurrency}-${targetCurrency}`;
    return mockRates[key] || { rate: 1, bidRate: 1, askRate: 1 };
  }
}
