"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocalEventBus = void 0;
const base_1 = require("./base");
class LocalDispatcher {
    constructor() {
        this.pidIdx = 0;
        this.initMessageQueue = [];
    }
    clear() {
        this.mainWorker = null;
        this.childWorker = null;
        this.pidIdx = 0;
    }
    generatePid() {
        return this.pidIdx++;
    }
}
const dispatcher = new LocalDispatcher();
class LocalWorker {
    constructor(pid) {
        this.pid = pid;
    }
    onMessage(handler) {
        this.handler = handler;
    }
    on() {
        // ignore exit event
    }
    getWorkerId() {
        return this.pid;
    }
    terminate() { }
}
class LocalEventBus extends base_1.AbstractEventBus {
    constructor(options = {}) {
        super(options);
        this.worker = new LocalWorker(dispatcher.generatePid());
        if (this.isMain()) {
            this.debugLogger(`Main id=${this.worker.getWorkerId()}`);
            dispatcher.mainWorker = this.worker;
        }
        else {
            this.debugLogger(`Child id=${this.worker.getWorkerId()}`);
            dispatcher.childWorker = this.worker;
        }
    }
    workerSubscribeMessage(subscribeMessageHandler) {
        if (this.isWorker()) {
            dispatcher.childWorker.onMessage(subscribeMessageHandler);
        }
    }
    workerListenMessage(worker, subscribeMessageHandler) {
        if (this.isMain()) {
            dispatcher.mainWorker.onMessage(subscribeMessageHandler);
        }
    }
    workerSendMessage(message) {
        // worker to main
        if (dispatcher.mainWorker.handler) {
            dispatcher.mainWorker.handler(message);
        }
        else {
            dispatcher.initMessageQueue.push({
                to: 'main',
                message,
            });
        }
    }
    mainSendMessage(worker, message) {
        // main to worker
        if (dispatcher.childWorker.handler) {
            dispatcher.childWorker.handler(message);
        }
        else {
            dispatcher.initMessageQueue.push({
                to: 'worker',
                message,
            });
        }
    }
    getWorkerId(worker) {
        return String((worker || this.worker).getWorkerId());
    }
    isMain() {
        return !this.isWorker();
    }
    isWorker() {
        return this.options.isWorker;
    }
    async start(err) {
        if (this.isMain()) {
            await (0, base_1.createWaitHandler)(() => dispatcher.childWorker != null, {
                timeout: this.options.waitWorkerTimeout,
                timeoutCheckInterval: this.options.waitWorkerCheckInterval || 200,
            });
            this.addWorker(dispatcher.childWorker);
            if (dispatcher.initMessageQueue.length) {
                dispatcher.initMessageQueue.forEach(({ to, message }) => {
                    if (to === 'worker') {
                        this.mainSendMessage(dispatcher.childWorker, message);
                    }
                    else {
                        this.workerSendMessage(message);
                    }
                });
                dispatcher.initMessageQueue = [];
            }
        }
        await super.start(err);
    }
    async stop() {
        dispatcher.clear();
        await super.stop();
    }
}
exports.LocalEventBus = LocalEventBus;
//# sourceMappingURL=local.js.map