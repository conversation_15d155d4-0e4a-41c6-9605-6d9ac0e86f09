{"version": 3, "file": "IsAlphanumeric.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAlphanumeric.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,uBAAuB,MAAM,8BAA8B,CAAC;AAGnE,MAAM,CAAC,IAAM,eAAe,GAAG,gBAAgB,CAAC;AAEhD;;;GAGG;AACH,MAAM,UAAU,cAAc,CAAC,KAAc,EAAE,MAAuC;IACpF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC7E,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,cAAc,CAC5B,MAAuC,EACvC,iBAAqC;IAErC,OAAO,UAAU,CACf;QACE,IAAI,EAAE,eAAe;QACrB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,cAAc,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAA3C,CAA2C;YAC/E,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,iDAAiD,EAA9D,CAA8D,EAC5E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAlphanumericValidator from 'validator/lib/isAlphanumeric';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ALPHANUMERIC = 'isAlphanumeric';\n\n/**\n * Checks if the string contains only letters and numbers.\n * If given value is not a string, then it returns false.\n */\nexport function isAlphanumeric(value: unknown, locale?: ValidatorJS.AlphanumericLocale): boolean {\n  return typeof value === 'string' && isAlphanumericValidator(value, locale);\n}\n\n/**\n * Checks if the string contains only letters and numbers.\n * If given value is not a string, then it returns false.\n */\nexport function IsAlphanumeric(\n  locale?: ValidatorJS.AlphanumericLocale,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ALPHANUMERIC,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isAlphanumeric(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only letters and numbers',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}