/// <reference types="node" />
/// <reference types="node" />
import { IMidwayContext } from '../interface';
export declare class ServerResponse<CTX extends IMidwayContext = IMidwayContext> {
    protected readonly ctx: any;
    protected isSuccess: boolean;
    constructor(ctx: CTX);
    static TEXT_TPL: <CTX_1 extends import("../interface").Context>(data: string, isSuccess: boolean, ctx: CTX_1) => unknown;
    static JSON_TPL: <CTX_1 extends import("../interface").Context>(data: Record<any, any>, isSuccess: boolean, ctx: CTX_1) => unknown;
    static BLOB_TPL: <CTX_1 extends import("../interface").Context>(data: Buffer, isSuccess: boolean, ctx: CTX_1) => unknown;
    json(data: Record<any, any>): any;
    text(data: string): any;
    blob(data: Buffer): any;
    success(): this;
    fail(): this;
}
//# sourceMappingURL=base.d.ts.map