{"version": 3, "sources": ["../browser/src/driver/aurora-postgres/AuroraPostgresQueryRunner.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AAInF,OAAO,EAAE,mBAAmB,EAAE,MAAM,iCAAiC,CAAA;AAErE,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAE5D,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAE1C,MAAM,0BAA2B,SAAQ,mBAAmB;IAGxD,YAAY,MAAW,EAAE,IAAqB;QAC1C,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;IACvB,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,yBACT,SAAQ,0BAA0B;IAuBlC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,MAA4B,EAC5B,MAAW,EACX,IAAqB;QAErB,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAEnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;IACxB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,kBAAkB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAEnD,IAAI,IAAI,CAAC,yBAAyB;YAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAA;QAEzC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,qBAAqB,EAAE;iBACvB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAA;gBAC9B,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACJ,SAAS;YACT,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,sBAAsB,EAAE;iBACxB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBACpC,IAAI,CAAC,eAAe,GAAG,OAAO,CAAA;gBAC9B,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAA;QACxC,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,6BAA6B,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC3D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAA;YACrC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAA;YACvC,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QAEtD,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;QAEhC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;QAEhB,IAAI,GAAG,EAAE,cAAc,CAAC,SAAS,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/D,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAA;QAChC,CAAC;QAED,IAAI,GAAG,EAAE,cAAc,CAAC,wBAAwB,CAAC,EAAE,CAAC;YAChD,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,sBAAsB,CAAA;QAChD,CAAC;QAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACvB,OAAO,MAAM,CAAC,GAAG,CAAA;QACrB,CAAC;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,YAAY,CAClB,yDAAyD,CAC5D,CAAA;IACL,CAAC;CACJ", "file": "AuroraPostgresQueryRunner.js", "sourcesContent": ["import { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { AuroraPostgresDriver } from \"./AuroraPostgresDriver\"\nimport { PostgresQueryRunner } from \"../postgres/PostgresQueryRunner\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TypeORMError } from \"../../error\"\n\nclass PostgresQueryRunnerWrapper extends PostgresQueryRunner {\n    driver: any\n\n    constructor(driver: any, mode: ReplicationMode) {\n        super(driver, mode)\n    }\n}\n\n/**\n * Runs queries on a single postgres database connection.\n */\nexport class AuroraPostgresQueryRunner\n    extends PostgresQueryRunnerWrapper\n    implements QueryRunner\n{\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: AuroraPostgresDriver\n\n    protected client: any\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        driver: AuroraPostgresDriver,\n        client: any,\n        mode: ReplicationMode,\n    ) {\n        super(driver, mode)\n\n        this.client = client\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        if (this.databaseConnection)\n            return Promise.resolve(this.databaseConnection)\n\n        if (this.databaseConnectionPromise)\n            return this.databaseConnectionPromise\n\n        if (this.mode === \"slave\" && this.driver.isReplicated) {\n            this.databaseConnectionPromise = this.driver\n                .obtainSlaveConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n                    this.releaseCallback = release\n                    return this.databaseConnection\n                })\n        } else {\n            // master\n            this.databaseConnectionPromise = this.driver\n                .obtainMasterConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n                    this.releaseCallback = release\n                    return this.databaseConnection\n                })\n        }\n\n        return this.databaseConnectionPromise\n    }\n\n    /**\n     * Starts transaction on the current connection.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        if (this.transactionDepth === 0) {\n            await this.client.startTransaction()\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `RELEASE SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.client.commitTransaction()\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.client.rollbackTransaction()\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const raw = await this.client.query(query, parameters)\n\n        const result = new QueryResult()\n\n        result.raw = raw\n\n        if (raw?.hasOwnProperty(\"records\") && Array.isArray(raw.records)) {\n            result.records = raw.records\n        }\n\n        if (raw?.hasOwnProperty(\"numberOfRecordsUpdated\")) {\n            result.affected = raw.numberOfRecordsUpdated\n        }\n\n        if (!useStructuredResult) {\n            return result.raw\n        }\n\n        return result\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `aurora-postgres driver does not support change comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}