{"version": 3, "sources": ["../../src/driver/expo/ExpoDriverFactory.ts"], "names": [], "mappings": ";;;AACA,6CAAyC;AACzC,gEAA4D;AAE5D,MAAa,iBAAiB;IAG1B,YAAY,UAAsB;QAC9B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;IAChC,CAAC;IAED,MAAM;QACF,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACtB,OAAO,IAAI,mCAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QAChD,CAAC;QAED,OAAO,IAAI,uBAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAC1C,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,CAAC,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;IACnE,CAAC;CACJ;AAlBD,8CAkBC", "file": "ExpoDriverFactory.js", "sourcesContent": ["import { DataSource } from \"../../data-source\"\nimport { ExpoDriver } from \"./ExpoDriver\"\nimport { ExpoLegacyDriver } from \"./legacy/ExpoLegacyDriver\"\n\nexport class ExpoDriverFactory {\n    connection: DataSource\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n    }\n\n    create(): ExpoDriver | ExpoLegacyDriver {\n        if (this.isLegacyDriver) {\n            return new ExpoLegacyDriver(this.connection)\n        }\n\n        return new ExpoDriver(this.connection)\n    }\n\n    private get isLegacyDriver(): boolean {\n        return !(\"openDatabaseAsync\" in this.connection.options.driver)\n    }\n}\n"], "sourceRoot": "../.."}