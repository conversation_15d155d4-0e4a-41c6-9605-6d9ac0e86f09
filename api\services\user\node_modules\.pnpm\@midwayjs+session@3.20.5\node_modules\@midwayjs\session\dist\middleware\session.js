"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionMiddleware = void 0;
const core_1 = require("@midwayjs/core");
const store_1 = require("../lib/store");
const util_1 = require("../lib/util");
const assert = require("assert");
const context_1 = require("../lib/context");
const CONTEXT_SESSION = Symbol('context#contextSession');
const _CONTEXT_SESSION = Symbol('context#_contextSession');
/**
 * format and check session options
 * @param  {Object} opts session options
 * @return {Object} new session options
 *
 * @api private
 */
function formatOpts(opts) {
    opts = opts || {};
    // key
    opts.key = opts.key || 'koa.sess';
    // back-compat maxage
    if (!('maxAge' in opts))
        opts.maxAge = opts.maxage;
    // defaults
    if (opts.overwrite == null)
        opts.overwrite = true;
    if (opts.httpOnly == null)
        opts.httpOnly = true;
    // delete null sameSite config
    if (opts.sameSite == null)
        delete opts.sameSite;
    if (opts.signed == null)
        opts.signed = true;
    if (opts.autoCommit == null)
        opts.autoCommit = true;
    // setup encoding/decoding
    if (typeof opts.encode !== 'function') {
        opts.encode = util_1.encode;
    }
    if (typeof opts.decode !== 'function') {
        opts.decode = util_1.decode;
    }
    const store = opts.store;
    if (store) {
        assert(core_1.Types.isFunction(store.get), 'store.get must be function');
        assert(core_1.Types.isFunction(store.set), 'store.set must be function');
        assert(core_1.Types.isFunction(store.destroy), 'store.destroy must be function');
    }
    const externalKey = opts.externalKey;
    if (externalKey) {
        assert(core_1.Types.isFunction(externalKey.get), 'externalKey.get must be function');
        assert(core_1.Types.isFunction(externalKey.set), 'externalKey.set must be function');
    }
    const ContextStore = opts.ContextStore;
    if (ContextStore) {
        assert(core_1.Types.isClass(ContextStore), 'ContextStore must be a class');
        assert(core_1.Types.isFunction(ContextStore.prototype.get), 'ContextStore.prototype.get must be function');
        assert(core_1.Types.isFunction(ContextStore.prototype.set), 'ContextStore.prototype.set must be function');
        assert(core_1.Types.isFunction(ContextStore.prototype.destroy), 'ContextStore.prototype.destroy must be function');
    }
    if (!opts.genid) {
        if (opts.prefix) {
            opts.genid = () => `${opts.prefix}${core_1.Utils.randomUUID()}`;
        }
        else {
            opts.genid = core_1.Utils.randomUUID;
        }
    }
    return opts;
}
/**
 * extend context prototype, add session properties
 *
 * @param  {Object} context koa's context prototype
 * @param  {Object} opts session options
 *
 * @api private
 */
function extendContext(context, opts) {
    // eslint-disable-next-line no-prototype-builtins
    if (context.hasOwnProperty(CONTEXT_SESSION)) {
        return;
    }
    Object.defineProperties(context, {
        [CONTEXT_SESSION]: {
            get() {
                if (this[_CONTEXT_SESSION]) {
                    return this[_CONTEXT_SESSION];
                }
                this[_CONTEXT_SESSION] = new context_1.ContextSession(this, opts);
                return this[_CONTEXT_SESSION];
            },
        },
        session: {
            get() {
                return this[CONTEXT_SESSION].get();
            },
            set(val) {
                this[CONTEXT_SESSION].set(val);
            },
            configurable: true,
        },
        sessionOptions: {
            get() {
                return this[CONTEXT_SESSION].opts;
            },
        },
    });
}
let SessionMiddleware = class SessionMiddleware {
    resolve(app) {
        if (!this.sessionConfig.httpOnly) {
            this.logger.warn('[midway-session]: please set `config.session.httpOnly` to true. It is very dangerous if session can read by client JavaScript.');
        }
        const store = this.sessionStoreManager.getSessionStore();
        if (store) {
            this.sessionConfig.store = store;
        }
        const opts = formatOpts(this.sessionConfig);
        extendContext(app.context, opts);
        return async function session(ctx, next) {
            const sess = ctx[CONTEXT_SESSION];
            if (sess.store) {
                await sess.initFromExternal();
            }
            try {
                await next();
            }
            finally {
                if (opts.autoCommit) {
                    await sess.commit();
                }
            }
        };
    }
    static getName() {
        return 'session';
    }
};
__decorate([
    (0, core_1.Config)('session'),
    __metadata("design:type", Object)
], SessionMiddleware.prototype, "sessionConfig", void 0);
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], SessionMiddleware.prototype, "logger", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", store_1.SessionStoreManager)
], SessionMiddleware.prototype, "sessionStoreManager", void 0);
SessionMiddleware = __decorate([
    (0, core_1.Middleware)()
], SessionMiddleware);
exports.SessionMiddleware = SessionMiddleware;
//# sourceMappingURL=session.js.map