import { IPluginInstance, IPluginHooks, IPluginCommands } from './interface/plugin';
import { ICoreInstance } from './interface/commandCore';
export declare class BasePlugin implements IPluginInstance {
    core: ICoreInstance;
    options: any;
    commands: IPluginCommands;
    hooks: IPluginHooks;
    private name;
    constructor(core: ICoreInstance, options: any);
    getName(): string;
    setStore(key: string, value: any, isGlobalScope?: boolean): void;
    getStore(key: string, scope?: string): any;
    setGlobalDependencies(name: string, version?: string): void;
}
export declare const filterPluginByCommand: (pluginList: any, options: any) => any;
export declare const getPluginClass: (pluginList: any, options: any) => Promise<any[]>;
//# sourceMappingURL=plugin.d.ts.map