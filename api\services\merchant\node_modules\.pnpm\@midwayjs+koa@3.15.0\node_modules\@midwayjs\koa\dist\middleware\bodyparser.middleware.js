"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BodyParserMiddleware = void 0;
const koaBodyParser = require("koa-bodyparser");
const core_1 = require("@midwayjs/core");
let BodyParserMiddleware = class BodyParserMiddleware {
    resolve() {
        // use bodyparser middleware
        if (this.bodyparserConfig.enable) {
            return koaBodyParser(this.bodyparserConfig);
        }
    }
    static getName() {
        return 'bodyParser';
    }
};
__decorate([
    (0, core_1.Config)('bodyParser'),
    __metadata("design:type", Object)
], BodyParserMiddleware.prototype, "bodyparserConfig", void 0);
BodyParserMiddleware = __decorate([
    (0, core_1.Middleware)()
], BodyParserMiddleware);
exports.BodyParserMiddleware = BodyParserMiddleware;
//# sourceMappingURL=bodyparser.middleware.js.map