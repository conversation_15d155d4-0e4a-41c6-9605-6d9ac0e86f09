{"version": 3, "file": "transformation-type.enum.js", "sourceRoot": "", "sources": ["../../../src/enums/transformation-type.enum.ts"], "names": [], "mappings": "AAAA,MAAM,CAAN,IAAY,kBAIX;AAJD,WAAY,kBAAkB;IAC5B,+EAAc,CAAA;IACd,+EAAc,CAAA;IACd,+EAAc,CAAA;AAChB,CAAC,EAJW,kBAAkB,KAAlB,kBAAkB,QAI7B", "sourcesContent": ["export enum TransformationType {\n  PLAIN_TO_CLASS,\n  CLASS_TO_PLAIN,\n  CLASS_TO_CLASS,\n}\n"]}