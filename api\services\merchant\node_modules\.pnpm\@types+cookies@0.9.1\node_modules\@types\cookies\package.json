{"name": "@types/cookies", "version": "0.9.1", "description": "TypeScript definitions for cookies", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/cookies", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "j<PERSON><PERSON>", "githubUsername": "j<PERSON>lu", "url": "https://github.com/jkeylu"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON><PERSON>bas"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/cookies"}, "scripts": {}, "dependencies": {"@types/connect": "*", "@types/express": "*", "@types/keygrip": "*", "@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "85092bc8e5ebb0107d0af49e6b6a36ab582597aa93b45ebf99cef398243e9169", "typeScriptVersion": "5.1"}