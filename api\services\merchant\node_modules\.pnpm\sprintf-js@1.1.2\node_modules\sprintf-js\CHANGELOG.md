## 1.1.2 (NOT YET RELEASED)

* Update <PERSON> config to test on all LTE Node versions (i.e. 6+)
* Reorganize README; add instructions re polyfills
* Refactor the code
* Upgrade dependencies
* Fix minifying issue with missing semicolons
* Configure .npmignore to reduce package size


## 1.1.1 (2017-05-29)

* This CHANGELOG
* Various optimizations for modern browsers
* Fix %g, %o, %x and %X specifiers
* Use ESLint instead of JSHint
* Add CONTRIBUTORS file
