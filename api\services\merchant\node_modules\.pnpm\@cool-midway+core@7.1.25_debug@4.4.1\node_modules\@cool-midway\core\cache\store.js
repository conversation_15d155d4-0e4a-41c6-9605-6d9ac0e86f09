"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolCacheStore = void 0;
const FsStore = require("@cool-midway/cache-manager-fs-hash");
/**
 * cool 基于磁盘的缓存
 */
class FsCacheStore {
    constructor(options = {}) {
        options = {
            ...options,
            path: "cache",
            ttl: -1,
        };
        this.options = options;
        this.store = FsStore.create(options);
    }
    /**
     * 获得
     * @param key
     * @returns
     */
    async get(key) {
        return await this.store.get(key);
    }
    /**
     * 设置
     * @param key
     * @param value
     * @param ttl
     */
    async set(key, value, ttl) {
        let t = ttl ? ttl : this.options.ttl;
        if (t > 0) {
            t = t / 1000;
        }
        await this.store.set(key, value, {
            ttl: t,
        });
    }
    /**
     * 删除
     * @param key
     */
    async del(key) {
        await this.store.del(key);
    }
    /**
     * 重置
     */
    async reset() {
        await this.store.reset();
    }
}
const CoolCacheStore = function (options = {}) {
    return new FsCacheStore(options);
};
exports.CoolCacheStore = CoolCacheStore;
