{"version": 3, "sources": ["../../src/driver/Driver.ts"], "names": [], "mappings": "", "file": "Driver.js", "sourcesContent": ["import { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { ColumnType } from \"./types/ColumnTypes\"\nimport { CteCapabilities } from \"./types/CteCapabilities\"\nimport { MappedColumnTypes } from \"./types/MappedColumnTypes\"\nimport { SchemaBuilder } from \"../schema-builder/SchemaBuilder\"\nimport { DataTypeDefaults } from \"./types/DataTypeDefaults\"\nimport { BaseDataSourceOptions } from \"../data-source/BaseDataSourceOptions\"\nimport { TableColumn } from \"../schema-builder/table/TableColumn\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ReplicationMode } from \"./types/ReplicationMode\"\nimport { Table } from \"../schema-builder/table/Table\"\nimport { View } from \"../schema-builder/view/View\"\nimport { TableForeignKey } from \"../schema-builder/table/TableForeignKey\"\nimport { UpsertType } from \"./types/UpsertType\"\nimport { OnDeleteType } from \"../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../metadata/types/OnUpdateType\"\n\nexport type ReturningType = \"insert\" | \"update\" | \"delete\"\n\n/**\n * Driver organizes TypeORM communication with specific database management system.\n */\nexport interface Driver {\n    /**\n     * Connection options.\n     */\n    options: BaseDataSourceOptions\n\n    /**\n     * Database version/release. Often requires a SQL query to the DB, so it is not always set\n     */\n    version?: string\n\n    /**\n     * Database name used to perform all write queries.\n     *\n     * todo: probably move into query runner.\n     */\n    database?: string\n\n    /**\n     * Schema name used to perform all write queries.\n     */\n    schema?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport: boolean\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport: \"simple\" | \"nested\" | \"none\"\n\n    /**\n     * Gets list of supported column data types by a driver.\n     */\n    supportedDataTypes: ColumnType[]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[]\n\n    /**\n     * Returns list of supported onDelete types by driver\n     */\n    supportedOnDeleteTypes?: OnDeleteType[]\n\n    /**\n     * Returns list of supported onUpdate types by driver\n     */\n    supportedOnUpdateTypes?: OnUpdateType[]\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[]\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes\n\n    /**\n     * The prefix used for the parameters\n     */\n    parametersPrefix?: string\n\n    /**\n     * Max length allowed by the DBMS for aliases (execution of queries).\n     */\n    maxAliasLength?: number\n\n    cteCapabilities: CteCapabilities\n\n    /**\n     * Dummy table name\n     */\n    dummyTableName?: string\n\n    /**\n     * Performs connection to the database.\n     * Depend on driver type it may create a connection pool.\n     */\n    connect(): Promise<void>\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void>\n\n    /**\n     * Closes connection with database and releases all resources.\n     */\n    disconnect(): Promise<void>\n\n    /**\n     * Synchronizes database schema (creates tables, indices, etc).\n     */\n    createSchemaBuilder(): SchemaBuilder\n\n    /**\n     * Creates a query runner used for common queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]]\n\n    /**\n     * Escapes a table name, column name or an alias.\n     *\n     * todo: probably escape should be able to handle dots in the names and automatically escape them\n     */\n    escape(name: string): string\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { tableName: string; schema?: string; database?: string }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, column: ColumnMetadata): any\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type.\n     */\n    prepareHydratedValue(value: any, column: ColumnMetadata): any\n\n    /**\n     * Transforms type of the given column to a database column type.\n     */\n    normalizeType(column: {\n        type?: ColumnType | string\n        length?: number | string\n        precision?: number | null\n        scale?: number\n        isArray?: boolean\n    }): string\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean\n\n    /**\n     * Calculates column length taking into account the default length values.\n     */\n    getColumnLength(column: ColumnMetadata): string\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    createFullType(column: TableColumn): string\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any>\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any>\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(\n        metadata: EntityMetadata,\n        insertResult: any,\n        entityIndex?: number,\n        entityNum?: number,\n    ): ObjectLiteral | undefined\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[]\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(returningType: ReturningType): boolean\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string\n}\n"], "sourceRoot": ".."}