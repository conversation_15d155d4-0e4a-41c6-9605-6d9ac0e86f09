"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Types = exports.isNullOrUndefined = exports.isNull = exports.isUndefined = exports.isRegExp = exports.isSet = exports.isMap = exports.isProxy = exports.isNumber = exports.isPlainObject = exports.isObject = exports.isFunction = exports.isPromise = exports.isGeneratorFunction = exports.isAsyncFunction = exports.isClass = exports.isString = void 0;
const util = require("util");
const ToString = Function.prototype.toString;
const hasOwn = Object.prototype.hasOwnProperty;
const toStr = Object.prototype.toString;
function isString(value) {
    return typeof value === 'string';
}
exports.isString = isString;
function isClass(fn) {
    if (typeof fn !== 'function') {
        return false;
    }
    if (/^class[\s{]/.test(ToString.call(fn))) {
        return true;
    }
}
exports.isClass = isClass;
function isAsyncFunction(value) {
    return util.types.isAsyncFunction(value);
}
exports.isAsyncFunction = isAsyncFunction;
function isGeneratorFunction(value) {
    return util.types.isGeneratorFunction(value);
}
exports.isGeneratorFunction = isGeneratorFunction;
function isPromise(value) {
    return util.types.isPromise(value);
}
exports.isPromise = isPromise;
function isFunction(value) {
    return typeof value === 'function';
}
exports.isFunction = isFunction;
function isObject(value) {
    return value !== null && typeof value === 'object';
}
exports.isObject = isObject;
function isPlainObject(obj) {
    if (!obj || toStr.call(obj) !== '[object Object]') {
        return false;
    }
    const hasOwnConstructor = hasOwn.call(obj, 'constructor');
    const hasIsPrototypeOf = obj.constructor &&
        obj.constructor.prototype &&
        hasOwn.call(obj.constructor.prototype, 'isPrototypeOf');
    // Not own constructor property must be Object
    if (obj.constructor && !hasOwnConstructor && !hasIsPrototypeOf) {
        return false;
    }
    // Own properties are enumerated firstly, so to speed up,
    // if last one is own, then all properties are own.
    let key;
    for (key in obj) {
        /**/
    }
    return typeof key === 'undefined' || hasOwn.call(obj, key);
}
exports.isPlainObject = isPlainObject;
function isNumber(value) {
    return typeof value === 'number';
}
exports.isNumber = isNumber;
function isProxy(value) {
    return util.types.isProxy(value);
}
exports.isProxy = isProxy;
function isMap(value) {
    return util.types.isMap(value);
}
exports.isMap = isMap;
function isSet(value) {
    return util.types.isSet(value);
}
exports.isSet = isSet;
function isRegExp(value) {
    return util.types.isRegExp(value);
}
exports.isRegExp = isRegExp;
function isUndefined(value) {
    return value === undefined;
}
exports.isUndefined = isUndefined;
function isNull(value) {
    return value === null;
}
exports.isNull = isNull;
function isNullOrUndefined(value) {
    return isUndefined(value) || isNull(value);
}
exports.isNullOrUndefined = isNullOrUndefined;
exports.Types = {
    isClass,
    isAsyncFunction,
    isGeneratorFunction,
    isString,
    isPromise,
    isFunction,
    isObject,
    isPlainObject,
    isNumber,
    isProxy,
    isMap,
    isSet,
    isRegExp,
    isUndefined,
    isNull,
    isNullOrUndefined,
};
//# sourceMappingURL=types.js.map