"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayHttpError = exports.MidwayError = exports.registerErrorCode = void 0;
const http_1 = require("http");
const codeGroup = new Set();
/**
 * Register error group and code, return the standard ErrorCode
 * @param errorGroup
 * @param errorCodeMapping
 */
function registerErrorCode(errorGroup, errorCodeMapping) {
    if (codeGroup.has(errorGroup)) {
        throw new MidwayError(`Error group ${errorGroup} is duplicated, please check before adding.`);
    }
    else {
        codeGroup.add(errorGroup);
    }
    const newCodeEnum = {};
    // ERROR => GROUP_10000
    for (const errKey in errorCodeMapping) {
        newCodeEnum[errKey] =
            errorGroup.toUpperCase() +
                '_' +
                String(errorCodeMapping[errKey]).toUpperCase();
    }
    return newCodeEnum;
}
exports.registerErrorCode = registerErrorCode;
class MidwayError extends Error {
    constructor(message, code, options) {
        super(message);
        if (!code || typeof code === 'object') {
            options = code;
            code = 'MIDWAY_10000';
        }
        this.name = this.constructor.name;
        this.code = code;
        this.cause = options === null || options === void 0 ? void 0 : options.cause;
    }
}
exports.MidwayError = MidwayError;
class MidwayHttpError extends MidwayError {
    constructor(resOrMessage, status, code, options) {
        super(resOrMessage
            ? typeof resOrMessage === 'string'
                ? resOrMessage
                : resOrMessage.message
            : http_1.STATUS_CODES[status], code !== null && code !== void 0 ? code : String(status), options);
        if (resOrMessage && resOrMessage['stack']) {
            this.stack = resOrMessage['stack'];
        }
        this.status = status;
    }
}
exports.MidwayHttpError = MidwayHttpError;
//# sourceMappingURL=base.js.map