{"version": 3, "sources": ["../../src/driver/sqlserver/authentication/AzureActiveDirectoryDefaultAuthentication.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryDefaultAuthentication.js", "sourcesContent": ["export interface AzureActiveDirectoryDefaultAuthentication {\n    /**\n     * This uses DefaultAzureCredential from @azure/identity to try multiple methods of authentication\n     */\n    type: \"azure-active-directory-default\"\n    options: {\n        /**\n         * The clientId of the user you want to log in with, mapped to the managedIdentityClientId in tedious\n         */\n        clientId?: string\n    }\n}\n"], "sourceRoot": "../../.."}