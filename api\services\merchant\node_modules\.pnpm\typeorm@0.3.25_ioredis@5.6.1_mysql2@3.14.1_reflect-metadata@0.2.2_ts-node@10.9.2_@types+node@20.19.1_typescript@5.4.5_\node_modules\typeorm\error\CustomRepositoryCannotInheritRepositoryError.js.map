{"version": 3, "sources": ["../../src/error/CustomRepositoryCannotInheritRepositoryError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,4CAA6C,SAAQ,2BAAY;IAC1E,YAAY,UAAe;QACvB,KAAK,CACD,4BACI,OAAO,UAAU,KAAK,UAAU;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IACjC,GAAG;YACC,+FAA+F,CACtG,CAAA;IACL,CAAC;CACJ;AAXD,oGAWC", "file": "CustomRepositoryCannotInheritRepositoryError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if custom repository inherits Repository class however entity is not set in @EntityRepository decorator.\n */\nexport class CustomRepositoryCannotInheritRepositoryError extends TypeORMError {\n    constructor(repository: any) {\n        super(\n            `Custom entity repository ${\n                typeof repository === \"function\"\n                    ? repository.name\n                    : repository.constructor.name\n            } ` +\n                ` cannot inherit Repository class without entity being set in the @EntityRepository decorator.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}