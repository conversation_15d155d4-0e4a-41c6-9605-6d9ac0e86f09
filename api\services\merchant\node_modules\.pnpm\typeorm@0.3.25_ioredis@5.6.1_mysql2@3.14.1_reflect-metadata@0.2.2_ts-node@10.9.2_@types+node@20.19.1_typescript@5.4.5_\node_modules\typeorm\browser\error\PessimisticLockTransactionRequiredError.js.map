{"version": 3, "sources": ["../browser/src/error/PessimisticLockTransactionRequiredError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uCAAwC,SAAQ,YAAY;IACrE;QACI,KAAK,CAAC,uDAAuD,CAAC,CAAA;IAClE,CAAC;CACJ", "file": "PessimisticLockTransactionRequiredError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when a transaction is required for the current operation, but there is none open.\n */\nexport class PessimisticLockTransactionRequiredError extends TypeORMError {\n    constructor() {\n        super(`An open transaction is required for pessimistic lock.`)\n    }\n}\n"], "sourceRoot": ".."}