"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Validate = void 0;
const core_1 = require("@midwayjs/core");
const constants_1 = require("../constants");
function Validate(options = {}) {
    return (target, methodName, descriptor) => {
        (0, core_1.savePropertyMetadata)(constants_1.VALIDATE_KEY, options, target, methodName);
    };
}
exports.Validate = Validate;
//# sourceMappingURL=validate.js.map