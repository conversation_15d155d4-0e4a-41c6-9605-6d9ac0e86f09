{"name": "eslint-visitor-keys", "version": "3.3.0", "description": "Constants and utilities about visitor keys to traverse AST.", "type": "module", "main": "dist/eslint-visitor-keys.cjs", "types": "./dist/index.d.ts", "exports": {".": [{"import": "./lib/index.js", "require": "./dist/eslint-visitor-keys.cjs"}, "./dist/eslint-visitor-keys.cjs"], "./package.json": "./package.json"}, "files": ["dist/index.d.ts", "dist/visitor-keys.d.ts", "dist/eslint-visitor-keys.cjs", "lib"], "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "devDependencies": {"c8": "^7.7.3", "eslint": "^7.29.0", "eslint-config-eslint": "^7.0.0", "eslint-plugin-jsdoc": "^35.4.0", "eslint-plugin-node": "^11.1.0", "eslint-release": "^3.2.0", "mocha": "^9.0.1", "opener": "^1.5.2", "rollup": "^2.52.1", "tsd": "^0.19.1", "typescript": "^4.5.5"}, "scripts": {"prepare": "npm run build", "build": "rollup -c && npm run tsc", "lint": "eslint .", "tsc": "tsc", "tsd": "tsd", "test": "mocha tests/lib/**/*.cjs && c8 mocha tests/lib/**/*.js && npm run tsd", "coverage": "c8 report --reporter lcov && opener coverage/lcov-report/index.html", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}, "repository": "eslint/eslint-visitor-keys", "keywords": [], "author": "<PERSON><PERSON> (https://github.com/mysticatea)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/eslint/eslint-visitor-keys/issues"}, "homepage": "https://github.com/eslint/eslint-visitor-keys#readme"}