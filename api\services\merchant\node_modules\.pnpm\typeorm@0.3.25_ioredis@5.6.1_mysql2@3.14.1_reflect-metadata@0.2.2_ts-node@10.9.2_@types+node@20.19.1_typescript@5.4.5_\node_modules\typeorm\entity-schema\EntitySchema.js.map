{"version": 3, "sources": ["../../src/entity-schema/EntitySchema.ts"], "names": [], "mappings": ";;;AAEA;;GAEG;AACH,MAAa,YAAY;IAGrB,YAAmB,OAA+B;QAA/B,YAAO,GAAP,OAAO,CAAwB;QAFzC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;IAEE,CAAC;CACzD;AAJD,oCAIC", "file": "EntitySchema.js", "sourcesContent": ["import { EntitySchemaOptions } from \"./EntitySchemaOptions\"\n\n/**\n * Interface for entity metadata mappings stored inside \"schemas\" instead of models decorated by decorators.\n */\nexport class EntitySchema<T = any> {\n    readonly \"@instanceof\" = Symbol.for(\"EntitySchema\")\n\n    constructor(public options: EntitySchemaOptions<T>) {}\n}\n"], "sourceRoot": ".."}