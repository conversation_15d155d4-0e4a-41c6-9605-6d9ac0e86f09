#!/sbin/openrc-run
# Copyright 2013-2022 the PM2 project authors. All rights reserved.
# Init script automatically generated by pm2 startup

description="Production process manager for Node.js apps with a built-in load balancer."

extra_started_commands="reload"

PM2="%PM2_PATH%"
user=${PM2_USER:-%USER%}
export PM2_HOME=$(eval echo ~${user})"/.pm2/"
# Options for start-stop-daemon (default start function)
command=${PM2}
command_user=${user}
command_args="resurrect"
pidfile=${PM2_HOME}/pm2.pid

run_pm2_as_user() {
	einfo "${PM2} $@"
	eval su -l ${user} -c \'${PM2} $@\'
}

depend() {
	need net
	need localmount
	after bootmisc
}

start_post() {
	if [ "${user}" == "root" ]; then
		ewarn "PM2: Better run this daemon as a non root user. To set this user create"
		ewarn "PM2: /etc/conf.d/pm2 file and define 'PM2_USER=user' there."
		ewarn "PM2: Note user MUST have home directory for PM2 logs/state/etc..."
	fi
	einfo "PM2: Process Manager started. To start services run:"
	einfo "PM2: # su -l ${user} -c '$PM2 start /path/to/app'"
}

stop() {
	ebegin "Stopping PM2 process manager..."
	run_pm2_as_user dump
	run_pm2_as_user kill
	eend $?
}

reload() {
	ebegin "Reloading pm2"
	run_pm2_as_user reload all
	eend $?
}

# vim: ts=4
