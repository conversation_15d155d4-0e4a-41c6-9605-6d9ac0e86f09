const { asyncWrapper, start } = require('<%=starter %>');
const SimpleLock  = require('@midwayjs/simple-lock').default;
const lock = new SimpleLock();
const layers = [];
<% layerDeps.forEach(function(layer){ %>
try {
  const <%=layer.name%> = require('<%=layer.path%>');
  layers.push(<%=layer.name%>);
} catch(e) { }
<% }); %>

let runtime;
let initStatus = 'uninitialized';
let initError;

const initializeMethod = async (initializeContext = {}) => {
  initStatus = 'initialing';
  return lock.sureOnce(async () => {
    runtime = await start({
      layers: layers,
      isAppMode: true,
      initContext: initializeContext,
      runtimeConfig: <%-JSON.stringify(runtimeConfig)%>,
    });
    initStatus = 'initialized';
  }, 'APP_START_LOCK_KEY');
};

exports.<%=initializer%> = asyncWrapper(async (...args) => {
  console.log(`initializer: process uptime: ${process.uptime()}, initStatus: ${initStatus}`);
  if (initStatus === 'initializationError') {
    console.error('init failed due to init status is error, and that error is: ' + initError);
    console.error('FATAL: duplicated init! Will exit with code 121.');
    process.exit(121);
  }
  if (initStatus === 'initialized') {
    console.warn('skip init due to init status is initialized');
    return;
  }
  if (initStatus !== 'uninitialized') {
    throw new Error('init failed due to init status is ' + initStatus);
  }
  try {
    if (initStatus !== 'initialized') {
      await initializeMethod();
    }
  } catch (e) {
    initStatus = 'initializationError';
    initError = e;
    throw e;
  }
});


<% handlers.forEach(function(handlerData){ %>
  exports.<%=handlerData.name%> = asyncWrapper(async (...args) => {
    <% if(initializeInHandler) { %>
    try {
      if (initStatus !== 'initialized') {
        await initializeMethod();
      }
    } catch (e) {
      initStatus = 'initializationError';
      initError = e;
      throw e;
    }
    <% } %>
    if (initStatus !== 'initialized') {
      throw new Error('invoke failed due to init status is ' + initStatus);
    }
    return runtime.asyncEvent()(...args);
  });
<% }); %>
