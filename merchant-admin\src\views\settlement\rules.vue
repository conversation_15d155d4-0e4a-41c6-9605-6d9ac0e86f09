<template>
  <div class="settlement-rules-page">
    <!-- 操作区域 -->
    <div class="custom-card art-custom-card filter-card">
      <div class="custom-card-content">
        <el-form :inline="true">
          <el-form-item>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon>
              新增规则
            </el-button>
            <el-button type="success" @click="handleBatchEnable">
              <el-icon><Check /></el-icon>
              批量启用
            </el-button>
            <el-button type="warning" @click="handleBatchDisable">
              <el-icon><Close /></el-icon>
              批量禁用
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 规则列表 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <el-table
          :data="rulesList"
          v-loading="loading"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="规则名称" min-width="150" />
          <el-table-column prop="merchantType" label="适用商户" width="120">
            <template #default="{ row }">
              <el-tag :type="getMerchantTypeTag(row.merchantType)">
                {{ getMerchantTypeName(row.merchantType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="settlementCycle" label="结算周期" width="120">
            <template #default="{ row }">
              <el-tag type="primary">{{ getSettlementCycleName(row.settlementCycle) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="creatorAuthType" label="认证类型" width="120">
            <template #default="{ row }">
              <el-tag size="small" :type="getCreatorAuthTag(row.creatorAuthType)">
                {{ getCreatorAuthName(row.creatorAuthType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="isNewMerchant" label="优惠政策" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.isNewMerchant" size="small" class="policy-tag">前3个月</el-tag>
              <el-tag v-else-if="row.creatorAuthType === 'handicraft'" size="small" class="policy-tag">已认证</el-tag>
              <el-tag v-else-if="row.creatorAuthType === 'heritage'" size="small" class="policy-tag">已认证</el-tag>
              <el-tag v-else-if="row.creatorAuthType === 'none'" size="small" class="normal-policy-tag">普通</el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="commissionRate" label="交易佣金" width="100">
            <template #default="{ row }">
              <span>{{ row.commissionRate }}%</span>
            </template>
          </el-table-column>
          <el-table-column prop="fees" label="手续费" width="120">
            <template #default="{ row }">
              <div class="fee-info">
                <div v-if="row.transactionFee > 0">交易: ¥{{ row.transactionFee }}/笔</div>
                <div v-if="row.withdrawFee > 0">提现: ¥{{ row.withdrawFee }}/笔</div>
                <div v-if="row.serviceFee > 0">服务: ¥{{ row.serviceFee }}/月</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="minAmount" label="结算限制" width="120">
            <template #default="{ row }">
              <div class="amount-range">
                <div>最小: ¥{{ row.minAmount }}</div>
                <div v-if="row.maxAmount > 0">最大: ¥{{ row.maxAmount }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="specialRules" label="特殊规则" width="120">
            <template #default="{ row }">
              <div class="special-rules">
                <el-tag v-if="row.newMerchantDiscount?.enabled" size="small" type="success">新商户优惠</el-tag>
                <el-tag v-if="row.promotionRule?.enabled" size="small" type="danger">活动费率</el-tag>
                <el-tag v-if="row.holidayRule?.enabled" size="small" type="info">节假日</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="80">
            <template #default="{ row }">
              <el-switch
                v-model="row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="80" />
          <el-table-column prop="createTime" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatTime(row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="info" size="small" @click="handleCopy(row)">
                复制
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="800px"
      @close="resetForm"
    >
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基础配置 -->
        <el-tab-pane label="基础配置" name="basic">
          <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="规则名称" prop="name">
                  <el-input v-model="form.name" placeholder="请输入规则名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="适用商户类型" prop="merchantType">
                  <el-select v-model="form.merchantType" placeholder="选择商户类型" style="width: 100%">
                    <el-option label="全部商户" value="all" />
                    <el-option label="个人商户" value="personal" />
                    <el-option label="企业商户" value="enterprise" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="认证类型" prop="creatorAuthType">
                  <el-select v-model="form.creatorAuthType" placeholder="选择认证类型" style="width: 100%">
                    <el-option label="普通" value="none" />
                    <el-option label="手工艺人认证" value="handicraft" />
                    <el-option label="非遗传承人认证" value="heritage" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否新商户优惠" prop="isNewMerchant">
                  <el-select v-model="form.isNewMerchant" placeholder="选择优惠类型" style="width: 100%">
                    <el-option label="否" :value="false" />
                    <el-option label="是（前3个月2.0%优惠）" :value="true" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结算周期" prop="settlementCycle">
                  <el-select v-model="form.settlementCycle" placeholder="选择结算周期" style="width: 100%">
                    <el-option label="T+0（实时结算）" value="T0" />
                    <el-option label="T+1（次日结算）" value="T1" />
                    <el-option label="T+3（3日后结算）" value="T3" />
                    <el-option label="T+7（7日后结算）" value="T7" />
                    <el-option label="周结算" value="weekly" />
                    <el-option label="月结算" value="monthly" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="优先级" prop="priority">
                  <el-input-number
                    v-model="form.priority"
                    :min="1"
                    :max="100"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="规则描述">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="3"
                placeholder="请输入规则描述"
              />
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 费率配置 -->
        <el-tab-pane label="费率配置" name="rates">
          <el-form :model="form" label-width="120px">
            <el-card class="rate-card" shadow="never">
              <template #header>
                <span>佣金配置</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="基础佣金率">
                    <el-input-number
                      v-model="form.commissionRate"
                      :min="0"
                      :max="100"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="最低佣金">
                    <el-input-number
                      v-model="form.minCommission"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="最高佣金">
                    <el-input-number
                      v-model="form.maxCommission"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="rate-card" shadow="never">
              <template #header>
                <span>手续费配置</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="交易手续费">
                    <el-input-number
                      v-model="form.transactionFee"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元/笔</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="提现手续费">
                    <el-input-number
                      v-model="form.withdrawFee"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元/笔</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="服务费">
                    <el-input-number
                      v-model="form.serviceFee"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元/月</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="rate-card" shadow="never">
              <template #header>
                <span>结算限制</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最小结算金额">
                    <el-input-number
                      v-model="form.minAmount"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最大结算金额">
                    <el-input-number
                      v-model="form.maxAmount"
                      :min="0"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">元</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>

        <!-- 阶梯费率 -->
        <el-tab-pane label="阶梯费率" name="ladder">
          <el-form label-width="120px">
            <el-form-item>
              <el-switch
                v-model="form.enableLadder"
                active-text="启用阶梯费率"
                inactive-text="禁用阶梯费率"
              />
            </el-form-item>

            <div v-if="form.enableLadder">
              <el-card shadow="never">
                <template #header>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span>交易量阶梯</span>
                    <el-button type="primary" size="small" @click="addLadderRule">
                      <el-icon><Plus /></el-icon>
                      添加阶梯
                    </el-button>
                  </div>
                </template>

                <el-table :data="form.ladderRules" style="width: 100%">
                  <el-table-column label="阶梯名称" width="150">
                    <template #default="{ row, $index }">
                      <el-input v-model="row.name" placeholder="阶梯名称" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column label="交易量范围" width="200">
                    <template #default="{ row, $index }">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <el-input-number v-model="row.minAmount" :min="0" size="small" style="width: 80px" />
                        <span>-</span>
                        <el-input-number v-model="row.maxAmount" :min="0" size="small" style="width: 80px" />
                        <span>元</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="佣金率" width="120">
                    <template #default="{ row, $index }">
                      <div style="display: flex; align-items: center; gap: 8px;">
                        <el-input-number v-model="row.rate" :min="0" :max="100" :precision="2" size="small" style="width: 80px" />
                        <span>%</span>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template #default="{ row, $index }">
                      <el-button type="danger" size="small" @click="removeLadderRule($index)">
                        删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-card>
            </div>
          </el-form>
        </el-tab-pane>



        <!-- 智能定价 -->
        <el-tab-pane label="智能定价" name="pricing">
          <el-form label-width="120px">
            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>动态费率引擎</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item>
                    <el-switch
                      v-model="form.enableDynamicPricing"
                      active-text="启用动态定价"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="调整频率">
                    <el-select v-model="form.pricingFrequency" style="width: 100%">
                      <el-option label="实时调整" value="realtime" />
                      <el-option label="每日调整" value="daily" />
                      <el-option label="每周调整" value="weekly" />
                      <el-option label="每月调整" value="monthly" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <div v-if="form.enableDynamicPricing">
                <el-divider content-position="left">定价因子配置</el-divider>
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="交易量权重">
                      <el-slider v-model="form.volumeWeight" :max="100" show-input />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="信用评分权重">
                      <el-slider v-model="form.creditWeight" :max="100" show-input />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="市场竞争权重">
                      <el-slider v-model="form.marketWeight" :max="100" show-input />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="风险评估权重">
                      <el-slider v-model="form.riskWeight" :max="100" show-input />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>

            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>竞争性定价</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item>
                    <el-switch
                      v-model="form.enableCompetitivePricing"
                      active-text="启用竞争性定价"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="价格匹配策略">
                    <el-select v-model="form.priceMatchStrategy" style="width: 100%">
                      <el-option label="最低价匹配" value="lowest" />
                      <el-option label="平均价匹配" value="average" />
                      <el-option label="优质价匹配" value="premium" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>

        <!-- 特殊规则 -->
        <el-tab-pane label="特殊规则" name="special">
          <el-form label-width="120px">
            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>新商户优惠</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item>
                    <el-switch
                      v-model="form.newMerchantDiscount.enabled"
                      active-text="启用新商户优惠"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="优惠期限">
                    <el-input-number
                      v-model="form.newMerchantDiscount.duration"
                      :min="1"
                      :max="12"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">个月</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="优惠费率">
                    <el-input-number
                      v-model="form.newMerchantDiscount.rate"
                      :min="0"
                      :max="100"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">%</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>节假日规则</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item>
                    <el-switch
                      v-model="form.holidayRule.enabled"
                      active-text="启用节假日规则"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="节假日处理">
                    <el-select v-model="form.holidayRule.action" style="width: 100%">
                      <el-option label="延迟到工作日" value="delay" />
                      <el-option label="正常结算" value="normal" />
                      <el-option label="暂停结算" value="pause" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>活动期间规则</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item>
                    <el-switch
                      v-model="form.promotionRule.enabled"
                      active-text="启用活动规则"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="活动费率">
                    <el-input-number
                      v-model="form.promotionRule.rate"
                      :min="0"
                      :max="100"
                      :precision="2"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="开始时间">
                    <el-date-picker
                      v-model="form.promotionRule.startTime"
                      type="datetime"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="结束时间">
                    <el-date-picker
                      v-model="form.promotionRule.endTime"
                      type="datetime"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>



        <!-- 高级功能 -->
        <el-tab-pane label="高级功能" name="advanced">
          <el-form label-width="120px">
            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>AI智能优化</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item>
                    <el-switch
                      v-model="form.enableAIOptimization"
                      active-text="启用AI智能优化"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="优化目标">
                    <el-select v-model="form.optimizationTarget" style="width: 100%">
                      <el-option label="收益最大化" value="revenue" />
                      <el-option label="风险最小化" value="risk" />
                      <el-option label="用户满意度" value="satisfaction" />
                      <el-option label="综合平衡" value="balanced" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="学习周期">
                    <el-input-number
                      v-model="form.learningPeriod"
                      :min="1"
                      :max="365"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">天</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="调整幅度">
                    <el-input-number
                      v-model="form.adjustmentRange"
                      :min="0"
                      :max="50"
                      :precision="1"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">%</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="置信度阈值">
                    <el-input-number
                      v-model="form.confidenceThreshold"
                      :min="0"
                      :max="100"
                      :precision="1"
                      style="width: 100%"
                    />
                    <span style="margin-left: 8px;">%</span>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>

            <el-card class="special-rule-card" shadow="never">
              <template #header>
                <span>区块链集成</span>
              </template>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item>
                    <el-switch
                      v-model="form.enableBlockchain"
                      active-text="启用区块链结算"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="区块链网络">
                    <el-select v-model="form.blockchainNetwork" style="width: 100%">
                      <el-option label="以太坊" value="ethereum" />
                      <el-option label="币安智能链" value="bsc" />
                      <el-option label="Polygon" value="polygon" />
                      <el-option label="Solana" value="solana" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="智能合约">
                    <el-input v-model="form.smartContract" placeholder="合约地址" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Gas费策略">
                    <el-select v-model="form.gasStrategy" style="width: 100%">
                      <el-option label="标准" value="standard" />
                      <el-option label="快速" value="fast" />
                      <el-option label="经济" value="economy" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="确认数">
                    <el-input-number
                      v-model="form.confirmations"
                      :min="1"
                      :max="100"
                      style="width: 100%"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <el-button @click="dialog.visible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="dialog.loading">
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Check, Close, Star } from '@element-plus/icons-vue'
import { useSettlementStore } from '@/stores/settlement'

// 使用共享的结算配置状态
const settlementStore = useSettlementStore()

// 数据源 - 5条核心规则
const rulesList = ref([
  {
    id: 1,
    name: '个人商户结算规则',
    merchantType: 'personal',
    creatorAuthType: 'none',
    isNewMerchant: false,
    settlementCycle: 'T+1',
    commissionRate: 4.5,
    transactionFee: 0.3,
    withdrawFee: 2.0,
    minAmount: 100,
    status: 1,
    priority: 10,
    description: '适用于个人商户结算规则',
    createTime: '2025-01-15 10:30:00'
  },
  {
    id: 2,
    name: '企业商户结算规则',
    merchantType: 'enterprise',
    creatorAuthType: 'none',
    isNewMerchant: false,
    settlementCycle: 'T+0',
    commissionRate: 3.8,
    transactionFee: 0,
    withdrawFee: 1.0,
    minAmount: 500,
    status: 1,
    priority: 20,
    description: '适用于企业商户的结算规则',
    createTime: '2025-01-14 14:20:00'
  },
  {
    id: 3,
    name: '手工艺人结算规则',
    merchantType: 'all',
    creatorAuthType: 'handicraft',
    isNewMerchant: false,
    settlementCycle: 'T+0',
    commissionRate: 2.0, // 优惠佣金
    transactionFee: 0,
    withdrawFee: 0, // 免提现手续费
    minAmount: 1,
    status: 1,
    priority: 100, // 最高优先级
    description: '非遗传承人享受免佣金政策，支持传统手工艺传承',
    createTime: '2025-01-13 09:15:00'
  },
  {
    id: 4,
    name: '非遗传承人结算规则',
    merchantType: 'all',
    creatorAuthType: 'heritage',
    isNewMerchant: false,
    settlementCycle: 'T+0',
    commissionRate: 0, // 免佣金
    transactionFee: 0,
    withdrawFee: 0, // 免提现手续费
    minAmount: 1,
    status: 1,
    priority: 100, // 最高优先级
    description: '认证非遗传承人享受免佣金政策，保护和传承非物质文化遗产',
    createTime: '2025-01-12 16:20:00'
  },
  {
    id: 5,
    name: '新商户优惠规则',
    merchantType: 'all',
    creatorAuthType: 'none',
    isNewMerchant: true,
    settlementCycle: 'T+1',
    commissionRate: 2.0, // 新商户优惠费率
    transactionFee: 0,
    withdrawFee: 1.0,
    minAmount: 50,
    status: 1,
    priority: 50,
    description: '新商户前3个月享受2.0%优惠佣金，鼓励新商户入驻',
    createTime: '2025-01-10 11:30:00'
  }
])

const loading = ref(false)
const selectedRows = ref<any[]>([])

// 对话框
const dialog = reactive({
  visible: false,
  title: '新增结算规则',
  loading: false
})

// 当前激活的标签页
const activeTab = ref('basic')

// 表单数据
const form = reactive({
  id: null,
  name: '',
  merchantType: '',
  creatorAuthType: 'none', // 创作者认证类型
  isNewMerchant: false, // 是否新商户优惠
  settlementCycle: '',
  commissionRate: 3.0,
  minCommission: 0,
  maxCommission: 0,
  transactionFee: 0,
  withdrawFee: 2.0,
  serviceFee: 0,
  minAmount: 100,
  maxAmount: 0,
  priority: 10,
  description: '',
  // 阶梯费率
  enableLadder: false,
  ladderRules: [] as any[],
  // 新商户优惠
  newMerchantDiscount: {
    enabled: false,
    duration: 3,
    rate: 2.0
  },
  // 节假日规则
  holidayRule: {
    enabled: false,
    action: 'delay'
  },
  // 活动规则
  promotionRule: {
    enabled: false,
    rate: 1.5,
    startTime: '',
    endTime: ''
  },
  // 风控与合规
  riskLevel: 'low',
  dailyLimit: 100000,
  monthlyLimit: 3000000,
  enableAML: false,
  enableEscrow: false,
  kycLevel: 'basic',
  taxHandling: 'platform',
  reportingLevel: 'standard',
  // 智能定价
  enableDynamicPricing: false,
  pricingFrequency: 'daily',
  volumeWeight: 25,
  creditWeight: 25,
  marketWeight: 25,
  riskWeight: 25,
  enableCompetitivePricing: false,
  priceMatchStrategy: 'average',
  // 国际化
  baseCurrency: 'CNY',
  supportedCurrencies: ['CNY'],
  exchangeRateUpdate: 'daily',
  enableCurrencyHedging: false,
  hedgingStrategy: 'partial',
  crossBorderChannel: 'swift',
  crossBorderSettlement: 'T3',
  crossBorderFee: 0.5,
  applicableRegions: ['CN'],
  timezoneHandling: 'platform',
  // 高级功能
  enableAIOptimization: false,
  optimizationTarget: 'balanced',
  learningPeriod: 30,
  adjustmentRange: 10,
  confidenceThreshold: 80,
  enableBlockchain: false,
  blockchainNetwork: 'ethereum',
  smartContract: '',
  gasStrategy: 'standard',
  confirmations: 12
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  merchantType: [{ required: true, message: '请选择商户类型', trigger: 'change' }],
  settlementCycle: [{ required: true, message: '请选择结算周期', trigger: 'change' }],
  commissionRate: [{ required: true, message: '请输入佣金比例', trigger: 'blur' }],
  minAmount: [{ required: true, message: '请输入最小结算金额', trigger: 'blur' }],
  priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }]
}

const formRef = ref()

// 获取商户类型标签
const getMerchantTypeTag = (type: string) => {
  const tags = { all: 'info', personal: 'success', enterprise: 'primary' }
  return tags[type] || 'info'
}

// 获取商户类型名称
const getMerchantTypeName = (type: string) => {
  const names: Record<string, string> = {
    all: '全部商户',
    personal: '个人商户',
    enterprise: '企业商户'
  }
  return names[type] || '未知'
}

// 获取结算周期名称
const getSettlementCycleName = (cycle: string) => {
  const names: Record<string, string> = {
    T0: 'T+0',
    T1: 'T+1',
    T3: 'T+3',
    T7: 'T+7',
    weekly: '周结',
    monthly: '月结'
  }
  return names[cycle] || '未知'
}

// 获取创作者认证名称
const getCreatorAuthName = (authType: string) => {
  const names: Record<string, string> = {
    none: '普通',
    handicraft: '手工艺人',
    heritage: '非遗传承人',
    general: '通用认证'
  }
  return names[authType] || '未知'
}

// 获取创作者认证标签类型
const getCreatorAuthTag = (authType: string): 'info' | 'success' | 'primary' | 'danger' | 'warning' => {
  const tags: Record<string, 'info' | 'success' | 'primary' | 'danger' | 'warning'> = {
    none: 'info',
    handicraft: 'success',
    heritage: 'danger',
    general: 'primary'
  }
  return tags[authType] || 'info'
}

// 格式化时间
const formatTime = (time: string) => {
  return time
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 新增规则
const handleAdd = () => {
  dialog.title = '新增结算规则'
  dialog.visible = true
}

// 编辑规则
const handleEdit = (row: any) => {
  dialog.title = '编辑结算规则'
  Object.assign(form, { ...row })
  dialog.visible = true
}

// 复制规则
const handleCopy = (row: any) => {
  dialog.title = '复制结算规则'
  Object.assign(form, { ...row, id: null, name: row.name + ' - 副本' })
  dialog.visible = true
}

// 删除规则
const handleDelete = (row: any) => {
  ElMessageBox.confirm('确定要删除此结算规则吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('删除成功')
  })
}

// 状态变化
const handleStatusChange = (row: any) => {
  const status = row.status ? '启用' : '禁用'
  ElMessage.success(`${status}成功`)
}

// 批量启用
const handleBatchEnable = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要启用的规则')
    return
  }
  ElMessage.success(`批量启用 ${selectedRows.value.length} 条规则`)
}

// 批量禁用
const handleBatchDisable = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要禁用的规则')
    return
  }
  ElMessage.success(`批量禁用 ${selectedRows.value.length} 条规则`)
}

// 添加阶梯规则
const addLadderRule = () => {
  form.ladderRules.push({
    name: `阶梯${form.ladderRules.length + 1}`,
    minAmount: 0,
    maxAmount: 10000,
    rate: 3.0
  })
}

// 删除阶梯规则
const removeLadderRule = (index: number) => {
  form.ladderRules.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  formRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      dialog.loading = true
      try {
        // 使用共享状态管理保存配置
        const result = await settlementStore.saveSettings()
        if (result.success) {
          ElMessage.success(form.id ? '编辑成功' : '新增成功')
          dialog.visible = false
          resetForm()
        } else {
          ElMessage.error(result.message || '保存失败')
        }
      } catch (error) {
        ElMessage.error('保存失败')
      } finally {
        dialog.loading = false
      }
    }
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    merchantType: '',
    settlementCycle: '',
    commissionRate: 3.0,
    minAmount: 100,
    priority: 10,
    description: ''
  })
  formRef.value?.resetFields()
}

onMounted(async () => {
  // 加载共享的结算配置
  await settlementStore.loadSettings()
})
</script>

<style scoped lang="scss">
.settlement-rules-page {
  padding-bottom: 20px;

  // 使用系统统一的卡片样式
  :deep(.custom-card) {
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 4px);
    box-shadow: none;
    margin-bottom: 20px;
  }

  // 卡片内容样式
  .custom-card-content {
    padding: 20px;
  }

  // 筛选卡片
  .filter-card {
    .custom-card-content {
      padding: 16px 20px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.rate-card {
  margin-bottom: 16px;

  :deep(.el-card__header) {
    background-color: var(--art-bg-gray-50);
    border-bottom: 1px solid var(--art-border-color);
    padding: 12px 16px;

    span {
      font-weight: 500;
      color: var(--art-text-gray-900);
    }
  }

  :deep(.el-card__body) {
    padding: 16px;
  }
}

.special-rule-card {
  margin-bottom: 16px;

  :deep(.el-card__header) {
    background-color: var(--art-bg-blue-50);
    border-bottom: 1px solid var(--art-border-color);
    padding: 12px 16px;

    span {
      font-weight: 500;
      color: var(--art-text-blue-600);
    }
  }

  :deep(.el-card__body) {
    padding: 16px;
  }
}

// 优惠政策标签样式
.policy-tag {
  background-color: #f0f9ff !important;
  border-color: #67c23a !important;
  color: #67c23a !important;
  font-weight: 600;
}

// 普通政策标签样式
.normal-policy-tag {
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #999999 !important;
  font-weight: 500;
}

.ladder-indicator {
  margin-top: 2px;
}

.fee-info {
  font-size: 12px;
  line-height: 1.4;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.amount-range {
  font-size: 12px;
  line-height: 1.4;

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.special-rules {
  display: flex;
  flex-direction: column;
  gap: 2px;

  .el-tag {
    font-size: 10px;
    height: 18px;
    line-height: 16px;
  }
}

// 免佣金标识样式
.free-commission {
  .el-tag {
    background: linear-gradient(135deg, #67c23a, #85ce61);
    border: none;
    color: white;
    font-weight: 500;

    .el-icon {
      margin-right: 2px;
    }
  }
}

// 文本样式
.text-muted {
  color: var(--art-text-gray-400);
  font-size: 12px;
}
</style>
