{"version": 3, "file": "container.js", "sourceRoot": "", "sources": ["../../src/container.ts"], "names": [], "mappings": "AAeA;;;GAGG;AACH,IAAM,gBAAgB,GAAqE,IAAI;IAAC;QACtF,cAAS,GAAsC,EAAE,CAAC;IAU5D,CAAC;IATC,qBAAG,GAAH,UAAO,SAAsC;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,QAAQ,IAAI,OAAA,QAAQ,CAAC,IAAI,KAAK,SAAS,EAA3B,CAA2B,CAAC,CAAC;QAC5E,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,QAAQ,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,SAAS,EAAE,EAAE,CAAC;YACxD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChC,CAAC;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC;IACzB,CAAC;IACH,cAAC;AAAD,CAAC,AAX+F,IAW9F,EAAE,CAAC;AAEL,IAAI,aAA+E,CAAC;AACpF,IAAI,oBAAyC,CAAC;AAE9C;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,YAA0C,EAAE,OAA6B;IACpG,aAAa,GAAG,YAAY,CAAC;IAC7B,oBAAoB,GAAG,OAAO,CAAC;AACjC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAI,SAAiD;IACnF,IAAI,aAAa,EAAE,CAAC;QAClB,IAAI,CAAC;YACH,IAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9C,IAAI,QAAQ;gBAAE,OAAO,QAAQ,CAAC;YAE9B,IAAI,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,QAAQ;gBAAE,OAAO,QAAQ,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,oBAAoB,IAAI,CAAC,oBAAoB,CAAC,gBAAgB;gBAAE,MAAM,KAAK,CAAC;QACnF,CAAC;IACH,CAAC;IACD,OAAO,gBAAgB,CAAC,GAAG,CAAI,SAAS,CAAC,CAAC;AAC5C,CAAC", "sourcesContent": ["/**\n * Container options.\n */\nexport interface UseContainerOptions {\n  /**\n   * If set to true, then default container will be used in the case if given container haven't returned anything.\n   */\n  fallback?: boolean;\n\n  /**\n   * If set to true, then default container will be used in the case if given container thrown an exception.\n   */\n  fallbackOnErrors?: boolean;\n}\n\n/**\n * Container to be used by this library for inversion control. If container was not implicitly set then by default\n * container simply creates a new instance of the given class.\n */\nconst defaultContainer: { get<T>(someClass: { new (...args: any[]): T } | Function): T } = new (class {\n  private instances: { type: Function; object: any }[] = [];\n  get<T>(someClass: { new (...args: any[]): T }): T {\n    let instance = this.instances.find(instance => instance.type === someClass);\n    if (!instance) {\n      instance = { type: someClass, object: new someClass() };\n      this.instances.push(instance);\n    }\n\n    return instance.object;\n  }\n})();\n\nlet userContainer: { get<T>(someClass: { new (...args: any[]): T } | Function): T };\nlet userContainerOptions: UseContainerOptions;\n\n/**\n * Sets container to be used by this library.\n */\nexport function useContainer(iocContainer: { get(someClass: any): any }, options?: UseContainerOptions): void {\n  userContainer = iocContainer;\n  userContainerOptions = options;\n}\n\n/**\n * Gets the IOC container used by this library.\n */\nexport function getFromContainer<T>(someClass: { new (...args: any[]): T } | Function): T {\n  if (userContainer) {\n    try {\n      const instance = userContainer.get(someClass);\n      if (instance) return instance;\n\n      if (!userContainerOptions || !userContainerOptions.fallback) return instance;\n    } catch (error) {\n      if (!userContainerOptions || !userContainerOptions.fallbackOnErrors) throw error;\n    }\n  }\n  return defaultContainer.get<T>(someClass);\n}\n"]}