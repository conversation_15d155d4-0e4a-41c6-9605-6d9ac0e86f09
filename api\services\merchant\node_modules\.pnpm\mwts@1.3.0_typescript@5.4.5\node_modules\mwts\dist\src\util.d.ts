/// <reference types="node" />
import * as fs from 'fs';
import * as ncp from 'ncp';
export declare const readFilep: ReadFileP;
export declare const rimrafp: (arg1: string) => Promise<void>;
export declare const writeFileAtomicp: (arg1: string, arg2: string | Buffer) => Promise<void>;
export declare const ncpp: (source: string, destination: string, options?: ncp.Options | undefined) => Promise<void>;
export interface Bag<T> {
    [script: string]: T;
}
export interface DefaultPackage extends Bag<string> {
    mwts: string;
    typescript: string;
    '@types/node': string;
}
export declare function readJsonp(jsonPath: string): Promise<any>;
export interface ReadFileP {
    (path: string, encoding: string): Promise<string>;
}
export declare function nop(): void;
export declare function safeError(err: unknown): NodeJS.ErrnoException;
/**
 * An interface containing the top level data fields present in Config Files
 */
export interface ConfigFile {
    files?: string[];
    compilerOptions?: unknown;
    include?: string[];
    exclude?: string[];
    extends?: string[] | string;
}
/**
 * Automatically defines npm or yarn is going to be used:
 * - If only yarn.lock exists, use yarn
 * - If only package-lock.json or both exist, use npm
 */
export declare function isYarnUsed(existsSync?: typeof fs.existsSync): boolean;
export declare function getPkgManagerCommand(isYarnUsed?: boolean): string;
/**
 * Find the tsconfig.json, read it, and return parsed contents.
 * @param rootDir Directory where the tsconfig.json should be found.
 * If the tsconfig.json file has an "extends" field hop down the dependency tree
 * until it ends or a circular reference is found in which case an error will be
 * thrown
 */
export declare function getTSConfig(rootDir: string, customReadFilep?: ReadFileP): Promise<ConfigFile>;
export declare function readJSON(filepath: string): unknown;
//# sourceMappingURL=util.d.ts.map