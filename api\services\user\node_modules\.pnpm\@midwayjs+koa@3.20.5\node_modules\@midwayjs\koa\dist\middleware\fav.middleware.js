"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SiteFileMiddleware = void 0;
const core_1 = require("@midwayjs/core");
const path = require('path');
const MAX_AGE = 'public, max-age=2592000'; // 30 days
let SiteFileMiddleware = class SiteFileMiddleware {
    resolve() {
        // use bodyparser middleware
        if (this.siteFileConfig.enable) {
            return async (ctx, next) => {
                if (ctx.method !== 'HEAD' && ctx.method !== 'GET')
                    return next();
                /* istanbul ignore if */
                if (ctx.path[0] !== '/')
                    return next();
                if (ctx.path !== '/favicon.ico') {
                    return next();
                }
                let content = this.siteFileConfig['favicon'];
                if (content === undefined) {
                    content = Buffer.from('');
                }
                if (!content)
                    return next();
                // content is url
                if (typeof content === 'string')
                    return ctx.redirect(content);
                // '/robots.txt': Buffer <xx..
                // content is buffer
                if (Buffer.isBuffer(content)) {
                    ctx.set('cache-control', MAX_AGE);
                    ctx.body = content;
                    ctx.type = path.extname(ctx.path);
                    return;
                }
                return next();
            };
        }
    }
    static getName() {
        return 'siteFile';
    }
};
__decorate([
    (0, core_1.Config)('siteFile'),
    __metadata("design:type", Object)
], SiteFileMiddleware.prototype, "siteFileConfig", void 0);
SiteFileMiddleware = __decorate([
    (0, core_1.Middleware)()
], SiteFileMiddleware);
exports.SiteFileMiddleware = SiteFileMiddleware;
//# sourceMappingURL=fav.middleware.js.map