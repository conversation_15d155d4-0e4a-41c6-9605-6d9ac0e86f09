import { IConfigurationOptions, IMidwayContainer, IMidwayFramework } from '../interface';
import { AsyncContextManager } from '../common/asyncContextManager';
export declare const getCurrentApplicationContext: () => IMidwayContainer;
export declare const getCurrentMainFramework: <APP extends import("../interface").IMidwayBaseApplication<CTX>, CTX extends import("../interface").Context, CONFIG extends IConfigurationOptions>() => IMidwayFramework<APP, CTX, CONFIG, unknown, unknown>;
export declare const getCurrentMainApp: <APP extends import("../interface").IMidwayBaseApplication<import("../interface").Context>>() => APP;
export declare const getCurrentAsyncContextManager: () => AsyncContextManager;
//# sourceMappingURL=contextUtil.d.ts.map