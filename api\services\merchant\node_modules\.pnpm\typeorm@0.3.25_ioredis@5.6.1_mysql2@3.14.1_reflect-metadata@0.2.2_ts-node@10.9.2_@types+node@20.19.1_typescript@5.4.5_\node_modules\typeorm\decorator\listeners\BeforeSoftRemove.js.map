{"version": 3, "sources": ["../../src/decorator/listeners/BeforeSoftRemove.ts"], "names": [], "mappings": ";;AAOA,4CAQC;AAfD,2CAAsD;AACtD,gFAA4E;AAG5E;;GAEG;AACH,SAAgB,gBAAgB;IAC5B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,uCAAkB,CAAC,kBAAkB;SAChB,CAAC,CAAA;IACpC,CAAC,CAAA;AACL,CAAC", "file": "BeforeSoftRemove.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { EventListenerTypes } from \"../../metadata/types/EventListenerTypes\"\nimport { EntityListenerMetadataArgs } from \"../../metadata-args/EntityListenerMetadataArgs\"\n\n/**\n * Calls a method on which this decorator is applied before this entity soft removal.\n */\nexport function BeforeSoftRemove(): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().entityListeners.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            type: EventListenerTypes.BEFORE_SOFT_REMOVE,\n        } as EntityListenerMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}