"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createConfiguration = exports.FunctionalConfiguration = void 0;
class FunctionalConfiguration {
    constructor(options) {
        this.options = options;
        this.readyHandler = () => { };
        this.stopHandler = () => { };
        this.configLoadHandler = () => { };
        this.serverReadyHandler = () => { };
    }
    onConfigLoad(configLoadHandler, app) {
        if (typeof configLoadHandler === 'function') {
            this.configLoadHandler = configLoadHandler;
        }
        else {
            return this.configLoadHandler(configLoadHandler, app);
        }
        return this;
    }
    onReady(readyHandler, app) {
        if (typeof readyHandler === 'function') {
            this.readyHandler = readyHandler;
        }
        else {
            return this.readyHandler(readyHandler, app);
        }
        return this;
    }
    onServerReady(serverReady<PERSON><PERSON><PERSON>, app) {
        if (typeof serverReadyHandler === 'function') {
            this.serverReadyHandler = serverReadyHandler;
        }
        else {
            return this.serverReadyHandler(serverReadyHandler, app);
        }
        return this;
    }
    onStop(stopHandler, app) {
        if (typeof stopHandler === 'function') {
            this.stopHandler = stopHandler;
        }
        else {
            return this.stopHandler(stopHandler, app);
        }
        return this;
    }
    getConfigurationOptions() {
        return this.options;
    }
}
exports.FunctionalConfiguration = FunctionalConfiguration;
const createConfiguration = (options) => {
    return new FunctionalConfiguration(options);
};
exports.createConfiguration = createConfiguration;
//# sourceMappingURL=configuration.js.map