{"name": "koa", "version": "2.16.1", "description": "Koa web app framework", "main": "lib/application.js", "exports": {".": {"require": "./lib/application.js", "import": "./dist/koa.mjs"}, "./lib/request": "./lib/request.js", "./lib/request.js": "./lib/request.js", "./lib/response": "./lib/response.js", "./lib/response.js": "./lib/response.js", "./lib/application": "./lib/application.js", "./lib/application.js": "./lib/application.js", "./lib/context": "./lib/context.js", "./lib/context.js": "./lib/context.js", "./*": "./*.js", "./*.js": "./*.js", "./package": "./package.json", "./package.json": "./package.json"}, "scripts": {"test": "jest --force<PERSON>xit", "lint": "eslint --ignore-path .gitignore .", "authors": "git log --format='%aN <%aE>' | sort -u > AUTHORS", "build": "gen-esm-wrapper . ./dist/koa.mjs", "prepare": "npm run build"}, "repository": "koajs/koa", "keywords": ["web", "app", "http", "application", "framework", "middleware", "rack"], "license": "MIT", "dependencies": {"accepts": "^1.3.5", "cache-content-type": "^1.0.0", "content-disposition": "~0.5.2", "content-type": "^1.0.4", "cookies": "~0.9.0", "debug": "^4.3.2", "delegates": "^1.0.0", "depd": "^2.0.0", "destroy": "^1.0.4", "encodeurl": "^1.0.2", "escape-html": "^1.0.3", "fresh": "~0.5.2", "http-assert": "^1.3.0", "http-errors": "^1.6.3", "is-generator-function": "^1.0.7", "koa-compose": "^4.1.0", "koa-convert": "^2.0.0", "on-finished": "^2.3.0", "only": "~0.0.2", "parseurl": "^1.3.2", "statuses": "^1.5.0", "type-is": "^1.6.16", "vary": "^1.1.2"}, "devDependencies": {"eslint": "^7.32.0", "eslint-config-koa": "^2.0.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.1.0", "eslint-plugin-standard": "^5.0.0", "gen-esm-wrapper": "^1.0.6", "jest": "^27.0.6", "supertest": "^3.1.0"}, "engines": {"node": "^4.8.4 || ^6.10.1 || ^7.10.1 || >= 8.1.4"}, "files": ["dist", "lib"], "jest": {"testEnvironment": "node"}}