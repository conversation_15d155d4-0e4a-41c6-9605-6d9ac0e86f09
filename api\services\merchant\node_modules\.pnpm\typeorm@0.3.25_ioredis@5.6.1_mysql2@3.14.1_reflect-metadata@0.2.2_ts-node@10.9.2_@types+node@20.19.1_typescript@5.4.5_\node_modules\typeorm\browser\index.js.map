{"version": 3, "sources": ["../browser/src/index.ts"], "names": [], "mappings": "AAAA;GACG;AACH,OAAO,kBAAkB,CAAA;AAEzB,4EAA4E;AAC5E,wBAAwB;AACxB,4EAA4E;AAE5E,cAAc,WAAW,CAAA;AACzB,cAAc,aAAa,CAAA;AAC3B,cAAc,uBAAuB,CAAA;AACrC,cAAc,qBAAqB,CAAA;AACnC,cAAc,wBAAwB,CAAA;AACtC,cAAc,oBAAoB,CAAA;AAClC,cAAc,sBAAsB,CAAA;AACpC,cAAc,uBAAuB,CAAA;AACrC,cAAc,SAAS,CAAA;AACvB,cAAc,4BAA4B,CAAA;AAC1C,cAAc,sCAAsC,CAAA;AACpD,cAAc,sCAAsC,CAAA;AACpD,cAAc,4CAA4C,CAAA;AAC1D,cAAc,mCAAmC,CAAA;AACjD,cAAc,sCAAsC,CAAA;AACpD,cAAc,mCAAmC,CAAA;AACjD,cAAc,mCAAmC,CAAA;AACjD,cAAc,gCAAgC,CAAA;AAC9C,cAAc,oCAAoC,CAAA;AAClD,cAAc,mCAAmC,CAAA;AACjD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,mCAAmC,CAAA;AACjD,cAAc,uCAAuC,CAAA;AACrD,cAAc,oCAAoC,CAAA;AAClD,cAAc,mCAAmC,CAAA;AACjD,cAAc,oCAAoC,CAAA;AAClD,cAAc,oCAAoC,CAAA;AAClD,cAAc,wCAAwC,CAAA;AACtD,cAAc,qCAAqC,CAAA;AACnD,cAAc,oCAAoC,CAAA;AAClD,cAAc,uCAAuC,CAAA;AACrD,cAAc,mCAAmC,CAAA;AACjD,cAAc,kCAAkC,CAAA;AAChD,cAAc,uCAAuC,CAAA;AACrD,cAAc,sCAAsC,CAAA;AACpD,cAAc,qCAAqC,CAAA;AACnD,cAAc,mCAAmC,CAAA;AACjD,cAAc,sCAAsC,CAAA;AACpD,cAAc,kCAAkC,CAAA;AAChD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,kCAAkC,CAAA;AAChD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,qCAAqC,CAAA;AACnD,cAAc,kCAAkC,CAAA;AAChD,cAAc,2BAA2B,CAAA;AACzC,cAAc,gCAAgC,CAAA;AAC9C,cAAc,qCAAqC,CAAA;AACnD,cAAc,oCAAoC,CAAA;AAClD,cAAc,kCAAkC,CAAA;AAChD,cAAc,6BAA6B,CAAA;AAC3C,cAAc,+BAA+B,CAAA;AAC7C,cAAc,uBAAuB,CAAA;AACrC,cAAc,mBAAmB,CAAA;AACjC,cAAc,wBAAwB,CAAA;AACtC,cAAc,oBAAoB,CAAA;AAClC,cAAc,mBAAmB,CAAA;AACjC,cAAc,uBAAuB,CAAA;AACrC,cAAc,uBAAuB,CAAA;AACrC,cAAc,8BAA8B,CAAA;AAC5C,cAAc,6BAA6B,CAAA;AAC3C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,6BAA6B,CAAA;AAC3C,cAAc,0CAA0C,CAAA;AACxD,cAAc,uCAAuC,CAAA;AACrD,cAAc,sCAAsC,CAAA;AACpD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,+BAA+B,CAAA;AAC7C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,kCAAkC,CAAA;AAChD,cAAc,yCAAyC,CAAA;AACvD,cAAc,+BAA+B,CAAA;AAC7C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,kCAAkC,CAAA;AAChD,cAAc,yCAAyC,CAAA;AACvD,cAAc,6BAA6B,CAAA;AAC3C,cAAc,6BAA6B,CAAA;AAC3C,cAAc,sCAAsC,CAAA;AACpD,cAAc,8BAA8B,CAAA;AAC5C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,+BAA+B,CAAA;AAC7C,cAAc,6BAA6B,CAAA;AAC3C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,qCAAqC,CAAA;AACnD,cAAc,kCAAkC,CAAA;AAChD,cAAc,iCAAiC,CAAA;AAC/C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,yBAAyB,CAAA;AACvC,cAAc,iBAAiB,CAAA;AAC/B,cAAc,wBAAwB,CAAA;AACtC,cAAc,gCAAgC,CAAA;AAC9C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,qBAAqB,CAAA;AACnC,cAAc,2BAA2B,CAAA;AACzC,cAAc,gCAAgC,CAAA;AAC9C,cAAc,iCAAiC,CAAA;AAC/C,cAAc,yBAAyB,CAAA;AACvC,cAAc,yBAAyB,CAAA;AACvC,cAAc,6BAA6B,CAAA;AAC3C,cAAc,8BAA8B,CAAA;AAC5C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,0BAA0B,CAAA;AACxC,cAAc,mCAAmC,CAAA;AACjD,cAAc,oCAAoC,CAAA;AAClD,cAAc,uCAAuC,CAAA;AACrD,cAAc,wCAAwC,CAAA;AACtD,cAAc,mCAAmC,CAAA;AACjD,cAAc,oCAAoC,CAAA;AAClD,cAAc,8BAA8B,CAAA;AAC5C,cAAc,4BAA4B,CAAA;AAC1C,cAAc,4CAA4C,CAAA;AAC1D,cAAc,6CAA6C,CAAA;AAC3D,cAAc,gDAAgD,CAAA;AAC9D,cAAc,iDAAiD,CAAA;AAC/D,cAAc,4CAA4C,CAAA;AAC1D,cAAc,uCAAuC,CAAA;AACrD,cAAc,6CAA6C,CAAA;AAC3D,cAAc,sCAAsC,CAAA;AACpD,cAAc,0BAA0B,CAAA;AACxC,cAAc,6BAA6B,CAAA;AAC3C,cAAc,6BAA6B,CAAA;AAC3C,cAAc,gCAAgC,CAAA;AAC9C,cAAc,mCAAmC,CAAA;AACjD,cAAc,+BAA+B,CAAA;AAE7C,iCAAiC;AAEjC,OAAO,EAAE,uBAAuB,EAAE,MAAM,sCAAsC,CAAA;AAE9E,OAAO,EAAE,UAAU,EAAE,MAAM,0BAA0B,CAAA;AACrD,OAAO,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAA;AACpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,gCAAgC,CAAA;AAGlE,OAAO,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAA;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAA;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAA;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAA;AACvE,OAAO,EAAE,kBAAkB,EAAE,MAAM,oCAAoC,CAAA;AACvE,OAAO,EAAE,oBAAoB,EAAE,MAAM,sCAAsC,CAAA;AAC3E,OAAO,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAA;AACnD,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAA;AAGzD,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAA;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAA;AAClE,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAA;AAExD,OAAO,EAAE,kBAAkB,EAAE,MAAM,qCAAqC,CAAA;AACxE,OAAO,EAAE,SAAS,EAAE,MAAM,uBAAuB,CAAA;AACjD,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAA;AAEjE,OAAO,EAAE,qBAAqB,EAAE,MAAM,yCAAyC,CAAA;AAE/E,OAAO,EAAE,0BAA0B,EAAE,MAAM,8CAA8C,CAAA;AAWzF,OAAO,EAAE,YAAY,EAAE,MAAM,8BAA8B,CAAA;AAI3D,OAAO,EAAE,iCAAiC,EAAE,MAAM,mDAAmD,CAAA;AAErG,OAAO,EAAE,mBAAmB,EAAE,MAAM,qCAAqC,CAAA;AACzE,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AACxD,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAA", "file": "index.js", "sourcesContent": ["/*!\n */\nimport \"reflect-metadata\"\n\n// -------------------------------------------------------------------------\n// Commonly Used exports\n// -------------------------------------------------------------------------\n\nexport * from \"./globals\"\nexport * from \"./container\"\nexport * from \"./common/EntityTarget\"\nexport * from \"./common/ObjectType\"\nexport * from \"./common/ObjectLiteral\"\nexport * from \"./common/MixedList\"\nexport * from \"./common/DeepPartial\"\nexport * from \"./common/RelationType\"\nexport * from \"./error\"\nexport * from \"./decorator/columns/Column\"\nexport * from \"./decorator/columns/CreateDateColumn\"\nexport * from \"./decorator/columns/DeleteDateColumn\"\nexport * from \"./decorator/columns/PrimaryGeneratedColumn\"\nexport * from \"./decorator/columns/PrimaryColumn\"\nexport * from \"./decorator/columns/UpdateDateColumn\"\nexport * from \"./decorator/columns/VersionColumn\"\nexport * from \"./decorator/columns/VirtualColumn\"\nexport * from \"./decorator/columns/ViewColumn\"\nexport * from \"./decorator/columns/ObjectIdColumn\"\nexport * from \"./decorator/listeners/AfterInsert\"\nexport * from \"./decorator/listeners/AfterLoad\"\nexport * from \"./decorator/listeners/AfterRemove\"\nexport * from \"./decorator/listeners/AfterSoftRemove\"\nexport * from \"./decorator/listeners/AfterRecover\"\nexport * from \"./decorator/listeners/AfterUpdate\"\nexport * from \"./decorator/listeners/BeforeInsert\"\nexport * from \"./decorator/listeners/BeforeRemove\"\nexport * from \"./decorator/listeners/BeforeSoftRemove\"\nexport * from \"./decorator/listeners/BeforeRecover\"\nexport * from \"./decorator/listeners/BeforeUpdate\"\nexport * from \"./decorator/listeners/EventSubscriber\"\nexport * from \"./decorator/options/ColumnOptions\"\nexport * from \"./decorator/options/IndexOptions\"\nexport * from \"./decorator/options/JoinColumnOptions\"\nexport * from \"./decorator/options/JoinTableOptions\"\nexport * from \"./decorator/options/RelationOptions\"\nexport * from \"./decorator/options/EntityOptions\"\nexport * from \"./decorator/options/ValueTransformer\"\nexport * from \"./decorator/relations/JoinColumn\"\nexport * from \"./decorator/relations/JoinTable\"\nexport * from \"./decorator/relations/ManyToMany\"\nexport * from \"./decorator/relations/ManyToOne\"\nexport * from \"./decorator/relations/OneToMany\"\nexport * from \"./decorator/relations/OneToOne\"\nexport * from \"./decorator/relations/RelationCount\"\nexport * from \"./decorator/relations/RelationId\"\nexport * from \"./decorator/entity/Entity\"\nexport * from \"./decorator/entity/ChildEntity\"\nexport * from \"./decorator/entity/TableInheritance\"\nexport * from \"./decorator/entity-view/ViewEntity\"\nexport * from \"./decorator/tree/TreeLevelColumn\"\nexport * from \"./decorator/tree/TreeParent\"\nexport * from \"./decorator/tree/TreeChildren\"\nexport * from \"./decorator/tree/Tree\"\nexport * from \"./decorator/Index\"\nexport * from \"./decorator/ForeignKey\"\nexport * from \"./decorator/Unique\"\nexport * from \"./decorator/Check\"\nexport * from \"./decorator/Exclusion\"\nexport * from \"./decorator/Generated\"\nexport * from \"./decorator/EntityRepository\"\nexport * from \"./find-options/operator/And\"\nexport * from \"./find-options/operator/Or\"\nexport * from \"./find-options/operator/Any\"\nexport * from \"./find-options/operator/ArrayContainedBy\"\nexport * from \"./find-options/operator/ArrayContains\"\nexport * from \"./find-options/operator/ArrayOverlap\"\nexport * from \"./find-options/operator/Between\"\nexport * from \"./find-options/operator/Equal\"\nexport * from \"./find-options/operator/In\"\nexport * from \"./find-options/operator/IsNull\"\nexport * from \"./find-options/operator/LessThan\"\nexport * from \"./find-options/operator/LessThanOrEqual\"\nexport * from \"./find-options/operator/ILike\"\nexport * from \"./find-options/operator/Like\"\nexport * from \"./find-options/operator/MoreThan\"\nexport * from \"./find-options/operator/MoreThanOrEqual\"\nexport * from \"./find-options/operator/Not\"\nexport * from \"./find-options/operator/Raw\"\nexport * from \"./find-options/operator/JsonContains\"\nexport * from \"./find-options/EqualOperator\"\nexport * from \"./find-options/FindManyOptions\"\nexport * from \"./find-options/FindOneOptions\"\nexport * from \"./find-options/FindOperator\"\nexport * from \"./find-options/FindOperatorType\"\nexport * from \"./find-options/FindOptionsOrder\"\nexport * from \"./find-options/FindOptionsRelations\"\nexport * from \"./find-options/FindOptionsSelect\"\nexport * from \"./find-options/FindOptionsUtils\"\nexport * from \"./find-options/FindOptionsWhere\"\nexport * from \"./find-options/FindTreeOptions\"\nexport * from \"./find-options/JoinOptions\"\nexport * from \"./find-options/OrderByCondition\"\nexport * from \"./logger/AbstractLogger\"\nexport * from \"./logger/Logger\"\nexport * from \"./logger/LoggerOptions\"\nexport * from \"./logger/AdvancedConsoleLogger\"\nexport * from \"./logger/FormattedConsoleLogger\"\nexport * from \"./logger/SimpleConsoleLogger\"\nexport * from \"./logger/FileLogger\"\nexport * from \"./metadata/EntityMetadata\"\nexport * from \"./entity-manager/EntityManager\"\nexport * from \"./repository/AbstractRepository\"\nexport * from \"./repository/Repository\"\nexport * from \"./repository/BaseEntity\"\nexport * from \"./repository/TreeRepository\"\nexport * from \"./repository/MongoRepository\"\nexport * from \"./repository/RemoveOptions\"\nexport * from \"./repository/SaveOptions\"\nexport * from \"./schema-builder/table/TableCheck\"\nexport * from \"./schema-builder/table/TableColumn\"\nexport * from \"./schema-builder/table/TableExclusion\"\nexport * from \"./schema-builder/table/TableForeignKey\"\nexport * from \"./schema-builder/table/TableIndex\"\nexport * from \"./schema-builder/table/TableUnique\"\nexport * from \"./schema-builder/table/Table\"\nexport * from \"./schema-builder/view/View\"\nexport * from \"./schema-builder/options/TableCheckOptions\"\nexport * from \"./schema-builder/options/TableColumnOptions\"\nexport * from \"./schema-builder/options/TableExclusionOptions\"\nexport * from \"./schema-builder/options/TableForeignKeyOptions\"\nexport * from \"./schema-builder/options/TableIndexOptions\"\nexport * from \"./schema-builder/options/TableOptions\"\nexport * from \"./schema-builder/options/TableUniqueOptions\"\nexport * from \"./schema-builder/options/ViewOptions\"\nexport * from \"./driver/mongodb/typings\"\nexport * from \"./driver/types/DatabaseType\"\nexport * from \"./driver/types/GeoJsonTypes\"\nexport * from \"./driver/types/ReplicationMode\"\nexport * from \"./driver/sqlserver/MssqlParameter\"\nexport * from \"./subscriber/event/QueryEvent\"\n\n// export * from \"./data-source\";\n\nexport { ConnectionOptionsReader } from \"./connection/ConnectionOptionsReader\"\nexport { ConnectionOptions } from \"./connection/ConnectionOptions\"\nexport { DataSource } from \"./data-source/DataSource\"\nexport { Connection } from \"./connection/Connection\"\nexport { ConnectionManager } from \"./connection/ConnectionManager\"\nexport { DataSourceOptions } from \"./data-source/DataSourceOptions\"\nexport { Driver } from \"./driver/Driver\"\nexport { QueryBuilder } from \"./query-builder/QueryBuilder\"\nexport { SelectQueryBuilder } from \"./query-builder/SelectQueryBuilder\"\nexport { DeleteQueryBuilder } from \"./query-builder/DeleteQueryBuilder\"\nexport { InsertQueryBuilder } from \"./query-builder/InsertQueryBuilder\"\nexport { UpdateQueryBuilder } from \"./query-builder/UpdateQueryBuilder\"\nexport { RelationQueryBuilder } from \"./query-builder/RelationQueryBuilder\"\nexport { Brackets } from \"./query-builder/Brackets\"\nexport { NotBrackets } from \"./query-builder/NotBrackets\"\nexport { WhereExpressionBuilder } from \"./query-builder/WhereExpressionBuilder\"\nexport { WhereExpression } from \"./query-builder/WhereExpressionBuilder\"\nexport { InsertResult } from \"./query-builder/result/InsertResult\"\nexport { UpdateResult } from \"./query-builder/result/UpdateResult\"\nexport { DeleteResult } from \"./query-builder/result/DeleteResult\"\nexport { QueryResult } from \"./query-runner/QueryResult\"\nexport { QueryRunner } from \"./query-runner/QueryRunner\"\nexport { MongoEntityManager } from \"./entity-manager/MongoEntityManager\"\nexport { Migration } from \"./migration/Migration\"\nexport { MigrationExecutor } from \"./migration/MigrationExecutor\"\nexport { MigrationInterface } from \"./migration/MigrationInterface\"\nexport { DefaultNamingStrategy } from \"./naming-strategy/DefaultNamingStrategy\"\nexport { NamingStrategyInterface } from \"./naming-strategy/NamingStrategyInterface\"\nexport { LegacyOracleNamingStrategy } from \"./naming-strategy/LegacyOracleNamingStrategy\"\nexport { InsertEvent } from \"./subscriber/event/InsertEvent\"\nexport { LoadEvent } from \"./subscriber/event/LoadEvent\"\nexport { UpdateEvent } from \"./subscriber/event/UpdateEvent\"\nexport { RemoveEvent } from \"./subscriber/event/RemoveEvent\"\nexport { SoftRemoveEvent } from \"./subscriber/event/SoftRemoveEvent\"\nexport { RecoverEvent } from \"./subscriber/event/RecoverEvent\"\nexport { TransactionCommitEvent } from \"./subscriber/event/TransactionCommitEvent\"\nexport { TransactionRollbackEvent } from \"./subscriber/event/TransactionRollbackEvent\"\nexport { TransactionStartEvent } from \"./subscriber/event/TransactionStartEvent\"\nexport { EntitySubscriberInterface } from \"./subscriber/EntitySubscriberInterface\"\nexport { EntitySchema } from \"./entity-schema/EntitySchema\"\nexport { EntitySchemaColumnOptions } from \"./entity-schema/EntitySchemaColumnOptions\"\nexport { EntitySchemaIndexOptions } from \"./entity-schema/EntitySchemaIndexOptions\"\nexport { EntitySchemaRelationOptions } from \"./entity-schema/EntitySchemaRelationOptions\"\nexport { EntitySchemaEmbeddedColumnOptions } from \"./entity-schema/EntitySchemaEmbeddedColumnOptions\"\nexport { ColumnType } from \"./driver/types/ColumnTypes\"\nexport { EntitySchemaOptions } from \"./entity-schema/EntitySchemaOptions\"\nexport { InstanceChecker } from \"./util/InstanceChecker\"\nexport { TreeRepositoryUtils } from \"./util/TreeRepositoryUtils\"\n"], "sourceRoot": "."}