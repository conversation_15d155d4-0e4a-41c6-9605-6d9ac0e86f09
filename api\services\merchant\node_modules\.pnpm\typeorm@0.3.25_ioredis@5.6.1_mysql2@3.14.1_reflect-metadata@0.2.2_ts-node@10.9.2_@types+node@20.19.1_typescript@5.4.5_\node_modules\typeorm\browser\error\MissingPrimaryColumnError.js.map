{"version": 3, "sources": ["../browser/src/error/MissingPrimaryColumnError.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,yBAA0B,SAAQ,YAAY;IACvD,YAAY,cAA8B;QACtC,KAAK,CACD,WAAW,cAAc,CAAC,IAAI,kEAAkE;YAC5F,iGAAiG,CACxG,CAAA;IACL,CAAC;CACJ", "file": "MissingPrimaryColumnError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingPrimaryColumnError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata) {\n        super(\n            `Entity \"${entityMetadata.name}\" does not have a primary column. Primary column is required to ` +\n                `have in all your entities. Use @PrimaryColumn decorator to add a primary column to your entity.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}