import { Observable } from '../Observable';
import { MonoTypeOperatorFunction, OperatorFunction } from '../types';
/** @deprecated Deprecated in favor of static race. */
export declare function race<T>(observables: Array<Observable<T>>): MonoTypeOperatorFunction<T>;
/** @deprecated Deprecated in favor of static race. */
export declare function race<T, R>(observables: Array<Observable<T>>): OperatorFunction<T, R>;
/** @deprecated Deprecated in favor of static race. */
export declare function race<T>(...observables: Array<Observable<T> | Array<Observable<T>>>): MonoTypeOperatorFunction<T>;
/** @deprecated Deprecated in favor of static race. */
export declare function race<T, R>(...observables: Array<Observable<any> | Array<Observable<any>>>): OperatorFunction<T, R>;
