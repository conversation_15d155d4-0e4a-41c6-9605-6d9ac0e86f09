
# koa-compose

[![NPM version][npm-image]][npm-url]
[![Build status][travis-image]][travis-url]
[![Test coverage][codecov-image]][codecov-url]
[![Dependency Status][david-image]][david-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

 Compose middleware.

## Installation

```js
$ npm install koa-compose
```

## API

### compose([a, b, c, ...])

  Compose the given middleware and return middleware.

## License

  MIT

[npm-image]: https://img.shields.io/npm/v/koa-compose.svg?style=flat-square
[npm-url]: https://npmjs.org/package/koa-compose
[travis-image]: https://img.shields.io/travis/koajs/compose/next.svg?style=flat-square
[travis-url]: https://travis-ci.org/koajs/compose
[codecov-image]: https://img.shields.io/codecov/c/github/koajs/compose/next.svg?style=flat-square
[codecov-url]: https://codecov.io/github/koajs/compose
[david-image]: http://img.shields.io/david/koajs/compose.svg?style=flat-square
[david-url]: https://david-dm.org/koajs/compose
[license-image]: http://img.shields.io/npm/l/koa-compose.svg?style=flat-square
[license-url]: LICENSE
[downloads-image]: http://img.shields.io/npm/dm/koa-compose.svg?style=flat-square
[downloads-url]: https://npmjs.org/package/koa-compose
