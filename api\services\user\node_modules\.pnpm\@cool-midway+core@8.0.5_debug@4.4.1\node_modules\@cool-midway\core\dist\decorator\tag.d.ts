export declare const COOL_URL_TAG_KEY = "decorator:cool:url:tag";
export declare const COOL_METHOD_TAG_KEY = "decorator:cool:method:tag";
export declare enum TagTypes {
    IGNORE_TOKEN = "ignoreToken",
    IGNORE_SIGN = "ignoreSign"
}
export interface CoolUrlTagConfig {
    key: TagTypes | string;
    value?: string[];
}
/**
 * 打标记
 * @param data
 * @returns
 */
export declare function CoolUrlTag(data?: CoolUrlTagConfig): ClassDecorator;
/**
 * 方法打标记
 * @param data
 * @returns
 */
export declare function CoolTag(tag: TagTypes | string): MethodDecorator;
