{"version": 3, "sources": ["../browser/src/error/DriverOptionNotSetError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,YAAY;IACrD,YAAY,UAAkB;QAC1B,KAAK,CACD,kBAAkB,UAAU,gBAAgB;YACxC,sDAAsD,CAC7D,CAAA;IACL,CAAC;CACJ", "file": "DriverOptionNotSetError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if some required driver's option is not set.\n */\nexport class DriverOptionNotSetError extends TypeORMError {\n    constructor(optionName: string) {\n        super(\n            `Driver option (${optionName}) is not set. ` +\n                `Please set it to perform connection to the database.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}