"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MOLECYLER_KEY = void 0;
exports.CoolRpcService = CoolRpcService;
const core_1 = require("@midwayjs/core");
exports.MOLECYLER_KEY = 'decorator:cool:rpc';
/**
 * moleculer 微服务配置
 * @param option
 * @returns
 */
function CoolRpcService(option) {
    return (target) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, core_1.saveModule)(exports.MOLECYLER_KEY, target);
        // 保存一些元数据信息，任意你希望存的东西
        (0, core_1.saveClassMetadata)(exports.MOLECYLER_KEY, option, target);
        // 指定 IoC 容器创建实例的作用域，这里注册为请求作用域，这样能取到 ctx
        (0, core_1.Scope)(core_1.ScopeEnum.Request)(target);
    };
}
