{"version": 3, "sources": ["../browser/src/error/MissingJoinColumnError.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,sBAAuB,SAAQ,YAAY;IACpD,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,EAAE,CAAA;QAEP,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO;gBACR,0CAA0C,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,OAAO;oBAC7F,GAAG,QAAQ,CAAC,qBAAqB,CAAC,IAAI,IAAI,QAAQ,CAAC,eAAe,CAAC,YAAY,4BAA4B;oBAC3G,2DAA2D,CAAA;QACnE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,OAAO;gBACR,4BAA4B,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,4BAA4B;oBACpG,6CAA6C,CAAA;QACrD,CAAC;IACL,CAAC;CACJ", "file": "MissingJoinColumnError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingJoinColumnError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super()\n\n        if (relation.inverseRelation) {\n            this.message =\n                `<PERSON><PERSON><PERSON>olumn is missing on both sides of ${entityMetadata.name}#${relation.propertyName} and ` +\n                `${relation.inverseEntityMetadata.name}#${relation.inverseRelation.propertyName} one-to-one relationship. ` +\n                `You need to put Join<PERSON>olumn decorator on one of the sides.`\n        } else {\n            this.message =\n                `<PERSON><PERSON><PERSON><PERSON>um<PERSON> is missing on ${entityMetadata.name}#${relation.propertyName} one-to-one relationship. ` +\n                `You need to put <PERSON><PERSON><PERSON><PERSON>um<PERSON> decorator on it.`\n        }\n    }\n}\n"], "sourceRoot": ".."}