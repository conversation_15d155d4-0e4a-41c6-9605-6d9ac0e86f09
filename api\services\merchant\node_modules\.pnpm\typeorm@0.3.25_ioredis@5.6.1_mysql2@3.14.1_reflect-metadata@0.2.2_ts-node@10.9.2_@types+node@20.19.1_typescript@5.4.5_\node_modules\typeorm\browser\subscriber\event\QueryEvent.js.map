{"version": 3, "sources": ["../browser/src/subscriber/event/QueryEvent.ts"], "names": [], "mappings": "", "file": "QueryEvent.js", "sourcesContent": ["import { EntityManager } from \"../../entity-manager/EntityManager\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\n\n/**\n * BeforeQueryEvent is an object that broadcaster sends to the entity subscriber before query is ran against the database.\n */\nexport interface QueryEvent<Entity> {\n    /**\n     * Connection used in the event.\n     */\n    connection: DataSource\n\n    /**\n     * QueryRunner used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this query runner instance.\n     */\n    queryRunner: QueryRunner\n\n    /**\n     * EntityManager used in the event transaction.\n     * All database operations in the subscribed event listener should be performed using this entity manager instance.\n     */\n    manager: EntityManager\n\n    /**\n     * Query that is being executed.\n     */\n    query: string\n\n    /**\n     * Parameters used in the query.\n     */\n    parameters?: any[]\n}\n\nexport interface BeforeQueryEvent<Entity> extends QueryEvent<Entity> {}\n\nexport interface AfterQueryEvent<Entity> extends QueryEvent<Entity> {\n    /**\n     * Whether the query was successful.\n     */\n    success: boolean\n\n    /**\n     * The duration of the query execution, in milliseconds.\n     */\n    executionTime?: number\n\n    /**\n     * The raw results from the database if the query was successful.\n     */\n    rawResults?: any\n\n    /**\n     * The error thrown if the query was unsuccessful.\n     */\n    error?: any\n}\n"], "sourceRoot": "../.."}