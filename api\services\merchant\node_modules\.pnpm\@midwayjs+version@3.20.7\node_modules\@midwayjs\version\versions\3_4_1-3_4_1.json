{"@midwayjs/orm": "3.4.1", "@midwayjs/runtime-engine": "3.4.0", "@midwayjs/runtime-mock": "3.4.0", "@midwayjs/serverless-app": "3.4.1", "@midwayjs/serverless-aws-starter": "3.4.0", "@midwayjs/serverless-fc-starter": "3.4.1", "@midwayjs/serverless-fc-trigger": "3.4.1", "@midwayjs/serverless-scf-starter": "3.4.0", "@midwayjs/serverless-scf-trigger": "3.4.1", "@midwayjs/serverless-vercel-starter": "3.4.1", "@midwayjs/serverless-vercel-trigger": "3.4.0", "@midwayjs/egg-layer": "3.4.1", "@midwayjs/express-layer": "3.4.1", "@midwayjs/faas-typings": "3.3.5", "@midwayjs/koa-layer": "3.4.1", "@midwayjs/fc-starter": "3.4.1", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/serverless-worker-starter": "3.4.1", "@midwayjs/static-layer": "3.4.1", "@midwayjs/async-hooks-context-manager": "3.4.1", "@midwayjs/axios": "3.4.1", "@midwayjs/bootstrap": "3.4.1", "@midwayjs/cache": "3.4.1", "@midwayjs/code-dye": "3.4.1", "@midwayjs/consul": "3.4.1", "@midwayjs/core": "3.4.1", "@midwayjs/cos": "3.4.1", "@midwayjs/cross-domain": "3.4.1", "@midwayjs/decorator": "3.4.1", "@midwayjs/express-session": "3.4.1", "@midwayjs/faas": "3.4.1", "@midwayjs/grpc": ["3.4.1", "3.4.2"], "@midwayjs/http-proxy": "3.4.1", "@midwayjs/i18n": "3.4.1", "@midwayjs/info": "3.4.1", "@midwayjs/jwt": "3.4.1", "@midwayjs/kafka": "3.4.1", "@midwayjs/mikro": "3.4.1", "@midwayjs/mock": "3.4.1", "@midwayjs/mongoose": "3.4.1", "@midwayjs/oss": "3.4.1", "@midwayjs/otel": "3.4.1", "@midwayjs/passport": "3.4.1", "@midwayjs/process-agent": "3.4.1", "@midwayjs/prometheus-socket-io": "3.4.1", "@midwayjs/prometheus": "3.4.1", "@midwayjs/rabbitmq": "3.4.1", "@midwayjs/redis": "3.4.1", "@midwayjs/security": "3.4.1", "@midwayjs/sequelize": "3.4.1", "@midwayjs/session": "3.4.1", "@midwayjs/socketio": "3.4.1", "@midwayjs/static-file": "3.4.1", "@midwayjs/swagger": "3.4.1", "@midwayjs/tablestore": "3.4.1", "@midwayjs/task": "3.4.1", "@midwayjs/typegoose": "3.4.1", "@midwayjs/typeorm": "3.4.1", "@midwayjs/upload": "3.4.1", "@midwayjs/validate": "3.4.1", "@midwayjs/version": ["3.4.1", "3.4.2"], "@midwayjs/view-ejs": "3.4.1", "@midwayjs/view-nunjucks": "3.4.1", "@midwayjs/view": "3.4.1", "@midwayjs/express": "3.4.1", "@midwayjs/koa": "3.4.1", "@midwayjs/web": "3.4.1", "@midwayjs/ws": "3.4.1"}