{"@midwayjs/faas-typings": "3.3.5", "@midwayjs/fc-starter": "3.4.13", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.13", "@midwayjs/axios": "3.4.13", "@midwayjs/bootstrap": "3.4.13", "@midwayjs/cache": "3.4.13", "@midwayjs/code-dye": "3.4.13", "@midwayjs/consul": "3.4.13", "@midwayjs/core": "3.4.13", "@midwayjs/cos": "3.4.13", "@midwayjs/cross-domain": "3.4.13", "@midwayjs/decorator": "3.4.11", "@midwayjs/express-session": "3.4.13", "@midwayjs/faas": "3.4.13", "@midwayjs/grpc": "3.4.13", "@midwayjs/http-proxy": "3.4.13", "@midwayjs/i18n": "3.4.13", "@midwayjs/info": "3.4.13", "@midwayjs/jwt": "3.4.13", "@midwayjs/kafka": "3.4.13", "@midwayjs/mikro": "3.4.13", "@midwayjs/mock": "3.4.13", "@midwayjs/mongoose": "3.4.13", "@midwayjs/oss": "3.4.13", "@midwayjs/otel": "3.4.13", "@midwayjs/passport": "3.4.13", "@midwayjs/process-agent": "3.4.13", "@midwayjs/prometheus-socket-io": "3.4.13", "@midwayjs/prometheus": "3.4.13", "@midwayjs/rabbitmq": "3.4.13", "@midwayjs/redis": "3.4.13", "@midwayjs/security": "3.4.13", "@midwayjs/sequelize": "3.4.13", "@midwayjs/session": "3.4.13", "@midwayjs/socketio": "3.4.13", "@midwayjs/static-file": "3.4.13", "@midwayjs/swagger": "3.4.13", "@midwayjs/tablestore": "3.4.13", "@midwayjs/task": "3.4.13", "@midwayjs/typegoose": "3.4.13", "@midwayjs/typeorm": "3.4.13", "@midwayjs/upload": "3.4.13", "@midwayjs/validate": "3.4.13", "@midwayjs/version": "3.4.13", "@midwayjs/view-ejs": "3.4.13", "@midwayjs/view-nunjucks": "3.4.13", "@midwayjs/view": "3.4.13", "@midwayjs/express": "3.4.13", "@midwayjs/koa": "3.4.13", "@midwayjs/web": "3.4.13", "@midwayjs/ws": "3.4.13"}