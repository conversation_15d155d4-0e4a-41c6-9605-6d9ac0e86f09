{"name": "payment-service", "version": "1.0.0", "description": "支付微服务，负责订单支付、回调、退款等业务", "main": "main.ts", "scripts": {"start": "node bootstrap.js", "dev": "cross-env NODE_ENV=local ts-node-dev --respawn --transpileOnly main.ts"}, "dependencies": {"@midwayjs/core": "^3.0.0", "@midwayjs/koa": "^3.0.0", "@midwayjs/decorator": "^3.0.0", "@midwayjs/typeorm": "^3.0.0", "@cool-midway/core": "^3.0.0", "@cool-midway/rpc": "^3.0.0", "typeorm": "^0.2.41", "bignumber.js": "^9.0.0"}, "devDependencies": {"ts-node": "^10.0.0", "ts-node-dev": "^2.0.0", "typescript": "^4.0.0", "cross-env": "^7.0.0"}}