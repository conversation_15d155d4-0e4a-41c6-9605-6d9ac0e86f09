{"version": 3, "sources": ["../browser/src/metadata-args/EmbeddedMetadataArgs.ts"], "names": [], "mappings": "", "file": "EmbeddedMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for EmbeddedMetadata class.\n */\nexport interface EmbeddedMetadataArgs {\n    /**\n     * Class to which this column is applied.\n     */\n    target: Function | string\n\n    /**\n     * Class's property name to which this column is applied.\n     */\n    propertyName: string\n\n    /**\n     * Indicates if this embedded is array or not.\n     */\n    isArray: boolean\n\n    /**\n     * Prefix of the embedded, used instead of propertyName.\n     * If set to empty string, then prefix is not set at all.\n     */\n    prefix?: string | boolean\n\n    /**\n     * Type of the class to be embedded.\n     */\n    type: (type?: any) => Function | string\n}\n"], "sourceRoot": ".."}