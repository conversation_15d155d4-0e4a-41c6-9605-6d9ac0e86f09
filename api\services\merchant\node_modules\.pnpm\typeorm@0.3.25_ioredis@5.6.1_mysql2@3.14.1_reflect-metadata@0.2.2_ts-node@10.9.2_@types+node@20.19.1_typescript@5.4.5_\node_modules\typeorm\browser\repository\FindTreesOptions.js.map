{"version": 3, "sources": ["../browser/src/repository/FindTreesOptions.ts"], "names": [], "mappings": "", "file": "FindTreesOptions.js", "sourcesContent": ["/**\n * Special options passed to TreeRepository#findTrees\n */\nexport interface FindTreesOptions {\n    /**\n     * When loading a tree from a TreeRepository, limits the depth of the descendents loaded\n     */\n    depth?: number\n}\n"], "sourceRoot": ".."}