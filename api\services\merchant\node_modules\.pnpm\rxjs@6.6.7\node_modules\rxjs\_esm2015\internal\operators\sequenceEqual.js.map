{"version": 3, "file": "sequenceEqual.js", "sources": ["../../../src/internal/operators/sequenceEqual.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AA8D3C,MAAM,UAAU,aAAa,CAAI,SAAwB,EACxB,UAAoC;IACnE,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC;AAClG,CAAC;AAED,MAAM,OAAO,qBAAqB;IAChC,YAAoB,SAAwB,EACxB,UAAmC;QADnC,cAAS,GAAT,SAAS,CAAe;QACxB,eAAU,GAAV,UAAU,CAAyB;IACvD,CAAC;IAED,IAAI,CAAC,UAA+B,EAAE,MAAW;QAC/C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACpG,CAAC;CACF;AAOD,MAAM,OAAO,uBAA8B,SAAQ,UAAa;IAK9D,YAAY,WAAwB,EAChB,SAAwB,EACxB,UAAmC;QACrD,KAAK,CAAC,WAAW,CAAC,CAAC;QAFD,cAAS,GAAT,SAAS,CAAe;QACxB,eAAU,GAAV,UAAU,CAAyB;QAN/C,OAAE,GAAQ,EAAE,CAAC;QACb,OAAE,GAAQ,EAAE,CAAC;QACb,iBAAY,GAAG,KAAK,CAAC;QAM1B,IAAI,CAAC,WAA4B,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,gCAAgC,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACvH,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAEM,SAAS;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,WAAW;QACT,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QACpC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI;gBACF,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACpD;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;SACF;IACH,CAAC;IAED,IAAI,CAAC,KAAc;QACjB,MAAM,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAC7B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,KAAQ;QACZ,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;IACH,CAAC;CACF;AAED,MAAM,gCAAuC,SAAQ,UAAa;IAChE,YAAY,WAAwB,EAAU,MAAqC;QACjF,KAAK,CAAC,WAAW,CAAC,CAAC;QADyB,WAAM,GAAN,MAAM,CAA+B;IAEnF,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAES,MAAM,CAAC,GAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,SAAS;QACjB,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;CACF"}