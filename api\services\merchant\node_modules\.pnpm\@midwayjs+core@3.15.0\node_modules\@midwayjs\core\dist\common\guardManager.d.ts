import { CommonGuardUnion, IGuard, IMidwayContext } from '../interface';
export declare class GuardManager<CTX extends IMidwayContext = IMidwayContext> extends Array<new (...args: any[]) => IGuard<any>> {
    addGlobalGuard(guards: CommonGuardUnion<CTX>): void;
    runGuard(ctx: CTX, supplierClz: new (...args: any[]) => any, methodName: string): Promise<boolean>;
}
//# sourceMappingURL=guardManager.d.ts.map