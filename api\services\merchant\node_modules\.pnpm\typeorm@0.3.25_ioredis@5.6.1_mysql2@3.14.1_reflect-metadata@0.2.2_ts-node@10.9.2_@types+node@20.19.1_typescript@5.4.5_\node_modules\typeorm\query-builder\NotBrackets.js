"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotBrackets = void 0;
const Brackets_1 = require("./Brackets");
/**
 * Syntax sugar.
 * Allows to use negate brackets in WHERE expressions for better syntax.
 */
class NotBrackets extends Brackets_1.Brackets {
    constructor() {
        super(...arguments);
        this["@instanceof"] = Symbol.for("NotBrackets");
    }
}
exports.NotBrackets = NotBrackets;

//# sourceMappingURL=NotBrackets.js.map
