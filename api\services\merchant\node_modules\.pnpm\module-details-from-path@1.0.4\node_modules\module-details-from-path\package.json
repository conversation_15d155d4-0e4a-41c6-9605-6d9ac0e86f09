{"name": "module-details-from-path", "version": "1.0.4", "description": "Resolve npm package details, like name and base path, given an absolute path to a file inside a package", "main": "index.js", "files": [], "dependencies": {}, "devDependencies": {"bench-node": "^0.5.4", "standard": "^15.0.1", "tape": "^4.6.0"}, "scripts": {"bench": "node --allow-natives-syntax bench.js", "lint": "standard", "test": "standard && node test.js", "test:ci": "node test.js"}, "repository": {"type": "git", "url": "git+https://github.com/watson/module-details-from-path.git"}, "keywords": ["node", "nodejs", "npm", "module", "package", "extract", "parse", "name", "basedir", "directory", "path", "relative"], "author": "<PERSON> <<EMAIL>> (https://wa.tson.dk/)", "license": "MIT", "bugs": {"url": "https://github.com/watson/module-details-from-path/issues"}, "homepage": "https://github.com/watson/module-details-from-path#readme", "coordinates": [55.666507, 12.5798711]}