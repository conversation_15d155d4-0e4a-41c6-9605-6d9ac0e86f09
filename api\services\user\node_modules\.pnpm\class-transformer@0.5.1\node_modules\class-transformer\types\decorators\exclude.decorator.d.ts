import { ExcludeOptions } from '../interfaces';
/**
 * Marks the given class or property as excluded. By default the property is excluded in both
 * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction
 * via using the `toPlainOnly` or `toClassOnly` option.
 *
 * Can be applied to class definitions and properties.
 */
export declare function Exclude(options?: ExcludeOptions): PropertyDecorator & ClassDecorator;
