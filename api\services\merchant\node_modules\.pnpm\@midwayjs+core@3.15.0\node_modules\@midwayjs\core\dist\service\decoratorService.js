"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayDecoratorService = void 0;
const decorator_1 = require("../decorator");
const interface_1 = require("../interface");
const aspectService_1 = require("./aspectService");
const error_1 = require("../error");
const util = require("util");
const types_1 = require("../util/types");
const debug = util.debuglog('midway:debug');
let MidwayDecoratorService = class MidwayDecoratorService {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
        this.propertyHandlerMap = new Map();
        this.methodDecoratorMap = new Map();
        this.parameterDecoratorMap = new Map();
        this.parameterDecoratorPipes = new Map();
    }
    init() {
        // add custom method decorator listener
        this.applicationContext.onBeforeBind(Clzz => {
            // find custom method decorator metadata, include method decorator information array
            const methodDecoratorMetadataList = (0, decorator_1.getClassMetadata)(decorator_1.INJECT_CUSTOM_METHOD, Clzz);
            if (methodDecoratorMetadataList) {
                // loop it, save this order for decorator run
                for (const meta of methodDecoratorMetadataList) {
                    const { propertyName, key, metadata, options } = meta;
                    if (!options || !options.impl) {
                        continue;
                    }
                    // add aspect implementation first
                    this.aspectService.interceptPrototypeMethod(Clzz, propertyName, () => {
                        const methodDecoratorHandler = this.methodDecoratorMap.get(key);
                        if (!methodDecoratorHandler) {
                            throw new error_1.MidwayCommonError(`Method Decorator "${key}" handler not found, please register first.`);
                        }
                        return methodDecoratorHandler({
                            target: Clzz,
                            propertyName,
                            metadata,
                        });
                    });
                }
            }
            // find custom param decorator metadata
            const parameterDecoratorMetadata = (0, decorator_1.getClassMetadata)(decorator_1.INJECT_CUSTOM_PARAM, Clzz);
            if (parameterDecoratorMetadata) {
                // loop it, save this order for decorator run
                for (const methodName of Object.keys(parameterDecoratorMetadata)) {
                    // add aspect implementation first
                    this.aspectService.interceptPrototypeMethod(Clzz, methodName, () => {
                        return {
                            before: async (joinPoint) => {
                                // joinPoint.args
                                const newArgs = [...joinPoint.args];
                                for (const meta of parameterDecoratorMetadata[methodName]) {
                                    const { propertyName, key, metadata, parameterIndex, options, } = meta;
                                    let parameterDecoratorHandler;
                                    if (options && options.impl) {
                                        parameterDecoratorHandler =
                                            this.parameterDecoratorMap.get(key);
                                        if (!parameterDecoratorHandler) {
                                            throw new error_1.MidwayCommonError(`Parameter Decorator "${key}" handler not found, please register first.`);
                                        }
                                    }
                                    else {
                                        // set default handler
                                        parameterDecoratorHandler = async ({ parameterIndex, originArgs, }) => {
                                            return originArgs[parameterIndex];
                                        };
                                    }
                                    const paramTypes = (0, decorator_1.getMethodParamTypes)(Clzz, propertyName);
                                    let skipPipes = false;
                                    try {
                                        newArgs[parameterIndex] = await parameterDecoratorHandler({
                                            metadata,
                                            propertyName,
                                            parameterIndex,
                                            target: Clzz,
                                            originArgs: newArgs,
                                            originParamType: paramTypes[parameterIndex],
                                        });
                                    }
                                    catch (err) {
                                        skipPipes = true;
                                        if ((options === null || options === void 0 ? void 0 : options.throwError) === true) {
                                            throw err;
                                        }
                                        else {
                                            // ignore
                                            debug(`[core]: Parameter decorator throw error and use origin args, ${err.stack}`);
                                        }
                                    }
                                    if (skipPipes) {
                                        continue;
                                    }
                                    const pipes = [
                                        ...(this.parameterDecoratorPipes.get(key) || []),
                                        ...((options === null || options === void 0 ? void 0 : options.pipes) || []),
                                    ];
                                    for (const pipe of pipes) {
                                        let transform;
                                        if ('transform' in pipe) {
                                            transform = pipe['transform'].bind(pipe);
                                        }
                                        else if ((0, types_1.isClass)(pipe)) {
                                            const ins = await this.applicationContext.getAsync(pipe);
                                            transform = ins.transform.bind(ins);
                                        }
                                        else if (typeof pipe === 'function') {
                                            transform = pipe;
                                        }
                                        else {
                                            throw new error_1.MidwayParameterError('Pipe must be a function or implement PipeTransform interface');
                                        }
                                        newArgs[parameterIndex] = await transform(newArgs[parameterIndex], {
                                            metaType: (0, decorator_1.transformTypeFromTSDesign)(paramTypes[parameterIndex]),
                                            metadata,
                                            target: joinPoint.target,
                                            methodName: joinPoint.methodName,
                                        });
                                    }
                                }
                                joinPoint.args = newArgs;
                            },
                        };
                    });
                }
            }
        });
        // add custom property decorator listener
        this.applicationContext.onObjectCreated((instance, options) => {
            if (this.propertyHandlerMap.size > 0 &&
                Array.isArray(options.definition.handlerProps)) {
                // has bind in container
                for (const item of options.definition.handlerProps) {
                    this.defineGetterPropertyValue(item, instance, this.getHandler(item.key));
                }
            }
        });
        // register @ApplicationContext
        this.registerPropertyHandler(decorator_1.APPLICATION_CONTEXT_KEY, (propertyName, mete) => {
            return this.applicationContext;
        });
    }
    registerPropertyHandler(decoratorKey, fn) {
        debug(`[core]: Register property decorator key="${decoratorKey}"`);
        this.propertyHandlerMap.set(decoratorKey, fn);
    }
    registerMethodHandler(decoratorKey, fn) {
        debug(`[core]: Register method decorator key="${decoratorKey}"`);
        this.methodDecoratorMap.set(decoratorKey, fn);
    }
    registerParameterHandler(decoratorKey, fn) {
        debug(`[core]: Register parameter decorator key="${decoratorKey}"`);
        this.parameterDecoratorMap.set(decoratorKey, fn);
    }
    registerParameterPipes(decoratorKey, pipes) {
        if (!this.parameterDecoratorPipes.has(decoratorKey)) {
            this.parameterDecoratorPipes.set(decoratorKey, []);
        }
        this.parameterDecoratorPipes.set(decoratorKey, this.parameterDecoratorPipes.get(decoratorKey).concat(pipes));
    }
    /**
     * binding getter method for decorator
     *
     * @param prop
     * @param instance
     * @param getterHandler
     */
    defineGetterPropertyValue(prop, instance, getterHandler) {
        if (prop && getterHandler) {
            if (prop.propertyName) {
                Object.defineProperty(instance, prop.propertyName, {
                    get: () => { var _a; return getterHandler(prop.propertyName, (_a = prop.metadata) !== null && _a !== void 0 ? _a : {}, instance); },
                    configurable: true,
                    enumerable: true,
                });
            }
        }
    }
    getHandler(key) {
        if (this.propertyHandlerMap.has(key)) {
            return this.propertyHandlerMap.get(key);
        }
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", aspectService_1.MidwayAspectService)
], MidwayDecoratorService.prototype, "aspectService", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MidwayDecoratorService.prototype, "init", null);
MidwayDecoratorService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayDecoratorService);
exports.MidwayDecoratorService = MidwayDecoratorService;
//# sourceMappingURL=decoratorService.js.map