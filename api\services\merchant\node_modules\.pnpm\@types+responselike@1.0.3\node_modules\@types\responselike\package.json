{"name": "@types/responselike", "version": "1.0.3", "description": "TypeScript definitions for responselike", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/responselike", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/responselike"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "ea1d581578b0ef6027b7cd6aa25990bb9ee8723d002d0617acf0aa4d3324aa49", "typeScriptVersion": "4.5"}