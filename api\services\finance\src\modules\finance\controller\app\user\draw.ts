import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceUserDrawEntity } from '../../../entity/user/draw';
import { FinanceUserDrawService } from '../../../service/user/draw';
import { Body, Inject, Post } from '@midwayjs/core';

/**
 * 用户提现信息
 */
@CoolController({
  api: ['update'],
  entity: FinanceUserDrawEntity,
  service: FinanceUserDrawService,
  insertParam: ctx => {
    return {
      userId: ctx.user.id,
    };
  },
  pageQueryOp: {
    where: ctx => {
      return [['a.userId =:userId', { userId: ctx.user.id }]];
    },
  },
})
export class AppFinanceUserDrawController extends BaseController {
  @Inject()
  financeUserDrawService: FinanceUserDrawService;

  @Inject()
  ctx;

  @Post('/submit', { summary: '提交' })
  async submit(
    // 短信验证码
    @Body('code') code: string,
    // 其他数据
    @Body('data') data: FinanceUserDrawEntity
  ) {
    await this.financeUserDrawService.submit(data, code, this.ctx.user.id);
    return this.ok();
  }

  @Post('/detail', { summary: '详情' })
  async detail() {
    return this.ok(
      await this.financeUserDrawService.getAccount(this.ctx.user.id)
    );
  }
}
