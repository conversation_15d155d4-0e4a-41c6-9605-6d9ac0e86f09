{"name": "@types/superagent", "version": "4.1.14", "description": "TypeScript definitions for SuperAgent", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/superagent", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/NicoZelaya", "githubUsername": "NicoZelaya"}, {"name": "<PERSON>", "url": "https://github.com/mxl", "githubUsername": "mxl"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/paplorinc", "githubUsername": "paplorinc"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/shreyjain1994", "githubUsername": "shreyjain1994"}, {"name": "<PERSON>", "url": "https://github.com/zopf", "githubUsername": "zopf"}, {"name": "<PERSON>", "url": "https://github.com/beeequeue", "githubUsername": "beeequeue"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lukaselmer", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theQuazz", "githubUsername": "theQuazz"}, {"name": "<PERSON>", "url": "https://github.com/carnesen", "githubUsername": "carnesen"}, {"name": "<PERSON>", "url": "https://github.com/ghostganz", "githubUsername": "ghostganz"}, {"name": "LuckyWind_sck", "url": "https://github.com/LuckyWindsck", "githubUsername": "LuckyW<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/superagent"}, "scripts": {}, "dependencies": {"@types/cookiejar": "*", "@types/node": "*"}, "typesPublisherContentHash": "adb7f75ff6782d598492e408db18e6fe5cb5011ac5f0397805a0b6c686ba2542", "typeScriptVersion": "3.8"}