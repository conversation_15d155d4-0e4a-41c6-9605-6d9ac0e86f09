{"version": 3, "sources": ["../../src/metadata-args/EntitySubscriberMetadataArgs.ts"], "names": [], "mappings": "", "file": "EntitySubscriberMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for EntitySubscriberMetadata class.\n */\nexport interface EntitySubscriberMetadataArgs {\n    /**\n     * Class to which subscriber is applied.\n     */\n    readonly target: Function\n}\n"], "sourceRoot": ".."}