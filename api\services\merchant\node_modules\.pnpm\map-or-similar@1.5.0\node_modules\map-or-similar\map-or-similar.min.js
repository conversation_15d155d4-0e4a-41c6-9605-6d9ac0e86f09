!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var i;i="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,i.mapOrSimilar=t()}}(function(){var t,i,e;return function t(i,e,s){function n(o,l){if(!e[o]){if(!i[o]){var a="function"==typeof require&&require;if(!l&&a)return a(o,!0);if(r)return r(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var h=e[o]={exports:{}};i[o][0].call(h.exports,function(t){var e=i[o][1][t];return n(e?e:t)},h,h.exports,t,i,e,s)}return e[o].exports}for(var r="function"==typeof require&&require,o=0;o<s.length;o++)n(s[o]);return n}({1:[function(t,i,e){i.exports=function(i){if("function"!=typeof Map||i){var e=t("./similar");return new e}return new Map}},{"./similar":2}],2:[function(t,i,e){function s(){return this.list=[],this.lastItem=void 0,this.size=0,this}s.prototype.get=function(t){var i;return this.lastItem&&this.isEqual(this.lastItem.key,t)?this.lastItem.val:(i=this.indexOf(t),i>=0?(this.lastItem=this.list[i],this.list[i].val):void 0)},s.prototype.set=function(t,i){var e;return this.lastItem&&this.isEqual(this.lastItem.key,t)?(this.lastItem.val=i,this):(e=this.indexOf(t),e>=0?(this.lastItem=this.list[e],this.list[e].val=i,this):(this.lastItem={key:t,val:i},this.list.push(this.lastItem),this.size++,this))},s.prototype.delete=function(t){var i;if(this.lastItem&&this.isEqual(this.lastItem.key,t)&&(this.lastItem=void 0),i=this.indexOf(t),i>=0)return this.size--,this.list.splice(i,1)[0]},s.prototype.has=function(t){var i;return!(!this.lastItem||!this.isEqual(this.lastItem.key,t))||(i=this.indexOf(t),i>=0&&(this.lastItem=this.list[i],!0))},s.prototype.forEach=function(t,i){var e;for(e=0;e<this.size;e++)t.call(i||this,this.list[e].val,this.list[e].key,this)},s.prototype.indexOf=function(t){var i;for(i=0;i<this.size;i++)if(this.isEqual(this.list[i].key,t))return i;return-1},s.prototype.isEqual=function(t,i){return t===i||t!==t&&i!==i},i.exports=s},{}]},{},[1])(1)});
