"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Caching = exports.CACHE_DECORATOR_KEY = void 0;
const core_1 = require("@midwayjs/core");
exports.CACHE_DECORATOR_KEY = 'cache-manager:caching';
function Caching(cacheInstanceName, cacheKeyOrTTL, ttl) {
    if (typeof cacheKeyOrTTL === 'number') {
        ttl = cacheKeyOrTTL;
        cacheKeyOrTTL = undefined;
    }
    return (0, core_1.createCustomMethodDecorator)(exports.CACHE_DECORATOR_KEY, {
        cacheInstanceName,
        cacheKey: cacheKeyOrTTL,
        ttl,
    });
}
exports.Caching = Caching;
//# sourceMappingURL=cacheKey.js.map