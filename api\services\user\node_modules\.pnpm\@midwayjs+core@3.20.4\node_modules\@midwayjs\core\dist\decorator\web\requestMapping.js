"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.All = exports.Head = exports.Options = exports.Patch = exports.Put = exports.Del = exports.Get = exports.Post = exports.RequestMapping = exports.RequestMethod = void 0;
/**
 * 'HEAD', 'OPTIONS', 'GET', 'PUT', 'PATCH', 'POST', 'DELETE' 封装
 */
const __1 = require("../");
exports.RequestMethod = {
    GET: 'get',
    POST: 'post',
    PUT: 'put',
    DELETE: 'delete',
    PATCH: 'patch',
    ALL: 'all',
    OPTIONS: 'options',
    HEAD: 'head',
};
const defaultMetadata = {
    path: '/',
    requestMethod: exports.RequestMethod.GET,
    routerName: null,
    middleware: [],
};
const RequestMapping = (metadata = defaultMetadata) => {
    const path = metadata.path || '/';
    const requestMethod = metadata.requestMethod || exports.RequestMethod.GET;
    const routerName = metadata.routerName;
    const middleware = metadata.middleware;
    return (target, key, descriptor) => {
        var _a;
        (0, __1.attachClassMetadata)(__1.WEB_ROUTER_KEY, {
            path,
            requestMethod,
            routerName,
            method: key,
            middleware,
            summary: (metadata === null || metadata === void 0 ? void 0 : metadata.summary) || '',
            description: (metadata === null || metadata === void 0 ? void 0 : metadata.description) || '',
            ignoreGlobalPrefix: (_a = metadata === null || metadata === void 0 ? void 0 : metadata.ignoreGlobalPrefix) !== null && _a !== void 0 ? _a : false,
        }, target);
        return descriptor;
    };
};
exports.RequestMapping = RequestMapping;
const createMappingDecorator = (method) => (path, routerOptions = { middleware: [] }) => {
    return (0, exports.RequestMapping)(Object.assign(routerOptions, {
        requestMethod: method,
        path,
    }));
};
/**
 * Routes HTTP POST requests to the specified path.
 */
exports.Post = createMappingDecorator(exports.RequestMethod.POST);
/**
 * Routes HTTP GET requests to the specified path.
 */
exports.Get = createMappingDecorator(exports.RequestMethod.GET);
/**
 * Routes HTTP DELETE requests to the specified path.
 */
exports.Del = createMappingDecorator(exports.RequestMethod.DELETE);
/**
 * Routes HTTP PUT requests to the specified path.
 */
exports.Put = createMappingDecorator(exports.RequestMethod.PUT);
/**
 * Routes HTTP PATCH requests to the specified path.
 */
exports.Patch = createMappingDecorator(exports.RequestMethod.PATCH);
/**
 * Routes HTTP OPTIONS requests to the specified path.
 */
exports.Options = createMappingDecorator(exports.RequestMethod.OPTIONS);
/**
 * Routes HTTP HEAD requests to the specified path.
 */
exports.Head = createMappingDecorator(exports.RequestMethod.HEAD);
/**
 * Routes all HTTP requests to the specified path.
 */
exports.All = createMappingDecorator(exports.RequestMethod.ALL);
//# sourceMappingURL=requestMapping.js.map