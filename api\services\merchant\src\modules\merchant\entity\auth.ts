import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * 商户系统菜单实体
 */
@Entity('merchant_sys_menu')
export class MerchantSysMenuEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ comment: '菜单名称' })
  name!: string;

  @Column({ comment: '路由地址', nullable: true })
  router!: string;

  @Column({ comment: '组件路径', nullable: true })
  component!: string;

  @Column({ comment: '权限标识', nullable: true })
  perms!: string;

  @Column({ comment: '菜单类型 0-目录 1-菜单 2-按钮', default: 0 })
  type!: number;

  @Column({ comment: '图标', nullable: true })
  icon!: string;

  @Column({ comment: '排序', default: 0 })
  orderNum!: number;

  @Column({ comment: '上级菜单ID', default: 0 })
  parentId!: number;

  @Column({ comment: '路由缓存', default: true })
  keepAlive!: boolean;

  @Column({ comment: '是否显示', default: true })
  isShow!: boolean;

  @CreateDateColumn({ comment: '创建时间' })
  createTime!: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime!: Date;
}

/**
 * 商户系统角色实体
 */
@Entity('merchant_sys_role')
export class MerchantSysRoleEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ comment: '角色名称' })
  name!: string;

  @Column({ comment: '角色标识' })
  label!: string;

  @Column({ comment: '备注', nullable: true })
  remark!: string;

  @Column({ comment: '状态：1-启用 0-禁用', default: 1 })
  status!: number;

  @Column({ comment: '数据权限', default: 1 })
  relevance!: number;

  @Column({ comment: '菜单权限', type: 'text', nullable: true })
  menuIdList!: string;

  @Column({ comment: '部门权限', type: 'text', nullable: true })
  departmentIdList!: string;

  @CreateDateColumn({ comment: '创建时间' })
  createTime!: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime!: Date;
}

/**
 * 商户系统用户实体 (严格对应前端 Api.User.UserInfo)
 */
@Entity('merchant_sys_user')
export class MerchantSysUserEntity {
  @PrimaryGeneratedColumn()
  userId!: number;

  @Column({ comment: '用户名', unique: true })
  userName!: string;

  @Column({ comment: '密码' })
  password!: string;

  @Column({ comment: '密码版本', default: 1 })
  passwordV!: number;

  @Column({ comment: '角色权限数组 (严格对应前端 Api.User.UserInfo.roles)', type: 'text', nullable: true })
  roles!: string;

  @Column({ comment: '按钮权限数组 (严格对应前端 Api.User.UserInfo.buttons)', type: 'text', nullable: true })
  buttons!: string;

  @Column({ comment: '头像 (严格对应前端 Api.User.UserInfo.avatar)', nullable: true })
  avatar!: string;

  @Column({ comment: '邮箱 (严格对应前端 Api.User.UserInfo.email)', nullable: true })
  email!: string;

  @Column({ comment: '手机号 (严格对应前端 Api.User.UserInfo.phone)', nullable: true })
  phone!: string;

  @Column({ comment: '状态 1-启用 0-禁用', default: 1 })
  status!: number;

  @CreateDateColumn({ comment: '创建时间' })
  createTime!: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime!: Date;
} 