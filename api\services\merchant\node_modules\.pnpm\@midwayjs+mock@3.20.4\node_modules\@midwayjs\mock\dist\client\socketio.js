"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSocketIOClient = exports.SocketIOWrapperClient = void 0;
class SocketIOWrapperClient {
    constructor(socket) {
        this.socket = socket;
    }
    async connect() {
        return new Promise((resolve, reject) => {
            this.socket.on('connect', () => {
                resolve(this.socket);
            });
        });
    }
    getSocket() {
        return this.socket;
    }
    send(eventName, ...args) {
        this.socket.emit(eventName, ...args);
    }
    on(eventName, handler) {
        this.socket.on(eventName, handler);
    }
    once(eventName, handler) {
        return this.socket.once(eventName, handler);
    }
    removeListener(event, fn) {
        return this.socket.removeListener(event, fn);
    }
    emit(eventName, ...args) {
        return this.socket.emit(eventName, ...args);
    }
    async sendWithAck(eventName, ...args) {
        return new Promise((resolve, reject) => {
            this.socket.emit(eventName, ...args, resolve);
        });
    }
    close() {
        this.socket.close();
    }
}
exports.SocketIOWrapperClient = SocketIOWrapperClient;
async function createSocketIOClient(opts) {
    let url;
    if (opts.url) {
        url = opts.url;
    }
    else {
        url = `${opts.protocol || 'http'}://${opts.host || '127.0.0.1'}:${opts.port || 80}`;
        delete opts['port'];
    }
    if (opts.namespace) {
        url += opts.namespace;
    }
    const socket = require('socket.io-client')(url, opts);
    const client = new SocketIOWrapperClient(socket);
    await client.connect();
    return client;
}
exports.createSocketIOClient = createSocketIOClient;
//# sourceMappingURL=socketio.js.map