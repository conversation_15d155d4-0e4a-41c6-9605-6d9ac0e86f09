"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ThreadEventBus = void 0;
const worker_threads_1 = require("worker_threads");
const base_1 = require("./base");
class ThreadEventBus extends base_1.AbstractEventBus {
    constructor(options = {}) {
        super(options);
        if (!this.options.encoder) {
            this.options.encoder = message => {
                return message;
            };
        }
        if (!this.options.decoder) {
            this.options.decoder = serializedData => {
                return serializedData;
            };
        }
    }
    workerSubscribeMessage(subscribeMessageHandler) {
        worker_threads_1.parentPort.on('message', (serializedData) => {
            subscribeMessageHandler(this.options.decoder(serializedData));
        });
    }
    workerListenMessage(worker, subscribeMessageHandler) {
        worker.on('message', serializedData => {
            subscribeMessageHandler(this.options.decoder(serializedData));
        });
    }
    workerSendMessage(message) {
        worker_threads_1.parentPort.postMessage(this.options.encoder(message));
    }
    mainSendMessage(worker, message) {
        worker.postMessage(this.options.encoder(message));
    }
    isMain() {
        return !this.isWorker();
    }
    isWorker() {
        var _a;
        return (_a = this.options.isWorker) !== null && _a !== void 0 ? _a : !worker_threads_1.isMainThread;
    }
    getWorkerId(worker) {
        return String(worker ? worker.threadId : worker_threads_1.threadId);
    }
}
exports.ThreadEventBus = ThreadEventBus;
//# sourceMappingURL=thread.js.map