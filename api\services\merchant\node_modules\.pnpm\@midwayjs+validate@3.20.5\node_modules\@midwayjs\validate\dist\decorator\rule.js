"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RuleType = exports.Rule = void 0;
const Joi = require("joi");
exports.RuleType = Joi;
const core_1 = require("@midwayjs/core");
const constants_1 = require("../constants");
function Rule(rule, options = { required: true }) {
    return function (target, propertyKey) {
        if (propertyKey) {
            // property decorator
            if (!Joi.isSchema(rule)) {
                // 老代码，待废弃
                rule = Joi.object((0, core_1.getClassMetadata)(constants_1.RULES_KEY, rule)).meta({
                    id: rule.name,
                });
                if ((0, core_1.getPropertyType)(target, propertyKey).name === 'Array') {
                    rule = Joi.array().items(rule);
                    if (options.min) {
                        rule = rule.min(options.min);
                    }
                    if (options.max) {
                        rule = rule.max(options.max);
                    }
                }
                if (options.required) {
                    rule = rule.required();
                }
            }
            (0, core_1.attachClassMetadata)(constants_1.RULES_KEY, rule, target, propertyKey);
        }
        else {
            // class decorator
            if (Joi.isSchema(rule)) {
                // TODO 下一个大版本，metadata 这里要完全重构，临时先加一个后缀
                // mix schema with property
                // saveClassMetadata(RULES_CLASS_KEY + '_EXT', rule, target);
            }
        }
    };
}
exports.Rule = Rule;
//# sourceMappingURL=rule.js.map