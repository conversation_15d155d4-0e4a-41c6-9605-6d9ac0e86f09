import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { FinanceApplyDrawEntity } from '../../../entity/apply/draw';
import { Body, Inject, Post } from '@midwayjs/core';
import { FinanceApplyDrawService } from '../../../service/apply/draw';

/**
 * 提现申请
 */
@CoolController({
  api: ['page'],
  entity: FinanceApplyDrawEntity,
  service: FinanceApplyDrawService,
  pageQueryOp: {
    where: ctx => {
      const userId = ctx.user?.id;
      return [['a.userId = :userId', { userId }]];
    },
  },
})
export class AppFinanceApplyDrawController extends BaseController {
  @Inject()
  financeApplyDrawService: FinanceApplyDrawService;

  @Inject()
  ctx;

  @Post('/submit', { summary: '提现申请' })
  async submit(@Body('amount') amount: number, @Body('type') type: number) {
    const userId = this.ctx.user?.id;
    await this.financeApplyDrawService.submit(userId, amount, type);
    return this.ok();
  }
}
