{"version": 3, "file": "throttle.js", "sources": ["../../../src/internal/operators/throttle.ts"], "names": [], "mappings": ";AAMA,OAAO,EAAE,qBAAqB,EAAE,cAAc,EAAE,qBAAqB,EAAE,MAAM,mBAAmB,CAAC;AAOjG,MAAM,CAAC,IAAM,qBAAqB,GAAmB;IACnD,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,KAAK;CAChB,CAAC;AAgDF,MAAM,UAAU,QAAQ,CAAI,gBAA0D,EAC1D,MAA8C;IAA9C,uBAAA,EAAA,8BAA8C;IACxE,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAxF,CAAwF,CAAC;AAC7H,CAAC;AAED;IACE,0BAAoB,gBAA0D,EAC1D,OAAgB,EAChB,QAAiB;QAFjB,qBAAgB,GAAhB,gBAAgB,CAA0C;QAC1D,YAAO,GAAP,OAAO,CAAS;QAChB,aAAQ,GAAR,QAAQ,CAAS;IACrC,CAAC;IAED,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CACrB,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CACvF,CAAC;IACJ,CAAC;IACH,uBAAC;AAAD,CAAC,AAXD,IAWC;AAOD;IAAuC,8CAA2B;IAKhE,4BAAsB,WAA0B,EAC5B,gBAA6D,EAC7D,QAAiB,EACjB,SAAkB;QAHtC,YAIE,kBAAM,WAAW,CAAC,SACnB;QALqB,iBAAW,GAAX,WAAW,CAAe;QAC5B,sBAAgB,GAAhB,gBAAgB,CAA6C;QAC7D,cAAQ,GAAR,QAAQ,CAAS;QACjB,eAAS,GAAT,SAAS,CAAS;QAL9B,eAAS,GAAG,KAAK,CAAC;;IAO1B,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,IAAI,EAAE,CAAC;aACb;iBAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACtB;SACF;IACH,CAAC;IAEO,iCAAI,GAAZ;QACQ,IAAA,SAAgC,EAA9B,wBAAS,EAAE,0BAAU,CAAU;QACvC,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,UAAW,CAAC,CAAC;SAC5B;QACD,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAEO,qCAAQ,GAAhB,UAAiB,KAAQ;QACvB,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,CAAC,QAAQ,EAAE;YACd,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,QAAQ,EAAE,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACvF;IACH,CAAC;IAEO,gDAAmB,GAA3B,UAA4B,KAAQ;QAClC,IAAI;YACF,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;SACrC;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC5B,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAEO,2CAAc,GAAtB;QACQ,IAAA,SAAgC,EAA9B,0BAAU,EAAE,wBAAS,CAAU;QACvC,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,WAAW,EAAE,CAAC;SAC1B;QACD,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAE5B,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,IAAI,EAAE,CAAC;SACb;IACH,CAAC;IAED,uCAAU,GAAV;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,2CAAc,GAAd;QACE,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IACH,yBAAC;AAAD,CAAC,AAtED,CAAuC,qBAAqB,GAsE3D"}