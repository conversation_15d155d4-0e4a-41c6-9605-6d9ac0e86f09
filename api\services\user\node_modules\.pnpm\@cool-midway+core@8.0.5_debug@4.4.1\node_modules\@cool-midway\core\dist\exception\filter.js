"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolExceptionFilter = void 0;
const core_1 = require("@midwayjs/core");
const global_1 = require("../constant/global");
/**
 * 全局异常处理
 */
let CoolExceptionFilter = class CoolExceptionFilter {
    async catch(err) {
        const { RESCODE } = global_1.GlobalConfig.getInstance();
        this.coreLogger.error(err);
        return {
            code: err.status || RESCODE.COMMFAIL,
            message: err.message,
        };
    }
};
exports.CoolExceptionFilter = CoolExceptionFilter;
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], CoolExceptionFilter.prototype, "coreLogger", void 0);
exports.CoolExceptionFilter = CoolExceptionFilter = __decorate([
    (0, core_1.Catch)()
], CoolExceptionFilter);
