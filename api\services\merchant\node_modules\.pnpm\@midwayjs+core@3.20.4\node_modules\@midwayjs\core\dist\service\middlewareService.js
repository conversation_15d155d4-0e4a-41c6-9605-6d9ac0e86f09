"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayMiddlewareService = void 0;
const decorator_1 = require("../decorator");
const interface_1 = require("../interface");
const error_1 = require("../error");
const util_1 = require("../util");
const types_1 = require("../util/types");
const util_2 = require("util");
const debug = (0, util_2.debuglog)('midway:debug');
let MidwayMiddlewareService = class MidwayMiddlewareService {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
    }
    async compose(middleware, app, name) {
        var _a;
        if (!Array.isArray(middleware)) {
            throw new error_1.MidwayParameterError('Middleware stack must be an array');
        }
        const newMiddlewareArr = [];
        for (let fn of middleware) {
            if (types_1.Types.isClass(fn) || typeof fn === 'string' || (fn === null || fn === void 0 ? void 0 : fn['middleware'])) {
                let mw = (_a = fn === null || fn === void 0 ? void 0 : fn['middleware']) !== null && _a !== void 0 ? _a : fn;
                const mwConfig = fn === null || fn === void 0 ? void 0 : fn['options'];
                let mwName = fn === null || fn === void 0 ? void 0 : fn['name'];
                if (typeof mw === 'string' &&
                    !this.applicationContext.hasDefinition(mw)) {
                    throw new error_1.MidwayCommonError(`Middleware definition of "${mw}" not found in midway container`);
                }
                const classMiddleware = await this.applicationContext.getAsync(mw);
                if (classMiddleware) {
                    mwName = mwName !== null && mwName !== void 0 ? mwName : classMiddleware.constructor.name;
                    mw = await classMiddleware.resolve(app, mwConfig);
                    if (!mw) {
                        // for middleware enabled
                        continue;
                    }
                    if (!classMiddleware.match && !classMiddleware.ignore) {
                        if (!mw.name) {
                            mw._name = mwName;
                        }
                        // just got fn
                        newMiddlewareArr.push(mw);
                    }
                    else {
                        // wrap ignore and match
                        const match = (0, util_1.pathMatching)({
                            match: classMiddleware.match,
                            ignore: classMiddleware.ignore,
                            thisResolver: classMiddleware,
                        });
                        fn = (ctx, next, options) => {
                            if (!match(ctx))
                                return next();
                            return mw(ctx, next, options);
                        };
                        fn._name = mwName;
                        newMiddlewareArr.push(fn);
                    }
                }
                else {
                    throw new error_1.MidwayCommonError('Middleware must have resolve method!');
                }
            }
            else {
                newMiddlewareArr.push(fn);
            }
        }
        /**
         * @param {Object} context
         * @param next
         * @return {Promise}
         * @api public
         */
        const composeFn = (context, next) => {
            const supportBody = (0, util_1.isIncludeProperty)(context, 'body');
            // last called middleware #
            let index = -1;
            return dispatch(0);
            function dispatch(i) {
                if (i <= index)
                    return Promise.reject(new error_1.MidwayCommonError('next() called multiple times'));
                index = i;
                let fn = newMiddlewareArr[i];
                if (i === newMiddlewareArr.length)
                    fn = next;
                if (!fn)
                    return Promise.resolve();
                const middlewareName = `${name ? `${name}.` : ''}${index} ${fn._name || fn.name || 'anonymous'}`;
                const startTime = Date.now();
                debug(`[middleware]: in ${middlewareName} +0`);
                try {
                    if (supportBody) {
                        return Promise.resolve(fn(context, dispatch.bind(null, i + 1), {
                            index,
                        })).then(result => {
                            /**
                             * 1、return 和 ctx.body，return 的优先级更高
                             * 2、如果 result 有值（非 undefined），则不管什么情况，都会覆盖当前 body，注意，这里有可能赋值 null，导致 status 为 204，会在中间件处进行修正
                             * 3、如果 result 没值，且 ctx.body 已经赋值，则向 result 赋值
                             */
                            if (result !== undefined) {
                                context['body'] = result;
                            }
                            else if (context['body'] !== undefined) {
                                result = context['body'];
                            }
                            debug(`[middleware]: out ${middlewareName} +${Date.now() - startTime} with body`);
                            return result;
                        });
                    }
                    else {
                        return Promise.resolve(fn(context, dispatch.bind(null, i + 1), {
                            index,
                        })).then(result => {
                            debug(`[middleware]: out ${middlewareName} +${Date.now() - startTime}`);
                            return result;
                        });
                    }
                }
                catch (err) {
                    debug(`[middleware]: out ${middlewareName} +${Date.now() - startTime} with err ${err.message}`);
                    return Promise.reject(err);
                }
            }
        };
        if (name) {
            composeFn._name = name;
        }
        return composeFn;
    }
};
MidwayMiddlewareService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayMiddlewareService);
exports.MidwayMiddlewareService = MidwayMiddlewareService;
//# sourceMappingURL=middlewareService.js.map