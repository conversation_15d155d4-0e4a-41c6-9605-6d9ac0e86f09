{"version": 3, "sources": ["chance.min.js"], "names": ["Chance", "seed", "this", "random", "arguments", "length", "i", "seedling", "Object", "prototype", "toString", "call", "j", "hash", "k", "charCodeAt", "mt", "mersenne_twister", "bimd5", "blueimp_md5", "initOptions", "options", "defaults", "testRange", "test", "errorMessage", "RangeError", "diceFn", "range", "natural", "_copyObject", "source", "target", "key", "keys", "o_keys", "l", "_copyArray", "copyObject", "_target", "isArray", "Array", "MAX_INT", "CHARS_LOWER", "CHARS_UPPER", "toUpperCase", "HEX_POOL", "NUMBERS", "slice", "VERSION", "base64", "Error", "btoa", "<PERSON><PERSON><PERSON>", "input", "bool", "likelihood", "animal", "type", "get", "toLowerCase", "pick", "animalTypeArray", "character", "alpha", "symbols", "letters", "pool", "casing", "char<PERSON>t", "max", "floating", "fixed", "precision", "num", "Math", "pow", "min", "num_fixed", "integer", "toFixed", "parseFloat", "floor", "numerals", "hex", "letter", "string", "n", "join", "capitalize", "word", "substr", "mixin", "obj", "func_name", "unique", "fn", "comparator", "arr", "val", "indexOf", "result", "count", "MAX_DUPLICATES", "params", "clonedParams", "JSON", "parse", "stringify", "apply", "push", "pad", "number", "width", "shuffle", "pickone", "pickset", "old_array", "new_array", "Number", "splice", "weighted", "weights", "trim", "sum", "weightIndex", "isNaN", "chosenIdx", "selected", "total", "lastGoodIdx", "chosen", "paragraph", "sentences", "sentence", "text", "words", "punctuation", "syllable", "chr", "consonants", "syllables", "substring", "age", "<PERSON><PERSON><PERSON><PERSON>", "birthday", "currentYear", "Date", "getFullYear", "setFullYear", "year", "date", "cpf", "formatted", "d1", "d2", "replace", "cnpj", "first", "gender", "nationality", "profession", "rank", "company", "extraGenders", "concat", "last", "israelId", "x", "y", "thisDigit", "parseInt", "mrz", "checkDigit", "split", "multipliers", "runningTotal", "for<PERSON>ach", "idx", "pos", "that", "passportNumber", "dob", "getMonth", "getDate", "expiry", "issuer", "opts", "generate", "name", "middle", "middle_initial", "prefix", "suffix", "name_prefixes", "prefixes", "abbreviation", "name_prefix", "full", "HIDN", "idn", "ssn", "dash", "ssnFour", "dashes", "name_suffixes", "name_suffix", "nationalities", "android_id", "apple_token", "wp8_anid2", "wp7_anid", "guid", "bb_pin", "avatar", "protocol", "email", "fileExtension", "size", "fallback", "rating", "constructor", "http", "https", "g", "pg", "r", "404", "mm", "identicon", "monsterid", "wavatar", "retro", "blank", "bmp", "gif", "jpg", "png", "md5", "color", "gray", "value", "delimiter", "rgb", "has<PERSON><PERSON><PERSON>", "rgbValue", "alphaChannel", "min_alpha", "max_alpha", "min_rgb", "max_rgb", "min_green", "max_green", "min_blue", "max_blue", "start", "end", "withHash", "symbol", "hexstring", "isGrayscale", "format", "min_red", "max_red", "undefined", "grayscale", "colorValue", "domain", "tld", "fbid", "google_analytics", "hashtag", "ip", "ipv6", "klout", "semver", "include_prerelease", "prerelease", "rpg", "tlds", "twitter", "url", "extension", "domain_prefix", "path", "extensions", "port", "locale", "region", "locales", "address", "street", "altitude", "areacode", "parens", "city", "coordinates", "latitude", "longitude", "countries", "country", "depth", "geo<PERSON>h", "g<PERSON><PERSON><PERSON>", "phone", "numPick", "self", "ukNum", "parts", "section", "sections", "area", "mobile", "match", "exchange", "subscriber", "postal", "counties", "county", "provinces", "province", "state", "states", "us_states_and_dc", "territories", "armed_forces", "short_suffix", "street_suffix", "street_suffixes", "zip", "plusfour", "ampm", "date_string", "american", "getTime", "m", "month", "raw", "daysInMonth", "days", "numeric", "day", "hour", "twentyfour", "minute", "second", "millisecond", "hammertime", "months", "timestamp", "weekday", "weekdays", "weekday_only", "cc", "to_generate", "cc_type", "luhn_calculate", "cc_types", "types", "short_name", "currency_types", "currency", "timezones", "timezone", "currency_pair", "returnAsString", "currencies", "reduce", "acc", "item", "code", "dollar", "cents", "euro", "toLocaleString", "exp", "exp_year", "exp_month", "future", "month_int", "cur<PERSON><PERSON><PERSON>", "curYear", "vat", "it_vat", "iban", "cf", "name_generator", "isLast", "temp", "return_value", "map", "c", "date_generator", "range1", "range2", "evens", "digit", "checkdigit_generator", "pl_pesel", "controlNumber", "pl_nip", "pl_regon", "note", "notes", "scales", "naturals", "flats", "sharps", "all", "flat<PERSON>ey", "<PERSON><PERSON><PERSON>", "midi_note", "chord_quality", "chord_qualities", "jazz", "chord", "tempo", "coin", "d4", "d6", "d8", "d10", "d12", "d20", "d30", "d100", "thrown", "bits", "rolls", "p", "version", "guid_pool", "luhn_check", "str", "digits", "reverse", "file", "fileName", "fileOptions", "typeRange", "extensionObjectCollection", "fileType", "data", "firstNames", "male", "en", "it", "nl", "female", "lastNames", "uk", "ca", "locale_languages", "locale_regions", "country_regions", "us", "colorNames", "raster", "vector", "3d", "document", "abbr", "offset", "isdst", "utc", "animals", "ocean", "desert", "grassland", "forest", "farm", "pet", "zoo", "o_hasOwnProperty", "hasOwnProperty", "mac_address", "separator", "networkVersion", "normal", "mean", "dev", "normal_pool", "s", "u", "v", "norm", "sqrt", "log", "performanceCounter", "round", "radio", "fl", "side", "set", "values", "tv", "<PERSON><PERSON><PERSON>", "BlueImpMD5", "N", "M", "MATRIX_A", "UPPER_MASK", "LOWER_MASK", "mti", "init_genrand", "init_by_array", "init_key", "key_length", "genrand_int32", "mag01", "kk", "genrand_int31", "genrand_real1", "genrand_real3", "genrand_res53", "safe_add", "lsw", "bit_roll", "cnt", "md5_cmn", "q", "a", "b", "t", "md5_ff", "d", "md5_gg", "md5_hh", "md5_ii", "binl_md5", "len", "olda", "oldb", "oldc", "oldd", "binl2rstr", "output", "String", "fromCharCode", "rstr2binl", "rstr_md5", "rstr_hmac_md5", "bkey", "ipad", "opad", "rstr2hex", "str2rstr_utf8", "unescape", "encodeURIComponent", "raw_md5", "hex_md5", "raw_hmac_md5", "hex_hmac_md5", "exports", "module", "define", "amd", "importScripts", "chance", "window"], "mappings": "CAKA,WAcI,SAASA,EAAQC,GACb,KAAMC,gBAAgBF,GAElB,OADKC,IAAQA,EAAO,MACJ,OAATA,EAAgB,IAAID,EAAW,IAAIA,EAAOC,GAIrD,GAAoB,mBAATA,EAEP,OADAC,KAAKC,OAASF,EACPC,KAGPE,UAAUC,SAEVH,KAAKD,KAAO,GAKhB,IAAK,IAAIK,EAAI,EAAGA,EAAIF,UAAUC,OAAQC,IAAK,CACvC,IAAIC,EAAW,EACf,GAAqD,oBAAjDC,OAAOC,UAAUC,SAASC,KAAKP,UAAUE,IACzC,IAAK,IAAIM,EAAI,EAAGA,EAAIR,UAAUE,GAAGD,OAAQO,IAAK,CAG1C,IAAK,IADDC,EAAO,EACFC,EAAI,EAAGA,EAAIV,UAAUE,GAAGD,OAAQS,IACrCD,EAAOT,UAAUE,GAAGS,WAAWD,IAAMD,GAAQ,IAAMA,GAAQ,IAAMA,EAErEN,GAAYM,OAGhBN,EAAWH,UAAUE,GAEzBJ,KAAKD,OAASG,UAAUC,OAASC,GAAKC,EAU1C,OANAL,KAAKc,GAAKd,KAAKe,iBAAiBf,KAAKD,MACrCC,KAAKgB,MAAQhB,KAAKiB,cAClBjB,KAAKC,OAAS,WACV,OAAOD,KAAKc,GAAGb,OAAOD,KAAKD,OAGxBC,KAMX,SAASkB,EAAYC,EAASC,GAG1B,GAFAD,EAAUA,MAENC,EACA,IAAK,IAAIhB,KAAKgB,OACgB,IAAfD,EAAQf,KACfe,EAAQf,GAAKgB,EAAShB,IAKlC,OAAOe,EAGX,SAASE,EAAUC,EAAMC,GACrB,GAAID,EACA,MAAM,IAAIE,WAAWD,GAgjE7B,SAASE,EAAQC,GACb,OAAO,WACH,OAAO1B,KAAK2B,QAAQD,IA+9I5B,SAASE,EAAYC,EAAQC,GAI3B,IAAK,IAFDC,EADAC,EAAOC,EAAOJ,GAGTzB,EAAI,EAAG8B,EAAIF,EAAK7B,OAAQC,EAAI8B,EAAG9B,IAEtC0B,EADAC,EAAMC,EAAK5B,IACGyB,EAAOE,IAAQD,EAAOC,GAIxC,SAASI,EAAWN,EAAQC,GAC1B,IAAK,IAAI1B,EAAI,EAAG8B,EAAIL,EAAO1B,OAAQC,EAAI8B,EAAG9B,IACxC0B,EAAO1B,GAAKyB,EAAOzB,GAIvB,SAASgC,EAAWP,EAAQQ,GACxB,IAAIC,EAAUC,MAAMD,QAAQT,GACxBC,EAASO,IAAYC,EAAU,IAAIC,MAAMV,EAAO1B,YAQpD,OANImC,EACFH,EAAWN,EAAQC,GAEnBF,EAAYC,EAAQC,GAGfA,EAvnNX,IAAIU,EAAU,iBAGVC,EAAc,6BACdC,EAAcD,EAAYE,cAC1BC,EAAYC,mBAGZC,EAAQP,MAAMhC,UAAUuC,MAiD5BhD,EAAOS,UAAUwC,QAAU,SA0B3B,IAAIC,EAAS,WACT,MAAM,IAAIC,MAAM,iCAKI,mBAATC,KACPF,EAASE,KACgB,mBAAXC,SACdH,EAAS,SAASI,GACd,OAAO,IAAID,OAAOC,GAAO5C,SAAS,YAe9CV,EAAOS,UAAU8C,KAAO,SAAUlC,GAgB9B,OAdAA,EAAUD,EAAYC,GAAUmC,WAAa,KAS7CjC,EACIF,EAAQmC,WAAa,GAAKnC,EAAQmC,WAAa,IAC/C,oDAGmB,IAAhBtD,KAAKC,SAAiBkB,EAAQmC,YAGzCxD,EAAOS,UAAUgD,OAAS,SAAUpC,GAIlC,YAA2B,KAF3BA,EAAUD,EAAYC,IAEJqC,MAEhBnC,GACIrB,KAAKyD,IAAI,WAAWtC,EAAQqC,KAAKE,eAClC,uEAGM1D,KAAK2D,KAAK3D,KAAKyD,IAAI,WAAWtC,EAAQqC,KAAKE,kBAGtDE,iBAAmB,SAAS,SAAS,QAAQ,MAAM,OAAO,MAAM,aACzD5D,KAAK2D,KAAK3D,KAAKyD,IAAI,WAAWzD,KAAK2D,KAAKC,qBAWjD9D,EAAOS,UAAUsD,UAAY,SAAU1C,GAEnCE,GADAF,EAAUD,EAAYC,IAEV2C,OAAS3C,EAAQ4C,QACzB,kDAGJ,IACIC,EAASC,EAoBb,OAjBID,EADmB,UAAnB7C,EAAQ+C,OACEzB,EACgB,UAAnBtB,EAAQ+C,OACLxB,EAEAD,EAAcC,GAIxBuB,EADA9C,EAAQ8C,KACD9C,EAAQ8C,KACR9C,EAAQ2C,MACRE,EACA7C,EAAQ4C,QAfL,eAkBHC,EAhLD,0BAmLEG,OAAOnE,KAAK2B,SAASyC,IAAMH,EAAK9D,OAAS,MAiBzDL,EAAOS,UAAU8D,SAAW,SAAUlD,GAElCE,GADAF,EAAUD,EAAYC,GAAUmD,MAAQ,KAE5BA,OAASnD,EAAQoD,UACzB,oDAGJ,IAAIC,EACAF,EAAQG,KAAKC,IAAI,GAAIvD,EAAQmD,OAE7BF,EAAM5B,EAAU8B,EAChBK,GAAOP,EAEX/C,EACIF,EAAQwD,KAAOxD,EAAQmD,OAASnD,EAAQwD,IAAMA,EAC9C,8EAAgFA,GAEpFtD,EACIF,EAAQiD,KAAOjD,EAAQmD,OAASnD,EAAQiD,IAAMA,EAC9C,6EAA+EA,GAGnFjD,EAAUD,EAAYC,GAAWwD,IAAMA,EAAKP,IAAMA,IAMlD,IAAIQ,IADJJ,EAAMxE,KAAK6E,SAASF,IAAKxD,EAAQwD,IAAML,EAAOF,IAAKjD,EAAQiD,IAAME,KAC1CA,GAAOQ,QAAQ3D,EAAQmD,OAE9C,OAAOS,WAAWH,IActB9E,EAAOS,UAAUsE,QAAU,SAAU1D,GAMjC,OAHAA,EAAUD,EAAYC,GAAUwD,KApPtB,iBAoPoCP,IAAK5B,IACnDnB,EAAUF,EAAQwD,IAAMxD,EAAQiD,IAAK,2CAE9BK,KAAKO,MAAMhF,KAAKC,UAAYkB,EAAQiD,IAAMjD,EAAQwD,IAAM,GAAKxD,EAAQwD,MAchF7E,EAAOS,UAAUoB,QAAU,SAAUR,GAQjC,MANgC,iBADhCA,EAAUD,EAAYC,GAAUwD,IAAK,EAAGP,IAAK5B,KAC1ByC,WACjB5D,EAAUF,EAAQ8D,SAAW,EAAG,6CAChC9D,EAAQwD,IAAMF,KAAKC,IAAI,GAAIvD,EAAQ8D,SAAW,GAC9C9D,EAAQiD,IAAMK,KAAKC,IAAI,GAAIvD,EAAQ8D,UAAY,GAEjD5D,EAAUF,EAAQwD,IAAM,EAAG,yCACpB3E,KAAK6E,QAAQ1D,IAcxBrB,EAAOS,UAAU2E,IAAM,SAAU/D,GAE7BE,GADAF,EAAUD,EAAYC,GAAUwD,IAAK,EAAGP,IAAK5B,EAAS0B,OAAQ,WAC5CS,IAAM,EAAG,yCACjC,IAAIE,EAAU7E,KAAK2B,SAASgD,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,MAC3D,MAAuB,UAAnBjD,EAAQ+C,OACJW,EAAQrE,SAAS,IAAImC,cAEtBkC,EAAQrE,SAAS,KAGtBV,EAAOS,UAAU4E,OAAS,SAAShE,GAC/BA,EAAUD,EAAYC,GAAU+C,OAAQ,UACxC,IACIiB,EAASnF,KAAK6D,WAAWI,KADlB,+BAKX,MAHuB,UAAnB9C,EAAQ+C,SACRiB,EAASA,EAAOxC,eAEbwC,GAUXrF,EAAOS,UAAU6E,OAAS,SAAUjE,GAEhCE,GADAF,EAAUD,EAAYC,GAAWhB,OAAQH,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,QAClDjE,OAAS,EAAG,4CAC9B,IAAIA,EAASgB,EAAQhB,OAGrB,OAFWH,KAAKqF,EAAErF,KAAK6D,UAAW1D,EAAQgB,GAE9BmE,KAAK,KAOrBxF,EAAOS,UAAUgF,WAAa,SAAUC,GACpC,OAAOA,EAAKrB,OAAO,GAAGxB,cAAgB6C,EAAKC,OAAO,IAGtD3F,EAAOS,UAAUmF,MAAQ,SAAUC,GAC/B,IAAK,IAAIC,KAAaD,EAClB7F,EAAOS,UAAUqF,GAAaD,EAAIC,GAEtC,OAAO5F,MAcXF,EAAOS,UAAUsF,OAAS,SAASC,EAAItB,EAAKrD,GACxCE,EACkB,mBAAPyE,EACP,kDAGJ,IAAIC,EAAa,SAASC,EAAKC,GAAO,OAA6B,IAAtBD,EAAIE,QAAQD,IAErD9E,IACA4E,EAAa5E,EAAQ4E,YAAcA,GAKvC,IAFA,IAAyBI,EAArBH,KAAUI,EAAQ,EAAWC,EAAuB,GAAN7B,EAAU8B,EAASxD,EAAMrC,KAAKP,UAAW,GAEpF8F,EAAI7F,OAASqE,GAAK,CACrB,IAAI+B,EAAeC,KAAKC,MAAMD,KAAKE,UAAUJ,IAQ7C,GAPAH,EAASL,EAAGa,MAAM3G,KAAMuG,GACnBR,EAAWC,EAAKG,KACjBH,EAAIY,KAAKT,GAETC,EAAQ,KAGNA,EAAQC,EACV,MAAM,IAAI7E,WAAW,kDAG7B,OAAOwE,GAYXlG,EAAOS,UAAU8E,EAAI,SAASS,EAAIT,GAC9BhE,EACkB,mBAAPyE,EACP,uDAGa,IAANT,IACPA,EAAI,GAER,IAAIjF,EAAIiF,EAAGW,KAAUM,EAASxD,EAAMrC,KAAKP,UAAW,GAKpD,IAFAE,EAAIqE,KAAKL,IAAK,EAAGhE,GAEZ,KAAMA,IAAK,KACZ4F,EAAIY,KAAKd,EAAGa,MAAM3G,KAAMsG,IAG5B,OAAON,GAIXlG,EAAOS,UAAUsG,IAAM,SAAUC,EAAQC,EAAOF,GAK5C,OAHAA,EAAMA,GAAO,KAEbC,GAAkB,IACJ3G,QAAU4G,EAAQD,EAAS,IAAIvE,MAAMwE,EAAQD,EAAO3G,OAAS,GAAGmF,KAAKuB,GAAOC,GAI9FhH,EAAOS,UAAUoD,KAAO,SAAUqC,EAAKI,GACnC,GAAmB,IAAfJ,EAAI7F,OACJ,MAAM,IAAIqB,WAAW,6CAEzB,OAAK4E,GAAmB,IAAVA,EAGHpG,KAAKgH,QAAQhB,GAAKlD,MAAM,EAAGsD,GAF3BJ,EAAIhG,KAAK2B,SAASyC,IAAK4B,EAAI7F,OAAS,MAOnDL,EAAOS,UAAU0G,QAAU,SAAUjB,GACjC,GAAmB,IAAfA,EAAI7F,OACN,MAAM,IAAIqB,WAAW,gDAEvB,OAAOwE,EAAIhG,KAAK2B,SAASyC,IAAK4B,EAAI7F,OAAS,MAI/CL,EAAOS,UAAU2G,QAAU,SAAUlB,EAAKI,GACtC,GAAc,IAAVA,EACA,SAEJ,GAAmB,IAAfJ,EAAI7F,OACJ,MAAM,IAAIqB,WAAW,gDAEzB,GAAI4E,EAAQ,EACR,MAAM,IAAI5E,WAAW,2CAEzB,OAAK4E,GAAmB,IAAVA,EAGHpG,KAAKgH,QAAQhB,GAAKlD,MAAM,EAAGsD,IAFzBpG,KAAKiH,QAAQjB,KAM9BlG,EAAOS,UAAUyG,QAAU,SAAUhB,GAMjC,IAAK,IALDmB,EAAYnB,EAAIlD,MAAM,GACtBsE,KACA1G,EAAI,EACJP,EAASkH,OAAOF,EAAUhH,QAErBC,EAAI,EAAGA,EAAID,EAAQC,IAExBM,EAAIV,KAAK2B,SAASyC,IAAK+C,EAAUhH,OAAS,IAE1CiH,EAAUhH,GAAK+G,EAAUzG,GAEzByG,EAAUG,OAAO5G,EAAG,GAGxB,OAAO0G,GAIXtH,EAAOS,UAAUgH,SAAW,SAAUvB,EAAKwB,EAASC,GAChD,GAAIzB,EAAI7F,SAAWqH,EAAQrH,OACvB,MAAM,IAAIqB,WAAW,kDAMzB,IAAK,IADDyE,EADAyB,EAAM,EAEDC,EAAc,EAAGA,EAAcH,EAAQrH,SAAUwH,EAAa,CAEnE,GADA1B,EAAMuB,EAAQG,GACVC,MAAM3B,GACN,MAAM,IAAIzE,WAAW,uCAGrByE,EAAM,IACNyB,GAAOzB,GAIf,GAAY,IAARyB,EACA,MAAM,IAAIlG,WAAW,6CAIzB,IAKIqG,EALAC,EAAW9H,KAAKC,SAAWyH,EAG3BK,EAAQ,EACRC,GAAe,EAEnB,IAAKL,EAAc,EAAGA,EAAcH,EAAQrH,SAAUwH,EAAa,CAG/D,GAFA1B,EAAMuB,EAAQG,GACdI,GAAS9B,EACLA,EAAM,EAAG,CACT,GAAI6B,GAAYC,EAAO,CACnBF,EAAYF,EACZ,MAEJK,EAAcL,EAIdA,IAAiBH,EAAQrH,OAAS,IAClC0H,EAAYG,GAIpB,IAAIC,EAASjC,EAAI6B,GAOjB,OANAJ,OAAwB,IAATA,GAAgCA,KAE3CzB,EAAIsB,OAAOO,EAAW,GACtBL,EAAQF,OAAOO,EAAW,IAGvBI,GAOXnI,EAAOS,UAAU2H,UAAY,SAAU/G,GAGnC,IAAIgH,GAFJhH,EAAUD,EAAYC,IAEEgH,WAAanI,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAGhE,OAFqBpE,KAAKqF,EAAErF,KAAKoI,SAAUD,GAErB7C,KAAK,MAK/BxF,EAAOS,UAAU6H,SAAW,SAAUjH,GAGlC,IAEIkH,EAFAC,GAFJnH,EAAUD,EAAYC,IAEFmH,OAAStI,KAAK2B,SAASgD,IAAK,GAAIP,IAAK,KACrDmE,EAAcpH,EAAQoH,YAkB1B,OAfAF,EAFuBrI,KAAKqF,EAAErF,KAAKwF,KAAM8C,GAEvBhD,KAAK,KAGvB+C,EAAOrI,KAAKuF,WAAW8C,IAGH,IAAhBE,GAA0B,cAAcjH,KAAKiH,KAC7CA,EAAc,KAIdA,IACAF,GAAQE,GAGLF,GAGXvI,EAAOS,UAAUiI,SAAW,SAAUrH,GAYlC,IAAK,IAJDsH,EALAtI,GAFJgB,EAAUD,EAAYC,IAEDhB,QAAUH,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAItDiE,EAAO,GAKFjI,EAAI,EAAGA,EAAID,EAAQC,IAYxBiI,GATII,EAFM,IAANrI,EAEMJ,KAAK6D,WAAWI,KATpByE,6BAUkC,IAZ3B,qBAYSxC,QAAQuC,GAEpBzI,KAAK6D,WAAWI,KAdb,uBAiBHjE,KAAK6D,WAAWI,KAhBjB,UA0Bb,OAJI9C,EAAQoE,aACR8C,EAAOrI,KAAKuF,WAAW8C,IAGpBA,GAGXvI,EAAOS,UAAUiF,KAAO,SAAUrE,GAG9BE,GAFAF,EAAUD,EAAYC,IAGVwH,WAAaxH,EAAQhB,OAC7B,qDAGJ,IAAIwI,EAAYxH,EAAQwH,WAAa3I,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAC5DiE,EAAO,GAEX,GAAIlH,EAAQhB,OAAQ,CAEhB,GACIkI,GAAQrI,KAAKwI,iBACRH,EAAKlI,OAASgB,EAAQhB,QAC/BkI,EAAOA,EAAKO,UAAU,EAAGzH,EAAQhB,aAGjC,IAAK,IAAIC,EAAI,EAAGA,EAAIuI,EAAWvI,IAC3BiI,GAAQrI,KAAKwI,WAQrB,OAJIrH,EAAQoE,aACR8C,EAAOrI,KAAKuF,WAAW8C,IAGpBA,GAOXvI,EAAOS,UAAUsI,IAAM,SAAU1H,GAE7B,IAAI2H,EAEJ,QAHA3H,EAAUD,EAAYC,IAGNqC,MACZ,IAAK,QACDsF,GAAYnE,IAAK,EAAGP,IAAK,IACzB,MACJ,IAAK,OACD0E,GAAYnE,IAAK,GAAIP,IAAK,IAC1B,MACJ,IAAK,QACD0E,GAAYnE,IAAK,GAAIP,IAAK,IAC1B,MACJ,IAAK,SACD0E,GAAYnE,IAAK,GAAIP,IAAK,KAC1B,MACJ,IAAK,MACD0E,GAAYnE,IAAK,EAAGP,IAAK,KACzB,MACJ,QACI0E,GAAYnE,IAAK,GAAIP,IAAK,IAIlC,OAAOpE,KAAK2B,QAAQmH,IAGxBhJ,EAAOS,UAAUwI,SAAW,SAAU5H,GAClC,IAAI0H,EAAM7I,KAAK6I,IAAI1H,GACf6H,GAAc,IAAIC,MAAOC,cAE7B,GAAI/H,GAAWA,EAAQqC,KAAM,CACzB,IAAImB,EAAM,IAAIsE,KACV7E,EAAM,IAAI6E,KACdtE,EAAIwE,YAAYH,EAAcH,EAAM,GACpCzE,EAAI+E,YAAYH,EAAcH,GAE9B1H,EAAUD,EAAYC,GAClBwD,IAAKA,EACLP,IAAKA,SAGTjD,EAAUD,EAAYC,GAClBiI,KAAMJ,EAAcH,IAI5B,OAAO7I,KAAKqJ,KAAKlI,IAIrBrB,EAAOS,UAAU+I,IAAM,SAAUnI,GAC7BA,EAAUD,EAAYC,GAClBoI,WAAW,IAGf,IAAIlE,EAAIrF,KAAKqF,EAAErF,KAAK2B,QAAS,GAAKyC,IAAK,IACnCoF,EAAU,EAALnE,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,GAALA,EAAE,IACnEmE,EAAK,GAAMA,EAAK,KACR,KACJA,EAAK,GAET,IAAIC,EAAQ,EAAHD,EAAU,EAALnE,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,GAALA,EAAE,GAAW,GAALA,EAAE,IACzEoE,EAAK,GAAMA,EAAK,KACR,KACJA,EAAK,GAET,IAAIH,EAAM,GAAGjE,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAImE,EAAGC,EACzE,OAAOtI,EAAQoI,UAAYD,EAAMA,EAAII,QAAQ,MAAM,KAIvD5J,EAAOS,UAAUoJ,KAAO,SAAUxI,GAC9BA,EAAUD,EAAYC,GAClBoI,WAAW,IAGf,IAAIlE,EAAIrF,KAAKqF,EAAErF,KAAK2B,QAAS,IAAMyC,IAAK,KACpCoF,EAAW,EAANnE,EAAE,IAAY,EAANA,EAAE,IAAW,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,IAC1FmE,EAAK,GAAMA,EAAK,IACT,IACHA,EAAK,GAET,IAAIC,EAAQ,EAAHD,EAAW,EAANnE,EAAE,IAAY,EAANA,EAAE,IAAW,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,IAC/FoE,EAAK,GAAMA,EAAK,IACT,IACHA,EAAK,GAET,IAAIE,EAAO,GAAGtE,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,IAAIA,EAAE,IAAI,IAAImE,EAAGC,EAC/F,OAAOtI,EAAQoI,UAAYI,EAAOA,EAAKD,QAAQ,MAAM,KAGzD5J,EAAOS,UAAUqJ,MAAQ,SAAUzI,GAE/B,OADAA,EAAUD,EAAYC,GAAU0I,OAAQ7J,KAAK6J,SAAUC,YAAa,OAC7D9J,KAAK2D,KAAK3D,KAAKyD,IAAI,cAActC,EAAQ0I,OAAOnG,eAAevC,EAAQ2I,YAAYpG,iBAG9F5D,EAAOS,UAAUwJ,WAAa,SAAU5I,GAEpC,OADAA,EAAUD,EAAYC,IACX6I,KACAhK,KAAK2D,MAAM,cAAe,UAAW,UAAW,UAAY3D,KAAK2D,KAAK3D,KAAKyD,IAAI,eAE/EzD,KAAK2D,KAAK3D,KAAKyD,IAAI,gBAIlC3D,EAAOS,UAAU0J,QAAU,WACvB,OAAOjK,KAAK2D,KAAK3D,KAAKyD,IAAI,aAG9B3D,EAAOS,UAAUsJ,OAAS,SAAU1I,GAEhC,OADAA,EAAUD,EAAYC,GAAU+I,kBACzBlK,KAAK2D,MAAM,OAAQ,UAAUwG,OAAOhJ,EAAQ+I,gBAGvDpK,EAAOS,UAAU6J,KAAO,SAAUjJ,GAE9B,OADAA,EAAUD,EAAYC,GAAU2I,YAAa,OACtC9J,KAAK2D,KAAK3D,KAAKyD,IAAI,aAAatC,EAAQ2I,YAAYpG,iBAG/D5D,EAAOS,UAAU8J,SAAS,WAGtB,IAAK,IAFDC,EAAEtK,KAAKoF,QAAQnB,KAAM,aAAa9D,OAAO,IACzCoK,EAAE,EACGnK,EAAE,EAAEA,EAAEkK,EAAEnK,OAAOC,IAAI,CACxB,IAAIoK,EAAYF,EAAElK,IAAOA,EAAE,IAAIqK,SAASrK,EAAE,GAAK,EAAI,GACnDoK,EAAUxK,KAAK6G,IAAI2D,EAAU,GAAGhK,WAEhC+J,GADAC,EAAUC,SAASD,EAAU,IAAMC,SAASD,EAAU,IAI1D,OADAF,IAAK,GAAGG,SAASF,EAAE/J,WAAWsC,OAAO,KAAKtC,WAAWsC,OAAO,IAIhEhD,EAAOS,UAAUmK,IAAM,SAAUvJ,GAC7B,IAAIwJ,EAAa,SAAUvH,GACvB,IAAIU,EAAQ,+BAA+B8G,MAAM,IAC7CC,GAAgB,EAAG,EAAG,GACtBC,EAAe,EAiBnB,MAfqB,iBAAV1H,IACPA,EAAQA,EAAM5C,YAGlB4C,EAAMwH,MAAM,IAAIG,QAAQ,SAASlH,EAAWmH,GACxC,IAAIC,EAAMnH,EAAMoC,QAAQrC,GAGpBA,GADQ,IAAToH,EACqB,IAARA,EAAY,EAAIA,EAAM,EAEtBR,SAAS5G,EAAW,IAGpCiH,GADAjH,GAAagH,EAAYG,EAAMH,EAAY1K,UAGxC2K,EAAe,IA6BtBI,EAAOlL,KAsBX,OApBAmB,EAAUD,EAAYC,GAClByI,MAAO5J,KAAK4J,QACZQ,KAAMpK,KAAKoK,OACXe,eAAgBnL,KAAK6E,SAASF,IAAK,IAAWP,IAAK,YACnDgH,IAAM,WACF,IAAI/B,EAAO6B,EAAKnC,UAAUvF,KAAM,UAChC,OAAQ6F,EAAKH,cAAc1I,WAAWiF,OAAO,GACrCyF,EAAKrE,IAAIwC,EAAKgC,WAAa,EAAG,GAC9BH,EAAKrE,IAAIwC,EAAKiC,UAAW,IAAIhG,KAAK,IAJzC,GAMLiG,OAAS,WACL,IAAIlC,EAAO,IAAIJ,KACf,QAASI,EAAKH,cAAgB,GAAG1I,WAAWiF,OAAO,GAC3CyF,EAAKrE,IAAIwC,EAAKgC,WAAa,EAAG,GAC9BH,EAAKrE,IAAIwC,EAAKiC,UAAW,IAAIhG,KAAK,IAJtC,GAMRuE,OAA0B,WAAlB7J,KAAK6J,SAAwB,IAAK,IAC1C2B,OAAQ,MACR1B,YAAa,QA/CF,SAAU2B,GACrB,IAAI5E,EAAM,SAAU1G,GAChB,OAAO,IAAIoC,MAAMpC,EAAS,GAAGmF,KAAK,MAElCwB,GAAW,KACA2E,EAAKD,OACLC,EAAKrB,KAAKzH,cACV,KACA8I,EAAK7B,MAAMjH,cACXkE,EAAI,IAAM4E,EAAKrB,KAAKjK,OAASsL,EAAK7B,MAAMzJ,OAAS,IACjDsL,EAAKN,eACLR,EAAWc,EAAKN,gBAChBM,EAAK3B,YACL2B,EAAKL,IACLT,EAAWc,EAAKL,KAChBK,EAAK5B,OACL4B,EAAKF,OACLZ,EAAWc,EAAKF,QAChB1E,EAAI,IACJ8D,EAAW9D,EAAI,MAAOvB,KAAK,IAE1C,OAAOwB,EACF6D,EAAW7D,EAAOrB,OAAO,GAAI,IAClBqB,EAAOrB,OAAO,GAAI,GAClBqB,EAAOrB,OAAO,GAAI,IAyB/BiG,CAAUvK,IAGrBrB,EAAOS,UAAUoL,KAAO,SAAUxK,GAC9BA,EAAUD,EAAYC,GAEtB,IAEIwK,EAFA/B,EAAQ5J,KAAK4J,MAAMzI,GACnBiJ,EAAOpK,KAAKoK,KAAKjJ,GAmBrB,OAfIwK,EADAxK,EAAQyK,OACDhC,EAAQ,IAAM5J,KAAK4J,MAAMzI,GAAW,IAAMiJ,EAC1CjJ,EAAQ0K,eACRjC,EAAQ,IAAM5J,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,UAAY,KAAOkG,EAEtER,EAAQ,IAAMQ,EAGrBjJ,EAAQ2K,SACRH,EAAO3L,KAAK8L,OAAO3K,GAAW,IAAMwK,GAGpCxK,EAAQ4K,SACRJ,EAAOA,EAAO,IAAM3L,KAAK+L,OAAO5K,IAG7BwK,GAKX7L,EAAOS,UAAUyL,cAAgB,SAAUnC,GAIvC,IAAIoC,IACEN,KAAM,SAAUO,aAAc,QAYpC,MATe,UANfrC,GADAA,EAASA,GAAU,OACHnG,gBAMoB,QAAXmG,GACrBoC,EAASrF,MAAO+E,KAAM,SAAUO,aAAc,QAGnC,WAAXrC,GAAkC,QAAXA,IACvBoC,EAASrF,MAAO+E,KAAM,OAAQO,aAAc,SAC5CD,EAASrF,MAAO+E,KAAM,SAAUO,aAAc,UAG3CD,GAIXnM,EAAOS,UAAUuL,OAAS,SAAU3K,GAChC,OAAOnB,KAAKmM,YAAYhL,IAG5BrB,EAAOS,UAAU4L,YAAc,SAAUhL,GAErC,OADAA,EAAUD,EAAYC,GAAW0I,OAAQ,SAC1BuC,KACXpM,KAAK2D,KAAK3D,KAAKgM,cAAc7K,EAAQ0I,SAAS8B,KAC9C3L,KAAK2D,KAAK3D,KAAKgM,cAAc7K,EAAQ0I,SAASqC,cAGtDpM,EAAOS,UAAU8L,KAAM,WAErB,IAEIC,EAAI,GAGN,OAFAA,GAAKtM,KAAKoF,QAAQnB,KAHP,aAGqB9D,OAAO,IACvCmM,GAAKtM,KAAKoF,QAAQnB,KAHP,8BAGqB9D,OAAO,KAK3CL,EAAOS,UAAUgM,IAAM,SAAUpL,GAE7B,IAEIqL,GAHJrL,EAAUD,EAAYC,GAAUsL,SAAS,EAAOC,QAAQ,KAGrCA,OAAS,IAAM,GASlC,OAPIvL,EAAQsL,QAKFzM,KAAKoF,QAAQnB,KATR,aASwB9D,OAAQ,IAJrCH,KAAKoF,QAAQnB,KALR,aAKwB9D,OAAQ,IAAMqM,EACjDxM,KAAKoF,QAAQnB,KANF,aAMkB9D,OAAQ,IAAMqM,EAC3CxM,KAAKoF,QAAQnB,KAPF,aAOkB9D,OAAQ,KAS7CL,EAAOS,UAAUoM,cAAgB,WAiB7B,QAfMhB,KAAM,iCAAkCO,aAAc,SACtDP,KAAM,uBAAwBO,aAAc,UAC5CP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,eAAgBO,aAAc,SACpCP,KAAM,iBAAkBO,aAAc,SACtCP,KAAM,oCAAqCO,aAAc,WACzDP,KAAM,oBAAqBO,aAAc,SACzCP,KAAM,iBAAkBO,aAAc,SACtCP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,YAAaO,aAAc,QACjCP,KAAM,aAAcO,aAAc,OAClCP,KAAM,0BAA2BO,aAAc,QAC/CP,KAAM,yBAA0BO,aAAc,YAMxDpM,EAAOS,UAAUwL,OAAS,SAAU5K,GAChC,OAAOnB,KAAK4M,YAAYzL,IAG5BrB,EAAOS,UAAUqM,YAAc,SAAUzL,GAErC,OADAA,EAAUD,EAAYC,IACPiL,KACXpM,KAAK2D,KAAK3D,KAAK2M,iBAAiBhB,KAChC3L,KAAK2D,KAAK3D,KAAK2M,iBAAiBT,cAGxCpM,EAAOS,UAAUsM,cAAgB,WAC7B,OAAO7M,KAAKyD,IAAI,kBAIpB3D,EAAOS,UAAUuJ,YAAc,WAE3B,OADkB9J,KAAK2D,KAAK3D,KAAK6M,iBACdlB,MAOvB7L,EAAOS,UAAUuM,WAAa,WAC1B,MAAO,QAAU9M,KAAKoF,QAASnB,KAAM,kEAAmE9D,OAAQ,OAIpHL,EAAOS,UAAUwM,YAAc,WAC3B,OAAO/M,KAAKoF,QAASnB,KAAM,mBAAoB9D,OAAQ,MAI3DL,EAAOS,UAAUyM,UAAY,WACzB,OAAOhK,EAAQhD,KAAKW,MAAQR,OAAS,OAIzCL,EAAOS,UAAU0M,SAAW,WACxB,MAAO,KAAOjN,KAAKkN,OAAOxD,QAAQ,KAAM,IAAI/G,cAAgB,MAAQ3C,KAAKW,MAAOR,OAAO,IAAO,MAAQH,KAAK6E,SAAUF,IAAI,EAAGP,IAAI,KAIpItE,EAAOS,UAAU4M,OAAS,WACtB,OAAOnN,KAAKW,MAAOR,OAAQ,KAM/BL,EAAOS,UAAU6M,OAAS,SAAUjM,GAChC,IA2BIsK,GACA4B,SAAU,KACVC,MAAO,KACPC,cAAe,KACfC,KAAM,KACNC,SAAU,KACVC,OAAQ,MAGZ,GAAKvM,EAKA,GAAuB,iBAAZA,EACZsK,EAAK6B,MAAQnM,EACbA,SAEC,CAAA,GAAuB,iBAAZA,EACZ,OAAO,KAEN,GAA4B,UAAxBA,EAAQwM,YACb,OAAO,UAXPlC,EAAK6B,MAAQtN,KAAKsN,QAClBnM,KAsCJ,OAzBAsK,EAAOvK,EAAYC,EAASsK,IAElB6B,QAEN7B,EAAK6B,MAAQtN,KAAKsN,SAItB7B,EAAK4B,UAzDDO,KAAM,OACNC,MAAO,SAwDepC,EAAK4B,UAAY5B,EAAK4B,SAAW,IAAM,GACjE5B,EAAK+B,KAAO/C,SAASgB,EAAK+B,KAAM,GAAK/B,EAAK+B,KAAO,GACjD/B,EAAKiC,QAxCDI,EAAG,IACHC,GAAI,KACJC,EAAG,IACH1D,EAAG,KAqCemB,EAAKiC,QAAUjC,EAAKiC,OAAS,GACnDjC,EAAKgC,UAlDDQ,IAAO,MACPC,GAAI,KACJC,UAAW,YACXC,UAAW,YACXC,QAAS,UACTC,MAAO,QACPC,MAAO,SA4Ce9C,EAAKgC,UAAYhC,EAAKgC,SAAW,GAC3DhC,EAAK8B,eAzDDiB,IAAK,MACLC,IAAK,MACLC,IAAK,MACLC,IAAK,OAsDuBlD,EAAK8B,eAAiB9B,EAAK8B,cAAgB,GAGvE9B,EAAK4B,SAlEM,6BAoEXrN,KAAKgB,MAAM4N,IAAInD,EAAK6B,QACnB7B,EAAK8B,cAAgB,IAAM9B,EAAK8B,cAAgB,KAChD9B,EAAK+B,MAAQ/B,EAAKiC,QAAUjC,EAAKgC,SAAW,IAAM,KAClDhC,EAAK+B,KAAO,MAAQ/B,EAAK+B,KAAKhN,WAAa,KAC3CiL,EAAKiC,OAAS,MAAQjC,EAAKiC,OAAS,KACpCjC,EAAKgC,SAAW,MAAQhC,EAAKgC,SAAW,KA6CjD3N,EAAOS,UAAUsO,MAAQ,SAAU1N,GAC/B,SAAS2N,EAAKC,EAAOC,GACjB,OAAQD,EAAOA,EAAOA,GAAOzJ,KAAK0J,GAAa,IAGnD,SAASC,EAAIC,GACT,IAAIC,EAAe,EAAgB,OAAS,MACxCC,EAAe,EAAiB,IAAMpP,KAAKqE,UAAUM,IAAI0K,EAAWjL,IAAIkL,IAAe,GAE3F,OAAOH,EAAW,KADC,EAAiBL,EAAK9O,KAAK2B,SAASgD,IAAK4K,EAASnL,IAAKoL,IAAW,KAASxP,KAAK2B,SAASgD,IAAK8K,EAAWrL,IAAKsL,IAAc,IAAM1P,KAAK2B,SAASgD,IAAKgL,EAAUvL,IAAKwL,IAAa,IAAM5P,KAAK2B,SAASyC,IAAK,OACxLgL,EAAe,IAGxD,SAASlK,EAAI2K,EAAOC,EAAKC,GACrB,IAAIC,EAAS,EAAa,IAAM,GAC5BC,EAAY,GAoBhB,OAlBIC,GACAD,EAAYnB,EAAK9O,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAK4K,EAASnL,IAAKoL,IAAW,IAC3C,aAAnBrO,EAAQgP,SACRF,EAAYnB,EAAK9O,KAAKkF,KAAKP,IAAK,EAAGP,IAAK,QAKxC6L,EADmB,aAAnB9O,EAAQgP,OACInQ,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAKF,KAAKO,MAAMoL,EAAU,IAAKhM,IAAKK,KAAKO,MAAMqL,EAAU,MAAO,GAAKrQ,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAKF,KAAKO,MAAMyK,EAAY,IAAKrL,IAAKK,KAAKO,MAAM0K,EAAY,MAAO,GAAK1P,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAKF,KAAKO,MAAM2K,EAAW,IAAKvL,IAAKK,KAAKO,MAAM4K,EAAW,MAAO,QAEpQU,IAAZF,QAAqCE,IAAZD,QAAuCC,IAAdb,QAAyCa,IAAdZ,QAAwCY,IAAbX,QAAuCW,IAAbV,EAC3H5P,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAKyL,EAAShM,IAAKiM,IAAW,GAAKrQ,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAK8K,EAAWrL,IAAKsL,IAAa,GAAK1P,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAKgL,EAAUvL,IAAKwL,IAAY,GAGnK5P,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAK4K,EAASnL,IAAKoL,IAAW,GAAKxP,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAK4K,EAASnL,IAAKoL,IAAW,GAAKxP,KAAK6G,IAAI7G,KAAKkF,KAAKP,IAAK4K,EAASnL,IAAKoL,IAAW,GAI1KQ,EAASC,EAmBpB,IAAIC,GAhBJ/O,EAAUD,EAAYC,GAClBgP,OAAQnQ,KAAK2D,MAAM,MAAO,WAAY,MAAO,OAAQ,KAAM,SAC3D4M,WAAW,EACXrM,OAAQ,QACRS,IAAK,EACLP,IAAK,IACLgM,aAASE,EACTD,aAASC,EACTb,eAAWa,EACXZ,eAAWY,EACXX,cAAUW,EACVV,cAAUU,EACVjB,UAAW,EACXC,UAAW,KAGWiB,UACtBhB,EAAUpO,EAAQwD,IAClB6K,EAAUrO,EAAQiD,IAClBgM,EAAUjP,EAAQiP,QAClBC,EAAUlP,EAAQkP,QAClBZ,EAAYtO,EAAQsO,UACpBC,EAAYvO,EAAQuO,UACpBC,EAAWxO,EAAQwO,SACnBC,EAAWzO,EAAQyO,SACnBP,EAAYlO,EAAQkO,UACpBC,EAAYnO,EAAQmO,eACAgB,IAApBnP,EAAQiP,UAAyBA,EAAUb,QACvBe,IAApBnP,EAAQkP,UAAyBA,EAAUb,QACrBc,IAAtBnP,EAAQsO,YAA2BA,EAAYF,QACzBe,IAAtBnP,EAAQuO,YAA2BA,EAAYF,QAC1Bc,IAArBnP,EAAQwO,WAA0BA,EAAWJ,QACxBe,IAArBnP,EAAQyO,WAA0BA,EAAWJ,QACvBc,IAAtBnP,EAAQkO,YAA2BA,EAAY,QACzBiB,IAAtBnP,EAAQmO,YAA2BA,EAAY,GAC/CY,GAA2B,IAAZX,GAA6B,MAAZC,QAA+Bc,IAAZF,QAAqCE,IAAZD,IAC5Ed,GAAYa,EAAUX,EAAYE,GAAY,EAC9CH,GAAYa,EAAUX,EAAYE,GAAY,GAElD,IAAIY,EAEJ,GAAuB,QAAnBrP,EAAQgP,OACRK,EAAatL,EAAIzE,KAAKT,KAAM,EAAG,GAAG,QAEjC,GAAuB,aAAnBmB,EAAQgP,OACbK,EAAatL,EAAIzE,KAAKT,KAAM,EAAG,GAAG,QAEjC,GAAuB,QAAnBmB,EAAQgP,OACbK,EAAavB,EAAIxO,KAAKT,MAAM,QAE3B,GAAuB,SAAnBmB,EAAQgP,OACbK,EAAavB,EAAIxO,KAAKT,MAAM,OAE3B,CAAA,GAAuB,OAAnBmB,EAAQgP,OAGZ,CAAA,GAAsB,SAAnBhP,EAAQgP,OACZ,OAAOnQ,KAAK2D,KAAK3D,KAAKyD,IAAI,eAG1B,MAAM,IAAIjC,WAAW,oGANrBgP,EAAa,KAAOtL,EAAIzE,KAAKT,KAAM,EAAG,GAa1C,MAJuB,UAAnBmB,EAAQ+C,SACRsM,EAAaA,EAAW7N,eAGrB6N,GAGX1Q,EAAOS,UAAUkQ,OAAS,SAAUtP,GAEhC,OADAA,EAAUD,EAAYC,GACfnB,KAAKwF,OAAS,KAAOrE,EAAQuP,KAAO1Q,KAAK0Q,QAGpD5Q,EAAOS,UAAU+M,MAAQ,SAAUnM,GAE/B,OADAA,EAAUD,EAAYC,GACfnB,KAAKwF,MAAMrF,OAAQgB,EAAQhB,SAAW,KAAOgB,EAAQsP,QAAUzQ,KAAKyQ,WAoB/E3Q,EAAOS,UAAUoQ,KAAO,WACpB,MAAO,QAAU3Q,KAAKoF,QAAQnB,KAAM,aAAc9D,OAAQ,MAG9DL,EAAOS,UAAUqQ,iBAAmB,WAIhC,MAAO,MAHO5Q,KAAK6G,IAAI7G,KAAK2B,SAASyC,IAAK,SAAU,GAG3B,IAFVpE,KAAK6G,IAAI7G,KAAK2B,SAASyC,IAAK,KAAM,IAKrDtE,EAAOS,UAAUsQ,QAAU,WACvB,MAAO,IAAM7Q,KAAKwF,QAGtB1F,EAAOS,UAAUuQ,GAAK,WAGlB,OAAO9Q,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,MAAQ,IACnCpE,KAAK2B,SAASyC,IAAK,MAAQ,IAC3BpE,KAAK2B,SAASyC,IAAK,MAAQ,IAC3BpE,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,OAGtCtE,EAAOS,UAAUwQ,KAAO,WAGpB,OAFc/Q,KAAKqF,EAAErF,KAAKW,KAAM,GAAIR,OAAQ,IAE7BmF,KAAK,MAGxBxF,EAAOS,UAAUyQ,MAAQ,WACrB,OAAOhR,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,MAGtCtE,EAAOS,UAAU0Q,OAAS,SAAU9P,GAChCA,EAAUD,EAAYC,GAAW+P,oBAAoB,IAErD,IAAIxP,EAAQ1B,KAAKiH,SAAS,IAAK,IAAK,IAAK,IAAK,KAAM,KAAM,MACtD9F,EAAQO,QACRA,EAAQP,EAAQO,OAGpB,IAAIyP,EAAa,GAIjB,OAHIhQ,EAAQ+P,qBACRC,EAAanR,KAAKuH,UAAU,GAAI,OAAQ,QAAS,WAAY,GAAI,GAAI,EAAG,KAErE7F,EAAQ1B,KAAKoR,IAAI,QAAQ9L,KAAK,KAAO6L,GAGhDrR,EAAOS,UAAU8Q,KAAO,WACpB,OAAQ,MAAO,MAAO,MAAO,MAAO,QAAS,MAAO,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAGhiDvR,EAAOS,UAAUmQ,IAAM,WACnB,OAAO1Q,KAAK2D,KAAK3D,KAAKqR,SAG1BvR,EAAOS,UAAU+Q,QAAU,WACvB,MAAO,IAAMtR,KAAKwF,QAGtB1F,EAAOS,UAAUgR,IAAM,SAAUpQ,GAG7B,IAAIqQ,GAFJrQ,EAAUD,EAAYC,GAAWkM,SAAU,OAAQoD,OAAQzQ,KAAKyQ,OAAOtP,GAAUsQ,cAAe,GAAIC,KAAM1R,KAAKwF,OAAQmM,iBAE/FA,WAAWxR,OAAS,EAAI,IAAMH,KAAK2D,KAAKxC,EAAQwQ,YAAc,GAClFlB,EAAStP,EAAQsQ,cAAgBtQ,EAAQsQ,cAAgB,IAAMtQ,EAAQsP,OAAStP,EAAQsP,OAE5F,OAAOtP,EAAQkM,SAAW,MAAQoD,EAAS,IAAMtP,EAAQuQ,KAAOF,GAGpE1R,EAAOS,UAAUqR,KAAO,WACpB,OAAO5R,KAAK6E,SAASF,IAAK,EAAGP,IAAK,SAGtCtE,EAAOS,UAAUsR,OAAS,SAAU1Q,GAEhC,OADAA,EAAUD,EAAYC,IACV2Q,OACH9R,KAAK2D,KAAK3D,KAAKyD,IAAI,mBAEnBzD,KAAK2D,KAAK3D,KAAKyD,IAAI,sBAIhC3D,EAAOS,UAAUwR,QAAU,SAAU5Q,GAEnC,OADAA,EAAUD,EAAYC,IACV2Q,OACH9R,KAAKyD,IAAI,kBAETzD,KAAKyD,IAAI,qBAQpB3D,EAAOS,UAAUyR,QAAU,SAAU7Q,GAEjC,OADAA,EAAUD,EAAYC,GACfnB,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,MAAS,IAAMpE,KAAKiS,OAAO9Q,IAGjErB,EAAOS,UAAU2R,SAAW,SAAU/Q,GAElC,OADAA,EAAUD,EAAYC,GAAUmD,MAAO,EAAGK,IAAK,EAAGP,IAAK,OAChDpE,KAAKqE,UACRM,IAAKxD,EAAQwD,IACbP,IAAKjD,EAAQiD,IACbE,MAAOnD,EAAQmD,SAIvBxE,EAAOS,UAAU4R,SAAW,SAAUhR,GAClCA,EAAUD,EAAYC,GAAUiR,QAAS,IAEzC,IAAID,EAAWnS,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAAI5D,WACtCR,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAAI5D,WAC/BR,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,IAAI5D,WAEvC,OAAOW,EAAQiR,OAAS,IAAMD,EAAW,IAAMA,GAGnDrS,EAAOS,UAAU8R,KAAO,WACpB,OAAOrS,KAAKuF,WAAWvF,KAAKwF,MAAMmD,UAAW,MAGjD7I,EAAOS,UAAU+R,YAAc,SAAUnR,GACrC,OAAOnB,KAAKuS,SAASpR,GAAW,KAAOnB,KAAKwS,UAAUrR,IAG1DrB,EAAOS,UAAUkS,UAAY,WACzB,OAAOzS,KAAKyD,IAAI,cAGpB3D,EAAOS,UAAUmS,QAAU,SAAUvR,GACjCA,EAAUD,EAAYC,GACtB,IAAIuR,EAAU1S,KAAK2D,KAAK3D,KAAKyS,aAC7B,OAAOtR,EAAQiL,KAAOsG,EAAQ/G,KAAO+G,EAAQxG,cAGjDpM,EAAOS,UAAUoS,MAAQ,SAAUxR,GAE/B,OADAA,EAAUD,EAAYC,GAAUmD,MAAO,EAAGK,KAAM,MAAOP,IAAK,IACrDpE,KAAKqE,UACRM,IAAKxD,EAAQwD,IACbP,IAAKjD,EAAQiD,IACbE,MAAOnD,EAAQmD,SAIvBxE,EAAOS,UAAUqS,QAAU,SAAUzR,GAEjC,OADAA,EAAUD,EAAYC,GAAWhB,OAAQ,IAClCH,KAAKoF,QAASjF,OAAQgB,EAAQhB,OAAQ8D,KAAM,sCAGvDnE,EAAOS,UAAUsS,QAAU,SAAU1R,GACjC,OAAOnB,KAAKuS,SAASpR,GAAW,KAAOnB,KAAKwS,UAAUrR,GAAW,KAAOnB,KAAKkS,SAAS/Q,IAG1FrB,EAAOS,UAAUgS,SAAW,SAAUpR,GAElC,OADAA,EAAUD,EAAYC,GAAUmD,MAAO,EAAGK,KAAM,GAAIP,IAAK,KAClDpE,KAAKqE,UAAUM,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,IAAKE,MAAOnD,EAAQmD,SAG7ExE,EAAOS,UAAUiS,UAAY,SAAUrR,GAEnC,OADAA,EAAUD,EAAYC,GAAUmD,MAAO,EAAGK,KAAM,IAAKP,IAAK,MACnDpE,KAAKqE,UAAUM,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,IAAKE,MAAOnD,EAAQmD,SAG7ExE,EAAOS,UAAUuS,MAAQ,SAAU3R,GAC/B,IACI4R,EADAC,EAAOhT,KAEPiT,EAAQ,SAAUC,GACd,IAAIC,KAKJ,OAHAD,EAAME,SAASrI,QAAQ,SAAS1F,GAC5B8N,EAAQvM,KAAKoM,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQkF,OAEpD6N,EAAMG,KAAOF,EAAQ7N,KAAK,OAEzCnE,EAAUD,EAAYC,GAClBoI,WAAW,EACXmJ,QAAS,KACTY,QAAQ,KAEC/J,YACTpI,EAAQiR,QAAS,GAErB,IAAIU,EACJ,OAAQ3R,EAAQuR,SACZ,IAAK,KACIvR,EAAQmS,QAYTP,EAAU/S,KAAK2D,MAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC9E2S,EAAQ3R,EAAQoI,UAAYwJ,EAAQQ,MAAM,OAAOjO,KAAK,KAAOyN,IAZ7DA,EAAU/S,KAAK2D,MAEX,KAAO3D,KAAK2D,MAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACrQ,KAAOH,KAAK2D,MAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACvU,KAAOH,KAAK2D,MAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACnV,KAAOH,KAAK2D,MAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACrW,KAAOH,KAAK2D,MAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,OAASqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC3Q,KAAO6S,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,MAErD2S,EAAQ3R,EAAQoI,UAAYwJ,EAAQQ,MAAM,OAAOjO,KAAK,KAAOyN,GAKjE,MACJ,IAAK,KACI5R,EAAQmS,QAoBTP,EAAU/S,KAAK2D,OACT0P,KAAM,KAAOrT,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,MAAOyP,UAAW,EAAE,KAC5DC,KAAM,SAAUD,UAAW,MAEjCN,EAAQ3R,EAAQoI,UAAY0J,EAAMF,GAAWE,EAAMF,GAASrJ,QAAQ,IAAK,MAvBzEqJ,EAAU/S,KAAK2D,OAGT0P,KAAM,KAAOrT,KAAK6D,WAAYI,KAAM,WAAc,KAAMmP,UAAW,EAAE,KACrEC,KAAM,OAASrT,KAAK6D,WAAYI,KAAM,QAAUmP,UAAW,EAAE,KAC7DC,KAAM,OAASrT,KAAK6D,WAAYI,KAAM,OAASmP,UAAW,EAAE,KAC5DC,KAAM,QAASD,UAAW,EAAE,KAC5BC,KAAM,OAASrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAQyP,UAAW,EAAE,KAClFC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KAC3EC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KACjEC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KAC3EC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KACjEC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KAC3EC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KACjEC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,KACjEC,KAAM,MAAQrT,KAAK2D,MAAM,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,OAAS,IAAKyP,UAAW,MAEtFN,EAAQ3R,EAAQoI,UAAY0J,EAAMF,GAAWE,EAAMF,GAASrJ,QAAQ,IAAK,GAAI,MAQjF,MACJ,IAAK,KACIvI,EAAQmS,QAUTP,EAAU/S,KAAK2D,MACX,MAAQ3D,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC7F,MAAQH,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC7F,KAAQ6S,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAClD,MAAQH,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACzG,KAAQH,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC7F,KAAQH,KAAK2D,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,MAE7F2S,EAAQ3R,EAAQoI,WAAawJ,IAjB7BA,EAAU/S,KAAK2D,MACZ,KAAO3D,KAAK2D,MAAM,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC5G,KAAOH,KAAK2D,MAAM,IAAK,IAAK,IAAK,IAAK,IAAK,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC7F,KAAOH,KAAK2D,MAAM,IAAK,IAAK,IAAK,IAAK,IAAK,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IAC7F,KAAOH,KAAK2D,MAAM,IAAK,IAAK,IAAK,IAAK,IAAI,IAAI,IAAK,IAAI,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,IACzG,KAAOH,KAAK2D,MAAM,IAAK,IAAK,IAAK,IAAK,IAAK,MAAQqP,EAAK5N,QAASnB,KAAM,aAAc9D,OAAQ,MAEhG2S,EAAQ3R,EAAQoI,WAAawJ,GAajC,MAEJ,IAAK,KACD,IAAIZ,EAAWnS,KAAKmS,SAAShR,GAASX,WAClCgT,EAAWxT,KAAK2B,SAAUgD,IAAK,EAAGP,IAAK,IAAK5D,WAC5CR,KAAK2B,SAAUgD,IAAK,EAAGP,IAAK,IAAK5D,WACjCR,KAAK2B,SAAUgD,IAAK,EAAGP,IAAK,IAAK5D,WACjCiT,EAAazT,KAAK2B,SAAUgD,IAAK,IAAMP,IAAK,OAAQ5D,WACxDsS,EAAQ3R,EAAQoI,UAAY4I,EAAW,IAAMqB,EAAW,IAAMC,EAAatB,EAAWqB,EAAWC,EAEzG,OAAOX,GAGXhT,EAAOS,UAAUmT,OAAS,WAQtB,OANS1T,KAAK6D,WAAWI,KAAM,sBAEhBjE,KAAK2B,SAASyC,IAAK,IAAMpE,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,UAIhE,KAFHlE,KAAK2B,SAASyC,IAAK,IAAMpE,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,UAAYlE,KAAK2B,SAASyC,IAAK,MAK3GtE,EAAOS,UAAUoT,SAAW,SAAUxS,GAElC,OADAA,EAAUD,EAAYC,GAAWuR,QAAS,OACnC1S,KAAKyD,IAAI,YAAYtC,EAAQuR,QAAQhP,gBAGhD5D,EAAOS,UAAUqT,OAAS,SAAUzS,GAChC,OAAOnB,KAAK2D,KAAK3D,KAAK2T,SAASxS,IAAUwK,MAG7C7L,EAAOS,UAAUsT,UAAY,SAAU1S,GAEnC,OADAA,EAAUD,EAAYC,GAAWuR,QAAS,OACnC1S,KAAKyD,IAAI,aAAatC,EAAQuR,QAAQhP,gBAGjD5D,EAAOS,UAAUuT,SAAW,SAAU3S,GAClC,OAAQA,GAAWA,EAAQiL,KACvBpM,KAAK2D,KAAK3D,KAAK6T,UAAU1S,IAAUwK,KACnC3L,KAAK2D,KAAK3D,KAAK6T,UAAU1S,IAAU+K,cAG3CpM,EAAOS,UAAUwT,MAAQ,SAAU5S,GAC/B,OAAQA,GAAWA,EAAQiL,KACvBpM,KAAK2D,KAAK3D,KAAKgU,OAAO7S,IAAUwK,KAChC3L,KAAK2D,KAAK3D,KAAKgU,OAAO7S,IAAU+K,cAGxCpM,EAAOS,UAAUyT,OAAS,SAAU7S,GAGhC,IAAI6S,EAEJ,QAJA7S,EAAUD,EAAYC,GAAWuR,QAAS,KAAMuB,kBAAkB,KAIlDvB,QAAQhP,eACpB,IAAK,KACD,IAAIuQ,EAAmBjU,KAAKyD,IAAI,oBAC5ByQ,EAAclU,KAAKyD,IAAI,eACvB0Q,EAAenU,KAAKyD,IAAI,gBAE5BuQ,KAEI7S,EAAQ8S,mBACRD,EAASA,EAAO7J,OAAO8J,IAEvB9S,EAAQ+S,cACRF,EAASA,EAAO7J,OAAO+J,IAEvB/S,EAAQgT,eACRH,EAASA,EAAO7J,OAAOgK,IAE3B,MACJ,IAAK,KACDH,EAAShU,KAAKyD,IAAI,mBAAmBtC,EAAQuR,QAAQhP,eACrD,MACJ,IAAK,KACDsQ,EAAShU,KAAKyD,IAAI,YAAYtC,EAAQuR,QAAQhP,eAItD,OAAOsQ,GAGXlU,EAAOS,UAAU0R,OAAS,SAAU9Q,GAEhC,IAAQ8Q,EAER,QAHA9Q,EAAUD,EAAYC,GAAWuR,QAAS,KAAM/J,UAAW,KAG3C+J,QAAQhP,eACpB,IAAK,KACDuO,EAASjS,KAAKwF,MAAOmD,UAAWxH,EAAQwH,YACxCsJ,EAASjS,KAAKuF,WAAW0M,GACzBA,GAAU,IACVA,GAAU9Q,EAAQiT,aACdpU,KAAKqU,cAAclT,GAAS+K,aAC5BlM,KAAKqU,cAAclT,GAASwK,KAChC,MACJ,IAAK,KACDsG,EAASjS,KAAKwF,MAAOmD,UAAWxH,EAAQwH,YACxCsJ,EAASjS,KAAKuF,WAAW0M,GACzBA,GAAU9Q,EAAQiT,aACdpU,KAAKqU,cAAclT,GAAS+K,aAC5BlM,KAAKqU,cAAclT,GAASwK,MAAQ,IAAMsG,EAGtD,OAAOA,GAGXnS,EAAOS,UAAU8T,cAAgB,SAAUlT,GAEvC,OADAA,EAAUD,EAAYC,GAAWuR,QAAS,OACnC1S,KAAK2D,KAAK3D,KAAKsU,gBAAgBnT,KAG1CrB,EAAOS,UAAU+T,gBAAkB,SAAUnT,GAGzC,OAFAA,EAAUD,EAAYC,GAAWuR,QAAS,OAEnC1S,KAAKyD,IAAI,mBAAmBtC,EAAQuR,QAAQhP,gBAKvD5D,EAAOS,UAAUgU,IAAM,SAAUpT,GAC7B,IAAIoT,EAAMvU,KAAKqF,EAAErF,KAAK2B,QAAS,GAAIyC,IAAK,IAOxC,OALIjD,IAAgC,IAArBA,EAAQqT,WACnBD,EAAI3N,KAAK,KACT2N,EAAMA,EAAIpK,OAAOnK,KAAKqF,EAAErF,KAAK2B,QAAS,GAAIyC,IAAK,MAG5CmQ,EAAIjP,KAAK,KAOpBxF,EAAOS,UAAUkU,KAAO,WACpB,OAAOzU,KAAKqD,OAAS,KAAO,MAGhCvD,EAAOS,UAAU8I,KAAO,SAAUlI,GAC9B,IAAIuT,EAAarL,EAGjB,GAAGlI,IAAYA,EAAQwD,KAAOxD,EAAQiD,KAAM,CAKxC,IAAIO,OAA6B,KAJjCxD,EAAUD,EAAYC,GAClBwT,UAAU,EACVvP,QAAQ,KAEaT,IAAsBxD,EAAQwD,IAAIiQ,UAAY,EAEnExQ,OAA6B,IAAhBjD,EAAQiD,IAAsBjD,EAAQiD,IAAIwQ,UAAY,OAEvEvL,EAAO,IAAIJ,KAAKjJ,KAAK6E,SAASF,IAAKA,EAAKP,IAAKA,SAC1C,CACH,IAAIyQ,EAAI7U,KAAK8U,OAAOC,KAAK,IACrBC,EAAcH,EAAEI,KAEjB9T,GAAWA,EAAQ2T,QAElBE,EAAchV,KAAKyD,IAAI,WAAYtC,EAAQ2T,MAAQ,GAAM,IAAM,IAAIG,MAGvE9T,EAAUD,EAAYC,GAClBiI,KAAMqB,SAASzK,KAAKoJ,OAAQ,IAG5B0L,MAAOD,EAAEK,QAAU,EACnBC,IAAKnV,KAAK2B,SAASgD,IAAK,EAAGP,IAAK4Q,IAChCI,KAAMpV,KAAKoV,MAAMC,YAAY,IAC7BC,OAAQtV,KAAKsV,SACbC,OAAQvV,KAAKuV,SACbC,YAAaxV,KAAKwV,cAClBb,UAAU,EACVvP,QAAQ,IAGZiE,EAAO,IAAIJ,KAAK9H,EAAQiI,KAAMjI,EAAQ2T,MAAO3T,EAAQgU,IAAKhU,EAAQiU,KAAMjU,EAAQmU,OAAQnU,EAAQoU,OAAQpU,EAAQqU,aAWpH,OALId,EAHAvT,EAAQwT,SAGOtL,EAAKgC,WAAa,EAAK,IAAMhC,EAAKiC,UAAY,IAAMjC,EAAKH,cAE1DG,EAAKiC,UAAY,KAAOjC,EAAKgC,WAAa,GAAK,IAAMhC,EAAKH,cAGrE/H,EAAQiE,OAASsP,EAAcrL,GAG1CvJ,EAAOS,UAAUkV,WAAa,SAAUtU,GACpC,OAAOnB,KAAKqJ,KAAKlI,GAASyT,WAG9B9U,EAAOS,UAAU6U,KAAO,SAAUjU,GAW9B,OAVAA,EAAUD,EAAYC,GAClBwD,IAAKxD,GAAWA,EAAQkU,WAAa,EAAI,EACzCjR,IAAKjD,GAAWA,EAAQkU,WAAa,GAAK,KAG9ChU,EAAUF,EAAQwD,IAAM,EAAG,sCAC3BtD,EAAUF,EAAQkU,YAAclU,EAAQiD,IAAM,GAAI,gEAClD/C,GAAWF,EAAQkU,YAAclU,EAAQiD,IAAM,GAAI,0CACnD/C,EAAUF,EAAQwD,IAAMxD,EAAQiD,IAAK,2CAE9BpE,KAAK2B,SAASgD,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,OAGxDtE,EAAOS,UAAUiV,YAAc,WAC3B,OAAOxV,KAAK2B,SAASyC,IAAK,OAG9BtE,EAAOS,UAAU+U,OAASxV,EAAOS,UAAUgV,OAAS,SAAUpU,GAO1D,OANAA,EAAUD,EAAYC,GAAUwD,IAAK,EAAGP,IAAK,KAE7C/C,EAAUF,EAAQwD,IAAM,EAAG,sCAC3BtD,EAAUF,EAAQiD,IAAM,GAAI,0CAC5B/C,EAAUF,EAAQwD,IAAMxD,EAAQiD,IAAK,2CAE9BpE,KAAK2B,SAASgD,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,OAGxDtE,EAAOS,UAAUuU,MAAQ,SAAU3T,GAG/BE,GAFAF,EAAUD,EAAYC,GAAUwD,IAAK,EAAGP,IAAK,MAE3BO,IAAM,EAAG,sCAC3BtD,EAAUF,EAAQiD,IAAM,GAAI,0CAC5B/C,EAAUF,EAAQwD,IAAMxD,EAAQiD,IAAK,2CAErC,IAAI0Q,EAAQ9U,KAAK2D,KAAK3D,KAAK0V,SAAS5S,MAAM3B,EAAQwD,IAAM,EAAGxD,EAAQiD,MACnE,OAAOjD,EAAQ4T,IAAMD,EAAQA,EAAMnJ,MAGvC7L,EAAOS,UAAUmV,OAAS,WACtB,OAAO1V,KAAKyD,IAAI,WAGpB3D,EAAOS,UAAUgV,OAAS,WACtB,OAAOvV,KAAK2B,SAASyC,IAAK,MAG9BtE,EAAOS,UAAUoV,UAAY,WACzB,OAAO3V,KAAK2B,SAASgD,IAAK,EAAGP,IAAKqG,UAAS,IAAIxB,MAAO2L,UAAY,IAAM,OAG5E9U,EAAOS,UAAUqV,QAAU,SAAUzU,GAEjC,IAAI0U,GAAY,SAAU,UAAW,YAAa,WAAY,UAK9D,OANA1U,EAAUD,EAAYC,GAAU2U,cAAc,KAEjCA,eACTD,EAASjP,KAAK,YACdiP,EAASjP,KAAK,WAEX5G,KAAKiH,QAAQ4O,IAGxB/V,EAAOS,UAAU6I,KAAO,SAAUjI,GAO9B,OALAA,EAAUD,EAAYC,GAAUwD,KAAK,IAAIsE,MAAOC,gBAGhD/H,EAAQiD,SAA8B,IAAhBjD,EAAQiD,IAAuBjD,EAAQiD,IAAMjD,EAAQwD,IAAM,IAE1E3E,KAAK2B,QAAQR,GAASX,YAOjCV,EAAOS,UAAUwV,GAAK,SAAU5U,GAG5B,IAAIqC,EAAMsD,EAAQkP,EAelB,OAbAxS,GAJArC,EAAUD,EAAYC,IAIF,KACRnB,KAAKiW,SAAUtK,KAAMxK,EAAQqC,KAAMuR,KAAK,IACxC/U,KAAKiW,SAAUlB,KAAK,IAEhCjO,EAAStD,EAAKsI,OAAOlB,MAAM,IAC3BoL,EAAcxS,EAAKrD,OAASqD,EAAKsI,OAAO3L,OAAS,GAGjD2G,EAASA,EAAOqD,OAAOnK,KAAKqF,EAAErF,KAAK6E,QAASmR,GAAcrR,IAAK,EAAGP,IAAK,MAGhEwC,KAAK5G,KAAKkW,eAAepP,EAAOxB,KAAK,MAErCwB,EAAOxB,KAAK,KAGvBxF,EAAOS,UAAU4V,SAAW,WAExB,OAAOnW,KAAKyD,IAAI,aAGpB3D,EAAOS,UAAU0V,QAAU,SAAU9U,GACjCA,EAAUD,EAAYC,GACtB,IAAIiV,EAAQpW,KAAKmW,WACb3S,EAAO,KAEX,GAAIrC,EAAQwK,KAAM,CACd,IAAK,IAAIvL,EAAI,EAAGA,EAAIgW,EAAMjW,OAAQC,IAE9B,GAAIgW,EAAMhW,GAAGuL,OAASxK,EAAQwK,MAAQyK,EAAMhW,GAAGiW,aAAelV,EAAQwK,KAAM,CACxEnI,EAAO4S,EAAMhW,GACb,MAGR,GAAa,OAAToD,EACA,MAAM,IAAIhC,WAAW,6BAA+BL,EAAQwK,KAAO,2BAGvEnI,EAAOxD,KAAK2D,KAAKyS,GAGrB,OAAOjV,EAAQ4T,IAAMvR,EAAOA,EAAKmI,MAIrC7L,EAAOS,UAAU+V,eAAiB,WAC9B,OAAOtW,KAAKyD,IAAI,mBAIpB3D,EAAOS,UAAUgW,SAAW,WACxB,OAAOvW,KAAK2D,KAAK3D,KAAKsW,mBAI1BxW,EAAOS,UAAUiW,UAAY,WACzB,OAAOxW,KAAKyD,IAAI,cAIpB3D,EAAOS,UAAUkW,SAAW,WACxB,OAAOzW,KAAK2D,KAAK3D,KAAKwW,cAI1B1W,EAAOS,UAAUmW,cAAgB,SAAUC,GACvC,IAAIC,EAAa5W,KAAK6F,OAAO7F,KAAKuW,SAAU,GACxCxQ,WAAY,SAASC,EAAKC,GAEtB,OAAOD,EAAI6Q,OAAO,SAASC,EAAKC,GAE5B,OAAOD,GAAQC,EAAKC,OAAS/Q,EAAI+Q,OAClC,MAIX,OAAIL,EACOC,EAAW,GAAGI,KAAO,IAAMJ,EAAW,GAAGI,KAEzCJ,GAIf9W,EAAOS,UAAU0W,OAAS,SAAU9V,GAEhCA,EAAUD,EAAYC,GAAUiD,IAAM,IAAOO,IAAM,IAEnD,IAAIsS,EAASjX,KAAKqE,UAAUM,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,IAAKE,MAAO,IAAI9D,WACvE0W,EAAQD,EAAOrM,MAAM,KAAK,GAQ9B,YANc0F,IAAV4G,EACAD,GAAU,MACHC,EAAM/W,OAAS,IACtB8W,GAAkB,KAGlBA,EAAS,EACF,KAAOA,EAAOvN,QAAQ,IAAK,IAE3B,IAAMuN,GAIrBnX,EAAOS,UAAU4W,KAAO,SAAUhW,GAC9B,OAAOkG,OAAOrH,KAAKiX,OAAO9V,GAASuI,QAAQ,IAAK,KAAK0N,iBAAmB,KAG5EtX,EAAOS,UAAU8W,IAAM,SAAUlW,GAC7BA,EAAUD,EAAYC,GACtB,IAAIkW,KAYJ,OAVAA,EAAIjO,KAAOpJ,KAAKsX,WAIZD,EAAIjO,QAAU,IAAIH,MAAOC,cAAe1I,WACxC6W,EAAIvC,MAAQ9U,KAAKuX,WAAWC,QAAQ,IAEpCH,EAAIvC,MAAQ9U,KAAKuX,YAGdpW,EAAQ4T,IAAMsC,EAAMA,EAAIvC,MAAQ,IAAMuC,EAAIjO,MAGrDtJ,EAAOS,UAAUgX,UAAY,SAAUpW,GACnCA,EAAUD,EAAYC,GACtB,IAAI2T,EAAO2C,EAEPC,GAAW,IAAIzO,MAAOoC,WAAa,EAEvC,GAAIlK,EAAQqW,QAAwB,KAAbE,EACnB,GACI5C,EAAQ9U,KAAK8U,OAAOC,KAAK,IAAOG,QAChCuC,EAAYhN,SAASqK,EAAO,UACvB2C,GAAaC,QAEtB5C,EAAQ9U,KAAK8U,OAAOC,KAAK,IAAOG,QAGpC,OAAOJ,GAGXhV,EAAOS,UAAU+W,SAAW,WACxB,IAAII,GAAW,IAAIzO,MAAOoC,WAAa,EACnCsM,GAAU,IAAI1O,MAAOC,cAEzB,OAAOlJ,KAAKoJ,MAAMzE,IAAoB,KAAb+S,EAAoBC,EAAU,EAAKA,EAAUvT,IAAMuT,EAAU,MAG1F7X,EAAOS,UAAUqX,IAAM,SAAUzW,GAE7B,QADAA,EAAUD,EAAYC,GAAWuR,QAAS,QAC1BA,QAAQhP,eACpB,IAAK,KACD,OAAO1D,KAAK6X,WAQxB/X,EAAOS,UAAUuX,KAAO,WACpB,IAAIhU,EAAQ,6BAOZ,OAJI9D,KAAKoF,QAASjF,OAAQ,EAAG8D,KAAMH,IAC/B9D,KAAK6G,IAAI7G,KAAK6E,SAAUF,IAAK,EAAGP,IAAK,KAAO,GAC5CpE,KAAKoF,QAASjF,OAAQ,EAAG8D,KAJdH,yCAKX9D,KAAK6G,IAAI7G,KAAK2B,UAAW3B,KAAK2B,SAAUgD,IAAK,EAAGP,IAAK,OAQ7DtE,EAAOS,UAAUsX,OAAS,WACtB,IAAIA,EAAS7X,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,OAGxC,OADAyT,EAAS7X,KAAK6G,IAAIgR,EAAQ,GAAK7X,KAAK6G,IAAI7G,KAAK2D,KAAK3D,KAAK6T,WAAYnB,QAAS,QAASsE,KAAM,IAC3EhX,KAAKkW,eAAe2B,IAiBxC/X,EAAOS,UAAUwX,GAAK,SAAU5W,GAE5B,IAAI0I,GADJ1I,EAAUA,OACa0I,OAAS1I,EAAQ0I,OAAS7J,KAAK6J,SAClDD,EAAUzI,EAAQyI,MAAQzI,EAAQyI,MAAQ5J,KAAK4J,OAASC,OAAQA,EAAQC,YAAa,OACrFM,EAASjJ,EAAQiJ,KAAOjJ,EAAQiJ,KAAOpK,KAAKoK,MAAQN,YAAa,OACjEf,EAAa5H,EAAQ4H,SAAW5H,EAAQ4H,SAAW/I,KAAK+I,WACxDsJ,EAASlR,EAAQkR,KAAOlR,EAAQkR,KAAOrS,KAAKiH,SAAS,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,MAAQjH,KAAK6G,IAAI7G,KAAK2B,SAASyC,IAAI,MAAO,GACtJ2T,KACAC,EAAiB,SAASrM,EAAMsM,GAC5B,IAAIC,EACAC,KAyBJ,OAvBIxM,EAAKxL,OAAS,EACdgY,EAAexM,EAAKf,MAAM,IAAIT,OAAO,MAAMS,MAAM,KAAKtD,OAAO,EAAE,KAG/D4Q,EAAOvM,EAAKhJ,cAAciI,MAAM,IAAIwN,IAAI,SAASC,GAC7C,OAA6C,IAArC,qBAAqBnS,QAAQmS,GAAaA,OAAI/H,IACvDhL,KAAK,KACCnF,OAAS,IAEV+X,EADAD,EACOC,EAAKzS,OAAO,EAAE,GAEdyS,EAAK,GAAKA,EAAKzS,OAAO,EAAE,IAGnCyS,EAAK/X,OAAS,IACdgY,EAAeD,EACfA,EAAOvM,EAAKhJ,cAAciI,MAAM,IAAIwN,IAAI,SAASC,GAC7C,OAAgC,IAAxB,QAAQnS,QAAQmS,GAAaA,OAAI/H,IAC1ChL,KAAK,IAAIG,OAAO,EAAG,EAAI0S,EAAahY,SAE3CgY,GAA8BD,GAG3BC,GA+Bf,OAHAJ,EAAKA,EAAG5N,OAAO6N,EAAe5N,GAAM,GAAO4N,EAAepO,GA1BrC,SAASb,EAAUc,EAAQqB,GAGxC,OAAQnC,EAASG,cAAc1I,WAAWiF,OAAO,IAF7B,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KAGtDsD,EAASsC,YACtBH,EAAKrE,IAAIkC,EAASuC,WAAuC,WAAzBzB,EAAOnG,cAA8B,GAAK,GAAI,GAqB5B4U,CAAevP,EAAUc,EAAQ7J,MAAOqS,EAAK1P,cAAciI,MAAM,KAAKtF,KAAK,KAC7IyS,GApB2B,SAASA,GAQ5B,IAAI,IAPAQ,EAAS,uCACTC,EAAS,uCACTC,EAAS,6BAETC,EAAS,EAGLtY,EAAI,EAAGA,EAAI,GAAIA,IAEfsY,GADAtY,EAAI,GAAM,EACDqY,EAAMvS,QAAQsS,EAAOD,EAAOrS,QAAQ6R,EAAG3X,MAN3C,6BASU8F,QAAQsS,EAAOD,EAAOrS,QAAQ6R,EAAG3X,MAGxD,OAAOqY,EAAMC,EAAQ,IAIvBC,CAAqBZ,EAAGpV,gBAEpBA,eAGd7C,EAAOS,UAAUqY,SAAW,WAGxB,IAAK,IAFD9R,EAAS9G,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,aACpC4B,EAAMhG,KAAK6G,IAAIC,EAAQ,IAAI8D,MAAM,IAC5BxK,EAAI,EAAGA,EAAI4F,EAAI7F,OAAQC,IAC5B4F,EAAI5F,GAAKqK,SAASzE,EAAI5F,IAG1B,IAAIyY,GAAiB,EAAI7S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAKxJ,OAJqB,IAAlB6S,IACCA,EAAgB,GAAKA,GAGlB7S,EAAIV,KAAK,IAAMuT,GAG1B/Y,EAAOS,UAAUuY,OAAS,WAGtB,IAAK,IAFDhS,EAAS9G,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,YACpC4B,EAAMhG,KAAK6G,IAAIC,EAAQ,GAAG8D,MAAM,IAC3BxK,EAAI,EAAGA,EAAI4F,EAAI7F,OAAQC,IAC5B4F,EAAI5F,GAAKqK,SAASzE,EAAI5F,IAG1B,IAAIyY,GAAiB,EAAI7S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAC3I,OAAqB,KAAlB6S,EACQ7Y,KAAK8Y,SAGT9S,EAAIV,KAAK,IAAMuT,GAG1B/Y,EAAOS,UAAUwY,SAAW,WAGxB,IAAK,IAFDjS,EAAS9G,KAAK2B,SAASgD,IAAK,EAAGP,IAAK,WACpC4B,EAAMhG,KAAK6G,IAAIC,EAAQ,GAAG8D,MAAM,IAC3BxK,EAAI,EAAGA,EAAI4F,EAAI7F,OAAQC,IAC5B4F,EAAI5F,GAAKqK,SAASzE,EAAI5F,IAG1B,IAAIyY,GAAiB,EAAI7S,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,GAAK,EAAIA,EAAI,IAAM,GAK9H,OAJqB,KAAlB6S,IACCA,EAAgB,GAGb7S,EAAIV,KAAK,IAAMuT,GAO1B/Y,EAAOS,UAAUyY,KAAO,SAAS7X,GAQ/BA,EAAUD,EAAYC,GAAW8X,MAAQ,YACzC,IAAIC,GACFC,UAAW,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,KACzCC,OAAQ,KAAM,KAAM,KAAM,KAAM,MAChCC,QAAS,KAAM,KAAM,KAAM,KAAM,OAKnC,OAHAH,EAAOI,IAAMJ,EAAOC,SAAShP,OAAO+O,EAAOE,MAAMjP,OAAO+O,EAAOG,SAC/DH,EAAOK,QAAUL,EAAOC,SAAShP,OAAO+O,EAAOE,OAC/CF,EAAOM,SAAWN,EAAOC,SAAShP,OAAO+O,EAAOG,QACzCrZ,KAAKiH,QAAQiS,EAAO/X,EAAQ8X,SAGrCnZ,EAAOS,UAAUkZ,UAAY,SAAStY,GAIpC,OADAA,EAAUD,EAAYC,GAAWwD,IAFvB,EAEkCP,IADlC,MAEHpE,KAAK6E,SAASF,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,OAGtDtE,EAAOS,UAAUmZ,cAAgB,SAASvY,GAExC,IAAIwY,GAAmB,MAAO,MAAO,MAAO,OAW5C,OAZAxY,EAAUD,EAAYC,GAAWyY,MAAM,KAE3BA,OACVD,GACE,OACA,OACA,IACA,MACA,MACA,MAGG3Z,KAAKiH,QAAQ0S,IAGtB7Z,EAAOS,UAAUsZ,MAAQ,SAAU1Y,GAEjC,OADAA,EAAUD,EAAYC,GACfnB,KAAKgZ,KAAK7X,GAAWnB,KAAK0Z,cAAcvY,IAGjDrB,EAAOS,UAAUuZ,MAAQ,SAAU3Y,GAIjC,OADAA,EAAUD,EAAYC,GAAUwD,IAFtB,GAEgCP,IADhC,MAEHpE,KAAK6E,SAASF,IAAKxD,EAAQwD,IAAKP,IAAKjD,EAAQiD,OAQtDtE,EAAOS,UAAUwZ,KAAO,SAAS5Y,GAC/B,OAAOnB,KAAKqD,OAAS,QAAU,SASjCvD,EAAOS,UAAUyZ,GAAKvY,GAAQkD,IAAK,EAAGP,IAAK,IAC3CtE,EAAOS,UAAU0Z,GAAKxY,GAAQkD,IAAK,EAAGP,IAAK,IAC3CtE,EAAOS,UAAU2Z,GAAKzY,GAAQkD,IAAK,EAAGP,IAAK,IAC3CtE,EAAOS,UAAU4Z,IAAM1Y,GAAQkD,IAAK,EAAGP,IAAK,KAC5CtE,EAAOS,UAAU6Z,IAAM3Y,GAAQkD,IAAK,EAAGP,IAAK,KAC5CtE,EAAOS,UAAU8Z,IAAM5Y,GAAQkD,IAAK,EAAGP,IAAK,KAC5CtE,EAAOS,UAAU+Z,IAAM7Y,GAAQkD,IAAK,EAAGP,IAAK,KAC5CtE,EAAOS,UAAUga,KAAO9Y,GAAQkD,IAAK,EAAGP,IAAK,MAE7CtE,EAAOS,UAAU6Q,IAAM,SAAUoJ,EAAQrZ,GAErC,GADAA,EAAUD,EAAYC,GACjBqZ,EAEE,CACH,IAAIC,EAAOD,EAAO9W,cAAckH,MAAM,KAClC8P,KAEJ,GAAoB,IAAhBD,EAAKta,SAAiBsK,SAASgQ,EAAK,GAAI,MAAQhQ,SAASgQ,EAAK,GAAI,IAClE,MAAM,IAAIxX,MAAM,4IAEpB,IAAK,IAAI7C,EAAIqa,EAAK,GAAIra,EAAI,EAAGA,IACzBsa,EAAMta,EAAI,GAAKJ,KAAK2B,SAASgD,IAAK,EAAGP,IAAKqW,EAAK,KAEnD,YAA+B,IAAhBtZ,EAAQuG,KAAuBvG,EAAQuG,IAAOgT,EAAM7D,OAAO,SAAU8D,EAAGtC,GAAK,OAAOsC,EAAItC,IAAQqC,EAX/G,MAAM,IAAIlZ,WAAW,gDAgB7B1B,EAAOS,UAAU2M,KAAO,SAAU/L,GAC9BA,EAAUD,EAAYC,GAAWyZ,QAAS,IAE1C,IAAIC,EAAY,mBAWhB,OATW7a,KAAKoF,QAASnB,KAAM4W,EAAW1a,OAAQ,IAAO,IAC9CH,KAAKoF,QAASnB,KAAM4W,EAAW1a,OAAQ,IAAO,IAE9CgB,EAAQyZ,QACR5a,KAAKoF,QAASnB,KAAM4W,EAAW1a,OAAQ,IAAO,IAE9CH,KAAKoF,QAASnB,KAPN,OAO0B9D,OAAQ,IAC1CH,KAAKoF,QAASnB,KAAM4W,EAAW1a,OAAQ,IAAO,IAC9CH,KAAKoF,QAASnB,KAAM4W,EAAW1a,OAAQ,MAKtDL,EAAOS,UAAUI,KAAO,SAAUQ,GAE9B,IAAI8C,EAA0B,WAD9B9C,EAAUD,EAAYC,GAAUhB,OAAS,GAAI+D,OAAQ,WAClCA,OAAqBtB,EAASD,cAAgBC,EACjE,OAAO5C,KAAKoF,QAAQnB,KAAMA,EAAM9D,OAAQgB,EAAQhB,UAGpDL,EAAOS,UAAUua,WAAa,SAAUtW,GACpC,IAAIuW,EAAMvW,EAAIhE,WAEd,OADkBua,EAAInS,UAAUmS,EAAI5a,OAAS,KACvBH,KAAKkW,gBAAgB6E,EAAInS,UAAU,EAAGmS,EAAI5a,OAAS,KAG7EL,EAAOS,UAAU2V,eAAiB,SAAU1R,GAKxC,IAAK,IAFDkU,EAFAsC,EAASxW,EAAIhE,WAAWoK,MAAM,IAAIqQ,UAClCvT,EAAM,EAGDtH,EAAI,EAAG8B,EAAI8Y,EAAO7a,OAAQ+B,EAAI9B,IAAKA,EACxCsY,GAASsC,EAAO5a,GACZA,EAAI,GAAM,IACVsY,GAAS,GACG,IACRA,GAAS,GAGjBhR,GAAOgR,EAEX,OAAc,EAANhR,EAAW,IAIvB5H,EAAOS,UAAUqO,IAAM,SAASzN,GAC5B,IAAIsK,GAASsP,IAAK,GAAIhZ,IAAK,KAAMgT,KAAK,GAEtC,GAAK5T,EAIA,GAAuB,iBAAZA,EACZsK,EAAKsP,IAAM5Z,EACXA,SAEC,CAAA,GAAuB,iBAAZA,EACZ,OAAO,KAEN,GAA2B,UAAxBA,EAAQwM,YACZ,OAAO,UAXPlC,EAAKsP,IAAM/a,KAAKoF,SAChBjE,KAeJ,KAFAsK,EAAOvK,EAAYC,EAASsK,IAEnBsP,IACL,MAAM,IAAI9X,MAAM,kDAGpB,OAAOjD,KAAKgB,MAAM4N,IAAInD,EAAKsP,IAAKtP,EAAK1J,IAAK0J,EAAKsJ,MAgEnDjV,EAAOS,UAAU2a,KAAO,SAAS/Z,GAE7B,IAGIga,EACA5N,EAJA6N,EAAcja,MAEdka,EAAc/a,OAAO0B,KAAKhC,KAAKyD,IAAI,kBAQvC,GAHA0X,EAAWnb,KAAKwF,MAAMrF,OAASib,EAAYjb,SAGxCib,EAAY5J,UAGX,OADAjE,EAAgB6N,EAAY5J,UACpB2J,EAAW,IAAM5N,EAI7B,GAAG6N,EAAYzJ,WAAY,CAEvB,GAAGpP,MAAMD,QAAQ8Y,EAAYzJ,YAGzB,OADApE,EAAgBvN,KAAKiH,QAAQmU,EAAYzJ,YACjCwJ,EAAW,IAAM5N,EAExB,GAAG6N,EAAYzJ,WAAWhE,cAAgBrN,OAAQ,CAEnD,IAAIgb,EAA4BF,EAAYzJ,WACxC3P,EAAO1B,OAAO0B,KAAKsZ,GAGvB,OADA/N,EAAgBvN,KAAKiH,QAAQqU,EAA0Btb,KAAKiH,QAAQjF,KAC5DmZ,EAAW,IAAM5N,EAG7B,MAAM,IAAItK,MAAM,iDAIpB,GAAGmY,EAAYG,SAAU,CAErB,IAAIA,EAAWH,EAAYG,SAC3B,IAAoC,IAAjCF,EAAUnV,QAAQqV,GAGjB,OADAhO,EAAgBvN,KAAKiH,QAAQjH,KAAKyD,IAzClB,iBAyCyC8X,IACjDJ,EAAW,IAAM5N,EAG7B,MAAM,IAAI/L,WAAW,+EAKzB,OADA+L,EAAgBvN,KAAKiH,QAAQjH,KAAKyD,IAjDV,iBAiDiCzD,KAAKiH,QAAQoU,KAC9DF,EAAW,IAAM5N,GAG7B,IAAIiO,GAEAC,YACIC,MACIC,IAAO,QAAS,OAAQ,SAAU,UAAW,UAAW,QAAS,UAAW,SAAU,UAAW,SAAU,cAAe,SAAU,UAAW,SAAU,SAAU,UAAW,OAAQ,OAAQ,SAAU,SAAU,UAAW,SAAU,QAAS,SAAU,QAAS,SAAU,UAAW,QAAS,UAAW,QAAS,OAAQ,OAAQ,WAAY,OAAQ,UAAW,QAAS,QAAS,WAAY,QAAS,UAAW,SAAU,UAAW,UAAW,SAAU,WAAY,UAAW,OAAQ,QAAS,SAAU,SAAU,QAAS,YAAa,QAAS,QAAS,UAAW,SAAU,QAAS,OAAQ,OAAQ,SAAU,UAAW,OAAQ,SAAU,SAAU,OAAQ,WAAY,MAAO,SAAU,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,QAAS,OAAQ,QAAS,MAAO,QAAS,QAAS,SAAU,QAAS,SAAU,YAAa,QAAS,QAAS,UAAW,SAAU,OAAQ,QAAS,SAAU,SAAU,OAAQ,OAAQ,QAAS,UAAW,QAAS,QAAS,SAAU,UAAW,SAAU,WAAY,SAAU,SAAU,QAAS,UAAW,QAAS,SAAU,UAAW,UAAW,OAAQ,UAAW,QAAS,UAAW,OAAQ,OAAQ,QAAS,OAAQ,OAAQ,SAAU,QAAS,YAAa,QAAS,SAAU,OAAQ,OAAQ,OAAQ,SAAU,SAAU,QAAS,OAAQ,YAAa,SAAU,SAAU,SAAU,QAAS,UAAW,OAAQ,QAAS,QAAS,OAAQ,UAAW,MAAO,UAAW,UAAW,QAAS,QAAS,QAAS,SAAU,WAAY,SAAU,OAAQ,QAAS,QAAS,OAAQ,OAAQ,SAAU,MAAO,SAAU,UAAW,QAAS,QAAS,UAAW,SAAU,MAAO,QAAS,QAAS,SAAU,SAAU,QAAS,WAAY,MAAO,QAAS,SAAU,QAAS,QAAS,QAAS,SAAU,MAAO,QAAS,SAAU,UAAW,SAAU,SAAU,MAAO,OAAQ,MAAO,SAAU,SAAU,OAAQ,QAAS,UAAW,WAAY,OAAQ,SAAU,UAAW,WAAY,QAAS,QAAS,OAAQ,QAAS,MAAO,QAAS,UAAW,SAAU,SAAU,QAAS,OAAQ,QAAS,SAAU,QAAS,QAAS,SAAU,QAAS,QAAS,MAAO,UAAW,QAAS,QAAS,OAAQ,SAAU,QAAS,UAAW,UAAW,WAAY,UAAW,MAAO,QAAS,OAAQ,SAAU,UAAW,QAAS,OAAQ,YAAa,OAAQ,OAAQ,UAAW,UAAW,MAAO,QAAS,UAAW,YAAa,OAAQ,SAAU,SAAU,OAAQ,OAAQ,QAAS,QAAS,UAEl1EC,IAAO,SAAU,UAAW,OAAQ,aAAc,UAAW,UAAW,SAAU,SAAU,SAAU,UAAW,WAAY,UAAW,UAAW,SAAU,WAAY,QAAS,QAAS,SAAU,YAAa,UAAW,UAAW,SAAU,WAAY,YAAa,UAAW,QAAS,QAAS,SAAU,QAAS,OAAQ,WAAY,SAAU,UAAW,OAAQ,OAAQ,WAAY,WAAY,SAAU,SAAU,OAAQ,SAAU,QAAS,WAAY,WAAY,aAAc,WAAY,UAAW,YAAa,SAAU,WAAY,UAAW,YAAa,YAAa,YAAa,aAAc,WAAY,YAAa,SAAU,OAAQ,UAAW,WAAY,WAAY,SAAU,WAAY,WAAY,WAAY,QAAS,SAAU,SAAU,OAAQ,WAAY,UAAW,OAAQ,UAAW,QAAS,SAAU,WAAY,QAAS,SAAU,QAAS,eAAgB,UAAW,SAAU,SAAU,WAAY,QAAS,UAAW,QAAS,UAAW,QAAS,OAAQ,UAAW,SAAU,UAAW,SAAU,QAAS,aAAc,QAAS,SAAU,WAAY,OAAQ,SAAU,QAAS,WAAY,UAAW,UAAW,SAAU,YAAa,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,SAAU,UAAW,SAAU,MAAO,UAAW,UAAW,SAAU,QAAS,WAAY,YAEnyCC,IAAO,QAAQ,OAAO,OAAO,UAAU,SAAS,YAAY,MAAM,QAAQ,OAAO,OAAO,MAAM,WAAW,WAAW,MAAO,QAAQ,OAAO,QAAQ,MAAM,SAAS,QAAQ,aAAa,WAAW,OAAO,QAAQ,SAAS,OAAO,SAAS,SAAS,QAAQ,OAAO,OAAO,QAAQ,SAAS,SAAS,OAAO,QAAQ,QAAQ,OAAO,SAAS,QAAQ,OAAO,UAAU,SAAS,QAAQ,QAAQ,WAAW,QAAQ,QAAQ,SAAS,SAAS,SAAS,OAAO,OAAO,OAAO,UAAU,OAAO,SAAS,QAAQ,OAAO,OAAO,WAAW,aAAa,OAAO,QAAQ,MAAM,QAAQ,SAAS,SAAS,QAAQ,QAAQ,OAAO,SAAS,QAAQ,MAAM,MAAM,OAAO,WAAW,OAAO,WAAW,QAAQ,SAAS,OAAO,SAAS,OAAO,QAAQ,OAAO,OAAO,UAAU,WAAW,UAAU,WAAW,MAAM,OAAO,QAAQ,QAAQ,OAAO,UAAU,SAAS,UAAU,SAAS,WAAW,UAAU,MAAM,OAAO,QAAQ,OAAO,OAAO,QAAQ,UAAU,WAAW,SAAS,WAAW,SAAS,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO,UAAU,QAAQ,OAAO,OAAO,SAAS,QAAQ,SAAS,MAAM,UAAU,UAAU,OAAO,QAAQ,QAAQ,MAAM,SAAS,SAAS,aAAa,MAAM,MAAM,OAAO,OAAO,QAAQ,OAAO,OAAO,SAAS,QAAQ,OAAO,OAAO,UAAU,QAAQ,SAAS,OAAO,MAAM,OAAO,SAAS,MAAM,SAAS,QAAQ,SAAS,MAAM,SAAS,UAG9yCC,QACIH,IAAO,OAAQ,OAAQ,YAAa,SAAU,WAAY,MAAO,QAAS,SAAU,QAAS,QAAS,QAAS,OAAQ,WAAY,OAAQ,SAAU,QAAS,SAAU,QAAS,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,QAAS,SAAU,QAAS,SAAU,OAAQ,YAAa,UAAW,MAAO,SAAU,QAAS,SAAU,SAAU,QAAS,OAAQ,SAAU,MAAO,UAAW,OAAQ,OAAQ,OAAQ,SAAU,QAAS,QAAS,SAAU,YAAa,OAAQ,OAAQ,YAAa,QAAS,QAAS,OAAQ,MAAO,QAAS,UAAW,SAAU,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,SAAU,QAAS,QAAS,OAAQ,MAAO,OAAQ,UAAW,QAAS,WAAY,QAAS,QAAS,QAAS,OAAQ,QAAS,SAAU,OAAQ,QAAS,UAAW,QAAS,OAAQ,SAAU,SAAU,SAAU,QAAS,YAAa,UAAW,OAAQ,QAAS,QAAS,SAAU,SAAU,OAAQ,WAAY,QAAS,UAAW,QAAS,UAAW,SAAU,SAAU,YAAa,QAAS,MAAO,SAAU,OAAQ,UAAW,UAAW,UAAW,MAAO,UAAW,QAAS,OAAQ,SAAU,QAAS,OAAQ,UAAW,MAAO,SAAU,SAAU,MAAO,QAAS,UAAW,UAAW,MAAO,SAAU,QAAS,QAAS,SAAU,UAAW,OAAQ,QAAS,SAAU,SAAU,UAAW,SAAU,OAAQ,OAAQ,QAAS,QAAS,SAAU,QAAS,WAAY,WAAY,OAAQ,WAAY,OAAQ,QAAS,YAAa,WAAY,SAAU,WAAY,QAAS,OAAQ,SAAU,SAAU,SAAU,SAAU,UAAW,WAAY,QAAS,QAAS,MAAO,QAAS,SAAU,OAAQ,UAAW,YAAa,OAAQ,OAAQ,OAAQ,QAAS,WAAY,QAAS,OAAQ,SAAU,SAAU,SAAU,QAAS,MAAO,SAAU,WAAY,YAAa,QAAS,QAAS,MAAO,UAAW,QAAS,UAAW,UAAW,SAAU,QAAS,UAAW,SAAU,QAAS,QAAS,UAAW,YAAa,OAAQ,WAAY,SAAU,UAAW,WAAY,UAAW,SAAU,OAAQ,OAAQ,OAAQ,SAAU,SAAU,WAAY,OAAQ,SAAU,SAAU,SAAU,OAAQ,OAAQ,SAAU,UAAW,aAAc,OAAQ,UAAW,OAAQ,SAAU,UAAW,QAAS,SAAU,UAAW,OAAQ,SAAU,UAAW,UAAW,SAAU,SAAU,QAAS,OAAQ,SAAU,UAAW,QAAS,QAAS,SAAU,UAAW,SAAU,UAAW,UAAW,WAE/1EC,IAAO,MAAO,UAAW,aAAc,UAAW,QAAS,SAAU,OAAQ,aAAc,WAAY,SAAU,aAAc,YAAa,UAAW,OAAQ,UAAW,SAAU,UAAW,WAAY,YAAa,SAAU,QAAS,UAAW,QAAS,WAAY,UAAW,WAAY,WAAY,QAAS,UAAW,SAAU,SAAU,QAAS,UAAW,WAAY,WAAY,UAAW,SAAU,UAAW,OAAQ,YAAa,QAAS,WAAY,QAAS,aAAc,WAAY,OAAQ,MAAO,WAAY,WAAY,WAAY,WAAY,QAAS,SAAU,YAAa,YAAa,OAAQ,QAAS,QAAS,SAAU,OAAQ,UAAW,UAAW,WAAY,SAAU,WAAY,WAAY,aAAc,SAAU,YAAa,QAAS,MAAO,SAAU,OAAQ,UAAW,QAAS,OAAQ,WAAY,UAAW,QAAS,MAAO,UAAW,QAAS,QAAS,UAAW,OAAQ,QAAS,OAAQ,QAAS,UAAW,QAAS,QAAS,UAAW,WAAY,QAAS,UAAW,OAAQ,WAAY,aAAc,QAAS,iBAAkB,eAAgB,cAAe,YAAa,eAAgB,SAAU,SAAU,QAAS,UAAW,SAAU,UAAW,UAAW,UAAW,SAAU,UAAW,SAAU,WAAY,QAAS,YAAa,QAAS,OAAQ,QAAS,WAAY,QAAS,UAAW,YAAa,UAAW,SAAU,OAAQ,OAAQ,UAAW,OAAQ,UAAW,UAAW,WAAY,UAAW,SAAU,OAAQ,SAAU,UAAW,SAAU,SAAU,YAAa,QAAS,QAAS,WAAY,UAAW,SAAU,OAAQ,UAAW,QAAS,YAAa,UAAW,QAAS,UAAW,QAAS,OAAQ,WAAY,QAAS,QAAS,WAAY,YAE9pDC,IAAO,MAAO,UAAW,OAAQ,SAAU,QAAS,MAAO,QAAS,QAAS,OAAQ,OAAQ,OAAQ,WAAY,YAAa,UAAW,QAAS,SAAU,QAAS,UAAW,SAAU,QAAS,UAAW,WAAY,UAAW,YAAa,UAAW,WAAY,SAAU,QAAS,OAAQ,QAAS,QAAS,OAAQ,QAAS,OAAQ,QAAS,UAAW,SAAU,QAAS,MAAO,QAAS,QAAS,QAAS,UAAW,MAAO,QAAS,QAAS,UAAW,SAAU,QAAS,QAAS,MAAO,OAAQ,SAAU,OAAQ,SAAU,WAAY,UAAW,UAAW,UAAW,WAAY,UAAW,UAAW,OAAQ,QAAS,QAAS,QAAS,QAAS,QAAS,MAAO,OAAQ,QAAS,OAAQ,SAAU,QAAS,SAAU,QAAS,OAAQ,UAAW,QAAS,SAAU,SAAU,QAAS,QAAS,QAAS,UAAW,QAAS,YAAa,UAAW,UAAW,UAAW,QAAS,UAAW,WAAY,SAAU,SAAU,QAAS,UAAW,SAAU,OAAQ,UAAW,SAAU,WAAY,QAAS,UAAW,SAAU,MAAO,SAAU,OAAQ,UAAW,OAAQ,UAAW,SAAU,QAAS,OAAQ,SAAU,SAAU,QAAS,SAAU,QAAS,UAAW,SAAU,OAAQ,QAAS,SAAU,UAAW,UAAW,SAAU,OAAQ,WAAY,QAAS,UAAW,SAAU,SAI1wCE,WACIJ,IAAO,QAAS,UAAW,WAAY,QAAS,QAAS,QAAS,SAAU,SAAU,QAAS,SAAU,WAAY,SAAU,UAAW,QAAS,SAAU,SAAU,WAAY,SAAU,WAAY,WAAY,QAAS,YAAa,QAAS,MAAO,SAAU,OAAQ,QAAS,QAAS,YAAa,OAAQ,SAAU,QAAS,OAAQ,QAAS,QAAS,QAAS,QAAS,WAAY,SAAU,SAAU,WAAY,QAAS,UAAW,SAAU,WAAY,WAAY,SAAU,QAAS,UAAW,UAAW,UAAW,UAAW,SAAU,SAAU,OAAQ,OAAQ,SAAU,OAAQ,SAAU,SAAU,SAAU,SAAU,aAAc,MAAO,SAAU,OAAQ,SAAU,WAAY,OAAQ,UAAW,QAAS,SAAU,SAAU,QAAS,UAAW,QAAS,UAAW,OAAQ,SAAU,OAAQ,YAAa,UAAW,UAAW,QAAS,SAAU,OAAQ,YAAa,SAAU,SAAU,aAAc,SAAU,UAAW,SAAU,WAAY,SAAU,YAAa,UAAW,UAAW,OAAQ,QAAS,QAAS,OAAQ,WAAY,SAAU,WAAY,UAAW,QAAS,OAAQ,OAAQ,SAAU,QAAS,WAAY,SAAU,QAAS,WAAY,SAAU,WAAY,OAAQ,WAAY,QAAS,QAAS,SAAU,UAAW,QAAS,OAAQ,UAAW,UAAW,SAAU,SAAU,SAAU,QAAS,WAAY,QAAS,OAAQ,QAAS,UAAW,UAAW,SAAU,QAAS,QAAS,QAAS,QAAS,SAAU,OAAQ,SAAU,OAAQ,YAAa,OAAQ,QAAS,UAAW,SAAU,QAAS,UAAW,QAAS,SAAU,WAAY,OAAQ,QAAS,UAAW,OAAQ,UAAW,SAAU,UAAW,UAAW,WAAY,QAAS,SAAU,QAAS,WAAY,SAAU,SAAU,SAAU,MAAO,UAAW,QAAS,UAAW,SAAU,SAAU,OAAQ,aAAc,UAAW,OAAQ,UAAW,OAAQ,SAAU,MAAO,QAAS,YAAa,YAAa,SAAU,SAAU,WAAY,UAAW,SAAU,OAAQ,SAAU,SAAU,SAAU,WAAY,SAAU,SAAU,YAAa,OAAQ,UAAW,OAAQ,UAAW,WAAY,UAAW,UAAW,SAAU,aAAc,WAAY,aAAc,WAAY,QAAS,QAAS,SAAU,QAAS,SAAU,UAAW,WAAY,SAAU,YAAa,QAAS,SAAU,SAAU,SAAU,UAAW,SAAU,SAAU,SAAU,OAAQ,MAAO,SAAU,QAAS,OAAQ,UAAW,UAAW,SAAU,QAAS,SAAU,UAAW,QAAS,SAAU,MAAO,UAAW,SAAU,SAAU,SAAU,SAAU,SAAU,UAAW,UAAW,QAAS,UAAW,UAAW,UAAW,UAAW,SAAU,SAAU,OAAQ,WAAY,UAAW,MAAO,QAAS,UAAW,OAAQ,OAAQ,UAAW,SAAU,OAAQ,WAAY,OAAQ,WAAY,UAAW,SAAU,UAAW,SAAU,UAAW,UAAW,SAAU,SAAU,SAAU,UAAW,WAAY,QAAS,QAAS,QAAS,YAAa,WAAY,OAAQ,UAAW,WAAY,QAAS,QAAS,OAAQ,SAAU,OAAQ,OAAQ,SAAU,SAAU,WAAY,SAAU,OAAQ,SAAU,QAAS,SAAU,WAAY,SAAU,QAAS,OAAQ,SAAU,QAAS,SAAU,UAAW,SAAU,SAAU,OAAQ,QAAS,OAAQ,SAAU,WAAY,QAAS,UAAW,QAAS,QAAS,SAAU,QAAS,YAAa,UAAW,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,QAAS,UAAW,WAAY,SAAU,UAAW,UAAW,SAAU,SAAU,YAAa,UAAW,SAAU,OAAQ,QAAS,SAAU,OAAQ,OAAQ,OAAQ,WAAY,SAAU,QAAS,SAAU,UAAW,UAAW,OAAQ,SAAU,UAAW,QAAS,SAAU,UAAW,UAAW,SAAU,OAAQ,QAAS,UAAW,SAAU,QAAS,SAAU,aAAc,WAAY,SAAU,UAAW,SAAU,OAAQ,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,UAAW,YAAa,QAAS,SAAU,WAAY,SAAU,OAAQ,SAAU,SAAU,UAAW,SAAU,SAAU,UAAW,UAAW,OAAQ,QAAS,QAAS,QAAS,UAAW,OAAQ,QAAS,UAAW,OAAQ,WAAY,WAAY,UAAW,UAAW,WAAY,QAAS,QAAS,QAAS,aAAc,SAAU,QAAS,UAAW,WAAY,OAAQ,QAAS,OAAQ,WAAY,QAAS,UAAW,QAAS,SAAU,QAAS,UAAW,WAAY,UAAW,UAAW,cAAe,QAAS,QAAS,SAAU,UAAW,aAAc,YAAa,SAAU,WAAY,QAAS,WAAY,MAAO,UAAW,QAAS,YAAa,WAAY,QAAS,QAAS,QAAS,QAAS,OAAQ,OAAQ,QAAS,QAAS,OAAQ,YAAa,OAAQ,SAAU,SAAU,SAAU,UAAW,SAAU,OAAQ,UAAW,SAAU,QAAS,WAAY,SAAU,SAAU,WAAY,SAAU,OAAQ,OAAQ,aAAc,QAAS,QAAS,SAAU,SAAU,SAAU,YAAa,UAAW,OAAQ,QAAS,YAAa,QAAS,WAAY,UAAW,OAAQ,SAAU,UAAW,UAAW,UAAW,YAAa,OAAQ,UAAW,UAEj7JC,IAAO,SAAU,WAAY,WAAY,UAAW,QAAS,SAAU,WAAY,UAAW,SAAU,SAAU,UAAW,WAAY,QAAS,YAAa,aAAc,SAAU,WAAY,WAAY,SAAU,WAAY,YAAa,WAAY,QAAS,SAAU,WAAY,UAAW,UAAW,WAAY,QAAS,YAAa,QAAS,WAAY,WAAY,QAAS,UAAW,aAAc,QAAS,UAAW,YAAa,QAAS,UAAW,UAAW,QAAS,SAAU,aAAc,UAAW,OAAQ,WAAY,WAAY,aAAc,WAAY,WAAY,QAAS,WAAY,aAAc,aAAc,QAAS,aAAc,YAAa,SAAU,SAAU,UAAW,YAAa,aAAc,UAAW,YAAa,YAAa,aAAc,UAAW,SAAU,QAAS,WAAY,YAAa,aAAc,QAAS,UAAW,YAAa,SAAU,UAAW,WAAY,UAAW,QAAS,UAAW,WAAY,UAAW,YAAa,UAAW,OAAQ,SAAU,QAAS,UAAW,YAAa,cAAe,eAAgB,WAAY,aAAc,QAAS,UAAW,WAAY,QAAS,UAAW,QAAS,QAAS,UAAW,QAAS,UAAW,WAAY,YAAa,aAAc,UAAW,YAAa,SAAU,UAAW,UAAW,OAAQ,WAAY,QAAS,UAAW,QAAS,OAAQ,SAAU,WAAY,QAAS,SAAU,YAAa,WAAY,UAAW,WAAY,aAAc,QAAS,UAAW,UAAW,OAAQ,SAAU,SAAU,UAAW,SAAU,WAAY,WAAY,QAAS,WAAY,SAAU,SAAU,SAAU,WAAY,SAAU,UAAW,QAAS,WAAY,UAAW,SAAU,SAAU,WAAY,QAAS,WAAY,WAAY,WAAY,QAAS,QAAS,UAAW,UAAW,QAAS,cAAe,WAAY,UAAW,QAAS,UAAW,SAAU,WAAY,UAAW,WAAY,OAAQ,WAAY,SAAU,YAAa,UAAW,cAAe,UAAW,QAAS,WAAY,SAAU,WAAY,QAAS,eAAgB,WAAY,WAAY,WAAY,UAAW,UAAW,WAAY,SAAU,cAAe,WAAY,aAAc,UAAW,WAAY,SAAU,UAAW,UAAW,UAAW,UAAW,WAAY,QAAS,SAAU,UAAW,SAAU,YAAa,QAAS,SAAU,WAAY,SAAU,UAAW,SAAU,aAAc,aAAc,WAAY,cAAe,WAAY,UAAW,WAAY,cAAe,YAAa,YAAa,WAAY,SAAU,aAAc,aAAc,cAAe,aAAc,SAAU,WAAY,UAAW,MAAO,SAAU,QAAS,UAAW,WAAY,OAAQ,QAAS,SAAU,SAAU,SAAU,YAAa,UAAW,YAAa,QAAS,WAAY,OAAQ,QAAS,UAAW,YAAa,cAAe,cAAe,YAAa,SAAU,WAAY,WAAY,YAAa,SAAU,WAAY,QAAS,UAAW,QAAS,WAAY,SAAU,UAAW,cAAe,aAAc,QAAS,WAAY,SAAU,SAAU,SAAU,SAAU,UAAW,UAAW,OAAQ,UAAW,SAAU,QAAS,WAAY,UAAW,QAAS,UAAW,SAAU,SAAU,WAAY,WAAY,SAAU,UAAW,OAAQ,UAAW,UAAW,QAAS,YAAa,WAAY,WAAY,QAAS,QAAS,UAAW,UAAW,UAAW,QAAS,UAAW,QAAS,UAAW,OAAQ,QAAS,aAAc,aAAc,QAAS,SAAU,YAAa,SAAU,QAAS,UAAW,aAAc,eAAgB,UAAW,WAAY,QAAS,WAAY,SAAU,QAAS,UAAW,aAAc,UAAW,WAAY,UAAW,YAAa,YAAa,UAAW,iBAAkB,kBAAmB,MAAO,YAAa,SAAU,WAAY,aAAc,OAAQ,OAAQ,QAAS,QAAS,SAAU,UAAW,OAAQ,UAAW,QAAS,UAAW,SAAU,WAAY,cAAe,SAAU,WAAY,YAAa,WAAY,YAAa,UAAW,WAAY,QAAS,UAAW,SAAU,QAAS,WAAY,UAAW,UAAW,UAAW,UAAW,UAAW,YAAa,WAAY,UAAW,UAAW,UAAW,OAAQ,aAAc,WAAY,QAAS,UAAW,UAAW,SAAU,UAAW,UAAW,OAAQ,MAAO,WAAY,UAAW,UAAW,UAAW,WAAY,QAAS,UAAW,UAAW,UAAW,QAAS,UAAW,UAAW,YAAa,OAAQ,aAAc,QAAS,aAAc,UAAW,QAAS,UAAW,QAAS,UAAW,aAAc,UAAW,UAAW,WAAY,QAAS,QAAS,UAAW,UAAW,QAAS,cAAe,aAAc,eAAgB,UAAW,YAAa,SAAU,WAAY,YAAa,SAAU,aAAc,QAAS,UAAW,SAAU,SAAU,UAAW,UAAW,WAAY,QAAS,OAAQ,aAAc,YAAa,YAAa,UAAW,UAAW,WAAY,WAAY,QAAS,QAAS,UAAW,cAAe,UAAW,WAAY,aAAc,QAAS,YAAa,QAAS,QAAS,UAAW,UAAW,UAAW,OAAQ,SAAU,QAAS,QAAS,YAAa,SAAU,aAAc,WAAY,QAAS,YAAa,YAAa,SAAU,WAAY,WAAY,YAAa,YAAa,QAAS,WAAY,YAAa,SAAU,cAAe,cAAe,cAAe,aAAc,aAAc,WAAY,SAAU,WAAY,SAAU,WAAY,UAAW,OAAQ,QAAS,WAAY,SAAU,SAAU,WAAY,WAAY,QAAS,UAAW,SAAU,WAAY,SAAU,YAAa,YAAa,UAAW,YAAa,SAAU,SAAU,SAAU,WAAY,QAAS,WAAY,KAAM,QAAS,KAAM,QAAS,WAAY,UAAW,YAAa,MAAO,UAAW,MAAO,QAAS,UAAW,SAAU,OAAQ,SAAU,OAAQ,cAAe,YAAa,SAAU,UAAW,YAAa,QAAS,QAAS,WAAY,WAAY,QAAS,QAAS,QAAS,KAAM,OAAQ,MAAO,SAAU,QAAS,OAAQ,OAAQ,WAAY,aAAc,WAAY,QAAS,QAAS,UAAW,YAAa,SAAU,QAAS,KAAM,WAAY,YAAa,SAAU,OAAQ,SAAU,YAAa,SAAU,QAAS,UAAW,YAAa,SAAU,UAAW,WAAY,QAAS,WAAY,UAAW,YAAa,YAAa,QAAS,UAAW,UAAW,WAAY,UAAW,WAAY,QAAS,UAAW,WAAY,WAAY,UAAW,WAAY,WAAY,YAAa,SAAU,YAAa,aAAc,UAAW,WAAY,WAAY,OAAQ,UAAW,UAAW,UAAW,WAAY,YAAa,SAAU,SAAU,WAAY,UAAW,WAAY,aAAc,UAAW,UAAW,QAAS,OAAQ,SAAU,SAAU,SAAU,YAAa,SAAU,WAAY,YAAa,YAAa,WAAY,YAAa,UAAW,QAAS,WAAY,SAAU,WAAY,QAAS,UAAW,YAAa,UAAW,UAAW,WAAY,SAAU,UAAW,QAAS,SAAU,OAAQ,OAAQ,UAAW,aAAc,QAAS,UAAW,UAAW,UAAW,SAAU,YAAa,SAAU,UAAW,YAAa,aAAc,WAAY,aAAc,SAAU,UAAW,SAAU,SAAU,aAAc,WAAY,YAAa,cAAe,QAAS,aAAc,UAAW,UAAW,YAAa,UAAW,UAAW,WAAY,OAAQ,SAAU,SAAU,UAAW,SAAU,WAAY,UAAW,QAAS,UAAW,WAAY,QAAS,UAAW,WAAY,QAAS,UAAW,UAAW,SAAU,SAAU,WAAY,UAAW,WAAY,OAAQ,OAAQ,QAAS,WAAY,UAAW,YAAa,OAAQ,SAAU,YAAa,WAAY,UAAW,QAAS,OAAQ,SAAU,QAAS,WAAY,OAAQ,UAAW,YAAa,UAAW,SAAU,WAAY,YAAa,WAAY,OAAQ,OAAQ,SAAU,SAAU,SAAU,WAAY,UAAW,QAAS,UAAW,WAAY,SAAU,YAAa,UAAW,WAAY,UAAW,YAAa,UAAW,WAAY,aAAc,UAAW,UAAW,WAAY,QAAS,UAAW,OAAQ,SAAU,UAAW,UAAW,SAAU,SAAU,QAAS,UAAW,WAAY,UAAW,YAAa,WAAY,aAAc,OAAQ,SAAU,UAAW,UAAW,QAAS,WAAY,QAAS,UAAW,WAAY,UAAW,UAAW,YAAa,QAAS,SAAU,WAAY,WAAY,WAAY,SAAU,UAAW,WAAY,aAAc,cAAe,WAAY,aAAc,QAAS,UAAW,UAAW,WAAY,OAAQ,QAAS,QAAS,UAAW,WAAY,QAAS,SAAU,YAAa,UAAW,QAAS,WAAY,aAAc,OAAQ,YAAa,WAAY,QAAS,aAAc,UAAW,WAAY,UAAW,QAAS,UAAW,WAAY,OAAQ,WAAY,SAAU,SAAU,YAAa,QAAS,OAAQ,YAAa,YAAa,WAAY,UAAW,YAAa,SAAU,UAAW,QAAS,QAAS,UAAW,QAAS,YAAa,OAAQ,UAAW,UAAW,QAAS,UAAW,UAAW,WAAY,UAAW,QAAS,SAAU,WAAY,QAAS,YAAa,YAAa,SAAU,SAAU,QAAS,QAAS,SAAU,SAAU,WAAY,UAAW,SAAU,UAAW,OAAQ,WAAY,QAAS,UAAW,QAAS,UAAW,WAAY,QAAS,WAAY,WAAY,YAAa,SAAU,QAAS,UAAW,YAAa,UAAW,YAAa,cAAe,QAAS,UAAW,SAAU,OAAQ,QAAS,QAAS,UAAW,UAAW,UAAW,WAAY,QAAS,QAAS,QAAS,QAAS,SAAU,QAAS,YAAa,YAAa,YAAa,QAAS,QAAS,WAAY,SAAU,WAAY,SAAU,QAAS,UAAW,UAAW,QAAS,YAAa,YAAa,YAAa,YAAa,SAAU,QAAS,OAAQ,QAAS,UAAW,QAAS,UAAW,aAAc,UAAW,QAAS,WAAY,YAAa,YAAa,aAAc,UAAW,YAAa,SAAU,SAAU,SAAU,UAAW,SAAU,cAAe,YAAa,aAAc,OAAQ,SAAU,YAAa,SAAU,YAAa,WAAY,QAAS,UAAW,WAAY,UAAW,OAAQ,QAAS,QAAS,QAAS,QAAS,aAAc,aAAc,WAAY,SAAU,aAAc,UAAW,SAAU,UAAW,UAAW,OAAQ,OAAQ,QAAS,YAAa,UAAW,QAAS,SAAU,UAAW,QAAS,UAAW,UAAW,YAAa,aAAc,QAAS,UAAW,UAAW,WAAY,YAAa,WAAY,UAAW,UAAW,YAAa,SAAU,UAAW,QAAS,UAAW,QAAS,UAAW,UAAW,YAAa,WAAY,UAAW,QAAS,SAAU,SAAU,SAAU,OAAQ,UAAW,SAAU,QAAS,QAAS,OAAQ,KAAM,KAAM,OAAQ,KAAM,QAAS,OAAQ,UAAW,WAAY,SAAU,QAAS,QAAS,QAAS,OAAQ,MAAO,UAAW,OAAQ,SAEvrVC,IAAM,SAAU,SAAU,WAAY,QAAS,OAAQ,SAAU,QAAS,UAAW,OAAQ,OAAQ,OAAQ,QAAS,YAAa,OAAQ,OAAQ,MAAO,QAAS,QAAS,SAAU,QAAS,SAAU,UAAW,SAAU,UAAW,SAAU,QAAS,aAAc,QAAS,QAAS,SAAU,UAAW,UAAW,WAAY,WAAY,QAAS,QAAS,QAAS,QAAS,SAAU,YAAa,WAAY,WAAY,QAAS,cAAe,QAAS,OAAQ,OAAQ,YAAa,WAAY,UAAW,WAAY,SAAU,SAAU,UAAW,SAAU,SAAU,UAAW,SAAU,UAAW,UAAW,WAAY,QAAS,OAAQ,WAAY,MAAO,OAAQ,UAAW,WAAY,SAAU,SAAU,SAAU,QAAS,WAAY,SAAU,UAAW,OAAQ,SAAU,UAAW,MAAO,UAAW,OAAQ,SAAU,UAAW,SAAU,MAAO,WAAY,SAAU,cAAe,UAAW,SAAU,SAAU,UAAW,OAAQ,OAAQ,SAAU,QAAS,QAAS,YAAa,WAAY,QAAS,OAAQ,UAAW,SAAU,WAAY,SAAU,WAAY,WAAY,UAAW,WAAY,WAAY,QAAS,UAAW,YAAa,SAAU,SAAU,OAAQ,QAAS,OAAQ,WAAY,MAAO,WAAY,aAAc,MAAO,QAAS,SAAU,OAAQ,WAAY,WAAY,UAAW,UAAW,YAAa,UAAW,YAAa,UAAW,OAAQ,SAAU,QAAS,MAAO,WAAY,WAAY,UAAW,YAAa,YAAa,QAAS,UAAW,WAAY,QAAS,UAAW,UAAW,YAAa,WAAY,WAAY,WAAY,UAAW,UAAW,WAAY,UAAW,WAAY,YAAa,WAAY,WAAY,YAAa,WAAY,YAAa,SAAU,WAAY,UAAW,SAAU,WAAY,WAAY,YAAa,UAAW,WAAY,aAAc,YAAa,aAAc,WAAY,SAAU,aAAc,eAAgB,aAAc,YAAa,aAAc,eAAgB,cAAe,WAAY,WAAY,YAAa,YAAa,cAAe,aAAc,eAAgB,WAAY,aAAc,WAAY,YAAa,WAAY,kBAAmB,eAAgB,aAAc,gBAAiB,aAAc,eAAgB,gBAAiB,gBAAiB,gBAAiB,iBAAkB,kBAAmB,gBAAiB,gBAAiB,gBAAiB,eAAgB,iBAAkB,eAAgB,eAAgB,iBAAkB,gBAAiB,gBAAiB,eAAgB,eAAgB,gBAAiB,iBAAkB,gBAAiB,gBAIthFpJ,YAAa9G,KAAO,cAAcO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,oBAAoBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,uBAAuBO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,iCAAiCO,aAAe,OAAOP,KAAO,yBAAyBO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,wBAAwBO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,2BAA2BO,aAAe,OAAOP,KAAO,kBAAkBO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,0BAA0BO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,sBAAsBO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,qBAAqBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,oBAAoBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,8BAA8BO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,sBAAsBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,kBAAkBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,kBAAkBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,2BAA2BO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,0BAA0BO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,wBAAwBO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,kBAAkBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,yCAAyCO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,oBAAoBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,wBAAwBO,aAAe,OAAOP,KAAO,2BAA2BO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,uBAAuBO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAAOP,KAAO,cAAcO,aAAe,OAAOP,KAAO,OAAOO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,oBAAoBO,aAAe,OAAOP,KAAO,mBAAmBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,yBAAyBO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,wBAAwBO,aAAe,OAAOP,KAAO,sBAAsBO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,uBAAuBO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,gBAAgBO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,aAAaO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,eAAeO,aAAe,OAAOP,KAAO,YAAYO,aAAe,OAAOP,KAAO,UAAUO,aAAe,OAAOP,KAAO,kBAAkBO,aAAe,OAAOP,KAAO,iBAAiBO,aAAe,OAAOP,KAAO,QAAQO,aAAe,OAAOP,KAAO,SAASO,aAAe,OAAOP,KAAO,WAAWO,aAAe,OAE1zUyH,UAEJqI,KACKrQ,KAAM,iCACNA,KAAM,kBACNA,KAAM,aACNA,KAAM,UACNA,KAAM,YACNA,KAAM,0BACNA,KAAM,cACNA,KAAM,gBACNA,KAAM,qBACNA,KAAM,oBACNA,KAAM,YACNA,KAAM,oBACNA,KAAM,mBACNA,KAAM,oBACNA,KAAM,yBACNA,KAAM,eACNA,KAAM,kBACNA,KAAM,8BACNA,KAAM,qBACNA,KAAM,UACNA,KAAM,aACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,gBACNA,KAAM,kBACNA,KAAM,qBACNA,KAAM,uBACNA,KAAM,kBACNA,KAAM,YACNA,KAAM,eACNA,KAAM,iBACNA,KAAM,UACNA,KAAM,eACNA,KAAM,UACNA,KAAM,WACNA,KAAM,0BACNA,KAAM,WACNA,KAAM,iBACNA,KAAM,6BACNA,KAAM,gBACNA,KAAM,eACNA,KAAM,UACNA,KAAM,YACNA,KAAM,SACNA,KAAM,eACNA,KAAM,oBACNA,KAAM,mBACNA,KAAM,uBACNA,KAAM,UACNA,KAAM,YACNA,KAAM,WACNA,KAAM,cACNA,KAAM,eACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,cACNA,KAAM,SACNA,KAAM,kBACNA,KAAM,oBACNA,KAAM,SACNA,KAAM,eACNA,KAAM,cACNA,KAAM,mBACNA,KAAM,iBACNA,KAAM,YACNA,KAAM,UACNA,KAAM,WACNA,KAAM,eACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,UACNA,KAAM,YACNA,KAAM,4BACNA,KAAM,uBACNA,KAAM,mBACNA,KAAM,oBACNA,KAAM,qBACNA,KAAM,mBACNA,KAAM,eACNA,KAAM,oBACNA,KAAM,gBACNA,KAAM,kBACNA,KAAM,sBACNA,KAAM,iBACNA,KAAM,aACNA,KAAM,UACNA,KAAM,eACNA,KAAM,UACNA,KAAM,YACNA,KAAM,yBACNA,KAAM,YACNA,KAAM,qBACNA,KAAM,eACNA,KAAM,WACNA,KAAM,aACNA,KAAM,oBACNA,KAAM,0BACNA,KAAM,oBACNA,KAAM,gBACNA,KAAM,oBACNA,KAAM,kBACNA,KAAM,kBACNA,KAAM,qBACNA,KAAM,mBACNA,KAAM,gBACNA,KAAM,YACNA,KAAM,WACNA,KAAM,YACNA,KAAM,uBACNA,KAAM,aACNA,KAAM,WACNA,KAAM,kBACNA,KAAM,eACNA,KAAM,iBACNA,KAAM,mBACNA,KAAM,mBACNA,KAAM,iBACNA,KAAM,kBACNA,KAAM,gBACNA,KAAM,mBACNA,KAAM,kBACNA,KAAM,cACNA,KAAM,2BACNA,KAAM,cACNA,KAAM,mBACNA,KAAM,YACNA,KAAM,UAEfkI,WACIoI,KACKtQ,KAAM,UAAWO,aAAc,OAC/BP,KAAM,mBAAoBO,aAAc,OACxCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,gBAAiBO,aAAc,OACrCP,KAAM,4BAA6BO,aAAc,OACjDP,KAAM,cAAeO,aAAc,OACnCP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,uBAAwBO,aAAc,OAC5CP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,eAAgBO,aAAc,OAKpCP,KAAM,wBAAyBO,aAAc,OAC7CP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,QAASO,aAAc,OAElC0P,KACMjQ,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,cAAeO,aAAc,KAAM8K,KAAM,IAC/CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,IACzCrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,IACxCrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,KACxCrL,KAAM,wBAAyBO,aAAc,KAAM8K,KAAM,KACzDrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,aAAcO,aAAc,KAAM8K,KAAM,KAC9CrL,KAAM,oBAAqBO,aAAc,KAAM8K,KAAM,KACrDrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,KACxCrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,MAC3CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,IACzCrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,KACxCrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,eAAgBO,aAAc,KAAM8K,KAAM,KAChDrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,IAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,KACxCrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,kBAAmBO,aAAc,KAAM8K,KAAM,KACnDrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,IAC1CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,eAAgBO,aAAc,KAAM8K,KAAM,KAChDrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,OAAQO,aAAc,KAAM8K,KAAM,KACxCrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,YAAaO,aAAc,KAAM8K,KAAM,KAC7CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,MACzCrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,kBAAmBO,aAAc,KAAM8K,KAAM,KACnDrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,KACjDrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,OAAQO,aAAc,OAAQ8K,KAAM,KAC1CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,IAC1CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,IAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,QAASO,aAAc,KAAM8K,KAAM,KACzCrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,KAC5CrL,KAAM,WAAYO,aAAc,KAAM8K,KAAM,IAC5CrL,KAAM,SAAUO,aAAc,KAAM8K,KAAM,KAC1CrL,KAAM,gBAAiBO,aAAc,KAAM8K,KAAM,MACjDrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,KAC3CrL,KAAM,UAAWO,aAAc,KAAM8K,KAAM,MAKrDnK,gBACIlB,KAAM,WACNA,KAAM,aACNA,KAAM,aACNA,KAAM,aACNA,KAAM,aACNA,KAAM,YACNA,KAAM,cACNA,KAAM,gBACNA,KAAM,aACNA,KAAM,eACNA,KAAM,aACNA,KAAM,gBACNA,KAAM,WACNA,KAAM,aACNA,KAAM,gBACNA,KAAM,cACNA,KAAM,cACNA,KAAM,aACNA,KAAM,eACNA,KAAM,YACNA,KAAM,aACNA,KAAM,aACNA,KAAM,cACNA,KAAM,aACNA,KAAM,YACNA,KAAM,cACNA,KAAM,YACNA,KAAM,aACNA,KAAM,cACNA,KAAM,cACNA,KAAM,YACNA,KAAM,cACNA,KAAM,cACNA,KAAM,gBACNA,KAAM,aACNA,KAAM,iBACNA,KAAM,oBACNA,KAAM,YACNA,KAAM,YACNA,KAAM,YACNA,KAAM,cACNA,KAAM,YACNA,KAAM,cACNA,KAAM,gBACNA,KAAM,aACNA,KAAM,UACNA,KAAM,YACNA,KAAM,UACNA,KAAM,WACNA,KAAM,aACNA,KAAM,cACNA,KAAM,UACNA,KAAM,kBACNA,KAAM,eACNA,KAAM,aACNA,KAAM,YACNA,KAAM,uBACNA,KAAM,aACNA,KAAM,aACNA,KAAM,cACNA,KAAM,WACNA,KAAM,aACNA,KAAM,YACNA,KAAM,WACNA,KAAM,aACNA,KAAM,YACNA,KAAM,aACNA,KAAM,WACNA,KAAM,aACNA,KAAM,UACNA,KAAM,cACNA,KAAM,eACNA,KAAM,oBACNA,KAAM,YACNA,KAAM,aACNA,KAAM,YACNA,KAAM,kBACNA,KAAM,aACNA,KAAM,cACNA,KAAM,eACNA,KAAM,cACNA,KAAM,WACNA,KAAM,eACNA,KAAM,YACNA,KAAM,UACNA,KAAM,UACNA,KAAM,YACNA,KAAM,YACNA,KAAM,YACNA,KAAM,aACNA,KAAM,aACNA,KAAM,cACNA,KAAM,gBACNA,KAAM,WACNA,KAAM,yBACNA,KAAM,YACNA,KAAM,WACNA,KAAM,YACNA,KAAM,YACNA,KAAM,aACNA,KAAM,aACNA,KAAM,WACNA,KAAM,oBACNA,KAAM,eACNA,KAAM,iBACNA,KAAM,eACNA,KAAM,aACNA,KAAM,aACNA,KAAM,cACNA,KAAM,aACNA,KAAM,WACNA,KAAM,YACNA,KAAM,gBACNA,KAAM,gBACNA,KAAM,cACNA,KAAM,YACNA,KAAM,gBACNA,KAAM,aACNA,KAAM,YACNA,KAAM,cACNA,KAAM,aACNA,KAAM,YACNA,KAAM,aACNA,KAAM,eACNA,KAAM,aACNA,KAAM,YACNA,KAAM,aACNA,KAAM,kBACNA,KAAM,eACNA,KAAM,aACNA,KAAM,aACNA,KAAM,iBACNA,KAAM,mBACNA,KAAM,cACNA,KAAM,UACNA,KAAM,cACNA,KAAM,YACNA,KAAM,eACNA,KAAM,sBACNA,KAAM,eACNA,KAAM,aACNA,KAAM,WACNA,KAAM,eACNA,KAAM,WACNA,KAAM,WACNA,KAAM,YACNA,KAAM,YACNA,KAAM,iBACNA,KAAM,eACNA,KAAM,WACNA,KAAM,iBACNA,KAAM,eACNA,KAAM,UACNA,KAAM,aACNA,KAAM,eACNA,KAAM,YACNA,KAAM,gBACNA,KAAM,mBACNA,KAAM,gBACNA,KAAM,cACNA,KAAM,cACNA,KAAM,qBACNA,KAAM,WACNA,KAAM,kBACNA,KAAM,iBACNA,KAAM,YACNA,KAAM,eACNA,KAAM,aACNA,KAAM,cACNA,KAAM,UACNA,KAAM,YACNA,KAAM,UACNA,KAAM,WACNA,KAAM,cACNA,KAAM,UACNA,KAAM,cACNA,KAAM,SACNA,KAAM,aACNA,KAAM,WACNA,KAAM,8BACNA,KAAM,aACNA,KAAM,YACNA,KAAM,aACNA,KAAM,YACNA,KAAM,cACNA,KAAM,aACNA,KAAM,gBACNA,KAAM,cACNA,KAAM,eACNA,KAAM,SACNA,KAAM,YACNA,KAAM,WACNA,KAAM,aAGVuQ,kBACE,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,MAIFC,gBACE,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,SACA,WACA,cACA,WACA,cACA,SACA,SACA,SACA,SACA,WACA,cACA,WACA,cACA,WACA,cACA,SACA,SACA,SACA,SACA,SACA,QACA,QACA,QACA,QACA,SACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,UACA,aACA,QACA,QACA,UACA,aACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,UACA,aACA,QACA,QACA,iBACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,SACA,SACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,cACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,SACA,SACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,aACA,aACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,QACA,UACA,aACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,QACA,UACA,aACA,aACA,aACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,UACA,aACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,aACA,aACA,aACA,UACA,aACA,aACA,aACA,aACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,UACA,aACA,QACA,QACA,QACA,UACA,aACA,UACA,aACA,UACA,aACA,QACA,SACA,QACA,QACA,UACA,aACA,aACA,aACA,aACA,UACA,aACA,aACA,aACA,SAGFlI,mBACKtI,KAAM,UAAWO,aAAc,OAC/BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,aAAcO,aAAc,OAClCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,cAAeO,aAAc,OACnCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,uBAAwBO,aAAc,OAC5CP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,YAAaO,aAAc,OACjCP,KAAM,QAASO,aAAc,OAC7BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,gBAAiBO,aAAc,OACrCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,YAAaO,aAAc,OACjCP,KAAM,cAAeO,aAAc,OACnCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,gBAAiBO,aAAc,OACrCP,KAAM,aAAcO,aAAc,OAClCP,KAAM,aAAcO,aAAc,OAClCP,KAAM,WAAYO,aAAc,OAChCP,KAAM,iBAAkBO,aAAc,OACtCP,KAAM,eAAgBO,aAAc,OACpCP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,eAAgBO,aAAc,OACpCP,KAAM,eAAgBO,aAAc,OACpCP,KAAM,iBAAkBO,aAAc,OACtCP,KAAM,eAAgBO,aAAc,OACpCP,KAAM,YAAaO,aAAc,OACjCP,KAAM,QAASO,aAAc,OAC7BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,UAAWO,aAAc,OAC/BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,aAAcO,aAAc,OAClCP,KAAM,gBAAiBO,aAAc,OACrCP,KAAM,YAAaO,aAAc,OACjCP,KAAM,UAAWO,aAAc,OAGpCgI,cACKvI,KAAM,iBAAkBO,aAAc,OACtCP,KAAM,iCAAkCO,aAAc,OACtDP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,mBAAoBO,aAAc,OACxCP,KAAM,2BAA4BO,aAAc,OAChDP,KAAM,cAAeO,aAAc,OACnCP,KAAM,uBAAwBO,aAAc,OAGjDiI,eACKxI,KAAM,sBAAuBO,aAAc,OAC3CP,KAAM,uBAAwBO,aAAc,OAC5CP,KAAM,4BAA6BO,aAAc,OAGtDkQ,iBACIR,KACMjQ,KAAM,gBAAiBO,aAAc,QACrCP,KAAM,WAAYO,aAAc,QAChCP,KAAM,YAAaO,aAAc,QACjCP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,sBAAuBO,aAAc,QAC3CP,KAAM,wBAAyBO,aAAc,QAC7CP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,iBAAkBO,aAAc,QACtCP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,WAAYO,aAAc,QAChCP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,aAAcO,aAAc,QAClCP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,WAAYO,aAAc,QAChCP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,WAAYO,aAAc,SAI1CoI,iBACI+H,KACK1Q,KAAM,SAAUO,aAAc,QAC9BP,KAAM,YAAaO,aAAc,SACjCP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,YAAaO,aAAc,QACjCP,KAAM,OAAQO,aAAc,QAC5BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,WAAYO,aAAc,QAChCP,KAAM,MAAOO,aAAc,QAC3BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,WAAYO,aAAc,SAChCP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,MAAOO,aAAc,QAEhC0P,KACMjQ,KAAM,UAAWO,aAAc,SAC/BP,KAAM,SAAUO,aAAc,SAC9BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,aAAcO,aAAc,SAClCP,KAAM,QAASO,aAAc,UAC7BP,KAAM,SAAUO,aAAc,WAC9BP,KAAM,SAAUO,aAAc,WAC9BP,KAAM,SAAUO,aAAc,WAC9BP,KAAM,WAAYO,aAAc,SAChCP,KAAM,WAAYO,aAAc,SAChCP,KAAM,YAAaO,aAAc,UACjCP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,QAASO,aAAc,SAC7BP,KAAM,SAAUO,aAAc,SAC9BP,KAAM,QAASO,aAAc,UAC7BP,KAAM,YAAaO,aAAc,SACjCP,KAAM,QAASO,aAAc,SAC7BP,KAAM,SAAUO,aAAc,SAC9BP,KAAM,UAAWO,aAAc,UAC/BP,KAAM,UAAWO,aAAc,YAC/BP,KAAM,cAAeO,aAAc,SACnCP,KAAM,aAAcO,aAAc,QAClCP,KAAM,mBAAoBO,aAAc,QACxCP,KAAM,aAAcO,aAAc,SAClCP,KAAM,WAAYO,aAAc,SAChCP,KAAM,QAASO,aAAc,SAC7BP,KAAM,QAASO,aAAc,SAC7BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,cAAeO,aAAc,SACnCP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,aAAcO,aAAc,SAClCP,KAAM,QAASO,aAAc,SAC7BP,KAAM,WAAYO,aAAc,QAChCP,KAAM,QAASO,aAAc,QAC7BP,KAAM,QAASO,aAAc,SAC7BP,KAAM,YAAaO,aAAc,SACjCP,KAAM,YAAaO,aAAc,cACjCP,KAAM,WAAYO,aAAc,YAChCP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,oBAAqBO,aAAc,gBACzCP,KAAM,cAAeO,aAAc,UACnCP,KAAM,SAAUO,aAAc,UAC9BP,KAAM,WAAYO,aAAc,SAChCP,KAAM,QAASO,aAAc,SAC7BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,QAASO,aAAc,UAC7BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,QAASO,aAAc,SAC7BP,KAAM,MAAOO,aAAc,QAC3BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,OAAQO,aAAc,SAC5BP,KAAM,QAASO,aAAc,UAC7BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,UAAWO,aAAc,UAC/BP,KAAM,SAAUO,aAAc,SAC9BP,KAAM,YAAaO,aAAc,UACjCP,KAAM,UAAWO,aAAc,UAC/BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,eAAgBO,aAAc,UACpCP,KAAM,SAAUO,aAAc,SAC9BP,KAAM,WAAYO,aAAc,WAChCP,KAAM,YAAaO,aAAc,YACjCP,KAAM,WAAYO,aAAc,UAChCP,KAAM,MAAOO,aAAc,OAC3BP,KAAM,QAASO,aAAc,SAC7BP,KAAM,WAAYO,aAAc,WAChCP,KAAM,SAAUO,aAAc,SAEpC8P,KACKrQ,KAAM,SAAUO,aAAc,QAC9BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,WAAYO,aAAc,OAChCP,KAAM,QAASO,aAAc,OAC7BP,KAAM,SAAUO,aAAc,QAC9BP,KAAM,UAAWO,aAAc,SAC/BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,QAASO,aAAc,OAC7BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,QAASO,aAAc,QAC7BP,KAAM,OAAQO,aAAc,OAC5BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,SAAUO,aAAc,OAC9BP,KAAM,UAAWO,aAAc,QAC/BP,KAAM,SAAUO,aAAc,SAIvCwJ,SACK/J,KAAM,UAAW0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KAEzDtJ,KAAM,WAAY0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KAC1DtJ,KAAM,QAAS0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACvDtJ,KAAM,QAAS0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACvDtJ,KAAM,MAAO0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACrDtJ,KAAM,OAAQ0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACtDtJ,KAAM,OAAQ0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACtDtJ,KAAM,SAAU0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACxDtJ,KAAM,YAAa0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KAC3DtJ,KAAM,UAAW0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KACzDtJ,KAAM,WAAY0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KAC1DtJ,KAAM,WAAY0K,WAAY,MAAOnB,QAAS,KAAMD,KAAM,KAI/DkB,WACKxK,KAAM,mBAAoB0K,WAAY,OAAQvK,OAAQ,KAAM3L,OAAQ,KACpEwL,KAAM,WAAY0K,WAAY,WAAYvK,OAAQ,OAAQ3L,OAAQ,KAClEwL,KAAM,iBAAkB0K,WAAY,aAAcvK,OAAQ,KAAM3L,OAAQ,KACxEwL,KAAM,4BAA6B0K,WAAY,UAAWvK,OAAQ,MAAO3L,OAAQ,KACjFwL,KAAM,sBAAuB0K,WAAY,YAAavK,OAAQ,OAAQ3L,OAAQ,KAC9EwL,KAAM,4BAA6B0K,WAAY,SAAUvK,OAAQ,KAAM3L,OAAQ,KAC/EwL,KAAM,qCAAsC0K,WAAY,QAASvK,OAAQ,KAAM3L,OAAQ,KACvFwL,KAAM,gBAAiB0K,WAAY,WAAYvK,OAAQ,OAAQ3L,OAAQ,KACvEwL,KAAM,eAAgB0K,WAAY,WAAYvK,OAAQ,MAAO3L,OAAQ,KACrEwL,KAAM,MAAO0K,WAAY,MAAOvK,OAAQ,OAAQ3L,OAAQ,KACxDwL,KAAM,QAAS0K,WAAY,QAASvK,OAAQ,OAAQ3L,OAAQ,KAC5DwL,KAAM,UAAW0K,WAAY,UAAWvK,OAAQ,OAAQ3L,OAAQ,KAChEwL,KAAM,aAAc0K,WAAY,KAAMvK,OAAQ,KAAM3L,OAAQ,KAC5DwL,KAAM,OAAQ0K,WAAY,OAAQvK,OAAQ,OAAQ3L,OAAQ,KAC1DwL,KAAM,SAAU0K,WAAY,SAAUvK,OAAQ,OAAQ3L,OAAQ,KAC9DwL,KAAM,OAAQ0K,WAAY,OAAQvK,OAAQ,IAAK3L,OAAQ,KACvDwL,KAAM,gBAAiB0K,WAAY,WAAYvK,OAAQ,OAAQ3L,OAAQ,KAI5EmW,iBACKU,KAAS,MAAOrL,KAAS,gCACzBqL,KAAS,MAAOrL,KAAS,wBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,iCACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,yBACzBqL,KAAS,MAAOrL,KAAS,6CACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,6BACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,yBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,eACzBqL,KAAS,MAAOrL,KAAS,wBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,cACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,4BACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,sCACzBqL,KAAS,MAAOrL,KAAS,yBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,eACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,eACzBqL,KAAS,MAAOrL,KAAS,cACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,cACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,aACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,yBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,uCACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,cACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,2BACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,qBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,gCACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,wBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,uBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,gBACzBqL,KAAS,MAAOrL,KAAS,+BACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,oBACzBqL,KAAS,MAAOrL,KAAS,yBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,mBACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,iBACzBqL,KAAS,MAAOrL,KAAS,eACzBqL,KAAS,MAAOrL,KAAS,0DACzBqL,KAAS,MAAOrL,KAAS,0BACzBqL,KAAS,MAAOrL,KAAS,6DACzBqL,KAAS,MAAOrL,KAAS,kDACzBqL,KAAS,MAAOrL,KAAS,gDACzBqL,KAAS,MAAOrL,KAAS,eACzBqL,KAAS,MAAOrL,KAAS,sBACzBqL,KAAS,MAAOrL,KAAS,kBACzBqL,KAAS,MAAOrL,KAAS,oBAI9B2Q,YAAgB,YAAa,QAAS,OAAQ,WAAY,aAAc,OAAQ,YAAa,QAAS,OAAQ,WAAY,cAAe,gBAAiB,oBAAqB,OAAQ,cACnL,OAAQ,OAAQ,eAAgB,aAAc,gBAAiB,cAAe,WAAY,gBAAiB,YAAa,iBAAkB,YAAa,YAAa,YAAa,gBAAiB,kBAClM,SAAU,iBAAkB,YAAa,iBAAkB,gBAAiB,mBAAoB,UAAW,YAAa,YAAa,YAAa,iBAAkB,kBAAmB,YAAa,aACpM,aAAc,SAAU,SAAU,QAAS,OAAQ,UAAW,eAAgB,aAAc,UAAW,cAAe,cAAe,QAAS,QAC9I,eAAgB,aAAc,eAAgB,aAAc,YAAa,aAAc,cAAe,SAAU,QAAS,WAAY,YAAa,cAAe,gBAAiB,iBAAkB,aACpM,YAAa,gBAAiB,eAAgB,YAAa,YAAa,SAAU,kBAAmB,YAAa,OAAQ,YAAa,MAAO,YAAa,UAAW,SAAU,YAAa,gBAC7L,UAAW,YAAa,OAAQ,YAAa,YAAa,WAAY,aAAc,SAAU,gBAAiB,aAAc,QAAS,YAAa,WAAY,QAAS,aAAc,QAAS,QAAS,aACxM,YAAa,aAAc,SAAU,eAAgB,QAAS,uBAAwB,UAAW,MAAO,UAAW,UAAW,WAAY,YAAa,SAAU,UAAW,QAAS,aAAc,cAAe,SAClN,YAAa,OAAQ,OAAQ,YAAa,cAAe,WAAY,SAAU,YAAa,iBAAkB,aAAc,gBAAiB,WAAY,WAAY,eAAgB,cAAe,OAAQ,SAAU,eAI1NrS,SAAW,YACX,aACA,oBACA,sBACA,0BACA,8BACA,2BACA,yBACA,gBACA,iCACA,+BACA,sCACA,oBACA,6BACA,uBACA,2BACA,+BACA,mBACA,mCACA,sBACA,aACA,qCACA,qBACA,mBACA,6BACA,aACA,8CACA,mCACA,iBACA,eACA,+BACA,yBACA,yBACA,oBACA,aACA,wBACA,yBACA,sCACA,iBACA,eACA,6BACA,gCACA,kCACA,2BACA,qBACA,yBACA,mBACA,yBACA,wCACA,2BACA,SACA,qBACA,oCACA,+CACA,kCACA,wCACA,2BACA,iCACA,iCACA,qCACA,mCACA,qCACA,gCACA,+BACA,aACA,yBACA,kBACA,mBACA,eACA,iCACA,uBACA,iCACA,6BACA,iBACA,eACA,uBACA,kBACA,qBACA,uBACA,sBACA,wCACA,0BACA,eACA,sBACA,kBACA,iCACA,4BACA,2BACA,0BACA,qBACA,eACA,gCACA,aACA,oBACA,2BACA,uBACA,gBACA,kCACA,mBACA,iBACA,aACA,6BACA,qBACA,cACA,sBACA,4BACA,mBACA,8BACA,qCACA,uBACA,wBACA,oBACA,uBACA,6BACA,4BACA,mBACA,kCACA,yBACA,wBACA,gCACA,yBACA,aACA,0BACA,wBACA,aACA,sBACA,8BACA,0BACA,qBACA,8BACA,4BACA,iBACA,sBACA,4BACA,iCACA,0BACA,qBACA,qBACA,4BACA,sBACA,kBACA,gCACA,uBACA,gCACA,oBACA,8BACA,+BACA,kBACA,2BACA,2BACA,wBACA,qBACA,gDACA,8BACA,2CACA,4BACA,gCACA,2BACA,aACA,+BACA,gBACA,oBACA,8BACA,uBACA,mBACA,qBACA,6BACA,8BACA,mBACA,kBACA,YACA,4BACA,iBACA,eACA,oCACA,eACA,kBACA,iBACA,sBACA,4BACA,uBACA,wBACA,8BACA,6BACA,sBACA,qCACA,aACA,cACA,aACA,6BACA,gBACA,eACA,2BACA,qBACA,iBACA,8BACA,uBACA,oCACA,iBACA,YACA,mBACA,WACA,gBACA,6BACA,wBACA,yBACA,gBACA,gBACA,gBACA,2BACA,wBACA,gCACA,yBACA,yCACA,0BACA,kBACA,2BACA,qBACA,mBACA,gBACA,aACA,eACA,iCACA,2BACA,4BACA,kCACA,4BACA,kBACA,uBACA,yBACA,2BACA,mCACA,eACA,yBACA,qCACA,4BACA,0BACA,YACA,iBACA,6BACA,iBACA,YACA,eACA,YACA,wBACA,iCACA,mBACA,mBACA,sBACA,0BACA,cACA,qBACA,kBACA,qBACA,4BACA,eACA,uBACA,qBACA,2BACA,yBACA,mBACA,uBACA,iBACA,qBACA,0BACA,6BACA,2BACA,2BACA,qBACA,0BACA,uBACA,4BACA,WACA,WACA,iCACA,oBACA,iBACA,sCACA,mBACA,wBACA,+BACA,UACA,cACA,sBACA,uBACA,kBACA,2BACA,wBACA,oBACA,sCACA,cACA,uBACA,WACA,sBACA,uBACA,gCACA,wBACA,kBACA,mBACA,uBACA,iCACA,0BACA,0BACA,wBACA,cACA,sBACA,oCACA,sBACA,eACA,2BACA,iCACA,sCACA,8BACA,qBACA,qBACA,8CACA,uBACA,yBACA,6CACA,4BACA,2BACA,sBACA,mCACA,wBACA,4BACA,cACA,mCACA,sBACA,iCACA,mBACA,kCACA,iCACA,oBACA,cACA,uCACA,4BACA,6BACA,yBACA,qBACA,gBACA,aACA,WACA,2BACA,kBACA,gBACA,gBACA,2BACA,sBACA,sBACA,iBACA,0BACA,sCACA,oBACA,sCACA,oBACA,WACA,eACA,mBACA,sCACA,eACA,4BACA,+BACA,2BACA,oBACA,6BACA,+BACA,cACA,+BACA,wBACA,eACA,sBACA,2BACA,8BACA,mBACA,iBACA,4BACA,oCACA,2BACA,uBACA,qCACA,oCACA,gCACA,mCACA,2BACA,6BACA,0BACA,yBACA,0BACA,sBACA,iBACA,sBACA,qBACA,kBACA,uBACA,uCACA,8BACA,eACA,eACA,yCACA,cACA,oCACA,WACA,oCACA,kBACA,mBACA,oBACA,gBACA,qBACA,sBACA,0BACA,iBACA,8BACA,sBACA,gCACA,kBACA,sBACA,+BACA,qBACA,sBACA,gCACA,6BACA,iBACA,eACA,qBACA,cACA,6BACA,eACA,kBACA,6BACA,2BACA,kBACA,yBACA,kBACA,oBACA,oBACA,2BACA,sCACA,oBACA,0BACA,sCACA,kCACA,wCACA,0CACA,uCACA,cACA,gBACA,sBACA,aACA,oCACA,iBACA,0BACA,qBACA,uBACA,gCACA,qBACA,sBACA,uCACA,oBACA,wBACA,2BACA,UACA,kBACA,mBACA,sBACA,cACA,kBACA,yBACA,UACA,gBACA,6BACA,6BACA,0BACA,yBACA,eACA,qBACA,eACA,uBACA,aACA,mCACA,6CACA,2BACA,mCACA,kBACA,uBACA,gBACA,mBACA,kBACA,uBACA,gCACA,qBACA,4BACA,8BACA,qBACA,6BACA,eACA,+BACA,wBACA,qBACA,qBACA,8BACA,oBACA,gCACA,gCACA,wBACA,wBACA,sBACA,2BACA,2BACA,4BACA,yBACA,gCACA,iBACA,wBACA,kBACA,gBACA,2BACA,2BACA,qBACA,8BACA,kCACA,0BACA,gCACA,iCACA,oBACA,wBACA,cACA,cACA,iCACA,qBACA,cACA,oCACA,qBACA,mBACA,mCACA,yBACA,iCACA,uBACA,yBACA,uBACA,2BACA,2BACA,iBACA,+BACA,2BACA,oBACA,8BACA,2BACA,wBACA,kBACA,eACA,uBACA,8BACA,aACA,uBACA,yBACA,wBACA,gBACA,4BACA,qBACA,yBACA,qBACA,sBACA,mCACA,gBACA,iBACA,yBACA,wBACA,uBACA,qBACA,sBACA,0CACA,4BACA,uBACA,2DACA,qCACA,kCACA,qCACA,kBACA,+BACA,mCACA,yBACA,wBACA,6BACA,4BACA,YACA,WACA,eACA,mBACA,gBACA,+BACA,aACA,gCACA,6BACA,6BACA,+BACA,2BACA,uBACA,QACA,mBACA,aACA,cACA,UACA,0BACA,4BACA,mBACA,oBACA,gBACA,kBACA,yBACA,sBACA,mCACA,aACA,eACA,eACA,gBACA,wBACA,YACA,cACA,qBACA,0BACA,qBACA,gBACA,qBACA,0BACA,aACA,gCACA,6BACA,cACA,YACA,aACA,gCACA,wBACA,uBACA,cACA,yBACA,mBACA,gCACA,cACA,uBACA,iBACA,6BACA,6BACA,4BACA,qBACA,eACA,6BACA,cACA,kBACA,qBACA,6BACA,2BACA,eACA,aACA,+BACA,iBACA,gBACA,qBACA,+BACA,wBACA,4BACA,wBACA,sBACA,8BACA,6BACA,oCACA,oBACA,wBACA,kCACA,oCACA,oBACA,0BACA,yBACA,eACA,cACA,gBACA,qBACA,WACA,cACA,2BACA,eACA,0BACA,eACA,iCACA,uBACA,2BACA,sBACA,0BACA,uBACA,4BACA,4BACA,4BACA,wBACA,uCACA,4BACA,oBACA,kBACA,eACA,uBACA,eACA,yBACA,eACA,0BACA,gCACA,gCACA,gCACA,0BACA,+BACA,mBACA,mCACA,4BACA,0BACA,oBACA,gCACA,sBACA,oBACA,wBACA,aACA,uBACA,gBACA,yBACA,cACA,iCACA,eACA,0BACA,uBACA,kBACA,kBACA,WACA,eACA,mBACA,mBACA,oBACA,sBACA,cACA,6BACA,cACA,WACA,kBACA,gBACA,yBACA,cACA,uBACA,kBACA,mBACA,kCACA,yBACA,iBACA,gBACA,kBACA,qBACA,gBACA,aACA,8BACA,mBACA,iBACA,2BACA,oBACA,qBACA,6BACA,2BACA,uBACA,uBACA,2BACA,kBACA,0BACA,uBACA,+BACA,cACA,iBACA,cACA,wBACA,sBACA,mBACA,yBACA,mBACA,wBACA,qBACA,yBACA,qBACA,gBACA,uBACA,eACA,WACA,uBACA,gBACA,qBACA,+BACA,yBACA,gBACA,cACA,iBACA,0CACA,qBACA,6BACA,gBACA,iBACA,mCACA,oCACA,yBACA,iCACA,eACA,4BACA,wBACA,4BACA,cACA,qBACA,gBACA,iCACA,0BACA,0BACA,aACA,gBACA,eACA,wBACA,kBACA,4BACA,gBACA,wBACA,+BACA,eACA,oBACA,+BACA,0BACA,eACA,aACA,yBACA,wBACA,iCACA,cACA,8BACA,6BACA,eACA,iBACA,oBACA,oBACA,oBACA,wBACA,eACA,wBACA,kBACA,kCACA,qBACA,iBACA,gCACA,sBACA,kBACA,yBACA,yBACA,qCACA,sBACA,UACA,WACA,kBACA,eACA,uBACA,kBACA,kBACA,8BACA,4BACA,sBACA,wBACA,qBACA,wBACA,iCACA,4BACA,qBACA,wBACA,kCACA,kCACA,cACA,wBACA,gCACA,gCACA,qBACA,YACA,4BACA,kBACA,uBACA,kBACA,kBACA,2BACA,kBACA,UACA,4BACA,sBACA,mCACA,0BACA,sBACA,+BACA,6BACA,iBACA,aACA,YACA,uBACA,6BACA,sBACA,gCACA,2BACA,2BACA,kBACA,oBACA,uBACA,uBACA,cACA,gCACA,sBACA,iBACA,wBACA,wBACA,sBACA,uBACA,aACA,gCACA,oBACA,gCACA,wBACA,4BACA,yBACA,0BACA,sBACA,4BACA,wBACA,sBACA,mBACA,wBACA,yBACA,6BACA,yBACA,sBACA,wBACA,+BACA,wBACA,kCACA,eACA,6BACA,4BACA,QACA,4BACA,kBACA,aACA,aACA,wBACA,qBACA,0BACA,kBACA,mBACA,wBAGAsD,eACIgP,QAAe,MAAO,MAAO,MAAO,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,QAC/EC,QAAe,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,OAC/GC,MAAe,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,QAAS,QAAS,MAAO,MAAO,MAAO,MAAO,MAAO,KAAM,MAAO,MAAO,IAAK,OACxIC,UAAe,MAAO,OAAQ,MAAO,OAAQ,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAAS,QAI3GlG,YAEY7K,KAAQ,yBACRgR,KAAQ,MACRC,QAAW,GACXC,OAAS,EACTxU,KAAQ,2CACRyU,KACE,gBAIFnR,KAAQ,SACRgR,KAAQ,IACRC,QAAW,GACXC,OAAS,EACTxU,KAAQ,4CACRyU,KACE,aACA,iBACA,eACA,uBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,QAAW,GACXC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,aACA,mBACA,mBACA,oBACA,oBAIFnR,KAAQ,wBACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,oBACA,iBACA,eACA,gBACA,qBAIFnR,KAAQ,iCACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,8BACRyU,KACE,0BAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,yCACRyU,KACE,iBACA,sBACA,kBACA,oBACA,qBACA,aAIFnR,KAAQ,4BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,kBACA,uBACA,qBACA,kBACA,eAIFnR,KAAQ,kCACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,0CACRyU,KACE,oBACA,sBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,0CACRyU,KACE,gBACA,wBACA,iBACA,mBACA,iBACA,kBACA,sBACA,aAIFnR,KAAQ,gCACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,8BACRyU,KACE,iBACA,qBACA,sBACA,oBACA,kBACA,sBACA,YACA,uBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,yCACRyU,KACE,kBACA,uBACA,4BACA,oBACA,oBACA,8BACA,8BACA,iCACA,sBACA,uBACA,mBACA,mBACA,aAIFnR,KAAQ,iCACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,kDACRyU,KACE,yBACA,iBACA,iBACA,sBACA,uBAIFnR,KAAQ,+BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,2BACRyU,KACE,iBACA,2BAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,kCACRyU,KACE,iBACA,iBACA,wBACA,mBACA,oBACA,kBACA,eACA,iBACA,qBACA,eAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,yCACRyU,KACE,kBACA,iBACA,6BACA,4BACA,0BACA,kBACA,8BACA,qBACA,mBACA,iBACA,mBACA,kBACA,sBACA,yBACA,sBACA,kBACA,aAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,6BACRyU,KACE,0BACA,wBACA,0BAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,QAAW,IACXC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,qBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,sBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,qCACRyU,KACE,oBACA,oBACA,kBACA,kBACA,gBACA,sBAIFnR,KAAQ,kCACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,uBACA,oBAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,mDACRyU,KACE,mBACA,kBACA,gBACA,mBACA,uBACA,oBACA,kBACA,mBACA,qBACA,kBACA,qBACA,iBACA,qBACA,iBACA,wBACA,iBACA,kBACA,qBACA,qBACA,wBACA,sBACA,sBACA,wBACA,wBACA,mBACA,mBACA,oBACA,qBACA,kBACA,eAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,mBACA,uBAIFnR,KAAQ,6BACRgR,KAAQ,MACRC,QAAW,IACXC,OAAS,EACTxU,KAAQ,2BACRyU,KACE,sBAIFnR,KAAQ,iCACRgR,KAAQ,QACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,uBAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,2BACRyU,KACE,6BACA,iCACA,0BACA,6BACA,6BACA,4BACA,4BACA,uBACA,oBACA,kBACA,gBACA,qBAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,iCACRyU,KACE,oBACA,gBACA,kBACA,oBACA,iBACA,qBACA,iBACA,mBACA,qBACA,mBACA,eAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,wBACRyU,KACE,qBAIFnR,KAAQ,2BACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,yBACRyU,KACE,wBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,mBAIFnR,KAAQ,SACRgR,KAAQ,IACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,4CACRyU,KACE,kBACA,yBACA,eAIFnR,KAAQ,6BACRgR,KAAQ,MACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,mCAGRsD,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,uBACA,qBAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,QAAW,EACXC,OAAS,EACTxU,KAAQ,6BACRyU,KACE,sBACA,eAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,mBACRyU,KACE,oBACA,qBAIFnR,KAAQ,MACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,mCACRyU,KACE,uBACA,aAIFnR,KAAQ,oBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,0CACRyU,KACE,kBACA,kBACA,mBACA,gBACA,kBACA,qBACA,gBACA,gBACA,mBAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,4BACRyU,KACE,iBACA,eACA,gBACA,gBACA,gBACA,iBACA,eACA,kBACA,cACA,kBACA,oBACA,qBACA,kBACA,qBACA,wBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,+DACRyU,KACE,sBACA,mBACA,iBACA,gBACA,kBACA,mBACA,oBACA,eACA,gBACA,cACA,cACA,oBACA,mBACA,eACA,iBACA,gBACA,mBAIFnR,KAAQ,+BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,gEACRyU,KACE,kBACA,oBACA,kBACA,mBACA,mBACA,gBACA,mBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,kDACRyU,KACE,eACA,kBACA,oBACA,gBACA,kBAIFnR,KAAQ,iCACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,+CACRyU,KACE,kBACA,gBACA,gBACA,mBAIFnR,KAAQ,kCACRgR,KAAQ,QACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,kCACRyU,KACE,iBACA,gBACA,qBACA,gBACA,kBACA,eACA,oBACA,gBACA,gBACA,kBACA,gBACA,oBACA,eACA,eAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,qBAIFnR,KAAQ,oBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,gCACRyU,KACE,eACA,gBACA,mBACA,qBAIFnR,KAAQ,4BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,iBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,kBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,mBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,0BAGRsD,KAAQ,6BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,+BACRyU,KACE,kBACA,mBACA,kBACA,gBACA,sBACA,gBACA,oBACA,gBACA,gBACA,gBACA,iBACA,eAIFnR,KAAQ,oBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,4DACRyU,KACE,kBACA,cACA,mBACA,cACA,eACA,iBACA,kBACA,iBACA,uBAIFnR,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,qBAIFnR,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,wBACRyU,KACE,oBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,oBAIFnR,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,gBAIFnR,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,kBAIFnR,KAAQ,4BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,iCACRyU,KACE,qBACA,kBAIFnR,KAAQ,qBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,6BACRyU,KACE,YACA,eACA,cACA,aACA,iBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,qBACA,gBACA,uBACA,kBACA,cACA,iBACA,kBACA,mBACA,iBACA,mBACA,YACA,sBACA,gBACA,oBAIFnR,KAAQ,qBACRgR,KAAQ,MACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,iBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,gCACRyU,KACE,aACA,cACA,eAIFnR,KAAQ,2BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,mBACRyU,KACE,eAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,gDACRyU,KACE,gBACA,gBACA,oBACA,sBAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,yBACRyU,KACE,cACA,mBACA,oBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,kBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,kBAIFnR,KAAQ,4BACRgR,KAAQ,MACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,gBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,iCACRyU,KACE,oBACA,aACA,cACA,gBACA,gBACA,YACA,iBACA,gBACA,YACA,mBACA,qBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,iCACRyU,KACE,kBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,kDACRyU,KACE,mBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,kCACRyU,KACE,kBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,KACVC,OAAS,EACTxU,KAAQ,wBACRyU,KACE,mBAIFnR,KAAQ,6BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,oBACA,cACA,eACA,iBACA,cACA,YACA,mBAIFnR,KAAQ,2BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,aACA,kBAIFnR,KAAQ,6BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,2BACRyU,KACE,wBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,+BACRyU,KACE,eACA,kBAIFnR,KAAQ,wBACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sCACRyU,KACE,mBACA,eACA,YACA,eACA,kBACA,iBACA,cACA,iBACA,YACA,sBAIFnR,KAAQ,gCACRgR,KAAQ,QACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,0BACRyU,KACE,oBACA,mBACA,eAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oDACRyU,KACE,iBACA,aACA,mBAIFnR,KAAQ,2BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,0BACRyU,KACE,sBAIFnR,KAAQ,0BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sCACRyU,KACE,cACA,oBACA,eACA,gBACA,cACA,iBACA,eAIFnR,KAAQ,6BACRgR,KAAQ,OACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,mBACA,qBAIFnR,KAAQ,uBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,iBAIFnR,KAAQ,4BACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,0BACRyU,KACE,kBACA,sBAIFnR,KAAQ,gCACRgR,KAAQ,QACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,kBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oCACRyU,KACE,YACA,gBACA,aACA,YACA,mBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,EACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,iBACA,gBAIFnR,KAAQ,+BACRgR,KAAQ,OACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,qBACA,2BAIFnR,KAAQ,4BACRgR,KAAQ,OACRC,OAAU,IACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,sBAIFnR,KAAQ,6BACRgR,KAAQ,OACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,uBACRyU,KACE,qBACA,wBAIFnR,KAAQ,4BACRgR,KAAQ,OACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,0CACRyU,KACE,sBACA,sBAIFnR,KAAQ,6BACRgR,KAAQ,OACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,iCACRyU,KACE,4BACA,aACA,eACA,uBACA,iBACA,kBAIFnR,KAAQ,yBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,qBACRyU,KACE,mBACA,sBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,aACA,gBACA,kBAIFnR,KAAQ,gCACRgR,KAAQ,OACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,yCACRyU,KACE,uBACA,aACA,gBACA,sBACA,iBACA,iBACA,oBAIFnR,KAAQ,4BACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,0BACRyU,KACE,gBACA,gBACA,sBAIFnR,KAAQ,4BACRgR,KAAQ,OACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,mCACRyU,KACE,qBACA,sBAIFnR,KAAQ,SACRgR,KAAQ,IACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,4CACRyU,KACE,aACA,mBACA,oBACA,iBACA,gBACA,iBACA,eACA,oBAIFnR,KAAQ,qBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,mBACRyU,KACE,kBAIFnR,KAAQ,wBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,sBACRyU,KACE,cACA,iBACA,eACA,wBAIFnR,KAAQ,0BACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,+CAGRsD,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,yBACRyU,KACE,aACA,oBACA,kBACA,uBAIFnR,KAAQ,sBACRgR,KAAQ,MACRC,OAAU,GACVC,OAAS,EACTxU,KAAQ,oBACRyU,KACE,kBAKd/S,YACI,gBACA,gBACA,aACA,oBACA,QACA,UACA,sBACA,uBACA,yBACA,gBACA,uBACA,qBACA,QACA,yBACA,yBACA,yBACA,iBACA,iBACA,YACA,YACA,eACA,SACA,aACA,iBACA,UACA,SACA,QACA,SACA,sBACA,mBACA,YACA,0BACA,sBACA,0BACA,cACA,SACA,mBACA,sBACA,qBACA,mBACA,mBACA,mBACA,QACA,sBACA,mBACA,eACA,mBACA,0BACA,0BACA,OACA,oBACA,UACA,qBACA,wBACA,eACA,kBACA,uBACA,eACA,iBACA,iBACA,8BACA,uBACA,qBACA,cACA,mBACA,oBACA,kCACA,0BACA,0BACA,uBACA,sBACA,wBACA,oBACA,oBACA,+BACA,wBACA,uBACA,aACA,6BACA,yBACA,qBACA,aACA,oBACA,sBACA,gBACA,iBACA,kBACA,4BACA,2BACA,2BACA,eACA,SACA,wBACA,mBACA,sBACA,UACA,WACA,kBACA,oBACA,YACA,sBACA,mBACA,YACA,kBACA,WACA,uBACA,oBACA,YACA,yBACA,SACA,sBACA,sBACA,2BACA,uBACA,qBACA,mBACA,sBACA,eACA,wBACA,yBACA,yBACA,2BACA,iBACA,YACA,sBACA,qBACA,sBACA,qBACA,mBACA,yBACA,uBACA,oBACA,gBACA,4BACA,oBACA,oBACA,YACA,cACA,sBACA,qBACA,mBACA,kBACA,kBACA,2BACA,uBACA,sBACA,uBACA,kBACA,qBACA,mBACA,cACA,kBACA,YACA,kBACA,uBACA,gBACA,uBACA,oBACA,eACA,mBACA,iBACA,0BACA,sBACA,mBACA,+BACA,kBACA,iBACA,iBACA,yBACA,gBACA,0BACA,WACA,sBACA,sBACA,uBACA,eACA,qBACA,qBACA,6BACA,sBACA,wBACA,uBACA,cACA,eACA,oBACA,qBACA,eACA,qBACA,qBACA,UACA,wBACA,aACA,mBACA,kBACA,0BACA,wBACA,iBACA,iBACA,sBACA,0BACA,SACA,yBACA,8BACA,kBACA,kBACA,kBACA,sBACA,eACA,WACA,oBACA,sBACA,wBACA,wBACA,mBACA,yBACA,mBACA,mBACA,sBACA,qBACA,oBACA,gBACA,sBACA,WACA,sBACA,cACA,mBACA,oBACA,qBACA,0BACA,eACA,4BACA,gBACA,iBACA,cACA,0BACA,sBACA,WACA,wBACA,qBACA,mBACA,sBACA,WACA,mBACA,qBACA,eACA,wBACA,yBACA,gBACA,iBACA,qBACA,+BACA,qBACA,cACA,qCACA,0BACA,YACA,cACA,kBACA,qBACA,uBACA,qBACA,aACA,eACA,qBACA,YACA,sBACA,YACA,oBACA,aACA,oBACA,sBACA,aACA,oBACA,uBACA,oBACA,YACA,iBACA,uBACA,oBACA,mBACA,WACA,kBACA,mBACA,sBACA,qBACA,uBACA,qBACA,YACA,mBACA,kBACA,kBACA,mBACA,uBACA,yBACA,gBACA,YACA,mBACA,sBACA,+BACA,2BACA,4BACA,oBACA,oBACA,qBACA,wBACA,YACA,2BACA,6BACA,mBACA,2BACA,qBACA,WACA,sBACA,qBACA,uBACA,eACA,kBACA,iBACA,gBACA,0BACA,uBACA,gBACA,kBACA,sBACA,wBACA,oBACA,qBACA,sBACA,oBACA,oBACA,iBACA,SACA,kBACA,yBACA,kBACA,mBACA,gBACA,cACA,qBACA,oBACA,yBACA,iBACA,yBACA,4BACA,4BACA,qBACA,gBACA,uBACA,eACA,gBACA,8BACA,8BACA,8BACA,cACA,WACA,sBACA,iBACA,wBACA,kBACA,kBACA,qBACA,uBACA,iBACA,UACA,+BACA,wBACA,mBACA,sBACA,kBACA,eACA,sBACA,iBACA,cACA,sBACA,kBACA,qBACA,yBACA,eACA,YACA,gBACA,cACA,uBACA,2BACA,kBACA,gBACA,eACA,4BACA,gBACA,qBAEJgT,SAEEC,OAAW,aAAa,UAAU,iBAAiB,WAAW,WAAW,kBAAkB,UAAU,gBAAgB,cAAc,wBAAwB,eAAe,2BAA2B,uBAAuB,oBAAoB,eAAe,uBAAuB,sBAAsB,mBAAmB,WAAW,iBAAiB,YAAY,gBAAgB,OAAO,eAAe,kBAAkB,kBAAkB,WAAW,4BAA4B,cAAc,aAAa,qBAAqB,YAAY,aAAa,uBAAuB,aAAa,qBAAqB,sBAAsB,wBAAwB,iBAAiB,sBAAsB,OAAO,oBAAoB,oBAAoB,MAAM,aAAa,sBAAsB,mBAAmB,QAAQ,qBAAqB,UAAU,QAAQ,YAAY,cAAc,OAAO,2BAA2B,eAAe,aAAa,wBAAwB,UAAU,cAAc,OAAO,aAAa,SAAS,gBAAgB,iBAAiB,wBAAwB,sBAAsB,MAAM,gBAAgB,gBAAgB,iBAAiB,sBAAsB,kBAAkB,eAAe,YAAY,YAAY,wBAAwB,kBAAkB,kBAAkB,WAAW,cAAc,WAAW,QAAQ,mBAAmB,gBAAgB,gCAAgC,MAAM,UAAU,sBAAsB,8BAA8B,aAAa,eAAe,iBAAiB,gBAAgB,wBAAwB,iBAAiB,iBAAiB,cAAc,yBAAyB,kBAAkB,eAAe,YAAY,oBAAoB,kBAAkB,qBAAqB,UAAU,UAAU,oBAAoB,UAAU,OAAO,UAAU,mBAAmB,SAAS,kBAAkB,cAAc,cAAc,qBAAqB,mBAAmB,mBAAmB,cAAc,UAAU,OAAO,aAAa,iBAAiB,sBAAsB,iBAAiB,UAAU,sBAAsB,sBAAsB,SAAS,mBAAmB,uBAAuB,YAAY,YAAY,0BAA0B,eAAe,eAAe,QAAQ,qBAAqB,UAAU,kBAAkB,eAAe,SAAS,OAAO,WAAW,uBAAuB,aAAa,UAAU,oBAAoB,oBAAoB,qBAAqB,iBAAiB,oBAAoB,UAAU,YAAY,SAAS,kBAAkB,mBAAmB,gBAAgB,aAAa,UAAU,WAAW,YAAY,SAAS,SAAS,YAAY,kBAAkB,iBAAiB,UAAU,WAAW,aAAa,oBAAoB,6BAA6B,uBAAuB,iBAAiB,aAAa,cAAc,UAAU,gBAAgB,yBAAyB,UAAU,kBAAkB,gBAAgB,WAAW,QAAQ,SAAS,qBAAqB,sBAAsB,kBAAkB,kBAAkB,+BAA+B,8BAA8B,8BAA8B,uBAAuB,wBAAwB,yBAAyB,UAAU,qBAAqB,WAAW,cAAc,WAAW,WAAW,WAAW,aAAa,wBAAwB,wBAAwB,SAAS,kBAAkB,cAAc,mBAAmB,qBAAqB,UAAU,UAAU,eAAe,MAAM,WAAW,gBAAgB,cAAc,kBAAkB,aAAa,YAAY,SAAS,cAAc,gBAAgB,UAAU,uBAAuB,6BAA6B,WAAW,eAAe,WAAW,aAAa,OAAO,QAAQ,sBAAsB,wBAAwB,SAAS,kBAAkB,gBAAgB,oBAAoB,qBAAqB,SAAS,iBAAiB,oBAAoB,SAAS,wBAAwB,kBAAkB,oBAAoB,gBAAgB,QAAQ,YAAY,WAAW,cAAc,YAAY,qBAAqB,WAAW,YAAY,mBAAmB,qBAAqB,wBAAwB,uBAAuB,cAAc,cAAc,WAAW,WAAW,4BAA4B,OAAO,iBAAiB,cAAc,4BAA4B,oBAAoB,YAAY,gBAAgB,UAAU,QAAQ,SAAS,sBAAsB,QAAQ,cAAc,kBAAkB,uBAAuB,wBAAwB,eAAe,YAAY,SAAS,YAAY,YAAY,wBAAwB,qBAAqB,yBAAyB,qBAAqB,iBAAiB,aAAa,eAEhiJC,QAAY,WAAW,QAAQ,mBAAmB,MAAM,WAAW,YAAY,SAAS,SAAS,MAAM,iBAAiB,SAAS,OAAO,mBAAmB,MAAM,aAAa,UAAU,YAAY,QAAQ,UAAU,WAAW,cAAc,YAAY,UAAU,WAAW,aAAa,iBAAiB,QAAQ,QAAQ,aAAa,SAAS,UAAU,YAAY,OAAO,aAAa,OAAO,UAAU,QAAQ,UAAU,WAAW,MAAM,SAAS,MAAM,MAAM,YAAY,QAAQ,iBAAiB,SAAS,cAAc,UAAU,QAAQ,UAAU,OAAO,WAAW,QAAQ,QAAQ,SAAS,WAAW,eAAe,UAAU,SAAS,UAAU,UAAU,OAAO,QAAQ,UAAU,QAAQ,OAAO,UAAU,MAAM,YAAY,SAAS,SAAS,UAAU,cAAc,aAAa,WAAW,kBAAkB,cAAc,WAAW,YAAY,YAAY,MAAM,OAAO,WAAW,oBAAoB,QAAQ,UAAU,UAAU,QAAQ,SACp6BC,WAAe,WAAW,WAAW,WAAW,kBAAkB,mBAAmB,SAAS,WAAW,MAAM,WAAW,WAAW,YAAY,SAAS,SAAS,YAAY,SAAS,MAAM,MAAM,YAAY,SAAS,OAAO,QAAQ,mBAAmB,sBAAsB,WAAW,MAAM,YAAY,aAAa,WAAW,aAAa,UAAU,YAAY,UAAU,UAAU,WAAW,WAAW,cAAc,UAAU,WAAW,QAAQ,iBAAiB,kBAAkB,QAAQ,QAAQ,WAAW,YAAY,eAAe,aAAa,SAAS,UAAU,SAAS,QAAQ,YAAY,UAAU,OAAO,SAAS,cAAc,OAAO,aAAa,QAAQ,WAAW,OAAO,SAAS,OAAO,SAAS,UAAU,QAAQ,UAAU,WAAW,MAAM,MAAM,SAAS,QAAQ,OAAO,MAAM,cAAc,MAAM,OAAO,YAAY,eAAe,UAAU,QAAQ,iBAAiB,SAAS,iBAAiB,UAAU,cAAc,SAAS,YAAY,SAAS,UAAU,aAAa,UAAU,UAAU,aAAa,OAAO,WAAW,eAAe,eAAe,WAAW,QAAQ,QAAQ,SAAS,SAAS,SAAS,aAAa,WAAW,eAAe,UAAU,UAAU,UAAU,UAAU,OAAO,QAAQ,UAAU,QAAQ,OAAO,OAAO,UAAU,MAAM,WAAW,WAAW,cAAc,YAAY,qBAAqB,SAAS,QAAQ,SAAS,QAAQ,aAAa,WAAW,aAAa,kBAAkB,WAAW,WAAW,YAAY,WAAW,kBAAkB,YAAY,SAAS,OAAO,YACl+CC,QAAY,SAAS,WAAW,OAAO,MAAM,WAAW,WAAW,YAAY,mBAAmB,UAAU,WAAW,SAAS,SAAS,YAAY,UAAU,SAAS,WAAW,MAAM,iBAAiB,MAAM,YAAY,SAAS,UAAU,YAAY,mBAAmB,QAAQ,YAAY,SAAS,WAAW,WAAW,aAAa,YAAY,UAAU,YAAY,WAAW,YAAY,cAAc,YAAY,YAAY,aAAa,SAAS,QAAQ,kBAAkB,QAAQ,QAAQ,WAAW,YAAY,SAAS,UAAU,aAAa,SAAS,YAAY,UAAU,YAAY,OAAO,SAAS,SAAS,cAAc,OAAO,QAAQ,QAAQ,WAAW,SAAS,OAAO,SAAS,QAAQ,UAAU,WAAW,QAAQ,kBAAkB,OAAO,eAAe,MAAM,cAAc,QAAQ,OAAO,YAAY,OAAO,QAAQ,UAAU,SAAS,wBAAwB,OAAO,WAAW,eAAe,WAAW,QAAQ,SAAS,SAAS,SAAS,aAAa,UAAU,UAAU,QAAQ,UAAU,OAAO,QAAQ,WAAW,SAAS,SAAS,QAAQ,aAAa,OAAO,QAAQ,uBAAuB,YAAY,MAAM,WAAW,UAAU,WAAW,SAAS,SAAS,SAAS,SAAS,UAAU,YAAY,gBAAgB,aAAa,aAAa,kBAAkB,WAAW,WAAW,YAAY,WAAW,WAAW,UAAU,QAAQ,YAAY,SAAS,QAAQ,MAAM,OAAO,WAAW,SAAS,SAAS,YAAY,SAAS,SAAS,oBAAoB,QAAQ,UAAU,UAAU,UAAU,OAAO,UAAU,SAAS,eAAe,WAAW,YAAY,SAAS,cAAc,OAAO,OAAO,SAAS,eAAe,WAAW,kBAAkB,sBAAsB,QAAQ,OAAO,QAAQ,MAAM,aAAa,WAAW,eAAe,aAAa,SAAS,aAAa,WAAW,MAAM,MAAM,SAAS,MAAM,eAAe,cAAc,iBAAiB,YAAY,SAAS,UAAU,iBAAiB,WAAW,QAAQ,SAAS,QAAQ,cAAc,kBAAkB,YAAY,SAAS,OAAO,SAAS,UAAU,OAAO,SAAS,YAAY,cAE3gEC,MAAU,SAAS,UAAU,UAAU,MAAM,MAAM,UAAU,OAAO,QAAQ,SAAS,MAAM,OAAO,MAAM,OAAO,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,QAAQ,MAAM,SAAS,OAAO,SAAS,QAAQ,WAAW,SAAS,MAAM,QAEjOC,KAAS,iBAAiB,QAAQ,QAAQ,OAAO,aAAa,WAAW,cAAc,uBAAuB,OAAO,OAAO,SAAS,QAAQ,UAAU,OAAO,SAAS,QAAQ,UAAU,QAAQ,cAAc,cAAc,WAAW,YAAY,SAAS,UAAU,SAAS,UAAU,OAAO,OAAO,UAAU,gBAAgB,UAAU,SAAS,kBAAkB,UAAU,OAAO,QAAQ,SAAS,SAAS,gBAAgB,gBAAgB,YAAY,UAAU,WAExcC,KAAS,WAAW,mBAAmB,mBAAmB,qBAAqB,iBAAiB,aAAa,WAAW,gBAAgB,iBAAiB,kBAAkB,aAAa,aAAa,cAAc,YAAY,sBAAsB,qBAAqB,aAAa,iBAAiB,oBAAoB,sBAAsB,eAAe,aAAa,QAAQ,SAAS,iBAAiB,UAAU,UAAU,eAAe,6BAA6B,iBAAiB,oBAAoB,aAAa,eAAe,gBAAgB,yBAAyB,OAAO,iBAAiB,sBAAsB,qBAAqB,gBAAgB,gBAAgB,0BAA0B,cAAc,oBAAoB,iBAAiB,2BAA2B,cAAc,QAAQ,mBAAmB,aAAa,iBAAiB,oBAAoB,YAAY,mBAAmB,oBAAoB,uBAAuB,aAAa,2BAA2B,eAAe,YAAY,2BAA2B,6BAA6B,kBAAkB,gBAAgB,uBAAuB,2BAA2B,WAI3nCC,EAAmBjd,OAAOC,UAAUid,eACpCvb,EAAU3B,OAAO0B,MAAQ,SAAS2D,GACpC,IAAIQ,KACJ,IAAK,IAAIpE,KAAO4D,EACV4X,EAAiB9c,KAAKkF,EAAK5D,IAC7BoE,EAAOS,KAAK7E,GAIhB,OAAOoE,GAkCTrG,EAAOS,UAAUkD,IAAM,SAAUkI,GAC7B,OAAOvJ,EAAWoZ,EAAK7P,KAI3B7L,EAAOS,UAAUkd,YAAc,SAAStc,IAKpCA,EAAUD,EAAYC,IACVuc,YACRvc,EAAQuc,UAAavc,EAAQwc,eAAiB,IAAM,KAWxD,OANIxc,EAAQwc,eAGF3d,KAAKqF,EAAErF,KAAKoF,OAAQ,GAAKnB,KALtB,mBAKsC9D,OAAO,IAAKmF,KAAKnE,EAAQuc,WAFlE1d,KAAKqF,EAAErF,KAAKoF,OAAQ,GAAKnB,KAHtB,mBAGsC9D,OAAO,IAAKmF,KAAKnE,EAAQuc,YAQhF5d,EAAOS,UAAUqd,OAAS,SAAUzc,GAkBhC,GAjBAA,EAAUD,EAAYC,GAAU0c,KAAO,EAAGC,IAAM,EAAG7Z,UAEnD5C,EACIF,EAAQ8C,KAAK0J,cAAgBpL,MAC7B,kDAEJlB,EAC4B,iBAAjBF,EAAQ0c,KACf,wCAEJxc,EAC2B,iBAAhBF,EAAQ2c,IACf,qDAKA3c,EAAQ8C,KAAK9D,OAAS,EACtB,OAAOH,KAAK+d,YAAY5c,GAI5B,IAAI6c,EAAGC,EAAGC,EAAGC,EACTN,EAAO1c,EAAQ0c,KACfC,EAAM3c,EAAQ2c,IAElB,GAKIE,GAHAC,EAAoB,EAAhBje,KAAKC,SAAe,GAGhBge,GAFRC,EAAoB,EAAhBle,KAAKC,SAAe,GAERie,QACXF,GAAK,GAMd,OAHAG,EAAOF,EAAIxZ,KAAK2Z,MAAM,EAAI3Z,KAAK4Z,IAAIL,GAAKA,GAGjCF,EAAMK,EAAON,GAGxB/d,EAAOS,UAAUwd,YAAc,SAAS5c,GACpC,IAAImd,EAAqB,EACzB,EAAG,CACC,IAAItT,EAAMvG,KAAK8Z,MAAMve,KAAK4d,QAASC,KAAM1c,EAAQ0c,KAAMC,IAAK3c,EAAQ2c,OACpE,GAAI9S,EAAM7J,EAAQ8C,KAAK9D,QAAU6K,GAAO,EACpC,OAAO7J,EAAQ8C,KAAK+G,GAEpBsT,UAEAA,EAAqB,KAE7B,MAAM,IAAI9c,WAAW,6FAGzB1B,EAAOS,UAAUie,MAAQ,SAAUrd,GAG/B,IAAIsd,EAAK,GACT,QAFAtd,EAAUD,EAAYC,GAAUud,KAAO,OAEvBA,KAAKhb,eACrB,IAAK,OACL,IAAK,IACD+a,EAAK,IACL,MACJ,IAAK,OACL,IAAK,IACDA,EAAK,IACL,MACJ,QACIA,EAAKze,KAAK6D,WAAWI,KAAM,OAI/B,OAAOwa,EAAKze,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,UACzClE,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,UACrClE,KAAK6D,WAAWC,OAAO,EAAMI,OAAQ,WAIjDpE,EAAOS,UAAUoe,IAAM,SAAUhT,EAAMiT,GACf,iBAATjT,EACP6P,EAAK7P,GAAQiT,EAEbpD,EAAOpZ,EAAWuJ,EAAM6P,IAIhC1b,EAAOS,UAAUse,GAAK,SAAU1d,GAC5B,OAAOnB,KAAKwe,MAAMrd,IAItBrB,EAAOS,UAAUoJ,KAAO,WACpB,IAAItE,EAAIrF,KAAKqF,EAAErF,KAAK2B,QAAS,GAAKyC,IAAK,IACnCoF,EAAK,EAAO,EAALnE,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,IAC9DmE,EAAK,GAAMA,EAAK,KACR,KACJA,EAAK,GAET,IAAIC,EAAQ,EAAHD,EAAK,EAAO,EAALnE,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAAU,EAALA,EAAE,GAKnE,OAJAoE,EAAK,GAAMA,EAAK,KACR,KACJA,EAAK,GAEF,GAAGpE,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,IAAIA,EAAE,GAAGA,EAAE,GAAGA,EAAE,GAAG,SAASmE,EAAGC,GAK1E3J,EAAOS,UAAUQ,iBAAmB,SAAUhB,GAC1C,OAAO,IAAI+e,EAAgB/e,IAG/BD,EAAOS,UAAUU,YAAc,WAC3B,OAAO,IAAI8d,GA8Cf,IAAID,EAAkB,SAAU/e,QACfuQ,IAATvQ,IAEAA,EAAO0E,KAAKO,MAAMP,KAAKxE,SAASwE,KAAKC,IAAI,GAAG,MAGhD1E,KAAKgf,EAAI,IACThf,KAAKif,EAAI,IACTjf,KAAKkf,SAAW,WAChBlf,KAAKmf,WAAa,WAClBnf,KAAKof,WAAa,WAElBpf,KAAKc,GAAK,IAAIyB,MAAMvC,KAAKgf,GACzBhf,KAAKqf,IAAMrf,KAAKgf,EAAI,EAEpBhf,KAAKsf,aAAavf,IAItB+e,EAAgBve,UAAU+e,aAAe,SAAUtB,GAE/C,IADAhe,KAAKc,GAAG,GAAKkd,IAAM,EACdhe,KAAKqf,IAAM,EAAGrf,KAAKqf,IAAMrf,KAAKgf,EAAGhf,KAAKqf,MACvCrB,EAAIhe,KAAKc,GAAGd,KAAKqf,IAAM,GAAMrf,KAAKc,GAAGd,KAAKqf,IAAM,KAAO,GACvDrf,KAAKc,GAAGd,KAAKqf,MAAsC,aAAtB,WAAJrB,KAAoB,KAAqB,IAAyB,YAAd,MAAJA,GAAgChe,KAAKqf,IAK9Grf,KAAKc,GAAGd,KAAKqf,QAAU,GAS/BP,EAAgBve,UAAUgf,cAAgB,SAAUC,EAAUC,GAC1D,IAAkB7e,EAAGod,EAAjB5d,EAAI,EAAGM,EAAI,EAGf,IAFAV,KAAKsf,aAAa,UAClB1e,EAAKZ,KAAKgf,EAAIS,EAAazf,KAAKgf,EAAIS,EAC7B7e,EAAGA,IACNod,EAAIhe,KAAKc,GAAGV,EAAI,GAAMJ,KAAKc,GAAGV,EAAI,KAAO,GACzCJ,KAAKc,GAAGV,IAAMJ,KAAKc,GAAGV,IAAoC,UAAtB,WAAJ4d,KAAoB,KAAkB,IAA0B,SAAd,MAAJA,IAA+BwB,EAAS9e,GAAKA,EAC3HV,KAAKc,GAAGV,MAAQ,EAEhBM,MADAN,GAESJ,KAAKgf,IAAKhf,KAAKc,GAAG,GAAKd,KAAKc,GAAGd,KAAKgf,EAAI,GAAI5e,EAAI,GACrDM,GAAK+e,IAAc/e,EAAI,GAE/B,IAAKE,EAAIZ,KAAKgf,EAAI,EAAGpe,EAAGA,IACpBod,EAAIhe,KAAKc,GAAGV,EAAI,GAAMJ,KAAKc,GAAGV,EAAI,KAAO,GACzCJ,KAAKc,GAAGV,IAAMJ,KAAKc,GAAGV,IAAoC,aAAtB,WAAJ4d,KAAoB,KAAqB,IAAyB,YAAd,MAAJA,IAAiC5d,EACjHJ,KAAKc,GAAGV,MAAQ,IAChBA,GACSJ,KAAKgf,IAAKhf,KAAKc,GAAG,GAAKd,KAAKc,GAAGd,KAAKgf,EAAI,GAAI5e,EAAI,GAG7DJ,KAAKc,GAAG,GAAK,YAIjBge,EAAgBve,UAAUmf,cAAgB,WACtC,IAAInV,EACAoV,EAAQ,IAAIpd,MAAM,EAAKvC,KAAKkf,UAGhC,GAAIlf,KAAKqf,KAAOrf,KAAKgf,EAAG,CACpB,IAAIY,EAKJ,IAHI5f,KAAKqf,MAAQrf,KAAKgf,EAAI,GACtBhf,KAAKsf,aAAa,MAEjBM,EAAK,EAAGA,EAAK5f,KAAKgf,EAAIhf,KAAKif,EAAGW,IAC/BrV,EAAKvK,KAAKc,GAAG8e,GAAI5f,KAAKmf,WAAanf,KAAKc,GAAG8e,EAAK,GAAG5f,KAAKof,WACxDpf,KAAKc,GAAG8e,GAAM5f,KAAKc,GAAG8e,EAAK5f,KAAKif,GAAM1U,IAAM,EAAKoV,EAAU,EAAJpV,GAE3D,KAAMqV,EAAK5f,KAAKgf,EAAI,EAAGY,IACnBrV,EAAKvK,KAAKc,GAAG8e,GAAI5f,KAAKmf,WAAanf,KAAKc,GAAG8e,EAAK,GAAG5f,KAAKof,WACxDpf,KAAKc,GAAG8e,GAAM5f,KAAKc,GAAG8e,GAAM5f,KAAKif,EAAIjf,KAAKgf,IAAOzU,IAAM,EAAKoV,EAAU,EAAJpV,GAEtEA,EAAKvK,KAAKc,GAAGd,KAAKgf,EAAI,GAAGhf,KAAKmf,WAAanf,KAAKc,GAAG,GAAGd,KAAKof,WAC3Dpf,KAAKc,GAAGd,KAAKgf,EAAI,GAAKhf,KAAKc,GAAGd,KAAKif,EAAI,GAAM1U,IAAM,EAAKoV,EAAU,EAAJpV,GAE9DvK,KAAKqf,IAAM,EAWf,OARA9U,EAAIvK,KAAKc,GAAGd,KAAKqf,OAGjB9U,GAAMA,IAAM,GACZA,GAAMA,GAAK,EAAK,WAChBA,GAAMA,GAAK,GAAM,YACjBA,GAAMA,IAAM,MAEC,GAIjBuU,EAAgBve,UAAUsf,cAAgB,WACtC,OAAQ7f,KAAK0f,kBAAoB,GAIrCZ,EAAgBve,UAAUuf,cAAgB,WACtC,OAAO9f,KAAK0f,iBAAmB,EAAM,aAKzCZ,EAAgBve,UAAUN,OAAS,WAC/B,OAAOD,KAAK0f,iBAAmB,EAAM,aAKzCZ,EAAgBve,UAAUwf,cAAgB,WACtC,OAAQ/f,KAAK0f,gBAAkB,KAAQ,EAAM,aAKjDZ,EAAgBve,UAAUyf,cAAgB,WAEtC,OAAY,UADJhgB,KAAK0f,kBAAkB,IAAO1f,KAAK0f,kBAAkB,KAC9B,EAAM,mBAIzC,IAAIX,EAAa,aAEjBA,EAAWxe,UAAUwC,QAAU,QAM/Bgc,EAAWxe,UAAU0f,SAAW,SAAkB3V,EAAGC,GACjD,IAAI2V,GAAW,MAAJ5V,IAAmB,MAAJC,GAE1B,OADWD,GAAK,KAAOC,GAAK,KAAO2V,GAAO,KAC3B,GAAa,MAANA,GAM1BnB,EAAWxe,UAAU4f,SAAW,SAAU3b,EAAK4b,GAC3C,OAAQ5b,GAAO4b,EAAQ5b,IAAS,GAAK4b,GAMzCrB,EAAWxe,UAAU8f,QAAU,SAAUC,EAAGC,EAAGC,EAAGlW,EAAG0T,EAAGyC,GACpD,OAAOzgB,KAAKigB,SAASjgB,KAAKmgB,SAASngB,KAAKigB,SAASjgB,KAAKigB,SAASM,EAAGD,GAAItgB,KAAKigB,SAAS3V,EAAGmW,IAAKzC,GAAIwC,IAEpGzB,EAAWxe,UAAUmgB,OAAS,SAAUH,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAG0T,EAAGyC,GACtD,OAAOzgB,KAAKqgB,QAASG,EAAInI,GAAQmI,EAAKG,EAAIJ,EAAGC,EAAGlW,EAAG0T,EAAGyC,IAE1D1B,EAAWxe,UAAUqgB,OAAS,SAAUL,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAG0T,EAAGyC,GACtD,OAAOzgB,KAAKqgB,QAASG,EAAIG,EAAMtI,GAAMsI,EAAKJ,EAAGC,EAAGlW,EAAG0T,EAAGyC,IAE1D1B,EAAWxe,UAAUsgB,OAAS,SAAUN,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAG0T,EAAGyC,GACtD,OAAOzgB,KAAKqgB,QAAQG,EAAInI,EAAIsI,EAAGJ,EAAGC,EAAGlW,EAAG0T,EAAGyC,IAE/C1B,EAAWxe,UAAUugB,OAAS,SAAUP,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAG0T,EAAGyC,GACtD,OAAOzgB,KAAKqgB,QAAQhI,GAAKmI,GAAMG,GAAKJ,EAAGC,EAAGlW,EAAG0T,EAAGyC,IAMpD1B,EAAWxe,UAAUwgB,SAAW,SAAUzW,EAAG0W,GAEzC1W,EAAE0W,GAAO,IAAM,KAASA,EAAM,GAC9B1W,EAA8B,IAAzB0W,EAAM,KAAQ,GAAM,IAAWA,EAEpC,IAAI5gB,EAAG6gB,EAAMC,EAAMC,EAAMC,EACrBb,EAAK,WACLC,GAAK,UACLnI,GAAK,WACLsI,EAAK,UAET,IAAKvgB,EAAI,EAAGA,EAAIkK,EAAEnK,OAAQC,GAAK,GAC3B6gB,EAAOV,EACPW,EAAOV,EACPW,EAAO9I,EACP+I,EAAOT,EAEPJ,EAAIvgB,KAAK0gB,OAAOH,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,GAAU,GAAI,WAC5CugB,EAAI3gB,KAAK0gB,OAAOC,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,IAAK,WAC5CiY,EAAIrY,KAAK0gB,OAAOrI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,GAAK,WAC5CogB,EAAIxgB,KAAK0gB,OAAOF,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,YAC5CmgB,EAAIvgB,KAAK0gB,OAAOH,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,WAC5CugB,EAAI3gB,KAAK0gB,OAAOC,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,GAAK,YAC5CiY,EAAIrY,KAAK0gB,OAAOrI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,IAAK,YAC5CogB,EAAIxgB,KAAK0gB,OAAOF,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,UAC5CmgB,EAAIvgB,KAAK0gB,OAAOH,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,EAAI,YAC5CugB,EAAI3gB,KAAK0gB,OAAOC,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,IAAK,YAC5CiY,EAAIrY,KAAK0gB,OAAOrI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,IAAK,OAC5CogB,EAAIxgB,KAAK0gB,OAAOF,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,IAAK,YAC5CmgB,EAAIvgB,KAAK0gB,OAAOH,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAI,IAAM,EAAI,YAC5CugB,EAAI3gB,KAAK0gB,OAAOC,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAK,IAAK,UAC5CiY,EAAIrY,KAAK0gB,OAAOrI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,IAAK,YAC5CogB,EAAIxgB,KAAK0gB,OAAOF,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,GAAK,YAE5CmgB,EAAIvgB,KAAK4gB,OAAOL,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,WAC5CugB,EAAI3gB,KAAK4gB,OAAOD,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAK,GAAI,YAC5CiY,EAAIrY,KAAK4gB,OAAOvI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,GAAK,WAC5CogB,EAAIxgB,KAAK4gB,OAAOJ,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,GAAS,IAAK,WAC5CmgB,EAAIvgB,KAAK4gB,OAAOL,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,WAC5CugB,EAAI3gB,KAAK4gB,OAAOD,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAM,EAAI,UAC5CiY,EAAIrY,KAAK4gB,OAAOvI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,IAAK,WAC5CogB,EAAIxgB,KAAK4gB,OAAOJ,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,WAC5CmgB,EAAIvgB,KAAK4gB,OAAOL,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,EAAI,WAC5CugB,EAAI3gB,KAAK4gB,OAAOD,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAM,GAAI,YAC5CiY,EAAIrY,KAAK4gB,OAAOvI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,IAAK,WAC5CogB,EAAIxgB,KAAK4gB,OAAOJ,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,GAAK,YAC5CmgB,EAAIvgB,KAAK4gB,OAAOL,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAI,IAAM,GAAI,YAC5CugB,EAAI3gB,KAAK4gB,OAAOD,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAK,GAAI,UAC5CiY,EAAIrY,KAAK4gB,OAAOvI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,GAAK,YAC5CogB,EAAIxgB,KAAK4gB,OAAOJ,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,IAAK,YAE5CmgB,EAAIvgB,KAAK6gB,OAAON,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,QAC5CugB,EAAI3gB,KAAK6gB,OAAOF,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,IAAK,YAC5CiY,EAAIrY,KAAK6gB,OAAOxI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,GAAK,YAC5CogB,EAAIxgB,KAAK6gB,OAAOL,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,IAAK,UAC5CmgB,EAAIvgB,KAAK6gB,OAAON,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,YAC5CugB,EAAI3gB,KAAK6gB,OAAOF,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,GAAK,YAC5CiY,EAAIrY,KAAK6gB,OAAOxI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,IAAK,WAC5CogB,EAAIxgB,KAAK6gB,OAAOL,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,IAAK,YAC5CmgB,EAAIvgB,KAAK6gB,OAAON,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAI,IAAM,EAAI,WAC5CugB,EAAI3gB,KAAK6gB,OAAOF,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,GAAS,IAAK,WAC5CiY,EAAIrY,KAAK6gB,OAAOxI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,IAAK,WAC5CogB,EAAIxgB,KAAK6gB,OAAOL,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,GAAK,UAC5CmgB,EAAIvgB,KAAK6gB,OAAON,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,WAC5CugB,EAAI3gB,KAAK6gB,OAAOF,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAK,IAAK,WAC5CiY,EAAIrY,KAAK6gB,OAAOxI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,GAAK,WAC5CogB,EAAIxgB,KAAK6gB,OAAOL,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,WAE5CmgB,EAAIvgB,KAAK8gB,OAAOP,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,GAAU,GAAI,WAC5CugB,EAAI3gB,KAAK8gB,OAAOH,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,GAAK,YAC5CiY,EAAIrY,KAAK8gB,OAAOzI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,IAAK,YAC5CogB,EAAIxgB,KAAK8gB,OAAON,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,UAC5CmgB,EAAIvgB,KAAK8gB,OAAOP,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAI,IAAM,EAAI,YAC5CugB,EAAI3gB,KAAK8gB,OAAOH,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAK,GAAI,IAAK,YAC5CiY,EAAIrY,KAAK8gB,OAAOzI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAI,IAAK,IAAK,SAC5CogB,EAAIxgB,KAAK8gB,OAAON,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,YAC5CmgB,EAAIvgB,KAAK8gB,OAAOP,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,EAAI,YAC5CugB,EAAI3gB,KAAK8gB,OAAOH,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAK,IAAK,UAC5CiY,EAAIrY,KAAK8gB,OAAOzI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,IAAK,YAC5CogB,EAAIxgB,KAAK8gB,OAAON,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAI,IAAK,GAAK,YAC5CmgB,EAAIvgB,KAAK8gB,OAAOP,EAAGC,EAAGnI,EAAGsI,EAAGrW,EAAElK,EAAK,GAAK,GAAI,WAC5CugB,EAAI3gB,KAAK8gB,OAAOH,EAAGJ,EAAGC,EAAGnI,EAAG/N,EAAElK,EAAI,IAAK,IAAK,YAC5CiY,EAAIrY,KAAK8gB,OAAOzI,EAAGsI,EAAGJ,EAAGC,EAAGlW,EAAElK,EAAK,GAAI,GAAK,WAC5CogB,EAAIxgB,KAAK8gB,OAAON,EAAGnI,EAAGsI,EAAGJ,EAAGjW,EAAElK,EAAK,GAAI,IAAK,WAE5CmgB,EAAIvgB,KAAKigB,SAASM,EAAGU,GACrBT,EAAIxgB,KAAKigB,SAASO,EAAGU,GACrB7I,EAAIrY,KAAKigB,SAAS5H,EAAG8I,GACrBR,EAAI3gB,KAAKigB,SAASU,EAAGS,GAEzB,OAAQb,EAAGC,EAAGnI,EAAGsI,IAMrB5B,EAAWxe,UAAU8gB,UAAY,SAAUje,GACvC,IAAIhD,EACAkhB,EAAS,GACb,IAAKlhB,EAAI,EAAGA,EAAmB,GAAfgD,EAAMjD,OAAaC,GAAK,EACpCkhB,GAAUC,OAAOC,aAAcpe,EAAMhD,GAAK,KAAQA,EAAI,GAAO,KAEjE,OAAOkhB,GAOXvC,EAAWxe,UAAUkhB,UAAY,SAAUre,GACvC,IAAIhD,EACAkhB,KAEJ,IADAA,GAAQle,EAAMjD,QAAU,GAAK,QAAKmQ,EAC7BlQ,EAAI,EAAGA,EAAIkhB,EAAOnhB,OAAQC,GAAK,EAChCkhB,EAAOlhB,GAAK,EAEhB,IAAKA,EAAI,EAAGA,EAAmB,EAAfgD,EAAMjD,OAAYC,GAAK,EACnCkhB,EAAOlhB,GAAK,KAAiC,IAA1BgD,EAAMvC,WAAWT,EAAI,KAAeA,EAAI,GAE/D,OAAOkhB,GAMXvC,EAAWxe,UAAUmhB,SAAW,SAAU1D,GACtC,OAAOhe,KAAKqhB,UAAUrhB,KAAK+gB,SAAS/gB,KAAKyhB,UAAUzD,GAAe,EAAXA,EAAE7d,UAM7D4e,EAAWxe,UAAUohB,cAAgB,SAAU5f,EAAKyZ,GAChD,IAAIpb,EAIAO,EAHAihB,EAAO5hB,KAAKyhB,UAAU1f,GACtB8f,KACAC,KAMJ,IAJAD,EAAK,IAAMC,EAAK,SAAMxR,EAClBsR,EAAKzhB,OAAS,KACdyhB,EAAO5hB,KAAK+gB,SAASa,EAAmB,EAAb7f,EAAI5B,SAE9BC,EAAI,EAAGA,EAAI,GAAIA,GAAK,EACrByhB,EAAKzhB,GAAe,UAAVwhB,EAAKxhB,GACf0hB,EAAK1hB,GAAe,WAAVwhB,EAAKxhB,GAGnB,OADAO,EAAOX,KAAK+gB,SAASc,EAAK1X,OAAOnK,KAAKyhB,UAAUjG,IAAQ,IAAoB,EAAdA,EAAKrb,QAC5DH,KAAKqhB,UAAUrhB,KAAK+gB,SAASe,EAAK3X,OAAOxJ,GAAO,OAM3Doe,EAAWxe,UAAUwhB,SAAW,SAAU3e,GACtC,IAEIkH,EACAlK,EAFAkhB,EAAS,GAGb,IAAKlhB,EAAI,EAAGA,EAAIgD,EAAMjD,OAAQC,GAAK,EAC/BkK,EAAIlH,EAAMvC,WAAWT,GACrBkhB,GANU,mBAMQnd,OAAQmG,IAAM,EAAK,IAN3B,mBAOEnG,OAAW,GAAJmG,GAEvB,OAAOgX,GAMXvC,EAAWxe,UAAUyhB,cAAgB,SAAU5e,GAC3C,OAAO6e,SAASC,mBAAmB9e,KAMvC2b,EAAWxe,UAAU4hB,QAAU,SAAUnE,GACrC,OAAOhe,KAAK0hB,SAAS1hB,KAAKgiB,cAAchE,KAE5Ce,EAAWxe,UAAU6hB,QAAU,SAAUpE,GACrC,OAAOhe,KAAK+hB,SAAS/hB,KAAKmiB,QAAQnE,KAEtCe,EAAWxe,UAAU8hB,aAAe,SAAUzhB,EAAG+f,GAC7C,OAAO3gB,KAAK2hB,cAAc3hB,KAAKgiB,cAAcphB,GAAIZ,KAAKgiB,cAAcrB,KAExE5B,EAAWxe,UAAU+hB,aAAe,SAAU1hB,EAAG+f,GAC7C,OAAO3gB,KAAK+hB,SAAS/hB,KAAKqiB,aAAazhB,EAAG+f,KAG9C5B,EAAWxe,UAAUqO,IAAM,SAAUxJ,EAAQrD,EAAKgT,GAC9C,OAAKhT,EAQAgT,EAIE/U,KAAKqiB,aAAatgB,EAAKqD,GAHnBpF,KAAKsiB,aAAavgB,EAAKqD,GARzB2P,EAIE/U,KAAKmiB,QAAQ/c,GAHTpF,KAAKoiB,QAAQhd,IAcT,oBAAZmd,UACe,oBAAXC,QAA0BA,OAAOD,UACxCA,QAAUC,OAAOD,QAAUziB,GAE/ByiB,QAAQziB,OAASA,GAIC,mBAAX2iB,QAAyBA,OAAOC,KACvCD,UAAW,WACP,OAAO3iB,IAMc,oBAAlB6iB,gBACPC,OAAS,IAAI9iB,EACbkT,KAAKlT,OAASA,GAKI,iBAAX+iB,QAAkD,iBAApBA,OAAOnG,WAC5CmG,OAAO/iB,OAASA,EAChB+iB,OAAOD,OAAS,IAAI9iB,GA3sO5B", "file": "chance.min.js", "sourcesContent": ["//  Chance.js 1.0.13\n//  http://chancejs.com\n//  (c) 2013 <PERSON>\n//  Chance may be freely distributed or modified under the MIT license.\n\n(function () {\n\n    // Constants\n    var MAX_INT = 9007199254740992;\n    var MIN_INT = -MAX_INT;\n    var NUMBERS = '**********';\n    var CHARS_LOWER = 'abcdefghijklmnopqrstuvwxyz';\n    var CHARS_UPPER = CHARS_LOWER.toUpperCase();\n    var HEX_POOL  = NUMBERS + \"abcdef\";\n\n    // Cached array helpers\n    var slice = Array.prototype.slice;\n\n    // Constructor\n    function Chance (seed) {\n        if (!(this instanceof Chance)) {\n            if (!seed) { seed = null; } // handle other non-truthy seeds, as described in issue #322\n            return seed === null ? new Chance() : new Chance(seed);\n        }\n\n        // if user has provided a function, use that as the generator\n        if (typeof seed === 'function') {\n            this.random = seed;\n            return this;\n        }\n\n        if (arguments.length) {\n            // set a starting value of zero so we can add to it\n            this.seed = 0;\n        }\n\n        // otherwise, leave this.seed blank so that MT will receive a blank\n\n        for (var i = 0; i < arguments.length; i++) {\n            var seedling = 0;\n            if (Object.prototype.toString.call(arguments[i]) === '[object String]') {\n                for (var j = 0; j < arguments[i].length; j++) {\n                    // create a numeric hash for each argument, add to seedling\n                    var hash = 0;\n                    for (var k = 0; k < arguments[i].length; k++) {\n                        hash = arguments[i].charCodeAt(k) + (hash << 6) + (hash << 16) - hash;\n                    }\n                    seedling += hash;\n                }\n            } else {\n                seedling = arguments[i];\n            }\n            this.seed += (arguments.length - i) * seedling;\n        }\n\n        // If no generator function was provided, use our MT\n        this.mt = this.mersenne_twister(this.seed);\n        this.bimd5 = this.blueimp_md5();\n        this.random = function () {\n            return this.mt.random(this.seed);\n        };\n\n        return this;\n    }\n\n    Chance.prototype.VERSION = \"1.0.13\";\n\n    // Random helper functions\n    function initOptions(options, defaults) {\n        options = options || {};\n\n        if (defaults) {\n            for (var i in defaults) {\n                if (typeof options[i] === 'undefined') {\n                    options[i] = defaults[i];\n                }\n            }\n        }\n\n        return options;\n    }\n\n    function testRange(test, errorMessage) {\n        if (test) {\n            throw new RangeError(errorMessage);\n        }\n    }\n\n    /**\n     * Encode the input string with Base64.\n     */\n    var base64 = function() {\n        throw new Error('No Base64 encoder available.');\n    };\n\n    // Select proper Base64 encoder.\n    (function determineBase64Encoder() {\n        if (typeof btoa === 'function') {\n            base64 = btoa;\n        } else if (typeof Buffer === 'function') {\n            base64 = function(input) {\n                return new Buffer(input).toString('base64');\n            };\n        }\n    })();\n\n    // -- Basics --\n\n    /**\n     *  Return a random bool, either true or false\n     *\n     *  @param {Object} [options={ likelihood: 50 }] alter the likelihood of\n     *    receiving a true or false value back.\n     *  @throws {RangeError} if the likelihood is out of bounds\n     *  @returns {Bool} either true or false\n     */\n    Chance.prototype.bool = function (options) {\n        // likelihood of success (true)\n        options = initOptions(options, {likelihood : 50});\n\n        // Note, we could get some minor perf optimizations by checking range\n        // prior to initializing defaults, but that makes code a bit messier\n        // and the check more complicated as we have to check existence of\n        // the object then existence of the key before checking constraints.\n        // Since the options initialization should be minor computationally,\n        // decision made for code cleanliness intentionally. This is mentioned\n        // here as it's the first occurrence, will not be mentioned again.\n        testRange(\n            options.likelihood < 0 || options.likelihood > 100,\n            \"Chance: Likelihood accepts values from 0 to 100.\"\n        );\n\n        return this.random() * 100 < options.likelihood;\n    };\n\n    Chance.prototype.animal = function (options){\n      //returns a random animal\n      options = initOptions(options);\n\n      if(typeof options.type !== 'undefined'){\n        //if user does not put in a valid animal type, user will get an error\n        testRange(\n           !this.get(\"animals\")[options.type.toLowerCase()],\n           \"Please pick from desert, ocean, grassland, forest, zoo, pets, farm.\"\n         );\n         //if user does put in valid animal type, will return a random animal of that type\n          return this.pick(this.get(\"animals\")[options.type.toLowerCase()]);\n      }\n       //if user does not put in any animal type, will return a random animal regardless\n      animalTypeArray = [\"desert\",\"forest\",\"ocean\",\"zoo\",\"farm\",\"pet\",\"grassland\"];\n      return this.pick(this.get(\"animals\")[this.pick(animalTypeArray)]);\n    };\n\n    /**\n     *  Return a random character.\n     *\n     *  @param {Object} [options={}] can specify a character pool, only alpha,\n     *    only symbols, and casing (lower or upper)\n     *  @returns {String} a single random character\n     *  @throws {RangeError} Can only specify alpha or symbols, not both\n     */\n    Chance.prototype.character = function (options) {\n        options = initOptions(options);\n        testRange(\n            options.alpha && options.symbols,\n            \"Chance: Cannot specify both alpha and symbols.\"\n        );\n\n        var symbols = \"!@#$%^&*()[]\",\n            letters, pool;\n\n        if (options.casing === 'lower') {\n            letters = CHARS_LOWER;\n        } else if (options.casing === 'upper') {\n            letters = CHARS_UPPER;\n        } else {\n            letters = CHARS_LOWER + CHARS_UPPER;\n        }\n\n        if (options.pool) {\n            pool = options.pool;\n        } else if (options.alpha) {\n            pool = letters;\n        } else if (options.symbols) {\n            pool = symbols;\n        } else {\n            pool = letters + NUMBERS + symbols;\n        }\n\n        return pool.charAt(this.natural({max: (pool.length - 1)}));\n    };\n\n    // Note, wanted to use \"float\" or \"double\" but those are both JS reserved words.\n\n    // Note, fixed means N OR LESS digits after the decimal. This because\n    // It could be 14.9000 but in JavaScript, when this is cast as a number,\n    // the trailing zeroes are dropped. Left to the consumer if trailing zeroes are\n    // needed\n    /**\n     *  Return a random floating point number\n     *\n     *  @param {Object} [options={}] can specify a fixed precision, min, max\n     *  @returns {Number} a single floating point number\n     *  @throws {RangeError} Can only specify fixed or precision, not both. Also\n     *    min cannot be greater than max\n     */\n    Chance.prototype.floating = function (options) {\n        options = initOptions(options, {fixed : 4});\n        testRange(\n            options.fixed && options.precision,\n            \"Chance: Cannot specify both fixed and precision.\"\n        );\n\n        var num;\n        var fixed = Math.pow(10, options.fixed);\n\n        var max = MAX_INT / fixed;\n        var min = -max;\n\n        testRange(\n            options.min && options.fixed && options.min < min,\n            \"Chance: Min specified is out of range with fixed. Min should be, at least, \" + min\n        );\n        testRange(\n            options.max && options.fixed && options.max > max,\n            \"Chance: Max specified is out of range with fixed. Max should be, at most, \" + max\n        );\n\n        options = initOptions(options, { min : min, max : max });\n\n        // Todo - Make this work!\n        // options.precision = (typeof options.precision !== \"undefined\") ? options.precision : false;\n\n        num = this.integer({min: options.min * fixed, max: options.max * fixed});\n        var num_fixed = (num / fixed).toFixed(options.fixed);\n\n        return parseFloat(num_fixed);\n    };\n\n    /**\n     *  Return a random integer\n     *\n     *  NOTE the max and min are INCLUDED in the range. So:\n     *  chance.integer({min: 1, max: 3});\n     *  would return either 1, 2, or 3.\n     *\n     *  @param {Object} [options={}] can specify a min and/or max\n     *  @returns {Number} a single random integer number\n     *  @throws {RangeError} min cannot be greater than max\n     */\n    Chance.prototype.integer = function (options) {\n        // 9007199254740992 (2^53) is the max integer number in JavaScript\n        // See: http://vq.io/132sa2j\n        options = initOptions(options, {min: MIN_INT, max: MAX_INT});\n        testRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\n\n        return Math.floor(this.random() * (options.max - options.min + 1) + options.min);\n    };\n\n    /**\n     *  Return a random natural\n     *\n     *  NOTE the max and min are INCLUDED in the range. So:\n     *  chance.natural({min: 1, max: 3});\n     *  would return either 1, 2, or 3.\n     *\n     *  @param {Object} [options={}] can specify a min and/or maxm or a numerals count.\n     *  @returns {Number} a single random integer number\n     *  @throws {RangeError} min cannot be greater than max\n     */\n    Chance.prototype.natural = function (options) {\n        options = initOptions(options, {min: 0, max: MAX_INT});\n        if (typeof options.numerals === 'number'){\n          testRange(options.numerals < 1, \"Chance: Numerals cannot be less than one.\");\n          options.min = Math.pow(10, options.numerals - 1);\n          options.max = Math.pow(10, options.numerals) - 1;\n        }\n        testRange(options.min < 0, \"Chance: Min cannot be less than zero.\");\n        return this.integer(options);\n    };\n\n    /**\n     *  Return a random hex number as string\n     *\n     *  NOTE the max and min are INCLUDED in the range. So:\n     *  chance.hex({min: '9', max: 'B'});\n     *  would return either '9', 'A' or 'B'.\n     *\n     *  @param {Object} [options={}] can specify a min and/or max and/or casing\n     *  @returns {String} a single random string hex number\n     *  @throws {RangeError} min cannot be greater than max\n     */\n    Chance.prototype.hex = function (options) {\n        options = initOptions(options, {min: 0, max: MAX_INT, casing: 'lower'});\n        testRange(options.min < 0, \"Chance: Min cannot be less than zero.\");\n\t\tvar integer = this.natural({min: options.min, max: options.max});\n\t\tif (options.casing === 'upper') {\n\t\t\treturn integer.toString(16).toUpperCase();\n\t\t}\n\t\treturn integer.toString(16);\n    };\n\n    Chance.prototype.letter = function(options) {\n        options = initOptions(options, {casing: 'lower'});\n        var pool = \"abcdefghijklmnopqrstuvwxyz\";\n        var letter = this.character({pool: pool});\n        if (options.casing === 'upper') {\n            letter = letter.toUpperCase();\n        }\n        return letter;\n    }\n\n    /**\n     *  Return a random string\n     *\n     *  @param {Object} [options={}] can specify a length\n     *  @returns {String} a string of random length\n     *  @throws {RangeError} length cannot be less than zero\n     */\n    Chance.prototype.string = function (options) {\n        options = initOptions(options, { length: this.natural({min: 5, max: 20}) });\n        testRange(options.length < 0, \"Chance: Length cannot be less than zero.\");\n        var length = options.length,\n            text = this.n(this.character, length, options);\n\n        return text.join(\"\");\n    };\n\n    // -- End Basics --\n\n    // -- Helpers --\n\n    Chance.prototype.capitalize = function (word) {\n        return word.charAt(0).toUpperCase() + word.substr(1);\n    };\n\n    Chance.prototype.mixin = function (obj) {\n        for (var func_name in obj) {\n            Chance.prototype[func_name] = obj[func_name];\n        }\n        return this;\n    };\n\n    /**\n     *  Given a function that generates something random and a number of items to generate,\n     *    return an array of items where none repeat.\n     *\n     *  @param {Function} fn the function that generates something random\n     *  @param {Number} num number of terms to generate\n     *  @param {Object} options any options to pass on to the generator function\n     *  @returns {Array} an array of length `num` with every item generated by `fn` and unique\n     *\n     *  There can be more parameters after these. All additional parameters are provided to the given function\n     */\n    Chance.prototype.unique = function(fn, num, options) {\n        testRange(\n            typeof fn !== \"function\",\n            \"Chance: The first argument must be a function.\"\n        );\n\n        var comparator = function(arr, val) { return arr.indexOf(val) !== -1; };\n\n        if (options) {\n            comparator = options.comparator || comparator;\n        }\n\n        var arr = [], count = 0, result, MAX_DUPLICATES = num * 50, params = slice.call(arguments, 2);\n\n        while (arr.length < num) {\n            var clonedParams = JSON.parse(JSON.stringify(params));\n            result = fn.apply(this, clonedParams);\n            if (!comparator(arr, result)) {\n                arr.push(result);\n                // reset count when unique found\n                count = 0;\n            }\n\n            if (++count > MAX_DUPLICATES) {\n                throw new RangeError(\"Chance: num is likely too large for sample set\");\n            }\n        }\n        return arr;\n    };\n\n    /**\n     *  Gives an array of n random terms\n     *\n     *  @param {Function} fn the function that generates something random\n     *  @param {Number} n number of terms to generate\n     *  @returns {Array} an array of length `n` with items generated by `fn`\n     *\n     *  There can be more parameters after these. All additional parameters are provided to the given function\n     */\n    Chance.prototype.n = function(fn, n) {\n        testRange(\n            typeof fn !== \"function\",\n            \"Chance: The first argument must be a function.\"\n        );\n\n        if (typeof n === 'undefined') {\n            n = 1;\n        }\n        var i = n, arr = [], params = slice.call(arguments, 2);\n\n        // Providing a negative count should result in a noop.\n        i = Math.max( 0, i );\n\n        for (null; i--; null) {\n            arr.push(fn.apply(this, params));\n        }\n\n        return arr;\n    };\n\n    // H/T to SO for this one: http://vq.io/OtUrZ5\n    Chance.prototype.pad = function (number, width, pad) {\n        // Default pad to 0 if none provided\n        pad = pad || '0';\n        // Convert number to a string\n        number = number + '';\n        return number.length >= width ? number : new Array(width - number.length + 1).join(pad) + number;\n    };\n\n    // DEPRECATED on 2015-10-01\n    Chance.prototype.pick = function (arr, count) {\n        if (arr.length === 0) {\n            throw new RangeError(\"Chance: Cannot pick() from an empty array\");\n        }\n        if (!count || count === 1) {\n            return arr[this.natural({max: arr.length - 1})];\n        } else {\n            return this.shuffle(arr).slice(0, count);\n        }\n    };\n\n    // Given an array, returns a single random element\n    Chance.prototype.pickone = function (arr) {\n        if (arr.length === 0) {\n          throw new RangeError(\"Chance: Cannot pickone() from an empty array\");\n        }\n        return arr[this.natural({max: arr.length - 1})];\n    };\n\n    // Given an array, returns a random set with 'count' elements\n    Chance.prototype.pickset = function (arr, count) {\n        if (count === 0) {\n            return [];\n        }\n        if (arr.length === 0) {\n            throw new RangeError(\"Chance: Cannot pickset() from an empty array\");\n        }\n        if (count < 0) {\n            throw new RangeError(\"Chance: Count must be a positive number\");\n        }\n        if (!count || count === 1) {\n            return [ this.pickone(arr) ];\n        } else {\n            return this.shuffle(arr).slice(0, count);\n        }\n    };\n\n    Chance.prototype.shuffle = function (arr) {\n        var old_array = arr.slice(0),\n            new_array = [],\n            j = 0,\n            length = Number(old_array.length);\n\n        for (var i = 0; i < length; i++) {\n            // Pick a random index from the array\n            j = this.natural({max: old_array.length - 1});\n            // Add it to the new array\n            new_array[i] = old_array[j];\n            // Remove that element from the original array\n            old_array.splice(j, 1);\n        }\n\n        return new_array;\n    };\n\n    // Returns a single item from an array with relative weighting of odds\n    Chance.prototype.weighted = function (arr, weights, trim) {\n        if (arr.length !== weights.length) {\n            throw new RangeError(\"Chance: Length of array and weights must match\");\n        }\n\n        // scan weights array and sum valid entries\n        var sum = 0;\n        var val;\n        for (var weightIndex = 0; weightIndex < weights.length; ++weightIndex) {\n            val = weights[weightIndex];\n            if (isNaN(val)) {\n                throw new RangeError(\"Chance: All weights must be numbers\");\n            }\n\n            if (val > 0) {\n                sum += val;\n            }\n        }\n\n        if (sum === 0) {\n            throw new RangeError(\"Chance: No valid entries in array weights\");\n        }\n\n        // select a value within range\n        var selected = this.random() * sum;\n\n        // find array entry corresponding to selected value\n        var total = 0;\n        var lastGoodIdx = -1;\n        var chosenIdx;\n        for (weightIndex = 0; weightIndex < weights.length; ++weightIndex) {\n            val = weights[weightIndex];\n            total += val;\n            if (val > 0) {\n                if (selected <= total) {\n                    chosenIdx = weightIndex;\n                    break;\n                }\n                lastGoodIdx = weightIndex;\n            }\n\n            // handle any possible rounding error comparison to ensure something is picked\n            if (weightIndex === (weights.length - 1)) {\n                chosenIdx = lastGoodIdx;\n            }\n        }\n\n        var chosen = arr[chosenIdx];\n        trim = (typeof trim === 'undefined') ? false : trim;\n        if (trim) {\n            arr.splice(chosenIdx, 1);\n            weights.splice(chosenIdx, 1);\n        }\n\n        return chosen;\n    };\n\n    // -- End Helpers --\n\n    // -- Text --\n\n    Chance.prototype.paragraph = function (options) {\n        options = initOptions(options);\n\n        var sentences = options.sentences || this.natural({min: 3, max: 7}),\n            sentence_array = this.n(this.sentence, sentences);\n\n        return sentence_array.join(' ');\n    };\n\n    // Could get smarter about this than generating random words and\n    // chaining them together. Such as: http://vq.io/1a5ceOh\n    Chance.prototype.sentence = function (options) {\n        options = initOptions(options);\n\n        var words = options.words || this.natural({min: 12, max: 18}),\n            punctuation = options.punctuation,\n            text, word_array = this.n(this.word, words);\n\n        text = word_array.join(' ');\n\n        // Capitalize first letter of sentence\n        text = this.capitalize(text);\n\n        // Make sure punctuation has a usable value\n        if (punctuation !== false && !/^[\\.\\?;!:]$/.test(punctuation)) {\n            punctuation = '.';\n        }\n\n        // Add punctuation mark\n        if (punctuation) {\n            text += punctuation;\n        }\n\n        return text;\n    };\n\n    Chance.prototype.syllable = function (options) {\n        options = initOptions(options);\n\n        var length = options.length || this.natural({min: 2, max: 3}),\n            consonants = 'bcdfghjklmnprstvwz', // consonants except hard to speak ones\n            vowels = 'aeiou', // vowels\n            all = consonants + vowels, // all\n            text = '',\n            chr;\n\n        // I'm sure there's a more elegant way to do this, but this works\n        // decently well.\n        for (var i = 0; i < length; i++) {\n            if (i === 0) {\n                // First character can be anything\n                chr = this.character({pool: all});\n            } else if (consonants.indexOf(chr) === -1) {\n                // Last character was a vowel, now we want a consonant\n                chr = this.character({pool: consonants});\n            } else {\n                // Last character was a consonant, now we want a vowel\n                chr = this.character({pool: vowels});\n            }\n\n            text += chr;\n        }\n\n        if (options.capitalize) {\n            text = this.capitalize(text);\n        }\n\n        return text;\n    };\n\n    Chance.prototype.word = function (options) {\n        options = initOptions(options);\n\n        testRange(\n            options.syllables && options.length,\n            \"Chance: Cannot specify both syllables AND length.\"\n        );\n\n        var syllables = options.syllables || this.natural({min: 1, max: 3}),\n            text = '';\n\n        if (options.length) {\n            // Either bound word by length\n            do {\n                text += this.syllable();\n            } while (text.length < options.length);\n            text = text.substring(0, options.length);\n        } else {\n            // Or by number of syllables\n            for (var i = 0; i < syllables; i++) {\n                text += this.syllable();\n            }\n        }\n\n        if (options.capitalize) {\n            text = this.capitalize(text);\n        }\n\n        return text;\n    };\n\n    // -- End Text --\n\n    // -- Person --\n\n    Chance.prototype.age = function (options) {\n        options = initOptions(options);\n        var ageRange;\n\n        switch (options.type) {\n            case 'child':\n                ageRange = {min: 0, max: 12};\n                break;\n            case 'teen':\n                ageRange = {min: 13, max: 19};\n                break;\n            case 'adult':\n                ageRange = {min: 18, max: 65};\n                break;\n            case 'senior':\n                ageRange = {min: 65, max: 100};\n                break;\n            case 'all':\n                ageRange = {min: 0, max: 100};\n                break;\n            default:\n                ageRange = {min: 18, max: 65};\n                break;\n        }\n\n        return this.natural(ageRange);\n    };\n\n    Chance.prototype.birthday = function (options) {\n        var age = this.age(options);\n        var currentYear = new Date().getFullYear();\n\n        if (options && options.type) {\n            var min = new Date();\n            var max = new Date();\n            min.setFullYear(currentYear - age - 1);\n            max.setFullYear(currentYear - age);\n\n            options = initOptions(options, {\n                min: min,\n                max: max\n            });\n        } else {\n            options = initOptions(options, {\n                year: currentYear - age\n            });\n        }\n\n        return this.date(options);\n    };\n\n    // CPF; ID to identify taxpayers in Brazil\n    Chance.prototype.cpf = function (options) {\n        options = initOptions(options, {\n            formatted: true\n        });\n\n        var n = this.n(this.natural, 9, { max: 9 });\n        var d1 = n[8]*2+n[7]*3+n[6]*4+n[5]*5+n[4]*6+n[3]*7+n[2]*8+n[1]*9+n[0]*10;\n        d1 = 11 - (d1 % 11);\n        if (d1>=10) {\n            d1 = 0;\n        }\n        var d2 = d1*2+n[8]*3+n[7]*4+n[6]*5+n[5]*6+n[4]*7+n[3]*8+n[2]*9+n[1]*10+n[0]*11;\n        d2 = 11 - (d2 % 11);\n        if (d2>=10) {\n            d2 = 0;\n        }\n        var cpf = ''+n[0]+n[1]+n[2]+'.'+n[3]+n[4]+n[5]+'.'+n[6]+n[7]+n[8]+'-'+d1+d2;\n        return options.formatted ? cpf : cpf.replace(/\\D/g,'');\n    };\n\n    // CNPJ: ID to identify companies in Brazil\n    Chance.prototype.cnpj = function (options) {\n        options = initOptions(options, {\n            formatted: true\n        });\n\n        var n = this.n(this.natural, 12, { max: 12 });\n        var d1 = n[11]*2+n[10]*3+n[9]*4+n[8]*5+n[7]*6+n[6]*7+n[5]*8+n[4]*9+n[3]*2+n[2]*3+n[1]*4+n[0]*5;\n        d1 = 11 - (d1 % 11);\n        if (d1<2) {\n            d1 = 0;\n        }\n        var d2 = d1*2+n[11]*3+n[10]*4+n[9]*5+n[8]*6+n[7]*7+n[6]*8+n[5]*9+n[4]*2+n[3]*3+n[2]*4+n[1]*5+n[0]*6;\n        d2 = 11 - (d2 % 11);\n        if (d2<2) {\n            d2 = 0;\n        }\n        var cnpj = ''+n[0]+n[1]+'.'+n[2]+n[3]+n[4]+'.'+n[5]+n[6]+n[7]+'/'+n[8]+n[9]+n[10]+n[11]+'-'+d1+d2;\n        return options.formatted ? cnpj : cnpj.replace(/\\D/g,'');\n    };\n\n    Chance.prototype.first = function (options) {\n        options = initOptions(options, {gender: this.gender(), nationality: 'en'});\n        return this.pick(this.get(\"firstNames\")[options.gender.toLowerCase()][options.nationality.toLowerCase()]);\n    };\n\n    Chance.prototype.profession = function (options) {\n        options = initOptions(options);\n        if(options.rank){\n            return this.pick(['Apprentice ', 'Junior ', 'Senior ', 'Lead ']) + this.pick(this.get(\"profession\"));\n        } else{\n            return this.pick(this.get(\"profession\"));\n        }\n    };\n\n    Chance.prototype.company = function (){\n        return this.pick(this.get(\"company\"));\n    };\n\n    Chance.prototype.gender = function (options) {\n        options = initOptions(options, {extraGenders: []});\n        return this.pick(['Male', 'Female'].concat(options.extraGenders));\n    };\n\n    Chance.prototype.last = function (options) {\n        options = initOptions(options, {nationality: 'en'});\n        return this.pick(this.get(\"lastNames\")[options.nationality.toLowerCase()]);\n    };\n\n    Chance.prototype.israelId=function(){\n        var x=this.string({pool: '**********',length:8});\n        var y=0;\n        for (var i=0;i<x.length;i++){\n            var thisDigit=  x[i] *  (i/2===parseInt(i/2) ? 1 : 2);\n            thisDigit=this.pad(thisDigit,2).toString();\n            thisDigit=parseInt(thisDigit[0]) + parseInt(thisDigit[1]);\n            y=y+thisDigit;\n        }\n        x=x+(10-parseInt(y.toString().slice(-1))).toString().slice(-1);\n        return x;\n    };\n\n    Chance.prototype.mrz = function (options) {\n        var checkDigit = function (input) {\n            var alpha = \"<ABCDEFGHIJKLMNOPQRSTUVWXYXZ\".split(''),\n                multipliers = [ 7, 3, 1 ],\n                runningTotal = 0;\n\n            if (typeof input !== 'string') {\n                input = input.toString();\n            }\n\n            input.split('').forEach(function(character, idx) {\n                var pos = alpha.indexOf(character);\n\n                if(pos !== -1) {\n                    character = pos === 0 ? 0 : pos + 9;\n                } else {\n                    character = parseInt(character, 10);\n                }\n                character *= multipliers[idx % multipliers.length];\n                runningTotal += character;\n            });\n            return runningTotal % 10;\n        };\n        var generate = function (opts) {\n            var pad = function (length) {\n                return new Array(length + 1).join('<');\n            };\n            var number = [ 'P<',\n                           opts.issuer,\n                           opts.last.toUpperCase(),\n                           '<<',\n                           opts.first.toUpperCase(),\n                           pad(39 - (opts.last.length + opts.first.length + 2)),\n                           opts.passportNumber,\n                           checkDigit(opts.passportNumber),\n                           opts.nationality,\n                           opts.dob,\n                           checkDigit(opts.dob),\n                           opts.gender,\n                           opts.expiry,\n                           checkDigit(opts.expiry),\n                           pad(14),\n                           checkDigit(pad(14)) ].join('');\n\n            return number +\n                (checkDigit(number.substr(44, 10) +\n                            number.substr(57, 7) +\n                            number.substr(65, 7)));\n        };\n\n        var that = this;\n\n        options = initOptions(options, {\n            first: this.first(),\n            last: this.last(),\n            passportNumber: this.integer({min: *********, max: *********}),\n            dob: (function () {\n                var date = that.birthday({type: 'adult'});\n                return [date.getFullYear().toString().substr(2),\n                        that.pad(date.getMonth() + 1, 2),\n                        that.pad(date.getDate(), 2)].join('');\n            }()),\n            expiry: (function () {\n                var date = new Date();\n                return [(date.getFullYear() + 5).toString().substr(2),\n                        that.pad(date.getMonth() + 1, 2),\n                        that.pad(date.getDate(), 2)].join('');\n            }()),\n            gender: this.gender() === 'Female' ? 'F': 'M',\n            issuer: 'GBR',\n            nationality: 'GBR'\n        });\n        return generate (options);\n    };\n\n    Chance.prototype.name = function (options) {\n        options = initOptions(options);\n\n        var first = this.first(options),\n            last = this.last(options),\n            name;\n\n        if (options.middle) {\n            name = first + ' ' + this.first(options) + ' ' + last;\n        } else if (options.middle_initial) {\n            name = first + ' ' + this.character({alpha: true, casing: 'upper'}) + '. ' + last;\n        } else {\n            name = first + ' ' + last;\n        }\n\n        if (options.prefix) {\n            name = this.prefix(options) + ' ' + name;\n        }\n\n        if (options.suffix) {\n            name = name + ' ' + this.suffix(options);\n        }\n\n        return name;\n    };\n\n    // Return the list of available name prefixes based on supplied gender.\n    // @todo introduce internationalization\n    Chance.prototype.name_prefixes = function (gender) {\n        gender = gender || \"all\";\n        gender = gender.toLowerCase();\n\n        var prefixes = [\n            { name: 'Doctor', abbreviation: 'Dr.' }\n        ];\n\n        if (gender === \"male\" || gender === \"all\") {\n            prefixes.push({ name: 'Mister', abbreviation: 'Mr.' });\n        }\n\n        if (gender === \"female\" || gender === \"all\") {\n            prefixes.push({ name: 'Miss', abbreviation: 'Miss' });\n            prefixes.push({ name: 'Misses', abbreviation: 'Mrs.' });\n        }\n\n        return prefixes;\n    };\n\n    // Alias for name_prefix\n    Chance.prototype.prefix = function (options) {\n        return this.name_prefix(options);\n    };\n\n    Chance.prototype.name_prefix = function (options) {\n        options = initOptions(options, { gender: \"all\" });\n        return options.full ?\n            this.pick(this.name_prefixes(options.gender)).name :\n            this.pick(this.name_prefixes(options.gender)).abbreviation;\n    };\n    //Hungarian ID number\n    Chance.prototype.HIDN= function(){\n     //Hungarian ID nuber structure: XXXXXXYY (X=number,Y=Capital Latin letter)\n      var idn_pool=\"**********\";\n      var idn_chrs=\"ABCDEFGHIJKLMNOPQRSTUVWXYXZ\";\n      var idn=\"\";\n        idn+=this.string({pool:idn_pool,length:6});\n        idn+=this.string({pool:idn_chrs,length:2});\n        return idn;\n    };\n\n\n    Chance.prototype.ssn = function (options) {\n        options = initOptions(options, {ssnFour: false, dashes: true});\n        var ssn_pool = \"**********\",\n            ssn,\n            dash = options.dashes ? '-' : '';\n\n        if(!options.ssnFour) {\n            ssn = this.string({pool: ssn_pool, length: 3}) + dash +\n            this.string({pool: ssn_pool, length: 2}) + dash +\n            this.string({pool: ssn_pool, length: 4});\n        } else {\n            ssn = this.string({pool: ssn_pool, length: 4});\n        }\n        return ssn;\n    };\n\n    // Return the list of available name suffixes\n    // @todo introduce internationalization\n    Chance.prototype.name_suffixes = function () {\n        var suffixes = [\n            { name: 'Doctor of Osteopathic Medicine', abbreviation: 'D.O.' },\n            { name: 'Doctor of Philosophy', abbreviation: 'Ph.D.' },\n            { name: 'Esquire', abbreviation: 'Esq.' },\n            { name: 'Junior', abbreviation: 'Jr.' },\n            { name: 'Juris Doctor', abbreviation: 'J.D.' },\n            { name: 'Master of Arts', abbreviation: 'M.A.' },\n            { name: 'Master of Business Administration', abbreviation: 'M.B.A.' },\n            { name: 'Master of Science', abbreviation: 'M.S.' },\n            { name: 'Medical Doctor', abbreviation: 'M.D.' },\n            { name: 'Senior', abbreviation: 'Sr.' },\n            { name: 'The Third', abbreviation: 'III' },\n            { name: 'The Fourth', abbreviation: 'IV' },\n            { name: 'Bachelor of Engineering', abbreviation: 'B.E' },\n            { name: 'Bachelor of Technology', abbreviation: 'B.TECH' }\n        ];\n        return suffixes;\n    };\n\n    // Alias for name_suffix\n    Chance.prototype.suffix = function (options) {\n        return this.name_suffix(options);\n    };\n\n    Chance.prototype.name_suffix = function (options) {\n        options = initOptions(options);\n        return options.full ?\n            this.pick(this.name_suffixes()).name :\n            this.pick(this.name_suffixes()).abbreviation;\n    };\n\n    Chance.prototype.nationalities = function () {\n        return this.get(\"nationalities\");\n    };\n\n    // Generate random nationality based on json list\n    Chance.prototype.nationality = function () {\n        var nationality = this.pick(this.nationalities());\n        return nationality.name;\n    };\n\n    // -- End Person --\n\n    // -- Mobile --\n    // Android GCM Registration ID\n    Chance.prototype.android_id = function () {\n        return \"APA91\" + this.string({ pool: \"**********abcefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_\", length: 178 });\n    };\n\n    // Apple Push Token\n    Chance.prototype.apple_token = function () {\n        return this.string({ pool: \"abcdef**********\", length: 64 });\n    };\n\n    // Windows Phone 8 ANID2\n    Chance.prototype.wp8_anid2 = function () {\n        return base64( this.hash( { length : 32 } ) );\n    };\n\n    // Windows Phone 7 ANID\n    Chance.prototype.wp7_anid = function () {\n        return 'A=' + this.guid().replace(/-/g, '').toUpperCase() + '&E=' + this.hash({ length:3 }) + '&W=' + this.integer({ min:0, max:9 });\n    };\n\n    // BlackBerry Device PIN\n    Chance.prototype.bb_pin = function () {\n        return this.hash({ length: 8 });\n    };\n\n    // -- End Mobile --\n\n    // -- Web --\n    Chance.prototype.avatar = function (options) {\n        var url = null;\n        var URL_BASE = '//www.gravatar.com/avatar/';\n        var PROTOCOLS = {\n            http: 'http',\n            https: 'https'\n        };\n        var FILE_TYPES = {\n            bmp: 'bmp',\n            gif: 'gif',\n            jpg: 'jpg',\n            png: 'png'\n        };\n        var FALLBACKS = {\n            '404': '404', // Return 404 if not found\n            mm: 'mm', // Mystery man\n            identicon: 'identicon', // Geometric pattern based on hash\n            monsterid: 'monsterid', // A generated monster icon\n            wavatar: 'wavatar', // A generated face\n            retro: 'retro', // 8-bit icon\n            blank: 'blank' // A transparent png\n        };\n        var RATINGS = {\n            g: 'g',\n            pg: 'pg',\n            r: 'r',\n            x: 'x'\n        };\n        var opts = {\n            protocol: null,\n            email: null,\n            fileExtension: null,\n            size: null,\n            fallback: null,\n            rating: null\n        };\n\n        if (!options) {\n            // Set to a random email\n            opts.email = this.email();\n            options = {};\n        }\n        else if (typeof options === 'string') {\n            opts.email = options;\n            options = {};\n        }\n        else if (typeof options !== 'object') {\n            return null;\n        }\n        else if (options.constructor === 'Array') {\n            return null;\n        }\n\n        opts = initOptions(options, opts);\n\n        if (!opts.email) {\n            // Set to a random email\n            opts.email = this.email();\n        }\n\n        // Safe checking for params\n        opts.protocol = PROTOCOLS[opts.protocol] ? opts.protocol + ':' : '';\n        opts.size = parseInt(opts.size, 0) ? opts.size : '';\n        opts.rating = RATINGS[opts.rating] ? opts.rating : '';\n        opts.fallback = FALLBACKS[opts.fallback] ? opts.fallback : '';\n        opts.fileExtension = FILE_TYPES[opts.fileExtension] ? opts.fileExtension : '';\n\n        url =\n            opts.protocol +\n            URL_BASE +\n            this.bimd5.md5(opts.email) +\n            (opts.fileExtension ? '.' + opts.fileExtension : '') +\n            (opts.size || opts.rating || opts.fallback ? '?' : '') +\n            (opts.size ? '&s=' + opts.size.toString() : '') +\n            (opts.rating ? '&r=' + opts.rating : '') +\n            (opts.fallback ? '&d=' + opts.fallback : '')\n            ;\n\n        return url;\n    };\n\n    /**\n     * #Description:\n     * ===============================================\n     * Generate random color value base on color type:\n     * -> hex\n     * -> rgb\n     * -> rgba\n     * -> 0x\n     * -> named color\n     *\n     * #Examples:\n     * ===============================================\n     * * Geerate random hex color\n     * chance.color() => '#79c157' / 'rgb(110,52,164)' / '0x67ae0b' / '#e2e2e2' / '#29CFA7'\n     *\n     * * Generate Hex based color value\n     * chance.color({format: 'hex'})    => '#d67118'\n     *\n     * * Generate simple rgb value\n     * chance.color({format: 'rgb'})    => 'rgb(110,52,164)'\n     *\n     * * Generate Ox based color value\n     * chance.color({format: '0x'})     => '0x67ae0b'\n     *\n     * * Generate graiscale based value\n     * chance.color({grayscale: true})  => '#e2e2e2'\n     *\n     * * Return valide color name\n     * chance.color({format: 'name'})   => 'red'\n     *\n     * * Make color uppercase\n     * chance.color({casing: 'upper'})  => '#29CFA7'\n     *\n     * * Min Max values for RGBA\n     * var light_red = chance.color({format: 'hex', min_red: 200, max_red: 255, max_green: 0, max_blue: 0, min_alpha: .2, max_alpha: .3});\n     *\n     * @param  [object] options\n     * @return [string] color value\n     */\n    Chance.prototype.color = function (options) {\n        function gray(value, delimiter) {\n            return [value, value, value].join(delimiter || '');\n        }\n\n        function rgb(hasAlpha) {\n            var rgbValue     = (hasAlpha)    ? 'rgba' : 'rgb';\n            var alphaChannel = (hasAlpha)    ? (',' + this.floating({min:min_alpha, max:max_alpha})) : \"\";\n            var colorValue   = (isGrayscale) ? (gray(this.natural({min: min_rgb, max: max_rgb}), ',')) : (this.natural({min: min_green, max: max_green}) + ',' + this.natural({min: min_blue, max: max_blue}) + ',' + this.natural({max: 255}));\n            return rgbValue + '(' + colorValue + alphaChannel + ')';\n        }\n\n        function hex(start, end, withHash) {\n            var symbol = (withHash) ? \"#\" : \"\";\n            var hexstring = \"\";\n\n            if (isGrayscale) {\n                hexstring = gray(this.pad(this.hex({min: min_rgb, max: max_rgb}), 2));\n                if (options.format === \"shorthex\") {\n                    hexstring = gray(this.hex({min: 0, max: 15}));\n                }\n            }\n            else {\n                if (options.format === \"shorthex\") {\n                    hexstring = this.pad(this.hex({min: Math.floor(min_red / 16), max: Math.floor(max_red / 16)}), 1) + this.pad(this.hex({min: Math.floor(min_green / 16), max: Math.floor(max_green / 16)}), 1) + this.pad(this.hex({min: Math.floor(min_blue / 16), max: Math.floor(max_blue / 16)}), 1);\n                }\n                else if (min_red !== undefined || max_red !== undefined || min_green !== undefined || max_green !== undefined || min_blue !== undefined || max_blue !== undefined) {\n                    hexstring = this.pad(this.hex({min: min_red, max: max_red}), 2) + this.pad(this.hex({min: min_green, max: max_green}), 2) + this.pad(this.hex({min: min_blue, max: max_blue}), 2);\n                }\n                else {\n                    hexstring = this.pad(this.hex({min: min_rgb, max: max_rgb}), 2) + this.pad(this.hex({min: min_rgb, max: max_rgb}), 2) + this.pad(this.hex({min: min_rgb, max: max_rgb}), 2);\n                }\n            }\n\n            return symbol + hexstring;\n        }\n\n        options = initOptions(options, {\n            format: this.pick(['hex', 'shorthex', 'rgb', 'rgba', '0x', 'name']),\n            grayscale: false,\n            casing: 'lower',\n            min: 0,\n            max: 255,\n            min_red: undefined,\n            max_red: undefined,\n            min_green: undefined,\n            max_green: undefined,\n            min_blue: undefined,\n            max_blue: undefined,\n            min_alpha: 0,\n            max_alpha: 1\n        });\n\n        var isGrayscale = options.grayscale;\n        var min_rgb = options.min;\n        var max_rgb = options.max;\n        var min_red = options.min_red;\n        var max_red = options.max_red;\n        var min_green = options.min_green;\n        var max_green = options.max_green;\n        var min_blue = options.min_blue;\n        var max_blue = options.max_blue;\n        var min_alpha = options.min_alpha;\n        var max_alpha = options.max_alpha;\n        if (options.min_red === undefined) { min_red = min_rgb; }\n        if (options.max_red === undefined) { max_red = max_rgb; }\n        if (options.min_green === undefined) { min_green = min_rgb; }\n        if (options.max_green === undefined) { max_green = max_rgb; }\n        if (options.min_blue === undefined) { min_blue = min_rgb; }\n        if (options.max_blue === undefined) { max_blue = max_rgb; }\n        if (options.min_alpha === undefined) { min_alpha = 0; }\n        if (options.max_alpha === undefined) { max_alpha = 1; }\n        if (isGrayscale && min_rgb === 0 && max_rgb === 255 && min_red !== undefined && max_red !== undefined) {\n            min_rgb = ((min_red + min_green + min_blue) / 3);\n            max_rgb = ((max_red + max_green + max_blue) / 3);\n        }\n        var colorValue;\n\n        if (options.format === 'hex') {\n            colorValue = hex.call(this, 2, 6, true);\n        }\n        else if (options.format === 'shorthex') {\n            colorValue = hex.call(this, 1, 3, true);\n        }\n        else if (options.format === 'rgb') {\n            colorValue = rgb.call(this, false);\n        }\n        else if (options.format === 'rgba') {\n            colorValue = rgb.call(this, true);\n        }\n        else if (options.format === '0x') {\n            colorValue = '0x' + hex.call(this, 2, 6);\n        }\n        else if(options.format === 'name') {\n            return this.pick(this.get(\"colorNames\"));\n        }\n        else {\n            throw new RangeError('Invalid format provided. Please provide one of \"hex\", \"shorthex\", \"rgb\", \"rgba\", \"0x\" or \"name\".');\n        }\n\n        if (options.casing === 'upper' ) {\n            colorValue = colorValue.toUpperCase();\n        }\n\n        return colorValue;\n    };\n\n    Chance.prototype.domain = function (options) {\n        options = initOptions(options);\n        return this.word() + '.' + (options.tld || this.tld());\n    };\n\n    Chance.prototype.email = function (options) {\n        options = initOptions(options);\n        return this.word({length: options.length}) + '@' + (options.domain || this.domain());\n    };\n\n    /**\n     * #Description:\n     * ===============================================\n     * Generate a random Facebook id, aka fbid.\n     *\n     * NOTE: At the moment (Sep 2017), Facebook ids are\n     * \"numeric strings\" of length 16.\n     * However, Facebook Graph API documentation states that\n     * \"it is extremely likely to change over time\".\n     * @see https://developers.facebook.com/docs/graph-api/overview/\n     *\n     * #Examples:\n     * ===============================================\n     * chance.fbid() => '****************'\n     *\n     * @return [string] facebook id\n     */\n    Chance.prototype.fbid = function () {\n        return '10000' + this.string({pool: \"**********\", length: 11});\n    };\n\n    Chance.prototype.google_analytics = function () {\n        var account = this.pad(this.natural({max: 999999}), 6);\n        var property = this.pad(this.natural({max: 99}), 2);\n\n        return 'UA-' + account + '-' + property;\n    };\n\n    Chance.prototype.hashtag = function () {\n        return '#' + this.word();\n    };\n\n    Chance.prototype.ip = function () {\n        // Todo: This could return some reserved IPs. See http://vq.io/137dgYy\n        // this should probably be updated to account for that rare as it may be\n        return this.natural({min: 1, max: 254}) + '.' +\n               this.natural({max: 255}) + '.' +\n               this.natural({max: 255}) + '.' +\n               this.natural({min: 1, max: 254});\n    };\n\n    Chance.prototype.ipv6 = function () {\n        var ip_addr = this.n(this.hash, 8, {length: 4});\n\n        return ip_addr.join(\":\");\n    };\n\n    Chance.prototype.klout = function () {\n        return this.natural({min: 1, max: 99});\n    };\n\n    Chance.prototype.semver = function (options) {\n        options = initOptions(options, { include_prerelease: true });\n\n        var range = this.pickone([\"^\", \"~\", \"<\", \">\", \"<=\", \">=\", \"=\"]);\n        if (options.range) {\n            range = options.range;\n        }\n\n        var prerelease = \"\";\n        if (options.include_prerelease) {\n            prerelease = this.weighted([\"\", \"-dev\", \"-beta\", \"-alpha\"], [50, 10, 5, 1]);\n        }\n        return range + this.rpg('3d10').join('.') + prerelease;\n    };\n\n    Chance.prototype.tlds = function () {\n        return ['com', 'org', 'edu', 'gov', 'co.uk', 'net', 'io', 'ac', 'ad', 'ae', 'af', 'ag', 'ai', 'al', 'am', 'an', 'ao', 'aq', 'ar', 'as', 'at', 'au', 'aw', 'ax', 'az', 'ba', 'bb', 'bd', 'be', 'bf', 'bg', 'bh', 'bi', 'bj', 'bm', 'bn', 'bo', 'bq', 'br', 'bs', 'bt', 'bv', 'bw', 'by', 'bz', 'ca', 'cc', 'cd', 'cf', 'cg', 'ch', 'ci', 'ck', 'cl', 'cm', 'cn', 'co', 'cr', 'cu', 'cv', 'cw', 'cx', 'cy', 'cz', 'de', 'dj', 'dk', 'dm', 'do', 'dz', 'ec', 'ee', 'eg', 'eh', 'er', 'es', 'et', 'eu', 'fi', 'fj', 'fk', 'fm', 'fo', 'fr', 'ga', 'gb', 'gd', 'ge', 'gf', 'gg', 'gh', 'gi', 'gl', 'gm', 'gn', 'gp', 'gq', 'gr', 'gs', 'gt', 'gu', 'gw', 'gy', 'hk', 'hm', 'hn', 'hr', 'ht', 'hu', 'id', 'ie', 'il', 'im', 'in', 'io', 'iq', 'ir', 'is', 'it', 'je', 'jm', 'jo', 'jp', 'ke', 'kg', 'kh', 'ki', 'km', 'kn', 'kp', 'kr', 'kw', 'ky', 'kz', 'la', 'lb', 'lc', 'li', 'lk', 'lr', 'ls', 'lt', 'lu', 'lv', 'ly', 'ma', 'mc', 'md', 'me', 'mg', 'mh', 'mk', 'ml', 'mm', 'mn', 'mo', 'mp', 'mq', 'mr', 'ms', 'mt', 'mu', 'mv', 'mw', 'mx', 'my', 'mz', 'na', 'nc', 'ne', 'nf', 'ng', 'ni', 'nl', 'no', 'np', 'nr', 'nu', 'nz', 'om', 'pa', 'pe', 'pf', 'pg', 'ph', 'pk', 'pl', 'pm', 'pn', 'pr', 'ps', 'pt', 'pw', 'py', 'qa', 're', 'ro', 'rs', 'ru', 'rw', 'sa', 'sb', 'sc', 'sd', 'se', 'sg', 'sh', 'si', 'sj', 'sk', 'sl', 'sm', 'sn', 'so', 'sr', 'ss', 'st', 'su', 'sv', 'sx', 'sy', 'sz', 'tc', 'td', 'tf', 'tg', 'th', 'tj', 'tk', 'tl', 'tm', 'tn', 'to', 'tp', 'tr', 'tt', 'tv', 'tw', 'tz', 'ua', 'ug', 'uk', 'us', 'uy', 'uz', 'va', 'vc', 've', 'vg', 'vi', 'vn', 'vu', 'wf', 'ws', 'ye', 'yt', 'za', 'zm', 'zw'];\n    };\n\n    Chance.prototype.tld = function () {\n        return this.pick(this.tlds());\n    };\n\n    Chance.prototype.twitter = function () {\n        return '@' + this.word();\n    };\n\n    Chance.prototype.url = function (options) {\n        options = initOptions(options, { protocol: \"http\", domain: this.domain(options), domain_prefix: \"\", path: this.word(), extensions: []});\n\n        var extension = options.extensions.length > 0 ? \".\" + this.pick(options.extensions) : \"\";\n        var domain = options.domain_prefix ? options.domain_prefix + \".\" + options.domain : options.domain;\n\n        return options.protocol + \"://\" + domain + \"/\" + options.path + extension;\n    };\n\n    Chance.prototype.port = function() {\n        return this.integer({min: 0, max: 65535});\n    };\n\n    Chance.prototype.locale = function (options) {\n        options = initOptions(options);\n        if (options.region){\n          return this.pick(this.get(\"locale_regions\"));\n        } else {\n          return this.pick(this.get(\"locale_languages\"));\n        }\n    };\n\n    Chance.prototype.locales = function (options) {\n      options = initOptions(options);\n      if (options.region){\n        return this.get(\"locale_regions\");\n      } else {\n        return this.get(\"locale_languages\");\n      }\n    };\n\n    // -- End Web --\n\n    // -- Location --\n\n    Chance.prototype.address = function (options) {\n        options = initOptions(options);\n        return this.natural({min: 5, max: 2000}) + ' ' + this.street(options);\n    };\n\n    Chance.prototype.altitude = function (options) {\n        options = initOptions(options, {fixed: 5, min: 0, max: 8848});\n        return this.floating({\n            min: options.min,\n            max: options.max,\n            fixed: options.fixed\n        });\n    };\n\n    Chance.prototype.areacode = function (options) {\n        options = initOptions(options, {parens : true});\n        // Don't want area codes to start with 1, or have a 9 as the second digit\n        var areacode = this.natural({min: 2, max: 9}).toString() +\n                this.natural({min: 0, max: 8}).toString() +\n                this.natural({min: 0, max: 9}).toString();\n\n        return options.parens ? '(' + areacode + ')' : areacode;\n    };\n\n    Chance.prototype.city = function () {\n        return this.capitalize(this.word({syllables: 3}));\n    };\n\n    Chance.prototype.coordinates = function (options) {\n        return this.latitude(options) + ', ' + this.longitude(options);\n    };\n\n    Chance.prototype.countries = function () {\n        return this.get(\"countries\");\n    };\n\n    Chance.prototype.country = function (options) {\n        options = initOptions(options);\n        var country = this.pick(this.countries());\n        return options.full ? country.name : country.abbreviation;\n    };\n\n    Chance.prototype.depth = function (options) {\n        options = initOptions(options, {fixed: 5, min: -10994, max: 0});\n        return this.floating({\n            min: options.min,\n            max: options.max,\n            fixed: options.fixed\n        });\n    };\n\n    Chance.prototype.geohash = function (options) {\n        options = initOptions(options, { length: 7 });\n        return this.string({ length: options.length, pool: '**********bcdefghjkmnpqrstuvwxyz' });\n    };\n\n    Chance.prototype.geojson = function (options) {\n        return this.latitude(options) + ', ' + this.longitude(options) + ', ' + this.altitude(options);\n    };\n\n    Chance.prototype.latitude = function (options) {\n        options = initOptions(options, {fixed: 5, min: -90, max: 90});\n        return this.floating({min: options.min, max: options.max, fixed: options.fixed});\n    };\n\n    Chance.prototype.longitude = function (options) {\n        options = initOptions(options, {fixed: 5, min: -180, max: 180});\n        return this.floating({min: options.min, max: options.max, fixed: options.fixed});\n    };\n\n    Chance.prototype.phone = function (options) {\n        var self = this,\n            numPick,\n            ukNum = function (parts) {\n                var section = [];\n                //fills the section part of the phone number with random numbers.\n                parts.sections.forEach(function(n) {\n                    section.push(self.string({ pool: '**********', length: n}));\n                });\n                return parts.area + section.join(' ');\n            };\n        options = initOptions(options, {\n            formatted: true,\n            country: 'us',\n            mobile: false\n        });\n        if (!options.formatted) {\n            options.parens = false;\n        }\n        var phone;\n        switch (options.country) {\n            case 'fr':\n                if (!options.mobile) {\n                    numPick = this.pick([\n                        // Valid zone and département codes.\n                        '01' + this.pick(['30', '34', '39', '40', '41', '42', '43', '44', '45', '46', '47', '48', '49', '53', '55', '56', '58', '60', '64', '69', '70', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83']) + self.string({ pool: '**********', length: 6}),\n                        '02' + this.pick(['14', '18', '22', '23', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '40', '41', '43', '44', '45', '46', '47', '48', '49', '50', '51', '52', '53', '54', '56', '57', '61', '62', '69', '72', '76', '77', '78', '85', '90', '96', '97', '98', '99']) + self.string({ pool: '**********', length: 6}),\n                        '03' + this.pick(['10', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '39', '44', '45', '51', '52', '54', '55', '57', '58', '59', '60', '61', '62', '63', '64', '65', '66', '67', '68', '69', '70', '71', '72', '73', '80', '81', '82', '83', '84', '85', '86', '87', '88', '89', '90']) + self.string({ pool: '**********', length: 6}),\n                        '04' + this.pick(['11', '13', '15', '20', '22', '26', '27', '30', '32', '34', '37', '42', '43', '44', '50', '56', '57', '63', '66', '67', '68', '69', '70', '71', '72', '73', '74', '75', '76', '77', '78', '79', '80', '81', '82', '83', '84', '85', '86', '88', '89', '90', '91', '92', '93', '94', '95', '97', '98']) + self.string({ pool: '**********', length: 6}),\n                        '05' + this.pick(['08', '16', '17', '19', '24', '31', '32', '33', '34', '35', '40', '45', '46', '47', '49', '53', '55', '56', '57', '58', '59', '61', '62', '63', '64', '65', '67', '79', '81', '82', '86', '87', '90', '94']) + self.string({ pool: '**********', length: 6}),\n                        '09' + self.string({ pool: '**********', length: 8}),\n                    ]);\n                    phone = options.formatted ? numPick.match(/../g).join(' ') : numPick;\n                } else {\n                    numPick = this.pick(['06', '07']) + self.string({ pool: '**********', length: 8});\n                    phone = options.formatted ? numPick.match(/../g).join(' ') : numPick;\n                }\n                break;\n            case 'uk':\n                if (!options.mobile) {\n                    numPick = this.pick([\n                        //valid area codes of major cities/counties followed by random numbers in required format.\n\n                        { area: '01' + this.character({ pool: '234569' }) + '1 ', sections: [3,4] },\n                        { area: '020 ' + this.character({ pool: '378' }), sections: [3,4] },\n                        { area: '023 ' + this.character({ pool: '89' }), sections: [3,4] },\n                        { area: '024 7', sections: [3,4] },\n                        { area: '028 ' + this.pick(['25','28','37','71','82','90','92','95']), sections: [2,4] },\n                        { area: '012' + this.pick(['04','08','54','76','97','98']) + ' ', sections: [6] },\n                        { area: '013' + this.pick(['63','64','84','86']) + ' ', sections: [6] },\n                        { area: '014' + this.pick(['04','20','60','61','80','88']) + ' ', sections: [6] },\n                        { area: '015' + this.pick(['24','27','62','66']) + ' ', sections: [6] },\n                        { area: '016' + this.pick(['06','29','35','47','59','95']) + ' ', sections: [6] },\n                        { area: '017' + this.pick(['26','44','50','68']) + ' ', sections: [6] },\n                        { area: '018' + this.pick(['27','37','84','97']) + ' ', sections: [6] },\n                        { area: '019' + this.pick(['00','05','35','46','49','63','95']) + ' ', sections: [6] }\n                    ]);\n                    phone = options.formatted ? ukNum(numPick) : ukNum(numPick).replace(' ', '', 'g');\n                } else {\n                    numPick = this.pick([\n                        { area: '07' + this.pick(['4','5','7','8','9']), sections: [2,6] },\n                        { area: '07624 ', sections: [6] }\n                    ]);\n                    phone = options.formatted ? ukNum(numPick) : ukNum(numPick).replace(' ', '');\n                }\n                break;\n            case 'za':\n                if (!options.mobile) {\n                    numPick = this.pick([\n                       '01' + this.pick(['0', '1', '2', '3', '4', '5', '6', '7', '8']) + self.string({ pool: '**********', length: 7}),\n                       '02' + this.pick(['1', '2', '3', '4', '7', '8']) + self.string({ pool: '**********', length: 7}),\n                       '03' + this.pick(['1', '2', '3', '5', '6', '9']) + self.string({ pool: '**********', length: 7}),\n                       '04' + this.pick(['1', '2', '3', '4', '5','6','7', '8','9']) + self.string({ pool: '**********', length: 7}),\n                       '05' + this.pick(['1', '3', '4', '6', '7', '8']) + self.string({ pool: '**********', length: 7}),\n                    ]);\n                    phone = options.formatted || numPick;\n                } else {\n                    numPick = this.pick([\n                        '060' + this.pick(['3','4','5','6','7','8','9']) + self.string({ pool: '**********', length: 6}),\n                        '061' + this.pick(['0','1','2','3','4','5','8']) + self.string({ pool: '**********', length: 6}),\n                        '06'  + self.string({ pool: '**********', length: 7}),\n                        '071' + this.pick(['0','1','2','3','4','5','6','7','8','9']) + self.string({ pool: '**********', length: 6}),\n                        '07'  + this.pick(['2','3','4','6','7','8','9']) + self.string({ pool: '**********', length: 7}),\n                        '08'  + this.pick(['0','1','2','3','4','5']) + self.string({ pool: '**********', length: 7}),\n                    ]);\n                    phone = options.formatted || numPick;\n                }\n\n                break;\n\n            case 'us':\n                var areacode = this.areacode(options).toString();\n                var exchange = this.natural({ min: 2, max: 9 }).toString() +\n                    this.natural({ min: 0, max: 9 }).toString() +\n                    this.natural({ min: 0, max: 9 }).toString();\n                var subscriber = this.natural({ min: 1000, max: 9999 }).toString(); // this could be random [0-9]{4}\n                phone = options.formatted ? areacode + ' ' + exchange + '-' + subscriber : areacode + exchange + subscriber;\n        }\n        return phone;\n    };\n\n    Chance.prototype.postal = function () {\n        // Postal District\n        var pd = this.character({pool: \"XVTSRPNKLMHJGECBA\"});\n        // Forward Sortation Area (FSA)\n        var fsa = pd + this.natural({max: 9}) + this.character({alpha: true, casing: \"upper\"});\n        // Local Delivery Unut (LDU)\n        var ldu = this.natural({max: 9}) + this.character({alpha: true, casing: \"upper\"}) + this.natural({max: 9});\n\n        return fsa + \" \" + ldu;\n    };\n\n    Chance.prototype.counties = function (options) {\n        options = initOptions(options, { country: 'uk' });\n        return this.get(\"counties\")[options.country.toLowerCase()];\n    };\n\n    Chance.prototype.county = function (options) {\n        return this.pick(this.counties(options)).name;\n    };\n\n    Chance.prototype.provinces = function (options) {\n        options = initOptions(options, { country: 'ca' });\n        return this.get(\"provinces\")[options.country.toLowerCase()];\n    };\n\n    Chance.prototype.province = function (options) {\n        return (options && options.full) ?\n            this.pick(this.provinces(options)).name :\n            this.pick(this.provinces(options)).abbreviation;\n    };\n\n    Chance.prototype.state = function (options) {\n        return (options && options.full) ?\n            this.pick(this.states(options)).name :\n            this.pick(this.states(options)).abbreviation;\n    };\n\n    Chance.prototype.states = function (options) {\n        options = initOptions(options, { country: 'us', us_states_and_dc: true } );\n\n        var states;\n\n        switch (options.country.toLowerCase()) {\n            case 'us':\n                var us_states_and_dc = this.get(\"us_states_and_dc\"),\n                    territories = this.get(\"territories\"),\n                    armed_forces = this.get(\"armed_forces\");\n\n                states = [];\n\n                if (options.us_states_and_dc) {\n                    states = states.concat(us_states_and_dc);\n                }\n                if (options.territories) {\n                    states = states.concat(territories);\n                }\n                if (options.armed_forces) {\n                    states = states.concat(armed_forces);\n                }\n                break;\n            case 'it':\n                states = this.get(\"country_regions\")[options.country.toLowerCase()];\n                break;\n            case 'uk':\n                states = this.get(\"counties\")[options.country.toLowerCase()];\n                break;\n        }\n\n        return states;\n    };\n\n    Chance.prototype.street = function (options) {\n        options = initOptions(options, { country: 'us', syllables: 2 });\n        var     street;\n\n        switch (options.country.toLowerCase()) {\n            case 'us':\n                street = this.word({ syllables: options.syllables });\n                street = this.capitalize(street);\n                street += ' ';\n                street += options.short_suffix ?\n                    this.street_suffix(options).abbreviation :\n                    this.street_suffix(options).name;\n                break;\n            case 'it':\n                street = this.word({ syllables: options.syllables });\n                street = this.capitalize(street);\n                street = (options.short_suffix ?\n                    this.street_suffix(options).abbreviation :\n                    this.street_suffix(options).name) + \" \" + street;\n                break;\n        }\n        return street;\n    };\n\n    Chance.prototype.street_suffix = function (options) {\n        options = initOptions(options, { country: 'us' });\n        return this.pick(this.street_suffixes(options));\n    };\n\n    Chance.prototype.street_suffixes = function (options) {\n        options = initOptions(options, { country: 'us' });\n        // These are the most common suffixes.\n        return this.get(\"street_suffixes\")[options.country.toLowerCase()];\n    };\n\n    // Note: only returning US zip codes, internationalization will be a whole\n    // other beast to tackle at some point.\n    Chance.prototype.zip = function (options) {\n        var zip = this.n(this.natural, 5, {max: 9});\n\n        if (options && options.plusfour === true) {\n            zip.push('-');\n            zip = zip.concat(this.n(this.natural, 4, {max: 9}));\n        }\n\n        return zip.join(\"\");\n    };\n\n    // -- End Location --\n\n    // -- Time\n\n    Chance.prototype.ampm = function () {\n        return this.bool() ? 'am' : 'pm';\n    };\n\n    Chance.prototype.date = function (options) {\n        var date_string, date;\n\n        // If interval is specified we ignore preset\n        if(options && (options.min || options.max)) {\n            options = initOptions(options, {\n                american: true,\n                string: false\n            });\n            var min = typeof options.min !== \"undefined\" ? options.min.getTime() : 1;\n            // 100,000,000 days measured relative to midnight at the beginning of 01 January, 1970 UTC. http://es5.github.io/#x15.9.1.1\n            var max = typeof options.max !== \"undefined\" ? options.max.getTime() : 8640000000000000;\n\n            date = new Date(this.integer({min: min, max: max}));\n        } else {\n            var m = this.month({raw: true});\n            var daysInMonth = m.days;\n\n            if(options && options.month) {\n                // Mod 12 to allow months outside range of 0-11 (not encouraged, but also not prevented).\n                daysInMonth = this.get('months')[((options.month % 12) + 12) % 12].days;\n            }\n\n            options = initOptions(options, {\n                year: parseInt(this.year(), 10),\n                // Necessary to subtract 1 because Date() 0-indexes month but not day or year\n                // for some reason.\n                month: m.numeric - 1,\n                day: this.natural({min: 1, max: daysInMonth}),\n                hour: this.hour({twentyfour: true}),\n                minute: this.minute(),\n                second: this.second(),\n                millisecond: this.millisecond(),\n                american: true,\n                string: false\n            });\n\n            date = new Date(options.year, options.month, options.day, options.hour, options.minute, options.second, options.millisecond);\n        }\n\n        if (options.american) {\n            // Adding 1 to the month is necessary because Date() 0-indexes\n            // months but not day for some odd reason.\n            date_string = (date.getMonth() + 1) + '/' + date.getDate() + '/' + date.getFullYear();\n        } else {\n            date_string = date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear();\n        }\n\n        return options.string ? date_string : date;\n    };\n\n    Chance.prototype.hammertime = function (options) {\n        return this.date(options).getTime();\n    };\n\n    Chance.prototype.hour = function (options) {\n        options = initOptions(options, {\n            min: options && options.twentyfour ? 0 : 1,\n            max: options && options.twentyfour ? 23 : 12\n        });\n\n        testRange(options.min < 0, \"Chance: Min cannot be less than 0.\");\n        testRange(options.twentyfour && options.max > 23, \"Chance: Max cannot be greater than 23 for twentyfour option.\");\n        testRange(!options.twentyfour && options.max > 12, \"Chance: Max cannot be greater than 12.\");\n        testRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\n\n        return this.natural({min: options.min, max: options.max});\n    };\n\n    Chance.prototype.millisecond = function () {\n        return this.natural({max: 999});\n    };\n\n    Chance.prototype.minute = Chance.prototype.second = function (options) {\n        options = initOptions(options, {min: 0, max: 59});\n\n        testRange(options.min < 0, \"Chance: Min cannot be less than 0.\");\n        testRange(options.max > 59, \"Chance: Max cannot be greater than 59.\");\n        testRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\n\n        return this.natural({min: options.min, max: options.max});\n    };\n\n    Chance.prototype.month = function (options) {\n        options = initOptions(options, {min: 1, max: 12});\n\n        testRange(options.min < 1, \"Chance: Min cannot be less than 1.\");\n        testRange(options.max > 12, \"Chance: Max cannot be greater than 12.\");\n        testRange(options.min > options.max, \"Chance: Min cannot be greater than Max.\");\n\n        var month = this.pick(this.months().slice(options.min - 1, options.max));\n        return options.raw ? month : month.name;\n    };\n\n    Chance.prototype.months = function () {\n        return this.get(\"months\");\n    };\n\n    Chance.prototype.second = function () {\n        return this.natural({max: 59});\n    };\n\n    Chance.prototype.timestamp = function () {\n        return this.natural({min: 1, max: parseInt(new Date().getTime() / 1000, 10)});\n    };\n\n    Chance.prototype.weekday = function (options) {\n        options = initOptions(options, {weekday_only: false});\n        var weekdays = [\"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\"];\n        if (!options.weekday_only) {\n            weekdays.push(\"Saturday\");\n            weekdays.push(\"Sunday\");\n        }\n        return this.pickone(weekdays);\n    };\n\n    Chance.prototype.year = function (options) {\n        // Default to current year as min if none specified\n        options = initOptions(options, {min: new Date().getFullYear()});\n\n        // Default to one century after current year as max if none specified\n        options.max = (typeof options.max !== \"undefined\") ? options.max : options.min + 100;\n\n        return this.natural(options).toString();\n    };\n\n    // -- End Time\n\n    // -- Finance --\n\n    Chance.prototype.cc = function (options) {\n        options = initOptions(options);\n\n        var type, number, to_generate;\n\n        type = (options.type) ?\n                    this.cc_type({ name: options.type, raw: true }) :\n                    this.cc_type({ raw: true });\n\n        number = type.prefix.split(\"\");\n        to_generate = type.length - type.prefix.length - 1;\n\n        // Generates n - 1 digits\n        number = number.concat(this.n(this.integer, to_generate, {min: 0, max: 9}));\n\n        // Generates the last digit according to Luhn algorithm\n        number.push(this.luhn_calculate(number.join(\"\")));\n\n        return number.join(\"\");\n    };\n\n    Chance.prototype.cc_types = function () {\n        // http://en.wikipedia.org/wiki/Bank_card_number#Issuer_identification_number_.28IIN.29\n        return this.get(\"cc_types\");\n    };\n\n    Chance.prototype.cc_type = function (options) {\n        options = initOptions(options);\n        var types = this.cc_types(),\n            type = null;\n\n        if (options.name) {\n            for (var i = 0; i < types.length; i++) {\n                // Accept either name or short_name to specify card type\n                if (types[i].name === options.name || types[i].short_name === options.name) {\n                    type = types[i];\n                    break;\n                }\n            }\n            if (type === null) {\n                throw new RangeError(\"Chance: Credit card type '\" + options.name + \"' is not supported\");\n            }\n        } else {\n            type = this.pick(types);\n        }\n\n        return options.raw ? type : type.name;\n    };\n\n    // return all world currency by ISO 4217\n    Chance.prototype.currency_types = function () {\n        return this.get(\"currency_types\");\n    };\n\n    // return random world currency by ISO 4217\n    Chance.prototype.currency = function () {\n        return this.pick(this.currency_types());\n    };\n\n    // return all timezones available\n    Chance.prototype.timezones = function () {\n        return this.get(\"timezones\");\n    };\n\n    // return random timezone\n    Chance.prototype.timezone = function () {\n        return this.pick(this.timezones());\n    };\n\n    //Return random correct currency exchange pair (e.g. EUR/USD) or array of currency code\n    Chance.prototype.currency_pair = function (returnAsString) {\n        var currencies = this.unique(this.currency, 2, {\n            comparator: function(arr, val) {\n\n                return arr.reduce(function(acc, item) {\n                    // If a match has been found, short circuit check and just return\n                    return acc || (item.code === val.code);\n                }, false);\n            }\n        });\n\n        if (returnAsString) {\n            return currencies[0].code + '/' + currencies[1].code;\n        } else {\n            return currencies;\n        }\n    };\n\n    Chance.prototype.dollar = function (options) {\n        // By default, a somewhat more sane max for dollar than all available numbers\n        options = initOptions(options, {max : 10000, min : 0});\n\n        var dollar = this.floating({min: options.min, max: options.max, fixed: 2}).toString(),\n            cents = dollar.split('.')[1];\n\n        if (cents === undefined) {\n            dollar += '.00';\n        } else if (cents.length < 2) {\n            dollar = dollar + '0';\n        }\n\n        if (dollar < 0) {\n            return '-$' + dollar.replace('-', '');\n        } else {\n            return '$' + dollar;\n        }\n    };\n\n    Chance.prototype.euro = function (options) {\n        return Number(this.dollar(options).replace(\"$\", \"\")).toLocaleString() + \"€\";\n    };\n\n    Chance.prototype.exp = function (options) {\n        options = initOptions(options);\n        var exp = {};\n\n        exp.year = this.exp_year();\n\n        // If the year is this year, need to ensure month is greater than the\n        // current month or this expiration will not be valid\n        if (exp.year === (new Date().getFullYear()).toString()) {\n            exp.month = this.exp_month({future: true});\n        } else {\n            exp.month = this.exp_month();\n        }\n\n        return options.raw ? exp : exp.month + '/' + exp.year;\n    };\n\n    Chance.prototype.exp_month = function (options) {\n        options = initOptions(options);\n        var month, month_int,\n            // Date object months are 0 indexed\n            curMonth = new Date().getMonth() + 1;\n\n        if (options.future && (curMonth !== 12)) {\n            do {\n                month = this.month({raw: true}).numeric;\n                month_int = parseInt(month, 10);\n            } while (month_int <= curMonth);\n        } else {\n            month = this.month({raw: true}).numeric;\n        }\n\n        return month;\n    };\n\n    Chance.prototype.exp_year = function () {\n        var curMonth = new Date().getMonth() + 1,\n            curYear = new Date().getFullYear();\n\n        return this.year({min: ((curMonth === 12) ? (curYear + 1) : curYear), max: (curYear + 10)});\n    };\n\n    Chance.prototype.vat = function (options) {\n        options = initOptions(options, { country: 'it' });\n        switch (options.country.toLowerCase()) {\n            case 'it':\n                return this.it_vat();\n        }\n    };\n\n    /**\n     * Generate a string matching IBAN pattern (https://en.wikipedia.org/wiki/International_Bank_Account_Number).\n     * No country-specific formats support (yet)\n     */\n    Chance.prototype.iban = function () {\n        var alpha = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n        var alphanum = alpha + '**********';\n        var iban =\n            this.string({ length: 2, pool: alpha }) +\n            this.pad(this.integer({ min: 0, max: 99 }), 2) +\n            this.string({ length: 4, pool: alphanum }) +\n            this.pad(this.natural(), this.natural({ min: 6, max: 26 }));\n        return iban;\n    };\n\n    // -- End Finance\n\n    // -- Regional\n\n    Chance.prototype.it_vat = function () {\n        var it_vat = this.natural({min: 1, max: 1800000});\n\n        it_vat = this.pad(it_vat, 7) + this.pad(this.pick(this.provinces({ country: 'it' })).code, 3);\n        return it_vat + this.luhn_calculate(it_vat);\n    };\n\n    /*\n     * this generator is written following the official algorithm\n     * all data can be passed explicitely or randomized by calling chance.cf() without options\n     * the code does not check that the input data is valid (it goes beyond the scope of the generator)\n     *\n     * @param  [Object] options = { first: first name,\n     *                              last: last name,\n     *                              gender: female|male,\n                                    birthday: JavaScript date object,\n                                    city: string(4), 1 letter + 3 numbers\n                                   }\n     * @return [string] codice fiscale\n     *\n    */\n    Chance.prototype.cf = function (options) {\n        options = options || {};\n        var gender = !!options.gender ? options.gender : this.gender(),\n            first = !!options.first ? options.first : this.first( { gender: gender, nationality: 'it'} ),\n            last = !!options.last ? options.last : this.last( { nationality: 'it'} ),\n            birthday = !!options.birthday ? options.birthday : this.birthday(),\n            city = !!options.city ? options.city : this.pickone(['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'L', 'M', 'Z']) + this.pad(this.natural({max:999}), 3),\n            cf = [],\n            name_generator = function(name, isLast) {\n                var temp,\n                    return_value = [];\n\n                if (name.length < 3) {\n                    return_value = name.split(\"\").concat(\"XXX\".split(\"\")).splice(0,3);\n                }\n                else {\n                    temp = name.toUpperCase().split('').map(function(c){\n                        return (\"BCDFGHJKLMNPRSTVWZ\".indexOf(c) !== -1) ? c : undefined;\n                    }).join('');\n                    if (temp.length > 3) {\n                        if (isLast) {\n                            temp = temp.substr(0,3);\n                        } else {\n                            temp = temp[0] + temp.substr(2,2);\n                        }\n                    }\n                    if (temp.length < 3) {\n                        return_value = temp;\n                        temp = name.toUpperCase().split('').map(function(c){\n                            return (\"AEIOU\".indexOf(c) !== -1) ? c : undefined;\n                        }).join('').substr(0, 3 - return_value.length);\n                    }\n                    return_value = return_value + temp;\n                }\n\n                return return_value;\n            },\n            date_generator = function(birthday, gender, that) {\n                var lettermonths = ['A', 'B', 'C', 'D', 'E', 'H', 'L', 'M', 'P', 'R', 'S', 'T'];\n\n                return  birthday.getFullYear().toString().substr(2) +\n                        lettermonths[birthday.getMonth()] +\n                        that.pad(birthday.getDate() + ((gender.toLowerCase() === \"female\") ? 40 : 0), 2);\n            },\n            checkdigit_generator = function(cf) {\n                var range1 = \"**********ABCDEFGHIJKLMNOPQRSTUVWXYZ\",\n                    range2 = \"ABCDEFGHIJABCDEFGHIJKLMNOPQRSTUVWXYZ\",\n                    evens  = \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\",\n                    odds   = \"BAKPLCQDREVOSFTGUHMINJWZYX\",\n                    digit  = 0;\n\n\n                for(var i = 0; i < 15; i++) {\n                    if (i % 2 !== 0) {\n                        digit += evens.indexOf(range2[range1.indexOf(cf[i])]);\n                    }\n                    else {\n                        digit +=  odds.indexOf(range2[range1.indexOf(cf[i])]);\n                    }\n                }\n                return evens[digit % 26];\n            };\n\n        cf = cf.concat(name_generator(last, true), name_generator(first), date_generator(birthday, gender, this), city.toUpperCase().split(\"\")).join(\"\");\n        cf += checkdigit_generator(cf.toUpperCase(), this);\n\n        return cf.toUpperCase();\n    };\n\n    Chance.prototype.pl_pesel = function () {\n        var number = this.natural({min: 1, max: *********9});\n        var arr = this.pad(number, 10).split('');\n        for (var i = 0; i < arr.length; i++) {\n            arr[i] = parseInt(arr[i]);\n        }\n\n        var controlNumber = (1 * arr[0] + 3 * arr[1] + 7 * arr[2] + 9 * arr[3] + 1 * arr[4] + 3 * arr[5] + 7 * arr[6] + 9 * arr[7] + 1 * arr[8] + 3 * arr[9]) % 10;\n        if(controlNumber !== 0) {\n            controlNumber = 10 - controlNumber;\n        }\n\n        return arr.join('') + controlNumber;\n    };\n\n    Chance.prototype.pl_nip = function () {\n        var number = this.natural({min: 1, max: *********});\n        var arr = this.pad(number, 9).split('');\n        for (var i = 0; i < arr.length; i++) {\n            arr[i] = parseInt(arr[i]);\n        }\n\n        var controlNumber = (6 * arr[0] + 5 * arr[1] + 7 * arr[2] + 2 * arr[3] + 3 * arr[4] + 4 * arr[5] + 5 * arr[6] + 6 * arr[7] + 7 * arr[8]) % 11;\n        if(controlNumber === 10) {\n            return this.pl_nip();\n        }\n\n        return arr.join('') + controlNumber;\n    };\n\n    Chance.prototype.pl_regon = function () {\n        var number = this.natural({min: 1, max: 99999999});\n        var arr = this.pad(number, 8).split('');\n        for (var i = 0; i < arr.length; i++) {\n            arr[i] = parseInt(arr[i]);\n        }\n\n        var controlNumber = (8 * arr[0] + 9 * arr[1] + 2 * arr[2] + 3 * arr[3] + 4 * arr[4] + 5 * arr[5] + 6 * arr[6] + 7 * arr[7]) % 11;\n        if(controlNumber === 10) {\n            controlNumber = 0;\n        }\n\n        return arr.join('') + controlNumber;\n    };\n\n    // -- End Regional\n\n    // -- Music --\n\n    Chance.prototype.note = function(options) {\n      // choices for 'notes' option:\n      // flatKey - chromatic scale with flat notes (default)\n      // sharpKey - chromatic scale with sharp notes\n      // flats - just flat notes\n      // sharps - just sharp notes\n      // naturals - just natural notes\n      // all - naturals, sharps and flats\n      options = initOptions(options, { notes : 'flatKey'});\n      var scales = {\n        naturals: ['C', 'D', 'E', 'F', 'G', 'A', 'B'],\n        flats: ['D♭', 'E♭', 'G♭', 'A♭', 'B♭'],\n        sharps: ['C♯', 'D♯', 'F♯', 'G♯', 'A♯']\n      };\n      scales.all = scales.naturals.concat(scales.flats.concat(scales.sharps))\n      scales.flatKey = scales.naturals.concat(scales.flats)\n      scales.sharpKey = scales.naturals.concat(scales.sharps)\n      return this.pickone(scales[options.notes]);\n    }\n\n    Chance.prototype.midi_note = function(options) {\n      var min = 0;\n      var max = 127;\n      options = initOptions(options, { min : min, max : max });\n      return this.integer({min: options.min, max: options.max});\n    }\n\n    Chance.prototype.chord_quality = function(options) {\n      options = initOptions(options, { jazz: true });\n      var chord_qualities = ['maj', 'min', 'aug', 'dim'];\n      if (options.jazz){\n        chord_qualities = [\n          'maj7',\n          'min7',\n          '7',\n          'sus',\n          'dim',\n          'ø'\n        ];\n      }\n      return this.pickone(chord_qualities);\n    }\n\n    Chance.prototype.chord = function (options) {\n      options = initOptions(options);\n      return this.note(options) + this.chord_quality(options);\n    }\n\n    Chance.prototype.tempo = function (options) {\n      var min = 40;\n      var max = 320;\n      options = initOptions(options, {min: min, max: max});\n      return this.integer({min: options.min, max: options.max});\n    }\n\n    // -- End Music\n\n    // -- Miscellaneous --\n\n    // Coin - Flip, flip, flipadelphia\n    Chance.prototype.coin = function(options) {\n      return this.bool() ? \"heads\" : \"tails\";\n    }\n\n    // Dice - For all the board game geeks out there, myself included ;)\n    function diceFn (range) {\n        return function () {\n            return this.natural(range);\n        };\n    }\n    Chance.prototype.d4 = diceFn({min: 1, max: 4});\n    Chance.prototype.d6 = diceFn({min: 1, max: 6});\n    Chance.prototype.d8 = diceFn({min: 1, max: 8});\n    Chance.prototype.d10 = diceFn({min: 1, max: 10});\n    Chance.prototype.d12 = diceFn({min: 1, max: 12});\n    Chance.prototype.d20 = diceFn({min: 1, max: 20});\n    Chance.prototype.d30 = diceFn({min: 1, max: 30});\n    Chance.prototype.d100 = diceFn({min: 1, max: 100});\n\n    Chance.prototype.rpg = function (thrown, options) {\n        options = initOptions(options);\n        if (!thrown) {\n            throw new RangeError(\"Chance: A type of die roll must be included\");\n        } else {\n            var bits = thrown.toLowerCase().split(\"d\"),\n                rolls = [];\n\n            if (bits.length !== 2 || !parseInt(bits[0], 10) || !parseInt(bits[1], 10)) {\n                throw new Error(\"Chance: Invalid format provided. Please provide #d# where the first # is the number of dice to roll, the second # is the max of each die\");\n            }\n            for (var i = bits[0]; i > 0; i--) {\n                rolls[i - 1] = this.natural({min: 1, max: bits[1]});\n            }\n            return (typeof options.sum !== 'undefined' && options.sum) ? rolls.reduce(function (p, c) { return p + c; }) : rolls;\n        }\n    };\n\n    // Guid\n    Chance.prototype.guid = function (options) {\n        options = initOptions(options, { version: 5 });\n\n        var guid_pool = \"abcdef**********\",\n            variant_pool = \"ab89\",\n            guid = this.string({ pool: guid_pool, length: 8 }) + '-' +\n                   this.string({ pool: guid_pool, length: 4 }) + '-' +\n                   // The Version\n                   options.version +\n                   this.string({ pool: guid_pool, length: 3 }) + '-' +\n                   // The Variant\n                   this.string({ pool: variant_pool, length: 1 }) +\n                   this.string({ pool: guid_pool, length: 3 }) + '-' +\n                   this.string({ pool: guid_pool, length: 12 });\n        return guid;\n    };\n\n    // Hash\n    Chance.prototype.hash = function (options) {\n        options = initOptions(options, {length : 40, casing: 'lower'});\n        var pool = options.casing === 'upper' ? HEX_POOL.toUpperCase() : HEX_POOL;\n        return this.string({pool: pool, length: options.length});\n    };\n\n    Chance.prototype.luhn_check = function (num) {\n        var str = num.toString();\n        var checkDigit = +str.substring(str.length - 1);\n        return checkDigit === this.luhn_calculate(+str.substring(0, str.length - 1));\n    };\n\n    Chance.prototype.luhn_calculate = function (num) {\n        var digits = num.toString().split(\"\").reverse();\n        var sum = 0;\n        var digit;\n\n        for (var i = 0, l = digits.length; l > i; ++i) {\n            digit = +digits[i];\n            if (i % 2 === 0) {\n                digit *= 2;\n                if (digit > 9) {\n                    digit -= 9;\n                }\n            }\n            sum += digit;\n        }\n        return (sum * 9) % 10;\n    };\n\n    // MD5 Hash\n    Chance.prototype.md5 = function(options) {\n        var opts = { str: '', key: null, raw: false };\n\n        if (!options) {\n            opts.str = this.string();\n            options = {};\n        }\n        else if (typeof options === 'string') {\n            opts.str = options;\n            options = {};\n        }\n        else if (typeof options !== 'object') {\n            return null;\n        }\n        else if(options.constructor === 'Array') {\n            return null;\n        }\n\n        opts = initOptions(options, opts);\n\n        if(!opts.str){\n            throw new Error('A parameter is required to return an md5 hash.');\n        }\n\n        return this.bimd5.md5(opts.str, opts.key, opts.raw);\n    };\n\n    /**\n     * #Description:\n     * =====================================================\n     * Generate random file name with extension\n     *\n     * The argument provide extension type\n     * -> raster\n     * -> vector\n     * -> 3d\n     * -> document\n     *\n     * If nothing is provided the function return random file name with random\n     * extension type of any kind\n     *\n     * The user can validate the file name length range\n     * If nothing provided the generated file name is random\n     *\n     * #Extension Pool :\n     * * Currently the supported extensions are\n     *  -> some of the most popular raster image extensions\n     *  -> some of the most popular vector image extensions\n     *  -> some of the most popular 3d image extensions\n     *  -> some of the most popular document extensions\n     *\n     * #Examples :\n     * =====================================================\n     *\n     * Return random file name with random extension. The file extension\n     * is provided by a predefined collection of extensions. More about the extension\n     * pool can be found in #Extension Pool section\n     *\n     * chance.file()\n     * => dsfsdhjf.xml\n     *\n     * In order to generate a file name with specific length, specify the\n     * length property and integer value. The extension is going to be random\n     *\n     * chance.file({length : 10})\n     * => asrtineqos.pdf\n     *\n     * In order to generate file with extension from some of the predefined groups\n     * of the extension pool just specify the extension pool category in fileType property\n     *\n     * chance.file({fileType : 'raster'})\n     * => dshgssds.psd\n     *\n     * You can provide specific extension for your files\n     * chance.file({extension : 'html'})\n     * => djfsd.html\n     *\n     * Or you could pass custom collection of extensions by array or by object\n     * chance.file({extensions : [...]})\n     * => dhgsdsd.psd\n     *\n     * chance.file({extensions : { key : [...], key : [...]}})\n     * => djsfksdjsd.xml\n     *\n     * @param  [collection] options\n     * @return [string]\n     *\n     */\n    Chance.prototype.file = function(options) {\n\n        var fileOptions = options || {};\n        var poolCollectionKey = \"fileExtension\";\n        var typeRange   = Object.keys(this.get(\"fileExtension\"));//['raster', 'vector', '3d', 'document'];\n        var fileName;\n        var fileExtension;\n\n        // Generate random file name\n        fileName = this.word({length : fileOptions.length});\n\n        // Generate file by specific extension provided by the user\n        if(fileOptions.extension) {\n\n            fileExtension = fileOptions.extension;\n            return (fileName + '.' + fileExtension);\n        }\n\n        // Generate file by specific extension collection\n        if(fileOptions.extensions) {\n\n            if(Array.isArray(fileOptions.extensions)) {\n\n                fileExtension = this.pickone(fileOptions.extensions);\n                return (fileName + '.' + fileExtension);\n            }\n            else if(fileOptions.extensions.constructor === Object) {\n\n                var extensionObjectCollection = fileOptions.extensions;\n                var keys = Object.keys(extensionObjectCollection);\n\n                fileExtension = this.pickone(extensionObjectCollection[this.pickone(keys)]);\n                return (fileName + '.' + fileExtension);\n            }\n\n            throw new Error(\"Chance: Extensions must be an Array or Object\");\n        }\n\n        // Generate file extension based on specific file type\n        if(fileOptions.fileType) {\n\n            var fileType = fileOptions.fileType;\n            if(typeRange.indexOf(fileType) !== -1) {\n\n                fileExtension = this.pickone(this.get(poolCollectionKey)[fileType]);\n                return (fileName + '.' + fileExtension);\n            }\n\n            throw new RangeError(\"Chance: Expect file type value to be 'raster', 'vector', '3d' or 'document'\");\n        }\n\n        // Generate random file name if no extension options are passed\n        fileExtension = this.pickone(this.get(poolCollectionKey)[this.pickone(typeRange)]);\n        return (fileName + '.' + fileExtension);\n    };\n\n    var data = {\n\n        firstNames: {\n            \"male\": {\n                \"en\": [\"James\", \"John\", \"Robert\", \"Michael\", \"William\", \"David\", \"Richard\", \"Joseph\", \"Charles\", \"Thomas\", \"Christopher\", \"Daniel\", \"Matthew\", \"George\", \"Donald\", \"Anthony\", \"Paul\", \"Mark\", \"Edward\", \"Steven\", \"Kenneth\", \"Andrew\", \"Brian\", \"Joshua\", \"Kevin\", \"Ronald\", \"Timothy\", \"Jason\", \"Jeffrey\", \"Frank\", \"Gary\", \"Ryan\", \"Nicholas\", \"Eric\", \"Stephen\", \"Jacob\", \"Larry\", \"Jonathan\", \"Scott\", \"Raymond\", \"Justin\", \"Brandon\", \"Gregory\", \"Samuel\", \"Benjamin\", \"Patrick\", \"Jack\", \"Henry\", \"Walter\", \"Dennis\", \"Jerry\", \"Alexander\", \"Peter\", \"Tyler\", \"Douglas\", \"Harold\", \"Aaron\", \"Jose\", \"Adam\", \"Arthur\", \"Zachary\", \"Carl\", \"Nathan\", \"Albert\", \"Kyle\", \"Lawrence\", \"Joe\", \"Willie\", \"Gerald\", \"Roger\", \"Keith\", \"Jeremy\", \"Terry\", \"Harry\", \"Ralph\", \"Sean\", \"Jesse\", \"Roy\", \"Louis\", \"Billy\", \"Austin\", \"Bruce\", \"Eugene\", \"Christian\", \"Bryan\", \"Wayne\", \"Russell\", \"Howard\", \"Fred\", \"Ethan\", \"Jordan\", \"Philip\", \"Alan\", \"Juan\", \"Randy\", \"Vincent\", \"Bobby\", \"Dylan\", \"Johnny\", \"Phillip\", \"Victor\", \"Clarence\", \"Ernest\", \"Martin\", \"Craig\", \"Stanley\", \"Shawn\", \"Travis\", \"Bradley\", \"Leonard\", \"Earl\", \"Gabriel\", \"Jimmy\", \"Francis\", \"Todd\", \"Noah\", \"Danny\", \"Dale\", \"Cody\", \"Carlos\", \"Allen\", \"Frederick\", \"Logan\", \"Curtis\", \"Alex\", \"Joel\", \"Luis\", \"Norman\", \"Marvin\", \"Glenn\", \"Tony\", \"Nathaniel\", \"Rodney\", \"Melvin\", \"Alfred\", \"Steve\", \"Cameron\", \"Chad\", \"Edwin\", \"Caleb\", \"Evan\", \"Antonio\", \"Lee\", \"Herbert\", \"Jeffery\", \"Isaac\", \"Derek\", \"Ricky\", \"Marcus\", \"Theodore\", \"Elijah\", \"Luke\", \"Jesus\", \"Eddie\", \"Troy\", \"Mike\", \"Dustin\", \"Ray\", \"Adrian\", \"Bernard\", \"Leroy\", \"Angel\", \"Randall\", \"Wesley\", \"Ian\", \"Jared\", \"Mason\", \"Hunter\", \"Calvin\", \"Oscar\", \"Clifford\", \"Jay\", \"Shane\", \"Ronnie\", \"Barry\", \"Lucas\", \"Corey\", \"Manuel\", \"Leo\", \"Tommy\", \"Warren\", \"Jackson\", \"Isaiah\", \"Connor\", \"Don\", \"Dean\", \"Jon\", \"Julian\", \"Miguel\", \"Bill\", \"Lloyd\", \"Charlie\", \"Mitchell\", \"Leon\", \"Jerome\", \"Darrell\", \"Jeremiah\", \"Alvin\", \"Brett\", \"Seth\", \"Floyd\", \"Jim\", \"Blake\", \"Micheal\", \"Gordon\", \"Trevor\", \"Lewis\", \"Erik\", \"Edgar\", \"Vernon\", \"Devin\", \"Gavin\", \"Jayden\", \"Chris\", \"Clyde\", \"Tom\", \"Derrick\", \"Mario\", \"Brent\", \"Marc\", \"Herman\", \"Chase\", \"Dominic\", \"Ricardo\", \"Franklin\", \"Maurice\", \"Max\", \"Aiden\", \"Owen\", \"Lester\", \"Gilbert\", \"Elmer\", \"Gene\", \"Francisco\", \"Glen\", \"Cory\", \"Garrett\", \"Clayton\", \"Sam\", \"Jorge\", \"Chester\", \"Alejandro\", \"Jeff\", \"Harvey\", \"Milton\", \"Cole\", \"Ivan\", \"Andre\", \"Duane\", \"Landon\"],\n                // Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0163\n                \"it\": [\"Adolfo\", \"Alberto\", \"Aldo\", \"Alessandro\", \"Alessio\", \"Alfredo\", \"Alvaro\", \"Andrea\", \"Angelo\", \"Angiolo\", \"Antonino\", \"Antonio\", \"Attilio\", \"Benito\", \"Bernardo\", \"Bruno\", \"Carlo\", \"Cesare\", \"Christian\", \"Claudio\", \"Corrado\", \"Cosimo\", \"Cristian\", \"Cristiano\", \"Daniele\", \"Dario\", \"David\", \"Davide\", \"Diego\", \"Dino\", \"Domenico\", \"Duccio\", \"Edoardo\", \"Elia\", \"Elio\", \"Emanuele\", \"Emiliano\", \"Emilio\", \"Enrico\", \"Enzo\", \"Ettore\", \"Fabio\", \"Fabrizio\", \"Federico\", \"Ferdinando\", \"Fernando\", \"Filippo\", \"Francesco\", \"Franco\", \"Gabriele\", \"Giacomo\", \"Giampaolo\", \"Giampiero\", \"Giancarlo\", \"Gianfranco\", \"Gianluca\", \"Gianmarco\", \"Gianni\", \"Gino\", \"Giorgio\", \"Giovanni\", \"Giuliano\", \"Giulio\", \"Giuseppe\", \"Graziano\", \"Gregorio\", \"Guido\", \"Iacopo\", \"Jacopo\", \"Lapo\", \"Leonardo\", \"Lorenzo\", \"Luca\", \"Luciano\", \"Luigi\", \"Manuel\", \"Marcello\", \"Marco\", \"Marino\", \"Mario\", \"Massimiliano\", \"Massimo\", \"Matteo\", \"Mattia\", \"Maurizio\", \"Mauro\", \"Michele\", \"Mirko\", \"Mohamed\", \"Nello\", \"Neri\", \"Niccolò\", \"Nicola\", \"Osvaldo\", \"Otello\", \"Paolo\", \"Pier Luigi\", \"Piero\", \"Pietro\", \"Raffaele\", \"Remo\", \"Renato\", \"Renzo\", \"Riccardo\", \"Roberto\", \"Rolando\", \"Romano\", \"Salvatore\", \"Samuele\", \"Sandro\", \"Sergio\", \"Silvano\", \"Simone\", \"Stefano\", \"Thomas\", \"Tommaso\", \"Ubaldo\", \"Ugo\", \"Umberto\", \"Valerio\", \"Valter\", \"Vasco\", \"Vincenzo\", \"Vittorio\"],\n                // Data taken from http://www.svbkindernamen.nl/int/nl/kindernamen/index.html\n                \"nl\": [\"Aaron\",\"Abel\",\"Adam\",\"Adriaan\",\"Albert\",\"Alexander\",\"Ali\",\"Arjen\",\"Arno\",\"Bart\",\"Bas\",\"Bastiaan\",\"Benjamin\",\"Bob\", \"Boris\",\"Bram\",\"Brent\",\"Cas\",\"Casper\",\"Chris\",\"Christiaan\",\"Cornelis\",\"Daan\",\"Daley\",\"Damian\",\"Dani\",\"Daniel\",\"Daniël\",\"David\",\"Dean\",\"Dirk\",\"Dylan\",\"Egbert\",\"Elijah\",\"Erik\",\"Erwin\",\"Evert\",\"Ezra\",\"Fabian\",\"Fedde\",\"Finn\",\"Florian\",\"Floris\",\"Frank\",\"Frans\",\"Frederik\",\"Freek\",\"Geert\",\"Gerard\",\"Gerben\",\"Gerrit\",\"Gijs\",\"Guus\",\"Hans\",\"Hendrik\",\"Henk\",\"Herman\",\"Hidde\",\"Hugo\",\"Jaap\",\"Jan Jaap\",\"Jan-Willem\",\"Jack\",\"Jacob\",\"Jan\",\"Jason\",\"Jasper\",\"Jayden\",\"Jelle\",\"Jelte\",\"Jens\",\"Jeroen\",\"Jesse\",\"Jim\",\"Job\",\"Joep\",\"Johannes\",\"John\",\"Jonathan\",\"Joris\",\"Joshua\",\"Joël\",\"Julian\",\"Kees\",\"Kevin\",\"Koen\",\"Lars\",\"Laurens\",\"Leendert\",\"Lennard\",\"Lodewijk\",\"Luc\",\"Luca\",\"Lucas\",\"Lukas\",\"Luuk\",\"Maarten\",\"Marcus\",\"Martijn\",\"Martin\",\"Matthijs\",\"Maurits\",\"Max\",\"Mees\",\"Melle\",\"Mick\",\"Mika\",\"Milan\",\"Mohamed\",\"Mohammed\",\"Morris\",\"Muhammed\",\"Nathan\",\"Nick\",\"Nico\",\"Niek\",\"Niels\",\"Noah\",\"Noud\",\"Olivier\",\"Oscar\",\"Owen\",\"Paul\",\"Pepijn\",\"Peter\",\"Pieter\",\"Pim\",\"Quinten\",\"Reinier\",\"Rens\",\"Robin\",\"Ruben\",\"Sam\",\"Samuel\",\"Sander\",\"Sebastiaan\",\"Sem\",\"Sep\",\"Sepp\",\"Siem\",\"Simon\",\"Stan\",\"Stef\",\"Steven\",\"Stijn\",\"Sven\",\"Teun\",\"Thijmen\",\"Thijs\",\"Thomas\",\"Tijn\",\"Tim\",\"Timo\",\"Tobias\",\"Tom\",\"Victor\",\"Vince\",\"Willem\",\"Wim\",\"Wouter\",\"Yusuf\"]\n            },\n\n            \"female\": {\n                \"en\": [\"Mary\", \"Emma\", \"Elizabeth\", \"Minnie\", \"Margaret\", \"Ida\", \"Alice\", \"Bertha\", \"Sarah\", \"Annie\", \"Clara\", \"Ella\", \"Florence\", \"Cora\", \"Martha\", \"Laura\", \"Nellie\", \"Grace\", \"Carrie\", \"Maude\", \"Mabel\", \"Bessie\", \"Jennie\", \"Gertrude\", \"Julia\", \"Hattie\", \"Edith\", \"Mattie\", \"Rose\", \"Catherine\", \"Lillian\", \"Ada\", \"Lillie\", \"Helen\", \"Jessie\", \"Louise\", \"Ethel\", \"Lula\", \"Myrtle\", \"Eva\", \"Frances\", \"Lena\", \"Lucy\", \"Edna\", \"Maggie\", \"Pearl\", \"Daisy\", \"Fannie\", \"Josephine\", \"Dora\", \"Rosa\", \"Katherine\", \"Agnes\", \"Marie\", \"Nora\", \"May\", \"Mamie\", \"Blanche\", \"Stella\", \"Ellen\", \"Nancy\", \"Effie\", \"Sallie\", \"Nettie\", \"Della\", \"Lizzie\", \"Flora\", \"Susie\", \"Maud\", \"Mae\", \"Etta\", \"Harriet\", \"Sadie\", \"Caroline\", \"Katie\", \"Lydia\", \"Elsie\", \"Kate\", \"Susan\", \"Mollie\", \"Alma\", \"Addie\", \"Georgia\", \"Eliza\", \"Lulu\", \"Nannie\", \"Lottie\", \"Amanda\", \"Belle\", \"Charlotte\", \"Rebecca\", \"Ruth\", \"Viola\", \"Olive\", \"Amelia\", \"Hannah\", \"Jane\", \"Virginia\", \"Emily\", \"Matilda\", \"Irene\", \"Kathryn\", \"Esther\", \"Willie\", \"Henrietta\", \"Ollie\", \"Amy\", \"Rachel\", \"Sara\", \"Estella\", \"Theresa\", \"Augusta\", \"Ora\", \"Pauline\", \"Josie\", \"Lola\", \"Sophia\", \"Leona\", \"Anne\", \"Mildred\", \"Ann\", \"Beulah\", \"Callie\", \"Lou\", \"Delia\", \"Eleanor\", \"Barbara\", \"Iva\", \"Louisa\", \"Maria\", \"Mayme\", \"Evelyn\", \"Estelle\", \"Nina\", \"Betty\", \"Marion\", \"Bettie\", \"Dorothy\", \"Luella\", \"Inez\", \"Lela\", \"Rosie\", \"Allie\", \"Millie\", \"Janie\", \"Cornelia\", \"Victoria\", \"Ruby\", \"Winifred\", \"Alta\", \"Celia\", \"Christine\", \"Beatrice\", \"Birdie\", \"Harriett\", \"Mable\", \"Myra\", \"Sophie\", \"Tillie\", \"Isabel\", \"Sylvia\", \"Carolyn\", \"Isabelle\", \"Leila\", \"Sally\", \"Ina\", \"Essie\", \"Bertie\", \"Nell\", \"Alberta\", \"Katharine\", \"Lora\", \"Rena\", \"Mina\", \"Rhoda\", \"Mathilda\", \"Abbie\", \"Eula\", \"Dollie\", \"Hettie\", \"Eunice\", \"Fanny\", \"Ola\", \"Lenora\", \"Adelaide\", \"Christina\", \"Lelia\", \"Nelle\", \"Sue\", \"Johanna\", \"Lilly\", \"Lucinda\", \"Minerva\", \"Lettie\", \"Roxie\", \"Cynthia\", \"Helena\", \"Hilda\", \"Hulda\", \"Bernice\", \"Genevieve\", \"Jean\", \"Cordelia\", \"Marian\", \"Francis\", \"Jeanette\", \"Adeline\", \"Gussie\", \"Leah\", \"Lois\", \"Lura\", \"Mittie\", \"Hallie\", \"Isabella\", \"Olga\", \"Phoebe\", \"Teresa\", \"Hester\", \"Lida\", \"Lina\", \"Winnie\", \"Claudia\", \"Marguerite\", \"Vera\", \"Cecelia\", \"Bess\", \"Emilie\", \"Rosetta\", \"Verna\", \"Myrtie\", \"Cecilia\", \"Elva\", \"Olivia\", \"Ophelia\", \"Georgie\", \"Elnora\", \"Violet\", \"Adele\", \"Lily\", \"Linnie\", \"Loretta\", \"Madge\", \"Polly\", \"Virgie\", \"Eugenia\", \"Lucile\", \"Lucille\", \"Mabelle\", \"Rosalie\"],\n                // Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0162\n                \"it\": [\"Ada\", \"Adriana\", \"Alessandra\", \"Alessia\", \"Alice\", \"Angela\", \"Anna\", \"Anna Maria\", \"Annalisa\", \"Annita\", \"Annunziata\", \"Antonella\", \"Arianna\", \"Asia\", \"Assunta\", \"Aurora\", \"Barbara\", \"Beatrice\", \"Benedetta\", \"Bianca\", \"Bruna\", \"Camilla\", \"Carla\", \"Carlotta\", \"Carmela\", \"Carolina\", \"Caterina\", \"Catia\", \"Cecilia\", \"Chiara\", \"Cinzia\", \"Clara\", \"Claudia\", \"Costanza\", \"Cristina\", \"Daniela\", \"Debora\", \"Diletta\", \"Dina\", \"Donatella\", \"Elena\", \"Eleonora\", \"Elisa\", \"Elisabetta\", \"Emanuela\", \"Emma\", \"Eva\", \"Federica\", \"Fernanda\", \"Fiorella\", \"Fiorenza\", \"Flora\", \"Franca\", \"Francesca\", \"Gabriella\", \"Gaia\", \"Gemma\", \"Giada\", \"Gianna\", \"Gina\", \"Ginevra\", \"Giorgia\", \"Giovanna\", \"Giulia\", \"Giuliana\", \"Giuseppa\", \"Giuseppina\", \"Grazia\", \"Graziella\", \"Greta\", \"Ida\", \"Ilaria\", \"Ines\", \"Iolanda\", \"Irene\", \"Irma\", \"Isabella\", \"Jessica\", \"Laura\", \"Lea\", \"Letizia\", \"Licia\", \"Lidia\", \"Liliana\", \"Lina\", \"Linda\", \"Lisa\", \"Livia\", \"Loretta\", \"Luana\", \"Lucia\", \"Luciana\", \"Lucrezia\", \"Luisa\", \"Manuela\", \"Mara\", \"Marcella\", \"Margherita\", \"Maria\", \"Maria Cristina\", \"Maria Grazia\", \"Maria Luisa\", \"Maria Pia\", \"Maria Teresa\", \"Marina\", \"Marisa\", \"Marta\", \"Martina\", \"Marzia\", \"Matilde\", \"Melissa\", \"Michela\", \"Milena\", \"Mirella\", \"Monica\", \"Natalina\", \"Nella\", \"Nicoletta\", \"Noemi\", \"Olga\", \"Paola\", \"Patrizia\", \"Piera\", \"Pierina\", \"Raffaella\", \"Rebecca\", \"Renata\", \"Rina\", \"Rita\", \"Roberta\", \"Rosa\", \"Rosanna\", \"Rossana\", \"Rossella\", \"Sabrina\", \"Sandra\", \"Sara\", \"Serena\", \"Silvana\", \"Silvia\", \"Simona\", \"Simonetta\", \"Sofia\", \"Sonia\", \"Stefania\", \"Susanna\", \"Teresa\", \"Tina\", \"Tiziana\", \"Tosca\", \"Valentina\", \"Valeria\", \"Vanda\", \"Vanessa\", \"Vanna\", \"Vera\", \"Veronica\", \"Vilma\", \"Viola\", \"Virginia\", \"Vittoria\"],\n                // Data taken from http://www.svbkindernamen.nl/int/nl/kindernamen/index.html\n                \"nl\": [\"Ada\", \"Arianne\", \"Afke\", \"Amanda\", \"Amber\", \"Amy\", \"Aniek\", \"Anita\", \"Anja\", \"Anna\", \"Anne\", \"Annelies\", \"Annemarie\", \"Annette\", \"Anouk\", \"Astrid\", \"Aukje\", \"Barbara\", \"Bianca\", \"Carla\", \"Carlijn\", \"Carolien\", \"Chantal\", \"Charlotte\", \"Claudia\", \"Daniëlle\", \"Debora\", \"Diane\", \"Dora\", \"Eline\", \"Elise\", \"Ella\", \"Ellen\", \"Emma\", \"Esmee\", \"Evelien\", \"Esther\", \"Erica\", \"Eva\", \"Femke\", \"Fleur\", \"Floor\", \"Froukje\", \"Gea\", \"Gerda\", \"Hanna\", \"Hanneke\", \"Heleen\", \"Hilde\", \"Ilona\", \"Ina\", \"Inge\", \"Ingrid\", \"Iris\", \"Isabel\", \"Isabelle\", \"Janneke\", \"Jasmijn\", \"Jeanine\", \"Jennifer\", \"Jessica\", \"Johanna\", \"Joke\", \"Julia\", \"Julie\", \"Karen\", \"Karin\", \"Katja\", \"Kim\", \"Lara\", \"Laura\", \"Lena\", \"Lianne\", \"Lieke\", \"Lilian\", \"Linda\", \"Lisa\", \"Lisanne\", \"Lotte\", \"Louise\", \"Maaike\", \"Manon\", \"Marga\", \"Maria\", \"Marissa\", \"Marit\", \"Marjolein\", \"Martine\", \"Marleen\", \"Melissa\", \"Merel\", \"Miranda\", \"Michelle\", \"Mirjam\", \"Mirthe\", \"Naomi\", \"Natalie\", 'Nienke', \"Nina\", \"Noortje\", \"Olivia\", \"Patricia\", \"Paula\", \"Paulien\", \"Ramona\", \"Ria\", \"Rianne\", \"Roos\", \"Rosanne\", \"Ruth\", \"Sabrina\", \"Sandra\", \"Sanne\", \"Sara\", \"Saskia\", \"Silvia\", \"Sofia\", \"Sophie\", \"Sonja\", \"Suzanne\", \"Tamara\", \"Tess\", \"Tessa\", \"Tineke\", \"Valerie\", \"Vanessa\", \"Veerle\", \"Vera\", \"Victoria\", \"Wendy\", \"Willeke\", \"Yvonne\", \"Zoë\"]\n            }\n        },\n\n        lastNames: {\n            \"en\": ['Smith', 'Johnson', 'Williams', 'Jones', 'Brown', 'Davis', 'Miller', 'Wilson', 'Moore', 'Taylor', 'Anderson', 'Thomas', 'Jackson', 'White', 'Harris', 'Martin', 'Thompson', 'Garcia', 'Martinez', 'Robinson', 'Clark', 'Rodriguez', 'Lewis', 'Lee', 'Walker', 'Hall', 'Allen', 'Young', 'Hernandez', 'King', 'Wright', 'Lopez', 'Hill', 'Scott', 'Green', 'Adams', 'Baker', 'Gonzalez', 'Nelson', 'Carter', 'Mitchell', 'Perez', 'Roberts', 'Turner', 'Phillips', 'Campbell', 'Parker', 'Evans', 'Edwards', 'Collins', 'Stewart', 'Sanchez', 'Morris', 'Rogers', 'Reed', 'Cook', 'Morgan', 'Bell', 'Murphy', 'Bailey', 'Rivera', 'Cooper', 'Richardson', 'Cox', 'Howard', 'Ward', 'Torres', 'Peterson', 'Gray', 'Ramirez', 'James', 'Watson', 'Brooks', 'Kelly', 'Sanders', 'Price', 'Bennett', 'Wood', 'Barnes', 'Ross', 'Henderson', 'Coleman', 'Jenkins', 'Perry', 'Powell', 'Long', 'Patterson', 'Hughes', 'Flores', 'Washington', 'Butler', 'Simmons', 'Foster', 'Gonzales', 'Bryant', 'Alexander', 'Russell', 'Griffin', 'Diaz', 'Hayes', 'Myers', 'Ford', 'Hamilton', 'Graham', 'Sullivan', 'Wallace', 'Woods', 'Cole', 'West', 'Jordan', 'Owens', 'Reynolds', 'Fisher', 'Ellis', 'Harrison', 'Gibson', 'McDonald', 'Cruz', 'Marshall', 'Ortiz', 'Gomez', 'Murray', 'Freeman', 'Wells', 'Webb', 'Simpson', 'Stevens', 'Tucker', 'Porter', 'Hunter', 'Hicks', 'Crawford', 'Henry', 'Boyd', 'Mason', 'Morales', 'Kennedy', 'Warren', 'Dixon', 'Ramos', 'Reyes', 'Burns', 'Gordon', 'Shaw', 'Holmes', 'Rice', 'Robertson', 'Hunt', 'Black', 'Daniels', 'Palmer', 'Mills', 'Nichols', 'Grant', 'Knight', 'Ferguson', 'Rose', 'Stone', 'Hawkins', 'Dunn', 'Perkins', 'Hudson', 'Spencer', 'Gardner', 'Stephens', 'Payne', 'Pierce', 'Berry', 'Matthews', 'Arnold', 'Wagner', 'Willis', 'Ray', 'Watkins', 'Olson', 'Carroll', 'Duncan', 'Snyder', 'Hart', 'Cunningham', 'Bradley', 'Lane', 'Andrews', 'Ruiz', 'Harper', 'Fox', 'Riley', 'Armstrong', 'Carpenter', 'Weaver', 'Greene', 'Lawrence', 'Elliott', 'Chavez', 'Sims', 'Austin', 'Peters', 'Kelley', 'Franklin', 'Lawson', 'Fields', 'Gutierrez', 'Ryan', 'Schmidt', 'Carr', 'Vasquez', 'Castillo', 'Wheeler', 'Chapman', 'Oliver', 'Montgomery', 'Richards', 'Williamson', 'Johnston', 'Banks', 'Meyer', 'Bishop', 'McCoy', 'Howell', 'Alvarez', 'Morrison', 'Hansen', 'Fernandez', 'Garza', 'Harvey', 'Little', 'Burton', 'Stanley', 'Nguyen', 'George', 'Jacobs', 'Reid', 'Kim', 'Fuller', 'Lynch', 'Dean', 'Gilbert', 'Garrett', 'Romero', 'Welch', 'Larson', 'Frazier', 'Burke', 'Hanson', 'Day', 'Mendoza', 'Moreno', 'Bowman', 'Medina', 'Fowler', 'Brewer', 'Hoffman', 'Carlson', 'Silva', 'Pearson', 'Holland', 'Douglas', 'Fleming', 'Jensen', 'Vargas', 'Byrd', 'Davidson', 'Hopkins', 'May', 'Terry', 'Herrera', 'Wade', 'Soto', 'Walters', 'Curtis', 'Neal', 'Caldwell', 'Lowe', 'Jennings', 'Barnett', 'Graves', 'Jimenez', 'Horton', 'Shelton', 'Barrett', 'Obrien', 'Castro', 'Sutton', 'Gregory', 'McKinney', 'Lucas', 'Miles', 'Craig', 'Rodriquez', 'Chambers', 'Holt', 'Lambert', 'Fletcher', 'Watts', 'Bates', 'Hale', 'Rhodes', 'Pena', 'Beck', 'Newman', 'Haynes', 'McDaniel', 'Mendez', 'Bush', 'Vaughn', 'Parks', 'Dawson', 'Santiago', 'Norris', 'Hardy', 'Love', 'Steele', 'Curry', 'Powers', 'Schultz', 'Barker', 'Guzman', 'Page', 'Munoz', 'Ball', 'Keller', 'Chandler', 'Weber', 'Leonard', 'Walsh', 'Lyons', 'Ramsey', 'Wolfe', 'Schneider', 'Mullins', 'Benson', 'Sharp', 'Bowen', 'Daniel', 'Barber', 'Cummings', 'Hines', 'Baldwin', 'Griffith', 'Valdez', 'Hubbard', 'Salazar', 'Reeves', 'Warner', 'Stevenson', 'Burgess', 'Santos', 'Tate', 'Cross', 'Garner', 'Mann', 'Mack', 'Moss', 'Thornton', 'Dennis', 'McGee', 'Farmer', 'Delgado', 'Aguilar', 'Vega', 'Glover', 'Manning', 'Cohen', 'Harmon', 'Rodgers', 'Robbins', 'Newton', 'Todd', 'Blair', 'Higgins', 'Ingram', 'Reese', 'Cannon', 'Strickland', 'Townsend', 'Potter', 'Goodwin', 'Walton', 'Rowe', 'Hampton', 'Ortega', 'Patton', 'Swanson', 'Joseph', 'Francis', 'Goodman', 'Maldonado', 'Yates', 'Becker', 'Erickson', 'Hodges', 'Rios', 'Conner', 'Adkins', 'Webster', 'Norman', 'Malone', 'Hammond', 'Flowers', 'Cobb', 'Moody', 'Quinn', 'Blake', 'Maxwell', 'Pope', 'Floyd', 'Osborne', 'Paul', 'McCarthy', 'Guerrero', 'Lindsey', 'Estrada', 'Sandoval', 'Gibbs', 'Tyler', 'Gross', 'Fitzgerald', 'Stokes', 'Doyle', 'Sherman', 'Saunders', 'Wise', 'Colon', 'Gill', 'Alvarado', 'Greer', 'Padilla', 'Simon', 'Waters', 'Nunez', 'Ballard', 'Schwartz', 'McBride', 'Houston', 'Christensen', 'Klein', 'Pratt', 'Briggs', 'Parsons', 'McLaughlin', 'Zimmerman', 'French', 'Buchanan', 'Moran', 'Copeland', 'Roy', 'Pittman', 'Brady', 'McCormick', 'Holloway', 'Brock', 'Poole', 'Frank', 'Logan', 'Owen', 'Bass', 'Marsh', 'Drake', 'Wong', 'Jefferson', 'Park', 'Morton', 'Abbott', 'Sparks', 'Patrick', 'Norton', 'Huff', 'Clayton', 'Massey', 'Lloyd', 'Figueroa', 'Carson', 'Bowers', 'Roberson', 'Barton', 'Tran', 'Lamb', 'Harrington', 'Casey', 'Boone', 'Cortez', 'Clarke', 'Mathis', 'Singleton', 'Wilkins', 'Cain', 'Bryan', 'Underwood', 'Hogan', 'McKenzie', 'Collier', 'Luna', 'Phelps', 'McGuire', 'Allison', 'Bridges', 'Wilkerson', 'Nash', 'Summers', 'Atkins'],\n                // Data taken from http://www.dati.gov.it/dataset/comune-di-firenze_0164 (first 1000)\n            \"it\": [\"Acciai\", \"Aglietti\", \"Agostini\", \"Agresti\", \"Ahmed\", \"Aiazzi\", \"Albanese\", \"Alberti\", \"Alessi\", \"Alfani\", \"Alinari\", \"Alterini\", \"Amato\", \"Ammannati\", \"Ancillotti\", \"Andrei\", \"Andreini\", \"Andreoni\", \"Angeli\", \"Anichini\", \"Antonelli\", \"Antonini\", \"Arena\", \"Ariani\", \"Arnetoli\", \"Arrighi\", \"Baccani\", \"Baccetti\", \"Bacci\", \"Bacherini\", \"Badii\", \"Baggiani\", \"Baglioni\", \"Bagni\", \"Bagnoli\", \"Baldassini\", \"Baldi\", \"Baldini\", \"Ballerini\", \"Balli\", \"Ballini\", \"Balloni\", \"Bambi\", \"Banchi\", \"Bandinelli\", \"Bandini\", \"Bani\", \"Barbetti\", \"Barbieri\", \"Barchielli\", \"Bardazzi\", \"Bardelli\", \"Bardi\", \"Barducci\", \"Bargellini\", \"Bargiacchi\", \"Barni\", \"Baroncelli\", \"Baroncini\", \"Barone\", \"Baroni\", \"Baronti\", \"Bartalesi\", \"Bartoletti\", \"Bartoli\", \"Bartolini\", \"Bartoloni\", \"Bartolozzi\", \"Basagni\", \"Basile\", \"Bassi\", \"Batacchi\", \"Battaglia\", \"Battaglini\", \"Bausi\", \"Becagli\", \"Becattini\", \"Becchi\", \"Becucci\", \"Bellandi\", \"Bellesi\", \"Belli\", \"Bellini\", \"Bellucci\", \"Bencini\", \"Benedetti\", \"Benelli\", \"Beni\", \"Benini\", \"Bensi\", \"Benucci\", \"Benvenuti\", \"Berlincioni\", \"Bernacchioni\", \"Bernardi\", \"Bernardini\", \"Berni\", \"Bernini\", \"Bertelli\", \"Berti\", \"Bertini\", \"Bessi\", \"Betti\", \"Bettini\", \"Biagi\", \"Biagini\", \"Biagioni\", \"Biagiotti\", \"Biancalani\", \"Bianchi\", \"Bianchini\", \"Bianco\", \"Biffoli\", \"Bigazzi\", \"Bigi\", \"Biliotti\", \"Billi\", \"Binazzi\", \"Bindi\", \"Bini\", \"Biondi\", \"Bizzarri\", \"Bocci\", \"Bogani\", \"Bolognesi\", \"Bonaiuti\", \"Bonanni\", \"Bonciani\", \"Boncinelli\", \"Bondi\", \"Bonechi\", \"Bongini\", \"Boni\", \"Bonini\", \"Borchi\", \"Boretti\", \"Borghi\", \"Borghini\", \"Borgioli\", \"Borri\", \"Borselli\", \"Boschi\", \"Bottai\", \"Bracci\", \"Braccini\", \"Brandi\", \"Braschi\", \"Bravi\", \"Brazzini\", \"Breschi\", \"Brilli\", \"Brizzi\", \"Brogelli\", \"Brogi\", \"Brogioni\", \"Brunelli\", \"Brunetti\", \"Bruni\", \"Bruno\", \"Brunori\", \"Bruschi\", \"Bucci\", \"Bucciarelli\", \"Buccioni\", \"Bucelli\", \"Bulli\", \"Burberi\", \"Burchi\", \"Burgassi\", \"Burroni\", \"Bussotti\", \"Buti\", \"Caciolli\", \"Caiani\", \"Calabrese\", \"Calamai\", \"Calamandrei\", \"Caldini\", \"Calo'\", \"Calonaci\", \"Calosi\", \"Calvelli\", \"Cambi\", \"Camiciottoli\", \"Cammelli\", \"Cammilli\", \"Campolmi\", \"Cantini\", \"Capanni\", \"Capecchi\", \"Caponi\", \"Cappelletti\", \"Cappelli\", \"Cappellini\", \"Cappugi\", \"Capretti\", \"Caputo\", \"Carbone\", \"Carboni\", \"Cardini\", \"Carlesi\", \"Carletti\", \"Carli\", \"Caroti\", \"Carotti\", \"Carrai\", \"Carraresi\", \"Carta\", \"Caruso\", \"Casalini\", \"Casati\", \"Caselli\", \"Casini\", \"Castagnoli\", \"Castellani\", \"Castelli\", \"Castellucci\", \"Catalano\", \"Catarzi\", \"Catelani\", \"Cavaciocchi\", \"Cavallaro\", \"Cavallini\", \"Cavicchi\", \"Cavini\", \"Ceccarelli\", \"Ceccatelli\", \"Ceccherelli\", \"Ceccherini\", \"Cecchi\", \"Cecchini\", \"Cecconi\", \"Cei\", \"Cellai\", \"Celli\", \"Cellini\", \"Cencetti\", \"Ceni\", \"Cenni\", \"Cerbai\", \"Cesari\", \"Ceseri\", \"Checcacci\", \"Checchi\", \"Checcucci\", \"Cheli\", \"Chellini\", \"Chen\", \"Cheng\", \"Cherici\", \"Cherubini\", \"Chiaramonti\", \"Chiarantini\", \"Chiarelli\", \"Chiari\", \"Chiarini\", \"Chiarugi\", \"Chiavacci\", \"Chiesi\", \"Chimenti\", \"Chini\", \"Chirici\", \"Chiti\", \"Ciabatti\", \"Ciampi\", \"Cianchi\", \"Cianfanelli\", \"Cianferoni\", \"Ciani\", \"Ciapetti\", \"Ciappi\", \"Ciardi\", \"Ciatti\", \"Cicali\", \"Ciccone\", \"Cinelli\", \"Cini\", \"Ciobanu\", \"Ciolli\", \"Cioni\", \"Cipriani\", \"Cirillo\", \"Cirri\", \"Ciucchi\", \"Ciuffi\", \"Ciulli\", \"Ciullini\", \"Clemente\", \"Cocchi\", \"Cognome\", \"Coli\", \"Collini\", \"Colombo\", \"Colzi\", \"Comparini\", \"Conforti\", \"Consigli\", \"Conte\", \"Conti\", \"Contini\", \"Coppini\", \"Coppola\", \"Corsi\", \"Corsini\", \"Corti\", \"Cortini\", \"Cosi\", \"Costa\", \"Costantini\", \"Costantino\", \"Cozzi\", \"Cresci\", \"Crescioli\", \"Cresti\", \"Crini\", \"Curradi\", \"D'Agostino\", \"D'Alessandro\", \"D'Amico\", \"D'Angelo\", \"Daddi\", \"Dainelli\", \"Dallai\", \"Danti\", \"Davitti\", \"De Angelis\", \"De Luca\", \"De Marco\", \"De Rosa\", \"De Santis\", \"De Simone\", \"De Vita\", \"Degl'Innocenti\", \"Degli Innocenti\", \"Dei\", \"Del Lungo\", \"Del Re\", \"Di Marco\", \"Di Stefano\", \"Dini\", \"Diop\", \"Dobre\", \"Dolfi\", \"Donati\", \"Dondoli\", \"Dong\", \"Donnini\", \"Ducci\", \"Dumitru\", \"Ermini\", \"Esposito\", \"Evangelisti\", \"Fabbri\", \"Fabbrini\", \"Fabbrizzi\", \"Fabbroni\", \"Fabbrucci\", \"Fabiani\", \"Facchini\", \"Faggi\", \"Fagioli\", \"Failli\", \"Faini\", \"Falciani\", \"Falcini\", \"Falcone\", \"Fallani\", \"Falorni\", \"Falsini\", \"Falugiani\", \"Fancelli\", \"Fanelli\", \"Fanetti\", \"Fanfani\", \"Fani\", \"Fantappie'\", \"Fantechi\", \"Fanti\", \"Fantini\", \"Fantoni\", \"Farina\", \"Fattori\", \"Favilli\", \"Fedi\", \"Fei\", \"Ferrante\", \"Ferrara\", \"Ferrari\", \"Ferraro\", \"Ferretti\", \"Ferri\", \"Ferrini\", \"Ferroni\", \"Fiaschi\", \"Fibbi\", \"Fiesoli\", \"Filippi\", \"Filippini\", \"Fini\", \"Fioravanti\", \"Fiore\", \"Fiorentini\", \"Fiorini\", \"Fissi\", \"Focardi\", \"Foggi\", \"Fontana\", \"Fontanelli\", \"Fontani\", \"Forconi\", \"Formigli\", \"Forte\", \"Forti\", \"Fortini\", \"Fossati\", \"Fossi\", \"Francalanci\", \"Franceschi\", \"Franceschini\", \"Franchi\", \"Franchini\", \"Franci\", \"Francini\", \"Francioni\", \"Franco\", \"Frassineti\", \"Frati\", \"Fratini\", \"Frilli\", \"Frizzi\", \"Frosali\", \"Frosini\", \"Frullini\", \"Fusco\", \"Fusi\", \"Gabbrielli\", \"Gabellini\", \"Gagliardi\", \"Galanti\", \"Galardi\", \"Galeotti\", \"Galletti\", \"Galli\", \"Gallo\", \"Gallori\", \"Gambacciani\", \"Gargani\", \"Garofalo\", \"Garuglieri\", \"Gashi\", \"Gasperini\", \"Gatti\", \"Gelli\", \"Gensini\", \"Gentile\", \"Gentili\", \"Geri\", \"Gerini\", \"Gheri\", \"Ghini\", \"Giachetti\", \"Giachi\", \"Giacomelli\", \"Gianassi\", \"Giani\", \"Giannelli\", \"Giannetti\", \"Gianni\", \"Giannini\", \"Giannoni\", \"Giannotti\", \"Giannozzi\", \"Gigli\", \"Giordano\", \"Giorgetti\", \"Giorgi\", \"Giovacchini\", \"Giovannelli\", \"Giovannetti\", \"Giovannini\", \"Giovannoni\", \"Giuliani\", \"Giunti\", \"Giuntini\", \"Giusti\", \"Gonnelli\", \"Goretti\", \"Gori\", \"Gradi\", \"Gramigni\", \"Grassi\", \"Grasso\", \"Graziani\", \"Grazzini\", \"Greco\", \"Grifoni\", \"Grillo\", \"Grimaldi\", \"Grossi\", \"Gualtieri\", \"Guarducci\", \"Guarino\", \"Guarnieri\", \"Guasti\", \"Guerra\", \"Guerri\", \"Guerrini\", \"Guidi\", \"Guidotti\", \"He\", \"Hoxha\", \"Hu\", \"Huang\", \"Iandelli\", \"Ignesti\", \"Innocenti\", \"Jin\", \"La Rosa\", \"Lai\", \"Landi\", \"Landini\", \"Lanini\", \"Lapi\", \"Lapini\", \"Lari\", \"Lascialfari\", \"Lastrucci\", \"Latini\", \"Lazzeri\", \"Lazzerini\", \"Lelli\", \"Lenzi\", \"Leonardi\", \"Leoncini\", \"Leone\", \"Leoni\", \"Lepri\", \"Li\", \"Liao\", \"Lin\", \"Linari\", \"Lippi\", \"Lisi\", \"Livi\", \"Lombardi\", \"Lombardini\", \"Lombardo\", \"Longo\", \"Lopez\", \"Lorenzi\", \"Lorenzini\", \"Lorini\", \"Lotti\", \"Lu\", \"Lucchesi\", \"Lucherini\", \"Lunghi\", \"Lupi\", \"Madiai\", \"Maestrini\", \"Maffei\", \"Maggi\", \"Maggini\", \"Magherini\", \"Magini\", \"Magnani\", \"Magnelli\", \"Magni\", \"Magnolfi\", \"Magrini\", \"Malavolti\", \"Malevolti\", \"Manca\", \"Mancini\", \"Manetti\", \"Manfredi\", \"Mangani\", \"Mannelli\", \"Manni\", \"Mannini\", \"Mannucci\", \"Manuelli\", \"Manzini\", \"Marcelli\", \"Marchese\", \"Marchetti\", \"Marchi\", \"Marchiani\", \"Marchionni\", \"Marconi\", \"Marcucci\", \"Margheri\", \"Mari\", \"Mariani\", \"Marilli\", \"Marinai\", \"Marinari\", \"Marinelli\", \"Marini\", \"Marino\", \"Mariotti\", \"Marsili\", \"Martelli\", \"Martinelli\", \"Martini\", \"Martino\", \"Marzi\", \"Masi\", \"Masini\", \"Masoni\", \"Massai\", \"Materassi\", \"Mattei\", \"Matteini\", \"Matteucci\", \"Matteuzzi\", \"Mattioli\", \"Mattolini\", \"Matucci\", \"Mauro\", \"Mazzanti\", \"Mazzei\", \"Mazzetti\", \"Mazzi\", \"Mazzini\", \"Mazzocchi\", \"Mazzoli\", \"Mazzoni\", \"Mazzuoli\", \"Meacci\", \"Mecocci\", \"Meini\", \"Melani\", \"Mele\", \"Meli\", \"Mengoni\", \"Menichetti\", \"Meoni\", \"Merlini\", \"Messeri\", \"Messina\", \"Meucci\", \"Miccinesi\", \"Miceli\", \"Micheli\", \"Michelini\", \"Michelozzi\", \"Migliori\", \"Migliorini\", \"Milani\", \"Miniati\", \"Misuri\", \"Monaco\", \"Montagnani\", \"Montagni\", \"Montanari\", \"Montelatici\", \"Monti\", \"Montigiani\", \"Montini\", \"Morandi\", \"Morandini\", \"Morelli\", \"Moretti\", \"Morganti\", \"Mori\", \"Morini\", \"Moroni\", \"Morozzi\", \"Mugnai\", \"Mugnaini\", \"Mustafa\", \"Naldi\", \"Naldini\", \"Nannelli\", \"Nanni\", \"Nannini\", \"Nannucci\", \"Nardi\", \"Nardini\", \"Nardoni\", \"Natali\", \"Ndiaye\", \"Nencetti\", \"Nencini\", \"Nencioni\", \"Neri\", \"Nesi\", \"Nesti\", \"Niccolai\", \"Niccoli\", \"Niccolini\", \"Nigi\", \"Nistri\", \"Nocentini\", \"Noferini\", \"Novelli\", \"Nucci\", \"Nuti\", \"Nutini\", \"Oliva\", \"Olivieri\", \"Olmi\", \"Orlandi\", \"Orlandini\", \"Orlando\", \"Orsini\", \"Ortolani\", \"Ottanelli\", \"Pacciani\", \"Pace\", \"Paci\", \"Pacini\", \"Pagani\", \"Pagano\", \"Paggetti\", \"Pagliai\", \"Pagni\", \"Pagnini\", \"Paladini\", \"Palagi\", \"Palchetti\", \"Palloni\", \"Palmieri\", \"Palumbo\", \"Pampaloni\", \"Pancani\", \"Pandolfi\", \"Pandolfini\", \"Panerai\", \"Panichi\", \"Paoletti\", \"Paoli\", \"Paolini\", \"Papi\", \"Papini\", \"Papucci\", \"Parenti\", \"Parigi\", \"Parisi\", \"Parri\", \"Parrini\", \"Pasquini\", \"Passeri\", \"Pecchioli\", \"Pecorini\", \"Pellegrini\", \"Pepi\", \"Perini\", \"Perrone\", \"Peruzzi\", \"Pesci\", \"Pestelli\", \"Petri\", \"Petrini\", \"Petrucci\", \"Pettini\", \"Pezzati\", \"Pezzatini\", \"Piani\", \"Piazza\", \"Piazzesi\", \"Piazzini\", \"Piccardi\", \"Picchi\", \"Piccini\", \"Piccioli\", \"Pieraccini\", \"Pieraccioni\", \"Pieralli\", \"Pierattini\", \"Pieri\", \"Pierini\", \"Pieroni\", \"Pietrini\", \"Pini\", \"Pinna\", \"Pinto\", \"Pinzani\", \"Pinzauti\", \"Piras\", \"Pisani\", \"Pistolesi\", \"Poggesi\", \"Poggi\", \"Poggiali\", \"Poggiolini\", \"Poli\", \"Pollastri\", \"Porciani\", \"Pozzi\", \"Pratellesi\", \"Pratesi\", \"Prosperi\", \"Pruneti\", \"Pucci\", \"Puccini\", \"Puccioni\", \"Pugi\", \"Pugliese\", \"Puliti\", \"Querci\", \"Quercioli\", \"Raddi\", \"Radu\", \"Raffaelli\", \"Ragazzini\", \"Ranfagni\", \"Ranieri\", \"Rastrelli\", \"Raugei\", \"Raveggi\", \"Renai\", \"Renzi\", \"Rettori\", \"Ricci\", \"Ricciardi\", \"Ridi\", \"Ridolfi\", \"Rigacci\", \"Righi\", \"Righini\", \"Rinaldi\", \"Risaliti\", \"Ristori\", \"Rizzo\", \"Rocchi\", \"Rocchini\", \"Rogai\", \"Romagnoli\", \"Romanelli\", \"Romani\", \"Romano\", \"Romei\", \"Romeo\", \"Romiti\", \"Romoli\", \"Romolini\", \"Rontini\", \"Rosati\", \"Roselli\", \"Rosi\", \"Rossetti\", \"Rossi\", \"Rossini\", \"Rovai\", \"Ruggeri\", \"Ruggiero\", \"Russo\", \"Sabatini\", \"Saccardi\", \"Sacchetti\", \"Sacchi\", \"Sacco\", \"Salerno\", \"Salimbeni\", \"Salucci\", \"Salvadori\", \"Salvestrini\", \"Salvi\", \"Salvini\", \"Sanesi\", \"Sani\", \"Sanna\", \"Santi\", \"Santini\", \"Santoni\", \"Santoro\", \"Santucci\", \"Sardi\", \"Sarri\", \"Sarti\", \"Sassi\", \"Sbolci\", \"Scali\", \"Scarpelli\", \"Scarselli\", \"Scopetani\", \"Secci\", \"Selvi\", \"Senatori\", \"Senesi\", \"Serafini\", \"Sereni\", \"Serra\", \"Sestini\", \"Sguanci\", \"Sieni\", \"Signorini\", \"Silvestri\", \"Simoncini\", \"Simonetti\", \"Simoni\", \"Singh\", \"Sodi\", \"Soldi\", \"Somigli\", \"Sorbi\", \"Sorelli\", \"Sorrentino\", \"Sottili\", \"Spina\", \"Spinelli\", \"Staccioli\", \"Staderini\", \"Stefanelli\", \"Stefani\", \"Stefanini\", \"Stella\", \"Susini\", \"Tacchi\", \"Tacconi\", \"Taddei\", \"Tagliaferri\", \"Tamburini\", \"Tanganelli\", \"Tani\", \"Tanini\", \"Tapinassi\", \"Tarchi\", \"Tarchiani\", \"Targioni\", \"Tassi\", \"Tassini\", \"Tempesti\", \"Terzani\", \"Tesi\", \"Testa\", \"Testi\", \"Tilli\", \"Tinti\", \"Tirinnanzi\", \"Toccafondi\", \"Tofanari\", \"Tofani\", \"Tognaccini\", \"Tonelli\", \"Tonini\", \"Torelli\", \"Torrini\", \"Tosi\", \"Toti\", \"Tozzi\", \"Trambusti\", \"Trapani\", \"Tucci\", \"Turchi\", \"Ugolini\", \"Ulivi\", \"Valente\", \"Valenti\", \"Valentini\", \"Vangelisti\", \"Vanni\", \"Vannini\", \"Vannoni\", \"Vannozzi\", \"Vannucchi\", \"Vannucci\", \"Ventura\", \"Venturi\", \"Venturini\", \"Vestri\", \"Vettori\", \"Vichi\", \"Viciani\", \"Vieri\", \"Vigiani\", \"Vignoli\", \"Vignolini\", \"Vignozzi\", \"Villani\", \"Vinci\", \"Visani\", \"Vitale\", \"Vitali\", \"Viti\", \"Viviani\", \"Vivoli\", \"Volpe\", \"Volpi\", \"Wang\", \"Wu\", \"Xu\", \"Yang\", \"Ye\", \"Zagli\", \"Zani\", \"Zanieri\", \"Zanobini\", \"Zecchi\", \"Zetti\", \"Zhang\", \"Zheng\", \"Zhou\", \"Zhu\", \"Zingoni\", \"Zini\", \"Zoppi\"],\n            // http://www.voornamelijk.nl/meest-voorkomende-achternamen-in-nederland-en-amsterdam/\n            \"nl\":[\"Albers\", \"Alblas\", \"Appelman\", \"Baars\", \"Baas\", \"Bakker\", \"Blank\", \"Bleeker\", \"Blok\", \"Blom\", \"Boer\", \"Boers\", \"Boldewijn\", \"Boon\", \"Boot\", \"Bos\", \"Bosch\", \"Bosma\", \"Bosman\", \"Bouma\", \"Bouman\", \"Bouwman\", \"Brands\", \"Brouwer\", \"Burger\", \"Buijs\", \"Buitenhuis\", \"Ceder\", \"Cohen\", \"Dekker\", \"Dekkers\", \"Dijkman\", \"Dijkstra\", \"Driessen\", \"Drost\", \"Engel\", \"Evers\", \"Faber\", \"Franke\", \"Gerritsen\", \"Goedhart\", \"Goossens\", \"Groen\", \"Groenenberg\", \"Groot\", \"Haan\", \"Hart\", \"Heemskerk\", \"Hendriks\", \"Hermans\", \"Hoekstra\", \"Hofman\", \"Hopman\", \"Huisman\", \"Jacobs\", \"Jansen\", \"Janssen\", \"Jonker\", \"Jaspers\", \"Keijzer\", \"Klaassen\", \"Klein\", \"Koek\", \"Koenders\", \"Kok\", \"Kool\", \"Koopman\", \"Koopmans\", \"Koning\", \"Koster\", \"Kramer\", \"Kroon\", \"Kuijpers\", \"Kuiper\", \"Kuipers\", \"Kurt\", \"Koster\", \"Kwakman\", \"Los\", \"Lubbers\", \"Maas\", \"Markus\", \"Martens\", \"Meijer\", \"Mol\", \"Molenaar\", \"Mulder\", \"Nieuwenhuis\", \"Peeters\", \"Peters\", \"Pengel\", \"Pieters\", \"Pool\", \"Post\", \"Postma\", \"Prins\", \"Pronk\", \"Reijnders\", \"Rietveld\", \"Roest\", \"Roos\", \"Sanders\", \"Schaap\", \"Scheffer\", \"Schenk\", \"Schilder\", \"Schipper\", \"Schmidt\", \"Scholten\", \"Schouten\", \"Schut\", \"Schutte\", \"Schuurman\", \"Simons\", \"Smeets\", \"Smit\", \"Smits\", \"Snel\", \"Swinkels\", \"Tas\", \"Terpstra\", \"Timmermans\", \"Tol\", \"Tromp\", \"Troost\", \"Valk\", \"Veenstra\", \"Veldkamp\", \"Verbeek\", \"Verheul\", \"Verhoeven\", \"Vermeer\", \"Vermeulen\", \"Verweij\", \"Vink\", \"Visser\", \"Voorn\", \"Vos\", \"Wagenaar\", \"Wiersema\", \"Willems\", \"Willemsen\", \"Witteveen\", \"Wolff\", \"Wolters\", \"Zijlstra\", \"Zwart\", \"de Beer\", \"de Boer\", \"de Bruijn\", \"de Bruin\", \"de Graaf\", \"de Groot\", \"de Haan\", \"de Haas\", \"de Jager\", \"de Jong\", \"de Jonge\", \"de Koning\", \"de Lange\", \"de Leeuw\", \"de Ridder\", \"de Rooij\", \"de Ruiter\", \"de Vos\", \"de Vries\", \"de Waal\", \"de Wit\", \"de Zwart\", \"van Beek\", \"van Boven\", \"van Dam\", \"van Dijk\", \"van Dongen\", \"van Doorn\", \"van Egmond\", \"van Eijk\", \"van Es\", \"van Gelder\", \"van Gelderen\", \"van Houten\", \"van Hulst\", \"van Kempen\", \"van Kesteren\", \"van Leeuwen\", \"van Loon\", \"van Mill\", \"van Noord\", \"van Ommen\", \"van Ommeren\", \"van Oosten\", \"van Oostveen\", \"van Rijn\", \"van Schaik\", \"van Veen\", \"van Vliet\", \"van Wijk\", \"van Wijngaarden\", \"van den Poel\", \"van de Pol\", \"van den Ploeg\", \"van de Ven\", \"van den Berg\", \"van den Bosch\", \"van den Brink\", \"van den Broek\", \"van den Heuvel\", \"van der Heijden\", \"van der Horst\", \"van der Hulst\", \"van der Kroon\", \"van der Laan\", \"van der Linden\", \"van der Meer\", \"van der Meij\", \"van der Meulen\", \"van der Molen\", \"van der Sluis\", \"van der Spek\", \"van der Veen\", \"van der Velde\", \"van der Velden\", \"van der Vliet\", \"van der Wal\"]\n        },\n\n        // Data taken from https://github.com/umpirsky/country-list/blob/master/data/en_US/country.json\n        countries: [{\"name\":\"Afghanistan\",\"abbreviation\":\"AF\"},{\"name\":\"Åland Islands\",\"abbreviation\":\"AX\"},{\"name\":\"Albania\",\"abbreviation\":\"AL\"},{\"name\":\"Algeria\",\"abbreviation\":\"DZ\"},{\"name\":\"American Samoa\",\"abbreviation\":\"AS\"},{\"name\":\"Andorra\",\"abbreviation\":\"AD\"},{\"name\":\"Angola\",\"abbreviation\":\"AO\"},{\"name\":\"Anguilla\",\"abbreviation\":\"AI\"},{\"name\":\"Antarctica\",\"abbreviation\":\"AQ\"},{\"name\":\"Antigua & Barbuda\",\"abbreviation\":\"AG\"},{\"name\":\"Argentina\",\"abbreviation\":\"AR\"},{\"name\":\"Armenia\",\"abbreviation\":\"AM\"},{\"name\":\"Aruba\",\"abbreviation\":\"AW\"},{\"name\":\"Ascension Island\",\"abbreviation\":\"AC\"},{\"name\":\"Australia\",\"abbreviation\":\"AU\"},{\"name\":\"Austria\",\"abbreviation\":\"AT\"},{\"name\":\"Azerbaijan\",\"abbreviation\":\"AZ\"},{\"name\":\"Bahamas\",\"abbreviation\":\"BS\"},{\"name\":\"Bahrain\",\"abbreviation\":\"BH\"},{\"name\":\"Bangladesh\",\"abbreviation\":\"BD\"},{\"name\":\"Barbados\",\"abbreviation\":\"BB\"},{\"name\":\"Belarus\",\"abbreviation\":\"BY\"},{\"name\":\"Belgium\",\"abbreviation\":\"BE\"},{\"name\":\"Belize\",\"abbreviation\":\"BZ\"},{\"name\":\"Benin\",\"abbreviation\":\"BJ\"},{\"name\":\"Bermuda\",\"abbreviation\":\"BM\"},{\"name\":\"Bhutan\",\"abbreviation\":\"BT\"},{\"name\":\"Bolivia\",\"abbreviation\":\"BO\"},{\"name\":\"Bosnia & Herzegovina\",\"abbreviation\":\"BA\"},{\"name\":\"Botswana\",\"abbreviation\":\"BW\"},{\"name\":\"Brazil\",\"abbreviation\":\"BR\"},{\"name\":\"British Indian Ocean Territory\",\"abbreviation\":\"IO\"},{\"name\":\"British Virgin Islands\",\"abbreviation\":\"VG\"},{\"name\":\"Brunei\",\"abbreviation\":\"BN\"},{\"name\":\"Bulgaria\",\"abbreviation\":\"BG\"},{\"name\":\"Burkina Faso\",\"abbreviation\":\"BF\"},{\"name\":\"Burundi\",\"abbreviation\":\"BI\"},{\"name\":\"Cambodia\",\"abbreviation\":\"KH\"},{\"name\":\"Cameroon\",\"abbreviation\":\"CM\"},{\"name\":\"Canada\",\"abbreviation\":\"CA\"},{\"name\":\"Canary Islands\",\"abbreviation\":\"IC\"},{\"name\":\"Cape Verde\",\"abbreviation\":\"CV\"},{\"name\":\"Caribbean Netherlands\",\"abbreviation\":\"BQ\"},{\"name\":\"Cayman Islands\",\"abbreviation\":\"KY\"},{\"name\":\"Central African Republic\",\"abbreviation\":\"CF\"},{\"name\":\"Ceuta & Melilla\",\"abbreviation\":\"EA\"},{\"name\":\"Chad\",\"abbreviation\":\"TD\"},{\"name\":\"Chile\",\"abbreviation\":\"CL\"},{\"name\":\"China\",\"abbreviation\":\"CN\"},{\"name\":\"Christmas Island\",\"abbreviation\":\"CX\"},{\"name\":\"Cocos (Keeling) Islands\",\"abbreviation\":\"CC\"},{\"name\":\"Colombia\",\"abbreviation\":\"CO\"},{\"name\":\"Comoros\",\"abbreviation\":\"KM\"},{\"name\":\"Congo - Brazzaville\",\"abbreviation\":\"CG\"},{\"name\":\"Congo - Kinshasa\",\"abbreviation\":\"CD\"},{\"name\":\"Cook Islands\",\"abbreviation\":\"CK\"},{\"name\":\"Costa Rica\",\"abbreviation\":\"CR\"},{\"name\":\"Côte d'Ivoire\",\"abbreviation\":\"CI\"},{\"name\":\"Croatia\",\"abbreviation\":\"HR\"},{\"name\":\"Cuba\",\"abbreviation\":\"CU\"},{\"name\":\"Curaçao\",\"abbreviation\":\"CW\"},{\"name\":\"Cyprus\",\"abbreviation\":\"CY\"},{\"name\":\"Czech Republic\",\"abbreviation\":\"CZ\"},{\"name\":\"Denmark\",\"abbreviation\":\"DK\"},{\"name\":\"Diego Garcia\",\"abbreviation\":\"DG\"},{\"name\":\"Djibouti\",\"abbreviation\":\"DJ\"},{\"name\":\"Dominica\",\"abbreviation\":\"DM\"},{\"name\":\"Dominican Republic\",\"abbreviation\":\"DO\"},{\"name\":\"Ecuador\",\"abbreviation\":\"EC\"},{\"name\":\"Egypt\",\"abbreviation\":\"EG\"},{\"name\":\"El Salvador\",\"abbreviation\":\"SV\"},{\"name\":\"Equatorial Guinea\",\"abbreviation\":\"GQ\"},{\"name\":\"Eritrea\",\"abbreviation\":\"ER\"},{\"name\":\"Estonia\",\"abbreviation\":\"EE\"},{\"name\":\"Ethiopia\",\"abbreviation\":\"ET\"},{\"name\":\"Falkland Islands\",\"abbreviation\":\"FK\"},{\"name\":\"Faroe Islands\",\"abbreviation\":\"FO\"},{\"name\":\"Fiji\",\"abbreviation\":\"FJ\"},{\"name\":\"Finland\",\"abbreviation\":\"FI\"},{\"name\":\"France\",\"abbreviation\":\"FR\"},{\"name\":\"French Guiana\",\"abbreviation\":\"GF\"},{\"name\":\"French Polynesia\",\"abbreviation\":\"PF\"},{\"name\":\"French Southern Territories\",\"abbreviation\":\"TF\"},{\"name\":\"Gabon\",\"abbreviation\":\"GA\"},{\"name\":\"Gambia\",\"abbreviation\":\"GM\"},{\"name\":\"Georgia\",\"abbreviation\":\"GE\"},{\"name\":\"Germany\",\"abbreviation\":\"DE\"},{\"name\":\"Ghana\",\"abbreviation\":\"GH\"},{\"name\":\"Gibraltar\",\"abbreviation\":\"GI\"},{\"name\":\"Greece\",\"abbreviation\":\"GR\"},{\"name\":\"Greenland\",\"abbreviation\":\"GL\"},{\"name\":\"Grenada\",\"abbreviation\":\"GD\"},{\"name\":\"Guadeloupe\",\"abbreviation\":\"GP\"},{\"name\":\"Guam\",\"abbreviation\":\"GU\"},{\"name\":\"Guatemala\",\"abbreviation\":\"GT\"},{\"name\":\"Guernsey\",\"abbreviation\":\"GG\"},{\"name\":\"Guinea\",\"abbreviation\":\"GN\"},{\"name\":\"Guinea-Bissau\",\"abbreviation\":\"GW\"},{\"name\":\"Guyana\",\"abbreviation\":\"GY\"},{\"name\":\"Haiti\",\"abbreviation\":\"HT\"},{\"name\":\"Honduras\",\"abbreviation\":\"HN\"},{\"name\":\"Hong Kong SAR China\",\"abbreviation\":\"HK\"},{\"name\":\"Hungary\",\"abbreviation\":\"HU\"},{\"name\":\"Iceland\",\"abbreviation\":\"IS\"},{\"name\":\"India\",\"abbreviation\":\"IN\"},{\"name\":\"Indonesia\",\"abbreviation\":\"ID\"},{\"name\":\"Iran\",\"abbreviation\":\"IR\"},{\"name\":\"Iraq\",\"abbreviation\":\"IQ\"},{\"name\":\"Ireland\",\"abbreviation\":\"IE\"},{\"name\":\"Isle of Man\",\"abbreviation\":\"IM\"},{\"name\":\"Israel\",\"abbreviation\":\"IL\"},{\"name\":\"Italy\",\"abbreviation\":\"IT\"},{\"name\":\"Jamaica\",\"abbreviation\":\"JM\"},{\"name\":\"Japan\",\"abbreviation\":\"JP\"},{\"name\":\"Jersey\",\"abbreviation\":\"JE\"},{\"name\":\"Jordan\",\"abbreviation\":\"JO\"},{\"name\":\"Kazakhstan\",\"abbreviation\":\"KZ\"},{\"name\":\"Kenya\",\"abbreviation\":\"KE\"},{\"name\":\"Kiribati\",\"abbreviation\":\"KI\"},{\"name\":\"Kosovo\",\"abbreviation\":\"XK\"},{\"name\":\"Kuwait\",\"abbreviation\":\"KW\"},{\"name\":\"Kyrgyzstan\",\"abbreviation\":\"KG\"},{\"name\":\"Laos\",\"abbreviation\":\"LA\"},{\"name\":\"Latvia\",\"abbreviation\":\"LV\"},{\"name\":\"Lebanon\",\"abbreviation\":\"LB\"},{\"name\":\"Lesotho\",\"abbreviation\":\"LS\"},{\"name\":\"Liberia\",\"abbreviation\":\"LR\"},{\"name\":\"Libya\",\"abbreviation\":\"LY\"},{\"name\":\"Liechtenstein\",\"abbreviation\":\"LI\"},{\"name\":\"Lithuania\",\"abbreviation\":\"LT\"},{\"name\":\"Luxembourg\",\"abbreviation\":\"LU\"},{\"name\":\"Macau SAR China\",\"abbreviation\":\"MO\"},{\"name\":\"Macedonia\",\"abbreviation\":\"MK\"},{\"name\":\"Madagascar\",\"abbreviation\":\"MG\"},{\"name\":\"Malawi\",\"abbreviation\":\"MW\"},{\"name\":\"Malaysia\",\"abbreviation\":\"MY\"},{\"name\":\"Maldives\",\"abbreviation\":\"MV\"},{\"name\":\"Mali\",\"abbreviation\":\"ML\"},{\"name\":\"Malta\",\"abbreviation\":\"MT\"},{\"name\":\"Marshall Islands\",\"abbreviation\":\"MH\"},{\"name\":\"Martinique\",\"abbreviation\":\"MQ\"},{\"name\":\"Mauritania\",\"abbreviation\":\"MR\"},{\"name\":\"Mauritius\",\"abbreviation\":\"MU\"},{\"name\":\"Mayotte\",\"abbreviation\":\"YT\"},{\"name\":\"Mexico\",\"abbreviation\":\"MX\"},{\"name\":\"Micronesia\",\"abbreviation\":\"FM\"},{\"name\":\"Moldova\",\"abbreviation\":\"MD\"},{\"name\":\"Monaco\",\"abbreviation\":\"MC\"},{\"name\":\"Mongolia\",\"abbreviation\":\"MN\"},{\"name\":\"Montenegro\",\"abbreviation\":\"ME\"},{\"name\":\"Montserrat\",\"abbreviation\":\"MS\"},{\"name\":\"Morocco\",\"abbreviation\":\"MA\"},{\"name\":\"Mozambique\",\"abbreviation\":\"MZ\"},{\"name\":\"Myanmar (Burma)\",\"abbreviation\":\"MM\"},{\"name\":\"Namibia\",\"abbreviation\":\"NA\"},{\"name\":\"Nauru\",\"abbreviation\":\"NR\"},{\"name\":\"Nepal\",\"abbreviation\":\"NP\"},{\"name\":\"Netherlands\",\"abbreviation\":\"NL\"},{\"name\":\"New Caledonia\",\"abbreviation\":\"NC\"},{\"name\":\"New Zealand\",\"abbreviation\":\"NZ\"},{\"name\":\"Nicaragua\",\"abbreviation\":\"NI\"},{\"name\":\"Niger\",\"abbreviation\":\"NE\"},{\"name\":\"Nigeria\",\"abbreviation\":\"NG\"},{\"name\":\"Niue\",\"abbreviation\":\"NU\"},{\"name\":\"Norfolk Island\",\"abbreviation\":\"NF\"},{\"name\":\"North Korea\",\"abbreviation\":\"KP\"},{\"name\":\"Northern Mariana Islands\",\"abbreviation\":\"MP\"},{\"name\":\"Norway\",\"abbreviation\":\"NO\"},{\"name\":\"Oman\",\"abbreviation\":\"OM\"},{\"name\":\"Pakistan\",\"abbreviation\":\"PK\"},{\"name\":\"Palau\",\"abbreviation\":\"PW\"},{\"name\":\"Palestinian Territories\",\"abbreviation\":\"PS\"},{\"name\":\"Panama\",\"abbreviation\":\"PA\"},{\"name\":\"Papua New Guinea\",\"abbreviation\":\"PG\"},{\"name\":\"Paraguay\",\"abbreviation\":\"PY\"},{\"name\":\"Peru\",\"abbreviation\":\"PE\"},{\"name\":\"Philippines\",\"abbreviation\":\"PH\"},{\"name\":\"Pitcairn Islands\",\"abbreviation\":\"PN\"},{\"name\":\"Poland\",\"abbreviation\":\"PL\"},{\"name\":\"Portugal\",\"abbreviation\":\"PT\"},{\"name\":\"Puerto Rico\",\"abbreviation\":\"PR\"},{\"name\":\"Qatar\",\"abbreviation\":\"QA\"},{\"name\":\"Réunion\",\"abbreviation\":\"RE\"},{\"name\":\"Romania\",\"abbreviation\":\"RO\"},{\"name\":\"Russia\",\"abbreviation\":\"RU\"},{\"name\":\"Rwanda\",\"abbreviation\":\"RW\"},{\"name\":\"Samoa\",\"abbreviation\":\"WS\"},{\"name\":\"San Marino\",\"abbreviation\":\"SM\"},{\"name\":\"São Tomé and Príncipe\",\"abbreviation\":\"ST\"},{\"name\":\"Saudi Arabia\",\"abbreviation\":\"SA\"},{\"name\":\"Senegal\",\"abbreviation\":\"SN\"},{\"name\":\"Serbia\",\"abbreviation\":\"RS\"},{\"name\":\"Seychelles\",\"abbreviation\":\"SC\"},{\"name\":\"Sierra Leone\",\"abbreviation\":\"SL\"},{\"name\":\"Singapore\",\"abbreviation\":\"SG\"},{\"name\":\"Sint Maarten\",\"abbreviation\":\"SX\"},{\"name\":\"Slovakia\",\"abbreviation\":\"SK\"},{\"name\":\"Slovenia\",\"abbreviation\":\"SI\"},{\"name\":\"Solomon Islands\",\"abbreviation\":\"SB\"},{\"name\":\"Somalia\",\"abbreviation\":\"SO\"},{\"name\":\"South Africa\",\"abbreviation\":\"ZA\"},{\"name\":\"South Georgia & South Sandwich Islands\",\"abbreviation\":\"GS\"},{\"name\":\"South Korea\",\"abbreviation\":\"KR\"},{\"name\":\"South Sudan\",\"abbreviation\":\"SS\"},{\"name\":\"Spain\",\"abbreviation\":\"ES\"},{\"name\":\"Sri Lanka\",\"abbreviation\":\"LK\"},{\"name\":\"St. Barthélemy\",\"abbreviation\":\"BL\"},{\"name\":\"St. Helena\",\"abbreviation\":\"SH\"},{\"name\":\"St. Kitts & Nevis\",\"abbreviation\":\"KN\"},{\"name\":\"St. Lucia\",\"abbreviation\":\"LC\"},{\"name\":\"St. Martin\",\"abbreviation\":\"MF\"},{\"name\":\"St. Pierre & Miquelon\",\"abbreviation\":\"PM\"},{\"name\":\"St. Vincent & Grenadines\",\"abbreviation\":\"VC\"},{\"name\":\"Sudan\",\"abbreviation\":\"SD\"},{\"name\":\"Suriname\",\"abbreviation\":\"SR\"},{\"name\":\"Svalbard & Jan Mayen\",\"abbreviation\":\"SJ\"},{\"name\":\"Swaziland\",\"abbreviation\":\"SZ\"},{\"name\":\"Sweden\",\"abbreviation\":\"SE\"},{\"name\":\"Switzerland\",\"abbreviation\":\"CH\"},{\"name\":\"Syria\",\"abbreviation\":\"SY\"},{\"name\":\"Taiwan\",\"abbreviation\":\"TW\"},{\"name\":\"Tajikistan\",\"abbreviation\":\"TJ\"},{\"name\":\"Tanzania\",\"abbreviation\":\"TZ\"},{\"name\":\"Thailand\",\"abbreviation\":\"TH\"},{\"name\":\"Timor-Leste\",\"abbreviation\":\"TL\"},{\"name\":\"Togo\",\"abbreviation\":\"TG\"},{\"name\":\"Tokelau\",\"abbreviation\":\"TK\"},{\"name\":\"Tonga\",\"abbreviation\":\"TO\"},{\"name\":\"Trinidad & Tobago\",\"abbreviation\":\"TT\"},{\"name\":\"Tristan da Cunha\",\"abbreviation\":\"TA\"},{\"name\":\"Tunisia\",\"abbreviation\":\"TN\"},{\"name\":\"Turkey\",\"abbreviation\":\"TR\"},{\"name\":\"Turkmenistan\",\"abbreviation\":\"TM\"},{\"name\":\"Turks & Caicos Islands\",\"abbreviation\":\"TC\"},{\"name\":\"Tuvalu\",\"abbreviation\":\"TV\"},{\"name\":\"U.S. Outlying Islands\",\"abbreviation\":\"UM\"},{\"name\":\"U.S. Virgin Islands\",\"abbreviation\":\"VI\"},{\"name\":\"Uganda\",\"abbreviation\":\"UG\"},{\"name\":\"Ukraine\",\"abbreviation\":\"UA\"},{\"name\":\"United Arab Emirates\",\"abbreviation\":\"AE\"},{\"name\":\"United Kingdom\",\"abbreviation\":\"GB\"},{\"name\":\"United States\",\"abbreviation\":\"US\"},{\"name\":\"Uruguay\",\"abbreviation\":\"UY\"},{\"name\":\"Uzbekistan\",\"abbreviation\":\"UZ\"},{\"name\":\"Vanuatu\",\"abbreviation\":\"VU\"},{\"name\":\"Vatican City\",\"abbreviation\":\"VA\"},{\"name\":\"Venezuela\",\"abbreviation\":\"VE\"},{\"name\":\"Vietnam\",\"abbreviation\":\"VN\"},{\"name\":\"Wallis & Futuna\",\"abbreviation\":\"WF\"},{\"name\":\"Western Sahara\",\"abbreviation\":\"EH\"},{\"name\":\"Yemen\",\"abbreviation\":\"YE\"},{\"name\":\"Zambia\",\"abbreviation\":\"ZM\"},{\"name\":\"Zimbabwe\",\"abbreviation\":\"ZW\"}],\n\n                counties: {\n            // Data taken from http://www.downloadexcelfiles.com/gb_en/download-excel-file-list-counties-uk\n            \"uk\": [\n                {name: 'Bath and North East Somerset'},\n                {name: 'Aberdeenshire'},\n                {name: 'Anglesey'},\n                {name: 'Angus'},\n                {name: 'Bedford'},\n                {name: 'Blackburn with Darwen'},\n                {name: 'Blackpool'},\n                {name: 'Bournemouth'},\n                {name: 'Bracknell Forest'},\n                {name: 'Brighton & Hove'},\n                {name: 'Bristol'},\n                {name: 'Buckinghamshire'},\n                {name: 'Cambridgeshire'},\n                {name: 'Carmarthenshire'},\n                {name: 'Central Bedfordshire'},\n                {name: 'Ceredigion'},\n                {name: 'Cheshire East'},\n                {name: 'Cheshire West and Chester'},\n                {name: 'Clackmannanshire'},\n                {name: 'Conwy'},\n                {name: 'Cornwall'},\n                {name: 'County Antrim'},\n                {name: 'County Armagh'},\n                {name: 'County Down'},\n                {name: 'County Durham'},\n                {name: 'County Fermanagh'},\n                {name: 'County Londonderry'},\n                {name: 'County Tyrone'},\n                {name: 'Cumbria'},\n                {name: 'Darlington'},\n                {name: 'Denbighshire'},\n                {name: 'Derby'},\n                {name: 'Derbyshire'},\n                {name: 'Devon'},\n                {name: 'Dorset'},\n                {name: 'Dumfries and Galloway'},\n                {name: 'Dundee'},\n                {name: 'East Lothian'},\n                {name: 'East Riding of Yorkshire'},\n                {name: 'East Sussex'},\n                {name: 'Edinburgh?'},\n                {name: 'Essex'},\n                {name: 'Falkirk'},\n                {name: 'Fife'},\n                {name: 'Flintshire'},\n                {name: 'Gloucestershire'},\n                {name: 'Greater London'},\n                {name: 'Greater Manchester'},\n                {name: 'Gwent'},\n                {name: 'Gwynedd'},\n                {name: 'Halton'},\n                {name: 'Hampshire'},\n                {name: 'Hartlepool'},\n                {name: 'Herefordshire'},\n                {name: 'Hertfordshire'},\n                {name: 'Highlands'},\n                {name: 'Hull'},\n                {name: 'Isle of Wight'},\n                {name: 'Isles of Scilly'},\n                {name: 'Kent'},\n                {name: 'Lancashire'},\n                {name: 'Leicester'},\n                {name: 'Leicestershire'},\n                {name: 'Lincolnshire'},\n                {name: 'Lothian'},\n                {name: 'Luton'},\n                {name: 'Medway'},\n                {name: 'Merseyside'},\n                {name: 'Mid Glamorgan'},\n                {name: 'Middlesbrough'},\n                {name: 'Milton Keynes'},\n                {name: 'Monmouthshire'},\n                {name: 'Moray'},\n                {name: 'Norfolk'},\n                {name: 'North East Lincolnshire'},\n                {name: 'North Lincolnshire'},\n                {name: 'North Somerset'},\n                {name: 'North Yorkshire'},\n                {name: 'Northamptonshire'},\n                {name: 'Northumberland'},\n                {name: 'Nottingham'},\n                {name: 'Nottinghamshire'},\n                {name: 'Oxfordshire'},\n                {name: 'Pembrokeshire'},\n                {name: 'Perth and Kinross'},\n                {name: 'Peterborough'},\n                {name: 'Plymouth'},\n                {name: 'Poole'},\n                {name: 'Portsmouth'},\n                {name: 'Powys'},\n                {name: 'Reading'},\n                {name: 'Redcar and Cleveland'},\n                {name: 'Rutland'},\n                {name: 'Scottish Borders'},\n                {name: 'Shropshire'},\n                {name: 'Slough'},\n                {name: 'Somerset'},\n                {name: 'South Glamorgan'},\n                {name: 'South Gloucestershire'},\n                {name: 'South Yorkshire'},\n                {name: 'Southampton'},\n                {name: 'Southend-on-Sea'},\n                {name: 'Staffordshire'},\n                {name: 'Stirlingshire'},\n                {name: 'Stockton-on-Tees'},\n                {name: 'Stoke-on-Trent'},\n                {name: 'Strathclyde'},\n                {name: 'Suffolk'},\n                {name: 'Surrey'},\n                {name: 'Swindon'},\n                {name: 'Telford and Wrekin'},\n                {name: 'Thurrock'},\n                {name: 'Torbay'},\n                {name: 'Tyne and Wear'},\n                {name: 'Warrington'},\n                {name: 'Warwickshire'},\n                {name: 'West Berkshire'},\n                {name: 'West Glamorgan'},\n                {name: 'West Lothian'},\n                {name: 'West Midlands'},\n                {name: 'West Sussex'},\n                {name: 'West Yorkshire'},\n                {name: 'Western Isles'},\n                {name: 'Wiltshire'},\n                {name: 'Windsor and Maidenhead'},\n                {name: 'Wokingham'},\n                {name: 'Worcestershire'},\n                {name: 'Wrexham'},\n                {name: 'York'}]\n                                },\n        provinces: {\n            \"ca\": [\n                {name: 'Alberta', abbreviation: 'AB'},\n                {name: 'British Columbia', abbreviation: 'BC'},\n                {name: 'Manitoba', abbreviation: 'MB'},\n                {name: 'New Brunswick', abbreviation: 'NB'},\n                {name: 'Newfoundland and Labrador', abbreviation: 'NL'},\n                {name: 'Nova Scotia', abbreviation: 'NS'},\n                {name: 'Ontario', abbreviation: 'ON'},\n                {name: 'Prince Edward Island', abbreviation: 'PE'},\n                {name: 'Quebec', abbreviation: 'QC'},\n                {name: 'Saskatchewan', abbreviation: 'SK'},\n\n                // The case could be made that the following are not actually provinces\n                // since they are technically considered \"territories\" however they all\n                // look the same on an envelope!\n                {name: 'Northwest Territories', abbreviation: 'NT'},\n                {name: 'Nunavut', abbreviation: 'NU'},\n                {name: 'Yukon', abbreviation: 'YT'}\n            ],\n            \"it\": [\n                { name: \"Agrigento\", abbreviation: \"AG\", code: 84 },\n                { name: \"Alessandria\", abbreviation: \"AL\", code: 6 },\n                { name: \"Ancona\", abbreviation: \"AN\", code: 42 },\n                { name: \"Aosta\", abbreviation: \"AO\", code: 7 },\n                { name: \"L'Aquila\", abbreviation: \"AQ\", code: 66 },\n                { name: \"Arezzo\", abbreviation: \"AR\", code: 51 },\n                { name: \"Ascoli-Piceno\", abbreviation: \"AP\", code: 44 },\n                { name: \"Asti\", abbreviation: \"AT\", code: 5 },\n                { name: \"Avellino\", abbreviation: \"AV\", code: 64 },\n                { name: \"Bari\", abbreviation: \"BA\", code: 72 },\n                { name: \"Barletta-Andria-Trani\", abbreviation: \"BT\", code: 72 },\n                { name: \"Belluno\", abbreviation: \"BL\", code: 25 },\n                { name: \"Benevento\", abbreviation: \"BN\", code: 62 },\n                { name: \"Bergamo\", abbreviation: \"BG\", code: 16 },\n                { name: \"Biella\", abbreviation: \"BI\", code: 96 },\n                { name: \"Bologna\", abbreviation: \"BO\", code: 37 },\n                { name: \"Bolzano\", abbreviation: \"BZ\", code: 21 },\n                { name: \"Brescia\", abbreviation: \"BS\", code: 17 },\n                { name: \"Brindisi\", abbreviation: \"BR\", code: 74 },\n                { name: \"Cagliari\", abbreviation: \"CA\", code: 92 },\n                { name: \"Caltanissetta\", abbreviation: \"CL\", code: 85 },\n                { name: \"Campobasso\", abbreviation: \"CB\", code: 70 },\n                { name: \"Carbonia Iglesias\", abbreviation: \"CI\", code: 70 },\n                { name: \"Caserta\", abbreviation: \"CE\", code: 61 },\n                { name: \"Catania\", abbreviation: \"CT\", code: 87 },\n                { name: \"Catanzaro\", abbreviation: \"CZ\", code: 79 },\n                { name: \"Chieti\", abbreviation: \"CH\", code: 69 },\n                { name: \"Como\", abbreviation: \"CO\", code: 13 },\n                { name: \"Cosenza\", abbreviation: \"CS\", code: 78 },\n                { name: \"Cremona\", abbreviation: \"CR\", code: 19 },\n                { name: \"Crotone\", abbreviation: \"KR\", code: 101 },\n                { name: \"Cuneo\", abbreviation: \"CN\", code: 4 },\n                { name: \"Enna\", abbreviation: \"EN\", code: 86 },\n                { name: \"Fermo\", abbreviation: \"FM\", code: 86 },\n                { name: \"Ferrara\", abbreviation: \"FE\", code: 38 },\n                { name: \"Firenze\", abbreviation: \"FI\", code: 48 },\n                { name: \"Foggia\", abbreviation: \"FG\", code: 71 },\n                { name: \"Forli-Cesena\", abbreviation: \"FC\", code: 71 },\n                { name: \"Frosinone\", abbreviation: \"FR\", code: 60 },\n                { name: \"Genova\", abbreviation: \"GE\", code: 10 },\n                { name: \"Gorizia\", abbreviation: \"GO\", code: 31 },\n                { name: \"Grosseto\", abbreviation: \"GR\", code: 53 },\n                { name: \"Imperia\", abbreviation: \"IM\", code: 8 },\n                { name: \"Isernia\", abbreviation: \"IS\", code: 94 },\n                { name: \"La-Spezia\", abbreviation: \"SP\", code: 66 },\n                { name: \"Latina\", abbreviation: \"LT\", code: 59 },\n                { name: \"Lecce\", abbreviation: \"LE\", code: 75 },\n                { name: \"Lecco\", abbreviation: \"LC\", code: 97 },\n                { name: \"Livorno\", abbreviation: \"LI\", code: 49 },\n                { name: \"Lodi\", abbreviation: \"LO\", code: 98 },\n                { name: \"Lucca\", abbreviation: \"LU\", code: 46 },\n                { name: \"Macerata\", abbreviation: \"MC\", code: 43 },\n                { name: \"Mantova\", abbreviation: \"MN\", code: 20 },\n                { name: \"Massa-Carrara\", abbreviation: \"MS\", code: 45 },\n                { name: \"Matera\", abbreviation: \"MT\", code: 77 },\n                { name: \"Medio Campidano\", abbreviation: \"VS\", code: 77 },\n                { name: \"Messina\", abbreviation: \"ME\", code: 83 },\n                { name: \"Milano\", abbreviation: \"MI\", code: 15 },\n                { name: \"Modena\", abbreviation: \"MO\", code: 36 },\n                { name: \"Monza-Brianza\", abbreviation: \"MB\", code: 36 },\n                { name: \"Napoli\", abbreviation: \"NA\", code: 63 },\n                { name: \"Novara\", abbreviation: \"NO\", code: 3 },\n                { name: \"Nuoro\", abbreviation: \"NU\", code: 91 },\n                { name: \"Ogliastra\", abbreviation: \"OG\", code: 91 },\n                { name: \"Olbia Tempio\", abbreviation: \"OT\", code: 91 },\n                { name: \"Oristano\", abbreviation: \"OR\", code: 95 },\n                { name: \"Padova\", abbreviation: \"PD\", code: 28 },\n                { name: \"Palermo\", abbreviation: \"PA\", code: 82 },\n                { name: \"Parma\", abbreviation: \"PR\", code: 34 },\n                { name: \"Pavia\", abbreviation: \"PV\", code: 18 },\n                { name: \"Perugia\", abbreviation: \"PG\", code: 54 },\n                { name: \"Pesaro-Urbino\", abbreviation: \"PU\", code: 41 },\n                { name: \"Pescara\", abbreviation: \"PE\", code: 68 },\n                { name: \"Piacenza\", abbreviation: \"PC\", code: 33 },\n                { name: \"Pisa\", abbreviation: \"PI\", code: 50 },\n                { name: \"Pistoia\", abbreviation: \"PT\", code: 47 },\n                { name: \"Pordenone\", abbreviation: \"PN\", code: 93 },\n                { name: \"Potenza\", abbreviation: \"PZ\", code: 76 },\n                { name: \"Prato\", abbreviation: \"PO\", code: 100 },\n                { name: \"Ragusa\", abbreviation: \"RG\", code: 88 },\n                { name: \"Ravenna\", abbreviation: \"RA\", code: 39 },\n                { name: \"Reggio-Calabria\", abbreviation: \"RC\", code: 35 },\n                { name: \"Reggio-Emilia\", abbreviation: \"RE\", code: 35 },\n                { name: \"Rieti\", abbreviation: \"RI\", code: 57 },\n                { name: \"Rimini\", abbreviation: \"RN\", code: 99 },\n                { name: \"Roma\", abbreviation: \"Roma\", code: 58 },\n                { name: \"Rovigo\", abbreviation: \"RO\", code: 29 },\n                { name: \"Salerno\", abbreviation: \"SA\", code: 65 },\n                { name: \"Sassari\", abbreviation: \"SS\", code: 90 },\n                { name: \"Savona\", abbreviation: \"SV\", code: 9 },\n                { name: \"Siena\", abbreviation: \"SI\", code: 52 },\n                { name: \"Siracusa\", abbreviation: \"SR\", code: 89 },\n                { name: \"Sondrio\", abbreviation: \"SO\", code: 14 },\n                { name: \"Taranto\", abbreviation: \"TA\", code: 73 },\n                { name: \"Teramo\", abbreviation: \"TE\", code: 67 },\n                { name: \"Terni\", abbreviation: \"TR\", code: 55 },\n                { name: \"Torino\", abbreviation: \"TO\", code: 1 },\n                { name: \"Trapani\", abbreviation: \"TP\", code: 81 },\n                { name: \"Trento\", abbreviation: \"TN\", code: 22 },\n                { name: \"Treviso\", abbreviation: \"TV\", code: 26 },\n                { name: \"Trieste\", abbreviation: \"TS\", code: 32 },\n                { name: \"Udine\", abbreviation: \"UD\", code: 30 },\n                { name: \"Varese\", abbreviation: \"VA\", code: 12 },\n                { name: \"Venezia\", abbreviation: \"VE\", code: 27 },\n                { name: \"Verbania\", abbreviation: \"VB\", code: 27 },\n                { name: \"Vercelli\", abbreviation: \"VC\", code: 2 },\n                { name: \"Verona\", abbreviation: \"VR\", code: 23 },\n                { name: \"Vibo-Valentia\", abbreviation: \"VV\", code: 102 },\n                { name: \"Vicenza\", abbreviation: \"VI\", code: 24 },\n                { name: \"Viterbo\", abbreviation: \"VT\", code: 56 }\n            ]\n        },\n\n            // from: https://github.com/samsargent/Useful-Autocomplete-Data/blob/master/data/nationalities.json\n        nationalities: [\n           {name: 'Afghan'},\n           {name: 'Albanian'},\n           {name: 'Algerian'},\n           {name: 'American'},\n           {name: 'Andorran'},\n           {name: 'Angolan'},\n           {name: 'Antiguans'},\n           {name: 'Argentinean'},\n           {name: 'Armenian'},\n           {name: 'Australian'},\n           {name: 'Austrian'},\n           {name: 'Azerbaijani'},\n           {name: 'Bahami'},\n           {name: 'Bahraini'},\n           {name: 'Bangladeshi'},\n           {name: 'Barbadian'},\n           {name: 'Barbudans'},\n           {name: 'Batswana'},\n           {name: 'Belarusian'},\n           {name: 'Belgian'},\n           {name: 'Belizean'},\n           {name: 'Beninese'},\n           {name: 'Bhutanese'},\n           {name: 'Bolivian'},\n           {name: 'Bosnian'},\n           {name: 'Brazilian'},\n           {name: 'British'},\n           {name: 'Bruneian'},\n           {name: 'Bulgarian'},\n           {name: 'Burkinabe'},\n           {name: 'Burmese'},\n           {name: 'Burundian'},\n           {name: 'Cambodian'},\n           {name: 'Cameroonian'},\n           {name: 'Canadian'},\n           {name: 'Cape Verdean'},\n           {name: 'Central African'},\n           {name: 'Chadian'},\n           {name: 'Chilean'},\n           {name: 'Chinese'},\n           {name: 'Colombian'},\n           {name: 'Comoran'},\n           {name: 'Congolese'},\n           {name: 'Costa Rican'},\n           {name: 'Croatian'},\n           {name: 'Cuban'},\n           {name: 'Cypriot'},\n           {name: 'Czech'},\n           {name: 'Danish'},\n           {name: 'Djibouti'},\n           {name: 'Dominican'},\n           {name: 'Dutch'},\n           {name: 'East Timorese'},\n           {name: 'Ecuadorean'},\n           {name: 'Egyptian'},\n           {name: 'Emirian'},\n           {name: 'Equatorial Guinean'},\n           {name: 'Eritrean'},\n           {name: 'Estonian'},\n           {name: 'Ethiopian'},\n           {name: 'Fijian'},\n           {name: 'Filipino'},\n           {name: 'Finnish'},\n           {name: 'French'},\n           {name: 'Gabonese'},\n           {name: 'Gambian'},\n           {name: 'Georgian'},\n           {name: 'German'},\n           {name: 'Ghanaian'},\n           {name: 'Greek'},\n           {name: 'Grenadian'},\n           {name: 'Guatemalan'},\n           {name: 'Guinea-Bissauan'},\n           {name: 'Guinean'},\n           {name: 'Guyanese'},\n           {name: 'Haitian'},\n           {name: 'Herzegovinian'},\n           {name: 'Honduran'},\n           {name: 'Hungarian'},\n           {name: 'I-Kiribati'},\n           {name: 'Icelander'},\n           {name: 'Indian'},\n           {name: 'Indonesian'},\n           {name: 'Iranian'},\n           {name: 'Iraqi'},\n           {name: 'Irish'},\n           {name: 'Israeli'},\n           {name: 'Italian'},\n           {name: 'Ivorian'},\n           {name: 'Jamaican'},\n           {name: 'Japanese'},\n           {name: 'Jordanian'},\n           {name: 'Kazakhstani'},\n           {name: 'Kenyan'},\n           {name: 'Kittian and Nevisian'},\n           {name: 'Kuwaiti'},\n           {name: 'Kyrgyz'},\n           {name: 'Laotian'},\n           {name: 'Latvian'},\n           {name: 'Lebanese'},\n           {name: 'Liberian'},\n           {name: 'Libyan'},\n           {name: 'Liechtensteiner'},\n           {name: 'Lithuanian'},\n           {name: 'Luxembourger'},\n           {name: 'Macedonian'},\n           {name: 'Malagasy'},\n           {name: 'Malawian'},\n           {name: 'Malaysian'},\n           {name: 'Maldivan'},\n           {name: 'Malian'},\n           {name: 'Maltese'},\n           {name: 'Marshallese'},\n           {name: 'Mauritanian'},\n           {name: 'Mauritian'},\n           {name: 'Mexican'},\n           {name: 'Micronesian'},\n           {name: 'Moldovan'},\n           {name: 'Monacan'},\n           {name: 'Mongolian'},\n           {name: 'Moroccan'},\n           {name: 'Mosotho'},\n           {name: 'Motswana'},\n           {name: 'Mozambican'},\n           {name: 'Namibian'},\n           {name: 'Nauruan'},\n           {name: 'Nepalese'},\n           {name: 'New Zealander'},\n           {name: 'Nicaraguan'},\n           {name: 'Nigerian'},\n           {name: 'Nigerien'},\n           {name: 'North Korean'},\n           {name: 'Northern Irish'},\n           {name: 'Norwegian'},\n           {name: 'Omani'},\n           {name: 'Pakistani'},\n           {name: 'Palauan'},\n           {name: 'Panamanian'},\n           {name: 'Papua New Guinean'},\n           {name: 'Paraguayan'},\n           {name: 'Peruvian'},\n           {name: 'Polish'},\n           {name: 'Portuguese'},\n           {name: 'Qatari'},\n           {name: 'Romani'},\n           {name: 'Russian'},\n           {name: 'Rwandan'},\n           {name: 'Saint Lucian'},\n           {name: 'Salvadoran'},\n           {name: 'Samoan'},\n           {name: 'San Marinese'},\n           {name: 'Sao Tomean'},\n           {name: 'Saudi'},\n           {name: 'Scottish'},\n           {name: 'Senegalese'},\n           {name: 'Serbian'},\n           {name: 'Seychellois'},\n           {name: 'Sierra Leonean'},\n           {name: 'Singaporean'},\n           {name: 'Slovakian'},\n           {name: 'Slovenian'},\n           {name: 'Solomon Islander'},\n           {name: 'Somali'},\n           {name: 'South African'},\n           {name: 'South Korean'},\n           {name: 'Spanish'},\n           {name: 'Sri Lankan'},\n           {name: 'Sudanese'},\n           {name: 'Surinamer'},\n           {name: 'Swazi'},\n           {name: 'Swedish'},\n           {name: 'Swiss'},\n           {name: 'Syrian'},\n           {name: 'Taiwanese'},\n           {name: 'Tajik'},\n           {name: 'Tanzanian'},\n           {name: 'Thai'},\n           {name: 'Togolese'},\n           {name: 'Tongan'},\n           {name: 'Trinidadian or Tobagonian'},\n           {name: 'Tunisian'},\n           {name: 'Turkish'},\n           {name: 'Tuvaluan'},\n           {name: 'Ugandan'},\n           {name: 'Ukrainian'},\n           {name: 'Uruguaya'},\n           {name: 'Uzbekistani'},\n           {name: 'Venezuela'},\n           {name: 'Vietnamese'},\n           {name: 'Wels'},\n           {name: 'Yemenit'},\n           {name: 'Zambia'},\n           {name: 'Zimbabwe'},\n        ],\n          // http://www.loc.gov/standards/iso639-2/php/code_list.php (ISO-639-1 codes)\n        locale_languages: [\n          \"aa\",\n          \"ab\",\n          \"ae\",\n          \"af\",\n          \"ak\",\n          \"am\",\n          \"an\",\n          \"ar\",\n          \"as\",\n          \"av\",\n          \"ay\",\n          \"az\",\n          \"ba\",\n          \"be\",\n          \"bg\",\n          \"bh\",\n          \"bi\",\n          \"bm\",\n          \"bn\",\n          \"bo\",\n          \"br\",\n          \"bs\",\n          \"ca\",\n          \"ce\",\n          \"ch\",\n          \"co\",\n          \"cr\",\n          \"cs\",\n          \"cu\",\n          \"cv\",\n          \"cy\",\n          \"da\",\n          \"de\",\n          \"dv\",\n          \"dz\",\n          \"ee\",\n          \"el\",\n          \"en\",\n          \"eo\",\n          \"es\",\n          \"et\",\n          \"eu\",\n          \"fa\",\n          \"ff\",\n          \"fi\",\n          \"fj\",\n          \"fo\",\n          \"fr\",\n          \"fy\",\n          \"ga\",\n          \"gd\",\n          \"gl\",\n          \"gn\",\n          \"gu\",\n          \"gv\",\n          \"ha\",\n          \"he\",\n          \"hi\",\n          \"ho\",\n          \"hr\",\n          \"ht\",\n          \"hu\",\n          \"hy\",\n          \"hz\",\n          \"ia\",\n          \"id\",\n          \"ie\",\n          \"ig\",\n          \"ii\",\n          \"ik\",\n          \"io\",\n          \"is\",\n          \"it\",\n          \"iu\",\n          \"ja\",\n          \"jv\",\n          \"ka\",\n          \"kg\",\n          \"ki\",\n          \"kj\",\n          \"kk\",\n          \"kl\",\n          \"km\",\n          \"kn\",\n          \"ko\",\n          \"kr\",\n          \"ks\",\n          \"ku\",\n          \"kv\",\n          \"kw\",\n          \"ky\",\n          \"la\",\n          \"lb\",\n          \"lg\",\n          \"li\",\n          \"ln\",\n          \"lo\",\n          \"lt\",\n          \"lu\",\n          \"lv\",\n          \"mg\",\n          \"mh\",\n          \"mi\",\n          \"mk\",\n          \"ml\",\n          \"mn\",\n          \"mr\",\n          \"ms\",\n          \"mt\",\n          \"my\",\n          \"na\",\n          \"nb\",\n          \"nd\",\n          \"ne\",\n          \"ng\",\n          \"nl\",\n          \"nn\",\n          \"no\",\n          \"nr\",\n          \"nv\",\n          \"ny\",\n          \"oc\",\n          \"oj\",\n          \"om\",\n          \"or\",\n          \"os\",\n          \"pa\",\n          \"pi\",\n          \"pl\",\n          \"ps\",\n          \"pt\",\n          \"qu\",\n          \"rm\",\n          \"rn\",\n          \"ro\",\n          \"ru\",\n          \"rw\",\n          \"sa\",\n          \"sc\",\n          \"sd\",\n          \"se\",\n          \"sg\",\n          \"si\",\n          \"sk\",\n          \"sl\",\n          \"sm\",\n          \"sn\",\n          \"so\",\n          \"sq\",\n          \"sr\",\n          \"ss\",\n          \"st\",\n          \"su\",\n          \"sv\",\n          \"sw\",\n          \"ta\",\n          \"te\",\n          \"tg\",\n          \"th\",\n          \"ti\",\n          \"tk\",\n          \"tl\",\n          \"tn\",\n          \"to\",\n          \"tr\",\n          \"ts\",\n          \"tt\",\n          \"tw\",\n          \"ty\",\n          \"ug\",\n          \"uk\",\n          \"ur\",\n          \"uz\",\n          \"ve\",\n          \"vi\",\n          \"vo\",\n          \"wa\",\n          \"wo\",\n          \"xh\",\n          \"yi\",\n          \"yo\",\n          \"za\",\n          \"zh\",\n          \"zu\"\n        ],\n\n        // From http://data.okfn.org/data/core/language-codes#resource-language-codes-full (IETF language tags)\n        locale_regions: [\n          \"agq-CM\",\n          \"asa-TZ\",\n          \"ast-ES\",\n          \"bas-CM\",\n          \"bem-ZM\",\n          \"bez-TZ\",\n          \"brx-IN\",\n          \"cgg-UG\",\n          \"chr-US\",\n          \"dav-KE\",\n          \"dje-NE\",\n          \"dsb-DE\",\n          \"dua-CM\",\n          \"dyo-SN\",\n          \"ebu-KE\",\n          \"ewo-CM\",\n          \"fil-PH\",\n          \"fur-IT\",\n          \"gsw-CH\",\n          \"gsw-FR\",\n          \"gsw-LI\",\n          \"guz-KE\",\n          \"haw-US\",\n          \"hsb-DE\",\n          \"jgo-CM\",\n          \"jmc-TZ\",\n          \"kab-DZ\",\n          \"kam-KE\",\n          \"kde-TZ\",\n          \"kea-CV\",\n          \"khq-ML\",\n          \"kkj-CM\",\n          \"kln-KE\",\n          \"kok-IN\",\n          \"ksb-TZ\",\n          \"ksf-CM\",\n          \"ksh-DE\",\n          \"lag-TZ\",\n          \"lkt-US\",\n          \"luo-KE\",\n          \"luy-KE\",\n          \"mas-KE\",\n          \"mas-TZ\",\n          \"mer-KE\",\n          \"mfe-MU\",\n          \"mgh-MZ\",\n          \"mgo-CM\",\n          \"mua-CM\",\n          \"naq-NA\",\n          \"nmg-CM\",\n          \"nnh-CM\",\n          \"nus-SD\",\n          \"nyn-UG\",\n          \"rof-TZ\",\n          \"rwk-TZ\",\n          \"sah-RU\",\n          \"saq-KE\",\n          \"sbp-TZ\",\n          \"seh-MZ\",\n          \"ses-ML\",\n          \"shi-Latn\",\n          \"shi-Latn-MA\",\n          \"shi-Tfng\",\n          \"shi-Tfng-MA\",\n          \"smn-FI\",\n          \"teo-KE\",\n          \"teo-UG\",\n          \"twq-NE\",\n          \"tzm-Latn\",\n          \"tzm-Latn-MA\",\n          \"vai-Latn\",\n          \"vai-Latn-LR\",\n          \"vai-Vaii\",\n          \"vai-Vaii-LR\",\n          \"vun-TZ\",\n          \"wae-CH\",\n          \"xog-UG\",\n          \"yav-CM\",\n          \"zgh-MA\",\n          \"af-NA\",\n          \"af-ZA\",\n          \"ak-GH\",\n          \"am-ET\",\n          \"ar-001\",\n          \"ar-AE\",\n          \"ar-BH\",\n          \"ar-DJ\",\n          \"ar-DZ\",\n          \"ar-EG\",\n          \"ar-EH\",\n          \"ar-ER\",\n          \"ar-IL\",\n          \"ar-IQ\",\n          \"ar-JO\",\n          \"ar-KM\",\n          \"ar-KW\",\n          \"ar-LB\",\n          \"ar-LY\",\n          \"ar-MA\",\n          \"ar-MR\",\n          \"ar-OM\",\n          \"ar-PS\",\n          \"ar-QA\",\n          \"ar-SA\",\n          \"ar-SD\",\n          \"ar-SO\",\n          \"ar-SS\",\n          \"ar-SY\",\n          \"ar-TD\",\n          \"ar-TN\",\n          \"ar-YE\",\n          \"as-IN\",\n          \"az-Cyrl\",\n          \"az-Cyrl-AZ\",\n          \"az-Latn\",\n          \"az-Latn-AZ\",\n          \"be-BY\",\n          \"bg-BG\",\n          \"bm-Latn\",\n          \"bm-Latn-ML\",\n          \"bn-BD\",\n          \"bn-IN\",\n          \"bo-CN\",\n          \"bo-IN\",\n          \"br-FR\",\n          \"bs-Cyrl\",\n          \"bs-Cyrl-BA\",\n          \"bs-Latn\",\n          \"bs-Latn-BA\",\n          \"ca-AD\",\n          \"ca-ES\",\n          \"ca-ES-VALENCIA\",\n          \"ca-FR\",\n          \"ca-IT\",\n          \"cs-CZ\",\n          \"cy-GB\",\n          \"da-DK\",\n          \"da-GL\",\n          \"de-AT\",\n          \"de-BE\",\n          \"de-CH\",\n          \"de-DE\",\n          \"de-LI\",\n          \"de-LU\",\n          \"dz-BT\",\n          \"ee-GH\",\n          \"ee-TG\",\n          \"el-CY\",\n          \"el-GR\",\n          \"en-001\",\n          \"en-150\",\n          \"en-AG\",\n          \"en-AI\",\n          \"en-AS\",\n          \"en-AU\",\n          \"en-BB\",\n          \"en-BE\",\n          \"en-BM\",\n          \"en-BS\",\n          \"en-BW\",\n          \"en-BZ\",\n          \"en-CA\",\n          \"en-CC\",\n          \"en-CK\",\n          \"en-CM\",\n          \"en-CX\",\n          \"en-DG\",\n          \"en-DM\",\n          \"en-ER\",\n          \"en-FJ\",\n          \"en-FK\",\n          \"en-FM\",\n          \"en-GB\",\n          \"en-GD\",\n          \"en-GG\",\n          \"en-GH\",\n          \"en-GI\",\n          \"en-GM\",\n          \"en-GU\",\n          \"en-GY\",\n          \"en-HK\",\n          \"en-IE\",\n          \"en-IM\",\n          \"en-IN\",\n          \"en-IO\",\n          \"en-JE\",\n          \"en-JM\",\n          \"en-KE\",\n          \"en-KI\",\n          \"en-KN\",\n          \"en-KY\",\n          \"en-LC\",\n          \"en-LR\",\n          \"en-LS\",\n          \"en-MG\",\n          \"en-MH\",\n          \"en-MO\",\n          \"en-MP\",\n          \"en-MS\",\n          \"en-MT\",\n          \"en-MU\",\n          \"en-MW\",\n          \"en-MY\",\n          \"en-NA\",\n          \"en-NF\",\n          \"en-NG\",\n          \"en-NR\",\n          \"en-NU\",\n          \"en-NZ\",\n          \"en-PG\",\n          \"en-PH\",\n          \"en-PK\",\n          \"en-PN\",\n          \"en-PR\",\n          \"en-PW\",\n          \"en-RW\",\n          \"en-SB\",\n          \"en-SC\",\n          \"en-SD\",\n          \"en-SG\",\n          \"en-SH\",\n          \"en-SL\",\n          \"en-SS\",\n          \"en-SX\",\n          \"en-SZ\",\n          \"en-TC\",\n          \"en-TK\",\n          \"en-TO\",\n          \"en-TT\",\n          \"en-TV\",\n          \"en-TZ\",\n          \"en-UG\",\n          \"en-UM\",\n          \"en-US\",\n          \"en-US-POSIX\",\n          \"en-VC\",\n          \"en-VG\",\n          \"en-VI\",\n          \"en-VU\",\n          \"en-WS\",\n          \"en-ZA\",\n          \"en-ZM\",\n          \"en-ZW\",\n          \"eo-001\",\n          \"es-419\",\n          \"es-AR\",\n          \"es-BO\",\n          \"es-CL\",\n          \"es-CO\",\n          \"es-CR\",\n          \"es-CU\",\n          \"es-DO\",\n          \"es-EA\",\n          \"es-EC\",\n          \"es-ES\",\n          \"es-GQ\",\n          \"es-GT\",\n          \"es-HN\",\n          \"es-IC\",\n          \"es-MX\",\n          \"es-NI\",\n          \"es-PA\",\n          \"es-PE\",\n          \"es-PH\",\n          \"es-PR\",\n          \"es-PY\",\n          \"es-SV\",\n          \"es-US\",\n          \"es-UY\",\n          \"es-VE\",\n          \"et-EE\",\n          \"eu-ES\",\n          \"fa-AF\",\n          \"fa-IR\",\n          \"ff-CM\",\n          \"ff-GN\",\n          \"ff-MR\",\n          \"ff-SN\",\n          \"fi-FI\",\n          \"fo-FO\",\n          \"fr-BE\",\n          \"fr-BF\",\n          \"fr-BI\",\n          \"fr-BJ\",\n          \"fr-BL\",\n          \"fr-CA\",\n          \"fr-CD\",\n          \"fr-CF\",\n          \"fr-CG\",\n          \"fr-CH\",\n          \"fr-CI\",\n          \"fr-CM\",\n          \"fr-DJ\",\n          \"fr-DZ\",\n          \"fr-FR\",\n          \"fr-GA\",\n          \"fr-GF\",\n          \"fr-GN\",\n          \"fr-GP\",\n          \"fr-GQ\",\n          \"fr-HT\",\n          \"fr-KM\",\n          \"fr-LU\",\n          \"fr-MA\",\n          \"fr-MC\",\n          \"fr-MF\",\n          \"fr-MG\",\n          \"fr-ML\",\n          \"fr-MQ\",\n          \"fr-MR\",\n          \"fr-MU\",\n          \"fr-NC\",\n          \"fr-NE\",\n          \"fr-PF\",\n          \"fr-PM\",\n          \"fr-RE\",\n          \"fr-RW\",\n          \"fr-SC\",\n          \"fr-SN\",\n          \"fr-SY\",\n          \"fr-TD\",\n          \"fr-TG\",\n          \"fr-TN\",\n          \"fr-VU\",\n          \"fr-WF\",\n          \"fr-YT\",\n          \"fy-NL\",\n          \"ga-IE\",\n          \"gd-GB\",\n          \"gl-ES\",\n          \"gu-IN\",\n          \"gv-IM\",\n          \"ha-Latn\",\n          \"ha-Latn-GH\",\n          \"ha-Latn-NE\",\n          \"ha-Latn-NG\",\n          \"he-IL\",\n          \"hi-IN\",\n          \"hr-BA\",\n          \"hr-HR\",\n          \"hu-HU\",\n          \"hy-AM\",\n          \"id-ID\",\n          \"ig-NG\",\n          \"ii-CN\",\n          \"is-IS\",\n          \"it-CH\",\n          \"it-IT\",\n          \"it-SM\",\n          \"ja-JP\",\n          \"ka-GE\",\n          \"ki-KE\",\n          \"kk-Cyrl\",\n          \"kk-Cyrl-KZ\",\n          \"kl-GL\",\n          \"km-KH\",\n          \"kn-IN\",\n          \"ko-KP\",\n          \"ko-KR\",\n          \"ks-Arab\",\n          \"ks-Arab-IN\",\n          \"kw-GB\",\n          \"ky-Cyrl\",\n          \"ky-Cyrl-KG\",\n          \"lb-LU\",\n          \"lg-UG\",\n          \"ln-AO\",\n          \"ln-CD\",\n          \"ln-CF\",\n          \"ln-CG\",\n          \"lo-LA\",\n          \"lt-LT\",\n          \"lu-CD\",\n          \"lv-LV\",\n          \"mg-MG\",\n          \"mk-MK\",\n          \"ml-IN\",\n          \"mn-Cyrl\",\n          \"mn-Cyrl-MN\",\n          \"mr-IN\",\n          \"ms-Latn\",\n          \"ms-Latn-BN\",\n          \"ms-Latn-MY\",\n          \"ms-Latn-SG\",\n          \"mt-MT\",\n          \"my-MM\",\n          \"nb-NO\",\n          \"nb-SJ\",\n          \"nd-ZW\",\n          \"ne-IN\",\n          \"ne-NP\",\n          \"nl-AW\",\n          \"nl-BE\",\n          \"nl-BQ\",\n          \"nl-CW\",\n          \"nl-NL\",\n          \"nl-SR\",\n          \"nl-SX\",\n          \"nn-NO\",\n          \"om-ET\",\n          \"om-KE\",\n          \"or-IN\",\n          \"os-GE\",\n          \"os-RU\",\n          \"pa-Arab\",\n          \"pa-Arab-PK\",\n          \"pa-Guru\",\n          \"pa-Guru-IN\",\n          \"pl-PL\",\n          \"ps-AF\",\n          \"pt-AO\",\n          \"pt-BR\",\n          \"pt-CV\",\n          \"pt-GW\",\n          \"pt-MO\",\n          \"pt-MZ\",\n          \"pt-PT\",\n          \"pt-ST\",\n          \"pt-TL\",\n          \"qu-BO\",\n          \"qu-EC\",\n          \"qu-PE\",\n          \"rm-CH\",\n          \"rn-BI\",\n          \"ro-MD\",\n          \"ro-RO\",\n          \"ru-BY\",\n          \"ru-KG\",\n          \"ru-KZ\",\n          \"ru-MD\",\n          \"ru-RU\",\n          \"ru-UA\",\n          \"rw-RW\",\n          \"se-FI\",\n          \"se-NO\",\n          \"se-SE\",\n          \"sg-CF\",\n          \"si-LK\",\n          \"sk-SK\",\n          \"sl-SI\",\n          \"sn-ZW\",\n          \"so-DJ\",\n          \"so-ET\",\n          \"so-KE\",\n          \"so-SO\",\n          \"sq-AL\",\n          \"sq-MK\",\n          \"sq-XK\",\n          \"sr-Cyrl\",\n          \"sr-Cyrl-BA\",\n          \"sr-Cyrl-ME\",\n          \"sr-Cyrl-RS\",\n          \"sr-Cyrl-XK\",\n          \"sr-Latn\",\n          \"sr-Latn-BA\",\n          \"sr-Latn-ME\",\n          \"sr-Latn-RS\",\n          \"sr-Latn-XK\",\n          \"sv-AX\",\n          \"sv-FI\",\n          \"sv-SE\",\n          \"sw-CD\",\n          \"sw-KE\",\n          \"sw-TZ\",\n          \"sw-UG\",\n          \"ta-IN\",\n          \"ta-LK\",\n          \"ta-MY\",\n          \"ta-SG\",\n          \"te-IN\",\n          \"th-TH\",\n          \"ti-ER\",\n          \"ti-ET\",\n          \"to-TO\",\n          \"tr-CY\",\n          \"tr-TR\",\n          \"ug-Arab\",\n          \"ug-Arab-CN\",\n          \"uk-UA\",\n          \"ur-IN\",\n          \"ur-PK\",\n          \"uz-Arab\",\n          \"uz-Arab-AF\",\n          \"uz-Cyrl\",\n          \"uz-Cyrl-UZ\",\n          \"uz-Latn\",\n          \"uz-Latn-UZ\",\n          \"vi-VN\",\n          \"yi-001\",\n          \"yo-BJ\",\n          \"yo-NG\",\n          \"zh-Hans\",\n          \"zh-Hans-CN\",\n          \"zh-Hans-HK\",\n          \"zh-Hans-MO\",\n          \"zh-Hans-SG\",\n          \"zh-Hant\",\n          \"zh-Hant-HK\",\n          \"zh-Hant-MO\",\n          \"zh-Hant-TW\",\n          \"zu-ZA\"\n        ],\n\n        us_states_and_dc: [\n            {name: 'Alabama', abbreviation: 'AL'},\n            {name: 'Alaska', abbreviation: 'AK'},\n            {name: 'Arizona', abbreviation: 'AZ'},\n            {name: 'Arkansas', abbreviation: 'AR'},\n            {name: 'California', abbreviation: 'CA'},\n            {name: 'Colorado', abbreviation: 'CO'},\n            {name: 'Connecticut', abbreviation: 'CT'},\n            {name: 'Delaware', abbreviation: 'DE'},\n            {name: 'District of Columbia', abbreviation: 'DC'},\n            {name: 'Florida', abbreviation: 'FL'},\n            {name: 'Georgia', abbreviation: 'GA'},\n            {name: 'Hawaii', abbreviation: 'HI'},\n            {name: 'Idaho', abbreviation: 'ID'},\n            {name: 'Illinois', abbreviation: 'IL'},\n            {name: 'Indiana', abbreviation: 'IN'},\n            {name: 'Iowa', abbreviation: 'IA'},\n            {name: 'Kansas', abbreviation: 'KS'},\n            {name: 'Kentucky', abbreviation: 'KY'},\n            {name: 'Louisiana', abbreviation: 'LA'},\n            {name: 'Maine', abbreviation: 'ME'},\n            {name: 'Maryland', abbreviation: 'MD'},\n            {name: 'Massachusetts', abbreviation: 'MA'},\n            {name: 'Michigan', abbreviation: 'MI'},\n            {name: 'Minnesota', abbreviation: 'MN'},\n            {name: 'Mississippi', abbreviation: 'MS'},\n            {name: 'Missouri', abbreviation: 'MO'},\n            {name: 'Montana', abbreviation: 'MT'},\n            {name: 'Nebraska', abbreviation: 'NE'},\n            {name: 'Nevada', abbreviation: 'NV'},\n            {name: 'New Hampshire', abbreviation: 'NH'},\n            {name: 'New Jersey', abbreviation: 'NJ'},\n            {name: 'New Mexico', abbreviation: 'NM'},\n            {name: 'New York', abbreviation: 'NY'},\n            {name: 'North Carolina', abbreviation: 'NC'},\n            {name: 'North Dakota', abbreviation: 'ND'},\n            {name: 'Ohio', abbreviation: 'OH'},\n            {name: 'Oklahoma', abbreviation: 'OK'},\n            {name: 'Oregon', abbreviation: 'OR'},\n            {name: 'Pennsylvania', abbreviation: 'PA'},\n            {name: 'Rhode Island', abbreviation: 'RI'},\n            {name: 'South Carolina', abbreviation: 'SC'},\n            {name: 'South Dakota', abbreviation: 'SD'},\n            {name: 'Tennessee', abbreviation: 'TN'},\n            {name: 'Texas', abbreviation: 'TX'},\n            {name: 'Utah', abbreviation: 'UT'},\n            {name: 'Vermont', abbreviation: 'VT'},\n            {name: 'Virginia', abbreviation: 'VA'},\n            {name: 'Washington', abbreviation: 'WA'},\n            {name: 'West Virginia', abbreviation: 'WV'},\n            {name: 'Wisconsin', abbreviation: 'WI'},\n            {name: 'Wyoming', abbreviation: 'WY'}\n        ],\n\n        territories: [\n            {name: 'American Samoa', abbreviation: 'AS'},\n            {name: 'Federated States of Micronesia', abbreviation: 'FM'},\n            {name: 'Guam', abbreviation: 'GU'},\n            {name: 'Marshall Islands', abbreviation: 'MH'},\n            {name: 'Northern Mariana Islands', abbreviation: 'MP'},\n            {name: 'Puerto Rico', abbreviation: 'PR'},\n            {name: 'Virgin Islands, U.S.', abbreviation: 'VI'}\n        ],\n\n        armed_forces: [\n            {name: 'Armed Forces Europe', abbreviation: 'AE'},\n            {name: 'Armed Forces Pacific', abbreviation: 'AP'},\n            {name: 'Armed Forces the Americas', abbreviation: 'AA'}\n        ],\n\n        country_regions: {\n            it: [\n                { name: \"Valle d'Aosta\", abbreviation: \"VDA\" },\n                { name: \"Piemonte\", abbreviation: \"PIE\" },\n                { name: \"Lombardia\", abbreviation: \"LOM\" },\n                { name: \"Veneto\", abbreviation: \"VEN\" },\n                { name: \"Trentino Alto Adige\", abbreviation: \"TAA\" },\n                { name: \"Friuli Venezia Giulia\", abbreviation: \"FVG\" },\n                { name: \"Liguria\", abbreviation: \"LIG\" },\n                { name: \"Emilia Romagna\", abbreviation: \"EMR\" },\n                { name: \"Toscana\", abbreviation: \"TOS\" },\n                { name: \"Umbria\", abbreviation: \"UMB\" },\n                { name: \"Marche\", abbreviation: \"MAR\" },\n                { name: \"Abruzzo\", abbreviation: \"ABR\" },\n                { name: \"Lazio\", abbreviation: \"LAZ\" },\n                { name: \"Campania\", abbreviation: \"CAM\" },\n                { name: \"Puglia\", abbreviation: \"PUG\" },\n                { name: \"Basilicata\", abbreviation: \"BAS\" },\n                { name: \"Molise\", abbreviation: \"MOL\" },\n                { name: \"Calabria\", abbreviation: \"CAL\" },\n                { name: \"Sicilia\", abbreviation: \"SIC\" },\n                { name: \"Sardegna\", abbreviation: \"SAR\" }\n            ]\n        },\n\n        street_suffixes: {\n            'us': [\n                {name: 'Avenue', abbreviation: 'Ave'},\n                {name: 'Boulevard', abbreviation: 'Blvd'},\n                {name: 'Center', abbreviation: 'Ctr'},\n                {name: 'Circle', abbreviation: 'Cir'},\n                {name: 'Court', abbreviation: 'Ct'},\n                {name: 'Drive', abbreviation: 'Dr'},\n                {name: 'Extension', abbreviation: 'Ext'},\n                {name: 'Glen', abbreviation: 'Gln'},\n                {name: 'Grove', abbreviation: 'Grv'},\n                {name: 'Heights', abbreviation: 'Hts'},\n                {name: 'Highway', abbreviation: 'Hwy'},\n                {name: 'Junction', abbreviation: 'Jct'},\n                {name: 'Key', abbreviation: 'Key'},\n                {name: 'Lane', abbreviation: 'Ln'},\n                {name: 'Loop', abbreviation: 'Loop'},\n                {name: 'Manor', abbreviation: 'Mnr'},\n                {name: 'Mill', abbreviation: 'Mill'},\n                {name: 'Park', abbreviation: 'Park'},\n                {name: 'Parkway', abbreviation: 'Pkwy'},\n                {name: 'Pass', abbreviation: 'Pass'},\n                {name: 'Path', abbreviation: 'Path'},\n                {name: 'Pike', abbreviation: 'Pike'},\n                {name: 'Place', abbreviation: 'Pl'},\n                {name: 'Plaza', abbreviation: 'Plz'},\n                {name: 'Point', abbreviation: 'Pt'},\n                {name: 'Ridge', abbreviation: 'Rdg'},\n                {name: 'River', abbreviation: 'Riv'},\n                {name: 'Road', abbreviation: 'Rd'},\n                {name: 'Square', abbreviation: 'Sq'},\n                {name: 'Street', abbreviation: 'St'},\n                {name: 'Terrace', abbreviation: 'Ter'},\n                {name: 'Trail', abbreviation: 'Trl'},\n                {name: 'Turnpike', abbreviation: 'Tpke'},\n                {name: 'View', abbreviation: 'Vw'},\n                {name: 'Way', abbreviation: 'Way'}\n            ],\n            'it': [\n                { name: 'Accesso', abbreviation: 'Acc.' },\n                { name: 'Alzaia', abbreviation: 'Alz.' },\n                { name: 'Arco', abbreviation: 'Arco' },\n                { name: 'Archivolto', abbreviation: 'Acv.' },\n                { name: 'Arena', abbreviation: 'Arena' },\n                { name: 'Argine', abbreviation: 'Argine' },\n                { name: 'Bacino', abbreviation: 'Bacino' },\n                { name: 'Banchi', abbreviation: 'Banchi' },\n                { name: 'Banchina', abbreviation: 'Ban.' },\n                { name: 'Bastioni', abbreviation: 'Bas.' },\n                { name: 'Belvedere', abbreviation: 'Belv.' },\n                { name: 'Borgata', abbreviation: 'B.ta' },\n                { name: 'Borgo', abbreviation: 'B.go' },\n                { name: 'Calata', abbreviation: 'Cal.' },\n                { name: 'Calle', abbreviation: 'Calle' },\n                { name: 'Campiello', abbreviation: 'Cam.' },\n                { name: 'Campo', abbreviation: 'Cam.' },\n                { name: 'Canale', abbreviation: 'Can.' },\n                { name: 'Carraia', abbreviation: 'Carr.' },\n                { name: 'Cascina', abbreviation: 'Cascina' },\n                { name: 'Case sparse', abbreviation: 'c.s.' },\n                { name: 'Cavalcavia', abbreviation: 'Cv.' },\n                { name: 'Circonvallazione', abbreviation: 'Cv.' },\n                { name: 'Complanare', abbreviation: 'C.re' },\n                { name: 'Contrada', abbreviation: 'C.da' },\n                { name: 'Corso', abbreviation: 'C.so' },\n                { name: 'Corte', abbreviation: 'C.te' },\n                { name: 'Cortile', abbreviation: 'C.le' },\n                { name: 'Diramazione', abbreviation: 'Dir.' },\n                { name: 'Fondaco', abbreviation: 'F.co' },\n                { name: 'Fondamenta', abbreviation: 'F.ta' },\n                { name: 'Fondo', abbreviation: 'F.do' },\n                { name: 'Frazione', abbreviation: 'Fr.' },\n                { name: 'Isola', abbreviation: 'Is.' },\n                { name: 'Largo', abbreviation: 'L.go' },\n                { name: 'Litoranea', abbreviation: 'Lit.' },\n                { name: 'Lungolago', abbreviation: 'L.go lago' },\n                { name: 'Lungo Po', abbreviation: 'l.go Po' },\n                { name: 'Molo', abbreviation: 'Molo' },\n                { name: 'Mura', abbreviation: 'Mura' },\n                { name: 'Passaggio privato', abbreviation: 'pass. priv.' },\n                { name: 'Passeggiata', abbreviation: 'Pass.' },\n                { name: 'Piazza', abbreviation: 'P.zza' },\n                { name: 'Piazzale', abbreviation: 'P.le' },\n                { name: 'Ponte', abbreviation: 'P.te' },\n                { name: 'Portico', abbreviation: 'P.co' },\n                { name: 'Rampa', abbreviation: 'Rampa' },\n                { name: 'Regione', abbreviation: 'Reg.' },\n                { name: 'Rione', abbreviation: 'R.ne' },\n                { name: 'Rio', abbreviation: 'Rio' },\n                { name: 'Ripa', abbreviation: 'Ripa' },\n                { name: 'Riva', abbreviation: 'Riva' },\n                { name: 'Rondò', abbreviation: 'Rondò' },\n                { name: 'Rotonda', abbreviation: 'Rot.' },\n                { name: 'Sagrato', abbreviation: 'Sagr.' },\n                { name: 'Salita', abbreviation: 'Sal.' },\n                { name: 'Scalinata', abbreviation: 'Scal.' },\n                { name: 'Scalone', abbreviation: 'Scal.' },\n                { name: 'Slargo', abbreviation: 'Sl.' },\n                { name: 'Sottoportico', abbreviation: 'Sott.' },\n                { name: 'Strada', abbreviation: 'Str.' },\n                { name: 'Stradale', abbreviation: 'Str.le' },\n                { name: 'Strettoia', abbreviation: 'Strett.' },\n                { name: 'Traversa', abbreviation: 'Trav.' },\n                { name: 'Via', abbreviation: 'V.' },\n                { name: 'Viale', abbreviation: 'V.le' },\n                { name: 'Vicinale', abbreviation: 'Vic.le' },\n                { name: 'Vicolo', abbreviation: 'Vic.' }\n            ],\n            'uk' : [\n                {name: 'Avenue', abbreviation: 'Ave'},\n                {name: 'Close', abbreviation: 'Cl'},\n                {name: 'Court', abbreviation: 'Ct'},\n                {name: 'Crescent', abbreviation: 'Cr'},\n                {name: 'Drive', abbreviation: 'Dr'},\n                {name: 'Garden', abbreviation: 'Gdn'},\n                {name: 'Gardens', abbreviation: 'Gdns'},\n                {name: 'Green', abbreviation: 'Gn'},\n                {name: 'Grove', abbreviation: 'Gr'},\n                {name: 'Lane', abbreviation: 'Ln'},\n                {name: 'Mount', abbreviation: 'Mt'},\n                {name: 'Place', abbreviation: 'Pl'},\n                {name: 'Park', abbreviation: 'Pk'},\n                {name: 'Ridge', abbreviation: 'Rdg'},\n                {name: 'Road', abbreviation: 'Rd'},\n                {name: 'Square', abbreviation: 'Sq'},\n                {name: 'Street', abbreviation: 'St'},\n                {name: 'Terrace', abbreviation: 'Ter'},\n                {name: 'Valley', abbreviation: 'Val'}\n            ]\n        },\n\n        months: [\n            {name: 'January', short_name: 'Jan', numeric: '01', days: 31},\n            // Not messing with leap years...\n            {name: 'February', short_name: 'Feb', numeric: '02', days: 28},\n            {name: 'March', short_name: 'Mar', numeric: '03', days: 31},\n            {name: 'April', short_name: 'Apr', numeric: '04', days: 30},\n            {name: 'May', short_name: 'May', numeric: '05', days: 31},\n            {name: 'June', short_name: 'Jun', numeric: '06', days: 30},\n            {name: 'July', short_name: 'Jul', numeric: '07', days: 31},\n            {name: 'August', short_name: 'Aug', numeric: '08', days: 31},\n            {name: 'September', short_name: 'Sep', numeric: '09', days: 30},\n            {name: 'October', short_name: 'Oct', numeric: '10', days: 31},\n            {name: 'November', short_name: 'Nov', numeric: '11', days: 30},\n            {name: 'December', short_name: 'Dec', numeric: '12', days: 31}\n        ],\n\n        // http://en.wikipedia.org/wiki/Bank_card_number#Issuer_identification_number_.28IIN.29\n        cc_types: [\n            {name: \"American Express\", short_name: 'amex', prefix: '34', length: 15},\n            {name: \"Bankcard\", short_name: 'bankcard', prefix: '5610', length: 16},\n            {name: \"China UnionPay\", short_name: 'chinaunion', prefix: '62', length: 16},\n            {name: \"Diners Club Carte Blanche\", short_name: 'dccarte', prefix: '300', length: 14},\n            {name: \"Diners Club enRoute\", short_name: 'dcenroute', prefix: '2014', length: 15},\n            {name: \"Diners Club International\", short_name: 'dcintl', prefix: '36', length: 14},\n            {name: \"Diners Club United States & Canada\", short_name: 'dcusc', prefix: '54', length: 16},\n            {name: \"Discover Card\", short_name: 'discover', prefix: '6011', length: 16},\n            {name: \"InstaPayment\", short_name: 'instapay', prefix: '637', length: 16},\n            {name: \"JCB\", short_name: 'jcb', prefix: '3528', length: 16},\n            {name: \"Laser\", short_name: 'laser', prefix: '6304', length: 16},\n            {name: \"Maestro\", short_name: 'maestro', prefix: '5018', length: 16},\n            {name: \"Mastercard\", short_name: 'mc', prefix: '51', length: 16},\n            {name: \"Solo\", short_name: 'solo', prefix: '6334', length: 16},\n            {name: \"Switch\", short_name: 'switch', prefix: '4903', length: 16},\n            {name: \"Visa\", short_name: 'visa', prefix: '4', length: 16},\n            {name: \"Visa Electron\", short_name: 'electron', prefix: '4026', length: 16}\n        ],\n\n        //return all world currency by ISO 4217\n        currency_types: [\n            {'code' : 'AED', 'name' : 'United Arab Emirates Dirham'},\n            {'code' : 'AFN', 'name' : 'Afghanistan Afghani'},\n            {'code' : 'ALL', 'name' : 'Albania Lek'},\n            {'code' : 'AMD', 'name' : 'Armenia Dram'},\n            {'code' : 'ANG', 'name' : 'Netherlands Antilles Guilder'},\n            {'code' : 'AOA', 'name' : 'Angola Kwanza'},\n            {'code' : 'ARS', 'name' : 'Argentina Peso'},\n            {'code' : 'AUD', 'name' : 'Australia Dollar'},\n            {'code' : 'AWG', 'name' : 'Aruba Guilder'},\n            {'code' : 'AZN', 'name' : 'Azerbaijan New Manat'},\n            {'code' : 'BAM', 'name' : 'Bosnia and Herzegovina Convertible Marka'},\n            {'code' : 'BBD', 'name' : 'Barbados Dollar'},\n            {'code' : 'BDT', 'name' : 'Bangladesh Taka'},\n            {'code' : 'BGN', 'name' : 'Bulgaria Lev'},\n            {'code' : 'BHD', 'name' : 'Bahrain Dinar'},\n            {'code' : 'BIF', 'name' : 'Burundi Franc'},\n            {'code' : 'BMD', 'name' : 'Bermuda Dollar'},\n            {'code' : 'BND', 'name' : 'Brunei Darussalam Dollar'},\n            {'code' : 'BOB', 'name' : 'Bolivia Boliviano'},\n            {'code' : 'BRL', 'name' : 'Brazil Real'},\n            {'code' : 'BSD', 'name' : 'Bahamas Dollar'},\n            {'code' : 'BTN', 'name' : 'Bhutan Ngultrum'},\n            {'code' : 'BWP', 'name' : 'Botswana Pula'},\n            {'code' : 'BYR', 'name' : 'Belarus Ruble'},\n            {'code' : 'BZD', 'name' : 'Belize Dollar'},\n            {'code' : 'CAD', 'name' : 'Canada Dollar'},\n            {'code' : 'CDF', 'name' : 'Congo/Kinshasa Franc'},\n            {'code' : 'CHF', 'name' : 'Switzerland Franc'},\n            {'code' : 'CLP', 'name' : 'Chile Peso'},\n            {'code' : 'CNY', 'name' : 'China Yuan Renminbi'},\n            {'code' : 'COP', 'name' : 'Colombia Peso'},\n            {'code' : 'CRC', 'name' : 'Costa Rica Colon'},\n            {'code' : 'CUC', 'name' : 'Cuba Convertible Peso'},\n            {'code' : 'CUP', 'name' : 'Cuba Peso'},\n            {'code' : 'CVE', 'name' : 'Cape Verde Escudo'},\n            {'code' : 'CZK', 'name' : 'Czech Republic Koruna'},\n            {'code' : 'DJF', 'name' : 'Djibouti Franc'},\n            {'code' : 'DKK', 'name' : 'Denmark Krone'},\n            {'code' : 'DOP', 'name' : 'Dominican Republic Peso'},\n            {'code' : 'DZD', 'name' : 'Algeria Dinar'},\n            {'code' : 'EGP', 'name' : 'Egypt Pound'},\n            {'code' : 'ERN', 'name' : 'Eritrea Nakfa'},\n            {'code' : 'ETB', 'name' : 'Ethiopia Birr'},\n            {'code' : 'EUR', 'name' : 'Euro Member Countries'},\n            {'code' : 'FJD', 'name' : 'Fiji Dollar'},\n            {'code' : 'FKP', 'name' : 'Falkland Islands (Malvinas) Pound'},\n            {'code' : 'GBP', 'name' : 'United Kingdom Pound'},\n            {'code' : 'GEL', 'name' : 'Georgia Lari'},\n            {'code' : 'GGP', 'name' : 'Guernsey Pound'},\n            {'code' : 'GHS', 'name' : 'Ghana Cedi'},\n            {'code' : 'GIP', 'name' : 'Gibraltar Pound'},\n            {'code' : 'GMD', 'name' : 'Gambia Dalasi'},\n            {'code' : 'GNF', 'name' : 'Guinea Franc'},\n            {'code' : 'GTQ', 'name' : 'Guatemala Quetzal'},\n            {'code' : 'GYD', 'name' : 'Guyana Dollar'},\n            {'code' : 'HKD', 'name' : 'Hong Kong Dollar'},\n            {'code' : 'HNL', 'name' : 'Honduras Lempira'},\n            {'code' : 'HRK', 'name' : 'Croatia Kuna'},\n            {'code' : 'HTG', 'name' : 'Haiti Gourde'},\n            {'code' : 'HUF', 'name' : 'Hungary Forint'},\n            {'code' : 'IDR', 'name' : 'Indonesia Rupiah'},\n            {'code' : 'ILS', 'name' : 'Israel Shekel'},\n            {'code' : 'IMP', 'name' : 'Isle of Man Pound'},\n            {'code' : 'INR', 'name' : 'India Rupee'},\n            {'code' : 'IQD', 'name' : 'Iraq Dinar'},\n            {'code' : 'IRR', 'name' : 'Iran Rial'},\n            {'code' : 'ISK', 'name' : 'Iceland Krona'},\n            {'code' : 'JEP', 'name' : 'Jersey Pound'},\n            {'code' : 'JMD', 'name' : 'Jamaica Dollar'},\n            {'code' : 'JOD', 'name' : 'Jordan Dinar'},\n            {'code' : 'JPY', 'name' : 'Japan Yen'},\n            {'code' : 'KES', 'name' : 'Kenya Shilling'},\n            {'code' : 'KGS', 'name' : 'Kyrgyzstan Som'},\n            {'code' : 'KHR', 'name' : 'Cambodia Riel'},\n            {'code' : 'KMF', 'name' : 'Comoros Franc'},\n            {'code' : 'KPW', 'name' : 'Korea (North) Won'},\n            {'code' : 'KRW', 'name' : 'Korea (South) Won'},\n            {'code' : 'KWD', 'name' : 'Kuwait Dinar'},\n            {'code' : 'KYD', 'name' : 'Cayman Islands Dollar'},\n            {'code' : 'KZT', 'name' : 'Kazakhstan Tenge'},\n            {'code' : 'LAK', 'name' : 'Laos Kip'},\n            {'code' : 'LBP', 'name' : 'Lebanon Pound'},\n            {'code' : 'LKR', 'name' : 'Sri Lanka Rupee'},\n            {'code' : 'LRD', 'name' : 'Liberia Dollar'},\n            {'code' : 'LSL', 'name' : 'Lesotho Loti'},\n            {'code' : 'LTL', 'name' : 'Lithuania Litas'},\n            {'code' : 'LYD', 'name' : 'Libya Dinar'},\n            {'code' : 'MAD', 'name' : 'Morocco Dirham'},\n            {'code' : 'MDL', 'name' : 'Moldova Leu'},\n            {'code' : 'MGA', 'name' : 'Madagascar Ariary'},\n            {'code' : 'MKD', 'name' : 'Macedonia Denar'},\n            {'code' : 'MMK', 'name' : 'Myanmar (Burma) Kyat'},\n            {'code' : 'MNT', 'name' : 'Mongolia Tughrik'},\n            {'code' : 'MOP', 'name' : 'Macau Pataca'},\n            {'code' : 'MRO', 'name' : 'Mauritania Ouguiya'},\n            {'code' : 'MUR', 'name' : 'Mauritius Rupee'},\n            {'code' : 'MVR', 'name' : 'Maldives (Maldive Islands) Rufiyaa'},\n            {'code' : 'MWK', 'name' : 'Malawi Kwacha'},\n            {'code' : 'MXN', 'name' : 'Mexico Peso'},\n            {'code' : 'MYR', 'name' : 'Malaysia Ringgit'},\n            {'code' : 'MZN', 'name' : 'Mozambique Metical'},\n            {'code' : 'NAD', 'name' : 'Namibia Dollar'},\n            {'code' : 'NGN', 'name' : 'Nigeria Naira'},\n            {'code' : 'NIO', 'name' : 'Nicaragua Cordoba'},\n            {'code' : 'NOK', 'name' : 'Norway Krone'},\n            {'code' : 'NPR', 'name' : 'Nepal Rupee'},\n            {'code' : 'NZD', 'name' : 'New Zealand Dollar'},\n            {'code' : 'OMR', 'name' : 'Oman Rial'},\n            {'code' : 'PAB', 'name' : 'Panama Balboa'},\n            {'code' : 'PEN', 'name' : 'Peru Nuevo Sol'},\n            {'code' : 'PGK', 'name' : 'Papua New Guinea Kina'},\n            {'code' : 'PHP', 'name' : 'Philippines Peso'},\n            {'code' : 'PKR', 'name' : 'Pakistan Rupee'},\n            {'code' : 'PLN', 'name' : 'Poland Zloty'},\n            {'code' : 'PYG', 'name' : 'Paraguay Guarani'},\n            {'code' : 'QAR', 'name' : 'Qatar Riyal'},\n            {'code' : 'RON', 'name' : 'Romania New Leu'},\n            {'code' : 'RSD', 'name' : 'Serbia Dinar'},\n            {'code' : 'RUB', 'name' : 'Russia Ruble'},\n            {'code' : 'RWF', 'name' : 'Rwanda Franc'},\n            {'code' : 'SAR', 'name' : 'Saudi Arabia Riyal'},\n            {'code' : 'SBD', 'name' : 'Solomon Islands Dollar'},\n            {'code' : 'SCR', 'name' : 'Seychelles Rupee'},\n            {'code' : 'SDG', 'name' : 'Sudan Pound'},\n            {'code' : 'SEK', 'name' : 'Sweden Krona'},\n            {'code' : 'SGD', 'name' : 'Singapore Dollar'},\n            {'code' : 'SHP', 'name' : 'Saint Helena Pound'},\n            {'code' : 'SLL', 'name' : 'Sierra Leone Leone'},\n            {'code' : 'SOS', 'name' : 'Somalia Shilling'},\n            {'code' : 'SPL', 'name' : 'Seborga Luigino'},\n            {'code' : 'SRD', 'name' : 'Suriname Dollar'},\n            {'code' : 'STD', 'name' : 'São Tomé and Príncipe Dobra'},\n            {'code' : 'SVC', 'name' : 'El Salvador Colon'},\n            {'code' : 'SYP', 'name' : 'Syria Pound'},\n            {'code' : 'SZL', 'name' : 'Swaziland Lilangeni'},\n            {'code' : 'THB', 'name' : 'Thailand Baht'},\n            {'code' : 'TJS', 'name' : 'Tajikistan Somoni'},\n            {'code' : 'TMT', 'name' : 'Turkmenistan Manat'},\n            {'code' : 'TND', 'name' : 'Tunisia Dinar'},\n            {'code' : 'TOP', 'name' : 'Tonga Pa\\'anga'},\n            {'code' : 'TRY', 'name' : 'Turkey Lira'},\n            {'code' : 'TTD', 'name' : 'Trinidad and Tobago Dollar'},\n            {'code' : 'TVD', 'name' : 'Tuvalu Dollar'},\n            {'code' : 'TWD', 'name' : 'Taiwan New Dollar'},\n            {'code' : 'TZS', 'name' : 'Tanzania Shilling'},\n            {'code' : 'UAH', 'name' : 'Ukraine Hryvnia'},\n            {'code' : 'UGX', 'name' : 'Uganda Shilling'},\n            {'code' : 'USD', 'name' : 'United States Dollar'},\n            {'code' : 'UYU', 'name' : 'Uruguay Peso'},\n            {'code' : 'UZS', 'name' : 'Uzbekistan Som'},\n            {'code' : 'VEF', 'name' : 'Venezuela Bolivar'},\n            {'code' : 'VND', 'name' : 'Viet Nam Dong'},\n            {'code' : 'VUV', 'name' : 'Vanuatu Vatu'},\n            {'code' : 'WST', 'name' : 'Samoa Tala'},\n            {'code' : 'XAF', 'name' : 'Communauté Financière Africaine (BEAC) CFA Franc BEAC'},\n            {'code' : 'XCD', 'name' : 'East Caribbean Dollar'},\n            {'code' : 'XDR', 'name' : 'International Monetary Fund (IMF) Special Drawing Rights'},\n            {'code' : 'XOF', 'name' : 'Communauté Financière Africaine (BCEAO) Franc'},\n            {'code' : 'XPF', 'name' : 'Comptoirs Français du Pacifique (CFP) Franc'},\n            {'code' : 'YER', 'name' : 'Yemen Rial'},\n            {'code' : 'ZAR', 'name' : 'South Africa Rand'},\n            {'code' : 'ZMW', 'name' : 'Zambia Kwacha'},\n            {'code' : 'ZWD', 'name' : 'Zimbabwe Dollar'}\n        ],\n\n        // return the names of all valide colors\n        colorNames : [  \"AliceBlue\", \"Black\", \"Navy\", \"DarkBlue\", \"MediumBlue\", \"Blue\", \"DarkGreen\", \"Green\", \"Teal\", \"DarkCyan\", \"DeepSkyBlue\", \"DarkTurquoise\", \"MediumSpringGreen\", \"Lime\", \"SpringGreen\",\n            \"Aqua\", \"Cyan\", \"MidnightBlue\", \"DodgerBlue\", \"LightSeaGreen\", \"ForestGreen\", \"SeaGreen\", \"DarkSlateGray\", \"LimeGreen\", \"MediumSeaGreen\", \"Turquoise\", \"RoyalBlue\", \"SteelBlue\", \"DarkSlateBlue\", \"MediumTurquoise\",\n            \"Indigo\", \"DarkOliveGreen\", \"CadetBlue\", \"CornflowerBlue\", \"RebeccaPurple\", \"MediumAquaMarine\", \"DimGray\", \"SlateBlue\", \"OliveDrab\", \"SlateGray\", \"LightSlateGray\", \"MediumSlateBlue\", \"LawnGreen\", \"Chartreuse\",\n            \"Aquamarine\", \"Maroon\", \"Purple\", \"Olive\", \"Gray\", \"SkyBlue\", \"LightSkyBlue\", \"BlueViolet\", \"DarkRed\", \"DarkMagenta\", \"SaddleBrown\", \"Ivory\", \"White\",\n            \"DarkSeaGreen\", \"LightGreen\", \"MediumPurple\", \"DarkViolet\", \"PaleGreen\", \"DarkOrchid\", \"YellowGreen\", \"Sienna\", \"Brown\", \"DarkGray\", \"LightBlue\", \"GreenYellow\", \"PaleTurquoise\", \"LightSteelBlue\", \"PowderBlue\",\n            \"FireBrick\", \"DarkGoldenRod\", \"MediumOrchid\", \"RosyBrown\", \"DarkKhaki\", \"Silver\", \"MediumVioletRed\", \"IndianRed\", \"Peru\", \"Chocolate\", \"Tan\", \"LightGray\", \"Thistle\", \"Orchid\", \"GoldenRod\", \"PaleVioletRed\",\n            \"Crimson\", \"Gainsboro\", \"Plum\", \"BurlyWood\", \"LightCyan\", \"Lavender\", \"DarkSalmon\", \"Violet\", \"PaleGoldenRod\", \"LightCoral\", \"Khaki\", \"AliceBlue\", \"HoneyDew\", \"Azure\", \"SandyBrown\", \"Wheat\", \"Beige\", \"WhiteSmoke\",\n            \"MintCream\", \"GhostWhite\", \"Salmon\", \"AntiqueWhite\", \"Linen\", \"LightGoldenRodYellow\", \"OldLace\", \"Red\", \"Fuchsia\", \"Magenta\", \"DeepPink\", \"OrangeRed\", \"Tomato\", \"HotPink\", \"Coral\", \"DarkOrange\", \"LightSalmon\", \"Orange\",\n            \"LightPink\", \"Pink\", \"Gold\", \"PeachPuff\", \"NavajoWhite\", \"Moccasin\", \"Bisque\", \"MistyRose\", \"BlanchedAlmond\", \"PapayaWhip\", \"LavenderBlush\", \"SeaShell\", \"Cornsilk\", \"LemonChiffon\", \"FloralWhite\", \"Snow\", \"Yellow\", \"LightYellow\"\n        ],\n\n        // Data taken from https://www.sec.gov/rules/other/4-460list.htm\n        company: [ \"3Com Corp\",\n        \"3M Company\",\n        \"A.G. Edwards Inc.\",\n        \"Abbott Laboratories\",\n        \"Abercrombie & Fitch Co.\",\n        \"ABM Industries Incorporated\",\n        \"Ace Hardware Corporation\",\n        \"ACT Manufacturing Inc.\",\n        \"Acterna Corp.\",\n        \"Adams Resources & Energy, Inc.\",\n        \"ADC Telecommunications, Inc.\",\n        \"Adelphia Communications Corporation\",\n        \"Administaff, Inc.\",\n        \"Adobe Systems Incorporated\",\n        \"Adolph Coors Company\",\n        \"Advance Auto Parts, Inc.\",\n        \"Advanced Micro Devices, Inc.\",\n        \"AdvancePCS, Inc.\",\n        \"Advantica Restaurant Group, Inc.\",\n        \"The AES Corporation\",\n        \"Aetna Inc.\",\n        \"Affiliated Computer Services, Inc.\",\n        \"AFLAC Incorporated\",\n        \"AGCO Corporation\",\n        \"Agilent Technologies, Inc.\",\n        \"Agway Inc.\",\n        \"Apartment Investment and Management Company\",\n        \"Air Products and Chemicals, Inc.\",\n        \"Airborne, Inc.\",\n        \"Airgas, Inc.\",\n        \"AK Steel Holding Corporation\",\n        \"Alaska Air Group, Inc.\",\n        \"Alberto-Culver Company\",\n        \"Albertson's, Inc.\",\n        \"Alcoa Inc.\",\n        \"Alleghany Corporation\",\n        \"Allegheny Energy, Inc.\",\n        \"Allegheny Technologies Incorporated\",\n        \"Allergan, Inc.\",\n        \"ALLETE, Inc.\",\n        \"Alliant Energy Corporation\",\n        \"Allied Waste Industries, Inc.\",\n        \"Allmerica Financial Corporation\",\n        \"The Allstate Corporation\",\n        \"ALLTEL Corporation\",\n        \"The Alpine Group, Inc.\",\n        \"Amazon.com, Inc.\",\n        \"AMC Entertainment Inc.\",\n        \"American Power Conversion Corporation\",\n        \"Amerada Hess Corporation\",\n        \"AMERCO\",\n        \"Ameren Corporation\",\n        \"America West Holdings Corporation\",\n        \"American Axle & Manufacturing Holdings, Inc.\",\n        \"American Eagle Outfitters, Inc.\",\n        \"American Electric Power Company, Inc.\",\n        \"American Express Company\",\n        \"American Financial Group, Inc.\",\n        \"American Greetings Corporation\",\n        \"American International Group, Inc.\",\n        \"American Standard Companies Inc.\",\n        \"American Water Works Company, Inc.\",\n        \"AmerisourceBergen Corporation\",\n        \"Ames Department Stores, Inc.\",\n        \"Amgen Inc.\",\n        \"Amkor Technology, Inc.\",\n        \"AMR Corporation\",\n        \"AmSouth Bancorp.\",\n        \"Amtran, Inc.\",\n        \"Anadarko Petroleum Corporation\",\n        \"Analog Devices, Inc.\",\n        \"Anheuser-Busch Companies, Inc.\",\n        \"Anixter International Inc.\",\n        \"AnnTaylor Inc.\",\n        \"Anthem, Inc.\",\n        \"AOL Time Warner Inc.\",\n        \"Aon Corporation\",\n        \"Apache Corporation\",\n        \"Apple Computer, Inc.\",\n        \"Applera Corporation\",\n        \"Applied Industrial Technologies, Inc.\",\n        \"Applied Materials, Inc.\",\n        \"Aquila, Inc.\",\n        \"ARAMARK Corporation\",\n        \"Arch Coal, Inc.\",\n        \"Archer Daniels Midland Company\",\n        \"Arkansas Best Corporation\",\n        \"Armstrong Holdings, Inc.\",\n        \"Arrow Electronics, Inc.\",\n        \"ArvinMeritor, Inc.\",\n        \"Ashland Inc.\",\n        \"Astoria Financial Corporation\",\n        \"AT&T Corp.\",\n        \"Atmel Corporation\",\n        \"Atmos Energy Corporation\",\n        \"Audiovox Corporation\",\n        \"Autoliv, Inc.\",\n        \"Automatic Data Processing, Inc.\",\n        \"AutoNation, Inc.\",\n        \"AutoZone, Inc.\",\n        \"Avaya Inc.\",\n        \"Avery Dennison Corporation\",\n        \"Avista Corporation\",\n        \"Avnet, Inc.\",\n        \"Avon Products, Inc.\",\n        \"Baker Hughes Incorporated\",\n        \"Ball Corporation\",\n        \"Bank of America Corporation\",\n        \"The Bank of New York Company, Inc.\",\n        \"Bank One Corporation\",\n        \"Banknorth Group, Inc.\",\n        \"Banta Corporation\",\n        \"Barnes & Noble, Inc.\",\n        \"Bausch & Lomb Incorporated\",\n        \"Baxter International Inc.\",\n        \"BB&T Corporation\",\n        \"The Bear Stearns Companies Inc.\",\n        \"Beazer Homes USA, Inc.\",\n        \"Beckman Coulter, Inc.\",\n        \"Becton, Dickinson and Company\",\n        \"Bed Bath & Beyond Inc.\",\n        \"Belk, Inc.\",\n        \"Bell Microproducts Inc.\",\n        \"BellSouth Corporation\",\n        \"Belo Corp.\",\n        \"Bemis Company, Inc.\",\n        \"Benchmark Electronics, Inc.\",\n        \"Berkshire Hathaway Inc.\",\n        \"Best Buy Co., Inc.\",\n        \"Bethlehem Steel Corporation\",\n        \"Beverly Enterprises, Inc.\",\n        \"Big Lots, Inc.\",\n        \"BJ Services Company\",\n        \"BJ's Wholesale Club, Inc.\",\n        \"The Black & Decker Corporation\",\n        \"Black Hills Corporation\",\n        \"BMC Software, Inc.\",\n        \"The Boeing Company\",\n        \"Boise Cascade Corporation\",\n        \"Borders Group, Inc.\",\n        \"BorgWarner Inc.\",\n        \"Boston Scientific Corporation\",\n        \"Bowater Incorporated\",\n        \"Briggs & Stratton Corporation\",\n        \"Brightpoint, Inc.\",\n        \"Brinker International, Inc.\",\n        \"Bristol-Myers Squibb Company\",\n        \"Broadwing, Inc.\",\n        \"Brown Shoe Company, Inc.\",\n        \"Brown-Forman Corporation\",\n        \"Brunswick Corporation\",\n        \"Budget Group, Inc.\",\n        \"Burlington Coat Factory Warehouse Corporation\",\n        \"Burlington Industries, Inc.\",\n        \"Burlington Northern Santa Fe Corporation\",\n        \"Burlington Resources Inc.\",\n        \"C. H. Robinson Worldwide Inc.\",\n        \"Cablevision Systems Corp\",\n        \"Cabot Corp\",\n        \"Cadence Design Systems, Inc.\",\n        \"Calpine Corp.\",\n        \"Campbell Soup Co.\",\n        \"Capital One Financial Corp.\",\n        \"Cardinal Health Inc.\",\n        \"Caremark Rx Inc.\",\n        \"Carlisle Cos. Inc.\",\n        \"Carpenter Technology Corp.\",\n        \"Casey's General Stores Inc.\",\n        \"Caterpillar Inc.\",\n        \"CBRL Group Inc.\",\n        \"CDI Corp.\",\n        \"CDW Computer Centers Inc.\",\n        \"CellStar Corp.\",\n        \"Cendant Corp\",\n        \"Cenex Harvest States Cooperatives\",\n        \"Centex Corp.\",\n        \"CenturyTel Inc.\",\n        \"Ceridian Corp.\",\n        \"CH2M Hill Cos. Ltd.\",\n        \"Champion Enterprises Inc.\",\n        \"Charles Schwab Corp.\",\n        \"Charming Shoppes Inc.\",\n        \"Charter Communications Inc.\",\n        \"Charter One Financial Inc.\",\n        \"ChevronTexaco Corp.\",\n        \"Chiquita Brands International Inc.\",\n        \"Chubb Corp\",\n        \"Ciena Corp.\",\n        \"Cigna Corp\",\n        \"Cincinnati Financial Corp.\",\n        \"Cinergy Corp.\",\n        \"Cintas Corp.\",\n        \"Circuit City Stores Inc.\",\n        \"Cisco Systems Inc.\",\n        \"Citigroup, Inc\",\n        \"Citizens Communications Co.\",\n        \"CKE Restaurants Inc.\",\n        \"Clear Channel Communications Inc.\",\n        \"The Clorox Co.\",\n        \"CMGI Inc.\",\n        \"CMS Energy Corp.\",\n        \"CNF Inc.\",\n        \"Coca-Cola Co.\",\n        \"Coca-Cola Enterprises Inc.\",\n        \"Colgate-Palmolive Co.\",\n        \"Collins & Aikman Corp.\",\n        \"Comcast Corp.\",\n        \"Comdisco Inc.\",\n        \"Comerica Inc.\",\n        \"Comfort Systems USA Inc.\",\n        \"Commercial Metals Co.\",\n        \"Community Health Systems Inc.\",\n        \"Compass Bancshares Inc\",\n        \"Computer Associates International Inc.\",\n        \"Computer Sciences Corp.\",\n        \"Compuware Corp.\",\n        \"Comverse Technology Inc.\",\n        \"ConAgra Foods Inc.\",\n        \"Concord EFS Inc.\",\n        \"Conectiv, Inc\",\n        \"Conoco Inc\",\n        \"Conseco Inc.\",\n        \"Consolidated Freightways Corp.\",\n        \"Consolidated Edison Inc.\",\n        \"Constellation Brands Inc.\",\n        \"Constellation Emergy Group Inc.\",\n        \"Continental Airlines Inc.\",\n        \"Convergys Corp.\",\n        \"Cooper Cameron Corp.\",\n        \"Cooper Industries Ltd.\",\n        \"Cooper Tire & Rubber Co.\",\n        \"Corn Products International Inc.\",\n        \"Corning Inc.\",\n        \"Costco Wholesale Corp.\",\n        \"Countrywide Credit Industries Inc.\",\n        \"Coventry Health Care Inc.\",\n        \"Cox Communications Inc.\",\n        \"Crane Co.\",\n        \"Crompton Corp.\",\n        \"Crown Cork & Seal Co. Inc.\",\n        \"CSK Auto Corp.\",\n        \"CSX Corp.\",\n        \"Cummins Inc.\",\n        \"CVS Corp.\",\n        \"Cytec Industries Inc.\",\n        \"D&K Healthcare Resources, Inc.\",\n        \"D.R. Horton Inc.\",\n        \"Dana Corporation\",\n        \"Danaher Corporation\",\n        \"Darden Restaurants Inc.\",\n        \"DaVita Inc.\",\n        \"Dean Foods Company\",\n        \"Deere & Company\",\n        \"Del Monte Foods Co\",\n        \"Dell Computer Corporation\",\n        \"Delphi Corp.\",\n        \"Delta Air Lines Inc.\",\n        \"Deluxe Corporation\",\n        \"Devon Energy Corporation\",\n        \"Di Giorgio Corporation\",\n        \"Dial Corporation\",\n        \"Diebold Incorporated\",\n        \"Dillard's Inc.\",\n        \"DIMON Incorporated\",\n        \"Dole Food Company, Inc.\",\n        \"Dollar General Corporation\",\n        \"Dollar Tree Stores, Inc.\",\n        \"Dominion Resources, Inc.\",\n        \"Domino's Pizza LLC\",\n        \"Dover Corporation, Inc.\",\n        \"Dow Chemical Company\",\n        \"Dow Jones & Company, Inc.\",\n        \"DPL Inc.\",\n        \"DQE Inc.\",\n        \"Dreyer's Grand Ice Cream, Inc.\",\n        \"DST Systems, Inc.\",\n        \"DTE Energy Co.\",\n        \"E.I. Du Pont de Nemours and Company\",\n        \"Duke Energy Corp\",\n        \"Dun & Bradstreet Inc.\",\n        \"DURA Automotive Systems Inc.\",\n        \"DynCorp\",\n        \"Dynegy Inc.\",\n        \"E*Trade Group, Inc.\",\n        \"E.W. Scripps Company\",\n        \"Earthlink, Inc.\",\n        \"Eastman Chemical Company\",\n        \"Eastman Kodak Company\",\n        \"Eaton Corporation\",\n        \"Echostar Communications Corporation\",\n        \"Ecolab Inc.\",\n        \"Edison International\",\n        \"EGL Inc.\",\n        \"El Paso Corporation\",\n        \"Electronic Arts Inc.\",\n        \"Electronic Data Systems Corp.\",\n        \"Eli Lilly and Company\",\n        \"EMC Corporation\",\n        \"Emcor Group Inc.\",\n        \"Emerson Electric Co.\",\n        \"Encompass Services Corporation\",\n        \"Energizer Holdings Inc.\",\n        \"Energy East Corporation\",\n        \"Engelhard Corporation\",\n        \"Enron Corp.\",\n        \"Entergy Corporation\",\n        \"Enterprise Products Partners L.P.\",\n        \"EOG Resources, Inc.\",\n        \"Equifax Inc.\",\n        \"Equitable Resources Inc.\",\n        \"Equity Office Properties Trust\",\n        \"Equity Residential Properties Trust\",\n        \"Estee Lauder Companies Inc.\",\n        \"Exelon Corporation\",\n        \"Exide Technologies\",\n        \"Expeditors International of Washington Inc.\",\n        \"Express Scripts Inc.\",\n        \"ExxonMobil Corporation\",\n        \"Fairchild Semiconductor International Inc.\",\n        \"Family Dollar Stores Inc.\",\n        \"Farmland Industries Inc.\",\n        \"Federal Mogul Corp.\",\n        \"Federated Department Stores Inc.\",\n        \"Federal Express Corp.\",\n        \"Felcor Lodging Trust Inc.\",\n        \"Ferro Corp.\",\n        \"Fidelity National Financial Inc.\",\n        \"Fifth Third Bancorp\",\n        \"First American Financial Corp.\",\n        \"First Data Corp.\",\n        \"First National of Nebraska Inc.\",\n        \"First Tennessee National Corp.\",\n        \"FirstEnergy Corp.\",\n        \"Fiserv Inc.\",\n        \"Fisher Scientific International Inc.\",\n        \"FleetBoston Financial Co.\",\n        \"Fleetwood Enterprises Inc.\",\n        \"Fleming Companies Inc.\",\n        \"Flowers Foods Inc.\",\n        \"Flowserv Corp\",\n        \"Fluor Corp\",\n        \"FMC Corp\",\n        \"Foamex International Inc\",\n        \"Foot Locker Inc\",\n        \"Footstar Inc.\",\n        \"Ford Motor Co\",\n        \"Forest Laboratories Inc.\",\n        \"Fortune Brands Inc.\",\n        \"Foster Wheeler Ltd.\",\n        \"FPL Group Inc.\",\n        \"Franklin Resources Inc.\",\n        \"Freeport McMoran Copper & Gold Inc.\",\n        \"Frontier Oil Corp\",\n        \"Furniture Brands International Inc.\",\n        \"Gannett Co., Inc.\",\n        \"Gap Inc.\",\n        \"Gateway Inc.\",\n        \"GATX Corporation\",\n        \"Gemstar-TV Guide International Inc.\",\n        \"GenCorp Inc.\",\n        \"General Cable Corporation\",\n        \"General Dynamics Corporation\",\n        \"General Electric Company\",\n        \"General Mills Inc\",\n        \"General Motors Corporation\",\n        \"Genesis Health Ventures Inc.\",\n        \"Gentek Inc.\",\n        \"Gentiva Health Services Inc.\",\n        \"Genuine Parts Company\",\n        \"Genuity Inc.\",\n        \"Genzyme Corporation\",\n        \"Georgia Gulf Corporation\",\n        \"Georgia-Pacific Corporation\",\n        \"Gillette Company\",\n        \"Gold Kist Inc.\",\n        \"Golden State Bancorp Inc.\",\n        \"Golden West Financial Corporation\",\n        \"Goldman Sachs Group Inc.\",\n        \"Goodrich Corporation\",\n        \"The Goodyear Tire & Rubber Company\",\n        \"Granite Construction Incorporated\",\n        \"Graybar Electric Company Inc.\",\n        \"Great Lakes Chemical Corporation\",\n        \"Great Plains Energy Inc.\",\n        \"GreenPoint Financial Corp.\",\n        \"Greif Bros. Corporation\",\n        \"Grey Global Group Inc.\",\n        \"Group 1 Automotive Inc.\",\n        \"Guidant Corporation\",\n        \"H&R Block Inc.\",\n        \"H.B. Fuller Company\",\n        \"H.J. Heinz Company\",\n        \"Halliburton Co.\",\n        \"Harley-Davidson Inc.\",\n        \"Harman International Industries Inc.\",\n        \"Harrah's Entertainment Inc.\",\n        \"Harris Corp.\",\n        \"Harsco Corp.\",\n        \"Hartford Financial Services Group Inc.\",\n        \"Hasbro Inc.\",\n        \"Hawaiian Electric Industries Inc.\",\n        \"HCA Inc.\",\n        \"Health Management Associates Inc.\",\n        \"Health Net Inc.\",\n        \"Healthsouth Corp\",\n        \"Henry Schein Inc.\",\n        \"Hercules Inc.\",\n        \"Herman Miller Inc.\",\n        \"Hershey Foods Corp.\",\n        \"Hewlett-Packard Company\",\n        \"Hibernia Corp.\",\n        \"Hillenbrand Industries Inc.\",\n        \"Hilton Hotels Corp.\",\n        \"Hollywood Entertainment Corp.\",\n        \"Home Depot Inc.\",\n        \"Hon Industries Inc.\",\n        \"Honeywell International Inc.\",\n        \"Hormel Foods Corp.\",\n        \"Host Marriott Corp.\",\n        \"Household International Corp.\",\n        \"Hovnanian Enterprises Inc.\",\n        \"Hub Group Inc.\",\n        \"Hubbell Inc.\",\n        \"Hughes Supply Inc.\",\n        \"Humana Inc.\",\n        \"Huntington Bancshares Inc.\",\n        \"Idacorp Inc.\",\n        \"IDT Corporation\",\n        \"IKON Office Solutions Inc.\",\n        \"Illinois Tool Works Inc.\",\n        \"IMC Global Inc.\",\n        \"Imperial Sugar Company\",\n        \"IMS Health Inc.\",\n        \"Ingles Market Inc\",\n        \"Ingram Micro Inc.\",\n        \"Insight Enterprises Inc.\",\n        \"Integrated Electrical Services Inc.\",\n        \"Intel Corporation\",\n        \"International Paper Co.\",\n        \"Interpublic Group of Companies Inc.\",\n        \"Interstate Bakeries Corporation\",\n        \"International Business Machines Corp.\",\n        \"International Flavors & Fragrances Inc.\",\n        \"International Multifoods Corporation\",\n        \"Intuit Inc.\",\n        \"IT Group Inc.\",\n        \"ITT Industries Inc.\",\n        \"Ivax Corp.\",\n        \"J.B. Hunt Transport Services Inc.\",\n        \"J.C. Penny Co.\",\n        \"J.P. Morgan Chase & Co.\",\n        \"Jabil Circuit Inc.\",\n        \"Jack In The Box Inc.\",\n        \"Jacobs Engineering Group Inc.\",\n        \"JDS Uniphase Corp.\",\n        \"Jefferson-Pilot Co.\",\n        \"John Hancock Financial Services Inc.\",\n        \"Johnson & Johnson\",\n        \"Johnson Controls Inc.\",\n        \"Jones Apparel Group Inc.\",\n        \"KB Home\",\n        \"Kellogg Company\",\n        \"Kellwood Company\",\n        \"Kelly Services Inc.\",\n        \"Kemet Corp.\",\n        \"Kennametal Inc.\",\n        \"Kerr-McGee Corporation\",\n        \"KeyCorp\",\n        \"KeySpan Corp.\",\n        \"Kimball International Inc.\",\n        \"Kimberly-Clark Corporation\",\n        \"Kindred Healthcare Inc.\",\n        \"KLA-Tencor Corporation\",\n        \"K-Mart Corp.\",\n        \"Knight-Ridder Inc.\",\n        \"Kohl's Corp.\",\n        \"KPMG Consulting Inc.\",\n        \"Kroger Co.\",\n        \"L-3 Communications Holdings Inc.\",\n        \"Laboratory Corporation of America Holdings\",\n        \"Lam Research Corporation\",\n        \"LandAmerica Financial Group Inc.\",\n        \"Lands' End Inc.\",\n        \"Landstar System Inc.\",\n        \"La-Z-Boy Inc.\",\n        \"Lear Corporation\",\n        \"Legg Mason Inc.\",\n        \"Leggett & Platt Inc.\",\n        \"Lehman Brothers Holdings Inc.\",\n        \"Lennar Corporation\",\n        \"Lennox International Inc.\",\n        \"Level 3 Communications Inc.\",\n        \"Levi Strauss & Co.\",\n        \"Lexmark International Inc.\",\n        \"Limited Inc.\",\n        \"Lincoln National Corporation\",\n        \"Linens 'n Things Inc.\",\n        \"Lithia Motors Inc.\",\n        \"Liz Claiborne Inc.\",\n        \"Lockheed Martin Corporation\",\n        \"Loews Corporation\",\n        \"Longs Drug Stores Corporation\",\n        \"Louisiana-Pacific Corporation\",\n        \"Lowe's Companies Inc.\",\n        \"LSI Logic Corporation\",\n        \"The LTV Corporation\",\n        \"The Lubrizol Corporation\",\n        \"Lucent Technologies Inc.\",\n        \"Lyondell Chemical Company\",\n        \"M & T Bank Corporation\",\n        \"Magellan Health Services Inc.\",\n        \"Mail-Well Inc.\",\n        \"Mandalay Resort Group\",\n        \"Manor Care Inc.\",\n        \"Manpower Inc.\",\n        \"Marathon Oil Corporation\",\n        \"Mariner Health Care Inc.\",\n        \"Markel Corporation\",\n        \"Marriott International Inc.\",\n        \"Marsh & McLennan Companies Inc.\",\n        \"Marsh Supermarkets Inc.\",\n        \"Marshall & Ilsley Corporation\",\n        \"Martin Marietta Materials Inc.\",\n        \"Masco Corporation\",\n        \"Massey Energy Company\",\n        \"MasTec Inc.\",\n        \"Mattel Inc.\",\n        \"Maxim Integrated Products Inc.\",\n        \"Maxtor Corporation\",\n        \"Maxxam Inc.\",\n        \"The May Department Stores Company\",\n        \"Maytag Corporation\",\n        \"MBNA Corporation\",\n        \"McCormick & Company Incorporated\",\n        \"McDonald's Corporation\",\n        \"The McGraw-Hill Companies Inc.\",\n        \"McKesson Corporation\",\n        \"McLeodUSA Incorporated\",\n        \"M.D.C. Holdings Inc.\",\n        \"MDU Resources Group Inc.\",\n        \"MeadWestvaco Corporation\",\n        \"Medtronic Inc.\",\n        \"Mellon Financial Corporation\",\n        \"The Men's Wearhouse Inc.\",\n        \"Merck & Co., Inc.\",\n        \"Mercury General Corporation\",\n        \"Merrill Lynch & Co. Inc.\",\n        \"Metaldyne Corporation\",\n        \"Metals USA Inc.\",\n        \"MetLife Inc.\",\n        \"Metris Companies Inc\",\n        \"MGIC Investment Corporation\",\n        \"MGM Mirage\",\n        \"Michaels Stores Inc.\",\n        \"Micron Technology Inc.\",\n        \"Microsoft Corporation\",\n        \"Milacron Inc.\",\n        \"Millennium Chemicals Inc.\",\n        \"Mirant Corporation\",\n        \"Mohawk Industries Inc.\",\n        \"Molex Incorporated\",\n        \"The MONY Group Inc.\",\n        \"Morgan Stanley Dean Witter & Co.\",\n        \"Motorola Inc.\",\n        \"MPS Group Inc.\",\n        \"Murphy Oil Corporation\",\n        \"Nabors Industries Inc\",\n        \"Nacco Industries Inc\",\n        \"Nash Finch Company\",\n        \"National City Corp.\",\n        \"National Commerce Financial Corporation\",\n        \"National Fuel Gas Company\",\n        \"National Oilwell Inc\",\n        \"National Rural Utilities Cooperative Finance Corporation\",\n        \"National Semiconductor Corporation\",\n        \"National Service Industries Inc\",\n        \"Navistar International Corporation\",\n        \"NCR Corporation\",\n        \"The Neiman Marcus Group Inc.\",\n        \"New Jersey Resources Corporation\",\n        \"New York Times Company\",\n        \"Newell Rubbermaid Inc\",\n        \"Newmont Mining Corporation\",\n        \"Nextel Communications Inc\",\n        \"Nicor Inc\",\n        \"Nike Inc\",\n        \"NiSource Inc\",\n        \"Noble Energy Inc\",\n        \"Nordstrom Inc\",\n        \"Norfolk Southern Corporation\",\n        \"Nortek Inc\",\n        \"North Fork Bancorporation Inc\",\n        \"Northeast Utilities System\",\n        \"Northern Trust Corporation\",\n        \"Northrop Grumman Corporation\",\n        \"NorthWestern Corporation\",\n        \"Novellus Systems Inc\",\n        \"NSTAR\",\n        \"NTL Incorporated\",\n        \"Nucor Corp\",\n        \"Nvidia Corp\",\n        \"NVR Inc\",\n        \"Northwest Airlines Corp\",\n        \"Occidental Petroleum Corp\",\n        \"Ocean Energy Inc\",\n        \"Office Depot Inc.\",\n        \"OfficeMax Inc\",\n        \"OGE Energy Corp\",\n        \"Oglethorpe Power Corp.\",\n        \"Ohio Casualty Corp.\",\n        \"Old Republic International Corp.\",\n        \"Olin Corp.\",\n        \"OM Group Inc\",\n        \"Omnicare Inc\",\n        \"Omnicom Group\",\n        \"On Semiconductor Corp\",\n        \"ONEOK Inc\",\n        \"Oracle Corp\",\n        \"Oshkosh Truck Corp\",\n        \"Outback Steakhouse Inc.\",\n        \"Owens & Minor Inc.\",\n        \"Owens Corning\",\n        \"Owens-Illinois Inc\",\n        \"Oxford Health Plans Inc\",\n        \"Paccar Inc\",\n        \"PacifiCare Health Systems Inc\",\n        \"Packaging Corp. of America\",\n        \"Pactiv Corp\",\n        \"Pall Corp\",\n        \"Pantry Inc\",\n        \"Park Place Entertainment Corp\",\n        \"Parker Hannifin Corp.\",\n        \"Pathmark Stores Inc.\",\n        \"Paychex Inc\",\n        \"Payless Shoesource Inc\",\n        \"Penn Traffic Co.\",\n        \"Pennzoil-Quaker State Company\",\n        \"Pentair Inc\",\n        \"Peoples Energy Corp.\",\n        \"PeopleSoft Inc\",\n        \"Pep Boys Manny, Moe & Jack\",\n        \"Potomac Electric Power Co.\",\n        \"Pepsi Bottling Group Inc.\",\n        \"PepsiAmericas Inc.\",\n        \"PepsiCo Inc.\",\n        \"Performance Food Group Co.\",\n        \"Perini Corp\",\n        \"PerkinElmer Inc\",\n        \"Perot Systems Corp\",\n        \"Petco Animal Supplies Inc.\",\n        \"Peter Kiewit Sons', Inc.\",\n        \"PETsMART Inc\",\n        \"Pfizer Inc\",\n        \"Pacific Gas & Electric Corp.\",\n        \"Pharmacia Corp\",\n        \"Phar Mor Inc.\",\n        \"Phelps Dodge Corp.\",\n        \"Philip Morris Companies Inc.\",\n        \"Phillips Petroleum Co\",\n        \"Phillips Van Heusen Corp.\",\n        \"Phoenix Companies Inc\",\n        \"Pier 1 Imports Inc.\",\n        \"Pilgrim's Pride Corporation\",\n        \"Pinnacle West Capital Corp\",\n        \"Pioneer-Standard Electronics Inc.\",\n        \"Pitney Bowes Inc.\",\n        \"Pittston Brinks Group\",\n        \"Plains All American Pipeline LP\",\n        \"PNC Financial Services Group Inc.\",\n        \"PNM Resources Inc\",\n        \"Polaris Industries Inc.\",\n        \"Polo Ralph Lauren Corp\",\n        \"PolyOne Corp\",\n        \"Popular Inc\",\n        \"Potlatch Corp\",\n        \"PPG Industries Inc\",\n        \"PPL Corp\",\n        \"Praxair Inc\",\n        \"Precision Castparts Corp\",\n        \"Premcor Inc.\",\n        \"Pride International Inc\",\n        \"Primedia Inc\",\n        \"Principal Financial Group Inc.\",\n        \"Procter & Gamble Co.\",\n        \"Pro-Fac Cooperative Inc.\",\n        \"Progress Energy Inc\",\n        \"Progressive Corporation\",\n        \"Protective Life Corp\",\n        \"Provident Financial Group\",\n        \"Providian Financial Corp.\",\n        \"Prudential Financial Inc.\",\n        \"PSS World Medical Inc\",\n        \"Public Service Enterprise Group Inc.\",\n        \"Publix Super Markets Inc.\",\n        \"Puget Energy Inc.\",\n        \"Pulte Homes Inc\",\n        \"Qualcomm Inc\",\n        \"Quanta Services Inc.\",\n        \"Quantum Corp\",\n        \"Quest Diagnostics Inc.\",\n        \"Questar Corp\",\n        \"Quintiles Transnational\",\n        \"Qwest Communications Intl Inc\",\n        \"R.J. Reynolds Tobacco Company\",\n        \"R.R. Donnelley & Sons Company\",\n        \"Radio Shack Corporation\",\n        \"Raymond James Financial Inc.\",\n        \"Raytheon Company\",\n        \"Reader's Digest Association Inc.\",\n        \"Reebok International Ltd.\",\n        \"Regions Financial Corp.\",\n        \"Regis Corporation\",\n        \"Reliance Steel & Aluminum Co.\",\n        \"Reliant Energy Inc.\",\n        \"Rent A Center Inc\",\n        \"Republic Services Inc\",\n        \"Revlon Inc\",\n        \"RGS Energy Group Inc\",\n        \"Rite Aid Corp\",\n        \"Riverwood Holding Inc.\",\n        \"RoadwayCorp\",\n        \"Robert Half International Inc.\",\n        \"Rock-Tenn Co\",\n        \"Rockwell Automation Inc\",\n        \"Rockwell Collins Inc\",\n        \"Rohm & Haas Co.\",\n        \"Ross Stores Inc\",\n        \"RPM Inc.\",\n        \"Ruddick Corp\",\n        \"Ryder System Inc\",\n        \"Ryerson Tull Inc\",\n        \"Ryland Group Inc.\",\n        \"Sabre Holdings Corp\",\n        \"Safeco Corp\",\n        \"Safeguard Scientifics Inc.\",\n        \"Safeway Inc\",\n        \"Saks Inc\",\n        \"Sanmina-SCI Inc\",\n        \"Sara Lee Corp\",\n        \"SBC Communications Inc\",\n        \"Scana Corp.\",\n        \"Schering-Plough Corp\",\n        \"Scholastic Corp\",\n        \"SCI Systems Onc.\",\n        \"Science Applications Intl. Inc.\",\n        \"Scientific-Atlanta Inc\",\n        \"Scotts Company\",\n        \"Seaboard Corp\",\n        \"Sealed Air Corp\",\n        \"Sears Roebuck & Co\",\n        \"Sempra Energy\",\n        \"Sequa Corp\",\n        \"Service Corp. International\",\n        \"ServiceMaster Co\",\n        \"Shaw Group Inc\",\n        \"Sherwin-Williams Company\",\n        \"Shopko Stores Inc\",\n        \"Siebel Systems Inc\",\n        \"Sierra Health Services Inc\",\n        \"Sierra Pacific Resources\",\n        \"Silgan Holdings Inc.\",\n        \"Silicon Graphics Inc\",\n        \"Simon Property Group Inc\",\n        \"SLM Corporation\",\n        \"Smith International Inc\",\n        \"Smithfield Foods Inc\",\n        \"Smurfit-Stone Container Corp\",\n        \"Snap-On Inc\",\n        \"Solectron Corp\",\n        \"Solutia Inc\",\n        \"Sonic Automotive Inc.\",\n        \"Sonoco Products Co.\",\n        \"Southern Company\",\n        \"Southern Union Company\",\n        \"SouthTrust Corp.\",\n        \"Southwest Airlines Co\",\n        \"Southwest Gas Corp\",\n        \"Sovereign Bancorp Inc.\",\n        \"Spartan Stores Inc\",\n        \"Spherion Corp\",\n        \"Sports Authority Inc\",\n        \"Sprint Corp.\",\n        \"SPX Corp\",\n        \"St. Jude Medical Inc\",\n        \"St. Paul Cos.\",\n        \"Staff Leasing Inc.\",\n        \"StanCorp Financial Group Inc\",\n        \"Standard Pacific Corp.\",\n        \"Stanley Works\",\n        \"Staples Inc\",\n        \"Starbucks Corp\",\n        \"Starwood Hotels & Resorts Worldwide Inc\",\n        \"State Street Corp.\",\n        \"Stater Bros. Holdings Inc.\",\n        \"Steelcase Inc\",\n        \"Stein Mart Inc\",\n        \"Stewart & Stevenson Services Inc\",\n        \"Stewart Information Services Corp\",\n        \"Stilwell Financial Inc\",\n        \"Storage Technology Corporation\",\n        \"Stryker Corp\",\n        \"Sun Healthcare Group Inc.\",\n        \"Sun Microsystems Inc.\",\n        \"SunGard Data Systems Inc.\",\n        \"Sunoco Inc.\",\n        \"SunTrust Banks Inc\",\n        \"Supervalu Inc\",\n        \"Swift Transportation, Co., Inc\",\n        \"Symbol Technologies Inc\",\n        \"Synovus Financial Corp.\",\n        \"Sysco Corp\",\n        \"Systemax Inc.\",\n        \"Target Corp.\",\n        \"Tech Data Corporation\",\n        \"TECO Energy Inc\",\n        \"Tecumseh Products Company\",\n        \"Tektronix Inc\",\n        \"Teleflex Incorporated\",\n        \"Telephone & Data Systems Inc\",\n        \"Tellabs Inc.\",\n        \"Temple-Inland Inc\",\n        \"Tenet Healthcare Corporation\",\n        \"Tenneco Automotive Inc.\",\n        \"Teradyne Inc\",\n        \"Terex Corp\",\n        \"Tesoro Petroleum Corp.\",\n        \"Texas Industries Inc.\",\n        \"Texas Instruments Incorporated\",\n        \"Textron Inc\",\n        \"Thermo Electron Corporation\",\n        \"Thomas & Betts Corporation\",\n        \"Tiffany & Co\",\n        \"Timken Company\",\n        \"TJX Companies Inc\",\n        \"TMP Worldwide Inc\",\n        \"Toll Brothers Inc\",\n        \"Torchmark Corporation\",\n        \"Toro Company\",\n        \"Tower Automotive Inc.\",\n        \"Toys 'R' Us Inc\",\n        \"Trans World Entertainment Corp.\",\n        \"TransMontaigne Inc\",\n        \"Transocean Inc\",\n        \"TravelCenters of America Inc.\",\n        \"Triad Hospitals Inc\",\n        \"Tribune Company\",\n        \"Trigon Healthcare Inc.\",\n        \"Trinity Industries Inc\",\n        \"Trump Hotels & Casino Resorts Inc.\",\n        \"TruServ Corporation\",\n        \"TRW Inc\",\n        \"TXU Corp\",\n        \"Tyson Foods Inc\",\n        \"U.S. Bancorp\",\n        \"U.S. Industries Inc.\",\n        \"UAL Corporation\",\n        \"UGI Corporation\",\n        \"Unified Western Grocers Inc\",\n        \"Union Pacific Corporation\",\n        \"Union Planters Corp\",\n        \"Unisource Energy Corp\",\n        \"Unisys Corporation\",\n        \"United Auto Group Inc\",\n        \"United Defense Industries Inc.\",\n        \"United Parcel Service Inc\",\n        \"United Rentals Inc\",\n        \"United Stationers Inc\",\n        \"United Technologies Corporation\",\n        \"UnitedHealth Group Incorporated\",\n        \"Unitrin Inc\",\n        \"Universal Corporation\",\n        \"Universal Forest Products Inc\",\n        \"Universal Health Services Inc\",\n        \"Unocal Corporation\",\n        \"Unova Inc\",\n        \"UnumProvident Corporation\",\n        \"URS Corporation\",\n        \"US Airways Group Inc\",\n        \"US Oncology Inc\",\n        \"USA Interactive\",\n        \"USFreighways Corporation\",\n        \"USG Corporation\",\n        \"UST Inc\",\n        \"Valero Energy Corporation\",\n        \"Valspar Corporation\",\n        \"Value City Department Stores Inc\",\n        \"Varco International Inc\",\n        \"Vectren Corporation\",\n        \"Veritas Software Corporation\",\n        \"Verizon Communications Inc\",\n        \"VF Corporation\",\n        \"Viacom Inc\",\n        \"Viad Corp\",\n        \"Viasystems Group Inc\",\n        \"Vishay Intertechnology Inc\",\n        \"Visteon Corporation\",\n        \"Volt Information Sciences Inc\",\n        \"Vulcan Materials Company\",\n        \"W.R. Berkley Corporation\",\n        \"W.R. Grace & Co\",\n        \"W.W. Grainger Inc\",\n        \"Wachovia Corporation\",\n        \"Wakenhut Corporation\",\n        \"Walgreen Co\",\n        \"Wallace Computer Services Inc\",\n        \"Wal-Mart Stores Inc\",\n        \"Walt Disney Co\",\n        \"Walter Industries Inc\",\n        \"Washington Mutual Inc\",\n        \"Washington Post Co.\",\n        \"Waste Management Inc\",\n        \"Watsco Inc\",\n        \"Weatherford International Inc\",\n        \"Weis Markets Inc.\",\n        \"Wellpoint Health Networks Inc\",\n        \"Wells Fargo & Company\",\n        \"Wendy's International Inc\",\n        \"Werner Enterprises Inc\",\n        \"WESCO International Inc\",\n        \"Western Digital Inc\",\n        \"Western Gas Resources Inc\",\n        \"WestPoint Stevens Inc\",\n        \"Weyerhauser Company\",\n        \"WGL Holdings Inc\",\n        \"Whirlpool Corporation\",\n        \"Whole Foods Market Inc\",\n        \"Willamette Industries Inc.\",\n        \"Williams Companies Inc\",\n        \"Williams Sonoma Inc\",\n        \"Winn Dixie Stores Inc\",\n        \"Wisconsin Energy Corporation\",\n        \"Wm Wrigley Jr Company\",\n        \"World Fuel Services Corporation\",\n        \"WorldCom Inc\",\n        \"Worthington Industries Inc\",\n        \"WPS Resources Corporation\",\n        \"Wyeth\",\n        \"Wyndham International Inc\",\n        \"Xcel Energy Inc\",\n        \"Xerox Corp\",\n        \"Xilinx Inc\",\n        \"XO Communications Inc\",\n        \"Yellow Corporation\",\n        \"York International Corp\",\n        \"Yum Brands Inc.\",\n        \"Zale Corporation\",\n        \"Zions Bancorporation\"\n      ],\n\n        fileExtension : {\n            \"raster\"    : [\"bmp\", \"gif\", \"gpl\", \"ico\", \"jpeg\", \"psd\", \"png\", \"psp\", \"raw\", \"tiff\"],\n            \"vector\"    : [\"3dv\", \"amf\", \"awg\", \"ai\", \"cgm\", \"cdr\", \"cmx\", \"dxf\", \"e2d\", \"egt\", \"eps\", \"fs\", \"odg\", \"svg\", \"xar\"],\n            \"3d\"        : [\"3dmf\", \"3dm\", \"3mf\", \"3ds\", \"an8\", \"aoi\", \"blend\", \"cal3d\", \"cob\", \"ctm\", \"iob\", \"jas\", \"max\", \"mb\", \"mdx\", \"obj\", \"x\", \"x3d\"],\n            \"document\"  : [\"doc\", \"docx\", \"dot\", \"html\", \"xml\", \"odt\", \"odm\", \"ott\", \"csv\", \"rtf\", \"tex\", \"xhtml\", \"xps\"]\n        },\n\n        // Data taken from https://github.com/dmfilipenko/timezones.json/blob/master/timezones.json\n        timezones: [\n                  {\n                    \"name\": \"Dateline Standard Time\",\n                    \"abbr\": \"DST\",\n                    \"offset\": -12,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-12:00) International Date Line West\",\n                    \"utc\": [\n                      \"Etc/GMT+12\"\n                    ]\n                  },\n                  {\n                    \"name\": \"UTC-11\",\n                    \"abbr\": \"U\",\n                    \"offset\": -11,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-11:00) Coordinated Universal Time-11\",\n                    \"utc\": [\n                      \"Etc/GMT+11\",\n                      \"Pacific/Midway\",\n                      \"Pacific/Niue\",\n                      \"Pacific/Pago_Pago\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Hawaiian Standard Time\",\n                    \"abbr\": \"HST\",\n                    \"offset\": -10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-10:00) Hawaii\",\n                    \"utc\": [\n                      \"Etc/GMT+10\",\n                      \"Pacific/Honolulu\",\n                      \"Pacific/Johnston\",\n                      \"Pacific/Rarotonga\",\n                      \"Pacific/Tahiti\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Alaskan Standard Time\",\n                    \"abbr\": \"AKDT\",\n                    \"offset\": -8,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-09:00) Alaska\",\n                    \"utc\": [\n                      \"America/Anchorage\",\n                      \"America/Juneau\",\n                      \"America/Nome\",\n                      \"America/Sitka\",\n                      \"America/Yakutat\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Pacific Standard Time (Mexico)\",\n                    \"abbr\": \"PDT\",\n                    \"offset\": -7,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-08:00) Baja California\",\n                    \"utc\": [\n                      \"America/Santa_Isabel\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Pacific Standard Time\",\n                    \"abbr\": \"PDT\",\n                    \"offset\": -7,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-08:00) Pacific Time (US & Canada)\",\n                    \"utc\": [\n                      \"America/Dawson\",\n                      \"America/Los_Angeles\",\n                      \"America/Tijuana\",\n                      \"America/Vancouver\",\n                      \"America/Whitehorse\",\n                      \"PST8PDT\"\n                    ]\n                  },\n                  {\n                    \"name\": \"US Mountain Standard Time\",\n                    \"abbr\": \"UMST\",\n                    \"offset\": -7,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-07:00) Arizona\",\n                    \"utc\": [\n                      \"America/Creston\",\n                      \"America/Dawson_Creek\",\n                      \"America/Hermosillo\",\n                      \"America/Phoenix\",\n                      \"Etc/GMT+7\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Mountain Standard Time (Mexico)\",\n                    \"abbr\": \"MDT\",\n                    \"offset\": -6,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-07:00) Chihuahua, La Paz, Mazatlan\",\n                    \"utc\": [\n                      \"America/Chihuahua\",\n                      \"America/Mazatlan\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Mountain Standard Time\",\n                    \"abbr\": \"MDT\",\n                    \"offset\": -6,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-07:00) Mountain Time (US & Canada)\",\n                    \"utc\": [\n                      \"America/Boise\",\n                      \"America/Cambridge_Bay\",\n                      \"America/Denver\",\n                      \"America/Edmonton\",\n                      \"America/Inuvik\",\n                      \"America/Ojinaga\",\n                      \"America/Yellowknife\",\n                      \"MST7MDT\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central America Standard Time\",\n                    \"abbr\": \"CAST\",\n                    \"offset\": -6,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-06:00) Central America\",\n                    \"utc\": [\n                      \"America/Belize\",\n                      \"America/Costa_Rica\",\n                      \"America/El_Salvador\",\n                      \"America/Guatemala\",\n                      \"America/Managua\",\n                      \"America/Tegucigalpa\",\n                      \"Etc/GMT+6\",\n                      \"Pacific/Galapagos\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Standard Time\",\n                    \"abbr\": \"CDT\",\n                    \"offset\": -5,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-06:00) Central Time (US & Canada)\",\n                    \"utc\": [\n                      \"America/Chicago\",\n                      \"America/Indiana/Knox\",\n                      \"America/Indiana/Tell_City\",\n                      \"America/Matamoros\",\n                      \"America/Menominee\",\n                      \"America/North_Dakota/Beulah\",\n                      \"America/North_Dakota/Center\",\n                      \"America/North_Dakota/New_Salem\",\n                      \"America/Rainy_River\",\n                      \"America/Rankin_Inlet\",\n                      \"America/Resolute\",\n                      \"America/Winnipeg\",\n                      \"CST6CDT\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Standard Time (Mexico)\",\n                    \"abbr\": \"CDT\",\n                    \"offset\": -5,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-06:00) Guadalajara, Mexico City, Monterrey\",\n                    \"utc\": [\n                      \"America/Bahia_Banderas\",\n                      \"America/Cancun\",\n                      \"America/Merida\",\n                      \"America/Mexico_City\",\n                      \"America/Monterrey\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Canada Central Standard Time\",\n                    \"abbr\": \"CCST\",\n                    \"offset\": -6,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-06:00) Saskatchewan\",\n                    \"utc\": [\n                      \"America/Regina\",\n                      \"America/Swift_Current\"\n                    ]\n                  },\n                  {\n                    \"name\": \"SA Pacific Standard Time\",\n                    \"abbr\": \"SPST\",\n                    \"offset\": -5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-05:00) Bogota, Lima, Quito\",\n                    \"utc\": [\n                      \"America/Bogota\",\n                      \"America/Cayman\",\n                      \"America/Coral_Harbour\",\n                      \"America/Eirunepe\",\n                      \"America/Guayaquil\",\n                      \"America/Jamaica\",\n                      \"America/Lima\",\n                      \"America/Panama\",\n                      \"America/Rio_Branco\",\n                      \"Etc/GMT+5\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Eastern Standard Time\",\n                    \"abbr\": \"EDT\",\n                    \"offset\": -4,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-05:00) Eastern Time (US & Canada)\",\n                    \"utc\": [\n                      \"America/Detroit\",\n                      \"America/Havana\",\n                      \"America/Indiana/Petersburg\",\n                      \"America/Indiana/Vincennes\",\n                      \"America/Indiana/Winamac\",\n                      \"America/Iqaluit\",\n                      \"America/Kentucky/Monticello\",\n                      \"America/Louisville\",\n                      \"America/Montreal\",\n                      \"America/Nassau\",\n                      \"America/New_York\",\n                      \"America/Nipigon\",\n                      \"America/Pangnirtung\",\n                      \"America/Port-au-Prince\",\n                      \"America/Thunder_Bay\",\n                      \"America/Toronto\",\n                      \"EST5EDT\"\n                    ]\n                  },\n                  {\n                    \"name\": \"US Eastern Standard Time\",\n                    \"abbr\": \"UEDT\",\n                    \"offset\": -4,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-05:00) Indiana (East)\",\n                    \"utc\": [\n                      \"America/Indiana/Marengo\",\n                      \"America/Indiana/Vevay\",\n                      \"America/Indianapolis\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Venezuela Standard Time\",\n                    \"abbr\": \"VST\",\n                    \"offset\": -4.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-04:30) Caracas\",\n                    \"utc\": [\n                      \"America/Caracas\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Paraguay Standard Time\",\n                    \"abbr\": \"PST\",\n                    \"offset\": -4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-04:00) Asuncion\",\n                    \"utc\": [\n                      \"America/Asuncion\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Atlantic Standard Time\",\n                    \"abbr\": \"ADT\",\n                    \"offset\": -3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-04:00) Atlantic Time (Canada)\",\n                    \"utc\": [\n                      \"America/Glace_Bay\",\n                      \"America/Goose_Bay\",\n                      \"America/Halifax\",\n                      \"America/Moncton\",\n                      \"America/Thule\",\n                      \"Atlantic/Bermuda\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Brazilian Standard Time\",\n                    \"abbr\": \"CBST\",\n                    \"offset\": -4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-04:00) Cuiaba\",\n                    \"utc\": [\n                      \"America/Campo_Grande\",\n                      \"America/Cuiaba\"\n                    ]\n                  },\n                  {\n                    \"name\": \"SA Western Standard Time\",\n                    \"abbr\": \"SWST\",\n                    \"offset\": -4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-04:00) Georgetown, La Paz, Manaus, San Juan\",\n                    \"utc\": [\n                      \"America/Anguilla\",\n                      \"America/Antigua\",\n                      \"America/Aruba\",\n                      \"America/Barbados\",\n                      \"America/Blanc-Sablon\",\n                      \"America/Boa_Vista\",\n                      \"America/Curacao\",\n                      \"America/Dominica\",\n                      \"America/Grand_Turk\",\n                      \"America/Grenada\",\n                      \"America/Guadeloupe\",\n                      \"America/Guyana\",\n                      \"America/Kralendijk\",\n                      \"America/La_Paz\",\n                      \"America/Lower_Princes\",\n                      \"America/Manaus\",\n                      \"America/Marigot\",\n                      \"America/Martinique\",\n                      \"America/Montserrat\",\n                      \"America/Port_of_Spain\",\n                      \"America/Porto_Velho\",\n                      \"America/Puerto_Rico\",\n                      \"America/Santo_Domingo\",\n                      \"America/St_Barthelemy\",\n                      \"America/St_Kitts\",\n                      \"America/St_Lucia\",\n                      \"America/St_Thomas\",\n                      \"America/St_Vincent\",\n                      \"America/Tortola\",\n                      \"Etc/GMT+4\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Pacific SA Standard Time\",\n                    \"abbr\": \"PSST\",\n                    \"offset\": -4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-04:00) Santiago\",\n                    \"utc\": [\n                      \"America/Santiago\",\n                      \"Antarctica/Palmer\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Newfoundland Standard Time\",\n                    \"abbr\": \"NDT\",\n                    \"offset\": -2.5,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-03:30) Newfoundland\",\n                    \"utc\": [\n                      \"America/St_Johns\"\n                    ]\n                  },\n                  {\n                    \"name\": \"E. South America Standard Time\",\n                    \"abbr\": \"ESAST\",\n                    \"offset\": -3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-03:00) Brasilia\",\n                    \"utc\": [\n                      \"America/Sao_Paulo\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Argentina Standard Time\",\n                    \"abbr\": \"AST\",\n                    \"offset\": -3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-03:00) Buenos Aires\",\n                    \"utc\": [\n                      \"America/Argentina/La_Rioja\",\n                      \"America/Argentina/Rio_Gallegos\",\n                      \"America/Argentina/Salta\",\n                      \"America/Argentina/San_Juan\",\n                      \"America/Argentina/San_Luis\",\n                      \"America/Argentina/Tucuman\",\n                      \"America/Argentina/Ushuaia\",\n                      \"America/Buenos_Aires\",\n                      \"America/Catamarca\",\n                      \"America/Cordoba\",\n                      \"America/Jujuy\",\n                      \"America/Mendoza\"\n                    ]\n                  },\n                  {\n                    \"name\": \"SA Eastern Standard Time\",\n                    \"abbr\": \"SEST\",\n                    \"offset\": -3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-03:00) Cayenne, Fortaleza\",\n                    \"utc\": [\n                      \"America/Araguaina\",\n                      \"America/Belem\",\n                      \"America/Cayenne\",\n                      \"America/Fortaleza\",\n                      \"America/Maceio\",\n                      \"America/Paramaribo\",\n                      \"America/Recife\",\n                      \"America/Santarem\",\n                      \"Antarctica/Rothera\",\n                      \"Atlantic/Stanley\",\n                      \"Etc/GMT+3\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Greenland Standard Time\",\n                    \"abbr\": \"GDT\",\n                    \"offset\": -2,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-03:00) Greenland\",\n                    \"utc\": [\n                      \"America/Godthab\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Montevideo Standard Time\",\n                    \"abbr\": \"MST\",\n                    \"offset\": -3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-03:00) Montevideo\",\n                    \"utc\": [\n                      \"America/Montevideo\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Bahia Standard Time\",\n                    \"abbr\": \"BST\",\n                    \"offset\": -3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-03:00) Salvador\",\n                    \"utc\": [\n                      \"America/Bahia\"\n                    ]\n                  },\n                  {\n                    \"name\": \"UTC-02\",\n                    \"abbr\": \"U\",\n                    \"offset\": -2,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-02:00) Coordinated Universal Time-02\",\n                    \"utc\": [\n                      \"America/Noronha\",\n                      \"Atlantic/South_Georgia\",\n                      \"Etc/GMT+2\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Mid-Atlantic Standard Time\",\n                    \"abbr\": \"MDT\",\n                    \"offset\": -1,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-02:00) Mid-Atlantic - Old\"\n                  },\n                  {\n                    \"name\": \"Azores Standard Time\",\n                    \"abbr\": \"ADT\",\n                    \"offset\": 0,\n                    \"isdst\": true,\n                    \"text\": \"(UTC-01:00) Azores\",\n                    \"utc\": [\n                      \"America/Scoresbysund\",\n                      \"Atlantic/Azores\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Cape Verde Standard Time\",\n                    \"abbr\": \"CVST\",\n                    \"offset\": -1,\n                    \"isdst\": false,\n                    \"text\": \"(UTC-01:00) Cape Verde Is.\",\n                    \"utc\": [\n                      \"Atlantic/Cape_Verde\",\n                      \"Etc/GMT+1\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Morocco Standard Time\",\n                    \"abbr\": \"MDT\",\n                    \"offset\": 1,\n                    \"isdst\": true,\n                    \"text\": \"(UTC) Casablanca\",\n                    \"utc\": [\n                      \"Africa/Casablanca\",\n                      \"Africa/El_Aaiun\"\n                    ]\n                  },\n                  {\n                    \"name\": \"UTC\",\n                    \"abbr\": \"CUT\",\n                    \"offset\": 0,\n                    \"isdst\": false,\n                    \"text\": \"(UTC) Coordinated Universal Time\",\n                    \"utc\": [\n                      \"America/Danmarkshavn\",\n                      \"Etc/GMT\"\n                    ]\n                  },\n                  {\n                    \"name\": \"GMT Standard Time\",\n                    \"abbr\": \"GDT\",\n                    \"offset\": 1,\n                    \"isdst\": true,\n                    \"text\": \"(UTC) Dublin, Edinburgh, Lisbon, London\",\n                    \"utc\": [\n                      \"Atlantic/Canary\",\n                      \"Atlantic/Faeroe\",\n                      \"Atlantic/Madeira\",\n                      \"Europe/Dublin\",\n                      \"Europe/Guernsey\",\n                      \"Europe/Isle_of_Man\",\n                      \"Europe/Jersey\",\n                      \"Europe/Lisbon\",\n                      \"Europe/London\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Greenwich Standard Time\",\n                    \"abbr\": \"GST\",\n                    \"offset\": 0,\n                    \"isdst\": false,\n                    \"text\": \"(UTC) Monrovia, Reykjavik\",\n                    \"utc\": [\n                      \"Africa/Abidjan\",\n                      \"Africa/Accra\",\n                      \"Africa/Bamako\",\n                      \"Africa/Banjul\",\n                      \"Africa/Bissau\",\n                      \"Africa/Conakry\",\n                      \"Africa/Dakar\",\n                      \"Africa/Freetown\",\n                      \"Africa/Lome\",\n                      \"Africa/Monrovia\",\n                      \"Africa/Nouakchott\",\n                      \"Africa/Ouagadougou\",\n                      \"Africa/Sao_Tome\",\n                      \"Atlantic/Reykjavik\",\n                      \"Atlantic/St_Helena\"\n                    ]\n                  },\n                  {\n                    \"name\": \"W. Europe Standard Time\",\n                    \"abbr\": \"WEDT\",\n                    \"offset\": 2,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+01:00) Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna\",\n                    \"utc\": [\n                      \"Arctic/Longyearbyen\",\n                      \"Europe/Amsterdam\",\n                      \"Europe/Andorra\",\n                      \"Europe/Berlin\",\n                      \"Europe/Busingen\",\n                      \"Europe/Gibraltar\",\n                      \"Europe/Luxembourg\",\n                      \"Europe/Malta\",\n                      \"Europe/Monaco\",\n                      \"Europe/Oslo\",\n                      \"Europe/Rome\",\n                      \"Europe/San_Marino\",\n                      \"Europe/Stockholm\",\n                      \"Europe/Vaduz\",\n                      \"Europe/Vatican\",\n                      \"Europe/Vienna\",\n                      \"Europe/Zurich\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Europe Standard Time\",\n                    \"abbr\": \"CEDT\",\n                    \"offset\": 2,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+01:00) Belgrade, Bratislava, Budapest, Ljubljana, Prague\",\n                    \"utc\": [\n                      \"Europe/Belgrade\",\n                      \"Europe/Bratislava\",\n                      \"Europe/Budapest\",\n                      \"Europe/Ljubljana\",\n                      \"Europe/Podgorica\",\n                      \"Europe/Prague\",\n                      \"Europe/Tirane\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Romance Standard Time\",\n                    \"abbr\": \"RDT\",\n                    \"offset\": 2,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+01:00) Brussels, Copenhagen, Madrid, Paris\",\n                    \"utc\": [\n                      \"Africa/Ceuta\",\n                      \"Europe/Brussels\",\n                      \"Europe/Copenhagen\",\n                      \"Europe/Madrid\",\n                      \"Europe/Paris\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central European Standard Time\",\n                    \"abbr\": \"CEDT\",\n                    \"offset\": 2,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+01:00) Sarajevo, Skopje, Warsaw, Zagreb\",\n                    \"utc\": [\n                      \"Europe/Sarajevo\",\n                      \"Europe/Skopje\",\n                      \"Europe/Warsaw\",\n                      \"Europe/Zagreb\"\n                    ]\n                  },\n                  {\n                    \"name\": \"W. Central Africa Standard Time\",\n                    \"abbr\": \"WCAST\",\n                    \"offset\": 1,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+01:00) West Central Africa\",\n                    \"utc\": [\n                      \"Africa/Algiers\",\n                      \"Africa/Bangui\",\n                      \"Africa/Brazzaville\",\n                      \"Africa/Douala\",\n                      \"Africa/Kinshasa\",\n                      \"Africa/Lagos\",\n                      \"Africa/Libreville\",\n                      \"Africa/Luanda\",\n                      \"Africa/Malabo\",\n                      \"Africa/Ndjamena\",\n                      \"Africa/Niamey\",\n                      \"Africa/Porto-Novo\",\n                      \"Africa/Tunis\",\n                      \"Etc/GMT-1\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Namibia Standard Time\",\n                    \"abbr\": \"NST\",\n                    \"offset\": 1,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+01:00) Windhoek\",\n                    \"utc\": [\n                      \"Africa/Windhoek\"\n                    ]\n                  },\n                  {\n                    \"name\": \"GTB Standard Time\",\n                    \"abbr\": \"GDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Athens, Bucharest\",\n                    \"utc\": [\n                      \"Asia/Nicosia\",\n                      \"Europe/Athens\",\n                      \"Europe/Bucharest\",\n                      \"Europe/Chisinau\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Middle East Standard Time\",\n                    \"abbr\": \"MEDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Beirut\",\n                    \"utc\": [\n                      \"Asia/Beirut\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Egypt Standard Time\",\n                    \"abbr\": \"EST\",\n                    \"offset\": 2,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+02:00) Cairo\",\n                    \"utc\": [\n                      \"Africa/Cairo\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Syria Standard Time\",\n                    \"abbr\": \"SDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Damascus\",\n                    \"utc\": [\n                      \"Asia/Damascus\"\n                    ]\n                  },\n                  {\n                    \"name\": \"E. Europe Standard Time\",\n                    \"abbr\": \"EEDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) E. Europe\"\n                  },\n                  {\n                    \"name\": \"South Africa Standard Time\",\n                    \"abbr\": \"SAST\",\n                    \"offset\": 2,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+02:00) Harare, Pretoria\",\n                    \"utc\": [\n                      \"Africa/Blantyre\",\n                      \"Africa/Bujumbura\",\n                      \"Africa/Gaborone\",\n                      \"Africa/Harare\",\n                      \"Africa/Johannesburg\",\n                      \"Africa/Kigali\",\n                      \"Africa/Lubumbashi\",\n                      \"Africa/Lusaka\",\n                      \"Africa/Maputo\",\n                      \"Africa/Maseru\",\n                      \"Africa/Mbabane\",\n                      \"Etc/GMT-2\"\n                    ]\n                  },\n                  {\n                    \"name\": \"FLE Standard Time\",\n                    \"abbr\": \"FDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Helsinki, Kyiv, Riga, Sofia, Tallinn, Vilnius\",\n                    \"utc\": [\n                      \"Europe/Helsinki\",\n                      \"Europe/Kiev\",\n                      \"Europe/Mariehamn\",\n                      \"Europe/Riga\",\n                      \"Europe/Sofia\",\n                      \"Europe/Tallinn\",\n                      \"Europe/Uzhgorod\",\n                      \"Europe/Vilnius\",\n                      \"Europe/Zaporozhye\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Turkey Standard Time\",\n                    \"abbr\": \"TDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Istanbul\",\n                    \"utc\": [\n                      \"Europe/Istanbul\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Israel Standard Time\",\n                    \"abbr\": \"JDT\",\n                    \"offset\": 3,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+02:00) Jerusalem\",\n                    \"utc\": [\n                      \"Asia/Jerusalem\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Libya Standard Time\",\n                    \"abbr\": \"LST\",\n                    \"offset\": 2,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+02:00) Tripoli\",\n                    \"utc\": [\n                      \"Africa/Tripoli\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Jordan Standard Time\",\n                    \"abbr\": \"JST\",\n                    \"offset\": 3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+03:00) Amman\",\n                    \"utc\": [\n                      \"Asia/Amman\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Arabic Standard Time\",\n                    \"abbr\": \"AST\",\n                    \"offset\": 3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+03:00) Baghdad\",\n                    \"utc\": [\n                      \"Asia/Baghdad\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Kaliningrad Standard Time\",\n                    \"abbr\": \"KST\",\n                    \"offset\": 3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+03:00) Kaliningrad, Minsk\",\n                    \"utc\": [\n                      \"Europe/Kaliningrad\",\n                      \"Europe/Minsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Arab Standard Time\",\n                    \"abbr\": \"AST\",\n                    \"offset\": 3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+03:00) Kuwait, Riyadh\",\n                    \"utc\": [\n                      \"Asia/Aden\",\n                      \"Asia/Bahrain\",\n                      \"Asia/Kuwait\",\n                      \"Asia/Qatar\",\n                      \"Asia/Riyadh\"\n                    ]\n                  },\n                  {\n                    \"name\": \"E. Africa Standard Time\",\n                    \"abbr\": \"EAST\",\n                    \"offset\": 3,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+03:00) Nairobi\",\n                    \"utc\": [\n                      \"Africa/Addis_Ababa\",\n                      \"Africa/Asmera\",\n                      \"Africa/Dar_es_Salaam\",\n                      \"Africa/Djibouti\",\n                      \"Africa/Juba\",\n                      \"Africa/Kampala\",\n                      \"Africa/Khartoum\",\n                      \"Africa/Mogadishu\",\n                      \"Africa/Nairobi\",\n                      \"Antarctica/Syowa\",\n                      \"Etc/GMT-3\",\n                      \"Indian/Antananarivo\",\n                      \"Indian/Comoro\",\n                      \"Indian/Mayotte\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Iran Standard Time\",\n                    \"abbr\": \"IDT\",\n                    \"offset\": 4.5,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+03:30) Tehran\",\n                    \"utc\": [\n                      \"Asia/Tehran\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Arabian Standard Time\",\n                    \"abbr\": \"AST\",\n                    \"offset\": 4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:00) Abu Dhabi, Muscat\",\n                    \"utc\": [\n                      \"Asia/Dubai\",\n                      \"Asia/Muscat\",\n                      \"Etc/GMT-4\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Azerbaijan Standard Time\",\n                    \"abbr\": \"ADT\",\n                    \"offset\": 5,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+04:00) Baku\",\n                    \"utc\": [\n                      \"Asia/Baku\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Russian Standard Time\",\n                    \"abbr\": \"RST\",\n                    \"offset\": 4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:00) Moscow, St. Petersburg, Volgograd\",\n                    \"utc\": [\n                      \"Europe/Moscow\",\n                      \"Europe/Samara\",\n                      \"Europe/Simferopol\",\n                      \"Europe/Volgograd\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Mauritius Standard Time\",\n                    \"abbr\": \"MST\",\n                    \"offset\": 4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:00) Port Louis\",\n                    \"utc\": [\n                      \"Indian/Mahe\",\n                      \"Indian/Mauritius\",\n                      \"Indian/Reunion\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Georgian Standard Time\",\n                    \"abbr\": \"GST\",\n                    \"offset\": 4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:00) Tbilisi\",\n                    \"utc\": [\n                      \"Asia/Tbilisi\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Caucasus Standard Time\",\n                    \"abbr\": \"CST\",\n                    \"offset\": 4,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:00) Yerevan\",\n                    \"utc\": [\n                      \"Asia/Yerevan\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Afghanistan Standard Time\",\n                    \"abbr\": \"AST\",\n                    \"offset\": 4.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+04:30) Kabul\",\n                    \"utc\": [\n                      \"Asia/Kabul\"\n                    ]\n                  },\n                  {\n                    \"name\": \"West Asia Standard Time\",\n                    \"abbr\": \"WAST\",\n                    \"offset\": 5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+05:00) Ashgabat, Tashkent\",\n                    \"utc\": [\n                      \"Antarctica/Mawson\",\n                      \"Asia/Aqtau\",\n                      \"Asia/Aqtobe\",\n                      \"Asia/Ashgabat\",\n                      \"Asia/Dushanbe\",\n                      \"Asia/Oral\",\n                      \"Asia/Samarkand\",\n                      \"Asia/Tashkent\",\n                      \"Etc/GMT-5\",\n                      \"Indian/Kerguelen\",\n                      \"Indian/Maldives\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Pakistan Standard Time\",\n                    \"abbr\": \"PST\",\n                    \"offset\": 5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+05:00) Islamabad, Karachi\",\n                    \"utc\": [\n                      \"Asia/Karachi\"\n                    ]\n                  },\n                  {\n                    \"name\": \"India Standard Time\",\n                    \"abbr\": \"IST\",\n                    \"offset\": 5.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+05:30) Chennai, Kolkata, Mumbai, New Delhi\",\n                    \"utc\": [\n                      \"Asia/Calcutta\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Sri Lanka Standard Time\",\n                    \"abbr\": \"SLST\",\n                    \"offset\": 5.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+05:30) Sri Jayawardenepura\",\n                    \"utc\": [\n                      \"Asia/Colombo\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Nepal Standard Time\",\n                    \"abbr\": \"NST\",\n                    \"offset\": 5.75,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+05:45) Kathmandu\",\n                    \"utc\": [\n                      \"Asia/Katmandu\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Asia Standard Time\",\n                    \"abbr\": \"CAST\",\n                    \"offset\": 6,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+06:00) Astana\",\n                    \"utc\": [\n                      \"Antarctica/Vostok\",\n                      \"Asia/Almaty\",\n                      \"Asia/Bishkek\",\n                      \"Asia/Qyzylorda\",\n                      \"Asia/Urumqi\",\n                      \"Etc/GMT-6\",\n                      \"Indian/Chagos\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Bangladesh Standard Time\",\n                    \"abbr\": \"BST\",\n                    \"offset\": 6,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+06:00) Dhaka\",\n                    \"utc\": [\n                      \"Asia/Dhaka\",\n                      \"Asia/Thimphu\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Ekaterinburg Standard Time\",\n                    \"abbr\": \"EST\",\n                    \"offset\": 6,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+06:00) Ekaterinburg\",\n                    \"utc\": [\n                      \"Asia/Yekaterinburg\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Myanmar Standard Time\",\n                    \"abbr\": \"MST\",\n                    \"offset\": 6.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+06:30) Yangon (Rangoon)\",\n                    \"utc\": [\n                      \"Asia/Rangoon\",\n                      \"Indian/Cocos\"\n                    ]\n                  },\n                  {\n                    \"name\": \"SE Asia Standard Time\",\n                    \"abbr\": \"SAST\",\n                    \"offset\": 7,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+07:00) Bangkok, Hanoi, Jakarta\",\n                    \"utc\": [\n                      \"Antarctica/Davis\",\n                      \"Asia/Bangkok\",\n                      \"Asia/Hovd\",\n                      \"Asia/Jakarta\",\n                      \"Asia/Phnom_Penh\",\n                      \"Asia/Pontianak\",\n                      \"Asia/Saigon\",\n                      \"Asia/Vientiane\",\n                      \"Etc/GMT-7\",\n                      \"Indian/Christmas\"\n                    ]\n                  },\n                  {\n                    \"name\": \"N. Central Asia Standard Time\",\n                    \"abbr\": \"NCAST\",\n                    \"offset\": 7,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+07:00) Novosibirsk\",\n                    \"utc\": [\n                      \"Asia/Novokuznetsk\",\n                      \"Asia/Novosibirsk\",\n                      \"Asia/Omsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"China Standard Time\",\n                    \"abbr\": \"CST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Beijing, Chongqing, Hong Kong, Urumqi\",\n                    \"utc\": [\n                      \"Asia/Hong_Kong\",\n                      \"Asia/Macau\",\n                      \"Asia/Shanghai\"\n                    ]\n                  },\n                  {\n                    \"name\": \"North Asia Standard Time\",\n                    \"abbr\": \"NAST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Krasnoyarsk\",\n                    \"utc\": [\n                      \"Asia/Krasnoyarsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Singapore Standard Time\",\n                    \"abbr\": \"MPST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Kuala Lumpur, Singapore\",\n                    \"utc\": [\n                      \"Asia/Brunei\",\n                      \"Asia/Kuala_Lumpur\",\n                      \"Asia/Kuching\",\n                      \"Asia/Makassar\",\n                      \"Asia/Manila\",\n                      \"Asia/Singapore\",\n                      \"Etc/GMT-8\"\n                    ]\n                  },\n                  {\n                    \"name\": \"W. Australia Standard Time\",\n                    \"abbr\": \"WAST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Perth\",\n                    \"utc\": [\n                      \"Antarctica/Casey\",\n                      \"Australia/Perth\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Taipei Standard Time\",\n                    \"abbr\": \"TST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Taipei\",\n                    \"utc\": [\n                      \"Asia/Taipei\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Ulaanbaatar Standard Time\",\n                    \"abbr\": \"UST\",\n                    \"offset\": 8,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+08:00) Ulaanbaatar\",\n                    \"utc\": [\n                      \"Asia/Choibalsan\",\n                      \"Asia/Ulaanbaatar\"\n                    ]\n                  },\n                  {\n                    \"name\": \"North Asia East Standard Time\",\n                    \"abbr\": \"NAEST\",\n                    \"offset\": 9,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+09:00) Irkutsk\",\n                    \"utc\": [\n                      \"Asia/Irkutsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Tokyo Standard Time\",\n                    \"abbr\": \"TST\",\n                    \"offset\": 9,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+09:00) Osaka, Sapporo, Tokyo\",\n                    \"utc\": [\n                      \"Asia/Dili\",\n                      \"Asia/Jayapura\",\n                      \"Asia/Tokyo\",\n                      \"Etc/GMT-9\",\n                      \"Pacific/Palau\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Korea Standard Time\",\n                    \"abbr\": \"KST\",\n                    \"offset\": 9,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+09:00) Seoul\",\n                    \"utc\": [\n                      \"Asia/Pyongyang\",\n                      \"Asia/Seoul\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Cen. Australia Standard Time\",\n                    \"abbr\": \"CAST\",\n                    \"offset\": 9.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+09:30) Adelaide\",\n                    \"utc\": [\n                      \"Australia/Adelaide\",\n                      \"Australia/Broken_Hill\"\n                    ]\n                  },\n                  {\n                    \"name\": \"AUS Central Standard Time\",\n                    \"abbr\": \"ACST\",\n                    \"offset\": 9.5,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+09:30) Darwin\",\n                    \"utc\": [\n                      \"Australia/Darwin\"\n                    ]\n                  },\n                  {\n                    \"name\": \"E. Australia Standard Time\",\n                    \"abbr\": \"EAST\",\n                    \"offset\": 10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+10:00) Brisbane\",\n                    \"utc\": [\n                      \"Australia/Brisbane\",\n                      \"Australia/Lindeman\"\n                    ]\n                  },\n                  {\n                    \"name\": \"AUS Eastern Standard Time\",\n                    \"abbr\": \"AEST\",\n                    \"offset\": 10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+10:00) Canberra, Melbourne, Sydney\",\n                    \"utc\": [\n                      \"Australia/Melbourne\",\n                      \"Australia/Sydney\"\n                    ]\n                  },\n                  {\n                    \"name\": \"West Pacific Standard Time\",\n                    \"abbr\": \"WPST\",\n                    \"offset\": 10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+10:00) Guam, Port Moresby\",\n                    \"utc\": [\n                      \"Antarctica/DumontDUrville\",\n                      \"Etc/GMT-10\",\n                      \"Pacific/Guam\",\n                      \"Pacific/Port_Moresby\",\n                      \"Pacific/Saipan\",\n                      \"Pacific/Truk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Tasmania Standard Time\",\n                    \"abbr\": \"TST\",\n                    \"offset\": 10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+10:00) Hobart\",\n                    \"utc\": [\n                      \"Australia/Currie\",\n                      \"Australia/Hobart\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Yakutsk Standard Time\",\n                    \"abbr\": \"YST\",\n                    \"offset\": 10,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+10:00) Yakutsk\",\n                    \"utc\": [\n                      \"Asia/Chita\",\n                      \"Asia/Khandyga\",\n                      \"Asia/Yakutsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Central Pacific Standard Time\",\n                    \"abbr\": \"CPST\",\n                    \"offset\": 11,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+11:00) Solomon Is., New Caledonia\",\n                    \"utc\": [\n                      \"Antarctica/Macquarie\",\n                      \"Etc/GMT-11\",\n                      \"Pacific/Efate\",\n                      \"Pacific/Guadalcanal\",\n                      \"Pacific/Kosrae\",\n                      \"Pacific/Noumea\",\n                      \"Pacific/Ponape\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Vladivostok Standard Time\",\n                    \"abbr\": \"VST\",\n                    \"offset\": 11,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+11:00) Vladivostok\",\n                    \"utc\": [\n                      \"Asia/Sakhalin\",\n                      \"Asia/Ust-Nera\",\n                      \"Asia/Vladivostok\"\n                    ]\n                  },\n                  {\n                    \"name\": \"New Zealand Standard Time\",\n                    \"abbr\": \"NZST\",\n                    \"offset\": 12,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+12:00) Auckland, Wellington\",\n                    \"utc\": [\n                      \"Antarctica/McMurdo\",\n                      \"Pacific/Auckland\"\n                    ]\n                  },\n                  {\n                    \"name\": \"UTC+12\",\n                    \"abbr\": \"U\",\n                    \"offset\": 12,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+12:00) Coordinated Universal Time+12\",\n                    \"utc\": [\n                      \"Etc/GMT-12\",\n                      \"Pacific/Funafuti\",\n                      \"Pacific/Kwajalein\",\n                      \"Pacific/Majuro\",\n                      \"Pacific/Nauru\",\n                      \"Pacific/Tarawa\",\n                      \"Pacific/Wake\",\n                      \"Pacific/Wallis\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Fiji Standard Time\",\n                    \"abbr\": \"FST\",\n                    \"offset\": 12,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+12:00) Fiji\",\n                    \"utc\": [\n                      \"Pacific/Fiji\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Magadan Standard Time\",\n                    \"abbr\": \"MST\",\n                    \"offset\": 12,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+12:00) Magadan\",\n                    \"utc\": [\n                      \"Asia/Anadyr\",\n                      \"Asia/Kamchatka\",\n                      \"Asia/Magadan\",\n                      \"Asia/Srednekolymsk\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Kamchatka Standard Time\",\n                    \"abbr\": \"KDT\",\n                    \"offset\": 13,\n                    \"isdst\": true,\n                    \"text\": \"(UTC+12:00) Petropavlovsk-Kamchatsky - Old\"\n                  },\n                  {\n                    \"name\": \"Tonga Standard Time\",\n                    \"abbr\": \"TST\",\n                    \"offset\": 13,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+13:00) Nuku'alofa\",\n                    \"utc\": [\n                      \"Etc/GMT-13\",\n                      \"Pacific/Enderbury\",\n                      \"Pacific/Fakaofo\",\n                      \"Pacific/Tongatapu\"\n                    ]\n                  },\n                  {\n                    \"name\": \"Samoa Standard Time\",\n                    \"abbr\": \"SST\",\n                    \"offset\": 13,\n                    \"isdst\": false,\n                    \"text\": \"(UTC+13:00) Samoa\",\n                    \"utc\": [\n                      \"Pacific/Apia\"\n                    ]\n                  }\n                ],\n        //List source: http://answers.google.com/answers/threadview/id/589312.html\n        profession: [\n            \"Airline Pilot\",\n            \"Academic Team\",\n            \"Accountant\",\n            \"Account Executive\",\n            \"Actor\",\n            \"Actuary\",\n            \"Acquisition Analyst\",\n            \"Administrative Asst.\",\n            \"Administrative Analyst\",\n            \"Administrator\",\n            \"Advertising Director\",\n            \"Aerospace Engineer\",\n            \"Agent\",\n            \"Agricultural Inspector\",\n            \"Agricultural Scientist\",\n            \"Air Traffic Controller\",\n            \"Animal Trainer\",\n            \"Anthropologist\",\n            \"Appraiser\",\n            \"Architect\",\n            \"Art Director\",\n            \"Artist\",\n            \"Astronomer\",\n            \"Athletic Coach\",\n            \"Auditor\",\n            \"Author\",\n            \"Baker\",\n            \"Banker\",\n            \"Bankruptcy Attorney\",\n            \"Benefits Manager\",\n            \"Biologist\",\n            \"Bio-feedback Specialist\",\n            \"Biomedical Engineer\",\n            \"Biotechnical Researcher\",\n            \"Broadcaster\",\n            \"Broker\",\n            \"Building Manager\",\n            \"Building Contractor\",\n            \"Building Inspector\",\n            \"Business Analyst\",\n            \"Business Planner\",\n            \"Business Manager\",\n            \"Buyer\",\n            \"Call Center Manager\",\n            \"Career Counselor\",\n            \"Cash Manager\",\n            \"Ceramic Engineer\",\n            \"Chief Executive Officer\",\n            \"Chief Operation Officer\",\n            \"Chef\",\n            \"Chemical Engineer\",\n            \"Chemist\",\n            \"Child Care Manager\",\n            \"Chief Medical Officer\",\n            \"Chiropractor\",\n            \"Cinematographer\",\n            \"City Housing Manager\",\n            \"City Manager\",\n            \"Civil Engineer\",\n            \"Claims Manager\",\n            \"Clinical Research Assistant\",\n            \"Collections Manager.\",\n            \"Compliance Manager\",\n            \"Comptroller\",\n            \"Computer Manager\",\n            \"Commercial Artist\",\n            \"Communications Affairs Director\",\n            \"Communications Director\",\n            \"Communications Engineer\",\n            \"Compensation Analyst\",\n            \"Computer Programmer\",\n            \"Computer Ops. Manager\",\n            \"Computer Engineer\",\n            \"Computer Operator\",\n            \"Computer Graphics Specialist\",\n            \"Construction Engineer\",\n            \"Construction Manager\",\n            \"Consultant\",\n            \"Consumer Relations Manager\",\n            \"Contract Administrator\",\n            \"Copyright Attorney\",\n            \"Copywriter\",\n            \"Corporate Planner\",\n            \"Corrections Officer\",\n            \"Cosmetologist\",\n            \"Credit Analyst\",\n            \"Cruise Director\",\n            \"Chief Information Officer\",\n            \"Chief Technology Officer\",\n            \"Customer Service Manager\",\n            \"Cryptologist\",\n            \"Dancer\",\n            \"Data Security Manager\",\n            \"Database Manager\",\n            \"Day Care Instructor\",\n            \"Dentist\",\n            \"Designer\",\n            \"Design Engineer\",\n            \"Desktop Publisher\",\n            \"Developer\",\n            \"Development Officer\",\n            \"Diamond Merchant\",\n            \"Dietitian\",\n            \"Direct Marketer\",\n            \"Director\",\n            \"Distribution Manager\",\n            \"Diversity Manager\",\n            \"Economist\",\n            \"EEO Compliance Manager\",\n            \"Editor\",\n            \"Education Adminator\",\n            \"Electrical Engineer\",\n            \"Electro Optical Engineer\",\n            \"Electronics Engineer\",\n            \"Embassy Management\",\n            \"Employment Agent\",\n            \"Engineer Technician\",\n            \"Entrepreneur\",\n            \"Environmental Analyst\",\n            \"Environmental Attorney\",\n            \"Environmental Engineer\",\n            \"Environmental Specialist\",\n            \"Escrow Officer\",\n            \"Estimator\",\n            \"Executive Assistant\",\n            \"Executive Director\",\n            \"Executive Recruiter\",\n            \"Facilities Manager\",\n            \"Family Counselor\",\n            \"Fashion Events Manager\",\n            \"Fashion Merchandiser\",\n            \"Fast Food Manager\",\n            \"Film Producer\",\n            \"Film Production Assistant\",\n            \"Financial Analyst\",\n            \"Financial Planner\",\n            \"Financier\",\n            \"Fine Artist\",\n            \"Wildlife Specialist\",\n            \"Fitness Consultant\",\n            \"Flight Attendant\",\n            \"Flight Engineer\",\n            \"Floral Designer\",\n            \"Food & Beverage Director\",\n            \"Food Service Manager\",\n            \"Forestry Technician\",\n            \"Franchise Management\",\n            \"Franchise Sales\",\n            \"Fraud Investigator\",\n            \"Freelance Writer\",\n            \"Fund Raiser\",\n            \"General Manager\",\n            \"Geologist\",\n            \"General Counsel\",\n            \"Geriatric Specialist\",\n            \"Gerontologist\",\n            \"Glamour Photographer\",\n            \"Golf Club Manager\",\n            \"Gourmet Chef\",\n            \"Graphic Designer\",\n            \"Grounds Keeper\",\n            \"Hazardous Waste Manager\",\n            \"Health Care Manager\",\n            \"Health Therapist\",\n            \"Health Service Administrator\",\n            \"Hearing Officer\",\n            \"Home Economist\",\n            \"Horticulturist\",\n            \"Hospital Administrator\",\n            \"Hotel Manager\",\n            \"Human Resources Manager\",\n            \"Importer\",\n            \"Industrial Designer\",\n            \"Industrial Engineer\",\n            \"Information Director\",\n            \"Inside Sales\",\n            \"Insurance Adjuster\",\n            \"Interior Decorator\",\n            \"Internal Controls Director\",\n            \"International Acct.\",\n            \"International Courier\",\n            \"International Lawyer\",\n            \"Interpreter\",\n            \"Investigator\",\n            \"Investment Banker\",\n            \"Investment Manager\",\n            \"IT Architect\",\n            \"IT Project Manager\",\n            \"IT Systems Analyst\",\n            \"Jeweler\",\n            \"Joint Venture Manager\",\n            \"Journalist\",\n            \"Labor Negotiator\",\n            \"Labor Organizer\",\n            \"Labor Relations Manager\",\n            \"Lab Services Director\",\n            \"Lab Technician\",\n            \"Land Developer\",\n            \"Landscape Architect\",\n            \"Law Enforcement Officer\",\n            \"Lawyer\",\n            \"Lead Software Engineer\",\n            \"Lead Software Test Engineer\",\n            \"Leasing Manager\",\n            \"Legal Secretary\",\n            \"Library Manager\",\n            \"Litigation Attorney\",\n            \"Loan Officer\",\n            \"Lobbyist\",\n            \"Logistics Manager\",\n            \"Maintenance Manager\",\n            \"Management Consultant\",\n            \"Managed Care Director\",\n            \"Managing Partner\",\n            \"Manufacturing Director\",\n            \"Manpower Planner\",\n            \"Marine Biologist\",\n            \"Market Res. Analyst\",\n            \"Marketing Director\",\n            \"Materials Manager\",\n            \"Mathematician\",\n            \"Membership Chairman\",\n            \"Mechanic\",\n            \"Mechanical Engineer\",\n            \"Media Buyer\",\n            \"Medical Investor\",\n            \"Medical Secretary\",\n            \"Medical Technician\",\n            \"Mental Health Counselor\",\n            \"Merchandiser\",\n            \"Metallurgical Engineering\",\n            \"Meteorologist\",\n            \"Microbiologist\",\n            \"MIS Manager\",\n            \"Motion Picture Director\",\n            \"Multimedia Director\",\n            \"Musician\",\n            \"Network Administrator\",\n            \"Network Specialist\",\n            \"Network Operator\",\n            \"New Product Manager\",\n            \"Novelist\",\n            \"Nuclear Engineer\",\n            \"Nuclear Specialist\",\n            \"Nutritionist\",\n            \"Nursing Administrator\",\n            \"Occupational Therapist\",\n            \"Oceanographer\",\n            \"Office Manager\",\n            \"Operations Manager\",\n            \"Operations Research Director\",\n            \"Optical Technician\",\n            \"Optometrist\",\n            \"Organizational Development Manager\",\n            \"Outplacement Specialist\",\n            \"Paralegal\",\n            \"Park Ranger\",\n            \"Patent Attorney\",\n            \"Payroll Specialist\",\n            \"Personnel Specialist\",\n            \"Petroleum Engineer\",\n            \"Pharmacist\",\n            \"Photographer\",\n            \"Physical Therapist\",\n            \"Physician\",\n            \"Physician Assistant\",\n            \"Physicist\",\n            \"Planning Director\",\n            \"Podiatrist\",\n            \"Political Analyst\",\n            \"Political Scientist\",\n            \"Politician\",\n            \"Portfolio Manager\",\n            \"Preschool Management\",\n            \"Preschool Teacher\",\n            \"Principal\",\n            \"Private Banker\",\n            \"Private Investigator\",\n            \"Probation Officer\",\n            \"Process Engineer\",\n            \"Producer\",\n            \"Product Manager\",\n            \"Product Engineer\",\n            \"Production Engineer\",\n            \"Production Planner\",\n            \"Professional Athlete\",\n            \"Professional Coach\",\n            \"Professor\",\n            \"Project Engineer\",\n            \"Project Manager\",\n            \"Program Manager\",\n            \"Property Manager\",\n            \"Public Administrator\",\n            \"Public Safety Director\",\n            \"PR Specialist\",\n            \"Publisher\",\n            \"Purchasing Agent\",\n            \"Publishing Director\",\n            \"Quality Assurance Specialist\",\n            \"Quality Control Engineer\",\n            \"Quality Control Inspector\",\n            \"Radiology Manager\",\n            \"Railroad Engineer\",\n            \"Real Estate Broker\",\n            \"Recreational Director\",\n            \"Recruiter\",\n            \"Redevelopment Specialist\",\n            \"Regulatory Affairs Manager\",\n            \"Registered Nurse\",\n            \"Rehabilitation Counselor\",\n            \"Relocation Manager\",\n            \"Reporter\",\n            \"Research Specialist\",\n            \"Restaurant Manager\",\n            \"Retail Store Manager\",\n            \"Risk Analyst\",\n            \"Safety Engineer\",\n            \"Sales Engineer\",\n            \"Sales Trainer\",\n            \"Sales Promotion Manager\",\n            \"Sales Representative\",\n            \"Sales Manager\",\n            \"Service Manager\",\n            \"Sanitation Engineer\",\n            \"Scientific Programmer\",\n            \"Scientific Writer\",\n            \"Securities Analyst\",\n            \"Security Consultant\",\n            \"Security Director\",\n            \"Seminar Presenter\",\n            \"Ship's Officer\",\n            \"Singer\",\n            \"Social Director\",\n            \"Social Program Planner\",\n            \"Social Research\",\n            \"Social Scientist\",\n            \"Social Worker\",\n            \"Sociologist\",\n            \"Software Developer\",\n            \"Software Engineer\",\n            \"Software Test Engineer\",\n            \"Soil Scientist\",\n            \"Special Events Manager\",\n            \"Special Education Teacher\",\n            \"Special Projects Director\",\n            \"Speech Pathologist\",\n            \"Speech Writer\",\n            \"Sports Event Manager\",\n            \"Statistician\",\n            \"Store Manager\",\n            \"Strategic Alliance Director\",\n            \"Strategic Planning Director\",\n            \"Stress Reduction Specialist\",\n            \"Stockbroker\",\n            \"Surveyor\",\n            \"Structural Engineer\",\n            \"Superintendent\",\n            \"Supply Chain Director\",\n            \"System Engineer\",\n            \"Systems Analyst\",\n            \"Systems Programmer\",\n            \"System Administrator\",\n            \"Tax Specialist\",\n            \"Teacher\",\n            \"Technical Support Specialist\",\n            \"Technical Illustrator\",\n            \"Technical Writer\",\n            \"Technology Director\",\n            \"Telecom Analyst\",\n            \"Telemarketer\",\n            \"Theatrical Director\",\n            \"Title Examiner\",\n            \"Tour Escort\",\n            \"Tour Guide Director\",\n            \"Traffic Manager\",\n            \"Trainer Translator\",\n            \"Transportation Manager\",\n            \"Travel Agent\",\n            \"Treasurer\",\n            \"TV Programmer\",\n            \"Underwriter\",\n            \"Union Representative\",\n            \"University Administrator\",\n            \"University Dean\",\n            \"Urban Planner\",\n            \"Veterinarian\",\n            \"Vendor Relations Director\",\n            \"Viticulturist\",\n            \"Warehouse Manager\"\n        ],\n        animals : {\n          //list of ocean animals comes from https://owlcation.com/stem/list-of-ocean-animals\n          \"ocean\" : [\"Acantharea\",\"Anemone\",\"Angelfish King\",\"Ahi Tuna\",\"Albacore\",\"American Oyster\",\"Anchovy\",\"Armored Snail\",\"Arctic Char\",\"Atlantic Bluefin Tuna\",\"Atlantic Cod\",\"Atlantic Goliath Grouper\",\"Atlantic Trumpetfish\",\"Atlantic Wolffish\",\"Baleen Whale\",\"Banded Butterflyfish\",\"Banded Coral Shrimp\",\"Banded Sea Krait\",\"Barnacle\",\"Barndoor Skate\",\"Barracuda\",\"Basking Shark\",\"Bass\",\"Beluga Whale\",\"Bluebanded Goby\",\"Bluehead Wrasse\",\"Bluefish\",\"Bluestreak Cleaner-Wrasse\",\"Blue Marlin\",\"Blue Shark\",\"Blue Spiny Lobster\",\"Blue Tang\",\"Blue Whale\",\"Broadclub Cuttlefish\",\"Bull Shark\",\"Chambered Nautilus\",\"Chilean Basket Star\",\"Chilean Jack Mackerel\",\"Chinook Salmon\",\"Christmas Tree Worm\",\"Clam\",\"Clown Anemonefish\",\"Clown Triggerfish\",\"Cod\",\"Coelacanth\",\"Cockscomb Cup Coral\",\"Common Fangtooth\",\"Conch\",\"Cookiecutter Shark\",\"Copepod\",\"Coral\",\"Corydoras\",\"Cownose Ray\",\"Crab\",\"Crown-of-Thorns Starfish\",\"Cushion Star\",\"Cuttlefish\",\"California Sea Otters\",\"Dolphin\",\"Dolphinfish\",\"Dory\",\"Devil Fish\",\"Dugong\",\"Dumbo Octopus\",\"Dungeness Crab\",\"Eccentric Sand Dollar\",\"Edible Sea Cucumber\",\"Eel\",\"Elephant Seal\",\"Elkhorn Coral\",\"Emperor Shrimp\",\"Estuarine Crocodile\",\"Fathead Sculpin\",\"Fiddler Crab\",\"Fin Whale\",\"Flameback\",\"Flamingo Tongue Snail\",\"Flashlight Fish\",\"Flatback Turtle\",\"Flatfish\",\"Flying Fish\",\"Flounder\",\"Fluke\",\"French Angelfish\",\"Frilled Shark\",\"Fugu (also called Pufferfish)\",\"Gar\",\"Geoduck\",\"Giant Barrel Sponge\",\"Giant Caribbean Sea Anemone\",\"Giant Clam\",\"Giant Isopod\",\"Giant Kingfish\",\"Giant Oarfish\",\"Giant Pacific Octopus\",\"Giant Pyrosome\",\"Giant Sea Star\",\"Giant Squid\",\"Glowing Sucker Octopus\",\"Giant Tube Worm\",\"Goblin Shark\",\"Goosefish\",\"Great White Shark\",\"Greenland Shark\",\"Grey Atlantic Seal\",\"Grouper\",\"Grunion\",\"Guineafowl Puffer\",\"Haddock\",\"Hake\",\"Halibut\",\"Hammerhead Shark\",\"Hapuka\",\"Harbor Porpoise\",\"Harbor Seal\",\"Hatchetfish\",\"Hawaiian Monk Seal\",\"Hawksbill Turtle\",\"Hector's Dolphin\",\"Hermit Crab\",\"Herring\",\"Hoki\",\"Horn Shark\",\"Horseshoe Crab\",\"Humpback Anglerfish\",\"Humpback Whale\",\"Icefish\",\"Imperator Angelfish\",\"Irukandji Jellyfish\",\"Isopod\",\"Ivory Bush Coral\",\"Japanese Spider Crab\",\"Jellyfish\",\"John Dory\",\"Juan Fernandez Fur Seal\",\"Killer Whale\",\"Kiwa Hirsuta\",\"Krill\",\"Lagoon Triggerfish\",\"Lamprey\",\"Leafy Seadragon\",\"Leopard Seal\",\"Limpet\",\"Ling\",\"Lionfish\",\"Lions Mane Jellyfish\",\"Lobe Coral\",\"Lobster\",\"Loggerhead Turtle\",\"Longnose Sawshark\",\"Longsnout Seahorse\",\"Lophelia Coral\",\"Marrus Orthocanna\",\"Manatee\",\"Manta Ray\",\"Marlin\",\"Megamouth Shark\",\"Mexican Lookdown\",\"Mimic Octopus\",\"Moon Jelly\",\"Mollusk\",\"Monkfish\",\"Moray Eel\",\"Mullet\",\"Mussel\",\"Megaladon\",\"Napoleon Wrasse\",\"Nassau Grouper\",\"Narwhal\",\"Nautilus\",\"Needlefish\",\"Northern Seahorse\",\"North Atlantic Right Whale\",\"Northern Red Snapper\",\"Norway Lobster\",\"Nudibranch\",\"Nurse Shark\",\"Oarfish\",\"Ocean Sunfish\",\"Oceanic Whitetip Shark\",\"Octopus\",\"Olive Sea Snake\",\"Orange Roughy\",\"Ostracod\",\"Otter\",\"Oyster\",\"Pacific Angelshark\",\"Pacific Blackdragon\",\"Pacific Halibut\",\"Pacific Sardine\",\"Pacific Sea Nettle Jellyfish\",\"Pacific White Sided Dolphin\",\"Pantropical Spotted Dolphin\",\"Patagonian Toothfish\",\"Peacock Mantis Shrimp\",\"Pelagic Thresher Shark\",\"Penguin\",\"Peruvian Anchoveta\",\"Pilchard\",\"Pink Salmon\",\"Pinniped\",\"Plankton\",\"Porpoise\",\"Polar Bear\",\"Portuguese Man o' War\",\"Pycnogonid Sea Spider\",\"Quahog\",\"Queen Angelfish\",\"Queen Conch\",\"Queen Parrotfish\",\"Queensland Grouper\",\"Ragfish\",\"Ratfish\",\"Rattail Fish\",\"Ray\",\"Red Drum\",\"Red King Crab\",\"Ringed Seal\",\"Risso's Dolphin\",\"Ross Seals\",\"Sablefish\",\"Salmon\",\"Sand Dollar\",\"Sandbar Shark\",\"Sawfish\",\"Sarcastic Fringehead\",\"Scalloped Hammerhead Shark\",\"Seahorse\",\"Sea Cucumber\",\"Sea Lion\",\"Sea Urchin\",\"Seal\",\"Shark\",\"Shortfin Mako Shark\",\"Shovelnose Guitarfish\",\"Shrimp\",\"Silverside Fish\",\"Skipjack Tuna\",\"Slender Snipe Eel\",\"Smalltooth Sawfish\",\"Smelts\",\"Sockeye Salmon\",\"Southern Stingray\",\"Sponge\",\"Spotted Porcupinefish\",\"Spotted Dolphin\",\"Spotted Eagle Ray\",\"Spotted Moray\",\"Squid\",\"Squidworm\",\"Starfish\",\"Stickleback\",\"Stonefish\",\"Stoplight Loosejaw\",\"Sturgeon\",\"Swordfish\",\"Tan Bristlemouth\",\"Tasseled Wobbegong\",\"Terrible Claw Lobster\",\"Threespot Damselfish\",\"Tiger Prawn\",\"Tiger Shark\",\"Tilefish\",\"Toadfish\",\"Tropical Two-Wing Flyfish\",\"Tuna\",\"Umbrella Squid\",\"Velvet Crab\",\"Venus Flytrap Sea Anemone\",\"Vigtorniella Worm\",\"Viperfish\",\"Vampire Squid\",\"Vaquita\",\"Wahoo\",\"Walrus\",\"West Indian Manatee\",\"Whale\",\"Whale Shark\",\"Whiptail Gulper\",\"White-Beaked Dolphin\",\"White-Ring Garden Eel\",\"White Shrimp\",\"Wobbegong\",\"Wrasse\",\"Wreckfish\",\"Xiphosura\",\"Yellowtail Damselfish\",\"Yelloweye Rockfish\",\"Yellow Cup Black Coral\",\"Yellow Tube Sponge\",\"Yellowfin Tuna\",\"Zebrashark\",\"Zooplankton\"],\n          //list of desert, grassland, and forest animals comes from http://www.skyenimals.com/\n          \"desert\" : [\"Aardwolf\",\"Addax\",\"African Wild Ass\",\"Ant\",\"Antelope\",\"Armadillo\",\"Baboon\",\"Badger\",\"Bat\",\"Bearded Dragon\",\"Beetle\",\"Bird\",\"Black-footed Cat\",\"Boa\",\"Brown Bear\",\"Bustard\",\"Butterfly\",\"Camel\",\"Caracal\",\"Caracara\",\"Caterpillar\",\"Centipede\",\"Cheetah\",\"Chipmunk\",\"Chuckwalla\",\"Climbing Mouse\",\"Coati\",\"Cobra\",\"Cotton Rat\",\"Cougar\",\"Courser\",\"Crane Fly\",\"Crow\",\"Dassie Rat\",\"Dove\",\"Dunnart\",\"Eagle\",\"Echidna\",\"Elephant\",\"Emu\",\"Falcon\",\"Fly\",\"Fox\",\"Frogmouth\",\"Gecko\",\"Geoffroy's Cat\",\"Gerbil\",\"Grasshopper\",\"Guanaco\",\"Gundi\",\"Hamster\",\"Hawk\",\"Hedgehog\",\"Hyena\",\"Hyrax\",\"Jackal\",\"Kangaroo\",\"Kangaroo Rat\",\"Kestrel\",\"Kowari\",\"Kultarr\",\"Leopard\",\"Lion\",\"Macaw\",\"Meerkat\",\"Mouse\",\"Oryx\",\"Ostrich\",\"Owl\",\"Pronghorn\",\"Python\",\"Rabbit\",\"Raccoon\",\"Rattlesnake\",\"Rhinoceros\",\"Sand Cat\",\"Spectacled Bear\",\"Spiny Mouse\",\"Starling\",\"Stick Bug\",\"Tarantula\",\"Tit\",\"Toad\",\"Tortoise\",\"Tyrant Flycatcher\",\"Viper\",\"Vulture\",\"Waxwing\",\"Xerus\",\"Zebra\"],\n          \"grassland\" : [\"Aardvark\",\"Aardwolf\",\"Accentor\",\"African Buffalo\",\"African Wild Dog\",\"Alpaca\",\"Anaconda\",\"Ant\",\"Anteater\",\"Antelope\",\"Armadillo\",\"Baboon\",\"Badger\",\"Bandicoot\",\"Barbet\",\"Bat\",\"Bee\",\"Bee-eater\",\"Beetle\",\"Bird\",\"Bison\",\"Black-footed Cat\",\"Black-footed Ferret\",\"Bluebird\",\"Boa\",\"Bowerbird\",\"Brown Bear\",\"Bush Dog\",\"Bushshrike\",\"Bustard\",\"Butterfly\",\"Buzzard\",\"Caracal\",\"Caracara\",\"Cardinal\",\"Caterpillar\",\"Cheetah\",\"Chipmunk\",\"Civet\",\"Climbing Mouse\",\"Clouded Leopard\",\"Coati\",\"Cobra\",\"Cockatoo\",\"Cockroach\",\"Common Genet\",\"Cotton Rat\",\"Cougar\",\"Courser\",\"Coyote\",\"Crane\",\"Crane Fly\",\"Cricket\",\"Crow\",\"Culpeo\",\"Death Adder\",\"Deer\",\"Deer Mouse\",\"Dingo\",\"Dinosaur\",\"Dove\",\"Drongo\",\"Duck\",\"Duiker\",\"Dunnart\",\"Eagle\",\"Echidna\",\"Elephant\",\"Elk\",\"Emu\",\"Falcon\",\"Finch\",\"Flea\",\"Fly\",\"Flying Frog\",\"Fox\",\"Frog\",\"Frogmouth\",\"Garter Snake\",\"Gazelle\",\"Gecko\",\"Geoffroy's Cat\",\"Gerbil\",\"Giant Tortoise\",\"Giraffe\",\"Grasshopper\",\"Grison\",\"Groundhog\",\"Grouse\",\"Guanaco\",\"Guinea Pig\",\"Hamster\",\"Harrier\",\"Hartebeest\",\"Hawk\",\"Hedgehog\",\"Helmetshrike\",\"Hippopotamus\",\"Hornbill\",\"Hyena\",\"Hyrax\",\"Impala\",\"Jackal\",\"Jaguar\",\"Jaguarundi\",\"Kangaroo\",\"Kangaroo Rat\",\"Kestrel\",\"Kultarr\",\"Ladybug\",\"Leopard\",\"Lion\",\"Macaw\",\"Meerkat\",\"Mouse\",\"Newt\",\"Oryx\",\"Ostrich\",\"Owl\",\"Pangolin\",\"Pheasant\",\"Prairie Dog\",\"Pronghorn\",\"Przewalski's Horse\",\"Python\",\"Quoll\",\"Rabbit\",\"Raven\",\"Rhinoceros\",\"Shelduck\",\"Sloth Bear\",\"Spectacled Bear\",\"Squirrel\",\"Starling\",\"Stick Bug\",\"Tamandua\",\"Tasmanian Devil\",\"Thornbill\",\"Thrush\",\"Toad\",\"Tortoise\"],\n          \"forest\" : [\"Agouti\",\"Anaconda\",\"Anoa\",\"Ant\",\"Anteater\",\"Antelope\",\"Armadillo\",\"Asian Black Bear\",\"Aye-aye\",\"Babirusa\",\"Baboon\",\"Badger\",\"Bandicoot\",\"Banteng\",\"Barbet\",\"Basilisk\",\"Bat\",\"Bearded Dragon\",\"Bee\",\"Bee-eater\",\"Beetle\",\"Bettong\",\"Binturong\",\"Bird-of-paradise\",\"Bongo\",\"Bowerbird\",\"Bulbul\",\"Bush Dog\",\"Bushbaby\",\"Bushshrike\",\"Butterfly\",\"Buzzard\",\"Caecilian\",\"Cardinal\",\"Cassowary\",\"Caterpillar\",\"Centipede\",\"Chameleon\",\"Chimpanzee\",\"Cicada\",\"Civet\",\"Clouded Leopard\",\"Coati\",\"Cobra\",\"Cockatoo\",\"Cockroach\",\"Colugo\",\"Cotinga\",\"Cotton Rat\",\"Cougar\",\"Crane Fly\",\"Cricket\",\"Crocodile\",\"Crow\",\"Cuckoo\",\"Cuscus\",\"Death Adder\",\"Deer\",\"Dhole\",\"Dingo\",\"Dinosaur\",\"Drongo\",\"Duck\",\"Duiker\",\"Eagle\",\"Echidna\",\"Elephant\",\"Finch\",\"Flat-headed Cat\",\"Flea\",\"Flowerpecker\",\"Fly\",\"Flying Frog\",\"Fossa\",\"Frog\",\"Frogmouth\",\"Gaur\",\"Gecko\",\"Gorilla\",\"Grison\",\"Hawaiian Honeycreeper\",\"Hawk\",\"Hedgehog\",\"Helmetshrike\",\"Hornbill\",\"Hyrax\",\"Iguana\",\"Jackal\",\"Jaguar\",\"Jaguarundi\",\"Kestrel\",\"Ladybug\",\"Lemur\",\"Leopard\",\"Lion\",\"Macaw\",\"Mandrill\",\"Margay\",\"Monkey\",\"Mouse\",\"Mouse Deer\",\"Newt\",\"Okapi\",\"Old World Flycatcher\",\"Orangutan\",\"Owl\",\"Pangolin\",\"Peafowl\",\"Pheasant\",\"Possum\",\"Python\",\"Quokka\",\"Rabbit\",\"Raccoon\",\"Red Panda\",\"Red River Hog\",\"Rhinoceros\",\"Sloth Bear\",\"Spectacled Bear\",\"Squirrel\",\"Starling\",\"Stick Bug\",\"Sun Bear\",\"Tamandua\",\"Tamarin\",\"Tapir\",\"Tarantula\",\"Thrush\",\"Tiger\",\"Tit\",\"Toad\",\"Tortoise\",\"Toucan\",\"Trogon\",\"Trumpeter\",\"Turaco\",\"Turtle\",\"Tyrant Flycatcher\",\"Viper\",\"Vulture\",\"Wallaby\",\"Warbler\",\"Wasp\",\"Waxwing\",\"Weaver\",\"Weaver-finch\",\"Whistler\",\"White-eye\",\"Whydah\",\"Woodswallow\",\"Worm\",\"Wren\",\"Xenops\",\"Yellowjacket\",\"Accentor\",\"African Buffalo\",\"American Black Bear\",\"Anole\",\"Bird\",\"Bison\",\"Boa\",\"Brown Bear\",\"Chipmunk\",\"Common Genet\",\"Copperhead\",\"Coyote\",\"Deer Mouse\",\"Dormouse\",\"Elk\",\"Emu\",\"Fisher\",\"Fox\",\"Garter Snake\",\"Giant Panda\",\"Giant Tortoise\",\"Groundhog\",\"Grouse\",\"Guanaco\",\"Himalayan Tahr\",\"Kangaroo\",\"Koala\",\"Numbat\",\"Quoll\",\"Raccoon dog\",\"Tasmanian Devil\",\"Thornbill\",\"Turkey\",\"Vole\",\"Weasel\",\"Wildcat\",\"Wolf\",\"Wombat\",\"Woodchuck\",\"Woodpecker\"],\n          //list of farm animals comes from https://www.buzzle.com/articles/farm-animals-list.html\n          \"farm\" : [\"Alpaca\",\"Buffalo\",\"Banteng\",\"Cow\",\"Cat\",\"Chicken\",\"Carp\",\"Camel\",\"Donkey\",\"Dog\",\"Duck\",\"Emu\",\"Goat\",\"Gayal\",\"Guinea\",\"Goose\",\"Horse\",\"Honey\",\"Llama\",\"Pig\",\"Pigeon\",\"Rhea\",\"Rabbit\",\"Sheep\",\"Silkworm\",\"Turkey\",\"Yak\",\"Zebu\"],\n          //list of pet animals comes from https://www.dogbreedinfo.com/pets/pet.htm\n          \"pet\" : [\"Bearded Dragon\",\"Birds\",\"Burro\",\"Cats\",\"Chameleons\",\"Chickens\",\"Chinchillas\",\"Chinese Water Dragon\",\"Cows\",\"Dogs\",\"Donkey\",\"Ducks\",\"Ferrets\",\"Fish\",\"Geckos\",\"Geese\",\"Gerbils\",\"Goats\",\"Guinea Fowl\",\"Guinea Pigs\",\"Hamsters\",\"Hedgehogs\",\"Horses\",\"Iguanas\",\"Llamas\",\"Lizards\",\"Mice\",\"Mule\",\"Peafowl\",\"Pigs and Hogs\",\"Pigeons\",\"Ponies\",\"Pot Bellied Pig\",\"Rabbits\",\"Rats\",\"Sheep\",\"Skinks\",\"Snakes\",\"Stick Insects\",\"Sugar Gliders\",\"Tarantula\",\"Turkeys\",\"Turtles\"],\n          //list of zoo animals comes from https://bronxzoo.com/animals\n          \"zoo\" : [\"Aardvark\",\"African Wild Dog\",\"Aldabra Tortoise\",\"American Alligator\",\"American Bison\",\"Amur Tiger\",\"Anaconda\",\"Andean Condor\",\"Asian Elephant\",\"Baby Doll Sheep\",\"Bald Eagle\",\"Barred Owl\",\"Blue Iguana\",\"Boer Goat\",\"California Sea Lion\",\"Caribbean Flamingo\",\"Chinchilla\",\"Collared Lemur\",\"Coquerel's Sifaka\",\"Cuban Amazon Parrot\",\"Ebony Langur\",\"Fennec Fox\",\"Fossa\",\"Gelada\",\"Giant Anteater\",\"Giraffe\",\"Gorilla\",\"Grizzly Bear\",\"Henkel's Leaf-tailed Gecko\",\"Indian Gharial\",\"Indian Rhinoceros\",\"King Cobra\",\"King Vulture\",\"Komodo Dragon\",\"Linne's Two-toed Sloth\",\"Lion\",\"Little Penguin\",\"Madagascar Tree Boa\",\"Magellanic Penguin\",\"Malayan Tapir\",\"Malayan Tiger\",\"Matschies Tree Kangaroo\",\"Mini Donkey\",\"Monarch Butterfly\",\"Nile crocodile\",\"North American Porcupine\",\"Nubian Ibex\",\"Okapi\",\"Poison Dart Frog\",\"Polar Bear\",\"Pygmy Marmoset\",\"Radiated Tortoise\",\"Red Panda\",\"Red Ruffed Lemur\",\"Ring-tailed Lemur\",\"Ring-tailed Mongoose\",\"Rock Hyrax\",\"Small Clawed Asian Otter\",\"Snow Leopard\",\"Snowy Owl\",\"Southern White-faced Owl\",\"Southern White Rhinocerous\",\"Squirrel Monkey\",\"Tufted Puffin\",\"White Cheeked Gibbon\",\"White-throated Bee Eater\",\"Zebra\"]\n        }\n    };\n\n    var o_hasOwnProperty = Object.prototype.hasOwnProperty;\n    var o_keys = (Object.keys || function(obj) {\n      var result = [];\n      for (var key in obj) {\n        if (o_hasOwnProperty.call(obj, key)) {\n          result.push(key);\n        }\n      }\n\n      return result;\n    });\n\n\n    function _copyObject(source, target) {\n      var keys = o_keys(source);\n      var key;\n\n      for (var i = 0, l = keys.length; i < l; i++) {\n        key = keys[i];\n        target[key] = source[key] || target[key];\n      }\n    }\n\n    function _copyArray(source, target) {\n      for (var i = 0, l = source.length; i < l; i++) {\n        target[i] = source[i];\n      }\n    }\n\n    function copyObject(source, _target) {\n        var isArray = Array.isArray(source);\n        var target = _target || (isArray ? new Array(source.length) : {});\n\n        if (isArray) {\n          _copyArray(source, target);\n        } else {\n          _copyObject(source, target);\n        }\n\n        return target;\n    }\n\n    /** Get the data based on key**/\n    Chance.prototype.get = function (name) {\n        return copyObject(data[name]);\n    };\n\n    // Mac Address\n    Chance.prototype.mac_address = function(options){\n        // typically mac addresses are separated by \":\"\n        // however they can also be separated by \"-\"\n        // the network variant uses a dot every fourth byte\n\n        options = initOptions(options);\n        if(!options.separator) {\n            options.separator =  options.networkVersion ? \".\" : \":\";\n        }\n\n        var mac_pool=\"ABCDEF**********\",\n            mac = \"\";\n        if(!options.networkVersion) {\n            mac = this.n(this.string, 6, { pool: mac_pool, length:2 }).join(options.separator);\n        } else {\n            mac = this.n(this.string, 3, { pool: mac_pool, length:4 }).join(options.separator);\n        }\n\n        return mac;\n    };\n\n    Chance.prototype.normal = function (options) {\n        options = initOptions(options, {mean : 0, dev : 1, pool : []});\n\n        testRange(\n            options.pool.constructor !== Array,\n            \"Chance: The pool option must be a valid array.\"\n        );\n        testRange(\n            typeof options.mean !== 'number',\n            \"Chance: Mean (mean) must be a number\"\n        );\n        testRange(\n            typeof options.dev !== 'number',\n            \"Chance: Standard deviation (dev) must be a number\"\n        );\n\n        // If a pool has been passed, then we are returning an item from that pool,\n        // using the normal distribution settings that were passed in\n        if (options.pool.length > 0) {\n            return this.normal_pool(options);\n        }\n\n        // The Marsaglia Polar method\n        var s, u, v, norm,\n            mean = options.mean,\n            dev = options.dev;\n\n        do {\n            // U and V are from the uniform distribution on (-1, 1)\n            u = this.random() * 2 - 1;\n            v = this.random() * 2 - 1;\n\n            s = u * u + v * v;\n        } while (s >= 1);\n\n        // Compute the standard normal variate\n        norm = u * Math.sqrt(-2 * Math.log(s) / s);\n\n        // Shape and scale\n        return dev * norm + mean;\n    };\n\n    Chance.prototype.normal_pool = function(options) {\n        var performanceCounter = 0;\n        do {\n            var idx = Math.round(this.normal({ mean: options.mean, dev: options.dev }));\n            if (idx < options.pool.length && idx >= 0) {\n                return options.pool[idx];\n            } else {\n                performanceCounter++;\n            }\n        } while(performanceCounter < 100);\n\n        throw new RangeError(\"Chance: Your pool is too small for the given mean and standard deviation. Please adjust.\");\n    };\n\n    Chance.prototype.radio = function (options) {\n        // Initial Letter (Typically Designated by Side of Mississippi River)\n        options = initOptions(options, {side : \"?\"});\n        var fl = \"\";\n        switch (options.side.toLowerCase()) {\n        case \"east\":\n        case \"e\":\n            fl = \"W\";\n            break;\n        case \"west\":\n        case \"w\":\n            fl = \"K\";\n            break;\n        default:\n            fl = this.character({pool: \"KW\"});\n            break;\n        }\n\n        return fl + this.character({alpha: true, casing: \"upper\"}) +\n                this.character({alpha: true, casing: \"upper\"}) +\n                this.character({alpha: true, casing: \"upper\"});\n    };\n\n    // Set the data as key and data or the data map\n    Chance.prototype.set = function (name, values) {\n        if (typeof name === \"string\") {\n            data[name] = values;\n        } else {\n            data = copyObject(name, data);\n        }\n    };\n\n    Chance.prototype.tv = function (options) {\n        return this.radio(options);\n    };\n\n    // ID number for Brazil companies\n    Chance.prototype.cnpj = function () {\n        var n = this.n(this.natural, 8, { max: 9 });\n        var d1 = 2+n[7]*6+n[6]*7+n[5]*8+n[4]*9+n[3]*2+n[2]*3+n[1]*4+n[0]*5;\n        d1 = 11 - (d1 % 11);\n        if (d1>=10){\n            d1 = 0;\n        }\n        var d2 = d1*2+3+n[7]*7+n[6]*8+n[5]*9+n[4]*2+n[3]*3+n[2]*4+n[1]*5+n[0]*6;\n        d2 = 11 - (d2 % 11);\n        if (d2>=10){\n            d2 = 0;\n        }\n        return ''+n[0]+n[1]+'.'+n[2]+n[3]+n[4]+'.'+n[5]+n[6]+n[7]+'/0001-'+d1+d2;\n    };\n\n    // -- End Miscellaneous --\n\n    Chance.prototype.mersenne_twister = function (seed) {\n        return new MersenneTwister(seed);\n    };\n\n    Chance.prototype.blueimp_md5 = function () {\n        return new BlueImpMD5();\n    };\n\n    // Mersenne Twister from https://gist.github.com/banksean/300494\n    /*\n       A C-program for MT19937, with initialization improved 2002/1/26.\n       Coded by Takuji Nishimura and Makoto Matsumoto.\n\n       Before using, initialize the state by using init_genrand(seed)\n       or init_by_array(init_key, key_length).\n\n       Copyright (C) 1997 - 2002, Makoto Matsumoto and Takuji Nishimura,\n       All rights reserved.\n\n       Redistribution and use in source and binary forms, with or without\n       modification, are permitted provided that the following conditions\n       are met:\n\n       1. Redistributions of source code must retain the above copyright\n       notice, this list of conditions and the following disclaimer.\n\n       2. Redistributions in binary form must reproduce the above copyright\n       notice, this list of conditions and the following disclaimer in the\n       documentation and/or other materials provided with the distribution.\n\n       3. The names of its contributors may not be used to endorse or promote\n       products derived from this software without specific prior written\n       permission.\n\n       THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS\n       \"AS IS\" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT\n       LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR\n       A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR\n       CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,\n       EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,\n       PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR\n       PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF\n       LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n       NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS\n       SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n\n\n       Any feedback is very welcome.\n       http://www.math.sci.hiroshima-u.ac.jp/~m-mat/MT/emt.html\n       email: m-mat @ math.sci.hiroshima-u.ac.jp (remove space)\n     */\n    var MersenneTwister = function (seed) {\n        if (seed === undefined) {\n            // kept random number same size as time used previously to ensure no unexpected results downstream\n            seed = Math.floor(Math.random()*Math.pow(10,13));\n        }\n        /* Period parameters */\n        this.N = 624;\n        this.M = 397;\n        this.MATRIX_A = 0x9908b0df;   /* constant vector a */\n        this.UPPER_MASK = 0x80000000; /* most significant w-r bits */\n        this.LOWER_MASK = 0x7fffffff; /* least significant r bits */\n\n        this.mt = new Array(this.N); /* the array for the state vector */\n        this.mti = this.N + 1; /* mti==N + 1 means mt[N] is not initialized */\n\n        this.init_genrand(seed);\n    };\n\n    /* initializes mt[N] with a seed */\n    MersenneTwister.prototype.init_genrand = function (s) {\n        this.mt[0] = s >>> 0;\n        for (this.mti = 1; this.mti < this.N; this.mti++) {\n            s = this.mt[this.mti - 1] ^ (this.mt[this.mti - 1] >>> 30);\n            this.mt[this.mti] = (((((s & 0xffff0000) >>> 16) * 1812433253) << 16) + (s & 0x0000ffff) * 1812433253) + this.mti;\n            /* See Knuth TAOCP Vol2. 3rd Ed. P.106 for multiplier. */\n            /* In the previous versions, MSBs of the seed affect   */\n            /* only MSBs of the array mt[].                        */\n            /* 2002/01/09 modified by Makoto Matsumoto             */\n            this.mt[this.mti] >>>= 0;\n            /* for >32 bit machines */\n        }\n    };\n\n    /* initialize by an array with array-length */\n    /* init_key is the array for initializing keys */\n    /* key_length is its length */\n    /* slight change for C++, 2004/2/26 */\n    MersenneTwister.prototype.init_by_array = function (init_key, key_length) {\n        var i = 1, j = 0, k, s;\n        this.init_genrand(19650218);\n        k = (this.N > key_length ? this.N : key_length);\n        for (; k; k--) {\n            s = this.mt[i - 1] ^ (this.mt[i - 1] >>> 30);\n            this.mt[i] = (this.mt[i] ^ (((((s & 0xffff0000) >>> 16) * 1664525) << 16) + ((s & 0x0000ffff) * 1664525))) + init_key[j] + j; /* non linear */\n            this.mt[i] >>>= 0; /* for WORDSIZE > 32 machines */\n            i++;\n            j++;\n            if (i >= this.N) { this.mt[0] = this.mt[this.N - 1]; i = 1; }\n            if (j >= key_length) { j = 0; }\n        }\n        for (k = this.N - 1; k; k--) {\n            s = this.mt[i - 1] ^ (this.mt[i - 1] >>> 30);\n            this.mt[i] = (this.mt[i] ^ (((((s & 0xffff0000) >>> 16) * 1566083941) << 16) + (s & 0x0000ffff) * 1566083941)) - i; /* non linear */\n            this.mt[i] >>>= 0; /* for WORDSIZE > 32 machines */\n            i++;\n            if (i >= this.N) { this.mt[0] = this.mt[this.N - 1]; i = 1; }\n        }\n\n        this.mt[0] = 0x80000000; /* MSB is 1; assuring non-zero initial array */\n    };\n\n    /* generates a random number on [0,0xffffffff]-interval */\n    MersenneTwister.prototype.genrand_int32 = function () {\n        var y;\n        var mag01 = new Array(0x0, this.MATRIX_A);\n        /* mag01[x] = x * MATRIX_A  for x=0,1 */\n\n        if (this.mti >= this.N) { /* generate N words at one time */\n            var kk;\n\n            if (this.mti === this.N + 1) {   /* if init_genrand() has not been called, */\n                this.init_genrand(5489); /* a default initial seed is used */\n            }\n            for (kk = 0; kk < this.N - this.M; kk++) {\n                y = (this.mt[kk]&this.UPPER_MASK)|(this.mt[kk + 1]&this.LOWER_MASK);\n                this.mt[kk] = this.mt[kk + this.M] ^ (y >>> 1) ^ mag01[y & 0x1];\n            }\n            for (;kk < this.N - 1; kk++) {\n                y = (this.mt[kk]&this.UPPER_MASK)|(this.mt[kk + 1]&this.LOWER_MASK);\n                this.mt[kk] = this.mt[kk + (this.M - this.N)] ^ (y >>> 1) ^ mag01[y & 0x1];\n            }\n            y = (this.mt[this.N - 1]&this.UPPER_MASK)|(this.mt[0]&this.LOWER_MASK);\n            this.mt[this.N - 1] = this.mt[this.M - 1] ^ (y >>> 1) ^ mag01[y & 0x1];\n\n            this.mti = 0;\n        }\n\n        y = this.mt[this.mti++];\n\n        /* Tempering */\n        y ^= (y >>> 11);\n        y ^= (y << 7) & 0x9d2c5680;\n        y ^= (y << 15) & 0xefc60000;\n        y ^= (y >>> 18);\n\n        return y >>> 0;\n    };\n\n    /* generates a random number on [0,0x7fffffff]-interval */\n    MersenneTwister.prototype.genrand_int31 = function () {\n        return (this.genrand_int32() >>> 1);\n    };\n\n    /* generates a random number on [0,1]-real-interval */\n    MersenneTwister.prototype.genrand_real1 = function () {\n        return this.genrand_int32() * (1.0 / 4294967295.0);\n        /* divided by 2^32-1 */\n    };\n\n    /* generates a random number on [0,1)-real-interval */\n    MersenneTwister.prototype.random = function () {\n        return this.genrand_int32() * (1.0 / 4294967296.0);\n        /* divided by 2^32 */\n    };\n\n    /* generates a random number on (0,1)-real-interval */\n    MersenneTwister.prototype.genrand_real3 = function () {\n        return (this.genrand_int32() + 0.5) * (1.0 / 4294967296.0);\n        /* divided by 2^32 */\n    };\n\n    /* generates a random number on [0,1) with 53-bit resolution*/\n    MersenneTwister.prototype.genrand_res53 = function () {\n        var a = this.genrand_int32()>>>5, b = this.genrand_int32()>>>6;\n        return (a * 67108864.0 + b) * (1.0 / 9007199254740992.0);\n    };\n\n    // BlueImp MD5 hashing algorithm from https://github.com/blueimp/JavaScript-MD5\n    var BlueImpMD5 = function () {};\n\n    BlueImpMD5.prototype.VERSION = '1.0.1';\n\n    /*\n    * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n    * to work around bugs in some JS interpreters.\n    */\n    BlueImpMD5.prototype.safe_add = function safe_add(x, y) {\n        var lsw = (x & 0xFFFF) + (y & 0xFFFF),\n            msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n        return (msw << 16) | (lsw & 0xFFFF);\n    };\n\n    /*\n    * Bitwise rotate a 32-bit number to the left.\n    */\n    BlueImpMD5.prototype.bit_roll = function (num, cnt) {\n        return (num << cnt) | (num >>> (32 - cnt));\n    };\n\n    /*\n    * These functions implement the five basic operations the algorithm uses.\n    */\n    BlueImpMD5.prototype.md5_cmn = function (q, a, b, x, s, t) {\n        return this.safe_add(this.bit_roll(this.safe_add(this.safe_add(a, q), this.safe_add(x, t)), s), b);\n    };\n    BlueImpMD5.prototype.md5_ff = function (a, b, c, d, x, s, t) {\n        return this.md5_cmn((b & c) | ((~b) & d), a, b, x, s, t);\n    };\n    BlueImpMD5.prototype.md5_gg = function (a, b, c, d, x, s, t) {\n        return this.md5_cmn((b & d) | (c & (~d)), a, b, x, s, t);\n    };\n    BlueImpMD5.prototype.md5_hh = function (a, b, c, d, x, s, t) {\n        return this.md5_cmn(b ^ c ^ d, a, b, x, s, t);\n    };\n    BlueImpMD5.prototype.md5_ii = function (a, b, c, d, x, s, t) {\n        return this.md5_cmn(c ^ (b | (~d)), a, b, x, s, t);\n    };\n\n    /*\n    * Calculate the MD5 of an array of little-endian words, and a bit length.\n    */\n    BlueImpMD5.prototype.binl_md5 = function (x, len) {\n        /* append padding */\n        x[len >> 5] |= 0x80 << (len % 32);\n        x[(((len + 64) >>> 9) << 4) + 14] = len;\n\n        var i, olda, oldb, oldc, oldd,\n            a =  1732584193,\n            b = -271733879,\n            c = -1732584194,\n            d =  271733878;\n\n        for (i = 0; i < x.length; i += 16) {\n            olda = a;\n            oldb = b;\n            oldc = c;\n            oldd = d;\n\n            a = this.md5_ff(a, b, c, d, x[i],       7, -680876936);\n            d = this.md5_ff(d, a, b, c, x[i +  1], 12, -389564586);\n            c = this.md5_ff(c, d, a, b, x[i +  2], 17,  606105819);\n            b = this.md5_ff(b, c, d, a, x[i +  3], 22, -1044525330);\n            a = this.md5_ff(a, b, c, d, x[i +  4],  7, -176418897);\n            d = this.md5_ff(d, a, b, c, x[i +  5], 12,  1200080426);\n            c = this.md5_ff(c, d, a, b, x[i +  6], 17, -1473231341);\n            b = this.md5_ff(b, c, d, a, x[i +  7], 22, -45705983);\n            a = this.md5_ff(a, b, c, d, x[i +  8],  7,  1770035416);\n            d = this.md5_ff(d, a, b, c, x[i +  9], 12, -1958414417);\n            c = this.md5_ff(c, d, a, b, x[i + 10], 17, -42063);\n            b = this.md5_ff(b, c, d, a, x[i + 11], 22, -1990404162);\n            a = this.md5_ff(a, b, c, d, x[i + 12],  7,  1804603682);\n            d = this.md5_ff(d, a, b, c, x[i + 13], 12, -40341101);\n            c = this.md5_ff(c, d, a, b, x[i + 14], 17, -1502002290);\n            b = this.md5_ff(b, c, d, a, x[i + 15], 22,  1236535329);\n\n            a = this.md5_gg(a, b, c, d, x[i +  1],  5, -165796510);\n            d = this.md5_gg(d, a, b, c, x[i +  6],  9, -1069501632);\n            c = this.md5_gg(c, d, a, b, x[i + 11], 14,  643717713);\n            b = this.md5_gg(b, c, d, a, x[i],      20, -373897302);\n            a = this.md5_gg(a, b, c, d, x[i +  5],  5, -701558691);\n            d = this.md5_gg(d, a, b, c, x[i + 10],  9,  38016083);\n            c = this.md5_gg(c, d, a, b, x[i + 15], 14, -660478335);\n            b = this.md5_gg(b, c, d, a, x[i +  4], 20, -405537848);\n            a = this.md5_gg(a, b, c, d, x[i +  9],  5,  568446438);\n            d = this.md5_gg(d, a, b, c, x[i + 14],  9, -1019803690);\n            c = this.md5_gg(c, d, a, b, x[i +  3], 14, -187363961);\n            b = this.md5_gg(b, c, d, a, x[i +  8], 20,  1163531501);\n            a = this.md5_gg(a, b, c, d, x[i + 13],  5, -1444681467);\n            d = this.md5_gg(d, a, b, c, x[i +  2],  9, -51403784);\n            c = this.md5_gg(c, d, a, b, x[i +  7], 14,  1735328473);\n            b = this.md5_gg(b, c, d, a, x[i + 12], 20, -1926607734);\n\n            a = this.md5_hh(a, b, c, d, x[i +  5],  4, -378558);\n            d = this.md5_hh(d, a, b, c, x[i +  8], 11, -2022574463);\n            c = this.md5_hh(c, d, a, b, x[i + 11], 16,  1839030562);\n            b = this.md5_hh(b, c, d, a, x[i + 14], 23, -35309556);\n            a = this.md5_hh(a, b, c, d, x[i +  1],  4, -1530992060);\n            d = this.md5_hh(d, a, b, c, x[i +  4], 11,  1272893353);\n            c = this.md5_hh(c, d, a, b, x[i +  7], 16, -155497632);\n            b = this.md5_hh(b, c, d, a, x[i + 10], 23, -1094730640);\n            a = this.md5_hh(a, b, c, d, x[i + 13],  4,  681279174);\n            d = this.md5_hh(d, a, b, c, x[i],      11, -358537222);\n            c = this.md5_hh(c, d, a, b, x[i +  3], 16, -722521979);\n            b = this.md5_hh(b, c, d, a, x[i +  6], 23,  76029189);\n            a = this.md5_hh(a, b, c, d, x[i +  9],  4, -640364487);\n            d = this.md5_hh(d, a, b, c, x[i + 12], 11, -421815835);\n            c = this.md5_hh(c, d, a, b, x[i + 15], 16,  530742520);\n            b = this.md5_hh(b, c, d, a, x[i +  2], 23, -995338651);\n\n            a = this.md5_ii(a, b, c, d, x[i],       6, -198630844);\n            d = this.md5_ii(d, a, b, c, x[i +  7], 10,  1126891415);\n            c = this.md5_ii(c, d, a, b, x[i + 14], 15, -1416354905);\n            b = this.md5_ii(b, c, d, a, x[i +  5], 21, -57434055);\n            a = this.md5_ii(a, b, c, d, x[i + 12],  6,  1700485571);\n            d = this.md5_ii(d, a, b, c, x[i +  3], 10, -1894986606);\n            c = this.md5_ii(c, d, a, b, x[i + 10], 15, -1051523);\n            b = this.md5_ii(b, c, d, a, x[i +  1], 21, -2054922799);\n            a = this.md5_ii(a, b, c, d, x[i +  8],  6,  1873313359);\n            d = this.md5_ii(d, a, b, c, x[i + 15], 10, -30611744);\n            c = this.md5_ii(c, d, a, b, x[i +  6], 15, -1560198380);\n            b = this.md5_ii(b, c, d, a, x[i + 13], 21,  1309151649);\n            a = this.md5_ii(a, b, c, d, x[i +  4],  6, -145523070);\n            d = this.md5_ii(d, a, b, c, x[i + 11], 10, -1120210379);\n            c = this.md5_ii(c, d, a, b, x[i +  2], 15,  718787259);\n            b = this.md5_ii(b, c, d, a, x[i +  9], 21, -343485551);\n\n            a = this.safe_add(a, olda);\n            b = this.safe_add(b, oldb);\n            c = this.safe_add(c, oldc);\n            d = this.safe_add(d, oldd);\n        }\n        return [a, b, c, d];\n    };\n\n    /*\n    * Convert an array of little-endian words to a string\n    */\n    BlueImpMD5.prototype.binl2rstr = function (input) {\n        var i,\n            output = '';\n        for (i = 0; i < input.length * 32; i += 8) {\n            output += String.fromCharCode((input[i >> 5] >>> (i % 32)) & 0xFF);\n        }\n        return output;\n    };\n\n    /*\n    * Convert a raw string to an array of little-endian words\n    * Characters >255 have their high-byte silently ignored.\n    */\n    BlueImpMD5.prototype.rstr2binl = function (input) {\n        var i,\n            output = [];\n        output[(input.length >> 2) - 1] = undefined;\n        for (i = 0; i < output.length; i += 1) {\n            output[i] = 0;\n        }\n        for (i = 0; i < input.length * 8; i += 8) {\n            output[i >> 5] |= (input.charCodeAt(i / 8) & 0xFF) << (i % 32);\n        }\n        return output;\n    };\n\n    /*\n    * Calculate the MD5 of a raw string\n    */\n    BlueImpMD5.prototype.rstr_md5 = function (s) {\n        return this.binl2rstr(this.binl_md5(this.rstr2binl(s), s.length * 8));\n    };\n\n    /*\n    * Calculate the HMAC-MD5, of a key and some data (raw strings)\n    */\n    BlueImpMD5.prototype.rstr_hmac_md5 = function (key, data) {\n        var i,\n            bkey = this.rstr2binl(key),\n            ipad = [],\n            opad = [],\n            hash;\n        ipad[15] = opad[15] = undefined;\n        if (bkey.length > 16) {\n            bkey = this.binl_md5(bkey, key.length * 8);\n        }\n        for (i = 0; i < 16; i += 1) {\n            ipad[i] = bkey[i] ^ 0x36363636;\n            opad[i] = bkey[i] ^ 0x5C5C5C5C;\n        }\n        hash = this.binl_md5(ipad.concat(this.rstr2binl(data)), 512 + data.length * 8);\n        return this.binl2rstr(this.binl_md5(opad.concat(hash), 512 + 128));\n    };\n\n    /*\n    * Convert a raw string to a hex string\n    */\n    BlueImpMD5.prototype.rstr2hex = function (input) {\n        var hex_tab = '**********abcdef',\n            output = '',\n            x,\n            i;\n        for (i = 0; i < input.length; i += 1) {\n            x = input.charCodeAt(i);\n            output += hex_tab.charAt((x >>> 4) & 0x0F) +\n                hex_tab.charAt(x & 0x0F);\n        }\n        return output;\n    };\n\n    /*\n    * Encode a string as utf-8\n    */\n    BlueImpMD5.prototype.str2rstr_utf8 = function (input) {\n        return unescape(encodeURIComponent(input));\n    };\n\n    /*\n    * Take string arguments and return either raw or hex encoded strings\n    */\n    BlueImpMD5.prototype.raw_md5 = function (s) {\n        return this.rstr_md5(this.str2rstr_utf8(s));\n    };\n    BlueImpMD5.prototype.hex_md5 = function (s) {\n        return this.rstr2hex(this.raw_md5(s));\n    };\n    BlueImpMD5.prototype.raw_hmac_md5 = function (k, d) {\n        return this.rstr_hmac_md5(this.str2rstr_utf8(k), this.str2rstr_utf8(d));\n    };\n    BlueImpMD5.prototype.hex_hmac_md5 = function (k, d) {\n        return this.rstr2hex(this.raw_hmac_md5(k, d));\n    };\n\n    BlueImpMD5.prototype.md5 = function (string, key, raw) {\n        if (!key) {\n            if (!raw) {\n                return this.hex_md5(string);\n            }\n\n            return this.raw_md5(string);\n        }\n\n        if (!raw) {\n            return this.hex_hmac_md5(key, string);\n        }\n\n        return this.raw_hmac_md5(key, string);\n    };\n\n    // CommonJS module\n    if (typeof exports !== 'undefined') {\n        if (typeof module !== 'undefined' && module.exports) {\n            exports = module.exports = Chance;\n        }\n        exports.Chance = Chance;\n    }\n\n    // Register as an anonymous AMD module\n    if (typeof define === 'function' && define.amd) {\n        define([], function () {\n            return Chance;\n        });\n    }\n\n    // if there is a importsScrips object define chance for worker\n    // allows worker to use full Chance functionality with seed\n    if (typeof importScripts !== 'undefined') {\n        chance = new Chance();\n        self.Chance = Chance;\n    }\n\n    // If there is a window object, that at least has a document property,\n    // instantiate and define chance on the window\n    if (typeof window === \"object\" && typeof window.document === \"object\") {\n        window.Chance = Chance;\n        window.chance = new Chance();\n    }\n})();\n"]}