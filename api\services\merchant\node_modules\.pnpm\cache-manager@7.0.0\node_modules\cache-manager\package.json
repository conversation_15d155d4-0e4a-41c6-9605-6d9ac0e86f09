{"name": "cache-manager", "version": "7.0.0", "description": "Cache Manager for Node.js", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "files": ["dist", "LICENSE"], "repository": {"type": "git", "url": "git+https://github.com/jaredwray/cacheable.git", "directory": "packages/cache-manager"}, "keywords": ["cache", "caching", "cache manager", "node", "node.js", "in-memory cache", "redis", "memcached", "multi-store cache", "ttl", "caching layer", "cache abstraction", "cache middleware", "cache strategies", "cache wrapper"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "dependencies": {"keyv": "^5.3.3"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@keyv/redis": "^4.4.0", "@types/node": "^22.15.30", "@vitest/coverage-v8": "^3.2.2", "@vitest/spy": "^3.2.2", "cache-manager-redis-yet": "^5.1.5", "rimraf": "^6.0.1", "tsup": "^8.5.0", "typescript": "^5.8.3", "vitest": "^3.2.2", "xo": "^1.0.5", "cacheable": "^1.9.0"}, "xo": {"rules": {"@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-assignment": "off"}}, "scripts": {"clean": "rimraf ./dist ./coverage ./node_modules", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest run", "build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "prepublish": "pnpm run build"}}