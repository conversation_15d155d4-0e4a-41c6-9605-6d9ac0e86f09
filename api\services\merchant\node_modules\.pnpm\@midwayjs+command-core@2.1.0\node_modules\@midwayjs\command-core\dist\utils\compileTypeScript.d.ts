import { DiagnosticRelatedInformation } from 'typescript';
export declare const compileTypeScript: (options: {
    baseDir: string;
    sourceDir?: string;
    outDir?: string;
    tsOptions?: any;
    coverOptions?: any;
}) => Promise<{
    fileNames: any;
    options: any;
    errors: any[];
    necessaryErrors: any[];
}>;
export declare const formatTsError: (baseDir: string, error: DiagnosticRelatedInformation) => {
    message: string;
    path: string;
};
export declare const readJson: (path: string) => Promise<any>;
export declare const getTsConfig: (baseDir: string) => Promise<any>;
//# sourceMappingURL=compileTypeScript.d.ts.map