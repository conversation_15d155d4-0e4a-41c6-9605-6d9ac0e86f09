{"version": 3, "sources": ["../../src/decorator/columns/DeleteDateColumn.ts"], "names": [], "mappings": ";;AAQA,4CASC;AAjBD,2CAAsD;AAItD;;;GAGG;AACH,SAAgB,gBAAgB,CAAC,OAAuB;IACpD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,OAAO,IAAI,EAAE;SACH,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "DeleteDateColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * This column will store a delete date of the soft-deleted object.\n * This date is being updated each time you soft-delete the object.\n */\nexport function DeleteDateColumn(options?: ColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"deleteDate\",\n            options: options || {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}