"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayPriorityManager = exports.DEFAULT_PRIORITY = void 0;
const decorator_1 = require("../decorator");
const interface_1 = require("../interface");
exports.DEFAULT_PRIORITY = {
    L1: 'High',
    L2: 'Medium',
    L3: 'Low',
};
let MidwayPriorityManager = class MidwayPriorityManager {
    constructor() {
        this.priorityList = exports.DEFAULT_PRIORITY;
        this.defaultPriority = exports.DEFAULT_PRIORITY.L2;
    }
    getCurrentPriorityList() {
        return this.priorityList;
    }
    getDefaultPriority() {
        return this.defaultPriority;
    }
    isHighPriority(priority = exports.DEFAULT_PRIORITY.L2) {
        return priority === exports.DEFAULT_PRIORITY.L1;
    }
    isMediumPriority(priority = exports.DEFAULT_PRIORITY.L2) {
        return priority === exports.DEFAULT_PRIORITY.L2;
    }
    isLowPriority(priority = exports.DEFAULT_PRIORITY.L2) {
        return priority === exports.DEFAULT_PRIORITY.L3;
    }
    getPriority(priority) {
        return priority || this.getDefaultPriority();
    }
};
MidwayPriorityManager = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayPriorityManager);
exports.MidwayPriorityManager = MidwayPriorityManager;
//# sourceMappingURL=priorityManager.js.map