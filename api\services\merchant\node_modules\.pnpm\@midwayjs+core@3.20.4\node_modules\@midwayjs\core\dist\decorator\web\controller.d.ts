import { MiddlewareParamArray } from '../../interface';
export interface ControllerOption {
    prefix: string;
    routerOptions?: {
        sensitive?: boolean;
        middleware?: MiddlewareParamArray;
        alias?: string[];
        description?: string;
        tagName?: string;
        ignoreGlobalPrefix?: boolean;
    };
}
export declare function Controller(prefix?: string, routerOptions?: {
    sensitive?: boolean;
    middleware?: MiddlewareParamArray;
    description?: string;
    tagName?: string;
    ignoreGlobalPrefix?: boolean;
}): ClassDecorator;
//# sourceMappingURL=controller.d.ts.map