# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.0.2](https://github.com/inspect-js/is-object/compare/v1.0.1...v1.0.2) - 2020-12-02

### Merged

- Expanded tests to check for undefined, boolean values, and functions. [`#2`](https://github.com/inspect-js/is-object/pull/2)

### Commits

- [Tests] migrate tests to Github Actions [`1503b2b`](https://github.com/inspect-js/is-object/commit/1503b2beabea6408504e5ed95aa0c6a69683313f)
- [Dev Deps] add `eslint`, drop `jscs` [`0b33130`](https://github.com/inspect-js/is-object/commit/0b33130d6d1b77a4c6f184e760a5dd28e65ef105)
- [meta] add `auto-changelog` [`4c1af71`](https://github.com/inspect-js/is-object/commit/4c1af7108d1498119d8be4014b26f92a6823ae36)
- Use my standard jscs.json. [`8e02200`](https://github.com/inspect-js/is-object/commit/8e0220046caee3243be8312e563646758f842486)
- [Tests] use shared travis-ci configs [`ffaa4b7`](https://github.com/inspect-js/is-object/commit/ffaa4b7503e0df857338ce2359de9845af83cd98)
- Update `tape`, `covert`, `jscs` [`23ae901`](https://github.com/inspect-js/is-object/commit/23ae9016a087da5d433b27228e7d4d066b5f7223)
- [readme] fix repo URLs, remove testling [`f48732a`](https://github.com/inspect-js/is-object/commit/f48732a6839f9cf70443759d02e4a58cb7d90e53)
- [meta] avoid publishing github workflows [`b3a84ee`](https://github.com/inspect-js/is-object/commit/b3a84ee7895e212b626b174be6b248d39b591fca)
- [Tests] run `nyc` on all tests [`a03b699`](https://github.com/inspect-js/is-object/commit/a03b699fdb3b7a78832cfa8005c803f6c455ea11)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `tape` [`db89fc0`](https://github.com/inspect-js/is-object/commit/db89fc0fc9b8366130b7e605990f653ae53cf8ff)
- Update `tape`, `jscs` [`bcb3ac9`](https://github.com/inspect-js/is-object/commit/bcb3ac9f9eedd0bfcc6988f0a0001d4162c7cba3)
- Expanded tests to check for undefined, boolean values and functions. [`a12751e`](https://github.com/inspect-js/is-object/commit/a12751e6a7050331970e7a82cd961ad18473a6c9)
- Test up to `io.js` `v2.0` [`74d46d7`](https://github.com/inspect-js/is-object/commit/74d46d7f52c9e227068dbef454c97d9c5a7b2442)
- [actions] add automatic rebasing / merge commit blocking [`b5505c3`](https://github.com/inspect-js/is-object/commit/b5505c34800a197724ea2bfb4445a13fce5cd934)
- [actions] add "Allow Edits" workflow [`c79aee4`](https://github.com/inspect-js/is-object/commit/c79aee4d0ecc1aba4be0a1a23d8e19ddf87ca5e4)
- Naming deps/devDeps URLs in README [`1095633`](https://github.com/inspect-js/is-object/commit/1095633d062deea6300c6e35be6e7142a3dc018d)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog` [`5db6e17`](https://github.com/inspect-js/is-object/commit/5db6e1783d638c05a539621ae652d32fbc34c6e1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape`; add `safe-publish-latest` [`ceaf8ab`](https://github.com/inspect-js/is-object/commit/ceaf8ab48b0b8d7e300dce5ad40a5eafbe60df3a)
- Test up to `io.js` `v2.2` [`cf9f70e`](https://github.com/inspect-js/is-object/commit/cf9f70e623a7e0e78c34d056bdfb99d8d5133b69)
- Naming contributor URLs in README [`41b759b`](https://github.com/inspect-js/is-object/commit/41b759befa23b1c67bc573699188d8c930fa96f1)
- Test up to `io.js` `v3.0` [`29cd85b`](https://github.com/inspect-js/is-object/commit/29cd85ba942a33112af94c994c23e27e33f9d4ad)
- Only apps should have lockfiles [`5510e47`](https://github.com/inspect-js/is-object/commit/5510e47639ccb51f64ab82bd14f75d4b4ee7c625)
- All grade A-supported `node`/`iojs` versions now ship with an `npm` that understands `^`. [`849b718`](https://github.com/inspect-js/is-object/commit/849b718ed57979757af84e637a3cd24871e61a5d)
- Run `travis-ci` tests on `iojs` and `0.12`; allow `0.8` failures; speed up builds. [`655c5f7`](https://github.com/inspect-js/is-object/commit/655c5f7cdcb7f925fee00a44905ff018002049b4)
- Naming npm package URL in README [`af52357`](https://github.com/inspect-js/is-object/commit/af5235703e31bdecf39f663fe647e4ba1132191e)
- Naming testling-ci URLs in README [`70ab476`](https://github.com/inspect-js/is-object/commit/70ab476401e4122fefc45612ac549b8b791fbb7f)
- Naming travis-ci URLs in README [`08175df`](https://github.com/inspect-js/is-object/commit/08175dffa933ce93e686ee3e48e05af33e13349a)
- [Dev Deps] update `auto-changelog`, `tape` [`0f0dde7`](https://github.com/inspect-js/is-object/commit/0f0dde7501e280cc262d4bfe375a98965c21bbd6)
- [Dev Deps] update `covert`, `tape` [`6148d0e`](https://github.com/inspect-js/is-object/commit/6148d0e857aa4e4f167a5c8892f748b0701caf8b)
- Update `tape`, `jscs` [`dff288a`](https://github.com/inspect-js/is-object/commit/dff288a20b1382836d100d3caf9df3c80fe6290a)
- Update `tape`, `jscs` [`ff04f70`](https://github.com/inspect-js/is-object/commit/ff04f70e14a4844f6446df3ed5cc4b44aaa64dc2)
- Update `jscs` [`75e8791`](https://github.com/inspect-js/is-object/commit/75e879153be96fd7429c1a933b6caffd4d4f3316)
- Update `jscs`, `tape` [`cc7e221`](https://github.com/inspect-js/is-object/commit/cc7e221724339942d13ba52d6541c633e1a160ca)
- Update `tape`, `jscs` [`be32959`](https://github.com/inspect-js/is-object/commit/be3295953a66d6f305be9d5c1f003ae0df0d8c95)
- Use consistent quotes. [`0dedc4b`](https://github.com/inspect-js/is-object/commit/0dedc4b8656063e99b2780b3b989fa404b9242be)
- Naming npm badge PNG in README [`0376b83`](https://github.com/inspect-js/is-object/commit/0376b835c57c93a4e10d99beb7b5be382266faa7)
- Naming npm version badge SVG in README [`fe00aab`](https://github.com/inspect-js/is-object/commit/fe00aab6bdc2ff718749f4ab4327d97f4616a382)
- [readme] remove travis badge [`028ff10`](https://github.com/inspect-js/is-object/commit/028ff106f6862a89adbcf6a0b4acd894f90acdc2)
- [Tests] add missing `lint` script [`f431139`](https://github.com/inspect-js/is-object/commit/f431139c39b7b3b7cd525994e8ae5402db78792b)
- [Dev Deps] update `auto-changelog`; add `aud` [`ad0b964`](https://github.com/inspect-js/is-object/commit/ad0b9648c626edfc9bf66f2ec0322b4821940a56)
- [meta] add `funding` field [`ab362a6`](https://github.com/inspect-js/is-object/commit/ab362a630fd1baef26a990cea3f9873723117e74)
- [actions] update rebase action to use checkout v2 [`592539d`](https://github.com/inspect-js/is-object/commit/592539d9b5a047d1fb4161a0b0dea091ddee057e)
- [actions] switch Automatic Rebase workflow to `pull_request_target` event [`f26329d`](https://github.com/inspect-js/is-object/commit/f26329daab3387e947a642d73850e56570b32741)
- [Tests] only audit prod deps [`58efaae`](https://github.com/inspect-js/is-object/commit/58efaaeecd35df9903df3649e4235f2514a31cd3)
- Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG. [`120491a`](https://github.com/inspect-js/is-object/commit/120491aefe3c96e235d9da96cd00c6e77fb23819)
- Update `tape` [`99ccffe`](https://github.com/inspect-js/is-object/commit/99ccffe074de63562c007ebc35e9ed808ce3ec11)
- Lock covert to v1.0.0. [`9d03921`](https://github.com/inspect-js/is-object/commit/9d039218b89667363910689661fde7c3934e4055)
- Updating jscs [`a72781e`](https://github.com/inspect-js/is-object/commit/a72781e070f35be6a184418f9d649146bad82035)
- Updating tape [`31aabd0`](https://github.com/inspect-js/is-object/commit/31aabd0451663cd9495ea06d7aa26a8e51ba0532)
- Fixing indentation. [`4c3e061`](https://github.com/inspect-js/is-object/commit/4c3e0619d94937a5d9507ced13f97436962a3c8e)
- Updating jscs [`368053e`](https://github.com/inspect-js/is-object/commit/368053e0f2d8179d9538e91339f7c43d270ec4a0)

## [v1.0.1](https://github.com/inspect-js/is-object/compare/v1.0.0...v1.0.1) - 2014-09-02

### Commits

- Add license and download badges [`be23442`](https://github.com/inspect-js/is-object/commit/be23442865506ac6c93f42c2b6ab9ac64939901c)
- Updating jscs [`9924028`](https://github.com/inspect-js/is-object/commit/992402874cf123393243f6561b5a27cfdc6de88a)
- Adding a singular license key to package.json [`3f2bd51`](https://github.com/inspect-js/is-object/commit/3f2bd51596a586b222b2571ad3fb18fde9056ba9)
- Rename LICENSE file to proper spelling. [`a7cfdbf`](https://github.com/inspect-js/is-object/commit/a7cfdbf8d35835745bcfb5ea84b3a0bcf82a9315)

## [v1.0.0](https://github.com/inspect-js/is-object/compare/v0.1.2...v1.0.0) - 2014-08-18

### Merged

- Updating tape. [`#3`](https://github.com/inspect-js/is-object/pull/3)
- update dependencies [`#2`](https://github.com/inspect-js/is-object/pull/2)
- Update tape, test more browsers [`#1`](https://github.com/inspect-js/is-object/pull/1)

### Commits

- Making indentation consistent. [`aed32ae`](https://github.com/inspect-js/is-object/commit/aed32ae56e9400497f2833fe3cb1e12dde68144e)
- Removing unused example/build code. [`b894fe1`](https://github.com/inspect-js/is-object/commit/b894fe16e0f95203dab0baf7aacde6210f7528ba)
- Adding `npm run lint` [`676f803`](https://github.com/inspect-js/is-object/commit/676f8036f4d492b4fffdb26209f038ee7f6fc416)
- Using single quotes, semicolons, etc. [`96e7760`](https://github.com/inspect-js/is-object/commit/96e7760bc7f40715f43b1217a92cecbf08c68a9a)
- Updating contributors and repo location. [`836e536`](https://github.com/inspect-js/is-object/commit/836e536d11e1715364bccbf4dc1b60c467363a2b)
- Updating travis.yml [`7287d63`](https://github.com/inspect-js/is-object/commit/7287d63d359f916bf01fe1a2351d05bbc6f21872)
- Adding more testling browsers. [`8d1447b`](https://github.com/inspect-js/is-object/commit/8d1447bb2ece7a316a49df911d4345df6056dfa7)
- Updating Travis so it tests on node 0.10, not node 0.1. Also adding node 0.6 and removing unstable 0.9. [`0d773cd`](https://github.com/inspect-js/is-object/commit/0d773cd0d4592122e6d56b40ac7b2378a2cada24)
- Updating README examples [`57a796b`](https://github.com/inspect-js/is-object/commit/57a796b7dd23e02e03efcf14e2eefb5ec92de48b)
- Adding `npm run coverage` and `npm run coverage-quiet` [`228bb1b`](https://github.com/inspect-js/is-object/commit/228bb1ba10df7d560d8f1a37156708856c780acb)
- Use SVG instead of PNG for badges. [`c9c35fa`](https://github.com/inspect-js/is-object/commit/c9c35fac9a235762fe13408f0d4cd5637d4c4317)
- Add dev dependency badge. [`ef78c97`](https://github.com/inspect-js/is-object/commit/ef78c97ee90a1ddffcf197705dcae15f792478ac)
- Adding the npm module version. [`48ddbd5`](https://github.com/inspect-js/is-object/commit/48ddbd54d24b8d8ff8e073a0b890a76905f100a1)
- Updating tape and testem [`adb2d36`](https://github.com/inspect-js/is-object/commit/adb2d369a97953b4570fc2cd9ec4f5cdebd049da)
- Adding npm badge. [`35d55a8`](https://github.com/inspect-js/is-object/commit/35d55a8b2377a640734a0895eaa7d1502f4a48a7)
- Run linter as part of tests [`d9a416b`](https://github.com/inspect-js/is-object/commit/d9a416bef698bf2767c821e9891624194f6d2d86)
- Americanize spellings. [`c644aca`](https://github.com/inspect-js/is-object/commit/c644aca26f525b5d84431b447bf03132a014b7a0)
- Run code coverage as part of tests. [`8fa74b3`](https://github.com/inspect-js/is-object/commit/8fa74b32c5ba2c350858d81403968445701ab56e)
- Updating covert [`2c59d40`](https://github.com/inspect-js/is-object/commit/2c59d401a85493cdd6e1a3b34e7bd8f583664021)
- Updating `tape` [`e65b0d3`](https://github.com/inspect-js/is-object/commit/e65b0d35f7ff9bbaba32a9ce06b69782101a3477)
- `npm test` should be simple :-) [`df503c0`](https://github.com/inspect-js/is-object/commit/df503c0271665204cb4c42d573c2e5f7dfa78012)
- make travis run 0.10 [`3a198b9`](https://github.com/inspect-js/is-object/commit/3a198b92b9a169a385a6350db21e62b3a6b9f99f)
- travis should run node ./test [`33abb28`](https://github.com/inspect-js/is-object/commit/33abb286b229f2eb9576e2d2d64330ff2b337b7f)

## [v0.1.2](https://github.com/inspect-js/is-object/compare/v0.1.1...v0.1.2) - 2013-03-12

### Commits

- not private -.- [`b1ef3b8`](https://github.com/inspect-js/is-object/commit/b1ef3b83dc8cf4ad64484f5f6faa59897633ff9d)

## v0.1.1 - 2013-03-12

### Commits

- initial [`e62f915`](https://github.com/inspect-js/is-object/commit/e62f91558d2dabf271a100e91eae6c6204cd4641)
