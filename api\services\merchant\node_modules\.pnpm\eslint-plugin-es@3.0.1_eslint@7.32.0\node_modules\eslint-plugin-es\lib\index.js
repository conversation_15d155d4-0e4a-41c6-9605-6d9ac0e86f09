/**
 * DON'T EDIT THIS FILE.
 * This file was generated automatically by 'scripts/update-index.js'.
 */
"use strict"

module.exports = {
    configs: {
        "no-2019": {
            rules: {
                "es/no-json-superset": "error",
                "es/no-optional-catch-binding": "error",
                "es/no-regexp-unicode-property-escapes-2019": "error",
            },
        },
        "no-2018": {
            rules: {
                "es/no-async-iteration": "error",
                "es/no-malformed-template-literals": "error",
                "es/no-regexp-lookbehind-assertions": "error",
                "es/no-regexp-named-capture-groups": "error",
                "es/no-regexp-s-flag": "error",
                "es/no-regexp-unicode-property-escapes": "error",
                "es/no-rest-spread-properties": "error",
            },
        },
        "no-2017": {
            rules: {
                "es/no-async-functions": "error",
                "es/no-atomics": "error",
                "es/no-object-entries": "error",
                "es/no-object-getownpropertydescriptors": "error",
                "es/no-object-values": "error",
                "es/no-shared-array-buffer": "error",
                "es/no-trailing-function-commas": "error",
            },
        },
        "no-2016": {
            rules: {
                "es/no-exponential-operators": "error",
            },
        },
        "no-2015": {
            rules: {
                "es/no-array-from": "error",
                "es/no-array-of": "error",
                "es/no-arrow-functions": "error",
                "es/no-binary-numeric-literals": "error",
                "es/no-block-scoped-functions": "error",
                "es/no-block-scoped-variables": "error",
                "es/no-classes": "error",
                "es/no-computed-properties": "error",
                "es/no-default-parameters": "error",
                "es/no-destructuring": "error",
                "es/no-for-of-loops": "error",
                "es/no-generators": "error",
                "es/no-map": "error",
                "es/no-math-acosh": "error",
                "es/no-math-asinh": "error",
                "es/no-math-atanh": "error",
                "es/no-math-cbrt": "error",
                "es/no-math-clz32": "error",
                "es/no-math-cosh": "error",
                "es/no-math-expm1": "error",
                "es/no-math-fround": "error",
                "es/no-math-hypot": "error",
                "es/no-math-imul": "error",
                "es/no-math-log10": "error",
                "es/no-math-log1p": "error",
                "es/no-math-log2": "error",
                "es/no-math-sign": "error",
                "es/no-math-sinh": "error",
                "es/no-math-tanh": "error",
                "es/no-math-trunc": "error",
                "es/no-modules": "error",
                "es/no-new-target": "error",
                "es/no-number-epsilon": "error",
                "es/no-number-isfinite": "error",
                "es/no-number-isinteger": "error",
                "es/no-number-isnan": "error",
                "es/no-number-issafeinteger": "error",
                "es/no-number-maxsafeinteger": "error",
                "es/no-number-minsafeinteger": "error",
                "es/no-number-parsefloat": "error",
                "es/no-number-parseint": "error",
                "es/no-object-assign": "error",
                "es/no-object-getownpropertysymbols": "error",
                "es/no-object-is": "error",
                "es/no-object-setprototypeof": "error",
                "es/no-object-super-properties": "error",
                "es/no-octal-numeric-literals": "error",
                "es/no-promise": "error",
                "es/no-property-shorthands": "error",
                "es/no-proxy": "error",
                "es/no-reflect": "error",
                "es/no-regexp-u-flag": "error",
                "es/no-regexp-y-flag": "error",
                "es/no-rest-parameters": "error",
                "es/no-set": "error",
                "es/no-spread-elements": "error",
                "es/no-string-fromcodepoint": "error",
                "es/no-string-raw": "error",
                "es/no-subclassing-builtins": "error",
                "es/no-symbol": "error",
                "es/no-template-literals": "error",
                "es/no-typed-arrays": "error",
                "es/no-unicode-codepoint-escapes": "error",
                "es/no-weak-map": "error",
                "es/no-weak-set": "error",
            },
        },
        "no-5": {
            rules: {
                "es/no-accessor-properties": "error",
                "es/no-array-isarray": "error",
                "es/no-date-now": "error",
                "es/no-json": "error",
                "es/no-keyword-properties": "error",
                "es/no-object-defineproperties": "error",
                "es/no-object-defineproperty": "error",
                "es/no-object-freeze": "error",
                "es/no-object-getownpropertydescriptor": "error",
                "es/no-object-getownpropertynames": "error",
                "es/no-object-getprototypeof": "error",
                "es/no-object-isextensible": "error",
                "es/no-object-isfrozen": "error",
                "es/no-object-issealed": "error",
                "es/no-object-keys": "error",
                "es/no-object-preventextensions": "error",
                "es/no-object-seal": "error",
                "es/no-trailing-commas": "error",
            },
        },
    },
    rules: {
        "no-accessor-properties": require("./rules/no-accessor-properties"),
        "no-array-from": require("./rules/no-array-from"),
        "no-array-isarray": require("./rules/no-array-isarray"),
        "no-array-of": require("./rules/no-array-of"),
        "no-arrow-functions": require("./rules/no-arrow-functions"),
        "no-async-functions": require("./rules/no-async-functions"),
        "no-async-iteration": require("./rules/no-async-iteration"),
        "no-atomics": require("./rules/no-atomics"),
        "no-bigint": require("./rules/no-bigint"),
        "no-binary-numeric-literals": require("./rules/no-binary-numeric-literals"),
        "no-block-scoped-functions": require("./rules/no-block-scoped-functions"),
        "no-block-scoped-variables": require("./rules/no-block-scoped-variables"),
        "no-classes": require("./rules/no-classes"),
        "no-computed-properties": require("./rules/no-computed-properties"),
        "no-date-now": require("./rules/no-date-now"),
        "no-default-parameters": require("./rules/no-default-parameters"),
        "no-destructuring": require("./rules/no-destructuring"),
        "no-dynamic-import": require("./rules/no-dynamic-import"),
        "no-exponential-operators": require("./rules/no-exponential-operators"),
        "no-for-of-loops": require("./rules/no-for-of-loops"),
        "no-generators": require("./rules/no-generators"),
        "no-global-this": require("./rules/no-global-this"),
        "no-json-superset": require("./rules/no-json-superset"),
        "no-json": require("./rules/no-json"),
        "no-keyword-properties": require("./rules/no-keyword-properties"),
        "no-malformed-template-literals": require("./rules/no-malformed-template-literals"),
        "no-map": require("./rules/no-map"),
        "no-math-acosh": require("./rules/no-math-acosh"),
        "no-math-asinh": require("./rules/no-math-asinh"),
        "no-math-atanh": require("./rules/no-math-atanh"),
        "no-math-cbrt": require("./rules/no-math-cbrt"),
        "no-math-clz32": require("./rules/no-math-clz32"),
        "no-math-cosh": require("./rules/no-math-cosh"),
        "no-math-expm1": require("./rules/no-math-expm1"),
        "no-math-fround": require("./rules/no-math-fround"),
        "no-math-hypot": require("./rules/no-math-hypot"),
        "no-math-imul": require("./rules/no-math-imul"),
        "no-math-log10": require("./rules/no-math-log10"),
        "no-math-log1p": require("./rules/no-math-log1p"),
        "no-math-log2": require("./rules/no-math-log2"),
        "no-math-sign": require("./rules/no-math-sign"),
        "no-math-sinh": require("./rules/no-math-sinh"),
        "no-math-tanh": require("./rules/no-math-tanh"),
        "no-math-trunc": require("./rules/no-math-trunc"),
        "no-modules": require("./rules/no-modules"),
        "no-new-target": require("./rules/no-new-target"),
        "no-number-epsilon": require("./rules/no-number-epsilon"),
        "no-number-isfinite": require("./rules/no-number-isfinite"),
        "no-number-isinteger": require("./rules/no-number-isinteger"),
        "no-number-isnan": require("./rules/no-number-isnan"),
        "no-number-issafeinteger": require("./rules/no-number-issafeinteger"),
        "no-number-maxsafeinteger": require("./rules/no-number-maxsafeinteger"),
        "no-number-minsafeinteger": require("./rules/no-number-minsafeinteger"),
        "no-number-parsefloat": require("./rules/no-number-parsefloat"),
        "no-number-parseint": require("./rules/no-number-parseint"),
        "no-object-assign": require("./rules/no-object-assign"),
        "no-object-defineproperties": require("./rules/no-object-defineproperties"),
        "no-object-defineproperty": require("./rules/no-object-defineproperty"),
        "no-object-entries": require("./rules/no-object-entries"),
        "no-object-freeze": require("./rules/no-object-freeze"),
        "no-object-getownpropertydescriptor": require("./rules/no-object-getownpropertydescriptor"),
        "no-object-getownpropertydescriptors": require("./rules/no-object-getownpropertydescriptors"),
        "no-object-getownpropertynames": require("./rules/no-object-getownpropertynames"),
        "no-object-getownpropertysymbols": require("./rules/no-object-getownpropertysymbols"),
        "no-object-getprototypeof": require("./rules/no-object-getprototypeof"),
        "no-object-is": require("./rules/no-object-is"),
        "no-object-isextensible": require("./rules/no-object-isextensible"),
        "no-object-isfrozen": require("./rules/no-object-isfrozen"),
        "no-object-issealed": require("./rules/no-object-issealed"),
        "no-object-keys": require("./rules/no-object-keys"),
        "no-object-preventextensions": require("./rules/no-object-preventextensions"),
        "no-object-seal": require("./rules/no-object-seal"),
        "no-object-setprototypeof": require("./rules/no-object-setprototypeof"),
        "no-object-super-properties": require("./rules/no-object-super-properties"),
        "no-object-values": require("./rules/no-object-values"),
        "no-octal-numeric-literals": require("./rules/no-octal-numeric-literals"),
        "no-optional-catch-binding": require("./rules/no-optional-catch-binding"),
        "no-promise-all-settled": require("./rules/no-promise-all-settled"),
        "no-promise": require("./rules/no-promise"),
        "no-property-shorthands": require("./rules/no-property-shorthands"),
        "no-proxy": require("./rules/no-proxy"),
        "no-reflect": require("./rules/no-reflect"),
        "no-regexp-lookbehind-assertions": require("./rules/no-regexp-lookbehind-assertions"),
        "no-regexp-named-capture-groups": require("./rules/no-regexp-named-capture-groups"),
        "no-regexp-s-flag": require("./rules/no-regexp-s-flag"),
        "no-regexp-u-flag": require("./rules/no-regexp-u-flag"),
        "no-regexp-unicode-property-escapes-2019": require("./rules/no-regexp-unicode-property-escapes-2019"),
        "no-regexp-unicode-property-escapes": require("./rules/no-regexp-unicode-property-escapes"),
        "no-regexp-y-flag": require("./rules/no-regexp-y-flag"),
        "no-rest-parameters": require("./rules/no-rest-parameters"),
        "no-rest-spread-properties": require("./rules/no-rest-spread-properties"),
        "no-set": require("./rules/no-set"),
        "no-shared-array-buffer": require("./rules/no-shared-array-buffer"),
        "no-spread-elements": require("./rules/no-spread-elements"),
        "no-string-fromcodepoint": require("./rules/no-string-fromcodepoint"),
        "no-string-raw": require("./rules/no-string-raw"),
        "no-subclassing-builtins": require("./rules/no-subclassing-builtins"),
        "no-symbol": require("./rules/no-symbol"),
        "no-template-literals": require("./rules/no-template-literals"),
        "no-trailing-commas": require("./rules/no-trailing-commas"),
        "no-trailing-function-commas": require("./rules/no-trailing-function-commas"),
        "no-typed-arrays": require("./rules/no-typed-arrays"),
        "no-unicode-codepoint-escapes": require("./rules/no-unicode-codepoint-escapes"),
        "no-weak-map": require("./rules/no-weak-map"),
        "no-weak-set": require("./rules/no-weak-set"),
    },
}
