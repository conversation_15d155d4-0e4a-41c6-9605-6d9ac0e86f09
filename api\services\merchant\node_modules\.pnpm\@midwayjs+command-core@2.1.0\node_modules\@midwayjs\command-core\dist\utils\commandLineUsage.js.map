{"version": 3, "file": "commandLineUsage.js", "sourceRoot": "", "sources": ["../../src/utils/commandLineUsage.ts"], "names": [], "mappings": ";;;AAAO,MAAM,gBAAgB,GAAG,CAAC,OAAO,EAAE,MAAO,EAAE,EAAE;IACnD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,OAAO;aACX,GAAG,CAAC,OAAO,CAAC,EAAE;YACb,OAAO,IAAA,wBAAgB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAC;KACf;IACD,MAAM,aAAa,GAAG,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,KAAI,EAAE,CAAC;IAC3C,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,KAAI,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;QAEtE,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,KAAK,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;SACrD;QACD,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;KAC9D;IACD,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,MAAM,GAAG,CAAC,CAAC;IACf,IAAI,OAAO,CAAC,UAAU,EAAE;QACtB,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,MAAM,MAAM,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;YAC1E,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,EAAE;gBAC1B,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;aAC5B;YACD,WAAW,CAAC,IAAI,CAAC;gBACf,MAAM;gBACN,IAAI,EAAE,IAAI,CAAC,WAAW,IAAI,EAAE;aAC7B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;KACJ;IACD,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5B,MAAM,CAAC,IAAI,CACT,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CACzE,CAAC;IACJ,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,EAAE;QACxE,MAAM,CAAC,IAAI,CACT,IAAA,wBAAgB,EAAC,OAAO,CAAC,aAAa,EAAE;YACtC,MAAM,EAAE,aAAa,GAAG,EAAE;YAC1B,OAAO,EACL,CAAC,CAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,OAAO,KAAI,EAAE,CAAC;gBACvB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;SAC/C,CAAC,CACH,CAAC;KACH;IACD,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,CAAC,CAAC;AAjDW,QAAA,gBAAgB,oBAiD3B"}