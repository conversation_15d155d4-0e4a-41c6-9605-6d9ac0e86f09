{"version": 3, "file": "index.js", "sources": ["../lib/helpers/deep-extend.js", "../lib/helpers/replace.js", "../lib/messages.js", "../lib/rules/any.js", "../lib/rules/array.js", "../lib/rules/boolean.js", "../lib/rules/class.js", "../lib/rules/custom.js", "../lib/rules/currency.js", "../lib/rules/date.js", "../lib/rules/email.js", "../lib/rules/enum.js", "../lib/rules/equal.js", "../lib/rules/forbidden.js", "../lib/rules/function.js", "../lib/rules/multi.js", "../lib/rules/number.js", "../lib/rules/object.js", "../lib/rules/objectID.js", "../lib/rules/record.js", "../lib/rules/string.js", "../lib/rules/tuple.js", "../lib/rules/url.js", "../lib/rules/uuid.js", "../lib/rules/mac.js", "../lib/rules/luhn.js", "../lib/helpers/prettier.js", "../lib/validator.js", "../index.js"], "sourcesContent": ["\"use strict\";\n\nfunction isObjectHasKeys(v) {\n\tif (typeof v !== \"object\" || Array.isArray(v) || v == null) return false;\n\treturn Object.keys(v).length > 0;\n}\n\nfunction deepExtend(destination, source, options = {}) {\n\tfor (let property in source) {\n\t\tif (isObjectHasKeys(source[property])) {\n\t\t\tdestination[property] = destination[property] || {};\n\t\t\tdeepExtend(destination[property], source[property], options);\n\t\t} else {\n\t\t\tif (options.skipIfExist === true && destination[property] !== undefined) continue;\n\t\t\tdestination[property] = source[property];\n\t\t}\n\t}\n\treturn destination;\n}\n\nmodule.exports = deepExtend;\n", "function convertible(value) {\n\tif (value === undefined) return \"\";\n\tif (value === null) return \"\";\n\tif (typeof value.toString === \"function\") return value;\n\treturn typeof value;\n}\n\nmodule.exports = (string, searchValue, newValue) => string.replace(searchValue, convertible(newValue));\n", "\"use strict\";\n\nmodule.exports = {\n\trequired: \"The '{field}' field is required.\",\n\n\tstring: \"The '{field}' field must be a string.\",\n\tstringEmpty: \"The '{field}' field must not be empty.\",\n\tstringMin: \"The '{field}' field length must be greater than or equal to {expected} characters long.\",\n\tstringMax: \"The '{field}' field length must be less than or equal to {expected} characters long.\",\n\tstringLength: \"The '{field}' field length must be {expected} characters long.\",\n\tstringPattern: \"The '{field}' field fails to match the required pattern.\",\n\tstringContains: \"The '{field}' field must contain the '{expected}' text.\",\n\tstringEnum: \"The '{field}' field does not match any of the allowed values.\",\n\tstringNumeric: \"The '{field}' field must be a numeric string.\",\n\tstringAlpha: \"The '{field}' field must be an alphabetic string.\",\n\tstringAlphanum: \"The '{field}' field must be an alphanumeric string.\",\n\tstringAlphadash: \"The '{field}' field must be an alphadash string.\",\n\tstringHex: \"The '{field}' field must be a hex string.\",\n\tstringSingleLine: \"The '{field}' field must be a single line string.\",\n\tstringBase64: \"The '{field}' field must be a base64 string.\",\n\n\tnumber: \"The '{field}' field must be a number.\",\n\tnumberMin: \"The '{field}' field must be greater than or equal to {expected}.\",\n\tnumberMax: \"The '{field}' field must be less than or equal to {expected}.\",\n\tnumberEqual: \"The '{field}' field must be equal to {expected}.\",\n\tnumberNotEqual: \"The '{field}' field can't be equal to {expected}.\",\n\tnumberInteger: \"The '{field}' field must be an integer.\",\n\tnumberPositive: \"The '{field}' field must be a positive number.\",\n\tnumberNegative: \"The '{field}' field must be a negative number.\",\n\n\tarray: \"The '{field}' field must be an array.\",\n\tarrayEmpty: \"The '{field}' field must not be an empty array.\",\n\tarrayMin: \"The '{field}' field must contain at least {expected} items.\",\n\tarrayMax: \"The '{field}' field must contain less than or equal to {expected} items.\",\n\tarrayLength: \"The '{field}' field must contain {expected} items.\",\n\tarrayContains: \"The '{field}' field must contain the '{expected}' item.\",\n\tarrayUnique: \"The '{actual}' value in '{field}' field does not unique the '{expected}' values.\",\n\tarrayEnum: \"The '{actual}' value in '{field}' field does not match any of the '{expected}' values.\",\n\n\ttuple: \"The '{field}' field must be an array.\",\n\ttupleEmpty: \"The '{field}' field must not be an empty array.\",\n\ttupleLength: \"The '{field}' field must contain {expected} items.\",\n\n\tboolean: \"The '{field}' field must be a boolean.\",\n\n\tcurrency: \"The '{field}' must be a valid currency format\",\n\n\tdate: \"The '{field}' field must be a Date.\",\n\tdateMin: \"The '{field}' field must be greater than or equal to {expected}.\",\n\tdateMax: \"The '{field}' field must be less than or equal to {expected}.\",\n\n\tenumValue: \"The '{field}' field value '{expected}' does not match any of the allowed values.\",\n\n\tequalValue: \"The '{field}' field value must be equal to '{expected}'.\",\n\tequalField: \"The '{field}' field value must be equal to '{expected}' field value.\",\n\n\tforbidden: \"The '{field}' field is forbidden.\",\n\n\tfunction: \"The '{field}' field must be a function.\",\n\n\temail: \"The '{field}' field must be a valid e-mail.\",\n\temailEmpty: \"The '{field}' field must not be empty.\",\n\temailMin: \"The '{field}' field length must be greater than or equal to {expected} characters long.\",\n\temailMax: \"The '{field}' field length must be less than or equal to {expected} characters long.\",\n\n\tluhn: \"The '{field}' field must be a valid checksum luhn.\",\n\n\tmac: \"The '{field}' field must be a valid MAC address.\",\n\n\tobject: \"The '{field}' must be an Object.\",\n\tobjectStrict: \"The object '{field}' contains forbidden keys: '{actual}'.\",\n\tobjectMinProps: \"The object '{field}' must contain at least {expected} properties.\",\n\tobjectMaxProps: \"The object '{field}' must contain {expected} properties at most.\",\n\n\turl: \"The '{field}' field must be a valid URL.\",\n\turlEmpty: \"The '{field}' field must not be empty.\",\n\n\tuuid: \"The '{field}' field must be a valid UUID.\",\n\tuuidVersion: \"The '{field}' field must be a valid UUID version provided.\",\n\n\tclassInstanceOf: \"The '{field}' field must be an instance of the '{expected}' class.\",\n\n\tobjectID: \"The '{field}' field must be an valid ObjectID\",\n\n\trecord: \"The '{field}' must be an Object.\"\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function(/*{ schema, messages }, path, context*/) {\n\tconst src = [];\n\tsrc.push(`\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function ({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tlet sanitized = false;\n\tif (schema.convert === true) {\n\t\tsanitized = true;\n\t\t// Convert to array if not and the value is not null or undefined\n\t\tsrc.push(`\n\t\t\tif (!Array.isArray(value) && value != null) {\n\t\t\t\tvalue = [value];\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (!Array.isArray(value)) {\n\t\t\t${this.makeError({ type: \"array\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\tvar len = value.length;\n\t`);\n\n\tif (schema.empty === false) {\n\t\tsrc.push(`\n\t\t\tif (len === 0) {\n\t\t\t\t${this.makeError({ type: \"arrayEmpty\", actual: \"value\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.min != null) {\n\t\tsrc.push(`\n\t\t\tif (len < ${schema.min}) {\n\t\t\t\t${this.makeError({ type: \"arrayMin\", expected: schema.min, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.max != null) {\n\t\tsrc.push(`\n\t\t\tif (len > ${schema.max}) {\n\t\t\t\t${this.makeError({ type: \"arrayMax\", expected: schema.max, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.length != null) {\n\t\tsrc.push(`\n\t\t\tif (len !== ${schema.length}) {\n\t\t\t\t${this.makeError({ type: \"arrayLength\", expected: schema.length, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.contains != null) {\n\t\tsrc.push(`\n\t\t\tif (value.indexOf(${JSON.stringify(schema.contains)}) === -1) {\n\t\t\t\t${this.makeError({ type: \"arrayContains\", expected: JSON.stringify(schema.contains), actual: \"value\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.unique === true) {\n\t\tsrc.push(`\n\t\t\tif(len > (new Set(value)).size) {\n\t\t\t\t${this.makeError({ type: \"arrayUnique\", expected: \"Array.from(new Set(value.filter((item, index) => value.indexOf(item) !== index)))\", actual: \"value\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.enum != null) {\n\t\tconst enumStr = JSON.stringify(schema.enum);\n\t\tsrc.push(`\n\t\t\tfor (var i = 0; i < value.length; i++) {\n\t\t\t\tif (${enumStr}.indexOf(value[i]) === -1) {\n\t\t\t\t\t${this.makeError({ type: \"arrayEnum\", expected: \"\\\"\" + schema.enum.join(\", \") + \"\\\"\", actual: \"value[i]\", messages })}\n\t\t\t\t}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.items != null) {\n\t\tsrc.push(`\n\t\t\tvar arr = value;\n\t\t\tvar parentField = field;\n\t\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\t\tvalue = arr[i];\n\t\t`);\n\n\t\tconst itemPath = path + \"[]\";\n\t\tconst rule = this.getRuleFromSchema(schema.items);\n\t\t// eslint-disable-next-line quotes\n\t\tconst innerSource = `arr[i] = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](arr[i], (parentField ? parentField : \"\") + \"[\" + i + \"]\", parent, errors, context)`;\n\t\tsrc.push(this.compileRule(rule, context, itemPath, innerSource, \"arr[i]\"));\n\t\tsrc.push(`\n\t\t\t}\n\t\t`);\n\t\tsrc.push(`\n\t\treturn arr;\n\t`);\n\t} else {\n\t\tsrc.push(`\n\t\treturn value;\n\t`);\n\t}\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\tlet sanitized = false;\n\n\tsrc.push(`\n\t\tvar origValue = value;\n\t`);\n\n\tif (schema.convert === true) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tif (typeof value !== \"boolean\") {\n\t\t\t\tif (\n\t\t\t\tvalue === 1\n\t\t\t\t|| value === \"true\"\n\t\t\t\t|| value === \"1\"\n\t\t\t\t|| value === \"on\"\n\t\t\t\t) {\n\t\t\t\t\tvalue = true;\n\t\t\t\t} else if (\n\t\t\t\tvalue === 0\n\t\t\t\t|| value === \"false\"\n\t\t\t\t|| value === \"0\"\n\t\t\t\t|| value === \"off\"\n\t\t\t\t) {\n\t\t\t\t\tvalue = false;\n\t\t\t\t}\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (typeof value !== \"boolean\") {\n\t\t\t${this.makeError({ type: \"boolean\",  actual: \"origValue\", messages })}\n\t\t}\n\t\t\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages, index }, path, context) {\n\tconst src = [];\n\n\tconst className = schema.instanceOf.name ? schema.instanceOf.name : \"<UnknowClass>\";\n\tif (!context.customs[index]) context.customs[index] = { schema };\n\telse context.customs[index].schema = schema;\n\n\tsrc.push(`\n\t\tif (!(value instanceof context.customs[${index}].schema.instanceOf))\n\t\t\t${this.makeError({ type: \"classInstanceOf\",  actual: \"value\", expected: \"'\" + className + \"'\", messages })}\n\t`);\n\n\tsrc.push(`\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\nmodule.exports = function ({ schema, messages, index }, path, context) {\n\tconst src = [];\n\n\tsrc.push(`\n\t\t${this.makeCustomValidator({ fnName: \"check\", path, schema, messages, context, ruleIndex: index })}\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\nconst CURRENCY_REGEX = \"(?=.*\\\\d)^(-?~1|~1-?)(([0-9]\\\\d{0,2}(~2\\\\d{3})*)|0)?(\\\\~3\\\\d{1,2})?$\";\n/**\tSignature: function(value, field, parent, errors, context)\n */\n\nmodule.exports = function ({schema, messages}, path, context) {\n\tconst currencySymbol = schema.currencySymbol || null;\n\tconst thousandSeparator = schema.thousandSeparator || \",\";\n\tconst decimalSeparator = schema.decimalSeparator || \".\";\n\tconst customRegex = schema.customRegex;\n\tlet isCurrencySymbolMandatory = !schema.symbolOptional;\n\tlet finalRegex = CURRENCY_REGEX.replace(/~1/g, currencySymbol ? (`\\\\${currencySymbol}${(isCurrencySymbolMandatory ? \"\" : \"?\")}`) : \"\")\n\t\t.replace(\"~2\", thousandSeparator)\n\t\t.replace(\"~3\", decimalSeparator);\n\n\n\tconst src = [];\n\n\tsrc.push(`\n\t\tif (!value.match(${customRegex || new RegExp(finalRegex)})) {\n\t\t\t${this.makeError({ type: \"currency\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\tlet sanitized = false;\n\n\tsrc.push(`\n\t\tvar origValue = value;\n\t`);\n\n\tif (schema.convert === true) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tif (!(value instanceof Date)) {\n\t\t\t\tvalue = new Date(value.length && !isNaN(+value) ? +value : value);\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (!(value instanceof Date) || isNaN(value.getTime()))\n\t\t\t${this.makeError({ type: \"date\",  actual: \"origValue\", messages })}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\nconst PRECISE_PATTERN = /^(([^<>()[\\]\\\\.,;:\\s@\"]+(\\.[^<>()[\\]\\\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$/;\nconst BASIC_PATTERN = /^\\S+@\\S+\\.\\S+$/;\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tconst pattern = schema.mode == \"precise\" ? PRECISE_PATTERN : BASIC_PATTERN;\n\tlet sanitized = false;\n\n\tsrc.push(`\n\t\tif (typeof value !== \"string\") {\n\t\t\t${this.makeError({ type: \"string\",  actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\t`);\n\n\tif (!schema.empty) {\n\t\tsrc.push(`\n\t\t\tif (value.length === 0) {\n\t\t\t\t${this.makeError({ type: \"emailEmpty\", actual: \"value\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\t} else {\n\t\tsrc.push(`\n\t\t\tif (value.length === 0) return value;\n\t\t`);\n\t}\n\n\tif (schema.normalize) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.trim().toLowerCase();\n\t\t`);\n\t}\n\n\tif (schema.min != null) {\n\t\tsrc.push(`\n\t\t\tif (value.length < ${schema.min}) {\n\t\t\t\t${this.makeError({ type: \"emailMin\", expected: schema.min, actual: \"value.length\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.max != null) {\n\t\tsrc.push(`\n\t\t\tif (value.length > ${schema.max}) {\n\t\t\t\t${this.makeError({ type: \"emailMax\", expected: schema.max, actual: \"value.length\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (!${pattern.toString()}.test(value)) {\n\t\t\t${this.makeError({ type: \"email\",  actual: \"value\", messages })}\n\t\t}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst enumStr = JSON.stringify(schema.values || []);\n\treturn {\n\t\tsource: `\n\t\t\tif (${enumStr}.indexOf(value) === -1)\n\t\t\t\t${this.makeError({ type: \"enumValue\", expected: \"\\\"\" + schema.values.join(\", \") + \"\\\"\", actual: \"value\", messages })}\n\t\t\t\n\t\t\treturn value;\n\t\t`\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tif (schema.field) {\n\t\tif (schema.strict) {\n\t\t\tsrc.push(`\n\t\t\t\tif (value !== parent[\"${schema.field}\"])\n\t\t\t`);\n\t\t} else {\n\t\t\tsrc.push(`\n\t\t\t\tif (value != parent[\"${schema.field}\"])\n\t\t\t`);\n\t\t}\n\t\tsrc.push(`\n\t\t\t\t${this.makeError({ type: \"equalField\",  actual: \"value\", expected: JSON.stringify(schema.field), messages })}\n\t\t`);\n\t} else {\n\t\tif (schema.strict) {\n\t\t\tsrc.push(`\n\t\t\t\tif (value !== ${JSON.stringify(schema.value)})\n\t\t\t`);\n\t\t} else {\n\t\t\tsrc.push(`\n\t\t\t\tif (value != ${JSON.stringify(schema.value)})\n\t\t\t`);\n\t\t}\n\t\tsrc.push(`\n\t\t\t\t${this.makeError({ type: \"equalValue\",  actual: \"value\", expected: JSON.stringify(schema.value), messages })}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function checkForbidden({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tsrc.push(`\n\t\tif (value !== null && value !== undefined) {\n\t`);\n\n\tif (schema.remove) {\n\t\tsrc.push(`\n\t\t\treturn undefined;\n\t\t`);\n\n\t} else {\n\t\tsrc.push(`\n\t\t\t${this.makeError({ type: \"forbidden\",  actual: \"value\", messages })}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\t}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\treturn {\n\t\tsource: `\n\t\t\tif (typeof value !== \"function\")\n\t\t\t\t${this.makeError({ type: \"function\",  actual: \"value\", messages })}\n\n\t\t\treturn value;\n\t\t`\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tsrc.push(`\n\t\tvar hasValid = false;\n\t\tvar newVal = value;\n\t\tvar checkErrors = [];\n\t\tvar errorsSize = errors.length;\n\t`);\n\n\tfor (let i = 0; i < schema.rules.length; i++) {\n\t\tsrc.push(`\n\t\t\tif (!hasValid) {\n\t\t\t\tvar _errors = [];\n\t\t`);\n\n\t\tconst rule = this.getRuleFromSchema(schema.rules[i]);\n\t\tsrc.push(this.compileRule(rule, context, path, `var tmpVal = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](value, field, parent, _errors, context);`, \"tmpVal\"));\n\t\tsrc.push(`\n\t\t\t\tif (errors.length == errorsSize && _errors.length == 0) {\n\t\t\t\t\thasValid = true;\n\t\t\t\t\tnewVal = tmpVal;\n\t\t\t\t} else {\n\t\t\t\t\tArray.prototype.push.apply(checkErrors, [].concat(_errors, errors.splice(errorsSize)));\n\t\t\t\t}\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (!hasValid) {\n\t\t\tArray.prototype.push.apply(errors, checkErrors);\n\t\t}\n\n\t\treturn newVal;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tsrc.push(`\n\t\tvar origValue = value;\n\t`);\n\n\tlet sanitized = false;\n\tif (schema.convert === true) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tif (typeof value !== \"number\") {\n\t\t\t\tvalue = Number(value);\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (typeof value !== \"number\" || isNaN(value) || !isFinite(value)) {\n\t\t\t${this.makeError({ type: \"number\",  actual: \"origValue\", messages })}\n\t\t\treturn value;\n\t\t}\n\t`);\n\n\tif (schema.min != null) {\n\t\tsrc.push(`\n\t\t\tif (value < ${schema.min}) {\n\t\t\t\t${this.makeError({ type: \"numberMin\", expected: schema.min, actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.max != null) {\n\t\tsrc.push(`\n\t\t\tif (value > ${schema.max}) {\n\t\t\t\t${this.makeError({ type: \"numberMax\", expected: schema.max, actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\t// Check fix value\n\tif (schema.equal != null) {\n\t\tsrc.push(`\n\t\t\tif (value !== ${schema.equal}) {\n\t\t\t\t${this.makeError({ type: \"numberEqual\", expected: schema.equal, actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\t// Check not fix value\n\tif (schema.notEqual != null) {\n\t\tsrc.push(`\n\t\t\tif (value === ${schema.notEqual}) {\n\t\t\t\t${this.makeError({ type: \"numberNotEqual\", expected: schema.notEqual, actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\t// Check integer\n\tif (schema.integer === true) {\n\t\tsrc.push(`\n\t\t\tif (value % 1 !== 0) {\n\t\t\t\t${this.makeError({ type: \"numberInteger\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\t// Check positive\n\tif (schema.positive === true) {\n\t\tsrc.push(`\n\t\t\tif (value <= 0) {\n\t\t\t\t${this.makeError({ type: \"numberPositive\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\t// Check negative\n\tif (schema.negative === true) {\n\t\tsrc.push(`\n\t\t\tif (value >= 0) {\n\t\t\t\t${this.makeError({ type: \"numberNegative\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n// Quick regex to match most common unquoted JavaScript property names. Note the spec allows Unicode letters.\n// Unmatched property names will be quoted and validate slighly slower. https://www.ecma-international.org/ecma-262/5.1/#sec-7.6\nconst identifierRegex = /^[_$a-zA-Z][_$a-zA-Z0-9]*$/;\n\n// Regex to escape quoted property names for eval/new Function\nconst escapeEvalRegex = /[\"'\\\\\\n\\r\\u2028\\u2029]/g;\n\n/* istanbul ignore next */\nfunction escapeEvalString(str) {\n\t// Based on https://github.com/joliss/js-string-escape\n\treturn str.replace(escapeEvalRegex, function (character) {\n\t\tswitch (character) {\n\t\tcase \"\\\"\":\n\t\tcase \"'\":\n\t\tcase \"\\\\\":\n\t\t\treturn \"\\\\\" + character;\n\t\t\t// Four possible LineTerminator characters need to be escaped:\n\t\tcase \"\\n\":\n\t\t\treturn \"\\\\n\";\n\t\tcase \"\\r\":\n\t\t\treturn \"\\\\r\";\n\t\tcase \"\\u2028\":\n\t\t\treturn \"\\\\u2028\";\n\t\tcase \"\\u2029\":\n\t\t\treturn \"\\\\u2029\";\n\t\t}\n\t});\n}\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function ({ schema, messages }, path, context) {\n\tconst sourceCode = [];\n\n\tsourceCode.push(`\n\t\tif (typeof value !== \"object\" || value === null || Array.isArray(value)) {\n\t\t\t${this.makeError({ type: \"object\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\t`);\n\n\tconst subSchema = schema.properties || schema.props;\n\tif (subSchema) {\n\t\tsourceCode.push(\"var parentObj = value;\");\n\t\tsourceCode.push(\"var parentField = field;\");\n\n\t\tconst keys = Object.keys(subSchema).filter(key => !this.isMetaKey(key));\n\n\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\tconst property = keys[i];\n\t\t\tconst rule = this.getRuleFromSchema(subSchema[property]);\n\t\t\t\n\t\t\tconst name = escapeEvalString(property);\n\t\t\tconst safeSubName = identifierRegex.test(name) ? `.${name}` : `['${name}']`;\n\t\t\tconst safePropName = `parentObj${safeSubName}`;\n\t\t\tconst newPath = (path ? path + \".\" : \"\") + property;\n\n\t\t\tconst labelName = rule.schema.label;\n\t\t\tconst label = labelName ? `'${escapeEvalString(labelName)}'` : undefined;\n\n\t\t\tsourceCode.push(`\\n// Field: ${escapeEvalString(newPath)}`);\n\t\t\tsourceCode.push(`field = parentField ? parentField + \"${safeSubName}\" : \"${name}\";`);\n\t\t\tsourceCode.push(`value = ${safePropName};`);\n\t\t\tsourceCode.push(`label = ${label}`);\n\t\t\tconst innerSource = `\n\t\t\t\t${safePropName} = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](value, field, parentObj, errors, context, label);\n\t\t\t`;\n\t\t\tsourceCode.push(this.compileRule(rule, context, newPath, innerSource, safePropName));\n\t\t\tif (this.opts.haltOnFirstError === true) {\n\t\t\t\tsourceCode.push(\"if (errors.length) return parentObj;\");\n\t\t\t}\n\t\t}\n\n\t\t// Strict handler\n\t\tif (schema.strict) {\n\t\t\tconst allowedProps = Object.keys(subSchema);\n\n\t\t\tsourceCode.push(`\n\t\t\t\tfield = parentField;\n\t\t\t\tvar invalidProps = [];\n\t\t\t\tvar props = Object.keys(parentObj);\n\n\t\t\t\tfor (let i = 0; i < props.length; i++) {\n\t\t\t\t\tif (${JSON.stringify(allowedProps)}.indexOf(props[i]) === -1) {\n\t\t\t\t\t\tinvalidProps.push(props[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (invalidProps.length) {\n\t\t\t`);\n\t\t\tif (schema.strict === \"remove\") {\n\t\t\t\tsourceCode.push(`\n\t\t\t\t\tif (errors.length === 0) {\n\t\t\t\t`);\n\t\t\t\tsourceCode.push(`\n\t\t\t\t\t\tinvalidProps.forEach(function(field) {\n\t\t\t\t\t\t\tdelete parentObj[field];\n\t\t\t\t\t\t});\n\t\t\t\t`);\n\t\t\t\tsourceCode.push(`\n\t\t\t\t\t}\n\t\t\t\t`);\n\t\t\t} else {\n\t\t\t\tsourceCode.push(`\n\t\t\t\t\t${this.makeError({ type: \"objectStrict\", expected: \"\\\"\" + allowedProps.join(\", \") + \"\\\"\", actual: \"invalidProps.join(', ')\", messages })}\n\t\t\t\t`);\n\t\t\t}\n\t\t\tsourceCode.push(`\n\t\t\t\t}\n\t\t\t`);\n\t\t}\n\t}\n\n\tif (schema.minProps != null || schema.maxProps != null) {\n\t\t// We recalculate props, because:\n\t\t//\t- if strict equals 'remove', we want to work on\n\t\t//\tthe payload with the extra keys removed,\n\t\t//\t- if no strict is set, we need them anyway.\n\t\tif (schema.strict) {\n\t\t\tsourceCode.push(`\n\t\t\t\tprops = Object.keys(${subSchema ? \"parentObj\" : \"value\"});\n\t\t\t`);\n\t\t} else {\n\t\t\tsourceCode.push(`\n\t\t\t\tvar props = Object.keys(${subSchema ? \"parentObj\" : \"value\"});\n\t\t\t\t${subSchema ? \"field = parentField;\" : \"\"}\n\t\t\t`);\n\t\t}\n\t}\n\n\tif (schema.minProps != null) {\n\t\tsourceCode.push(`\n\t\t\tif (props.length < ${schema.minProps}) {\n\t\t\t\t${this.makeError({ type: \"objectMinProps\", expected: schema.minProps, actual: \"props.length\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.maxProps != null) {\n\t\tsourceCode.push(`\n\t\t\tif (props.length > ${schema.maxProps}) {\n\t\t\t\t${this.makeError({ type: \"objectMaxProps\", expected: schema.maxProps, actual: \"props.length\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (subSchema) {\n\t\tsourceCode.push(`\n\t\t\treturn parentObj;\n\t\t`);\n\t} else {\n\t\tsourceCode.push(`\n\t\t\treturn value;\n\t\t`);\n\t}\n\n\treturn {\n\t\tsource: sourceCode.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages, index }, path, context) {\n\tconst src = [];\n\n\tif (!context.customs[index]) context.customs[index] = { schema };\n\telse context.customs[index].schema = schema;\n\n\tsrc.push(`\n\t\tconst ObjectID = context.customs[${index}].schema.ObjectID;\n\t\tif (!ObjectID.isValid(value)) {\n\t\t\t${this.makeError({ type: \"objectID\", actual: \"value\", messages })}\n\t\t\treturn;\n\t\t}\n\t`);\n\n\tif (schema.convert === true) src.push(\"return new ObjectID(value)\");\n\telse if (schema.convert === \"hexString\") src.push(\"return value.toString()\");\n\telse src.push(\"return value\");\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "function patchKeyRuleMessages(rule) {\n\tfor (const type in rule.messages) {\n\t\tif (type.startsWith(\"string\")) {\n\t\t\trule.messages[type] = rule.messages[type].replace(\" field \", \" key \");\n\t\t}\n\t}\n}\n\nmodule.exports = function compileRecordRule({ schema, messages }, path, context) {\n\tconst sourceCode = [];\n\tsourceCode.push(`\n\t\tif (typeof value !== \"object\" || value === null || Array.isArray(value)) {\n\t\t\t${this.makeError({ type: \"record\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\t`);\n\n\tconst keyRuleName = schema.key || \"string\";\n\tconst valueRuleName = schema.value || \"any\";\n\n\tsourceCode.push(`\n\t\tconst record = value;\n\t\tlet sanitizedKey, sanitizedValue;\n\t\tconst result = {};\n\t\tfor (let key in value) {\n\t`);\n\n\tsourceCode.push(\"sanitizedKey = value = key;\");\n\n\tconst keyRule = this.getRuleFromSchema(keyRuleName);\n\tpatchKeyRuleMessages(keyRule);\n\tconst keyInnerSource = `\n\t\tsanitizedKey = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](key, field ? field + \".\" + key : key, record, errors, context);\n\t`;\n\tsourceCode.push(this.compileRule(keyRule, context, null, keyInnerSource, \"sanitizedKey\"));\n\tsourceCode.push(\"sanitizedValue = value = record[key];\");\n\n\tconst valueRule = this.getRuleFromSchema(valueRuleName);\n\tconst valueInnerSource = `\n\t\tsanitizedValue = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](value, field ? field + \".\" + key : key, record, errors, context);\n\t`;\n\tsourceCode.push(this.compileRule(valueRule, context, `${path}[key]`, valueInnerSource, \"sanitizedValue\"));\n\tsourceCode.push(\"result[sanitizedKey] = sanitizedValue;\");\n\tsourceCode.push(`\n\t\t}\n\t`);\n\tsourceCode.push(\"return result;\");\n\n\treturn {\n\t\tsource: sourceCode.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\nconst NUMERIC_PATTERN = /^-?[0-9]\\d*(\\.\\d+)?$/;\nconst ALPHA_PATTERN = /^[a-zA-Z]+$/;\nconst ALPHANUM_PATTERN = /^[a-zA-Z0-9]+$/;\nconst ALPHADASH_PATTERN = /^[a-zA-Z0-9_-]+$/;\nconst HEX_PATTERN = /^[0-9a-fA-F]+$/;\nconst BASE64_PATTERN = /^(?:[A-Za-z0-9+\\\\/]{4})*(?:[A-Za-z0-9+\\\\/]{2}==|[A-Za-z0-9+/]{3}=)?$/;\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function checkString({ schema, messages }, path, context) {\n\tconst src = [];\n\tlet sanitized = false;\n\n\tif (schema.convert === true) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tif (typeof value !== \"string\") {\n\t\t\t\tvalue = String(value);\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (typeof value !== \"string\") {\n\t\t\t${this.makeError({ type: \"string\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\tvar origValue = value;\n\t`);\n\n\tif (schema.trim) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.trim();\n\t\t`);\n\t}\n\n\tif (schema.trimLeft) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.trimLeft();\n\t\t`);\n\t}\n\n\tif (schema.trimRight) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.trimRight();\n\t\t`);\n\t}\n\n\tif (schema.padStart) {\n\t\tsanitized = true;\n\t\tconst padChar = schema.padChar != null ? schema.padChar : \" \";\n\t\tsrc.push(`\n\t\t\tvalue = value.padStart(${schema.padStart}, ${JSON.stringify(padChar)});\n\t\t`);\n\t}\n\n\tif (schema.padEnd) {\n\t\tsanitized = true;\n\t\tconst padChar = schema.padChar != null ? schema.padChar : \" \";\n\t\tsrc.push(`\n\t\t\tvalue = value.padEnd(${schema.padEnd}, ${JSON.stringify(padChar)});\n\t\t`);\n\t}\n\n\tif (schema.lowercase) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.toLowerCase();\n\t\t`);\n\t}\n\n\tif (schema.uppercase) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.toUpperCase();\n\t\t`);\n\t}\n\n\tif (schema.localeLowercase) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.toLocaleLowerCase();\n\t\t`);\n\t}\n\n\tif (schema.localeUppercase) {\n\t\tsanitized = true;\n\t\tsrc.push(`\n\t\t\tvalue = value.toLocaleUpperCase();\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\t\tvar len = value.length;\n\t`);\n\n\tif (schema.empty === false) {\n\t\tsrc.push(`\n\t\t\tif (len === 0) {\n\t\t\t\t${this.makeError({ type: \"stringEmpty\",  actual: \"value\", messages })}\n\t\t\t}\n\t\t`);\n\t} else if (schema.empty === true) {\n\t\tsrc.push(`\n\t\t\tif (len === 0) {\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.min != null) {\n\t\tsrc.push(`\n\t\t\tif (len < ${schema.min}) {\n\t\t\t\t${this.makeError({ type: \"stringMin\", expected: schema.min, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.max != null) {\n\t\tsrc.push(`\n\t\t\tif (len > ${schema.max}) {\n\t\t\t\t${this.makeError({ type: \"stringMax\", expected: schema.max, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.length != null) {\n\t\tsrc.push(`\n\t\t\tif (len !== ${schema.length}) {\n\t\t\t\t${this.makeError({ type: \"stringLength\", expected: schema.length, actual: \"len\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.pattern != null) {\n\t\tlet pattern = schema.pattern;\n\t\tif (typeof schema.pattern == \"string\")\n\t\t\tpattern = new RegExp(schema.pattern, schema.patternFlags);\n\n\t\tsrc.push(`\n\t\t\tif (!${pattern.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringPattern\", expected: `\"${pattern.toString().replace(/\"/g, \"\\\\$&\")}\"`, actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.contains != null) {\n\t\tsrc.push(`\n\t\t\tif (value.indexOf(\"${schema.contains}\") === -1) {\n\t\t\t\t${this.makeError({ type: \"stringContains\", expected: \"\\\"\" + schema.contains + \"\\\"\", actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.enum != null) {\n\t\tconst enumStr = JSON.stringify(schema.enum);\n\t\tsrc.push(`\n\t\t\tif (${enumStr}.indexOf(value) === -1) {\n\t\t\t\t${this.makeError({ type: \"stringEnum\", expected: \"\\\"\" + schema.enum.join(\", \") + \"\\\"\", actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.numeric === true) {\n\t\tsrc.push(`\n\t\t\tif (!${NUMERIC_PATTERN.toString()}.test(value) ) {\n\t\t\t\t${this.makeError({ type: \"stringNumeric\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif(schema.alpha === true) {\n\t\tsrc.push(`\n\t\t\tif(!${ALPHA_PATTERN.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringAlpha\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif(schema.alphanum === true) {\n\t\tsrc.push(`\n\t\t\tif(!${ALPHANUM_PATTERN.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringAlphanum\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif(schema.alphadash === true) {\n\t\tsrc.push(`\n\t\t\tif(!${ALPHADASH_PATTERN.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringAlphadash\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif(schema.hex === true) {\n\t\tsrc.push(`\n\t\t\tif(value.length % 2 !== 0 || !${HEX_PATTERN.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringHex\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tif(schema.singleLine === true) {\n\t\tsrc.push(`\n\t\t\tif(value.includes(\"\\\\n\")) {\n\t\t\t\t${this.makeError({ type: \"stringSingleLine\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\n\tif(schema.base64 === true) {\n\t\tsrc.push(`\n\t\t\tif(!${BASE64_PATTERN.toString()}.test(value)) {\n\t\t\t\t${this.makeError({ type: \"stringBase64\",  actual: \"origValue\", messages })}\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsanitized,\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function ({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tif (schema.items != null) {\n\t\tif (!Array.isArray(schema.items)) {\n\t\t\tthrow new Error(`Invalid '${schema.type}' schema. The 'items' field must be an array.`);\n\t\t}\n\n\t\tif (schema.items.length === 0) {\n\t\t\tthrow new Error(`Invalid '${schema.type}' schema. The 'items' field must not be an empty array.`);\n\t\t}\n\t}\n\n\tsrc.push(`\n\t\tif (!Array.isArray(value)) {\n\t\t\t${this.makeError({ type: \"tuple\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\tvar len = value.length;\n\t`);\n\n\n\tif (schema.empty === false) {\n\t\tsrc.push(`\n\t\t\tif (len === 0) {\n\t\t\t\t${this.makeError({ type: \"tupleEmpty\", actual: \"value\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\t}\n\n\tif (schema.items != null) {\n\t\tsrc.push(`\n\t\t\tif (${schema.empty} !== false && len === 0) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (len !== ${schema.items.length}) {\n\t\t\t\t${this.makeError({type: \"tupleLength\", expected: schema.items.length, actual: \"len\", messages})}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\n\t\tsrc.push(`\n\t\t\tvar arr = value;\n\t\t\tvar parentField = field;\n\t\t`);\n\n\t\tfor (let i = 0; i < schema.items.length; i++) {\n\t\t\tsrc.push(`\n\t\t\tvalue = arr[${i}];\n\t\t`);\n\n\t\t\tconst itemPath = `${path}[${i}]`;\n\t\t\tconst rule = this.getRuleFromSchema(schema.items[i]);\n\t\t\tconst innerSource = `\n\t\t\tarr[${i}] = ${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](arr[${i}], (parentField ? parentField : \"\") + \"[\" + ${i} + \"]\", parent, errors, context);\n\t\t`;\n\t\t\tsrc.push(this.compileRule(rule, context, itemPath, innerSource, `arr[${i}]`));\n\t\t}\n\t\tsrc.push(`\n\t\treturn arr;\n\t`);\n\t} else {\n\t\tsrc.push(`\n\t\treturn value;\n\t`);\n\t}\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\nconst PATTERN = /^https?:\\/\\/\\S+/;\n//const PATTERN = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!(?:10|127)(?:\\.\\d{1,3}){3})(?!(?:169\\.254|192\\.168)(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))\\.?)(?::\\d{2,5})?(?:[/?#]\\S*)?$/i;\n//const PATTERN = /https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)/g;\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function ({ schema, messages }, path, context) {\n\tconst src = [];\n\n\tsrc.push(`\n\t\tif (typeof value !== \"string\") {\n\t\t\t${this.makeError({ type: \"string\", actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\t`);\n\n\tif (!schema.empty) {\n\t\tsrc.push(`\n\t\t\tif (value.length === 0) {\n\t\t\t\t${this.makeError({ type: \"urlEmpty\", actual: \"value\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\t} else {\n\t\tsrc.push(`\n\t\t\tif (value.length === 0) return value;\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tif (!${PATTERN.toString()}.test(value)) {\n\t\t\t${this.makeError({ type: \"url\", actual: \"value\", messages })}\n\t\t}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\"),\n\t};\n};\n", "\"use strict\";\n\nconst PATTERN = /^([0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[0-9a-f]{4}-[0-9a-f]{12}|[0]{8}-[0]{4}-[0]{4}-[0]{4}-[0]{12})$/i;\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path) {\n\tconst src = [];\n\tsrc.push(`\n\t\tif (typeof value !== \"string\") {\n\t\t\t${this.makeError({ type: \"string\",  actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\tvar val = value.toLowerCase();\n\t\tif (!${PATTERN.toString()}.test(val)) {\n\t\t\t${this.makeError({ type: \"uuid\",  actual: \"value\", messages })}\n\t\t\treturn value;\n\t\t}\n\n\t\tconst version = val.charAt(14) | 0;\n\t`);\n\n\tif(parseInt(schema.version) < 9) {\n\t\tsrc.push(`\n\t\t\tif (${schema.version} !== version) {\n\t\t\t\t${this.makeError({ type: \"uuidVersion\", expected: schema.version, actual: \"version\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\t\t`);\n\t}\n\n\tsrc.push(`\n\t\tswitch (version) {\n\t\tcase 0:\n\t\tcase 1:\n\t\tcase 2:\n\t\tcase 6:\n\t\t\tbreak;\n\t\tcase 3:\n\t\tcase 4:\n\t\tcase 5:\n  \t\tcase 7:\n\t\tcase 8:\n\t\t\tif ([\"8\", \"9\", \"a\", \"b\"].indexOf(val.charAt(19)) === -1) {\n\t\t\t\t${this.makeError({ type: \"uuid\",  actual: \"value\", messages })}\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t`);\n\n\treturn {\n\t\tsource: src.join(\"\\n\")\n\t};\n};\n", "\"use strict\";\n\nconst PATTERN = /^((([a-f0-9][a-f0-9]+[-]){5}|([a-f0-9][a-f0-9]+[:]){5})([a-f0-9][a-f0-9])$)|(^([a-f0-9][a-f0-9][a-f0-9][a-f0-9]+[.]){2}([a-f0-9][a-f0-9][a-f0-9][a-f0-9]))$/i;\n\n/**\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\treturn {\n\t\tsource: `\n\t\t\tif (typeof value !== \"string\") {\n\t\t\t\t${this.makeError({ type: \"string\",  actual: \"value\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvar v = value.toLowerCase();\n\t\t\tif (!${PATTERN.toString()}.test(v)) {\n\t\t\t\t${this.makeError({ type: \"mac\",  actual: \"value\", messages })}\n\t\t\t}\n\t\t\t\n\t\t\treturn value;\n\t\t`\n\t};\n};\n", "\"use strict\";\n\n/**\n * <PERSON><PERSON> algorithm checksum https://en.wikipedia.org/wiki/<PERSON><PERSON>_algorithm\n * Credit Card numbers, IMEI numbers, National Provider Identifier numbers and others\n * @param value\n * @param schema\n * @return {boolean|{actual, expected, type}|ValidationError}\n *\n *\tSignature: function(value, field, parent, errors, context)\n */\nmodule.exports = function({ schema, messages }, path, context) {\n\treturn {\n\t\tsource: `\n\t\t\tif (typeof value !== \"string\") {\n\t\t\t\t${this.makeError({ type: \"string\",  actual: \"value\", messages })}\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (typeof value !== \"string\")\n\t\t\t\tvalue = String(value);\n\n\t\t\tval = value.replace(/\\\\D+/g, \"\");\n\n\t\t\tvar array = [0, 2, 4, 6, 8, 1, 3, 5, 7, 9];\n\t\t\tvar len = val ? val.length : 0,\n\t\t\t\tbit = 1,\n\t\t\t\tsum = 0;\n\t\t\twhile (len--) {\n\t\t\t\tsum += !(bit ^= 1) ? parseInt(val[len], 10) : array[val[len]];\n\t\t\t}\n\n\t\t\tif (!(sum % 10 === 0 && sum > 0)) {\n\t\t\t\t${this.makeError({ type: \"luhn\",  actual: \"value\", messages })}\n\t\t\t}\n\n\t\t\treturn value;\n\t\t`\n\t};\n};\n", "// globals window\nlet prettier, prettierOpts;\nlet hljs, hljsOpts;\n\nlet mod1 = \"prettier\"; // rollup\nlet mod2 = \"cli-highlight\"; // rollup\n\nmodule.exports = function(source) {\n\tif (!prettier) {\n\t\tprettier = require(mod1);\n\t\tprettierOpts = {\n\t\t\tparser: \"babel\",\n\t\t\tuseTabs: false,\n\t\t\tprintWidth: 120,\n\t\t\ttrailingComma: \"none\",\n\t\t\ttabWidth: 4,\n\t\t\tsingleQuote: false,\n\t\t\tsemi: true,\n\t\t\tbracketSpacing: true\n\t\t};\n\n\t\thljs = require(mod2);\n\t\thljsOpts = {\n\t\t\tlanguage: \"js\",\n\t\t\ttheme: hljs.fromJson({\n\t\t\t\tkeyword: [\"white\", \"bold\"],\n\t\t\t\tbuilt_in: \"magenta\",\n\t\t\t\tliteral: \"cyan\",\n\t\t\t\tnumber: \"magenta\",\n\t\t\t\tregexp: \"red\",\n\t\t\t\tstring: [\"yellow\", \"bold\"],\n\t\t\t\tsymbol: \"plain\",\n\t\t\t\tclass: \"blue\",\n\t\t\t\tattr: \"plain\",\n\t\t\t\tfunction: [\"white\", \"bold\"],\n\t\t\t\ttitle: \"plain\",\n\t\t\t\tparams: \"green\",\n\t\t\t\tcomment: \"grey\"\n\t\t\t})\n\t\t};\n\t}\n\n\tconst res = prettier.format(source, prettierOpts);\n\treturn hljs.highlight(res, hljsOpts);\n};\n", "\"use strict\";\n\nlet AsyncFunction;\ntry {\n\tAsyncFunction = (new Function(\"return Object.getPrototypeOf(async function(){}).constructor\"))();\n} catch(err) { /* async is not supported */}\n\nconst deepExtend = require(\"./helpers/deep-extend\");\nconst replace = require(\"./helpers/replace\");\n\nfunction loadMessages() {\n\treturn Object.assign({} , require(\"./messages\"));\n}\n\nfunction loadRules() {\n\treturn {\n\t\tany: require(\"./rules/any\"),\n\t\tarray: require(\"./rules/array\"),\n\t\tboolean: require(\"./rules/boolean\"),\n\t\tclass: require(\"./rules/class\"),\n\t\tcustom: require(\"./rules/custom\"),\n\t\tcurrency: require(\"./rules/currency\"),\n\t\tdate: require(\"./rules/date\"),\n\t\temail: require(\"./rules/email\"),\n\t\tenum: require(\"./rules/enum\"),\n\t\tequal: require(\"./rules/equal\"),\n\t\tforbidden: require(\"./rules/forbidden\"),\n\t\tfunction: require(\"./rules/function\"),\n\t\tmulti: require(\"./rules/multi\"),\n\t\tnumber: require(\"./rules/number\"),\n\t\tobject: require(\"./rules/object\"),\n\t\tobjectID: require(\"./rules/objectID\"),\n\t\trecord: require(\"./rules/record\"),\n\t\tstring: require(\"./rules/string\"),\n\t\ttuple: require(\"./rules/tuple\"),\n\t\turl: require(\"./rules/url\"),\n\t\tuuid: require(\"./rules/uuid\"),\n\t\tmac: require(\"./rules/mac\"),\n\t\tluhn: require(\"./rules/luhn\")\n\t};\n}\n\n/**\n * Fastest Validator\n */\nclass Validator {\n\n\t/**\n\t * Validator class constructor\n\t *\n\t * @param {Object} opts\n\t */\n\tconstructor(opts) {\n\t\tthis.opts = {};\n\t\tthis.defaults = {};\n\t\tthis.messages = loadMessages();\n\t\tthis.rules = loadRules();\n\t\tthis.aliases = {};\n\t\tthis.cache = new Map();\n\t\tthis.customFunctions = {};\n\n\t\tif (opts) {\n\t\t\tdeepExtend(this.opts, opts);\n\t\t\tif (opts.defaults) deepExtend(this.defaults, opts.defaults);\n\n\t\t\tif (opts.messages) {\n\t\t\t\tfor (const messageName in opts.messages) this.addMessage(messageName, opts.messages[messageName]);\n\t\t\t}\n\n\t\t\tif (opts.aliases) {\n\t\t\t\tfor (const aliasName in opts.aliases) this.alias(aliasName, opts.aliases[aliasName]);\n\t\t\t}\n\n\t\t\tif (opts.customRules) {\n\t\t\t\tfor (const ruleName in opts.customRules) this.add(ruleName, opts.customRules[ruleName]);\n\t\t\t}\n\n\t\t\tif (opts.customFunctions) {\n\t\t\t\tfor (const customName in opts.customFunctions) this.addCustomFunction(customName, opts.customFunctions[customName]);\n\t\t\t}\n\n\t\t\tif (opts.plugins) {\n\t\t\t\tconst plugins = opts.plugins;\n\t\t\t\tif (!Array.isArray(plugins)) throw new Error(\"Plugins type must be array\");\n\t\t\t\tplugins.forEach(this.plugin.bind(this));\n\t\t\t}\n\n\t\t\t/* istanbul ignore next */\n\t\t\tif (this.opts.debug) {\n\t\t\t\tlet formatter = function (code) { return code; };\n\t\t\t\tif (typeof window === \"undefined\") {\n\t\t\t\t\tformatter = require(\"./helpers/prettier\");\n\t\t\t\t}\n\n\t\t\t\tthis._formatter = formatter;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Validate an object by schema\n\t *\n\t * @param {Object} obj\n\t * @param {Object} schema\n\t * @returns {Array<Object>|boolean}\n\t */\n\tvalidate(obj, schema) {\n\t\tconst check = this.compile(schema);\n\t\treturn check(obj);\n\t}\n\n\t/**\n\t * Wrap a source code with `required` & `optional` checker codes.\n\t * @param {Object} rule\n\t * @param {String} innerSrc\n\t * @param {String?} resVar\n\t * @returns {String}\n\t */\n\twrapRequiredCheckSourceCode(rule, innerSrc, context, resVar) {\n\t\tconst src = [];\n\t\tconst {considerNullAsAValue = false} = this.opts;\n\t\tlet handleNoValue;\n\n\t\tlet skipUndefinedValue = rule.schema.optional === true || rule.schema.type === \"forbidden\";\n\t\tlet skipNullValue = considerNullAsAValue ?\n\t\t\trule.schema.nullable !== false || rule.schema.type === \"forbidden\" :\n\t\t\trule.schema.optional === true || rule.schema.nullable === true || rule.schema.type === \"forbidden\";\n\n\t\tconst ruleHasDefault = considerNullAsAValue ?\n\t\t\trule.schema.default != undefined && rule.schema.default != null :\n\t\t\trule.schema.default != undefined;\n\n\t\tif (ruleHasDefault) {\n\t\t\t// We should set default-value when value is undefined or null, not skip! (Except when null is allowed)\n\t\t\tskipUndefinedValue = false;\n\t\t\tif (considerNullAsAValue) {\n\t\t\t\tif (rule.schema.nullable === false) skipNullValue = false;\n\t\t\t} else {\n\t\t\t\tif (rule.schema.nullable !== true) skipNullValue = false;\n\t\t\t}\n\n\t\t\tlet defaultValue;\n\t\t\tif (typeof rule.schema.default === \"function\") {\n\t\t\t\tif (!context.customs[rule.index]) context.customs[rule.index] = {};\n\t\t\t\tcontext.customs[rule.index].defaultFn = rule.schema.default;\n\t\t\t\tdefaultValue = `context.customs[${rule.index}].defaultFn.call(this, context.rules[${rule.index}].schema, field, parent, context)`;\n\t\t\t} else {\n\t\t\t\tdefaultValue = JSON.stringify(rule.schema.default);\n\t\t\t}\n\n\t\t\thandleNoValue = `\n\t\t\t\tvalue = ${defaultValue};\n\t\t\t\t${resVar} = value;\n\t\t\t`;\n\n\t\t} else {\n\t\t\thandleNoValue = this.makeError({ type: \"required\", actual: \"value\", messages: rule.messages });\n\t\t}\n\n\n\t\tsrc.push(`\n\t\t\t${`if (value === undefined) { ${skipUndefinedValue ? \"\\n// allow undefined\\n\" : handleNoValue} }`}\n\t\t\t${`else if (value === null) { ${skipNullValue ? \"\\n// allow null\\n\" : handleNoValue} }`}\n\t\t\t${innerSrc ? `else { ${innerSrc} }` : \"\"}\n\t\t`);\n\t\treturn src.join(\"\\n\");\n\t}\n\n\t/**\n\t * check if the key is a meta key\n\t *\n\t * @param key\n\t * @return {boolean}\n\t */\n\tisMetaKey(key) {\n\t\treturn key.startsWith(\"$$\");\n\t}\n\t/**\n\t * will remove all \"metas\" keys (keys starting with $$)\n\t *\n\t * @param obj\n\t */\n\tremoveMetasKeys(obj) {\n\t\tObject.keys(obj).forEach(key => {\n\t\t\tif(!this.isMetaKey(key)) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tdelete obj[key];\n\t\t});\n\t}\n\n\t/**\n\t * Compile a schema\n\t *\n\t * @param {Object} schema\n\t * @throws {Error} Invalid schema\n\t * @returns {Function}\n\t */\n\tcompile(schema) {\n\t\tif (schema === null || typeof schema !== \"object\") {\n\t\t\tthrow new Error(\"Invalid schema.\");\n\t\t}\n\n\t\tconst self = this;\n\t\tconst context = {\n\t\t\tindex: 0,\n\t\t\tasync: schema.$$async === true,\n\t\t\trules: [],\n\t\t\tfn: [],\n\t\t\tcustoms: {},\n\t\t\tcustomFunctions : this.customFunctions,\n\t\t\tutils: {\n\t\t\t\treplace,\n\t\t\t},\n\t\t};\n\t\tthis.cache.clear();\n\t\tdelete schema.$$async;\n\n\t\t/* istanbul ignore next */\n\t\tif (context.async && !AsyncFunction) {\n\t\t\tthrow new Error(\"Asynchronous mode is not supported.\");\n\t\t}\n\n\t\tif (schema.$$root !== true) {\n\t\t\tif (Array.isArray(schema)) {\n\t\t\t\tconst rule = this.getRuleFromSchema(schema);\n\t\t\t\tschema = rule.schema;\n\t\t\t} else {\n\t\t\t\tconst prevSchema = Object.assign({}, schema);\n\t\t\t\tschema = {\n\t\t\t\t\ttype: \"object\",\n\t\t\t\t\tstrict: prevSchema.$$strict,\n\t\t\t\t\tproperties: prevSchema\n\t\t\t\t};\n\n\t\t\t\tthis.removeMetasKeys(prevSchema);\n\t\t\t}\n\t\t}\n\n\t\tconst sourceCode = [\n\t\t\t\"var errors = [];\",\n\t\t\t\"var field;\",\n\t\t\t\"var parent = null;\",\n\t\t\t`var label = ${schema.label ? \"\\\"\" + schema.label + \"\\\"\" : \"null\"};`\n\t\t];\n\n\t\tconst rule = this.getRuleFromSchema(schema);\n\t\tsourceCode.push(this.compileRule(rule, context, null, `${context.async ? \"await \" : \"\"}context.fn[%%INDEX%%](value, field, null, errors, context, label);`, \"value\"));\n\n\t\tsourceCode.push(\"if (errors.length) {\");\n\t\tsourceCode.push(`\n\t\t\treturn errors.map(err => {\n\t\t\t\tif (err.message) {\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\\\{field\\\\}/g, err.label || err.field);\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\\\{expected\\\\}/g, err.expected);\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\\\{actual\\\\}/g, err.actual);\n\t\t\t\t}\n\t\t\t\tif(!err.label) delete err.label\n\t\t\t\treturn err;\n\t\t\t});\n\t\t`);\n\n\t\tsourceCode.push(\"}\");\n\t\tsourceCode.push(\"return true;\");\n\n\t\tconst src = sourceCode.join(\"\\n\");\n\n\t\tconst FnClass = context.async ? AsyncFunction : Function;\n\t\tconst checkFn = new FnClass(\"value\", \"context\", src);\n\n\t\t/* istanbul ignore next */\n\t\tif (this.opts.debug) {\n\t\t\tconsole.log(this._formatter(\"// Main check function\\n\" + checkFn.toString())); // eslint-disable-line no-console\n\t\t}\n\n\t\tthis.cache.clear();\n\n\t\tconst resFn = function (data, opts) {\n\t\t\tcontext.data = data;\n\t\t\tif (opts && opts.meta)\n\t\t\t\tcontext.meta = opts.meta;\n\t\t\treturn checkFn.call(self, data, context);\n\t\t};\n\t\tresFn.async = context.async;\n\t\treturn resFn;\n\t}\n\n\t/**\n\t * Compile a rule to source code.\n\t * @param {Object} rule\n\t * @param {Object} context\n\t * @param {String} path\n\t * @param {String} innerSrc\n\t * @param {String} resVar\n\t * @returns {String}\n\t */\n\tcompileRule(rule, context, path, innerSrc, resVar) {\n\t\tconst sourceCode = [];\n\n\t\tconst item = this.cache.get(rule.schema);\n\t\tif (item) {\n\t\t\t// Handle cyclic schema\n\t\t\trule = item;\n\t\t\trule.cycle = true;\n\t\t\trule.cycleStack = [];\n\t\t\tsourceCode.push(this.wrapRequiredCheckSourceCode(rule, `\n\t\t\t\tvar rule = context.rules[${rule.index}];\n\t\t\t\tif (rule.cycleStack.indexOf(value) === -1) {\n\t\t\t\t\trule.cycleStack.push(value);\n\t\t\t\t\t${innerSrc.replace(/%%INDEX%%/g, rule.index)}\n\t\t\t\t\trule.cycleStack.pop(value);\n\t\t\t\t}\n\t\t\t`, context, resVar));\n\n\t\t} else {\n\t\t\tthis.cache.set(rule.schema, rule);\n\t\t\trule.index = context.index;\n\t\t\tcontext.rules[context.index] = rule;\n\n\t\t\tconst customPath = path != null ? path : \"$$root\";\n\n\t\t\tcontext.index++;\n\t\t\tconst res = rule.ruleFunction.call(this, rule, path, context);\n\t\t\tres.source = res.source.replace(/%%INDEX%%/g, rule.index);\n\t\t\tconst FnClass = context.async ? AsyncFunction : Function;\n\t\t\tconst fn = new FnClass(\"value\", \"field\", \"parent\", \"errors\", \"context\", \"label\", res.source);\n\t\t\tcontext.fn[rule.index] = fn.bind(this);\n\t\t\tsourceCode.push(this.wrapRequiredCheckSourceCode(rule, innerSrc.replace(/%%INDEX%%/g, rule.index), context, resVar));\n\t\t\tsourceCode.push(this.makeCustomValidator({vName: resVar, path: customPath, schema: rule.schema, context, messages: rule.messages, ruleIndex: rule.index}));\n\n\t\t\t/* istanbul ignore next */\n\t\t\tif (this.opts.debug) {\n\t\t\t\tconsole.log(this._formatter(`// Context.fn[${rule.index}]\\n` + fn.toString())); // eslint-disable-line no-console\n\t\t\t}\n\t\t}\n\n\t\treturn sourceCode.join(\"\\n\");\n\t}\n\n\t/**\n\t * Create a rule instance from schema definition.\n\t * @param {Object} schema\n\t * @returns {Object} rule\n\t */\n\tgetRuleFromSchema(schema) {\n\t\tschema = this.resolveType(schema);\n\n\t\tconst alias = this.aliases[schema.type];\n\t\tif (alias) {\n\t\t\tdelete schema.type;\n\t\t\tschema = deepExtend(schema, alias, { skipIfExist: true });\n\t\t}\n\n\t\tconst ruleFunction = this.rules[schema.type];\n\t\tif (!ruleFunction)\n\t\t\tthrow new Error(\"Invalid '\" + schema.type + \"' type in validator schema.\");\n\n\t\tconst rule = {\n\t\t\tmessages: Object.assign({}, this.messages, schema.messages),\n\t\t\tschema: deepExtend(schema, this.defaults[schema.type], { skipIfExist: true }),\n\t\t\truleFunction: ruleFunction,\n\t\t};\n\n\t\treturn rule;\n\t}\n\n\t/**\n\t * Parse rule from shorthand string\n\t * @param {String} str shorthand string\n\t * @param {Object} schema schema reference\n\t */\n\n\tparseShortHand(str) {\n\t\tconst p = str.split(\"|\").map((s) => s.trim());\n\t\tlet type = p[0];\n\t\tlet schema;\n\t\tif (type.endsWith(\"[]\")) {\n\t\t\tschema = this.getRuleFromSchema({ type: \"array\", items: type.slice(0, -2) }).schema;\n\t\t} else {\n\t\t\tschema = {\n\t\t\t\ttype: p[0],\n\t\t\t};\n\t\t}\n\n\t\tp.slice(1).forEach((s) => {\n\t\t\tconst idx = s.indexOf(\":\");\n\t\t\tif (idx !== -1) {\n\t\t\t\tconst key = s.substring(0, idx).trim();\n\t\t\t\tlet value = s.substring(idx + 1).trim();\n\t\t\t\tif (value === \"true\" || value === \"false\")\n\t\t\t\t\tvalue = value === \"true\";\n\t\t\t\telse if (!Number.isNaN(Number(value))) {\n\t\t\t\t\tvalue = Number(value);\n\t\t\t\t}\n\t\t\t\tschema[key] = value;\n\t\t\t} else {\n\t\t\t\t// boolean value\n\t\t\t\tif (s.startsWith(\"no-\")) schema[s.slice(3)] = false;\n\t\t\t\telse schema[s] = true;\n\t\t\t}\n\t\t});\n\n\t\treturn schema;\n\t}\n\n\t/**\n\t * Generate error source code.\n\t * @param {Object} opts\n\t * @param {String} opts.type\n\t * @param {String} opts.field\n\t * @param {any} opts.expected\n\t * @param {any} opts.actual\n\t * @param {Object} opts.messages\n\t */\n\tmakeError({ type, field, expected, actual, messages }) {\n\t\tconst o = {\n\t\t\ttype: `\"${type}\"`,\n\t\t\tmessage: `\"${messages[type]}\"`,\n\t\t};\n\t\tif (field) o.field = `\"${field}\"`;\n\t\telse o.field = \"field\";\n\t\tif (expected != null) o.expected = expected;\n\t\tif (actual != null) o.actual = actual;\n\t\to.label = \"label\";\n\n\t\tconst s = Object.keys(o)\n\t\t\t.map(key => `${key}: ${o[key]}`)\n\t\t\t.join(\", \");\n\n\t\treturn `errors.push({ ${s} });`;\n\t}\n\n\t/**\n\t * Generate custom validator function source code.\n\t * @param {Object} opts\n\t * @param {String} opts.vName\n\t * @param {String} opts.fnName\n\t * @param {String} opts.ruleIndex\n\t * @param {String} opts.path\n\t * @param {Object} opts.schema\n\t * @param {Object} opts.context\n \t * @param {Object} opts.messages\n\t */\n\tmakeCustomValidator({ vName = \"value\", fnName = \"custom\", ruleIndex, path, schema, context, messages }) {\n\t\tconst ruleVName = \"rule\" + ruleIndex;\n\t\tconst fnCustomErrorsVName = \"fnCustomErrors\" + ruleIndex;\n\n\t\tif (typeof schema[fnName] == \"function\" || (Array.isArray(schema[fnName]))) {\n\t\t\tif (context.customs[ruleIndex]) {\n\t\t\t\tcontext.customs[ruleIndex].messages = messages;\n\t\t\t\tcontext.customs[ruleIndex].schema = schema;\n\t\t\t} else {\n\t\t\t\tcontext.customs[ruleIndex] = { messages, schema };\n\t\t\t}\n\t\t\tconst ret = [];\n\t\t\tif (this.opts.useNewCustomCheckerFunction) {\n\t\t\t\tret.push( `\n               \t\tconst ${ruleVName} = context.customs[${ruleIndex}];\n\t\t\t\t\tconst ${fnCustomErrorsVName} = [];\n\t\t\t\t`);\n\n\t\t\t\tif(Array.isArray(schema[fnName])){\n\t\t\t\t\tfor (let i = 0; i < schema[fnName].length; i++) {\n\n\t\t\t\t\t\tlet custom = schema[fnName][i];\n\n\t\t\t\t\t\tif (typeof custom === \"string\") {\n\t\t\t\t\t\t\tcustom = this.parseShortHand(custom);\n\t\t\t\t\t\t\tschema[fnName][i] = custom;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst customIndex = ruleIndex*1000+i;\n\t\t\t\t\t\tcontext.customs[customIndex] = { messages, schema: Object.assign({}, schema, { custom, index: i }) };\n\n\t\t\t\t\t\tret.push( `\n\t\t\t\t\t\t\tconst ${ruleVName}_${i} = context.customs[${customIndex}];\n\n\t\t\t\t\t \t`);\n\n\t\t\t\t\t\tif(custom.type){\n\t\t\t\t\t\t\tret.push( `\n\t\t\t\t\t\t\t ${vName} = ${context.async ? \"await \" : \"\"}context.customFunctions[${ruleVName}.schema.${fnName}[${i}].type].call(this, ${vName}, ${fnCustomErrorsVName} , ${ruleVName}_${i}.schema, \"${path}\", parent, context);\n\t\t\t\t\t\t\t`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(typeof custom===\"function\"){\n\t\t\t\t\t\t\tret.push( `\n\t\t\t\t\t\t\t${vName} = ${context.async ? \"await \" : \"\"}${ruleVName}.schema.${fnName}[${i}].call(this, ${vName}, ${fnCustomErrorsVName} , ${ruleVName}.schema, \"${path}\", parent, context);\n\t\t\t\t\t\t\t`);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}else{\n\t\t\t\t\tret.push( `\n\t\t\t\t\t${vName} = ${context.async ? \"await \" : \"\"}${ruleVName}.schema.${fnName}.call(this, ${vName}, ${fnCustomErrorsVName} , ${ruleVName}.schema, \"${path}\", parent, context);\n\t\t\t\t\t`);\n\t\t\t\t}\n\n\t\t\t\tret.push( `\n\t\t\t\t\tif (Array.isArray(${fnCustomErrorsVName} )) {\n                  \t\t${fnCustomErrorsVName} .forEach(err => errors.push(Object.assign({ message: ${ruleVName}.messages[err.type], field }, err)));\n\t\t\t\t\t}\n\t\t\t\t`);\n\t\t\t}else{\n\t\t\t\tconst result = \"res_\" + ruleVName;\n\t\t\t\tret.push( `\n\t\t\t\t\tconst ${ruleVName} = context.customs[${ruleIndex}];\n\t\t\t\t\tconst ${result} = ${context.async ? \"await \" : \"\"}${ruleVName}.schema.${fnName}.call(this, ${vName}, ${ruleVName}.schema, \"${path}\", parent, context);\n\t\t\t\t\tif (Array.isArray(${result})) {\n\t\t\t\t\t\t${result}.forEach(err => errors.push(Object.assign({ message: ${ruleVName}.messages[err.type], field }, err)));\n\t\t\t\t\t}\n\t\t\t`);\n\t\t\t}\n\t\t\treturn ret.join(\"\\n\");\n\n\t\t}\n\t\treturn \"\";\n\t}\n\n\t/**\n\t * Add a custom rule\n\t *\n\t * @param {String} type\n\t * @param {Function} fn\n\t */\n\tadd(type, fn) {\n\t\tthis.rules[type] = fn;\n\t}\n\n\t/**\n\t * Add a custom function\n\t *\n\t * @param {String} type\n\t * @param {Function} fn\n\t */\n\taddCustomFunction(name, fn) {\n\t\tthis.customFunctions[name] = fn;\n\t}\n\n\t/**\n\t * Add a message\n\t *\n\t * @param {String} name\n\t * @param {String} message\n\t */\n\taddMessage(name, message) {\n\t\tthis.messages[name] = message;\n\t}\n\n\t/**\n\t * create alias name for a rule\n\t *\n\t * @param {String} name\n\t * @param validationRule\n\t */\n\talias(name, validationRule) {\n\t\tif (this.rules[name]) throw new Error(\"Alias name must not be a rule name\");\n\t\tthis.aliases[name] = validationRule;\n\t}\n\n\t/**\n\t * Add a plugin\n\t *\n\t * @param {Function} fn\n\t */\n\tplugin(fn) {\n\t\tif (typeof fn !== \"function\") throw new Error(\"Plugin fn type must be function\");\n\t\treturn fn(this);\n\t}\n\n\t/**\n\t * Resolve the schema 'type' by:\n\t * - parsing short hands into full type definitions\n\t * - expanding arrays into 'multi' types with a rules property\n\t * - objects which have a root $$type property into a schema which\n\t *   explicitly has a 'type' property and a 'props' property.\n\t *\n\t * @param schema The schema to resolve the type of\n\t */\n\tresolveType(schema) {\n\t\tif (typeof schema === \"string\") {\n\t\t\tschema = this.parseShortHand(schema);\n\t\t} else if (Array.isArray(schema)) {\n\t\t\tif (schema.length === 0)\n\t\t\t\tthrow new Error(\"Invalid schema.\");\n\n\t\t\tschema = {\n\t\t\t\ttype: \"multi\",\n\t\t\t\trules: schema\n\t\t\t};\n\n\t\t\t// Check 'optional' flag\n\t\t\tconst isOptional = schema.rules\n\t\t\t\t.map(s => this.getRuleFromSchema(s))\n\t\t\t\t.every(rule => rule.schema.optional === true);\n\t\t\tif (isOptional)\n\t\t\t\tschema.optional = true;\n\n\t\t\t// Check 'nullable' flag\n\t\t\tconst nullCheck = this.opts.considerNullAsAValue ? false : true;\n\t\t\tconst setNullable = schema.rules\n\t\t\t\t.map(s => this.getRuleFromSchema(s))\n\t\t\t\t.every(rule => rule.schema.nullable === nullCheck);\n\t\t\tif (setNullable)\n\t\t\t\tschema.nullable = nullCheck;\n\t\t}\n\n\t\tif (schema.$$type) {\n\t\t\tconst type = schema.$$type;\n\t\t\tconst otherShorthandProps = this.getRuleFromSchema(type).schema;\n\t\t\tdelete schema.$$type;\n\t\t\tconst props = Object.assign({}, schema);\n\n\t\t\tfor (const key in schema) {  // clear object without changing reference\n\t\t\t\tdelete schema[key];\n\t\t\t}\n\n\t\t\tdeepExtend(schema, otherShorthandProps, { skipIfExist: true });\n\t\t\tschema.props = props;\n\t\t}\n\n\t\treturn schema;\n\t}\n\n\t/**\n\t * Normalize a schema, type or short hand definition by expanding it to a full form. The 'normalized'\n\t * form is the equivalent schema with any short hands undone. This ensure that each rule; always includes\n\t * a 'type' key, arrays always have an 'items' key, 'multi' always have a 'rules' key and objects always\n\t * have their properties defined in a 'props' key\n\t *\n\t * @param {Object|String} value The value to normalize\n\t * @returns {Object} The normalized form of the given rule or schema\n\t */\n\tnormalize(value) {\n\t\tlet result = this.resolveType(value);\n\t\tif(this.aliases[result.type])\n\t\t\tresult = deepExtend(result, this.normalize(this.aliases[result.type]), { skipIfExists: true});\n\n\t\tresult = deepExtend(result, this.defaults[result.type], { skipIfExist: true });\n\n\t\tif(result.type === \"multi\") {\n\t\t\tresult.rules = result.rules.map(r => this.normalize(r));\n\t\t\tresult.optional = result.rules.every(r => r.optional === true);\n\t\t\treturn result;\n\t\t}\n\t\tif(result.type === \"array\") {\n\t\t\tresult.items = this.normalize(result.items);\n\t\t\treturn result;\n\t\t}\n\t\tif(result.type === \"object\") {\n\t\t\tif(result.props) {\n\t\t\t\tObject.entries(result.props).forEach(([k,v]) => result.props[k] = this.normalize(v));\n\t\t\t}\n\t\t}\n\t\tif(typeof value === \"object\") {\n\t\t\tif(value.type) {\n\t\t\t\tconst config = this.normalize(value.type);\n\t\t\t\tdeepExtend(result, config, { skipIfExists: true });\n\t\t\t}\n\t\t\telse{\n\t\t\t\tObject.entries(value).forEach(([k,v]) => result[k] = this.normalize(v));\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t}\n}\n\nmodule.exports = Validator;\n", "module.exports = require(\"./lib/validator.js\");"], "names": ["let", "const", "this$1", "this", "padChar", "PATTERN", "require", "require$$0", "require$$1", "require$$2", "require$$3", "require$$4", "require$$5", "require$$6", "require$$7", "require$$8", "require$$9", "require$$10", "require$$11", "require$$12", "require$$13", "require$$14", "require$$15", "require$$16", "require$$17", "require$$18", "require$$19", "require$$20", "require$$21", "require$$22", "require$$23", "deepExtend", "require$$24", "rule"], "mappings": ";;;;;;CAEA,SAAS,eAAe,CAAC,CAAC,EAAE;CAC5B,CAAC,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,EAAA,OAAO,KAAK,CAAC,EAAA;CAC1E,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;CAClC,CAAC;AACD;CACA,SAAS,UAAU,CAAC,WAAW,EAAE,MAAM,EAAE,OAAY,EAAE;mCAAP,GAAG,EAAA,CAAA;AAAI;CACvD,CAAC,KAAKA,IAAI,QAAQ,IAAI,MAAM,EAAE;CAC9B,EAAE,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE;CACzC,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;CACvD,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE,OAAO,CAAC,CAAC;CAChE,GAAG,MAAM;CACT,GAAG,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,SAAS,IAAE,SAAS,EAAA;CACrF,GAAG,WAAW,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;CAC5C,GAAG;CACH,EAAE;CACF,CAAC,OAAO,WAAW,CAAC;CACpB,CAAC;AACD;CACA,IAAA,YAAc,GAAG,UAAU;;CCpB3B,SAAS,WAAW,CAAC,KAAK,EAAE;CAC5B,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE,EAAA,OAAO,EAAE,CAAC,EAAA;CACpC,CAAC,IAAI,KAAK,KAAK,IAAI,EAAE,EAAA,OAAO,EAAE,CAAC,EAAA;CAC/B,CAAC,IAAI,OAAO,KAAK,CAAC,QAAQ,KAAK,UAAU,EAAA,EAAE,OAAO,KAAK,CAAC,EAAA;CACxD,CAAC,OAAO,OAAO,KAAK,CAAC;CACrB,CAAC;AACD;CACA,IAAA,OAAc,aAAI,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAK,EAAA,OAAA,MAAM,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC,QAAQ,CAAC,IAAC;;CCLtG,IAAA,QAAc,GAAG;CACjB,CAAC,QAAQ,EAAE,kCAAkC;AAC7C;CACA,CAAC,MAAM,EAAE,uCAAuC;CAChD,CAAC,WAAW,EAAE,wCAAwC;CACtD,CAAC,SAAS,EAAE,yFAAyF;CACrG,CAAC,SAAS,EAAE,sFAAsF;CAClG,CAAC,YAAY,EAAE,gEAAgE;CAC/E,CAAC,aAAa,EAAE,0DAA0D;CAC1E,CAAC,cAAc,EAAE,yDAAyD;CAC1E,CAAC,UAAU,EAAE,+DAA+D;CAC5E,CAAC,aAAa,EAAE,+CAA+C;CAC/D,CAAC,WAAW,EAAE,mDAAmD;CACjE,CAAC,cAAc,EAAE,qDAAqD;CACtE,CAAC,eAAe,EAAE,kDAAkD;CACpE,CAAC,SAAS,EAAE,2CAA2C;CACvD,CAAC,gBAAgB,EAAE,mDAAmD;CACtE,CAAC,YAAY,EAAE,8CAA8C;AAC7D;CACA,CAAC,MAAM,EAAE,uCAAuC;CAChD,CAAC,SAAS,EAAE,kEAAkE;CAC9E,CAAC,SAAS,EAAE,+DAA+D;CAC3E,CAAC,WAAW,EAAE,kDAAkD;CAChE,CAAC,cAAc,EAAE,mDAAmD;CACpE,CAAC,aAAa,EAAE,yCAAyC;CACzD,CAAC,cAAc,EAAE,gDAAgD;CACjE,CAAC,cAAc,EAAE,gDAAgD;AACjE;CACA,CAAC,KAAK,EAAE,uCAAuC;CAC/C,CAAC,UAAU,EAAE,iDAAiD;CAC9D,CAAC,QAAQ,EAAE,6DAA6D;CACxE,CAAC,QAAQ,EAAE,0EAA0E;CACrF,CAAC,WAAW,EAAE,oDAAoD;CAClE,CAAC,aAAa,EAAE,yDAAyD;CACzE,CAAC,WAAW,EAAE,kFAAkF;CAChG,CAAC,SAAS,EAAE,wFAAwF;AACpG;CACA,CAAC,KAAK,EAAE,uCAAuC;CAC/C,CAAC,UAAU,EAAE,iDAAiD;CAC9D,CAAC,WAAW,EAAE,oDAAoD;AAClE;CACA,CAAC,OAAO,EAAE,wCAAwC;AAClD;CACA,CAAC,QAAQ,EAAE,+CAA+C;AAC1D;CACA,CAAC,IAAI,EAAE,qCAAqC;CAC5C,CAAC,OAAO,EAAE,kEAAkE;CAC5E,CAAC,OAAO,EAAE,+DAA+D;AACzE;CACA,CAAC,SAAS,EAAE,kFAAkF;AAC9F;CACA,CAAC,UAAU,EAAE,0DAA0D;CACvE,CAAC,UAAU,EAAE,sEAAsE;AACnF;CACA,CAAC,SAAS,EAAE,mCAAmC;AAC/C;CACA,CAAC,QAAQ,EAAE,yCAAyC;AACpD;CACA,CAAC,KAAK,EAAE,6CAA6C;CACrD,CAAC,UAAU,EAAE,wCAAwC;CACrD,CAAC,QAAQ,EAAE,yFAAyF;CACpG,CAAC,QAAQ,EAAE,sFAAsF;AACjG;CACA,CAAC,IAAI,EAAE,oDAAoD;AAC3D;CACA,CAAC,GAAG,EAAE,kDAAkD;AACxD;CACA,CAAC,MAAM,EAAE,kCAAkC;CAC3C,CAAC,YAAY,EAAE,2DAA2D;CAC1E,CAAC,cAAc,EAAE,mEAAmE;CACpF,CAAC,cAAc,EAAE,kEAAkE;AACnF;CACA,CAAC,GAAG,EAAE,0CAA0C;CAChD,CAAC,QAAQ,EAAE,wCAAwC;AACnD;CACA,CAAC,IAAI,EAAE,2CAA2C;CAClD,CAAC,WAAW,EAAE,4DAA4D;AAC1E;CACA,CAAC,eAAe,EAAE,oEAAoE;AACtF;CACA,CAAC,QAAQ,EAAE,+CAA+C;AAC1D;CACA,CAAC,MAAM,EAAE,kCAAkC;CAC3C,CAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CCnFD;CACA;CACA,IAAA,GAAc,GAAG,kDAAkD;CACnE,CAACC,IAAM,GAAG,GAAG,EAAE,CAAC;CAChB,CAAC,GAAG,CAAC,IAAI,CAAC,yBAER,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCXD;CACA;CACA,IAAc,KAAA,GAAG,UAA8B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAChE,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;CACvB,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB;CACA,EAAE,GAAG,CAAC,IAAI,CAAC,gGAIR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,kDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAK/D,iEAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;CAC7B,EAAE,GAAG,CAAC,IAAI,0CAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAEpE,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,0BACK,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACnB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAErF,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,0BACK,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACnB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAErF,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;CAC5B,EAAE,GAAG,CAAC,IAAI,4BACO,MAAM,CAAC,MAAM,CAAA,GAAA,eAAA,IACxB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE3F,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,EACa,4BAAA,IAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,EAAC,GAChD,uBAAA,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEjH,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;CAC7B,EAAE,GAAG,CAAC,IAAI,EAAC,qDAAA,IAEL,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,mFAAmF,EAAE,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAEpK,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;CAC1B,EAAEC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CAC9C,EAAE,GAAG,CAAC,IAAI,EAEA,gEAAA,GAAA,OAAO,iDACV,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,4BAAA,EAGtH,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;CAC3B,EAAE,GAAG,CAAC,IAAI,CAAC,uIAKR,CAAC,CAAC;AACL;CACA,EAAEA,IAAM,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;CAC/B,EAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CACpD;CACA,EAAEA,IAAM,WAAW,GAAG,WAAY,IAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAA,CAAE,mHAA0G,CAAC;CAC1K,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC;CAC7E,EAAE,GAAG,CAAC,IAAI,CAAC,iBAER,CAAC,CAAC;CACL,EAAE,GAAG,CAAC,IAAI,CAAC,uBAET,CAAC,CAAC;CACJ,EAAE,MAAM;CACR,EAAE,GAAG,CAAC,IAAI,CAAC,yBAET,CAAC,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCjHD;CACA;CACA,IAAc,SAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;CAChB,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;AACvB;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,kCAER,CAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,mZAkBR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,yDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,SAAS,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAItE,sCAAA,EAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC7CD;CACA;CACA,IAAA,MAAc,GAAG,SAAoC,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAAlC,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;CAAU,CAAA,IAAA,QAAA,GAAA,GAAA,CAAA,QAAA,CAAA;;AAAwB;CACtE,CAACC,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAACA,IAAM,SAAS,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,GAAG,eAAe,CAAC;CACrF,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAA,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,MAAA,EAAA,MAAM,EAAE,CAAC,EAAA;CAClE,MAAM,EAAA,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,EAAA;AAC7C;CACA,CAAC,GAAG,CAAC,IAAI,oDACkC,KAAK,GAAA,+BAAA,IAC3C,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,GAAG,SAAS,GAAG,GAAG,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,MAAA,EAC1G,CAAC;AACJ;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,yBAER,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCrBD,IAAA,MAAc,GAAG,UAAqC,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAAlC,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;CAAU,CAAA,IAAA,QAAA,GAAA,GAAA,CAAA,QAAA,CAAA;;AAAwB;CACvE,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,EACL,QAAA,IAAA,IAAI,CAAC,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAA,IAAA,EAAE,IAAI,EAAA,MAAA,EAAE,MAAM,EAAA,QAAA,EAAE,QAAQ,EAAA,OAAA,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAA,GAAA,yBAAA,EAEjG,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCZDA,IAAM,cAAc,GAAG,sEAAsE,CAAC;CAC9F;CACA;AACA;CACA,IAAc,QAAA,GAAG,UAA4B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA1B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA0B;CAC9D,CAACA,IAAM,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC;CACtD,CAACA,IAAM,iBAAiB,GAAG,MAAM,CAAC,iBAAiB,IAAI,GAAG,CAAC;CAC3D,CAACA,IAAM,gBAAgB,GAAG,MAAM,CAAC,gBAAgB,IAAI,GAAG,CAAC;CACzD,CAACA,IAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;CACxC,CAACD,IAAI,yBAAyB,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC;CACxD,CAACA,IAAI,UAAU,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,cAAc,KAAS,IAAA,GAAA,cAAA,KAAkB,yBAAyB,GAAG,EAAE,GAAG,GAAG,EAAG,KAAI,EAAE,CAAC;CACvI,GAAG,OAAO,CAAC,IAAI,EAAE,iBAAiB,CAAC;CACnC,GAAG,OAAO,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACnC;AACA;CACA,CAACC,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,EACY,yBAAA,IAAA,WAAW,IAAI,IAAI,MAAM,CAAC,UAAU,CAAA,CAAC,qBACrD,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,uDAAA,EAKjE,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC5BD;CACA;CACA,IAAc,IAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;CAChB,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;AACvB;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,kCAER,CAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,oIAIR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,6EAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGnE,2BAAA,EAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC9BDC,IAAM,eAAe,GAAG,uJAAuJ,CAAC;CAChLA,IAAM,aAAa,GAAG,gBAAgB,CAAC;AACvC;CACA;CACA;CACA,IAAc,KAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAACA,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,IAAI,SAAS,GAAG,eAAe,GAAG,aAAa,CAAC;CAC5E,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;AACvB;CACA,CAAC,GAAG,CAAC,IAAI,wDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGjE,kCAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;CACpB,EAAE,GAAG,CAAC,IAAI,mDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGpE,wCAAA,EAAC,CAAC;CACL,EAAE,MAAM;CACR,EAAE,GAAG,CAAC,IAAI,CAAC,qDAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE;CACvB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,mDAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,mCACc,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,cAAc,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE9F,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,mCACc,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,cAAc,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE9F,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,EACA,aAAA,IAAA,OAAO,CAAC,QAAQ,EAAE,CAAA,GAAA,yBAAA,IACtB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,GAAG,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAIhE,kCAAA,EAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CClED;CACA;CACA,IAAc,KAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC;CACrD,CAAC,OAAO;CACR,EAAE,MAAM,GAAE,cAAA,GACD,OAAO,GAAA,mCAAA,IACV,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,EAAC,GAGrH,qCAAA,CAAA;CACH,EAAE,CAAC;CACH,CAAC;;CCZD;CACA;CACA,IAAc,KAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE;CACnB,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;CACrB,GAAG,GAAG,CAAC,IAAI,EAAC,mCAAA,IACgB,MAAM,CAAC,KAAA,CAAK,GACpC,cAAA,EAAC,CAAC;CACN,GAAG,MAAM;CACT,GAAG,GAAG,CAAC,IAAI,EAAC,kCAAA,IACe,MAAM,CAAC,KAAA,CAAK,GACnC,cAAA,EAAC,CAAC;CACN,GAAG;CACH,EAAE,GAAG,CAAC,IAAI,kBACJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,QAAA,EAC5G,CAAC;CACL,EAAE,MAAM;CACR,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;CACrB,GAAG,GAAG,CAAC,IAAI,gCACS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,GAAA,WAAA,EAC3C,CAAC;CACN,GAAG,MAAM;CACT,GAAG,GAAG,CAAC,IAAI,+BACQ,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA,GAAA,WAAA,EAC1C,CAAC;CACN,GAAG;CACH,EAAE,GAAG,CAAC,IAAI,kBACJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,GAAG,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,QAAA,EAC5G,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,yBAER,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCxCD;CACA;CACA,IAAA,SAAc,GAAG,SAAS,cAAc,CAAA,GAAqB,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC9E,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,wDAER,CAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;CACpB,EAAE,GAAG,CAAC,IAAI,CAAC,iCAER,CAAC,CAAC;AACL;CACA,EAAE,MAAM;CACR,EAAE,GAAG,CAAC,IAAI,gBACL,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GACnE,QAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,kCAIR,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC7BD;CACA;CACA,IAAc,SAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,GAAA,CAAA,OAAA;;AAA2B;CAC/D,CAAC,OAAO;CACR,EAAE,MAAM,GAAE,sDAAA,IAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,GAAG,MAAM,EAAE,OAAO,YAAE,QAAQ,EAAE,EAAC,GAGnE,+BAAA,CAAA;CACH,EAAE,CAAC;CACH,CAAC;;CCXD;CACA;CACA,IAAc,KAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,0HAKR,CAAC,CAAC;AACJ;CACA,CAAC,KAAKD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC/C,EAAE,GAAG,CAAC,IAAI,CAAC,2DAGR,CAAC,CAAC;AACL;CACA,EAAEC,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACvD,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,GAAE,eAAA,IAAgB,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAA,GAAA,gEAAA,GAAkE,QAAQ,CAAC,CAAC,CAAC;CAC3K,EAAE,GAAG,CAAC,IAAI,CAAC,4QAQR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,iHAMR,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC1CD;CACA;CACA,IAAc,MAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC/D,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,kCAER,CAAC,CAAC;AACJ;CACA,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;CACvB,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,2FAIR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,4FAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGrE,kCAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,4BACO,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACrB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE5F,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,4BACO,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACrB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,WAAW,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE5F,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;CAC3B,EAAE,GAAG,CAAC,IAAI,8BACS,MAAM,CAAC,KAAK,CAAA,GAAA,eAAA,IACzB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEhG,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,8BACS,MAAM,CAAC,QAAQ,CAAA,GAAA,eAAA,IAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEtG,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,gDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,eAAe,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAE5E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;CAC/B,EAAE,GAAG,CAAC,IAAI,2CAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAE7E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;CAC/B,EAAE,GAAG,CAAC,IAAI,2CAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,GAAG,MAAM,EAAE,WAAW,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAE7E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,yBAER,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC/FD;CACA;CACAC,IAAM,eAAe,GAAG,4BAA4B,CAAC;AACrD;CACA;CACAA,IAAM,eAAe,GAAG,yBAAyB,CAAC;AAClD;CACA;CACA,SAAS,gBAAgB,CAAC,GAAG,EAAE;CAC/B;CACA,CAAC,OAAO,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,UAAU,SAAS,EAAE;CAC1D,EAAE,QAAQ,SAAS;CACnB,EAAE,KAAK,IAAI,CAAC;CACZ,EAAE,KAAK,GAAG,CAAC;CACX,EAAE,KAAK,IAAI;CACX,GAAG,OAAO,IAAI,GAAG,SAAS,CAAC;CAC3B;CACA,EAAE,KAAK,IAAI;CACX,GAAG,OAAO,KAAK,CAAC;CAChB,EAAE,KAAK,IAAI;CACX,GAAG,OAAO,KAAK,CAAC;CAChB,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,SAAS,CAAC;CACpB,EAAE,KAAK,QAAQ;CACf,GAAG,OAAO,SAAS,CAAC;CACpB,GAAG;CACH,EAAE,CAAC,CAAC;CACJ,CAAC;AACD;CACA;CACA;CACA,IAAc,MAAA,GAAG,UAAA,GAA8B,EAAE,IAAI,EAAE,OAAO,EAAE;CAAnC,CAAA,IAAAC,QAAA,GAAA,IAAA,CAAA;CAAQ,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAChE,CAACD,IAAM,UAAU,GAAG,EAAE,CAAC;AACvB;CACA,CAAC,UAAU,CAAC,IAAI,kGAEX,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGhE,kCAAA,EAAC,CAAC;AACJ;CACA,CAACA,IAAM,SAAS,GAAG,MAAM,CAAC,UAAU,IAAI,MAAM,CAAC,KAAK,CAAC;CACrD,CAAC,IAAI,SAAS,EAAE;CAChB,EAAE,UAAU,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;CAC5C,EAAE,UAAU,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAC9C;CACA,EAAEA,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,UAAA,GAAA,EAAO,EAAA,OAAA,CAACE,QAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAA,EAAA,CAAC,CAAC;AAC1E;CACA,EAAE,KAAKH,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACxC,GAAGC,IAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CAC5B,GAAGA,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC;CAC5D;CACA,GAAGA,IAAM,IAAI,GAAG,gBAAgB,CAAC,QAAQ,CAAC,CAAC;CAC3C,GAAGA,IAAM,WAAW,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,IAAO,GAAA,GAAA,IAAA,KAAc,IAAA,GAAA,IAAI,QAAI,CAAC;CAC/E,GAAGA,IAAM,YAAY,GAAG,WAAA,GAAY,WAAa,CAAC;CAClD,GAAGA,IAAM,OAAO,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,IAAI,QAAQ,CAAC;AACvD;CACA,GAAGA,IAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;CACvC,GAAGA,IAAM,KAAK,GAAG,SAAS,IAAG,GAAA,IAAI,gBAAgB,CAAC,SAAS,CAAA,CAAC,GAAG,GAAA,IAAG,SAAS,CAAC;AAC5E;CACA,GAAG,UAAU,CAAC,IAAI,EAAC,cAAA,IAAe,gBAAgB,CAAC,OAAO,CAAG,CAAA,EAAC,CAAC;CAC/D,GAAG,UAAU,CAAC,IAAI,EAAC,wCAAA,GAAwC,WAAW,GAAQ,SAAA,GAAA,IAAI,GAAI,KAAA,EAAC,CAAC;CACxF,GAAG,UAAU,CAAC,IAAI,eAAY,YAAY,GAAA,GAAA,EAAI,CAAC;CAC/C,GAAG,UAAU,CAAC,IAAI,EAAY,UAAA,GAAA,KAAA,EAAQ,CAAC;CACvC,GAAGA,IAAM,WAAW,GAAG,eACjB,YAAY,GAAA,KAAA,IAAM,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAA,CAAE,oFACjD,CAAC;CACL,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,CAAC;CACxF,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE;CAC5C,IAAI,UAAU,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;CAC5D,IAAI;CACJ,GAAG;AACH;CACA;CACA,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;CACrB,GAAGA,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC/C;CACA,GAAG,UAAU,CAAC,IAAI,EAMP,iLAAA,IAAA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAA,GAAA,4IAAA,EAKlC,CAAC;CACN,GAAG,IAAI,MAAM,CAAC,MAAM,KAAK,QAAQ,EAAE;CACnC,IAAI,UAAU,CAAC,IAAI,CAAC,kDAEf,CAAC,CAAC;CACP,IAAI,UAAU,CAAC,IAAI,CAAC,yHAIf,CAAC,CAAC;CACP,IAAI,UAAU,CAAC,IAAI,CAAC,yBAEf,CAAC,CAAC;CACP,IAAI,MAAM;CACV,IAAI,UAAU,CAAC,IAAI,EAAC,cAAA,IACb,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,yBAAyB,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,YAAA,EACvI,CAAC;CACP,IAAI;CACJ,GAAG,UAAU,CAAC,IAAI,CAAC,qBAEf,CAAC,CAAC;CACN,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CACzD;CACA;CACA;CACA;CACA,EAAE,IAAI,MAAM,CAAC,MAAM,EAAE;CACrB,GAAG,UAAU,CAAC,IAAI,EACQ,gCAAA,IAAA,SAAS,GAAG,WAAW,GAAG,OAAA,CAAO,GACvD,YAAA,EAAC,CAAC;CACN,GAAG,MAAM;CACT,GAAG,UAAU,CAAC,IAAI,EACY,oCAAA,IAAA,SAAS,GAAG,WAAW,GAAG,OAAO,CAAA,GAAA,cAAA,IACzD,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAA,GAAA,UAAA,EACxC,CAAC;CACN,GAAG;CACH,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CAC9B,EAAE,UAAU,CAAC,IAAI,mCACO,MAAM,CAAC,QAAQ,CAAA,GAAA,eAAA,IACjC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEzG,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CAC9B,EAAE,UAAU,CAAC,IAAI,mCACO,MAAM,CAAC,QAAQ,CAAA,GAAA,eAAA,IACjC,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEzG,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,SAAS,EAAE;CAChB,EAAE,UAAU,CAAC,IAAI,CAAC,iCAEf,CAAC,CAAC;CACL,EAAE,MAAM;CACR,EAAE,UAAU,CAAC,IAAI,CAAC,6BAEf,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;CAC/B,EAAE,CAAC;CACH,CAAC;;CC9JD;CACA;CACA,IAAA,QAAc,GAAG,SAAoC,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAAlC,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;CAAU,CAAA,IAAA,QAAA,GAAA,GAAA,CAAA,QAAA,CAAA;;AAAwB;CACtE,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,EAAA,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,MAAA,EAAA,MAAM,EAAE,CAAC,EAAA;CAClE,MAAM,EAAA,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,EAAA;AAC7C;CACA,CAAC,GAAG,CAAC,IAAI,EAAC,yCAAA,GAC2B,KAAK,GAErC,iEAAA,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAGlE,4BAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,EAAA,GAAG,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,EAAA;CACrE,MAAM,IAAI,MAAM,CAAC,OAAO,KAAK,WAAW,EAAE,EAAA,GAAG,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAA;CAC9E,MAAA,EAAM,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAA;AAC/B;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCzBD,SAAS,oBAAoB,CAAC,IAAI,EAAE;CACpC,CAAC,KAAKA,IAAM,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;CACnC,EAAE,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;CACjC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;CACzE,GAAG;CACH,EAAE;CACF,CAAC;AACD;CACA,IAAA,MAAc,GAAG,SAAS,iBAAiB,CAAA,GAAqB,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CACjF,CAACA,IAAM,UAAU,GAAG,EAAE,CAAC;CACvB,CAAC,UAAU,CAAC,IAAI,kGAEX,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGhE,kCAAA,EAAC,CAAC;AACJ;CACA,CAACA,IAAM,WAAW,GAAG,MAAM,CAAC,GAAG,IAAI,QAAQ,CAAC;CAC5C,CAACA,IAAM,aAAa,GAAG,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC;AAC7C;CACA,CAAC,UAAU,CAAC,IAAI,CAAC,8HAKf,CAAC,CAAC;AACJ;CACA,CAAC,UAAU,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;AAChD;CACA,CAACA,IAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;CACrD,CAAC,oBAAoB,CAAC,OAAO,CAAC,CAAC;CAC/B,CAACA,IAAM,cAAc,GAAG,uBACL,IAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAA,CAAE,gGAC9C,CAAC;CACH,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC,CAAC;CAC3F,CAAC,UAAU,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;AAC1D;CACA,CAACA,IAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;CACzD,CAACA,IAAM,gBAAgB,GAAG,yBACL,IAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAA,CAAE,kGAChD,CAAC;CACH,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,GAAK,IAAI,aAAS,gBAAgB,EAAE,gBAAgB,CAAC,CAAC,CAAC;CAC3G,CAAC,UAAU,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;CAC3D,CAAC,UAAU,CAAC,IAAI,CAAC,aAEf,CAAC,CAAC;CACJ,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACnC;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;CAC/B,EAAE,CAAC;CACH,CAAC;;CCjDDA,IAAM,eAAe,GAAG,sBAAsB,CAAC;CAC/CA,IAAM,aAAa,GAAG,aAAa,CAAC;CACpCA,IAAM,gBAAgB,GAAG,gBAAgB,CAAC;CAC1CA,IAAM,iBAAiB,GAAG,kBAAkB,CAAC;CAC7CA,IAAM,WAAW,GAAG,gBAAgB,CAAC;CACrCA,IAAM,cAAc,GAAG,sEAAsE,CAAC;AAC9F;CACA;CACA;CACA,IAAA,MAAc,GAAG,SAAS,WAAW,CAAA,GAAqB,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAC3E,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;CAChB,CAACD,IAAI,SAAS,GAAG,KAAK,CAAC;AACvB;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,2FAIR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,wDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAKhE,gEAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE;CAClB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,qCAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE;CACtB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,yCAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE;CACvB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,0CAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,EAAE;CACtB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAEC,IAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;CAChE,EAAE,GAAG,CAAC,IAAI,EAAC,iCAAA,IACiB,MAAM,CAAC,QAAA,CAAQ,GAAK,IAAA,IAAA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA,GAAA,UAAA,EACnE,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,EAAE;CACpB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAEA,IAAMG,SAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;CAChE,EAAE,GAAG,CAAC,IAAI,EAAC,+BAAA,IACe,MAAM,CAAC,MAAA,CAAM,GAAK,IAAA,IAAA,IAAI,CAAC,SAAS,CAACA,SAAO,CAAC,CAAA,GAAA,UAAA,EAC/D,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE;CACvB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,4CAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,SAAS,EAAE;CACvB,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,4CAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE;CAC7B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,kDAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,eAAe,EAAE;CAC7B,EAAE,SAAS,GAAG,IAAI,CAAC;CACnB,EAAE,GAAG,CAAC,IAAI,CAAC,kDAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,qCAER,CAAC,CAAC;AACJ;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;CAC7B,EAAE,GAAG,CAAC,IAAI,0CAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAEtE,iBAAA,EAAC,CAAC;CACL,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;CACnC,EAAE,GAAG,CAAC,IAAI,CAAC,gEAIR,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,0BACK,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACnB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEtF,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,0BACK,MAAM,CAAC,GAAG,CAAA,GAAA,eAAA,IACnB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEtF,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE;CAC5B,EAAE,GAAG,CAAC,IAAI,4BACO,MAAM,CAAC,MAAM,CAAA,GAAA,eAAA,IACxB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAE5F,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE;CAC7B,EAAEJ,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;CAC/B,EAAE,IAAI,OAAO,MAAM,CAAC,OAAO,IAAI,QAAQ;CACvC,GAAA,EAAG,OAAO,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,EAAA;AAC7D;CACA,EAAE,GAAG,CAAC,IAAI,qBACA,OAAO,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IACtB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,GAAE,IAAA,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA,GAAA,IAAA,CAAG,EAAE,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAErI,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,EAAC,8BAAA,IACa,MAAM,CAAC,QAAA,CAAQ,GACjC,yBAAA,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEpH,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;CAC1B,EAAEC,IAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CAC9C,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,GAAA,OAAO,4CACV,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAEvH,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,EACA,eAAA,IAAA,eAAe,CAAC,QAAQ,EAAE,CAAA,GAAA,4BAAA,IAC9B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,eAAe,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAE5E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,IAAI,EAAE;CAC3B,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,IAAA,aAAa,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IAC3B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAE1E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE;CAC9B,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,IAAA,gBAAgB,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IAC9B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,gBAAgB,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAE7E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,MAAM,CAAC,SAAS,KAAK,IAAI,EAAE;CAC/B,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,IAAA,iBAAiB,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IAC/B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,iBAAiB,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAE9E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,MAAM,CAAC,GAAG,KAAK,IAAI,EAAE;CACzB,EAAE,GAAG,CAAC,IAAI,EACyB,wCAAA,IAAA,WAAW,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IACnD,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,WAAW,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAExE,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,MAAM,CAAC,UAAU,KAAK,IAAI,EAAE;CAChC,EAAE,GAAG,CAAC,IAAI,EAEJ,iDAAA,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,iBAAA,EAExD,CAAC;CACL,EAAE;AACF;AACA;CACA,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;CAC5B,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,IAAA,cAAc,CAAC,QAAQ,EAAE,CAAA,GAAA,2BAAA,IAC5B,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,cAAc,GAAG,MAAM,EAAE,WAAW,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAE3E,iBAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,CAAC,yBAER,CAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAA,SAAA,EAAE,SAAS;CACX,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCxOD;CACA;CACA,IAAc,KAAA,GAAG,UAA8B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAChE,CAACA,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;CAC3B,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;CACpC,GAAG,MAAM,IAAI,KAAK,EAAC,WAAA,IAAY,MAAM,CAAC,IAAA,CAAI,GAA+C,+CAAA,EAAC,CAAC;CAC3F,GAAG;AACH;CACA,EAAE,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;CACjC,GAAG,MAAM,IAAI,KAAK,EAAC,WAAA,IAAY,MAAM,CAAC,IAAA,CAAI,GAAyD,yDAAA,EAAC,CAAC;CACrG,GAAG;CACH,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,kDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAK/D,iEAAA,EAAC,CAAC;AACJ;AACA;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;CAC7B,EAAE,GAAG,CAAC,IAAI,0CAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGpE,wCAAA,EAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,IAAI,EAAE;CAC3B,EAAE,GAAG,CAAC,IAAI,EACD,cAAA,IAAA,MAAM,CAAC,KAAK,CAAA,GAAA,kFAAA,IAIJ,MAAM,CAAC,KAAK,CAAC,MAAA,CAAM,sBAC9B,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAA,EAAA,QAAQ,CAAC,CAAC,CAAA,GAAA,wCAAA,EAG/F,CAAC;AACL;CACA,EAAE,GAAG,CAAC,IAAI,CAAC,gEAGR,CAAC,CAAC;AACL;CACA,EAAE,KAAKD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAChD,GAAG,GAAG,CAAC,IAAI,2BACM,CAAC,GAAA,UAAA,EACd,CAAC;AACL;CACA,GAAGC,IAAM,QAAQ,GAAM,IAAI,GAAI,GAAA,GAAA,CAAC,MAAG,CAAC;CACpC,GAAGA,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CACxD,GAAGA,IAAM,WAAW,GAAG,cACd,GAAA,CAAC,aAAO,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAE,CAAA,GAAA,4BAAA,GAA6B,CAAC,GAA+C,kDAAA,GAAA,CAAC,8CACzH,CAAC;CACJ,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,YAAS,CAAC,GAAA,GAAA,EAAI,CAAC,CAAC;CACjF,GAAG;CACH,EAAE,GAAG,CAAC,IAAI,CAAC,uBAET,CAAC,CAAC;CACJ,EAAE,MAAM;CACR,EAAE,GAAG,CAAC,IAAI,CAAC,yBAET,CAAC,CAAC;CACJ,EAAE;AACF;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CC3EDA,IAAMI,SAAO,GAAG,iBAAiB,CAAC;CAClC;CACA;AACA;CACA;CACA;CACA,IAAc,GAAA,GAAG,UAA8B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAA2B;CAChE,CAACJ,IAAM,GAAG,GAAG,EAAE,CAAC;AAChB;CACA,CAAC,GAAG,CAAC,IAAI,wDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGhE,kCAAA,EAAC,CAAC;AACJ;CACA,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;CACpB,EAAE,GAAG,CAAC,IAAI,mDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAGlE,wCAAA,EAAC,CAAC;CACL,EAAE,MAAM;CACR,EAAE,GAAG,CAAC,IAAI,CAAC,qDAER,CAAC,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,EACA,aAAA,IAAAI,SAAO,CAAC,QAAQ,EAAE,CAAA,GAAA,yBAAA,IACtB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAA,CAAC,GAI7D,kCAAA,EAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCxCDJ,IAAMI,SAAO,GAAG,4GAA4G,CAAC;AAC7H;CACA;CACA;CACA,IAAc,IAAA,GAAG,SAAA,GAA6B,EAAE,IAAI,EAAE;CAAlB,CAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAAkB;CACtD,CAACJ,IAAM,GAAG,GAAG,EAAE,CAAC;CAChB,CAAC,GAAG,CAAC,IAAI,wDAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,+EAAA,IAK1DI,SAAO,CAAC,QAAQ,GAAE,GACtB,uBAAA,IAAA,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAK/D,6EAAA,EAAC,CAAC;AACJ;CACA,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;CAClC,EAAE,GAAG,CAAC,IAAI,oBACD,MAAM,CAAC,OAAO,CAAA,GAAA,2BAAA,IACjB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,wCAAA,EAGhG,CAAC;CACL,EAAE;AACF;CACA,CAAC,GAAG,CAAC,IAAI,yPAaH,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,CAAA,CAAC,GAKhE,2CAAA,EAAC,CAAC;AACJ;CACA,CAAC,OAAO;CACR,EAAE,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;CACxB,EAAE,CAAC;CACH,CAAC;;CCrDDJ,IAAM,OAAO,GAAG,8JAA8J,CAAC;AAC/K;CACA;CACA;CACA,IAAc,GAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,GAAA,CAAA,OAAA;;AAA2B;CAC/D,CAAC,OAAO;CACR,EAAE,MAAM,GAAE,sDAAA,IAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,OAAO,EAAE,QAAA,EAAA,QAAQ,EAAE,CAAC,CAAA,GAAA,qFAAA,IAK1D,OAAO,CAAC,QAAQ,EAAE,CAAA,GAAA,uBAAA,IACtB,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,KAAK,GAAG,MAAM,EAAE,OAAO,YAAE,QAAQ,EAAE,EAAC,GAI9D,8CAAA,CAAA;CACH,EAAE,CAAC;CACH,CAAC;;CCpBD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAc,IAAA,GAAG,SAA6B,GAAA,EAAE,IAAI,EAAE,OAAO,EAAE;CAA3B,CAAA,GAAA,CAAA,OAAA;;AAA2B;CAC/D,CAAC,OAAO;CACR,EAAE,MAAM,GAAE,sDAAA,IAEJ,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,OAAO,YAAE,QAAQ,EAAE,CAAC,CAAA,GAAA,wbAAA,IAkB9D,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,OAAO,EAAA,QAAA,EAAE,QAAQ,EAAE,EAAC,GAI/D,wCAAA,CAAA;CACH,EAAE,CAAC;CACH,CAAC;;;;;;CCvCD;CACAD,IAAI,QAAQ,EAAE,YAAY,CAAC;CAC3BA,IAAI,IAAI,EAAE,QAAQ,CAAC;AAInB;CACA,IAAc,UAAA,GAAG,SAAS,MAAM,EAAE;CAClC,CAAC,IAAI,CAAC,QAAQ,EAAE;CAChB,EAAE,QAAQ,GAAGM,eAAO,CAAK,CAAC,CAAC;CAC3B,EAAE,YAAY,GAAG;CACjB,GAAG,MAAM,EAAE,OAAO;CAClB,GAAG,OAAO,EAAE,KAAK;CACjB,GAAG,UAAU,EAAE,GAAG;CAClB,GAAG,aAAa,EAAE,MAAM;CACxB,GAAG,QAAQ,EAAE,CAAC;CACd,GAAG,WAAW,EAAE,KAAK;CACrB,GAAG,IAAI,EAAE,IAAI;CACb,GAAG,cAAc,EAAE,IAAI;CACvB,GAAG,CAAC;AACJ;CACA,EAAE,IAAI,GAAGA,eAAO,CAAK,CAAC,CAAC;CACvB,EAAE,QAAQ,GAAG;CACb,GAAG,QAAQ,EAAE,IAAI;CACjB,GAAG,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC;CACxB,IAAI,OAAO,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;CAC9B,IAAI,QAAQ,EAAE,SAAS;CACvB,IAAI,OAAO,EAAE,MAAM;CACnB,IAAI,MAAM,EAAE,SAAS;CACrB,IAAI,MAAM,EAAE,KAAK;CACjB,IAAI,MAAM,EAAE,CAAC,QAAQ,EAAE,MAAM,CAAC;CAC9B,IAAI,MAAM,EAAE,OAAO;CACnB,IAAI,KAAK,EAAE,MAAM;CACjB,IAAI,IAAI,EAAE,OAAO;CACjB,IAAI,QAAQ,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;CAC/B,IAAI,KAAK,EAAE,OAAO;CAClB,IAAI,MAAM,EAAE,OAAO;CACnB,IAAI,OAAO,EAAE,MAAM;CACnB,IAAI,CAAC;CACL,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAACL,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;CACnD,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;CACtC,CAAC;;CC1CDD,IAAI,aAAa,CAAC;CAClB,IAAI;CACJ,CAAC,aAAa,GAAG,CAAC,IAAI,QAAQ,CAAC,8DAA8D,CAAC,GAAG,CAAC;CAClG,CAAC,CAAC,MAAM,GAAG,EAAE,+BAA+B;AAC5C;AACoD;AACP;AAC7C;CACA,SAAS,YAAY,GAAG;CACxB,CAAC,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAGO,QAAqB,CAAC,CAAC;CAClD,CAAC;AACD;CACA,SAAS,SAAS,GAAG;CACrB,CAAC,OAAO;CACR,EAAE,GAAG,EAAEC,GAAsB;CAC7B,EAAE,KAAK,EAAEC,KAAwB;CACjC,EAAE,OAAO,EAAEC,SAA0B;CACrC,EAAE,KAAK,EAAEC,MAAwB;CACjC,EAAE,MAAM,EAAEC,MAAyB;CACnC,EAAE,QAAQ,EAAEC,QAA2B;CACvC,EAAE,IAAI,EAAEC,IAAuB;CAC/B,EAAE,KAAK,EAAEC,KAAwB;CACjC,EAAE,IAAI,EAAEC,KAAuB;CAC/B,EAAE,KAAK,EAAEC,KAAwB;CACjC,EAAE,SAAS,EAAEC,SAA4B;CACzC,EAAE,QAAQ,EAAEC,SAA2B;CACvC,EAAE,KAAK,EAAEC,KAAwB;CACjC,EAAE,MAAM,EAAEC,MAAyB;CACnC,EAAE,MAAM,EAAEC,MAAyB;CACnC,EAAE,QAAQ,EAAEC,QAA2B;CACvC,EAAE,MAAM,EAAEC,MAAyB;CACnC,EAAE,MAAM,EAAEC,MAAyB;CACnC,EAAE,KAAK,EAAEC,KAAwB;CACjC,EAAE,GAAG,EAAEC,GAAsB;CAC7B,EAAE,IAAI,EAAEC,IAAuB;CAC/B,EAAE,GAAG,EAAEC,GAAsB;CAC7B,EAAE,IAAI,EAAEC,IAAuB;CAC/B,EAAE,CAAC;CACH,CAAC;AACD;CACA;CACA;CACA;CACA,IAAM,SAAS,GAOd,SAAW,SAAA,CAAC,IAAI,EAAE;CACnB,CAAE,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;CACjB,CAAE,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;CACrB,CAAE,IAAI,CAAC,QAAQ,GAAG,YAAY,EAAE,CAAC;CACjC,CAAE,IAAI,CAAC,KAAK,GAAG,SAAS,EAAE,CAAC;CAC3B,CAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CACpB,CAAE,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;CACzB,CAAE,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC5B;CACA,CAAE,IAAI,IAAI,EAAE;CACZ,EAAGC,YAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;CAC/B,EAAG,IAAI,IAAI,CAAC,QAAQ,IAAEA,YAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAA;AAC/D;CACA,EAAG,IAAI,IAAI,CAAC,QAAQ,EAAE;CACtB,GAAI,KAAK9B,IAAM,WAAW,IAAI,IAAI,CAAC,QAAQ,EAAA,EAAE,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,EAAA;CACtG,GAAI;AACJ;CACA,EAAG,IAAI,IAAI,CAAC,OAAO,EAAE;CACrB,GAAI,KAAKA,IAAM,SAAS,IAAI,IAAI,CAAC,OAAO,EAAA,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,EAAA;CACzF,GAAI;AACJ;CACA,EAAG,IAAI,IAAI,CAAC,WAAW,EAAE;CACzB,GAAI,KAAKA,IAAM,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAA,EAAE,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAA;CAC5F,GAAI;AACJ;CACA,EAAG,IAAI,IAAI,CAAC,eAAe,EAAE;CAC7B,GAAI,KAAKA,IAAM,UAAU,IAAI,IAAI,CAAC,eAAe,EAAA,EAAE,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,EAAA;CACxH,GAAI;AACJ;CACA,EAAG,IAAI,IAAI,CAAC,OAAO,EAAE;CACrB,GAAIA,IAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;CACjC,GAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAA,EAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC,EAAA;CAC/E,GAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC5C,GAAI;AACJ;CACA;CACA,EAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACxB,GAAID,IAAI,SAAS,GAAG,UAAU,IAAI,EAAE,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC;CACrD,GAAI,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;CACvC,IAAK,SAAS,GAAGgC,UAA6B,CAAC;CAC/C,IAAK;AACL;CACA,GAAI,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;CAChC,GAAI;CACJ,EAAG;CACD,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,QAAA,GAAA,SAAA,QAAA,EAAS,GAAG,EAAE,MAAM,EAAE;CACvB,CAAE/B,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACrC,CAAE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC;CAClB,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,2BAA2B,GAAA,SAAA,2BAAA,EAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;CAC9D,CAAEA,IAAM,GAAG,GAAG,EAAE,CAAC;CACjB,CAAsC,IAAA,GAAA,GAAG,IAAI,CAAC,IAAA,CAAA;sHAAd,KAAmB,CAAA;CACnD,CAAED,IAAI,aAAa,CAAC;AACpB;CACA,CAAEA,IAAI,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC;CAC7F,CAAEA,IAAI,aAAa,GAAG,oBAAoB;CAC1C,EAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW;CACrE,EAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,WAAW,CAAC;AACtG;CACA,CAAEC,IAAM,cAAc,GAAG,oBAAoB;CAC7C,EAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,IAAI;CAClE,EAAG,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,SAAS,CAAC;AACpC;CACA,CAAE,IAAI,cAAc,EAAE;CACtB;CACA,EAAG,kBAAkB,GAAG,KAAK,CAAC;CAC9B,EAAG,IAAI,oBAAoB,EAAE;CAC7B,GAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,EAAE,EAAA,aAAa,GAAG,KAAK,CAAC,EAAA;CAC9D,GAAI,MAAM;CACV,GAAI,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAI,EAAE,EAAA,aAAa,GAAG,KAAK,CAAC,EAAA;CAC7D,GAAI;AACJ;CACA,EAAGD,IAAI,YAAY,CAAC;CACpB,EAAG,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,KAAK,UAAU,EAAE;CAClD,GAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,EAAA;CACvE,GAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;CAChE,GAAI,YAAY,GAAG,kBAAA,IAAmB,IAAI,CAAC,KAAK,CAAA,GAAA,uCAAA,IAAwC,IAAI,CAAC,KAAK,CAAA,GAAA,mCAAmC,CAAC;CACtI,GAAI,MAAM;CACV,GAAI,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;CACvD,GAAI;AACJ;CACA,EAAG,aAAa,GAAG,oBAAA,GACL,YAAY,GACpB,aAAA,GAAA,MAAM,sBACR,CAAC;AACL;CACA,EAAG,MAAM;CACT,EAAG,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;CAClG,EAAG;AACH;AACA;CACA,CAAE,GAAG,CAAC,IAAI,EACL,UAAA,IAAA,6BAAA,IAA8B,kBAAkB,GAAG,wBAAwB,GAAG,aAAa,CAAA,GAAA,IAAA,CAAI,iBAC/F,6BAA8B,IAAA,aAAa,GAAG,mBAAmB,GAAG,aAAA,CAAa,GAAI,IAAA,CAAA,GAAA,UAAA,IACrF,QAAQ,IAAa,SAAA,GAAA,QAAQ,GAAI,IAAA,IAAG,EAAE,CAAA,GAAA,QAAA,EACvC,CAAC;CACL,CAAE,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CACtB,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,SAAS,GAAA,SAAA,SAAA,EAAC,GAAG,EAAE;CAChB,CAAE,OAAO,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;CAC5B,CAAA,CAAA;CACF;CACA;CACA;CACA;CACA;qBACC,eAAe,GAAA,SAAA,eAAA,EAAC,GAAG,EAAE;;AAAA;CACtB,CAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,UAAA,GAAA,EAAO;CAClC,EAAG,GAAG,CAACG,QAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE;CAC5B,GAAI,OAAO;CACX,GAAI;AACJ;CACA,EAAG,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;CACnB,EAAG,CAAC,CAAC;CACH,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,OAAO,GAAA,SAAA,OAAA,EAAC,MAAM,EAAE;CACjB,CAAE,IAAI,MAAM,KAAK,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;CACrD,EAAG,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;CACtC,EAAG;AACH;CACA,CAAEF,IAAM,IAAI,GAAG,IAAI,CAAC;CACpB,CAAEA,IAAM,OAAO,GAAG;CAClB,EAAG,KAAK,EAAE,CAAC;CACX,EAAG,KAAK,EAAE,MAAM,CAAC,OAAO,KAAK,IAAI;CACjC,EAAG,KAAK,EAAE,EAAE;CACZ,EAAG,EAAE,EAAE,EAAE;CACT,EAAG,OAAO,EAAE,EAAE;CACd,EAAG,eAAe,GAAG,IAAI,CAAC,eAAe;CACzC,EAAG,KAAK,EAAE;CACV,GAAA,OAAA,EAAI,OAAO;CACX,GAAI;CACJ,EAAG,CAAC;CACJ,CAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;CACrB,CAAE,OAAO,MAAM,CAAC,OAAO,CAAC;AACxB;CACA;CACA,CAAE,IAAI,OAAO,CAAC,KAAK,IAAI,CAAC,aAAa,EAAE;CACvC,EAAG,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;CAC1D,EAAG;AACH;CACA,CAAE,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;CAC9B,EAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;CAC9B,GAAIA,IAAMgC,MAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;CAChD,GAAI,MAAM,GAAGA,MAAI,CAAC,MAAM,CAAC;CACzB,GAAI,MAAM;CACV,GAAIhC,IAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;CACjD,GAAI,MAAM,GAAG;CACb,IAAK,IAAI,EAAE,QAAQ;CACnB,IAAK,MAAM,EAAE,UAAU,CAAC,QAAQ;CAChC,IAAK,UAAU,EAAE,UAAU;CAC3B,IAAK,CAAC;AACN;CACA,GAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;CACrC,GAAI;CACJ,EAAG;AACH;CACA,CAAEA,IAAM,UAAU,GAAG;CACrB,EAAG,kBAAkB;CACrB,EAAG,YAAY;CACf,EAAG,oBAAoB;CACvB,GAAkB,cAAA,IAAA,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,OAAM,GAAG,GAAA;CACvE,EAAG,CAAC;AACJ;CACA,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;CAC9C,CAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAK,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAE,IAAA,oEAAA,GAAsE,OAAO,CAAC,CAAC,CAAC;AACxK;CACA,CAAE,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;CAC1C,CAAE,UAAU,CAAC,IAAI,CAAC,ubAUf,CAAC,CAAC;AACL;CACA,CAAE,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACvB,CAAE,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAClC;CACA,CAAEA,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC;CACA,CAAEA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,aAAa,GAAG,QAAQ,CAAC;CAC3D,CAAEA,IAAM,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;AACvD;CACA;CACA,CAAE,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACvB,EAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,0BAA0B,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;CACjF,EAAG;AACH;CACA,CAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;AACrB;CACA,CAAEA,IAAM,KAAK,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE;CACtC,EAAG,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;CACvB,EAAG,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI;CACxB,GAAA,EAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,EAAA;CAC7B,EAAG,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;CAC5C,EAAG,CAAC;CACJ,CAAE,KAAK,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;CAC9B,CAAE,OAAO,KAAK,CAAC;CACb,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,WAAW,GAAA,SAAA,WAAA,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE;CACpD,CAAEA,IAAM,UAAU,GAAG,EAAE,CAAC;AACxB;CACA,CAAEA,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC3C,CAAE,IAAI,IAAI,EAAE;CACZ;CACA,EAAG,IAAI,GAAG,IAAI,CAAC;CACf,EAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;CACrB,EAAG,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;CACxB,EAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,GAAE,qCAAA,IAC3B,IAAI,CAAC,MAAK,GAGlC,8GAAA,IAAA,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAA,CAAC,GAG7C,4DAAA,GAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;AACxB;CACA,EAAG,MAAM;CACT,EAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;CACrC,EAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;CAC9B,EAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AACvC;CACA,EAAGA,IAAM,UAAU,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,QAAQ,CAAC;AACrD;CACA,EAAG,OAAO,CAAC,KAAK,EAAE,CAAC;CACnB,EAAGA,IAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;CACjE,EAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;CAC7D,EAAGA,IAAM,OAAO,GAAG,OAAO,CAAC,KAAK,GAAG,aAAa,GAAG,QAAQ,CAAC;CAC5D,EAAGA,IAAM,EAAE,GAAG,IAAI,OAAO,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;CAChG,EAAG,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC1C,EAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;CACxH,EAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAA,EAAA,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9J;CACA;CACA,EAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACxB,GAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,gBAAiB,IAAA,IAAI,CAAC,KAAK,CAAA,GAAA,KAAK,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;CACnF,GAAI;CACJ,EAAG;AACH;CACA,CAAE,OAAO,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAC7B,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;qBACC,iBAAiB,GAAA,SAAA,iBAAA,EAAC,MAAM,EAAE;CAC3B,CAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACpC;CACA,CAAEA,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CAC1C,CAAE,IAAI,KAAK,EAAE;CACb,EAAG,OAAO,MAAM,CAAC,IAAI,CAAC;CACtB,EAAG,MAAM,GAAG8B,YAAU,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;CAC7D,EAAG;AACH;CACA,CAAE9B,IAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;CAC/C,CAAE,IAAI,CAAC,YAAY;CACnB,EAAA,EAAG,MAAM,IAAI,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,GAAG,6BAA6B,CAAC,CAAC,EAAA;AAC9E;CACA,CAAEA,IAAM,IAAI,GAAG;CACf,EAAG,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC;CAC9D,EAAG,MAAM,EAAE8B,YAAU,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;CAChF,EAAG,YAAY,EAAE,YAAY;CAC7B,EAAG,CAAC;AACJ;CACA,CAAE,OAAO,IAAI,CAAC;CACZ,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;AACA;qBACC,cAAc,GAAA,SAAA,cAAA,EAAC,GAAG,EAAE;CACrB,CAAE9B,IAAM,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAA,UAAE,CAAC,EAAA,EAAA,OAAK,CAAC,CAAC,IAAI,EAAE,CAAA,EAAA,CAAC,CAAC;CAChD,CAAED,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CAClB,CAAEA,IAAI,MAAM,CAAC;CACb,CAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;CAC3B,EAAG,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;CACvF,EAAG,MAAM;CACT,EAAG,MAAM,GAAG;CACZ,GAAI,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;CACd,GAAI,CAAC;CACL,EAAG;AACH;CACA,CAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA,UAAE,CAAC,EAAK;CAC5B,EAAGC,IAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;CAC9B,EAAG,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;CACnB,GAAIA,IAAM,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;CAC3C,GAAID,IAAI,KAAK,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;CAC5C,GAAI,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,KAAK,OAAO;CAC7C,IAAA,EAAK,KAAK,GAAG,KAAK,KAAK,MAAM,CAAC,EAAA;CAC9B,QAAS,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;CAC3C,IAAK,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;CAC3B,IAAK;CACL,GAAI,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;CACxB,GAAI,MAAM;CACV;CACA,GAAI,IAAI,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,EAAA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAA;CACxD,UAAS,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,EAAA;CAC1B,GAAI;CACJ,EAAG,CAAC,CAAC;AACL;CACA,CAAE,OAAO,MAAM,CAAC;CACd,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,SAAS,GAAA,SAAA,SAAA,EAAA,GAA4C,EAAE;CAArC,EAAA,IAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA;CAAO,EAAA,IAAA,KAAA,GAAA,GAAA,CAAA,KAAA,CAAA;CAAU,EAAA,IAAA,QAAA,GAAA,GAAA,CAAA,QAAA,CAAA;CAAQ,EAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;;AAAY;CACxD,CAAEC,IAAM,CAAC,GAAG;CACZ,EAAG,IAAI,GAAM,IAAA,GAAA,IAAI,GAAG,IAAA,CAAA;CACpB,EAAG,OAAO,GAAE,IAAA,IAAI,QAAQ,CAAC,IAAI,EAAC,GAAG,IAAA,CAAA;CACjC,EAAG,CAAC;CACJ,CAAE,IAAI,KAAK,EAAA,EAAE,CAAC,CAAC,KAAK,GAAG,IAAA,GAAI,KAAK,GAAA,IAAG,CAAC,EAAA;CACpC,QAAO,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,EAAA;CACzB,CAAE,IAAI,QAAQ,IAAI,IAAI,EAAA,EAAE,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC,EAAA;CAC9C,CAAE,IAAI,MAAM,IAAI,IAAI,EAAA,EAAE,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,EAAA;CACxC,CAAE,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC;AACpB;CACA,CAAEA,IAAM,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1B,GAAI,GAAG,CAAA,UAAC,GAAG,EAAA,EAAA,QAAO,GAAG,GAAA,IAAA,IAAK,CAAC,CAAC,GAAG,CAAA,CAAA,EAAA,EAAG,CAAC;CACnC,GAAI,IAAI,CAAC,IAAI,CAAC,CAAC;AACf;CACA,CAAE,QAAO,gBAAA,GAAiB,CAAC,GAAA,MAAA,EAAO;CAChC,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,mBAAA,GAAA,SAAA,mBAAA,KAAsG,EAAE;CAA1E,EAAA,IAAA,KAAA,GAAA,GAAA,CAAA,KAAA,CAAA,CAAA,KAAA,KAAA,KAAA,KAAA,CAAA,GAAA,KAAA,GAAA,OAAA,CAAA;8DAAkB,QAAU,CAAA;CAAW,EAAA,IAAA,SAAA,GAAA,GAAA,CAAA,SAAA,CAAA;CAAM,EAAA,IAAA,IAAA,GAAA,GAAA,CAAA,IAAA,CAAA;CAAQ,EAAA,IAAA,MAAA,GAAA,GAAA,CAAA,MAAA,CAAA;CAAS,EAAA,IAAA,OAAA,GAAA,GAAA,CAAA,OAAA,CAAA;;AAAY;CACzG,CAAEA,IAAM,SAAS,GAAG,MAAM,GAAG,SAAS,CAAC;CACvC,CAAEA,IAAM,mBAAmB,GAAG,gBAAgB,GAAG,SAAS,CAAC;AAC3D;CACA,CAAE,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;CAC9E,EAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;CACnC,GAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;CACnD,GAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC;CAC/C,GAAI,MAAM;CACV,GAAI,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,EAAA,QAAA,EAAE,QAAQ,EAAA,MAAA,EAAE,MAAM,EAAE,CAAC;CACtD,GAAI;CACJ,EAAGA,IAAM,GAAG,GAAG,EAAE,CAAC;CAClB,EAAG,IAAI,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE;CAC9C,GAAI,GAAG,CAAC,IAAI,GACa,6BAAA,GAAA,SAAS,GAAsB,qBAAA,GAAA,SAAS,GACpD,sBAAA,GAAA,mBAAmB,GAC3B,kBAAA,EAAC,CAAC;AACP;CACA,GAAI,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;CACrC,IAAK,KAAKD,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD;CACA,KAAMA,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACrC;CACA,KAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;CACtC,MAAO,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;CAC5C,MAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;CAClC,MAAO;AACP;CACA,KAAMC,IAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;CAC3C,KAAM,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,EAAA,QAAA,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,MAAA,EAAA,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AAC3G;CACA,KAAM,GAAG,CAAC,IAAI,GACC,wBAAA,GAAA,SAAS,GAAI,GAAA,GAAA,CAAC,GAAsB,qBAAA,GAAA,WAAW,GAEtD,qBAAA,EAAC,CAAC;AACV;CACA,KAAM,GAAG,MAAM,CAAC,IAAI,CAAC;CACrB,MAAO,GAAG,CAAC,IAAI,yBACL,KAAK,GAAA,KAAA,IAAM,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,GAAE,GAA2B,0BAAA,GAAA,SAAS,GAAW,UAAA,GAAA,MAAM,GAAI,GAAA,GAAA,CAAC,GAAsB,qBAAA,GAAA,KAAK,UAAK,mBAAmB,GAAA,KAAA,GAAM,SAAS,GAAA,GAAA,GAAI,CAAC,GAAA,aAAA,GAAa,IAAI,GAAA,uCAAA,EAC3L,CAAC;CACV,MAAO;CACP,KAAM,GAAG,OAAO,MAAM,GAAG,UAAU,CAAC;CACpC,MAAO,GAAG,CAAC,IAAI,GACN,kBAAA,GAAA,KAAK,GAAM,KAAA,IAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAA,CAAA,GAAK,SAAS,GAAA,UAAA,GAAW,MAAM,GAAA,GAAA,GAAI,CAAC,GAAA,eAAA,GAAgB,KAAK,GAAA,IAAA,GAAK,mBAAmB,GAAA,KAAA,GAAM,SAAS,GAAA,aAAA,GAAa,IAAI,GAAA,uCAAA,EACvJ,CAAC;CACV,MAAO;CACP,KAAM;CACN,IAAK,KAAI;CACT,IAAK,GAAG,CAAC,IAAI,GAAE,cAAA,GACR,KAAK,GAAA,KAAA,IAAM,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAK,CAAA,GAAA,SAAS,GAAW,UAAA,GAAA,MAAM,GAAe,cAAA,GAAA,KAAK,GAAK,IAAA,GAAA,mBAAmB,GAAM,KAAA,GAAA,SAAS,GAAa,aAAA,GAAA,IAAI,GAClJ,mCAAA,EAAC,CAAC;CACR,IAAK;AACL;CACA,GAAI,GAAG,CAAC,IAAI,GACa,gCAAA,GAAA,mBAAmB,GACtB,+BAAA,GAAA,mBAAmB,GAAyD,wDAAA,GAAA,SAAS,GAEtG,8DAAA,EAAC,CAAC;CACP,GAAI,KAAI;CACR,GAAIA,IAAM,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;CACtC,GAAI,GAAG,CAAC,IAAI,0BACC,SAAS,GAAA,qBAAA,GAAsB,SAAS,GAAA,sBAAA,GACxC,MAAM,GAAM,KAAA,IAAA,OAAO,CAAC,KAAK,GAAG,QAAQ,GAAG,EAAK,CAAA,GAAA,SAAS,gBAAW,MAAM,GAAA,cAAA,GAAe,KAAK,GAAA,IAAA,GAAK,SAAS,GAAa,aAAA,GAAA,IAAI,GAC7G,qDAAA,GAAA,MAAM,0BACvB,MAAM,GAAA,uDAAA,GAAwD,SAAS,GAAA,4DAAA,EAE1E,CAAC;CACN,GAAI;CACJ,EAAG,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzB;CACA,EAAG;CACH,CAAE,OAAO,EAAE,CAAC;CACV,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,GAAA,GAAA,SAAA,GAAA,EAAI,IAAI,EAAE,EAAE,EAAE;CACf,CAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;CACtB,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,iBAAA,GAAA,SAAA,iBAAA,EAAkB,IAAI,EAAE,EAAE,EAAE;CAC7B,CAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;CAChC,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,UAAA,GAAA,SAAA,UAAA,EAAW,IAAI,EAAE,OAAO,EAAE;CAC3B,CAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC;CAC9B,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACC,SAAA,CAAA,SAAA,CAAA,KAAA,GAAA,SAAA,KAAA,EAAM,IAAI,EAAE,cAAc,EAAE;CAC7B,CAAE,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAA,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC,EAAA;CAC9E,CAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC;CACpC,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;qBACC,MAAM,GAAA,SAAA,MAAA,EAAC,EAAE,EAAE;CACZ,CAAE,IAAI,OAAO,EAAE,KAAK,UAAU,EAAA,EAAE,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,EAAA;CACnF,CAAE,OAAO,EAAE,CAAC,IAAI,CAAC,CAAC;CAChB,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,WAAW,GAAA,SAAA,WAAA,EAAC,MAAM,EAAE;;AAAA;CACrB,CAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;CAClC,EAAG,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;CACxC,EAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;CACpC,EAAG,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;CAC1B,GAAA,EAAI,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC,EAAA;AACvC;CACA,EAAG,MAAM,GAAG;CACZ,GAAI,IAAI,EAAE,OAAO;CACjB,GAAI,KAAK,EAAE,MAAM;CACjB,GAAI,CAAC;AACL;CACA;CACA,EAAGA,IAAM,UAAU,GAAG,MAAM,CAAC,KAAK;CAClC,IAAK,GAAG,CAAA,UAAC,CAAC,EAAA,EAAA,OAAIE,QAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA,CAAA,EAAC,CAAC;CACxC,IAAK,KAAK,CAAC,UAAA,IAAA,EAAQ,EAAA,OAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,IAAA,CAAA,EAAI,CAAC,CAAC;CAClD,EAAG,IAAI,UAAU;CACjB,GAAA,EAAI,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAA;AAC3B;CACA;CACA,EAAGF,IAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,GAAG,KAAK,GAAG,IAAI,CAAC;CACnE,EAAGA,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK;CACnC,IAAK,GAAG,CAAA,UAAC,CAAC,EAAA,EAAA,OAAIE,QAAI,CAAC,iBAAiB,CAAC,CAAC,CAAA,CAAA,EAAC,CAAC;CACxC,IAAK,KAAK,CAAC,UAAA,IAAA,EAAQ,EAAA,OAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,SAAA,CAAA,EAAS,CAAC,CAAC;CACvD,EAAG,IAAI,WAAW;CAClB,GAAA,EAAI,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,EAAA;CAChC,EAAG;AACH;CACA,CAAE,IAAI,MAAM,CAAC,MAAM,EAAE;CACrB,EAAGF,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;CAC9B,EAAGA,IAAM,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;CACnE,EAAG,OAAO,MAAM,CAAC,MAAM,CAAC;CACxB,EAAGA,IAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;AAC3C;CACA,EAAG,KAAKA,IAAM,GAAG,IAAI,MAAM,EAAE;CAC7B,GAAI,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;CACvB,GAAI;AACJ;CACA,EAAG8B,YAAU,CAAC,MAAM,EAAE,mBAAmB,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;CAClE,EAAG,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;CACxB,EAAG;AACH;CACA,CAAE,OAAO,MAAM,CAAC;CACd,CAAA,CAAA;AACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;qBACC,SAAS,GAAA,SAAA,SAAA,EAAC,KAAK,EAAE;;AAAA;CAClB,CAAE/B,IAAI,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACvC,CAAE,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;CAC9B,EAAG,EAAA,MAAM,GAAG+B,YAAU,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,EAAA;AACjG;CACA,CAAE,MAAM,GAAGA,YAAU,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;AACjF;CACA,CAAE,GAAG,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;CAC9B,EAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,WAAC,CAAC,EAAA,EAAA,OAAI5B,QAAI,CAAC,SAAS,CAAC,CAAC,CAAA,CAAA,EAAC,CAAC,CAAC;CAC3D,EAAG,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,UAAA,CAAA,WAAK,CAAC,CAAC,QAAQ,KAAK,IAAA,CAAA,EAAI,CAAC,CAAC;CAClE,EAAG,OAAO,MAAM,CAAC;CACjB,EAAG;CACH,CAAE,GAAG,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE;CAC9B,EAAG,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CAC/C,EAAG,OAAO,MAAM,CAAC;CACjB,EAAG;CACH,CAAE,GAAG,MAAM,CAAC,IAAI,KAAK,QAAQ,EAAE;CAC/B,EAAG,GAAG,MAAM,CAAC,KAAK,EAAE;CACpB,GAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAA,GAAM,EAAJ;CAAE,KAAA,IAAA,CAAA,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;;aAAO,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAGA,QAAI,CAAC,SAAS,CAAC,CAAC,CAAA,CAAA;CAAC,KAAA,CAAC,CAAC;CACzF,GAAI;CACJ,EAAG;CACH,CAAE,GAAG,OAAO,KAAK,KAAK,QAAQ,EAAE;CAChC,EAAG,GAAG,KAAK,CAAC,IAAI,EAAE;CAClB,GAAIF,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CAC9C,GAAI8B,YAAU,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;CACvD,GAAI;CACJ,OAAO;CACP,GAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,UAAA,GAAM,EAAJ;CAAE,KAAA,IAAA,CAAA,GAAA,GAAA,CAAA,CAAA,CAAA,CAAA;;;aAAO,MAAM,CAAC,CAAC,CAAC,GAAG5B,QAAI,CAAC,SAAS,CAAC,CAAC,CAAA,CAAA;CAAC,KAAA,CAAC,CAAC;CAC5E,GAAI;CACJ,EAAG;AACH;CACA,CAAE,OAAO,MAAM,CAAC;CACd,CACD,CAAA;AACD;CACA,IAAA,SAAc,GAAG,SAAS;;AC3pB1B,KAAA,gBAAc,GAAGI;;;;;;;;"}