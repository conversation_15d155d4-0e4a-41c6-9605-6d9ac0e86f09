{"version": 3, "sources": ["../browser/src/driver/Query.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,KAAK;IAGd,YAAmB,KAAa,EAAS,UAAkB;QAAxC,UAAK,GAAL,KAAK,CAAQ;QAAS,eAAU,GAAV,UAAU,CAAQ;QAFlD,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IAEkB,CAAC;CAClE", "file": "Query.js", "sourcesContent": ["/**\n * This class stores query and its parameters\n */\nexport class Query {\n    readonly \"@instanceof\" = Symbol.for(\"Query\")\n\n    constructor(public query: string, public parameters?: any[]) {}\n}\n"], "sourceRoot": ".."}