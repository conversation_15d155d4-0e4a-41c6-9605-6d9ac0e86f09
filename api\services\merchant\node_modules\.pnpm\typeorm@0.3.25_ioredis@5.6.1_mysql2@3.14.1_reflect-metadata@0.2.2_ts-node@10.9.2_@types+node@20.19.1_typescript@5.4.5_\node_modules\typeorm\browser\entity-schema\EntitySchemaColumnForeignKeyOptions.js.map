{"version": 3, "sources": ["../browser/src/entity-schema/EntitySchemaColumnForeignKeyOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaColumnForeignKeyOptions.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\nimport { ForeignKeyOptions } from \"../decorator/options/ForeignKeyOptions\"\n\nexport interface EntitySchemaColumnForeignKeyOptions extends ForeignKeyOptions {\n    /**\n     * Indicates with which entity this relation is made.\n     */\n    target: EntityTarget<any>\n\n    /**\n     * Inverse side of the relation.\n     */\n    inverseSide?: string\n}\n"], "sourceRoot": ".."}