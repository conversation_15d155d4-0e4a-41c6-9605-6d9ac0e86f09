{"name": "query-string", "version": "5.1.1", "description": "Parse and stringify URL query strings", "license": "MIT", "repository": "sindresorhus/query-string", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["browser", "querystring", "query", "string", "qs", "param", "parameter", "url", "uri", "parse", "stringify", "encode", "decode"], "dependencies": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "devDependencies": {"ava": "^0.17.0", "xo": "^0.16.0"}}