{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,mCAA0C;AAC1C,2BAA8C;AAC9C,+BAA0D;AAC1D,uCAAwC;AAExC,8CAA4B;AAC5B,4CAA0B;AAC1B,4CAA0B;AAC1B,iCAAsE;AAA7D,6GAAA,oBAAoB,OAAA;AAAE,8GAAA,qBAAqB,OAAA;AAEpD,MAAM,OAAO,GAAG,yBAAyB,CAAC;AAEnC,MAAM,SAAS,GAAG,CAAC,oBAAyB,EAAE,UAAW,EAAE,EAAE;IAClE,IAAI,MAAM,GAAQ,oBAAoB,CAAC;IACvC,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;QAC5C,IAAI,IAAA,eAAU,EAAC,oBAAoB,CAAC,EAAE;YACpC,MAAM,OAAO,GAAG,IAAA,iBAAY,EAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;YAC3D,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBAC1D,IAAI,GAAG,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;oBAC1B,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC;iBACtD;YACH,CAAC,CAAC,CAAC;YACH,UAAU;YACV,MAAM,GAAG,IAAA,aAAK,EAAC,oBAAoB,EAAE,WAAW,CAAC,CAAC;SACnD;KACF;IACD,IAAI,CAAC,MAAM,EAAE;QACX,OAAO;KACR;IACD,IAAI,UAAU,EAAE;QACd,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;KACxC;SAAM;QACL,OAAO,IAAI,qBAAW,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;KACzC;AACH,CAAC,CAAC;AAtBW,QAAA,SAAS,aAsBpB;AAEF,iCAA0C;AAAjC,iGAAA,QAAQ,OAAA;AAAE,8FAAA,KAAK,OAAA;AAEjB,MAAM,QAAQ,GAAG,CACtB,oBAAyB,EACzB,cAAsB,EACtB,UAAW,EACX,EAAE;IACF,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;IAC5B,IAAI,mBAAmB,GAAG,EAAE,CAAC;IAC7B,IAAI,OAAO,oBAAoB,KAAK,QAAQ,EAAE;QAC5C,IAAI,CAAC,IAAA,iBAAU,EAAC,oBAAoB,CAAC,EAAE;YACrC,oBAAoB,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,oBAAoB,CAAC,CAAC;SAC5D;aAAM;YACL,OAAO,GAAG,IAAA,cAAO,EAAC,oBAAoB,CAAC,CAAC;SACzC;KACF;IACD,mBAAmB,GAAG,IAAA,iBAAS,EAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;IAClE,IAAI,CAAC,IAAA,iBAAU,EAAC,cAAc,CAAC,EAAE;QAC/B,cAAc,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,cAAc,CAAC,CAAC;KAChD;IACD,OAAO,IAAA,gBAAQ,EAAC,cAAc,EAAE,mBAAmB,CAAC,CAAC;AACvD,CAAC,CAAC;AAnBW,QAAA,QAAQ,YAmBnB;AAEK,MAAM,WAAW,GAAG,OAAO,CAAC,EAAE;IACnC,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC;IACnC,MAAM,QAAQ,GAAG;QACf,OAAO;QACP,QAAQ;QACR,gBAAgB;QAChB,iBAAiB;KAClB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,eAAU,EAAC,IAAA,cAAO,EAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IACnD,IAAI,QAAQ,EAAE;QACZ,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAA,cAAO,EAAC,OAAO,EAAE,QAAQ,CAAC;SACjC,CAAC;KACH;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAfW,QAAA,WAAW,eAetB;AAEK,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,YAAa,EAAE,EAAE;IACjD,MAAM,QAAQ,GAAG,YAAY,IAAI,IAAA,mBAAW,EAAC,OAAO,CAAC,CAAC;IACtD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;QAC/B,OAAO,EAAE,CAAC;KACX;IACD,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,EAAE;QAC5B,OAAO,IAAA,iBAAS,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;KACjC;AACH,CAAC,CAAC;AARW,QAAA,QAAQ,YAQnB;AAEK,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,YAAa,EAAE,EAAE;IAChE,MAAM,QAAQ,GAAG,YAAY,IAAI,IAAA,mBAAW,EAAC,OAAO,CAAC,CAAC;IACtD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;QAC/B,OAAO,EAAE,CAAC;KACX;IACD,IAAI,QAAQ,CAAC,IAAI,KAAK,MAAM,EAAE;QAC5B,OAAO,IAAA,gBAAQ,EAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;KAC5C;AACH,CAAC,CAAC;AARW,QAAA,WAAW,eAQtB"}