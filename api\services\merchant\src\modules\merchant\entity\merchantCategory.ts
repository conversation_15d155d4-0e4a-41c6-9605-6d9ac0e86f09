import { Column, Entity, Index, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * 商户入驻分类
 */
@Entity('merchant_category')
export class MerchantCategoryEntity {
  @PrimaryGeneratedColumn()
  id!: number;
  @Column({ comment: '类目名称', length: 50 })
  name!: string;

  @Column({ comment: '类目描述', type: 'text', nullable: true })
  description?: string;

  @Column({ comment: '类目图标', length: 100, nullable: true })
  icon?: string;

  @Column({ comment: '排序号，数字越大越靠前', default: 0, name: 'sort_num' })
  sortNum!: number;

  @Index()
  @Column({ comment: '状态：0-禁用 1-启用', default: 1 })
  status!: number;

  @Index()
  @Column({ comment: '适用商户类型：0-个人 1-企业 2-通用', name: 'merchant_type' })
  merchantType!: number;

  @Column({ comment: '入驻要求说明', type: 'text', nullable: true })
  requirements?: string;

  @CreateDateColumn({ comment: '创建时间', name: 'create_time' })
  createTime!: Date;

  @UpdateDateColumn({ comment: '更新时间', name: 'update_time' })
  updateTime!: Date;
}
