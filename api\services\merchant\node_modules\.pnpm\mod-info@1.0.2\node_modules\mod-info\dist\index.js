var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __markAsModule = (target) => __defProp(target, "__esModule", { value: true });
var __export = (target, all) => {
  __markAsModule(target);
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __reExport = (target, module2, desc) => {
  if (module2 && typeof module2 === "object" || typeof module2 === "function") {
    for (let key of __getOwnPropNames(module2))
      if (!__hasOwnProp.call(target, key) && key !== "default")
        __defProp(target, key, { get: () => module2[key], enumerable: !(desc = __getOwnPropDesc(module2, key)) || desc.enumerable });
  }
  return target;
};
var __toModule = (module2) => {
  return __reExport(__markAsModule(__defProp(module2 != null ? __create(__getProtoOf(module2)) : {}, "default", module2 && module2.__esModule && "default" in module2 ? { get: () => module2.default, enumerable: true } : { value: module2, enumerable: true })), module2);
};
__export(exports, {
  default: () => src_default
});
var import_os = __toModule(require("os"));
var import_path = __toModule(require("path"));
var import_fs = __toModule(require("fs"));
var import_utils = __toModule(require("./utils"));
var src_default = async (moduleName, currentVersion, options) => {
  const { level, timeout, ignoreInformalVersion, registry } = {
    level: ["major", "minor", "patch"],
    timeout: 24 * 60 * 60 * 1e3,
    ignoreInformalVersion: true,
    registry: "",
    ...options
  };
  const curVersion = (0, import_utils.formatVersion)(currentVersion);
  const cacheDir = (0, import_path.join)((0, import_os.tmpdir)(), `npmModInfoCache`);
  if (!(0, import_fs.existsSync)(cacheDir)) {
    (0, import_fs.mkdirSync)(cacheDir, 511);
  }
  const cacheFile = (0, import_path.join)(cacheDir, `${(moduleName + "_" + currentVersion).replace(/[^\w]/g, "_")}_cache.json`);
  let cache;
  if ((0, import_fs.existsSync)(cacheFile)) {
    cache = JSON.parse((0, import_fs.readFileSync)(cacheFile, "utf-8"));
  }
  if ((cache == null ? void 0 : cache.time) && Date.now() - cache.time < timeout) {
    return cache.value;
  }
  let npmCmd = `npm`;
  if (registry) {
    npmCmd = `npm --registry=${registry}`;
  } else if (process.env.LANG === "zh_CN.UTF-8") {
    npmCmd = "npm --registry=https://registry.npmmirror.com";
  }
  let result = {
    update: false,
    tips: [],
    version: ""
  };
  try {
    const data = await (0, import_utils.exec)({
      cmd: `${npmCmd} view ${moduleName} --json`,
      baseDir: process.env.HOME,
      timeout: 2e3
    });
    const { versions, "module-info-tips": tipRules = [] } = JSON.parse(data);
    let filterVersions = versions.map(import_utils.formatVersion).filter((version) => {
      if (ignoreInformalVersion && version.tag) {
        return;
      }
      if (level.includes("patch")) {
        if (version.major === curVersion.major && version.minor === curVersion.minor && version.pacth > curVersion.pacth) {
          return true;
        }
      }
      if (level.includes("minor")) {
        if (version.major === curVersion.major && version.minor > curVersion.minor) {
          return true;
        }
      }
      if (level.includes("major")) {
        if (version.major > curVersion.major) {
          return true;
        }
      }
    });
    filterVersions = filterVersions.sort((aVer, bVer) => {
      return bVer.score - aVer.score;
    });
    let update = false;
    let newVersion;
    if (filterVersions.length) {
      update = true;
      newVersion = filterVersions[0].version;
    }
    const tips = tipRules.filter((rule) => {
      if (!(rule == null ? void 0 : rule.tip)) {
        return false;
      }
      const ignore = [].concat(rule.ignore || []).find((rule2) => (0, import_utils.matchVersion)(curVersion, rule2));
      if (ignore) {
        return false;
      }
      const match = [].concat(rule.match || []).find((rule2) => (0, import_utils.matchVersion)(curVersion, rule2));
      if (match) {
        return true;
      }
      return false;
    });
    result = {
      update,
      version: newVersion,
      tips: tips.map((rule) => rule.tip)
    };
  } catch (e) {
  }
  (0, import_fs.writeFileSync)(cacheFile, JSON.stringify({ time: Date.now(), value: result }));
  return result;
};
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {});
