{"name": "@cool-midway/rpc", "version": "7.0.0", "description": "cool-js.com rpc 微服务", "main": "index.js", "typings": "index.d.ts", "scripts": {"build": "cross-env midway-bin build -c", "cov": "cross-env midway-bin cov --ts", "lint": "mwts check", "lint:fix": "mwts fix"}, "keywords": [], "author": "", "files": ["**/*.js", "**/*.d.ts", "index.d.ts"], "repository": {"type": "git", "url": "https://cool-js.com"}, "license": "MIT", "devDependencies": {"@cool-midway/core": "^7.0.0", "@midwayjs/cli": "^2.0.9", "@midwayjs/core": "^3.9.0", "@midwayjs/decorator": "^3.9.0", "@midwayjs/mock": "^3.9.0", "@midwayjs/redis": "^3.9.0", "@midwayjs/typeorm": "^3.9.5", "@types/jest": "^29.2.5", "@types/node": "^18.11.18", "cross-env": "^7.0.3", "jest": "^29.3.1", "lodash": "^4.17.21", "mwts": "^1.3.0", "ts-jest": "^29.0.3", "typeorm": "^0.3.11", "typescript": "^4.9.4"}, "dependencies": {"ioredis": "4.28.5", "moleculer": "^0.14.28"}}