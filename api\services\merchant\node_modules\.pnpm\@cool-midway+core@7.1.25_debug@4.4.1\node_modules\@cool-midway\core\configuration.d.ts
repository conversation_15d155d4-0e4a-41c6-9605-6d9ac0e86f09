import { Context, ILifeCycle, ILogger, IMidwayBaseApplication, IMidwayContainer } from "@midwayjs/core";
import * as koa from "@midwayjs/koa";
import { CoolEventManager } from "./event";
export declare class CoolConfiguration implements ILifeCycle {
    coreLogger: ILogger;
    app: koa.Application;
    coolEventManager: CoolEventManager;
    onReady(container: IMidwayContainer): Promise<void>;
    onConfigLoad(container: IMidwayContainer, mainApp?: IMidwayBaseApplication<Context>): Promise<void>;
    onServerReady(container: IMidwayContainer): Promise<void>;
}
