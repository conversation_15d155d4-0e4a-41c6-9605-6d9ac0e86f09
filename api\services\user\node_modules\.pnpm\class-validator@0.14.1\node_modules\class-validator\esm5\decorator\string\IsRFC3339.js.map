{"version": 3, "file": "IsRFC3339.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsRFC3339.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,kBAAkB,MAAM,yBAAyB,CAAC;AAEzD,MAAM,CAAC,IAAM,WAAW,GAAG,WAAW,CAAC;AAEvC;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,KAAc;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,SAAS,CAAC,iBAAqC;IAC7D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,SAAS,CAAC,KAAK,CAAC,EAAhB,CAAgB;YACpD,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,iCAAiC,EAA9C,CAA8C,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isRFC3339Validator from 'validator/lib/isRFC3339';\n\nexport const IS_RFC_3339 = 'isRFC3339';\n\n/**\n * Check if the string is a valid RFC 3339 date.\n * If given value is not a string, then it returns false.\n */\nexport function isRFC3339(value: unknown): boolean {\n  return typeof value === 'string' && isRFC3339Validator(value);\n}\n\n/**\n * Check if the string is a valid RFC 3339 date.\n * If given value is not a string, then it returns false.\n */\nexport function IsRFC3339(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_RFC_3339,\n      validator: {\n        validate: (value, args): boolean => isRFC3339(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be RFC 3339 date', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}