{"version": 3, "sources": ["../../src/error/QueryRunnerProviderAlreadyReleasedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uCAAwC,SAAQ,2BAAY;IACrE;QACI,KAAK,CACD,6DAA6D;YACzD,gEAAgE,CACvE,CAAA;IACL,CAAC;CACJ;AAPD,0FAOC", "file": "QueryRunnerProviderAlreadyReleasedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to use query runner from query runner provider after it was released.\n */\nexport class QueryRunnerProviderAlreadyReleasedError extends TypeORMError {\n    constructor() {\n        super(\n            `Database connection provided by a query runner was already ` +\n                `released, cannot continue to use its querying methods anymore.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}