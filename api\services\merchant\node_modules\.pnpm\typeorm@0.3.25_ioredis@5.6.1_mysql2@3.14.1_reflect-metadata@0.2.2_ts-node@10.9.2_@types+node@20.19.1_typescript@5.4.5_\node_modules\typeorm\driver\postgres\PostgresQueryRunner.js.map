{"version": 3, "sources": ["../../src/driver/postgres/PostgresQueryRunner.ts"], "names": [], "mappings": ";;;AACA,uCAA0C;AAC1C,mEAA+D;AAC/D,iGAA6F;AAC7F,uFAAmF;AAEnF,wEAAoE;AACpE,gEAA4D;AAG5D,4DAAwD;AACxD,sEAAkE;AAClE,wEAAoE;AACpE,8EAA0E;AAC1E,gFAA4E;AAC5E,sEAAkE;AAClE,wEAAoE;AACpE,yDAAqD;AACrD,8DAA0D;AAC1D,0EAAsE;AACtE,gEAA4D;AAC5D,kDAA8C;AAC9C,gDAA4C;AAC5C,oCAAgC;AAGhC,kEAA8D;AAI9D;;GAEG;AACH,MAAa,mBACT,SAAQ,iCAAe;IA0BvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAsB,EAAE,IAAqB;QACrD,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,IAAI,IAAI,CAAC,kBAAkB;YACvB,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;QAEnD,IAAI,IAAI,CAAC,yBAAyB;YAC9B,OAAO,IAAI,CAAC,yBAAyB,CAAA;QAEzC,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YACpD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,qBAAqB,EAAE;iBACvB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBAEpC,MAAM,eAAe,GAAG,CAAC,GAAU,EAAE,EAAE,CACnC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAA;gBACvC,IAAI,CAAC,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;oBACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAClC,OAAO,EACP,eAAe,CAClB,CAAA;oBACD,OAAO,CAAC,GAAG,CAAC,CAAA;gBAChB,CAAC,CAAA;gBACD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;gBAEpD,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;aAAM,CAAC;YACJ,SAAS;YACT,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,MAAM;iBACvC,sBAAsB,EAAE;iBACxB,IAAI,CAAC,CAAC,CAAC,UAAU,EAAE,OAAO,CAAQ,EAAE,EAAE;gBACnC,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC5C,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAA;gBAEpC,MAAM,eAAe,GAAG,CAAC,GAAU,EAAE,EAAE,CACnC,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAA;gBACvC,IAAI,CAAC,eAAe,GAAG,CAAC,GAAW,EAAE,EAAE;oBACnC,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAClC,OAAO,EACP,eAAe,CAClB,CAAA;oBACD,OAAO,CAAC,GAAG,CAAC,CAAA;gBAChB,CAAC,CAAA;gBACD,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;gBAEpD,OAAO,IAAI,CAAC,kBAAkB,CAAA;YAClC,CAAC,CAAC,CAAA;QACV,CAAC;QAED,OAAO,IAAI,CAAC,yBAAyB,CAAA;IACzC,CAAC;IAED;;;OAGG;IACK,KAAK,CAAC,yBAAyB,CAAC,GAAW;QAC/C,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAM;QACV,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,eAAe,GAAG,SAAS,CAAA;QACpC,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;QAE7D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAA;IAC3C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;YACrC,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,KAAK,CACZ,kCAAkC,GAAG,cAAc,CACtD,CAAA;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,qBAAqB,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAClE,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,6BAA6B,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC3D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;IAC9D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,uDAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,iCAAiC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC/D,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;YAC5B,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;QACpC,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAE1B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,sBAA+B,KAAK;QAEpC,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YACjC,MAAM,GAAG,GAAG,MAAM,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAC7D,oDAAoD;YACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;YAED,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB;gBAE1C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YAEL,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAChC,IAAI,GAAG,EAAE,CAAC;gBACN,IAAI,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC7B,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,IAAI,CAAA;gBAC7B,CAAC;gBAED,IAAI,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBACjC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAA;gBAClC,CAAC;gBAED,QAAQ,GAAG,CAAC,OAAO,EAAE,CAAC;oBAClB,KAAK,QAAQ,CAAC;oBACd,KAAK,QAAQ;wBACT,0EAA0E;wBAC1E,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,QAAQ,CAAC,CAAA;wBACrC,MAAK;oBACT;wBACI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,IAAI,CAAA;gBAC7B,CAAC;gBAED,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACvB,OAAO,MAAM,CAAC,GAAG,CAAA;gBACrB,CAAC;YACL,CAAC;YAED,OAAO,MAAM,CAAA;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YAED,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAClC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAA;QACtD,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/C,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,MAAM,GAAG,kBAAkB,CAAC,KAAK,CACnC,IAAI,WAAW,CAAC,KAAK,EAAE,UAAU,CAAC,CACrC,CAAA;QACD,IAAI,KAAK;YAAE,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAClC,IAAI,OAAO;YAAE,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAExC,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,4CAA4C,QAAQ,IAAI,CAC3D,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,kCAAkC,CAAC,CAAA;QAClE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,wEAAwE,MAAM,GAAG,CACpF,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAA;QAChE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,uEAAuE,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,GAAG,CAAA;QAC9J,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,wEAAwE,eAAe,CAAC,MAAM,yBAAyB,eAAe,CAAC,SAAS,0BAA0B,UAAU,GAAG,CAAA;QACnM,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;YAE9D,IAAI,qBAAqB;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QACvD,CAAC;QAED,MAAM,EAAE,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,GAAG,kBAAkB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,4BAA4B,QAAQ,GAAG;YACzC,CAAC,CAAC,kBAAkB,QAAQ,GAAG,CAAA;QACnC,MAAM,IAAI,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,MAAM,EAAE,GAAG,UAAU;YACjB,CAAC,CAAC,gCAAgC,MAAM,GAAG;YAC3C,CAAC,CAAC,kBAAkB,MAAM,GAAG,CAAA;QACjC,MAAM,IAAI,GAAG,gBAAgB,MAAM,WAAW,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,UAAkB,EAClB,OAAiB,EACjB,SAAmB;QAEnB,MAAM,MAAM,GACR,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QAElC,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,0BAA0B,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;YACnE,CAAC,CAAC,gBAAgB,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAC7D,MAAM,IAAI,GAAG,kBAAkB,MAAM,GAAG,CAAA;QACxC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,EAAE,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,6EAA6E;QAC7E,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACpC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,CACtE,CAAA;QACD,MAAM,gBAAgB,GAAa,EAAE,CAAA;QACrC,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAC/B,2EAA2E;YAC3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAElD,8FAA8F;YAC9F,IAAI,CAAC,OAAO,IAAI,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBACxD,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAA;YACnE,CAAC;QACL,CAAC;QAED,6FAA6F;QAC7F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,aAAa,KAAK,QAAQ,IAAI,MAAM,CAAC,YAAY,CAC/D,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,mBAAmB,GAAG,CACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAChD,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACZ,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YAErC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,mBAAmB;gBACf,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC;gBACtB,OAAO;gBACP,KAAK,CAAC,OAAO;gBACb,GAAG,CACV,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAC5D,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,MAAsB,EACtB,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,qGAAqG;QACrG,wDAAwD;QACxD,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;YAChD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAC3C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAClD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,mBAAmB,GAAG,CACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAChD,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACZ,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YAErC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,iCAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,iCAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,GACjD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAExC,QAAQ,CAAC,IAAI,GAAG,UAAU;YACtB,CAAC,CAAC,GAAG,UAAU,IAAI,YAAY,EAAE;YACjC,CAAC,CAAC,YAAY,CAAA;QAElB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,eAAe,YAAY,GAAG,CAClC,CACJ,CAAA;QAED,yEAAyE;QACzE,IACI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YAClC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,EACtD,CAAC;YACC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;QACL,CAAC;QAED,mBAAmB;QACnB,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;YACzB,IAAI,GAAG,CAAC,WAAW,IAAI,GAAG,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;gBAC5D,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;gBAC/D,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAA;gBAE/D,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC1C,QAAQ,EACR,GAAG,CAAC,IAAI,CACX,CAAA;gBACD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC1C,QAAQ,EACR,GAAG,CAAC,IAAI,CACX,CAAA;gBAED,MAAM,EAAE,GAAG,kBAAkB,IAAI,CAAC,UAAU,CACxC,YAAY,CACf,eAAe,eAAe,GAAG,CAAA;gBAClC,MAAM,IAAI,GAAG,kBAAkB,IAAI,CAAC,UAAU,CAC1C,eAAe,CAClB,eAAe,YAAY,GAAG,CAAA;gBAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;gBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YACrC,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,4BAA4B;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;gBAAE,OAAM;YAEzC,4BAA4B;YAC5B,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,4BAA4B;YAC5B,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YACvD,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,gBAAgB;YAChB,MAAM,EAAE,GAAG,MAAM;gBACb,CAAC,CAAC,gBAAgB,MAAM,MAAM,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG;gBACvE,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAAA;YAC/D,MAAM,IAAI,GAAG,MAAM;gBACf,CAAC,CAAC,gBAAgB,MAAM,MAAM,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG;gBACvE,CAAC,CAAC,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAAA;YAC/D,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;YAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;YAEjC,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,4BAA4B;YAC5B,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,QAAQ,CACX,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;YAED,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,oBAAoB;QACpB,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,CACtE,CAAA;QACD,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE,CAAC;YAC/B,2CAA2C;YAC3C,IAAI,MAAM,CAAC,QAAQ;gBAAE,SAAQ;YAE7B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACjD,QAAQ,EACR,MAAM,CACT,CAAA;YACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,WAAW,CAAC,MAAM,MAC7B,WAAW,CAAC,IAChB,eAAe,IAAI,CAAC,aAAa,CAC7B,QAAQ,EACR,MAAM,EACN,KAAK,CACR,EAAE,CACN,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,cAAc,IAAI,CAAC,aAAa,CAC5B,QAAQ,EACR,MAAM,CACT,eAAe,WAAW,CAAC,IAAI,GAAG,CACtC,CACJ,CAAA;QACL,CAAC;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,IAAI,CAAC,OAAO,EAAE,CAAC;gBACX,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;gBACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;YACzD,CAAC;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CACtD,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,wEAAwE;YACxE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;gBAEP,MAAM,WAAW,GAAG,cAAc;qBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;qBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACL,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,gBAAgB,GAAG,IAAI,yBAAW,CAAC;gBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;gBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,cAAc,MAAM,CAAC,IAAI,IAAI,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,gBAAgB,CAAC,IACrB,GAAG,CACN,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3D,MAAM,mBAAmB,GAAG,CACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAChD,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACZ,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YAErC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,MAAM,CAAC,IACX,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/C,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,MAAM,CAAC,IACX,QAAQ,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAC/C,CACJ,CAAA;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,CAAA;QACb,IAAI,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACzD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,IAAI,mBAAmB,GAAG,KAAK,CAAA;QAE/B,MAAM,SAAS,GAAG,iCAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACP,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,oBAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IACI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;YACvC,CAAC,CAAC,SAAS,CAAC,aAAa;gBACrB,SAAS,CAAC,aAAa,KAAK,QAAQ,CAAC;YACzC,CAAC,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY;gBAC9C,SAAS,CAAC,aAAa,KAAK,QAAQ,CAAC,EAC3C,CAAC;YACC,oDAAoD;YACpD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,gBAAgB;gBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,mBACjC,SAAS,CAAC,IACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBAED,mBAAmB;gBACnB,IACI,SAAS,CAAC,IAAI,KAAK,MAAM;oBACzB,SAAS,CAAC,IAAI,KAAK,aAAa,EAClC,CAAC;oBACC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CACjD,KAAK,EACL,SAAS,CACZ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,WAAW,CAAC,MAAM,MAC7B,WAAW,CAAC,IAChB,eAAe,IAAI,CAAC,aAAa,CAC7B,KAAK,EACL,SAAS,EACT,KAAK,CACR,EAAE,CACN,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,cAAc,IAAI,CAAC,aAAa,CAC5B,KAAK,EACL,SAAS,CACZ,eAAe,WAAW,CAAC,IAAI,GAAG,CACtC,CACJ,CAAA;gBACL,CAAC;gBAED,uCAAuC;gBACvC,IACI,SAAS,CAAC,SAAS,KAAK,IAAI;oBAC5B,CAAC,SAAS,CAAC,wBAAwB,EACrC,CAAC;oBACC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;oBAEjD,oCAAoC;oBACpC,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;oBACD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,+CAA+C;oBAC/C,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC1D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAEhC,oCAAoC;oBACpC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,SAAS,SAAS,SAAS,GAAG,CACzD,CACJ,CAAA;gBACL,CAAC;gBAED,yBAAyB;gBACzB,IACI,SAAS,CAAC,WAAW,KAAK,IAAI;oBAC9B,SAAS,CAAC,kBAAkB,KAAK,WAAW,EAC9C,CAAC;oBACC,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CACvC,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBACD,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CACvC,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBAED,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC1C,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBACD,MAAM,eAAe,GAAG,IAAI,CAAC,iBAAiB,CAC1C,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBAED,MAAM,EAAE,GAAG,kBAAkB,IAAI,CAAC,UAAU,CACxC,YAAY,CACf,eAAe,eAAe,GAAG,CAAA;oBAClC,MAAM,IAAI,GAAG,kBAAkB,IAAI,CAAC,UAAU,CAC1C,eAAe,CAClB,eAAe,YAAY,GAAG,CAAA;oBAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;gBACrC,CAAC;gBAED,4BAA4B;gBAC5B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxD,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,2DAA2D;oBAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;wBAAE,OAAM;oBAEzC,4BAA4B;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,CACrB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,MAAM,CAAC,IACX,SAAS,aAAa,GAAG,CAC5B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,aAAa,SACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;gBAC/B,CAAC,CAAC,CAAA;gBAEF,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;wBAAE,OAAM;oBAEvC,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;oBACpD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,gBAAgB;oBAChB,MAAM,EAAE,GAAG,MAAM;wBACb,CAAC,CAAC,gBAAgB,MAAM,MAAM,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG;wBACvE,CAAC,CAAC,gBAAgB,KAAK,CAAC,IAAI,gBAAgB,YAAY,GAAG,CAAA;oBAC/D,MAAM,IAAI,GAAG,MAAM;wBACf,CAAC,CAAC,gBAAgB,MAAM,MAAM,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG;wBACvE,CAAC,CAAC,gBAAgB,YAAY,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAAA;oBAE/D,SAAS,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC7B,WAAW,CAAC,IAAI,CAAC,IAAI,aAAK,CAAC,IAAI,CAAC,CAAC,CAAA;oBAEjC,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gEAAgE;oBAChE,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc;wBAAE,OAAM;oBAE9C,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBACG,UAAU,CAAC,IACf,SAAS,iBAAiB,GAAG,CAChC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,uBAAuB,iBAAiB,SACrC,UAAU,CAAC,IACf,GAAG,CACN,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;gBACvC,CAAC,CAAC,CAAA;gBAEN,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IACI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS;gBAC3C,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,EACrC,CAAC;gBACC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;YACL,CAAC;YAED,IACI,CAAC,SAAS,CAAC,IAAI,KAAK,MAAM;gBACtB,SAAS,CAAC,IAAI,KAAK,aAAa,CAAC;gBACrC,CAAC,SAAS,CAAC,IAAI,KAAK,MAAM;oBACtB,SAAS,CAAC,IAAI,KAAK,aAAa,CAAC;gBACrC,CAAC,CAAC,mBAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,IAAK,EAAE,SAAS,CAAC,IAAK,CAAC;oBACtD,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,CAAC,EAChD,CAAC;gBACC,MAAM,WAAW,GAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAA;gBAEjD,sBAAsB;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;gBAExD,sBAAsB;gBACtB,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;gBAExD,aAAa;gBACb,MAAM,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAC/C,KAAK,EACL,SAAS,EACT,KAAK,CACR,CAAA;gBAED,yBAAyB;gBACzB,MAAM,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAChD,KAAK,EACL,SAAS,EACT,IAAI,EACJ,KAAK,EACL,IAAI,CACP,CAAA;gBAED,gBAAgB;gBAChB,MAAM,4BAA4B,GAAG,IAAI,CAAC,aAAa,CACnD,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,EACL,IAAI,CACP,CAAA;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,cAAc,WAAW,cAAc,4BAA4B,EAAE,CACxE,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,cAAc,yBAAyB,cAAc,wBAAwB,EAAE,CAClF,CACJ,CAAA;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CACxD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,SAAS,EAAE,WAAW,CAAC,CACtD,CAAA;gBAED,kFAAkF;gBAClF,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,mDAAmD;oBACnD,mBAAmB,GAAG,IAAI,CAAA;oBAC1B,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;gBACL,CAAC;gBAED,qBAAqB;gBACrB,MAAM,MAAM,GAAG,GAAG,WAAW,GAAG,WAAW,WAAW,SAAS,CAAC,IAAI,cAAc,WAAW,GAAG,WAAW,EAAE,CAAA;gBAC7G,MAAM,QAAQ,GAAG,GAAG,yBAAyB,GAAG,WAAW,WAAW,SAAS,CAAC,IAAI,cAAc,yBAAyB,GAAG,WAAW,EAAE,CAAA;gBAE3I,gCAAgC;gBAChC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,MAAM,EAAE,CACrB,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,QAAQ,EAAE,CACvB,CACJ,CAAA;gBAED,2CAA2C;gBAC3C,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;gBACL,CAAC;gBAED,kBAAkB;gBAClB,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,eAAe,CAChB,KAAK,EACL,SAAS,EACT,yBAAyB,CAC5B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAClB,KAAK,EACL,SAAS,EACT,yBAAyB,CAC5B,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,UAAU,EAAE,CAAC;gBAChD,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;oBACvB,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAAiB,CACrD,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,qBAAqB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACvC,SAAS,CAAC,IACd,QAAQ,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAClD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBAED,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;6BAC3B,wBAAwB;4BACzB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;4BAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;wBAEP,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;6BACnC,IAAI,CAAC,IAAI,CAAC,CAAA;wBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,gBAAgB,GAAG,IAAI,yBAAW,CAAC;wBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,SAAS,CAAC,IAAI,CAAC,CACnB;wBACD,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;qBAChC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;oBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAgB,CAAC,IACrB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAgB,CAAC,IAAI,GAAG,CACjD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,MAAM,EAAE,EAAE;wBACP,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC/B,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CACrB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,KAAK,SAAS,CAAC,IAAI,CACpC,CACJ,CAAA;oBACL,CAAC,CACJ,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAiB,CAAC,IAAI,GAAG,CAClD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAiB,CAAC,IACtB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,EAAE,CAAC;gBAClD,0DAA0D;gBAC1D,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;oBACxB,IAAI,SAAS,CAAC,kBAAkB,KAAK,MAAM,EAAE,CAAC;wBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,iBAAiB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAC/C,CACJ,CAAA;oBACL,CAAC;yBAAM,IAAI,SAAS,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;wBACtD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,0BAA0B,IAAI,CAAC,UAAU,CACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,IAAI,CACR,CACJ,CAAA;wBAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,EAAE,CACN,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,iCAAiC,IAAI,CAAC,UAAU,CAC5C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAChC,SAAS,CAAC,IACd,GAAG,CACN,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,kBAAkB,KAAK,MAAM,EAAE,CAAC;oBAC1C,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;wBACjC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,iBAAiB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAC/C,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,iBAAiB,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAC/C,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,IAAI,SAAS,CAAC,kBAAkB,KAAK,WAAW,EAAE,CAAC;oBACtD,IAAI,SAAS,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;wBACjC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,iCAAiC,IAAI,CAAC,UAAU,CAC5C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAChC,SAAS,CAAC,IACd,GAAG,CACN,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,EAAE,CACN,CACJ,CAAA;wBAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,0BAA0B,IAAI,CAAC,UAAU,CACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,IAAI,CACR,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,0BAA0B,IAAI,CAAC,UAAU,CACrC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,IAAI,CACR,CACJ,CAAA;wBAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,iBAAiB,IAAI,CAAC,UAAU,CAC5B,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,EAAE,CACN,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,iCAAiC,IAAI,CAAC,UAAU,CAC5C,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAC3C,aAAa,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAChC,SAAS,CAAC,IACd,GAAG,CACN,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,uDAAuD;YACvD,IACI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO;gBACvC,CAAC,mBAAmB,EACtB,CAAC;gBACC,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;oBAED,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;wBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;wBACC,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,iBAAiB,SAAS,CAAC,OAAO,EAAE,CACvC,CACJ,CAAA;oBACL,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBACG,SAAS,CAAC,IACd,gBAAgB,CACnB,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;qBAAM,IACH,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,gBAAgB,CACpD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,kBAAkB,SAAS,CAAC,IAAI,iBAC7B,SAAS,CAAC,OACd,EAAE,CACL,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IACI,CAAC,SAAS,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;gBAC9C,CAAC,SAAS,CAAC,kBAAkB,IAAI,EAAE,CAAC,CAAC,WAAW,EAAE;gBACtD,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EACnC,CAAC;gBACC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,EAAE,CACpD,CACJ,CAAA;YACL,CAAC;YAED,0BAA0B;YAC1B,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,SAAS,CAAC,IAAI,aACpB,SAAS,CAAC,SACd,GAAG,CACN,CACJ,CAAA;gBAED,MAAM,YAAY,GAAG,SAAS,CAAC,SAAS;oBACpC,CAAC,CAAC,IAAI,SAAS,CAAC,SAAS,GAAG;oBAC5B,CAAC,CAAC,sBAAsB,CAAA,CAAC,2CAA2C;gBAExE,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,kBACjC,SAAS,CAAC,IACd,UAAU,SAAS,CAAC,IAAI,YAAY,YAAY,EAAE,CACrD,CACJ,CAAA;YACL,CAAC;YAED,IAAI,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,EAAE,CAAC;gBACtD,iDAAiD;gBACjD,IACI,CAAC,SAAS,CAAC,aAAa;oBACxB,SAAS,CAAC,aAAa,KAAK,SAAS,EACvC,CAAC;oBACC,mDAAmD;oBACnD,MAAM,mBAAmB,GAAG,CACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAChD,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBACZ,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;oBACxC,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;oBAErC,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,mBAAmB,SAAS,CAAC,IAAI,kBAC9B,SAAS,CAAC,IACd,GAAG,CACN,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAC9B,KAAK,EACL,SAAS,CACZ,EAAE,CACN,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,SAC5B,SAAS,CAAC,IACd,iBAAiB,SAAS,CAAC,IAAI,GAAG,CACrC,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,0BAA0B,SAAS,CAAC,IAAI,GAAG,CAC/C,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;wBAC9B,MAAM;wBACN,KAAK,EAAE,SAAS;wBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;qBACvB,CAAC,CACL,CAAA;oBACD,uEAAuE;oBACvE,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;wBAC9B,MAAM;wBACN,KAAK,EAAE,SAAS;wBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,KAAK,EAAE,SAAS,CAAC,YAAY;qBAChC,CAAC,CACL,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAC9B,KAAK,EACL,SAAS,CACZ,EAAE,CACN,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,SAAS,CAAC,IAAI,GAAG,CACtC,CACJ,CAAA;oBACD,oBAAoB;oBACpB,sCAAsC;oBACtC,0CAA0C;oBAC1C,kBAAkB;oBAClB,4BAA4B;oBAC5B,oDAAoD;oBACpD,gCAAgC;oBAChC,UAAU;oBACV,IAAI;gBACR,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,oBAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB;gBAC1C,CAAC,CAAC,MAAM,CAAC,wBAAwB;gBACjC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;YAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC;qBACvC,wBAAwB;oBACzB,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACxD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CACJ,CAAA;gBAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;qBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;QACD,IAAI,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EACzC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAA;YACjE,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,CACtD,CAAA;QACL,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,EAAE,CACtD,CACJ,CAAA;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YACrD,IAAI,OAAO,EAAE,CAAC;gBACV,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAC9C,KAAK,EACL,MAAM,CACT,CAAA;gBACD,MAAM,eAAe,GAAG,IAAI,QAAQ,CAAC,MAAM,MAAM,QAAQ,CAAC,IAAI,GAAG,CAAA;gBACjE,SAAS,CAAC,IAAI,CACV,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CACvD,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE,eAAe,CAAC,CACzD,CAAA;YACL,CAAC;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;YACpC,MAAM,mBAAmB,GAAG,CACxB,MAAM,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,IAAI,CAAC,CAChD,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YACZ,MAAM,SAAS,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACxC,MAAM,MAAM,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAA;YACrC,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,MAAM;gBACN,KAAK,EAAE,SAAS;gBAChB,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB,EACrB,cAAuB;QAEvB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;QAEvE,4GAA4G;QAC5G,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAEhD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,EAAE,wBAAwB;YACtD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;QAEP,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,cAAuB;QAEvB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CACjC,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EACjD,cAAc,CACjB,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACtB,gBAAgB,CAAC,IAAI;gBACjB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,gBAAgB,CAAC,WAAW,CAC/B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YAC/C,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QACpE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChE,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,oBAAY,CAClB,qDAAqD,KAAK,CAAC,IAAI,EAAE,CACpE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACpE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,KAAK,MAAM,gBAAgB,IAAI,iBAAiB,EAAE,CAAC;YAC/C,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,oBAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,mBAAmB,CAAC,IAAI;YACzB,mBAAmB,CAAC,IAAI;gBACpB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAClD,KAAK,EACL,mBAAmB,CAAC,UAAW,CAClC,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAA;QACxE,MAAM,IAAI,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAA;QACxE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,sBAAsB,CAAC,mBAAmB,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAC9D,IAAI,CAAC,yBAAyB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CACnE,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,mBAAmB,GAAG,iCAAe,CAAC,gBAAgB,CACxD,eAAe,CAClB;YACG,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,eAAe,CAAC,CAAA;QAC9D,IAAI,CAAC,mBAAmB;YACpB,MAAM,IAAI,oBAAY,CAClB,wDAAwD,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAA;QACtE,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAC1C,KAAK,EACL,mBAAmB,CACtB,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,yBAAyB,CAAC,mBAAmB,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,QAAQ,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAC9D,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,mBAAmB,CAAC,CACjE,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACxD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,iCAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,oBAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YACnB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QACL,CAAC;QAED,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACnC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,UAAyB,EACzB,KAAiB;QAEjB,MAAM,IAAI,GAAG,iCAAe,CAAC,MAAM,CAAC,UAAU,CAAC;YAC3C,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAE1C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAEjE,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAC/C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAC3C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,UAAyB,EACzB,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;QACjD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,oBAAY,CAClB,kBAAkB,WAAW,2BAA2B,KAAK,CAAC,IAAI,EAAE,CACvE,CAAA;QACL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,UAAyB,EACzB,WAAgC;QAEhC,MAAM,IAAI,GAAG,iCAAe,CAAC,MAAM,CAAC,UAAU,CAAC;YAC3C,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC1C,MAAM,KAAK,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,oBAAY,CAClB,kBAAkB,WAAW,0BAA0B,IAAI,CAAC,IAAI,EAAE,CACrE,CAAA;QACL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAEjE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACzC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QACjD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;YAC1B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CAAA;QAC5C,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa;QACf,MAAM,OAAO,GAAa,EAAE,CAAA;QAC5B,IAAI,CAAC,UAAU,CAAC,eAAe;aAC1B,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;aACrC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAClB,MAAM,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAChC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,QAAQ,CAAC,MAAM,CACzC,CAAA;YACD,IAAI,CAAC,aAAa;gBAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAO,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QACN,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,kBAAkB,CAAC,CAAA;QAC9D,MAAM,iBAAiB,GAAG,OAAO;aAC5B,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;YACV,OAAO,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,GAAG,CAAA;QAChE,CAAC,CAAC;aACD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,aAAa;YACb,MAAM,oBAAoB,GACtB,+FAA+F;gBAC/F,0CAA0C,iBAAiB,yGAAyG,CAAA;YACxK,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,oBAAoB,CACvB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACrD,CAAA;YAED,0BAA0B;YAC1B,sDAAsD;YACtD,IAAI,yBAAW,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,CAAC;gBAC5D,MAAM,uBAAuB,GACzB,+GAA+G;oBAC/G,6CAA6C,iBAAiB,GAAG,CAAA;gBACrE,MAAM,kBAAkB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACxD,uBAAuB,CAC1B,CAAA;gBACD,MAAM,OAAO,CAAC,GAAG,CACb,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACxD,CAAA;YACL,CAAC;YAED,kEAAkE;YAClE,mDAAmD;YAEnD,cAAc;YACd,MAAM,qBAAqB,GAAG,0IAA0I,iBAAiB,8CAA8C,CAAA;YACvO,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,qBAAqB,CACxB,CAAA;YACD,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CACtD,CAAA;YAED,kBAAkB;YAClB,MAAM,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAA;YAE3C,IAAI,CAAC,0BAA0B,EAAE,CAAC;gBAC9B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;YAClC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B,EAAE,CAAC;oBAC9B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;gBACpC,CAAC;YACL,CAAC;YAAC,MAAM,CAAC;gBACL,QAAQ;YACZ,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QAExE,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,CAAA;QAExB,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACvD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,cAAc,GAChB,SAAS,CAAC,MAAM,KAAK,CAAC;YAClB,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,SAAS;iBACJ,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;iBACzD,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM;wBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAA;gBACnD,CAAC;gBAED,OAAO,oBAAoB,MAAM,uBAAuB,SAAS,IAAI,CAAA;YACzE,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAE3B,MAAM,oBAAoB,GACtB,SAAS,CAAC,MAAM,KAAK,CAAC;YAClB,CAAC,CAAC,KAAK;YACP,CAAC,CAAC,SAAS;iBACJ,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;iBACzD,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;oBACV,MAAM;wBACF,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAA;gBACnD,CAAC;gBAED,OAAO,sBAAsB,MAAM,0BAA0B,SAAS,IAAI,CAAA;YAC9E,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAE3B,MAAM,UAAU,GACZ,8IAA8I;YAC9I,4IAA4I;YAC5I,mCAAmC;YACnC,sBAAsB;YACtB,4DAA4D;YAC5D,sGAAsG;YACtG,oEAAoE;YACpE,6DAA6D;YAC7D,iEAAiE;YACjE,uEAAuE;YACvE,kEAAkE,oBAAoB,GAAG,CAAA;QAE7F,MAAM,KAAK,GACP,qBAAqB,IAAI,CAAC,UAAU,CAChC,IAAI,CAAC,2BAA2B,EAAE,CACrC,OAAO;YACR,uEAAuE;YACvE,mGAAmG;YACnG,yBAAyB,qCAAiB,CAAC,IAAI,OAC3C,qCAAiB,CAAC,iBACtB,MAAM,cAAc,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAE3D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,MAAM,SAAS,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;QAC/D,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;gBACzB,OAAO,CACH,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC;oBACxC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC,CAC/C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAC1C,CAAA;YACD,MAAM,IAAI,GAAG,IAAI,WAAI,EAAE,CAAA;YACvB,MAAM,MAAM,GACR,MAAM,CAAC,QAAQ,CAAC,KAAK,aAAa;gBAClC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,eAAe,CAAA;YAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,CAAA;YAC9D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,IAAI,CAAC,YAAY;gBACb,MAAM,CAAC,MAAM,CAAC,KAAK,qCAAiB,CAAC,iBAAiB,CAAA;YAC1D,IAAI,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,OAAO,CACH,KAAK,CAAC,cAAc,CAAC,KAAK,UAAU,CAAC,cAAc,CAAC;wBACpD,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;wBAChD,KAAK,CAAC,iBAAiB,CAAC;4BACpB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,IAAI,uBAAU,CAAoB;oBACrC,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,KAAK,MAAM;oBAC5C,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC;oBAC9B,UAAU,EAAE,KAAK;iBACpB,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACF,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,6CAA6C;QAC7C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,MAAM,QAAQ,GAIR,EAAE,CAAA;QAER,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,SAAS,GAAG,yLAAyL,CAAA;YAC3M,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,eAAe,GAAG,UAAU;iBAC7B,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;iBACzD,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;gBAC3B,OAAO,sBACH,MAAM,IAAI,aACd,yBAAyB,SAAS,IAAI,CAAA;YAC1C,CAAC,CAAC;iBACD,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,MAAM,SAAS,GACX,gMAAgM;gBAChM,eAAe,CAAA;YACnB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAA;QACb,CAAC;QAED;;;;WAIG;QACH,MAAM,gBAAgB,GAAG,QAAQ;aAC5B,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,yBAAyB,UAAU,IAAI,CAAA;QACpF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QACjB,MAAM,UAAU,GACZ,+KAA+K;YAC/K,sKAAsK;YACtK,sCAAsC;YACtC,wGAAwG;YACxG,gCAAgC;YAChC,2DAA2D;YAC3D,qFAAqF;YACrF,iDAAiD;YACjD,gDAAgD;YAChD,IAAI;YACJ,QAAQ;YACR,gBAAgB,CAAA;QAEpB,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,0BAA0B,UAAU,IAAI,CAAA;QACrF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,cAAc,GAChB,iHAAiH;YACjH,sDAAsD;YACtD,8KAA8K;YAC9K,8BAA8B;YAC9B,6DAA6D;YAC7D,uEAAuE;YACvE,8GAA8G;YAC9G,0CAA0C,oBAAoB,GAAG,CAAA;QAErE,MAAM,UAAU,GACZ,8IAA8I;YAC9I,4IAA4I;YAC5I,kEAAkE;YAClE,sBAAsB;YACtB,4DAA4D;YAC5D,sGAAsG;YACtG,oEAAoE;YACpE,6DAA6D;YAC7D,iEAAiE;YACjE,sDAAsD;YACtD,uEAAuE;YACvE,uEAAuE,oBAAoB,GAAG,CAAA;QAElG,MAAM,oBAAoB,GAAG,QAAQ;aAChC,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;YAClC,OAAO,sBAAsB,YAAY,2BAA2B,UAAU,IAAI,CAAA;QACtF,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,uBAAuB,GACzB,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAA;QAC/C,MAAM,oBAAoB,GAAG,uBAAuB;YAChD,CAAC,CAAC,kCAAkC;YACpC,CAAC,CAAC,EAAE,CAAA;QAER,MAAM,cAAc,GAChB,sJAAsJ;YACtJ,2KAA2K;YAC3K,+GAA+G;YAC/G,SAAS;YACT,gLAAgL;YAChL,0CAA0C;YAC1C,0GAA0G;YAC1G,kLAAkL;YAClL,iLAAiL;YACjL,uBAAuB;YACvB,qEAAqE;YACrE,sEAAsE;YACtE,qCAAqC,oBAAoB,IAAI;YAC7D,UAAU;YACV,6GAA6G;YAC7G,gEAAgE,oBAAoB,EAAE;YACtF,qEAAqE;YACrE,+GAA+G,CAAA;QAEnH,MAAM,CACF,SAAS,EACT,aAAa,EACb,SAAS,EACT,aAAa,EAChB,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SAC7B,CAAC,CAAA;QAEF,kCAAkC;QAClC,OAAO,OAAO,CAAC,GAAG,CACd,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,aAAK,EAAE,CAAA;YAEzB,MAAM,gBAAgB,GAAG,CAAC,QAAa,EAAE,GAAW,EAAE,EAAE;gBACpD,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,aAAa;oBAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;wBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC;oBACjD,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YACvB,CAAC,CAAA;YACD,mEAAmE;YACnE,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;YACxD,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAA;YAChC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YACtC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;YACxC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,MAAM,CACT,CAAA;YAED,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,QAAQ,CAAC,cAAc,CAAC;oBACpB,OAAO,CAAC,cAAc,CAAC,CAClC;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAC1C,CAAC,YAAY,EAAE,EAAE;oBACb,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;wBACtB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,YAAY,CAAC,cAAc,CAAC;4BACxB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAC9B,CAAA;gBACL,CAAC,CACJ,CAAA;gBAED,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1C,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAA;gBAEpD,IACI,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC9B,WAAW,CAAC,IAAI,KAAK,WAAW;oBAChC,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC9B,WAAW,CAAC,IAAI,KAAK,OAAO,EAC9B,CAAC;oBACC,IAAI,gBAAgB,GAChB,QAAQ,CAAC,mBAAmB,CAAC,CAAA;oBACjC,IAAI,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,CAAA;oBAC5C,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,CAAC;wBACpC,MAAM,WAAW,GAAG,QAAQ,CACxB,aAAa,CAChB,CAAC,KAAK,CACH,oCAAoC,CACvC,CAAA;wBACD,IAAI,WAAW,EAAE,CAAC;4BACd,gBAAgB,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;4BAClC,YAAY,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;wBAClC,CAAC;oBACL,CAAC;oBACD,wGAAwG;oBACxG,iFAAiF;oBACjF,IACI,gBAAgB,KAAK,IAAI;wBACzB,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,gBAAgB,CACnB,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,gBAAgB,CAAA;oBAC5C,CAAC;yBAAM,IACH,YAAY,KAAK,IAAI;wBACrB,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,YAAY,CACf,EACH,CAAC;wBACC,WAAW,CAAC,SAAS,GAAG,SAAS,CAAA;oBACrC,CAAC;oBACD,IACI,YAAY,KAAK,IAAI;wBACrB,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,YAAY,CACf,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,YAAY,CAAA;oBACpC,CAAC;yBAAM,IACH,gBAAgB,KAAK,IAAI;wBACzB,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,gBAAgB,CACnB,EACH,CAAC;wBACC,WAAW,CAAC,KAAK,GAAG,SAAS,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,UAAU;oBAC/B,WAAW,CAAC,IAAI,KAAK,wBAAwB;oBAC7C,WAAW,CAAC,IAAI,KAAK,qBAAqB;oBAC1C,WAAW,CAAC,IAAI;wBACZ,6BAA6B;oBACjC,WAAW,CAAC,IAAI,KAAK,0BAA0B,EACjD,CAAC;oBACC,WAAW,CAAC,SAAS;wBACjB,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,oBAAoB,CAAC,CACjC;4BACG,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC;4BAChC,CAAC,CAAC,SAAS,CAAA;gBACvB,CAAC;gBAED,8CAA8C;gBAC9C,gGAAgG;gBAChG,IACI,QAAQ,CAAC,WAAW,CAAC,KAAK,cAAc;oBACxC,QAAQ,CAAC,WAAW,CAAC,KAAK,OAAO,EACnC,CAAC;oBACC,MAAM,EAAE,IAAI,EAAE,GACV,MAAM,IAAI,CAAC,sBAAsB,CAC7B,KAAK,EACL,WAAW,CACd,CAAA;oBAEL,2CAA2C;oBAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CACpC,KAAK,EACL,WAAW,EACX,KAAK,EACL,IAAI,CACP,CAAA;oBACD,MAAM,QAAQ,GACV,aAAa,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAA;oBAE7C,wBAAwB;oBACxB,MAAM,GAAG,GACL,uDAAuD;wBACvD,0DAA0D;wBAC1D,kEAAkE;wBAClE,0BACI,OAAO,CAAC,cAAc,CAC1B,0BACI,QAAQ,IAAI,IAChB,GAAG,CAAA;oBACP,MAAM,OAAO,GACT,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAEzB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;wBACjB,WAAW,CAAC,IAAI,GAAG,MAAM,CAAA;wBACzB,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAC9B,CAAA;wBACD,WAAW,CAAC,QAAQ,GAAG,QAAQ,CAAA;oBACnC,CAAC;oBAED,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE,CAAC;wBACpC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAA;wBAC1B,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CACjC,IAAI,EACJ,EAAE,CACL,CAAA;wBACD,WAAW,CAAC,IAAI;4BACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;gCACjC,IAAI,EAAE,IAAI;6BACb,CAAC,CAAA;oBACV,CAAC;gBACL,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,UAAU;oBAC/B,WAAW,CAAC,IAAI,KAAK,WAAW,EAClC,CAAC;oBACC,MAAM,GAAG,GACL,iBAAiB;wBACjB,uEAAuE;wBACvE,MAAM,WAAW,CAAC,IAAI,yCAAyC;wBAC/D,SAAS,WAAW,CAAC,IAAI,WAAW;wBACpC,SAAS;wBACT,0BAA0B,QAAQ,CAAC,aAAa,CAAC,QAAQ;wBACzD,qBAAqB,QAAQ,CAAC,cAAc,CAAC,QAAQ;wBACrD,mBAAmB,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAA;oBAEhD,MAAM,OAAO,GACT,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;oBAEzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrB,WAAW,CAAC,kBAAkB;4BAC1B,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;wBACnB,WAAW,CAAC,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;oBACtC,CAAC;gBACL,CAAC;gBAED,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC,EACV,CAAC;oBACC,IAAI,MAAM,CAAA;oBACV,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;wBACtB,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAC1B,QAAQ,CAAC,aAAa,CAAC,CAC1B,CAAA;wBACD,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;oBACzC,CAAC;yBAAM,IACH,QAAQ,CAAC,0BAA0B,CAAC,EACtC,CAAC;wBACC,MAAM;4BACF,QAAQ,CACJ,0BAA0B,CAC7B,CAAC,QAAQ,EAAE,CAAA;oBACpB,CAAC;oBACD,IAAI,MAAM,EAAE,CAAC;wBACT,WAAW,CAAC,MAAM;4BACd,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;gCACG,CAAC,CAAC,MAAM;gCACR,CAAC,CAAC,EAAE,CAAA;oBAChB,CAAC;gBACL,CAAC;gBACD,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAA;gBAErC,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAC5C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,SAAS,CAClD,CAAA;gBACD,IAAI,iBAAiB,EAAE,CAAC;oBACpB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAA;oBAC5B,0DAA0D;oBAC1D,MAAM,yBAAyB,GAC3B,aAAa,CAAC,MAAM,CAChB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,YAAY,CAAC;wBACpB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,UAAU,CAAC,cAAc,CAAC;4BACtB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,UAAU,CAAC,aAAa,CAAC;4BACrB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,UAAU,CAAC,iBAAiB,CAAC;4BACzB,SAAS,CACpB,CAAA;oBAEL,2BAA2B;oBAC3B,MAAM,WAAW,GACb,yBAAyB,CAAC,GAAG,CACzB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,aAAa,CAAC,CAChC,CAAA;oBACL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;oBAEzC,4CAA4C;oBAC5C,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,WAAW,CACd,CAAA;oBAEL,4EAA4E;oBAC5E,IACI,iBAAiB,CAAC,iBAAiB,CAAC;wBACpC,MAAM,EACR,CAAC;wBACC,WAAW,CAAC,wBAAwB;4BAChC,iBAAiB,CAAC,iBAAiB,CAAC,CAAA;oBAC5C,CAAC;gBACL,CAAC;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC9C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CACjD,CAAA;gBACD,MAAM,qBAAqB,GACvB,iBAAiB,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI,CACrB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,iBAAiB,CAAC;wBAC3B,QAAQ;wBACZ,YAAY,CAAC,iBAAiB,CAAC;4BAC3B,gBAAgB,CACZ,iBAAiB,CACpB;wBACL,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBACN,WAAW,CAAC,QAAQ;oBAChB,iBAAiB,CAAC,MAAM,GAAG,CAAC;wBAC5B,CAAC,qBAAqB,CAAA;gBAE1B,IAAI,QAAQ,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;oBACjC,+BAA+B;oBAC/B,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;oBAC9B,WAAW,CAAC,kBAAkB,GAAG,UAAU,CAAA;oBAC3C,WAAW,CAAC,iBAAiB;wBACzB,QAAQ,CAAC,mBAAmB,CAAA;gBACpC,CAAC;qBAAM,IACH,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;oBACnC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,SAAS,EAC1C,CAAC;oBACC,MAAM,iBAAiB,GAAG,YAAY,IAAI,CAAC,iBAAiB,CACxD,KAAK,EACL,QAAQ,CAAC,aAAa,CAAC,CAC1B,cAAc,CAAA;oBACf,MAAM,iBAAiB,GAAG,YAAY,IAAI,CAAC,iBAAiB,CACxD,KAAK,EACL,QAAQ,CAAC,aAAa,CAAC,CAC1B,cAAc,CAAA;oBAEf,MAAM,oBAAoB,GAAG,QAAQ,CACjC,gBAAgB,CACnB,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;oBAEnB,IACI,oBAAoB;wBAChB,iBAAiB;wBACrB,oBAAoB,KAAK,iBAAiB,EAC5C,CAAC;wBACC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;wBAC9B,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;oBAChD,CAAC;yBAAM,IACH,QAAQ,CAAC,gBAAgB,CAAC;wBACtB,mBAAmB;wBACvB,wBAAwB,CAAC,IAAI,CACzB,QAAQ,CAAC,gBAAgB,CAAC,CAC7B,EACH,CAAC;wBACC,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;4BAC9B,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;4BAC9B,WAAW,CAAC,kBAAkB,GAAG,MAAM,CAAA;wBAC3C,CAAC;6BAAM,CAAC;4BACJ,WAAW,CAAC,OAAO;gCACf,QAAQ,CAAC,gBAAgB,CAAC,CAAA;wBAClC,CAAC;oBACL,CAAC;yBAAM,IACH,QAAQ,CAAC,gBAAgB,CAAC,KAAK,OAAO;wBACtC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAC9B,aAAa,CAChB,KAAK,CAAC,CAAC,EACV,CAAC;wBACC,WAAW,CAAC,OAAO;4BACf,QAAQ,CAAC,gBAAgB,CAAC,CAAA;oBAClC,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,OAAO,GAAG,QAAQ,CAC1B,gBAAgB,CACnB,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAA;wBAClC,WAAW,CAAC,OAAO;4BACf,WAAW,CAAC,OAAO,CAAC,OAAO,CACvB,WAAW,EACX,MAAM,CACT,CAAA;oBACT,CAAC;gBACL,CAAC;gBAED,IACI,QAAQ,CAAC,cAAc,CAAC,KAAK,QAAQ;oBACrC,QAAQ,CAAC,uBAAuB,CAAC,EACnC,CAAC;oBACC,wDAAwD;oBACxD,WAAW,CAAC,aAAa,GAAG,QAAQ,CAAA;oBACpC,0GAA0G;oBAC1G,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,QAAQ,EAAE,eAAe;wBACzB,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC;wBAC/B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,qCAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC;oBACzC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACzB,CAAC,CAAC,SAAS,CAAA;gBACf,IAAI,QAAQ,CAAC,oBAAoB,CAAC;oBAC9B,WAAW,CAAC,OAAO;wBACf,QAAQ,CAAC,oBAAoB,CAAC,CAAA;gBACtC,IAAI,QAAQ,CAAC,gBAAgB,CAAC;oBAC1B,WAAW,CAAC,SAAS;wBACjB,QAAQ,CAAC,gBAAgB,CAAC,CAAA;gBAClC,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,yFAAyF;YACzF,MAAM,sBAAsB,GAAG,mBAAQ,CAAC,IAAI,CACxC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CAC/C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtD,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,yBAAW,CAAC;oBACnB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC;wBAChC,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC;wBACxB,CAAC,CAAC,SAAS;iBAClB,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAC9C,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACpD,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,uBAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,OAAO,CACxC,2BAA2B,EAC3B,IAAI,CACP;iBACJ,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,+FAA+F;YAC/F,MAAM,yBAAyB,GAAG,mBAAQ,CAAC,IAAI,CAC3C,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC;oBAC3B,YAAY,CAAC,iBAAiB,CAAC,KAAK,SAAS,CAChD,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,UAAU,GAAG,yBAAyB,CAAC,GAAG,CAC5C,CAAC,UAAU,EAAE,EAAE;gBACX,OAAO,IAAI,+BAAc,CAAC;oBACtB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,wCAAwC;iBAC9F,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,mBAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClC,OAAO,CACH,YAAY,CAAC,YAAY,CAAC;oBACtB,OAAO,CAAC,YAAY,CAAC;oBACzB,YAAY,CAAC,cAAc,CAAC;wBACxB,OAAO,CAAC,cAAc,CAAC,CAC9B,CAAA;YACL,CAAC,CAAC,EACF,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,iBAAiB,CAAC;oBACvB,YAAY,CAAC,iBAAiB,CAAC,CACtC,CAAA;gBAED,2GAA2G;gBAC3G,MAAM,MAAM,GAAG,gBAAgB,CAC3B,YAAY,EACZ,yBAAyB,CAC5B,CAAA;gBACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClD,YAAY,CAAC,uBAAuB,CAAC,EACrC,MAAM,CACT,CAAA;gBAED,OAAO,IAAI,iCAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,iBAAiB,CAAC;oBACrC,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,gBAAgB,EACZ,YAAY,CAAC,yBAAyB,CAAC;oBAC3C,mBAAmB,EAAE,mBAAmB;oBACxC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAC3C;oBACD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;oBACnC,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC;oBACnC,UAAU,EAAE,YAAY,CAAC,YAAY,CAAC;wBAClC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC;wBAC1B,CAAC,CAAC,SAAS;iBAClB,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,mBAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,EAAE;gBACzB,OAAO,CACH,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;oBAC/C,OAAO,CAAC,cAAc,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,CACtD,CAAA;YACL,CAAC,CAAC,EACF,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAC1C,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,OAAO,CACH,KAAK,CAAC,cAAc,CAAC;wBACjB,UAAU,CAAC,cAAc,CAAC;wBAC9B,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;wBAChD,KAAK,CAAC,iBAAiB,CAAC;4BACpB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,IAAI,uBAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC,KAAK,MAAM;oBAC5C,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC;oBAC9B,SAAS,EAAE,UAAU,CAAC,YAAY,CAAC,KAAK,MAAM;oBAC9C,UAAU,EAAE,KAAK;iBACpB,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;aACzD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACpC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;YACD,IAAI,CAAC,aAAa;gBACd,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,yBAAW,CAAC;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC7B,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;oBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,MAAM,CAAC,WAAW,CACrB,CAAA;gBACP,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,UAAU,GAAG,eAAe,UAAU,aAAa,WAAW,GAAG,CAAA;gBACrE,IAAI,MAAM,CAAC,UAAU;oBACjB,UAAU,IAAI,eAAe,MAAM,CAAC,UAAU,EAAE,CAAA;gBACpD,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU;iBACjC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;gBACf,MAAM,aAAa,GAAG,SAAS,CAAC,IAAI;oBAChC,CAAC,CAAC,SAAS,CAAC,IAAI;oBAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,uBAAuB,CAClD,KAAK,EACL,SAAS,CAAC,UAAW,CACxB,CAAA;gBACP,OAAO,eAAe,aAAa,aAAa,SAAS,CAAC,UAAU,EAAE,CAAA;YAC1E,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,aAAa,EAAE,CAAA;QAC/B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBAEL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,eACb,EAAE,CAAC,IACP,kBAAkB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,UAAU;oBACb,UAAU,IAAI,eAAe,EAAE,CAAC,UAAU,EAAE,CAAA;gBAEhD,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC7D,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,cAAc,kBAAkB,WAAW,GAAG,CAAA;QAC1E,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;aAC1B,OAAO,CACJ,CAAC,EAAE,EAAE,EAAE,CACH,CAAC,GAAG,IAAI,uBAAuB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KACjD,EAAE,CAAC,IACP,QAAQ,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,CAChD,CAAA;QAEL,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,0FAA0F;QAC1F,kDAAkD;QAClD,EAAE;QACF,OAAO;QACP,kDAAkD;QAClD,4FAA4F;QAC5F,MAAM,MAAM,GAA0B,MAAM,IAAI,CAAC,KAAK,CAClD,kBAAkB,CACrB,CAAA;QAED,YAAY;QACZ,uHAAuH;QACvH,sMAAsM;QACtM,OAAO,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,yBAAyB,EAAE,IAAI,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACO,YAAY,CAAC,WAA2B;QAC9C,OAAO,IAAI,aAAK,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC,CAAA;IAClE,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAA;QAEtC,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,aAAK,CACZ,UAAU,kBAAkB,QAAQ,QAAQ,OAAO,IAAI,CAAC,UAAU,EAAE,CACvE,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,aAAK,CACZ,UAAU,kBAAkB,QAAQ,QAAQ,OAAO,IAAI;iBAClD,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAElE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,aAAa,CAAA;QAC1B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,qCAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,qCAAiB,CAAC,IAAI,CAAA;QAC5B,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI;YACJ,MAAM;YACN,IAAI;YACJ,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,IAAU;QAC5B,MAAM,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACnE,OAAO,IAAI,aAAK,CACZ,QAAQ,kBAAkB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAC5D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAEnD,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAElE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,aAAa,CAAA;QAC1B,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY;YAC1B,CAAC,CAAC,qCAAiB,CAAC,iBAAiB;YACrC,CAAC,CAAC,qCAAiB,CAAC,IAAI,CAAA;QAC5B,OAAO,IAAI,CAAC,wBAAwB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,aAAa,CAAC,WAAmB;QAC7C,MAAM,gBAAgB,GAClB,kHAAkH;YAClH,0DAA0D;YAC1D,kEAAkE;YAClE,2BAA2B,WAAW,yCAAyC,CAAA;QACnF,MAAM,WAAW,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QACvE,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAA;IACrE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,WAAW,CACvB,KAAY,EACZ,MAAmB;QAEnB,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAElD,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,GAAG,GACL,yDAAyD;YACzD,kEAAkE;YAClE,0BAA0B,MAAM,0BAA0B,QAAQ,GAAG,CAAA;QACzE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,MAAmB,EACnB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3D,MAAM,UAAU,GAAG,MAAM;aACpB,IAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;aACxD,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CAAC,eAAe,QAAQ,YAAY,UAAU,GAAG,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACO,eAAe,CACrB,KAAY,EACZ,MAAmB,EACnB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QAC3D,OAAO,IAAI,aAAK,CAAC,aAAa,QAAQ,EAAE,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,QACrC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EAC3C,KAAK,KAAK,CAAC,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IACzC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EACtC,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,kBAAkB,CAAC,IAAU,EAAE,KAAiB;QACtD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,aAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UACrC,KAAK,CAAC,IACV,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,OAAO,KACrC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAC3C,EAAE,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAmB,EACnB,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,MAAM,UAAU,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACxD,CAAC,CAAC,WAAW,CAAC,YAAY;YAC1B,CAAC,CAAC,KAAK,CAAA;QACX,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QACpD,OAAO,MAAM;YACT,CAAC,CAAC,IAAI,aAAK,CACL,cACI,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EACnC,IAAI,MAAM,MAAM,SAAS,GAAG,CAC/B;YACH,CAAC,CAAC,IAAI,aAAK,CACL,cACI,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,EACnC,IAAI,SAAS,GAAG,CACnB,CAAA;IACX,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,WAAqB,EACrB,cAAuB;QAEvB,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,kBAAkB,iBAAiB,GAAG,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM;YAC5B,MAAM,IAAI,oBAAY,CAAC,SAAS,KAAK,uBAAuB,CAAC,CAAA;QAEjE,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAA;QACvE,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,yBAAyB,CAC/B,KAAY,EACZ,gBAA6B;QAE7B,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW;aAC3C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBAC3C,gBAAgB,CAAC,IACrB,aAAa,WAAW,GAAG,CAAA;QAC3B,IAAI,gBAAgB,CAAC,UAAU;YAC3B,GAAG,IAAI,eAAe,gBAAgB,CAAC,UAAU,EAAE,CAAA;QACvD,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC7B,KAAY,EACZ,YAAkC;QAElC,MAAM,UAAU,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAClB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,UAAU,GAAG,CACtC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,eAAe,CAAC,IACpB,YAAY,eAAe,CAAC,UAAU,GAAG,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,iCAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,SAAS,GAAG,CACrC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,4BAA4B,CAClC,KAAY,EACZ,mBAAmC;QAEnC,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,mBAAmB,CAAC,IACxB,aAAa,mBAAmB,CAAC,UAAU,EAAE,CAChD,CAAA;IACL,CAAC;IAED;;OAEG;IACO,0BAA0B,CAChC,KAAY,EACZ,eAAwC;QAExC,MAAM,aAAa,GAAG,iCAAe,CAAC,gBAAgB,CAAC,eAAe,CAAC;YACnE,CAAC,CAAC,eAAe,CAAC,IAAI;YACtB,CAAC,CAAC,eAAe,CAAA;QACrB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,aAAa,GAAG,CACzC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,UAAU,CAAC,IACf,kBAAkB,WAAW,IAAI;YACjC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;QACjC,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QACnE,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QACnE,IAAI,UAAU,CAAC,UAAU;YAAE,GAAG,IAAI,eAAe,UAAU,CAAC,UAAU,EAAE,CAAA;QAExE,OAAO,IAAI,aAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAAG,iCAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,aAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,YAAkC;QAElC,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEvD,MAAM,UAAU,GAAG,iCAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAElB,IAAI,OAAO,GAAG,GAAG,SAAS,IAAI,UAAU,MAAM,CAAA;QAE9C,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAe,EAAE,CAAC;YAC1D,wFAAwF;YACxF,OAAO,GAAG,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,UAAU,CAAC,SAAS,CAC3D,CAAC,EACD,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAC3C,MAAM,CAAA;QACX,CAAC;QAED,OAAO,OAAO,CAAA;IAClB,CAAC;IAES,iBAAiB,CACvB,KAAY,EACZ,YAAkC;QAElC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEpD,OAAO,MAAM;YACT,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,EAAE;YAC5D,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACO,aAAa,CACnB,KAAY,EACZ,MAAmB,EACnB,aAAsB,IAAI,EAC1B,aAAuB,EACvB,KAAe;QAEf,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAC/D,IAAI,QAAQ,GAAG,MAAM,CAAC,QAAQ;YAC1B,CAAC,CAAC,MAAM,CAAC,QAAQ;YACjB,CAAC,CAAC,GAAG,SAAS,IAAI,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAA;QACtD,IAAI,MAAM,IAAI,UAAU;YAAE,QAAQ,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAA;QAC5D,IAAI,KAAK;YAAE,QAAQ,GAAG,QAAQ,GAAG,MAAM,CAAA;QACvC,OAAO,QAAQ;aACV,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,OAAO,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAA;QACvC,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;IAES,KAAK,CAAC,sBAAsB,CAAC,KAAY,EAAE,MAAmB;QACpE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;QAEnE,IAAI,CAAC,MAAM,EAAE,CAAC;YACV,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,kCAAkC;YAC9B,+DAA+D,MAAM,yBAAyB,IAAI,wBAAwB,MAAM,CAAC,IAAI,GAAG,CAC/I,CAAA;QAED,4DAA4D;QAC5D,sGAAsG;QACtG,2GAA2G;QAC3G,OAAO;QACP,mEAAmE;QACnE,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;QACnC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,CAAA;QAC/C,CAAC;QACD,OAAO;YACH,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;YAC/B,IAAI,EAAE,OAAO;SAChB,CAAA;IACL,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,OAAgB;QACpC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAA,CAAC,wCAAwC;QAErG,OAAO,IAAI,OAAO,GAAG,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEhE,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAChD,OAAO,IAAI,MAAM,MAAM,SAAS,GAAG,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,SAAS,GAAG,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,sBAAsB,CAAC,MAAsB;QACzD,MAAM,SAAS,GAAG,iCAAe,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACxE,IAAI,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAA;YAChE,MAAM,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAA;YAChD,OAAO,GAAG,MAAM,IAAI,SAAS,EAAE,CAAA;QACnC,CAAC;aAAM,CAAC;YACJ,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,KAAY,EAAE,MAAmB;QAC5D,IAAI,CAAC,GAAG,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,GAAG,CAAA;QAC/B,IACI,MAAM,CAAC,WAAW,KAAK,IAAI;YAC3B,MAAM,CAAC,kBAAkB,KAAK,MAAM,EACtC,CAAC;YACC,IAAI,MAAM,CAAC,kBAAkB,KAAK,UAAU,EAAE,CAAC;gBAC3C,yCAAyC;gBACzC,MAAM,0BAA0B,GAC5B,MAAM,CAAC,iBAAiB,IAAI,YAAY,CAAA;gBAC5C,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,cAAc,0BAA0B,cAAc,CAAA;YAC9E,CAAC;iBAAM,CAAC;gBACJ,gCAAgC;gBAChC,IACI,MAAM,CAAC,IAAI,KAAK,SAAS;oBACzB,MAAM,CAAC,IAAI,KAAK,KAAK;oBACrB,MAAM,CAAC,IAAI,KAAK,MAAM;oBAEtB,CAAC,IAAI,SAAS,CAAA;gBAClB,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM;oBACpD,CAAC,IAAI,cAAc,CAAA;gBACvB,IAAI,MAAM,CAAC,IAAI,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM;oBAClD,CAAC,IAAI,YAAY,CAAA;YACzB,CAAC;QACL,CAAC;QACD,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC1D,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;YAC5C,IAAI,MAAM,CAAC,OAAO;gBAAE,CAAC,IAAI,QAAQ,CAAA;QACrC,CAAC;aAAM,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACvD,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC5D,CAAC;QAED,0DAA0D;QAC1D,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC3D,CAAC,IAAI,yBAAyB,MAAM,CAAC,YAAY,UAAU,CAAA;QAC/D,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,CAAC,IAAI,kBAAkB,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;QAClE,IAAI,MAAM,CAAC,SAAS;YAAE,CAAC,IAAI,YAAY,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QAChE,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI;YAAE,CAAC,IAAI,WAAW,CAAA;QAChD,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI;YACvD,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,OAAO,CAAA;QACrC,IACI,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,MAAM;YACpC,CAAC,MAAM,CAAC,OAAO;YAEf,CAAC,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAA;QAEhD,OAAO,CAAC,CAAA;IACZ,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,8BAA8B;QAC1C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,8GAA8G,CACjH,CAAA;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CACpB,WAA2B,EAC3B,UAAmB;QAEnB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,KAAK,GAAG,iCAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC3C,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAEjD,IAAI,UAAU,KAAK,OAAO,EAAE,CAAC;YACzB,OAAM;QACV,CAAC;QAED,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAE9B,SAAS,CAAC,IAAI,CACV,IAAI,aAAK,CACL,oBAAoB,IAAI,CAAC,UAAU,CAC/B,QAAQ,CACX,OAAO,UAAU,EAAE,CACvB,CACJ,CAAA;QAED,WAAW,CAAC,IAAI,CACZ,IAAI,aAAK,CACL,oBAAoB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,OAAO,EAAE,CAC7D,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,KAAK,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;IAC5C,CAAC;CACJ;AA/qJD,kDA+qJC", "file": "PostgresQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { DriverUtils } from \"../DriverUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { PostgresDriver } from \"./PostgresDriver\"\n\n/**\n * Runs queries on a single postgres database connection.\n */\nexport class PostgresQueryRunner\n    extends BaseQueryRunner\n    implements QueryRunner\n{\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: PostgresDriver\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Promise used to obtain a database connection for a first time.\n     */\n    protected databaseConnectionPromise: Promise<any>\n\n    /**\n     * Special callback provided by a driver used to release a created connection.\n     */\n    protected releaseCallback?: (err: any) => void\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: PostgresDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.mode = mode\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<any> {\n        if (this.databaseConnection)\n            return Promise.resolve(this.databaseConnection)\n\n        if (this.databaseConnectionPromise)\n            return this.databaseConnectionPromise\n\n        if (this.mode === \"slave\" && this.driver.isReplicated) {\n            this.databaseConnectionPromise = this.driver\n                .obtainSlaveConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n\n                    const onErrorCallback = (err: Error) =>\n                        this.releasePostgresConnection(err)\n                    this.releaseCallback = (err?: Error) => {\n                        this.databaseConnection.removeListener(\n                            \"error\",\n                            onErrorCallback,\n                        )\n                        release(err)\n                    }\n                    this.databaseConnection.on(\"error\", onErrorCallback)\n\n                    return this.databaseConnection\n                })\n        } else {\n            // master\n            this.databaseConnectionPromise = this.driver\n                .obtainMasterConnection()\n                .then(([connection, release]: any[]) => {\n                    this.driver.connectedQueryRunners.push(this)\n                    this.databaseConnection = connection\n\n                    const onErrorCallback = (err: Error) =>\n                        this.releasePostgresConnection(err)\n                    this.releaseCallback = (err?: Error) => {\n                        this.databaseConnection.removeListener(\n                            \"error\",\n                            onErrorCallback,\n                        )\n                        release(err)\n                    }\n                    this.databaseConnection.on(\"error\", onErrorCallback)\n\n                    return this.databaseConnection\n                })\n        }\n\n        return this.databaseConnectionPromise\n    }\n\n    /**\n     * Release a connection back to the pool, optionally specifying an Error to release with.\n     * Per pg-pool documentation this will prevent the pool from re-using the broken connection.\n     */\n    private async releasePostgresConnection(err?: Error) {\n        if (this.isReleased) {\n            return\n        }\n\n        this.isReleased = true\n        if (this.releaseCallback) {\n            this.releaseCallback(err)\n            this.releaseCallback = undefined\n        }\n\n        const index = this.driver.connectedQueryRunners.indexOf(this)\n\n        if (index !== -1) {\n            this.driver.connectedQueryRunners.splice(index, 1)\n        }\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    release(): Promise<void> {\n        return this.releasePostgresConnection()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n\n        if (this.transactionDepth === 0) {\n            await this.query(\"START TRANSACTION\")\n            if (isolationLevel) {\n                await this.query(\n                    \"SET TRANSACTION ISOLATION LEVEL \" + isolationLevel,\n                )\n            }\n        } else {\n            await this.query(`SAVEPOINT typeorm_${this.transactionDepth}`)\n        }\n        this.transactionDepth += 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `RELEASE SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"COMMIT\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TO SAVEPOINT typeorm_${this.transactionDepth - 1}`,\n            )\n        } else {\n            await this.query(\"ROLLBACK\")\n            this.isTransactionActive = false\n        }\n        this.transactionDepth -= 1\n\n        await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult: boolean = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        try {\n            const queryStartTime = Date.now()\n            const raw = await databaseConnection.query(query, parameters)\n            // log slow queries if maxQueryExecution time is set\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                raw,\n                undefined,\n            )\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            )\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n\n            const result = new QueryResult()\n            if (raw) {\n                if (raw.hasOwnProperty(\"rows\")) {\n                    result.records = raw.rows\n                }\n\n                if (raw.hasOwnProperty(\"rowCount\")) {\n                    result.affected = raw.rowCount\n                }\n\n                switch (raw.command) {\n                    case \"DELETE\":\n                    case \"UPDATE\":\n                        // for UPDATE and DELETE query additionally return number of affected rows\n                        result.raw = [raw.rows, raw.rowCount]\n                        break\n                    default:\n                        result.raw = raw.rows\n                }\n\n                if (!useStructuredResult) {\n                    return result.raw\n                }\n            }\n\n            return result\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        const QueryStream = this.driver.loadStreamDependency()\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        const stream = databaseConnection.query(\n            new QueryStream(query, parameters),\n        )\n        if (onEnd) stream.on(\"end\", onEnd)\n        if (onError) stream.on(\"error\", onError)\n\n        return stream\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        return Promise.resolve([])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM pg_database WHERE datname='${database}';`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<string> {\n        const query = await this.query(`SELECT * FROM current_database()`)\n        return query[0][\"current_database\"]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT * FROM \"information_schema\".\"schemata\" WHERE \"schema_name\" = '${schema}'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        const query = await this.query(`SELECT * FROM current_schema()`)\n        return query[0][\"current_schema\"]\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"information_schema\".\"tables\" WHERE \"table_schema\" = '${parsedTableName.schema}' AND \"table_name\" = '${parsedTableName.tableName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column with the given name exist in the given table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"information_schema\".\"columns\" WHERE \"table_schema\" = '${parsedTableName.schema}' AND \"table_name\" = '${parsedTableName.tableName}' AND \"column_name\" = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     * Note: Postgres does not support database creation inside a transaction block.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const databaseAlreadyExists = await this.hasDatabase(database)\n\n            if (databaseAlreadyExists) return Promise.resolve()\n        }\n\n        const up = `CREATE DATABASE \"${database}\"`\n        const down = `DROP DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops database.\n     * Note: Postgres does not support database dropping inside a transaction block.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        const up = ifExist\n            ? `DROP DATABASE IF EXISTS \"${database}\"`\n            : `DROP DATABASE \"${database}\"`\n        const down = `CREATE DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table schema.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[1]\n\n        const up = ifNotExist\n            ? `CREATE SCHEMA IF NOT EXISTS \"${schema}\"`\n            : `CREATE SCHEMA \"${schema}\"`\n        const down = `DROP SCHEMA \"${schema}\" CASCADE`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops table schema.\n     */\n    async dropSchema(\n        schemaPath: string,\n        ifExist?: boolean,\n        isCascade?: boolean,\n    ): Promise<void> {\n        const schema =\n            schemaPath.indexOf(\".\") === -1\n                ? schemaPath\n                : schemaPath.split(\".\")[1]\n\n        const up = ifExist\n            ? `DROP SCHEMA IF EXISTS \"${schema}\" ${isCascade ? \"CASCADE\" : \"\"}`\n            : `DROP SCHEMA \"${schema}\" ${isCascade ? \"CASCADE\" : \"\"}`\n        const down = `CREATE SCHEMA \"${schema}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table have column with ENUM type, we must create this type in postgres.\n        const enumColumns = table.columns.filter(\n            (column) => column.type === \"enum\" || column.type === \"simple-enum\",\n        )\n        const createdEnumTypes: string[] = []\n        for (const column of enumColumns) {\n            // TODO: Should also check if values of existing type matches expected ones\n            const hasEnum = await this.hasEnumType(table, column)\n            const enumName = this.buildEnumName(table, column)\n\n            // if enum with the same \"enumName\" is defined more then once, me must prevent double creation\n            if (!hasEnum && createdEnumTypes.indexOf(enumName) === -1) {\n                createdEnumTypes.push(enumName)\n                upQueries.push(this.createEnumTypeSql(table, column, enumName))\n                downQueries.push(this.dropEnumTypeSql(table, column, enumName))\n            }\n        }\n\n        // if table have column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) =>\n                column.generatedType === \"STORED\" && column.asExpression,\n        )\n        for (const column of generatedColumns) {\n            const tableNameWithSchema = (\n                await this.getTableNameWithSchema(table.name)\n            ).split(\".\")\n            const tableName = tableNameWithSchema[1]\n            const schema = tableNameWithSchema[0]\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(table, index))\n            })\n        }\n\n        if (table.comment) {\n            upQueries.push(\n                new Query(\n                    \"COMMENT ON TABLE \" +\n                        this.escapePath(table) +\n                        \" IS '\" +\n                        table.comment +\n                        \"'\",\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    \"COMMENT ON TABLE \" + this.escapePath(table) + \" IS NULL\",\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        target: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n        if (ifExist) {\n            const isTableExist = await this.hasTable(target)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const tablePath = this.getTablePath(target)\n        const table = await this.getCachedTable(tablePath)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(table, index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n        for (const column of generatedColumns) {\n            const tableNameWithSchema = (\n                await this.getTableNameWithSchema(table.name)\n            ).split(\".\")\n            const tableName = tableNameWithSchema[1]\n            const schema = tableNameWithSchema[0]\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata)\n            upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames the given table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        const { schema: schemaName, tableName: oldTableName } =\n            this.driver.parseTableName(oldTable)\n\n        newTable.name = schemaName\n            ? `${schemaName}.${newTableName}`\n            : newTableName\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    oldTable,\n                )} RENAME TO \"${newTableName}\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    newTable,\n                )} RENAME TO \"${oldTableName}\"`,\n            ),\n        )\n\n        // rename column primary key constraint if it has default constraint name\n        if (\n            newTable.primaryColumns.length > 0 &&\n            !newTable.primaryColumns[0].primaryKeyConstraintName\n        ) {\n            const columnNames = newTable.primaryColumns.map(\n                (column) => column.name,\n            )\n\n            const oldPkName = this.connection.namingStrategy.primaryKeyName(\n                oldTable,\n                columnNames,\n            )\n\n            const newPkName = this.connection.namingStrategy.primaryKeyName(\n                newTable,\n                columnNames,\n            )\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                ),\n            )\n        }\n\n        // rename sequences\n        newTable.columns.map((col) => {\n            if (col.isGenerated && col.generationStrategy === \"increment\") {\n                const sequencePath = this.buildSequencePath(oldTable, col.name)\n                const sequenceName = this.buildSequenceName(oldTable, col.name)\n\n                const newSequencePath = this.buildSequencePath(\n                    newTable,\n                    col.name,\n                )\n                const newSequenceName = this.buildSequenceName(\n                    newTable,\n                    col.name,\n                )\n\n                const up = `ALTER SEQUENCE ${this.escapePath(\n                    sequencePath,\n                )} RENAME TO \"${newSequenceName}\"`\n                const down = `ALTER SEQUENCE ${this.escapePath(\n                    newSequencePath,\n                )} RENAME TO \"${sequenceName}\"`\n\n                upQueries.push(new Query(up))\n                downQueries.push(new Query(down))\n            }\n        })\n\n        // rename unique constraints\n        newTable.uniques.forEach((unique) => {\n            const oldUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    oldTable,\n                    unique.columnNames,\n                )\n\n            // Skip renaming if Unique has user defined constraint name\n            if (unique.name !== oldUniqueName) return\n\n            // build new constraint name\n            const newUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    newTable,\n                    unique.columnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        unique.name\n                    }\" TO \"${newUniqueName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                        unique.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            unique.name = newUniqueName\n        })\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            // build new constraint name\n            const { schema } = this.driver.parseTableName(newTable)\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // build queries\n            const up = schema\n                ? `ALTER INDEX \"${schema}\".\"${index.name}\" RENAME TO \"${newIndexName}\"`\n                : `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`\n            const down = schema\n                ? `ALTER INDEX \"${schema}\".\"${newIndexName}\" RENAME TO \"${index.name}\"`\n                : `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`\n            upQueries.push(new Query(up))\n            downQueries.push(new Query(down))\n\n            // replace constraint name\n            index.name = newIndexName\n        })\n\n        // rename foreign key constraints\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            // build new constraint name\n            const newForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    newTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${\n                        foreignKey.name\n                    }\" TO \"${newForeignKeyName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        newTable,\n                    )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                        foreignKey.name\n                    }\"`,\n                ),\n            )\n\n            // replace constraint name\n            foreignKey.name = newForeignKeyName\n        })\n\n        // rename ENUM types\n        const enumColumns = newTable.columns.filter(\n            (column) => column.type === \"enum\" || column.type === \"simple-enum\",\n        )\n        for (const column of enumColumns) {\n            // skip renaming for user-defined enum name\n            if (column.enumName) continue\n\n            const oldEnumType = await this.getUserDefinedTypeName(\n                oldTable,\n                column,\n            )\n            upQueries.push(\n                new Query(\n                    `ALTER TYPE \"${oldEnumType.schema}\".\"${\n                        oldEnumType.name\n                    }\" RENAME TO ${this.buildEnumName(\n                        newTable,\n                        column,\n                        false,\n                    )}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TYPE ${this.buildEnumName(\n                        newTable,\n                        column,\n                    )} RENAME TO \"${oldEnumType.name}\"`,\n                ),\n            )\n        }\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            const hasEnum = await this.hasEnumType(table, column)\n            if (!hasEnum) {\n                upQueries.push(this.createEnumTypeSql(table, column))\n                downQueries.push(this.dropEnumTypeSql(table, column))\n            }\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(table, column)}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n\n        // create or update primary key constraint\n        if (column.isPrimary) {\n            const primaryColumns = clonedTable.primaryColumns\n            // if table already have primary key, me must drop it and recreate again\n            if (primaryColumns.length > 0) {\n                const pkName = primaryColumns[0].primaryKeyConstraintName\n                    ? primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          primaryColumns.map((column) => column.name),\n                      )\n\n                const columnNames = primaryColumns\n                    .map((column) => `\"${column.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n            }\n\n            primaryColumns.push(column)\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(table, columnIndex))\n        }\n\n        // create unique constraint\n        if (column.isUnique) {\n            const uniqueConstraint = new TableUnique({\n                name: this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    [column.name],\n                ),\n                columnNames: [column.name],\n            })\n            clonedTable.uniques.push(uniqueConstraint)\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\" UNIQUE (\"${column.name}\")`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\"`,\n                ),\n            )\n        }\n\n        if (column.generatedType === \"STORED\" && column.asExpression) {\n            const tableNameWithSchema = (\n                await this.getTableNameWithSchema(table.name)\n            ).split(\".\")\n            const tableName = tableNameWithSchema[1]\n            const schema = tableNameWithSchema[0]\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        // create column's comment\n        if (column.comment) {\n            upQueries.push(\n                new Query(\n                    `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        column.name\n                    }\" IS ${this.escapeComment(column.comment)}`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        column.name\n                    }\" IS ${this.escapeComment(column.comment)}`,\n                ),\n            )\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        return this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        let defaultValueChanged = false\n\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find(\n                  (column) => column.name === oldTableColumnOrName,\n              )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            oldColumn.type !== newColumn.type ||\n            oldColumn.length !== newColumn.length ||\n            newColumn.isArray !== oldColumn.isArray ||\n            (!oldColumn.generatedType &&\n                newColumn.generatedType === \"STORED\") ||\n            (oldColumn.asExpression !== newColumn.asExpression &&\n                newColumn.generatedType === \"STORED\")\n        ) {\n            // To avoid data conversion, we just recreate column\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (oldColumn.name !== newColumn.name) {\n                // rename column\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            oldColumn.name\n                        }\" TO \"${newColumn.name}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} RENAME COLUMN \"${\n                            newColumn.name\n                        }\" TO \"${oldColumn.name}\"`,\n                    ),\n                )\n\n                // rename ENUM type\n                if (\n                    oldColumn.type === \"enum\" ||\n                    oldColumn.type === \"simple-enum\"\n                ) {\n                    const oldEnumType = await this.getUserDefinedTypeName(\n                        table,\n                        oldColumn,\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TYPE \"${oldEnumType.schema}\".\"${\n                                oldEnumType.name\n                            }\" RENAME TO ${this.buildEnumName(\n                                table,\n                                newColumn,\n                                false,\n                            )}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TYPE ${this.buildEnumName(\n                                table,\n                                newColumn,\n                            )} RENAME TO \"${oldEnumType.name}\"`,\n                        ),\n                    )\n                }\n\n                // rename column primary key constraint\n                if (\n                    oldColumn.isPrimary === true &&\n                    !oldColumn.primaryKeyConstraintName\n                ) {\n                    const primaryColumns = clonedTable.primaryColumns\n\n                    // build old primary constraint name\n                    const columnNames = primaryColumns.map(\n                        (column) => column.name,\n                    )\n                    const oldPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // replace old column name with new column name\n                    columnNames.splice(columnNames.indexOf(oldColumn.name), 1)\n                    columnNames.push(newColumn.name)\n\n                    // build new primary constraint name\n                    const newPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${oldPkName}\" TO \"${newPkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newPkName}\" TO \"${oldPkName}\"`,\n                        ),\n                    )\n                }\n\n                // rename column sequence\n                if (\n                    oldColumn.isGenerated === true &&\n                    newColumn.generationStrategy === \"increment\"\n                ) {\n                    const sequencePath = this.buildSequencePath(\n                        table,\n                        oldColumn.name,\n                    )\n                    const sequenceName = this.buildSequenceName(\n                        table,\n                        oldColumn.name,\n                    )\n\n                    const newSequencePath = this.buildSequencePath(\n                        table,\n                        newColumn.name,\n                    )\n                    const newSequenceName = this.buildSequenceName(\n                        table,\n                        newColumn.name,\n                    )\n\n                    const up = `ALTER SEQUENCE ${this.escapePath(\n                        sequencePath,\n                    )} RENAME TO \"${newSequenceName}\"`\n                    const down = `ALTER SEQUENCE ${this.escapePath(\n                        newSequencePath,\n                    )} RENAME TO \"${sequenceName}\"`\n\n                    upQueries.push(new Query(up))\n                    downQueries.push(new Query(down))\n                }\n\n                // rename unique constraints\n                clonedTable.findColumnUniques(oldColumn).forEach((unique) => {\n                    const oldUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // Skip renaming if Unique has user defined constraint name\n                    if (unique.name !== oldUniqueName) return\n\n                    // build new constraint name\n                    unique.columnNames.splice(\n                        unique.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    unique.columnNames.push(newColumn.name)\n                    const newUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${\n                                unique.name\n                            }\" TO \"${newUniqueName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME CONSTRAINT \"${newUniqueName}\" TO \"${\n                                unique.name\n                            }\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    unique.name = newUniqueName\n                })\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    const oldIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // Skip renaming if Index has user defined constraint name\n                    if (index.name !== oldIndexName) return\n\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const { schema } = this.driver.parseTableName(table)\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // build queries\n                    const up = schema\n                        ? `ALTER INDEX \"${schema}\".\"${index.name}\" RENAME TO \"${newIndexName}\"`\n                        : `ALTER INDEX \"${index.name}\" RENAME TO \"${newIndexName}\"`\n                    const down = schema\n                        ? `ALTER INDEX \"${schema}\".\"${newIndexName}\" RENAME TO \"${index.name}\"`\n                        : `ALTER INDEX \"${newIndexName}\" RENAME TO \"${index.name}\"`\n\n                    upQueries.push(new Query(up))\n                    downQueries.push(new Query(down))\n\n                    // replace constraint name\n                    index.name = newIndexName\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // Skip renaming if foreign key has user defined constraint name\n                        if (foreignKey.name !== foreignKeyName) return\n\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // build queries\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${\n                                    foreignKey.name\n                                }\" TO \"${newForeignKeyName}\"`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} RENAME CONSTRAINT \"${newForeignKeyName}\" TO \"${\n                                    foreignKey.name\n                                }\"`,\n                            ),\n                        )\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n                    })\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (\n                newColumn.precision !== oldColumn.precision ||\n                newColumn.scale !== oldColumn.scale\n            ) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(newColumn)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(oldColumn)}`,\n                    ),\n                )\n            }\n\n            if (\n                (newColumn.type === \"enum\" ||\n                    newColumn.type === \"simple-enum\") &&\n                (oldColumn.type === \"enum\" ||\n                    oldColumn.type === \"simple-enum\") &&\n                (!OrmUtils.isArraysEqual(newColumn.enum!, oldColumn.enum!) ||\n                    newColumn.enumName !== oldColumn.enumName)\n            ) {\n                const arraySuffix = newColumn.isArray ? \"[]\" : \"\"\n\n                // \"public\".\"new_enum\"\n                const newEnumName = this.buildEnumName(table, newColumn)\n\n                // \"public\".\"old_enum\"\n                const oldEnumName = this.buildEnumName(table, oldColumn)\n\n                // \"old_enum\"\n                const oldEnumNameWithoutSchema = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    false,\n                )\n\n                //\"public\".\"old_enum_old\"\n                const oldEnumNameWithSchema_old = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    true,\n                    false,\n                    true,\n                )\n\n                //\"old_enum_old\"\n                const oldEnumNameWithoutSchema_old = this.buildEnumName(\n                    table,\n                    oldColumn,\n                    false,\n                    false,\n                    true,\n                )\n\n                // rename old ENUM\n                upQueries.push(\n                    new Query(\n                        `ALTER TYPE ${oldEnumName} RENAME TO ${oldEnumNameWithoutSchema_old}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TYPE ${oldEnumNameWithSchema_old} RENAME TO ${oldEnumNameWithoutSchema}`,\n                    ),\n                )\n\n                // create new ENUM\n                upQueries.push(\n                    this.createEnumTypeSql(table, newColumn, newEnumName),\n                )\n                downQueries.push(\n                    this.dropEnumTypeSql(table, newColumn, newEnumName),\n                )\n\n                // if column have default value, we must drop it to avoid issues with type casting\n                if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    // mark default as changed to prevent double update\n                    defaultValueChanged = true\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET DEFAULT ${\n                                oldColumn.default\n                            }`,\n                        ),\n                    )\n                }\n\n                // build column types\n                const upType = `${newEnumName}${arraySuffix} USING \"${newColumn.name}\"::\"text\"::${newEnumName}${arraySuffix}`\n                const downType = `${oldEnumNameWithSchema_old}${arraySuffix} USING \"${newColumn.name}\"::\"text\"::${oldEnumNameWithSchema_old}${arraySuffix}`\n\n                // update column to use new type\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${upType}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${downType}`,\n                    ),\n                )\n\n                // restore column default or create new one\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                newColumn.default\n                            }`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                }\n\n                // remove old ENUM\n                upQueries.push(\n                    this.dropEnumTypeSql(\n                        table,\n                        oldColumn,\n                        oldEnumNameWithSchema_old,\n                    ),\n                )\n                downQueries.push(\n                    this.createEnumTypeSql(\n                        table,\n                        oldColumn,\n                        oldEnumNameWithSchema_old,\n                    ),\n                )\n            }\n\n            if (oldColumn.isNullable !== newColumn.isNullable) {\n                if (newColumn.isNullable) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                } else {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" SET NOT NULL`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${oldColumn.name}\" DROP NOT NULL`,\n                        ),\n                    )\n                }\n            }\n\n            if (oldColumn.comment !== newColumn.comment) {\n                upQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            oldColumn.name\n                        }\" IS ${this.escapeComment(newColumn.comment)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                            newColumn.name\n                        }\" IS ${this.escapeComment(oldColumn.comment)}`,\n                    ),\n                )\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const pkName = primaryColumns[0]\n                            .primaryKeyConstraintName\n                            ? primaryColumns[0].primaryKeyConstraintName\n                            : this.connection.namingStrategy.primaryKeyName(\n                                  clonedTable,\n                                  primaryColumns.map((column) => column.name),\n                              )\n\n                        const columnNames = primaryColumns\n                            .map((column) => `\"${column.name}\"`)\n                            .join(\", \")\n\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP CONSTRAINT \"${pkName}\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueConstraint = new TableUnique({\n                        name: this.connection.namingStrategy.uniqueConstraintName(\n                            table,\n                            [newColumn.name],\n                        ),\n                        columnNames: [newColumn.name],\n                    })\n                    clonedTable.uniques.push(uniqueConstraint)\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint.name}\"`,\n                        ),\n                    )\n                } else {\n                    const uniqueConstraint = clonedTable.uniques.find(\n                        (unique) => {\n                            return (\n                                unique.columnNames.length === 1 &&\n                                !!unique.columnNames.find(\n                                    (columnName) =>\n                                        columnName === newColumn.name,\n                                )\n                            )\n                        },\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(uniqueConstraint!),\n                        1,\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint!.name}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint!.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                }\n            }\n\n            if (oldColumn.isGenerated !== newColumn.isGenerated) {\n                // if old column was \"generated\", we should clear defaults\n                if (oldColumn.isGenerated) {\n                    if (oldColumn.generationStrategy === \"uuid\") {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    oldColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    oldColumn.name\n                                }\" SET DEFAULT ${this.driver.uuidGenerator}`,\n                            ),\n                        )\n                    } else if (oldColumn.generationStrategy === \"increment\") {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT nextval('${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}')`,\n                            ),\n                        )\n\n                        upQueries.push(\n                            new Query(\n                                `DROP SEQUENCE ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `CREATE SEQUENCE IF NOT EXISTS ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )} OWNED BY ${this.escapePath(table)}.\"${\n                                    newColumn.name\n                                }\"`,\n                            ),\n                        )\n                    }\n                }\n\n                if (newColumn.generationStrategy === \"uuid\") {\n                    if (newColumn.isGenerated === true) {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT ${this.driver.uuidGenerator}`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                    } else {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT ${this.driver.uuidGenerator}`,\n                            ),\n                        )\n                    }\n                } else if (newColumn.generationStrategy === \"increment\") {\n                    if (newColumn.isGenerated === true) {\n                        upQueries.push(\n                            new Query(\n                                `CREATE SEQUENCE IF NOT EXISTS ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )} OWNED BY ${this.escapePath(table)}.\"${\n                                    newColumn.name\n                                }\"`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `DROP SEQUENCE ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}`,\n                            ),\n                        )\n\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT nextval('${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}')`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                    } else {\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT nextval('${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}')`,\n                            ),\n                        )\n\n                        upQueries.push(\n                            new Query(\n                                `DROP SEQUENCE ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )}`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `CREATE SEQUENCE IF NOT EXISTS ${this.escapePath(\n                                    this.buildSequencePath(table, newColumn),\n                                )} OWNED BY ${this.escapePath(table)}.\"${\n                                    newColumn.name\n                                }\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            // the default might have changed when the enum changed\n            if (\n                newColumn.default !== oldColumn.default &&\n                !defaultValueChanged\n            ) {\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                newColumn.default\n                            }`,\n                        ),\n                    )\n\n                    if (\n                        oldColumn.default !== null &&\n                        oldColumn.default !== undefined\n                    ) {\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" SET DEFAULT ${oldColumn.default}`,\n                            ),\n                        )\n                    } else {\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ALTER COLUMN \"${\n                                    newColumn.name\n                                }\" DROP DEFAULT`,\n                            ),\n                        )\n                    }\n                } else if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" DROP DEFAULT`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ALTER COLUMN \"${newColumn.name}\" SET DEFAULT ${\n                                oldColumn.default\n                            }`,\n                        ),\n                    )\n                }\n            }\n\n            if (\n                (newColumn.spatialFeatureType || \"\").toLowerCase() !==\n                    (oldColumn.spatialFeatureType || \"\").toLowerCase() ||\n                newColumn.srid !== oldColumn.srid\n            ) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(newColumn)}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${this.driver.createFullType(oldColumn)}`,\n                    ),\n                )\n            }\n\n            // update column collation\n            if (newColumn.collation !== oldColumn.collation) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${newColumn.type} COLLATE \"${\n                            newColumn.collation\n                        }\"`,\n                    ),\n                )\n\n                const oldCollation = oldColumn.collation\n                    ? `\"${oldColumn.collation}\"`\n                    : `pg_catalog.\"default\"` // if there's no old collation, use default\n\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(table)} ALTER COLUMN \"${\n                            newColumn.name\n                        }\" TYPE ${newColumn.type} COLLATE ${oldCollation}`,\n                    ),\n                )\n            }\n\n            if (newColumn.generatedType !== oldColumn.generatedType) {\n                // Convert generated column data to normal column\n                if (\n                    !newColumn.generatedType ||\n                    newColumn.generatedType === \"VIRTUAL\"\n                ) {\n                    // We can copy the generated data to the new column\n                    const tableNameWithSchema = (\n                        await this.getTableNameWithSchema(table.name)\n                    ).split(\".\")\n                    const tableName = tableNameWithSchema[1]\n                    const schema = tableNameWithSchema[0]\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} RENAME COLUMN \"${oldColumn.name}\" TO \"TEMP_OLD_${\n                                oldColumn.name\n                            }\"`,\n                        ),\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD ${this.buildCreateColumnSql(\n                                table,\n                                newColumn,\n                            )}`,\n                        ),\n                    )\n                    upQueries.push(\n                        new Query(\n                            `UPDATE ${this.escapePath(table)} SET \"${\n                                newColumn.name\n                            }\" = \"TEMP_OLD_${oldColumn.name}\"`,\n                        ),\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP COLUMN \"TEMP_OLD_${oldColumn.name}\"`,\n                        ),\n                    )\n                    upQueries.push(\n                        this.deleteTypeormMetadataSql({\n                            database: this.driver.database,\n                            schema,\n                            table: tableName,\n                            type: MetadataTableType.GENERATED_COLUMN,\n                            name: oldColumn.name,\n                        }),\n                    )\n                    // However, we can't copy it back on downgrade. It needs to regenerate.\n                    downQueries.push(\n                        this.insertTypeormMetadataSql({\n                            database: this.driver.database,\n                            schema,\n                            table: tableName,\n                            type: MetadataTableType.GENERATED_COLUMN,\n                            name: oldColumn.name,\n                            value: oldColumn.asExpression,\n                        }),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD ${this.buildCreateColumnSql(\n                                table,\n                                oldColumn,\n                            )}`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP COLUMN \"${newColumn.name}\"`,\n                        ),\n                    )\n                    // downQueries.push(\n                    //     this.deleteTypeormMetadataSql({\n                    //         database: this.driver.database,\n                    //         schema,\n                    //         table: tableName,\n                    //         type: MetadataTableType.GENERATED_COLUMN,\n                    //         name: newColumn.name,\n                    //     }),\n                    // )\n                }\n            }\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        if (column.isPrimary) {\n            const pkName = column.primaryKeyConstraintName\n                ? column.primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      clonedTable.primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const pkName = clonedTable.primaryColumns[0]\n                    .primaryKeyConstraintName\n                    ? clonedTable.primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          clonedTable.primaryColumns.map(\n                              (column) => column.name,\n                          ),\n                      )\n\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n            }\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        // drop column unique\n        const columnUnique = clonedTable.uniques.find(\n            (unique) =>\n                unique.columnNames.length === 1 &&\n                unique.columnNames[0] === column.name,\n        )\n        if (columnUnique) {\n            clonedTable.uniques.splice(\n                clonedTable.uniques.indexOf(columnUnique),\n                1,\n            )\n            upQueries.push(this.dropUniqueConstraintSql(table, columnUnique))\n            downQueries.push(\n                this.createUniqueConstraintSql(table, columnUnique),\n            )\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(table, column)}`,\n            ),\n        )\n\n        // drop enum type\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            const hasEnum = await this.hasEnumType(table, column)\n            if (hasEnum) {\n                const enumType = await this.getUserDefinedTypeName(\n                    table,\n                    column,\n                )\n                const escapedEnumName = `\"${enumType.schema}\".\"${enumType.name}\"`\n                upQueries.push(\n                    this.dropEnumTypeSql(table, column, escapedEnumName),\n                )\n                downQueries.push(\n                    this.createEnumTypeSql(table, column, escapedEnumName),\n                )\n            }\n        }\n\n        if (column.generatedType === \"STORED\") {\n            const tableNameWithSchema = (\n                await this.getTableNameWithSchema(table.name)\n            ).split(\".\")\n            const tableName = tableNameWithSchema[1]\n            const schema = tableNameWithSchema[0]\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: this.driver.database,\n                schema,\n                table: tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames, constraintName)\n\n        // mark columns as primary, because dropPrimaryKeySql build constraint name from table primary column names.\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        const down = this.dropPrimaryKeySql(clonedTable)\n\n        await this.executeQueries(up, down)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const columnNames = columns.map((column) => column.name)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNamesString = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const pkName = primaryColumns[0]?.primaryKeyConstraintName\n            ? primaryColumns[0].primaryKeyConstraintName\n            : this.connection.namingStrategy.primaryKeyName(\n                  clonedTable,\n                  columnNames,\n              )\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP CONSTRAINT \"${pkName}\"`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(\n        tableOrName: Table | string,\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const up = this.dropPrimaryKeySql(table)\n        const down = this.createPrimaryKeySql(\n            table,\n            table.primaryColumns.map((column) => column.name),\n            constraintName,\n        )\n        await this.executeQueries(up, down)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!uniqueConstraint.name)\n            uniqueConstraint.name =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    uniqueConstraint.columnNames,\n                )\n\n        const up = this.createUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.addUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Creates new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        for (const uniqueConstraint of uniqueConstraints) {\n            await this.createUniqueConstraint(tableOrName, uniqueConstraint)\n        }\n    }\n\n    /**\n     * Drops unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const uniqueConstraint = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName\n            : table.uniques.find((u) => u.name === uniqueOrName)\n        if (!uniqueConstraint)\n            throw new TypeORMError(\n                `Supplied unique constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.createUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.removeUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Drops unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        for (const uniqueConstraint of uniqueConstraints) {\n            await this.dropUniqueConstraint(tableOrName, uniqueConstraint)\n        }\n    }\n\n    /**\n     * Creates new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!exclusionConstraint.name)\n            exclusionConstraint.name =\n                this.connection.namingStrategy.exclusionConstraintName(\n                    table,\n                    exclusionConstraint.expression!,\n                )\n\n        const up = this.createExclusionConstraintSql(table, exclusionConstraint)\n        const down = this.dropExclusionConstraintSql(table, exclusionConstraint)\n        await this.executeQueries(up, down)\n        table.addExclusionConstraint(exclusionConstraint)\n    }\n\n    /**\n     * Creates new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        const promises = exclusionConstraints.map((exclusionConstraint) =>\n            this.createExclusionConstraint(tableOrName, exclusionConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const exclusionConstraint = InstanceChecker.isTableExclusion(\n            exclusionOrName,\n        )\n            ? exclusionOrName\n            : table.exclusions.find((c) => c.name === exclusionOrName)\n        if (!exclusionConstraint)\n            throw new TypeORMError(\n                `Supplied exclusion constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropExclusionConstraintSql(table, exclusionConstraint)\n        const down = this.createExclusionConstraintSql(\n            table,\n            exclusionConstraint,\n        )\n        await this.executeQueries(up, down)\n        table.removeExclusionConstraint(exclusionConstraint)\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        const promises = exclusionConstraints.map((exclusionConstraint) =>\n            this.dropExclusionConstraint(tableOrName, exclusionConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.createForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        if (!foreignKey.name) {\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n        }\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        for (const foreignKey of foreignKeys) {\n            await this.dropForeignKey(tableOrName, foreignKey)\n        }\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Create a new view index.\n     */\n    async createViewIndex(\n        viewOrName: View | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const view = InstanceChecker.isView(viewOrName)\n            ? viewOrName\n            : await this.getCachedView(viewOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(view, index)\n\n        const up = this.createViewIndexSql(view, index)\n        const down = this.dropIndexSql(view, index)\n        await this.executeQueries(up, down)\n        view.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.createIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Creates new view indices\n     */\n    async createViewIndices(\n        viewOrName: View | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.createViewIndex(viewOrName, index)\n        }\n    }\n\n    /**\n     * Drops an index from the table.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in table ${table.name}`,\n            )\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an index from a view.\n     */\n    async dropViewIndex(\n        viewOrName: View | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const view = InstanceChecker.isView(viewOrName)\n            ? viewOrName\n            : await this.getCachedView(viewOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : view.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index ${indexOrName} was not found in view ${view.name}`,\n            )\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(view, index)\n\n        const up = this.dropIndexSql(view, index)\n        const down = this.createViewIndexSql(view, index)\n        await this.executeQueries(up, down)\n        view.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        for (const index of indices) {\n            await this.dropIndex(tableOrName, index)\n        }\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tableName: string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tableName)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(): Promise<void> {\n        const schemas: string[] = []\n        this.connection.entityMetadatas\n            .filter((metadata) => metadata.schema)\n            .forEach((metadata) => {\n                const isSchemaExist = !!schemas.find(\n                    (schema) => schema === metadata.schema,\n                )\n                if (!isSchemaExist) schemas.push(metadata.schema!)\n            })\n        schemas.push(this.driver.options.schema || \"current_schema()\")\n        const schemaNamesString = schemas\n            .map((name) => {\n                return name === \"current_schema()\" ? name : \"'\" + name + \"'\"\n            })\n            .join(\", \")\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            // drop views\n            const selectViewDropsQuery =\n                `SELECT 'DROP VIEW IF EXISTS \"' || schemaname || '\".\"' || viewname || '\" CASCADE;' as \"query\" ` +\n                `FROM \"pg_views\" WHERE \"schemaname\" IN (${schemaNamesString}) AND \"viewname\" NOT IN ('geography_columns', 'geometry_columns', 'raster_columns', 'raster_overviews')`\n            const dropViewQueries: ObjectLiteral[] = await this.query(\n                selectViewDropsQuery,\n            )\n            await Promise.all(\n                dropViewQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            // drop materialized views\n            // Note: materialized views introduced in Postgres 9.3\n            if (DriverUtils.isReleaseVersionOrGreater(this.driver, \"9.3\")) {\n                const selectMatViewDropsQuery =\n                    `SELECT 'DROP MATERIALIZED VIEW IF EXISTS \"' || schemaname || '\".\"' || matviewname || '\" CASCADE;' as \"query\" ` +\n                    `FROM \"pg_matviews\" WHERE \"schemaname\" IN (${schemaNamesString})`\n                const dropMatViewQueries: ObjectLiteral[] = await this.query(\n                    selectMatViewDropsQuery,\n                )\n                await Promise.all(\n                    dropMatViewQueries.map((q) => this.query(q[\"query\"])),\n                )\n            }\n\n            // ignore spatial_ref_sys; it's a special table supporting PostGIS\n            // TODO generalize this as this.driver.ignoreTables\n\n            // drop tables\n            const selectTableDropsQuery = `SELECT 'DROP TABLE IF EXISTS \"' || schemaname || '\".\"' || tablename || '\" CASCADE;' as \"query\" FROM \"pg_tables\" WHERE \"schemaname\" IN (${schemaNamesString}) AND \"tablename\" NOT IN ('spatial_ref_sys')`\n            const dropTableQueries: ObjectLiteral[] = await this.query(\n                selectTableDropsQuery,\n            )\n            await Promise.all(\n                dropTableQueries.map((q) => this.query(q[\"query\"])),\n            )\n\n            // drop enum types\n            await this.dropEnumTypes(schemaNamesString)\n\n            if (!isAnotherTransactionActive) {\n                await this.commitTransaction()\n            }\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive) {\n                    await this.rollbackTransaction()\n                }\n            } catch {\n                // no-op\n            }\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewNames?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n\n        if (!hasTable) return []\n\n        if (!viewNames) {\n            viewNames = []\n        }\n\n        const currentDatabase = await this.getCurrentDatabase()\n        const currentSchema = await this.getCurrentSchema()\n        const viewsCondition =\n            viewNames.length === 0\n                ? \"1=1\"\n                : viewNames\n                      .map((tableName) => this.driver.parseTableName(tableName))\n                      .map(({ schema, tableName }) => {\n                          if (!schema) {\n                              schema =\n                                  this.driver.options.schema || currentSchema\n                          }\n\n                          return `(\"t\".\"schema\" = '${schema}' AND \"t\".\"name\" = '${tableName}')`\n                      })\n                      .join(\" OR \")\n\n        const constraintsCondition =\n            viewNames.length === 0\n                ? \"1=1\"\n                : viewNames\n                      .map((tableName) => this.driver.parseTableName(tableName))\n                      .map(({ schema, tableName }) => {\n                          if (!schema) {\n                              schema =\n                                  this.driver.options.schema || currentSchema\n                          }\n\n                          return `(\"ns\".\"nspname\" = '${schema}' AND \"t\".\"relname\" = '${tableName}')`\n                      })\n                      .join(\" OR \")\n\n        const indicesSql =\n            `SELECT \"ns\".\"nspname\" AS \"table_schema\", \"t\".\"relname\" AS \"table_name\", \"i\".\"relname\" AS \"constraint_name\", \"a\".\"attname\" AS \"column_name\", ` +\n            `CASE \"ix\".\"indisunique\" WHEN 't' THEN 'TRUE' ELSE'FALSE' END AS \"is_unique\", pg_get_expr(\"ix\".\"indpred\", \"ix\".\"indrelid\") AS \"condition\", ` +\n            `\"types\".\"typname\" AS \"type_name\" ` +\n            `FROM \"pg_class\" \"t\" ` +\n            `INNER JOIN \"pg_index\" \"ix\" ON \"ix\".\"indrelid\" = \"t\".\"oid\" ` +\n            `INNER JOIN \"pg_attribute\" \"a\" ON \"a\".\"attrelid\" = \"t\".\"oid\"  AND \"a\".\"attnum\" = ANY (\"ix\".\"indkey\") ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"ns\".\"oid\" = \"t\".\"relnamespace\" ` +\n            `INNER JOIN \"pg_class\" \"i\" ON \"i\".\"oid\" = \"ix\".\"indexrelid\" ` +\n            `INNER JOIN \"pg_type\" \"types\" ON \"types\".\"oid\" = \"a\".\"atttypid\" ` +\n            `LEFT JOIN \"pg_constraint\" \"cnst\" ON \"cnst\".\"conname\" = \"i\".\"relname\" ` +\n            `WHERE \"t\".\"relkind\" IN ('m') AND \"cnst\".\"contype\" IS NULL AND (${constraintsCondition})`\n\n        const query =\n            `SELECT \"t\".* FROM ${this.escapePath(\n                this.getTypeormMetadataTableName(),\n            )} \"t\" ` +\n            `INNER JOIN \"pg_catalog\".\"pg_class\" \"c\" ON \"c\".\"relname\" = \"t\".\"name\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"c\".\"relnamespace\" AND \"n\".\"nspname\" = \"t\".\"schema\" ` +\n            `WHERE \"t\".\"type\" IN ('${MetadataTableType.VIEW}', '${\n                MetadataTableType.MATERIALIZED_VIEW\n            }') ${viewsCondition ? `AND (${viewsCondition})` : \"\"}`\n\n        const dbViews = await this.query(query)\n        const dbIndices: ObjectLiteral[] = await this.query(indicesSql)\n        return dbViews.map((dbView: any) => {\n            // find index constraints of table, group them by constraint name and build TableIndex.\n            const tableIndexConstraints = OrmUtils.uniq(\n                dbIndices.filter((dbIndex) => {\n                    return (\n                        dbIndex[\"table_name\"] === dbView[\"name\"] &&\n                        dbIndex[\"table_schema\"] === dbView[\"schema\"]\n                    )\n                }),\n                (dbIndex) => dbIndex[\"constraint_name\"],\n            )\n            const view = new View()\n            const schema =\n                dbView[\"schema\"] === currentSchema &&\n                !this.driver.options.schema\n                    ? undefined\n                    : dbView[\"schema\"]\n            view.database = currentDatabase\n            view.schema = dbView[\"schema\"]\n            view.name = this.driver.buildTableName(dbView[\"name\"], schema)\n            view.expression = dbView[\"value\"]\n            view.materialized =\n                dbView[\"type\"] === MetadataTableType.MATERIALIZED_VIEW\n            view.indices = tableIndexConstraints.map((constraint) => {\n                const indices = dbIndices.filter((index) => {\n                    return (\n                        index[\"table_schema\"] === constraint[\"table_schema\"] &&\n                        index[\"table_name\"] === constraint[\"table_name\"] &&\n                        index[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"]\n                    )\n                })\n                return new TableIndex(<TableIndexOptions>{\n                    view: view,\n                    name: constraint[\"constraint_name\"],\n                    columnNames: indices.map((i) => i[\"column_name\"]),\n                    isUnique: constraint[\"is_unique\"] === \"TRUE\",\n                    where: constraint[\"condition\"],\n                    isFulltext: false,\n                })\n            })\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        // if no tables given then no need to proceed\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        const dbTables: {\n            table_schema: string\n            table_name: string\n            table_comment: string\n        }[] = []\n\n        if (!tableNames) {\n            const tablesSql = `SELECT \"table_schema\", \"table_name\", obj_description(('\"' || \"table_schema\" || '\".\"' || \"table_name\" || '\"')::regclass, 'pg_class') AS table_comment FROM \"information_schema\".\"tables\"`\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tablesCondition = tableNames\n                .map((tableName) => this.driver.parseTableName(tableName))\n                .map(({ schema, tableName }) => {\n                    return `(\"table_schema\" = '${\n                        schema || currentSchema\n                    }' AND \"table_name\" = '${tableName}')`\n                })\n                .join(\" OR \")\n\n            const tablesSql =\n                `SELECT \"table_schema\", \"table_name\", obj_description(('\"' || \"table_schema\" || '\".\"' || \"table_name\" || '\"')::regclass, 'pg_class') AS table_comment FROM \"information_schema\".\"tables\" WHERE ` +\n                tablesCondition\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (dbTables.length === 0) {\n            return []\n        }\n\n        /**\n         * Uses standard SQL information_schema.columns table and postgres-specific\n         * pg_catalog.pg_attribute table to get column information.\n         * @see https://stackoverflow.com/a/19541865\n         */\n        const columnsCondition = dbTables\n            .map(({ table_schema, table_name }) => {\n                return `(\"table_schema\" = '${table_schema}' AND \"table_name\" = '${table_name}')`\n            })\n            .join(\" OR \")\n        const columnsSql =\n            `SELECT columns.*, pg_catalog.col_description(('\"' || table_catalog || '\".\"' || table_schema || '\".\"' || table_name || '\"')::regclass::oid, ordinal_position) AS description, ` +\n            `('\"' || \"udt_schema\" || '\".\"' || \"udt_name\" || '\"')::\"regtype\" AS \"regtype\", pg_catalog.format_type(\"col_attr\".\"atttypid\", \"col_attr\".\"atttypmod\") AS \"format_type\" ` +\n            `FROM \"information_schema\".\"columns\" ` +\n            `LEFT JOIN \"pg_catalog\".\"pg_attribute\" AS \"col_attr\" ON \"col_attr\".\"attname\" = \"columns\".\"column_name\" ` +\n            `AND \"col_attr\".\"attrelid\" = ( ` +\n            `SELECT \"cls\".\"oid\" FROM \"pg_catalog\".\"pg_class\" AS \"cls\" ` +\n            `LEFT JOIN \"pg_catalog\".\"pg_namespace\" AS \"ns\" ON \"ns\".\"oid\" = \"cls\".\"relnamespace\" ` +\n            `WHERE \"cls\".\"relname\" = \"columns\".\"table_name\" ` +\n            `AND \"ns\".\"nspname\" = \"columns\".\"table_schema\" ` +\n            `) ` +\n            `WHERE ` +\n            columnsCondition\n\n        const constraintsCondition = dbTables\n            .map(({ table_schema, table_name }) => {\n                return `(\"ns\".\"nspname\" = '${table_schema}' AND \"t\".\"relname\" = '${table_name}')`\n            })\n            .join(\" OR \")\n\n        const constraintsSql =\n            `SELECT \"ns\".\"nspname\" AS \"table_schema\", \"t\".\"relname\" AS \"table_name\", \"cnst\".\"conname\" AS \"constraint_name\", ` +\n            `pg_get_constraintdef(\"cnst\".\"oid\") AS \"expression\", ` +\n            `CASE \"cnst\".\"contype\" WHEN 'p' THEN 'PRIMARY' WHEN 'u' THEN 'UNIQUE' WHEN 'c' THEN 'CHECK' WHEN 'x' THEN 'EXCLUDE' END AS \"constraint_type\", \"a\".\"attname\" AS \"column_name\" ` +\n            `FROM \"pg_constraint\" \"cnst\" ` +\n            `INNER JOIN \"pg_class\" \"t\" ON \"t\".\"oid\" = \"cnst\".\"conrelid\" ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"ns\".\"oid\" = \"cnst\".\"connamespace\" ` +\n            `LEFT JOIN \"pg_attribute\" \"a\" ON \"a\".\"attrelid\" = \"cnst\".\"conrelid\" AND \"a\".\"attnum\" = ANY (\"cnst\".\"conkey\") ` +\n            `WHERE \"t\".\"relkind\" IN ('r', 'p') AND (${constraintsCondition})`\n\n        const indicesSql =\n            `SELECT \"ns\".\"nspname\" AS \"table_schema\", \"t\".\"relname\" AS \"table_name\", \"i\".\"relname\" AS \"constraint_name\", \"a\".\"attname\" AS \"column_name\", ` +\n            `CASE \"ix\".\"indisunique\" WHEN 't' THEN 'TRUE' ELSE'FALSE' END AS \"is_unique\", pg_get_expr(\"ix\".\"indpred\", \"ix\".\"indrelid\") AS \"condition\", ` +\n            `\"types\".\"typname\" AS \"type_name\", \"am\".\"amname\" AS \"index_type\" ` +\n            `FROM \"pg_class\" \"t\" ` +\n            `INNER JOIN \"pg_index\" \"ix\" ON \"ix\".\"indrelid\" = \"t\".\"oid\" ` +\n            `INNER JOIN \"pg_attribute\" \"a\" ON \"a\".\"attrelid\" = \"t\".\"oid\"  AND \"a\".\"attnum\" = ANY (\"ix\".\"indkey\") ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"ns\".\"oid\" = \"t\".\"relnamespace\" ` +\n            `INNER JOIN \"pg_class\" \"i\" ON \"i\".\"oid\" = \"ix\".\"indexrelid\" ` +\n            `INNER JOIN \"pg_type\" \"types\" ON \"types\".\"oid\" = \"a\".\"atttypid\" ` +\n            `INNER JOIN \"pg_am\" \"am\" ON \"i\".\"relam\" = \"am\".\"oid\" ` +\n            `LEFT JOIN \"pg_constraint\" \"cnst\" ON \"cnst\".\"conname\" = \"i\".\"relname\" ` +\n            `WHERE \"t\".\"relkind\" IN ('r', 'p') AND \"cnst\".\"contype\" IS NULL AND (${constraintsCondition})`\n\n        const foreignKeysCondition = dbTables\n            .map(({ table_schema, table_name }) => {\n                return `(\"ns\".\"nspname\" = '${table_schema}' AND \"cl\".\"relname\" = '${table_name}')`\n            })\n            .join(\" OR \")\n\n        const hasRelispartitionColumn =\n            await this.hasSupportForPartitionedTables()\n        const isPartitionCondition = hasRelispartitionColumn\n            ? ` AND \"cl\".\"relispartition\" = 'f'`\n            : \"\"\n\n        const foreignKeysSql =\n            `SELECT \"con\".\"conname\" AS \"constraint_name\", \"con\".\"nspname\" AS \"table_schema\", \"con\".\"relname\" AS \"table_name\", \"att2\".\"attname\" AS \"column_name\", ` +\n            `\"ns\".\"nspname\" AS \"referenced_table_schema\", \"cl\".\"relname\" AS \"referenced_table_name\", \"att\".\"attname\" AS \"referenced_column_name\", \"con\".\"confdeltype\" AS \"on_delete\", ` +\n            `\"con\".\"confupdtype\" AS \"on_update\", \"con\".\"condeferrable\" AS \"deferrable\", \"con\".\"condeferred\" AS \"deferred\" ` +\n            `FROM ( ` +\n            `SELECT UNNEST (\"con1\".\"conkey\") AS \"parent\", UNNEST (\"con1\".\"confkey\") AS \"child\", \"con1\".\"confrelid\", \"con1\".\"conrelid\", \"con1\".\"conname\", \"con1\".\"contype\", \"ns\".\"nspname\", ` +\n            `\"cl\".\"relname\", \"con1\".\"condeferrable\", ` +\n            `CASE WHEN \"con1\".\"condeferred\" THEN 'INITIALLY DEFERRED' ELSE 'INITIALLY IMMEDIATE' END as condeferred, ` +\n            `CASE \"con1\".\"confdeltype\" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as \"confdeltype\", ` +\n            `CASE \"con1\".\"confupdtype\" WHEN 'a' THEN 'NO ACTION' WHEN 'r' THEN 'RESTRICT' WHEN 'c' THEN 'CASCADE' WHEN 'n' THEN 'SET NULL' WHEN 'd' THEN 'SET DEFAULT' END as \"confupdtype\" ` +\n            `FROM \"pg_class\" \"cl\" ` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"cl\".\"relnamespace\" = \"ns\".\"oid\" ` +\n            `INNER JOIN \"pg_constraint\" \"con1\" ON \"con1\".\"conrelid\" = \"cl\".\"oid\" ` +\n            `WHERE \"con1\".\"contype\" = 'f' AND (${foreignKeysCondition}) ` +\n            `) \"con\" ` +\n            `INNER JOIN \"pg_attribute\" \"att\" ON \"att\".\"attrelid\" = \"con\".\"confrelid\" AND \"att\".\"attnum\" = \"con\".\"child\" ` +\n            `INNER JOIN \"pg_class\" \"cl\" ON \"cl\".\"oid\" = \"con\".\"confrelid\" ${isPartitionCondition}` +\n            `INNER JOIN \"pg_namespace\" \"ns\" ON \"cl\".\"relnamespace\" = \"ns\".\"oid\" ` +\n            `INNER JOIN \"pg_attribute\" \"att2\" ON \"att2\".\"attrelid\" = \"con\".\"conrelid\" AND \"att2\".\"attnum\" = \"con\".\"parent\"`\n\n        const [\n            dbColumns,\n            dbConstraints,\n            dbIndices,\n            dbForeignKeys,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(constraintsSql),\n            this.query(indicesSql),\n            this.query(foreignKeysSql),\n        ])\n\n        // create tables for loaded tables\n        return Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n\n                const getSchemaFromKey = (dbObject: any, key: string) => {\n                    return dbObject[key] === currentSchema &&\n                        (!this.driver.options.schema ||\n                            this.driver.options.schema === currentSchema)\n                        ? undefined\n                        : dbObject[key]\n                }\n                // We do not need to join schema name, when database is by default.\n                const schema = getSchemaFromKey(dbTable, \"table_schema\")\n                table.database = currentDatabase\n                table.schema = dbTable[\"table_schema\"]\n                table.comment = dbTable[\"table_comment\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"table_name\"],\n                    schema,\n                )\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"table_name\"] ===\n                                    dbTable[\"table_name\"] &&\n                                dbColumn[\"table_schema\"] ===\n                                    dbTable[\"table_schema\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnConstraints = dbConstraints.filter(\n                                (dbConstraint) => {\n                                    return (\n                                        dbConstraint[\"table_name\"] ===\n                                            dbColumn[\"table_name\"] &&\n                                        dbConstraint[\"table_schema\"] ===\n                                            dbColumn[\"table_schema\"] &&\n                                        dbConstraint[\"column_name\"] ===\n                                            dbColumn[\"column_name\"]\n                                    )\n                                },\n                            )\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"column_name\"]\n                            tableColumn.type = dbColumn[\"regtype\"].toLowerCase()\n\n                            if (\n                                tableColumn.type === \"numeric\" ||\n                                tableColumn.type === \"numeric[]\" ||\n                                tableColumn.type === \"decimal\" ||\n                                tableColumn.type === \"float\"\n                            ) {\n                                let numericPrecision =\n                                    dbColumn[\"numeric_precision\"]\n                                let numericScale = dbColumn[\"numeric_scale\"]\n                                if (dbColumn[\"data_type\"] === \"ARRAY\") {\n                                    const numericSize = dbColumn[\n                                        \"format_type\"\n                                    ].match(\n                                        /^numeric\\(([0-9]+),([0-9]+)\\)\\[\\]$/,\n                                    )\n                                    if (numericSize) {\n                                        numericPrecision = +numericSize[1]\n                                        numericScale = +numericSize[2]\n                                    }\n                                }\n                                // If one of these properties was set, and another was not, Postgres sets '0' in to unspecified property\n                                // we set 'undefined' in to unspecified property to avoid changing column on sync\n                                if (\n                                    numericPrecision !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        numericPrecision,\n                                    )\n                                ) {\n                                    tableColumn.precision = numericPrecision\n                                } else if (\n                                    numericScale !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        numericScale,\n                                    )\n                                ) {\n                                    tableColumn.precision = undefined\n                                }\n                                if (\n                                    numericScale !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        numericScale,\n                                    )\n                                ) {\n                                    tableColumn.scale = numericScale\n                                } else if (\n                                    numericPrecision !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        numericPrecision,\n                                    )\n                                ) {\n                                    tableColumn.scale = undefined\n                                }\n                            }\n\n                            if (\n                                tableColumn.type === \"interval\" ||\n                                tableColumn.type === \"time without time zone\" ||\n                                tableColumn.type === \"time with time zone\" ||\n                                tableColumn.type ===\n                                    \"timestamp without time zone\" ||\n                                tableColumn.type === \"timestamp with time zone\"\n                            ) {\n                                tableColumn.precision =\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"datetime_precision\"],\n                                    )\n                                        ? dbColumn[\"datetime_precision\"]\n                                        : undefined\n                            }\n\n                            // check if column has user-defined data type.\n                            // NOTE: if ENUM type defined with \"array:true\" it comes with ARRAY type instead of USER-DEFINED\n                            if (\n                                dbColumn[\"data_type\"] === \"USER-DEFINED\" ||\n                                dbColumn[\"data_type\"] === \"ARRAY\"\n                            ) {\n                                const { name } =\n                                    await this.getUserDefinedTypeName(\n                                        table,\n                                        tableColumn,\n                                    )\n\n                                // check if `enumName` is specified by user\n                                const builtEnumName = this.buildEnumName(\n                                    table,\n                                    tableColumn,\n                                    false,\n                                    true,\n                                )\n                                const enumName =\n                                    builtEnumName !== name ? name : undefined\n\n                                // check if type is ENUM\n                                const sql =\n                                    `SELECT \"e\".\"enumlabel\" AS \"value\" FROM \"pg_enum\" \"e\" ` +\n                                    `INNER JOIN \"pg_type\" \"t\" ON \"t\".\"oid\" = \"e\".\"enumtypid\" ` +\n                                    `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n                                    `WHERE \"n\".\"nspname\" = '${\n                                        dbTable[\"table_schema\"]\n                                    }' AND \"t\".\"typname\" = '${\n                                        enumName || name\n                                    }'`\n                                const results: ObjectLiteral[] =\n                                    await this.query(sql)\n\n                                if (results.length) {\n                                    tableColumn.type = \"enum\"\n                                    tableColumn.enum = results.map(\n                                        (result) => result[\"value\"],\n                                    )\n                                    tableColumn.enumName = enumName\n                                }\n\n                                if (dbColumn[\"data_type\"] === \"ARRAY\") {\n                                    tableColumn.isArray = true\n                                    const type = tableColumn.type.replace(\n                                        \"[]\",\n                                        \"\",\n                                    )\n                                    tableColumn.type =\n                                        this.connection.driver.normalizeType({\n                                            type: type,\n                                        })\n                                }\n                            }\n\n                            if (\n                                tableColumn.type === \"geometry\" ||\n                                tableColumn.type === \"geography\"\n                            ) {\n                                const sql =\n                                    `SELECT * FROM (` +\n                                    `SELECT \"f_table_schema\" \"table_schema\", \"f_table_name\" \"table_name\", ` +\n                                    `\"f_${tableColumn.type}_column\" \"column_name\", \"srid\", \"type\" ` +\n                                    `FROM \"${tableColumn.type}_columns\"` +\n                                    `) AS _ ` +\n                                    `WHERE \"column_name\" = '${dbColumn[\"column_name\"]}' AND ` +\n                                    `\"table_schema\" = '${dbColumn[\"table_schema\"]}' AND ` +\n                                    `\"table_name\" = '${dbColumn[\"table_name\"]}'`\n\n                                const results: ObjectLiteral[] =\n                                    await this.query(sql)\n\n                                if (results.length > 0) {\n                                    tableColumn.spatialFeatureType =\n                                        results[0].type\n                                    tableColumn.srid = results[0].srid\n                                }\n                            }\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1\n                            ) {\n                                let length\n                                if (tableColumn.isArray) {\n                                    const match = /\\((\\d+)\\)/.exec(\n                                        dbColumn[\"format_type\"],\n                                    )\n                                    length = match ? match[1] : undefined\n                                } else if (\n                                    dbColumn[\"character_maximum_length\"]\n                                ) {\n                                    length =\n                                        dbColumn[\n                                            \"character_maximum_length\"\n                                        ].toString()\n                                }\n                                if (length) {\n                                    tableColumn.length =\n                                        !this.isDefaultColumnLength(\n                                            table,\n                                            tableColumn,\n                                            length,\n                                        )\n                                            ? length\n                                            : \"\"\n                                }\n                            }\n                            tableColumn.isNullable =\n                                dbColumn[\"is_nullable\"] === \"YES\"\n\n                            const primaryConstraint = columnConstraints.find(\n                                (constraint) =>\n                                    constraint[\"constraint_type\"] === \"PRIMARY\",\n                            )\n                            if (primaryConstraint) {\n                                tableColumn.isPrimary = true\n                                // find another columns involved in primary key constraint\n                                const anotherPrimaryConstraints =\n                                    dbConstraints.filter(\n                                        (constraint) =>\n                                            constraint[\"table_name\"] ===\n                                                dbColumn[\"table_name\"] &&\n                                            constraint[\"table_schema\"] ===\n                                                dbColumn[\"table_schema\"] &&\n                                            constraint[\"column_name\"] !==\n                                                dbColumn[\"column_name\"] &&\n                                            constraint[\"constraint_type\"] ===\n                                                \"PRIMARY\",\n                                    )\n\n                                // collect all column names\n                                const columnNames =\n                                    anotherPrimaryConstraints.map(\n                                        (constraint) =>\n                                            constraint[\"column_name\"],\n                                    )\n                                columnNames.push(dbColumn[\"column_name\"])\n\n                                // build default primary key constraint name\n                                const pkName =\n                                    this.connection.namingStrategy.primaryKeyName(\n                                        table,\n                                        columnNames,\n                                    )\n\n                                // if primary key has user-defined constraint name, write it in table column\n                                if (\n                                    primaryConstraint[\"constraint_name\"] !==\n                                    pkName\n                                ) {\n                                    tableColumn.primaryKeyConstraintName =\n                                        primaryConstraint[\"constraint_name\"]\n                                }\n                            }\n\n                            const uniqueConstraints = columnConstraints.filter(\n                                (constraint) =>\n                                    constraint[\"constraint_type\"] === \"UNIQUE\",\n                            )\n                            const isConstraintComposite =\n                                uniqueConstraints.every((uniqueConstraint) => {\n                                    return dbConstraints.some(\n                                        (dbConstraint) =>\n                                            dbConstraint[\"constraint_type\"] ===\n                                                \"UNIQUE\" &&\n                                            dbConstraint[\"constraint_name\"] ===\n                                                uniqueConstraint[\n                                                    \"constraint_name\"\n                                                ] &&\n                                            dbConstraint[\"column_name\"] !==\n                                                dbColumn[\"column_name\"],\n                                    )\n                                })\n                            tableColumn.isUnique =\n                                uniqueConstraints.length > 0 &&\n                                !isConstraintComposite\n\n                            if (dbColumn.is_identity === \"YES\") {\n                                // Postgres 10+ Identity column\n                                tableColumn.isGenerated = true\n                                tableColumn.generationStrategy = \"identity\"\n                                tableColumn.generatedIdentity =\n                                    dbColumn.identity_generation\n                            } else if (\n                                dbColumn[\"column_default\"] !== null &&\n                                dbColumn[\"column_default\"] !== undefined\n                            ) {\n                                const serialDefaultName = `nextval('${this.buildSequenceName(\n                                    table,\n                                    dbColumn[\"column_name\"],\n                                )}'::regclass)`\n                                const serialDefaultPath = `nextval('${this.buildSequencePath(\n                                    table,\n                                    dbColumn[\"column_name\"],\n                                )}'::regclass)`\n\n                                const defaultWithoutQuotes = dbColumn[\n                                    \"column_default\"\n                                ].replace(/\"/g, \"\")\n\n                                if (\n                                    defaultWithoutQuotes ===\n                                        serialDefaultName ||\n                                    defaultWithoutQuotes === serialDefaultPath\n                                ) {\n                                    tableColumn.isGenerated = true\n                                    tableColumn.generationStrategy = \"increment\"\n                                } else if (\n                                    dbColumn[\"column_default\"] ===\n                                        \"gen_random_uuid()\" ||\n                                    /^uuid_generate_v\\d\\(\\)/.test(\n                                        dbColumn[\"column_default\"],\n                                    )\n                                ) {\n                                    if (tableColumn.type === \"uuid\") {\n                                        tableColumn.isGenerated = true\n                                        tableColumn.generationStrategy = \"uuid\"\n                                    } else {\n                                        tableColumn.default =\n                                            dbColumn[\"column_default\"]\n                                    }\n                                } else if (\n                                    dbColumn[\"column_default\"] === \"now()\" ||\n                                    dbColumn[\"column_default\"].indexOf(\n                                        \"'now'::text\",\n                                    ) !== -1\n                                ) {\n                                    tableColumn.default =\n                                        dbColumn[\"column_default\"]\n                                } else {\n                                    tableColumn.default = dbColumn[\n                                        \"column_default\"\n                                    ].replace(/::[\\w\\s.[\\]\\-\"]+/g, \"\")\n                                    tableColumn.default =\n                                        tableColumn.default.replace(\n                                            /^(-?\\d+)$/,\n                                            \"'$1'\",\n                                        )\n                                }\n                            }\n\n                            if (\n                                dbColumn[\"is_generated\"] === \"ALWAYS\" &&\n                                dbColumn[\"generation_expression\"]\n                            ) {\n                                // In postgres there is no VIRTUAL generated column type\n                                tableColumn.generatedType = \"STORED\"\n                                // We cannot relay on information_schema.columns.generation_expression, because it is formatted different.\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        database: currentDatabase,\n                                        schema: dbTable[\"table_schema\"],\n                                        table: dbTable[\"table_name\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            tableColumn.comment = dbColumn[\"description\"]\n                                ? dbColumn[\"description\"]\n                                : undefined\n                            if (dbColumn[\"character_set_name\"])\n                                tableColumn.charset =\n                                    dbColumn[\"character_set_name\"]\n                            if (dbColumn[\"collation_name\"])\n                                tableColumn.collation =\n                                    dbColumn[\"collation_name\"]\n                            return tableColumn\n                        }),\n                )\n\n                // find unique constraints of table, group them by constraint name and build TableUnique.\n                const tableUniqueConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"UNIQUE\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.uniques = tableUniqueConstraints.map((constraint) => {\n                    const uniques = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"],\n                    )\n                    return new TableUnique({\n                        name: constraint[\"constraint_name\"],\n                        columnNames: uniques.map((u) => u[\"column_name\"]),\n                        deferrable: constraint[\"deferrable\"]\n                            ? constraint[\"deferred\"]\n                            : undefined,\n                    })\n                })\n\n                // find check constraints of table, group them by constraint name and build TableCheck.\n                const tableCheckConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"CHECK\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.checks = tableCheckConstraints.map((constraint) => {\n                    const checks = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"constraint_name\"] ===\n                            constraint[\"constraint_name\"],\n                    )\n                    return new TableCheck({\n                        name: constraint[\"constraint_name\"],\n                        columnNames: checks.map((c) => c[\"column_name\"]),\n                        expression: constraint[\"expression\"].replace(\n                            /^\\s*CHECK\\s*\\((.*)\\)\\s*$/i,\n                            \"$1\",\n                        ),\n                    })\n                })\n\n                // find exclusion constraints of table, group them by constraint name and build TableExclusion.\n                const tableExclusionConstraints = OrmUtils.uniq(\n                    dbConstraints.filter((dbConstraint) => {\n                        return (\n                            dbConstraint[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbConstraint[\"table_schema\"] ===\n                                dbTable[\"table_schema\"] &&\n                            dbConstraint[\"constraint_type\"] === \"EXCLUDE\"\n                        )\n                    }),\n                    (dbConstraint) => dbConstraint[\"constraint_name\"],\n                )\n\n                table.exclusions = tableExclusionConstraints.map(\n                    (constraint) => {\n                        return new TableExclusion({\n                            name: constraint[\"constraint_name\"],\n                            expression: constraint[\"expression\"].substring(8), // trim EXCLUDE from start of expression\n                        })\n                    },\n                )\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter((dbForeignKey) => {\n                        return (\n                            dbForeignKey[\"table_name\"] ===\n                                dbTable[\"table_name\"] &&\n                            dbForeignKey[\"table_schema\"] ===\n                                dbTable[\"table_schema\"]\n                        )\n                    }),\n                    (dbForeignKey) => dbForeignKey[\"constraint_name\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"constraint_name\"] ===\n                                dbForeignKey[\"constraint_name\"],\n                        )\n\n                        // if referenced table located in currently used schema, we don't need to concat schema name to table name.\n                        const schema = getSchemaFromKey(\n                            dbForeignKey,\n                            \"referenced_table_schema\",\n                        )\n                        const referencedTableName = this.driver.buildTableName(\n                            dbForeignKey[\"referenced_table_name\"],\n                            schema,\n                        )\n\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"constraint_name\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"column_name\"],\n                            ),\n                            referencedSchema:\n                                dbForeignKey[\"referenced_table_schema\"],\n                            referencedTableName: referencedTableName,\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"referenced_column_name\"],\n                            ),\n                            onDelete: dbForeignKey[\"on_delete\"],\n                            onUpdate: dbForeignKey[\"on_update\"],\n                            deferrable: dbForeignKey[\"deferrable\"]\n                                ? dbForeignKey[\"deferred\"]\n                                : undefined,\n                        })\n                    },\n                )\n\n                // find index constraints of table, group them by constraint name and build TableIndex.\n                const tableIndexConstraints = OrmUtils.uniq(\n                    dbIndices.filter((dbIndex) => {\n                        return (\n                            dbIndex[\"table_name\"] === dbTable[\"table_name\"] &&\n                            dbIndex[\"table_schema\"] === dbTable[\"table_schema\"]\n                        )\n                    }),\n                    (dbIndex) => dbIndex[\"constraint_name\"],\n                )\n\n                table.indices = tableIndexConstraints.map((constraint) => {\n                    const indices = dbIndices.filter((index) => {\n                        return (\n                            index[\"table_schema\"] ===\n                                constraint[\"table_schema\"] &&\n                            index[\"table_name\"] === constraint[\"table_name\"] &&\n                            index[\"constraint_name\"] ===\n                                constraint[\"constraint_name\"]\n                        )\n                    })\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"constraint_name\"],\n                        columnNames: indices.map((i) => i[\"column_name\"]),\n                        isUnique: constraint[\"is_unique\"] === \"TRUE\",\n                        where: constraint[\"condition\"],\n                        isSpatial: constraint[\"index_type\"] === \"gist\",\n                        isFulltext: false,\n                    })\n                })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds create table sql.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) => this.buildCreateColumnSql(table, column))\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueExist = table.uniques.some(\n                    (unique) =>\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames[0] === column.name,\n                )\n                if (!isUniqueExist)\n                    table.uniques.push(\n                        new TableUnique({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                        }),\n                    )\n            })\n\n        if (table.uniques.length > 0) {\n            const uniquesSql = table.uniques\n                .map((unique) => {\n                    const uniqueName = unique.name\n                        ? unique.name\n                        : this.connection.namingStrategy.uniqueConstraintName(\n                              table,\n                              unique.columnNames,\n                          )\n                    const columnNames = unique.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    let constraint = `CONSTRAINT \"${uniqueName}\" UNIQUE (${columnNames})`\n                    if (unique.deferrable)\n                        constraint += ` DEFERRABLE ${unique.deferrable}`\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${uniquesSql}`\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.exclusions.length > 0) {\n            const exclusionsSql = table.exclusions\n                .map((exclusion) => {\n                    const exclusionName = exclusion.name\n                        ? exclusion.name\n                        : this.connection.namingStrategy.exclusionConstraintName(\n                              table,\n                              exclusion.expression!,\n                          )\n                    return `CONSTRAINT \"${exclusionName}\" EXCLUDE ${exclusion.expression}`\n                })\n                .join(\", \")\n\n            sql += `, ${exclusionsSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \"${\n                        fk.name\n                    }\" FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    if (fk.onDelete) constraint += ` ON DELETE ${fk.onDelete}`\n                    if (fk.onUpdate) constraint += ` ON UPDATE ${fk.onUpdate}`\n                    if (fk.deferrable)\n                        constraint += ` DEFERRABLE ${fk.deferrable}`\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const primaryKeyName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      table,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        table.columns\n            .filter((it) => it.comment)\n            .forEach(\n                (it) =>\n                    (sql += `; COMMENT ON COLUMN ${this.escapePath(table)}.\"${\n                        it.name\n                    }\" IS ${this.escapeComment(it.comment)}`),\n            )\n\n        return new Query(sql)\n    }\n\n    /**\n     * Loads Postgres version.\n     */\n    async getVersion(): Promise<string> {\n        // we use `SELECT version()` instead of `SHOW server_version` or `SHOW server_version_num`\n        // to maintain compatability with Amazon Redshift.\n        //\n        // see:\n        //  - https://github.com/typeorm/typeorm/pull/9319\n        //  - https://docs.aws.amazon.com/redshift/latest/dg/c_unsupported-postgresql-functions.html\n        const result: [{ version: string }] = await this.query(\n            `SELECT version()`,\n        )\n\n        // Examples:\n        // Postgres: \"PostgreSQL 14.10 on x86_64-pc-linux-gnu, compiled by gcc (GCC) 8.5.0 20210514 (Red Hat 8.5.0-20), 64-bit\"\n        // Yugabyte: \"PostgreSQL 11.2-YB-2.18.1.0-b0 on x86_64-pc-linux-gnu, compiled by clang version 15.0.3 (https://github.com/yugabyte/llvm-project.git 0b8d1183745fd3998d8beffeec8cbe99c1b20529), 64-bit\"\n        return result[0].version.replace(/^PostgreSQL ([\\d.]+).*$/, \"$1\")\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(tableOrPath: Table | string): Query {\n        return new Query(`DROP TABLE ${this.escapePath(tableOrPath)}`)\n    }\n\n    protected createViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        const viewName = this.escapePath(view)\n\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE ${materializedClause}VIEW ${viewName} AS ${view.expression}`,\n            )\n        } else {\n            return new Query(\n                `CREATE ${materializedClause}VIEW ${viewName} AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        const currentSchema = await this.getCurrentSchema()\n\n        let { schema, tableName: name } = this.driver.parseTableName(view)\n\n        if (!schema) {\n            schema = currentSchema\n        }\n\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type,\n            schema,\n            name,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(view: View): Query {\n        const materializedClause = view.materialized ? \"MATERIALIZED \" : \"\"\n        return new Query(\n            `DROP ${materializedClause}VIEW ${this.escapePath(view)}`,\n        )\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(view: View): Promise<Query> {\n        const currentSchema = await this.getCurrentSchema()\n\n        let { schema, tableName: name } = this.driver.parseTableName(view)\n\n        if (!schema) {\n            schema = currentSchema\n        }\n\n        const type = view.materialized\n            ? MetadataTableType.MATERIALIZED_VIEW\n            : MetadataTableType.VIEW\n        return this.deleteTypeormMetadataSql({ type, schema, name })\n    }\n\n    /**\n     * Drops ENUM type from given schemas.\n     */\n    protected async dropEnumTypes(schemaNames: string): Promise<void> {\n        const selectDropsQuery =\n            `SELECT 'DROP TYPE IF EXISTS \"' || n.nspname || '\".\"' || t.typname || '\" CASCADE;' as \"query\" FROM \"pg_type\" \"t\" ` +\n            `INNER JOIN \"pg_enum\" \"e\" ON \"e\".\"enumtypid\" = \"t\".\"oid\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n            `WHERE \"n\".\"nspname\" IN (${schemaNames}) GROUP BY \"n\".\"nspname\", \"t\".\"typname\"`\n        const dropQueries: ObjectLiteral[] = await this.query(selectDropsQuery)\n        await Promise.all(dropQueries.map((q) => this.query(q[\"query\"])))\n    }\n\n    /**\n     * Checks if enum with the given name exist in the database.\n     */\n    protected async hasEnumType(\n        table: Table,\n        column: TableColumn,\n    ): Promise<boolean> {\n        let { schema } = this.driver.parseTableName(table)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        const enumName = this.buildEnumName(table, column, false, true)\n        const sql =\n            `SELECT \"n\".\"nspname\", \"t\".\"typname\" FROM \"pg_type\" \"t\" ` +\n            `INNER JOIN \"pg_namespace\" \"n\" ON \"n\".\"oid\" = \"t\".\"typnamespace\" ` +\n            `WHERE \"n\".\"nspname\" = '${schema}' AND \"t\".\"typname\" = '${enumName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Builds create ENUM type sql.\n     */\n    protected createEnumTypeSql(\n        table: Table,\n        column: TableColumn,\n        enumName?: string,\n    ): Query {\n        if (!enumName) enumName = this.buildEnumName(table, column)\n        const enumValues = column\n            .enum!.map((value) => `'${value.replaceAll(\"'\", \"''\")}'`)\n            .join(\", \")\n        return new Query(`CREATE TYPE ${enumName} AS ENUM(${enumValues})`)\n    }\n\n    /**\n     * Builds create ENUM type sql.\n     */\n    protected dropEnumTypeSql(\n        table: Table,\n        column: TableColumn,\n        enumName?: string,\n    ): Query {\n        if (!enumName) enumName = this.buildEnumName(table, column)\n        return new Query(`DROP TYPE ${enumName}`)\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX${\n                index.isConcurrent ? \" CONCURRENTLY\" : \"\"\n            } \"${index.name}\" ON ${this.escapePath(table)} ${\n                index.isSpatial ? \"USING GiST \" : \"\"\n            }(${columns}) ${index.where ? \"WHERE \" + index.where : \"\"}`,\n        )\n    }\n\n    /**\n     * Builds create view index sql.\n     */\n    protected createViewIndexSql(view: View, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX \"${\n                index.name\n            }\" ON ${this.escapePath(view)} (${columns}) ${\n                index.where ? \"WHERE \" + index.where : \"\"\n            }`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table | View,\n        indexOrName: TableIndex | string,\n    ): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        const concurrent = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.isConcurrent\n            : false\n        const { schema } = this.driver.parseTableName(table)\n        return schema\n            ? new Query(\n                  `DROP INDEX ${\n                      concurrent ? \"CONCURRENTLY \" : \"\"\n                  }\"${schema}\".\"${indexName}\"`,\n              )\n            : new Query(\n                  `DROP INDEX ${\n                      concurrent ? \"CONCURRENTLY \" : \"\"\n                  }\"${indexName}\"`,\n              )\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(\n        table: Table,\n        columnNames: string[],\n        constraintName?: string,\n    ): Query {\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        if (!table.primaryColumns.length)\n            throw new TypeORMError(`Table ${table} has no primary keys.`)\n\n        const columnNames = table.primaryColumns.map((column) => column.name)\n        const constraintName = table.primaryColumns[0].primaryKeyConstraintName\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${primaryKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds create unique constraint sql.\n     */\n    protected createUniqueConstraintSql(\n        table: Table,\n        uniqueConstraint: TableUnique,\n    ): Query {\n        const columnNames = uniqueConstraint.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        let sql = `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n            uniqueConstraint.name\n        }\" UNIQUE (${columnNames})`\n        if (uniqueConstraint.deferrable)\n            sql += ` DEFERRABLE ${uniqueConstraint.deferrable}`\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop unique constraint sql.\n     */\n    protected dropUniqueConstraintSql(\n        table: Table,\n        uniqueOrName: TableUnique | string,\n    ): Query {\n        const uniqueName = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName.name\n            : uniqueOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${uniqueName}\"`,\n        )\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                checkConstraint.name\n            }\" CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName.name\n            : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${checkName}\"`,\n        )\n    }\n\n    /**\n     * Builds create exclusion constraint sql.\n     */\n    protected createExclusionConstraintSql(\n        table: Table,\n        exclusionConstraint: TableExclusion,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                exclusionConstraint.name\n            }\" EXCLUDE ${exclusionConstraint.expression}`,\n        )\n    }\n\n    /**\n     * Builds drop exclusion constraint sql.\n     */\n    protected dropExclusionConstraintSql(\n        table: Table,\n        exclusionOrName: TableExclusion | string,\n    ): Query {\n        const exclusionName = InstanceChecker.isTableExclusion(exclusionOrName)\n            ? exclusionOrName.name\n            : exclusionOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${exclusionName}\"`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                foreignKey.name\n            }\" FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )}(${referencedColumnNames})`\n        if (foreignKey.onDelete) sql += ` ON DELETE ${foreignKey.onDelete}`\n        if (foreignKey.onUpdate) sql += ` ON UPDATE ${foreignKey.onUpdate}`\n        if (foreignKey.deferrable) sql += ` DEFERRABLE ${foreignKey.deferrable}`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${foreignKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds sequence name from given table and column.\n     */\n    protected buildSequenceName(\n        table: Table,\n        columnOrName: TableColumn | string,\n    ): string {\n        const { tableName } = this.driver.parseTableName(table)\n\n        const columnName = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName.name\n            : columnOrName\n\n        let seqName = `${tableName}_${columnName}_seq`\n\n        if (seqName.length > this.connection.driver.maxAliasLength!) {\n            // note doesn't yet handle corner cases where .length differs from number of UTF-8 bytes\n            seqName = `${tableName.substring(0, 29)}_${columnName.substring(\n                0,\n                Math.max(29, 63 - table.name.length - 5),\n            )}_seq`\n        }\n\n        return seqName\n    }\n\n    protected buildSequencePath(\n        table: Table,\n        columnOrName: TableColumn | string,\n    ): string {\n        const { schema } = this.driver.parseTableName(table)\n\n        return schema\n            ? `${schema}.${this.buildSequenceName(table, columnOrName)}`\n            : this.buildSequenceName(table, columnOrName)\n    }\n\n    /**\n     * Builds ENUM type name from given table and column.\n     */\n    protected buildEnumName(\n        table: Table,\n        column: TableColumn,\n        withSchema: boolean = true,\n        disableEscape?: boolean,\n        toOld?: boolean,\n    ): string {\n        const { schema, tableName } = this.driver.parseTableName(table)\n        let enumName = column.enumName\n            ? column.enumName\n            : `${tableName}_${column.name.toLowerCase()}_enum`\n        if (schema && withSchema) enumName = `${schema}.${enumName}`\n        if (toOld) enumName = enumName + \"_old\"\n        return enumName\n            .split(\".\")\n            .map((i) => {\n                return disableEscape ? i : `\"${i}\"`\n            })\n            .join(\".\")\n    }\n\n    protected async getUserDefinedTypeName(table: Table, column: TableColumn) {\n        let { schema, tableName: name } = this.driver.parseTableName(table)\n\n        if (!schema) {\n            schema = await this.getCurrentSchema()\n        }\n\n        const result = await this.query(\n            `SELECT \"udt_schema\", \"udt_name\" ` +\n                `FROM \"information_schema\".\"columns\" WHERE \"table_schema\" = '${schema}' AND \"table_name\" = '${name}' AND \"column_name\"='${column.name}'`,\n        )\n\n        // docs: https://www.postgresql.org/docs/current/xtypes.html\n        // When you define a new base type, PostgreSQL automatically provides support for arrays of that type.\n        // The array type typically has the same name as the base type with the underscore character (_) prepended.\n        // ----\n        // so, we must remove this underscore character from enum type name\n        let udtName = result[0][\"udt_name\"]\n        if (udtName.indexOf(\"_\") === 0) {\n            udtName = udtName.substr(1, udtName.length)\n        }\n        return {\n            schema: result[0][\"udt_schema\"],\n            name: udtName,\n        }\n    }\n\n    /**\n     * Escapes a given comment so it's safe to include in a query.\n     */\n    protected escapeComment(comment?: string) {\n        if (!comment || comment.length === 0) {\n            return \"NULL\"\n        }\n\n        comment = comment.replace(/'/g, \"''\").replace(/\\u0000/g, \"\") // Null bytes aren't allowed in comments\n\n        return `'${comment}'`\n    }\n\n    /**\n     * Escapes given table or view path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { schema, tableName } = this.driver.parseTableName(target)\n\n        if (schema && schema !== this.driver.searchSchema) {\n            return `\"${schema}\".\"${tableName}\"`\n        }\n\n        return `\"${tableName}\"`\n    }\n\n    /**\n     * Get the table name with table schema\n     * Note: Without ' or \"\n     */\n    protected async getTableNameWithSchema(target: Table | string) {\n        const tableName = InstanceChecker.isTable(target) ? target.name : target\n        if (tableName.indexOf(\".\") === -1) {\n            const schemaResult = await this.query(`SELECT current_schema()`)\n            const schema = schemaResult[0][\"current_schema\"]\n            return `${schema}.${tableName}`\n        } else {\n            return `${tableName.split(\".\")[0]}.${tableName.split(\".\")[1]}`\n        }\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(table: Table, column: TableColumn) {\n        let c = '\"' + column.name + '\"'\n        if (\n            column.isGenerated === true &&\n            column.generationStrategy !== \"uuid\"\n        ) {\n            if (column.generationStrategy === \"identity\") {\n                // Postgres 10+ Identity generated column\n                const generatedIdentityOrDefault =\n                    column.generatedIdentity || \"BY DEFAULT\"\n                c += ` ${column.type} GENERATED ${generatedIdentityOrDefault} AS IDENTITY`\n            } else {\n                // classic SERIAL primary column\n                if (\n                    column.type === \"integer\" ||\n                    column.type === \"int\" ||\n                    column.type === \"int4\"\n                )\n                    c += \" SERIAL\"\n                if (column.type === \"smallint\" || column.type === \"int2\")\n                    c += \" SMALLSERIAL\"\n                if (column.type === \"bigint\" || column.type === \"int8\")\n                    c += \" BIGSERIAL\"\n            }\n        }\n        if (column.type === \"enum\" || column.type === \"simple-enum\") {\n            c += \" \" + this.buildEnumName(table, column)\n            if (column.isArray) c += \" array\"\n        } else if (!column.isGenerated || column.type === \"uuid\") {\n            c += \" \" + this.connection.driver.createFullType(column)\n        }\n\n        // Postgres only supports the stored generated column type\n        if (column.generatedType === \"STORED\" && column.asExpression) {\n            c += ` GENERATED ALWAYS AS (${column.asExpression}) STORED`\n        }\n\n        if (column.charset) c += ' CHARACTER SET \"' + column.charset + '\"'\n        if (column.collation) c += ' COLLATE \"' + column.collation + '\"'\n        if (column.isNullable !== true) c += \" NOT NULL\"\n        if (column.default !== undefined && column.default !== null)\n            c += \" DEFAULT \" + column.default\n        if (\n            column.isGenerated &&\n            column.generationStrategy === \"uuid\" &&\n            !column.default\n        )\n            c += ` DEFAULT ${this.driver.uuidGenerator}`\n\n        return c\n    }\n\n    /**\n     * Checks if the PostgreSQL server has support for partitioned tables\n     */\n    protected async hasSupportForPartitionedTables() {\n        const result = await this.query(\n            `SELECT TRUE FROM information_schema.columns WHERE table_name = 'pg_class' and column_name = 'relispartition'`,\n        )\n        return result.length ? true : false\n    }\n\n    /**\n     * Change table comment.\n     */\n    async changeTableComment(\n        tableOrName: Table | string,\n        newComment?: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        newComment = this.escapeComment(newComment)\n        const comment = this.escapeComment(table.comment)\n\n        if (newComment === comment) {\n            return\n        }\n\n        const newTable = table.clone()\n\n        upQueries.push(\n            new Query(\n                `COMMENT ON TABLE ${this.escapePath(\n                    newTable,\n                )} IS ${newComment}`,\n            ),\n        )\n\n        downQueries.push(\n            new Query(\n                `COMMENT ON TABLE ${this.escapePath(table)} IS ${comment}`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n\n        table.comment = newTable.comment\n        this.replaceCachedTable(table, newTable)\n    }\n}\n"], "sourceRoot": "../.."}