import { CommonMiddlewareUnion, IConfigurationOptions, IMidwayApplication, IMidwayBootstrapOptions, IMidwayContainer, IMidwayContext, IMidwayFramework, CommonFilterUnion, MiddlewareRespond, CommonGuardUnion, ILogger, MidwayLoggerOptions } from './interface';
import { MidwayEnvironmentService } from './service/environmentService';
import { MidwayConfigService } from './service/configService';
import { MidwayInformationService } from './service/informationService';
import { MidwayLoggerService } from './service/loggerService';
import { ContextMiddlewareManager } from './common/middlewareManager';
import { MidwayMiddlewareService } from './service/middlewareService';
import { FilterManager } from './common/filterManager';
import { MidwayMockService } from './service/mockService';
import { AsyncContextManager } from './common/asyncContextManager';
import { GuardManager } from './common/guardManager';
export declare abstract class BaseFramework<APP extends IMidwayApplication<CTX>, CTX extends IMidwayContext, OPT extends IConfigurationOptions, ResOrNext = unknown, Next = unknown> implements IMidwayFramework<APP, CTX, OPT, ResOrNext, Next> {
    readonly applicationContext: IMidwayContainer;
    app: APP;
    configurationOptions: OPT;
    protected logger: ILogger;
    protected appLogger: ILogger;
    protected defaultContext: {};
    protected contextLoggerApplyLogger: string;
    protected contextLoggerFormat: any;
    protected middlewareManager: ContextMiddlewareManager<CTX, ResOrNext, Next>;
    protected filterManager: FilterManager<CTX, ResOrNext, Next>;
    protected guardManager: GuardManager<CTX>;
    protected composeMiddleware: any;
    protected bootstrapOptions: IMidwayBootstrapOptions;
    protected asyncContextManager: AsyncContextManager;
    private namespace;
    loggerService: MidwayLoggerService;
    environmentService: MidwayEnvironmentService;
    configService: MidwayConfigService;
    informationService: MidwayInformationService;
    middlewareService: MidwayMiddlewareService<CTX, ResOrNext, Next>;
    mockService: MidwayMockService;
    constructor(applicationContext: IMidwayContainer);
    init(): Promise<this>;
    abstract configure(options?: OPT): any;
    isEnable(): boolean;
    initialize(options?: IMidwayBootstrapOptions): Promise<void>;
    /**
     * @deprecated
     */
    protected containerInitialize(options: IMidwayBootstrapOptions): Promise<void>;
    /**
     * @deprecated
     */
    protected containerDirectoryLoad(options: IMidwayBootstrapOptions): Promise<void>;
    /**
     * @deprecated
     */
    protected containerReady(options: IMidwayBootstrapOptions): Promise<void>;
    getApplicationContext(): IMidwayContainer;
    getConfiguration(key?: string): any;
    getCurrentEnvironment(): string;
    getApplication(): APP;
    abstract applicationInitialize(options: IMidwayBootstrapOptions): any;
    abstract run(): Promise<void>;
    protected createContextLogger(ctx: CTX, name?: string): ILogger;
    stop(): Promise<void>;
    getAppDir(): string;
    getBaseDir(): string;
    protected defineApplicationProperties(applicationProperties?: {}, whiteList?: string[]): void;
    protected beforeStop(): Promise<void>;
    /**
     * @deprecated
     */
    protected beforeContainerInitialize(options: Partial<IMidwayBootstrapOptions>): Promise<void>;
    /**
     * @deprecated
     */
    protected afterContainerInitialize(options: Partial<IMidwayBootstrapOptions>): Promise<void>;
    /**
     * @deprecated
     */
    protected afterContainerDirectoryLoad(options: Partial<IMidwayBootstrapOptions>): Promise<void>;
    /**
     * @deprecated
     */
    protected afterContainerReady(options: Partial<IMidwayBootstrapOptions>): Promise<void>;
    applyMiddleware<R, N>(lastMiddleware?: CommonMiddlewareUnion<CTX, R, N>): Promise<MiddlewareRespond<CTX, R, N>>;
    getLogger(name?: string): any;
    getCoreLogger(): ILogger;
    createLogger(name: string, option?: MidwayLoggerOptions): any;
    getProjectName(): string;
    getFrameworkName(): string;
    useMiddleware(middleware: CommonMiddlewareUnion<CTX, ResOrNext, Next>): void;
    getMiddleware(): ContextMiddlewareManager<CTX, ResOrNext, Next>;
    useFilter(filter: CommonFilterUnion<CTX, ResOrNext, Next>): void;
    useGuard(guards: CommonGuardUnion<CTX>): void;
    runGuard(ctx: CTX, supplierClz: new (...args: any[]) => any, methodName: string): Promise<boolean>;
    protected createMiddlewareManager(): ContextMiddlewareManager<CTX, ResOrNext, Next>;
    protected createFilterManager(): FilterManager<CTX, ResOrNext, Next>;
    protected createGuardManager(): GuardManager<CTX>;
    setNamespace(namespace: string): void;
    getNamespace(): string;
}
//# sourceMappingURL=baseFramework.d.ts.map