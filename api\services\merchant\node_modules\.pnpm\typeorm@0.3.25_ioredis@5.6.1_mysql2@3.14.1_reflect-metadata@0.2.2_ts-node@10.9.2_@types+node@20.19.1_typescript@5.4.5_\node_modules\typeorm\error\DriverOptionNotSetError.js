"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DriverOptionNotSetError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown if some required driver's option is not set.
 */
class DriverOptionNotSetError extends TypeORMError_1.TypeORMError {
    constructor(optionName) {
        super(`Driver option (${optionName}) is not set. ` +
            `Please set it to perform connection to the database.`);
    }
}
exports.DriverOptionNotSetError = DriverOptionNotSetError;

//# sourceMappingURL=DriverOptionNotSetError.js.map
