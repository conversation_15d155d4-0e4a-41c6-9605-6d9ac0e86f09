<template>
  <div class="settlement-demo-page">
    <!-- 页面标题 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <h2>🎯 结算规则演示</h2>
        <p>展示不同类型商户的结算规则匹配效果</p>
      </div>
    </div>

    <!-- 商户类型演示 -->
    <div class="demo-grid">
      <!-- 普通个人商户 -->
      <div class="custom-card art-custom-card demo-card">
        <div class="custom-card-content">
          <div class="merchant-header">
            <el-avatar :size="50" src="/images/merchant-personal.png">👤</el-avatar>
            <div class="merchant-info">
              <h3>张三 - 个人商户</h3>
              <p>普通个人商户，无特殊认证</p>
            </div>
          </div>
          
          <div class="settlement-details">
            <div class="rate-display">
              <span class="rate-number">3.5%</span>
              <span class="rate-label">佣金费率</span>
            </div>
            
            <div class="features">
              <el-tag size="small">T+1结算</el-tag>
              <el-tag size="small" type="info">提现费¥2/笔</el-tag>
            </div>
            
            <div class="calculation">
              <p><strong>示例计算：</strong></p>
              <p>订单金额：¥1000</p>
              <p>平台佣金：¥1000 × 3.5% = ¥35</p>
              <p>实际到账：¥1000 - ¥35 = ¥965</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 企业商户 -->
      <div class="custom-card art-custom-card demo-card">
        <div class="custom-card-content">
          <div class="merchant-header">
            <el-avatar :size="50" src="/images/merchant-enterprise.png">🏢</el-avatar>
            <div class="merchant-info">
              <h3>某某文化公司 - 企业商户</h3>
              <p>企业商户，享受优惠费率</p>
            </div>
          </div>

          <div class="settlement-details">
            <div class="rate-display enterprise">
              <span class="rate-number">2.8%</span>
              <span class="rate-label">企业优惠费率</span>
            </div>

            <div class="features">
              <el-tag size="small" type="success">T+0实时结算</el-tag>
              <el-tag size="small" type="success">提现费¥1/笔</el-tag>
            </div>

            <div class="calculation">
              <p><strong>示例计算：</strong></p>
              <p>订单金额：¥1000</p>
              <p>平台佣金：¥1000 × 2.8% = ¥28</p>
              <p>实际到账：¥1000 - ¥28 = ¥972</p>
              <p class="savings">💰 比个人商户节省¥7</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 新商户优惠 -->
      <div class="custom-card art-custom-card demo-card">
        <div class="custom-card-content">
          <div class="merchant-header">
            <el-avatar :size="50" src="/images/merchant-new.png">🆕</el-avatar>
            <div class="merchant-info">
              <h3>李四 - 新商户</h3>
              <p>刚注册的新商户，享受3个月优惠</p>
            </div>
          </div>
          
          <div class="settlement-details">
            <div class="rate-display new">
              <span class="rate-number">2.0%</span>
              <span class="rate-label">新商户优惠费率</span>
              <el-tag size="small" type="warning">前3个月</el-tag>
            </div>
            
            <div class="features">
              <el-tag size="small">T+1结算</el-tag>
              <el-tag size="small" type="info">提现费¥1/笔</el-tag>
            </div>
            
            <div class="calculation">
              <p><strong>示例计算：</strong></p>
              <p>订单金额：¥1000</p>
              <p>平台佣金：¥1000 × 2.0% = ¥20</p>
              <p>实际到账：¥1000 - ¥20 = ¥980</p>
              <p class="savings">🎉 比标准费率节省¥15</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 手工艺人免佣金 -->
      <div class="custom-card art-custom-card demo-card special">
        <div class="custom-card-content">
          <div class="merchant-header">
            <el-avatar :size="50" src="/images/handicraft.png">🎨</el-avatar>
            <div class="merchant-info">
              <h3>王师傅 - 认证手工艺人</h3>
              <p>通过平台认证的传统手工艺人</p>
            </div>
          </div>
          
          <div class="settlement-details">
            <div class="rate-display free">
              <el-icon class="star-icon"><Star /></el-icon>
              <span class="rate-number">0%</span>
              <span class="rate-label">免佣金政策</span>
            </div>
            
            <div class="features">
              <el-tag size="small" type="success">T+0实时结算</el-tag>
              <el-tag size="small" type="success">免提现费</el-tag>
              <el-tag size="small" type="success">免交易费</el-tag>
            </div>
            
            <div class="calculation">
              <p><strong>示例计算：</strong></p>
              <p>订单金额：¥1000</p>
              <p>平台佣金：¥1000 × 0% = ¥0</p>
              <p>实际到账：¥1000 - ¥0 = ¥1000</p>
              <p class="savings special">🎨 支持传统手工艺传承</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 非遗传承人免佣金 -->
      <div class="custom-card art-custom-card demo-card special">
        <div class="custom-card-content">
          <div class="merchant-header">
            <el-avatar :size="50" src="/images/heritage.png">🏛️</el-avatar>
            <div class="merchant-info">
              <h3>陈大师 - 非遗传承人</h3>
              <p>国家级非物质文化遗产传承人</p>
            </div>
          </div>
          
          <div class="settlement-details">
            <div class="rate-display free heritage">
              <el-icon class="star-icon"><Star /></el-icon>
              <span class="rate-number">0%</span>
              <span class="rate-label">免佣金政策</span>
            </div>
            
            <div class="features">
              <el-tag size="small" type="success">T+0实时结算</el-tag>
              <el-tag size="small" type="success">免提现费</el-tag>
              <el-tag size="small" type="success">免交易费</el-tag>
              <el-tag size="small" type="danger">专属通道</el-tag>
            </div>
            
            <div class="calculation">
              <p><strong>示例计算：</strong></p>
              <p>订单金额：¥1000</p>
              <p>平台佣金：¥1000 × 0% = ¥0</p>
              <p>实际到账：¥1000 - ¥0 = ¥1000</p>
              <p class="savings special heritage">🏛️ 保护非物质文化遗产</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 规则说明 -->
      <div class="custom-card art-custom-card demo-card info">
        <div class="custom-card-content">
          <div class="info-header">
            <el-icon :size="40" color="#409eff"><InfoFilled /></el-icon>
            <h3>结算规则说明</h3>
          </div>
          
          <div class="rule-list">
            <div class="rule-item">
              <el-icon color="#f56c6c"><Star /></el-icon>
              <span>手工艺人和非遗传承人享受<strong>永久免佣金</strong>政策</span>
            </div>
            <div class="rule-item">
              <el-icon color="#67c23a"><Check /></el-icon>
              <span>认证可在商户入驻前或入驻后进行，认证通过立即生效</span>
            </div>
            <div class="rule-item">
              <el-icon color="#e6a23c"><Check /></el-icon>
              <span>新商户前3个月享受<strong>2.0%优惠费率</strong></span>
            </div>
            <div class="rule-item">
              <el-icon color="#409eff"><Check /></el-icon>
              <span>企业商户享受<strong>2.8%优惠费率</strong></span>
            </div>
            <div class="rule-item">
              <el-icon color="#909399"><Check /></el-icon>
              <span>个人商户标准费率为<strong>3.5%</strong></span>
            </div>
            <div class="rule-item priority">
              <el-icon color="#e6a23c"><Warning /></el-icon>
              <span><strong>优先级：</strong>认证身份 > 新商户优惠 > 商户类型</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 费率对比图表 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <h3>📊 费率对比分析</h3>
        <div class="chart-container">
          <div class="chart-bar">
            <div class="bar-item">
              <div class="bar-label">个人商户</div>
              <div class="bar-bg">
                <div class="bar-fill" style="width: 100%; background: #909399;"></div>
              </div>
              <div class="bar-value">3.5%</div>
            </div>
            <div class="bar-item">
              <div class="bar-label">企业商户</div>
              <div class="bar-bg">
                <div class="bar-fill" style="width: 80%; background: #409eff;"></div>
              </div>
              <div class="bar-value">2.8%</div>
            </div>
            <div class="bar-item">
              <div class="bar-label">新商户</div>
              <div class="bar-bg">
                <div class="bar-fill" style="width: 57%; background: #e6a23c;"></div>
              </div>
              <div class="bar-value">2.0%</div>
            </div>
            <div class="bar-item">
              <div class="bar-label">手工艺人</div>
              <div class="bar-bg">
                <div class="bar-fill" style="width: 0%; background: #67c23a;"></div>
              </div>
              <div class="bar-value free">0%</div>
            </div>
            <div class="bar-item">
              <div class="bar-label">非遗传承人</div>
              <div class="bar-bg">
                <div class="bar-fill" style="width: 0%; background: #f56c6c;"></div>
              </div>
              <div class="bar-value free">0%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Star, InfoFilled, Check, Warning } from '@element-plus/icons-vue'

defineOptions({
  name: 'SettlementDemo'
})
</script>

<style scoped lang="scss">
.settlement-demo-page {
  padding-bottom: 20px;

  // 使用系统统一的卡片样式
  :deep(.custom-card) {
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 4px);
    box-shadow: none;
    margin-bottom: 20px;
  }

  .custom-card-content {
    padding: 20px;
  }

  h2 {
    margin: 0 0 8px 0;
    color: var(--art-text-gray-900);
  }

  p {
    margin: 0;
    color: var(--art-text-gray-600);
  }
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.demo-card {
  &.special {
    border: 2px solid #67c23a;
    background: linear-gradient(135deg, #f0f9ff, #ecfdf5);
  }

  &.info {
    border: 2px solid #409eff;
    background: linear-gradient(135deg, #eff6ff, #f0f9ff);
  }
}

.merchant-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .merchant-info {
    margin-left: 12px;

    h3 {
      margin: 0 0 4px 0;
      font-size: 16px;
      color: var(--art-text-gray-900);
    }

    p {
      margin: 0;
      font-size: 12px;
      color: var(--art-text-gray-600);
    }
  }
}

.settlement-details {
  .rate-display {
    text-align: center;
    padding: 16px;
    border-radius: 8px;
    background: var(--art-bg-gray-50);
    margin-bottom: 16px;
    position: relative;

    &.enterprise {
      background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    }

    &.new {
      background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    }

    &.free {
      background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
      
      &.heritage {
        background: linear-gradient(135deg, #fce4ec, #f8bbd9);
      }
    }

    .star-icon {
      position: absolute;
      top: 8px;
      right: 8px;
      color: #ffd700;
    }

    .rate-number {
      display: block;
      font-size: 28px;
      font-weight: bold;
      color: var(--art-text-gray-900);
    }

    .rate-label {
      display: block;
      font-size: 12px;
      color: var(--art-text-gray-600);
      margin-top: 4px;
    }
  }

  .features {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
  }

  .calculation {
    background: var(--art-bg-gray-50);
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;

    p {
      margin: 4px 0;
      
      &.savings {
        color: #67c23a;
        font-weight: 500;
        
        &.special {
          color: #f56c6c;
          
          &.heritage {
            color: #e91e63;
          }
        }
      }
    }
  }
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  h3 {
    margin: 0 0 0 12px;
    color: var(--art-text-gray-900);
  }
}

.rule-list {
  .rule-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;

    .el-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    span {
      color: var(--art-text-gray-700);
    }

    &.priority {
      background: var(--art-bg-yellow-50);
      padding: 8px;
      border-radius: 4px;
      border-left: 3px solid #e6a23c;
    }
  }
}

.chart-container {
  margin-top: 20px;
}

.chart-bar {
  .bar-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .bar-label {
      width: 80px;
      font-size: 12px;
      color: var(--art-text-gray-600);
    }

    .bar-bg {
      flex: 1;
      height: 20px;
      background: var(--art-bg-gray-100);
      border-radius: 10px;
      margin: 0 12px;
      overflow: hidden;

      .bar-fill {
        height: 100%;
        border-radius: 10px;
        transition: width 0.3s ease;
      }
    }

    .bar-value {
      width: 60px;
      text-align: right;
      font-size: 12px;
      font-weight: 500;
      color: var(--art-text-gray-700);

      &.free {
        color: #67c23a;
        font-weight: bold;
      }
    }
  }
}
</style>
