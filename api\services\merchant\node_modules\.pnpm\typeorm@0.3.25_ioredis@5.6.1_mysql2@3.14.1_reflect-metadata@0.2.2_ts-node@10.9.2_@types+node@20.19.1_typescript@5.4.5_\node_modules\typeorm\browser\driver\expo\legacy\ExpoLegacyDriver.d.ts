import { AbstractSqliteDriver } from "../../sqlite-abstract/AbstractSqliteDriver";
import { ExpoConnectionOptions } from "../ExpoConnectionOptions";
import { QueryRunner } from "../../../query-runner/QueryRunner";
import { DataSource } from "../../../data-source/DataSource";
import { ReplicationMode } from "../../types/ReplicationMode";
export declare class ExpoLegacyDriver extends AbstractSqliteDriver {
    options: ExpoConnectionOptions;
    constructor(connection: DataSource);
    /**
     * Closes connection with database.
     */
    disconnect(): Promise<void>;
    /**
     * Creates a query runner used to execute database queries.
     */
    createQueryRunner(mode: ReplicationMode): QueryRunner;
    /**
     * Creates connection with the database.
     */
    protected createDatabaseConnection(): Promise<void>;
}
