"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.I18nMiddleware = exports.I18nFilter = void 0;
const core_1 = require("@midwayjs/core");
const interface_1 = require("./interface");
const i18nService_1 = require("./i18nService");
const utils_1 = require("./utils");
let I18nFilter = class I18nFilter {
    match(value, req, res) {
        const saveLocale = req.getAttr(interface_1.I18N_ATTR_KEY);
        if (this.resolverConfig) {
            if (this.i18nConfig.writeCookie &&
                saveLocale &&
                this.resolverConfig.cookieField) {
                const cookieOptions = {
                    // make sure browser javascript can read the cookie
                    httpOnly: false,
                    maxAge: this.resolverConfig.cookieField.cookieMaxAge,
                    signed: false,
                    domain: this.resolverConfig.cookieField.cookieDomain,
                };
                res.cookie(this.resolverConfig.cookieField.fieldName, saveLocale, cookieOptions);
            }
        }
        return value;
    }
};
__decorate([
    (0, core_1.Config)('i18n.resolver'),
    __metadata("design:type", Object)
], I18nFilter.prototype, "resolverConfig", void 0);
__decorate([
    (0, core_1.Config)('i18n'),
    __metadata("design:type", Object)
], I18nFilter.prototype, "i18nConfig", void 0);
I18nFilter = __decorate([
    (0, core_1.Match)()
], I18nFilter);
exports.I18nFilter = I18nFilter;
let I18nMiddleware = class I18nMiddleware {
    resolve(app) {
        if (app.getFrameworkType() === core_1.MidwayFrameworkType.WEB_EXPRESS) {
            // add a filter for i18n cookie
            app.useFilter(I18nFilter);
            return async (req, res, next) => {
                const i18nService = await req.requestContext.getAsync(i18nService_1.MidwayI18nService);
                let requestLocale;
                if (this.resolverConfig) {
                    // get request locale from query/header/cookie
                    requestLocale =
                        req.query[this.resolverConfig.queryField] ||
                            req.cookies[this.resolverConfig.cookieField.fieldName];
                    if (!requestLocale) {
                        // Accept-Language: zh-CN,zh;q=0.5
                        // Accept-Language: zh-CN
                        let languages = req.acceptsLanguages();
                        if (languages) {
                            if (Array.isArray(languages)) {
                                if (languages[0] === '*') {
                                    languages = languages.slice(1);
                                }
                                if (languages.length > 0) {
                                    for (let i = 0; i < languages.length; i++) {
                                        const lang = (0, utils_1.formatLocale)(languages[i]);
                                        if (i18nService.hasAvailableLocale(lang)) {
                                            requestLocale = lang;
                                            break;
                                        }
                                    }
                                }
                            }
                            else {
                                requestLocale = languages;
                            }
                        }
                    }
                }
                if (requestLocale) {
                    i18nService.saveRequestLocale(requestLocale);
                }
                else {
                    i18nService.saveRequestLocale();
                }
                res.locals[this.i18nConfig.localsField] = (message, data) => {
                    return i18nService.translate(message, {
                        args: data,
                    });
                };
                return next();
            };
        }
        else {
            return async (ctx, next) => {
                if (this.resolverConfig) {
                    // get request locale from query/header/cookie
                    let requestLocale = ctx.query[this.resolverConfig.queryField] ||
                        ctx.cookies.get(this.resolverConfig.cookieField.fieldName, {
                            signed: false,
                        });
                    const i18nService = await ctx.requestContext.getAsync(i18nService_1.MidwayI18nService);
                    if (!requestLocale) {
                        // Accept-Language: zh-CN,zh;q=0.5
                        // Accept-Language: zh-CN
                        let languages = ctx.acceptsLanguages();
                        if (languages) {
                            if (Array.isArray(languages)) {
                                if (languages[0] === '*') {
                                    languages = languages.slice(1);
                                }
                                if (languages.length > 0) {
                                    for (let i = 0; i < languages.length; i++) {
                                        const lang = (0, utils_1.formatLocale)(languages[i]);
                                        if (i18nService.hasAvailableLocale(lang)) {
                                            requestLocale = lang;
                                            break;
                                        }
                                    }
                                }
                            }
                            else {
                                requestLocale = languages;
                            }
                        }
                    }
                    // save current locale
                    if (requestLocale) {
                        i18nService.saveRequestLocale(requestLocale);
                    }
                    else {
                        i18nService.saveRequestLocale();
                    }
                    if (ctx.locals) {
                        ctx.locals[this.i18nConfig.localsField] = (message, data) => {
                            return i18nService.translate(message, {
                                args: data,
                            });
                        };
                    }
                }
                // run next middleware and controller
                await next();
                // get need save locale
                const saveLocale = ctx.getAttr(interface_1.I18N_ATTR_KEY);
                if (this.resolverConfig) {
                    if (this.i18nConfig.writeCookie &&
                        saveLocale &&
                        this.resolverConfig.cookieField) {
                        const cookieOptions = {
                            // make sure browser javascript can read the cookie
                            httpOnly: false,
                            maxAge: this.resolverConfig.cookieField.cookieMaxAge,
                            signed: false,
                            domain: this.resolverConfig.cookieField.cookieDomain,
                            overwrite: true,
                        };
                        ctx.cookies.set(this.resolverConfig.cookieField.fieldName, saveLocale, cookieOptions);
                    }
                }
            };
        }
    }
    static getName() {
        return 'i18n';
    }
};
__decorate([
    (0, core_1.Config)('i18n.resolver'),
    __metadata("design:type", Object)
], I18nMiddleware.prototype, "resolverConfig", void 0);
__decorate([
    (0, core_1.Config)('i18n'),
    __metadata("design:type", Object)
], I18nMiddleware.prototype, "i18nConfig", void 0);
I18nMiddleware = __decorate([
    (0, core_1.Middleware)()
], I18nMiddleware);
exports.I18nMiddleware = I18nMiddleware;
//# sourceMappingURL=middleware.js.map