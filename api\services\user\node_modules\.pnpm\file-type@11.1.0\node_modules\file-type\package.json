{"name": "file-type", "version": "11.1.0", "description": "Detect the file type of a Buffer/Uint8Array/ArrayBuffer", "license": "MIT", "repository": "sindresorhus/file-type", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts", "util.js"], "keywords": ["mime", "file", "type", "archive", "image", "img", "pic", "picture", "flash", "photo", "video", "detect", "check", "is", "exif", "exe", "binary", "buffer", "uint8array", "jpg", "png", "gif", "webp", "flif", "cr2", "orf", "arw", "dng", "nef", "tif", "bmp", "jxr", "psd", "zip", "tar", "rar", "gz", "bz2", "7z", "dmg", "mp4", "mid", "mkv", "webm", "mov", "avi", "mpg", "mp2", "mp3", "m4a", "ogg", "opus", "flac", "wav", "amr", "pdf", "epub", "mobi", "swf", "rtf", "woff", "woff2", "eot", "ttf", "otf", "ico", "flv", "ps", "xz", "sqlite", "xpi", "cab", "deb", "ar", "rpm", "Z", "lz", "msi", "mxf", "mts", "wasm", "webassembly", "blend", "bpg", "docx", "pptx", "xlsx", "3gp", "jp2", "jpm", "jpx", "mj2", "aif", "odt", "ods", "odp", "xml", "heic", "wma", "ics", "glb", "pcap", "dsf", "lnk", "alias", "voc", "ac3", "3g2", "m4a", "m4b", "m4p", "m4v", "f4a", "f4b", "f4p", "f4v"], "devDependencies": {"@types/node": "^11.12.2", "ava": "^1.4.1", "pify": "^4.0.1", "read-chunk": "^3.2.0", "tsd": "^0.7.1", "xo": "^0.24.0"}}