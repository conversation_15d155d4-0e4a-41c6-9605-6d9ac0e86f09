# Message 消息推送微服务

本服务为消息推送（message）相关业务的独立微服务，基于 Midway + cool-admin 架构。

## 🏗️ 业务范围

消息微服务负责处理所有与消息推送相关的业务逻辑，包括：

### 核心功能
- **站内消息** - 用户消息、系统通知
- **设备推送** - 设备消息、推送绑定
- **消息状态管理** - 已读/未读、批量更新
- **消息统计** - 未读数、消息列表

### 业务特色
- **🔔 高并发** - 支持大规模消息推送
- **📡 多终端** - 支持多设备消息同步
- **📊 实时统计** - 提供消息数据统计分析
- **🚀 高性能** - 支持批量操作和并发处理

## 📁 目录结构

```
services/message/
├── src/
│   ├── config/                    # 配置文件
│   │   ├── config.default.ts      # 默认配置
│   │   ├── config.local.ts        # 本地开发配置
│   │   └── config.prod.ts         # 生产环境配置
│   ├── configuration.ts           # 服务配置
│   └── modules/message/           # 消息业务模块
│       ├── entity/                # 数据实体
│       ├── service/               # 业务逻辑服务
│       └── controller/            # 控制器
│           ├── admin/             # 管理端接口
│           └── app/               # 应用端接口
├── main.ts                        # 启动入口
├── bootstrap.js                   # 启动脚本
├── package.json                   # 依赖配置
└── README.md                      # 本文档
```

## 🚀 启动方法

### 1. 安装依赖

```bash
cd services/message
npm install
# 或
yarn install
# 或
pnpm install
```

### 2. 配置数据库

修改 `src/config/config.local.ts` 中的数据库配置：

```typescript
database: 'cool', // 本地开发使用共享数据库
// 或生产环境使用独立数据库
database: 'message_service_db', // 独立数据库
```

### 3. 启动服务

```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

### 4. 验证启动

服务启动后会显示：
```
✅ 消息微服务启动成功！
🚀 服务端口: 9804
📋 服务名称: message-service
🔔 消息推送独立管理
🔗 RPC通信已启用
```

## 🔧 服务配置

### 端口配置
- **开发环境**: 9804
- **生产环境**: 9804

### 数据库配置
- **本地开发**: 共享数据库 `cool`
- **生产环境**: 独立数据库 `message_service_db`

### Redis配置
- **数据库**: db: 13 (独立Redis数据库)

## 📡 RPC接口

消息微服务通过RPC暴露以下主要接口：

### 消息服务 (MessageInfoService)

```typescript
// 发送消息
sendMessage(userId: number, title: string, content: string, obj?: any)

// 获取未读消息数
unreadCount(userId: number)

// 批量标记已读
markRead(userId: number, ids: number[])

// 分页获取消息
page(query: any, option: any)
```

## 💻 调用示例

### 在主服务或其他微服务中调用消息微服务

```typescript
import { CoolRpc } from '@cool-midway/rpc';

@Provide()
export class SomeService {
  @Inject()
  rpc: CoolRpc;

  // 发送消息
  async sendMsg(userId: number, title: string, content: string) {
    await this.rpc.call(
      'message-service',
      'messageInfoService',
      'sendMessage',
      userId,
      title,
      content
    );
  }
}
```

## 🛡️ 安全特性

### 数据隔离
- 独立数据库存储消息数据
- Redis缓存使用独立db避免数据混淆

### 权限控制
- RPC接口只对内部服务开放
- 敏感操作需要多重验证

## 📊 监控指标

### 消息统计
- 总消息数
- 未读消息数
- 已读消息数
- 用户消息分布

## 🔄 架构特点

### 微服务优势
- **独立部署** - 可单独扩容和发布
- **技术栈灵活** - 可选择最适合的技术
- **故障隔离** - 不影响其他业务模块
- **团队分工** - 专门团队负责消息功能

### 通信方式
- **RPC调用** - 基于Moleculer的服务间通信
- **事件驱动** - 支持异步事件通知
- **降级处理** - 服务不可用时的降级策略

## 🎯 未来规划

1. **多渠道推送** - 集成短信、邮件、App推送等
2. **消息模板** - 支持自定义消息模板
3. **消息队列** - 支持异步大规模推送
4. **消息回执** - 支持消息已读/送达回执
5. **API网关** - 统一的API管理和限流

---

**服务端口**: 9804  
**服务名称**: message-service  
**数据库**: message_service_db (生产) / cool (开发)  
**Redis DB**: 13 