# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.1.0](https://github.com/midwayjs/cli/compare/serverless-v1.2.39...serverless-v2.1.0) (2023-05-30)


### Bug Fixes

* **command-core:** fix path process within formatTsError() and add types ([#329](https://github.com/midwayjs/cli/issues/329)) ([685e9b7](https://github.com/midwayjs/cli/commit/685e9b7dc316e0c852a99a9771a6044b2133b695))



## 2.0.14 (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))



## 2.0.6 (2022-12-09)



## 2.0.5 (2022-12-01)



## 2.0.4 (2022-11-23)


### Bug Fixes

* find ts-node ([87944ea](https://github.com/midwayjs/cli/commit/87944eaa7dbe077b10ed907775000a982d2220e3))



## 2.0.3 (2022-11-21)


### Bug Fixes

* npm argv is undefined ([520ddeb](https://github.com/midwayjs/cli/commit/520ddeb5000db3a3255891412935c84f63f02837))



## 2.0.2 (2022-11-17)


### Bug Fixes

* dev pack 302 redirect ([#302](https://github.com/midwayjs/cli/issues/302)) ([3fc3160](https://github.com/midwayjs/cli/commit/3fc3160dc65fcc8208611ecdd123e8a9db167f0c)), closes [midwayjs/midway#2473](https://github.com/midwayjs/midway/issues/2473) [midwayjs/midway#2010](https://github.com/midwayjs/midway/issues/2010)


### Features

* support plugin loading normally in monorepo project ([#304](https://github.com/midwayjs/cli/issues/304)) ([6f23e9c](https://github.com/midwayjs/cli/commit/6f23e9c8090d9826c68b874786925daf89f7adc1))



## 2.0.1 (2022-10-28)



## 2.0.1-beta.1 (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))



# 2.0.0 (2022-10-13)



# 2.0.0-beta.1 (2022-10-10)


### Bug Fixes

* remove unexists plugin ([225a601](https://github.com/midwayjs/cli/commit/225a601948cadd66e8afa17916e9fe47d440400c))


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* fix package remove mwcc ([546038f](https://github.com/midwayjs/cli/commit/546038ff3b2aa6a2580acf431638ab93362de251))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))



## 1.3.14 (2022-10-10)



## 1.3.13 (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))



## 1.3.13-beta.3 (2022-09-07)



## 1.3.5 (2022-05-25)



## 1.3.5-beta.3 (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))



## 1.3.4 (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))
* midwayjs/hooks[#322](https://github.com/midwayjs/cli/issues/322) ([#280](https://github.com/midwayjs/cli/issues/280)) ([1966928](https://github.com/midwayjs/cli/commit/196692895f68fa95000f16d49ec8bba9eb68f5e9))



## 1.3.3 (2022-04-20)



## 1.3.1 (2022-03-10)



## 1.3.1-beta.1 (2022-03-10)



## 1.2.97 (2022-01-24)


### Bug Fixes

* auto select npm ([#258](https://github.com/midwayjs/cli/issues/258)) ([f6e497c](https://github.com/midwayjs/cli/commit/f6e497c331af2f51855f98fa880bccc8acbbdb4e))



## 1.2.96 (2022-01-21)


### Bug Fixes

* fix findNpmModule with pnp ([#256](https://github.com/midwayjs/cli/issues/256)) ([6a0f423](https://github.com/midwayjs/cli/commit/6a0f423af4646250bd7073bdb105699fe296a72b))
* install exec ([#257](https://github.com/midwayjs/cli/issues/257)) ([1d59d23](https://github.com/midwayjs/cli/commit/1d59d23a8f6dfcd544c8b9e195b7edcbbe25df8b))



## 1.2.95 (2022-01-20)


### Bug Fixes

* serverless devs cnpm ([#253](https://github.com/midwayjs/cli/issues/253)) ([93641a8](https://github.com/midwayjs/cli/commit/93641a8d76066ffe0016f297814070974b0f728a))



## 1.2.94 (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))



## 1.2.93 (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))



## 1.2.92 (2021-12-03)


### Bug Fixes

* using https://registry.npmmirror.com instead https://registry.np… ([#237](https://github.com/midwayjs/cli/issues/237)) ([56ce275](https://github.com/midwayjs/cli/commit/56ce275d98db601944892229e0645fcbf2bdb602))



## 1.2.91 (2021-11-26)


### Features

* fast install nm ([#231](https://github.com/midwayjs/cli/issues/231)) ([9974ed5](https://github.com/midwayjs/cli/commit/9974ed5d26dd4eca9627a709c72c2b677c0b49c1))



## 1.2.86 (2021-11-04)



## 1.2.85 (2021-10-21)


### Features

* support command alias ([#218](https://github.com/midwayjs/cli/issues/218)) ([380338c](https://github.com/midwayjs/cli/commit/380338c1b0a92d85874cba16ff37a1711e137027))



## 1.2.84 (2021-09-27)


### Bug Fixes

* opti check ([#201](https://github.com/midwayjs/cli/issues/201)) ([bfffdc6](https://github.com/midwayjs/cli/commit/bfffdc60d22d3732591b868bb863918ec086b7fb))



## 1.2.82 (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))



## 1.2.79 (2021-08-11)


### Bug Fixes

* usage support child commands ([#174](https://github.com/midwayjs/cli/issues/174)) ([b50a49d](https://github.com/midwayjs/cli/commit/b50a49d1ffac39dd72cd439bcc8b01aa728836d7))



## 1.2.76 (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))



## 1.2.73 (2021-06-25)



## 1.2.72 (2021-06-18)


### Bug Fixes

* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))



## 1.2.69 (2021-06-01)


### Features

* support serverlessFunction decorator ([#116](https://github.com/midwayjs/cli/issues/116)) ([006b671](https://github.com/midwayjs/cli/commit/006b671d96f51b627b401808907c5331b8f71d3d))



## 1.2.68 (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))



## 1.2.65 (2021-04-23)


### Bug Fixes

* more serverless dev options ([#95](https://github.com/midwayjs/cli/issues/95)) ([c3b9a4f](https://github.com/midwayjs/cli/commit/c3b9a4fa7f9f0bf6d19420ff38bf1abb23e74e32))



## 1.2.54 (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))



## 1.2.51 (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))



## 1.2.50 (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))



## 1.2.48 (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))



## 1.2.46 (2021-03-05)


### Bug Fixes

* cli find nm ([#55](https://github.com/midwayjs/cli/issues/55)) ([2c2873c](https://github.com/midwayjs/cli/commit/2c2873c2c0684c69e8b02cda14faeaae4437d534))



## 1.2.45 (2021-03-04)



## 1.2.41 (2021-02-17)



## 1.2.40 (2021-02-03)



## 1.2.39-beta.5 (2021-02-03)





## [2.0.14](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))





## [2.0.14-beta.4](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.4) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([2e9bfd6](https://github.com/midwayjs/cli/commit/2e9bfd60e579984c30b4de54cb7285f7918a1f22))





## [2.0.14-beta.3](https://github.com/midwayjs/cli/compare/v2.0.14-beta.2...v2.0.14-beta.3) (2023-02-15)


### Bug Fixes

* output ([e14592f](https://github.com/midwayjs/cli/commit/e14592f80a49173a7fa3514a6612fe86098d574b))





## [2.0.14-beta.2](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.2) (2023-02-14)


### Bug Fixes

* prevent log ([2a60bc6](https://github.com/midwayjs/cli/commit/2a60bc6e9a5f75edff826e817565cb6822faba0e))


### Features

* output level ([7001121](https://github.com/midwayjs/cli/commit/7001121f30d0e1d40bde672bba1b6d0a264ed2e3))





## [2.0.6](https://github.com/midwayjs/cli/compare/v2.0.6-beta.3...v2.0.6) (2022-12-09)

**Note:** Version bump only for package @midwayjs/command-core





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/command-core





## [2.0.4](https://github.com/midwayjs/cli/compare/v2.0.3...v2.0.4) (2022-11-23)


### Bug Fixes

* find ts-node ([87944ea](https://github.com/midwayjs/cli/commit/87944eaa7dbe077b10ed907775000a982d2220e3))





## [2.0.3](https://github.com/midwayjs/cli/compare/v2.0.2...v2.0.3) (2022-11-21)


### Bug Fixes

* npm argv is undefined ([520ddeb](https://github.com/midwayjs/cli/commit/520ddeb5000db3a3255891412935c84f63f02837))





## [2.0.2](https://github.com/midwayjs/cli/compare/v2.0.1...v2.0.2) (2022-11-17)


### Bug Fixes

* dev pack 302 redirect ([#302](https://github.com/midwayjs/cli/issues/302)) ([3fc3160](https://github.com/midwayjs/cli/commit/3fc3160dc65fcc8208611ecdd123e8a9db167f0c)), closes [midwayjs/midway#2473](https://github.com/midwayjs/midway/issues/2473) [midwayjs/midway#2010](https://github.com/midwayjs/midway/issues/2010)


### Features

* support plugin loading normally in monorepo project ([#304](https://github.com/midwayjs/cli/issues/304)) ([6f23e9c](https://github.com/midwayjs/cli/commit/6f23e9c8090d9826c68b874786925daf89f7adc1))





## [2.0.1](https://github.com/midwayjs/cli/compare/v2.0.1-beta.1...v2.0.1) (2022-10-28)

**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core





## [2.0.1-beta.1](https://github.com/midwayjs/cli/compare/v2.0.0...v2.0.1-beta.1) (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))





# [2.0.0](https://github.com/midwayjs/cli/compare/v1.3.15...v2.0.0) (2022-10-13)



# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)


### Bug Fixes

* remove unexists plugin ([225a601](https://github.com/midwayjs/cli/commit/225a601948cadd66e8afa17916e9fe47d440400c))


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* fix package remove mwcc ([546038f](https://github.com/midwayjs/cli/commit/546038ff3b2aa6a2580acf431638ab93362de251))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))





# [2.0.0](https://github.com/midwayjs/cli/compare/v2.0.0-beta.1...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/command-core





# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)


### Bug Fixes

* remove unexists plugin ([225a601](https://github.com/midwayjs/cli/commit/225a601948cadd66e8afa17916e9fe47d440400c))


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* fix package remove mwcc ([546038f](https://github.com/midwayjs/cli/commit/546038ff3b2aa6a2580acf431638ab93362de251))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))





## [1.3.14](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14) (2022-10-10)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.14-beta.10](https://github.com/midwayjs/cli/compare/v1.3.14-beta.9...v1.3.14-beta.10) (2022-09-30)


### Bug Fixes

* command core deps ([a1c0d34](https://github.com/midwayjs/cli/commit/a1c0d3418273c68addbd45ec7296783b6528befe))





## [1.3.14-beta.9](https://github.com/midwayjs/cli/compare/v1.3.14-beta.8...v1.3.14-beta.9) (2022-09-27)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.14-beta.8](https://github.com/midwayjs/cli/compare/v1.3.14-beta.7...v1.3.14-beta.8) (2022-09-27)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.14-beta.7](https://github.com/midwayjs/cli/compare/v1.3.14-beta.6...v1.3.14-beta.7) (2022-09-27)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.14-beta.6](https://github.com/midwayjs/cli/compare/v1.3.14-beta.5...v1.3.14-beta.6) (2022-09-26)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13-beta.4](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13-beta.4) (2022-09-07)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.13-beta.3](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.13-beta.3) (2022-09-07)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13) (2022-09-07)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.13-beta.2](https://github.com/midwayjs/cli/compare/v1.3.13-beta.1...v1.3.13-beta.2) (2022-09-06)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.13-beta.1](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13-beta.1) (2022-08-29)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.12](https://github.com/midwayjs/cli/compare/v1.3.12-beta.3...v1.3.12) (2022-08-25)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.5](https://github.com/midwayjs/cli/compare/v1.3.5-beta.3...v1.3.5) (2022-05-25)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.5-beta.3](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.3) (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))





## [1.3.5-beta.2](https://github.com/midwayjs/cli/compare/v1.3.5-beta.1...v1.3.5-beta.2) (2022-05-24)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.4](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4) (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))
* midwayjs/hooks[#322](https://github.com/midwayjs/cli/issues/322) ([#280](https://github.com/midwayjs/cli/issues/280)) ([1966928](https://github.com/midwayjs/cli/commit/196692895f68fa95000f16d49ec8bba9eb68f5e9))





## [1.3.4-beta.1](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4-beta.1) (2022-04-21)


### Bug Fixes

* cli time tick ([ca21feb](https://github.com/midwayjs/cli/commit/ca21feb39bb64e7f3af274a3ba6ae36143f113ab))





## [1.3.3](https://github.com/midwayjs/cli/compare/v1.3.2...v1.3.3) (2022-04-20)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.1](https://github.com/midwayjs/cli/compare/v1.3.1-beta.1...v1.3.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/command-core





## [1.3.1-beta.1](https://github.com/midwayjs/cli/compare/v1.3.0...v1.3.1-beta.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.97](https://github.com/midwayjs/cli/compare/v1.2.96...v1.2.97) (2022-01-24)


### Bug Fixes

* auto select npm ([#258](https://github.com/midwayjs/cli/issues/258)) ([f6e497c](https://github.com/midwayjs/cli/commit/f6e497c331af2f51855f98fa880bccc8acbbdb4e))





## [1.2.96](https://github.com/midwayjs/cli/compare/v1.2.95...v1.2.96) (2022-01-21)


### Bug Fixes

* fix findNpmModule with pnp ([#256](https://github.com/midwayjs/cli/issues/256)) ([6a0f423](https://github.com/midwayjs/cli/commit/6a0f423af4646250bd7073bdb105699fe296a72b))
* install exec ([#257](https://github.com/midwayjs/cli/issues/257)) ([1d59d23](https://github.com/midwayjs/cli/commit/1d59d23a8f6dfcd544c8b9e195b7edcbbe25df8b))





## [1.2.95](https://github.com/midwayjs/cli/compare/v1.2.94...v1.2.95) (2022-01-20)


### Bug Fixes

* serverless devs cnpm ([#253](https://github.com/midwayjs/cli/issues/253)) ([93641a8](https://github.com/midwayjs/cli/commit/93641a8d76066ffe0016f297814070974b0f728a))





## [1.2.94](https://github.com/midwayjs/cli/compare/v1.2.93...v1.2.94) (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))





## [1.2.93](https://github.com/midwayjs/cli/compare/v1.2.92...v1.2.93) (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))





## [1.2.92](https://github.com/midwayjs/cli/compare/v1.2.91...v1.2.92) (2021-12-03)


### Bug Fixes

* using https://registry.npmmirror.com instead https://registry.np… ([#237](https://github.com/midwayjs/cli/issues/237)) ([56ce275](https://github.com/midwayjs/cli/commit/56ce275d98db601944892229e0645fcbf2bdb602))





## [1.2.91](https://github.com/midwayjs/cli/compare/v1.2.90...v1.2.91) (2021-11-26)


### Features

* fast install nm ([#231](https://github.com/midwayjs/cli/issues/231)) ([9974ed5](https://github.com/midwayjs/cli/commit/9974ed5d26dd4eca9627a709c72c2b677c0b49c1))





## [1.2.86](https://github.com/midwayjs/cli/compare/v1.2.85...v1.2.86) (2021-11-04)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.85](https://github.com/midwayjs/cli/compare/v1.2.84...v1.2.85) (2021-10-21)


### Features

* support command alias ([#218](https://github.com/midwayjs/cli/issues/218)) ([380338c](https://github.com/midwayjs/cli/commit/380338c1b0a92d85874cba16ff37a1711e137027))





## [1.2.84](https://github.com/midwayjs/cli/compare/v1.2.83...v1.2.84) (2021-09-27)


### Bug Fixes

* opti check ([#201](https://github.com/midwayjs/cli/issues/201)) ([bfffdc6](https://github.com/midwayjs/cli/commit/bfffdc60d22d3732591b868bb863918ec086b7fb))





## [1.2.82](https://github.com/midwayjs/cli/compare/v1.2.81...v1.2.82) (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))





## [1.2.79](https://github.com/midwayjs/cli/compare/v1.2.78...v1.2.79) (2021-08-11)


### Bug Fixes

* usage support child commands ([#174](https://github.com/midwayjs/cli/issues/174)) ([b50a49d](https://github.com/midwayjs/cli/commit/b50a49d1ffac39dd72cd439bcc8b01aa728836d7))





## [1.2.76](https://github.com/midwayjs/cli/compare/v1.2.75...v1.2.76) (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))





## [1.2.73](https://github.com/midwayjs/cli/compare/v1.2.73-beta.1...v1.2.73) (2021-06-25)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.72](https://github.com/midwayjs/cli/compare/v1.2.71...v1.2.72) (2021-06-18)


### Bug Fixes

* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))





## [1.2.69](https://github.com/midwayjs/cli/compare/v1.2.68...v1.2.69) (2021-06-01)


### Features

* support serverlessFunction decorator ([#116](https://github.com/midwayjs/cli/issues/116)) ([006b671](https://github.com/midwayjs/cli/commit/006b671d96f51b627b401808907c5331b8f71d3d))





## [1.2.68](https://github.com/midwayjs/cli/compare/v1.2.67...v1.2.68) (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))





## [1.2.65](https://github.com/midwayjs/cli/compare/v1.2.63...v1.2.65) (2021-04-23)


### Bug Fixes

* more serverless dev options ([#95](https://github.com/midwayjs/cli/issues/95)) ([c3b9a4f](https://github.com/midwayjs/cli/commit/c3b9a4fa7f9f0bf6d19420ff38bf1abb23e74e32))





## [1.2.54](https://github.com/midwayjs/cli/compare/v1.2.53...v1.2.54) (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))





## [1.2.51](https://github.com/midwayjs/cli/compare/v1.2.50...v1.2.51) (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))





## [1.2.50](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.50) (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))







**Note:** Version bump only for package @midwayjs/command-core





## [1.2.49](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.49) (2021-03-06)

**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core





## [1.2.48](https://github.com/midwayjs/cli/compare/v1.2.46...v1.2.48) (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))





## [1.2.46](https://github.com/midwayjs/cli/compare/v1.2.45...v1.2.46) (2021-03-05)


### Bug Fixes

* cli find nm ([#55](https://github.com/midwayjs/cli/issues/55)) ([2c2873c](https://github.com/midwayjs/cli/commit/2c2873c2c0684c69e8b02cda14faeaae4437d534))





## [1.2.45](https://github.com/midwayjs/cli/compare/v1.2.44...v1.2.45) (2021-03-04)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.41](https://github.com/midwayjs/cli/compare/v1.2.40...v1.2.41) (2021-02-17)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.40](https://github.com/midwayjs/cli/compare/v1.2.39-beta.5...v1.2.40) (2021-02-03)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.37](https://github.com/midwayjs/cli/compare/v1.2.35...v1.2.37) (2021-01-08)


### Bug Fixes

* support package diagnostics & tsConfig config ([#38](https://github.com/midwayjs/cli/issues/38)) ([c499d14](https://github.com/midwayjs/cli/commit/c499d145f9cabf427877ec8ea65aea8ead42b9cd))





## [1.2.35](https://github.com/midwayjs/cli/compare/v1.2.33...v1.2.35) (2020-12-24)


### Bug Fixes

* copy file error catch ([8d2097c](https://github.com/midwayjs/cli/commit/8d2097c538f22ed6050c85d1c250436e0c2c71c1))
* cover deps ([#37](https://github.com/midwayjs/cli/issues/37)) ([5d28b8b](https://github.com/midwayjs/cli/commit/5d28b8b79265de521159d6ea543a7adb6d9890e2))





## [1.2.34](https://github.com/midwayjs/cli/compare/v1.2.34-beta.2...v1.2.34) (2020-12-20)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.32](https://github.com/midwayjs/cli/compare/v1.2.32-beta...v1.2.32) (2020-12-08)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.31](https://github.com/midwayjs/cli/compare/v1.2.30...v1.2.31) (2020-12-03)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.30](https://github.com/midwayjs/cli/compare/v1.2.30-beta...v1.2.30) (2020-11-30)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.29](https://github.com/midwayjs/cli/compare/serverless-v1.2.28...serverless-v1.2.29) (2020-11-18)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.28](https://github.com/midwayjs/cli/compare/serverless-v1.2.27...serverless-v1.2.28) (2020-11-18)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.27](https://github.com/midwayjs/cli/compare/serverless-v1.2.26...serverless-v1.2.27) (2020-11-17)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.26](https://github.com/midwayjs/cli/compare/serverless-v1.2.21...serverless-v1.2.26) (2020-11-17)


### Bug Fixes

* build copy file path ([#23](https://github.com/midwayjs/cli/issues/23)) ([ff94951](https://github.com/midwayjs/cli/commit/ff9495147571bb495fc2edd21ee09e7dbb323333))



## 1.2.25 (2020-11-12)



## 1.2.25-beta.1 (2020-11-12)



## 1.2.23 (2020-11-11)



## 1.2.23-beta.3 (2020-11-10)



## 1.2.23-beta.2 (2020-10-30)



## 1.2.23-beta.1 (2020-10-26)


### Bug Fixes

* dev pack start auto remove cache ([#17](https://github.com/midwayjs/cli/issues/17)) ([c2c090b](https://github.com/midwayjs/cli/commit/c2c090b4f6419ea6fe3cdde448e9ab07b795dfd2))
* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.25](https://github.com/midwayjs/cli/compare/v1.2.25-beta.1...v1.2.25) (2020-11-12)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.25-beta.1](https://github.com/midwayjs/cli/compare/v1.2.24-beta.1...v1.2.25-beta.1) (2020-11-12)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.23](https://github.com/midwayjs/cli/compare/v1.2.23-beta.3...v1.2.23) (2020-11-11)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.23-beta.3](https://github.com/midwayjs/cli/compare/v1.2.23-beta.2...v1.2.23-beta.3) (2020-11-10)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.23-beta.2](https://github.com/midwayjs/cli/compare/v1.2.23-beta.1...v1.2.23-beta.2) (2020-10-30)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.23-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.23-beta.1) (2020-10-26)


### Bug Fixes

* dev pack start auto remove cache ([#17](https://github.com/midwayjs/cli/issues/17)) ([c2c090b](https://github.com/midwayjs/cli/commit/c2c090b4f6419ea6fe3cdde448e9ab07b795dfd2))
* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.22](https://github.com/midwayjs/cli/compare/v1.2.22-beta.1...v1.2.22) (2020-10-21)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.22-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.22-beta.1) (2020-10-21)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.21](https://github.com/midwayjs/cli/compare/serverless-v1.2.19...serverless-v1.2.21) (2020-10-20)



## 1.2.20 (2020-10-19)



## 1.2.20-beta.5 (2020-10-19)



## 1.2.20-beta.4 (2020-10-19)


### Bug Fixes

* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/cli/issues/12)) ([543ca3c](https://github.com/midwayjs/cli/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))
* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20](https://github.com/midwayjs/cli/compare/v1.2.20-beta.5...v1.2.20) (2020-10-19)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.20-beta.5](https://github.com/midwayjs/cli/compare/v1.2.20-beta.4...v1.2.20-beta.5) (2020-10-19)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.20-beta.4](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.4) (2020-10-19)


### Bug Fixes

* faas deploy ([#4](https://github.com/midwayjs/cli/issues/4)) ([1623df9](https://github.com/midwayjs/cli/commit/1623df9154bf48ebee7131e24ce98402f8839ec4))
* mw new ([#6](https://github.com/midwayjs/cli/issues/6)) ([6afd197](https://github.com/midwayjs/cli/commit/6afd197ef456f7f701660920312cdc5797a25465))
* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/cli/issues/12)) ([543ca3c](https://github.com/midwayjs/cli/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))
* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20-beta.3](https://github.com/midwayjs/cli/compare/v1.2.20-beta.2...v1.2.20-beta.3) (2020-10-19)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.20-beta.2](https://github.com/midwayjs/cli/compare/v1.2.20-beta.1...v1.2.20-beta.2) (2020-10-19)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.20-beta.1](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.1) (2020-10-19)


### Bug Fixes

* faas deploy ([#4](https://github.com/midwayjs/cli/issues/4)) ([1623df9](https://github.com/midwayjs/cli/commit/1623df9154bf48ebee7131e24ce98402f8839ec4))
* mw new ([#6](https://github.com/midwayjs/cli/issues/6)) ([6afd197](https://github.com/midwayjs/cli/commit/6afd197ef456f7f701660920312cdc5797a25465))
* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/cli/issues/12)) ([543ca3c](https://github.com/midwayjs/cli/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))





## [1.2.18](https://github.com/midwayjs/cli/compare/serverless-v1.2.17...serverless-v1.2.18) (2020-09-23)


### Bug Fixes

* mw new ([#6](https://github.com/midwayjs/cli/issues/6)) ([6afd197](https://github.com/midwayjs/cli/commit/6afd197ef456f7f701660920312cdc5797a25465))





## [1.2.17](https://github.com/midwayjs/cli/compare/serverless-v1.2.16...serverless-v1.2.17) (2020-09-23)

**Note:** Version bump only for package @midwayjs/command-core





## [1.2.16](https://github.com/midwayjs/cli/compare/serverless-v1.2.15...serverless-v1.2.16) (2020-09-22)


### Bug Fixes

* faas deploy ([#4](https://github.com/midwayjs/cli/issues/4)) ([1623df9](https://github.com/midwayjs/cli/commit/1623df9154bf48ebee7131e24ce98402f8839ec4))





## 1.2.15 (2020-09-22)



## 1.0.4 (2020-09-20)



## 1.0.3 (2020-09-20)



## 1.0.1 (2020-09-17)


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))



# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))
* new ([7ca760c](https://github.com/midwayjs/cli/commit/7ca760c059715220c738a46a78d09d288a767f6d))





## [1.0.4](https://github.com/midwayjs/cli/compare/v1.0.3...v1.0.4) (2020-09-20)

**Note:** Version bump only for package @midwayjs/command-core





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)

**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core





## [1.0.1](https://github.com/midwayjs/cli/compare/v1.0.0...v1.0.1) (2020-09-17)


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))





# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))
* new ([7ca760c](https://github.com/midwayjs/cli/commit/7ca760c059715220c738a46a78d09d288a767f6d))





# [1.0.0](https://github.com/midwayjs/cli/compare/v1.1.0...v1.0.0) (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))







**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core







**Note:** Version bump only for package @midwayjs/command-core





# [1.0.0](https://github.com/midwayjs/bin/compare/v1.1.0...v1.0.0) (2020-09-17)

**Note:** Version bump only for package @midwayjs/command-core





# 1.1.0 (2020-09-16)


### Features

* new ([7ca760c](https://github.com/midwayjs/bin/commit/7ca760c059715220c738a46a78d09d288a767f6d))





## [1.2.11](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.10...serverless-v1.2.11) (2020-09-04)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.2.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.9...serverless-v1.2.10) (2020-08-30)


### Bug Fixes

* add lock init for egg app ([#622](https://github.com/midwayjs/midway-faas/issues/622)) ([ccb5fe5](https://github.com/midwayjs/midway-faas/commit/ccb5fe52778f19b3e66dc1d727cc38e09f2c3ed6))





## [1.2.3](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.2...serverless-v1.2.3) (2020-08-18)


### Bug Fixes

* provider check ([#605](https://github.com/midwayjs/midway-faas/issues/605)) ([c2808bf](https://github.com/midwayjs/midway-faas/commit/c2808bf0542d0fc051a61d8a5e88a2fe90f5bf91))





## [1.2.2](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.1...serverless-v1.2.2) (2020-08-18)


### Bug Fixes

* createLogger & commond-core support multi provider ([#604](https://github.com/midwayjs/midway-faas/issues/604)) ([c1748f0](https://github.com/midwayjs/midway-faas/commit/c1748f00516a86624fab3cd64d2a61052513cbf3))





# [1.2.0](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.21...serverless-v1.2.0) (2020-08-17)


### Features

* add hooks ([#601](https://github.com/midwayjs/midway-faas/issues/601)) ([e9973f1](https://github.com/midwayjs/midway-faas/commit/e9973f110e3654d619ff7bc4020608c2082e47ae))





## [1.1.21](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.20...serverless-v1.1.21) (2020-08-12)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.18](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.17...serverless-v1.1.18) (2020-08-11)


### Bug Fixes

* fix windows word when output ([#591](https://github.com/midwayjs/midway-faas/issues/591)) ([1ab9a7f](https://github.com/midwayjs/midway-faas/commit/1ab9a7f009d5b80db077e500ebc3302fa92c2664))





## [1.1.16](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.15...serverless-v1.1.16) (2020-08-05)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.15](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.14...serverless-v1.1.15) (2020-08-05)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.14](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.13...serverless-v1.1.14) (2020-08-04)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.13](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.12...serverless-v1.1.13) (2020-08-03)


### Bug Fixes

* fc auto domain ([#580](https://github.com/midwayjs/midway-faas/issues/580)) ([f8bf9e0](https://github.com/midwayjs/midway-faas/commit/f8bf9e05922b154285ee29f1a65b59d9d448cf5b))





## [1.1.12](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.11...serverless-v1.1.12) (2020-07-29)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.9...serverless-v1.1.10) (2020-07-28)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.9](https://github.com/midwayjs/midway-faas/compare/v1.1.4...v1.1.9) (2020-07-28)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.8](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.7...serverless-v1.1.8) (2020-07-27)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.7](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.6...serverless-v1.1.7) (2020-07-26)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.6](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.5...serverless-v1.1.6) (2020-07-26)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.5](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.3...serverless-v1.1.5) (2020-07-24)



## 1.1.4 (2020-07-24)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.1.4](https://github.com/midwayjs/midway-faas/compare/v1.0.8...v1.1.4) (2020-07-24)


### Bug Fixes

* ts-mode ([#552](https://github.com/midwayjs/midway-faas/issues/552)) ([d8f231c](https://github.com/midwayjs/midway-faas/commit/d8f231c8a0ad5c50d42809e915c1669b67902305))


### Features

* progressive ([#551](https://github.com/midwayjs/midway-faas/issues/551)) ([7b0060e](https://github.com/midwayjs/midway-faas/commit/7b0060e642de8e62ee07d9f4ca8c9aa569f3f34f))
* Support application layer ([#534](https://github.com/midwayjs/midway-faas/issues/534)) ([7a141c0](https://github.com/midwayjs/midway-faas/commit/7a141c0c9404dc20d4d146a14e01dff404943142))





## [1.1.3](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.2...serverless-v1.1.3) (2020-07-24)


### Bug Fixes

* ts-mode ([#552](https://github.com/midwayjs/midway-faas/issues/552)) ([d8f231c](https://github.com/midwayjs/midway-faas/commit/d8f231c8a0ad5c50d42809e915c1669b67902305))


### Features

* progressive ([#551](https://github.com/midwayjs/midway-faas/issues/551)) ([7b0060e](https://github.com/midwayjs/midway-faas/commit/7b0060e642de8e62ee07d9f4ca8c9aa569f3f34f))





## [1.1.2](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.1...serverless-v1.1.2) (2020-07-23)

**Note:** Version bump only for package @midwayjs/fcli-command-core





# [1.1.0](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.11...serverless-v1.1.0) (2020-07-21)


### Features

* Support application layer ([#534](https://github.com/midwayjs/midway-faas/issues/534)) ([7a141c0](https://github.com/midwayjs/midway-faas/commit/7a141c0c9404dc20d4d146a14e01dff404943142))





## [1.0.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.9...serverless-v1.0.10) (2020-07-16)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## 1.0.7 (2020-07-14)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* copyFiles to protected ([#86](https://github.com/midwayjs/midway-faas/issues/86)) ([bf54687](https://github.com/midwayjs/midway-faas/commit/bf5468776901c7c93d61eab8a38d8bd6a361eb01))
* core hooks ([#104](https://github.com/midwayjs/midway-faas/issues/104)) ([ac6b574](https://github.com/midwayjs/midway-faas/commit/ac6b574b7bab95358d5f14214711390959ad56b6))
* core support stop ([#111](https://github.com/midwayjs/midway-faas/issues/111)) ([4ecf423](https://github.com/midwayjs/midway-faas/commit/4ecf423dc2b0366b90b2d40b39a229421d4ee006)), closes [#107](https://github.com/midwayjs/midway-faas/issues/107)
* dev pack clean ([#141](https://github.com/midwayjs/midway-faas/issues/141)) ([d2731c8](https://github.com/midwayjs/midway-faas/commit/d2731c8bf8c662e06131eec80bdbc20474e39d89))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* Fix [#47](https://github.com/midwayjs/midway-faas/issues/47) ([#48](https://github.com/midwayjs/midway-faas/issues/48)) ([9060569](https://github.com/midwayjs/midway-faas/commit/90605695ab3158a2c2d3423ae4529416359908b7))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* get npm root ([#60](https://github.com/midwayjs/midway-faas/issues/60)) ([a3e8d3e](https://github.com/midwayjs/midway-faas/commit/a3e8d3ea0b4f68dc71469df8c6c7793cab3e491c))
* invoke bug ([#110](https://github.com/midwayjs/midway-faas/issues/110)) ([7c2d000](https://github.com/midwayjs/midway-faas/commit/7c2d000404cb185ff8597e9b3acea2a0955f1bda))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke hooks ([#113](https://github.com/midwayjs/midway-faas/issues/113)) ([765ec7e](https://github.com/midwayjs/midway-faas/commit/765ec7e259775360ff0c26755bdf78843b3658bf))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* load spec ([#119](https://github.com/midwayjs/midway-faas/issues/119)) ([66df38c](https://github.com/midwayjs/midway-faas/commit/66df38c221c033cf1ba9e28fcdc9953e33aabf34))
* npm install --no-save ([#521](https://github.com/midwayjs/midway-faas/issues/521)) ([0cf62d1](https://github.com/midwayjs/midway-faas/commit/0cf62d107968750c7dfaba61823ab4b7cad24f15))
* process ([#120](https://github.com/midwayjs/midway-faas/issues/120)) ([53da26f](https://github.com/midwayjs/midway-faas/commit/53da26f1937b2dc20d6068c89e6b5addb16fb655))
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* remove command usage ([#88](https://github.com/midwayjs/midway-faas/issues/88)) ([c4c6050](https://github.com/midwayjs/midway-faas/commit/c4c60507b12112eb34a16b2af06c8f24353c9d80))
* repair no provider & change doc to faas-cli ([#30](https://github.com/midwayjs/midway-faas/issues/30)) ([e72b4c5](https://github.com/midwayjs/midway-faas/commit/e72b4c538349985ac934cfe291e6e755f99cec92))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* using ws instead of websocket ([#93](https://github.com/midwayjs/midway-faas/issues/93)) ([01c0b38](https://github.com/midwayjs/midway-faas/commit/01c0b3817a55bd7d6c8326d33b076645ca9f885e))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add dev pack ([#134](https://github.com/midwayjs/midway-faas/issues/134)) ([cd08f54](https://github.com/midwayjs/midway-faas/commit/cd08f54859da80f517cb37f99857679286f10f0f))
* aws support ([#526](https://github.com/midwayjs/midway-faas/issues/526)) ([9da022e](https://github.com/midwayjs/midway-faas/commit/9da022ecdf1e7770c21705131679940adc67ff3c))
* code analysis ([#98](https://github.com/midwayjs/midway-faas/issues/98)) ([2651faa](https://github.com/midwayjs/midway-faas/commit/2651faa55155b3e00be875275536039792473f2c))
* command core store ([#72](https://github.com/midwayjs/midway-faas/issues/72)) ([10e68a9](https://github.com/midwayjs/midway-faas/commit/10e68a9a1cf096628eabb7b2b3f2c34dbdd14f3a))
* command-core support user lifecycle config ([#167](https://github.com/midwayjs/midway-faas/issues/167)) ([ebb84a2](https://github.com/midwayjs/midway-faas/commit/ebb84a2c215d6af504841d448db14149cac4d155))
* introduce `experimentalFeatures` root option ([#123](https://github.com/midwayjs/midway-faas/issues/123)) ([e20417f](https://github.com/midwayjs/midway-faas/commit/e20417f11e8bcdada52ca9835adeec6c16b67c06))
* new invoke ([#101](https://github.com/midwayjs/midway-faas/issues/101)) ([4c13a69](https://github.com/midwayjs/midway-faas/commit/4c13a695d9443f0f6683cad28967157f0d2ab496))





## [1.0.6](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.5...serverless-v1.0.6) (2020-07-13)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.0.4](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.3...serverless-v1.0.4) (2020-07-08)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [1.0.3](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.2...serverless-v1.0.3) (2020-07-07)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## 1.0.2 (2020-07-06)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* copyFiles to protected ([#86](https://github.com/midwayjs/midway-faas/issues/86)) ([bf54687](https://github.com/midwayjs/midway-faas/commit/bf5468776901c7c93d61eab8a38d8bd6a361eb01))
* core hooks ([#104](https://github.com/midwayjs/midway-faas/issues/104)) ([ac6b574](https://github.com/midwayjs/midway-faas/commit/ac6b574b7bab95358d5f14214711390959ad56b6))
* core support stop ([#111](https://github.com/midwayjs/midway-faas/issues/111)) ([4ecf423](https://github.com/midwayjs/midway-faas/commit/4ecf423dc2b0366b90b2d40b39a229421d4ee006)), closes [#107](https://github.com/midwayjs/midway-faas/issues/107)
* dev pack clean ([#141](https://github.com/midwayjs/midway-faas/issues/141)) ([d2731c8](https://github.com/midwayjs/midway-faas/commit/d2731c8bf8c662e06131eec80bdbc20474e39d89))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* Fix [#47](https://github.com/midwayjs/midway-faas/issues/47) ([#48](https://github.com/midwayjs/midway-faas/issues/48)) ([9060569](https://github.com/midwayjs/midway-faas/commit/90605695ab3158a2c2d3423ae4529416359908b7))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* get npm root ([#60](https://github.com/midwayjs/midway-faas/issues/60)) ([a3e8d3e](https://github.com/midwayjs/midway-faas/commit/a3e8d3ea0b4f68dc71469df8c6c7793cab3e491c))
* invoke bug ([#110](https://github.com/midwayjs/midway-faas/issues/110)) ([7c2d000](https://github.com/midwayjs/midway-faas/commit/7c2d000404cb185ff8597e9b3acea2a0955f1bda))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke hooks ([#113](https://github.com/midwayjs/midway-faas/issues/113)) ([765ec7e](https://github.com/midwayjs/midway-faas/commit/765ec7e259775360ff0c26755bdf78843b3658bf))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* load spec ([#119](https://github.com/midwayjs/midway-faas/issues/119)) ([66df38c](https://github.com/midwayjs/midway-faas/commit/66df38c221c033cf1ba9e28fcdc9953e33aabf34))
* npm install --no-save ([#521](https://github.com/midwayjs/midway-faas/issues/521)) ([0cf62d1](https://github.com/midwayjs/midway-faas/commit/0cf62d107968750c7dfaba61823ab4b7cad24f15))
* process ([#120](https://github.com/midwayjs/midway-faas/issues/120)) ([53da26f](https://github.com/midwayjs/midway-faas/commit/53da26f1937b2dc20d6068c89e6b5addb16fb655))
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* remove command usage ([#88](https://github.com/midwayjs/midway-faas/issues/88)) ([c4c6050](https://github.com/midwayjs/midway-faas/commit/c4c60507b12112eb34a16b2af06c8f24353c9d80))
* repair no provider & change doc to faas-cli ([#30](https://github.com/midwayjs/midway-faas/issues/30)) ([e72b4c5](https://github.com/midwayjs/midway-faas/commit/e72b4c538349985ac934cfe291e6e755f99cec92))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* using ws instead of websocket ([#93](https://github.com/midwayjs/midway-faas/issues/93)) ([01c0b38](https://github.com/midwayjs/midway-faas/commit/01c0b3817a55bd7d6c8326d33b076645ca9f885e))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add dev pack ([#134](https://github.com/midwayjs/midway-faas/issues/134)) ([cd08f54](https://github.com/midwayjs/midway-faas/commit/cd08f54859da80f517cb37f99857679286f10f0f))
* code analysis ([#98](https://github.com/midwayjs/midway-faas/issues/98)) ([2651faa](https://github.com/midwayjs/midway-faas/commit/2651faa55155b3e00be875275536039792473f2c))
* command core store ([#72](https://github.com/midwayjs/midway-faas/issues/72)) ([10e68a9](https://github.com/midwayjs/midway-faas/commit/10e68a9a1cf096628eabb7b2b3f2c34dbdd14f3a))
* command-core support user lifecycle config ([#167](https://github.com/midwayjs/midway-faas/issues/167)) ([ebb84a2](https://github.com/midwayjs/midway-faas/commit/ebb84a2c215d6af504841d448db14149cac4d155))
* introduce `experimentalFeatures` root option ([#123](https://github.com/midwayjs/midway-faas/issues/123)) ([e20417f](https://github.com/midwayjs/midway-faas/commit/e20417f11e8bcdada52ca9835adeec6c16b67c06))
* new invoke ([#101](https://github.com/midwayjs/midway-faas/issues/101)) ([4c13a69](https://github.com/midwayjs/midway-faas/commit/4c13a695d9443f0f6683cad28967157f0d2ab496))





## 1.0.1 (2020-07-06)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* copyFiles to protected ([#86](https://github.com/midwayjs/midway-faas/issues/86)) ([bf54687](https://github.com/midwayjs/midway-faas/commit/bf5468776901c7c93d61eab8a38d8bd6a361eb01))
* core hooks ([#104](https://github.com/midwayjs/midway-faas/issues/104)) ([ac6b574](https://github.com/midwayjs/midway-faas/commit/ac6b574b7bab95358d5f14214711390959ad56b6))
* core support stop ([#111](https://github.com/midwayjs/midway-faas/issues/111)) ([4ecf423](https://github.com/midwayjs/midway-faas/commit/4ecf423dc2b0366b90b2d40b39a229421d4ee006)), closes [#107](https://github.com/midwayjs/midway-faas/issues/107)
* dev pack clean ([#141](https://github.com/midwayjs/midway-faas/issues/141)) ([d2731c8](https://github.com/midwayjs/midway-faas/commit/d2731c8bf8c662e06131eec80bdbc20474e39d89))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* Fix [#47](https://github.com/midwayjs/midway-faas/issues/47) ([#48](https://github.com/midwayjs/midway-faas/issues/48)) ([9060569](https://github.com/midwayjs/midway-faas/commit/90605695ab3158a2c2d3423ae4529416359908b7))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* get npm root ([#60](https://github.com/midwayjs/midway-faas/issues/60)) ([a3e8d3e](https://github.com/midwayjs/midway-faas/commit/a3e8d3ea0b4f68dc71469df8c6c7793cab3e491c))
* invoke bug ([#110](https://github.com/midwayjs/midway-faas/issues/110)) ([7c2d000](https://github.com/midwayjs/midway-faas/commit/7c2d000404cb185ff8597e9b3acea2a0955f1bda))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke hooks ([#113](https://github.com/midwayjs/midway-faas/issues/113)) ([765ec7e](https://github.com/midwayjs/midway-faas/commit/765ec7e259775360ff0c26755bdf78843b3658bf))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* load spec ([#119](https://github.com/midwayjs/midway-faas/issues/119)) ([66df38c](https://github.com/midwayjs/midway-faas/commit/66df38c221c033cf1ba9e28fcdc9953e33aabf34))
* process ([#120](https://github.com/midwayjs/midway-faas/issues/120)) ([53da26f](https://github.com/midwayjs/midway-faas/commit/53da26f1937b2dc20d6068c89e6b5addb16fb655))
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* remove command usage ([#88](https://github.com/midwayjs/midway-faas/issues/88)) ([c4c6050](https://github.com/midwayjs/midway-faas/commit/c4c60507b12112eb34a16b2af06c8f24353c9d80))
* repair no provider & change doc to faas-cli ([#30](https://github.com/midwayjs/midway-faas/issues/30)) ([e72b4c5](https://github.com/midwayjs/midway-faas/commit/e72b4c538349985ac934cfe291e6e755f99cec92))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* using ws instead of websocket ([#93](https://github.com/midwayjs/midway-faas/issues/93)) ([01c0b38](https://github.com/midwayjs/midway-faas/commit/01c0b3817a55bd7d6c8326d33b076645ca9f885e))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add dev pack ([#134](https://github.com/midwayjs/midway-faas/issues/134)) ([cd08f54](https://github.com/midwayjs/midway-faas/commit/cd08f54859da80f517cb37f99857679286f10f0f))
* code analysis ([#98](https://github.com/midwayjs/midway-faas/issues/98)) ([2651faa](https://github.com/midwayjs/midway-faas/commit/2651faa55155b3e00be875275536039792473f2c))
* command core store ([#72](https://github.com/midwayjs/midway-faas/issues/72)) ([10e68a9](https://github.com/midwayjs/midway-faas/commit/10e68a9a1cf096628eabb7b2b3f2c34dbdd14f3a))
* command-core support user lifecycle config ([#167](https://github.com/midwayjs/midway-faas/issues/167)) ([ebb84a2](https://github.com/midwayjs/midway-faas/commit/ebb84a2c215d6af504841d448db14149cac4d155))
* introduce `experimentalFeatures` root option ([#123](https://github.com/midwayjs/midway-faas/issues/123)) ([e20417f](https://github.com/midwayjs/midway-faas/commit/e20417f11e8bcdada52ca9835adeec6c16b67c06))
* new invoke ([#101](https://github.com/midwayjs/midway-faas/issues/101)) ([4c13a69](https://github.com/midwayjs/midway-faas/commit/4c13a695d9443f0f6683cad28967157f0d2ab496))





# 1.0.0 (2020-07-02)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* copyFiles to protected ([#86](https://github.com/midwayjs/midway-faas/issues/86)) ([bf54687](https://github.com/midwayjs/midway-faas/commit/bf5468776901c7c93d61eab8a38d8bd6a361eb01))
* core hooks ([#104](https://github.com/midwayjs/midway-faas/issues/104)) ([ac6b574](https://github.com/midwayjs/midway-faas/commit/ac6b574b7bab95358d5f14214711390959ad56b6))
* core support stop ([#111](https://github.com/midwayjs/midway-faas/issues/111)) ([4ecf423](https://github.com/midwayjs/midway-faas/commit/4ecf423dc2b0366b90b2d40b39a229421d4ee006)), closes [#107](https://github.com/midwayjs/midway-faas/issues/107)
* dev pack clean ([#141](https://github.com/midwayjs/midway-faas/issues/141)) ([d2731c8](https://github.com/midwayjs/midway-faas/commit/d2731c8bf8c662e06131eec80bdbc20474e39d89))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* Fix [#47](https://github.com/midwayjs/midway-faas/issues/47) ([#48](https://github.com/midwayjs/midway-faas/issues/48)) ([9060569](https://github.com/midwayjs/midway-faas/commit/90605695ab3158a2c2d3423ae4529416359908b7))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* get npm root ([#60](https://github.com/midwayjs/midway-faas/issues/60)) ([a3e8d3e](https://github.com/midwayjs/midway-faas/commit/a3e8d3ea0b4f68dc71469df8c6c7793cab3e491c))
* invoke bug ([#110](https://github.com/midwayjs/midway-faas/issues/110)) ([7c2d000](https://github.com/midwayjs/midway-faas/commit/7c2d000404cb185ff8597e9b3acea2a0955f1bda))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke hooks ([#113](https://github.com/midwayjs/midway-faas/issues/113)) ([765ec7e](https://github.com/midwayjs/midway-faas/commit/765ec7e259775360ff0c26755bdf78843b3658bf))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* load spec ([#119](https://github.com/midwayjs/midway-faas/issues/119)) ([66df38c](https://github.com/midwayjs/midway-faas/commit/66df38c221c033cf1ba9e28fcdc9953e33aabf34))
* process ([#120](https://github.com/midwayjs/midway-faas/issues/120)) ([53da26f](https://github.com/midwayjs/midway-faas/commit/53da26f1937b2dc20d6068c89e6b5addb16fb655))
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* remove command usage ([#88](https://github.com/midwayjs/midway-faas/issues/88)) ([c4c6050](https://github.com/midwayjs/midway-faas/commit/c4c60507b12112eb34a16b2af06c8f24353c9d80))
* repair no provider & change doc to faas-cli ([#30](https://github.com/midwayjs/midway-faas/issues/30)) ([e72b4c5](https://github.com/midwayjs/midway-faas/commit/e72b4c538349985ac934cfe291e6e755f99cec92))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* using ws instead of websocket ([#93](https://github.com/midwayjs/midway-faas/issues/93)) ([01c0b38](https://github.com/midwayjs/midway-faas/commit/01c0b3817a55bd7d6c8326d33b076645ca9f885e))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add dev pack ([#134](https://github.com/midwayjs/midway-faas/issues/134)) ([cd08f54](https://github.com/midwayjs/midway-faas/commit/cd08f54859da80f517cb37f99857679286f10f0f))
* code analysis ([#98](https://github.com/midwayjs/midway-faas/issues/98)) ([2651faa](https://github.com/midwayjs/midway-faas/commit/2651faa55155b3e00be875275536039792473f2c))
* command core store ([#72](https://github.com/midwayjs/midway-faas/issues/72)) ([10e68a9](https://github.com/midwayjs/midway-faas/commit/10e68a9a1cf096628eabb7b2b3f2c34dbdd14f3a))
* command-core support user lifecycle config ([#167](https://github.com/midwayjs/midway-faas/issues/167)) ([ebb84a2](https://github.com/midwayjs/midway-faas/commit/ebb84a2c215d6af504841d448db14149cac4d155))
* introduce `experimentalFeatures` root option ([#123](https://github.com/midwayjs/midway-faas/issues/123)) ([e20417f](https://github.com/midwayjs/midway-faas/commit/e20417f11e8bcdada52ca9835adeec6c16b67c06))
* new invoke ([#101](https://github.com/midwayjs/midway-faas/issues/101)) ([4c13a69](https://github.com/midwayjs/midway-faas/commit/4c13a695d9443f0f6683cad28967157f0d2ab496))





## [0.3.6](https://github.com/midwayjs/midway-faas/compare/v0.3.5...v0.3.6) (2020-06-30)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.3.3](https://github.com/midwayjs/midway-faas/compare/v0.3.2...v0.3.3) (2020-06-16)


### Bug Fixes

* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))





# [0.3.0](https://github.com/midwayjs/midway-faas/compare/v0.2.99...v0.3.0) (2020-05-26)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.97](https://github.com/midwayjs/midway-faas/compare/v0.2.96...v0.2.97) (2020-05-16)


### Bug Fixes

* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))


### Features

* command-core support user lifecycle config ([#167](https://github.com/midwayjs/midway-faas/issues/167)) ([ebb84a2](https://github.com/midwayjs/midway-faas/commit/ebb84a2c215d6af504841d448db14149cac4d155))





## [0.2.95](https://github.com/midwayjs/midway-faas/compare/v0.2.94...v0.2.95) (2020-05-15)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.94](https://github.com/midwayjs/midway-faas/compare/v0.2.93...v0.2.94) (2020-05-06)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.92](https://github.com/midwayjs/midway-faas/compare/v0.2.91...v0.2.92) (2020-05-05)


### Bug Fixes

* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))





## [0.2.92-beta.1](https://github.com/midwayjs/midway-faas/compare/v0.2.91...v0.2.92-beta.1) (2020-05-04)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.91](https://github.com/midwayjs/midway-faas/compare/v0.2.90...v0.2.91) (2020-04-30)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.89](https://github.com/midwayjs/midway-faas/compare/v0.2.88...v0.2.89) (2020-04-28)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.88](https://github.com/midwayjs/midway-faas/compare/v0.2.87...v0.2.88) (2020-04-26)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.87](https://github.com/midwayjs/midway-faas/compare/v0.2.86...v0.2.87) (2020-04-26)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.84](https://github.com/midwayjs/midway-faas/compare/v0.2.83...v0.2.84) (2020-04-23)


### Bug Fixes

* dev pack clean ([#141](https://github.com/midwayjs/midway-faas/issues/141)) ([d2731c8](https://github.com/midwayjs/midway-faas/commit/d2731c8bf8c662e06131eec80bdbc20474e39d89))





## [0.2.82](https://github.com/midwayjs/midway-faas/compare/v0.2.81...v0.2.82) (2020-04-23)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.79](https://github.com/midwayjs/midway-faas/compare/v0.2.78...v0.2.79) (2020-04-19)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.78](https://github.com/midwayjs/midway-faas/compare/v0.2.77...v0.2.78) (2020-04-18)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.77](https://github.com/midwayjs/midway-faas/compare/v0.2.76...v0.2.77) (2020-04-18)


### Features

* add dev pack ([#134](https://github.com/midwayjs/midway-faas/issues/134)) ([cd08f54](https://github.com/midwayjs/midway-faas/commit/cd08f54859da80f517cb37f99857679286f10f0f))





## [0.2.76](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.76) (2020-04-16)


### Bug Fixes

* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))





## [0.2.75](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.75) (2020-04-15)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.74](https://github.com/midwayjs/midway-faas/compare/v0.2.73...v0.2.74) (2020-04-13)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.73](https://github.com/midwayjs/midway-faas/compare/v0.2.73-alpha.0...v0.2.73) (2020-04-11)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.72](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.72) (2020-04-11)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.71](https://github.com/midwayjs/midway-faas/compare/v0.2.70...v0.2.71) (2020-04-10)


### Features

* introduce `experimentalFeatures` root option ([#123](https://github.com/midwayjs/midway-faas/issues/123)) ([e20417f](https://github.com/midwayjs/midway-faas/commit/e20417f11e8bcdada52ca9835adeec6c16b67c06))





## [0.2.70](https://github.com/midwayjs/midway-faas/compare/v0.2.69...v0.2.70) (2020-04-09)


### Bug Fixes

* process ([#120](https://github.com/midwayjs/midway-faas/issues/120)) ([53da26f](https://github.com/midwayjs/midway-faas/commit/53da26f1937b2dc20d6068c89e6b5addb16fb655))





## [0.2.69](https://github.com/midwayjs/midway-faas/compare/v0.2.68...v0.2.69) (2020-04-08)


### Bug Fixes

* load spec ([#119](https://github.com/midwayjs/midway-faas/issues/119)) ([66df38c](https://github.com/midwayjs/midway-faas/commit/66df38c221c033cf1ba9e28fcdc9953e33aabf34))





## [0.2.67](https://github.com/midwayjs/midway-faas/compare/v0.2.66...v0.2.67) (2020-04-07)


### Bug Fixes

* invoke hooks ([#113](https://github.com/midwayjs/midway-faas/issues/113)) ([765ec7e](https://github.com/midwayjs/midway-faas/commit/765ec7e259775360ff0c26755bdf78843b3658bf))





## [0.2.66](https://github.com/midwayjs/midway-faas/compare/v0.2.65...v0.2.66) (2020-04-06)


### Bug Fixes

* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))





## [0.2.65](https://github.com/midwayjs/midway-faas/compare/v0.2.64...v0.2.65) (2020-04-05)


### Bug Fixes

* core support stop ([#111](https://github.com/midwayjs/midway-faas/issues/111)) ([4ecf423](https://github.com/midwayjs/midway-faas/commit/4ecf423dc2b0366b90b2d40b39a229421d4ee006)), closes [#107](https://github.com/midwayjs/midway-faas/issues/107)





## [0.2.64](https://github.com/midwayjs/midway-faas/compare/v0.2.63...v0.2.64) (2020-04-05)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.63](https://github.com/midwayjs/midway-faas/compare/v0.2.62...v0.2.63) (2020-04-03)


### Bug Fixes

* invoke bug ([#110](https://github.com/midwayjs/midway-faas/issues/110)) ([7c2d000](https://github.com/midwayjs/midway-faas/commit/7c2d000404cb185ff8597e9b3acea2a0955f1bda))





## [0.2.62](https://github.com/midwayjs/midway-faas/compare/v0.2.61...v0.2.62) (2020-04-03)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))





## [0.2.61](https://github.com/midwayjs/midway-faas/compare/v0.2.60...v0.2.61) (2020-03-31)


### Bug Fixes

* core hooks ([#104](https://github.com/midwayjs/midway-faas/issues/104)) ([ac6b574](https://github.com/midwayjs/midway-faas/commit/ac6b574b7bab95358d5f14214711390959ad56b6))





## [0.2.59](https://github.com/midwayjs/midway-faas/compare/v0.2.58...v0.2.59) (2020-03-30)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.58](https://github.com/midwayjs/midway-faas/compare/v0.2.57...v0.2.58) (2020-03-30)


### Features

* new invoke ([#101](https://github.com/midwayjs/midway-faas/issues/101)) ([4c13a69](https://github.com/midwayjs/midway-faas/commit/4c13a695d9443f0f6683cad28967157f0d2ab496))





## [0.2.57](https://github.com/midwayjs/midway-faas/compare/v0.2.56...v0.2.57) (2020-03-27)


### Features

* code analysis ([#98](https://github.com/midwayjs/midway-faas/issues/98)) ([2651faa](https://github.com/midwayjs/midway-faas/commit/2651faa55155b3e00be875275536039792473f2c))





## [0.2.55](https://github.com/midwayjs/midway-faas/compare/v0.2.54...v0.2.55) (2020-03-20)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.54](https://github.com/midwayjs/midway-faas/compare/v0.2.53...v0.2.54) (2020-03-19)


### Bug Fixes

* using ws instead of websocket ([#93](https://github.com/midwayjs/midway-faas/issues/93)) ([01c0b38](https://github.com/midwayjs/midway-faas/commit/01c0b3817a55bd7d6c8326d33b076645ca9f885e))





## [0.2.51](https://github.com/midwayjs/midway-faas/compare/v0.2.50...v0.2.51) (2020-03-17)


### Bug Fixes

* remove command usage ([#88](https://github.com/midwayjs/midway-faas/issues/88)) ([c4c6050](https://github.com/midwayjs/midway-faas/commit/c4c60507b12112eb34a16b2af06c8f24353c9d80))





## [0.2.50](https://github.com/midwayjs/midway-faas/compare/v0.2.49...v0.2.50) (2020-03-17)


### Bug Fixes

* copyFiles to protected ([#86](https://github.com/midwayjs/midway-faas/issues/86)) ([bf54687](https://github.com/midwayjs/midway-faas/commit/bf5468776901c7c93d61eab8a38d8bd6a361eb01))





## [0.2.49](https://github.com/midwayjs/midway-faas/compare/v0.2.48...v0.2.49) (2020-03-14)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.41](https://github.com/midwayjs/midway-faas/compare/v0.2.40...v0.2.41) (2020-03-06)


### Features

* command core store ([#72](https://github.com/midwayjs/midway-faas/issues/72)) ([10e68a9](https://github.com/midwayjs/midway-faas/commit/10e68a9a1cf096628eabb7b2b3f2c34dbdd14f3a))





## [0.2.40](https://github.com/midwayjs/midway-faas/compare/v0.2.39...v0.2.40) (2020-03-02)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.34](https://github.com/midwayjs/midway-faas/compare/v0.2.33...v0.2.34) (2020-02-26)


### Bug Fixes

* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))





## [0.2.33](https://github.com/midwayjs/midway-faas/compare/v0.2.32...v0.2.33) (2020-02-24)


### Bug Fixes

* get npm root ([#60](https://github.com/midwayjs/midway-faas/issues/60)) ([a3e8d3e](https://github.com/midwayjs/midway-faas/commit/a3e8d3ea0b4f68dc71469df8c6c7793cab3e491c))





## [0.2.29](https://github.com/midwayjs/midway-faas/compare/v0.2.28...v0.2.29) (2020-02-22)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.27](https://github.com/midwayjs/midway-faas/compare/v0.2.26...v0.2.27) (2020-02-21)


### Bug Fixes

* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)





## [0.2.22](https://github.com/midwayjs/midway-faas/compare/v0.2.21...v0.2.22) (2020-02-17)


### Bug Fixes

* Fix [#47](https://github.com/midwayjs/midway-faas/issues/47) ([#48](https://github.com/midwayjs/midway-faas/issues/48)) ([9060569](https://github.com/midwayjs/midway-faas/commit/90605695ab3158a2c2d3423ae4529416359908b7))





## [0.2.20](https://github.com/midwayjs/midway-faas/compare/v0.2.19...v0.2.20) (2020-02-11)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.2.14](https://github.com/midwayjs/midway-faas/compare/v0.2.13...v0.2.14) (2020-02-01)


### Bug Fixes

* repair no provider & change doc to faas-cli ([#30](https://github.com/midwayjs/midway-faas/issues/30)) ([e72b4c5](https://github.com/midwayjs/midway-faas/commit/e72b4c538349985ac934cfe291e6e755f99cec92))





## [0.2.10](https://github.com/midwayjs/midway-faas/compare/v0.2.9...v0.2.10) (2020-01-16)


### Bug Fixes

* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))





## [0.2.2](https://github.com/midwayjs/midway-faas/compare/v0.2.1...v0.2.2) (2020-01-08)


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))







**Note:** Version bump only for package @midwayjs/fcli-command-core





# [0.2.0](https://github.com/midwayjs/midway-faas/compare/v0.1.12...v0.2.0) (2020-01-05)

**Note:** Version bump only for package @midwayjs/fcli-command-core





## [0.1.12](https://github.com/midwayjs/midway-faas/compare/v0.1.11...v0.1.12) (2020-01-01)


### Bug Fixes

* command-core point and writeSpec ([d0dac1e](https://github.com/midwayjs/midway-faas/commit/d0dac1e41e783746b857eebcd9e4730e37737d72))
* invoke user runtime ([7e5ab36](https://github.com/midwayjs/midway-faas/commit/7e5ab3636523d13b79527c2f0edd5fb405550d5f))





## [0.1.11](https://github.com/midwayjs/midway-faas/compare/v0.1.10...v0.1.11) (2019-12-30)

**Note:** Version bump only for package @midwayjs/command-core





## [0.1.10](https://github.com/midwayjs/midway-faas/compare/v0.1.9...v0.1.10) (2019-12-27)

**Note:** Version bump only for package @midwayjs/command-core





## [0.1.9](https://github.com/midwayjs/midway-faas/compare/v0.1.8...v0.1.9) (2019-12-27)

**Note:** Version bump only for package @midwayjs/command-core





## [0.1.8](https://github.com/midwayjs/midway-faas/compare/v0.1.7...v0.1.8) (2019-12-25)

**Note:** Version bump only for package @midwayjs/command-core





## [0.1.7](https://github.com/midwayjs/midway-faas/compare/v0.1.6...v0.1.7) (2019-12-25)


### Bug Fixes

* faas-cli package dist ([302de75](https://github.com/midwayjs/midway-faas/commit/302de754e9839ab77dc0b554916633d2dc1cc5d2))





## [0.1.6](https://github.com/midwayjs/midway-faas/compare/v0.1.5...v0.1.6) (2019-12-25)


### Bug Fixes

* format ([99edaef](https://github.com/midwayjs/midway-faas/commit/99edaef97a5c4b21b1223e089a9d39dbe8694d97))
* lifecycle ([caa17ff](https://github.com/midwayjs/midway-faas/commit/caa17ffbf6e1b48c390db744a3ce7fcd17b2af3d))


### Features

* cli & cli core ([b1fc539](https://github.com/midwayjs/midway-faas/commit/b1fc5395598acc393786c550ef089321fc94ef2c))
* faas-cli default plugin manager ([ec6cf5f](https://github.com/midwayjs/midway-faas/commit/ec6cf5ff334225eed098414af6aa6dd42c3bd39b))
