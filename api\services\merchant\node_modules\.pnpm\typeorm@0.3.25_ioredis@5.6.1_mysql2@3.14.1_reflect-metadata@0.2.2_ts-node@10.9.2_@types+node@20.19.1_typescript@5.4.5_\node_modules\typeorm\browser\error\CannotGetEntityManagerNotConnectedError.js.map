{"version": 3, "sources": ["../browser/src/error/CannotGetEntityManagerNotConnectedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uCAAwC,SAAQ,YAAY;IACrE,YAAY,cAAsB;QAC9B,KAAK,CACD,kCAAkC,cAAc,yDAAyD,CAC5G,CAAA;IACL,CAAC;CACJ", "file": "CannotGetEntityManagerNotConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to access entity manager before connection is established.\n */\nexport class CannotGetEntityManagerNotConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot get entity manager for \"${connectionName}\" connection because connection is not yet established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}