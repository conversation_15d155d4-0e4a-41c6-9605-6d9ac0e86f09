{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaUniqueOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaUniqueOptions.js", "sourcesContent": ["import { DeferrableType } from \"../metadata/types/DeferrableType\"\n\nexport interface EntitySchemaUniqueOptions {\n    /**\n     * Unique constraint name.\n     */\n    name?: string\n\n    /**\n     * Unique column names.\n     */\n    columns?: ((object?: any) => any[] | { [key: string]: number }) | string[]\n\n    /**\n     * Indicate if unique constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n}\n"], "sourceRoot": ".."}