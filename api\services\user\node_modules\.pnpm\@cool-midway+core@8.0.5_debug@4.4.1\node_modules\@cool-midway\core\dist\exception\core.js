"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolCoreException = void 0;
const global_1 = require("../constant/global");
const base_1 = require("./base");
/**
 * 核心异常
 */
class CoolCoreException extends base_1.BaseException {
    constructor(message, statusCode) {
        const { RESCODE, RESMESSAGE } = global_1.GlobalConfig.getInstance();
        super('CoolCoreException', RESCODE.COREFAIL, message ? message : RESMESSAGE.COREFAIL, statusCode);
    }
}
exports.CoolCoreException = CoolCoreException;
