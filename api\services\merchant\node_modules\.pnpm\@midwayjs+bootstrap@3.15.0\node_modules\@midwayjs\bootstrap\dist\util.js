"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sleep = exports.logDate = void 0;
function logDate() {
    const d = new Date();
    let date = d.getDate();
    if (date < 10) {
        date = '0' + date;
    }
    let month = d.getMonth() + 1;
    if (month < 10) {
        month = '0' + month;
    }
    let hours = d.getHours();
    if (hours < 10) {
        hours = '0' + hours;
    }
    let mintues = d.getMinutes();
    if (mintues < 10) {
        mintues = '0' + mintues;
    }
    let seconds = d.getSeconds();
    if (seconds < 10) {
        seconds = '0' + seconds;
    }
    let milliseconds = d.getMilliseconds();
    if (milliseconds < 10) {
        milliseconds = '00' + milliseconds;
    }
    else if (milliseconds < 100) {
        milliseconds = '0' + milliseconds;
    }
    return (d.getFullYear() +
        '-' +
        month +
        '-' +
        date +
        ' ' +
        hours +
        ':' +
        mintues +
        ':' +
        seconds +
        '.' +
        milliseconds);
}
exports.logDate = logDate;
async function sleep(timeout) {
    return new Promise(resolve => {
        setTimeout(resolve, timeout);
    });
}
exports.sleep = sleep;
//# sourceMappingURL=util.js.map