import { ValidationOptions } from '../ValidationOptions';
export declare const IS_JWT = "isJwt";
/**
 * Checks if the string is valid JWT token.
 * If given value is not a string, then it returns false.
 */
export declare function isJWT(value: unknown): boolean;
/**
 * Checks if the string is valid JWT token.
 * If given value is not a string, then it returns false.
 */
export declare function IsJWT(validationOptions?: ValidationOptions): PropertyDecorator;
