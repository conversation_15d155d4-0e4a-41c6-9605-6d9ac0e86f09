{"version": 3, "sources": ["../browser/src/driver/sqlserver/authentication/DefaultAuthentication.ts"], "names": [], "mappings": "", "file": "DefaultAuthentication.js", "sourcesContent": ["export interface DefaultAuthentication {\n    type: \"default\"\n    options: {\n        /**\n         * User name to use for sql server login.\n         */\n        userName?: string\n        /**\n         * Password to use for sql server login.\n         */\n        password?: string\n    }\n}\n"], "sourceRoot": "../../.."}