{"version": 3, "sources": ["../browser/src/decorator/relations/JoinTable.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAyBtD;;;GAGG;AACH,MAAM,UAAU,SAAS,CACrB,OAA4D;IAE5D,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,OAAO;YACH,OAAO;gBACN,EAAyD,CAAA;QAC9D,sBAAsB,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;YACrC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,WAAW,EAAE,CAAC,OAAO,IAAK,OAA4B,CAAC,UAAU;gBAC7D,CAAC,CAAC,CAAE,OAA4B,CAAC,UAAW,CAAC;gBAC7C,CAAC,CAAE,OAA2C;qBACvC,WAAW,CAAQ;YAC9B,kBAAkB,EAAE,CAAC,OAAO;gBAC3B,OAA4B,CAAC,iBAAiB;gBAC3C,CAAC,CAAC,CAAE,OAA4B,CAAC,iBAAkB,CAAC;gBACpD,CAAC,CAAE,OAA2C;qBACvC,kBAAkB,CAAQ;YACrC,MAAM,EAAE,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC9D,QAAQ,EACJ,OAAO,IAAI,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YAC9D,WAAW,EAAE,CAAC,CAAC,OAAO,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC;SAClC,CAAC,CAAA;IAC/B,CAAC,CAAA;AACL,CAAC", "file": "JoinTable.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { JoinTableMetadataArgs } from \"../../metadata-args/JoinTableMetadataArgs\"\nimport { JoinTableMultipleColumnsOptions } from \"../options/JoinTableMultipleColumnsOptions\"\nimport { JoinTableOptions } from \"../options/JoinTableOptions\"\n\n/**\n * JoinTable decorator is used in many-to-many relationship to specify owner side of relationship.\n * Its also used to set a custom junction table's name, column names and referenced columns.\n */\nexport function JoinTable(): PropertyDecorator\n\n/**\n * JoinTable decorator is used in many-to-many relationship to specify owner side of relationship.\n * Its also used to set a custom junction table's name, column names and referenced columns.\n */\nexport function JoinTable(options: JoinTableOptions): PropertyDecorator\n\n/**\n * JoinTable decorator is used in many-to-many relationship to specify owner side of relationship.\n * Its also used to set a custom junction table's name, column names and referenced columns.\n */\nexport function JoinTable(\n    options: JoinTableMultipleColumnsOptions,\n): PropertyDecorator\n\n/**\n * JoinTable decorator is used in many-to-many relationship to specify owner side of relationship.\n * Its also used to set a custom junction table's name, column names and referenced columns.\n */\nexport function JoinTable(\n    options?: JoinTableOptions | JoinTableMultipleColumnsOptions,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        options =\n            options ||\n            ({} as JoinTableOptions | JoinTableMultipleColumnsOptions)\n        getMetadataArgsStorage().joinTables.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            name: options.name,\n            joinColumns: (options && (options as JoinTableOptions).joinColumn\n                ? [(options as JoinTableOptions).joinColumn!]\n                : (options as JoinTableMultipleColumnsOptions)\n                      .joinColumns) as any,\n            inverseJoinColumns: (options &&\n            (options as JoinTableOptions).inverseJoinColumn\n                ? [(options as JoinTableOptions).inverseJoinColumn!]\n                : (options as JoinTableMultipleColumnsOptions)\n                      .inverseJoinColumns) as any,\n            schema: options && options.schema ? options.schema : undefined,\n            database:\n                options && options.database ? options.database : undefined,\n            synchronize: !(options && options.synchronize === false),\n        } as JoinTableMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}