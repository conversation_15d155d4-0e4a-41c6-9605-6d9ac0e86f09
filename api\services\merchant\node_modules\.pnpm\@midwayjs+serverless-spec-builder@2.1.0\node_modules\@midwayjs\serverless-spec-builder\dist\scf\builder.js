"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.convertMethods = exports.SCFServerlessSpecBuilder = exports.nodejsVersion = void 0;
const index_1 = require("../index");
const utils_1 = require("../utils");
exports.nodejsVersion = {
    nodejs6: 'Nodejs6.10',
    nodejs8: 'Nodejs8.9',
    nodejs10: 'Nodejs10.15',
    nodejs12: 'Nodejs12.16',
};
function getNodejsRuntime(runtime) {
    if (exports.nodejsVersion[runtime]) {
        return exports.nodejsVersion[runtime];
    }
    if (runtime) {
        return runtime;
    }
    return 'Nodejs12.16';
}
class SCFServerlessSpecBuilder extends index_1.SpecBuilder {
    toJSON() {
        var _a;
        const providerData = this.getProvider();
        const serviceData = this.getService();
        const functionsData = this.getFunctions();
        const serviceName = serviceData.name;
        const userDefinedEnv = (0, utils_1.filterUserDefinedEnv)();
        const serverless = {
            service: serviceName,
            provider: {
                name: 'tencent',
                runtime: getNodejsRuntime(providerData.runtime),
                region: providerData.region,
                credentials: providerData.credentials,
                stage: providerData.stage,
                role: providerData.role,
                memorySize: providerData.memorySize || 128,
                environment: {
                    variables: {
                        ...providerData.environment,
                        ...userDefinedEnv,
                    },
                },
                timeout: providerData.timeout || 3,
            },
            functions: {},
            plugins: this.getPlugins(),
        };
        for (const funName in functionsData) {
            const funSpec = functionsData[funName];
            const functionTemplate = {
                handler: funSpec.handler || 'index.main_handler',
                description: funSpec.description || '',
                runtime: funSpec.runtime || serverless.provider.runtime,
                timeout: funSpec.timeout || serverless.provider.timeout,
                memorySize: funSpec.memorySize || serverless.provider.memorySize,
                environment: {
                    variables: {
                        ...funSpec.environment,
                    },
                },
                events: [],
            };
            for (const event of (_a = funSpec['events']) !== null && _a !== void 0 ? _a : []) {
                if (event['http'] || event['apigw']) {
                    const evt = (event['http'] || event['apigw']);
                    const apiGateway = {
                        name: `${funName}_apigw_${providerData.stage || 'dev'}`,
                        parameters: {
                            httpMethod: convertMethods(evt.method),
                            path: evt.path,
                            serviceTimeout: funSpec.timeout || evt.timeout,
                            stageName: funSpec.stage || providerData.stage,
                            serviceId: evt.serviceId || providerData.serviceId,
                            integratedResponse: evt.integratedResponse || true,
                            enableCORS: evt.cors,
                        },
                    };
                    functionTemplate.events.push({
                        apigw: apiGateway,
                    });
                }
                if (event['timer']) {
                    const evt = event['timer'];
                    const timer = {
                        name: 'timer',
                        parameters: {
                            cronExpression: evt.value,
                            enable: evt.enable === false ? false : true,
                        },
                    };
                    functionTemplate.events.push({
                        timer,
                    });
                }
                if (event['os'] || event['cos']) {
                    const evt = (event['os'] || event['cos']);
                    const cos = {
                        name: evt.name || 'cos',
                        parameters: {
                            bucket: evt.bucket,
                            filter: evt.filter,
                            events: evt.events,
                            enable: evt.enable === false ? false : true,
                        },
                    };
                    functionTemplate.events.push({ cos });
                }
                if (event['cmq'] || event['mq']) {
                    const evt = (event['cmq'] || event['mq']);
                    const cmq = {
                        name: 'cmq',
                        parameters: {
                            name: evt.topic,
                            enable: evt.enable === false ? false : true,
                        },
                    };
                    functionTemplate.events.push({ cmq });
                }
                // if (event['kafka']) {
                //   const ckafka = event['kafka'] as Ckafka;
                //   functionTemplate.events.push({ ckafka });
                // }
            }
            serverless.functions[funName] = functionTemplate;
        }
        return (0, utils_1.removeObjectEmptyAttributes)(serverless);
    }
}
exports.SCFServerlessSpecBuilder = SCFServerlessSpecBuilder;
function convertMethods(method) {
    // ref: https://cloud.tencent.com/document/product/583/12513
    const currentSupport = ['ANY', 'GET', 'HEAD', 'POST', 'PUT', 'DELETE'];
    if (!method) {
        method = 'any';
    }
    if (Array.isArray(method)) {
        // 腾讯云只支持单个方法类型
        method = method[0];
    }
    if (method.toUpperCase() === 'ALL') {
        method = 'any';
    }
    const upperMethod = method.toUpperCase();
    if (currentSupport.includes(upperMethod)) {
        return upperMethod;
    }
    return 'ANY';
}
exports.convertMethods = convertMethods;
//# sourceMappingURL=builder.js.map