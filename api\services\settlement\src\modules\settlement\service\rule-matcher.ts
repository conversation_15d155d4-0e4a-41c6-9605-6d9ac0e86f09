import { Provide, Inject } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { CoolRpc } from '@cool-midway/rpc';

/**
 * 结算规则匹配服务
 * 根据商户信息自动匹配最优结算规则
 */
@Provide()
export class SettlementRuleMatcherService extends BaseService {
  @Inject()
  rpc: CoolRpc;

  /**
   * 为商户匹配最优结算规则
   * @param merchantInfo 商户信息
   */
  async matchOptimalRule(merchantInfo: {
    id: number;
    type: 'personal' | 'enterprise'; // 商户类型
    level: 'newbie' | 'standard' | 'vip' | 'diamond'; // 商户等级
    industry: string; // 行业分类
    creatorAuth?: {
      type: 'handicraft' | 'heritage' | 'general'; // 创作者认证类型
      status: 'pending' | 'approved' | 'rejected'; // 认证状态
      certifiedAt?: Date; // 认证时间
    };
    registeredAt: Date; // 注册时间
    monthlyVolume: number; // 月交易量
    riskScore: number; // 风险评分
  }) {
    const { id, type, level, industry, creatorAuth, registeredAt, monthlyVolume, riskScore } = merchantInfo;

    // 1. 获取所有有效的结算规则
    const allRules = await this.getAllActiveRules();

    // 2. 筛选适用的规则
    const applicableRules = allRules.filter(rule => this.isRuleApplicable(rule, merchantInfo));

    // 3. 按优先级排序，选择最优规则
    const optimalRule = this.selectOptimalRule(applicableRules, merchantInfo);

    // 4. 计算实际费率（考虑新商户优惠等）
    const actualRate = await this.calculateActualRate(optimalRule, merchantInfo);

    return {
      rule: optimalRule,
      actualRate,
      reason: this.getMatchReason(optimalRule, merchantInfo),
      benefits: this.calculateBenefits(optimalRule, merchantInfo),
      nextReviewDate: this.calculateNextReviewDate(optimalRule, merchantInfo)
    };
  }

  /**
   * 检查规则是否适用于商户
   */
  private isRuleApplicable(rule: any, merchantInfo: any): boolean {
    const { type, level, industry, creatorAuth } = merchantInfo;

    // 商户类型匹配
    if (rule.merchantType !== 'all' && rule.merchantType !== type) {
      return false;
    }

    // 商户等级匹配
    if (rule.merchantLevel !== 'all' && rule.merchantLevel !== level) {
      return false;
    }

    // 行业分类匹配
    if (rule.industry !== 'all' && rule.industry !== industry) {
      return false;
    }

    // 创作者认证匹配 - 这是关键逻辑
    if (rule.creatorAuthType && rule.creatorAuthType !== 'none') {
      // 如果规则要求特定认证，检查商户是否有对应认证
      if (!creatorAuth || creatorAuth.status !== 'approved') {
        return false;
      }
      
      // 检查认证类型是否匹配
      if (rule.creatorAuthType !== creatorAuth.type) {
        return false;
      }
    }

    return true;
  }

  /**
   * 选择最优规则
   * 优先级：认证类型 > 商户等级 > 新商户优惠 > 基础规则
   */
  private selectOptimalRule(applicableRules: any[], merchantInfo: any): any {
    if (applicableRules.length === 0) {
      throw new Error('No applicable settlement rule found');
    }

    // 按优先级排序
    const sortedRules = applicableRules.sort((a, b) => {
      // 1. 认证类型优先级最高（手工艺人和非遗传承人免佣金）
      const aAuthPriority = this.getAuthPriority(a.creatorAuthType);
      const bAuthPriority = this.getAuthPriority(b.creatorAuthType);
      if (aAuthPriority !== bAuthPriority) {
        return bAuthPriority - aAuthPriority; // 降序
      }

      // 2. 系统设置的优先级
      if (a.priority !== b.priority) {
        return b.priority - a.priority; // 降序
      }

      // 3. 佣金率越低越优先（对商户更有利）
      return a.commissionRate - b.commissionRate; // 升序
    });

    return sortedRules[0];
  }

  /**
   * 获取认证类型优先级
   */
  private getAuthPriority(authType: string): number {
    const priorities = {
      'handicraft': 100, // 手工艺人认证最高优先级
      'heritage': 100,   // 非遗传承人认证最高优先级
      'general': 50,     // 通用认证中等优先级
      'none': 0          // 无认证最低优先级
    };
    return priorities[authType] || 0;
  }

  /**
   * 计算实际费率
   */
  private async calculateActualRate(rule: any, merchantInfo: any): Promise<number> {
    let actualRate = rule.commissionRate;

    // 新商户优惠
    if (this.isNewMerchant(merchantInfo.registeredAt) && rule.newMerchantDiscount?.enabled) {
      actualRate = rule.newMerchantDiscount.rate;
    }

    // 活动期间优惠
    if (rule.promotionRule?.enabled && this.isInPromotionPeriod(rule.promotionRule)) {
      actualRate = Math.min(actualRate, rule.promotionRule.rate);
    }

    // 阶梯费率
    if (rule.enableLadder && rule.ladderRules?.length > 0) {
      const ladderRate = this.calculateLadderRate(merchantInfo.monthlyVolume, rule.ladderRules);
      if (ladderRate < actualRate) {
        actualRate = ladderRate;
      }
    }

    return actualRate;
  }

  /**
   * 判断是否为新商户
   */
  private isNewMerchant(registeredAt: Date): boolean {
    const threeMonthsAgo = new Date();
    threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);
    return registeredAt > threeMonthsAgo;
  }

  /**
   * 判断是否在活动期间
   */
  private isInPromotionPeriod(promotionRule: any): boolean {
    const now = new Date();
    const startTime = new Date(promotionRule.startTime);
    const endTime = new Date(promotionRule.endTime);
    return now >= startTime && now <= endTime;
  }

  /**
   * 计算阶梯费率
   */
  private calculateLadderRate(volume: number, ladderRules: any[]): number {
    for (const ladder of ladderRules.sort((a, b) => a.minAmount - b.minAmount)) {
      if (volume >= ladder.minAmount && (ladder.maxAmount === 0 || volume <= ladder.maxAmount)) {
        return ladder.rate;
      }
    }
    return ladderRules[0]?.rate || 3.0;
  }

  /**
   * 获取匹配原因
   */
  private getMatchReason(rule: any, merchantInfo: any): string {
    const { creatorAuth } = merchantInfo;

    if (rule.creatorAuthType === 'handicraft' && creatorAuth?.type === 'handicraft') {
      return '🎨 恭喜！您是认证手工艺人，享受免佣金政策，支持传统手工艺传承发展';
    }

    if (rule.creatorAuthType === 'heritage' && creatorAuth?.type === 'heritage') {
      return '🏛️ 恭喜！您是认证非遗传承人，享受免佣金政策，保护和传承非物质文化遗产';
    }

    if (this.isNewMerchant(merchantInfo.registeredAt)) {
      return '🎉 新商户优惠：前3个月享受2.0%优惠佣金，欢迎加入我们的平台';
    }

    if (merchantInfo.level === 'vip') {
      return '⭐ VIP商户专享：享受2.8%优惠佣金和T+0实时结算服务';
    }

    if (merchantInfo.level === 'diamond') {
      return '💎 钻石商户尊享：最优惠的费率和最快的结算周期';
    }

    return `📋 标准结算规则：${rule.commissionRate}%佣金，${rule.settlementCycle}结算`;
  }

  /**
   * 计算优惠收益
   */
  private calculateBenefits(rule: any, merchantInfo: any): any {
    const standardRate = 3.5; // 标准个人商户费率
    const actualRate = rule.commissionRate;
    const monthlyVolume = merchantInfo.monthlyVolume;

    const monthlySavings = (standardRate - actualRate) / 100 * monthlyVolume;
    const yearlySavings = monthlySavings * 12;

    return {
      rateDiscount: `${(standardRate - actualRate).toFixed(1)}%`,
      monthlySavings: monthlySavings.toFixed(2),
      yearlySavings: yearlySavings.toFixed(2),
      freeWithdraw: rule.withdrawFee === 0,
      freeTransaction: rule.transactionFee === 0,
      fastSettlement: rule.settlementCycle === 'T0'
    };
  }

  /**
   * 计算下次审核日期
   */
  private calculateNextReviewDate(rule: any, merchantInfo: any): Date {
    const nextReview = new Date();

    // 新商户3个月后审核
    if (this.isNewMerchant(merchantInfo.registeredAt)) {
      const registeredAt = new Date(merchantInfo.registeredAt);
      nextReview.setTime(registeredAt.getTime());
      nextReview.setMonth(nextReview.getMonth() + 3);
      return nextReview;
    }

    // 其他商户每年审核一次
    nextReview.setFullYear(nextReview.getFullYear() + 1);
    return nextReview;
  }

  /**
   * 获取所有有效规则
   */
  private async getAllActiveRules(): Promise<any[]> {
    // 这里应该从数据库获取，暂时返回模拟数据
    return [
      {
        id: 1,
        name: '个人商户结算规则',
        merchantType: 'personal',
        merchantLevel: 'standard',
        industry: 'all',
        creatorAuthType: 'none',
        commissionRate: 4.5,
        priority: 10,
        status: 1
      },
      {
        id: 2,
        name: '企业商户结算规则',
        merchantType: 'enterprise',
        merchantLevel: 'vip',
        industry: 'all',
        creatorAuthType: 'none',
        commissionRate: 3.8,
        priority: 20,
        status: 1
      },
      {
        id: 3,
        name: '手工艺人结算规则',
        merchantType: 'all',
        merchantLevel: 'all',
        industry: 'handicraft',
        creatorAuthType: 'handicraft',
        commissionRate: 2.0,
        withdrawFee: 0,
        transactionFee: 0,
        priority: 100,
        status: 1
      },
      {
        id: 4,
        name: '非遗传承人结算规则',
        merchantType: 'all',
        merchantLevel: 'all',
        industry: 'heritage',
        creatorAuthType: 'heritage',
        commissionRate: 0,
        withdrawFee: 0,
        transactionFee: 0,
        priority: 100,
        status: 1
      },
      {
        id: 5,
        name: '新商户优惠规则',
        merchantType: 'all',
        merchantLevel: 'newbie',
        industry: 'all',
        creatorAuthType: 'none',
        commissionRate: 2.0,
        newMerchantDiscount: { enabled: true, duration: 3, rate: 2.0 },
        priority: 50,
        status: 1
      }
    ];
  }

  /**
   * 批量为商户匹配规则
   */
  async batchMatchRules(merchantIds: number[]): Promise<Map<number, any>> {
    const results = new Map();

    for (const merchantId of merchantIds) {
      try {
        // 获取商户信息
        const merchantInfo = await this.rpc.call('merchant', 'getMerchantInfo', merchantId);
        
        // 匹配规则
        const matchResult = await this.matchOptimalRule(merchantInfo);
        
        results.set(merchantId, matchResult);
      } catch (error) {
        console.error(`Failed to match rule for merchant ${merchantId}:`, error);
        results.set(merchantId, { error: error.message });
      }
    }

    return results;
  }

  /**
   * 获取商户当前适用的所有规则
   */
  async getMerchantApplicableRules(merchantId: number): Promise<any[]> {
    const merchantInfo = await this.rpc.call('merchant', 'getMerchantInfo', merchantId);
    const allRules = await this.getAllActiveRules();
    
    return allRules.filter(rule => this.isRuleApplicable(rule, merchantInfo));
  }
}
