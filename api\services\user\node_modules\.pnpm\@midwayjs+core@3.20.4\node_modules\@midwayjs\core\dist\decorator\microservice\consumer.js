"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Consumer = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Consumer(type, options = {}) {
    return (target) => {
        (0, __1.saveModule)(__1.MS_CONSUMER_KEY, target);
        (0, __1.saveClassMetadata)(__1.MS_CONSUMER_KEY, {
            type,
            metadata: options,
        }, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Consumer = Consumer;
//# sourceMappingURL=consumer.js.map