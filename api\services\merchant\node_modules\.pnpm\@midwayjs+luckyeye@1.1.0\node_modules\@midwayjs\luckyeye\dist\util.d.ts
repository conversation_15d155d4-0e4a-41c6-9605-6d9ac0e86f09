/**
 * Default color map.
 */
export declare const colors: {
    pass: number;
    fail: number;
    'bright pass': number;
    'bright fail': number;
    'bright yellow': number;
    pending: number;
    suite: number;
    'error title': number;
    'error message': number;
    'error stack': number;
    checkmark: number;
    fast: number;
    medium: number;
    slow: number;
    green: number;
    light: number;
    'diff gutter': number;
    'diff added': number;
    'diff removed': number;
    info: number;
    warn: number;
};
/**
 * Default symbol map.
 */
export declare const symbols: {
    ok: string;
    err: string;
    dot: string;
    comma: string;
    bang: string;
    info: string;
    warn: string;
};
export declare const color: (type: any, str: any) => string;
export declare function getDynamicPackageVersion(name: any): string;
export declare function getNpmList(dirPath: string, dataMap: any, level?: number): any;
export declare function hasPackage(baseDir: any, packageName: any, isDev?: boolean): boolean;
export declare function getPackageVersion(baseDir: any, packageName: any, isDev?: boolean): any;
export declare function versionCompare(current: any, target: any): any;
//# sourceMappingURL=util.d.ts.map