{"version": 3, "file": "uniqueItems.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/validation/uniqueItems.ts"], "names": [], "mappings": ";;AAEA,8DAAwF;AACxF,mDAAkD;AAClD,6CAA0C;AAC1C,+CAAuC;AAQvC,MAAM,KAAK,GAA2B;IACpC,OAAO,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAC,EAAE,EAAE,CAC5B,IAAA,aAAG,EAAA,2CAA2C,CAAC,QAAQ,CAAC,iBAAiB;IAC3E,MAAM,EAAE,CAAC,EAAC,MAAM,EAAE,EAAC,CAAC,EAAE,CAAC,EAAC,EAAC,EAAE,EAAE,CAAC,IAAA,WAAC,EAAA,OAAO,CAAC,QAAQ,CAAC,GAAG;CACpD,CAAA;AAED,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,aAAa;IACtB,IAAI,EAAE,OAAO;IACb,UAAU,EAAE,SAAS;IACrB,KAAK,EAAE,IAAI;IACX,KAAK;IACL,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACpE,IAAI,CAAC,KAAK,IAAI,CAAC,MAAM;YAAE,OAAM;QAC7B,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC9B,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,yBAAc,EAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;QAC9E,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,mBAAmB,EAAE,IAAA,WAAC,EAAA,GAAG,UAAU,YAAY,CAAC,CAAA;QACtE,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QAEb,SAAS,mBAAmB;YAC1B,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,SAAS,CAAC,CAAA;YACzC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;YACtB,GAAG,CAAC,SAAS,CAAC,EAAC,CAAC,EAAE,CAAC,EAAC,CAAC,CAAA;YACrB,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;YACvB,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QACnE,CAAC;QAED,SAAS,WAAW;YAClB,OAAO,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,QAAQ,IAAI,CAAC,KAAK,OAAO,CAAC,CAAA;QACxF,CAAC;QAED,SAAS,KAAK,CAAC,CAAO,EAAE,CAAO;YAC7B,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC7B,MAAM,SAAS,GAAG,IAAA,yBAAc,EAAC,SAAS,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,mBAAQ,CAAC,KAAK,CAAC,CAAA;YACxF,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,IAAA,WAAC,EAAA,IAAI,CAAC,CAAA;YAC3C,GAAG,CAAC,GAAG,CAAC,IAAA,WAAC,EAAA,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;gBACxB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC/B,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,IAAA,WAAC,EAAA,UAAU,CAAC,CAAA;gBAC9B,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC;oBAAE,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,UAAU,IAAI,cAAc,EAAE,IAAA,WAAC,EAAA,GAAG,IAAI,SAAS,CAAC,CAAA;gBAClF,GAAG;qBACA,EAAE,CAAC,IAAA,WAAC,EAAA,UAAU,OAAO,IAAI,IAAI,eAAe,EAAE,GAAG,EAAE;oBAClD,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAA,WAAC,EAAA,GAAG,OAAO,IAAI,IAAI,GAAG,CAAC,CAAA;oBACrC,GAAG,CAAC,KAAK,EAAE,CAAA;oBACX,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,KAAK,EAAE,CAAA;gBAClC,CAAC,CAAC;qBACD,IAAI,CAAC,IAAA,WAAC,EAAA,GAAG,OAAO,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,CAAA;YACxC,CAAC,CAAC,CAAA;QACJ,CAAC;QAED,SAAS,MAAM,CAAC,CAAO,EAAE,CAAO;YAC9B,MAAM,GAAG,GAAG,IAAA,cAAO,EAAC,GAAG,EAAE,eAAK,CAAC,CAAA;YAC/B,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC/B,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAA,WAAC,EAAA,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CACrC,GAAG,CAAC,GAAG,CAAC,IAAA,WAAC,EAAA,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,CACpC,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,IAAI,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,GAAG,EAAE;gBACnD,GAAG,CAAC,KAAK,EAAE,CAAA;gBACX,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YACvC,CAAC,CAAC,CACH,CACF,CAAA;QACH,CAAC;IACH,CAAC;CACF,CAAA;AAED,kBAAe,GAAG,CAAA"}