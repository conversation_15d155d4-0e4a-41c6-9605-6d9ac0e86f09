import { IFileDetector, IMidwayContainer } from '../interface';
export declare abstract class AbstractFileDetector<T> implements IFileDetector {
    options: T;
    extraDetectorOptions: T;
    constructor(options?: T);
    abstract run(container: IMidwayContainer): void | Promise<void>;
    setExtraDetectorOptions(detectorOptions: T): void;
}
/**
 * CommonJS module loader
 */
export declare class CommonJSFileDetector extends AbstractFileDetector<{
    loadDir?: string | string[];
    pattern?: string | string[];
    ignore?: string | string[];
    namespace?: string;
    conflictCheck?: boolean;
}> {
    private duplicateModuleCheckSet;
    run(container: any): void | Promise<void>;
    loadSync(container: any): void;
    loadAsync(container: any): Promise<void>;
    getType(): 'commonjs' | 'module';
}
/**
 * ES module loader
 */
export declare class ESModuleFileDetector extends CommonJSFileDetector {
    getType(): 'commonjs' | 'module';
}
export declare class CustomModuleDetector extends AbstractFileDetector<{
    modules?: any[];
    namespace?: string;
}> {
    run(container: any): Promise<void>;
}
//# sourceMappingURL=fileDetector.d.ts.map