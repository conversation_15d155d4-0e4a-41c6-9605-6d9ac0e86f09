"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TAGGED_CLS = exports.INJECT_CUSTOM_PARAM = exports.INJECT_CUSTOM_METHOD = exports.INJECT_CUSTOM_PROPERTY = exports.INJECT_TAG = exports.NAMED_TAG = exports.CLASS_KEY_CONSTRUCTOR = exports.APPLICATION_CONTEXT_KEY = exports.APPLICATION_KEY = exports.LOGGER_KEY = exports.PLUGIN_KEY = exports.CONFIG_KEY = exports.MS_HSF_METHOD_KEY = exports.MS_DUBBO_METHOD_KEY = exports.MS_GRPC_METHOD_KEY = exports.MS_PROVIDER_KEY = exports.MS_PRODUCER_KEY = exports.MS_CONSUMER_KEY = exports.RPC_DUBBO_KEY = exports.RPC_GRPC_KEY = exports.HSF_KEY = exports.WS_EVENT_KEY = exports.WS_CONTROLLER_KEY = exports.MODULE_TASK_QUEUE_OPTIONS = exports.MODULE_TASK_QUEUE_KEY = exports.MODULE_TASK_TASK_LOCAL_OPTIONS = exports.MODULE_TASK_TASK_LOCAL_KEY = exports.MODULE_TASK_METADATA = exports.MODULE_TASK_KEY = exports.WEB_RESPONSE_RENDER = exports.WEB_RESPONSE_CONTENT_TYPE = exports.WEB_RESPONSE_HEADER = exports.WEB_RESPONSE_REDIRECT = exports.WEB_RESPONSE_HTTP_CODE = exports.WEB_RESPONSE_KEY = exports.WEB_ROUTER_PARAM_KEY = exports.WEB_ROUTER_KEY = exports.CONTROLLER_KEY = exports.SERVERLESS_FUNC_KEY = exports.FUNC_KEY = exports.FACTORY_SERVICE_CLIENT_KEY = exports.MOCK_KEY = exports.GUARD_KEY = exports.MATCH_KEY = exports.CATCH_KEY = exports.ASPECT_KEY = exports.FRAMEWORK_KEY = exports.CONFIGURATION_KEY = exports.SCHEDULE_KEY = exports.ALL = void 0;
exports.PRIVATE_META_DATA_KEY = exports.MAIN_MODULE_KEY = exports.LIFECYCLE_IDENTIFIER_PREFIX = exports.PIPELINE_IDENTIFIER = exports.OBJ_DEF_CLS = exports.TAGGED_FUN = void 0;
// got all value with no property name
exports.ALL = 'common:all_value_key';
// common
exports.SCHEDULE_KEY = 'common:schedule';
exports.CONFIGURATION_KEY = 'common:configuration';
exports.FRAMEWORK_KEY = 'common:framework';
exports.ASPECT_KEY = 'common:aspect';
exports.CATCH_KEY = 'common:catch';
exports.MATCH_KEY = 'common:match';
exports.GUARD_KEY = 'common:guard';
exports.MOCK_KEY = 'common:mock';
exports.FACTORY_SERVICE_CLIENT_KEY = 'common:service_factory:client';
// faas
exports.FUNC_KEY = 'faas:func';
exports.SERVERLESS_FUNC_KEY = 'faas:serverless:function';
// web
exports.CONTROLLER_KEY = 'web:controller';
exports.WEB_ROUTER_KEY = 'web:router';
exports.WEB_ROUTER_PARAM_KEY = 'web:router_param';
exports.WEB_RESPONSE_KEY = 'web:response';
exports.WEB_RESPONSE_HTTP_CODE = 'web:response_http_code';
exports.WEB_RESPONSE_REDIRECT = 'web:response_redirect';
exports.WEB_RESPONSE_HEADER = 'web:response_header';
exports.WEB_RESPONSE_CONTENT_TYPE = 'web:response_content_type';
exports.WEB_RESPONSE_RENDER = 'web:response_render';
// task
exports.MODULE_TASK_KEY = 'task:task';
exports.MODULE_TASK_METADATA = 'task:task:options';
exports.MODULE_TASK_TASK_LOCAL_KEY = 'task:task:task_local';
exports.MODULE_TASK_TASK_LOCAL_OPTIONS = 'task:task:task_local:options';
exports.MODULE_TASK_QUEUE_KEY = 'task:task:queue';
exports.MODULE_TASK_QUEUE_OPTIONS = 'task:task:queue:options';
// ws
exports.WS_CONTROLLER_KEY = 'ws:controller';
exports.WS_EVENT_KEY = 'ws:event';
// RPC
exports.HSF_KEY = 'rpc:hsf';
exports.RPC_GRPC_KEY = 'rpc:grpc';
exports.RPC_DUBBO_KEY = 'rpc:dubbo';
// microservice
exports.MS_CONSUMER_KEY = 'ms:consumer';
exports.MS_PRODUCER_KEY = 'ms:producer';
exports.MS_PROVIDER_KEY = 'ms:provider';
// rpc method
exports.MS_GRPC_METHOD_KEY = 'ms:grpc:method';
exports.MS_DUBBO_METHOD_KEY = 'ms:dubbo:method';
exports.MS_HSF_METHOD_KEY = 'ms:hsf:method';
// framework
exports.CONFIG_KEY = 'config';
exports.PLUGIN_KEY = 'plugin';
exports.LOGGER_KEY = 'logger';
exports.APPLICATION_KEY = '__midway_framework_app__';
exports.APPLICATION_CONTEXT_KEY = '__midway_application_context__';
////////////////////////////////////////// inject keys
// constructor key
exports.CLASS_KEY_CONSTRUCTOR = 'midway:class_key_constructor';
// Used for named bindings
exports.NAMED_TAG = 'named';
// The name of the target at design time
exports.INJECT_TAG = 'inject';
// The name inject custom property decorator with resolver
exports.INJECT_CUSTOM_PROPERTY = 'inject_custom_property';
// The name inject custom param decorator with resolver
exports.INJECT_CUSTOM_METHOD = 'inject_custom_method';
// The name inject custom param decorator with resolver
exports.INJECT_CUSTOM_PARAM = 'inject_custom_param';
//
// // used to store constructor arguments tags
// export const TAGGED = 'injection:tagged';
//
// // used to store class properties tags
// export const TAGGED_PROP = 'injection:tagged_props';
// used to store class to be injected
exports.TAGGED_CLS = 'injection:tagged_class';
// used to store function to be injected
exports.TAGGED_FUN = 'injection:tagged_function';
exports.OBJ_DEF_CLS = 'injection:object_definition_class';
// pipeline
exports.PIPELINE_IDENTIFIER = '__pipeline_identifier__';
// lifecycle interface
exports.LIFECYCLE_IDENTIFIER_PREFIX = '__lifecycle__';
exports.MAIN_MODULE_KEY = '__main__';
exports.PRIVATE_META_DATA_KEY = '__midway_private_meta_data__';
//# sourceMappingURL=constant.js.map