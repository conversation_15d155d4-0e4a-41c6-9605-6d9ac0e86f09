 -- 初始化系统管理菜单的按钮权限
-- 此脚本用于确保菜单管理、用户管理、角色管理有正确的按钮权限

-- 1. 查找系统管理相关菜单的ID
SELECT id, name, router, type FROM merchant_sys_menu 
WHERE router IN ('/system/menu', '/system/user', '/system/role') AND type = 1;

-- 2. 删除已存在的按钮权限（避免重复）
DELETE FROM merchant_sys_menu 
WHERE parentId IN (
  SELECT id FROM (
    SELECT id FROM merchant_sys_menu 
    WHERE router IN ('/system/menu', '/system/user', '/system/role') AND type = 1
  ) AS temp
) AND type = 2;

-- 3. 为菜单管理添加按钮权限
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '新增' as name,
  '' as router,
  'add' as perms,
  2 as type,
  '' as icon,
  1 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/menu' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '编辑' as name,
  '' as router,
  'edit' as perms,
  2 as type,
  '' as icon,
  2 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/menu' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '删除' as name,
  '' as router,
  'delete' as perms,
  2 as type,
  '' as icon,
  3 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/menu' AND type = 1;

-- 4. 为用户管理添加按钮权限
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '新增' as name,
  '' as router,
  'add' as perms,
  2 as type,
  '' as icon,
  1 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/user' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '编辑' as name,
  '' as router,
  'edit' as perms,
  2 as type,
  '' as icon,
  2 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/user' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '删除' as name,
  '' as router,
  'delete' as perms,
  2 as type,
  '' as icon,
  3 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/user' AND type = 1;

-- 5. 为角色管理添加按钮权限
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '新增' as name,
  '' as router,
  'add' as perms,
  2 as type,
  '' as icon,
  1 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/role' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '编辑' as name,
  '' as router,
  'edit' as perms,
  2 as type,
  '' as icon,
  2 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/role' AND type = 1;

INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
SELECT 
  id as parentId,
  '删除' as name,
  '' as router,
  'delete' as perms,
  2 as type,
  '' as icon,
  3 as orderNum,
  0 as keepAlive,
  0 as isShow,
  NOW() as createTime,
  NOW() as updateTime
FROM merchant_sys_menu WHERE router = '/system/role' AND type = 1;

-- 6. 查询验证结果
SELECT 
  m1.name AS menu_name,
  m1.router AS menu_router,
  m2.name AS button_name,
  m2.perms AS button_perms,
  m2.type AS button_type
FROM merchant_sys_menu m1
LEFT JOIN merchant_sys_menu m2 ON m1.id = m2.parentId
WHERE m1.router IN ('/system/menu', '/system/user', '/system/role') 
  AND m1.type = 1
ORDER BY m1.id, m2.orderNum;