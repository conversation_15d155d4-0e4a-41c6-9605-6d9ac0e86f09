{"name": "@keyv/serialize", "version": "1.0.3", "description": "Serialization for Keyv", "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}}, "xo": {"rules": {"import/no-named-as-default": "off", "unicorn/prefer-module": "off", "unicorn/prefer-event-target": "off", "unicorn/no-array-reduce": "off", "n/prefer-global/process": "off", "node/prefer-global/process": "off", "unicorn/prefer-object-from-entries": "off", "unicorn/prefer-node-protocol": "off", "@typescript-eslint/consistent-type-imports": "off", "@typescript-eslint/consistent-type-definitions": "off", "@typescript-eslint/no-empty-function": "off", "import/extensions": "off", "no-promise-executor-return": "off", "import/no-extraneous-dependencies": "off", "@typescript-eslint/no-unsafe-call": "off", "@typescript-eslint/no-unsafe-assignment": "off"}}, "repository": {"type": "git", "url": "git+https://github.com/jaredwray/keyv.git"}, "keywords": ["keyv", "serialize", "key", "value", "store"], "author": "<PERSON> <<EMAIL>> (https://jaredwray.com)", "license": "MIT", "bugs": {"url": "https://github.com/jaredwray/keyv/issues"}, "homepage": "https://github.com/jaredwray/keyv", "dependencies": {"buffer": "^6.0.3"}, "devDependencies": {"rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsd": "^0.31.2", "typescript": "^5.7.3", "xo": "^0.60.0", "@keyv/test-suite": "^2.0.4", "keyv": "^5.2.3"}, "tsd": {"directory": "test"}, "files": ["dist", "LICENSE"], "scripts": {"build": "rimraf ./dist && tsup src/index.ts --format cjs,esm --dts --clean", "test": "xo --fix && vitest run --coverage", "test:ci": "xo && vitest --run --sequence.setupFiles=list", "clean": "rimraf ./node_modules ./coverage ./dist"}}