import { MidwayHttpError, ResOrMessage } from './base';
export declare enum HttpStatus {
    CONTINUE = 100,
    SWITCHING_PROTOCOLS = 101,
    PROCESSING = 102,
    EARLYHINTS = 103,
    OK = 200,
    CREATED = 201,
    ACCEPTED = 202,
    NON_AUTHORITATIVE_INFORMATION = 203,
    NO_CONTENT = 204,
    RESET_CONTENT = 205,
    PARTIAL_CONTENT = 206,
    AMBIGUOUS = 300,
    MOVED_PERMANENTLY = 301,
    FOUND = 302,
    SEE_OTHER = 303,
    NOT_MODIFIED = 304,
    TEMPORARY_REDIRECT = 307,
    PERMANENT_REDIRECT = 308,
    BAD_REQUEST = 400,
    UNAUTHORIZED = 401,
    PAYMENT_REQUIRED = 402,
    FORBIDDEN = 403,
    NOT_FOUND = 404,
    METHOD_NOT_ALLOWED = 405,
    NOT_ACCEPTABLE = 406,
    PROXY_AUTHENTICATION_REQUIRED = 407,
    REQUEST_TIMEOUT = 408,
    CONFLICT = 409,
    GONE = 410,
    LENGTH_REQUIRED = 411,
    PRECONDITION_FAILED = 412,
    PAYLOAD_TOO_LARGE = 413,
    URI_TOO_LONG = 414,
    UNSUPPORTED_MEDIA_TYPE = 415,
    REQUESTED_RANGE_NOT_SATISFIABLE = 416,
    EXPECTATION_FAILED = 417,
    I_AM_A_TEAPOT = 418,
    MISDIRECTED = 421,
    UNPROCESSABLE_ENTITY = 422,
    FAILED_DEPENDENCY = 424,
    PRECONDITION_REQUIRED = 428,
    TOO_MANY_REQUESTS = 429,
    INTERNAL_SERVER_ERROR = 500,
    NOT_IMPLEMENTED = 501,
    BAD_GATEWAY = 502,
    SERVICE_UNAVAILABLE = 503,
    GATEWAY_TIMEOUT = 504,
    HTTP_VERSION_NOT_SUPPORTED = 505
}
/**
 * 400 http error, Means that the request can be fulfilled because of the bad syntax.
 */
export declare class BadRequestError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 401 http error, Means that the request was legal, but the server is rejecting to answer it. For the use when authentication is required and has failed or has not yet been provided.
 */
export declare class UnauthorizedError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 	4o4 http error, Means that the requested page cannot be found at the moment, but it may be available again in the future.
 */
export declare class NotFoundError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 403 http error, Means that the request is legal, but the server is rejecting to answer it.
 */
export declare class ForbiddenError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 406 http error, Means that the server can only generate an answer which the client doesn't accept.
 */
export declare class NotAcceptableError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 408 http error, Means that the server timed out waiting for the request.
 */
export declare class RequestTimeoutError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 409 http error, Means that the request cannot be completed, because of a conflict in the request.
 */
export declare class ConflictError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 410 http error, Means that the requested page is not available anymore.
 */
export declare class GoneError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 413 http error, Means that the request entity is too large and that's why the server won't accept the request.
 */
export declare class PayloadTooLargeError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 415 http error, Means that the media type is not supported and that's why the server won't accept the request.
 */
export declare class UnsupportedMediaTypeError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
export declare class UnprocessableEntityError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 500 http error, Is a generic error and users receive this error message when there is no more suitable specific message.
 */
export declare class InternalServerErrorError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 501 http error, Means that the server doesn't recognize the request method or it lacks the ability to fulfill the request.
 */
export declare class NotImplementedError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 502 http error, Means that the server was acting as a gateway or proxy and it received an invalid answer from the upstream server.
 */
export declare class BadGatewayError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 503 http error, Means that the server is not available now (It may be overloaded or down).
 */
export declare class ServiceUnavailableError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
/**
 * 504 http error, Means that the server was acting as a gateway or proxy and it didn't get answer on time from the upstream server.
 */
export declare class GatewayTimeoutError extends MidwayHttpError {
    constructor(resOrMessage?: ResOrMessage);
}
export declare const httpError: {
    BadRequestError: typeof BadRequestError;
    UnauthorizedError: typeof UnauthorizedError;
    NotFoundError: typeof NotFoundError;
    ForbiddenError: typeof ForbiddenError;
    NotAcceptableError: typeof NotAcceptableError;
    RequestTimeoutError: typeof RequestTimeoutError;
    ConflictError: typeof ConflictError;
    GoneError: typeof GoneError;
    PayloadTooLargeError: typeof PayloadTooLargeError;
    UnsupportedMediaTypeError: typeof UnsupportedMediaTypeError;
    UnprocessableEntityError: typeof UnprocessableEntityError;
    InternalServerErrorError: typeof InternalServerErrorError;
    NotImplementedError: typeof NotImplementedError;
    BadGatewayError: typeof BadGatewayError;
    ServiceUnavailableError: typeof ServiceUnavailableError;
    GatewayTimeoutError: typeof GatewayTimeoutError;
};
//# sourceMappingURL=http.d.ts.map