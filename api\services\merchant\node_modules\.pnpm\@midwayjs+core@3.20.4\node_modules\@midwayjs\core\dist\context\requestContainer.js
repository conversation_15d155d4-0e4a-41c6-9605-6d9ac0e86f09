"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayRequestContainer = void 0;
const container_1 = require("./container");
const decorator_1 = require("../decorator");
const constants_1 = require("../constants");
class MidwayRequestContainer extends container_1.MidwayContainer {
    constructor(ctx, applicationContext) {
        super(applicationContext);
        this.applicationContext = applicationContext;
        // update legacy relationship
        this.registry.setIdentifierRelation(this.applicationContext.registry.getIdentifierRelation());
        this.ctx = ctx;
        // register ctx
        this.registerObject(constants_1.REQUEST_CTX_KEY, ctx);
        // register res
        this.registerObject('res', {});
        if (ctx.logger) {
            // register contextLogger
            this.registerObject('logger', ctx.logger);
        }
    }
    init() {
        // do nothing
    }
    get(identifier, args) {
        if (typeof identifier !== 'string') {
            identifier = this.getIdentifier(identifier);
        }
        if (this.registry.hasObject(identifier)) {
            return this.registry.getObject(identifier);
        }
        const definition = this.applicationContext.registry.getDefinition(identifier);
        if (definition) {
            if (definition.isRequestScope() ||
                definition.id === decorator_1.PIPELINE_IDENTIFIER) {
                // create object from applicationContext definition for requestScope
                return this.getManagedResolverFactory().create({
                    definition,
                    args,
                });
            }
        }
        if (this.parent) {
            return this.parent.get(identifier, args);
        }
    }
    async getAsync(identifier, args) {
        if (typeof identifier !== 'string') {
            identifier = this.getIdentifier(identifier);
        }
        if (this.registry.hasObject(identifier)) {
            return this.registry.getObject(identifier);
        }
        const definition = this.applicationContext.registry.getDefinition(identifier);
        if (definition) {
            if (definition.isRequestScope() ||
                definition.id === decorator_1.PIPELINE_IDENTIFIER) {
                // create object from applicationContext definition for requestScope
                return this.getManagedResolverFactory().createAsync({
                    definition,
                    args,
                });
            }
        }
        if (this.parent) {
            return this.parent.getAsync(identifier, args);
        }
    }
    async ready() {
        // ignore other things
    }
    getContext() {
        return this.ctx;
    }
}
exports.MidwayRequestContainer = MidwayRequestContainer;
//# sourceMappingURL=requestContainer.js.map