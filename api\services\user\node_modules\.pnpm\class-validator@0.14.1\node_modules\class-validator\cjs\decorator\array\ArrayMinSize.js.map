{"version": 3, "file": "ArrayMinSize.js", "sourceRoot": "", "sources": ["../../../../src/decorator/array/ArrayMinSize.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,SAAgB,YAAY,CAAC,KAAc,EAAE,GAAW;IACtD,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,CAAC;AACrD,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAAC,GAAW,EAAE,iBAAqC;IAC7E,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,WAAW,EAAE,CAAC,GAAG,CAAC;QAClB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC7E,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,uDAAuD,EAClF,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,oCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const ARRAY_MIN_SIZE = 'arrayMinSize';\n\n/**\n * Checks if the array's length is greater than or equal to the specified number.\n * If null or undefined is given then this function returns false.\n */\nexport function arrayMinSize(array: unknown, min: number): boolean {\n  return Array.isArray(array) && array.length >= min;\n}\n\n/**\n * Checks if the array's length is greater than or equal to the specified number.\n * If null or undefined is given then this function returns false.\n */\nexport function ArrayMinSize(min: number, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: ARRAY_MIN_SIZE,\n      constraints: [min],\n      validator: {\n        validate: (value, args): boolean => arrayMinSize(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain at least $constraint1 elements',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}