import { ConnectionIsNotSetError, } from "../..";
import { DriverPackageNotInstalledError } from "../../error/DriverPackageNotInstalledError";
import { TypeORMError } from "../../error/TypeORMError";
import { PlatformTools } from "../../platform/PlatformTools";
import { RdbmsSchemaBuilder } from "../../schema-builder/RdbmsSchemaBuilder";
import { ApplyValueTransformers } from "../../util/ApplyValueTransformers";
import { DateUtils } from "../../util/DateUtils";
import { OrmUtils } from "../../util/OrmUtils";
import { SapQueryRunner } from "./SapQueryRunner";
import { DriverUtils } from "../DriverUtils";
import { InstanceChecker } from "../../util/InstanceChecker";
/**
 * Organizes communication with SAP Hana DBMS.
 *
 * todo: looks like there is no built in support for connection pooling, we need to figure out something
 */
export class SapDriver {
    // -------------------------------------------------------------------------
    // Constructor
    // -------------------------------------------------------------------------
    constructor(connection) {
        /**
         * Pool for slave databases.
         * Used in replication.
         */
        this.slaves = [];
        /**
         * Indicates if replication is enabled.
         */
        this.isReplicated = false;
        /**
         * Indicates if tree tables are supported by this driver.
         */
        this.treeSupport = true;
        /**
         * Represent transaction support by this driver
         */
        this.transactionSupport = "simple";
        /**
         * Gets list of supported column data types by a driver.
         *
         * @see https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/20a1569875191014b507cf392724b7eb.html
         * @see https://help.sap.com/docs/hana-cloud-database/sap-hana-cloud-sap-hana-database-sql-reference-guide/data-types
         */
        this.supportedDataTypes = [
            "tinyint",
            "smallint",
            "int", // typeorm alias for "integer"
            "integer",
            "bigint",
            "smalldecimal",
            "decimal",
            "dec", // typeorm alias for "decimal"
            "real",
            "double",
            "float", // database alias for "real" / "double"
            "date",
            "time",
            "seconddate",
            "timestamp",
            "boolean",
            "char", // not officially supported, in SAP HANA Cloud: alias for "nchar"
            "nchar", // not officially supported
            "varchar", // in SAP HANA Cloud: alias for "nvarchar"
            "nvarchar",
            "text", // removed in SAP HANA Cloud
            "alphanum", // removed in SAP HANA Cloud
            "shorttext", // removed in SAP HANA Cloud
            "array",
            "varbinary",
            "blob",
            "clob", // in SAP HANA Cloud: alias for "nclob"
            "nclob",
            "st_geometry",
            "st_point",
        ];
        /**
         * Returns type of upsert supported by driver if any
         */
        this.supportedUpsertTypes = ["merge-into"];
        /**
         * Gets list of spatial column data types.
         */
        this.spatialTypes = ["st_geometry", "st_point"];
        /**
         * Gets list of column data types that support length by a driver.
         */
        this.withLengthColumnTypes = [
            "varchar",
            "nvarchar",
            "alphanum",
            "shorttext",
            "varbinary",
        ];
        /**
         * Gets list of column data types that support precision by a driver.
         */
        this.withPrecisionColumnTypes = ["decimal"];
        /**
         * Gets list of column data types that support scale by a driver.
         */
        this.withScaleColumnTypes = ["decimal"];
        /**
         * Orm has special columns and we need to know what database column types should be for those types.
         * Column types are driver dependant.
         */
        this.mappedDataTypes = {
            createDate: "timestamp",
            createDateDefault: "CURRENT_TIMESTAMP",
            updateDate: "timestamp",
            updateDateDefault: "CURRENT_TIMESTAMP",
            deleteDate: "timestamp",
            deleteDateNullable: true,
            version: "integer",
            treeLevel: "integer",
            migrationId: "integer",
            migrationName: "nvarchar",
            migrationTimestamp: "bigint",
            cacheId: "integer",
            cacheIdentifier: "nvarchar",
            cacheTime: "bigint",
            cacheDuration: "integer",
            cacheQuery: "nvarchar(5000)",
            cacheResult: "nclob",
            metadataType: "nvarchar",
            metadataDatabase: "nvarchar",
            metadataSchema: "nvarchar",
            metadataTable: "nvarchar",
            metadataName: "nvarchar",
            metadataValue: "nvarchar(5000)",
        };
        /**
         * Default values of length, precision and scale depends on column data type.
         * Used in the cases when length/precision/scale is not specified by user.
         */
        this.dataTypeDefaults = {
            char: { length: 1 },
            nchar: { length: 1 },
            varchar: { length: 255 },
            nvarchar: { length: 255 },
            shorttext: { length: 255 },
            varbinary: { length: 255 },
            decimal: { precision: 18, scale: 0 },
        };
        /**
         * Max length allowed by SAP HANA for aliases (identifiers).
         * @see https://help.sap.com/viewer/4fe29514fd584807ac9f2a04f6754767/2.0.03/en-US/20a760537519101497e3cfe07b348f3c.html
         */
        this.maxAliasLength = 128;
        this.cteCapabilities = {
            enabled: true,
        };
        this.dummyTableName = `SYS.DUMMY`;
        this.connection = connection;
        this.options = connection.options;
        this.loadDependencies();
        this.database = DriverUtils.buildDriverOptions(this.options).database;
        this.schema = DriverUtils.buildDriverOptions(this.options).schema;
    }
    // -------------------------------------------------------------------------
    // Public Implemented Methods
    // -------------------------------------------------------------------------
    /**
     * Performs connection to the database.
     * Based on pooling options, it can either create connection immediately,
     * either create a pool and create connection when needed.
     */
    async connect() {
        // HANA connection info
        const dbParams = {
            hostName: this.options.host,
            port: this.options.port,
            userName: this.options.username,
            password: this.options.password,
            ...this.options.extra,
        };
        if (this.options.database)
            dbParams.databaseName = this.options.database;
        if (this.options.schema)
            dbParams.currentSchema = this.options.schema;
        if (this.options.encrypt)
            dbParams.encrypt = this.options.encrypt;
        if (this.options.sslValidateCertificate)
            dbParams.validateCertificate = this.options.sslValidateCertificate;
        if (this.options.key)
            dbParams.key = this.options.key;
        if (this.options.cert)
            dbParams.cert = this.options.cert;
        if (this.options.ca)
            dbParams.ca = this.options.ca;
        // pool options
        const options = {
            min: this.options.pool && this.options.pool.min
                ? this.options.pool.min
                : 1,
            max: this.options.pool && this.options.pool.max
                ? this.options.pool.max
                : 10,
        };
        if (this.options.pool && this.options.pool.checkInterval)
            options.checkInterval = this.options.pool.checkInterval;
        if (this.options.pool && this.options.pool.maxWaitingRequests)
            options.maxWaitingRequests = this.options.pool.maxWaitingRequests;
        if (this.options.pool && this.options.pool.requestTimeout)
            options.requestTimeout = this.options.pool.requestTimeout;
        if (this.options.pool && this.options.pool.idleTimeout)
            options.idleTimeout = this.options.pool.idleTimeout;
        const { logger } = this.connection;
        const poolErrorHandler = options.poolErrorHandler ||
            ((error) => logger.log("warn", `SAP Hana pool raised an error. ${error}`));
        this.client.eventEmitter.on("poolError", poolErrorHandler);
        // create the pool
        this.master = this.client.createPool(dbParams, options);
        const queryRunner = this.createQueryRunner("master");
        const { version, database } = await queryRunner.getDatabaseAndVersion();
        this.version = version;
        this.database = database;
        if (!this.schema) {
            this.schema = await queryRunner.getCurrentSchema();
        }
        await queryRunner.release();
    }
    /**
     * Makes any action after connection (e.g. create extensions in Postgres driver).
     */
    afterConnect() {
        return Promise.resolve();
    }
    /**
     * Closes connection with the database.
     */
    async disconnect() {
        const pool = this.master;
        if (!pool) {
            throw new ConnectionIsNotSetError("sap");
        }
        this.master = undefined;
        await pool.clear();
    }
    /**
     * Creates a schema builder used to build and sync a schema.
     */
    createSchemaBuilder() {
        return new RdbmsSchemaBuilder(this.connection);
    }
    /**
     * Creates a query runner used to execute database queries.
     */
    createQueryRunner(mode) {
        return new SapQueryRunner(this, mode);
    }
    /**
     * Replaces parameters in the given sql with special escaping character
     * and an array of parameter names to be passed to a query.
     */
    escapeQueryWithParameters(sql, parameters, nativeParameters) {
        const escapedParameters = Object.keys(nativeParameters).map((key) => {
            if (nativeParameters[key] instanceof Date)
                return DateUtils.mixedDateToDatetimeString(nativeParameters[key], true);
            return nativeParameters[key];
        });
        if (!parameters || !Object.keys(parameters).length)
            return [sql, escapedParameters];
        sql = sql.replace(/:(\.\.\.)?([A-Za-z0-9_.]+)/g, (full, isArray, key) => {
            if (!parameters.hasOwnProperty(key)) {
                return full;
            }
            const value = parameters[key];
            if (isArray) {
                return value
                    .map((v) => {
                    escapedParameters.push(v);
                    return this.createParameter(key, escapedParameters.length - 1);
                })
                    .join(", ");
            }
            if (typeof value === "function") {
                return value();
            }
            if (value instanceof Date) {
                return DateUtils.mixedDateToDatetimeString(value, true);
            }
            escapedParameters.push(value);
            return this.createParameter(key, escapedParameters.length - 1);
        }); // todo: make replace only in value statements, otherwise problems
        return [sql, escapedParameters];
    }
    /**
     * Escapes a column name.
     */
    escape(columnName) {
        return `"${columnName}"`;
    }
    /**
     * Build full table name with schema name and table name.
     * E.g. myDB.mySchema.myTable
     */
    buildTableName(tableName, schema) {
        const tablePath = [tableName];
        if (schema) {
            tablePath.unshift(schema);
        }
        return tablePath.join(".");
    }
    /**
     * Parse a target table name or other types and return a normalized table definition.
     */
    parseTableName(target) {
        const driverDatabase = this.database;
        const driverSchema = this.schema;
        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {
            const parsed = this.parseTableName(target.name);
            return {
                database: target.database || parsed.database || driverDatabase,
                schema: target.schema || parsed.schema || driverSchema,
                tableName: parsed.tableName,
            };
        }
        if (InstanceChecker.isTableForeignKey(target)) {
            const parsed = this.parseTableName(target.referencedTableName);
            return {
                database: target.referencedDatabase ||
                    parsed.database ||
                    driverDatabase,
                schema: target.referencedSchema || parsed.schema || driverSchema,
                tableName: parsed.tableName,
            };
        }
        if (InstanceChecker.isEntityMetadata(target)) {
            // EntityMetadata tableName is never a path
            return {
                database: target.database || driverDatabase,
                schema: target.schema || driverSchema,
                tableName: target.tableName,
            };
        }
        const parts = target.split(".");
        return {
            database: driverDatabase,
            schema: (parts.length > 1 ? parts[0] : undefined) || driverSchema,
            tableName: parts.length > 1 ? parts[1] : parts[0],
        };
    }
    /**
     * Prepares given value to a value to be persisted, based on its column type and metadata.
     */
    preparePersistentValue(value, columnMetadata) {
        if (columnMetadata.transformer)
            value = ApplyValueTransformers.transformTo(columnMetadata.transformer, value);
        if (value === null || value === undefined)
            return value;
        if (columnMetadata.type === Boolean) {
            return value === true ? 1 : 0;
        }
        else if (columnMetadata.type === "date") {
            return DateUtils.mixedDateToDateString(value);
        }
        else if (columnMetadata.type === "time") {
            return DateUtils.mixedDateToTimeString(value);
        }
        else if (columnMetadata.type === "timestamp" ||
            columnMetadata.type === Date) {
            return DateUtils.mixedDateToDatetimeString(value, true);
        }
        else if (columnMetadata.type === "seconddate") {
            return DateUtils.mixedDateToDatetimeString(value, false);
        }
        else if (columnMetadata.type === "simple-array") {
            return DateUtils.simpleArrayToString(value);
        }
        else if (columnMetadata.type === "simple-json") {
            return DateUtils.simpleJsonToString(value);
        }
        else if (columnMetadata.type === "simple-enum") {
            return DateUtils.simpleEnumToString(value);
        }
        else if (columnMetadata.isArray) {
            return () => `ARRAY(${value.map((it) => `'${it}'`)})`;
        }
        return value;
    }
    /**
     * Prepares given value to a value to be persisted, based on its column type or metadata.
     */
    prepareHydratedValue(value, columnMetadata) {
        if (value === null || value === undefined)
            return columnMetadata.transformer
                ? ApplyValueTransformers.transformFrom(columnMetadata.transformer, value)
                : value;
        if (columnMetadata.type === Boolean) {
            value = value ? true : false;
        }
        else if (columnMetadata.type === "timestamp" ||
            columnMetadata.type === "seconddate" ||
            columnMetadata.type === Date) {
            value = DateUtils.normalizeHydratedDate(value);
        }
        else if (columnMetadata.type === "date") {
            value = DateUtils.mixedDateToDateString(value);
        }
        else if (columnMetadata.type === "time") {
            value = DateUtils.mixedTimeToString(value);
        }
        else if (columnMetadata.type === "simple-array") {
            value = DateUtils.stringToSimpleArray(value);
        }
        else if (columnMetadata.type === "simple-json") {
            value = DateUtils.stringToSimpleJson(value);
        }
        else if (columnMetadata.type === "simple-enum") {
            value = DateUtils.stringToSimpleEnum(value, columnMetadata);
        }
        else if (columnMetadata.type === Number) {
            // convert to number if number
            value = !isNaN(+value) ? parseInt(value) : value;
        }
        if (columnMetadata.transformer)
            value = ApplyValueTransformers.transformFrom(columnMetadata.transformer, value);
        return value;
    }
    /**
     * Creates a database type from a given column metadata.
     */
    normalizeType(column) {
        if (column.type === Number || column.type === "int") {
            return "integer";
        }
        else if (column.type === "dec") {
            return "decimal";
        }
        else if (column.type === "float") {
            const length = typeof column.length === "string"
                ? parseInt(column.length)
                : column.length;
            // https://help.sap.com/docs/SAP_HANA_PLATFORM/4fe29514fd584807ac9f2a04f6754767/4ee2f261e9c44003807d08ccc2e249ac.html
            if (length && length < 25) {
                return "real";
            }
            return "double";
        }
        else if (column.type === String) {
            return "nvarchar";
        }
        else if (column.type === Date) {
            return "timestamp";
        }
        else if (column.type === Boolean) {
            return "boolean";
        }
        else if (column.type === Buffer) {
            return "blob";
        }
        else if (column.type === "uuid") {
            return "nvarchar";
        }
        else if (column.type === "simple-array" ||
            column.type === "simple-json") {
            return "nclob";
        }
        else if (column.type === "simple-enum") {
            return "nvarchar";
        }
        if (DriverUtils.isReleaseVersionOrGreater(this, "4.0")) {
            // SAP HANA Cloud deprecated / removed these data types
            if (column.type === "varchar" ||
                column.type === "alphanum" ||
                column.type === "shorttext") {
                return "nvarchar";
            }
            else if (column.type === "text" || column.type === "clob") {
                return "nclob";
            }
            else if (column.type === "char") {
                return "nchar";
            }
        }
        return column.type || "";
    }
    /**
     * Normalizes "default" value of the column.
     */
    normalizeDefault(columnMetadata) {
        const defaultValue = columnMetadata.default;
        if (typeof defaultValue === "number") {
            return `${defaultValue}`;
        }
        if (typeof defaultValue === "boolean") {
            return defaultValue ? "true" : "false";
        }
        if (typeof defaultValue === "function") {
            return defaultValue();
        }
        if (typeof defaultValue === "string") {
            return `'${defaultValue}'`;
        }
        if (defaultValue === null || defaultValue === undefined) {
            return undefined;
        }
        return `${defaultValue}`;
    }
    /**
     * Normalizes "isUnique" value of the column.
     */
    normalizeIsUnique(column) {
        return column.entityMetadata.indices.some((idx) => idx.isUnique &&
            idx.columns.length === 1 &&
            idx.columns[0] === column);
    }
    /**
     * Returns default column lengths, which is required on column creation.
     */
    getColumnLength(column) {
        if (column.length)
            return column.length.toString();
        if (column.generationStrategy === "uuid")
            return "36";
        switch (column.type) {
            case "varchar":
            case "nvarchar":
            case "shorttext":
            case String:
                return "255";
            case "alphanum":
                return "127";
            case "varbinary":
                return "255";
        }
        return "";
    }
    /**
     * Creates column type definition including length, precision and scale
     */
    createFullType(column) {
        let type = column.type;
        // used 'getColumnLength()' method, because SqlServer sets `varchar` and `nvarchar` length to 1 by default.
        if (this.getColumnLength(column)) {
            type += `(${this.getColumnLength(column)})`;
        }
        else if (column.precision !== null &&
            column.precision !== undefined &&
            column.scale !== null &&
            column.scale !== undefined) {
            type += `(${column.precision},${column.scale})`;
        }
        else if (column.precision !== null &&
            column.precision !== undefined) {
            type += `(${column.precision})`;
        }
        if (column.isArray)
            type += " array";
        return type;
    }
    /**
     * Obtains a new database connection to a master server.
     * Used for replication.
     * If replication is not setup then returns default connection's database connection.
     */
    obtainMasterConnection() {
        if (!this.master) {
            throw new TypeORMError("Driver not Connected");
        }
        return this.master.getConnection();
    }
    /**
     * Obtains a new database connection to a slave server.
     * Used for replication.
     * If replication is not setup then returns master (default) connection's database connection.
     */
    obtainSlaveConnection() {
        return this.obtainMasterConnection();
    }
    /**
     * Creates generated map of values generated or returned by database after INSERT query.
     */
    createGeneratedMap(metadata, insertResult) {
        const generatedMap = metadata.generatedColumns.reduce((map, generatedColumn) => {
            let value;
            if (generatedColumn.generationStrategy === "increment" &&
                insertResult) {
                value = insertResult;
                // } else if (generatedColumn.generationStrategy === "uuid") {
                //     console.log("getting db value:", generatedColumn.databaseName);
                //     value = generatedColumn.getEntityValue(uuidMap);
            }
            return OrmUtils.mergeDeep(map, generatedColumn.createValueMap(value));
        }, {});
        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined;
    }
    /**
     * Differentiate columns of this table and columns from the given column metadatas columns
     * and returns only changed.
     */
    findChangedColumns(tableColumns, columnMetadatas) {
        return columnMetadatas.filter((columnMetadata) => {
            const tableColumn = tableColumns.find((c) => c.name === columnMetadata.databaseName);
            if (!tableColumn) {
                // we don't need new columns, we only need exist and changed
                return false;
            }
            const normalizedDefault = this.normalizeDefault(columnMetadata);
            return (tableColumn.name !== columnMetadata.databaseName ||
                tableColumn.type !== this.normalizeType(columnMetadata) ||
                (columnMetadata.length &&
                    tableColumn.length !==
                        this.getColumnLength(columnMetadata)) ||
                tableColumn.precision !== columnMetadata.precision ||
                tableColumn.scale !== columnMetadata.scale ||
                tableColumn.comment !==
                    this.escapeComment(columnMetadata.comment) ||
                (!tableColumn.isGenerated &&
                    normalizedDefault !== tableColumn.default) || // we included check for generated here, because generated columns already can have default values
                tableColumn.isPrimary !== columnMetadata.isPrimary ||
                tableColumn.isNullable !== columnMetadata.isNullable ||
                tableColumn.isUnique !==
                    this.normalizeIsUnique(columnMetadata) ||
                (columnMetadata.generationStrategy !== "uuid" &&
                    tableColumn.isGenerated !== columnMetadata.isGenerated));
        });
    }
    /**
     * Returns true if driver supports RETURNING / OUTPUT statement.
     */
    isReturningSqlSupported() {
        return false;
    }
    /**
     * Returns true if driver supports uuid values generation on its own.
     */
    isUUIDGenerationSupported() {
        return false;
    }
    /**
     * Returns true if driver supports fulltext indices.
     */
    isFullTextColumnTypeSupported() {
        return !DriverUtils.isReleaseVersionOrGreater(this, "4.0");
    }
    /**
     * Creates an escaped parameter.
     */
    createParameter(parameterName, index) {
        return "?";
    }
    // -------------------------------------------------------------------------
    // Protected Methods
    // -------------------------------------------------------------------------
    /**
     * If driver dependency is not given explicitly, then try to load it via "require".
     */
    loadDependencies() {
        try {
            const client = this.options.driver || PlatformTools.load("hdb-pool");
            this.client = client;
        }
        catch (e) {
            // todo: better error for browser env
            throw new DriverPackageNotInstalledError("SAP Hana", "hdb-pool");
        }
        try {
            if (!this.options.hanaClientDriver) {
                PlatformTools.load("@sap/hana-client");
                this.streamClient = PlatformTools.load("@sap/hana-client/extension/Stream");
            }
        }
        catch (e) {
            // todo: better error for browser env
            throw new DriverPackageNotInstalledError("SAP Hana", "@sap/hana-client");
        }
    }
    /**
     * Escapes a given comment.
     */
    escapeComment(comment) {
        if (!comment)
            return comment;
        comment = comment.replace(/\u0000/g, ""); // Null bytes aren't allowed in comments
        return comment;
    }
}

//# sourceMappingURL=SapDriver.js.map
