"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatLocale = exports.formatWithObject = exports.formatWithArray = void 0;
const ARRAY_INDEX_RE = /\{(\d+)\}/g;
function formatWithArray(text, values) {
    return text.replace(ARRAY_INDEX_RE, (original, matched) => {
        const index = parseInt(matched);
        if (index < values.length) {
            return values[index];
        }
        // not match index, return original text
        return original;
    });
}
exports.formatWithArray = formatWithArray;
const Object_INDEX_RE = /\{(.+?)\}/g;
function formatWithObject(text, values) {
    return text.replace(Object_INDEX_RE, (original, matched) => {
        const value = values[matched];
        if (value) {
            return value;
        }
        // not match index, return original text
        return original;
    });
}
exports.formatWithObject = formatWithObject;
function formatLocale(locale) {
    // support zh_CN, en_US => zh-CN, en-US
    return locale.replace('_', '-').toLowerCase();
}
exports.formatLocale = formatLocale;
//# sourceMappingURL=utils.js.map