import { IMidwayBootstrapOptions, IMidwayContainer } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';
export declare class BootstrapStarter {
    protected appDir: string;
    protected baseDir: string;
    protected globalOptions: Partial<IMidwayBootstrapOptions>;
    protected globalConfig: any;
    private applicationContext;
    private eventBus;
    configure(options?: IMidwayBootstrapOptions): this;
    init(): Promise<IMidwayContainer>;
    run(): Promise<void>;
    stop(): Promise<void>;
    getApplicationContext(): IMidwayContainer;
    protected getBaseDir(): string;
}
export declare class Bootstrap {
    protected static starter: BootstrapStarter;
    protected static logger: ILogger;
    protected static configured: boolean;
    protected static bootstrapLoggerFactory: import("@midwayjs/logger").LoggerFactory;
    /**
     * set global configuration for midway
     * @param configuration
     */
    static configure(configuration?: IMidwayBootstrapOptions): typeof Bootstrap;
    static getStarter(): BootstrapStarter;
    static run(): Promise<IMidwayContainer>;
    static stop(): Promise<void>;
    static reset(): void;
    /**
     * on bootstrap receive a exit signal
     * @param signal
     */
    private static onSignal;
    /**
     * on bootstrap process exit
     * @param code
     */
    private static onExit;
    private static uncaughtExceptionHandler;
    private static unhandledRejectionHandler;
    static getApplicationContext(): IMidwayContainer;
}
//# sourceMappingURL=bootstrap.d.ts.map