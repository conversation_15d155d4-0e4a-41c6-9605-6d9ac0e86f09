{"@midwayjs/orm": "3.4.0", "@midwayjs/runtime-engine": "3.4.0", "@midwayjs/runtime-mock": "3.4.0", "@midwayjs/serverless-app": "3.4.0", "@midwayjs/serverless-aws-starter": "3.4.0", "@midwayjs/serverless-fc-starter": "3.4.0", "@midwayjs/serverless-fc-trigger": "3.4.0", "@midwayjs/serverless-scf-starter": "3.4.0", "@midwayjs/serverless-scf-trigger": "3.4.0", "@midwayjs/serverless-vercel-starter": "3.4.0", "@midwayjs/serverless-vercel-trigger": "3.4.0", "@midwayjs/egg-layer": "3.4.0", "@midwayjs/express-layer": "3.4.0", "@midwayjs/faas-typings": "3.3.5", "@midwayjs/koa-layer": "3.4.0", "@midwayjs/fc-starter": "3.4.0", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/serverless-worker-starter": "3.4.0", "@midwayjs/static-layer": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.0", "@midwayjs/axios": "3.4.0", "@midwayjs/bootstrap": "3.4.0", "@midwayjs/cache": "3.4.0", "@midwayjs/code-dye": "3.4.0", "@midwayjs/consul": "3.4.0", "@midwayjs/core": "3.4.0", "@midwayjs/cos": "3.4.0", "@midwayjs/cross-domain": "3.4.0", "@midwayjs/decorator": "3.4.0", "@midwayjs/express-session": "3.4.0", "@midwayjs/faas": "3.4.0", "@midwayjs/grpc": "3.4.0", "@midwayjs/http-proxy": "3.4.0", "@midwayjs/i18n": "3.4.0", "@midwayjs/info": "3.4.0", "@midwayjs/jwt": "3.4.0", "@midwayjs/kafka": "3.4.0", "@midwayjs/mikro": "3.4.0", "@midwayjs/mock": "3.4.0", "@midwayjs/mongoose": "3.4.0", "@midwayjs/oss": "3.4.0", "@midwayjs/otel": "3.4.0", "@midwayjs/passport": "3.4.0", "@midwayjs/process-agent": "3.4.0", "@midwayjs/prometheus-socket-io": "3.4.0", "@midwayjs/prometheus": "3.4.0", "@midwayjs/rabbitmq": "3.4.0", "@midwayjs/redis": "3.4.0", "@midwayjs/security": "3.4.0", "@midwayjs/sequelize": "3.4.0", "@midwayjs/session": "3.4.0", "@midwayjs/socketio": "3.4.0", "@midwayjs/static-file": "3.4.0", "@midwayjs/swagger": "3.4.0", "@midwayjs/tablestore": "3.4.0", "@midwayjs/task": "3.4.0", "@midwayjs/typegoose": "3.4.0", "@midwayjs/typeorm": "3.4.0", "@midwayjs/upload": "3.4.0", "@midwayjs/validate": "3.4.0", "@midwayjs/version": "3.4.0", "@midwayjs/view-ejs": "3.4.0", "@midwayjs/view-nunjucks": "3.4.0", "@midwayjs/view": "3.4.0", "@midwayjs/express": "3.4.0", "@midwayjs/koa": "3.4.0", "@midwayjs/web": "3.4.0", "@midwayjs/ws": "3.4.0"}