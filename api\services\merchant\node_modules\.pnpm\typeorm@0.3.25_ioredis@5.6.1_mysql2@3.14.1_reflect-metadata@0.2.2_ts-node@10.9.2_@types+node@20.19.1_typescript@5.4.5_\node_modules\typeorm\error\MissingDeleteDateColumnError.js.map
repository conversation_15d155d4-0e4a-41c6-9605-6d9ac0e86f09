{"version": 3, "sources": ["../../src/error/MissingDeleteDateColumnError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C,MAAa,4BAA6B,SAAQ,2BAAY;IAC1D,YAAY,cAA8B;QACtC,KAAK,CACD,WAAW,cAAc,CAAC,IAAI,sCAAsC,CACvE,CAAA;IACL,CAAC;CACJ;AAND,oEAMC", "file": "MissingDeleteDateColumnError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingDeleteDateColumnError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata) {\n        super(\n            `Entity \"${entityMetadata.name}\" does not have delete date columns.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}