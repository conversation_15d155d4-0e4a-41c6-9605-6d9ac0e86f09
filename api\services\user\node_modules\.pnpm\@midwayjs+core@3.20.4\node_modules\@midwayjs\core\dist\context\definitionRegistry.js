"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectDefinitionRegistry = void 0;
/**
 * Object Definition Registry 实现
 */
const decorator_1 = require("../decorator");
const PREFIX = '_id_default_';
class LegacyIdentifierRelation extends Map {
    saveClassRelation(module, namespace) {
        const providerId = (0, decorator_1.getProviderUUId)(module);
        // save uuid
        this.set(providerId, providerId);
        if (providerId) {
            // save alias id
            const aliasId = (0, decorator_1.getProviderId)(module);
            if (aliasId) {
                // save alias Id
                this.set(aliasId, providerId);
            }
            // save className alias
            this.set((0, decorator_1.getProviderName)(module), providerId);
            // save namespace alias
            if (namespace) {
                this.set(namespace + ':' + (0, decorator_1.getProviderName)(module), providerId);
            }
        }
    }
    saveFunctionRelation(id, uuid) {
        this.set(uuid, uuid);
        this.set(id, uuid);
    }
    hasRelation(id) {
        return this.has(id);
    }
    getRelation(id) {
        return this.get(id);
    }
}
class ObjectDefinitionRegistry extends Map {
    constructor() {
        super(...arguments);
        this.singletonIds = [];
        this._identifierRelation = new LegacyIdentifierRelation();
    }
    get identifierRelation() {
        if (!this._identifierRelation) {
            this._identifierRelation = new LegacyIdentifierRelation();
        }
        return this._identifierRelation;
    }
    set identifierRelation(identifierRelation) {
        this._identifierRelation = identifierRelation;
    }
    get identifiers() {
        const ids = [];
        for (const key of this.keys()) {
            if (key.indexOf(PREFIX) === -1) {
                ids.push(key);
            }
        }
        return ids;
    }
    get count() {
        return this.size;
    }
    getSingletonDefinitionIds() {
        return this.singletonIds;
    }
    getDefinitionByName(name) {
        const definitions = [];
        for (const v of this.values()) {
            const definition = v;
            if (definition.name === name) {
                definitions.push(definition);
            }
        }
        return definitions;
    }
    registerDefinition(identifier, definition) {
        if (definition.isSingletonScope()) {
            this.singletonIds.push(identifier);
        }
        this.set(identifier, definition);
    }
    getDefinition(identifier) {
        var _a;
        identifier = (_a = this.identifierRelation.getRelation(identifier)) !== null && _a !== void 0 ? _a : identifier;
        return this.get(identifier);
    }
    removeDefinition(identifier) {
        this.delete(identifier);
    }
    hasDefinition(identifier) {
        var _a;
        identifier = (_a = this.identifierRelation.getRelation(identifier)) !== null && _a !== void 0 ? _a : identifier;
        return this.has(identifier);
    }
    clearAll() {
        this.singletonIds = [];
        this.clear();
    }
    hasObject(identifier) {
        var _a;
        identifier = (_a = this.identifierRelation.getRelation(identifier)) !== null && _a !== void 0 ? _a : identifier;
        return this.has(PREFIX + identifier);
    }
    registerObject(identifier, target) {
        this.set(PREFIX + identifier, target);
    }
    getObject(identifier) {
        var _a;
        identifier = (_a = this.identifierRelation.getRelation(identifier)) !== null && _a !== void 0 ? _a : identifier;
        return this.get(PREFIX + identifier);
    }
    getIdentifierRelation() {
        return this.identifierRelation;
    }
    setIdentifierRelation(identifierRelation) {
        this.identifierRelation = identifierRelation;
    }
}
exports.ObjectDefinitionRegistry = ObjectDefinitionRegistry;
//# sourceMappingURL=definitionRegistry.js.map