{"version": 3, "sources": ["../../src/error/CannotConnectAlreadyConnectedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,kCAAmC,SAAQ,2BAAY;IAChE,YAAY,cAAsB;QAC9B,KAAK,CACD,oBAAoB,cAAc,sEAAsE,CAC3G,CAAA;IACL,CAAC;CACJ;AAND,gFAMC", "file": "CannotConnectAlreadyConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to connect when he already connected.\n */\nexport class CannotConnectAlreadyConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot create a \"${connectionName}\" connection because connection to the database already established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}