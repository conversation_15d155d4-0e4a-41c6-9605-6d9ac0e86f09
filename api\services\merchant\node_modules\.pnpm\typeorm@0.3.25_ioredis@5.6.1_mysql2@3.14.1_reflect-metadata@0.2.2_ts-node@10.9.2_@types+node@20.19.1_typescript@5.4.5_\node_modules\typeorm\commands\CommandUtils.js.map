{"version": 3, "sources": ["../../src/commands/CommandUtils.ts"], "names": [], "mappings": ";;;;AAAA,mEAA4B;AAC5B,wDAAuB;AACvB,oCAAuC;AAEvC,6DAAyD;AACzD,qDAAyD;AAEzD;;GAEG;AACH,MAAa,YAAY;IACrB,MAAM,CAAC,KAAK,CAAC,cAAc,CACvB,kBAA0B;QAE1B,IAAI,qBAAqB,CAAA;QACzB,IAAI,CAAC;YACD,CAAC;YAAA,CAAC,qBAAqB,CAAC,GAAG,MAAM,IAAA,iCAAmB,EAChD,kBAAkB,CACrB,CAAA;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,IAAI,KAAK,CACX,yBAAyB,kBAAkB,MAAM,GAAG,CAAC,OAAO,EAAE,CACjE,CAAA;QACL,CAAC;QAED,IACI,CAAC,qBAAqB;YACtB,OAAO,qBAAqB,KAAK,QAAQ,EAC3C,CAAC;YACC,MAAM,IAAI,KAAK,CACX,qEAAqE,CACxE,CAAA;QACL,CAAC;QAED,IAAI,iCAAe,CAAC,YAAY,CAAC,qBAAqB,CAAC,EAAE,CAAC;YACtD,OAAO,qBAAqB,CAAA;QAChC,CAAC;QAED,MAAM,iBAAiB,GAAG,EAAE,CAAA;QAC5B,KAAK,MAAM,aAAa,IAAI,qBAAqB,EAAE,CAAC;YAChD,MAAM,UAAU,GAAG,qBAAqB,CAAC,aAAa,CAAC,CAAA;YACvD,2FAA2F;YAC3F,+EAA+E;YAC/E,8FAA8F;YAC9F,iGAAiG;YACjG,MAAM,iBAAiB,GAAG,MAAM,UAAU,CAAA;YAC1C,IAAI,iCAAe,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE,CAAC;gBAClD,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;YAC7C,CAAC;QACL,CAAC;QAED,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CACX,qEAAqE,CACxE,CAAA;QACL,CAAC;QACD,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CACX,4EAA4E,CAC/E,CAAA;QACL,CAAC;QACD,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAiB;QAC5C,MAAM,kBAAE,CAAC,KAAK,CAAC,SAAS,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,UAAU,CACnB,QAAgB,EAChB,OAAe,EACf,WAAoB,IAAI;QAExB,MAAM,YAAY,CAAC,iBAAiB,CAAC,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAA;QAC5D,IAAI,QAAQ,KAAK,KAAK,IAAI,CAAC,MAAM,YAAY,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YAClE,OAAM;QACV,CAAC;QACD,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAClC,MAAM,IAAI,GAAG,MAAM,kBAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;QAExC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAA;IAC1B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,QAAgB;QACpC,IAAI,CAAC;YACD,MAAM,kBAAE,CAAC,MAAM,CAAC,QAAQ,EAAE,kBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;YAC5C,OAAO,IAAI,CAAA;QACf,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAA;QAChB,CAAC;IACL,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,uBAA4B;QAC5C,IACI,uBAAuB;YACvB,CAAC,KAAK,CAAC,uBAAuB,CAAC,IAAI,uBAAuB,GAAG,CAAC,CAAC,EACjE,CAAC;YACC,MAAM,IAAI,oBAAY,CAClB,+DAA+D,uBAAuB,EAAE,CAC3F,CAAA;QACL,CAAC;QACD,OAAO,uBAAuB;YAC1B,CAAC,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,CAAC,OAAO,EAAE;YACrD,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;IACpB,CAAC;CACJ;AA9GD,oCA8GC", "file": "CommandUtils.js", "sourcesContent": ["import fs from \"fs/promises\"\nimport path from \"path\"\nimport { TypeORMError } from \"../error\"\nimport { DataSource } from \"../data-source\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { importOrRequireFile } from \"../util/ImportUtils\"\n\n/**\n * Command line utils functions.\n */\nexport class CommandUtils {\n    static async loadDataSource(\n        dataSourceFilePath: string,\n    ): Promise<DataSource> {\n        let dataSourceFileExports\n        try {\n            ;[dataSourceFileExports] = await importOrRequireFile(\n                dataSourceFilePath,\n            )\n        } catch (err) {\n            throw new Error(\n                `Unable to open file: \"${dataSourceFilePath}\". ${err.message}`,\n            )\n        }\n\n        if (\n            !dataSourceFileExports ||\n            typeof dataSourceFileExports !== \"object\"\n        ) {\n            throw new Error(\n                `Given data source file must contain export of a DataSource instance`,\n            )\n        }\n\n        if (InstanceChecker.isDataSource(dataSourceFileExports)) {\n            return dataSourceFileExports\n        }\n\n        const dataSourceExports = []\n        for (const fileExportKey in dataSourceFileExports) {\n            const fileExport = dataSourceFileExports[fileExportKey]\n            // It is necessary to await here in case of the exported async value (Promise<DataSource>).\n            // e.g. the DataSource is instantiated with an async factory in the source file\n            // It is safe to await regardless of the export being async or not due to `awaits` definition:\n            // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/await#return_value\n            const awaitedFileExport = await fileExport\n            if (InstanceChecker.isDataSource(awaitedFileExport)) {\n                dataSourceExports.push(awaitedFileExport)\n            }\n        }\n\n        if (dataSourceExports.length === 0) {\n            throw new Error(\n                `Given data source file must contain export of a DataSource instance`,\n            )\n        }\n        if (dataSourceExports.length > 1) {\n            throw new Error(\n                `Given data source file must contain only one export of DataSource instance`,\n            )\n        }\n        return dataSourceExports[0]\n    }\n\n    /**\n     * Creates directories recursively.\n     */\n    static async createDirectories(directory: string): Promise<void> {\n        await fs.mkdir(directory, { recursive: true })\n    }\n\n    /**\n     * Creates a file with the given content in the given path.\n     */\n    static async createFile(\n        filePath: string,\n        content: string,\n        override: boolean = true,\n    ): Promise<void> {\n        await CommandUtils.createDirectories(path.dirname(filePath))\n        if (override === false && (await CommandUtils.fileExists(filePath))) {\n            return\n        }\n        await fs.writeFile(filePath, content)\n    }\n\n    /**\n     * Reads everything from a given file and returns its content as a string.\n     */\n    static async readFile(filePath: string): Promise<string> {\n        const file = await fs.readFile(filePath)\n\n        return file.toString()\n    }\n\n    static async fileExists(filePath: string) {\n        try {\n            await fs.access(filePath, fs.constants.F_OK)\n            return true\n        } catch {\n            return false\n        }\n    }\n\n    /**\n     * Gets migration timestamp and validates argument (if sent)\n     */\n    static getTimestamp(timestampOptionArgument: any): number {\n        if (\n            timestampOptionArgument &&\n            (isNaN(timestampOptionArgument) || timestampOptionArgument < 0)\n        ) {\n            throw new TypeORMError(\n                `timestamp option should be a non-negative number. received: ${timestampOptionArgument}`,\n            )\n        }\n        return timestampOptionArgument\n            ? new Date(Number(timestampOptionArgument)).getTime()\n            : Date.now()\n    }\n}\n"], "sourceRoot": ".."}