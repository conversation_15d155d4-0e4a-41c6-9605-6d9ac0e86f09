"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolEps = void 0;
const decorator_1 = require("@midwayjs/decorator");
const _ = require("lodash");
const core_1 = require("@midwayjs/core");
const typeorm_1 = require("@midwayjs/typeorm");
const data_1 = require("../tag/data");
const tag_1 = require("../decorator/tag");
/**
 * 实体路径
 */
let CoolEps = class CoolEps {
    constructor() {
        this.admin = {};
        this.app = {};
        this.module = {};
    }
    // @Init()
    async init() {
        var _a;
        if (!this.epsConfig)
            return;
        const entitys = await this.entity();
        const controllers = await this.controller();
        const routers = await this.router();
        await this.modules();
        const adminArr = [];
        const appArr = [];
        for (const controller of controllers) {
            const { prefix, module, curdOption, routerOptions } = controller;
            const name = (_a = curdOption === null || curdOption === void 0 ? void 0 : curdOption.entity) === null || _a === void 0 ? void 0 : _a.name;
            (_.startsWith(prefix, "/admin/") ? adminArr : appArr).push({
                module,
                info: {
                    type: {
                        name: prefix.split("/").pop(),
                        description: (routerOptions === null || routerOptions === void 0 ? void 0 : routerOptions.description) || "",
                    },
                },
                api: routers[prefix],
                name,
                columns: entitys[name] || [],
                prefix,
            });
        }
        this.admin = _.groupBy(adminArr, "module");
        this.app = _.groupBy(appArr, "module");
    }
    /**
     * 模块信息
     * @param module
     */
    async modules(module) {
        for (const key in this.moduleConfig) {
            const config = this.moduleConfig[key];
            this.module[key] = {
                name: config.name,
                description: config.description,
            };
        }
        return module ? this.module[module] : this.module;
    }
    /**
     * 所有controller
     * @returns
     */
    async controller() {
        const result = [];
        const controllers = (0, decorator_1.listModule)(decorator_1.CONTROLLER_KEY);
        for (const controller of controllers) {
            result.push((0, decorator_1.getClassMetadata)(decorator_1.CONTROLLER_KEY, controller));
        }
        return result;
    }
    /**
     * 所有路由
     * @returns
     */
    async router() {
        let ignoreUrls = this.coolUrlTagData.byKey(tag_1.TagTypes.IGNORE_TOKEN);
        if (_.isEmpty(ignoreUrls)) {
            ignoreUrls = [];
        }
        return _.groupBy((await this.midwayWebRouterService.getFlattenRouterTable()).map((item) => {
            return {
                method: item.requestMethod,
                path: item.url,
                summary: item.summary,
                dts: {},
                tag: "",
                prefix: item.prefix,
                ignoreToken: ignoreUrls.includes(item.prefix + item.url),
            };
        }), "prefix");
    }
    /**
     * 所有实体
     * @returns
     */
    async entity() {
        const result = {};
        const dataSourceNames = this.typeORMDataSourceManager.getDataSourceNames();
        for (const dataSourceName of dataSourceNames) {
            const entityMetadatas = await this.typeORMDataSourceManager.getDataSource(dataSourceName).entityMetadatas;
            for (const entityMetadata of entityMetadatas) {
                const commColums = [];
                let columns = entityMetadata.columns;
                if (entityMetadata.tableType != "regular")
                    continue;
                columns = _.filter(columns.map((e) => {
                    return {
                        propertyName: e.propertyName,
                        type: typeof e.type == "string" ? e.type : e.type.name.toLowerCase(),
                        length: e.length,
                        comment: e.comment,
                        nullable: e.isNullable,
                    };
                }), (o) => {
                    if (["createTime", "updateTime"].includes(o.propertyName)) {
                        commColums.push(o);
                    }
                    return o && !["createTime", "updateTime"].includes(o.propertyName);
                }).concat(commColums);
                result[entityMetadata.name] = columns;
            }
        }
        return result;
    }
};
exports.CoolEps = CoolEps;
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayWebRouterService)
], CoolEps.prototype, "midwayWebRouterService", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolEps.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_1.Config)("cool.eps"),
    __metadata("design:type", Boolean)
], CoolEps.prototype, "epsConfig", void 0);
__decorate([
    (0, core_1.Config)("module"),
    __metadata("design:type", Object)
], CoolEps.prototype, "moduleConfig", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", data_1.CoolUrlTagData)
], CoolEps.prototype, "coolUrlTagData", void 0);
exports.CoolEps = CoolEps = __decorate([
    (0, decorator_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolEps);
