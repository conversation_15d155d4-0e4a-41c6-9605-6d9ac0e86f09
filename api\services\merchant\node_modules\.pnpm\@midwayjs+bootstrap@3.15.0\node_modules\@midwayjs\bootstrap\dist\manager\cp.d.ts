/// <reference types="node" />
import { Worker } from 'cluster';
import { ClusterOptions } from '../interface';
import { AbstractForkManager } from './base';
export declare class ClusterManager extends AbstractForkManager<Worker, ClusterOptions> {
    readonly options: ClusterOptions;
    constructor(options?: ClusterOptions);
    createWorker(): any;
    bindWorkerDisconnect(listener: (worker: Worker) => void): void;
    bindWorkerExit(listener: (worker: Worker, code: any, signal: any) => void): void;
    getWorkerId(worker: Worker): string;
    isWorkerDead(worker: Worker): boolean;
    closeWorker(worker: Worker): void;
    createEventBus(options: any): any;
    isPrimary(): boolean;
}
//# sourceMappingURL=cp.d.ts.map