import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

/**
 * 结算配置状态管理
 * 统一管理所有结算相关的配置数据
 */
export const useSettlementStore = defineStore('settlement', () => {
  // 结算规则表单数据 - 与原 rules.vue 保持一致
  const settlementForm = reactive({
    // 基础配置
    name: '',
    description: '',
    merchantType: 'all',
    industry: 'all',
    merchantLevel: 'all',
    settlementCycle: 'T1',
    priority: 10,
    status: 1,
    
    // 费率配置
    feeType: 'percentage',
    feeRate: 0.6,
    fixedFee: 0,
    minFee: 0,
    maxFee: 0,
    
    // 阶梯费率
    enableTieredRates: false,
    tieredRates: [
      { minAmount: 0, maxAmount: 10000, rate: 0.8 },
      { minAmount: 10000, maxAmount: 100000, rate: 0.6 },
      { minAmount: 100000, maxAmount: -1, rate: 0.4 }
    ],
    
    // 新商户优惠
    newMerchantDiscount: {
      enabled: false,
      duration: 3,
      rate: 0.3
    },
    
    // 节假日规则
    holidayRule: {
      enabled: false,
      action: 'delay'
    },
    
    // 活动期间规则
    promotionRule: {
      enabled: false,
      rate: 0.3,
      startTime: null,
      endTime: null
    },
    
    // 风控与合规配置
    riskLevel: 'low',
    dailyLimit: 100000,
    monthlyLimit: 3000000,
    enableAML: false,
    enableEscrow: false,
    kycLevel: 'basic',
    taxHandling: 'platform',
    reportingLevel: 'standard',
    dataProtection: true,
    auditLog: true,
    
    // 智能定价配置
    enableDynamicPricing: false,
    pricingFrequency: 'daily',
    volumeWeight: 30,
    creditWeight: 25,
    marketWeight: 25,
    riskWeight: 20,
    enableCompetitivePricing: false,
    priceMatchStrategy: 'average',
    
    // 国际化配置
    baseCurrency: 'CNY',
    supportedCurrencies: ['CNY'],
    exchangeRateFrequency: 'daily',
    exchangeRateSource: 'boc',
    defaultTimezone: 'Asia/Shanghai',
    supportedRegions: ['CN'],
    supportedLanguages: ['zh-CN'],
    crossBorderSettlement: false,
    enableCurrencyHedging: false,
    hedgingStrategy: 'partial',
    crossBorderChannel: 'swift',
    crossBorderFee: 0.5
  })

  // 风险控制设置 - 映射到 settlementForm
  const riskSettings = reactive({
    get riskLevel() { return settlementForm.riskLevel },
    set riskLevel(value) { settlementForm.riskLevel = value },
    
    get dailyLimit() { return settlementForm.dailyLimit },
    set dailyLimit(value) { settlementForm.dailyLimit = value },
    
    get monthlyLimit() { return settlementForm.monthlyLimit },
    set monthlyLimit(value) { settlementForm.monthlyLimit = value },
    
    get enableAML() { return settlementForm.enableAML },
    set enableAML(value) { settlementForm.enableAML = value },
    
    get enableEscrow() { return settlementForm.enableEscrow },
    set enableEscrow(value) { settlementForm.enableEscrow = value }
  })

  // 合规要求设置 - 映射到 settlementForm
  const complianceSettings = reactive({
    get kycLevel() { return settlementForm.kycLevel },
    set kycLevel(value) { settlementForm.kycLevel = value },
    
    get taxHandling() { return settlementForm.taxHandling },
    set taxHandling(value) { settlementForm.taxHandling = value },
    
    get reportingLevel() { return settlementForm.reportingLevel },
    set reportingLevel(value) { settlementForm.reportingLevel = value },
    
    get dataProtection() { return settlementForm.dataProtection },
    set dataProtection(value) { settlementForm.dataProtection = value },
    
    get auditLog() { return settlementForm.auditLog },
    set auditLog(value) { settlementForm.auditLog = value }
  })

  // 币种设置 - 映射到 settlementForm
  const currencySettings = reactive({
    get baseCurrency() { return settlementForm.baseCurrency },
    set baseCurrency(value) { settlementForm.baseCurrency = value },
    
    get supportedCurrencies() { return settlementForm.supportedCurrencies },
    set supportedCurrencies(value) { settlementForm.supportedCurrencies = value },
    
    get exchangeRateFrequency() { return settlementForm.exchangeRateFrequency },
    set exchangeRateFrequency(value) { settlementForm.exchangeRateFrequency = value },
    
    get exchangeRateSource() { return settlementForm.exchangeRateSource },
    set exchangeRateSource(value) { settlementForm.exchangeRateSource = value }
  })

  // 地区设置 - 映射到 settlementForm
  const regionSettings = reactive({
    get defaultTimezone() { return settlementForm.defaultTimezone },
    set defaultTimezone(value) { settlementForm.defaultTimezone = value },
    
    get supportedRegions() { return settlementForm.supportedRegions },
    set supportedRegions(value) { settlementForm.supportedRegions = value },
    
    get supportedLanguages() { return settlementForm.supportedLanguages },
    set supportedLanguages(value) { settlementForm.supportedLanguages = value },
    
    get crossBorderSettlement() { return settlementForm.crossBorderSettlement },
    set crossBorderSettlement(value) { settlementForm.crossBorderSettlement = value }
  })

  // 保存配置
  const saveSettings = async () => {
    try {
      // 这里应该调用API保存配置
      console.log('保存结算配置:', settlementForm)
      return { success: true, message: '配置保存成功' }
    } catch (error) {
      console.error('保存配置失败:', error)
      return { success: false, message: '配置保存失败' }
    }
  }

  // 加载配置
  const loadSettings = async () => {
    try {
      // 这里应该调用API加载配置
      console.log('加载结算配置')
      return { success: true }
    } catch (error) {
      console.error('加载配置失败:', error)
      return { success: false }
    }
  }

  // 重置配置
  const resetSettings = () => {
    Object.assign(settlementForm, {
      riskLevel: 'low',
      dailyLimit: 100000,
      monthlyLimit: 3000000,
      enableAML: false,
      enableEscrow: false,
      kycLevel: 'basic',
      taxHandling: 'platform',
      reportingLevel: 'standard',
      dataProtection: true,
      auditLog: true,
      baseCurrency: 'CNY',
      supportedCurrencies: ['CNY'],
      exchangeRateFrequency: 'daily',
      exchangeRateSource: 'boc',
      defaultTimezone: 'Asia/Shanghai',
      supportedRegions: ['CN'],
      supportedLanguages: ['zh-CN'],
      crossBorderSettlement: false
    })
  }

  return {
    // 原始表单数据
    settlementForm,
    
    // 分类设置对象（响应式映射）
    riskSettings,
    complianceSettings,
    currencySettings,
    regionSettings,
    
    // 方法
    saveSettings,
    loadSettings,
    resetSettings
  }
})
