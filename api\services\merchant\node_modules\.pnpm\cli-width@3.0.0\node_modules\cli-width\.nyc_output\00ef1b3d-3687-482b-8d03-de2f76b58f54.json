{"/Users/<USER>/maintained/cli-width/index.js": {"path": "/Users/<USER>/maintained/cli-width/index.js", "statementMap": {"0": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 36}}, "1": {"start": {"line": 6, "column": 20}, "end": {"line": 10, "column": 3}}, "2": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "3": {"start": {"line": 13, "column": 4}, "end": {"line": 13, "column": 23}}, "4": {"start": {"line": 16, "column": 2}, "end": {"line": 20, "column": 5}}, "5": {"start": {"line": 17, "column": 4}, "end": {"line": 19, "column": 5}}, "6": {"start": {"line": 18, "column": 6}, "end": {"line": 18, "column": 38}}, "7": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 17}}, "8": {"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 35}}, "9": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 63}}, "11": {"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 3}}, "12": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 60}}, "13": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, "14": {"start": {"line": 37, "column": 4}, "end": {"line": 37, "column": 31}}, "15": {"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}, "16": {"start": {"line": 41, "column": 16}, "end": {"line": 41, "column": 51}}, "17": {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}, "18": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 19}}, "19": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 27}}}, "fnMap": {"0": {"name": "normalizeOpts", "decl": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 22}}, "loc": {"start": {"line": 5, "column": 32}, "end": {"line": 23, "column": 1}}, "line": 5}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 35}, "end": {"line": 16, "column": 36}}, "loc": {"start": {"line": 16, "column": 50}, "end": {"line": 20, "column": 3}}, "line": 16}, "2": {"name": "cli<PERSON><PERSON><PERSON>", "decl": {"start": {"line": 25, "column": 9}, "end": {"line": 25, "column": 17}}, "loc": {"start": {"line": 25, "column": 27}, "end": {"line": 49, "column": 1}}, "line": 25}}, "branchMap": {"0": {"loc": {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, "type": "if", "locations": [{"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}, {"start": {"line": 12, "column": 2}, "end": {"line": 14, "column": 3}}], "line": 12}, "1": {"loc": {"start": {"line": 17, "column": 4}, "end": {"line": 19, "column": 5}}, "type": "if", "locations": [{"start": {"line": 17, "column": 4}, "end": {"line": 19, "column": 5}}, {"start": {"line": 17, "column": 4}, "end": {"line": 19, "column": 5}}], "line": 17}, "2": {"loc": {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, "type": "if", "locations": [{"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}, {"start": {"line": 28, "column": 2}, "end": {"line": 30, "column": 3}}], "line": 28}, "3": {"loc": {"start": {"line": 29, "column": 11}, "end": {"line": 29, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 29, "column": 11}, "end": {"line": 29, "column": 41}}, {"start": {"line": 29, "column": 45}, "end": {"line": 29, "column": 62}}], "line": 29}, "4": {"loc": {"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 3}}, "type": "if", "locations": [{"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 3}}, {"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 3}}], "line": 32}, "5": {"loc": {"start": {"line": 33, "column": 11}, "end": {"line": 33, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 33, "column": 11}, "end": {"line": 33, "column": 38}}, {"start": {"line": 33, "column": 42}, "end": {"line": 33, "column": 59}}], "line": 33}, "6": {"loc": {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, "type": "if", "locations": [{"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}, {"start": {"line": 36, "column": 2}, "end": {"line": 38, "column": 3}}], "line": 36}, "7": {"loc": {"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}, "type": "if", "locations": [{"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}, {"start": {"line": 40, "column": 2}, "end": {"line": 46, "column": 3}}], "line": 40}, "8": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}, {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}], "line": 43}, "9": {"loc": {"start": {"line": 43, "column": 8}, "end": {"line": 43, "column": 36}}, "type": "binary-expr", "locations": [{"start": {"line": 43, "column": 8}, "end": {"line": 43, "column": 21}}, {"start": {"line": 43, "column": 25}, "end": {"line": 43, "column": 36}}], "line": 43}}, "s": {"0": 1, "1": 11, "2": 11, "3": 5, "4": 6, "5": 18, "6": 12, "7": 6, "8": 11, "9": 11, "10": 3, "11": 8, "12": 3, "13": 5, "14": 1, "15": 4, "16": 2, "17": 2, "18": 1, "19": 3}, "f": {"0": 11, "1": 18, "2": 11}, "b": {"0": [5, 6], "1": [12, 6], "2": [3, 8], "3": [3, 1], "4": [3, 5], "5": [3, 1], "6": [1, 4], "7": [2, 2], "8": [1, 1], "9": [2, 1]}, "_coverageSchema": "1a1c01bbd47fc00a2c39e90264f33305004495a9", "hash": "a4d7f0a30376f059a5bc5bb60422e8b1332adf0c", "contentHash": "1ab1e2def09289f7630132524d4d3929b81fe2db9a3f07e64865500b9c42e817"}}