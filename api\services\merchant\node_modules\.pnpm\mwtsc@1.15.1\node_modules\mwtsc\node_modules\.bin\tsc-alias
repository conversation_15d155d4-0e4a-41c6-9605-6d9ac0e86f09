#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/dist/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/dist/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules/tsc-alias/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/tsc-alias@1.8.16/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../tsc-alias@1.8.16/node_modules/tsc-alias/dist/bin/index.js" "$@"
else
  exec node  "$basedir/../../../../../tsc-alias@1.8.16/node_modules/tsc-alias/dist/bin/index.js" "$@"
fi
