import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9804,
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: process.env.DB_HOST || '127.0.0.1',
        port: parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD,
        database: process.env.DB_DATABASE || 'message_service_db',
        synchronize: false, // 生产环境不自动同步
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ['dist/modules/message/entity/**/*.js'],
      },
    },
  },
  cool: {
    redis: {
      host: process.env.REDIS_HOST || '127.0.0.1',
      password: process.env.REDIS_PASSWORD || '',
      port: parseInt(process.env.REDIS_PORT) || 6379,
      db: parseInt(process.env.REDIS_DB) || 13,
    },
    eps: false,
    initDB: false,
    initMenu: false,
  } as CoolConfig,
  moleculer: {
    namespace: 'cool',
    nodeID: 'message-service',
    transporter: process.env.MOLECULER_TRANSPORTER || 'NATS',
  },
} as MidwayConfig; 