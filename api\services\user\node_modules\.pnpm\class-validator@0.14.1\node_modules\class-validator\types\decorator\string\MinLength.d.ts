import { ValidationOptions } from '../ValidationOptions';
export declare const MIN_LENGTH = "minLength";
/**
 * Checks if the string's length is not less than given number. Note: this function takes into account surrogate pairs.
 * If given value is not a string, then it returns false.
 */
export declare function minLength(value: unknown, min: number): boolean;
/**
 * Checks if the string's length is not less than given number. Note: this function takes into account surrogate pairs.
 * If given value is not a string, then it returns false.
 */
export declare function MinLength(min: number, validationOptions?: ValidationOptions): PropertyDecorator;
