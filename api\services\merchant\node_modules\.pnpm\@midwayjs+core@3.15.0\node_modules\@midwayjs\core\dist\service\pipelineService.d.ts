import { IMidwayContainer, ObjectIdentifier } from '../interface';
interface IPipelineInfo {
    /**
     * 上次执行结果(只有在执行 waterfall 时才有值)
     */
    prevValue?: any;
    /**
     * 当前执行的 valve 类
     */
    current: IValveHandler;
    /**
     * 当前执行的 valve 名称(类名)
     */
    currentName: string;
    /**
     * 之前执行的 valve 类
     */
    prev?: IValveHandler;
    /**
     * 之前执行的 valve 名称(类名)
     */
    prevName?: string;
    /**
     * 后一个将执行的 valve 类
     */
    next?: IValveHandler;
    /**
     * 后一个将执行的 valve 名称(类名)
     */
    nextName?: string;
}
/**
 * 执行pipeline 时当前上下文存储内容
 */
export interface IPipelineContext {
    /**
     * pipeline 执行原始参数
     */
    args: any;
    /**
     * valve 执行信息
     */
    info?: IPipelineInfo;
    /**
     * 用于缓存当前 pipeline 执行中的中间过程参数
     * @param key 关键词
     */
    get(key: string): any;
    /**
     * 用于缓存当前 pipeline 执行中的中间过程参数
     * @param key 关键词
     * @param val 值
     */
    set(key: string, val: any): void;
    /**
     * 返回存在的所有 key
     */
    keys(): string[];
}
/**
 * 每个具体的 valve 需要继承实现该接口
 */
export interface IValveHandler {
    /**
     * 最终合并结果object中的key，默认为 valve 名称
     */
    alias?: string;
    /**
     * 执行当前 valve
     * @param ctx 上下文
     */
    invoke(ctx: IPipelineContext): Promise<any>;
}
/**
 * pipeline 执行参数
 */
export interface IPipelineOptions {
    /**
     * pipeline 原始参数
     */
    args?: any;
    /**
     * 这次 pipeline 执行那几个 valve 白名单
     */
    valves?: valvesType;
}
/**
 * pipeline 执行返回结果
 */
export interface IPipelineResult<T> {
    /**
     * 是否成功
     */
    success: boolean;
    /**
     * 异常信息(如果有则返回)
     */
    error?: {
        /**
         * 异常出在那个 valve 上
         */
        valveName?: string;
        /**
         * 异常信息
         */
        message?: string;
        /**
         * 原始 Error
         */
        error?: Error;
    };
    /**
     * 返回结果
     */
    result: T;
}
export interface IPipelineHandler {
    parallel<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    concat<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    series<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    concatSeries<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    waterfall<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
}
type valvesType = Array<ObjectIdentifier | (new (...args: any[]) => any)>;
export declare class PipelineContext implements IPipelineContext {
    args: any;
    info: IPipelineInfo;
    constructor(args?: any);
    private data;
    get(key: string): any;
    set(key: string, val: any): void;
    keys(): string[];
}
export declare class MidwayPipelineService implements IPipelineHandler {
    readonly applicationContext: IMidwayContainer;
    readonly valves?: valvesType;
    constructor(applicationContext: IMidwayContainer, valves?: valvesType);
    /**
     * 并行执行，使用 Promise.all
     * @param opts 执行参数
     */
    parallel<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    /**
     * 并行执行，最终 result 为数组
     * @param opts 执行参数
     */
    concat<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    /**
     * 串行执行，使用 foreach await
     * @param opts 执行参数
     */
    series<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    /**
     * 串行执行，使用 foreach await，最终 result 为数组
     * @param opts 执行参数
     */
    concatSeries<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    /**
     * 串行执行，但是会把前者执行结果当成入参，传入到下一个执行中去，最后一个执行的 valve 结果会被返回
     * @param opts 执行参数
     */
    waterfall<T>(opts: IPipelineOptions): Promise<IPipelineResult<T>>;
    private mergeValves;
    private prepareParallelValves;
    private packResult;
}
export {};
//# sourceMappingURL=pipelineService.d.ts.map