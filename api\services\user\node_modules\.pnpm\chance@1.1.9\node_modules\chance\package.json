{"name": "chance", "main": "./chance.js", "version": "1.1.9", "description": "Chance - Utility library to generate anything random", "homepage": "http://chancejs.com", "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/chancejs/chancejs/issues"}, "repository": {"type": "git", "url": "https://github.com/chancejs/chancejs.git"}, "license": "MIT", "devDependencies": {"ava": "^0.19.1", "babel-eslint": "^7.2.3", "chai": "^3.5.0", "coveralls": "^2.11.2", "dirty-chai": "^1.2.2", "docpress": "0.7.1", "eslint": "^6.3.0", "git-update-ghpages": "1.3.0", "gulp": "^4.0.0", "gulp-ava": "^0.17.1", "gulp-eslint": "^3.0.1", "gulp-jshint": "^2.0.4", "gulp-rename": "^1.2.2", "gulp-sourcemaps": "^2.6.1", "gulp-uglify": "^3.0.0", "jshint-stylish": "^2.2.1", "lodash": "^4.17.4", "nyc": "^10.3.2", "pump": "^1.0.2"}, "scripts": {"coverage": "nyc npm test && nyc report --reporter=text-lcov --report-dir=./coverage > ./coverage/lcov.info", "docs": "docpress build", "docs:publish": "git-update-ghpages -e", "lint": "eslint --ignore-path .gitignore chance.js", "test": "ava"}, "keywords": ["chance", "random", "generator", "test", "mersenne", "name", "address", "dice"], "jam": {"main": "chance.js"}, "github": "https://github.com/chancejs/chancejs", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "https://www.victorquinn.com"}], "spm": {"main": "chance.js", "ignore": ["test"]}, "docpress": {"scripts": ["chance.js", "analytics.js", "https://platform.twitter.com/widgets.js"], "github": "chancejs/chancejs", "css": ["docs/chance.css"]}}