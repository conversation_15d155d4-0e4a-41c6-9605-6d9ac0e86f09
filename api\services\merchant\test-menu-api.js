const http = require('http');

// 测试菜单API
function testMenuAPI() {
  const postData = JSON.stringify({});

  const options = {
    hostname: '127.0.0.1',
    port: 9701,
    path: '/admin/merchant/menu/tree', // 直接测试后端路径
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  console.log('🚀 测试菜单API...');
  console.log(`请求地址: http://${options.hostname}:${options.port}${options.path}`);

  const req = http.request(options, (res) => {
    console.log(`状态码: ${res.statusCode}`);
    console.log(`响应头: ${JSON.stringify(res.headers)}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('\n📋 响应数据:');
      try {
        const jsonData = JSON.parse(data);
        console.log(JSON.stringify(jsonData, null, 2));
        
        // 检查监控相关菜单
        if (jsonData.data && Array.isArray(jsonData.data)) {
          const monitorMenus = jsonData.data.filter(menu => 
            menu.router && (menu.router.includes('monitor') || menu.router.includes('analytics'))
          );
          console.log('\n🔍 监控相关菜单:');
          console.log(JSON.stringify(monitorMenus, null, 2));
        }
      } catch (error) {
        console.log('原始响应:', data);
        console.error('JSON解析失败:', error.message);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`请求失败: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

testMenuAPI();
