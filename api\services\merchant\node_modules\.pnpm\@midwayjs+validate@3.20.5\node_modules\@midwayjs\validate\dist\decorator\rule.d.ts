import * as Jo<PERSON> from 'joi';
/**
 * @deprecated
 */
export interface RuleOptions {
    required?: boolean;
    min?: number;
    max?: number;
}
export declare function Rule(rule: Joi.AnySchema<any>): PropertyDecorator & ClassDecorator;
/**
 * @deprecated
 */
export declare function Rule(rule: new (...args: any[]) => any, options?: RuleOptions): PropertyDecorator & ClassDecorator;
export { Joi as RuleType };
//# sourceMappingURL=rule.d.ts.map