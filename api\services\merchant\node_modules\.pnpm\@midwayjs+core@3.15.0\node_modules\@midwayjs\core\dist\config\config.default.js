"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const util_1 = require("../util/");
exports.default = (appInfo) => {
    const isDevelopment = (0, util_1.isDevelopmentEnvironment)((0, util_1.getCurrentEnvironment)());
    return {
        core: {
            healthCheckTimeout: 1000,
        },
        asyncContextManager: {
            enable: false,
        },
        midwayLogger: {
            default: {
                level: 'info',
            },
            clients: {
                coreLogger: {
                    level: isDevelopment ? 'info' : 'warn',
                },
                appLogger: {
                    aliasName: 'logger',
                },
            },
        },
        debug: {
            recordConfigMergeOrder: isDevelopment,
        },
    };
};
//# sourceMappingURL=config.default.js.map