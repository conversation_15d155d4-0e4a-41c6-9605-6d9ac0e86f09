"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolEventManager = exports.COOL_EVENT_MESSAGE = void 0;
const decorator_1 = require("@midwayjs/decorator");
const Events = require("events");
const core_1 = require("@midwayjs/core");
const event_1 = require("../decorator/event");
const pm2 = require("pm2");
const _ = require("lodash");
exports.COOL_EVENT_MESSAGE = "cool:event:message";
/**
 * 事件
 */
let CoolEventManager = class CoolEventManager extends Events {
    constructor() {
        super(...arguments);
        // 事件数据 某个事件对应的模块对应的方法
        this.eventData = {};
    }
    /**
     * 初始化
     */
    async init() {
        const eventModules = (0, decorator_1.listModule)(event_1.COOL_CLS_EVENT_KEY);
        for (const module of eventModules) {
            await this.handlerEvent(module);
        }
        await this.commEvent();
        await this.globalEvent();
    }
    /**
     * 发送事件
     * @param event
     * @param args
     * @returns
     */
    emit(event, ...args) {
        return super.emit(exports.COOL_EVENT_MESSAGE, {
            type: exports.COOL_EVENT_MESSAGE,
            data: {
                event,
                args,
            },
        });
    }
    /**
     * 发送全局事件
     * @param event 事件
     * @param random 是否随机一个
     * @param args 参数
     * @returns
     */
    async globalEmit(event, random = false, ...args) {
        // 如果是本地运行还是转普通模式
        if (this.app.getEnv() === "local") {
            this.emit(event, ...args);
            return;
        }
        pm2.connect(() => {
            pm2.list((err, list) => {
                const ps = list.map((e) => {
                    return {
                        id: e.pm_id,
                        name: e.name,
                    };
                });
                // random 为 true 时随机发给同名称的一个进程
                if (random) {
                    // 按名称分组
                    const group = _.groupBy(ps, "name");
                    const names = Object.keys(group);
                    // 遍历名称
                    names.forEach((name) => {
                        const pss = group[name];
                        // 随机一个
                        const index = _.random(0, pss.length - 1);
                        const ps = pss[index];
                        // 发给这个进程
                        // @ts-ignore
                        pm2.sendDataToProcessId({
                            type: "process:msg",
                            data: {
                                type: `${exports.COOL_EVENT_MESSAGE}@${this.keys}`,
                                event,
                                args,
                            },
                            id: ps.id,
                            topic: "cool:event:topic",
                        }, (err, res) => { });
                    });
                }
                else {
                    // 发给所有进程
                    ps.forEach((e) => {
                        // @ts-ignore
                        pm2.sendDataToProcessId({
                            type: "process:msg",
                            data: {
                                type: `${exports.COOL_EVENT_MESSAGE}@${this.keys}`,
                                event,
                                args,
                            },
                            id: e.id,
                            topic: "cool:event:topic",
                        }, (err, res) => { });
                    });
                }
            });
        });
    }
    /**
     * 处理事件
     * @param module
     */
    async handlerEvent(module) {
        const events = (0, decorator_1.getClassMetadata)(event_1.COOL_EVENT_KEY, module);
        for (const event of events) {
            const listen = event.eventName ? event.eventName : event.propertyKey;
            if (!this.eventData[listen]) {
                this.eventData[listen] = [];
            }
            this.eventData[listen].push({
                module,
                method: event.propertyKey,
            });
        }
    }
    /**
     * 全局事件
     */
    async globalEvent() {
        process.on("message", async (message) => {
            const data = message === null || message === void 0 ? void 0 : message.data;
            if (!data)
                return;
            if (data.type != `${exports.COOL_EVENT_MESSAGE}@${this.keys}`)
                return;
            await this.doAction(message);
        });
    }
    /**
     * 普通事件
     */
    async commEvent() {
        this.on(exports.COOL_EVENT_MESSAGE, async (message) => {
            await this.doAction(message);
        });
    }
    /**
     * 执行事件
     * @param message
     */
    async doAction(message) {
        const data = message.data;
        const method = data.event;
        const args = data.args;
        if (this.eventData[method]) {
            for (const event of this.eventData[method]) {
                const moduleInstance = await this.app
                    .getApplicationContext()
                    .getAsync(event.module);
                moduleInstance[event.method](...args);
            }
        }
    }
};
exports.CoolEventManager = CoolEventManager;
__decorate([
    (0, decorator_1.App)(),
    __metadata("design:type", Object)
], CoolEventManager.prototype, "app", void 0);
__decorate([
    (0, core_1.Config)("keys"),
    __metadata("design:type", String)
], CoolEventManager.prototype, "keys", void 0);
exports.CoolEventManager = CoolEventManager = __decorate([
    (0, decorator_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolEventManager);
