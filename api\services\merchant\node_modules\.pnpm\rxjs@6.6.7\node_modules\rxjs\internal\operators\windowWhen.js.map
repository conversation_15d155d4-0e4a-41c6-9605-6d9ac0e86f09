{"version": 3, "file": "windowWhen.js", "sources": ["../../src/internal/operators/windowWhen.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAGA,sCAAqC;AAErC,sDAAqD;AAErD,+DAA8D;AAgD9D,SAAgB,UAAU,CAAI,eAAsC;IAClE,OAAO,SAAS,0BAA0B,CAAC,MAAqB;QAC9D,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,cAAc,CAAI,eAAe,CAAC,CAAC,CAAC;IAC7D,CAAC,CAAC;AACJ,CAAC;AAJD,gCAIC;AAED;IACE,wBAAoB,eAAsC;QAAtC,oBAAe,GAAf,eAAe,CAAuB;IAC1D,CAAC;IAED,6BAAI,GAAJ,UAAK,UAAqC,EAAE,MAAW;QACrD,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC;IAClF,CAAC;IACH,qBAAC;AAAD,CAAC,AAPD,IAOC;AAOD;IAAkC,oCAAuB;IAIvD,0BAAsB,WAAsC,EACxC,eAAsC;QAD1D,YAEE,kBAAM,WAAW,CAAC,SAEnB;QAJqB,iBAAW,GAAX,WAAW,CAA2B;QACxC,qBAAe,GAAf,eAAe,CAAuB;QAExD,KAAI,CAAC,UAAU,EAAE,CAAC;;IACpB,CAAC;IAED,qCAAU,GAAV,UAAW,WAAc,EAAE,WAAgB,EAChC,WAAmB,EAAE,WAAmB,EACxC,QAAiC;QAC1C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAED,sCAAW,GAAX,UAAY,KAAU;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;IAED,yCAAc,GAAd,UAAe,QAAiC;QAC9C,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC5B,CAAC;IAES,gCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAES,iCAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,MAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,CAAC,8BAA8B,EAAE,CAAC;IACxC,CAAC;IAES,oCAAS,GAAnB;QACE,IAAI,CAAC,MAAO,CAAC,QAAQ,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;QAC5B,IAAI,CAAC,8BAA8B,EAAE,CAAC;IACxC,CAAC;IAEO,yDAA8B,GAAtC;QACE,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,CAAC;SACxC;IACH,CAAC;IAEO,qCAAU,GAAlB,UAAmB,QAA+C;QAA/C,yBAAA,EAAA,eAA+C;QAChE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACtB,QAAQ,CAAC,WAAW,EAAE,CAAC;SACxB;QAED,IAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;QAED,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,iBAAO,EAAK,CAAC;QAC9C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAE9B,IAAI,eAAe,CAAC;QACpB,IAAI;YACM,IAAA,sCAAe,CAAU;YACjC,eAAe,GAAG,eAAe,EAAE,CAAC;SACrC;QAAC,OAAO,CAAC,EAAE;YACV,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACrB,OAAO;SACR;QACD,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,GAAG,qCAAiB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC;IAChF,CAAC;IACH,uBAAC;AAAD,CAAC,AAvED,CAAkC,iCAAe,GAuEhD"}