"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageCategory = exports.ListenerType = exports.MessageType = void 0;
var MessageType;
(function (MessageType) {
    /**
     * worker => main
     */
    MessageType["Inited"] = "inited";
    /**
     * main => worker
     */
    MessageType["Request"] = "request";
    /**
     * worker => main
     */
    MessageType["Response"] = "response";
    /**
     * publish async: main => worker
     */
    MessageType["Invoke"] = "invoke";
    /**
     * broadcast to all workers, or except the specified worker
     */
    MessageType["Broadcast"] = "broadcast";
})(MessageType = exports.MessageType || (exports.MessageType = {}));
var ListenerType;
(function (ListenerType) {
    ListenerType["Inited"] = "inited";
    ListenerType["Request"] = "request";
    ListenerType["Subscribe"] = "Subscribe";
    ListenerType["WorkerChanged"] = "worker_changed";
    ListenerType["Error"] = "error";
})(ListenerType = exports.ListenerType || (exports.ListenerType = {}));
var MessageCategory;
(function (MessageCategory) {
    MessageCategory["IN"] = "in";
    MessageCategory["OUT"] = "out";
})(MessageCategory = exports.MessageCategory || (exports.MessageCategory = {}));
//# sourceMappingURL=interface.js.map