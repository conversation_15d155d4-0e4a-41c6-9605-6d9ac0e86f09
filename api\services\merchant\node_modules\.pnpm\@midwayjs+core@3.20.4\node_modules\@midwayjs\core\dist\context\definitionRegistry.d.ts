import { IIdentifierRelationShip, IObjectDefinition, IObjectDefinitionRegistry, ObjectIdentifier } from '../interface';
export declare class ObjectDefinitionRegistry extends Map implements IObjectDefinitionRegistry {
    private singletonIds;
    private _identifierRelation;
    get identifierRelation(): IIdentifierRelationShip;
    set identifierRelation(identifierRelation: IIdentifierRelationShip);
    get identifiers(): any[];
    get count(): number;
    getSingletonDefinitionIds(): ObjectIdentifier[];
    getDefinitionByName(name: string): IObjectDefinition[];
    registerDefinition(identifier: ObjectIdentifier, definition: IObjectDefinition): void;
    getDefinition(identifier: ObjectIdentifier): IObjectDefinition;
    removeDefinition(identifier: ObjectIdentifier): void;
    hasDefinition(identifier: ObjectIdentifier): boolean;
    clearAll(): void;
    hasObject(identifier: ObjectIdentifier): boolean;
    registerObject(identifier: ObjectIdentifier, target: any): void;
    getObject(identifier: ObjectIdentifier): any;
    getIdentifierRelation(): IIdentifierRelationShip;
    setIdentifierRelation(identifierRelation: IIdentifierRelationShip): void;
}
//# sourceMappingURL=definitionRegistry.d.ts.map