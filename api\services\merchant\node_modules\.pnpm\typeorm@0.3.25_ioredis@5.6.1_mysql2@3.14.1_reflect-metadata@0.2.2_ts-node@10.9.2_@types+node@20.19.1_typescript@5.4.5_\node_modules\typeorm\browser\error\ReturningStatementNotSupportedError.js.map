{"version": 3, "sources": ["../browser/src/error/ReturningStatementNotSupportedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;;GAGG;AACH,MAAM,OAAO,mCAAoC,SAAQ,YAAY;IACjE;QACI,KAAK,CACD,0GAA0G,CAC7G,CAAA;IACL,CAAC;CACJ", "file": "ReturningStatementNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build a query with RETURNING / OUTPUT statement,\n * but used database does not support it.\n */\nexport class ReturningStatementNotSupportedError extends TypeORMError {\n    constructor() {\n        super(\n            `OUTPUT or RETURNING clause only supported by PostgreSQL, MariaDB, Microsoft SqlServer or Google Spanner.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}