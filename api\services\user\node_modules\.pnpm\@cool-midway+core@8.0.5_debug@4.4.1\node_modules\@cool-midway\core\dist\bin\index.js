#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const check_1 = require("./check");
const entity_1 = require("./entity");
const obfuscate_1 = require("./obfuscate");
const program = new commander_1.Command();
// 设置版本号（从 package.json 中获取）
program.version(require('../../package.json').version);
// 修改命令定义部分
const commands = {
    check: async () => await (0, check_1.check)(),
    entity: async (options = {}) => {
        if (options.clear) {
            await (0, entity_1.clearEntitiesFile)();
        }
        else {
            await (0, entity_1.generateEntitiesFile)();
        }
    },
    obfuscate: async () => await (0, obfuscate_1.obfuscate)(),
};
// 移除原有的单独命令定义
program
    .arguments('[cmds...]')
    .option('--clear', 'Clear entities file when using entity command')
    .description('Run one or multiple commands: check, entity, obfuscate')
    .action(async (cmds, options) => {
    if (!cmds.length) {
        program.outputHelp();
        return;
    }
    for (const cmd of cmds) {
        if (cmd in commands) {
            console.log(`Executing ${cmd}...`);
            await commands[cmd](options);
        }
        else {
            console.error(`Unknown command: ${cmd}`);
        }
    }
});
// 解析命令行参数
program.parse(process.argv);
// 如果没有任何命令，显示帮助信息
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
