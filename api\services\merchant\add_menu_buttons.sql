 -- 为菜单管理添加按钮权限（作为子菜单）
-- 这是更符合系统设计的方式

-- 首先查找菜单管理的ID
SELECT id, name, router FROM merchant_sys_menu WHERE name = '菜单管理' AND router = '/system/menu';

-- 假设菜单管理的ID是63，添加按钮权限作为子菜单
-- type=2 表示按钮权限
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(63, '新增', '', 'system:menu:add', 2, '', 1, 0, 0, NOW(), NOW()),
(63, '编辑', '', 'system:menu:edit', 2, '', 2, 0, 0, NOW(), NOW()),
(63, '删除', '', 'system:menu:delete', 2, '', 3, 0, 0, NOW(), NOW());

-- 同样为用户管理添加按钮权限
-- 首先查找用户管理的ID
SELECT id, name, router FROM merchant_sys_menu WHERE name = '用户管理' AND router = '/system/user';

-- 假设用户管理的ID是61
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(61, '新增', '', 'system:user:add', 2, '', 1, 0, 0, NOW(), NOW()),
(61, '编辑', '', 'system:user:edit', 2, '', 2, 0, 0, NOW(), NOW()),
(61, '删除', '', 'system:user:delete', 2, '', 3, 0, 0, NOW(), NOW());

-- 为角色管理添加按钮权限
-- 首先查找角色管理的ID
SELECT id, name, router FROM merchant_sys_menu WHERE name = '角色管理' AND router = '/system/role';

-- 假设角色管理的ID是62
INSERT INTO merchant_sys_menu (parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES
(62, '新增', '', 'system:role:add', 2, '', 1, 0, 0, NOW(), NOW()),
(62, '编辑', '', 'system:role:edit', 2, '', 2, 0, 0, NOW(), NOW()),
(62, '删除', '', 'system:role:delete', 2, '', 3, 0, 0, NOW(), NOW());

-- 查询验证结果
SELECT 
  m1.name AS parent_menu,
  m1.router AS parent_router,
  m2.name AS button_name,
  m2.perms AS permission,
  m2.type
FROM merchant_sys_menu m1
LEFT JOIN merchant_sys_menu m2 ON m1.id = m2.parentId
WHERE m1.router IN ('/system/menu', '/system/user', '/system/role') 
  AND m2.type = 2
ORDER BY m1.id, m2.orderNum;