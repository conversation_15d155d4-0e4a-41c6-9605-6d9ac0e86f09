{"name": "finance-service", "version": "1.0.0", "main": "main.ts", "license": "MIT", "scripts": {"start": "node bootstrap.js", "dev": "cross-env NODE_ENV=local midway-bin dev --ts", "build": "midway-bin build", "test": "midway-bin test --ts", "lint": "eslint src --ext .ts"}, "dependencies": {"@cool-midway/core": "^8.0.1", "@cool-midway/rpc": "^8.0.1", "@midwayjs/core": "^3.16.0", "@midwayjs/koa": "^3.16.0", "@midwayjs/typeorm": "^3.16.0", "typeorm": "^0.3.17", "mysql2": "^3.6.0", "moleculer": "^0.14.35", "redis": "^4.6.8", "moment": "^2.29.4", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^20.0.0", "@types/lodash": "^4.14.195", "typescript": "^5.0.0", "cross-env": "^7.0.3", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}, "engines": {"node": ">=16.0.0"}}