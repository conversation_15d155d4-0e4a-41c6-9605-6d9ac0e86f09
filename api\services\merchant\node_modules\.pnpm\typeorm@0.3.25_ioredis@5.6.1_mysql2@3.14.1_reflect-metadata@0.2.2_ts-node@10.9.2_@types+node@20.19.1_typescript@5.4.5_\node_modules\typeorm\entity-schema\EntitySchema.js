"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EntitySchema = void 0;
/**
 * Interface for entity metadata mappings stored inside "schemas" instead of models decorated by decorators.
 */
class EntitySchema {
    constructor(options) {
        this.options = options;
        this["@instanceof"] = Symbol.for("EntitySchema");
    }
}
exports.EntitySchema = EntitySchema;

//# sourceMappingURL=EntitySchema.js.map
