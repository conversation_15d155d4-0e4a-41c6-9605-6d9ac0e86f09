{"name": "@midwayjs/koa", "version": "3.20.5", "description": "Midway Web Framework for KOA", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test"}, "keywords": ["midway", "IoC", "web", "scene", "koa"], "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "license": "MIT", "devDependencies": {"@midwayjs/mock": "^3.20.4", "@types/koa-router": "7.4.8", "fs-extra": "11.3.0"}, "dependencies": {"@koa/router": "^12.0.0", "@midwayjs/cookies": "^1.3.0", "@midwayjs/core": "^3.20.4", "@midwayjs/session": "^3.20.5", "@types/koa": "2.15.0", "@types/qs": "6.9.18", "koa": "2.16.1", "koa-bodyparser": "4.4.1", "qs": "6.14.0"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "engines": {"node": ">=12"}, "gitHead": "7ce57281bd3ef5d18dc50b47ff9bffb8a27c071e"}