{"version": 3, "sources": ["../browser/src/metadata-args/EntityListenerMetadataArgs.ts"], "names": [], "mappings": "", "file": "EntityListenerMetadataArgs.js", "sourcesContent": ["import { EventListenerType } from \"../metadata/types/EventListenerTypes\"\n\n/**\n * Arguments for EntityListenerMetadata class.\n */\nexport interface EntityListenerMetadataArgs {\n    /**\n     * Class to which listener is applied.\n     */\n    readonly target: Function\n\n    /**\n     * Class's property name to which listener is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * The type of the listener.\n     */\n    readonly type: EventListenerType\n}\n"], "sourceRoot": ".."}