{"version": 3, "file": "IsNumberString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsNumberString.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAG5C,QAAA,gBAAgB,GAAG,gBAAgB,CAAC;AAEjD;;;GAGG;AACH,SAAgB,cAAc,CAAC,KAAc,EAAE,OAAsC;IACnF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACzE,CAAC;AAFD,wCAEC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAC5B,OAAsC,EACtC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,wBAAgB;QACtB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,cAAc,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/E,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,mCAAmC,EAAE,iBAAiB,CAAC;SAChH;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,wCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isNumericValidator from 'validator/lib/isNumeric';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_NUMBER_STRING = 'isNumberString';\n\n/**\n * Checks if the string is numeric.\n * If given value is not a string, then it returns false.\n */\nexport function isNumberString(value: unknown, options?: ValidatorJS.IsNumericOptions): boolean {\n  return typeof value === 'string' && isNumericValidator(value, options);\n}\n\n/**\n * Checks if the string is numeric.\n * If given value is not a string, then it returns false.\n */\nexport function IsNumberString(\n  options?: ValidatorJS.IsNumericOptions,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NUMBER_STRING,\n      constraints: [options],\n      validator: {\n        validate: (value, args): boolean => isNumberString(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a number string', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}