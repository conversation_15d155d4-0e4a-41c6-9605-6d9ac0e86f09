{"version": 3, "sources": ["../../src/find-options/operator/Between.ts"], "names": [], "mappings": ";;AAMA,0BAKC;AAXD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,OAAO,CACnB,IAAyB,EACzB,EAAuB;IAEvB,OAAO,IAAI,2BAAY,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE,CAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACrE,CAAC", "file": "Between.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Between(x, y) }\n */\nexport function Between<T>(\n    from: T | FindOperator<T>,\n    to: T | FindOperator<T>,\n): FindOperator<T> {\n    return new FindOperator(\"between\", [from, to] as any, true, true)\n}\n"], "sourceRoot": "../.."}