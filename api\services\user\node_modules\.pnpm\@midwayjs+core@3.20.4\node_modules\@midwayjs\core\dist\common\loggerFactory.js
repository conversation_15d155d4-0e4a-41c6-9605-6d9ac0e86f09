"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DefaultConsoleLoggerFactory = exports.LoggerFactory = void 0;
class LoggerFactory {
}
exports.LoggerFactory = LoggerFactory;
class DefaultConsoleLoggerFactory {
    constructor() {
        this.instance = new Map();
    }
    createLogger(name, options) {
        this.instance.set(name, console);
        return console;
    }
    getLogger(loggerName) {
        return this.instance.get(loggerName);
    }
    close(loggerName) { }
    removeLogger(loggerName) { }
    getDefaultMidwayLoggerConfig() {
        return {
            midwayLogger: {
                default: {},
                clients: {
                    coreLogger: {},
                    appLogger: {},
                },
            },
        };
    }
    createContextLogger(ctx, appLogger, contextOptions) {
        return appLogger;
    }
    getClients() {
        return this.instance;
    }
    getClientKeys() {
        return Array.from(this.instance.keys());
    }
}
exports.DefaultConsoleLoggerFactory = DefaultConsoleLoggerFactory;
//# sourceMappingURL=loggerFactory.js.map