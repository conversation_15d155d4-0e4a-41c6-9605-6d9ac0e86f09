"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findCommonDir = exports.findFile = exports.findDependenciesByAST = exports.filterModule = exports.propertyExists = exports.safeGetProperty = exports.safeReadJSON = exports.exists = void 0;
const fse = require("fs-extra");
exports.exists = async (file) => {
    return fse.pathExists(file);
};
exports.safeReadJSON = async (file) => {
    if (await exports.exists(file)) {
        try {
            return fse.readJSON(file);
        }
        catch (_) {
            return {};
        }
    }
    else {
        return {};
    }
};
exports.safeGetProperty = (json, property) => {
    if (!json) {
        return null;
    }
    let properties = property;
    if (typeof property === 'string') {
        properties = property.split('.');
    }
    const currentProperty = properties.shift();
    if (properties.length > 0 && typeof json[currentProperty] === 'object') {
        return exports.safeGetProperty(json[currentProperty], properties);
    }
    return json[currentProperty];
};
exports.propertyExists = (json, properties) => {
    for (let propertyText of properties) {
        const data = exports.safeGetProperty(json, propertyText);
        if (data) {
            return true;
        }
    }
    return false;
};
const nativeModule = [
    'assert',
    'async_hooks',
    'buffer',
    'child_process',
    'cluster',
    'crypto',
    'dgram',
    'dns',
    'domain',
    'events',
    'fs',
    'http',
    'http2',
    'https',
    'inspector',
    'module',
    'net',
    'os',
    'path',
    'perf_hooks',
    'process',
    'punycode',
    'querystring',
    'readline',
    'repl',
    'stream',
    'string_decoder',
    'sys',
    'timers',
    'tls',
    'trace_events',
    'tty',
    'url',
    'util',
    'v8',
    'vm',
    'wasi',
    'worker_threads',
    'zlib',
    'config',
];
exports.filterModule = (module, modules) => {
    var _a, _b;
    // remove local module
    if (/^\./.test(module))
        return;
    // filter native module
    if (nativeModule.indexOf(module) !== -1)
        return;
    if (/^@/.test(module)) {
        // @midwayjs/abc/bbb
        if (((_a = module.match(/\//g)) === null || _a === void 0 ? void 0 : _a.length) >= 2) {
            const result = module.split('/');
            module = result[0] + '/' + result[1];
        }
    }
    else {
        // abc/bbb
        if (((_b = module.match(/\//g)) === null || _b === void 0 ? void 0 : _b.length) >= 1) {
            const result = module.split('/');
            module = result[0];
        }
    }
    modules.add(module);
};
// export const findDependencies = src => {
//   const dep = [];
//   src.replace(/(import .+ )?(from|require)\s?['"(](.+)['")]/g, (...args) => {
//     dep.push(args[3]);
//     return args[3];
//   });
//   return dep;
// };
exports.findDependenciesByAST = (source, jsx = false) => {
    const matches = [
        [/(?:^|\n|;|\s)\s*import\s+[\s\w\$\_\{\},\*\.\[\]]+\s+from\s*['"](.*?)['"]\s*;?/mg, 1],
        [/(?:^|\n|;|\s)\s*import\s*\(\s*['"](.*?)['"]\s*\)/mg, 1],
        [/(?:^|\n|;|\s)\s*import\s*['"](.*?)['"]\s*/mg, 1],
        [/(?:^|\n|;|\s)\s*import\s+[\w\$\_]+\s*from\s*['"](.*?)['"]\s*;?/mg, 1],
        [/(?:^|\n|;|\s)\s*export\s+(?:\*|\{[^\}]+\})\s+from\s+['"](.*?)['"]\s*;?/mg, 1],
        [/(?:^|\n|;|\s)\s*require\s*\(\s*['"](.*?)['"]\s*\)/mg, 1],
    ];
    const depsMap = {};
    for (const match of matches) {
        const [reg, resIndex] = match;
        let execRes;
        while (execRes = reg.exec(source)) {
            // console.log('execRes', execRes);
            depsMap[execRes[resIndex]] = true;
        }
    }
    return Object.keys(depsMap);
};
exports.findFile = async (files) => {
    for (const file of files) {
        if (await exports.exists(file)) {
            return file;
        }
    }
};
exports.findCommonDir = (files) => {
    if (files.length == 0)
        return "";
    const isWindow = files[0].indexOf('\\') != -1;
    const splitStr = isWindow ? '\\' : '/';
    const allFilePath = files.map(file => {
        const list = file.split(splitStr);
        const last = list[list.length - 1];
        if (last.indexOf('.') != -1) {
            return list.slice(0, -1);
        }
        return list;
    });
    let ans = allFilePath[0];
    for (let i = 1; i < allFilePath.length; i++) {
        let j = 0;
        for (; j < ans.length && j < allFilePath[i].length; j++) {
            if (ans[j] != allFilePath[i][j])
                break;
        }
        ans = ans.slice(0, j);
        if (!ans.length) {
            return '';
        }
    }
    return ans.join(splitStr);
};
//# sourceMappingURL=data:application/json;base64,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