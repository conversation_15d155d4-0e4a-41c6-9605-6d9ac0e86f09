{"name": "@midwayjs/debugger", "version": "1.0.9", "description": "<PERSON><PERSON><PERSON><PERSON> Debug", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"test": "midway-bin test --ts --runInBand", "cov": "midway-bin cov --ts --runInBand", "build": "midway-bin build -c"}, "keywords": ["midway", "debug"], "files": ["dist", "src"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "engines": {"node": ">=10.3.0"}, "devDependencies": {"@types/mocha": "^5.2.7", "@types/node": "^10.3.0", "fs-extra": "^9.0.1", "midway-bin": "1", "typescript": "^3.7.4"}, "dependencies": {"node-fetch": "^2.6.0", "ws": "^7.2.3"}}