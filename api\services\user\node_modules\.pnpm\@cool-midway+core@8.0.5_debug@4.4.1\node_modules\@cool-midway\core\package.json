{"name": "@cool-midway/core", "version": "8.0.5", "description": "cool-admin midway core", "main": "dist/index.js", "typings": "index.d.ts", "bin": {"cool": "dist/bin/index.js"}, "scripts": {"build": "mwtsc --cleanOutDir", "test": "cross-env NODE_ENV=unittest jest", "cov": "jest --coverage", "lint": "mwts check", "lint:fix": "mwts fix"}, "keywords": ["cool", "cool-admin", "cooljs"], "author": "COOL", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "readme": "README.md", "license": "MIT", "repository": {"type": "git", "url": "https://cool-js.com"}, "devDependencies": {"@midwayjs/core": "^3.20.0", "@midwayjs/koa": "^3.20.0", "@midwayjs/logger": "^3.4.2", "@midwayjs/mock": "^3.20.0", "@midwayjs/typeorm": "^3.20.0", "@types/download": "^8.0.5", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "aedes": "^0.51.3", "cross-env": "^7.0.3", "jest": "^29.7.0", "mwts": "^1.3.0", "mwtsc": "^1.15.1", "ts-jest": "^29.2.5", "typeorm": "^0.3.20", "typescript": "~5.7.3"}, "dependencies": {"@cool-midway/cache-manager-fs-hash": "^7.0.0", "@midwayjs/cache-manager": "^3.20.0", "axios": "^1.7.9", "commander": "^13.1.0", "download": "^8.0.0", "glob": "^11.0.1", "javascript-obfuscator": "^4.1.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "pm2": "^5.4.3", "sqlstring": "^2.3.3", "uuid": "^11.0.5", "ws": "^8.18.0"}}