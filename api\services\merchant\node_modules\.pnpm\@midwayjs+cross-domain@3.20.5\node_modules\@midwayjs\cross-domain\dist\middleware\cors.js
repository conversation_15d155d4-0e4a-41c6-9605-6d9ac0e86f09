"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CorsMiddleware = void 0;
const core_1 = require("@midwayjs/core");
const vary = require("vary");
let CorsMiddleware = class CorsMiddleware {
    resolve(app) {
        if (app.getFrameworkType() === core_1.MidwayFrameworkType.WEB_EXPRESS) {
            return async (req, res, next) => {
                return this.compatibleMiddleware(req, res, next);
            };
        }
        else {
            return async (ctx, next) => {
                return this.compatibleMiddleware(ctx.request, ctx, next);
            };
        }
    }
    async compatibleMiddleware(request, response, next) {
        const requestOrigin = request.get('origin');
        // Always set Vary header
        response.vary('Origin');
        if (!requestOrigin) {
            return await next();
        }
        let origin;
        if (typeof this.cors.origin === 'function') {
            origin = await Promise.resolve(this.cors.origin(request));
            if (!origin) {
                return await next();
            }
        }
        else {
            origin = this.cors.origin || requestOrigin;
        }
        let credentials;
        if (typeof this.cors.credentials === 'function') {
            credentials = await Promise.resolve(this.cors.credentials(request));
        }
        else {
            credentials = !!this.cors.credentials;
        }
        if (request.method.toUpperCase() === 'OPTIONS') {
            if (!request.get('Access-Control-Request-Method')) {
                return await next();
            }
            response.set('Access-Control-Allow-Origin', origin);
            if (credentials === true) {
                response.set('Access-Control-Allow-Credentials', 'true');
            }
            if (this.cors.maxAge) {
                response.set('Access-Control-Max-Age', this.cors.maxAge);
            }
            if (this.cors.allowMethods) {
                response.set('Access-Control-Allow-Methods', this.cors.allowMethods);
            }
            let allowHeaders = this.cors.allowHeaders;
            if (!allowHeaders) {
                allowHeaders = request.get('Access-Control-Request-Headers');
            }
            if (allowHeaders) {
                response.set('Access-Control-Allow-Headers', allowHeaders);
            }
            if (response.sendStatus) {
                response.sendStatus(204);
            }
            else {
                response.status = 204;
            }
            return '';
        }
        const headersSet = {};
        function set(key, value) {
            response.set(key, value);
            headersSet[key] = value;
        }
        set('Access-Control-Allow-Origin', origin);
        if (credentials === true) {
            set('Access-Control-Allow-Credentials', 'true');
        }
        if (this.cors.exposeHeaders) {
            set('Access-Control-Expose-Headers', this.cors.exposeHeaders);
        }
        if (!this.cors.keepHeadersOnError) {
            return await next();
        }
        try {
            return await next();
        }
        catch (err) {
            const errHeadersSet = err.headers || {};
            const varyWithOrigin = vary.append(errHeadersSet.vary || errHeadersSet.Vary || '', 'Origin');
            delete errHeadersSet.Vary;
            err.headers = {
                ...errHeadersSet,
                ...headersSet,
                ...{ vary: varyWithOrigin },
            };
            throw err;
        }
    }
    static getName() {
        return 'cors';
    }
};
__decorate([
    (0, core_1.Config)('cors'),
    __metadata("design:type", Object)
], CorsMiddleware.prototype, "cors", void 0);
CorsMiddleware = __decorate([
    (0, core_1.Middleware)()
], CorsMiddleware);
exports.CorsMiddleware = CorsMiddleware;
//# sourceMappingURL=cors.js.map