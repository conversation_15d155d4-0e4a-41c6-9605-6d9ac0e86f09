"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const os_1 = require("os");
class LightSpinner {
    constructor(config) {
        this.index = 0;
        this.isWin = os_1.platform() === 'win32';
        const { spinners, text, timeout } = config || {};
        this.spinners = spinners || this.isWin ? ['-', '\\', '/'] : ['⠋', '⠙', '⠹', '⠼', '⠴', '⠦', '⠧', '⠏'];
        this.timeout = timeout || 100;
        this.text = text || '';
    }
    start() {
        this.index = 0;
        clearTimeout(this.timeHandler);
        this.doing();
    }
    stop() {
        clearTimeout(this.timeHandler);
        this.clearLine();
    }
    clearLine() {
        if (!this.message) {
            return;
        }
        const clearChar = '\u001b[2K';
        const moveCursor = `\u001b[${Buffer.byteLength(this.message)}D`;
        process.stdout.write(clearChar);
        process.stdout.write(moveCursor);
    }
    output(message) {
        this.message = message;
        process.stdout.write(message);
    }
    doing() {
        const message = `${this.spinners[this.index++]} ${this.text || ''} `;
        this.clearLine();
        this.output(message);
        if (this.index >= this.spinners.length) {
            this.index = 0;
        }
        this.timeHandler = setTimeout(() => {
            this.doing();
        }, this.timeout);
    }
}
exports.default = LightSpinner;
