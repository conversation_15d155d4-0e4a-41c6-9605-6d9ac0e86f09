import { GRPCMetadata, MSProviderType } from '../../interface';
export declare function Provider(type: MSProviderType.GRPC, metadata?: GRPCMetadata.ProviderOptions): ClassDecorator;
export declare function Provider(type: MSProviderType.DUBBO, metadata?: any): ClassDecorator;
export declare enum GrpcStreamTypeEnum {
    BASE = "base",
    DUPLEX = "ServerDuplexStream",
    READABLE = "ServerReadableStream",
    WRITEABLE = "ServerWritableStream"
}
export declare function GrpcMethod(methodOptions?: {
    methodName?: string;
    type?: GrpcStreamTypeEnum;
    onEnd?: string;
}): MethodDecorator;
export declare function DubboMethod(methodName?: string): MethodDecorator;
//# sourceMappingURL=provider.d.ts.map