-- =====================================================
-- 商户管理系统数据库初始化脚本
-- 严格按照 art-design-pro 前端系统的数据结构要求创建
-- 前端数据结构参考：Api.User.UserInfo, Api.Auth, AppRouteRecord
-- =====================================================

USE `merchant_service_db`;

-- ======================================
-- 第一步：删除可能存在的旧表
-- ======================================
DROP TABLE IF EXISTS `merchant_sys_role`;  -- 不需要角色表，前端采用简化设计
DROP TABLE IF EXISTS `merchant_sys_user`;
CREATE TABLE `merchant_sys_user` (
  `userId` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID (严格对应前端 Api.User.UserInfo.userId)',
  `userName` varchar(100) NOT NULL COMMENT '用户名 (严格对应前端 Api.User.UserInfo.userName)',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `passwordV` int(11) NOT NULL DEFAULT '1' COMMENT '密码版本',
  `roles` text COMMENT '角色权限数组 (严格对应前端 Api.User.UserInfo.roles: string[])',
  `buttons` text COMMENT '按钮权限数组 (严格对应前端 Api.User.UserInfo.buttons: string[])',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像 (严格对应前端 Api.User.UserInfo.avatar)',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱 (严格对应前端 Api.User.UserInfo.email)',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号 (严格对应前端 Api.User.UserInfo.phone)',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_userName` (`userName`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统用户表 (严格对应前端 Api.User.UserInfo)';

-- ======================================
-- 第二步：创建菜单表 (对应 AppRouteRecord)
-- ======================================
DROP TABLE IF EXISTS `merchant_sys_menu`;
CREATE TABLE `merchant_sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID (对应前端 AppRouteRecord.id)',
  `parentId` int(11) NOT NULL DEFAULT '0' COMMENT '父级菜单ID',
  `name` varchar(100) NOT NULL COMMENT '路由名称 (对应前端 AppRouteRecord.name)',
  `router` varchar(200) DEFAULT NULL COMMENT '路由路径 (对应前端 AppRouteRecord.path)',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径 (对应前端 AppRouteRecord.component)',
  `title` varchar(100) NOT NULL COMMENT '菜单标题 (对应前端 RouteMeta.title)',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标 (对应前端 RouteMeta.icon)',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '菜单类型 0-目录 1-菜单 2-按钮',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `isHide` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏 (对应前端 RouteMeta.isHide)',
  `keepAlive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否缓存 (对应前端 RouteMeta.keepAlive)',
  `authList` text COMMENT '按钮权限列表 (JSON格式, 对应前端 RouteMeta.authList)',
  `roles` text COMMENT '角色权限 (JSON格式, 对应前端 RouteMeta.roles)',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parentId`),
  KEY `idx_type` (`type`),
  KEY `idx_order` (`orderNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统菜单表 (严格对应前端 AppRouteRecord)';

-- ======================================
-- 第三步：插入初始菜单数据 (与前端 art-design-pro 完全匹配)
-- ======================================

-- 一级菜单：首页
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(1, 0, 'Dashboard', '/dashboard', '/dashboard/index', '首页', '&#xe7ae;', 1, 1, 0, 1);

-- 一级菜单：商户管理
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(100, 0, 'Merchant', '/merchant', '/index/index', '商户管理', '&#xe7b4;', 0, 2, 0, 0);

-- 商户管理子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(101, 100, 'MerchantSettleIn', '/merchant/settle-in', '/merchant/settle-in', '商户入驻', '', 1, 1, 0, 1),
(102, 100, 'MerchantHeritageAuth', '/merchant/heritage-auth', '/merchant/heritage-auth', '非遗人认证', '', 1, 2, 0, 1),
(103, 100, 'MerchantPersonal', '/merchant/personal', '/merchant/personal', '个人商户', '', 1, 3, 0, 1),
(104, 100, 'MerchantCompany', '/merchant/company', '/merchant/company', '企业商户', '', 1, 4, 0, 1);

-- 一级菜单：系统管理
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`) VALUES
(200, 0, 'System', '/system', '/index/index', '系统管理', '&#xe7ca;', 0, 3, 0, 0);

-- 系统管理子菜单
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `component`, `title`, `icon`, `type`, `orderNum`, `isHide`, `keepAlive`, `authList`) VALUES
(201, 200, 'SystemUser', '/system/user', '/system/user/index', '用户管理', '', 1, 1, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]'),
(202, 200, 'SystemRole', '/system/role', '/system/role/index', '角色管理', '', 1, 2, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]'),
(203, 200, 'SystemMenu', '/system/menu', '/system/menu/index', '菜单管理', '', 1, 3, 0, 1, '[{"title":"新增","auth_mark":"add"},{"title":"编辑","auth_mark":"edit"},{"title":"删除","auth_mark":"delete"}]');

-- ======================================
-- 第四步：插入初始用户数据 (严格对应前端 Api.User.UserInfo)
-- ======================================
INSERT INTO `merchant_sys_user` (`userId`, `userName`, `password`, `passwordV`, `roles`, `buttons`, `avatar`, `email`, `phone`, `status`) VALUES
(1, 'admin', '21232f297a57a5a743894a0e4a801fc3', 1, '["admin","super"]', '["add","edit","delete","view","import","export"]', NULL, '<EMAIL>', '13800138000', 1),
(2, 'merchant', '1d0258c2440a8d19e716292b231e3190', 1, '["merchant"]', '["view","edit"]', NULL, '<EMAIL>', '13800138001', 1),
(3, 'demo', '098f6bcd4621d373cade4e832627b4f6', 1, '["demo"]', '["view"]', NULL, '<EMAIL>', '13800138002', 1);

-- ======================================
-- 第五步：验证数据完整性
-- ======================================
SELECT '========== 商户管理系统初始化完成 ==========' AS message;

-- 验证表结构
SELECT 
    '用户表' AS table_name,
    COUNT(*) AS record_count
FROM merchant_sys_user
UNION ALL
SELECT 
    '菜单表' AS table_name,
    COUNT(*) AS record_count
FROM merchant_sys_menu;

-- 显示初始用户账号信息（严格对应前端 Api.Auth.LoginParams）
SELECT '========== 初始登录账号信息 ==========' AS message;

SELECT 
    CONCAT('用户名(userName): ', userName, ' | 密码(password): ', 
    CASE userName 
        WHEN 'admin' THEN 'admin'
        WHEN 'merchant' THEN 'merchant' 
        WHEN 'demo' THEN 'test'
    END, ' | 角色(roles): ', IFNULL(roles, '[]')) AS account_info
FROM merchant_sys_user
ORDER BY userId;

-- 显示菜单结构
SELECT '========== 菜单结构预览 ==========' AS message;

SELECT 
    CONCAT(
        CASE WHEN parentId = 0 THEN '📁 ' ELSE '  📄 ' END,
        title, 
        ' (', name, ')'
    ) AS menu_structure
FROM merchant_sys_menu 
ORDER BY parentId, orderNum; 