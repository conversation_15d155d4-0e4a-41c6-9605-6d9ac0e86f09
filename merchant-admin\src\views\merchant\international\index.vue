<template>
  <div class="international-merchant-page">




    <!-- 主要内容 -->
    <el-card shadow="never">
      <el-tabs v-model="activeTab" class="international-tabs">
        <!-- 国家配置 -->
        <el-tab-pane label="国家配置" name="countries">
          <div class="countries-section">
            <div class="section-header">
              <h3>支持的国家/地区</h3>
              <el-button type="primary" @click="showCountryDialog = true">
                <el-icon><Plus /></el-icon>
                添加国家
              </el-button>
            </div>

            <el-table :data="countriesData" style="width: 100%">
              <el-table-column prop="flag" label="国旗" width="80">
                <template #default="{ row }">
                  <span class="country-flag">{{ row.flag }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="国家名称" width="120" />
              <el-table-column prop="code" label="国家代码" width="100" />
              <el-table-column prop="currency" label="本地货币" width="100" />
              <el-table-column prop="timezone" label="时区" width="150" />
              <el-table-column prop="merchantCount" label="商户数量" width="100" />
              <el-table-column prop="complianceLevel" label="合规等级" width="120">
                <template #default="{ row }">
                  <el-tag :type="getComplianceType(row.complianceLevel)">
                    {{ getComplianceLabel(row.complianceLevel) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-switch 
                    v-model="row.status" 
                    @change="toggleCountryStatus(row)"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="{ row }">
                  <el-button size="small" @click="editCountry(row)">编辑</el-button>
                  <el-button size="small" @click="viewMerchants(row)">查看商户</el-button>
                  <el-button size="small" type="info" @click="viewCompliance(row)">合规</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 商户分布 -->
        <el-tab-pane label="商户分布" name="distribution">
          <div class="distribution-section">
            <div class="distribution-charts">
              <!-- 地区分布图 -->
              <el-card class="chart-card">
                <template #header>
                  <span>商户地区分布</span>
                </template>
                <div class="chart-container">
                  <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
                    商户地区分布图表
                  </div>
                </div>
              </el-card>

              <!-- 币种分布图 -->
              <el-card class="chart-card">
                <template #header>
                  <span>交易币种分布</span>
                </template>
                <div class="chart-container">
                  <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
                    币种分布饼图
                  </div>
                </div>
              </el-card>
            </div>

            <!-- 详细数据表 -->
            <el-table :data="distributionData" style="width: 100%; margin-top: 20px;">
              <el-table-column prop="country" label="国家" width="120" />
              <el-table-column prop="merchantCount" label="商户数量" width="100" />
              <el-table-column prop="monthlyVolume" label="月交易额" width="150">
                <template #default="{ row }">
                  {{ formatCurrency(row.monthlyVolume, row.currency) }}
                </template>
              </el-table-column>
              <el-table-column prop="avgOrderValue" label="平均订单金额" width="150">
                <template #default="{ row }">
                  {{ formatCurrency(row.avgOrderValue, row.currency) }}
                </template>
              </el-table-column>
              <el-table-column prop="commissionRate" label="平均费率" width="100">
                <template #default="{ row }">
                  {{ row.commissionRate }}%
                </template>
              </el-table-column>
              <el-table-column prop="growth" label="增长率" width="100">
                <template #default="{ row }">
                  <span :class="row.growth >= 0 ? 'growth-positive' : 'growth-negative'">
                    {{ row.growth >= 0 ? '+' : '' }}{{ row.growth }}%
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 合规管理 -->
        <el-tab-pane label="合规管理" name="compliance">
          <div class="compliance-section">
            <el-alert
              title="合规提醒"
              description="不同国家有不同的法律法规要求，请确保商户入驻符合当地合规标准"
              type="warning"
              :closable="false"
              style="margin-bottom: 20px;"
            />

            <el-table :data="complianceData" style="width: 100%">
              <el-table-column prop="country" label="国家" width="120" />
              <el-table-column prop="kycRequired" label="KYC要求" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.kycRequired ? 'success' : 'info'">
                    {{ row.kycRequired ? '必需' : '可选' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="taxReporting" label="税务申报" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.taxReporting ? 'warning' : 'info'">
                    {{ row.taxReporting ? '必需' : '免除' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="dataProtection" label="数据保护" width="120" />
              <el-table-column prop="financialLicense" label="金融牌照" width="120">
                <template #default="{ row }">
                  <el-tag :type="row.financialLicense ? 'success' : 'danger'">
                    {{ row.financialLicense ? '已获得' : '申请中' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="lastAudit" label="最后审计" width="120" />
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button size="small" @click="updateCompliance(row)">更新</el-button>
                  <el-button size="small" type="info" @click="viewDetails(row)">详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <!-- 本地化设置 -->
        <el-tab-pane label="本地化设置" name="localization">
          <div class="localization-section">
            <el-form :model="localizationForm" label-width="140px">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="选择国家">
                    <el-select v-model="localizationForm.country" @change="loadCountrySettings">
                      <el-option 
                        v-for="country in countriesData" 
                        :key="country.code"
                        :label="country.name" 
                        :value="country.code" 
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="默认语言">
                    <el-select v-model="localizationForm.language">
                      <el-option label="简体中文" value="zh-CN" />
                      <el-option label="English" value="en-US" />
                      <el-option label="日本語" value="ja-JP" />
                      <el-option label="한국어" value="ko-KR" />
                      <el-option label="Français" value="fr-FR" />
                      <el-option label="Deutsch" value="de-DE" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="支付方式">
                    <el-checkbox-group v-model="localizationForm.paymentMethods">
                      <el-checkbox label="credit_card" value="credit_card">信用卡</el-checkbox>
                      <el-checkbox label="bank_transfer" value="bank_transfer">银行转账</el-checkbox>
                      <el-checkbox label="digital_wallet" value="digital_wallet">数字钱包</el-checkbox>
                      <el-checkbox label="local_payment" value="local_payment">本地支付</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行对接">
                    <el-select v-model="localizationForm.bankPartner">
                      <el-option label="本地银行联盟" value="local_banks" />
                      <el-option label="国际银行" value="international_banks" />
                      <el-option label="第三方支付" value="third_party" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="入驻要求">
                <el-input 
                  v-model="localizationForm.requirements" 
                  type="textarea" 
                  :rows="4"
                  placeholder="描述该国家商户入驻的特殊要求..."
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveLocalizationSettings">
                  保存本地化设置
                </el-button>
                <el-button @click="resetLocalizationForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 添加国家对话框 -->
    <el-dialog v-model="showCountryDialog" title="添加支持国家" width="600px">
      <el-form :model="countryForm" label-width="120px">
        <el-form-item label="国家名称">
          <el-input v-model="countryForm.name" />
        </el-form-item>
        <el-form-item label="国家代码">
          <el-input v-model="countryForm.code" placeholder="如: US, JP, KR" />
        </el-form-item>
        <el-form-item label="本地货币">
          <el-input v-model="countryForm.currency" placeholder="如: USD, JPY, KRW" />
        </el-form-item>
        <el-form-item label="时区">
          <el-select v-model="countryForm.timezone">
            <el-option label="美东时间 (UTC-5)" value="America/New_York" />
            <el-option label="美西时间 (UTC-8)" value="America/Los_Angeles" />
            <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
            <el-option label="首尔时间 (UTC+9)" value="Asia/Seoul" />
            <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
            <el-option label="柏林时间 (UTC+1)" value="Europe/Berlin" />
          </el-select>
        </el-form-item>
        <el-form-item label="合规等级">
          <el-select v-model="countryForm.complianceLevel">
            <el-option label="标准合规" value="standard" />
            <el-option label="严格合规" value="strict" />
            <el-option label="简化合规" value="simplified" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCountryDialog = false">取消</el-button>
        <el-button type="primary" @click="addCountry">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { User, Money, TrendCharts, Plus } from '@element-plus/icons-vue'

defineOptions({ name: 'InternationalMerchant' })

const activeTab = ref('countries')
const showCountryDialog = ref(false)



// 国家数据 - 目前以中国为主，其他国家为规划中
const countriesData = ref([
  {
    flag: '🇨🇳',
    name: '中国',
    code: 'CN',
    currency: 'CNY',
    timezone: 'Asia/Shanghai',
    merchantCount: 8456,
    complianceLevel: 'standard',
    status: true,
    isActive: true,
    description: '主要市场，支持个人和企业商户'
  },
  {
    flag: '🇺🇸',
    name: '美国',
    code: 'US',
    currency: 'USD',
    timezone: 'America/New_York',
    merchantCount: 0,
    complianceLevel: 'strict',
    status: false,
    isActive: false,
    description: '规划中 - 预计Q2开通，需要严格KYC和AML合规'
  },
  {
    flag: '🇯🇵',
    name: '日本',
    code: 'JP',
    currency: 'JPY',
    timezone: 'Asia/Tokyo',
    merchantCount: 0,
    complianceLevel: 'standard',
    status: false,
    isActive: false,
    description: '规划中 - 预计Q3开通，需要JFSA合规认证'
  },
  {
    flag: '🇰🇷',
    name: '韩国',
    code: 'KR',
    currency: 'KRW',
    timezone: 'Asia/Seoul',
    merchantCount: 0,
    complianceLevel: 'standard',
    status: false,
    isActive: false,
    description: '规划中 - 预计Q4开通，需要本地银行对接'
  }
])

// 分布数据 - 目前只有中国市场数据
const distributionData = ref([
  {
    country: '中国',
    merchantCount: 8456,
    monthlyVolume: 28500000, // 2.85亿元
    avgOrderValue: 285,
    currency: 'CNY',
    commissionRate: 2.8,
    growth: 15.6
  },
  {
    country: '美国',
    merchantCount: 0,
    monthlyVolume: 0,
    avgOrderValue: 0,
    currency: 'USD',
    commissionRate: 3.2,
    growth: 0,
    status: '规划中'
  },
  {
    country: '日本',
    merchantCount: 0,
    monthlyVolume: 0,
    avgOrderValue: 0,
    currency: 'JPY',
    commissionRate: 3.0,
    growth: 0,
    status: '规划中'
  }
])

// 合规数据
const complianceData = ref([
  {
    country: '美国',
    kycRequired: true,
    taxReporting: true,
    dataProtection: 'CCPA',
    financialLicense: true,
    lastAudit: '2024-01-15'
  },
  {
    country: '欧盟',
    kycRequired: true,
    taxReporting: true,
    dataProtection: 'GDPR',
    financialLicense: true,
    lastAudit: '2024-01-10'
  }
])

// 本地化表单
const localizationForm = reactive({
  country: '',
  language: '',
  paymentMethods: [],
  bankPartner: '',
  requirements: ''
})

// 国家表单
const countryForm = reactive({
  name: '',
  code: '',
  currency: '',
  timezone: '',
  complianceLevel: 'standard'
})

// 获取合规等级类型
const getComplianceType = (level: string) => {
  const types: Record<string, string> = {
    'standard': '',
    'strict': 'warning',
    'simplified': 'success'
  }
  return types[level] || ''
}

// 获取合规等级标签
const getComplianceLabel = (level: string) => {
  const labels: Record<string, string> = {
    'standard': '标准',
    'strict': '严格',
    'simplified': '简化'
  }
  return labels[level] || level
}

// 格式化货币
const formatCurrency = (amount: number, currency: string) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

// 切换国家状态
const toggleCountryStatus = (country: any) => {
  const status = country.status ? '启用' : '禁用'
  ElMessage.success(`${country.name} ${status}成功`)
}

// 编辑国家
const editCountry = (country: any) => {
  ElMessage.info(`编辑 ${country.name} 配置`)
}

// 查看商户
const viewMerchants = (country: any) => {
  ElMessage.info(`查看 ${country.name} 的商户列表`)
}

// 查看合规
const viewCompliance = (country: any) => {
  ElMessage.info(`查看 ${country.name} 的合规详情`)
}

// 更新合规
const updateCompliance = (row: any) => {
  ElMessage.info(`更新 ${row.country} 的合规信息`)
}

// 查看详情
const viewDetails = (row: any) => {
  ElMessage.info(`查看 ${row.country} 的详细信息`)
}

// 加载国家设置
const loadCountrySettings = (countryCode: string) => {
  ElMessage.info(`加载 ${countryCode} 的本地化设置`)
}

// 保存本地化设置
const saveLocalizationSettings = () => {
  ElMessage.success('本地化设置保存成功')
}

// 重置本地化表单
const resetLocalizationForm = () => {
  Object.assign(localizationForm, {
    country: '',
    language: '',
    paymentMethods: [],
    bankPartner: '',
    requirements: ''
  })
}

// 添加国家
const addCountry = () => {
  if (!countryForm.name || !countryForm.code) {
    ElMessage.error('请填写完整信息')
    return
  }
  
  ElMessage.success(`${countryForm.name} 添加成功`)
  showCountryDialog.value = false
  
  // 重置表单
  Object.assign(countryForm, {
    name: '',
    code: '',
    currency: '',
    timezone: '',
    complianceLevel: 'standard'
  })
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.international-merchant-page {

  .china-highlights {
    margin-bottom: 24px;

    .china-header {
      display: flex;
      align-items: center;
      gap: 12px;

      .china-flag {
        font-size: 24px;
      }

      h3 {
        margin: 0;
        color: var(--el-color-primary);
      }
    }

    .china-features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 16px;
        border: 1px solid var(--el-border-color-light);
        border-radius: 8px;
        transition: all 0.3s;

        &:hover {
          border-color: var(--el-color-primary);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
          font-size: 24px;
          margin-top: 4px;
        }

        .feature-content {
          flex: 1;

          h4 {
            margin: 0 0 8px 0;
            font-size: 16px;
            color: var(--el-text-color-primary);
          }

          p {
            margin: 0;
            font-size: 14px;
            color: var(--el-text-color-regular);
            line-height: 1.4;
          }
        }
      }
    }
  }
  

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    
    h3 {
      margin: 0;
    }
  }
  
  .country-flag {
    font-size: 24px;
  }
  
  .distribution-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
    
    .chart-card {
      .chart-container {
        padding: 20px;
      }
    }
  }
  
  .growth-positive {
    color: var(--el-color-success);
  }
  
  .growth-negative {
    color: var(--el-color-danger);
  }
}

.international-tabs {
  :deep(.el-tabs__content) {
    padding-top: 20px;
  }
}
</style>
