{"version": 3, "sources": ["../browser/src/subscriber/event/SoftRemoveEvent.ts"], "names": [], "mappings": "", "file": "SoftRemoveEvent.js", "sourcesContent": ["import { RemoveEvent } from \"./RemoveEvent\"\n\n/**\n * SoftRemoveEvent is an object that broadcaster sends to the entity subscriber when entity is being soft removed to the database.\n */\nexport interface SoftRemoveEvent<Entity> extends RemoveEvent<Entity> {}\n"], "sourceRoot": "../.."}