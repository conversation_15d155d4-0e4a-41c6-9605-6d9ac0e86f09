"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.escapeRegExp = void 0;
// Escape special characters in regular expressions
// Per https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions#Escaping
const ESCAPE_REGEXP = /[.*+\-?^${}()|[\]\\]/g;
const escapeRegExp = (s) => s.replace(ESCAPE_REGEXP, "\\$&");
exports.escapeRegExp = escapeRegExp;

//# sourceMappingURL=escapeRegExp.js.map
