"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GuardManager = void 0;
const decorator_1 = require("../decorator");
class GuardManager extends Array {
    addGlobalGuard(guards) {
        if (!Array.isArray(guards)) {
            this.push(guards);
        }
        else {
            this.push(...guards);
        }
    }
    async runGuard(ctx, supplierClz, methodName) {
        // check global guard
        for (const Guard of this) {
            const guard = await ctx.requestContext.getAsync(Guard);
            const isPassed = await guard.canActivate(ctx, supplierClz, methodName);
            if (!isPassed) {
                return false;
            }
        }
        // check class Guard
        const classGuardList = (0, decorator_1.getClassMetadata)(decorator_1.GUARD_KEY, supplierClz);
        if (classGuardList) {
            for (const Guard of classGuardList) {
                const guard = await ctx.requestContext.getAsync(Guard);
                const isPassed = await guard.canActivate(ctx, supplierClz, methodName);
                if (!isPassed) {
                    return false;
                }
            }
        }
        // check method Guard
        const methodGuardList = (0, decorator_1.getPropertyMetadata)(decorator_1.GUARD_KEY, supplierClz, methodName);
        if (methodGuardList) {
            for (const Guard of methodGuardList) {
                const guard = await ctx.requestContext.getAsync(Guard);
                const isPassed = await guard.canActivate(ctx, supplierClz, methodName);
                if (!isPassed) {
                    return false;
                }
            }
        }
        return true;
    }
}
exports.GuardManager = GuardManager;
//# sourceMappingURL=guardManager.js.map