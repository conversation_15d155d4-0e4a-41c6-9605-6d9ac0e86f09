{"@midwayjs/faas-typings": "3.6.0", "@midwayjs/fc-starter": "3.8.0", "@midwayjs/serverless-http-parser": "3.6.0", "@midwayjs/async-hooks-context-manager": "3.8.0", "@midwayjs/axios": "3.8.0", "@midwayjs/bootstrap": "3.8.0", "@midwayjs/bull-board": ["3.8.0", "3.8.4"], "@midwayjs/bull": ["3.8.0", "3.8.4"], "@midwayjs/cache": "3.8.0", "@midwayjs/captcha": ["3.8.0", "3.8.4"], "@midwayjs/casbin-redis-adapter": "3.8.0", "@midwayjs/casbin-typeorm-adapter": ["3.8.0", "3.8.3"], "@midwayjs/casbin": "3.8.0", "@midwayjs/code-dye": "3.8.0", "@midwayjs/consul": "3.8.0", "@midwayjs/core": "3.8.0", "@midwayjs/cos": "3.8.0", "@midwayjs/cross-domain": "3.8.0", "@midwayjs/decorator": "3.8.0", "@midwayjs/etcd": ["3.8.0", "3.8.1"], "@midwayjs/express-session": "3.8.0", "@midwayjs/faas": "3.8.0", "@midwayjs/grpc": "3.8.0", "@midwayjs/http-proxy": "3.8.0", "@midwayjs/i18n": "3.8.0", "@midwayjs/info": "3.8.0", "@midwayjs/jwt": "3.8.0", "@midwayjs/kafka": "3.8.0", "@midwayjs/mikro": ["3.8.0", "3.8.3"], "@midwayjs/mock": "3.8.0", "@midwayjs/mongoose": "3.8.0", "@midwayjs/oss": "3.8.0", "@midwayjs/otel": "3.8.0", "@midwayjs/passport": "3.8.0", "@midwayjs/process-agent": "3.8.0", "@midwayjs/prometheus-socket-io": "3.8.0", "@midwayjs/prometheus": "3.8.0", "@midwayjs/rabbitmq": "3.8.0", "@midwayjs/redis": "3.8.0", "@midwayjs/security": "3.8.0", "@midwayjs/sequelize": ["3.8.0", "3.8.2", "3.8.3"], "@midwayjs/session": "3.8.0", "@midwayjs/socketio": "3.8.0", "@midwayjs/static-file": "3.8.0", "@midwayjs/swagger": ["3.8.0", "3.8.2"], "@midwayjs/tablestore": "3.8.0", "@midwayjs/typegoose": "3.8.0", "@midwayjs/typeorm": ["3.8.0", "3.8.3"], "@midwayjs/upload": "3.8.0", "@midwayjs/validate": "3.8.0", "@midwayjs/version": ["3.8.0", "3.8.1", "3.8.2", "3.8.3", "3.8.4"], "@midwayjs/view-ejs": "3.8.0", "@midwayjs/view-nunjucks": "3.8.0", "@midwayjs/view": "3.8.0", "@midwayjs/express": "3.8.0", "@midwayjs/koa": "3.8.0", "@midwayjs/web": "3.8.0", "@midwayjs/ws": "3.8.0"}