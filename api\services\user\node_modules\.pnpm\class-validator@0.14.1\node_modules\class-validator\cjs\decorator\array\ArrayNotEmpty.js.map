{"version": 3, "file": "ArrayNotEmpty.js", "sourceRoot": "", "sources": ["../../../../src/decorator/array/ArrayNotEmpty.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEnD,QAAA,eAAe,GAAG,eAAe,CAAC;AAE/C;;;GAGG;AACH,SAAgB,aAAa,CAAC,KAAc;IAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,CAAC;AAFD,sCAEC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAAC,iBAAqC;IACjE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,uBAAe;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;YACxD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+BAA+B,EAAE,iBAAiB,CAAC;SAC5G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,sCAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const ARRAY_NOT_EMPTY = 'arrayNotEmpty';\n\n/**\n * Checks if given array is not empty.\n * If null or undefined is given then this function returns false.\n */\nexport function arrayNotEmpty(array: unknown): boolean {\n  return Array.isArray(array) && array.length > 0;\n}\n\n/**\n * Checks if given array is not empty.\n * If null or undefined is given then this function returns false.\n */\nexport function ArrayNotEmpty(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: ARRAY_NOT_EMPTY,\n      validator: {\n        validate: (value, args): boolean => arrayNotEmpty(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property should not be empty', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}