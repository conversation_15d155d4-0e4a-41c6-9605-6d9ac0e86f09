import { IMiddleware, IMidwayApplication } from '@midwayjs/core';
import { I18nOptions } from './interface';
export declare class I18nFilter {
    resolverConfig: I18nOptions['resolver'];
    i18nConfig: I18nOptions;
    match(value: any, req: any, res: any): any;
}
export declare class I18nMiddleware implements IMiddleware<any, any> {
    resolverConfig: I18nOptions['resolver'];
    i18nConfig: I18nOptions;
    resolve(app: IMidwayApplication): (req: any, res: any, next: any) => Promise<any>;
    static getName(): string;
}
//# sourceMappingURL=middleware.d.ts.map