import { IMidwayApplication, IMidwayContainer, IMidwayContext, ISimulation } from '../interface';
export declare class MidwayMockService {
    readonly applicationContext: IMidwayContainer;
    /**
     * Save class prototype and object property mocks
     */
    protected mocks: Map<string, Array<{
        obj: any;
        key: string;
        descriptor: PropertyDescriptor;
        hasOwnProperty: boolean;
    }>>;
    /**
     * Save context mocks
     */
    protected contextMocks: Map<string, Array<{
        app: IMidwayApplication;
        key: string | ((ctx: IMidwayContext) => void);
        value: any;
    }>>;
    protected cache: Map<string, Map<any, Set<string>>>;
    protected simulatorList: Array<ISimulation>;
    constructor(applicationContext: IMidwayContainer);
    init(): Promise<void>;
    /**
     * Prepare mocks before the service is initialized
     */
    static prepareMocks: any[];
    static mockClassProperty(clzz: new (...args: any[]) => any, propertyName: string, value: any, group?: string): void;
    static mockProperty(obj: new (...args: any[]) => any, key: string, value: any, group?: string): void;
    mockClassProperty(clzz: new (...args: any[]) => any, propertyName: string, value: any, group?: string): void;
    mockProperty(obj: any, key: string, value: any, group?: string): void;
    mockContext(app: IMidwayApplication, key: string | ((ctx: IMidwayContext) => void), value?: PropertyDescriptor | any, group?: string): void;
    restore(group?: string): void;
    restoreAll(): void;
    private restoreGroup;
    isMocked(obj: any, key: any, group?: string): boolean;
    applyContextMocks(app: IMidwayApplication, ctx: IMidwayContext): void;
    getContextMocksSize(): number;
    private overridePropertyDescriptor;
    initSimulation(group?: string): Promise<void>;
    runSimulatorSetup(): Promise<void>;
    runSimulatorTearDown(): Promise<void>;
    runSimulatorAppSetup(app: IMidwayApplication): Promise<void>;
    runSimulatorAppTearDown(app: IMidwayApplication): Promise<void>;
    runSimulatorContextSetup(ctx: IMidwayContext, app: IMidwayApplication): Promise<void>;
    runSimulatorContextTearDown(ctx: IMidwayContext, app: IMidwayApplication): Promise<void>;
}
//# sourceMappingURL=mockService.d.ts.map