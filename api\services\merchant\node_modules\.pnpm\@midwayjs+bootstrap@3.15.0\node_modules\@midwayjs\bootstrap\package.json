{"name": "@midwayjs/bootstrap", "version": "3.15.0", "description": "<PERSON><PERSON>s bootstrap", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "link": "npm link"}, "keywords": ["midway", "IoC", "bootstrap"], "files": ["dist/**/*.js", "dist/**/*.d.ts"], "license": "MIT", "dependencies": {"@midwayjs/async-hooks-context-manager": "^3.15.0", "@midwayjs/event-bus": "1.9.4"}, "devDependencies": {"@midwayjs/core": "^3.15.0", "@midwayjs/logger": "^3.0.0", "request": "2.88.2", "socket.io-client": "4.7.4"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "engines": {"node": ">=12.11.0"}, "gitHead": "be0a091f940aa60965d9fabfbdcbf0fe2830e9c4"}