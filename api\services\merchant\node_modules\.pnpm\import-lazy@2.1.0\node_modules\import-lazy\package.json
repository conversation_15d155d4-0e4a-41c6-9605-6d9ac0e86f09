{"name": "import-lazy", "version": "2.1.0", "description": "Import modules lazily", "license": "MIT", "repository": "sindresorhus/import-lazy", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["import", "require", "load", "module", "modules", "lazy", "lazily", "defer", "deferred", "proxy", "proxies"], "devDependencies": {"ava": "*", "xo": "*"}}