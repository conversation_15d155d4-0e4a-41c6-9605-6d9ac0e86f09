import type { IObjectCreator, IObjectDefinition } from '../interface';
export declare class ObjectCreator implements IObjectCreator {
    protected definition: IObjectDefinition;
    constructor(definition: IObjectDefinition);
    /**
     * 加载对象class
     * @returns {class} Clzz对象的Class
     */
    load(): any;
    /**
     * 构建对象实例
     * @param Clzz 对象class，通过load加载
     * @param args 对象初始化参数
     * @returns {any} 返回创建的对象实例
     */
    doConstruct(Clzz: any, args?: any): any;
    /**
     * 异步构造对象
     * @param Clzz 对象class，通过load加载
     * @param args 对象初始化参数
     * @returns {any} 返回创建的对象实例
     */
    doConstructAsync(Clzz: any, args?: any): Promise<any>;
    /**
     * 调用对象初始化方法进行初始化
     * @param obj 对象，由doConstruct返回
     * @returns {void}
     */
    doInit(obj: any): void;
    /**
     * 调用对象初始化方法进行初始化
     * @param obj 对象，由doConstructAsync返回
     * @returns {void}
     */
    doInitAsync(obj: any): Promise<void>;
    /**
     * 对象销毁
     * @param obj 对象，由doConstruct返回
     * @returns {void}
     */
    doDestroy(obj: any): void;
    /**
     * 对象销毁
     * @param obj 对象，由doConstructAsync返回
     * @returns {void}
     */
    doDestroyAsync(obj: any): Promise<void>;
}
//# sourceMappingURL=objectCreator.d.ts.map