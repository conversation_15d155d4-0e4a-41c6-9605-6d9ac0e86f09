import { Cool<PERSON><PERSON>roller, BaseController } from '@cool-midway/core';
import { FinanceWalletUserEntity } from '../../../entity/wallet/user';
import { Get, Inject } from '@midwayjs/core';
import { FinanceWalletUserService } from '../../../service/wallet/user';
import { FinanceWalletRecordService } from '../../../service/wallet/record';

/**
 * 用户钱包
 */
@CoolController({
  api: [],
  entity: FinanceWalletUserEntity,
  service: FinanceWalletRecordService,
})
export class AppFinanceWalletUserController extends BaseController {
  @Inject()
  financeWalletUserService: FinanceWalletUserService;

  @Inject()
  ctx;

  @Get('/detail', { summary: '钱包详情' })
  async detail() {
    const userId = this.ctx.user.id;
    const info = await this.financeWalletUserService.detail(userId);
    return this.ok(info);
  }
}
