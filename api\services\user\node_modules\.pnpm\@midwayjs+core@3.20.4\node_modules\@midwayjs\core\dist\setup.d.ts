import { MidwayContainer, IMidwayBootstrapOptions, IMidwayContainer } from './';
/**
 * midway framework main entry, this method bootstrap all service and framework.
 * @param globalOptions
 */
export declare function initializeGlobalApplicationContext(globalOptions: IMidwayBootstrapOptions): Promise<IMidwayContainer>;
export declare function destroyGlobalApplicationContext(applicationContext: IMidwayContainer): Promise<void>;
/**
 * prepare applicationContext
 * @param globalOptions
 */
export declare function prepareGlobalApplicationContextAsync(globalOptions: IMidwayBootstrapOptions): Promise<IMidwayContainer | MidwayContainer>;
/**
 * prepare applicationContext, it use in egg framework, hooks and serverless function generator
 * @param globalOptions
 */
export declare function prepareGlobalApplicationContext(globalOptions: IMidwayBootstrapOptions): IMidwayContainer | MidwayContainer;
//# sourceMappingURL=setup.d.ts.map