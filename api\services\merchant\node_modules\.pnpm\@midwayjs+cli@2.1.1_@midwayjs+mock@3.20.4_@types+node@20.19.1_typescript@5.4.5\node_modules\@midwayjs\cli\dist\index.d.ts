import { CoreBase<PERSON><PERSON> } from '@midwayjs/command-core';
export * from './utils';
export { findNpm } from '@midwayjs/command-core';
export declare class CLI extends CoreBaseCLI {
    loadDefaultPlugin(): Promise<void>;
    loadExtensions(): {
        debug: any;
        enquirer: any;
    };
    error(err: any): void;
    loadPlugins(): Promise<void>;
    loadDefaultOptions(): Promise<void>;
    debug(...args: any[]): void;
    displayVersion(): void;
    displayUsage(commandsArray: any, usage: any, coreInstance: any, commandInfo?: any): void;
}
//# sourceMappingURL=index.d.ts.map