{"name": "@vercel/ncc", "description": "Simple CLI for compiling a Node.js module into a single file, together with all its dependencies, gcc-style.", "version": "0.30.0", "repository": "vercel/ncc", "license": "MIT", "main": "./dist/ncc/index.js", "bin": {"ncc": "./dist/ncc/cli.js"}, "files": ["dist"], "scripts": {"build": "node scripts/build.js", "build-test-binary": "cd test/binary && node-gyp rebuild && cp build/Release/hello.node ../integration/hello.node", "codecov": "codecov", "test": "node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest", "test-coverage": "node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest --coverage --globals \"{\\\"coverage\\\":true}\" && codecov", "prepublishOnly": "node scripts/build.js --no-cache"}, "devDependencies": {"@azure/cosmos": "^3.12.3", "@bugsnag/js": "^7.11.0", "@ffmpeg-installer/ffmpeg": "^1.0.17", "@google-cloud/bigquery": "^5.7.0", "@google-cloud/firestore": "^4.14.0", "@sentry/node": "^6.10.0", "@slack/web-api": "^6.3.0", "@tensorflow/tfjs-node": "^3.8.0", "@vercel/webpack-asset-relocator-loader": "1.6.0", "analytics-node": "^5.0.0", "apollo-server-express": "^2.2.2", "arg": "^5.0.0", "auth0": "^2.14.0", "aws-sdk": "^2.356.0", "axios": "^0.21.1", "azure-storage": "^2.10.2", "browserify-middleware": "^8.1.1", "bytes": "^3.0.0", "canvas": "^2.2.0", "chromeless": "^1.5.2", "codecov": "^3.8.3", "consolidate": "^0.16.0", "copy": "^0.3.2", "core-js": "^2.5.7", "cowsay": "^1.3.1", "esm": "^3.2.22", "express": "^4.16.4", "fetch-h2": "^3.0.0", "firebase": "^6.1.1", "firebase-admin": "^9.11.0", "fluent-ffmpeg": "^2.1.2", "fontkit": "^1.7.7", "get-folder-size": "^2.0.0", "glob": "^7.1.3", "got": "^11.8.2", "graceful-fs": "^4.1.15", "graphql": "^15.5.1", "highlights": "^3.1.1", "hot-shots": "^8.5.0", "ioredis": "^4.2.0", "isomorphic-unfetch": "^3.0.0", "jest": "^27.0.6", "jimp": "^0.16.1", "jugglingdb": "2.0.1", "koa": "^2.6.2", "leveldown": "^6.0.0", "license-webpack-plugin": "2.3.20", "lighthouse": "^8.1.0", "loopback": "^3.24.0", "mailgun": "^0.5.0", "mariadb": "^2.0.1-beta", "memcached": "^2.2.2", "mkdirp": "^1.0.4", "mongoose": "^5.3.12", "mysql": "^2.16.0", "node-gyp": "^8.1.0", "npm": "^6.13.4", "oracledb": "^4.2.0", "passport": "^0.4.0", "passport-google-oauth": "^2.0.0", "path-platform": "^0.11.15", "pdf2json": "^1.1.8", "pdfkit": "^0.12.1", "pg": "^8.7.1", "pug": "^3.0.1", "react": "^17.0.2", "react-dom": "^17.0.2", "redis": "^3.1.1", "request": "^2.88.0", "rxjs": "^7.3.0", "saslprep": "^1.0.2", "sequelize": "^6.6.5", "sharp": "^0.28.3", "shebang-loader": "^0.0.1", "socket.io": "^4.1.3", "source-map-support": "^0.5.9", "stripe": "^8.167.0", "swig": "^1.4.2", "terser": "^5.6.1", "the-answer": "^1.0.0", "tiny-json-http": "^7.0.2", "ts-loader": "^8.3.0", "tsconfig-paths": "^3.7.0", "tsconfig-paths-webpack-plugin": "^3.2.0", "twilio": "^3.23.2", "typescript": "^4.3.5", "vm2": "^3.6.6", "vue": "^2.5.17", "vue-server-renderer": "^2.5.17", "web-vitals": "^0.2.4", "webpack": "5.44.0", "when": "^3.7.8"}, "resolutions": {"grpc": "1.24.6"}}