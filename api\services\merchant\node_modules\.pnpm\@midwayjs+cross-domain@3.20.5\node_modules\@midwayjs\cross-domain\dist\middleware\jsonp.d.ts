import { IMiddleware } from '@midwayjs/core';
import { JSONPOptions } from '../interface';
export declare class J<PERSON>NPFilter {
    match(value: any, req: any): Promise<any>;
}
export declare class JSONPMiddleware implements IMiddleware<any, any> {
    jsonp: JSONPOptions;
    resolve(app: any): (req: any, res: any, next: any) => Promise<any>;
    compatibleMiddleware(context: any, next: any): Promise<any>;
    static getName(): string;
}
//# sourceMappingURL=jsonp.d.ts.map