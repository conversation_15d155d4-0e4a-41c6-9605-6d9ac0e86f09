import { IMidwayApplication, IMidwayContainer } from '../interface';
import { InjectionConfigurationOptions } from '../decorator';
export declare class FunctionalConfiguration {
    private readyHandler;
    private stopHandler;
    private configLoadHandler;
    private serverReadyHandler;
    private options;
    constructor(options: InjectionConfigurationOptions);
    onConfigLoad(configLoadHandler: ((container: IMidwayContainer, app: IMidwayApplication) => any) | IMidwayContainer, app?: IMidwayApplication): any;
    onReady(readyHandler: ((container: IMidwayContainer, app: IMidwayApplication) => void) | IMidwayContainer, app?: IMidwayApplication): any;
    onServerReady(serverReadyHandler: ((container: IMidwayContainer, app: IMidwayApplication) => void) | IMidwayContainer, app?: IMidwayApplication): any;
    onStop(stopHandler: ((container: IMidwayContainer, app: IMidwayApplication) => void) | IMidwayContainer, app?: IMidwayApplication): any;
    getConfigurationOptions(): InjectionConfigurationOptions;
}
export declare const createConfiguration: (options: InjectionConfigurationOptions) => FunctionalConfiguration;
//# sourceMappingURL=configuration.d.ts.map