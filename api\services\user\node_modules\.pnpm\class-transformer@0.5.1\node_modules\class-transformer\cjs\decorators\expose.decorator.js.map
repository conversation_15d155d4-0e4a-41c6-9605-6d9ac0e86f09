{"version": 3, "file": "expose.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/expose.decorator.ts"], "names": [], "mappings": ";;;AAAA,wCAAoD;AAGpD;;;;;;GAMG;AACH,SAAgB,MAAM,CAAC,UAAyB,EAAE;IAChD;;;;;OAKG;IACH,OAAO,UAAU,MAAW,EAAE,YAA8B;QAC1D,gCAAsB,CAAC,iBAAiB,CAAC;YACvC,MAAM,EAAE,MAAM,YAAY,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,WAAW;YAChE,YAAY,EAAE,YAAsB;YACpC,OAAO;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAdD,wBAcC", "sourcesContent": ["import { defaultMetadataStorage } from '../storage';\nimport { ExposeOptions } from '../interfaces';\n\n/**\n * Marks the given class or property as included. By default the property is included in both\n * constructorToPlain and plainToConstructor transformations. It can be limited to only one direction\n * via using the `toPlainOnly` or `toClassOnly` option.\n *\n * Can be applied to class definitions and properties.\n */\nexport function Expose(options: ExposeOptions = {}): PropertyDecorator & ClassDecorator {\n  /**\n   * NOTE: The `propertyName` property must be marked as optional because\n   * this decorator used both as a class and a property decorator and the\n   * Typescript compiler will freak out if we make it mandatory as a class\n   * decorator only receives one parameter.\n   */\n  return function (object: any, propertyName?: string | Symbol): void {\n    defaultMetadataStorage.addExposeMetadata({\n      target: object instanceof Function ? object : object.constructor,\n      propertyName: propertyName as string,\n      options,\n    });\n  };\n}\n"]}