<!DOCTYPE HTML>
<html>
<head>
	<meta charset="utf-8" />
	<title>Jasmine Spec Runner: has-symbol-support-x</title>

	<link rel="icon" href="http://jasmine.github.io/images/jasmine.ico" sizes="16x16">
	<link rel="icon" href="http://jasmine.github.io//images/jasmine_32x32.ico" sizes="32x32">

	<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/jasmine/1.3.1/jasmine.min.css">
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/es5-shim/4.5.10/es5-shim.min.js"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/es5-shim/4.5.10/es5-sham.min.js"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/json3/3.3.2/json3.min.js"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/es6-shim/0.35.3/es6-shim.js"></script>
	<script type="text/javascript" src="https://wzrd.in/standalone/es7-shim@latest"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jasmine/1.3.1/jasmine.min.js"></script>
	<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jasmine/1.3.1/jasmine-html.min.js"></script>

	<!-- include helper files here... -->

	<!-- include source files here... -->
	<script src="../lib/has-symbol-support-x.min.js"></script>

	<!-- include spec files here... -->
	<script src="spec/test.js"></script>

	<!-- run the tests -->
	<script src="./run.js"></script>

</head>

<body>
</body>
</html>
