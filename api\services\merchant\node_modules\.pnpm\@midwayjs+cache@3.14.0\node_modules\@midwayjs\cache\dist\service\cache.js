"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheManager = void 0;
const core_1 = require("@midwayjs/core");
const cacheManager = require("cache-manager");
let CacheManager = class CacheManager {
    async init() {
        this.cache = cacheManager.caching({
            store: this.cacheConfig.store,
            ...this.cacheConfig.options,
        });
    }
    // 获取key
    async get(key) {
        return new Promise((resolve, reject) => {
            this.cache.get(key, (err, result) => {
                if (err) {
                    reject(err);
                    return;
                }
                resolve(result);
            });
        });
    }
    // 设置cache
    async set(key, value, options) {
        return await this.cache.set(key, value, options);
    }
    // 删除key
    async del(key) {
        return await this.cache.del(key);
    }
    // 清空cache
    async reset() {
        return await this.cache.reset();
    }
};
__decorate([
    (0, core_1.Config)('cache'),
    __metadata("design:type", Object)
], CacheManager.prototype, "cacheConfig", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CacheManager.prototype, "init", null);
CacheManager = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CacheManager);
exports.CacheManager = CacheManager;
//# sourceMappingURL=cache.js.map