@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules\detect-port\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules\detect-port\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules\detect-port\bin\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules\detect-port\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\detect-port@1.6.1\node_modules;D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\detect-port.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\detect-port.js" %*
)
