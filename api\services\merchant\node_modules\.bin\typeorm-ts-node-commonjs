#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6.1_mysql2@3.14.1_reflect-metadata@0.2.2_ts-node@10.9.2_@types+node@20.19.1_typescript@5.4.5_/node_modules/typeorm/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6.1_mysql2@3.14.1_reflect-metadata@0.2.2_ts-node@10.9.2_@types+node@20.19.1_typescript@5.4.5_/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6.1_mysql2@3.14.1_reflect-metadata@0.2.2_ts-node@10.9.2_@types+node@20.19.1_typescript@5.4.5_/node_modules/typeorm/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/typeorm@0.3.25_ioredis@5.6.1_mysql2@3.14.1_reflect-metadata@0.2.2_ts-node@10.9.2_@types+node@20.19.1_typescript@5.4.5_/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../typeorm/cli-ts-node-commonjs.js" "$@"
else
  exec node  "$basedir/../typeorm/cli-ts-node-commonjs.js" "$@"
fi
