{"version": 3, "sources": ["../../src/metadata-args/ExclusionMetadataArgs.ts"], "names": [], "mappings": "", "file": "ExclusionMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for ExclusionMetadata class.\n */\nexport interface ExclusionMetadataArgs {\n    /**\n     * Class to which index is applied.\n     */\n    target: Function | string\n\n    /**\n     * Exclusion constraint name.\n     */\n    name?: string\n\n    /**\n     * Exclusion expression.\n     */\n    expression: string\n}\n"], "sourceRoot": ".."}