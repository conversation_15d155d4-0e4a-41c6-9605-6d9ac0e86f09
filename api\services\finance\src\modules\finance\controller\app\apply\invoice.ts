import {
  Cool<PERSON><PERSON><PERSON>er,
  BaseController,
  CoolCommException,
} from '@cool-midway/core';
import { FinanceApplyInvoiceEntity } from '../../../entity/apply/invoice';
import { FinanceApplyInvoiceService } from '../../../service/apply/invoice';
import { Body, Inject, Post } from '@midwayjs/core';
import * as _ from 'lodash';

/**
 * 发票申请
 */
@CoolController({
  api: ['page'],
  entity: FinanceApplyInvoiceEntity,
  service: FinanceApplyInvoiceService,
})
export class AppFinanceApplyInvoiceController extends BaseController {
  @Inject()
  financeApplyInvoiceService: FinanceApplyInvoiceService;

  @Inject()
  ctx;

  @Post('/submit', { summary: '提交申请' })
  async submit(
    @Body('amount') amount: number,
    @Body('invoiceId') invoiceId: number,
    @Body('orderIds') orderIds: number[]
  ) {
    if (_.isEmpty(orderIds)) throw new CoolCommException('订单ID不能为空');
    await this.financeApplyInvoiceService.submit(
      this.ctx.user.id,
      amount,
      invoiceId,
      orderIds
    );
    return this.ok();
  }
}
