#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/dist/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/dist/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/dist/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/dist/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/core/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules/@cool-midway/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/@cool-midway+core@8.0.5_debug@4.4.1/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@cool-midway/core/dist/bin/index.js" "$@"
else
  exec node  "$basedir/../@cool-midway/core/dist/bin/index.js" "$@"
fi
