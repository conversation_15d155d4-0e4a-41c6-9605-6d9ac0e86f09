<template>
  <div class="parent-menu-selector">
    <ElFormItem label="父级目录" prop="parentId">
      <ElTreeSelect
        v-model="parentMenuId"
        :data="menuTreeData"
        :props="treeProps"
        placeholder="选择父级目录（不选择则为一级菜单）"
        clearable
        filterable
        check-strictly
        :render-after-expand="false"
        class="parent-menu-tree-select"
        @change="handleParentChange"
      />
    </ElFormItem>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElFormItem, ElTreeSelect } from 'element-plus'
import type { AppRouteRecord } from '@/types/router'

interface Props {
  modelValue?: string | number
  menuList: AppRouteRecord[]
  currentMenuId?: string | number // 当前编辑的菜单ID，用于过滤自己和子菜单
}

interface Emits {
  (e: 'update:modelValue', value: string | number | undefined): void
  (e: 'change', value: string | number | undefined): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const parentMenuId = ref<string | number | undefined>(props.modelValue)

// 树形选择器配置
const treeProps = {
  value: 'id',
  label: 'title',
  children: 'children'
}

// 转换菜单数据为树形结构
const convertToTreeData = (menuList: AppRouteRecord[], currentId?: string | number): any[] => {
  const convertMenu = (menu: AppRouteRecord): any => {
    const treeNode = {
      id: menu.id || menu.name || menu.path,
      title: menu.meta?.title || menu.name || menu.path,
      path: menu.path,
      disabled: false,
      children: []
    }

    // 如果是当前编辑的菜单，禁用选择（防止选择自己作为父级）
    if (currentId && (menu.id === currentId || menu.name === currentId)) {
      treeNode.disabled = true
    }

    // 递归处理子菜单
    if (menu.children && menu.children.length > 0) {
      treeNode.children = menu.children
        .filter(child => {
          // 过滤掉当前菜单的所有子菜单（防止循环引用）
          return !isChildOfCurrent(child, currentId)
        })
        .map(child => convertMenu(child))
    }

    return treeNode
  }

  return menuList.map(menu => convertMenu(menu))
}

// 检查是否是当前菜单的子菜单
const isChildOfCurrent = (menu: AppRouteRecord, currentId?: string | number): boolean => {
  if (!currentId) return false
  
  const menuId = menu.id || menu.name || menu.path
  if (menuId === currentId) return true
  
  if (menu.children && menu.children.length > 0) {
    return menu.children.some(child => isChildOfCurrent(child, currentId))
  }
  
  return false
}

// 计算树形数据
const menuTreeData = computed(() => {
  return convertToTreeData(props.menuList, props.currentMenuId)
})

// 处理父级菜单变化
const handleParentChange = (value: string | number | undefined) => {
  emit('update:modelValue', value)
  emit('change', value)
}

// 监听外部值变化
watch(() => props.modelValue, (newVal) => {
  parentMenuId.value = newVal
})

// 监听当前菜单ID变化（编辑时）
watch(() => props.currentMenuId, () => {
  // 如果当前选择的父级菜单是自己或自己的子菜单，清空选择
  if (props.currentMenuId && parentMenuId.value) {
    const isInvalid = isChildOfCurrent(
      props.menuList.find(m => (m.id || m.name || m.path) === parentMenuId.value) || {} as AppRouteRecord,
      props.currentMenuId
    ) || parentMenuId.value === props.currentMenuId
    
    if (isInvalid) {
      parentMenuId.value = undefined
      handleParentChange(undefined)
    }
  }
})
</script>

<style scoped>
.parent-menu-tree-select {
  width: 100%;
}

:deep(.el-tree-select__popper) {
  max-height: 300px;
}

:deep(.el-tree-node__content) {
  height: 32px;
  line-height: 32px;
}

:deep(.el-tree-node.is-disabled > .el-tree-node__content) {
  color: #c0c4cc;
  cursor: not-allowed;
}

:deep(.el-tree-node.is-disabled > .el-tree-node__content:hover) {
  background-color: transparent;
}
</style>
