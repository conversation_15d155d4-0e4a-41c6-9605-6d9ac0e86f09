"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseCheck = void 0;
const child_process_1 = require("child_process");
const os = require("os");
const baseCheck = (runner) => {
    runner
        .group('Base check')
        .info('Hostname', () => {
        return os.hostname;
    })
        .info('VM CPU', () => {
        return os.cpus().length;
    })
        .info('USER HOME', () => {
        return os.homedir();
    })
        .info('Node.js Version', () => {
        try {
            return child_process_1.execSync(`node -v`).toString().trim();
        }
        catch (err) {
            return '?';
        }
    })
        .info('NPM Version', () => {
        try {
            return child_process_1.execSync(`npm -v`).toString().trim();
        }
        catch (err) {
            return '?';
        }
    });
};
exports.baseCheck = baseCheck;
exports.rules = [
    exports.baseCheck,
];
//# sourceMappingURL=base.js.map