import { EntityTarget } from 'typeorm';
export declare const ENTITY_MODEL_KEY = "typeorm:entity_model_key";
export declare const EVENT_SUBSCRIBER_KEY = "typeorm:event_subscriber_key";
export declare const ORM_MODEL_KEY = "typeorm:orm_model_key";
export declare const ORM_DATA_SOURCE_KEY = "typeorm:data_source_key";
export declare function InjectEntityModel(modelKey: EntityTarget<unknown>, connectionName?: string): PropertyDecorator;
/**
 * EventSubscriber - typeorm
 * implements EntitySubscriberInterface
 */
export declare function EventSubscriberModel(): ClassDecorator;
export declare function InjectDataSource(dataSourceName?: string): PropertyDecorator;
//# sourceMappingURL=decorator.d.ts.map