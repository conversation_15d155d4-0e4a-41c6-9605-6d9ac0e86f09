{"@midwayjs/faas-typings": "3.3.5", "@midwayjs/fc-starter": "3.4.11", "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.11", "@midwayjs/axios": "3.4.11", "@midwayjs/bootstrap": "3.4.11", "@midwayjs/cache": "3.4.11", "@midwayjs/code-dye": "3.4.11", "@midwayjs/consul": "3.4.11", "@midwayjs/core": "3.4.11", "@midwayjs/cos": "3.4.11", "@midwayjs/cross-domain": "3.4.11", "@midwayjs/decorator": "3.4.11", "@midwayjs/express-session": "3.4.11", "@midwayjs/faas": "3.4.11", "@midwayjs/grpc": "3.4.11", "@midwayjs/http-proxy": "3.4.11", "@midwayjs/i18n": "3.4.11", "@midwayjs/info": "3.4.11", "@midwayjs/jwt": "3.4.11", "@midwayjs/kafka": "3.4.11", "@midwayjs/mikro": "3.4.11", "@midwayjs/mock": "3.4.11", "@midwayjs/mongoose": "3.4.11", "@midwayjs/oss": "3.4.11", "@midwayjs/otel": "3.4.11", "@midwayjs/passport": "3.4.11", "@midwayjs/process-agent": "3.4.11", "@midwayjs/prometheus-socket-io": "3.4.11", "@midwayjs/prometheus": "3.4.11", "@midwayjs/rabbitmq": "3.4.11", "@midwayjs/redis": "3.4.11", "@midwayjs/security": "3.4.11", "@midwayjs/sequelize": "3.4.11", "@midwayjs/session": "3.4.11", "@midwayjs/socketio": "3.4.11", "@midwayjs/static-file": "3.4.11", "@midwayjs/swagger": "3.4.11", "@midwayjs/tablestore": "3.4.11", "@midwayjs/task": "3.4.11", "@midwayjs/typegoose": "3.4.11", "@midwayjs/typeorm": "3.4.11", "@midwayjs/upload": "3.4.11", "@midwayjs/validate": "3.4.11", "@midwayjs/version": "3.4.11", "@midwayjs/view-ejs": "3.4.11", "@midwayjs/view-nunjucks": "3.4.11", "@midwayjs/view": "3.4.11", "@midwayjs/express": "3.4.11", "@midwayjs/koa": "3.4.11", "@midwayjs/web": "3.4.11", "@midwayjs/ws": "3.4.11"}