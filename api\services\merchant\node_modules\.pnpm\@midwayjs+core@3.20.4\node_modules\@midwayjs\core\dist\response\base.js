"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerResponse = void 0;
class ServerResponse {
    constructor(ctx) {
        this.isSuccess = true;
        this.ctx = ctx;
    }
    json(data) {
        return Object.getPrototypeOf(this).constructor.JSON_TPL(data, this.isSuccess, this.ctx);
    }
    text(data) {
        return Object.getPrototypeOf(this).constructor.TEXT_TPL(data, this.isSuccess, this.ctx);
    }
    blob(data) {
        return Object.getPrototypeOf(this).constructor.BLOB_TPL(data, this.isSuccess, this.ctx);
    }
    success() {
        this.isSuccess = true;
        return this;
    }
    fail() {
        this.isSuccess = false;
        return this;
    }
}
exports.ServerResponse = ServerResponse;
ServerResponse.TEXT_TPL = (data, isSuccess, ctx) => {
    return data;
};
ServerResponse.JSON_TPL = (data, isSuccess, ctx) => {
    if (isSuccess) {
        return {
            success: 'true',
            data,
        };
    }
    else {
        return {
            success: 'false',
            message: data || 'fail',
        };
    }
};
ServerResponse.BLOB_TPL = (data, isSuccess, ctx) => {
    return data;
};
//# sourceMappingURL=base.js.map