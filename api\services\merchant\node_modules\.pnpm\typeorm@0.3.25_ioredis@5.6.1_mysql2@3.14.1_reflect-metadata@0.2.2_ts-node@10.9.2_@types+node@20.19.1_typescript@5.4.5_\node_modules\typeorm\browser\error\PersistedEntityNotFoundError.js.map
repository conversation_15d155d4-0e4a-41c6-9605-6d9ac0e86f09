{"version": 3, "sources": ["../browser/src/error/PersistedEntityNotFoundError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,4BAA6B,SAAQ,YAAY;IAC1D;QACI,KAAK,CACD,2FAA2F,CAC9F,CAAA;IACL,CAAC;CACJ", "file": "PersistedEntityNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown . Theoretically can't be thrown.\n */\nexport class PersistedEntityNotFoundError extends TypeORMError {\n    constructor() {\n        super(\n            `Internal error. Persisted entity was not found in the list of prepared operated entities.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}