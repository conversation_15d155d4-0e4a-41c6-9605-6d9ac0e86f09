export default {
  // 数据库配置
  typeorm: {
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'password',
    database: 'payment_service_db',
    synchronize: false,
    logging: false,
    entities: ['src/modules/payment/entity/*.ts'],
  },
  // Redis 配置
  redis: {
    host: 'localhost',
    port: 6379,
    db: 0,
  },
  // RPC 配置
  rpc: {
    namespace: 'payment',
    port: 9805,
  },
}; 