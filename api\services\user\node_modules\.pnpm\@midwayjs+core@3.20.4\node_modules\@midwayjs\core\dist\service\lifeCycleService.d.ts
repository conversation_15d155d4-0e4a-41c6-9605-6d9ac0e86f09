import { IMidwayContainer } from '../interface';
import { MidwayFrameworkService } from './frameworkService';
import { MidwayConfigService } from './configService';
import { MidwayMockService } from './mockService';
import { MidwayHealthService } from './healthService';
export declare class MidwayLifeCycleService {
    readonly applicationContext: IMidwayContainer;
    protected frameworkService: MidwayFrameworkService;
    protected configService: MidwayConfigService;
    protected mockService: MidwayMockService;
    protected healthService: MidwayHealthService;
    private lifecycleInstanceList;
    constructor(applicationContext: IMidwayContainer);
    protected init(): Promise<void>;
    stop(): Promise<void>;
    /**
     * run some lifecycle in configuration
     * @param lifecycleInstanceOrList
     * @param lifecycle
     * @param resultHandler
     */
    private runContainerLifeCycle;
    /**
     * run object lifecycle
     * @param lifecycleInstanceList
     * @param lifecycle
     */
    private runObjectLifeCycle;
    getLifecycleInstanceList(): {
        target: any;
        namespace: string;
        instance?: any;
    }[];
}
//# sourceMappingURL=lifeCycleService.d.ts.map