{"name": "decompress", "version": "4.2.1", "description": "Extracting archives made easy", "license": "MIT", "repository": "kevva/decompress", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["bz2", "bzip2", "decompress", "extract", "tar", "tar.bz", "tar.gz", "zip", "unzip"], "dependencies": {"decompress-tar": "^4.0.0", "decompress-tarbz2": "^4.0.0", "decompress-targz": "^4.0.0", "decompress-unzip": "^4.0.1", "graceful-fs": "^4.1.10", "make-dir": "^1.0.0", "pify": "^2.3.0", "strip-dirs": "^2.0.0"}, "devDependencies": {"ava": "*", "esm": "^3.2.25", "is-jpg": "^1.0.0", "path-exists": "^3.0.0", "pify": "^2.3.0", "rimraf": "^3.0.2", "xo": "*"}, "ava": {"require": ["esm"]}, "xo": {"rules": {"promise/prefer-await-to-then": "off"}}}