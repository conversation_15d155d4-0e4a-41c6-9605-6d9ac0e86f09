#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules/moleculer/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/moleculer@0.14.35_debug@4.4.1_ioredis@5.6.1/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../moleculer/bin/moleculer-runner.mjs" "$@"
else
  exec node  "$basedir/../moleculer/bin/moleculer-runner.mjs" "$@"
fi
