import { IMidwayFramework } from '@midwayjs/core';
import { ComponentModule } from './interface';
export declare function isTestEnvironment(): boolean;
export declare function isWin32(): boolean;
export declare function findFirstExistModule(moduleList: any): ComponentModule;
/**
 * transform a framework component or framework module to configuration class
 * @param Framework
 */
export declare function transformFrameworkToConfiguration<T extends IMidwayFramework<any, any, any>>(Framework: any, loadMode: 'commonjs' | 'esm'): Promise<{
    [key: string]: any;
    Configuration: any;
}>;
export declare function removeFile(file: string): Promise<void>;
export declare function mergeGlobalConfig(globalConfig: any, newConfigObject: Record<string, any>): any;
/**
 * 解析命令行参数的函数。
 * 它接受一个字符串数组作为输入，然后解析这个数组，
 * 将形如 `--key value` 或 `--key=value` 的参数转换为对象的键值对，
 * 形如 `--key` 的参数转换为 `{ key: true }`。
 * @param argv 命令行参数数组
 * @returns 解析后的参数对象
 */
export declare function processArgsParser(argv: string[]): {};
//# sourceMappingURL=utils.d.ts.map