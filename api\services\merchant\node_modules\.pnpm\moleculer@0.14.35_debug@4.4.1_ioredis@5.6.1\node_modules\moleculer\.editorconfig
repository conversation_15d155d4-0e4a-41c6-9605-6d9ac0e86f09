# EditorConfig helps developers define and maintain consistent
# coding styles between different editors and IDEs
# editorconfig.org

root = true

[*]

# Change these settings to your own preference
indent_style = tab
indent_size = 4
space_after_anon_function = true

# We recommend you to keep these unchanged
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true

[*.md]
trim_trailing_whitespace = false
indent_style = space
indent_size = 4

[{package,bower}.json]
indent_style = space
indent_size = 2

[*.yml]
indent_style = space
indent_size = 2

[*.js]
quote_type = "double"
