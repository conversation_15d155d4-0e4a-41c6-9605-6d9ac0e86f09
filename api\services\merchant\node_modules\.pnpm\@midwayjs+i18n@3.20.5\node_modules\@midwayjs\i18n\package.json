{"name": "@midwayjs/i18n", "description": "midway i18n component", "version": "3.20.5", "main": "dist/index.js", "typings": "index.d.ts", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "dependencies": {"picomatch": "2.3.1"}, "devDependencies": {"@midwayjs/core": "^3.20.4", "@midwayjs/express": "^3.20.4", "@midwayjs/koa": "^3.20.5", "@midwayjs/mock": "^3.20.4"}, "keywords": ["midway", "i18n"], "author": "czy88840616 <<EMAIL>>", "license": "MIT", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test", "lint": "mwts check"}, "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "gitHead": "7ce57281bd3ef5d18dc50b47ff9bffb8a27c071e"}