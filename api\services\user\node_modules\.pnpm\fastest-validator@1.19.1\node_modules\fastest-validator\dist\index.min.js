'use strict';var g=g||{};g.scope={};g.arrayIteratorImpl=function(e){var k=0;return function(){return k<e.length?{done:!1,value:e[k++]}:{done:!0}}};g.arrayIterator=function(e){return{next:g.arrayIteratorImpl(e)}};g.ASSUME_ES5=!1;g.ASSUME_NO_NATIVE_MAP=!1;g.ASSUME_NO_NATIVE_SET=!1;g.SIMPLE_FROUND_POLYFILL=!1;g.ISOLATE_POLYFILLS=!1;g.FORCE_POLYFILL_PROMISE=!1;g.FORCE_POLYFILL_PROMISE_WHEN_NO_UNHANDLED_REJECTION=!1;
g.defineProperty=g.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(e,k,m){if(e==Array.prototype||e==Object.prototype)return e;e[k]=m.value;return e};g.getGlobal=function(e){e=["object"==typeof globalThis&&globalThis,e,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var k=0;k<e.length;++k){var m=e[k];if(m&&m.Math==Math)return m}throw Error("Cannot find global object");};g.global=g.getGlobal(this);
g.IS_SYMBOL_NATIVE="function"===typeof Symbol&&"symbol"===typeof Symbol("x");g.TRUST_ES6_POLYFILLS=!g.ISOLATE_POLYFILLS||g.IS_SYMBOL_NATIVE;g.polyfills={};g.propertyToPolyfillSymbol={};g.POLYFILL_PREFIX="$jscp$";g.polyfill=function(e,k,m,n){k&&(g.ISOLATE_POLYFILLS?g.polyfillIsolated(e,k,m,n):g.polyfillUnisolated(e,k,m,n))};
g.polyfillUnisolated=function(e,k){var m=g.global;e=e.split(".");for(var n=0;n<e.length-1;n++){var t=e[n];if(!(t in m))return;m=m[t]}e=e[e.length-1];n=m[e];k=k(n);k!=n&&null!=k&&g.defineProperty(m,e,{configurable:!0,writable:!0,value:k})};
g.polyfillIsolated=function(e,k,m){var n=e.split(".");e=1===n.length;var t=n[0];t=!e&&t in g.polyfills?g.polyfills:g.global;for(var w=0;w<n.length-1;w++){var x=n[w];if(!(x in t))return;t=t[x]}n=n[n.length-1];m=g.IS_SYMBOL_NATIVE&&"es6"===m?t[n]:null;k=k(m);null!=k&&(e?g.defineProperty(g.polyfills,n,{configurable:!0,writable:!0,value:k}):k!==m&&(void 0===g.propertyToPolyfillSymbol[n]&&(e=1E9*Math.random()>>>0,g.propertyToPolyfillSymbol[n]=g.IS_SYMBOL_NATIVE?g.global.Symbol(n):g.POLYFILL_PREFIX+e+"$"+
n),g.defineProperty(t,g.propertyToPolyfillSymbol[n],{configurable:!0,writable:!0,value:k})))};g.initSymbol=function(){};
g.polyfill("Symbol",function(e){function k(w){if(this instanceof k)throw new TypeError("Symbol is not a constructor");return new m(n+(w||"")+"_"+t++,w)}function m(w,x){this.$jscomp$symbol$id_=w;g.defineProperty(this,"description",{configurable:!0,writable:!0,value:x})}if(e)return e;m.prototype.toString=function(){return this.$jscomp$symbol$id_};var n="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",t=0;return k},"es6","es3");
g.polyfill("Symbol.iterator",function(e){if(e)return e;e=Symbol("Symbol.iterator");for(var k="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),m=0;m<k.length;m++){var n=g.global[k[m]];"function"===typeof n&&"function"!=typeof n.prototype[e]&&g.defineProperty(n.prototype,e,{configurable:!0,writable:!0,value:function(){return g.iteratorPrototype(g.arrayIteratorImpl(this))}})}return e},"es6","es3");
g.iteratorPrototype=function(e){e={next:e};e[Symbol.iterator]=function(){return this};return e};g.iteratorFromArray=function(e,k){e instanceof String&&(e+="");var m=0,n=!1,t={next:function(){if(!n&&m<e.length){var w=m++;return{value:k(w,e[w]),done:!1}}n=!0;return{done:!0,value:void 0}}};t[Symbol.iterator]=function(){return t};return t};g.polyfill("Array.prototype.keys",function(e){return e?e:function(){return g.iteratorFromArray(this,function(k){return k})}},"es6","es3");
g.polyfill("Array.prototype.values",function(e){return e?e:function(){return g.iteratorFromArray(this,function(k,m){return m})}},"es8","es3");g.checkStringArgs=function(e,k,m){if(null==e)throw new TypeError("The 'this' value for String.prototype."+m+" must not be null or undefined");if(k instanceof RegExp)throw new TypeError("First argument to String.prototype."+m+" must not be a regular expression");return e+""};
g.polyfill("String.prototype.startsWith",function(e){return e?e:function(k,m){var n=g.checkStringArgs(this,k,"startsWith");k+="";var t=n.length,w=k.length;m=Math.max(0,Math.min(m|0,n.length));for(var x=0;x<w&&m<t;)if(n[m++]!=k[x++])return!1;return x>=w}},"es6","es3");g.owns=function(e,k){return Object.prototype.hasOwnProperty.call(e,k)};
g.assign=g.TRUST_ES6_POLYFILLS&&"function"==typeof Object.assign?Object.assign:function(e,k){for(var m=1;m<arguments.length;m++){var n=arguments[m];if(n)for(var t in n)g.owns(n,t)&&(e[t]=n[t])}return e};g.polyfill("Object.assign",function(e){return e||g.assign},"es6","es3");g.checkEs6ConformanceViaProxy=function(){try{var e={},k=Object.create(new g.global.Proxy(e,{get:function(m,n,t){return m==e&&"q"==n&&t==k}}));return!0===k.q}catch(m){return!1}};g.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS=!1;
g.ES6_CONFORMANCE=g.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS&&g.checkEs6ConformanceViaProxy();g.makeIterator=function(e){var k="undefined"!=typeof Symbol&&Symbol.iterator&&e[Symbol.iterator];return k?k.call(e):g.arrayIterator(e)};
g.polyfill("WeakMap",function(e){function k(l){this.id_=(p+=Math.random()+1).toString();if(l){l=g.makeIterator(l);for(var q;!(q=l.next()).done;)q=q.value,this.set(q[0],q[1])}}function m(){if(!e||!Object.seal)return!1;try{var l=Object.seal({}),q=Object.seal({}),v=new e([[l,2],[q,3]]);if(2!=v.get(l)||3!=v.get(q))return!1;v.delete(l);v.set(q,4);return!v.has(l)&&4==v.get(q)}catch(F){return!1}}function n(){}function t(l){var q=typeof l;return"object"===q&&null!==l||"function"===q}function w(l){if(!g.owns(l,
y)){var q=new n;g.defineProperty(l,y,{value:q})}}function x(l){if(!g.ISOLATE_POLYFILLS){var q=Object[l];q&&(Object[l]=function(v){if(v instanceof n)return v;Object.isExtensible(v)&&w(v);return q(v)})}}if(g.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(e&&g.ES6_CONFORMANCE)return e}else if(m())return e;var y="$jscomp_hidden_"+Math.random();x("freeze");x("preventExtensions");x("seal");var p=0;k.prototype.set=function(l,q){if(!t(l))throw Error("Invalid WeakMap key");w(l);if(!g.owns(l,y))throw Error("WeakMap key fail: "+
l);l[y][this.id_]=q;return this};k.prototype.get=function(l){return t(l)&&g.owns(l,y)?l[y][this.id_]:void 0};k.prototype.has=function(l){return t(l)&&g.owns(l,y)&&g.owns(l[y],this.id_)};k.prototype.delete=function(l){return t(l)&&g.owns(l,y)&&g.owns(l[y],this.id_)?delete l[y][this.id_]:!1};return k},"es6","es3");g.MapEntry=function(){};
g.polyfill("Map",function(e){function k(){var p={};return p.previous=p.next=p.head=p}function m(p,l){var q=p.head_;return g.iteratorPrototype(function(){if(q){for(;q.head!=p.head_;)q=q.previous;for(;q.next!=q.head;)return q=q.next,{done:!1,value:l(q)};q=null}return{done:!0,value:void 0}})}function n(p,l){var q=l&&typeof l;"object"==q||"function"==q?x.has(l)?q=x.get(l):(q=""+ ++y,x.set(l,q)):q="p_"+l;var v=p.data_[q];if(v&&g.owns(p.data_,q))for(p=0;p<v.length;p++){var F=v[p];if(l!==l&&F.key!==F.key||
l===F.key)return{id:q,list:v,index:p,entry:F}}return{id:q,list:v,index:-1,entry:void 0}}function t(p){this.data_={};this.head_=k();this.size=0;if(p){p=g.makeIterator(p);for(var l;!(l=p.next()).done;)l=l.value,this.set(l[0],l[1])}}function w(){if(g.ASSUME_NO_NATIVE_MAP||!e||"function"!=typeof e||!e.prototype.entries||"function"!=typeof Object.seal)return!1;try{var p=Object.seal({x:4}),l=new e(g.makeIterator([[p,"s"]]));if("s"!=l.get(p)||1!=l.size||l.get({x:4})||l.set({x:4},"t")!=l||2!=l.size)return!1;
var q=l.entries(),v=q.next();if(v.done||v.value[0]!=p||"s"!=v.value[1])return!1;v=q.next();return v.done||4!=v.value[0].x||"t"!=v.value[1]||!q.next().done?!1:!0}catch(F){return!1}}if(g.USE_PROXY_FOR_ES6_CONFORMANCE_CHECKS){if(e&&g.ES6_CONFORMANCE)return e}else if(w())return e;var x=new WeakMap;t.prototype.set=function(p,l){p=0===p?0:p;var q=n(this,p);q.list||(q.list=this.data_[q.id]=[]);q.entry?q.entry.value=l:(q.entry={next:this.head_,previous:this.head_.previous,head:this.head_,key:p,value:l},q.list.push(q.entry),
this.head_.previous.next=q.entry,this.head_.previous=q.entry,this.size++);return this};t.prototype.delete=function(p){p=n(this,p);return p.entry&&p.list?(p.list.splice(p.index,1),p.list.length||delete this.data_[p.id],p.entry.previous.next=p.entry.next,p.entry.next.previous=p.entry.previous,p.entry.head=null,this.size--,!0):!1};t.prototype.clear=function(){this.data_={};this.head_=this.head_.previous=k();this.size=0};t.prototype.has=function(p){return!!n(this,p).entry};t.prototype.get=function(p){return(p=
n(this,p).entry)&&p.value};t.prototype.entries=function(){return m(this,function(p){return[p.key,p.value]})};t.prototype.keys=function(){return m(this,function(p){return p.key})};t.prototype.values=function(){return m(this,function(p){return p.value})};t.prototype.forEach=function(p,l){for(var q=this.entries(),v;!(v=q.next()).done;)v=v.value,p.call(l,v[1],v[0],this)};t.prototype[Symbol.iterator]=t.prototype.entries;var y=0;return t},"es6","es3");
g.polyfill("String.prototype.endsWith",function(e){return e?e:function(k,m){var n=g.checkStringArgs(this,k,"endsWith");k+="";void 0===m&&(m=n.length);m=Math.max(0,Math.min(m|0,n.length));for(var t=k.length;0<t&&0<m;)if(n[--m]!=k[--t])return!1;return 0>=t}},"es6","es3");g.polyfill("Number.isNaN",function(e){return e?e:function(k){return"number"===typeof k&&isNaN(k)}},"es6","es3");
g.polyfill("Object.entries",function(e){return e?e:function(k){var m=[],n;for(n in k)g.owns(k,n)&&m.push([n,k[n]]);return m}},"es8","es3");var I=this;
function J(){function e(a){this.opts={};this.defaults={};this.messages=Object.assign({},r);this.rules={any:S,array:T,boolean:U,class:V,custom:W,currency:X,date:Y,email:Z,enum:aa,equal:ba,forbidden:ca,function:da,multi:F,number:v,object:q,objectID:l,record:p,string:y,tuple:x,url:w,uuid:t,mac:n,luhn:m};this.aliases={};this.cache=new Map;this.customFunctions={};if(a){E(this.opts,a);a.defaults&&E(this.defaults,a.defaults);if(a.messages)for(var b in a.messages)this.addMessage(b,a.messages[b]);if(a.aliases)for(var c in a.aliases)this.alias(c,
a.aliases[c]);if(a.customRules)for(var d in a.customRules)this.add(d,a.customRules[d]);if(a.customFunctions)for(var f in a.customFunctions)this.addCustomFunction(f,a.customFunctions[f]);if(a.plugins){a=a.plugins;if(!Array.isArray(a))throw Error("Plugins type must be array");a.forEach(this.plugin.bind(this))}this.opts.debug&&(a=function(h){return h},"undefined"===typeof window&&(a=k),this._formatter=a)}}function k(a){K||(K=P(),Q={parser:"babel",useTabs:!1,printWidth:120,trailingComma:"none",tabWidth:4,
singleQuote:!1,semi:!0,bracketSpacing:!0},L=P(),R={language:"js",theme:L.fromJson({keyword:["white","bold"],built_in:"magenta",literal:"cyan",number:"magenta",regexp:"red",string:["yellow","bold"],symbol:"plain",class:"blue",attr:"plain",function:["white","bold"],title:"plain",params:"green",comment:"grey"})});a=K.format(a,Q);return L.highlight(a,R)}function m(a){a.schema;a=a.messages;return{source:'\n\t\t\tif (typeof value !== "string") {\n\t\t\t\t'+this.makeError({type:"string",actual:"value",messages:a})+
'\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (typeof value !== "string")\n\t\t\t\tvalue = String(value);\n\n\t\t\tval = value.replace(/\\D+/g, "");\n\n\t\t\tvar array = [0, 2, 4, 6, 8, 1, 3, 5, 7, 9];\n\t\t\tvar len = val ? val.length : 0,\n\t\t\t\tbit = 1,\n\t\t\t\tsum = 0;\n\t\t\twhile (len--) {\n\t\t\t\tsum += !(bit ^= 1) ? parseInt(val[len], 10) : array[val[len]];\n\t\t\t}\n\n\t\t\tif (!(sum % 10 === 0 && sum > 0)) {\n\t\t\t\t'+this.makeError({type:"luhn",actual:"value",messages:a})+"\n\t\t\t}\n\n\t\t\treturn value;\n\t\t"}}
function n(a){a.schema;a=a.messages;return{source:'\n\t\t\tif (typeof value !== "string") {\n\t\t\t\t'+this.makeError({type:"string",actual:"value",messages:a})+"\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvar v = value.toLowerCase();\n\t\t\tif (!"+ea.toString()+".test(v)) {\n\t\t\t\t"+this.makeError({type:"mac",actual:"value",messages:a})+"\n\t\t\t}\n\t\t\t\n\t\t\treturn value;\n\t\t"}}function t(a){var b=a.schema;a=a.messages;var c=[];c.push('\n\t\tif (typeof value !== "string") {\n\t\t\t'+this.makeError({type:"string",
actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\n\t\tvar val = value.toLowerCase();\n\t\tif (!"+fa.toString()+".test(val)) {\n\t\t\t"+this.makeError({type:"uuid",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\n\t\tconst version = val.charAt(14) | 0;\n\t");9>parseInt(b.version)&&c.push("\n\t\t\tif ("+b.version+" !== version) {\n\t\t\t\t"+this.makeError({type:"uuidVersion",expected:b.version,actual:"version",messages:a})+"\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");c.push('\n\t\tswitch (version) {\n\t\tcase 0:\n\t\tcase 1:\n\t\tcase 2:\n\t\tcase 6:\n\t\t\tbreak;\n\t\tcase 3:\n\t\tcase 4:\n\t\tcase 5:\n  \t\tcase 7:\n\t\tcase 8:\n\t\t\tif (["8", "9", "a", "b"].indexOf(val.charAt(19)) === -1) {\n\t\t\t\t'+
this.makeError({type:"uuid",actual:"value",messages:a})+"\n\t\t\t}\n\t\t}\n\n\t\treturn value;\n\t");return{source:c.join("\n")}}function w(a){var b=a.schema;a=a.messages;var c=[];c.push('\n\t\tif (typeof value !== "string") {\n\t\t\t'+this.makeError({type:"string",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\t");b.empty?c.push("\n\t\t\tif (value.length === 0) return value;\n\t\t"):c.push("\n\t\t\tif (value.length === 0) {\n\t\t\t\t"+this.makeError({type:"urlEmpty",actual:"value",messages:a})+
"\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");c.push("\n\t\tif (!"+ha.toString()+".test(value)) {\n\t\t\t"+this.makeError({type:"url",actual:"value",messages:a})+"\n\t\t}\n\n\t\treturn value;\n\t");return{source:c.join("\n")}}function x(a,b,c){var d=a.schema,f=a.messages;a=[];if(null!=d.items){if(!Array.isArray(d.items))throw Error("Invalid '"+d.type+"' schema. The 'items' field must be an array.");if(0===d.items.length)throw Error("Invalid '"+d.type+"' schema. The 'items' field must not be an empty array.");
}a.push("\n\t\tif (!Array.isArray(value)) {\n\t\t\t"+this.makeError({type:"tuple",actual:"value",messages:f})+"\n\t\t\treturn value;\n\t\t}\n\n\t\tvar len = value.length;\n\t");!1===d.empty&&a.push("\n\t\t\tif (len === 0) {\n\t\t\t\t"+this.makeError({type:"tupleEmpty",actual:"value",messages:f})+"\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");if(null!=d.items){a.push("\n\t\t\tif ("+d.empty+" !== false && len === 0) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (len !== "+d.items.length+") {\n\t\t\t\t"+
this.makeError({type:"tupleLength",expected:d.items.length,actual:"len",messages:f})+"\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");a.push("\n\t\t\tvar arr = value;\n\t\t\tvar parentField = field;\n\t\t");for(f=0;f<d.items.length;f++){a.push("\n\t\t\tvalue = arr["+f+"];\n\t\t");var h=b+"["+f+"]",u=this.getRuleFromSchema(d.items[f]);a.push(this.compileRule(u,c,h,"\n\t\t\tarr["+f+"] = "+(c.async?"await ":"")+"context.fn[%%INDEX%%](arr["+f+'], (parentField ? parentField : "") + "[" + '+f+' + "]", parent, errors, context);\n\t\t',
"arr["+f+"]"))}a.push("\n\t\treturn arr;\n\t")}else a.push("\n\t\treturn value;\n\t");return{source:a.join("\n")}}function y(a){var b=a.schema;a=a.messages;var c=[],d=!1;!0===b.convert&&(d=!0,c.push('\n\t\t\tif (typeof value !== "string") {\n\t\t\t\tvalue = String(value);\n\t\t\t}\n\t\t'));c.push('\n\t\tif (typeof value !== "string") {\n\t\t\t'+this.makeError({type:"string",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\n\t\tvar origValue = value;\n\t");b.trim&&(d=!0,c.push("\n\t\t\tvalue = value.trim();\n\t\t"));
b.trimLeft&&(d=!0,c.push("\n\t\t\tvalue = value.trimLeft();\n\t\t"));b.trimRight&&(d=!0,c.push("\n\t\t\tvalue = value.trimRight();\n\t\t"));b.padStart&&(d=!0,c.push("\n\t\t\tvalue = value.padStart("+b.padStart+", "+JSON.stringify(null!=b.padChar?b.padChar:" ")+");\n\t\t"));b.padEnd&&(d=!0,c.push("\n\t\t\tvalue = value.padEnd("+b.padEnd+", "+JSON.stringify(null!=b.padChar?b.padChar:" ")+");\n\t\t"));b.lowercase&&(d=!0,c.push("\n\t\t\tvalue = value.toLowerCase();\n\t\t"));b.uppercase&&(d=!0,c.push("\n\t\t\tvalue = value.toUpperCase();\n\t\t"));
b.localeLowercase&&(d=!0,c.push("\n\t\t\tvalue = value.toLocaleLowerCase();\n\t\t"));b.localeUppercase&&(d=!0,c.push("\n\t\t\tvalue = value.toLocaleUpperCase();\n\t\t"));c.push("\n\t\t\tvar len = value.length;\n\t");!1===b.empty?c.push("\n\t\t\tif (len === 0) {\n\t\t\t\t"+this.makeError({type:"stringEmpty",actual:"value",messages:a})+"\n\t\t\t}\n\t\t"):!0===b.empty&&c.push("\n\t\t\tif (len === 0) {\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");null!=b.min&&c.push("\n\t\t\tif (len < "+b.min+") {\n\t\t\t\t"+
this.makeError({type:"stringMin",expected:b.min,actual:"len",messages:a})+"\n\t\t\t}\n\t\t");null!=b.max&&c.push("\n\t\t\tif (len > "+b.max+") {\n\t\t\t\t"+this.makeError({type:"stringMax",expected:b.max,actual:"len",messages:a})+"\n\t\t\t}\n\t\t");null!=b.length&&c.push("\n\t\t\tif (len !== "+b.length+") {\n\t\t\t\t"+this.makeError({type:"stringLength",expected:b.length,actual:"len",messages:a})+"\n\t\t\t}\n\t\t");if(null!=b.pattern){var f=b.pattern;"string"==typeof b.pattern&&(f=new RegExp(b.pattern,
b.patternFlags));c.push("\n\t\t\tif (!"+f.toString()+".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringPattern",expected:'"'+f.toString().replace(/"/g,"\\$&")+'"',actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t")}null!=b.contains&&c.push('\n\t\t\tif (value.indexOf("'+b.contains+'") === -1) {\n\t\t\t\t'+this.makeError({type:"stringContains",expected:'"'+b.contains+'"',actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");null!=b.enum&&(f=JSON.stringify(b.enum),c.push("\n\t\t\tif ("+f+".indexOf(value) === -1) {\n\t\t\t\t"+
this.makeError({type:"stringEnum",expected:'"'+b.enum.join(", ")+'"',actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t"));!0===b.numeric&&c.push("\n\t\t\tif (!"+ia.toString()+".test(value) ) {\n\t\t\t\t"+this.makeError({type:"stringNumeric",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.alpha&&c.push("\n\t\t\tif(!"+ja.toString()+".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringAlpha",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.alphanum&&c.push("\n\t\t\tif(!"+ka.toString()+
".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringAlphanum",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.alphadash&&c.push("\n\t\t\tif(!"+la.toString()+".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringAlphadash",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.hex&&c.push("\n\t\t\tif(value.length % 2 !== 0 || !"+ma.toString()+".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringHex",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.singleLine&&c.push('\n\t\t\tif(value.includes("\\n")) {\n\t\t\t\t'+
this.makeError({type:"stringSingleLine",messages:a})+"\n\t\t\t}\n\t\t");!0===b.base64&&c.push("\n\t\t\tif(!"+na.toString()+".test(value)) {\n\t\t\t\t"+this.makeError({type:"stringBase64",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");c.push("\n\t\treturn value;\n\t");return{sanitized:d,source:c.join("\n")}}function p(a,b,c){var d=a.schema,f=[];f.push('\n\t\tif (typeof value !== "object" || value === null || Array.isArray(value)) {\n\t\t\t'+this.makeError({type:"record",actual:"value",messages:a.messages})+
"\n\t\t\treturn value;\n\t\t}\n\t");a=d.key||"string";d=d.value||"any";f.push("\n\t\tconst record = value;\n\t\tlet sanitizedKey, sanitizedValue;\n\t\tconst result = {};\n\t\tfor (let key in value) {\n\t");f.push("sanitizedKey = value = key;");a=this.getRuleFromSchema(a);for(var h in a.messages)h.startsWith("string")&&(a.messages[h]=a.messages[h].replace(" field "," key "));f.push(this.compileRule(a,c,null,"\n\t\tsanitizedKey = "+(c.async?"await ":"")+'context.fn[%%INDEX%%](key, field ? field + "." + key : key, record, errors, context);\n\t',
"sanitizedKey"));f.push("sanitizedValue = value = record[key];");h=this.getRuleFromSchema(d);f.push(this.compileRule(h,c,b+"[key]","\n\t\tsanitizedValue = "+(c.async?"await ":"")+'context.fn[%%INDEX%%](value, field ? field + "." + key : key, record, errors, context);\n\t',"sanitizedValue"));f.push("result[sanitizedKey] = sanitizedValue;");f.push("\n\t\t}\n\t");f.push("return result;");return{source:f.join("\n")}}function l(a,b,c){b=a.schema;var d=a.messages;a=a.index;var f=[];c.customs[a]?c.customs[a].schema=
b:c.customs[a]={schema:b};f.push("\n\t\tconst ObjectID = context.customs["+a+"].schema.ObjectID;\n\t\tif (!ObjectID.isValid(value)) {\n\t\t\t"+this.makeError({type:"objectID",actual:"value",messages:d})+"\n\t\t\treturn;\n\t\t}\n\t");!0===b.convert?f.push("return new ObjectID(value)"):"hexString"===b.convert?f.push("return value.toString()"):f.push("return value");return{source:f.join("\n")}}function q(a,b,c){var d=this,f=a.schema;a=a.messages;var h=[];h.push('\n\t\tif (typeof value !== "object" || value === null || Array.isArray(value)) {\n\t\t\t'+
this.makeError({type:"object",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\t");var u=f.properties||f.props;if(u){h.push("var parentObj = value;");h.push("var parentField = field;");for(var A=Object.keys(u).filter(function(oa){return!d.isMetaKey(oa)}),D=0;D<A.length;D++){var z=A[D],B=this.getRuleFromSchema(u[z]),C=M(z),G=pa.test(C)?"."+C:"['"+C+"']",N="parentObj"+G;z=(b?b+".":"")+z;var H=B.schema.label;H=H?"'"+M(H)+"'":void 0;h.push("\n// Field: "+M(z));h.push('field = parentField ? parentField + "'+
G+'" : "'+C+'";');h.push("value = "+N+";");h.push("label = "+H);h.push(this.compileRule(B,c,z,"\n\t\t\t\t"+N+" = "+(c.async?"await ":"")+"context.fn[%%INDEX%%](value, field, parentObj, errors, context, label);\n\t\t\t",N));!0===this.opts.haltOnFirstError&&h.push("if (errors.length) return parentObj;")}f.strict&&(b=Object.keys(u),h.push("\n\t\t\t\tfield = parentField;\n\t\t\t\tvar invalidProps = [];\n\t\t\t\tvar props = Object.keys(parentObj);\n\n\t\t\t\tfor (let i = 0; i < props.length; i++) {\n\t\t\t\t\tif ("+
JSON.stringify(b)+".indexOf(props[i]) === -1) {\n\t\t\t\t\t\tinvalidProps.push(props[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (invalidProps.length) {\n\t\t\t"),"remove"===f.strict?(h.push("\n\t\t\t\t\tif (errors.length === 0) {\n\t\t\t\t"),h.push("\n\t\t\t\t\t\tinvalidProps.forEach(function(field) {\n\t\t\t\t\t\t\tdelete parentObj[field];\n\t\t\t\t\t\t});\n\t\t\t\t"),h.push("\n\t\t\t\t\t}\n\t\t\t\t")):h.push("\n\t\t\t\t\t"+this.makeError({type:"objectStrict",expected:'"'+b.join(", ")+'"',actual:"invalidProps.join(', ')",
messages:a})+"\n\t\t\t\t"),h.push("\n\t\t\t\t}\n\t\t\t"))}if(null!=f.minProps||null!=f.maxProps)f.strict?h.push("\n\t\t\t\tprops = Object.keys("+(u?"parentObj":"value")+");\n\t\t\t"):h.push("\n\t\t\t\tvar props = Object.keys("+(u?"parentObj":"value")+");\n\t\t\t\t"+(u?"field = parentField;":"")+"\n\t\t\t");null!=f.minProps&&h.push("\n\t\t\tif (props.length < "+f.minProps+") {\n\t\t\t\t"+this.makeError({type:"objectMinProps",expected:f.minProps,actual:"props.length",messages:a})+"\n\t\t\t}\n\t\t");
null!=f.maxProps&&h.push("\n\t\t\tif (props.length > "+f.maxProps+") {\n\t\t\t\t"+this.makeError({type:"objectMaxProps",expected:f.maxProps,actual:"props.length",messages:a})+"\n\t\t\t}\n\t\t");u?h.push("\n\t\t\treturn parentObj;\n\t\t"):h.push("\n\t\t\treturn value;\n\t\t");return{source:h.join("\n")}}function v(a){var b=a.schema;a=a.messages;var c=[];c.push("\n\t\tvar origValue = value;\n\t");var d=!1;!0===b.convert&&(d=!0,c.push('\n\t\t\tif (typeof value !== "number") {\n\t\t\t\tvalue = Number(value);\n\t\t\t}\n\t\t'));
c.push('\n\t\tif (typeof value !== "number" || isNaN(value) || !isFinite(value)) {\n\t\t\t'+this.makeError({type:"number",actual:"origValue",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\t");null!=b.min&&c.push("\n\t\t\tif (value < "+b.min+") {\n\t\t\t\t"+this.makeError({type:"numberMin",expected:b.min,actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");null!=b.max&&c.push("\n\t\t\tif (value > "+b.max+") {\n\t\t\t\t"+this.makeError({type:"numberMax",expected:b.max,actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");
null!=b.equal&&c.push("\n\t\t\tif (value !== "+b.equal+") {\n\t\t\t\t"+this.makeError({type:"numberEqual",expected:b.equal,actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");null!=b.notEqual&&c.push("\n\t\t\tif (value === "+b.notEqual+") {\n\t\t\t\t"+this.makeError({type:"numberNotEqual",expected:b.notEqual,actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.integer&&c.push("\n\t\t\tif (value % 1 !== 0) {\n\t\t\t\t"+this.makeError({type:"numberInteger",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");
!0===b.positive&&c.push("\n\t\t\tif (value <= 0) {\n\t\t\t\t"+this.makeError({type:"numberPositive",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");!0===b.negative&&c.push("\n\t\t\tif (value >= 0) {\n\t\t\t\t"+this.makeError({type:"numberNegative",actual:"origValue",messages:a})+"\n\t\t\t}\n\t\t");c.push("\n\t\treturn value;\n\t");return{sanitized:d,source:c.join("\n")}}function F(a,b,c){var d=a.schema;a.messages;a=[];a.push("\n\t\tvar hasValid = false;\n\t\tvar newVal = value;\n\t\tvar checkErrors = [];\n\t\tvar errorsSize = errors.length;\n\t");
for(var f=0;f<d.rules.length;f++){a.push("\n\t\t\tif (!hasValid) {\n\t\t\t\tvar _errors = [];\n\t\t");var h=this.getRuleFromSchema(d.rules[f]);a.push(this.compileRule(h,c,b,"var tmpVal = "+(c.async?"await ":"")+"context.fn[%%INDEX%%](value, field, parent, _errors, context);","tmpVal"));a.push("\n\t\t\t\tif (errors.length == errorsSize && _errors.length == 0) {\n\t\t\t\t\thasValid = true;\n\t\t\t\t\tnewVal = tmpVal;\n\t\t\t\t} else {\n\t\t\t\t\tArray.prototype.push.apply(checkErrors, [].concat(_errors, errors.splice(errorsSize)));\n\t\t\t\t}\n\t\t\t}\n\t\t")}a.push("\n\t\tif (!hasValid) {\n\t\t\tArray.prototype.push.apply(errors, checkErrors);\n\t\t}\n\n\t\treturn newVal;\n\t");
return{source:a.join("\n")}}function da(a){a.schema;return{source:'\n\t\t\tif (typeof value !== "function")\n\t\t\t\t'+this.makeError({type:"function",actual:"value",messages:a.messages})+"\n\n\t\t\treturn value;\n\t\t"}}function ca(a){var b=a.schema;a=a.messages;var c=[];c.push("\n\t\tif (value !== null && value !== undefined) {\n\t");b.remove?c.push("\n\t\t\treturn undefined;\n\t\t"):c.push("\n\t\t\t"+this.makeError({type:"forbidden",actual:"value",messages:a})+"\n\t\t");c.push("\n\t\t}\n\n\t\treturn value;\n\t");
return{source:c.join("\n")}}function ba(a){var b=a.schema;a=a.messages;var c=[];b.field?(b.strict?c.push('\n\t\t\t\tif (value !== parent["'+b.field+'"])\n\t\t\t'):c.push('\n\t\t\t\tif (value != parent["'+b.field+'"])\n\t\t\t'),c.push("\n\t\t\t\t"+this.makeError({type:"equalField",actual:"value",expected:JSON.stringify(b.field),messages:a})+"\n\t\t")):(b.strict?c.push("\n\t\t\t\tif (value !== "+JSON.stringify(b.value)+")\n\t\t\t"):c.push("\n\t\t\t\tif (value != "+JSON.stringify(b.value)+")\n\t\t\t"),
c.push("\n\t\t\t\t"+this.makeError({type:"equalValue",actual:"value",expected:JSON.stringify(b.value),messages:a})+"\n\t\t"));c.push("\n\t\treturn value;\n\t");return{source:c.join("\n")}}function aa(a){var b=a.schema;a=a.messages;return{source:"\n\t\t\tif ("+JSON.stringify(b.values||[])+".indexOf(value) === -1)\n\t\t\t\t"+this.makeError({type:"enumValue",expected:'"'+b.values.join(", ")+'"',actual:"value",messages:a})+"\n\t\t\t\n\t\t\treturn value;\n\t\t"}}function Z(a){var b=a.schema;a=a.messages;
var c=[],d="precise"==b.mode?qa:ra,f=!1;c.push('\n\t\tif (typeof value !== "string") {\n\t\t\t'+this.makeError({type:"string",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\t");b.empty?c.push("\n\t\t\tif (value.length === 0) return value;\n\t\t"):c.push("\n\t\t\tif (value.length === 0) {\n\t\t\t\t"+this.makeError({type:"emailEmpty",actual:"value",messages:a})+"\n\t\t\t\treturn value;\n\t\t\t}\n\t\t");b.normalize&&(f=!0,c.push("\n\t\t\tvalue = value.trim().toLowerCase();\n\t\t"));null!=
b.min&&c.push("\n\t\t\tif (value.length < "+b.min+") {\n\t\t\t\t"+this.makeError({type:"emailMin",expected:b.min,actual:"value.length",messages:a})+"\n\t\t\t}\n\t\t");null!=b.max&&c.push("\n\t\t\tif (value.length > "+b.max+") {\n\t\t\t\t"+this.makeError({type:"emailMax",expected:b.max,actual:"value.length",messages:a})+"\n\t\t\t}\n\t\t");c.push("\n\t\tif (!"+d.toString()+".test(value)) {\n\t\t\t"+this.makeError({type:"email",actual:"value",messages:a})+"\n\t\t}\n\n\t\treturn value;\n\t");return{sanitized:f,
source:c.join("\n")}}function Y(a){var b=a.schema;a=a.messages;var c=[],d=!1;c.push("\n\t\tvar origValue = value;\n\t");!0===b.convert&&(d=!0,c.push("\n\t\t\tif (!(value instanceof Date)) {\n\t\t\t\tvalue = new Date(value.length && !isNaN(+value) ? +value : value);\n\t\t\t}\n\t\t"));c.push("\n\t\tif (!(value instanceof Date) || isNaN(value.getTime()))\n\t\t\t"+this.makeError({type:"date",actual:"origValue",messages:a})+"\n\n\t\treturn value;\n\t");return{sanitized:d,source:c.join("\n")}}function X(a){var b=
a.schema;a=a.messages;var c=b.currencySymbol||null,d=b.thousandSeparator||",",f=b.decimalSeparator||".",h=b.customRegex;b=!b.symbolOptional;b="(?=.*\\d)^(-?~1|~1-?)(([0-9]\\d{0,2}(~2\\d{3})*)|0)?(\\~3\\d{1,2})?$".replace(/~1/g,c?"\\"+c+(b?"":"?"):"").replace("~2",d).replace("~3",f);c=[];c.push("\n\t\tif (!value.match("+(h||new RegExp(b))+")) {\n\t\t\t"+this.makeError({type:"currency",actual:"value",messages:a})+"\n\t\t\treturn value;\n\t\t}\n\n\t\treturn value;\n\t");return{source:c.join("\n")}}function W(a,
b,c){var d=[];d.push("\n\t\t"+this.makeCustomValidator({fnName:"check",path:b,schema:a.schema,messages:a.messages,context:c,ruleIndex:a.index})+"\n\t\treturn value;\n\t");return{source:d.join("\n")}}function V(a,b,c){b=a.schema;var d=a.messages;a=a.index;var f=[],h=b.instanceOf.name?b.instanceOf.name:"<UnknowClass>";c.customs[a]?c.customs[a].schema=b:c.customs[a]={schema:b};f.push("\n\t\tif (!(value instanceof context.customs["+a+"].schema.instanceOf))\n\t\t\t"+this.makeError({type:"classInstanceOf",
actual:"value",expected:"'"+h+"'",messages:d})+"\n\t");f.push("\n\t\treturn value;\n\t");return{source:f.join("\n")}}function U(a){var b=a.schema;a=a.messages;var c=[],d=!1;c.push("\n\t\tvar origValue = value;\n\t");!0===b.convert&&(d=!0,c.push('\n\t\t\tif (typeof value !== "boolean") {\n\t\t\t\tif (\n\t\t\t\tvalue === 1\n\t\t\t\t|| value === "true"\n\t\t\t\t|| value === "1"\n\t\t\t\t|| value === "on"\n\t\t\t\t) {\n\t\t\t\t\tvalue = true;\n\t\t\t\t} else if (\n\t\t\t\tvalue === 0\n\t\t\t\t|| value === "false"\n\t\t\t\t|| value === "0"\n\t\t\t\t|| value === "off"\n\t\t\t\t) {\n\t\t\t\t\tvalue = false;\n\t\t\t\t}\n\t\t\t}\n\t\t'));
c.push('\n\t\tif (typeof value !== "boolean") {\n\t\t\t'+this.makeError({type:"boolean",actual:"origValue",messages:a})+"\n\t\t}\n\t\t\n\t\treturn value;\n\t");return{sanitized:d,source:c.join("\n")}}function T(a,b,c){var d=a.schema,f=a.messages;a=[];var h=!1;!0===d.convert&&(h=!0,a.push("\n\t\t\tif (!Array.isArray(value) && value != null) {\n\t\t\t\tvalue = [value];\n\t\t\t}\n\t\t"));a.push("\n\t\tif (!Array.isArray(value)) {\n\t\t\t"+this.makeError({type:"array",actual:"value",messages:f})+"\n\t\t\treturn value;\n\t\t}\n\n\t\tvar len = value.length;\n\t");
!1===d.empty&&a.push("\n\t\t\tif (len === 0) {\n\t\t\t\t"+this.makeError({type:"arrayEmpty",actual:"value",messages:f})+"\n\t\t\t}\n\t\t");null!=d.min&&a.push("\n\t\t\tif (len < "+d.min+") {\n\t\t\t\t"+this.makeError({type:"arrayMin",expected:d.min,actual:"len",messages:f})+"\n\t\t\t}\n\t\t");null!=d.max&&a.push("\n\t\t\tif (len > "+d.max+") {\n\t\t\t\t"+this.makeError({type:"arrayMax",expected:d.max,actual:"len",messages:f})+"\n\t\t\t}\n\t\t");null!=d.length&&a.push("\n\t\t\tif (len !== "+d.length+
") {\n\t\t\t\t"+this.makeError({type:"arrayLength",expected:d.length,actual:"len",messages:f})+"\n\t\t\t}\n\t\t");null!=d.contains&&a.push("\n\t\t\tif (value.indexOf("+JSON.stringify(d.contains)+") === -1) {\n\t\t\t\t"+this.makeError({type:"arrayContains",expected:JSON.stringify(d.contains),actual:"value",messages:f})+"\n\t\t\t}\n\t\t");!0===d.unique&&a.push("\n\t\t\tif(len > (new Set(value)).size) {\n\t\t\t\t"+this.makeError({type:"arrayUnique",expected:"Array.from(new Set(value.filter((item, index) => value.indexOf(item) !== index)))",
actual:"value",messages:f})+"\n\t\t\t}\n\t\t");if(null!=d.enum){var u=JSON.stringify(d.enum);a.push("\n\t\t\tfor (var i = 0; i < value.length; i++) {\n\t\t\t\tif ("+u+".indexOf(value[i]) === -1) {\n\t\t\t\t\t"+this.makeError({type:"arrayEnum",expected:'"'+d.enum.join(", ")+'"',actual:"value[i]",messages:f})+"\n\t\t\t\t}\n\t\t\t}\n\t\t")}null!=d.items?(a.push("\n\t\t\tvar arr = value;\n\t\t\tvar parentField = field;\n\t\t\tfor (var i = 0; i < arr.length; i++) {\n\t\t\t\tvalue = arr[i];\n\t\t"),b+=
"[]",d=this.getRuleFromSchema(d.items),a.push(this.compileRule(d,c,b,"arr[i] = "+(c.async?"await ":"")+'context.fn[%%INDEX%%](arr[i], (parentField ? parentField : "") + "[" + i + "]", parent, errors, context)',"arr[i]")),a.push("\n\t\t\t}\n\t\t"),a.push("\n\t\treturn arr;\n\t")):a.push("\n\t\treturn value;\n\t");return{sanitized:h,source:a.join("\n")}}function S(){var a=[];a.push("\n\t\treturn value;\n\t");return{source:a.join("\n")}}function sa(a,b,c){return a.replace(b,void 0===c||null===c?"":"function"===
typeof c.toString?c:typeof c)}function E(a,b,c){void 0===c&&(c={});for(var d in b){var f=b[d];f="object"!==typeof f||Array.isArray(f)||null==f?!1:0<Object.keys(f).length;if(f)a[d]=a[d]||{},E(a[d],b[d],c);else if(!0!==c.skipIfExist||void 0===a[d])a[d]=b[d]}return a}function M(a){return a.replace(ta,function(b){switch(b){case '"':case "'":case "\\":return"\\"+b;case "\n":return"\\n";case "\r":return"\\r";case "\u2028":return"\\u2028";case "\u2029":return"\\u2029"}})}function P(){throw Error("Dynamic requires are not currently supported by rollup-plugin-commonjs");
}var r={required:"The '{field}' field is required.",string:"The '{field}' field must be a string.",stringEmpty:"The '{field}' field must not be empty.",stringMin:"The '{field}' field length must be greater than or equal to {expected} characters long.",stringMax:"The '{field}' field length must be less than or equal to {expected} characters long.",stringLength:"The '{field}' field length must be {expected} characters long.",stringPattern:"The '{field}' field fails to match the required pattern.",stringContains:"The '{field}' field must contain the '{expected}' text.",
stringEnum:"The '{field}' field does not match any of the allowed values.",stringNumeric:"The '{field}' field must be a numeric string.",stringAlpha:"The '{field}' field must be an alphabetic string.",stringAlphanum:"The '{field}' field must be an alphanumeric string.",stringAlphadash:"The '{field}' field must be an alphadash string.",stringHex:"The '{field}' field must be a hex string.",stringSingleLine:"The '{field}' field must be a single line string.",stringBase64:"The '{field}' field must be a base64 string.",
number:"The '{field}' field must be a number.",numberMin:"The '{field}' field must be greater than or equal to {expected}.",numberMax:"The '{field}' field must be less than or equal to {expected}.",numberEqual:"The '{field}' field must be equal to {expected}.",numberNotEqual:"The '{field}' field can't be equal to {expected}.",numberInteger:"The '{field}' field must be an integer.",numberPositive:"The '{field}' field must be a positive number.",numberNegative:"The '{field}' field must be a negative number.",
array:"The '{field}' field must be an array.",arrayEmpty:"The '{field}' field must not be an empty array.",arrayMin:"The '{field}' field must contain at least {expected} items.",arrayMax:"The '{field}' field must contain less than or equal to {expected} items.",arrayLength:"The '{field}' field must contain {expected} items.",arrayContains:"The '{field}' field must contain the '{expected}' item.",arrayUnique:"The '{actual}' value in '{field}' field does not unique the '{expected}' values.",arrayEnum:"The '{actual}' value in '{field}' field does not match any of the '{expected}' values.",
tuple:"The '{field}' field must be an array.",tupleEmpty:"The '{field}' field must not be an empty array.",tupleLength:"The '{field}' field must contain {expected} items.",boolean:"The '{field}' field must be a boolean.",currency:"The '{field}' must be a valid currency format",date:"The '{field}' field must be a Date.",dateMin:"The '{field}' field must be greater than or equal to {expected}.",dateMax:"The '{field}' field must be less than or equal to {expected}.",enumValue:"The '{field}' field value '{expected}' does not match any of the allowed values.",
equalValue:"The '{field}' field value must be equal to '{expected}'.",equalField:"The '{field}' field value must be equal to '{expected}' field value.",forbidden:"The '{field}' field is forbidden.",function:"The '{field}' field must be a function.",email:"The '{field}' field must be a valid e-mail.",emailEmpty:"The '{field}' field must not be empty.",emailMin:"The '{field}' field length must be greater than or equal to {expected} characters long.",emailMax:"The '{field}' field length must be less than or equal to {expected} characters long.",
luhn:"The '{field}' field must be a valid checksum luhn.",mac:"The '{field}' field must be a valid MAC address.",object:"The '{field}' must be an Object.",objectStrict:"The object '{field}' contains forbidden keys: '{actual}'.",objectMinProps:"The object '{field}' must contain at least {expected} properties.",objectMaxProps:"The object '{field}' must contain {expected} properties at most.",url:"The '{field}' field must be a valid URL.",urlEmpty:"The '{field}' field must not be empty.",uuid:"The '{field}' field must be a valid UUID.",
uuidVersion:"The '{field}' field must be a valid UUID version provided.",classInstanceOf:"The '{field}' field must be an instance of the '{expected}' class.",objectID:"The '{field}' field must be an valid ObjectID",record:"The '{field}' must be an Object."};r.required;r.string;r.stringEmpty;r.stringMin;r.stringMax;r.stringLength;r.stringPattern;r.stringContains;r.stringEnum;r.stringNumeric;r.stringAlpha;r.stringAlphanum;r.stringAlphadash;r.stringHex;r.stringSingleLine;r.stringBase64;r.number;r.numberMin;
r.numberMax;r.numberEqual;r.numberNotEqual;r.numberInteger;r.numberPositive;r.numberNegative;r.array;r.arrayEmpty;r.arrayMin;r.arrayMax;r.arrayLength;r.arrayContains;r.arrayUnique;r.arrayEnum;r.tuple;r.tupleEmpty;r.tupleLength;r.currency;r.date;r.dateMin;r.dateMax;r.enumValue;r.equalValue;r.equalField;r.forbidden;r.email;r.emailEmpty;r.emailMin;r.emailMax;r.luhn;r.mac;r.object;r.objectStrict;r.objectMinProps;r.objectMaxProps;r.url;r.urlEmpty;r.uuid;r.uuidVersion;r.classInstanceOf;r.objectID;r.record;
var qa=/^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,ra=/^\S+@\S+\.\S+$/,pa=/^[_$a-zA-Z][_$a-zA-Z0-9]*$/,ta=/["'\\\n\r\u2028\u2029]/g,ia=/^-?[0-9]\d*(\.\d+)?$/,ja=/^[a-zA-Z]+$/,ka=/^[a-zA-Z0-9]+$/,la=/^[a-zA-Z0-9_-]+$/,ma=/^[0-9a-fA-F]+$/,na=/^(?:[A-Za-z0-9+\\/]{4})*(?:[A-Za-z0-9+\\/]{2}==|[A-Za-z0-9+/]{3}=)?$/,ha=/^https?:\/\/\S+/,fa=/^([0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[0-9a-f]{4}-[0-9a-f]{12}|[0]{8}-[0]{4}-[0]{4}-[0]{4}-[0]{12})$/i,
ea=/^((([a-f0-9][a-f0-9]+[-]){5}|([a-f0-9][a-f0-9]+[:]){5})([a-f0-9][a-f0-9])$)|(^([a-f0-9][a-f0-9][a-f0-9][a-f0-9]+[.]){2}([a-f0-9][a-f0-9][a-f0-9][a-f0-9]))$/i,K,Q,L,R;try{var O=(new Function("return Object.getPrototypeOf(async function(){}).constructor"))()}catch(a){}e.prototype.validate=function(a,b){return this.compile(b)(a)};e.prototype.wrapRequiredCheckSourceCode=function(a,b,c,d){var f=[],h=this.opts.considerNullAsAValue;void 0===h&&(h=!1);var u=!0===a.schema.optional||"forbidden"===a.schema.type,
A=h?!1!==a.schema.nullable||"forbidden"===a.schema.type:!0===a.schema.optional||!0===a.schema.nullable||"forbidden"===a.schema.type;(h?void 0!=a.schema.default&&null!=a.schema.default:void 0!=a.schema.default)?(u=!1,h?!1===a.schema.nullable&&(A=!1):!0!==a.schema.nullable&&(A=!1),"function"===typeof a.schema.default?(c.customs[a.index]||(c.customs[a.index]={}),c.customs[a.index].defaultFn=a.schema.default,a="context.customs["+a.index+"].defaultFn.call(this, context.rules["+a.index+"].schema, field, parent, context)"):
a=JSON.stringify(a.schema.default),d="\n\t\t\t\tvalue = "+a+";\n\t\t\t\t"+d+" = value;\n\t\t\t"):d=this.makeError({type:"required",actual:"value",messages:a.messages});f.push("\n\t\t\tif (value === undefined) { "+((u?"\n// allow undefined\n":d)+" }\n\t\t\telse if (value === null) { ")+((A?"\n// allow null\n":d)+" }\n\t\t\t")+(b?"else { "+b+" }":"")+"\n\t\t");return f.join("\n")};e.prototype.isMetaKey=function(a){return a.startsWith("$$")};e.prototype.removeMetasKeys=function(a){var b=this;Object.keys(a).forEach(function(c){b.isMetaKey(c)&&
delete a[c]})};e.prototype.compile=function(a){function b(u,A){d.data=u;A&&A.meta&&(d.meta=A.meta);return h.call(c,u,d)}if(null===a||"object"!==typeof a)throw Error("Invalid schema.");var c=this,d={index:0,async:!0===a.$$async,rules:[],fn:[],customs:{},customFunctions:this.customFunctions,utils:{replace:sa}};this.cache.clear();delete a.$$async;if(d.async&&!O)throw Error("Asynchronous mode is not supported.");if(!0!==a.$$root)if(Array.isArray(a))a=this.getRuleFromSchema(a).schema;else{var f=Object.assign({},
a);a={type:"object",strict:f.$$strict,properties:f};this.removeMetasKeys(f)}f=["var errors = [];","var field;","var parent = null;","var label = "+(a.label?'"'+a.label+'"':"null")+";"];a=this.getRuleFromSchema(a);f.push(this.compileRule(a,d,null,(d.async?"await ":"")+"context.fn[%%INDEX%%](value, field, null, errors, context, label);","value"));f.push("if (errors.length) {");f.push("\n\t\t\treturn errors.map(err => {\n\t\t\t\tif (err.message) {\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\{field\\}/g, err.label || err.field);\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\{expected\\}/g, err.expected);\n\t\t\t\t\terr.message = context.utils.replace(err.message, /\\{actual\\}/g, err.actual);\n\t\t\t\t}\n\t\t\t\tif(!err.label) delete err.label\n\t\t\t\treturn err;\n\t\t\t});\n\t\t");
f.push("}");f.push("return true;");a=f.join("\n");var h=new (d.async?O:Function)("value","context",a);this.opts.debug&&console.log(this._formatter("// Main check function\n"+h.toString()));this.cache.clear();b.async=d.async;return b};e.prototype.compileRule=function(a,b,c,d,f){var h=[],u=this.cache.get(a.schema);u?(a=u,a.cycle=!0,a.cycleStack=[],h.push(this.wrapRequiredCheckSourceCode(a,"\n\t\t\t\tvar rule = context.rules["+a.index+"];\n\t\t\t\tif (rule.cycleStack.indexOf(value) === -1) {\n\t\t\t\t\trule.cycleStack.push(value);\n\t\t\t\t\t"+
d.replace(/%%INDEX%%/g,a.index)+"\n\t\t\t\t\trule.cycleStack.pop(value);\n\t\t\t\t}\n\t\t\t",b,f))):(this.cache.set(a.schema,a),a.index=b.index,b.rules[b.index]=a,u=null!=c?c:"$$root",b.index++,c=a.ruleFunction.call(this,a,c,b),c.source=c.source.replace(/%%INDEX%%/g,a.index),c=new (b.async?O:Function)("value","field","parent","errors","context","label",c.source),b.fn[a.index]=c.bind(this),h.push(this.wrapRequiredCheckSourceCode(a,d.replace(/%%INDEX%%/g,a.index),b,f)),h.push(this.makeCustomValidator({vName:f,
path:u,schema:a.schema,context:b,messages:a.messages,ruleIndex:a.index})),this.opts.debug&&console.log(this._formatter("// Context.fn["+a.index+"]\n"+c.toString())));return h.join("\n")};e.prototype.getRuleFromSchema=function(a){a=this.resolveType(a);var b=this.aliases[a.type];b&&(delete a.type,a=E(a,b,{skipIfExist:!0}));b=this.rules[a.type];if(!b)throw Error("Invalid '"+a.type+"' type in validator schema.");return{messages:Object.assign({},this.messages,a.messages),schema:E(a,this.defaults[a.type],
{skipIfExist:!0}),ruleFunction:b}};e.prototype.parseShortHand=function(a){a=a.split("|").map(function(d){return d.trim()});var b=a[0];var c=b.endsWith("[]")?this.getRuleFromSchema({type:"array",items:b.slice(0,-2)}).schema:{type:a[0]};a.slice(1).forEach(function(d){var f=d.indexOf(":");if(-1!==f){var h=d.substring(0,f).trim();d=d.substring(f+1).trim();"true"===d||"false"===d?d="true"===d:Number.isNaN(Number(d))||(d=Number(d));c[h]=d}else d.startsWith("no-")?c[d.slice(3)]=!1:c[d]=!0});return c};e.prototype.makeError=
function(a){var b=a.type,c=a.field,d=a.expected,f=a.actual,h={type:'"'+b+'"',message:'"'+a.messages[b]+'"'};h.field=c?'"'+c+'"':"field";null!=d&&(h.expected=d);null!=f&&(h.actual=f);h.label="label";return"errors.push({ "+Object.keys(h).map(function(u){return u+": "+h[u]}).join(", ")+" });"};e.prototype.makeCustomValidator=function(a){var b=a.vName;void 0===b&&(b="value");var c=a.fnName;void 0===c&&(c="custom");var d=a.ruleIndex,f=a.path,h=a.schema,u=a.context,A=a.messages;a="rule"+d;var D="fnCustomErrors"+
d;if("function"==typeof h[c]||Array.isArray(h[c])){u.customs[d]?(u.customs[d].messages=A,u.customs[d].schema=h):u.customs[d]={messages:A,schema:h};var z=[];if(this.opts.useNewCustomCheckerFunction){z.push("\n               \t\tconst "+a+" = context.customs["+d+"];\n\t\t\t\t\tconst "+D+" = [];\n\t\t\t\t");if(Array.isArray(h[c]))for(var B=0;B<h[c].length;B++){var C=h[c][B];"string"===typeof C&&(C=this.parseShortHand(C),h[c][B]=C);var G=1E3*d+B;u.customs[G]={messages:A,schema:Object.assign({},h,{custom:C,
index:B})};z.push("\n\t\t\t\t\t\t\tconst "+a+"_"+B+" = context.customs["+G+"];\n\n\t\t\t\t\t \t");C.type&&z.push("\n\t\t\t\t\t\t\t "+b+" = "+(u.async?"await ":"")+"context.customFunctions["+a+".schema."+c+"["+B+"].type].call(this, "+b+", "+D+" , "+a+"_"+B+'.schema, "'+f+'", parent, context);\n\t\t\t\t\t\t\t');"function"===typeof C&&z.push("\n\t\t\t\t\t\t\t"+b+" = "+(u.async?"await ":"")+a+".schema."+c+"["+B+"].call(this, "+b+", "+D+" , "+a+'.schema, "'+f+'", parent, context);\n\t\t\t\t\t\t\t')}else z.push("\n\t\t\t\t\t"+
b+" = "+(u.async?"await ":"")+a+".schema."+c+".call(this, "+b+", "+D+" , "+a+'.schema, "'+f+'", parent, context);\n\t\t\t\t\t');z.push("\n\t\t\t\t\tif (Array.isArray("+D+" )) {\n                  \t\t"+D+" .forEach(err => errors.push(Object.assign({ message: "+a+".messages[err.type], field }, err)));\n\t\t\t\t\t}\n\t\t\t\t")}else h="res_"+a,z.push("\n\t\t\t\t\tconst "+a+" = context.customs["+d+"];\n\t\t\t\t\tconst "+h+" = "+(u.async?"await ":"")+a+".schema."+c+".call(this, "+b+", "+a+'.schema, "'+
f+'", parent, context);\n\t\t\t\t\tif (Array.isArray('+h+")) {\n\t\t\t\t\t\t"+h+".forEach(err => errors.push(Object.assign({ message: "+a+".messages[err.type], field }, err)));\n\t\t\t\t\t}\n\t\t\t");return z.join("\n")}return""};e.prototype.add=function(a,b){this.rules[a]=b};e.prototype.addCustomFunction=function(a,b){this.customFunctions[a]=b};e.prototype.addMessage=function(a,b){this.messages[a]=b};e.prototype.alias=function(a,b){if(this.rules[a])throw Error("Alias name must not be a rule name");
this.aliases[a]=b};e.prototype.plugin=function(a){if("function"!==typeof a)throw Error("Plugin fn type must be function");return a(this)};e.prototype.resolveType=function(a){var b=this;if("string"===typeof a)a=this.parseShortHand(a);else if(Array.isArray(a)){if(0===a.length)throw Error("Invalid schema.");a={type:"multi",rules:a};a.rules.map(function(u){return b.getRuleFromSchema(u)}).every(function(u){return!0===u.schema.optional})&&(a.optional=!0);var c=this.opts.considerNullAsAValue?!1:!0;a.rules.map(function(u){return b.getRuleFromSchema(u)}).every(function(u){return u.schema.nullable===
c})&&(a.nullable=c)}if(a.$$type){var d=this.getRuleFromSchema(a.$$type).schema;delete a.$$type;var f=Object.assign({},a),h;for(h in a)delete a[h];E(a,d,{skipIfExist:!0});a.props=f}return a};e.prototype.normalize=function(a){var b=this,c=this.resolveType(a);this.aliases[c.type]&&(c=E(c,this.normalize(this.aliases[c.type]),{skipIfExists:!0}));c=E(c,this.defaults[c.type],{skipIfExist:!0});if("multi"===c.type)return c.rules=c.rules.map(function(d){return b.normalize(d)}),c.optional=c.rules.every(function(d){return!0===
d.optional}),c;if("array"===c.type)return c.items=this.normalize(c.items),c;"object"===c.type&&c.props&&Object.entries(c.props).forEach(function(d){return c.props[d[0]]=b.normalize(d[1])});"object"===typeof a&&(a.type?(a=this.normalize(a.type),E(c,a,{skipIfExists:!0})):Object.entries(a).forEach(function(d){return c[d[0]]=b.normalize(d[1])}));return c};return e}
"object"===typeof exports&&"undefined"!==typeof module?module.exports=J():"function"===typeof define&&define.amd?define(J):(I="undefined"!==typeof globalThis?globalThis:I||self,I.FastestValidator=J())
