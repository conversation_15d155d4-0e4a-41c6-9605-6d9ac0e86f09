"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.retryWith = exports.retryWithAsync = void 0;
const _1 = require(".");
const error_1 = require("../error");
/**
 * wrap async function with retry
 * @param retryFn
 * @param retryTimes
 * @param options
 */
function retryWithAsync(retryFn, retryTimes = 1, options = {}) {
    let defaultRetry = retryTimes;
    let error;
    return (async (...args) => {
        do {
            try {
                return await retryFn.bind(options.receiver || this)(...args);
            }
            catch (err) {
                error = err;
            }
            if (options.retryInterval >= 0) {
                await (0, _1.sleep)(options.retryInterval);
            }
        } while (defaultRetry-- > 0);
        if (options.throwOriginError) {
            throw error;
        }
        else {
            throw new error_1.MidwayRetryExceededMaxTimesError(retryFn.name || 'anonymous', retryTimes, error);
        }
    });
}
exports.retryWithAsync = retryWithAsync;
/**
 * wrap sync function with retry
 * @param retryFn
 * @param retryTimes
 * @param options
 */
function retryWith(retryFn, retryTimes = 1, options = {}) {
    let defaultRetry = retryTimes;
    let error;
    return ((...args) => {
        do {
            try {
                return retryFn.bind(options.receiver || this)(...args);
            }
            catch (err) {
                error = err;
            }
        } while (defaultRetry-- > 0);
        if (options.throwOriginError) {
            throw error;
        }
        else {
            throw new error_1.MidwayRetryExceededMaxTimesError(retryFn.name || 'anonymous', retryTimes, error);
        }
    });
}
exports.retryWith = retryWith;
//# sourceMappingURL=retry.js.map