/// <reference types="node" />
/// <reference types="mocha" />
import { IOptions } from './interface';
export declare const sendData: (proc: NodeJS.Process, result: any) => void;
export declare const onMessage: (proc: NodeJS.Process, cb: (message: any) => any) => void;
export declare const getData: (id: number | string) => any;
export declare const getRandomId: (key?: string) => string;
export declare const getDebugPath: () => {
    path: string;
    extensions: string[];
} | {
    path: string;
    extensions?: undefined;
};
export declare function getWssUrl(port: any, type?: string, count?: number): Promise<string>;
export declare function waitDebug(port: any): Promise<unknown>;
export declare const getFun: (options: IOptions) => (...args: any[]) => Promise<unknown>;
export declare const getType: (data: unknown) => string;
export declare const checkPort: (port: any) => Promise<boolean>;
export declare const vscodeSupport: (options: any) => void;
