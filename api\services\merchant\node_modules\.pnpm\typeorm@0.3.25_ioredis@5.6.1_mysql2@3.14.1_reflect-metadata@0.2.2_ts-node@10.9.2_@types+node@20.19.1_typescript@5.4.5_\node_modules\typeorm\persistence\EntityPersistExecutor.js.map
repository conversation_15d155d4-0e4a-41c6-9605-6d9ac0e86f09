{"version": 3, "sources": ["../../src/persistence/EntityPersistExecutor.ts"], "names": [], "mappings": ";;;AAGA,kEAA8D;AAC9D,uDAAmD;AACnD,oFAAgF;AAGhF,uCAAmC;AACnC,uFAAmF;AACnF,2GAAuG;AACvG,yFAAqF;AACrF,+EAA2E;AAC3E,qFAAiF;AACjF,+CAA2C;AAE3C;;GAEG;AACH,MAAa,qBAAqB;IAC9B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,UAAsB,EACtB,WAAoC,EACpC,IAAmD,EACnD,MAAqC,EACrC,MAAuC,EACvC,OAAqC;QALrC,eAAU,GAAV,UAAU,CAAY;QACtB,gBAAW,GAAX,WAAW,CAAyB;QACpC,SAAI,GAAJ,IAAI,CAA+C;QACnD,WAAM,GAAN,MAAM,CAA+B;QACrC,WAAM,GAAN,MAAM,CAAiC;QACvC,YAAO,GAAP,OAAO,CAA8B;IAChD,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,iEAAiE;QACjE,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ;YAC/C,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,qCAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAExE,uGAAuG;QACvG,MAAM,OAAO,CAAC,OAAO,EAAE,CAAA;QAEvB,6HAA6H;QAC7H,6GAA6G;QAC7G,MAAM,WAAW,GACb,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAE3D,uGAAuG;QACvG,6DAA6D;QAC7D,MAAM,kBAAkB,GAAG,WAAW,CAAC,IAAI,CAAA;QAC3C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YACpC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACxC,CAAC;QAED,IAAI,CAAC;YACD,+BAA+B;YAC/B,MAAM,QAAQ,GAAoB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;gBACxD,CAAC,CAAC,IAAI,CAAC,MAAM;gBACb,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACnB,MAAM,gBAAgB,GAClB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC;gBACxD,CAAC,CAAC,mBAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;gBAC9C,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;YAEpB,iDAAiD;YACjD,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpC,MAAM,QAAQ,GAAc,EAAE,CAAA;gBAE9B,mEAAmE;gBACnE,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxB,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM;wBAC5B,CAAC,CAAC,IAAI,CAAC,MAAM;wBACb,CAAC,CAAC,MAAM,CAAC,WAAW,CAAA;oBACxB,IAAI,YAAY,KAAK,MAAM;wBACvB,MAAM,IAAI,uDAA0B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAEnD,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;yBAC3B,WAAW,CAAC,YAAY,CAAC;yBACzB,uBAAuB,CAAC,MAAM,CAAC,CAAA;oBAEpC,QAAQ,CAAC,IAAI,CACT,IAAI,iBAAO,CAAC;wBACR,QAAQ;wBACR,MAAM,EAAE,MAAM;wBACd,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;wBACnC,YAAY,EAAE,IAAI,CAAC,IAAI,KAAK,MAAM;wBAClC,aAAa,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ;wBACrC,gBAAgB,EAAE,IAAI,CAAC,IAAI,KAAK,aAAa;wBAC7C,cAAc,EAAE,IAAI,CAAC,IAAI,KAAK,SAAS;qBAC1C,CAAC,CACL,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,wCAAwC;gBACxC,6FAA6F;gBAC7F,MAAM,sBAAsB,GAAG,IAAI,+CAAsB,CACrD,QAAQ,CACX,CAAA;gBACD,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;oBACzB,2DAA2D;oBAC3D,oGAAoG;oBACpG,sBAAsB,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;gBACpD,CAAC,CAAC,CAAA;gBACF,2CAA2C;gBAE3C,kDAAkD;gBAClD,kEAAkE;gBAClE,8BAA8B;gBAC9B,MAAM,IAAI,yDAA2B,CACjC,WAAW,EACX,QAAQ,CACX,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACjB,iCAAiC;gBAEjC,qCAAqC;gBACrC,6CAA6C;gBAC7C,IACI,IAAI,CAAC,IAAI,KAAK,MAAM;oBACpB,IAAI,CAAC,IAAI,KAAK,aAAa;oBAC3B,IAAI,CAAC,IAAI,KAAK,SAAS,EACzB,CAAC;oBACC,IAAI,iDAAuB,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAA;oBAC7C,IAAI,qEAAiC,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAA;oBACvD,IAAI,mDAAwB,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAA;gBAClD,CAAC;qBAAM,CAAC;oBACJ,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;wBACzB,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;4BACxB,IAAI,mDAAwB,CACxB,QAAQ,CACX,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;wBACjC,CAAC;oBACL,CAAC,CAAC,CAAA;gBACN,CAAC;gBACD,wCAAwC;gBACxC,2CAA2C;gBAC3C,qCAAqC;gBAErC,4BAA4B;gBAC5B,OAAO,IAAI,iCAAe,CACtB,WAAW,EACX,QAAQ,EACR,IAAI,CAAC,OAAO,CACf,CAAA;YACL,CAAC,CAAC,CACL,CAAA;YACD,oDAAoD;YAEpD,iGAAiG;YACjG,0FAA0F;YAC1F,MAAM,iCAAiC,GAAG,SAAS,CAAC,MAAM,CACtD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,uBAAuB,CACjD,CAAA;YACD,IAAI,iCAAiC,CAAC,MAAM,KAAK,CAAC;gBAAE,OAAM;YAE1D,yCAAyC;YACzC,+EAA+E;YAC/E,+EAA+E;YAC/E,IAAI,wBAAwB,GAAG,KAAK,CAAA;YACpC,IAAI,CAAC;gBACD,yCAAyC;gBACzC,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;oBACnC,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,KAAK,MAAM;wBACpD,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,EACvD,CAAC;wBACC,yDAAyD;wBACzD,wBAAwB,GAAG,IAAI,CAAA;wBAC/B,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;oBACxC,CAAC;gBACL,CAAC;gBAED,8DAA8D;gBAC9D,kDAAkD;gBAClD,KAAK,MAAM,QAAQ,IAAI,iCAAiC,EAAE,CAAC;oBACvD,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAA;gBAC5B,CAAC;gBACD,qDAAqD;gBAErD,6CAA6C;gBAC7C,0BAA0B;gBAC1B,IAAI,wBAAwB,KAAK,IAAI;oBACjC,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;gBACzC,6BAA6B;YACjC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,+CAA+C;gBAC/C,IAAI,wBAAwB,EAAE,CAAC;oBAC3B,IAAI,CAAC;wBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;oBAC3C,CAAC;oBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;gBAC9B,CAAC;gBACD,MAAM,KAAK,CAAA;YACf,CAAC;QACL,CAAC;gBAAS,CAAC;YACP,WAAW,CAAC,IAAI,GAAG,kBAAkB,CAAA;YAErC,iDAAiD;YACjD,IAAI,CAAC,IAAI,CAAC,WAAW;gBAAE,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;QACtD,CAAC;IACL,CAAC;CACJ;AA3LD,sDA2LC", "file": "EntityPersistExecutor.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { SaveOptions } from \"../repository/SaveOptions\"\nimport { RemoveOptions } from \"../repository/RemoveOptions\"\nimport { MustBeEntityError } from \"../error/MustBeEntityError\"\nimport { SubjectExecutor } from \"./SubjectExecutor\"\nimport { CannotDetermineEntityError } from \"../error/CannotDetermineEntityError\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { Subject } from \"./Subject\"\nimport { OneToManySubjectBuilder } from \"./subject-builder/OneToManySubjectBuilder\"\nimport { OneToOneInverseSideSubjectBuilder } from \"./subject-builder/OneToOneInverseSideSubjectBuilder\"\nimport { ManyToManySubjectBuilder } from \"./subject-builder/ManyToManySubjectBuilder\"\nimport { SubjectDatabaseEntityLoader } from \"./SubjectDatabaseEntityLoader\"\nimport { CascadesSubjectBuilder } from \"./subject-builder/CascadesSubjectBuilder\"\nimport { OrmUtils } from \"../util/OrmUtils\"\n\n/**\n * Persists a single entity or multiple entities - saves or removes them.\n */\nexport class EntityPersistExecutor {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected connection: DataSource,\n        protected queryRunner: QueryRunner | undefined,\n        protected mode: \"save\" | \"remove\" | \"soft-remove\" | \"recover\",\n        protected target: Function | string | undefined,\n        protected entity: ObjectLiteral | ObjectLiteral[],\n        protected options?: SaveOptions & RemoveOptions,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes persistence operation ob given entity or entities.\n     */\n    async execute(): Promise<void> {\n        // check if entity we are going to save is valid and is an object\n        if (!this.entity || typeof this.entity !== \"object\")\n            return Promise.reject(new MustBeEntityError(this.mode, this.entity))\n\n        // we MUST call \"fake\" resolve here to make sure all properties of lazily loaded relations are resolved\n        await Promise.resolve()\n\n        // if query runner is already defined in this class, it means this entity manager was already created for a single connection\n        // if its not defined we create a new query runner - single connection where we'll execute all our operations\n        const queryRunner =\n            this.queryRunner || this.connection.createQueryRunner()\n\n        // save data in the query runner - this is useful functionality to share data from outside of the world\n        // with third classes - like subscribers and listener methods\n        const oldQueryRunnerData = queryRunner.data\n        if (this.options && this.options.data) {\n            queryRunner.data = this.options.data\n        }\n\n        try {\n            // collect all operate subjects\n            const entities: ObjectLiteral[] = Array.isArray(this.entity)\n                ? this.entity\n                : [this.entity]\n            const entitiesInChunks =\n                this.options && this.options.chunk && this.options.chunk > 0\n                    ? OrmUtils.chunk(entities, this.options.chunk)\n                    : [entities]\n\n            // console.time(\"building subject executors...\");\n            const executors = await Promise.all(\n                entitiesInChunks.map(async (entities) => {\n                    const subjects: Subject[] = []\n\n                    // create subjects for all entities we received for the persistence\n                    entities.forEach((entity) => {\n                        const entityTarget = this.target\n                            ? this.target\n                            : entity.constructor\n                        if (entityTarget === Object)\n                            throw new CannotDetermineEntityError(this.mode)\n\n                        const metadata = this.connection\n                            .getMetadata(entityTarget)\n                            .findInheritanceMetadata(entity)\n\n                        subjects.push(\n                            new Subject({\n                                metadata,\n                                entity: entity,\n                                canBeInserted: this.mode === \"save\",\n                                canBeUpdated: this.mode === \"save\",\n                                mustBeRemoved: this.mode === \"remove\",\n                                canBeSoftRemoved: this.mode === \"soft-remove\",\n                                canBeRecovered: this.mode === \"recover\",\n                            }),\n                        )\n                    })\n\n                    // console.time(\"building cascades...\");\n                    // go through each entity with metadata and create subjects and subjects by cascades for them\n                    const cascadesSubjectBuilder = new CascadesSubjectBuilder(\n                        subjects,\n                    )\n                    subjects.forEach((subject) => {\n                        // next step we build list of subjects we will operate with\n                        // these subjects are subjects that we need to insert or update alongside with main persisted entity\n                        cascadesSubjectBuilder.build(subject, this.mode)\n                    })\n                    // console.timeEnd(\"building cascades...\");\n\n                    // load database entities for all subjects we have\n                    // next step is to load database entities for all operate subjects\n                    // console.time(\"loading...\");\n                    await new SubjectDatabaseEntityLoader(\n                        queryRunner,\n                        subjects,\n                    ).load(this.mode)\n                    // console.timeEnd(\"loading...\");\n\n                    // console.time(\"other subjects...\");\n                    // build all related subjects and change maps\n                    if (\n                        this.mode === \"save\" ||\n                        this.mode === \"soft-remove\" ||\n                        this.mode === \"recover\"\n                    ) {\n                        new OneToManySubjectBuilder(subjects).build()\n                        new OneToOneInverseSideSubjectBuilder(subjects).build()\n                        new ManyToManySubjectBuilder(subjects).build()\n                    } else {\n                        subjects.forEach((subject) => {\n                            if (subject.mustBeRemoved) {\n                                new ManyToManySubjectBuilder(\n                                    subjects,\n                                ).buildForAllRemoval(subject)\n                            }\n                        })\n                    }\n                    // console.timeEnd(\"other subjects...\");\n                    // console.timeEnd(\"building subjects...\");\n                    // console.log(\"subjects\", subjects);\n\n                    // create a subject executor\n                    return new SubjectExecutor(\n                        queryRunner,\n                        subjects,\n                        this.options,\n                    )\n                }),\n            )\n            // console.timeEnd(\"building subject executors...\");\n\n            // make sure we have at least one executable operation before we create a transaction and proceed\n            // if we don't have operations it means we don't really need to update or remove something\n            const executorsWithExecutableOperations = executors.filter(\n                (executor) => executor.hasExecutableOperations,\n            )\n            if (executorsWithExecutableOperations.length === 0) return\n\n            // start execute queries in a transaction\n            // if transaction is already opened in this query runner then we don't touch it\n            // if its not opened yet then we open it here, and once we finish - we close it\n            let isTransactionStartedByUs = false\n            try {\n                // open transaction if its not opened yet\n                if (!queryRunner.isTransactionActive) {\n                    if (\n                        this.connection.driver.transactionSupport !== \"none\" &&\n                        (!this.options || this.options.transaction !== false)\n                    ) {\n                        // start transaction until it was not explicitly disabled\n                        isTransactionStartedByUs = true\n                        await queryRunner.startTransaction()\n                    }\n                }\n\n                // execute all persistence operations for all entities we have\n                // console.time(\"executing subject executors...\");\n                for (const executor of executorsWithExecutableOperations) {\n                    await executor.execute()\n                }\n                // console.timeEnd(\"executing subject executors...\");\n\n                // commit transaction if it was started by us\n                // console.time(\"commit\");\n                if (isTransactionStartedByUs === true)\n                    await queryRunner.commitTransaction()\n                // console.timeEnd(\"commit\");\n            } catch (error) {\n                // rollback transaction if it was started by us\n                if (isTransactionStartedByUs) {\n                    try {\n                        await queryRunner.rollbackTransaction()\n                    } catch (rollbackError) {}\n                }\n                throw error\n            }\n        } finally {\n            queryRunner.data = oldQueryRunnerData\n\n            // release query runner only if its created by us\n            if (!this.queryRunner) await queryRunner.release()\n        }\n    }\n}\n"], "sourceRoot": ".."}