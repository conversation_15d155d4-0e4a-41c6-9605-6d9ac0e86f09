{"name": "registry-auth-token", "version": "4.2.2", "description": "Get the auth token set for an npm registry (if any)", "main": "index.js", "scripts": {"test": "mocha", "posttest": "standard", "coverage": "istanbul cover _mocha"}, "repository": {"type": "git", "url": "git+ssh://**************/rexxars/registry-auth-token.git"}, "engines": {"node": ">=6.0.0"}, "keywords": ["npm", "conf", "config", "npmconf", "registry", "auth", "token", "authtoken"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/rexxars/registry-auth-token/issues"}, "homepage": "https://github.com/rexxars/registry-auth-token#readme", "dependencies": {"rc": "1.2.8"}, "devDependencies": {"istanbul": "^0.4.2", "mocha": "^6.1.4", "require-uncached": "^1.0.2", "standard": "^12.0.1"}, "standard": {"ignore": ["coverage/**"]}}