"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMiddleware = exports.ContextMiddlewareManager = void 0;
class ContextMiddlewareManager extends Array {
    /**
     * insert a middleware or middleware array to first
     * @param middleware
     */
    insertFirst(middleware) {
        if (Array.isArray(middleware)) {
            this.unshift(...middleware);
        }
        else {
            this.unshift(middleware);
        }
    }
    /**
     * insert a middleware or middleware array to last
     * @param middleware
     */
    insertLast(middleware) {
        if (Array.isArray(middleware)) {
            this.push(...middleware);
        }
        else {
            this.push(middleware);
        }
    }
    /**
     * insert a middleware or middleware array to after another middleware
     * @param middleware
     * @param idxOrBeforeMiddleware
     */
    insertBefore(middleware, idxOrBeforeMiddleware) {
        if (typeof idxOrBeforeMiddleware !== 'number') {
            idxOrBeforeMiddleware = this.findItemIndex(idxOrBeforeMiddleware);
        }
        if (Array.isArray(middleware)) {
            this.splice(idxOrBeforeMiddleware, 0, ...middleware);
        }
        else {
            this.splice(idxOrBeforeMiddleware, 0, middleware);
        }
    }
    /**
     * insert a middleware or middleware array to after another middleware
     * @param middleware
     * @param idxOrAfterMiddleware
     */
    insertAfter(middleware, idxOrAfterMiddleware) {
        if (typeof idxOrAfterMiddleware !== 'number') {
            idxOrAfterMiddleware = this.findItemIndex(idxOrAfterMiddleware);
        }
        if (Array.isArray(middleware)) {
            this.splice(idxOrAfterMiddleware + 1, 0, ...middleware);
        }
        else {
            this.splice(idxOrAfterMiddleware + 1, 0, middleware);
        }
    }
    /**
     * move a middleware after another middleware
     * @param middlewareOrName
     * @param afterMiddleware
     */
    findAndInsertAfter(middlewareOrName, afterMiddleware) {
        middlewareOrName = this.findItem(middlewareOrName);
        afterMiddleware = this.findItem(afterMiddleware);
        if (!middlewareOrName ||
            !afterMiddleware ||
            middlewareOrName === afterMiddleware) {
            return;
        }
        if (afterMiddleware) {
            const mw = this.remove(middlewareOrName);
            if (mw) {
                this.insertAfter(mw, afterMiddleware);
            }
        }
    }
    /**
     * move a middleware before another middleware
     * @param middlewareOrName
     * @param beforeMiddleware
     */
    findAndInsertBefore(middlewareOrName, beforeMiddleware) {
        middlewareOrName = this.findItem(middlewareOrName);
        beforeMiddleware = this.findItem(beforeMiddleware);
        if (!middlewareOrName ||
            !beforeMiddleware ||
            middlewareOrName === beforeMiddleware) {
            return;
        }
        if (beforeMiddleware) {
            const mw = this.remove(middlewareOrName);
            if (mw) {
                this.insertBefore(mw, beforeMiddleware);
            }
        }
    }
    /**
     * find middleware and move to first
     * @param middlewareOrName
     */
    findAndInsertFirst(middlewareOrName) {
        const mw = this.remove(middlewareOrName);
        if (mw) {
            this.insertFirst(mw);
        }
    }
    /**
     * find middleware and move to last
     * @param middlewareOrName
     */
    findAndInsertLast(middlewareOrName) {
        const mw = this.remove(middlewareOrName);
        if (mw) {
            this.insertLast(mw);
        }
    }
    /**
     * find a middleware and return index
     * @param middlewareOrName
     */
    findItemIndex(middlewareOrName) {
        if (typeof middlewareOrName === 'number') {
            return middlewareOrName;
        }
        else if (typeof middlewareOrName === 'string') {
            return this.findIndex(item => this.getMiddlewareName(item) === middlewareOrName);
        }
        else {
            return this.findIndex(item => item === middlewareOrName);
        }
    }
    findItem(middlewareOrName) {
        if (typeof middlewareOrName === 'number') {
            if (middlewareOrName >= 0 && middlewareOrName <= this.length - 1) {
                return this[middlewareOrName];
            }
        }
        else if (typeof middlewareOrName === 'string') {
            return this.find(item => this.getMiddlewareName(item) === middlewareOrName);
        }
        else {
            return middlewareOrName;
        }
    }
    /**
     * get name from middleware
     * @param middleware
     */
    getMiddlewareName(middleware) {
        var _a, _b, _c;
        if (middleware['middleware']) {
            return ((_a = middleware.name) !== null && _a !== void 0 ? _a : this.getMiddlewareName(middleware['middleware']));
        }
        return ((_c = (_b = (middleware.getName && middleware.getName())) !== null && _b !== void 0 ? _b : middleware._name) !== null && _c !== void 0 ? _c : middleware.name);
    }
    /**
     * remove a middleware
     * @param middlewareOrNameOrIdx
     */
    remove(middlewareOrNameOrIdx) {
        if (typeof middlewareOrNameOrIdx === 'number' &&
            middlewareOrNameOrIdx !== -1) {
            return this.splice(middlewareOrNameOrIdx, 1)[0];
        }
        else {
            const idx = this.findItemIndex(middlewareOrNameOrIdx);
            if (idx !== -1) {
                return this.splice(idx, 1)[0];
            }
        }
    }
    push(...items) {
        items.forEach(item => {
            if (typeof item !== 'string' && !this.getMiddlewareName(item)) {
                item._name = 'anonymous';
            }
        });
        return super.push(...items);
    }
    unshift(...items) {
        items.forEach(item => {
            if (typeof item !== 'string' && !this.getMiddlewareName(item)) {
                item._name = 'anonymous';
            }
        });
        return super.unshift(...items);
    }
    /**
     * get middleware name list
     */
    getNames() {
        return this.map(item => {
            return this.getMiddlewareName(item);
        });
    }
}
exports.ContextMiddlewareManager = ContextMiddlewareManager;
/**
 * wrap a middleware with options and composition a new middleware
 */
function createMiddleware(middleware, options, name) {
    return {
        middleware,
        options,
        name,
    };
}
exports.createMiddleware = createMiddleware;
//# sourceMappingURL=middlewareManager.js.map