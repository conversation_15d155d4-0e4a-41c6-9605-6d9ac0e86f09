{"version": 3, "sources": ["../browser/src/find-options/operator/JsonContains.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,YAAY,CAE1B,KAA0B;IACxB,OAAO,IAAI,YAAY,CAAC,cAAc,EAAE,KAAY,CAAC,CAAA;AACzD,CAAC", "file": "JsonContains.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: JsonContains({...}) }\n */\nexport function JsonContains<\n    T extends Record<string | number | symbol, unknown>,\n>(value: T | FindOperator<T>): FindOperator<any> {\n    return new FindOperator(\"jsonContains\", value as any)\n}\n"], "sourceRoot": "../.."}