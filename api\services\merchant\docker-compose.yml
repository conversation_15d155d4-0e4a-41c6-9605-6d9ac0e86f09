# 商户微服务独立部署配置
version: "3.8"

services:
  # 商户微服务
  merchant-service:
    build: .
    container_name: merchant-service
    ports:
      - "9801:9801"
    environment:
      - NODE_ENV=production
      - DB_HOST=merchant-db
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=123456
      - DB_DATABASE=merchant_service_db
      - REDIS_HOST=merchant-redis
      - REDIS_PORT=6379
      - REDIS_DB=11
    depends_on:
      - merchant-db
      - merchant-redis
    networks:
      - merchant-network
    restart: unless-stopped

  # 商户微服务专用数据库
  merchant-db:
    image: mysql:8.0
    container_name: merchant-db
    command:
      --default-authentication-plugin=mysql_native_password
      --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
      --group_concat_max_len=102400
    restart: unless-stopped
    volumes:
      - ./data/mysql/:/var/lib/mysql/
      - ./migrate.sql:/docker-entrypoint-initdb.d/migrate.sql
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: "123456"
      MYSQL_DATABASE: "merchant_service_db"
      MYSQL_USER: "merchant"
      MYSQL_PASSWORD: "123456"
    ports:
      - "3307:3306"
    networks:
      - merchant-network

  # 商户微服务专用Redis
  merchant-redis:
    image: redis:7-alpine
    container_name: merchant-redis
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    volumes:
      - ./data/redis/:/data/
    ports:
      - "6380:6379"
    networks:
      - merchant-network

networks:
  merchant-network:
    driver: bridge

volumes:
  merchant-mysql-data:
  merchant-redis-data: 