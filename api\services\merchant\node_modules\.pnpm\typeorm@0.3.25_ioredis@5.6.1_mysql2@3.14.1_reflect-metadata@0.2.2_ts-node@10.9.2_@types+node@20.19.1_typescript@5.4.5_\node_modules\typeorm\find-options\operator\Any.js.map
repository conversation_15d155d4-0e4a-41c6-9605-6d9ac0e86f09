{"version": 3, "sources": ["../../src/find-options/operator/Any.ts"], "names": [], "mappings": ";;AAMA,kBAEC;AARD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,GAAG,CAAI,KAAqC;IACxD,OAAO,IAAI,2BAAY,CAAC,KAAK,EAAE,KAAY,CAAC,CAAA;AAChD,CAAC", "file": "Any.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Any([...]) }\n */\nexport function Any<T>(value: readonly T[] | FindOperator<T>): FindOperator<T> {\n    return new FindOperator(\"any\", value as any)\n}\n"], "sourceRoot": "../.."}