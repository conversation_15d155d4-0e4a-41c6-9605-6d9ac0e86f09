{"name": "has-to-string-tag-x", "version": "1.4.1", "description": "Tests if ES6 @@toStringTag is supported.", "homepage": "https://github.com/Xotic750/has-to-string-tag-x", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "copyright": "Copyright (c) 2015-2017", "keywords": ["ES6", "hasToStringTag", "module", "javascript", "nodejs", "browser"], "main": "index.js", "engines": {"node": "*"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Xotic750/has-to-string-tag-x.git"}, "bugs": {"url": "https://github.com/Xotic750/has-to-string-tag-x/issues"}, "dependencies": {"has-symbol-support-x": "^1.4.1"}, "devDependencies": {"@xotic750/eslint-config-standard-x": "^2.2.1", "browserify": "^14.4.0", "browserify-derequire": "^0.9.4", "cross-env": "^5.0.1", "es5-shim": "^4.5.9", "es6-shim": "^0.35.3", "es7-shim": "^6.0.0", "eslint": "^4.2.0", "eslint-plugin-compat": "^1.0.4", "eslint-plugin-css-modules": "^2.7.2", "eslint-plugin-eslint-comments": "^1.0.2", "eslint-plugin-jsdoc": "^3.1.1", "eslint-plugin-json": "^1.2.0", "eslint-plugin-no-use-extend-native": "^0.3.12", "husky": "^0.13.4", "jasmine-node": "^1.14.5", "jsdoc-to-markdown": "^3.0.0", "json3": "^3.3.2", "make-jasmine-spec-runner-html": "^1.3.0", "ncp": "^2.0.0", "nodemon": "^1.11.0", "nsp": "^2.6.3", "parallelshell": "^3.0.1", "replace-x": "^1.5.0", "rimraf": "^2.6.1", "serve": "^6.0.2", "uglify-js": "^3.0.24"}, "scripts": {"clean": "rimraf README.md lib/*", "clean:jasmine": "rimraf tests/index.html tests/run.js", "clean:all": "npm run clean:jasmine && npm run clean", "build": "npm run clean && npm run lint && npm run browserify && npm run uglify && npm run docs && npm test && npm run security", "build:jasmine": "npm run clean:jasmine && make-jasmine-spec-runner-html", "build:setver": "replace-x \" @version .*\" \" @version $(node -p -e \"require('./package.json').version\")\" index.js", "build:name": "replace-x \" @module .*\" \" @module $(node -p -e \"require('./package.json').name\")\" index.js", "build:description": "replace-x \" @file .*\" \" @file $(node -p -e \"require('./package.json').description\")\" index.js", "build:replace": "npm run build:setver && npm run build:name && npm run build:description", "production": "npm run clean:all && npm run build:jasmine && npm run build:replace && npm run build", "start": "parallelshell \"serve\" \"nodemon --watch index.js --exec 'npm run build'\"", "docs:name": "replace-x \"@{PACKAGE-NAME}\" \"$(node -p -e \"require('./package.json').name\")\" README.md", "docs:badges": "ncp badges.html README.md && npm run docs:name", "docs": "npm run docs:badges && jsdoc2md --name-format --example-lang js index.js >> README.md", "lint": "eslint *.js tests/spec/*.js", "lint-fix": "npm run lint -- --fix", "security": "nsp check", "test": "jasmine-node --matchall tests/spec/", "browserify": "browserify -p browserify-derequire -e index.js -o lib/has-to-string-tag-x.js -u 'crypto' -s returnExports", "uglify": "uglifyjs lib/has-to-string-tag-x.js -o lib/has-to-string-tag-x.min.js --config-file .uglifyjsrc.json", "precommit": "npm run production", "prepush": "npm run production"}}