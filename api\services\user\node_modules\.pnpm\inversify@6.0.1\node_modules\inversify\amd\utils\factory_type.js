define(["require", "exports"], function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.FactoryType = void 0;
    var FactoryType;
    (function (FactoryType) {
        FactoryType["DynamicValue"] = "toDynamicValue";
        FactoryType["Factory"] = "toFactory";
        FactoryType["Provider"] = "toProvider";
    })(FactoryType = exports.FactoryType || (exports.FactoryType = {}));
});
//# sourceMappingURL=factory_type.js.map