/**
 * 菜单模板配置
 * 用于快速创建常用的菜单项
 */

export interface MenuTemplate {
  name: string
  path: string
  component: string
  icon: string
  parentPath?: string
  permissions?: string[]
  description: string
}

/**
 * 结算模块菜单模板
 */
export const settlementMenuTemplates: MenuTemplate[] = [
  {
    name: '风险与合规',
    path: '/settlement/risk-compliance',
    component: '/settlement/risk-compliance',
    icon: 'icon-shield',
    parentPath: '/settlement',
    permissions: [
      'settlement:risk:view',
      'settlement:risk:edit',
      'settlement:compliance:view',
      'settlement:compliance:edit'
    ],
    description: '管理平台风险控制策略和合规要求'
  },
  {
    name: '国际化管理',
    path: '/settlement/internationalization',
    component: '/settlement/internationalization',
    icon: 'icon-global',
    parentPath: '/settlement',
    permissions: [
      'settlement:currency:view',
      'settlement:currency:edit',
      'settlement:region:view',
      'settlement:region:edit',
      'settlement:exchange:view',
      'settlement:exchange:refresh'
    ],
    description: '管理多币种结算和国际化配置'
  }
]

/**
 * 权限按钮模板
 */
export const permissionButtonTemplates = {
  'settlement:risk:view': '查看风险设置',
  'settlement:risk:edit': '修改风险设置',
  'settlement:compliance:view': '查看合规设置',
  'settlement:compliance:edit': '修改合规设置',
  'settlement:currency:view': '查看币种设置',
  'settlement:currency:edit': '修改币种设置',
  'settlement:region:view': '查看地区设置',
  'settlement:region:edit': '修改地区设置',
  'settlement:exchange:view': '查看汇率信息',
  'settlement:exchange:refresh': '刷新汇率'
}

/**
 * 生成菜单数据
 */
export function generateMenuData(template: MenuTemplate, parentId: number = 0) {
  return {
    name: template.name,
    path: template.path,
    component: template.component,
    parentId: parentId,
    meta: {
      title: template.name,
      icon: template.icon,
      keepAlive: true,
      isHide: false,
      isMenu: true,
      sort: 1
    },
    permissions: template.permissions || []
  }
}

/**
 * 生成权限按钮数据
 */
export function generatePermissionButtons(permissions: string[], parentMenuId: number) {
  return permissions.map((perm, index) => ({
    name: permissionButtonTemplates[perm] || perm,
    parentId: parentMenuId,
    perms: perm,
    type: 2, // 按钮类型
    orderNum: index + 1,
    isShow: false // 按钮不在菜单中显示
  }))
}

/**
 * 菜单创建助手
 */
export class MenuCreationHelper {
  /**
   * 创建结算模块的完整菜单结构
   */
  static async createSettlementMenus(menuStore: any) {
    try {
      // 首先确保财务结算父菜单存在
      const settlementParent = await this.ensureParentMenu(menuStore)
      
      // 创建子菜单
      for (const template of settlementMenuTemplates) {
        const menuData = generateMenuData(template, settlementParent.id)
        const newMenu = await menuStore.addMenu(menuData, settlementParent.id)
        
        // 创建权限按钮
        if (template.permissions && template.permissions.length > 0) {
          const buttonData = generatePermissionButtons(template.permissions, newMenu.id)
          for (const button of buttonData) {
            await menuStore.addMenu(button, newMenu.id)
          }
        }
      }
      
      return {
        success: true,
        message: '结算模块菜单创建成功'
      }
    } catch (error) {
      return {
        success: false,
        message: `菜单创建失败: ${error.message}`
      }
    }
  }
  
  /**
   * 确保父菜单存在
   */
  static async ensureParentMenu(menuStore: any) {
    // 查找现有的财务结算菜单
    const existingMenus = await menuStore.getMenuList()
    let settlementMenu = existingMenus.find((menu: any) => 
      menu.path === '/settlement' || menu.name === '财务结算'
    )
    
    // 如果不存在，创建父菜单
    if (!settlementMenu) {
      const parentMenuData = {
        name: '财务结算',
        path: '/settlement',
        component: '',
        parentId: 0,
        meta: {
          title: '财务结算',
          icon: 'icon-money',
          keepAlive: false,
          isHide: false,
          isMenu: false, // 目录类型
          sort: 5
        }
      }
      settlementMenu = await menuStore.addMenu(parentMenuData, 0)
    }
    
    return settlementMenu
  }
}

/**
 * 菜单配置预设
 */
export const menuPresets = {
  // 财务结算目录
  settlementDirectory: {
    name: '财务结算',
    path: '/settlement',
    icon: 'icon-money',
    type: 'directory',
    description: '财务结算管理模块'
  },
  
  // 风险与合规菜单
  riskCompliance: {
    name: '风险与合规',
    path: '/settlement/risk-compliance',
    icon: 'icon-shield',
    type: 'menu',
    parentPath: '/settlement',
    description: '风险控制和合规管理'
  },
  
  // 国际化管理菜单
  internationalization: {
    name: '国际化管理',
    path: '/settlement/internationalization',
    icon: 'icon-global',
    type: 'menu',
    parentPath: '/settlement',
    description: '多币种和国际化配置'
  }
}

/**
 * 快速填充表单的辅助函数
 */
export function fillMenuForm(preset: any, formRef: any) {
  if (!formRef || !preset) return
  
  Object.keys(preset).forEach(key => {
    if (formRef[key] !== undefined) {
      formRef[key] = preset[key]
    }
  })
}
