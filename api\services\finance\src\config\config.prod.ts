import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9803,
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: process.env.DB_HOST || '127.0.0.1',
        port: parseInt(process.env.DB_PORT) || 3306,
        username: process.env.DB_USERNAME || 'root',
        password: process.env.DB_PASSWORD,
        database: process.env.DB_DATABASE || 'finance_service_db',
        synchronize: false, // 生产环境不自动同步
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ['dist/modules/finance/entity/**/*.js'],
      },
    },
  },
  cool: {
    redis: {
      cluster: true,
      nodes: [
        { host: '*************', port: 6379 },
        { host: '*************', port: 6379 },
        { host: '*************', port: 6379 },
        // 更多节点可按需添加
      ],
      password: 'your_password', // 如有密码
    },
    eps: false,
    initDB: false,
    initMenu: false,
  } as CoolConfig,
  moleculer: {
    namespace: 'cool',
    nodeID: 'finance-service',
    transporter: process.env.MOLECULER_TRANSPORTER || 'NATS',
  },
} as MidwayConfig; 