"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SINGLETON_CONTAINER_CTX = exports.IGNORE_PATTERN = exports.DEFAULT_PATTERN = exports.ASYNC_CONTEXT_MANAGER_KEY = exports.ASYNC_CONTEXT_KEY = exports.REQUEST_CTX_LOGGER_CACHE_KEY = exports.HTTP_SERVER_KEY = exports.CONTAINER_OBJ_SCOPE = exports.REQUEST_OBJ_CTX_KEY = exports.REQUEST_CTX_KEY = exports.MIDWAY_LOGGER_WRITEABLE_DIR = exports.FUNCTION_INJECT_KEY = exports.KEYS = void 0;
/**
 * 静态参数
 *
 */
exports.KEYS = {
    OBJECTS_ELEMENT: 'objects',
    OBJECT_ELEMENT: 'object',
    IMPORT_ELEMENT: 'import',
    PROPERTY_ELEMENT: 'property',
    LIST_ELEMENT: 'list',
    MAP_ELEMENT: 'map',
    ENTRY_ELEMENT: 'entry',
    VALUE_ELEMENT: 'value',
    PROPS_ELEMENT: 'props',
    PROP_ELEMENT: 'prop',
    SET_ELEMENT: 'set',
    CONSTRUCTOR_ARG_ELEMENT: 'constructor-arg',
    REF_ELEMENT: 'ref',
    JSON_ELEMENT: 'json',
    CONFIGURATION_ELEMENT: 'configuration',
    ID_ATTRIBUTE: 'id',
    PATH_ATTRIBUTE: 'path',
    DIRECT_ATTRIBUTE: 'direct',
    AUTOWIRE_ATTRIBUTE: 'autowire',
    ASYNC_ATTRIBUTE: 'async',
    NAME_ATTRIBUTE: 'name',
    REF_ATTRIBUTE: 'ref',
    KEY_ATTRIBUTE: 'key',
    VALUE_ATTRIBUTE: 'value',
    TYPE_ATTRIBUTE: 'type',
    EXTERNAL_ATTRIBUTE: 'external',
    OBJECT_ATTRIBUTE: 'object',
    RESOURCE_ATTRIBUTE: 'resource',
    SCOPE_ATTRIBUTE: 'scope',
    ASPECT_ELEMENT: 'aspect',
    AROUND_ELEMENT: 'around',
    EXPRESSION_ATTRIBUTE: 'expression',
    EXECUTE_ATTRIBUTE: 'execute',
};
exports.FUNCTION_INJECT_KEY = 'midway:function_inject_key';
exports.MIDWAY_LOGGER_WRITEABLE_DIR = 'MIDWAY_LOGGER_WRITEABLE_DIR';
exports.REQUEST_CTX_KEY = 'ctx';
exports.REQUEST_OBJ_CTX_KEY = '_req_ctx';
exports.CONTAINER_OBJ_SCOPE = '_obj_scope';
exports.HTTP_SERVER_KEY = '_midway_http_server';
exports.REQUEST_CTX_LOGGER_CACHE_KEY = '_midway_ctx_logger_cache';
exports.ASYNC_CONTEXT_KEY = Symbol('ASYNC_CONTEXT_KEY');
exports.ASYNC_CONTEXT_MANAGER_KEY = 'MIDWAY_ASYNC_CONTEXT_MANAGER_KEY';
exports.DEFAULT_PATTERN = [
    '**/**.ts',
    '**/**.js',
    '**/**.mts',
    '**/**.mjs',
    '**/**.cts',
    '**/**.cjs',
];
exports.IGNORE_PATTERN = ['**/**.d.ts', '**/**.d.mts', '**/**.d.cts'];
exports.SINGLETON_CONTAINER_CTX = { _MAIN_CTX_: true };
//# sourceMappingURL=constants.js.map