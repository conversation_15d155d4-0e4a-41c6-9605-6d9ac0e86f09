{"version": 3, "file": "shareReplay.js", "sources": ["../../../src/internal/operators/shareReplay.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAiEjD,MAAM,UAAU,WAAW,CACzB,kBAA+C,EAC/C,UAAmB,EACnB,SAAyB;IAEzB,IAAI,MAAyB,CAAC;IAC9B,IAAI,kBAAkB,IAAI,OAAO,kBAAkB,KAAK,QAAQ,EAAE;QAChE,MAAM,GAAG,kBAAuC,CAAC;KAClD;SAAM;QACL,MAAM,GAAG;YACP,UAAU,EAAE,kBAAwC;YACpD,UAAU,YAAA;YACV,QAAQ,EAAE,KAAK;YACf,SAAS,WAAA;SACV,CAAC;KACH;IACD,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,EAAxC,CAAwC,CAAC;AAC7E,CAAC;AAED,SAAS,mBAAmB,CAAI,EAKZ;QAJlB,kBAAqC,EAArC,0DAAqC,EACrC,kBAAqC,EAArC,0DAAqC,EACrC,yBAAqB,EACrB,wBAAS;IAET,IAAI,OAAqC,CAAC;IAC1C,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,IAAI,YAAsC,CAAC;IAC3C,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,UAAU,GAAG,KAAK,CAAC;IAEvB,OAAO,SAAS,oBAAoB,CAElC,MAAqB;QAErB,QAAQ,EAAE,CAAC;QACX,IAAI,QAAsB,CAAC;QAC3B,IAAI,CAAC,OAAO,IAAI,QAAQ,EAAE;YACxB,QAAQ,GAAG,KAAK,CAAC;YACjB,OAAO,GAAG,IAAI,aAAa,CAAI,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;YAClE,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACnC,YAAY,GAAG,MAAM,CAAC,SAAS,CAAC;gBAC9B,IAAI,YAAC,KAAK;oBACR,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtB,CAAC;gBACD,KAAK,YAAC,GAAG;oBACP,QAAQ,GAAG,IAAI,CAAC;oBAChB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACrB,CAAC;gBACD,QAAQ;oBACN,UAAU,GAAG,IAAI,CAAC;oBAClB,YAAY,GAAG,SAAS,CAAC;oBACzB,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,CAAC;aACF,CAAC,CAAC;YAMH,IAAI,UAAU,EAAE;gBACd,YAAY,GAAG,SAAS,CAAC;aAC1B;SACF;aAAM;YACL,QAAQ,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;SACpC;QAED,IAAI,CAAC,GAAG,CAAC;YACP,QAAQ,EAAE,CAAC;YACX,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,QAAQ,GAAG,SAAS,CAAC;YACrB,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,QAAQ,KAAK,CAAC,EAAE;gBAChE,YAAY,CAAC,WAAW,EAAE,CAAC;gBAC3B,YAAY,GAAG,SAAS,CAAC;gBACzB,OAAO,GAAG,SAAS,CAAC;aACrB;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC"}