@echo off
chcp 65001 > nul

echo 正在启动商户微服务...

REM 检查MySQL是否可连接
echo 检查数据库连接...
mysql -h127.0.0.1 -uroot -pwap.336101 -e "SELECT 1;" > nul 2>&1
if %errorlevel% equ 0 (
    echo 数据库连接成功
) else (
    echo 数据库连接失败，请检查MySQL服务是否启动
    pause
    exit /b 1
)

REM 检查数据库是否已初始化
echo 检查数据库是否已初始化...
mysql -h127.0.0.1 -uroot -pwap.336101 -e "SHOW DATABASES LIKE 'merchant_service_db';" | findstr merchant_service_db > nul
if %errorlevel% neq 0 (
    echo 数据库不存在，正在初始化...
    mysql -h127.0.0.1 -uroot -pwap.336101 < init_merchant_data.sql
    echo 数据库初始化完成
) else (
    echo 数据库已存在，跳过初始化
)

REM 检查Redis是否可连接
echo 检查Redis连接...
redis-cli -h 127.0.0.1 -p 6379 ping > nul 2>&1
if %errorlevel% equ 0 (
    echo Redis连接成功
) else (
    echo Redis连接失败，请检查Redis服务是否启动
    pause
    exit /b 1
)

REM 安装依赖
echo 安装依赖包...
call npm install

REM 启动服务
echo 启动商户微服务...
call npm run dev

pause 