{"name": "@midwayjs/koa", "version": "3.15.0", "description": "Midway Web Framework for KOA", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test"}, "keywords": ["midway", "IoC", "web", "scene", "koa"], "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "license": "MIT", "devDependencies": {"@midwayjs/mock": "^3.15.0", "@types/koa-router": "7.4.8", "fs-extra": "11.2.0"}, "dependencies": {"@koa/router": "^12.0.0", "@midwayjs/cookies": "^1.0.2", "@midwayjs/core": "^3.15.0", "@midwayjs/session": "^3.15.0", "@types/koa": "2.14.0", "koa": "2.15.0", "koa-bodyparser": "4.4.1"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "engines": {"node": ">=12"}, "gitHead": "be0a091f940aa60965d9fabfbdcbf0fe2830e9c4"}