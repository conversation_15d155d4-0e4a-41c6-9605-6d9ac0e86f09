{"name": "supertest", "description": "SuperAgent driven library for testing HTTP servers", "version": "6.3.3", "author": "<PERSON><PERSON>", "contributors": [], "dependencies": {"methods": "^1.1.2", "superagent": "^8.0.5"}, "devDependencies": {"body-parser": "^1.20.1", "cookie-parser": "^1.4.6", "eslint": "^8.29.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.26.0", "express": "^4.18.2", "mocha": "^10.1.0", "nock": "^13.2.9", "nyc": "^15.1.0", "proxyquire": "^2.1.3", "should": "^13.2.3"}, "engines": {"node": ">=6.4.0"}, "files": ["index.js", "lib"], "keywords": ["bdd", "http", "request", "superagent", "tdd", "test", "testing"], "license": "MIT", "main": "index.js", "repository": {"type": "git", "url": "https://github.com/visionmedia/supertest.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov > coverage.lcov", "lint": "eslint lib/**/*.js test/**/*.js index.js", "lint:fix": "eslint --fix lib/**/*.js test/**/*.js index.js", "pretest": "npm run lint --if-present", "test": "nyc --reporter=html --reporter=text mocha --exit --require should --reporter spec --check-leaks"}}