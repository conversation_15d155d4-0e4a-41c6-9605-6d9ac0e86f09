# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.1.0](https://github.com/midwayjs/cli/compare/serverless-v1.2.39...serverless-v2.1.0) (2023-05-30)



## 2.0.14 (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))



## 2.0.11 (2023-02-02)



## 2.0.11-beta.1 (2023-02-01)


### Bug Fixes

* tsconfig 引用路径解析问题 ([#319](https://github.com/midwayjs/cli/issues/319)) ([cedab2d](https://github.com/midwayjs/cli/commit/cedab2d51585c56fcd52ab360ec64f073b341b47))



## 2.0.6 (2022-12-09)



## 2.0.5 (2022-12-01)



## 2.0.4 (2022-11-23)



## 2.0.3 (2022-11-21)



## 2.0.2 (2022-11-17)



## 2.0.1 (2022-10-28)



## 2.0.1-beta.1 (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))



# 2.0.0 (2022-10-13)



# 2.0.0-beta.1 (2022-10-10)


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))



## 1.3.14 (2022-10-10)



## 1.3.13 (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))



## 1.3.13-beta.3 (2022-09-07)



## 1.3.8 (2022-07-21)


### Features

* new faas & check ([#287](https://github.com/midwayjs/cli/issues/287)) ([2d62fdc](https://github.com/midwayjs/cli/commit/2d62fdc4d91d87ff60c92a0fd9060301e10a3fa5))



## 1.3.5 (2022-05-25)



## 1.3.5-beta.3 (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))



## 1.3.4 (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))



## 1.3.3 (2022-04-20)



## 1.3.1 (2022-03-10)



## 1.3.1-beta.1 (2022-03-10)



## 1.2.97 (2022-01-24)



## 1.2.95 (2022-01-20)


### Features

* support hooks 3.0 ([#254](https://github.com/midwayjs/cli/issues/254)) ([eb50b72](https://github.com/midwayjs/cli/commit/eb50b7287734cf965e744466ea725dde78525e3d))



## 1.2.94 (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))



## 1.2.93 (2021-12-29)



## 1.2.92 (2021-12-03)


### Bug Fixes

* using https://registry.npmmirror.com instead https://registry.np… ([#237](https://github.com/midwayjs/cli/issues/237)) ([56ce275](https://github.com/midwayjs/cli/commit/56ce275d98db601944892229e0645fcbf2bdb602))



## 1.2.91 (2021-11-26)



## 1.2.90 (2021-11-16)


### Bug Fixes

* remvoe hooks core ([#228](https://github.com/midwayjs/cli/issues/228)) ([d0c3fcc](https://github.com/midwayjs/cli/commit/d0c3fccbab299b15284f69f7574e84aec5cb7c5e))



## 1.2.89 (2021-11-16)


### Bug Fixes

* package check error ignore tsconfig error ([#226](https://github.com/midwayjs/cli/issues/226)) ([5d5468c](https://github.com/midwayjs/cli/commit/5d5468c7ac4fb45c73037fcd2ccc45a3494301b1))



## 1.2.86 (2021-11-04)



## 1.2.85 (2021-10-21)



## 1.2.84 (2021-09-27)



## 1.2.82 (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))



## 1.2.80 (2021-08-16)


### Features

* support fc authType、 timeout、 initTimeout ([#178](https://github.com/midwayjs/cli/issues/178)) ([baca74c](https://github.com/midwayjs/cli/commit/baca74c971bd916803594c7900769a291dc5cb6f))



## 1.2.79 (2021-08-11)



## 1.2.78 (2021-08-09)


### Features

* support wechat cloud function ([#153](https://github.com/midwayjs/cli/issues/153)) ([f64744c](https://github.com/midwayjs/cli/commit/f64744c537f4050611705691bbffc240d283ae3c))



## 1.2.76 (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))



## 1.2.74 (2021-07-06)



## 1.2.73 (2021-06-25)



## 1.2.73-beta.1 (2021-06-25)


### Bug Fixes

* midwayjs/midway[#1101](https://github.com/midwayjs/cli/issues/1101) ([#131](https://github.com/midwayjs/cli/issues/131)) ([56c9332](https://github.com/midwayjs/cli/commit/56c9332395d99faf14022a6d6340e883ec3ff26c))



## 1.2.72 (2021-06-18)


### Bug Fixes

* copy js file when execute build command ([#120](https://github.com/midwayjs/cli/issues/120)) ([404aa40](https://github.com/midwayjs/cli/commit/404aa4074e3866a0914d918e96a922bbb267bad9))
* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))



## 1.2.70 (2021-06-02)



## 1.2.69 (2021-06-01)



## 1.2.68 (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))



## 1.2.65 (2021-04-23)



## 1.2.63 (2021-04-16)



## 1.2.61 (2021-04-12)



## 1.2.55 (2021-03-23)



## 1.2.54 (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))



## 1.2.51 (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))



## 1.2.50 (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))



## 1.2.48 (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))



## 1.2.46 (2021-03-05)



## 1.2.45 (2021-03-04)



## 1.2.41 (2021-02-17)



## 1.2.40 (2021-02-03)



## 1.2.39-beta.5 (2021-02-03)





## [2.0.14](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14) (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))





## [2.0.14-beta.4](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.4) (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))





## [2.0.14-beta.3](https://github.com/midwayjs/cli/compare/v2.0.14-beta.2...v2.0.14-beta.3) (2023-02-15)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.14-beta.2](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.2) (2023-02-14)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([7001121](https://github.com/midwayjs/cli/commit/7001121f30d0e1d40bde672bba1b6d0a264ed2e3))





## [2.0.11](https://github.com/midwayjs/cli/compare/v2.0.11-beta.1...v2.0.11) (2023-02-02)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.11-beta.2](https://github.com/midwayjs/cli/compare/v2.0.11-beta.1...v2.0.11-beta.2) (2023-02-02)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.11-beta.1](https://github.com/midwayjs/cli/compare/v2.0.10...v2.0.11-beta.1) (2023-02-01)


### Bug Fixes

* tsconfig 引用路径解析问题 ([#319](https://github.com/midwayjs/cli/issues/319)) ([cedab2d](https://github.com/midwayjs/cli/commit/cedab2d51585c56fcd52ab360ec64f073b341b47))





## [2.0.6](https://github.com/midwayjs/cli/compare/v2.0.6-beta.3...v2.0.6) (2022-12-09)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.4](https://github.com/midwayjs/cli/compare/v2.0.3...v2.0.4) (2022-11-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.3](https://github.com/midwayjs/cli/compare/v2.0.2...v2.0.3) (2022-11-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.2](https://github.com/midwayjs/cli/compare/v2.0.1...v2.0.2) (2022-11-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.1](https://github.com/midwayjs/cli/compare/v2.0.1-beta.1...v2.0.1) (2022-10-28)

**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [2.0.1-beta.1](https://github.com/midwayjs/cli/compare/v2.0.0...v2.0.1-beta.1) (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))





# [2.0.0](https://github.com/midwayjs/cli/compare/v1.3.15...v2.0.0) (2022-10-13)



# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))





# [2.0.0](https://github.com/midwayjs/cli/compare/v2.0.0-beta.1...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)


### Features

* build command remove mwcc ([1a254ed](https://github.com/midwayjs/cli/commit/1a254eded97edaa16f77e78cdeb98b4fb5100ec2))
* remove mwcc ([5e78f46](https://github.com/midwayjs/cli/commit/5e78f46c0b2a3aee8d13db680568e0a8907aaee2))





## [1.3.14](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.14-beta.10](https://github.com/midwayjs/cli/compare/v1.3.14-beta.9...v1.3.14-beta.10) (2022-09-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.14-beta.9](https://github.com/midwayjs/cli/compare/v1.3.14-beta.8...v1.3.14-beta.9) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.14-beta.8](https://github.com/midwayjs/cli/compare/v1.3.14-beta.7...v1.3.14-beta.8) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.14-beta.7](https://github.com/midwayjs/cli/compare/v1.3.14-beta.6...v1.3.14-beta.7) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.14-beta.6](https://github.com/midwayjs/cli/compare/v1.3.14-beta.5...v1.3.14-beta.6) (2022-09-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13-beta.4](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13-beta.4) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.13-beta.3](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.13-beta.3) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.13-beta.2](https://github.com/midwayjs/cli/compare/v1.3.13-beta.1...v1.3.13-beta.2) (2022-09-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.13-beta.1](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13-beta.1) (2022-08-29)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.12](https://github.com/midwayjs/cli/compare/v1.3.12-beta.3...v1.3.12) (2022-08-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.8](https://github.com/midwayjs/cli/compare/v1.3.7...v1.3.8) (2022-07-21)


### Features

* new faas & check ([#287](https://github.com/midwayjs/cli/issues/287)) ([2d62fdc](https://github.com/midwayjs/cli/commit/2d62fdc4d91d87ff60c92a0fd9060301e10a3fa5))





## [1.3.5](https://github.com/midwayjs/cli/compare/v1.3.5-beta.3...v1.3.5) (2022-05-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.5-beta.3](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.3) (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))





## [1.3.5-beta.2](https://github.com/midwayjs/cli/compare/v1.3.5-beta.1...v1.3.5-beta.2) (2022-05-24)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.4](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4) (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))





## [1.3.4-beta.1](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4-beta.1) (2022-04-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.3](https://github.com/midwayjs/cli/compare/v1.3.2...v1.3.3) (2022-04-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.1](https://github.com/midwayjs/cli/compare/v1.3.1-beta.1...v1.3.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.3.1-beta.1](https://github.com/midwayjs/cli/compare/v1.3.0...v1.3.1-beta.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.97](https://github.com/midwayjs/cli/compare/v1.2.96...v1.2.97) (2022-01-24)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.95](https://github.com/midwayjs/cli/compare/v1.2.94...v1.2.95) (2022-01-20)


### Features

* support hooks 3.0 ([#254](https://github.com/midwayjs/cli/issues/254)) ([eb50b72](https://github.com/midwayjs/cli/commit/eb50b7287734cf965e744466ea725dde78525e3d))





## [1.2.94](https://github.com/midwayjs/cli/compare/v1.2.93...v1.2.94) (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))





## [1.2.93](https://github.com/midwayjs/cli/compare/v1.2.92...v1.2.93) (2021-12-29)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.92](https://github.com/midwayjs/cli/compare/v1.2.91...v1.2.92) (2021-12-03)


### Bug Fixes

* using https://registry.npmmirror.com instead https://registry.np… ([#237](https://github.com/midwayjs/cli/issues/237)) ([56ce275](https://github.com/midwayjs/cli/commit/56ce275d98db601944892229e0645fcbf2bdb602))





## [1.2.91](https://github.com/midwayjs/cli/compare/v1.2.90...v1.2.91) (2021-11-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.90](https://github.com/midwayjs/cli/compare/v1.2.89...v1.2.90) (2021-11-16)


### Bug Fixes

* remvoe hooks core ([#228](https://github.com/midwayjs/cli/issues/228)) ([d0c3fcc](https://github.com/midwayjs/cli/commit/d0c3fccbab299b15284f69f7574e84aec5cb7c5e))





## [1.2.89](https://github.com/midwayjs/cli/compare/v1.2.87...v1.2.89) (2021-11-16)


### Bug Fixes

* package check error ignore tsconfig error ([#226](https://github.com/midwayjs/cli/issues/226)) ([5d5468c](https://github.com/midwayjs/cli/commit/5d5468c7ac4fb45c73037fcd2ccc45a3494301b1))





## [1.2.86](https://github.com/midwayjs/cli/compare/v1.2.85...v1.2.86) (2021-11-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.85](https://github.com/midwayjs/cli/compare/v1.2.84...v1.2.85) (2021-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.84](https://github.com/midwayjs/cli/compare/v1.2.83...v1.2.84) (2021-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.82](https://github.com/midwayjs/cli/compare/v1.2.81...v1.2.82) (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))





## [1.2.80](https://github.com/midwayjs/cli/compare/v1.2.79...v1.2.80) (2021-08-16)


### Features

* support fc authType、 timeout、 initTimeout ([#178](https://github.com/midwayjs/cli/issues/178)) ([baca74c](https://github.com/midwayjs/cli/commit/baca74c971bd916803594c7900769a291dc5cb6f))





## [1.2.79](https://github.com/midwayjs/cli/compare/v1.2.78...v1.2.79) (2021-08-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.78](https://github.com/midwayjs/cli/compare/v1.2.77...v1.2.78) (2021-08-09)


### Features

* support wechat cloud function ([#153](https://github.com/midwayjs/cli/issues/153)) ([f64744c](https://github.com/midwayjs/cli/commit/f64744c537f4050611705691bbffc240d283ae3c))





## [1.2.76](https://github.com/midwayjs/cli/compare/v1.2.75...v1.2.76) (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))





## [1.2.74](https://github.com/midwayjs/cli/compare/v1.2.73...v1.2.74) (2021-07-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.73](https://github.com/midwayjs/cli/compare/v1.2.73-beta.1...v1.2.73) (2021-06-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.72](https://github.com/midwayjs/cli/compare/v1.2.71...v1.2.72) (2021-06-18)


### Bug Fixes

* copy js file when execute build command ([#120](https://github.com/midwayjs/cli/issues/120)) ([404aa40](https://github.com/midwayjs/cli/commit/404aa4074e3866a0914d918e96a922bbb267bad9))
* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))





## [1.2.70](https://github.com/midwayjs/cli/compare/v1.2.69...v1.2.70) (2021-06-02)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.69](https://github.com/midwayjs/cli/compare/v1.2.68...v1.2.69) (2021-06-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.68](https://github.com/midwayjs/cli/compare/v1.2.67...v1.2.68) (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))





## [1.2.65](https://github.com/midwayjs/cli/compare/v1.2.63...v1.2.65) (2021-04-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.63](https://github.com/midwayjs/cli/compare/v1.2.62...v1.2.63) (2021-04-16)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.61](https://github.com/midwayjs/cli/compare/v1.2.60...v1.2.61) (2021-04-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.55](https://github.com/midwayjs/cli/compare/v1.2.54...v1.2.55) (2021-03-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.54](https://github.com/midwayjs/cli/compare/v1.2.53...v1.2.54) (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))





## [1.2.51](https://github.com/midwayjs/cli/compare/v1.2.50...v1.2.51) (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))





## [1.2.50](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.50) (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))







**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.49](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.49) (2021-03-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.48](https://github.com/midwayjs/cli/compare/v1.2.46...v1.2.48) (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))





## [1.2.46](https://github.com/midwayjs/cli/compare/v1.2.45...v1.2.46) (2021-03-05)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.45](https://github.com/midwayjs/cli/compare/v1.2.44...v1.2.45) (2021-03-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.41](https://github.com/midwayjs/cli/compare/v1.2.40...v1.2.41) (2021-02-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.40](https://github.com/midwayjs/cli/compare/v1.2.39-beta.5...v1.2.40) (2021-02-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.37](https://github.com/midwayjs/cli/compare/v1.2.35...v1.2.37) (2021-01-08)


### Bug Fixes

* support package diagnostics & tsConfig config ([#38](https://github.com/midwayjs/cli/issues/38)) ([c499d14](https://github.com/midwayjs/cli/commit/c499d145f9cabf427877ec8ea65aea8ead42b9cd))





## [1.2.35](https://github.com/midwayjs/cli/compare/v1.2.33...v1.2.35) (2020-12-24)


### Bug Fixes

* copy file error catch ([8d2097c](https://github.com/midwayjs/cli/commit/8d2097c538f22ed6050c85d1c250436e0c2c71c1))





## [1.2.34](https://github.com/midwayjs/cli/compare/v1.2.34-beta.2...v1.2.34) (2020-12-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.32](https://github.com/midwayjs/cli/compare/v1.2.32-beta...v1.2.32) (2020-12-08)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.31](https://github.com/midwayjs/cli/compare/v1.2.30...v1.2.31) (2020-12-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.30](https://github.com/midwayjs/cli/compare/v1.2.30-beta...v1.2.30) (2020-11-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.29](https://github.com/midwayjs/cli/compare/serverless-v1.2.28...serverless-v1.2.29) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.28](https://github.com/midwayjs/cli/compare/serverless-v1.2.27...serverless-v1.2.28) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.27](https://github.com/midwayjs/cli/compare/serverless-v1.2.26...serverless-v1.2.27) (2020-11-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.26](https://github.com/midwayjs/cli/compare/serverless-v1.2.21...serverless-v1.2.26) (2020-11-17)


### Bug Fixes

* build copy file path ([#23](https://github.com/midwayjs/cli/issues/23)) ([ff94951](https://github.com/midwayjs/cli/commit/ff9495147571bb495fc2edd21ee09e7dbb323333))



## 1.2.25 (2020-11-12)



## 1.2.25-beta.1 (2020-11-12)



## 1.2.24 (2020-11-12)


### Bug Fixes

* support copy static file ([#20](https://github.com/midwayjs/cli/issues/20)) ([39021c4](https://github.com/midwayjs/cli/commit/39021c4b82f962d970a0fc5b9a96b8ae8d66dba1))



## 1.2.23 (2020-11-11)



## 1.2.23-beta.3 (2020-11-10)



## 1.2.23-beta.2 (2020-10-30)



## 1.2.23-beta.1 (2020-10-26)


### Bug Fixes

* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.25](https://github.com/midwayjs/cli/compare/v1.2.25-beta.1...v1.2.25) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.25-beta.1](https://github.com/midwayjs/cli/compare/v1.2.24-beta.1...v1.2.25-beta.1) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.24](https://github.com/midwayjs/cli/compare/v1.2.23...v1.2.24) (2020-11-12)


### Bug Fixes

* support copy static file ([#20](https://github.com/midwayjs/cli/issues/20)) ([39021c4](https://github.com/midwayjs/cli/commit/39021c4b82f962d970a0fc5b9a96b8ae8d66dba1))





## [1.2.23](https://github.com/midwayjs/cli/compare/v1.2.23-beta.3...v1.2.23) (2020-11-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.23-beta.3](https://github.com/midwayjs/cli/compare/v1.2.23-beta.2...v1.2.23-beta.3) (2020-11-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.23-beta.2](https://github.com/midwayjs/cli/compare/v1.2.23-beta.1...v1.2.23-beta.2) (2020-10-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.23-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.23-beta.1) (2020-10-26)


### Bug Fixes

* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.22](https://github.com/midwayjs/cli/compare/v1.2.22-beta.1...v1.2.22) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.22-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.22-beta.1) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.21](https://github.com/midwayjs/cli/compare/serverless-v1.2.19...serverless-v1.2.21) (2020-10-20)



## 1.2.20 (2020-10-19)



## 1.2.20-beta.5 (2020-10-19)



## 1.2.20-beta.4 (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20](https://github.com/midwayjs/cli/compare/v1.2.20-beta.5...v1.2.20) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.20-beta.5](https://github.com/midwayjs/cli/compare/v1.2.20-beta.4...v1.2.20-beta.5) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.20-beta.4](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.4) (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20-beta.3](https://github.com/midwayjs/cli/compare/v1.2.20-beta.2...v1.2.20-beta.3) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.20-beta.2](https://github.com/midwayjs/cli/compare/v1.2.20-beta.1...v1.2.20-beta.2) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.20-beta.1](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.1) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.18](https://github.com/midwayjs/cli/compare/serverless-v1.2.17...serverless-v1.2.18) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.17](https://github.com/midwayjs/cli/compare/serverless-v1.2.16...serverless-v1.2.17) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.2.16](https://github.com/midwayjs/cli/compare/serverless-v1.2.15...serverless-v1.2.16) (2020-09-22)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





## 1.2.15 (2020-09-22)



## 1.0.4 (2020-09-20)


### Features

* midway-bin clean ([63ce124](https://github.com/midwayjs/cli/commit/63ce124e2618086e194f68c9f4eee7ec441b9162))



## 1.0.3 (2020-09-20)



## 1.0.1 (2020-09-17)


### Bug Fixes

* build tsconfig error ([a364d73](https://github.com/midwayjs/cli/commit/a364d73d4162bbec76512b968b6f370668225154))


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))



# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





## [1.0.4](https://github.com/midwayjs/cli/compare/v1.0.3...v1.0.4) (2020-09-20)


### Features

* midway-bin clean ([63ce124](https://github.com/midwayjs/cli/commit/63ce124e2618086e194f68c9f4eee7ec441b9162))





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build





## [1.0.1](https://github.com/midwayjs/cli/compare/v1.0.0...v1.0.1) (2020-09-17)


### Bug Fixes

* build tsconfig error ([a364d73](https://github.com/midwayjs/cli/commit/a364d73d4162bbec76512b968b6f370668225154))


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))





# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





# [1.0.0](https://github.com/midwayjs/cli/compare/v1.1.0...v1.0.0) (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))







**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build







**Note:** Version bump only for package @midwayjs/cli-plugin-build





# [1.0.0](https://github.com/midwayjs/bin/compare/v1.1.0...v1.0.0) (2020-09-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-build





# 1.1.0 (2020-09-16)


### Features

* dev ([7e2dd87](https://github.com/midwayjs/bin/commit/7e2dd8773c2bd79de93a4aea7a41c0c74663b6bc))
* new ([7ca760c](https://github.com/midwayjs/bin/commit/7ca760c059715220c738a46a78d09d288a767f6d))
