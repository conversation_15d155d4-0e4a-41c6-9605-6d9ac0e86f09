{"version": 3, "sources": ["../browser/src/platform/BrowserFileLoggerDummy.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,OAAO,WAAW;IACpB;;OAEG;IACH,QAAQ;QACJ,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,aAAa;QACT,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,YAAY;QACR,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,cAAc;QACV,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;IAED;;OAEG;IACH,YAAY;QACR,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;IAED;;;OAGG;IACH,GAAG;QACC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;IAC1E,CAAC;CACJ;AAED,MAAM,OAAO,UAAW,SAAQ,WAAW;CAAG", "file": "BrowserFileLoggerDummy.js", "sourcesContent": ["/**\n * Performs logging of the events in TypeORM.\n * This version of logger logs everything into ormlogs.log file.\n */\nexport class DummyLogger {\n    /**\n     * Logs query and parameters used in it.\n     */\n    logQuery() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n\n    /**\n     * Logs query that is failed.\n     */\n    logQueryError() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n\n    /**\n     * Logs query that is slow.\n     */\n    logQuerySlow() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n\n    /**\n     * Logs events from the schema build process.\n     */\n    logSchemaBuild() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n\n    /**\n     * Logs events from the migrations run process.\n     */\n    logMigration() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n\n    /**\n     * Perform logging using given logger, or by default to the console.\n     * Log has its own level and message.\n     */\n    log() {\n        throw new Error('This logger is not applicable in a browser context');\n    }\n}\n\nexport class FileLogger extends DummyLogger {}\n"], "sourceRoot": ".."}