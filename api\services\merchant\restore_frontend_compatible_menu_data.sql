-- 恢复与前端 art-design-pro asyncRoutes 完全兼容的菜单数据
-- 基于前端 AppRouteRecord 类型结构设计

USE merchant_admin;

-- 清空现有菜单数据
TRUNCATE TABLE merchant_sys_menu;

-- 重置自增ID
ALTER TABLE merchant_sys_menu AUTO_INCREMENT = 1;

-- 插入菜单数据（严格按照前端 asyncRoutes 结构）
INSERT INTO merchant_sys_menu (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) VALUES

-- 1. Dashboard (仪表盘) - 一级菜单
(1, 0, '仪表盘', '/dashboard', '', 0, '&#xe721;', 1, 1, 1, NOW(), NOW()),
  -- Dashboard 子菜单
  (2, 1, '工作台', '/dashboard/console', 'dashboard:console', 1, '', 1, 0, 1, NOW(), NOW()),
  (3, 1, '分析页', '/dashboard/analysis', 'dashboard:analysis', 1, '', 2, 0, 1, NOW(), NOW()),
  (4, 1, '电子商务', '/dashboard/ecommerce', 'dashboard:ecommerce', 1, '', 3, 0, 1, NOW(), NOW()),

-- 2. Merchant (商户管理) - 一级菜单（核心业务模块）
(5, 0, '商户管理', '/merchant', '', 0, '&#xe7b4;', 2, 1, 1, NOW(), NOW()),
  -- Merchant 子菜单
  (6, 5, '商户入驻', '/merchant/settle-in', 'merchant:settle-in', 1, '', 1, 1, 1, NOW(), NOW()),
  (7, 5, '非遗人认证', '/merchant/heritage-auth', 'merchant:heritage-auth', 1, '', 2, 1, 1, NOW(), NOW()),
  (8, 5, '个人商户', '/merchant/personal', 'merchant:personal', 1, '', 3, 1, 1, NOW(), NOW()),
  (9, 5, '企业商户', '/merchant/company', 'merchant:company', 1, '', 4, 1, 1, NOW(), NOW()),

-- 3. Template (模板中心) - 一级菜单
(10, 0, '模板中心', '/template', '', 0, '&#xe860;', 3, 1, 1, NOW(), NOW()),
  -- Template 子菜单
  (11, 10, '卡片', '/template/cards', 'template:cards', 1, '', 1, 0, 1, NOW(), NOW()),
  (12, 10, '横幅', '/template/banners', 'template:banners', 1, '', 2, 0, 1, NOW(), NOW()),
  (13, 10, '图表', '/template/charts', 'template:charts', 1, '', 3, 0, 1, NOW(), NOW()),
  (14, 10, '地图', '/template/map', 'template:map', 1, '', 4, 1, 1, NOW(), NOW()),
  (15, 10, '聊天', '/template/chat', 'template:chat', 1, '', 5, 1, 1, NOW(), NOW()),
  (16, 10, '日历', '/template/calendar', 'template:calendar', 1, '', 6, 1, 1, NOW(), NOW()),
  (17, 10, '定价', '/template/pricing', 'template:pricing', 1, '', 7, 1, 1, NOW(), NOW()),

-- 4. Widgets (组件中心) - 一级菜单
(18, 0, '组件中心', '/widgets', '', 0, '&#xe81a;', 4, 1, 1, NOW(), NOW()),
  -- Widgets 子菜单
  (19, 18, '图标列表', '/widgets/icon-list', 'widgets:icon-list', 1, '', 1, 1, 1, NOW(), NOW()),
  (20, 18, '图标选择器', '/widgets/icon-selector', 'widgets:icon-selector', 1, '', 2, 1, 1, NOW(), NOW()),
  (21, 18, '图片裁剪', '/widgets/image-crop', 'widgets:image-crop', 1, '', 3, 1, 1, NOW(), NOW()),
  (22, 18, 'Excel', '/widgets/excel', 'widgets:excel', 1, '', 4, 1, 1, NOW(), NOW()),
  (23, 18, '视频', '/widgets/video', 'widgets:video', 1, '', 5, 1, 1, NOW(), NOW()),
  (24, 18, '数字动画', '/widgets/count-to', 'widgets:count-to', 1, '', 6, 0, 1, NOW(), NOW()),
  (25, 18, '富文本编辑器', '/widgets/wang-editor', 'widgets:wang-editor', 1, '', 7, 1, 1, NOW(), NOW()),
  (26, 18, '水印', '/widgets/watermark', 'widgets:watermark', 1, '', 8, 1, 1, NOW(), NOW()),
  (27, 18, '右键菜单', '/widgets/context-menu', 'widgets:context-menu', 1, '', 9, 1, 1, NOW(), NOW()),
  (28, 18, '二维码', '/widgets/qrcode', 'widgets:qrcode', 1, '', 10, 1, 1, NOW(), NOW()),
  (29, 18, '拖拽', '/widgets/drag', 'widgets:drag', 1, '', 11, 1, 1, NOW(), NOW()),
  (30, 18, '文字滚动', '/widgets/text-scroll', 'widgets:text-scroll', 1, '', 12, 1, 1, NOW(), NOW()),
  (31, 18, '烟花效果', '/widgets/fireworks', 'widgets:fireworks', 1, '', 13, 1, 1, NOW(), NOW()),

-- 5. System (系统管理) - 一级菜单
(32, 0, '系统管理', '/system', '', 0, '&#xe7b9;', 5, 1, 1, NOW(), NOW()),
  -- System 子菜单
  (33, 32, '用户管理', '/system/user', 'system:user', 1, '', 1, 1, 1, NOW(), NOW()),
  (34, 32, '角色管理', '/system/role', 'system:role', 1, '', 2, 1, 1, NOW(), NOW()),
  (35, 32, '个人中心', '/system/user-center', 'system:user-center', 1, '', 3, 1, 0, NOW(), NOW()),
  (36, 32, '菜单管理', '/system/menu', 'system:menu', 1, '', 4, 1, 1, NOW(), NOW()),

-- 6. Exception (异常页面) - 一级菜单
(37, 0, '异常页面', '/exception', '', 0, '&#xe820;', 6, 1, 1, NOW(), NOW()),
  -- Exception 子菜单
  (38, 37, '403', '/exception/403', 'exception:403', 1, '', 1, 1, 1, NOW(), NOW()),
  (39, 37, '404', '/exception/404', 'exception:404', 1, '', 2, 1, 1, NOW(), NOW()),
  (40, 37, '500', '/exception/500', 'exception:500', 1, '', 3, 1, 1, NOW(), NOW());

-- 显示插入结果
SELECT COUNT(*) as '总菜单数量' FROM merchant_sys_menu;

-- 显示菜单树形结构（验证数据）
SELECT 
  CASE 
    WHEN parentId = 0 THEN CONCAT('📁 ', name)
    ELSE CONCAT('  ├── ', name)
  END AS menu_tree,
  router,
  CASE type WHEN 0 THEN '目录' WHEN 1 THEN '菜单' END AS type_name,
  CASE isShow WHEN 1 THEN '显示' ELSE '隐藏' END AS show_status
FROM merchant_sys_menu 
ORDER BY parentId, orderNum;

-- 创建测试用户和角色
-- 确保有测试数据可以验证菜单功能 