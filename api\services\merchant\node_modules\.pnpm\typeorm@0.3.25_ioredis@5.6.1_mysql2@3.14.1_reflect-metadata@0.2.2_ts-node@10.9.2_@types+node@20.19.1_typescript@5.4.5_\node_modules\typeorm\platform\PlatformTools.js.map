{"version": 3, "sources": ["../../src/platform/PlatformTools.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,4DAA2B;AAC3B,oDAAmB;AACnB,wDAAuB;AACvB,iDAAyC;AACzC,mDAAyD;AAIzD,iCAAqC;AAA5B,sGAAA,YAAY,OAAA;AACrB,yBAA+B;AAAtB,gGAAA,UAAU,OAAA;AACnB,iCAA2C;AAAlC,kGAAA,QAAQ,OAAA;AAAE,kGAAA,QAAQ,OAAA;AAE3B;;GAEG;AACH,MAAa,aAAa;IAMtB;;OAEG;IACH,MAAM,CAAC,iBAAiB;QACpB,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,IAAI,CAAC,IAAY;QACpB,2HAA2H;QAC3H,8FAA8F;QAC9F,kCAAkC;QAElC,IAAI,CAAC;YACD,wEAAwE;YACxE,QAAQ,IAAI,EAAE,CAAC;gBACX;;mBAEG;gBACH,KAAK,SAAS;oBACV,OAAO,OAAO,CAAC,uBAAuB,CAAC,CAAA;gBAE3C;;mBAEG;gBACH,KAAK,SAAS;oBACV,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA;gBAE7B;;mBAEG;gBACH,KAAK,kBAAkB;oBACnB,OAAO,OAAO,CAAC,kBAAkB,CAAC,CAAA;gBAEtC,KAAK,mCAAmC;oBACpC,OAAO,OAAO,CAAC,mCAAmC,CAAC,CAAA;gBAEvD,KAAK,UAAU;oBACX,OAAO,OAAO,CAAC,UAAU,CAAC,CAAA;gBAE9B;;mBAEG;gBACH,KAAK,OAAO;oBACR,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;gBAE3B,KAAK,QAAQ;oBACT,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAA;gBAE5B;;mBAEG;gBACH,KAAK,UAAU;oBACX,OAAO,OAAO,CAAC,UAAU,CAAC,CAAA;gBAE9B;;mBAEG;gBACH,KAAK,IAAI;oBACL,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;gBAExB,KAAK,WAAW;oBACZ,OAAO,OAAO,CAAC,WAAW,CAAC,CAAA;gBAE/B,KAAK,iBAAiB;oBAClB,OAAO,OAAO,CAAC,iBAAiB,CAAC,CAAA;gBAErC,KAAK,gCAAgC;oBACjC,OAAO,OAAO,CAAC,gCAAgC,CAAC,CAAA;gBAEpD;;mBAEG;gBACH,KAAK,OAAO;oBACR,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;gBAE3B,KAAK,SAAS;oBACV,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA;gBAE7B;;mBAEG;gBACH,KAAK,gBAAgB;oBACjB,OAAO,OAAO,CAAC,gBAAgB,CAAC,CAAA;gBAEpC;;mBAEG;gBACH,KAAK,SAAS;oBACV,OAAO,OAAO,CAAC,SAAS,CAAC,CAAA;gBAE7B;;mBAEG;gBACH,KAAK,QAAQ;oBACT,OAAO,OAAO,CAAC,QAAQ,CAAC,CAAA;gBAE5B;;mBAEG;gBACH,KAAK,OAAO;oBACR,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;gBAE3B;;mBAEG;gBACH,KAAK,6BAA6B;oBAC9B,OAAO,OAAO,CAAC,6BAA6B,CAAC,CAAA;YACrD,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,cAAI,CAAC,OAAO,CACvB,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,GAAG,IAAI,CAC1C,CAAC,CAAA;QACN,CAAC;QAED,4FAA4F;QAC5F,6FAA6F;QAC7F,8FAA8F;QAC9F,YAAY;QACZ,MAAM,IAAI,SAAS,CAAC,2CAA2C,IAAI,EAAE,CAAC,CAAA;IAC1E,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,OAAe;QAChC,IAAI,cAAc,GAAG,cAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QAC5C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO;YAC5B,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACvD,OAAO,cAAc,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAe;QAC9B,OAAO,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW,CAAC,OAAe;QAC9B,OAAO,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;IAChC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,OAAe;QAC5B,OAAO,YAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;IACjC,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,QAAgB;QAChC,OAAO,YAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;IACpC,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,QAAgB,EAAE,IAAS;QAC7C,YAAE,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;IACrC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,IAAY,EAAE,IAAS;QAC1C,OAAO,YAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,MAAM,CAAC,OAAe;QACzB,gBAAM,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAA;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,IAAY;QAC9B,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,GAAW;QAC3B,OAAO,IAAA,yBAAS,EAAC,GAAG,EAAE;YAClB,MAAM,EAAE;gBACJ,OAAO,EAAE,eAAI,CAAC,UAAU,CAAC,IAAI;gBAC7B,QAAQ,EAAE,eAAI,CAAC,aAAa,CAAC,IAAI;gBACjC,MAAM,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;gBACvB,MAAM,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;gBACvB,UAAU,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;gBAC3B,OAAO,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;gBACxB,OAAO,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;gBACxB,OAAO,EAAE,eAAI,CAAC,IAAI,CAAC,IAAI;gBACvB,KAAK,EAAE,eAAI,CAAC,KAAK,CAAC,IAAI;aACzB;SACJ,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,SAAS,CAAC,GAAW,EAAE,cAA6B;QACvD,MAAM,mBAAmB,GAGrB;YACA,MAAM,EAAE,QAAQ;SACnB,CAAA;QAED,MAAM,gBAAgB,GAAG,cAAc;YACnC,CAAC,CAAC,mBAAmB,CAAC,cAAc,CAAC,IAAI,KAAK;YAC9C,CAAC,CAAC,KAAK,CAAA;QAEX,OAAO,IAAA,kBAAS,EAAC,GAAG,EAAE;YAClB,QAAQ,EAAE,gBAAgB;YAC1B,MAAM,EAAE,MAAM;SACjB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,MAAc,EAAE,IAAS;QACpC,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,MAAc,EAAE,KAAU;QACtC,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAA;IAClD,CAAC;IAED,MAAM,CAAC,OAAO,CAAC,MAAc,EAAE,OAAY;QACvC,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC;IAED,MAAM,CAAC,GAAG,CAAC,OAAe;QACtB,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAA;IACxC,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,IAAS;QACjB,OAAO,eAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,KAAU;QACnB,OAAO,eAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,OAAe;QACvB,OAAO,eAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;IAC/B,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAc,EAAE,GAAS;QACtC,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAA;QACrC,IAAI,GAAG;YAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;IAC/B,CAAC;;AAxQL,sCAyQC;AAxQG;;GAEG;AACI,kBAAI,GAAuB,MAAM,CAAA", "file": "PlatformTools.js", "sourcesContent": ["import ansi from \"ansis\"\nimport dotenv from \"dotenv\"\nimport fs from \"fs\"\nimport path from \"path\"\nimport { highlight } from \"sql-highlight\"\nimport { format as sqlFormat } from \"@sqltools/formatter\"\nimport { type Config as SqlFormatterConfig } from \"@sqltools/formatter/lib/core/types\"\nimport { type DatabaseType } from \"../driver/types/DatabaseType\"\n\nexport { EventEmitter } from \"events\"\nexport { ReadStream } from \"fs\"\nexport { Readable, Writable } from \"stream\"\n\n/**\n * Platform-specific tools.\n */\nexport class PlatformTools {\n    /**\n     * Type of the currently running platform.\n     */\n    static type: \"browser\" | \"node\" = \"node\"\n\n    /**\n     * Gets global variable where global stuff can be stored.\n     */\n    static getGlobalVariable(): any {\n        return global\n    }\n\n    /**\n     * Loads (\"require\"-s) given file or package.\n     * This operation only supports on node platform\n     */\n    static load(name: string): any {\n        // if name is not absolute or relative, then try to load package from the node_modules of the directory we are currently in\n        // this is useful when we are using typeorm package globally installed and it accesses drivers\n        // that are not installed globally\n\n        try {\n            // switch case to explicit require statements for webpack compatibility.\n            switch (name) {\n                /**\n                 * spanner\n                 */\n                case \"spanner\":\n                    return require(\"@google-cloud/spanner\")\n\n                /**\n                 * mongodb\n                 */\n                case \"mongodb\":\n                    return require(\"mongodb\")\n\n                /**\n                 * hana\n                 */\n                case \"@sap/hana-client\":\n                    return require(\"@sap/hana-client\")\n\n                case \"@sap/hana-client/extension/Stream\":\n                    return require(\"@sap/hana-client/extension/Stream\")\n\n                case \"hdb-pool\":\n                    return require(\"hdb-pool\")\n\n                /**\n                 * mysql\n                 */\n                case \"mysql\":\n                    return require(\"mysql\")\n\n                case \"mysql2\":\n                    return require(\"mysql2\")\n\n                /**\n                 * oracle\n                 */\n                case \"oracledb\":\n                    return require(\"oracledb\")\n\n                /**\n                 * postgres\n                 */\n                case \"pg\":\n                    return require(\"pg\")\n\n                case \"pg-native\":\n                    return require(\"pg-native\")\n\n                case \"pg-query-stream\":\n                    return require(\"pg-query-stream\")\n\n                case \"typeorm-aurora-data-api-driver\":\n                    return require(\"typeorm-aurora-data-api-driver\")\n\n                /**\n                 * redis\n                 */\n                case \"redis\":\n                    return require(\"redis\")\n\n                case \"ioredis\":\n                    return require(\"ioredis\")\n\n                /**\n                 * better-sqlite3\n                 */\n                case \"better-sqlite3\":\n                    return require(\"better-sqlite3\")\n\n                /**\n                 * sqlite\n                 */\n                case \"sqlite3\":\n                    return require(\"sqlite3\")\n\n                /**\n                 * sql.js\n                 */\n                case \"sql.js\":\n                    return require(\"sql.js\")\n\n                /**\n                 * sqlserver\n                 */\n                case \"mssql\":\n                    return require(\"mssql\")\n\n                /**\n                 * react-native-sqlite\n                 */\n                case \"react-native-sqlite-storage\":\n                    return require(\"react-native-sqlite-storage\")\n            }\n        } catch (err) {\n            return require(path.resolve(\n                process.cwd() + \"/node_modules/\" + name,\n            ))\n        }\n\n        // If nothing above matched and we get here, the package was not listed within PlatformTools\n        // and is an Invalid Package.  To make it explicit that this is NOT the intended use case for\n        // PlatformTools.load - it's not just a way to replace `require` all willy-nilly - let's throw\n        // an error.\n        throw new TypeError(`Invalid Package for PlatformTools.load: ${name}`)\n    }\n\n    /**\n     * Normalizes given path. Does \"path.normalize\" and replaces backslashes with forward slashes on Windows.\n     */\n    static pathNormalize(pathStr: string): string {\n        let normalizedPath = path.normalize(pathStr)\n        if (process.platform === \"win32\")\n            normalizedPath = normalizedPath.replace(/\\\\/g, \"/\")\n        return normalizedPath\n    }\n\n    /**\n     * Gets file extension. Does \"path.extname\".\n     */\n    static pathExtname(pathStr: string): string {\n        return path.extname(pathStr)\n    }\n\n    /**\n     * Resolved given path. Does \"path.resolve\".\n     */\n    static pathResolve(pathStr: string): string {\n        return path.resolve(pathStr)\n    }\n\n    /**\n     * Synchronously checks if file exist. Does \"fs.existsSync\".\n     */\n    static fileExist(pathStr: string): boolean {\n        return fs.existsSync(pathStr)\n    }\n\n    static readFileSync(filename: string): Buffer {\n        return fs.readFileSync(filename)\n    }\n\n    static appendFileSync(filename: string, data: any): void {\n        fs.appendFileSync(filename, data)\n    }\n\n    static async writeFile(path: string, data: any): Promise<void> {\n        return fs.promises.writeFile(path, data)\n    }\n\n    /**\n     * Loads a dotenv file into the environment variables.\n     *\n     * @param path The file to load as a dotenv configuration\n     */\n    static dotenv(pathStr: string): void {\n        dotenv.config({ path: pathStr })\n    }\n\n    /**\n     * Gets environment variable.\n     */\n    static getEnvVariable(name: string): any {\n        return process.env[name]\n    }\n\n    /**\n     * Highlights sql string to be printed in the console.\n     */\n    static highlightSql(sql: string) {\n        return highlight(sql, {\n            colors: {\n                keyword: ansi.blueBright.open,\n                function: ansi.magentaBright.open,\n                number: ansi.green.open,\n                string: ansi.white.open,\n                identifier: ansi.white.open,\n                special: ansi.white.open,\n                bracket: ansi.white.open,\n                comment: ansi.gray.open,\n                clear: ansi.reset.open,\n            },\n        })\n    }\n\n    /**\n     * Pretty-print sql string to be print in the console.\n     */\n    static formatSql(sql: string, dataSourceType?: DatabaseType): string {\n        const databaseLanguageMap: Record<\n            string,\n            SqlFormatterConfig[\"language\"]\n        > = {\n            oracle: \"pl/sql\",\n        }\n\n        const databaseLanguage = dataSourceType\n            ? databaseLanguageMap[dataSourceType] || \"sql\"\n            : \"sql\"\n\n        return sqlFormat(sql, {\n            language: databaseLanguage,\n            indent: \"    \",\n        })\n    }\n\n    /**\n     * Logging functions needed by AdvancedConsoleLogger\n     */\n    static logInfo(prefix: string, info: any) {\n        console.log(ansi.gray.underline(prefix), info)\n    }\n\n    static logError(prefix: string, error: any) {\n        console.log(ansi.underline.red(prefix), error)\n    }\n\n    static logWarn(prefix: string, warning: any) {\n        console.log(ansi.underline.yellow(prefix), warning)\n    }\n\n    static log(message: string) {\n        console.log(ansi.underline(message))\n    }\n\n    static info(info: any) {\n        return ansi.gray(info)\n    }\n\n    static error(error: any) {\n        return ansi.red(error)\n    }\n\n    static warn(message: string) {\n        return ansi.yellow(message)\n    }\n\n    static logCmdErr(prefix: string, err?: any) {\n        console.log(ansi.black.bgRed(prefix))\n        if (err) console.error(err)\n    }\n}\n"], "sourceRoot": ".."}