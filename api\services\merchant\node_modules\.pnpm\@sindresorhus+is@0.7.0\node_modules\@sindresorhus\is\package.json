{"name": "@sindresorhus/is", "version": "0.7.0", "description": "Type check values: `is.string('🦄') //=> true`", "license": "MIT", "repository": "sindresorhus/is", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "publishConfig": {"access": "public"}, "main": "dist/index.js", "engines": {"node": ">=4"}, "scripts": {"lint": "tslint --format stylish --project .", "build": "tsc", "test": "npm run lint && npm run build && ava dist/tests", "prepublish": "npm run build && del dist/tests"}, "files": ["dist"], "keywords": ["type", "types", "is", "check", "checking", "validate", "validation", "utility", "util", "typeof", "instanceof", "object", "assert", "assertion", "test", "kind", "primitive", "verify", "compare"], "devDependencies": {"@types/jsdom": "^2.0.31", "@types/node": "^8.0.47", "@types/tempy": "^0.1.0", "ava": "*", "del-cli": "^1.1.0", "jsdom": "^9.12.0", "tempy": "^0.2.1", "tslint": "^5.8.0", "tslint-xo": "^0.3.0", "typescript": "^2.6.1"}, "types": "dist/index.d.ts"}