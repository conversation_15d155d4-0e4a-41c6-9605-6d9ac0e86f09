"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Mock = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Mock() {
    return (target) => {
        (0, __1.saveModule)(__1.<PERSON><PERSON><PERSON>_<PERSON>EY, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Singleton)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Mock = Mock;
//# sourceMappingURL=mock.js.map