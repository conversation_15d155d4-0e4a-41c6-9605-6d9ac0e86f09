import { HandlerFunction, IMidwayContainer, MethodHandlerFunction, ParameterHandlerFunction, PipeUnionTransform } from '../interface';
export declare class MidwayDecoratorService {
    readonly applicationContext: IMidwayContainer;
    private propertyHandlerMap;
    private methodDecoratorMap;
    private parameterDecoratorMap;
    private parameterDecoratorPipes;
    private aspectService;
    constructor(applicationContext: IMidwayContainer);
    protected init(): void;
    registerPropertyHandler(decoratorKey: string, fn: HandlerFunction): void;
    registerMethodHandler(decoratorKey: string, fn: MethodHandlerFunction): void;
    registerParameterHandler(decoratorKey: string, fn: ParameterHandlerFunction): void;
    registerParameterPipes(decoratorKey: string, pipes: PipeUnionTransform[]): void;
    /**
     * binding getter method for decorator
     *
     * @param prop
     * @param instance
     * @param getterHandler
     */
    private defineGetterPropertyValue;
    private getHandler;
}
//# sourceMappingURL=decoratorService.d.ts.map