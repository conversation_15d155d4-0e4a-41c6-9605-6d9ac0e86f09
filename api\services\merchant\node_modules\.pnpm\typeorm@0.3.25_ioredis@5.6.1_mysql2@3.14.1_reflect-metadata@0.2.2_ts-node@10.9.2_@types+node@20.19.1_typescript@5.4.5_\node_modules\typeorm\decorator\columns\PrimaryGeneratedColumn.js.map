{"version": 3, "sources": ["../../src/decorator/columns/PrimaryGeneratedColumn.ts"], "names": [], "mappings": ";;AAsDA,wDAgEC;AAtHD,2CAAsD;AAMtD,wDAAoD;AA2CpD;;;;GAIG;AACH,SAAgB,sBAAsB,CAClC,iBAO2C,EAC3C,YAG2C;IAE3C,uBAAuB;IACvB,MAAM,OAAO,GAAkB,EAAE,CAAA;IACjC,IAAI,QAAqD,CAAA;IACzD,IAAI,iBAAiB,EAAE,CAAC;QACpB,IAAI,OAAO,iBAAiB,KAAK,QAAQ;YACrC,QAAQ,GAAG,iBAIK,CAAA;QAEpB,IAAI,yBAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;YAC1C,QAAQ,GAAG,WAAW,CAAA;YACtB,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,QAAQ,GAAG,WAAW,CAAA;IAC1B,CAAC;IACD,IAAI,yBAAW,CAAC,QAAQ,CAAC,YAAY,CAAC;QAAE,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,CAAC,CAAA;IAE5E,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,sFAAsF;QACtF,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAChB,IAAI,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,UAAU,EAAE,CAAC;gBACtD,OAAO,CAAC,IAAI,GAAG,MAAM,CAAA;YACzB,CAAC;iBAAM,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;gBAC7B,OAAO,CAAC,IAAI,GAAG,MAAM,CAAA;YACzB,CAAC;iBAAM,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;gBAC9B,OAAO,CAAC,IAAI,GAAG,KAAK,CAAA;YACxB,CAAC;QACL,CAAC;QAED,2DAA2D;QAC3D,OAAO,CAAC,OAAO,GAAG,IAAI,CAAA;QAEtB,gCAAgC;QAChC,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO;SACnB,CAAC,CAAA;QAEF,mCAAmC;QACnC,IAAA,gCAAsB,GAAE,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;SACI,CAAC,CAAA;IAC/B,CAAC,CAAA;AACL,CAAC", "file": "PrimaryGeneratedColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { PrimaryGeneratedColumnNumericOptions } from \"../options/PrimaryGeneratedColumnNumericOptions\"\nimport { PrimaryGeneratedColumnUUIDOptions } from \"../options/PrimaryGeneratedColumnUUIDOptions\"\nimport { GeneratedMetadataArgs } from \"../../metadata-args/GeneratedMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\nimport { PrimaryGeneratedColumnIdentityOptions } from \"../options/PrimaryGeneratedColumnIdentityOptions\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n */\nexport function PrimaryGeneratedColumn(): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n */\nexport function PrimaryGeneratedColumn(\n    options: PrimaryGeneratedColumnNumericOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n */\nexport function PrimaryGeneratedColumn(\n    strategy: \"increment\",\n    options?: PrimaryGeneratedColumnNumericOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n */\nexport function PrimaryGeneratedColumn(\n    strategy: \"uuid\",\n    options?: PrimaryGeneratedColumnUUIDOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n */\nexport function PrimaryGeneratedColumn(\n    strategy: \"rowid\",\n    options?: PrimaryGeneratedColumnUUIDOptions,\n): PropertyDecorator\n\nexport function PrimaryGeneratedColumn(\n    strategy: \"identity\",\n    options?: PrimaryGeneratedColumnIdentityOptions,\n): PropertyDecorator\n\n/**\n * Column decorator is used to mark a specific class property as a table column.\n * Only properties decorated with this decorator will be persisted to the database when entity be saved.\n * This column creates an integer PRIMARY COLUMN with generated set to true.\n */\nexport function PrimaryGeneratedColumn(\n    strategyOrOptions?:\n        | \"increment\"\n        | \"uuid\"\n        | \"rowid\"\n        | \"identity\"\n        | PrimaryGeneratedColumnNumericOptions\n        | PrimaryGeneratedColumnUUIDOptions\n        | PrimaryGeneratedColumnIdentityOptions,\n    maybeOptions?:\n        | PrimaryGeneratedColumnNumericOptions\n        | PrimaryGeneratedColumnUUIDOptions\n        | PrimaryGeneratedColumnIdentityOptions,\n): PropertyDecorator {\n    // normalize parameters\n    const options: ColumnOptions = {}\n    let strategy: \"increment\" | \"uuid\" | \"rowid\" | \"identity\"\n    if (strategyOrOptions) {\n        if (typeof strategyOrOptions === \"string\")\n            strategy = strategyOrOptions as\n                | \"increment\"\n                | \"uuid\"\n                | \"rowid\"\n                | \"identity\"\n\n        if (ObjectUtils.isObject(strategyOrOptions)) {\n            strategy = \"increment\"\n            Object.assign(options, strategyOrOptions)\n        }\n    } else {\n        strategy = \"increment\"\n    }\n    if (ObjectUtils.isObject(maybeOptions)) Object.assign(options, maybeOptions)\n\n    return function (object: Object, propertyName: string) {\n        // if column type is not explicitly set then determine it based on generation strategy\n        if (!options.type) {\n            if (strategy === \"increment\" || strategy === \"identity\") {\n                options.type = Number\n            } else if (strategy === \"uuid\") {\n                options.type = \"uuid\"\n            } else if (strategy === \"rowid\") {\n                options.type = \"int\"\n            }\n        }\n\n        // explicitly set a primary and generated to column options\n        options.primary = true\n\n        // register column metadata args\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"regular\",\n            options: options,\n        })\n\n        // register generated metadata args\n        getMetadataArgsStorage().generations.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            strategy: strategy,\n        } as GeneratedMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}