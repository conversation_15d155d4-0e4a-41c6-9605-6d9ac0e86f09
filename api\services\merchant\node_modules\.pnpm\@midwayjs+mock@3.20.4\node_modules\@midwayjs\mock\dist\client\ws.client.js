"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWebSocketClient = void 0;
async function createWebSocketClient(address, options) {
    const WebSocket = require('ws');
    const client = new WebSocket(address, options);
    return new Promise(resolve => {
        client.on('open', () => {
            resolve(client);
        });
    });
}
exports.createWebSocketClient = createWebSocketClient;
//# sourceMappingURL=ws.client.js.map