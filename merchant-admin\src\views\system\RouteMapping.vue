<template>
  <div class="route-mapping-container">
    <div class="header">
      <h2>路由映射管理</h2>
      <div class="actions">
        <el-button @click="refreshMappings" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新映射
        </el-button>
        <el-button @click="checkHealth" type="primary">
          <el-icon><Monitor /></el-icon>
          健康检查
        </el-button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-cards">
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.totalMappings }}</div>
          <div class="stat-label">总映射数</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.activeRedirects }}</div>
          <div class="stat-label">活跃重定向</div>
        </div>
      </el-card>
      <el-card class="stat-card">
        <div class="stat-content">
          <div class="stat-number">{{ stats.healthIssues }}</div>
          <div class="stat-label">健康问题</div>
        </div>
      </el-card>
    </div>

    <!-- 搜索和过滤 -->
    <div class="filters">
      <el-input
        v-model="searchQuery"
        placeholder="搜索路由标识符或路径"
        style="width: 300px"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 150px">
        <el-option label="全部" value="all" />
        <el-option label="正常" value="normal" />
        <el-option label="重定向" value="redirect" />
        <el-option label="异常" value="error" />
      </el-select>
    </div>

    <!-- 路由映射表格 -->
    <el-table :data="filteredMappings" v-loading="loading" stripe>
      <el-table-column prop="routeKey" label="路由标识符" width="200">
        <template #default="{ row }">
          <el-tag type="info" size="small">{{ row.routeKey }}</el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="name" label="路由名称" width="150" />
      
      <el-table-column label="路径状态" width="120">
        <template #default="{ row }">
          <el-tag 
            :type="getStatusType(row.status)" 
            size="small"
          >
            {{ getStatusText(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      
      <el-table-column prop="defaultPath" label="默认路径" width="200">
        <template #default="{ row }">
          <code class="path-code">{{ row.defaultPath }}</code>
        </template>
      </el-table-column>
      
      <el-table-column prop="currentPath" label="当前路径" width="200">
        <template #default="{ row }">
          <code class="path-code" :class="{ 'path-changed': row.status === 'redirect' }">
            {{ row.currentPath }}
          </code>
        </template>
      </el-table-column>
      
      <el-table-column prop="component" label="组件" width="150" />
      
      <el-table-column label="操作" width="200">
        <template #default="{ row }">
          <el-button 
            size="small" 
            @click="testRoute(row)"
            :disabled="!row.currentPath"
          >
            测试访问
          </el-button>
          <el-button 
            size="small" 
            type="primary" 
            @click="showDetails(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 健康检查结果对话框 -->
    <el-dialog v-model="healthDialogVisible" title="路由健康检查报告" width="600px">
      <div class="health-report">
        <div class="health-stats">
          <div class="health-item">
            <span class="label">总路由数:</span>
            <span class="value">{{ healthReport.totalRoutes }}</span>
          </div>
          <div class="health-item">
            <span class="label">动态路由数:</span>
            <span class="value">{{ healthReport.dynamicRoutes }}</span>
          </div>
          <div class="health-item">
            <span class="label">重定向路由数:</span>
            <span class="value">{{ healthReport.redirectRoutes }}</span>
          </div>
        </div>
        
        <div v-if="healthReport.issues.length > 0" class="health-issues">
          <h4>发现的问题:</h4>
          <ul>
            <li v-for="issue in healthReport.issues" :key="issue" class="issue-item">
              {{ issue }}
            </li>
          </ul>
        </div>
        
        <div v-else class="health-success">
          <el-icon color="#67C23A" size="24"><SuccessFilled /></el-icon>
          <span>路由健康状态良好</span>
        </div>
      </div>
    </el-dialog>

    <!-- 路由详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="路由详情" width="500px">
      <div v-if="selectedRoute" class="route-details">
        <div class="detail-item">
          <span class="label">路由标识符:</span>
          <span class="value">{{ selectedRoute.routeKey }}</span>
        </div>
        <div class="detail-item">
          <span class="label">路由名称:</span>
          <span class="value">{{ selectedRoute.name }}</span>
        </div>
        <div class="detail-item">
          <span class="label">组件:</span>
          <span class="value">{{ selectedRoute.component }}</span>
        </div>
        <div class="detail-item">
          <span class="label">默认路径:</span>
          <code class="value">{{ selectedRoute.defaultPath }}</code>
        </div>
        <div class="detail-item">
          <span class="label">当前路径:</span>
          <code class="value">{{ selectedRoute.currentPath }}</code>
        </div>
        <div class="detail-item">
          <span class="label">元数据:</span>
          <pre class="value">{{ JSON.stringify(selectedRoute.meta, null, 2) }}</pre>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Monitor, Search, SuccessFilled } from '@element-plus/icons-vue'
import { routeMapper } from '@/router/utils/routeMapping'
import { routeHealthChecker, routeDebugger } from '@/router/guards/routeRedirect'

interface MappingDisplay {
  routeKey: string
  name: string
  component: string
  defaultPath: string
  currentPath: string
  status: 'normal' | 'redirect' | 'error'
  meta: any
}

const router = useRouter()
const loading = ref(false)
const searchQuery = ref('')
const statusFilter = ref('all')
const healthDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const selectedRoute = ref<MappingDisplay | null>(null)

const mappings = ref<MappingDisplay[]>([])
const healthReport = ref({
  totalRoutes: 0,
  dynamicRoutes: 0,
  redirectRoutes: 0,
  issues: [] as string[]
})

// 统计信息
const stats = computed(() => ({
  totalMappings: mappings.value.length,
  activeRedirects: mappings.value.filter(m => m.status === 'redirect').length,
  healthIssues: healthReport.value.issues.length
}))

// 过滤后的映射
const filteredMappings = computed(() => {
  let filtered = mappings.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(m => 
      m.routeKey.toLowerCase().includes(query) ||
      m.defaultPath.toLowerCase().includes(query) ||
      m.currentPath.toLowerCase().includes(query) ||
      m.name.toLowerCase().includes(query)
    )
  }

  // 状态过滤
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(m => m.status === statusFilter.value)
  }

  return filtered
})

// 获取状态类型
const getStatusType = (status: string) => {
  switch (status) {
    case 'normal': return 'success'
    case 'redirect': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'normal': return '正常'
    case 'redirect': return '重定向'
    case 'error': return '异常'
    default: return '未知'
  }
}

// 刷新映射数据
const refreshMappings = async () => {
  loading.value = true
  try {
    const allMappings = routeMapper.getAllMappings()
    mappings.value = Object.values(allMappings).map(mapping => {
      const currentPath = routeMapper.getPathByRouteKey(mapping.routeKey)
      const status = currentPath === mapping.defaultPath ? 'normal' : 
                    currentPath ? 'redirect' : 'error'
      
      return {
        routeKey: mapping.routeKey,
        name: mapping.name,
        component: mapping.component,
        defaultPath: mapping.defaultPath || '',
        currentPath: currentPath || '',
        status,
        meta: mapping.meta
      }
    })
    
    ElMessage.success('映射数据已刷新')
  } catch (error) {
    console.error('刷新映射失败:', error)
    ElMessage.error('刷新映射失败')
  } finally {
    loading.value = false
  }
}

// 健康检查
const checkHealth = () => {
  healthReport.value = routeHealthChecker.checkHealth(router)
  healthDialogVisible.value = true
}

// 测试路由
const testRoute = (route: MappingDisplay) => {
  if (route.currentPath) {
    router.push(route.currentPath)
    ElMessage.success(`正在跳转到: ${route.currentPath}`)
  }
}

// 显示详情
const showDetails = (route: MappingDisplay) => {
  selectedRoute.value = route
  detailDialogVisible.value = true
}

onMounted(() => {
  refreshMappings()
})
</script>

<style scoped>
.route-mapping-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.stats-cards {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
}

.stat-content {
  text-align: center;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.stat-label {
  color: #666;
  margin-top: 5px;
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.path-code {
  background: #f5f5f5;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 12px;
}

.path-changed {
  background: #fff3cd;
  color: #856404;
}

.health-report {
  padding: 10px 0;
}

.health-stats {
  margin-bottom: 20px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.health-item .label {
  font-weight: bold;
}

.health-issues h4 {
  color: #E6A23C;
  margin-bottom: 10px;
}

.issue-item {
  color: #E6A23C;
  margin-bottom: 5px;
}

.health-success {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #67C23A;
  font-weight: bold;
}

.route-details {
  padding: 10px 0;
}

.detail-item {
  display: flex;
  margin-bottom: 15px;
}

.detail-item .label {
  width: 100px;
  font-weight: bold;
  flex-shrink: 0;
}

.detail-item .value {
  flex: 1;
  word-break: break-all;
}

.detail-item pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: pre-wrap;
}
</style>
