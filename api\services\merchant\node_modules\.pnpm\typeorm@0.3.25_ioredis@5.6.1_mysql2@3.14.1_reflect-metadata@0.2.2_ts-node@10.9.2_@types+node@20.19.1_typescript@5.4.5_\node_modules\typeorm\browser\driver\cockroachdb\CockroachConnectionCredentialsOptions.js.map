{"version": 3, "sources": ["../browser/src/driver/cockroachdb/CockroachConnectionCredentialsOptions.ts"], "names": [], "mappings": "", "file": "CockroachConnectionCredentialsOptions.js", "sourcesContent": ["import { TlsOptions } from \"tls\"\n\n/**\n * Cockroachdb specific connection credential options.\n */\nexport interface CockroachConnectionCredentialsOptions {\n    /**\n     * Connection url where the connection is performed.\n     */\n    readonly url?: string\n\n    /**\n     * Database host.\n     */\n    readonly host?: string\n\n    /**\n     * Database host port.\n     */\n    readonly port?: number\n\n    /**\n     * Database username.\n     */\n    readonly username?: string\n\n    /**\n     * Database password.\n     */\n    readonly password?: string\n\n    /**\n     * Database name to connect to.\n     */\n    readonly database?: string\n\n    /**\n     * Object with ssl parameters\n     */\n    readonly ssl?: boolean | TlsOptions\n}\n"], "sourceRoot": "../.."}