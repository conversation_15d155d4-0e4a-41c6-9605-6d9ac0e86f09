"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterManager = void 0;
const decorator_1 = require("../decorator");
const util_1 = require("../util");
class FilterManager {
    constructor() {
        this.errFilterList = [];
        this.successFilterList = [];
        this.exceptionMap = new WeakMap();
        this.defaultErrFilter = undefined;
        this.matchFnList = [];
        this.protoMatchList = [];
    }
    useFilter(Filters) {
        if (!Array.isArray(Filters)) {
            Filters = [Filters];
        }
        for (const Filter of Filters) {
            if ((0, decorator_1.getClassMetadata)(decorator_1.CATCH_KEY, Filter)) {
                this.errFilterList.push(Filter);
            }
            if ((0, decorator_1.getClassMetadata)(decorator_1.MATCH_KEY, Filter)) {
                this.successFilterList.push(Filter);
            }
        }
    }
    async init(applicationContext) {
        // for catch exception
        for (const FilterClass of this.errFilterList) {
            const filter = await applicationContext.getAsync(FilterClass);
            const exceptionMetadata = (0, decorator_1.getClassMetadata)(decorator_1.CATCH_KEY, FilterClass);
            if (exceptionMetadata && exceptionMetadata.catchTargets) {
                exceptionMetadata.catchOptions = exceptionMetadata.catchOptions || {};
                for (const Exception of exceptionMetadata.catchTargets) {
                    this.exceptionMap.set(Exception, {
                        filter,
                        catchOptions: exceptionMetadata.catchOptions,
                    });
                    if (exceptionMetadata.catchOptions['matchPrototype']) {
                        this.protoMatchList.push(err => {
                            if (err instanceof Exception) {
                                return Exception;
                            }
                            else {
                                return false;
                            }
                        });
                    }
                }
            }
            else {
                // default filter
                this.defaultErrFilter = filter;
            }
        }
        // for success return
        for (const FilterClass of this.successFilterList) {
            const filter = await applicationContext.getAsync(FilterClass);
            const matchMetadata = (0, decorator_1.getClassMetadata)(decorator_1.MATCH_KEY, FilterClass);
            if (matchMetadata && matchMetadata.matchPattern) {
                this.matchFnList.push({
                    matchFn: (0, util_1.toPathMatch)(matchMetadata.matchPattern),
                    target: filter,
                });
            }
        }
    }
    async runErrorFilter(err, ctx, res, next) {
        let result, error;
        let matched = false;
        if (this.exceptionMap.has(err.constructor)) {
            matched = true;
            const filterData = this.exceptionMap.get(err.constructor);
            result = await filterData.filter.catch(err, ctx, res, next);
        }
        // match with prototype
        if (!matched && this.protoMatchList.length) {
            let protoException;
            for (const matchPattern of this.protoMatchList) {
                protoException = matchPattern(err);
                if (protoException) {
                    break;
                }
            }
            if (protoException) {
                matched = true;
                const filterData = this.exceptionMap.get(protoException);
                result = await filterData.filter.catch(err, ctx, res, next);
            }
        }
        if (!matched && this.defaultErrFilter) {
            matched = true;
            result = await this.defaultErrFilter.catch(err, ctx, res, next);
        }
        if (!matched) {
            error = err;
        }
        return {
            result,
            error,
        };
    }
    async runResultFilter(result, ctx, res, next) {
        let returnValue = result;
        for (const matchData of this.matchFnList) {
            if (matchData.matchFn(ctx, res)) {
                returnValue = await matchData.target.match(returnValue, ctx, res, next);
            }
        }
        return {
            result: returnValue,
            error: undefined,
        };
    }
}
exports.FilterManager = FilterManager;
//# sourceMappingURL=filterManager.js.map