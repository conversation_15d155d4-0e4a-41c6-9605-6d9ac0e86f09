/// <reference types="node" />
import { ThreadOptions } from '../interface';
import { AbstractForkManager } from './base';
import { Worker } from 'worker_threads';
export declare class ThreadManager extends AbstractForkManager<Worker, ThreadOptions> {
    readonly options: ThreadOptions;
    private workerExitListener;
    constructor(options?: ThreadOptions);
    createWorker(): Worker;
    bindWorkerDisconnect(listener: (worker: Worker) => void): void;
    bindWorkerExit(listener: (worker: Worker, code: any, signal: any) => void): void;
    getWorkerId(worker: Worker): any;
    isWorkerDead(worker: Worker): boolean;
    closeWorker(worker: Worker): Promise<void>;
    createEventBus(options: any): any;
    isPrimary(): boolean;
}
//# sourceMappingURL=thread.d.ts.map