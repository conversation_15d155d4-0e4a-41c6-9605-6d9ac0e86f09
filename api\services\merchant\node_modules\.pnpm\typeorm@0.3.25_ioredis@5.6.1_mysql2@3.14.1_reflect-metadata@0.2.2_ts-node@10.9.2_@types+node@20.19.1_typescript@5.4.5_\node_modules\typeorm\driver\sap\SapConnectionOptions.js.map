{"version": 3, "sources": ["../../src/driver/sap/SapConnectionOptions.ts"], "names": [], "mappings": "", "file": "SapConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { SapConnectionCredentialsOptions } from \"./SapConnectionCredentialsOptions\"\n\n/**\n * SAP Hana specific connection options.\n */\nexport interface SapConnectionOptions\n    extends BaseDataSourceOptions,\n        SapConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"sap\"\n\n    /**\n     * Database schema.\n     */\n    readonly schema?: string\n\n    /**\n     * The driver objects\n     * This defaults to require(\"hdb-pool\")\n     */\n    readonly driver?: any\n\n    /**\n     * The driver objects\n     * This defaults to require(\"@sap/hana-client\")\n     */\n    readonly hanaClientDriver?: any\n\n    /**\n     * Pool options.\n     */\n    readonly pool?: {\n        /**\n         * Max number of connections.\n         */\n        readonly max?: number\n\n        /**\n         * Minimum number of connections.\n         */\n        readonly min?: number\n\n        /**\n         * Maximum number of waiting requests allowed. (default=0, no limit).\n         */\n        readonly maxWaitingRequests?: number\n        /**\n         * Max milliseconds a request will wait for a resource before timing out. (default=5000)\n         */\n        readonly requestTimeout?: number\n        /**\n         * How often to run resource timeout checks. (default=0, disabled)\n         */\n        readonly checkInterval?: number\n        /**\n         * Idle timeout\n         */\n        readonly idleTimeout?: number\n\n        /**\n         * Function handling errors thrown by drivers pool.\n         * Defaults to logging error with `warn` level.\n         */\n        readonly poolErrorHandler?: (err: any) => any\n    }\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}