{"version": 3, "sources": ["../browser/src/find-options/operator/Between.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,OAAO,CACnB,IAAyB,EACzB,EAAuB;IAEvB,OAAO,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE,CAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AACrE,CAAC", "file": "Between.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Between(x, y) }\n */\nexport function Between<T>(\n    from: T | FindOperator<T>,\n    to: T | FindOperator<T>,\n): FindOperator<T> {\n    return new FindOperator(\"between\", [from, to] as any, true, true)\n}\n"], "sourceRoot": "../.."}