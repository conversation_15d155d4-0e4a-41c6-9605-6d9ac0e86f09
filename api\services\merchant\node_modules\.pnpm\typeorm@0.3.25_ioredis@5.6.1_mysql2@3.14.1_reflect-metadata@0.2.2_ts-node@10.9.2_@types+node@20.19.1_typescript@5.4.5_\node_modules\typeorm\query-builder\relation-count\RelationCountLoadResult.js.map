{"version": 3, "sources": ["../../src/query-builder/relation-count/RelationCountLoadResult.ts"], "names": [], "mappings": "", "file": "RelationCountLoadResult.js", "sourcesContent": ["import { RelationCountAttribute } from \"./RelationCountAttribute\"\n\nexport interface RelationCountLoadResult {\n    relationCountAttribute: RelationCountAttribute\n    results: { cnt: any; parentId: any }[]\n}\n"], "sourceRoot": "../.."}