"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createRender = exports.ContentType = exports.SetHeader = exports.HttpCode = exports.Redirect = void 0;
const __1 = require("../");
function Redirect(url, code = 302) {
    return (target, key, descriptor) => {
        (0, __1.attachPropertyMetadata)(__1.WEB_RESPONSE_KEY, {
            type: __1.WEB_RESPONSE_REDIRECT,
            url,
            code,
        }, target, key);
        return descriptor;
    };
}
exports.Redirect = Redirect;
function HttpCode(code) {
    return (target, key, descriptor) => {
        (0, __1.attachPropertyMetadata)(__1.WEB_RESPONSE_KEY, {
            type: __1.WEB_RESPONSE_HTTP_CODE,
            code,
        }, target, key);
        return descriptor;
    };
}
exports.HttpCode = HttpCode;
function SetHeader(headerKey, value) {
    return (target, key, descriptor) => {
        let headerObject = {};
        if (value) {
            headerObject[headerKey] = value;
        }
        else {
            headerObject = headerKey;
        }
        (0, __1.attachPropertyMetadata)(__1.WEB_RESPONSE_KEY, {
            type: __1.WEB_RESPONSE_HEADER,
            setHeaders: headerObject,
        }, target, key);
        return descriptor;
    };
}
exports.SetHeader = SetHeader;
function ContentType(contentType) {
    return (target, key, descriptor) => {
        (0, __1.attachPropertyMetadata)(__1.WEB_RESPONSE_KEY, {
            type: __1.WEB_RESPONSE_CONTENT_TYPE,
            contentType,
        }, target, key);
        return descriptor;
    };
}
exports.ContentType = ContentType;
function createRender(RenderEngine) {
    return (templateName) => {
        return (target, key, descriptor) => {
            (0, __1.attachPropertyMetadata)(__1.WEB_RESPONSE_KEY, {
                type: __1.WEB_RESPONSE_RENDER,
                templateName,
            }, target, key);
            return descriptor;
        };
    };
}
exports.createRender = createRender;
//# sourceMappingURL=response.js.map