{"version": 3, "sources": ["../browser/src/error/FindRelationsNotFoundError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,YAAY;IACxD,YAAY,iBAA2B;QACnC,KAAK,EAAE,CAAA;QAEP,IAAI,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,IAAI,CAAC,OAAO,GAAG,aAAa,iBAAiB,CAAC,CAAC,CAAC,kFAAkF,CAAA;QACtI,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,OAAO,GAAG,aAAa,iBAAiB;iBACxC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,QAAQ,GAAG,CAAC;iBAClC,IAAI,CACD,IAAI,CACP,yFAAyF,CAAA;QAClG,CAAC;IACL,CAAC;CACJ", "file": "FindRelationsNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when relations specified in the find options were not found in the entities.\n */\nexport class FindRelationsNotFoundError extends TypeORMError {\n    constructor(notFoundRelations: string[]) {\n        super()\n\n        if (notFoundRelations.length === 1) {\n            this.message = `Relation \"${notFoundRelations[0]}\" was not found; please check if it is correct and really exists in your entity.`\n        } else {\n            this.message = `Relations ${notFoundRelations\n                .map((relation) => `\"${relation}\"`)\n                .join(\n                    \", \",\n                )} were not found; please check if relations are correct and they exist in your entities.`\n        }\n    }\n}\n"], "sourceRoot": ".."}