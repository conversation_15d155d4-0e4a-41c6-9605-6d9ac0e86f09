{"version": 3, "file": "builder.js", "sourceRoot": "", "sources": ["../../src/scf/builder.ts"], "names": [], "mappings": ";;;AAAA,oCASkB;AAUlB,oCAA6E;AAEhE,QAAA,aAAa,GAAG;IAC3B,OAAO,EAAE,YAAY;IACrB,OAAO,EAAE,WAAW;IACpB,QAAQ,EAAE,aAAa;IACvB,QAAQ,EAAE,aAAa;CACxB,CAAC;AAEF,SAAS,gBAAgB,CAAC,OAAO;IAC/B,IAAI,qBAAa,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,qBAAa,CAAC,OAAO,CAAC,CAAC;KAC/B;IACD,IAAI,OAAO,EAAE;QACX,OAAO,OAAO,CAAC;KAChB;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,MAAa,wBAAyB,SAAQ,mBAAW;IACvD,MAAM;;QACJ,MAAM,YAAY,GAAsB,IAAI,CAAC,WAAW,EAAE,CAAC;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QACtC,MAAM,aAAa,GAAuB,IAAI,CAAC,YAAY,EAAE,CAAC;QAC9D,MAAM,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC;QACrC,MAAM,cAAc,GAAG,IAAA,4BAAoB,GAAE,CAAC;QAE9C,MAAM,UAAU,GAAoC;YAClD,OAAO,EAAE,WAAW;YACpB,QAAQ,EAAE;gBACR,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC;gBAC/C,MAAM,EAAE,YAAY,CAAC,MAAM;gBAC3B,WAAW,EAAG,YAAoB,CAAC,WAAW;gBAC9C,KAAK,EAAE,YAAY,CAAC,KAAK;gBACzB,IAAI,EAAE,YAAY,CAAC,IAAI;gBACvB,UAAU,EAAE,YAAY,CAAC,UAAU,IAAI,GAAG;gBAC1C,WAAW,EAAE;oBACX,SAAS,EAAE;wBACT,GAAG,YAAY,CAAC,WAAW;wBAC3B,GAAG,cAAc;qBAClB;iBACF;gBACD,OAAO,EAAE,YAAY,CAAC,OAAO,IAAI,CAAC;aACnC;YACD,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE;SAC3B,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,aAAa,EAAE;YACnC,MAAM,OAAO,GAAG,aAAa,CAAC,OAAO,CAAsB,CAAC;YAE5D,MAAM,gBAAgB,GAAyB;gBAC7C,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,oBAAoB;gBAChD,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACtC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO;gBACvD,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO;gBACvD,UAAU,EAAE,OAAO,CAAC,UAAU,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU;gBAChE,WAAW,EAAE;oBACX,SAAS,EAAE;wBACT,GAAG,OAAO,CAAC,WAAW;qBACvB;iBACF;gBACD,MAAM,EAAE,EAAE;aACX,CAAC;YAEF,KAAK,MAAM,KAAK,IAAI,MAAA,OAAO,CAAC,QAAQ,CAAC,mCAAI,EAAE,EAAE;gBAC3C,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBACnC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAc,CAAC;oBAC3D,MAAM,UAAU,GAAuB;wBACrC,IAAI,EAAE,GAAG,OAAO,UAAU,YAAY,CAAC,KAAK,IAAI,KAAK,EAAE;wBACvD,UAAU,EAAE;4BACV,UAAU,EAAE,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC;4BACtC,IAAI,EAAE,GAAG,CAAC,IAAI;4BACd,cAAc,EAAE,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO;4BAC9C,SAAS,EAAE,OAAO,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK;4BAC9C,SAAS,EAAE,GAAG,CAAC,SAAS,IAAI,YAAY,CAAC,SAAS;4BAClD,kBAAkB,EAAE,GAAG,CAAC,kBAAkB,IAAI,IAAI;4BAClD,UAAU,EAAE,GAAG,CAAC,IAAI;yBACrB;qBACF,CAAC;oBAEF,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;wBAC3B,KAAK,EAAE,UAAU;qBAClB,CAAC,CAAC;iBACJ;gBAED,IAAI,KAAK,CAAC,OAAO,CAAC,EAAE;oBAClB,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAe,CAAC;oBACzC,MAAM,KAAK,GAAkB;wBAC3B,IAAI,EAAE,OAAO;wBACb,UAAU,EAAE;4BACV,cAAc,EAAE,GAAG,CAAC,KAAK;4BACzB,MAAM,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;yBAC5C;qBACF,CAAC;oBAEF,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC;wBAC3B,KAAK;qBACN,CAAC,CAAC;iBACJ;gBAED,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,EAAE;oBAC/B,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,CAAY,CAAC;oBACrD,MAAM,GAAG,GAAgB;wBACvB,IAAI,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK;wBACvB,UAAU,EAAE;4BACV,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,MAAM,EAAE,GAAG,CAAC,MAAM;4BAClB,MAAM,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;yBAC5C;qBACF,CAAC;oBACF,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;iBACvC;gBAED,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC/B,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAY,CAAC;oBACrD,MAAM,GAAG,GAAgB;wBACvB,IAAI,EAAE,KAAK;wBACX,UAAU,EAAE;4BACV,IAAI,EAAE,GAAG,CAAC,KAAK;4BACf,MAAM,EAAE,GAAG,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;yBAC5C;qBACF,CAAC;oBACF,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;iBACvC;gBAED,wBAAwB;gBACxB,6CAA6C;gBAC7C,8CAA8C;gBAC9C,IAAI;aACL;YAED,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,GAAG,gBAAgB,CAAC;SAClD;QAED,OAAO,IAAA,mCAA2B,EAAC,UAAU,CAAC,CAAC;IACjD,CAAC;CACF;AAxHD,4DAwHC;AAED,SAAgB,cAAc,CAAC,MAAyB;IACtD,4DAA4D;IAC5D,MAAM,cAAc,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IACvE,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,GAAG,KAAK,CAAC;KAChB;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACzB,eAAe;QACf,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;KACpB;IAED,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;QAClC,MAAM,GAAG,KAAK,CAAC;KAChB;IAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,EAAmB,CAAC;IAC1D,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;QACxC,OAAO,WAAW,CAAC;KACpB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AApBD,wCAoBC"}