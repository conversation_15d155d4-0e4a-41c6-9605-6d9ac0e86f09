{"version": 3, "file": "binding_when_syntax.js", "sourceRoot": "", "sources": ["../../src/syntax/binding_when_syntax.ts"], "names": [], "mappings": ";;;;IAIA;QAIE,2BAAmB,OAA8B;YAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;QAEM,gCAAI,GAAX,UAAY,UAAoD;YAC9D,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAA2C,CAAC;YACvE,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,2CAAe,GAAtB,UAAuB,IAA8B;YACnD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAA,oCAAe,EAAC,IAAI,CAAC,CAAC;YACjD,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,+CAAmB,GAA1B;YAEE,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAE5D,IAAI,OAAO,KAAK,IAAI,EAAE;oBACpB,OAAO,KAAK,CAAC;iBACd;gBAED,IAAM,eAAe,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;oBAC/C,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;oBAC3B,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAE/B,OAAO,eAAe,CAAC;YACzB,CAAC,CAAC;YAEF,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,4CAAgB,GAAvB,UAAwB,GAA6B,EAAE,KAAc;YACnE,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAA,qCAAgB,EAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YACxD,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,4CAAgB,GAAvB,UAAwB,MAAkC;YACxD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,mCAAc,EAAC,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YAAjE,CAAiE,CAAC;YAEpE,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,2CAAe,GAAtB,UAAuB,IAA8B;YACnD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,oCAAe,EAAC,IAAI,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YAAhE,CAAgE,CAAC;YAEnE,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,4CAAgB,GAAvB,UAAwB,GAA6B,EAAE,KAAc;YACnE,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,qCAAgB,EAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC;YAAvE,CAAuE,CAAC;YAE1E,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,6CAAiB,GAAxB,UAAyB,QAAoC;YAC3D,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,mCAAc,EAAC,QAAQ,CAAC,CAAC;YAAzE,CAAyE,CAAC;YAE5E,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,4CAAgB,GAAvB,UAAwB,QAAoC;YAC1D,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,mCAAc,EAAC,QAAQ,CAAC,CAAC;YAA1E,CAA0E,CAAC;YAE7E,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,gDAAoB,GAA3B,UAA4B,IAA8B;YAExD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,oCAAe,EAAC,IAAI,CAAC,CAAC;YAAtE,CAAsE,CAAC;YAEzE,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,+CAAmB,GAA1B,UAA2B,IAA8B;YAEvD,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,oCAAe,EAAC,IAAI,CAAC,CAAC;YAAvE,CAAuE,CAAC;YAE1E,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,iDAAqB,GAA5B,UAA6B,GAA6B,EAAE,KAAc;YAExE,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,qCAAgB,EAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAA7E,CAA6E,CAAC;YAEhF,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,gDAAoB,GAA3B,UAA4B,GAA6B,EAAE,KAAc;YAEvE,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAA,uCAAkB,EAAC,OAAO,EAAE,IAAA,qCAAgB,EAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;YAA9E,CAA8E,CAAC;YAEjF,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,kDAAsB,GAA7B,UAA8B,UAAoD;YAEhF,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,IAAA,uCAAkB,EAAC,OAAO,EAAE,UAA2C,CAAC;YAA5F,CAA4F,CAAC;YAE/F,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEM,iDAAqB,GAA5B,UAA6B,UAAoD;YAE/E,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,UAAC,OAAkC;gBAC5D,OAAA,OAAO,KAAK,IAAI,IAAI,CAAC,IAAA,uCAAkB,EAAC,OAAO,EAAE,UAA2C,CAAC;YAA7F,CAA6F,CAAC;YAEhG,OAAO,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAEH,wBAAC;IAAD,CAAC,AA5HD,IA4HC;IAEQ,8CAAiB"}