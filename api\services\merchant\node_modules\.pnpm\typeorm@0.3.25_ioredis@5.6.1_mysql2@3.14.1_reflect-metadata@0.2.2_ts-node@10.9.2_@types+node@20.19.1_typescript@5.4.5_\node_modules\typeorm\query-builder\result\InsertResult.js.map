{"version": 3, "sources": ["../../src/query-builder/result/InsertResult.ts"], "names": [], "mappings": ";;;AAGA;;GAEG;AACH,MAAa,YAAY;IAAzB;QAOI;;;WAGG;QACH,gBAAW,GAAoB,EAAE,CAAA;QAEjC;;;WAGG;QACH,kBAAa,GAAoB,EAAE,CAAA;IAMvC,CAAC;IAtBG,MAAM,CAAC,IAAI,CAAC,WAAwB;QAChC,MAAM,MAAM,GAAG,IAAI,IAAI,EAAE,CAAA;QACzB,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAA;QAC5B,OAAO,MAAM,CAAA;IACjB,CAAC;CAkBJ;AAvBD,oCAuBC", "file": "InsertResult.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\n\n/**\n * Result object returned by InsertQueryBuilder execution.\n */\nexport class InsertResult {\n    static from(queryResult: QueryResult) {\n        const result = new this()\n        result.raw = queryResult.raw\n        return result\n    }\n\n    /**\n     * Contains inserted entity id.\n     * Has entity-like structure (not just column database name and values).\n     */\n    identifiers: ObjectLiteral[] = []\n\n    /**\n     * Generated values returned by a database.\n     * Has entity-like structure (not just column database name and values).\n     */\n    generatedMaps: ObjectLiteral[] = []\n\n    /**\n     * Raw SQL result returned by executed query.\n     */\n    raw: any\n}\n"], "sourceRoot": "../.."}