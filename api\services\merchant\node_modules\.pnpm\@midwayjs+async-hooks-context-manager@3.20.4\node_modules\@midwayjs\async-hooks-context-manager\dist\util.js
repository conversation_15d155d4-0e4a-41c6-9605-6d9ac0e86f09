"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createContextManager = exports.isSemverGreaterThanOrEqualTo = void 0;
const asyncLocalStorageContextManager_1 = require("./asyncLocalStorageContextManager");
const asyncHooksContextManager_1 = require("./asyncHooksContextManager");
const semver = /^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z-]+(?:\.[\da-z-]+)*))?(?:\+[\da-z-]+(?:\.[\da-z-]+)*)?)?)?$/i;
// 判断 semver 大于等于 v14.8.0
function isSemverGreaterThanOrEqualTo(currentVersion, targetVersion) {
    const v = semver.exec(currentVersion);
    const t = semver.exec(targetVersion);
    if (v && t) {
        if (v[1] === t[1] && v[2] === t[2] && v[3] === t[3] && v[4] === t[4]) {
            return true;
        }
        return (gteString(v[1], t[1]) ||
            (v[1] === t[1] && gteString(v[2], t[2])) ||
            (v[1] === t[1] && v[2] === t[2] && gteString(v[3], t[3])) ||
            (v[1] === t[1] && v[2] === t[2] && v[3] === t[3] && gteString(v[4], t[4])));
    }
    return false;
}
exports.isSemverGreaterThanOrEqualTo = isSemverGreaterThanOrEqualTo;
function gteString(v1, v2) {
    // compare string with parseInt
    const v1Int = parseInt(v1, 10);
    const v2Int = parseInt(v2, 10);
    return v1Int > v2Int;
}
function createContextManager() {
    const ContextManager = isSemverGreaterThanOrEqualTo(process.version, '14.8.0')
        ? asyncLocalStorageContextManager_1.AsyncLocalStorageContextManager
        : asyncHooksContextManager_1.AsyncHooksContextManager;
    return new ContextManager();
}
exports.createContextManager = createContextManager;
//# sourceMappingURL=util.js.map