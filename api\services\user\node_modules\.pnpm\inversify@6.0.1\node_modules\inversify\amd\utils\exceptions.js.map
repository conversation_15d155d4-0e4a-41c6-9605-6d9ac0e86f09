{"version": 3, "file": "exceptions.js", "sourceRoot": "", "sources": ["../../src/utils/exceptions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAEA,SAAgB,uBAAuB,CAAC,KAAY;QAClD,OAAO,CACL,KAAK,YAAY,UAAU;YAC3B,KAAK,CAAC,OAAO,KAAK,UAAU,CAAC,cAAc,CAC5C,CAAC;IACJ,CAAC;IALD,0DAKC;IAEM,IAAM,+BAA+B,GAAG,UAAI,EAAW,EAAE,aAA0B;QACxF,IAAI;YACF,OAAO,EAAE,EAAE,CAAC;SACb;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,uBAAuB,CAAC,KAAK,CAAC,EAAE;gBAClC,KAAK,GAAG,aAAa,EAAE,CAAC;aACzB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC,CAAA;IATY,QAAA,+BAA+B,mCAS3C"}