{"version": 3, "sources": ["../browser/src/metadata-args/MetadataArgsStorage.ts"], "names": [], "mappings": "AAiBA,OAAO,EAAE,aAAa,EAAE,MAAM,mCAAmC,CAAA;AAQjE;;;;GAIG;AACH,MAAM,OAAO,mBAAmB;IAAhC;QACI,4EAA4E;QAC5E,aAAa;QACb,4EAA4E;QAEnE,WAAM,GAAwB,EAAE,CAAA;QAChC,UAAK,GAAuB,EAAE,CAAA;QAC9B,uBAAkB,GAAmC,EAAE,CAAA;QACvD,8BAAyB,GAAoC,EAAE,CAAA;QAC/D,4BAAuB,GAAwC,EAAE,CAAA;QACjE,qBAAgB,GAAiC,EAAE,CAAA;QACnD,sBAAiB,GAAmC,EAAE,CAAA;QACtD,YAAO,GAAwB,EAAE,CAAA;QACjC,gBAAW,GAA6B,EAAE,CAAA;QAC1C,YAAO,GAAyB,EAAE,CAAA;QAClC,WAAM,GAAwB,EAAE,CAAA;QAChC,eAAU,GAA4B,EAAE,CAAA;QACxC,YAAO,GAAyB,EAAE,CAAA;QAClC,gBAAW,GAA4B,EAAE,CAAA;QACzC,cAAS,GAA2B,EAAE,CAAA;QACtC,gBAAW,GAA6B,EAAE,CAAA;QAC1C,eAAU,GAA4B,EAAE,CAAA;QACxC,oBAAe,GAAiC,EAAE,CAAA;QAClD,mBAAc,GAAgC,EAAE,CAAA;QAChD,gBAAW,GAA6B,EAAE,CAAA;QAC1C,cAAS,GAA2B,EAAE,CAAA;QACtC,iBAAY,GAA8B,EAAE,CAAA;QAC5C,wBAAmB,GAAqC,EAAE,CAAA;IAiXvE,CAAC;IAzWG,YAAY,CACR,MAAmD;QAEnD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IACnD,CAAC;IAID,aAAa,CACT,MAAmD;QAEnD,OAAO,IAAI,CAAC,2CAA2C,CACnD,IAAI,CAAC,OAAO,EACZ,MAAM,CACT,CAAA;IACL,CAAC;IAUD,aAAa,CACT,MAAmD,EACnD,YAAoB;QAEpB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YACvC,OAAO,CACH,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACzC,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,MAAM,CAAC;gBAClC,SAAS,CAAC,YAAY,KAAK,YAAY,CAC1C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,QAAQ,CACJ,MAAmD;QAEnD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YAC5B,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAA;QAChC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,eAAe,CACX,MAAmD;QAEnD,OAAO,IAAI,CAAC,mDAAmD,CAC3D,IAAI,CAAC,SAAS,EACd,MAAM,CACT,CAAA;IACL,CAAC;IAID,iBAAiB,CACb,MAAmD;QAEnD,OAAO,IAAI,CAAC,2CAA2C,CACnD,IAAI,CAAC,WAAW,EAChB,MAAM,CACT,CAAA;IACL,CAAC;IAMD,oBAAoB,CAChB,MAAmD;QAEnD,OAAO,IAAI,CAAC,2CAA2C,CACnD,IAAI,CAAC,cAAc,EACnB,MAAM,CACT,CAAA;IACL,CAAC;IAID,aAAa,CACT,MAAmD;QAEnD,2CAA2C;QAC3C,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YACjC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrC,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAA;QACjC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,iBAAiB,CACb,MAAmD;QAEnD,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;YAC1C,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC1C,CAAC,CAAC,UAAU,CAAC,MAAM,KAAK,MAAM,CAAA;QACtC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,aAAa,CACT,MAAmD;QAEnD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAClC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC,CAAC,MAAM,CAAC,MAAM,KAAK,MAAM,CAAA;QAClC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,YAAY,CACR,MAAmD;QAEnD,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrC,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAA;QACjC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,gBAAgB,CACZ,MAAmD;QAEnD,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,EAAE;YACxC,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACzC,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,MAAM,CAAA;QACrC,CAAC,CAAC,CAAA;IACN,CAAC;IAID,eAAe,CACX,MAAmD;QAEnD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;IAC5D,CAAC;IAID,eAAe,CACX,MAAmD;QAEnD,OAAO,IAAI,CAAC,mDAAmD,CAC3D,IAAI,CAAC,SAAS,EACd,MAAM,CACT,CAAA;IACL,CAAC;IAED,aAAa,CACT,MAAyB,EACzB,YAAoB;QAEpB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YACtC,OAAO,CACH,SAAS,CAAC,MAAM,KAAK,MAAM;gBAC3B,SAAS,CAAC,YAAY,KAAK,YAAY,CAC1C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,iBAAiB,CACb,MAAyB,EACzB,YAAoB;QAEpB,2CAA2C;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;YAC1C,OAAO,CACH,UAAU,CAAC,MAAM,KAAK,MAAM;gBAC5B,UAAU,CAAC,YAAY,KAAK,YAAY,CAC3C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAMD,iBAAiB,CACb,MAAmD;QAEnD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;IAC9D,CAAC;IAQD,sBAAsB,CAClB,MAAmD;QAEnD,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAA;IAC7D,CAAC;IAED,+BAA+B,CAC3B,MAAyB,EACzB,YAAoB;QAEpB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,EAAE;YAC3D,OAAO,CACH,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC,CAAC,aAAa,CAAC,MAAM,KAAK,MAAM,CAAC;gBACtC,aAAa,CAAC,UAAU,KAAK,YAAY,CAC5C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,2BAA2B,CACvB,MAAyB,EACzB,YAAoB;QAEpB,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,EAAE;YACzD,OAAO,CACH,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBAClB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC7C,CAAC,CAAC,aAAa,CAAC,MAAM,KAAK,MAAM,CAAC;gBACtC,aAAa,CAAC,UAAU,KAAK,YAAY,CAC5C,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,yBAAyB,CAAC,MAAyB;QAC/C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,OAAO,CACH,OAAO,KAAK,CAAC,MAAM,KAAK,UAAU;gBAClC,OAAO,MAAM,KAAK,UAAU;gBAC5B,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;gBAC/C,KAAK,CAAC,IAAI,KAAK,cAAc,CAChC,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,mBAAmB,CACf,MAAyB;QAEzB,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CACzB,CAAC,WAAW,EAAE,EAAE,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CACjD,CAAA;IACL,CAAC;IAED,sBAAsB,CAClB,MAAyB;QAEzB,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAChC,CAAC,kBAAkB,EAAE,EAAE,CAAC,kBAAkB,CAAC,MAAM,KAAK,MAAM,CAC/D,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,cAAc,CACpB,KAAU,EACV,MAAmD;QAEnD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1B,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACxB,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrC,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,MAAM,CAAA;QACjC,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,2CAA2C,CAEnD,KAAU,EAAE,MAAmD;QAC7D,MAAM,QAAQ,GAAQ,EAAE,CAAA;QACxB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACnB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACpC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAA;YAC5B,IAAI,UAAU,EAAE,CAAC;gBACb,IACI,CAAC,QAAQ,CAAC,IAAI,CACV,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAC1D;oBAED,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC3B,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACO,mDAAmD,CAE3D,KAAU,EAAE,MAAmD;QAC7D,MAAM,QAAQ,GAAQ,EAAE,CAAA;QACxB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACnB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACpC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAA;YAC5B,IAAI,UAAU,EAAE,CAAC;gBACb,MAAM,aAAa,GAAG,QAAQ,CAAC,SAAS,CACpC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CAC1D,CAAA;gBACD,IACI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;oBACrB,aAAa,KAAK,CAAC,CAAC;oBACpB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC;wBACvB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,EACpD,CAAC;oBACC,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;oBACpD,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;oBACtB,QAAQ,CAAC,aAAa,CAAC,GAAG,KAAK,CAAA;gBACnC,CAAC;qBAAM,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;oBAC9B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACvB,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACO,mDAAmD,CAE3D,KAAU,EAAE,MAAmD;QAC7D,MAAM,QAAQ,GAAQ,EAAE,CAAA;QACxB,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YACnB,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;gBACpC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAA;YAC5B,IAAI,UAAU,EAAE,CAAC;gBACb,MAAM,2BAA2B,GAAG,QAAQ,CAAC,IAAI,CAC7C,CAAC,OAA6B,EAAW,EAAE,CACvC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM;oBAC9B,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,CACjD,CAAA;gBACD,IAAI,CAAC,2BAA2B;oBAAE,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACzD,CAAC;QACL,CAAC,CAAC,CAAA;QACF,OAAO,QAAQ,CAAA;IACnB,CAAC;CACJ", "file": "MetadataArgsStorage.js", "sourcesContent": ["import { RelationMetadataArgs } from \"./RelationMetadataArgs\"\nimport { ColumnMetadataArgs } from \"./ColumnMetadataArgs\"\nimport { RelationCountMetadataArgs } from \"./RelationCountMetadataArgs\"\nimport { IndexMetadataArgs } from \"./IndexMetadataArgs\"\nimport { EntityListenerMetadataArgs } from \"./EntityListenerMetadataArgs\"\nimport { TableMetadataArgs } from \"./TableMetadataArgs\"\nimport { NamingStrategyMetadataArgs } from \"./NamingStrategyMetadataArgs\"\nimport { JoinTableMetadataArgs } from \"./JoinTableMetadataArgs\"\nimport { JoinColumnMetadataArgs } from \"./JoinColumnMetadataArgs\"\nimport { EmbeddedMetadataArgs } from \"./EmbeddedMetadataArgs\"\nimport { EntitySubscriberMetadataArgs } from \"./EntitySubscriberMetadataArgs\"\nimport { RelationIdMetadataArgs } from \"./RelationIdMetadataArgs\"\nimport { InheritanceMetadataArgs } from \"./InheritanceMetadataArgs\"\nimport { DiscriminatorValueMetadataArgs } from \"./DiscriminatorValueMetadataArgs\"\nimport { EntityRepositoryMetadataArgs } from \"./EntityRepositoryMetadataArgs\"\nimport { TransactionEntityMetadataArgs } from \"./TransactionEntityMetadataArgs\"\nimport { TransactionRepositoryMetadataArgs } from \"./TransactionRepositoryMetadataArgs\"\nimport { MetadataUtils } from \"../metadata-builder/MetadataUtils\"\nimport { GeneratedMetadataArgs } from \"./GeneratedMetadataArgs\"\nimport { TreeMetadataArgs } from \"./TreeMetadataArgs\"\nimport { UniqueMetadataArgs } from \"./UniqueMetadataArgs\"\nimport { CheckMetadataArgs } from \"./CheckMetadataArgs\"\nimport { ExclusionMetadataArgs } from \"./ExclusionMetadataArgs\"\nimport { ForeignKeyMetadataArgs } from \"./ForeignKeyMetadataArgs\"\n\n/**\n * Storage all metadatas args of all available types: tables, columns, subscribers, relations, etc.\n * Each metadata args represents some specifications of what it represents.\n * MetadataArgs used to create a real Metadata objects.\n */\nexport class MetadataArgsStorage {\n    // -------------------------------------------------------------------------\n    // Properties\n    // -------------------------------------------------------------------------\n\n    readonly tables: TableMetadataArgs[] = []\n    readonly trees: TreeMetadataArgs[] = []\n    readonly entityRepositories: EntityRepositoryMetadataArgs[] = []\n    readonly transactionEntityManagers: TransactionEntityMetadataArgs[] = []\n    readonly transactionRepositories: TransactionRepositoryMetadataArgs[] = []\n    readonly namingStrategies: NamingStrategyMetadataArgs[] = []\n    readonly entitySubscribers: EntitySubscriberMetadataArgs[] = []\n    readonly indices: IndexMetadataArgs[] = []\n    readonly foreignKeys: ForeignKeyMetadataArgs[] = []\n    readonly uniques: UniqueMetadataArgs[] = []\n    readonly checks: CheckMetadataArgs[] = []\n    readonly exclusions: ExclusionMetadataArgs[] = []\n    readonly columns: ColumnMetadataArgs[] = []\n    readonly generations: GeneratedMetadataArgs[] = []\n    readonly relations: RelationMetadataArgs[] = []\n    readonly joinColumns: JoinColumnMetadataArgs[] = []\n    readonly joinTables: JoinTableMetadataArgs[] = []\n    readonly entityListeners: EntityListenerMetadataArgs[] = []\n    readonly relationCounts: RelationCountMetadataArgs[] = []\n    readonly relationIds: RelationIdMetadataArgs[] = []\n    readonly embeddeds: EmbeddedMetadataArgs[] = []\n    readonly inheritances: InheritanceMetadataArgs[] = []\n    readonly discriminatorValues: DiscriminatorValueMetadataArgs[] = []\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    filterTables(target: Function | string): TableMetadataArgs[]\n    filterTables(target: (Function | string)[]): TableMetadataArgs[]\n    filterTables(\n        target: (Function | string) | (Function | string)[],\n    ): TableMetadataArgs[] {\n        return this.filterByTarget(this.tables, target)\n    }\n\n    filterColumns(target: Function | string): ColumnMetadataArgs[]\n    filterColumns(target: (Function | string)[]): ColumnMetadataArgs[]\n    filterColumns(\n        target: (Function | string) | (Function | string)[],\n    ): ColumnMetadataArgs[] {\n        return this.filterByTargetAndWithoutDuplicateProperties(\n            this.columns,\n            target,\n        )\n    }\n\n    findGenerated(\n        target: Function | string,\n        propertyName: string,\n    ): GeneratedMetadataArgs | undefined\n    findGenerated(\n        target: (Function | string)[],\n        propertyName: string,\n    ): GeneratedMetadataArgs | undefined\n    findGenerated(\n        target: (Function | string) | (Function | string)[],\n        propertyName: string,\n    ): GeneratedMetadataArgs | undefined {\n        return this.generations.find((generated) => {\n            return (\n                (Array.isArray(target)\n                    ? target.indexOf(generated.target) !== -1\n                    : generated.target === target) &&\n                generated.propertyName === propertyName\n            )\n        })\n    }\n\n    findTree(\n        target: (Function | string) | (Function | string)[],\n    ): TreeMetadataArgs | undefined {\n        return this.trees.find((tree) => {\n            return Array.isArray(target)\n                ? target.indexOf(tree.target) !== -1\n                : tree.target === target\n        })\n    }\n\n    filterRelations(target: Function | string): RelationMetadataArgs[]\n    filterRelations(target: (Function | string)[]): RelationMetadataArgs[]\n    filterRelations(\n        target: (Function | string) | (Function | string)[],\n    ): RelationMetadataArgs[] {\n        return this.filterByTargetAndWithoutDuplicateRelationProperties(\n            this.relations,\n            target,\n        )\n    }\n\n    filterRelationIds(target: Function | string): RelationIdMetadataArgs[]\n    filterRelationIds(target: (Function | string)[]): RelationIdMetadataArgs[]\n    filterRelationIds(\n        target: (Function | string) | (Function | string)[],\n    ): RelationIdMetadataArgs[] {\n        return this.filterByTargetAndWithoutDuplicateProperties(\n            this.relationIds,\n            target,\n        )\n    }\n\n    filterRelationCounts(target: Function | string): RelationCountMetadataArgs[]\n    filterRelationCounts(\n        target: (Function | string)[],\n    ): RelationCountMetadataArgs[]\n    filterRelationCounts(\n        target: (Function | string) | (Function | string)[],\n    ): RelationCountMetadataArgs[] {\n        return this.filterByTargetAndWithoutDuplicateProperties(\n            this.relationCounts,\n            target,\n        )\n    }\n\n    filterIndices(target: Function | string): IndexMetadataArgs[]\n    filterIndices(target: (Function | string)[]): IndexMetadataArgs[]\n    filterIndices(\n        target: (Function | string) | (Function | string)[],\n    ): IndexMetadataArgs[] {\n        // todo: implement parent-entity overrides?\n        return this.indices.filter((index) => {\n            return Array.isArray(target)\n                ? target.indexOf(index.target) !== -1\n                : index.target === target\n        })\n    }\n\n    filterForeignKeys(target: Function | string): ForeignKeyMetadataArgs[]\n    filterForeignKeys(target: (Function | string)[]): ForeignKeyMetadataArgs[]\n    filterForeignKeys(\n        target: (Function | string) | (Function | string)[],\n    ): ForeignKeyMetadataArgs[] {\n        return this.foreignKeys.filter((foreignKey) => {\n            return Array.isArray(target)\n                ? target.indexOf(foreignKey.target) !== -1\n                : foreignKey.target === target\n        })\n    }\n\n    filterUniques(target: Function | string): UniqueMetadataArgs[]\n    filterUniques(target: (Function | string)[]): UniqueMetadataArgs[]\n    filterUniques(\n        target: (Function | string) | (Function | string)[],\n    ): UniqueMetadataArgs[] {\n        return this.uniques.filter((unique) => {\n            return Array.isArray(target)\n                ? target.indexOf(unique.target) !== -1\n                : unique.target === target\n        })\n    }\n\n    filterChecks(target: Function | string): CheckMetadataArgs[]\n    filterChecks(target: (Function | string)[]): CheckMetadataArgs[]\n    filterChecks(\n        target: (Function | string) | (Function | string)[],\n    ): CheckMetadataArgs[] {\n        return this.checks.filter((check) => {\n            return Array.isArray(target)\n                ? target.indexOf(check.target) !== -1\n                : check.target === target\n        })\n    }\n\n    filterExclusions(target: Function | string): ExclusionMetadataArgs[]\n    filterExclusions(target: (Function | string)[]): ExclusionMetadataArgs[]\n    filterExclusions(\n        target: (Function | string) | (Function | string)[],\n    ): ExclusionMetadataArgs[] {\n        return this.exclusions.filter((exclusion) => {\n            return Array.isArray(target)\n                ? target.indexOf(exclusion.target) !== -1\n                : exclusion.target === target\n        })\n    }\n\n    filterListeners(target: Function | string): EntityListenerMetadataArgs[]\n    filterListeners(target: (Function | string)[]): EntityListenerMetadataArgs[]\n    filterListeners(\n        target: (Function | string) | (Function | string)[],\n    ): EntityListenerMetadataArgs[] {\n        return this.filterByTarget(this.entityListeners, target)\n    }\n\n    filterEmbeddeds(target: Function | string): EmbeddedMetadataArgs[]\n    filterEmbeddeds(target: (Function | string)[]): EmbeddedMetadataArgs[]\n    filterEmbeddeds(\n        target: (Function | string) | (Function | string)[],\n    ): EmbeddedMetadataArgs[] {\n        return this.filterByTargetAndWithoutDuplicateEmbeddedProperties(\n            this.embeddeds,\n            target,\n        )\n    }\n\n    findJoinTable(\n        target: Function | string,\n        propertyName: string,\n    ): JoinTableMetadataArgs | undefined {\n        return this.joinTables.find((joinTable) => {\n            return (\n                joinTable.target === target &&\n                joinTable.propertyName === propertyName\n            )\n        })\n    }\n\n    filterJoinColumns(\n        target: Function | string,\n        propertyName: string,\n    ): JoinColumnMetadataArgs[] {\n        // todo: implement parent-entity overrides?\n        return this.joinColumns.filter((joinColumn) => {\n            return (\n                joinColumn.target === target &&\n                joinColumn.propertyName === propertyName\n            )\n        })\n    }\n\n    filterSubscribers(target: Function | string): EntitySubscriberMetadataArgs[]\n    filterSubscribers(\n        target: (Function | string)[],\n    ): EntitySubscriberMetadataArgs[]\n    filterSubscribers(\n        target: (Function | string) | (Function | string)[],\n    ): EntitySubscriberMetadataArgs[] {\n        return this.filterByTarget(this.entitySubscribers, target)\n    }\n\n    filterNamingStrategies(\n        target: Function | string,\n    ): NamingStrategyMetadataArgs[]\n    filterNamingStrategies(\n        target: (Function | string)[],\n    ): NamingStrategyMetadataArgs[]\n    filterNamingStrategies(\n        target: (Function | string) | (Function | string)[],\n    ): NamingStrategyMetadataArgs[] {\n        return this.filterByTarget(this.namingStrategies, target)\n    }\n\n    filterTransactionEntityManagers(\n        target: Function | string,\n        propertyName: string,\n    ): TransactionEntityMetadataArgs[] {\n        return this.transactionEntityManagers.filter((transactionEm) => {\n            return (\n                (Array.isArray(target)\n                    ? target.indexOf(transactionEm.target) !== -1\n                    : transactionEm.target === target) &&\n                transactionEm.methodName === propertyName\n            )\n        })\n    }\n\n    filterTransactionRepository(\n        target: Function | string,\n        propertyName: string,\n    ): TransactionRepositoryMetadataArgs[] {\n        return this.transactionRepositories.filter((transactionEm) => {\n            return (\n                (Array.isArray(target)\n                    ? target.indexOf(transactionEm.target) !== -1\n                    : transactionEm.target === target) &&\n                transactionEm.methodName === propertyName\n            )\n        })\n    }\n\n    filterSingleTableChildren(target: Function | string): TableMetadataArgs[] {\n        return this.tables.filter((table) => {\n            return (\n                typeof table.target === \"function\" &&\n                typeof target === \"function\" &&\n                MetadataUtils.isInherited(table.target, target) &&\n                table.type === \"entity-child\"\n            )\n        })\n    }\n\n    findInheritanceType(\n        target: Function | string,\n    ): InheritanceMetadataArgs | undefined {\n        return this.inheritances.find(\n            (inheritance) => inheritance.target === target,\n        )\n    }\n\n    findDiscriminatorValue(\n        target: Function | string,\n    ): DiscriminatorValueMetadataArgs | undefined {\n        return this.discriminatorValues.find(\n            (discriminatorValue) => discriminatorValue.target === target,\n        )\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Filters given array by a given target or targets.\n     */\n    protected filterByTarget<T extends { target: Function | string }>(\n        array: T[],\n        target: (Function | string) | (Function | string)[],\n    ): T[] {\n        return array.filter((table) => {\n            return Array.isArray(target)\n                ? target.indexOf(table.target) !== -1\n                : table.target === target\n        })\n    }\n\n    /**\n     * Filters given array by a given target or targets and prevents duplicate property names.\n     */\n    protected filterByTargetAndWithoutDuplicateProperties<\n        T extends { target: Function | string; propertyName: string },\n    >(array: T[], target: (Function | string) | (Function | string)[]): T[] {\n        const newArray: T[] = []\n        array.forEach((item) => {\n            const sameTarget = Array.isArray(target)\n                ? target.indexOf(item.target) !== -1\n                : item.target === target\n            if (sameTarget) {\n                if (\n                    !newArray.find(\n                        (newItem) => newItem.propertyName === item.propertyName,\n                    )\n                )\n                    newArray.push(item)\n            }\n        })\n        return newArray\n    }\n\n    /**\n     * Filters given array by a given target or targets and prevents duplicate relation property names.\n     */\n    protected filterByTargetAndWithoutDuplicateRelationProperties<\n        T extends RelationMetadataArgs,\n    >(array: T[], target: (Function | string) | (Function | string)[]): T[] {\n        const newArray: T[] = []\n        array.forEach((item) => {\n            const sameTarget = Array.isArray(target)\n                ? target.indexOf(item.target) !== -1\n                : item.target === target\n            if (sameTarget) {\n                const existingIndex = newArray.findIndex(\n                    (newItem) => newItem.propertyName === item.propertyName,\n                )\n                if (\n                    Array.isArray(target) &&\n                    existingIndex !== -1 &&\n                    target.indexOf(item.target) <\n                        target.indexOf(newArray[existingIndex].target)\n                ) {\n                    const clone = Object.create(newArray[existingIndex])\n                    clone.type = item.type\n                    newArray[existingIndex] = clone\n                } else if (existingIndex === -1) {\n                    newArray.push(item)\n                }\n            }\n        })\n        return newArray\n    }\n\n    /**\n     * Filters given array by a given target or targets and prevents duplicate embedded property names.\n     */\n    protected filterByTargetAndWithoutDuplicateEmbeddedProperties<\n        T extends EmbeddedMetadataArgs,\n    >(array: T[], target: (Function | string) | (Function | string)[]): T[] {\n        const newArray: T[] = []\n        array.forEach((item) => {\n            const sameTarget = Array.isArray(target)\n                ? target.indexOf(item.target) !== -1\n                : item.target === target\n            if (sameTarget) {\n                const isDuplicateEmbeddedProperty = newArray.find(\n                    (newItem: EmbeddedMetadataArgs): boolean =>\n                        newItem.prefix === item.prefix &&\n                        newItem.propertyName === item.propertyName,\n                )\n                if (!isDuplicateEmbeddedProperty) newArray.push(item)\n            }\n        })\n        return newArray\n    }\n}\n"], "sourceRoot": ".."}