import { MiddlewareParamArray } from '../../interface';
export declare enum WSEventTypeEnum {
    ON_CONNECTION = "ws:onConnection",
    ON_DISCONNECTION = "ws:onDisconnection",
    ON_MESSAGE = "ws:onMessage",
    ON_SOCKET_ERROR = "ws:onSocketError",
    EMIT = "ws:Emit",
    BROADCAST = "ws:broadcast"
}
export interface WSEventInfo {
    /**
     * web socket event name in enum
     */
    eventType: WSEventTypeEnum;
    /**
     * decorator method name
     */
    propertyName: string;
    descriptor: PropertyDescriptor;
    /**
     * the event name by user definition
     */
    messageEventName?: string;
    /**
     * the room name to emit
     */
    roomName?: string[];
    /**
     * event options, like middleware
     */
    eventOptions?: {
        middleware?: MiddlewareParamArray;
    };
}
export declare function OnWSConnection(eventOptions?: {
    middleware?: MiddlewareParamArray;
}): MethodDecorator;
export declare function OnWSDisConnection(): MethodDecorator;
export declare function OnWSMessage(eventName: string, eventOptions?: {
    middleware?: MiddlewareParamArray;
}): MethodDecorator;
export declare function WSEmit(messageName: string, roomName?: string | string[]): MethodDecorator;
export declare function WSBroadCast(messageName?: string, roomName?: string | string[]): MethodDecorator;
/**
 * @deprecated please use @OnWSMessage
 */
export declare const OnMessage: typeof OnWSMessage;
/**
 * @deprecated please use @WSEmit
 */
export declare const Emit: typeof WSEmit;
/**
 * @deprecated please use @OnWSDisConnection
 */
export declare const OnDisConnection: typeof OnWSDisConnection;
/**
 * @deprecated please use @OnWSConnection
 */
export declare const OnConnection: typeof OnWSConnection;
//# sourceMappingURL=webSocketEvent.d.ts.map