"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionStoreManager = void 0;
const core_1 = require("@midwayjs/core");
let SessionStoreManager = class SessionStoreManager {
    setSessionStore(sessionStore) {
        this.sessionStore = sessionStore;
    }
    getSessionStore() {
        return this.sessionStore;
    }
};
SessionStoreManager = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], SessionStoreManager);
exports.SessionStoreManager = SessionStoreManager;
//# sourceMappingURL=store.js.map