{"name": "@midwayjs/mock", "version": "3.20.4", "description": "create your test app from midway framework", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "link": "npm link"}, "keywords": ["midway", "IoC", "mock", "plugin", "bootstrap"], "files": ["dist/**/*.js", "dist/**/*.d.ts", "ssl", "app.js", "function.js"], "engines": {"node": ">=12"}, "license": "MIT", "devDependencies": {"@midwayjs/core": "^3.20.4", "@midwayjs/logger": "^3.0.0", "@types/amqplib": "0.10.6", "amqplib": "0.10.5", "kafkajs": "2.2.4", "socket.io": "4.8.1", "socket.io-client": "4.8.1", "ws": "8.18.0"}, "dependencies": {"@midwayjs/async-hooks-context-manager": "^3.20.4", "@types/superagent": "4.1.14", "@types/supertest": "2.0.16", "js-yaml": "4.1.0", "raw-body": "2.5.2", "supertest": "6.3.3"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "gitHead": "c3fb65a7ada8829635f3c6af5ef83c65c3a43d79"}