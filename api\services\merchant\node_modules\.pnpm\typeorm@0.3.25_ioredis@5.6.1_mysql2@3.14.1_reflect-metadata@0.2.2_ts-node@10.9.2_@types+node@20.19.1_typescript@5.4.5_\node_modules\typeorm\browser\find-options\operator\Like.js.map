{"version": 3, "sources": ["../browser/src/find-options/operator/Like.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,IAAI,CAAI,KAA0B;IAC9C,OAAO,IAAI,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;AAC1C,CAAC", "file": "Like.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: Like(\"%some string%\") }\n */\nexport function Like<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"like\", value)\n}\n"], "sourceRoot": "../.."}