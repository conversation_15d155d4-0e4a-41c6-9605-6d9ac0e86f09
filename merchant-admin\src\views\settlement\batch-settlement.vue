<template>
  <div class="batch-settlement-page">
    <!-- 操作区域 -->
    <div class="custom-card art-custom-card filter-card">
      <div class="custom-card-content">
        <el-form :model="filters" :inline="true">
          <el-form-item label="结算日期">
            <el-date-picker
              v-model="filters.settlementDate"
              type="date"
              placeholder="选择结算日期"
              style="width: 180px"
              @change="handleDateChange"
            />
          </el-form-item>
          
          <el-form-item label="商户类型">
            <el-select v-model="filters.merchantType" placeholder="选择类型" style="width: 120px" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="个人商户" value="personal" />
              <el-option label="企业商户" value="enterprise" />
            </el-select>
          </el-form-item>

          <el-form-item label="结算状态">
            <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="待结算" value="pending" />
              <el-option label="结算中" value="processing" />
              <el-option label="已结算" value="completed" />
              <el-option label="结算失败" value="failed" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleBatchSettle" :disabled="selectedRows.length === 0">
              <el-icon><Money /></el-icon>
              批量结算
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 结算统计 -->
    <div class="settlement-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon pending">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.pendingCount }}</div>
                  <div class="stat-label">待结算商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.pendingAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon processing">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.processingCount }}</div>
                  <div class="stat-label">结算中商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.processingAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon completed">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.completedCount }}</div>
                  <div class="stat-label">已结算商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.completedAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon failed">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.failedCount }}</div>
                  <div class="stat-label">结算失败</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.failedAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 结算列表 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <el-table
          :data="settlementList"
          v-loading="loading"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="merchantName" label="商户名称" min-width="150" />
          <el-table-column prop="merchantType" label="商户类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.merchantType === 'personal' ? 'success' : 'primary'">
                {{ row.merchantType === 'personal' ? '个人' : '企业' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="settlementAmount" label="结算金额" width="120">
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatNumber(row.settlementAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="commissionAmount" label="佣金金额" width="120">
            <template #default="{ row }">
              <span class="commission-text">¥{{ formatNumber(row.commissionAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="actualAmount" label="实际到账" width="120">
            <template #default="{ row }">
              <span class="actual-amount">¥{{ formatNumber(row.actualAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="结算状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="settlementTime" label="结算时间" width="160">
            <template #default="{ row }">
              {{ row.settlementTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleSettle(row)"
                :disabled="row.status !== 'pending'"
              >
                结算
              </el-button>
              <el-button type="info" size="small" @click="viewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchData"
            @current-change="fetchData"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Money, Clock, Loading, CircleCheck, Close } from '@element-plus/icons-vue'

// 筛选条件
const filters = reactive({
  settlementDate: new Date(),
  merchantType: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  pendingCount: 25,
  pendingAmount: 1250000,
  processingCount: 8,
  processingAmount: 380000,
  completedCount: 156,
  completedAmount: 8900000,
  failedCount: 3,
  failedAmount: 45000
})

// 结算列表数据
const settlementList = ref([
  {
    id: 1,
    merchantName: '张三手工艺品店',
    merchantType: 'personal',
    settlementAmount: 15800,
    commissionAmount: 553,
    actualAmount: 15247,
    status: 'pending',
    settlementTime: null
  },
  {
    id: 2,
    merchantName: '李四非遗工坊',
    merchantType: 'enterprise',
    settlementAmount: 28500,
    commissionAmount: 798,
    actualAmount: 27702,
    status: 'processing',
    settlementTime: null
  },
  {
    id: 3,
    merchantName: '王五文创店',
    merchantType: 'personal',
    settlementAmount: 12300,
    commissionAmount: 430.5,
    actualAmount: 11869.5,
    status: 'completed',
    settlementTime: '2025-01-18 14:30:00'
  }
])

const loading = ref(false)
const selectedRows = ref([])

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 获取状态标签
const getStatusTag = (status: string) => {
  const tags = { pending: 'warning', processing: 'primary', completed: 'success', failed: 'danger' }
  return tags[status] || 'info'
}

// 获取状态名称
const getStatusName = (status: string) => {
  const names = { pending: '待结算', processing: '结算中', completed: '已结算', failed: '结算失败' }
  return names[status] || '未知'
}

// 查询数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    pagination.total = 50
    loading.value = false
  }, 1000)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    settlementDate: new Date(),
    merchantType: '',
    status: ''
  })
  handleSearch()
}

// 日期变化
const handleDateChange = () => {
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection.filter(row => row.status === 'pending')
}

// 单个结算
const handleSettle = (row: any) => {
  ElMessageBox.confirm(`确定要结算商户"${row.merchantName}"吗？`, '确认结算', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('结算请求已提交')
    row.status = 'processing'
  })
}

// 批量结算
const handleBatchSettle = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要结算的商户')
    return
  }
  ElMessageBox.confirm(`确定要批量结算 ${selectedRows.value.length} 个商户吗？`, '确认批量结算', {
    type: 'warning'
  }).then(() => {
    ElMessage.success(`批量结算 ${selectedRows.value.length} 个商户成功`)
    selectedRows.value.forEach(row => {
      row.status = 'processing'
    })
  })
}

// 查看详情
const viewDetail = (row: any) => {
  ElMessage.info(`查看结算详情：${row.merchantName}`)
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
.batch-settlement-page {
  padding-bottom: 20px;

  // 使用系统统一的卡片样式
  :deep(.custom-card) {
    background: var(--art-main-bg-color);
    border: 1px solid var(--art-border-color);
    border-radius: calc(var(--custom-radius) + 4px);
    box-shadow: none;
    margin-bottom: 20px;
  }

  // 卡片内容样式
  .custom-card-content {
    padding: 20px;
  }

  // 筛选卡片
  .filter-card {
    .custom-card-content {
      padding: 16px 20px;
    }
  }
}

.filter-card {
  margin-bottom: 20px;
}

.settlement-stats {
  margin-bottom: 20px;

  .stat-card {
    .stat-item {
      display: flex;
      align-items: center;
      padding: 20px;

      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
        color: white;

        &.pending { background: linear-gradient(135deg, #e6a23c, #ebb563); }
        &.processing { background: linear-gradient(135deg, #409eff, #66b1ff); }
        &.completed { background: linear-gradient(135deg, #67c23a, #85ce61); }
        &.failed { background: linear-gradient(135deg, #f56c6c, #f78989); }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin: 5px 0;
        }

        .stat-amount {
          font-size: 12px;
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.amount-text {
  color: #67c23a;
  font-weight: bold;
}

.commission-text {
  color: #e6a23c;
  font-weight: bold;
}

.actual-amount {
  color: #409eff;
  font-weight: bold;
}
</style>
