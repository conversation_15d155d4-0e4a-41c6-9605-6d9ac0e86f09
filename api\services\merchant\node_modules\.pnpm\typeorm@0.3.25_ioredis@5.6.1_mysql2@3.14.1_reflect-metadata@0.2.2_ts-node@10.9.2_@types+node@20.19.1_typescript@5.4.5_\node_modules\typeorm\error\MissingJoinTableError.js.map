{"version": 3, "sources": ["../../src/error/MissingJoinTableError.ts"], "names": [], "mappings": ";;;AAEA,iDAA6C;AAE7C,MAAa,qBAAsB,SAAQ,2BAAY;IACnD,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,EAAE,CAAA;QAEP,IAAI,QAAQ,CAAC,eAAe,EAAE,CAAC;YAC3B,IAAI,CAAC,OAAO;gBACR,yCAAyC,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,OAAO;oBAC5F,GAAG,QAAQ,CAAC,qBAAqB,CAAC,IAAI,IAAI,QAAQ,CAAC,eAAe,CAAC,YAAY,8BAA8B;oBAC7G,0DAA0D,CAAA;QAClE,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,OAAO;gBACR,2BAA2B,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,8BAA8B;oBACrG,4CAA4C,CAAA;QACpD,CAAC;IACL,CAAC;CACJ;AAfD,sDAeC", "file": "MissingJoinTableError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingJoinTableError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super()\n\n        if (relation.inverseRelation) {\n            this.message =\n                `JoinTable is missing on both sides of ${entityMetadata.name}#${relation.propertyName} and ` +\n                `${relation.inverseEntityMetadata.name}#${relation.inverseRelation.propertyName} many-to-many relationship. ` +\n                `You need to put decorator decorator on one of the sides.`\n        } else {\n            this.message =\n                `JoinTable is missing on ${entityMetadata.name}#${relation.propertyName} many-to-many relationship. ` +\n                `You need to put JoinTable decorator on it.`\n        }\n    }\n}\n"], "sourceRoot": ".."}