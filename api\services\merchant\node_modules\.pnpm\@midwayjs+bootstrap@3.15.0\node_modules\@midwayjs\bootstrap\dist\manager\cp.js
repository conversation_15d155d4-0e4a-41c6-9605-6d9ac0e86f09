"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClusterManager = void 0;
const event_bus_1 = require("@midwayjs/event-bus");
const base_1 = require("./base");
const cluster = require('cluster');
const util_1 = require("util");
const core_1 = require("@midwayjs/core");
const debug = (0, util_1.debuglog)('midway:bootstrap');
class ClusterManager extends base_1.AbstractForkManager {
    constructor(options = {}) {
        super(options);
        this.options = options;
        options.args = options.args || [];
        options.execArgv = options.execArgv || [];
        if ((0, core_1.isTypeScriptEnvironment)()) {
            options.execArgv.push(...['--require', 'ts-node/register']);
        }
    }
    createWorker() {
        if (cluster['setupPrimary']) {
            cluster['setupPrimary'](this.options);
        }
        else if (cluster['setupMaster']) {
            cluster['setupMaster'](this.options);
        }
        return cluster.fork({
            MIDWAY_FORK_MODE: 'cluster',
            MIDWAY_STICKY_MODE: this.options.sticky ? 'true' : 'false',
            ...this.options.env,
        });
    }
    bindWorkerDisconnect(listener) {
        debug('Bind cluster.disconnect event');
        cluster.on('disconnect', listener);
    }
    bindWorkerExit(listener) {
        debug('Bind cluster.exit event');
        cluster.on('exit', listener);
    }
    getWorkerId(worker) {
        return String(worker.process.pid);
    }
    isWorkerDead(worker) {
        return worker.isDead();
    }
    closeWorker(worker) {
        worker.kill('SIGTERM');
    }
    createEventBus(options) {
        return new event_bus_1.ChildProcessEventBus(options);
    }
    isPrimary() {
        return !cluster.isWorker;
    }
}
exports.ClusterManager = ClusterManager;
//# sourceMappingURL=cp.js.map