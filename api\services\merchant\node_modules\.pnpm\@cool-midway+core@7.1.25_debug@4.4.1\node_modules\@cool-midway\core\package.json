{"name": "@cool-midway/core", "version": "7.1.25", "description": "", "main": "index.js", "typings": "index.d.ts", "scripts": {"build": "cross-env midway-bin build -c", "cov": "cross-env midway-bin cov --ts", "lint": "mwts check", "lint:fix": "mwts fix"}, "keywords": ["cool", "cool-admin", "cooljs"], "readme": "README.md", "author": "COOL", "files": ["**/*.js", "**/*.d.ts", "index.d.ts"], "license": "MIT", "repository": {"type": "git", "url": "https://cool-js.com"}, "devDependencies": {"@midwayjs/cli": "1.3.21", "@midwayjs/core": "^3.9.0", "@midwayjs/decorator": "^3.9.0", "@midwayjs/koa": "^3.9.0", "@midwayjs/mock": "^3.9.0", "@midwayjs/typeorm": "^3.9.0", "@types/jest": "^29.2.4", "@types/node": "^18.11.15", "cross-env": "^7.0.3", "jest": "^29.3.1", "mwts": "^1.3.0", "ts-jest": "^29.0.3", "typeorm": "^0.3.19", "typescript": "~4.9.4"}, "dependencies": {"@midwayjs/cache": "^3.14.0", "@midwayjs/cache-manager": "^3.15.0", "@cool-midway/cache-manager-fs-hash": "^7.0.0", "axios": "^1.6.5", "decompress": "^4.2.1", "download": "^8.0.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "md5": "^2.3.0", "moment": "^2.30.1", "sqlstring": "^2.3.3", "uuid": "^9.0.1", "ws": "^8.16.0", "pm2": "^5.3.1"}}