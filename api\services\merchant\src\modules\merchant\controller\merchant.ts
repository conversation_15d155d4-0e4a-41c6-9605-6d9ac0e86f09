import { Body, Controller, Get, Inject, Post, Provide } from '@midwayjs/decorator';
import { CoolController, BaseController } from '@cool-midway/core';
import { MerchantEntity } from '../entity/merchant';
import { MerchantService } from '../service/merchant';

/**
 * 商户管理
 */
@Provide()
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: MerchantEntity,
  service: MerchantService,
  pageQueryOp: {
    fieldEq: ['type', 'status', 'category'],
    keyWordLikeFields: ['name', 'companyName', 'phone', 'creditCode']
  }
})
@Controller('/merchant')
export class MerchantController extends BaseController {
  @Inject()
  merchantService!: MerchantService;

  /**
   * 商户入驻申请列表
   */
  @Post('/settle-in/page')
  async settleInPage(@Body() params: any) {
    return this.ok(await this.merchantService.page(params, {
      fieldEq: ['type', 'status'],
      keyWordLikeFields: ['name', 'companyName', 'phone', 'creditCode'],
      addOrderBy: { createTime: 'DESC' }
    }));
  }

  /**
   * 商户入驻申请详情
   */
  @Post('/settle-in/info')
  async settleInInfo(@Body() params: { id: number }) {
    return this.ok(await this.merchantService.info(params.id));
  }

  /**
   * 审核商户入驻申请
   */
  @Post('/settle-in/audit')
  async settleInAudit(@Body() params: { id: number; status: number; remark?: string }) {
    return this.ok(await this.merchantService.auditMerchant(params.id, params.status, params.remark));
  }

  /**
   * 批量审核商户入驻申请
   */
  @Post('/settle-in/batchAudit')
  async settleInBatchAudit(@Body() params: { ids: number[]; status: number; remark?: string }) {
    return this.ok(await this.merchantService.batchAuditMerchant(params.ids, params.status, params.remark));
  }

  /**
   * 商户入驻申请统计
   */
  @Get('/settle-in/statistics')
  async settleInStatistics() {
    return this.ok(await this.merchantService.getSettleInStatistics());
  }

  /**
   * 企业商户列表
   */
  @Post('/company/page')
  async companyPage(@Body() params: any) {
    return this.ok(await this.merchantService.page(params, {
      fieldEq: ['type', 'status', 'category'],
      keyWordLikeFields: ['companyName', 'creditCode', 'legalPerson', 'phone', 'email'],
      addOrderBy: { updateTime: 'DESC' }
    }));
  }

  /**
   * 企业商户详情
   */
  @Post('/company/info')
  async companyInfo(@Body() params: { id: number }) {
    return this.ok(await this.merchantService.info(params.id));
  }

  /**
   * 企业商户统计
   */
  @Get('/company/statistics')
  async companyStatistics() {
    return this.ok(await this.merchantService.getCompanyMerchantStatistics());
  }

  /**
   * 更新企业商户状态
   */
  @Post('/company/updateStatus')
  async updateCompanyStatus(@Body() params: { id: number; status: number; remark?: string }) {
    return this.ok(await this.merchantService.updateMerchantStatus(params));
  }

  /**
   * 个人商户列表
   */
  @Post('/personal/page')
  async personalPage(@Body() params: any) {
    return this.ok(await this.merchantService.page(params, {
      fieldEq: ['type', 'status', 'category'],
      keyWordLikeFields: ['name', 'idCard', 'phone'],
      addOrderBy: { updateTime: 'DESC' }
    }));
  }

  /**
   * 个人商户详情
   */
  @Post('/personal/info')
  async personalInfo(@Body() params: { id: number }) {
    return this.ok(await this.merchantService.info(params.id));
  }

  /**
   * 个人商户统计
   */
  @Get('/personal/statistics')
  async personalStatistics() {
    return this.ok(await this.merchantService.getPersonalMerchantStatistics());
  }

  /**
   * 更新个人商户状态
   */
  @Post('/personal/updateStatus')
  async updatePersonalStatus(@Body() params: { id: number; status: number; remark?: string }) {
    return this.ok(await this.merchantService.updateMerchantStatus(params));
  }

  // ✅ 移除与CoolController自动生成路由冲突的方法
  // CoolController已自动生成以下路由：
  // - POST /merchant/page (分页查询)
  // - POST /merchant/info (详情查询) 
  // - POST /merchant/update (更新)
  // - POST /merchant/delete (删除)
  // - POST /merchant/add (添加)
  // - POST /merchant/list (列表查询)

  /**
   * 商户统计（通用）
   */
  @Get('/statistics')
  async merchantStatistics() {
    return this.ok(await this.merchantService.getMerchantStats());
  }

  /**
   * 联系商户
   */
  @Post('/contact')
  async contact(@Body() params: { id: number; message: string }) {
    // TODO: 实现联系商户的逻辑
    return this.ok('消息已发送');
  }

  /**
   * 添加商户备注
   */
  @Post('/note')
  async addNote(@Body() params: { id: number; note: string }) {
    // TODO: 实现添加备注的逻辑
    return this.ok('备注已添加');
  }
} 