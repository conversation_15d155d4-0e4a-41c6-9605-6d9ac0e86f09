{"version": 3, "sources": ["../../src/migration/MigrationInterface.ts"], "names": [], "mappings": "", "file": "MigrationInterface.js", "sourcesContent": ["import { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Migrations should implement this interface and all its methods.\n */\nexport interface MigrationInterface {\n    /**\n     * Optional migration name, defaults to class name.\n     */\n    name?: string\n\n    /**\n     * Optional flag to determine whether to run the migration in a transaction or not.\n     * Can only be used when `migrationsTransactionMode` is either \"each\" or \"none\"\n     * Defaults to `true` when `migrationsTransactionMode` is \"each\"\n     * Defaults to `false` when `migrationsTransactionMode` is \"none\"\n     */\n    transaction?: boolean\n\n    /**\n     * Run the migrations.\n     */\n    up(queryRunner: QueryRunner): Promise<any>\n\n    /**\n     * Reverse the migrations.\n     */\n    down(queryRunner: QueryRunner): Promise<any>\n}\n"], "sourceRoot": ".."}