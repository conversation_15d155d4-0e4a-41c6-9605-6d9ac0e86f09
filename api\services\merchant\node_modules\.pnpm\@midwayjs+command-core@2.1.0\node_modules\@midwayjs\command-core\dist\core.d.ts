import { IOptions, ICommandCore, ICommands } from './interface/commandCore';
export declare class CommandCore implements ICommandCore {
    options: IOptions;
    private instances;
    private commands;
    private hooks;
    private coreInstance;
    private providers;
    private npmPlugin;
    private execId;
    private userLifecycle;
    private cwd;
    private preDebugTime;
    private stopLifecycles;
    private loadNpm;
    private timeTicks;
    private outputLevel;
    store: Map<any, any>;
    constructor(options: IOptions);
    addPlugin(Plugin: any): void;
    invoke(commandsArray?: string | string[], allowEntryPoints?: boolean, options?: any): Promise<void>;
    private execLifecycle;
    private tickTime;
    resume(options?: any): Promise<void>;
    spawn(commandsArray: string | string[], options?: any): Promise<void>;
    getCommands(): ICommands;
    ready(): Promise<void>;
    autoLoadPlugins(): void;
    getTimeTicks(): any[];
    private getCoreInstance;
    private setProvider;
    private getProvider;
    private loadCommands;
    private loadHooks;
    private loadLifecycle;
    private getCommand;
    private loadLocalPlugin;
    private loadNpmPlugins;
    private asyncInit;
    private loadUserLifecycleExtends;
    private commandOptions;
    private displayHelp;
    private getLog;
    error<T>(type: string, info?: T): void;
    debug(...args: any[]): void;
    getStackTrace(): {
        type?: undefined;
        path?: undefined;
        line?: undefined;
    } | {
        type: string;
        path: string;
        line: string;
    };
}
//# sourceMappingURL=core.d.ts.map