{"name": "@midwayjs/cross-domain", "version": "3.20.5", "description": "Midway Component for Cross Domain", "main": "dist/index.js", "typings": "index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test"}, "keywords": [], "author": "", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "engines": {"node": ">=12"}, "license": "MIT", "dependencies": {"vary": "1.1.2"}, "devDependencies": {"@midwayjs/core": "^3.20.4", "@midwayjs/express": "^3.20.4", "@midwayjs/faas": "^3.20.4", "@midwayjs/koa": "^3.20.5", "@midwayjs/mock": "^3.20.4", "@midwayjs/serverless-app": "^3.20.4", "@midwayjs/web": "^3.20.4"}, "gitHead": "7ce57281bd3ef5d18dc50b47ff9bffb8a27c071e"}