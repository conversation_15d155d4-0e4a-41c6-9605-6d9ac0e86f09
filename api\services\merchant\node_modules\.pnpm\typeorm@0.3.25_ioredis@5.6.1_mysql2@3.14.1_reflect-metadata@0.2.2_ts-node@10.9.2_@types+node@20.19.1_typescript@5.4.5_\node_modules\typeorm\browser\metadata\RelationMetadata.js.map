{"version": 3, "sources": ["../browser/src/metadata/RelationMetadata.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAA;AAUjD,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD;;GAEG;AACH,MAAM,OAAO,gBAAgB;IAqQzB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAIX;QAhND;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;WAEG;QACH,mBAAc,GAAY,KAAK,CAAA;QAE/B;;;WAGG;QACH,cAAS,GAAY,KAAK,CAAA;QAE1B;;WAEG;QACH,WAAM,GAAY,KAAK,CAAA;QAEvB;;WAEG;QACH,YAAO,GAAY,KAAK,CAAA;QAExB;;;;;WAKG;QACH,uBAAkB,GAAY,IAAI,CAAA;QASlC;;WAEG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;WAEG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;WAEG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;WAEG;QACH,wBAAmB,GAAY,KAAK,CAAA;QAEpC;;WAEG;QACH,qBAAgB,GAAY,KAAK,CAAA;QAEjC;;WAEG;QACH,eAAU,GAAY,IAAI,CAAA;QAiB1B;;;;WAIG;QACH,gCAA2B,GAAY,IAAI,CAAA;QAS3C;;WAEG;QACH,aAAQ,GAAY,KAAK,CAAA;QAEzB;;WAEG;QACH,eAAU,GAAY,KAAK,CAAA;QAE3B;;;WAGG;QACH,oBAAe,GAAY,KAAK,CAAA;QAEhC;;WAEG;QACH,qBAAgB,GAAY,KAAK,CAAA;QAEjC;;;WAGG;QACH,uBAAkB,GAAY,KAAK,CAAA;QAEnC;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,gBAAW,GAAY,KAAK,CAAA;QAE5B;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAE7B;;;WAGG;QACH,sBAAiB,GAAY,KAAK,CAAA;QAElC;;;WAGG;QACH,yBAAoB,GAAY,KAAK,CAAA;QA0BrC;;WAEG;QACH,gBAAW,GAAyB,EAAE,CAAA;QAEtC;;;;;;WAMG;QACH,gBAAW,GAAqB,EAAE,CAAA;QAElC;;;;;WAKG;QACH,uBAAkB,GAAqB,EAAE,CAAA;QAWrC,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAiB,CAAA;QACjD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QACzB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAA;QAErC,IAAI,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,+BAA+B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAEnE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,KAAK,CAAA;QAClC,oJAAoJ;QACpJ,oJAAoJ;QACpJ,oJAAoJ;QACpJ,6JAA6J;QAC7J,sJAAsJ;QACtJ,IAAI,CAAC,eAAe;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACtD,IAAI,CAAC,mBAAmB;YACpB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAC3D,IAAI,CAAC,gBAAgB;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,IAAI;gBAC7B,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;oBAChC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACvD,kDAAkD;QAClD,IAAI,CAAC,UAAU;YACX,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACpE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAA;QACzC,IAAI,CAAC,2BAA2B;YAC5B,IAAI,CAAC,OAAO,CAAC,2BAA2B,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACrE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,KAAK,CAAA;QAC1C,IAAI,CAAC,kBAAkB;YACnB,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;QACrD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,IAAI,SAAS,CAAA;QACpE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,IAAI,KAAK,CAAA;QAC9C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,KAAK,CAAA;QAElD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI;gBACL,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU;oBAC3B,CAAC,CAAE,IAAI,CAAC,IAAkB,EAAE;oBAC5B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAA;QACvB,CAAC;aAAM,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAA;QACtC,CAAC;aAAM,IACH,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;YAC/B,OAAQ,IAAI,CAAC,IAAY,CAAC,IAAI,KAAK,QAAQ,EAC7C,CAAC;YACC,IAAI,CAAC,IAAI,GAAI,IAAI,CAAC,IAAY,CAAC,IAAI,CAAA;QACvC,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAyB,CAAA;QAC9C,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,YAAY,KAAK,YAAY,CAAA;QACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,KAAK,aAAa,CAAA;QACtD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,KAAK,aAAa,CAAA;QACtD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,KAAK,cAAc,CAAA;QACxD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QACxD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IAChE,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,gBAAgB,CAAC,MAAqB;QAClC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;YAC7B,CAAC,CAAC,IAAI,CAAC,WAAW;YAClB,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAA;QACvC,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,CACrC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,gBAAiB,CAC/C,CAAA;QACD,iCAAiC;QACjC,uDAAuD;QACvD,OAAO,cAAc,CAAC,WAAW,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;IAChE,CAAC;IAED;;;;;OAKG;IACH,mBAAmB,CAAC,EAAO;QACvB,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YAAE,OAAO,EAAE,CAAA;QAEvC,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ;YAC7B,CAAC,CAAC,IAAI,CAAC,WAAW;YAClB,CAAC,CAAC,IAAI,CAAC,eAAgB,CAAC,WAAW,CAAA;QACvC,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,CACrC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,gBAAiB,CAC/C,CAAA;QAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC;YAC5B,MAAM,IAAI,YAAY,CAClB,yGAAyG,CAC5G,CAAA;QAEL,OAAO,iBAAiB,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;IAClD,CAAC;IAED;;;OAGG;IACH,cAAc,CACV,MAAqB,EACrB,+BAAwC,KAAK;QAE7C,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS;YAAE,OAAO,SAAS,CAAA;QAC7D,yEAAyE;QACzE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,yGAAyG;YACzG,uEAAuE;YAEvE,0HAA0H;YAC1H,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;YAEpE,oHAAoH;YACpH,uHAAuH;YACvH,MAAM,0BAA0B,GAAG,CAC/B,aAAuB,EACvB,KAAoB,EACjB,EAAE;gBACL,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;gBAC1C,IAAI,YAAY,EAAE,CAAC;oBACf,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC;wBACtB,OAAO,0BAA0B,CAC7B,aAAa,EACb,KAAK,CAAC,YAAY,CAAC,CACtB,CAAA;oBACL,CAAC;oBACD,OAAO,SAAS,CAAA;gBACpB,CAAC;gBACD,OAAO,KAAK,CAAA;YAChB,CAAC,CAAA;YAED,+GAA+G;YAC/G,MAAM,cAAc,GAAG,0BAA0B,CAC7C,aAAa,EACb,MAAM,CACT,CAAA;YAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IACI,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC/C,SAAS;oBAET,OAAO,cAAc,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAA;gBAE1D,IAAI,4BAA4B,KAAK,IAAI;oBACrC,OAAO,cAAc,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;gBAE5C,OAAO,SAAS,CAAA;YACpB,CAAC;YACD,OAAO,cAAc;gBACjB,CAAC,CAAC,cAAc,CACV,IAAI,CAAC,MAAM;oBACP,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI;oBACjC,CAAC,CAAC,IAAI,CAAC,YAAY,CAC1B;gBACH,CAAC,CAAC,SAAS,CAAA;QACnB,CAAC;aAAM,CAAC;YACJ,oFAAoF;YACpF,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBACd,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,SAAS;oBACrD,OAAO,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAA;gBAElD,IAAI,4BAA4B,KAAK,IAAI;oBACrC,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;gBAEpC,OAAO,SAAS,CAAA;YACpB,CAAC;YACD,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QACpC,CAAC;IACL,CAAC;IAED;;;;;OAKG;IACH,cAAc,CAAC,MAAqB,EAAE,KAAU;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM;YAC5B,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI;YACjC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAA;QAEvB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,0HAA0H;YAC1H,MAAM,0BAA0B,GAAG,CAC/B,iBAAqC,EACrC,GAAkB,EACf,EAAE;gBACL,8CAA8C;gBAC9C,yEAAyE;gBAEzE,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,KAAK,EAAE,CAAA;gBAClD,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC;wBACnC,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC;4BAC9B,gBAAgB,CAAC,MAAM,EAAE,CAAA;oBAEjC,0BAA0B,CACtB,iBAAiB,EACjB,GAAG,CAAC,gBAAgB,CAAC,YAAY,CAAC,CACrC,CAAA;oBACD,OAAO,GAAG,CAAA;gBACd,CAAC;gBACD,GAAG,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;gBACzB,OAAO,GAAG,CAAA;YACd,CAAC,CAAA;YACD,OAAO,0BAA0B,CAC7B,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,EAC/C,MAAM,CACT,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;QAChC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAU;QACrB,sEAAsE;QACtE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,yGAAyG;YACzG,0FAA0F;YAC1F,8DAA8D;YAE9D,0HAA0H;YAC1H,MAAM,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,CAAA;YAEpE,6FAA6F;YAC7F,kFAAkF;YAClF,gFAAgF;YAChF,4HAA4H;YAC5H,2CAA2C;YAC3C,MAAM,0BAA0B,GAAG,CAC/B,aAAuB,EACvB,GAAkB,EACf,EAAE;gBACL,MAAM,YAAY,GAAG,aAAa,CAAC,KAAK,EAAE,CAAA;gBAC1C,IAAI,YAAY,EAAE,CAAC;oBACf,GAAG,CAAC,YAAY,CAAC,GAAG,EAAE,CAAA;oBACtB,0BAA0B,CAAC,aAAa,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAA;oBAC5D,OAAO,GAAG,CAAA;gBACd,CAAC;gBACD,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAA;gBAC9B,OAAO,GAAG,CAAA;YACd,CAAC,CAAA;YACD,OAAO,0BAA0B,CAAC,aAAa,EAAE,EAAE,CAAC,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,0FAA0F;YAC1F,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAA;QACzC,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,kBAAkB;IAClB,wEAAwE;IAExE;;;OAGG;IACH,KAAK;QACD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;IAChD,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,GAAG,WAAiC;QACpD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAA;IACzC,CAAC;IAED;;;OAGG;IACH,mBAAmB,CACf,cAAgC,EAAE,EAClC,qBAAuC,EAAE;QAEzC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAA;QAC5C,IAAI,CAAC,QAAQ;YACT,IAAI,CAAC,WAAW;gBAChB,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;oBACnC,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAA;QACvD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QAC3D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAA;QAC3D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAA;QAC/D,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,eAAe,CAAA;IACpE,CAAC;IAED;;;OAGG;IACH,8BAA8B,CAAC,sBAAsC;QACjE,IAAI,CAAC,sBAAsB,GAAG,sBAAsB,CAAA;QACpD,IAAI,CAAC,aAAa,GAAG,sBAAsB,CAAC,SAAS,CAAA;QACrD,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,IAAI,CAAC,eAAe,CAAC,sBAAsB,GAAG,sBAAsB,CAAA;YACpE,IAAI,CAAC,aAAa,GAAG,sBAAsB,CAAC,SAAS,CAAA;QACzD,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,4BAA4B;QACxB,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;YACvC,MAAM,wBAAwB,GAC1B,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAA;YAC5C,IAAI,OAAO,IAAI,CAAC,+BAA+B,KAAK,UAAU;gBAC1D,OAAO,IAAI,CAAC,+BAA+B,CACvC,wBAAwB,CAC3B,CAAA;YAEL,IAAI,OAAO,IAAI,CAAC,+BAA+B,KAAK,QAAQ;gBACxD,OAAO,IAAI,CAAC,+BAA+B,CAAA;QACnD,CAAC;aAAM,IACH,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,cAAc,CAAC,oBAAoB,EAC1C,CAAC;YACC,OAAO,IAAI,CAAC,cAAc,CAAC,oBAAoB,CAAC,YAAY,CAAA;QAChE,CAAC;aAAM,IACH,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,cAAc,CAAC,kBAAkB,EACxC,CAAC;YACC,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,YAAY,CAAA;QAC9D,CAAC;QAED,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACH,iBAAiB;QACb,IACI,CAAC,IAAI,CAAC,gBAAgB;YACtB,CAAC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,MAAM;YAEjD,OAAO,IAAI,CAAC,YAAY,CAAA;QAE5B,OAAO,CACH,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC;YACnD,GAAG;YACH,IAAI,CAAC,YAAY,CACpB,CAAA;IACL,CAAC;CACJ", "file": "RelationMetadata.js", "sourcesContent": ["import { RelationType } from \"./types/RelationTypes\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { ForeignKeyMetadata } from \"./ForeignKeyMetadata\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"./ColumnMetadata\"\nimport { EmbeddedMetadata } from \"./EmbeddedMetadata\"\nimport { RelationMetadataArgs } from \"../metadata-args/RelationMetadataArgs\"\nimport { DeferrableType } from \"./types/DeferrableType\"\nimport { OnUpdateType } from \"./types/OnUpdateType\"\nimport { OnDeleteType } from \"./types/OnDeleteType\"\nimport { PropertyTypeFactory } from \"./types/PropertyTypeInFunction\"\nimport { TypeORMError } from \"../error\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Contains all information about some entity's relation.\n */\nexport class RelationMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the entity where this relation is placed.\n     *\n     * For example for @ManyToMany(type => Category) in Post, entityMetadata will be metadata of Post entity.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Entity metadata of the entity that is targeted by this relation.\n     *\n     * For example for @ManyToMany(type => Category) in Post, inverseEntityMetadata will be metadata of Category entity.\n     */\n    inverseEntityMetadata: EntityMetadata\n\n    /**\n     * Entity metadata of the junction table.\n     * Junction tables have their own entity metadata objects.\n     * Defined only for many-to-many relations.\n     */\n    junctionEntityMetadata?: EntityMetadata\n\n    /**\n     * Embedded metadata where this relation is.\n     * If this relation is not in embed then this property value is undefined.\n     */\n    embeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * Relation type, e.g. is it one-to-one, one-to-many, many-to-one or many-to-many.\n     */\n    relationType: RelationType\n\n    /**\n     * Target entity to which this relation is applied.\n     * Target IS NOT equal to entityMetadata.target, because relation\n     *\n     * For example for @ManyToMany(type => Category) in Post, target will be Post.\n     * If @ManyToMany(type => Category) is in Counters which is embedded into Post, target will be Counters.\n     * If @ManyToMany(type => Category) is in abstract class BaseUser which Post extends, target will be BaseUser.\n     * Target can be string if its defined in entity schema instead of class.\n     */\n    target: Function | string\n\n    /**\n     * Target's property name to which relation decorator is applied.\n     */\n    propertyName: string\n\n    /**\n     * Gets full path to this column property (including relation name).\n     * Full path is relevant when column is used in embeds (one or multiple nested).\n     * For example it will return \"counters.subcounters.likes\".\n     * If property is not in embeds then it returns just property name of the column.\n     */\n    propertyPath: string\n\n    /**\n     * Indicates if this is a parent (can be only many-to-one relation) relation in the tree tables.\n     */\n    isTreeParent: boolean = false\n\n    /**\n     * Indicates if this is a children (can be only one-to-many relation) relation in the tree tables.\n     */\n    isTreeChildren: boolean = false\n\n    /**\n     * Indicates if this relation's column is a primary key.\n     * Can be used only for many-to-one and owner one-to-one relations.\n     */\n    isPrimary: boolean = false\n\n    /**\n     * Indicates if this relation is lazily loaded.\n     */\n    isLazy: boolean = false\n\n    /**\n     * Indicates if this relation is eagerly loaded.\n     */\n    isEager: boolean = false\n\n    /**\n     * Indicates if persistence is enabled for the relation.\n     * By default its enabled, but if you want to avoid any changes in the relation to be reflected in the database you can disable it.\n     * If its disabled you can only change a relation from inverse side of a relation or using relation query builder functionality.\n     * This is useful for performance optimization since its disabling avoid multiple extra queries during entity save.\n     */\n    persistenceEnabled: boolean = true\n\n    /**\n     * When a parent is saved (with cascading but) without a child row that still exists in database, this will control what shall happen to them.\n     * delete will remove these rows from database. nullify will remove the relation key.\n     * skip will keep the relation intact. Removal of related item is only possible through its own repo.\n     */\n    orphanedRowAction?: \"nullify\" | \"delete\" | \"soft-delete\" | \"disable\"\n\n    /**\n     * If set to true then related objects are allowed to be inserted to the database.\n     */\n    isCascadeInsert: boolean = false\n\n    /**\n     * If set to true then related objects are allowed to be updated in the database.\n     */\n    isCascadeUpdate: boolean = false\n\n    /**\n     * If set to true then related objects are allowed to be remove from the database.\n     */\n    isCascadeRemove: boolean = false\n\n    /**\n     * If set to true then related objects are allowed to be soft-removed from the database.\n     */\n    isCascadeSoftRemove: boolean = false\n\n    /**\n     * If set to true then related objects are allowed to be recovered from the database.\n     */\n    isCascadeRecover: boolean = false\n\n    /**\n     * Indicates if relation column value can be nullable or not.\n     */\n    isNullable: boolean = true\n\n    /**\n     * What to do with a relation on deletion of the row containing a foreign key.\n     */\n    onDelete?: OnDeleteType\n\n    /**\n     * What to do with a relation on update of the row containing a foreign key.\n     */\n    onUpdate?: OnUpdateType\n\n    /**\n     * What to do with a relation on update of the row containing a foreign key.\n     */\n    deferrable?: DeferrableType\n\n    /**\n     * Indicates whether foreign key constraints will be created for join columns.\n     * Can be used only for many-to-one and owner one-to-one relations.\n     * Defaults to true.\n     */\n    createForeignKeyConstraints: boolean = true\n\n    /**\n     * Gets the property's type to which this relation is applied.\n     *\n     * For example for @ManyToMany(type => Category) in Post, target will be Category.\n     */\n    type: Function | string\n\n    /**\n     * Indicates if this side is an owner of this relation.\n     */\n    isOwning: boolean = false\n\n    /**\n     * Checks if this relation's type is \"one-to-one\".\n     */\n    isOneToOne: boolean = false\n\n    /**\n     * Checks if this relation is owner side of the \"one-to-one\" relation.\n     * Owner side means this side of relation has a join column in the table.\n     */\n    isOneToOneOwner: boolean = false\n\n    /**\n     * Checks if this relation has a join column (e.g. is it many-to-one or one-to-one owner side).\n     */\n    isWithJoinColumn: boolean = false\n\n    /**\n     * Checks if this relation is NOT owner side of the \"one-to-one\" relation.\n     * NOT owner side means this side of relation does not have a join column in the table.\n     */\n    isOneToOneNotOwner: boolean = false\n\n    /**\n     * Checks if this relation's type is \"one-to-many\".\n     */\n    isOneToMany: boolean = false\n\n    /**\n     * Checks if this relation's type is \"many-to-one\".\n     */\n    isManyToOne: boolean = false\n\n    /**\n     * Checks if this relation's type is \"many-to-many\".\n     */\n    isManyToMany: boolean = false\n\n    /**\n     * Checks if this relation's type is \"many-to-many\", and is owner side of the relationship.\n     * Owner side means this side of relation has a join table.\n     */\n    isManyToManyOwner: boolean = false\n\n    /**\n     * Checks if this relation's type is \"many-to-many\", and is NOT owner side of the relationship.\n     * Not owner side means this side of relation does not have a join table.\n     */\n    isManyToManyNotOwner: boolean = false\n\n    /**\n     * Gets the property path of the inverse side of the relation.\n     */\n    inverseSidePropertyPath: string\n\n    /**\n     * Inverse side of the relation set by user.\n     *\n     * Inverse side set in the relation can be either string - property name of the column on inverse side,\n     * either can be a function that accepts a map of properties with the object and returns one of them.\n     * Second approach is used to achieve type-safety.\n     */\n    givenInverseSidePropertyFactory: PropertyTypeFactory<any>\n\n    /**\n     * Gets the relation metadata of the inverse side of this relation.\n     */\n    inverseRelation?: RelationMetadata\n\n    /**\n     * Join table name.\n     */\n    joinTableName: string\n\n    /**\n     * Foreign keys created for this relation.\n     */\n    foreignKeys: ForeignKeyMetadata[] = []\n\n    /**\n     * Join table columns.\n     * Join columns can be obtained only from owner side of the relation.\n     * From non-owner side of the relation join columns will be empty.\n     * If this relation is a many-to-one/one-to-one then it takes join columns from the current entity.\n     * If this relation is many-to-many then it takes all owner join columns from the junction entity.\n     */\n    joinColumns: ColumnMetadata[] = []\n\n    /**\n     * Inverse join table columns.\n     * Inverse join columns are supported only for many-to-many relations\n     * and can be obtained only from owner side of the relation.\n     * From non-owner side of the relation join columns will be undefined.\n     */\n    inverseJoinColumns: ColumnMetadata[] = []\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        embeddedMetadata?: EmbeddedMetadata\n        args: RelationMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.embeddedMetadata = options.embeddedMetadata!\n        const args = options.args\n        this.target = args.target\n        this.propertyName = args.propertyName\n        this.relationType = args.relationType\n\n        if (args.inverseSideProperty)\n            this.givenInverseSidePropertyFactory = args.inverseSideProperty\n\n        this.isLazy = args.isLazy || false\n        // this.isCascadeInsert = args.options.cascade === true || (args.options.cascade instanceof Array && args.options.cascade.indexOf(\"insert\") !== -1);\n        // this.isCascadeUpdate = args.options.cascade === true || (args.options.cascade instanceof Array && args.options.cascade.indexOf(\"update\") !== -1);\n        // this.isCascadeRemove = args.options.cascade === true || (args.options.cascade instanceof Array && args.options.cascade.indexOf(\"remove\") !== -1);\n        // this.isCascadeSoftRemove = args.options.cascade === true || (args.options.cascade instanceof Array && args.options.cascade.indexOf(\"soft-remove\") !== -1);\n        // this.isCascadeRecover = args.options.cascade === true || (args.options.cascade instanceof Array && args.options.cascade.indexOf(\"recover\") !== -1);\n        this.isCascadeInsert =\n            args.options.cascade === true ||\n            (Array.isArray(args.options.cascade) &&\n                args.options.cascade.indexOf(\"insert\") !== -1)\n        this.isCascadeUpdate =\n            args.options.cascade === true ||\n            (Array.isArray(args.options.cascade) &&\n                args.options.cascade.indexOf(\"update\") !== -1)\n        this.isCascadeRemove =\n            args.options.cascade === true ||\n            (Array.isArray(args.options.cascade) &&\n                args.options.cascade.indexOf(\"remove\") !== -1)\n        this.isCascadeSoftRemove =\n            args.options.cascade === true ||\n            (Array.isArray(args.options.cascade) &&\n                args.options.cascade.indexOf(\"soft-remove\") !== -1)\n        this.isCascadeRecover =\n            args.options.cascade === true ||\n            (Array.isArray(args.options.cascade) &&\n                args.options.cascade.indexOf(\"recover\") !== -1)\n        // this.isPrimary = args.options.primary || false;\n        this.isNullable =\n            args.options.nullable === false || this.isPrimary ? false : true\n        this.onDelete = args.options.onDelete\n        this.onUpdate = args.options.onUpdate\n        this.deferrable = args.options.deferrable\n        this.createForeignKeyConstraints =\n            args.options.createForeignKeyConstraints === false ? false : true\n        this.isEager = args.options.eager || false\n        this.persistenceEnabled =\n            args.options.persistence === false ? false : true\n        this.orphanedRowAction = args.options.orphanedRowAction || \"nullify\"\n        this.isTreeParent = args.isTreeParent || false\n        this.isTreeChildren = args.isTreeChildren || false\n\n        if (typeof args.type === \"function\") {\n            this.type =\n                typeof args.type === \"function\"\n                    ? (args.type as () => any)()\n                    : args.type\n        } else if (InstanceChecker.isEntitySchema(args.type)) {\n            this.type = args.type.options.name\n        } else if (\n            ObjectUtils.isObject(args.type) &&\n            typeof (args.type as any).name === \"string\"\n        ) {\n            this.type = (args.type as any).name\n        } else {\n            this.type = args.type as string | Function\n        }\n\n        this.isOneToOne = this.relationType === \"one-to-one\"\n        this.isOneToMany = this.relationType === \"one-to-many\"\n        this.isManyToOne = this.relationType === \"many-to-one\"\n        this.isManyToMany = this.relationType === \"many-to-many\"\n        this.isOneToOneNotOwner = this.isOneToOne ? true : false\n        this.isManyToManyNotOwner = this.isManyToMany ? true : false\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Creates join column ids map from the given related entity ids array.\n     */\n    getRelationIdMap(entity: ObjectLiteral): ObjectLiteral | undefined {\n        const joinColumns = this.isOwning\n            ? this.joinColumns\n            : this.inverseRelation!.joinColumns\n        const referencedColumns = joinColumns.map(\n            (joinColumn) => joinColumn.referencedColumn!,\n        )\n        // console.log(\"entity\", entity);\n        // console.log(\"referencedColumns\", referencedColumns);\n        return EntityMetadata.getValueMap(entity, referencedColumns)\n    }\n\n    /**\n     * Ensures that given object is an entity id map.\n     * If given id is an object then it means its already id map.\n     * If given id isn't an object then it means its a value of the id column\n     * and it creates a new id map with this value and name of the primary column.\n     */\n    ensureRelationIdMap(id: any): ObjectLiteral {\n        if (ObjectUtils.isObject(id)) return id\n\n        const joinColumns = this.isOwning\n            ? this.joinColumns\n            : this.inverseRelation!.joinColumns\n        const referencedColumns = joinColumns.map(\n            (joinColumn) => joinColumn.referencedColumn!,\n        )\n\n        if (referencedColumns.length > 1)\n            throw new TypeORMError(\n                `Cannot create relation id map for a single value because relation contains multiple referenced columns.`,\n            )\n\n        return referencedColumns[0].createValueMap(id)\n    }\n\n    /**\n     * Extracts column value from the given entity.\n     * If column is in embedded (or recursive embedded) it extracts its value from there.\n     */\n    getEntityValue(\n        entity: ObjectLiteral,\n        getLazyRelationsPromiseValue: boolean = false,\n    ): any | undefined {\n        if (entity === null || entity === undefined) return undefined\n        // extract column value from embeddeds of entity if column is in embedded\n        if (this.embeddedMetadata) {\n            // example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeddeds\n            // we need to get value of \"id\" column from the post real entity object\n\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const propertyNames = [...this.embeddedMetadata.parentPropertyNames]\n\n            // next we need to access post[data][information][counters][this.propertyName] to get column value from the counters\n            // this recursive function takes array of generated property names and gets the post[data][information][counters] embed\n            const extractEmbeddedColumnValue = (\n                propertyNames: string[],\n                value: ObjectLiteral,\n            ): any => {\n                const propertyName = propertyNames.shift()\n                if (propertyName) {\n                    if (value[propertyName]) {\n                        return extractEmbeddedColumnValue(\n                            propertyNames,\n                            value[propertyName],\n                        )\n                    }\n                    return undefined\n                }\n                return value\n            }\n\n            // once we get nested embed object we get its column, e.g. post[data][information][counters][this.propertyName]\n            const embeddedObject = extractEmbeddedColumnValue(\n                propertyNames,\n                entity,\n            )\n\n            if (this.isLazy) {\n                if (\n                    embeddedObject[\"__\" + this.propertyName + \"__\"] !==\n                    undefined\n                )\n                    return embeddedObject[\"__\" + this.propertyName + \"__\"]\n\n                if (getLazyRelationsPromiseValue === true)\n                    return embeddedObject[this.propertyName]\n\n                return undefined\n            }\n            return embeddedObject\n                ? embeddedObject[\n                      this.isLazy\n                          ? \"__\" + this.propertyName + \"__\"\n                          : this.propertyName\n                  ]\n                : undefined\n        } else {\n            // no embeds - no problems. Simply return column name by property name of the entity\n            if (this.isLazy) {\n                if (entity[\"__\" + this.propertyName + \"__\"] !== undefined)\n                    return entity[\"__\" + this.propertyName + \"__\"]\n\n                if (getLazyRelationsPromiseValue === true)\n                    return entity[this.propertyName]\n\n                return undefined\n            }\n            return entity[this.propertyName]\n        }\n    }\n\n    /**\n     * Sets given entity's relation's value.\n     * Using of this method helps to set entity relation's value of the lazy and non-lazy relations.\n     *\n     * If merge is set to true, it merges given value into currently\n     */\n    setEntityValue(entity: ObjectLiteral, value: any): void {\n        const propertyName = this.isLazy\n            ? \"__\" + this.propertyName + \"__\"\n            : this.propertyName\n\n        if (this.embeddedMetadata) {\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const extractEmbeddedColumnValue = (\n                embeddedMetadatas: EmbeddedMetadata[],\n                map: ObjectLiteral,\n            ): any => {\n                // if (!object[embeddedMetadata.propertyName])\n                //     object[embeddedMetadata.propertyName] = embeddedMetadata.create();\n\n                const embeddedMetadata = embeddedMetadatas.shift()\n                if (embeddedMetadata) {\n                    if (!map[embeddedMetadata.propertyName])\n                        map[embeddedMetadata.propertyName] =\n                            embeddedMetadata.create()\n\n                    extractEmbeddedColumnValue(\n                        embeddedMetadatas,\n                        map[embeddedMetadata.propertyName],\n                    )\n                    return map\n                }\n                map[propertyName] = value\n                return map\n            }\n            return extractEmbeddedColumnValue(\n                [...this.embeddedMetadata.embeddedMetadataTree],\n                entity,\n            )\n        } else {\n            entity[propertyName] = value\n        }\n    }\n\n    /**\n     * Creates entity id map from the given entity ids array.\n     */\n    createValueMap(value: any) {\n        // extract column value from embeds of entity if column is in embedded\n        if (this.embeddedMetadata) {\n            // example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeddeds\n            // we need to get value of \"id\" column from the post real entity object and return it in a\n            // { data: { information: { counters: { id: ... } } } } format\n\n            // first step - we extract all parent properties of the entity relative to this column, e.g. [data, information, counters]\n            const propertyNames = [...this.embeddedMetadata.parentPropertyNames]\n\n            // now need to access post[data][information][counters] to get column value from the counters\n            // and on each step we need to create complex literal object, e.g. first { data },\n            // then { data: { information } }, then { data: { information: { counters } } },\n            // then { data: { information: { counters: [this.propertyName]: entity[data][information][counters][this.propertyName] } } }\n            // this recursive function helps doing that\n            const extractEmbeddedColumnValue = (\n                propertyNames: string[],\n                map: ObjectLiteral,\n            ): any => {\n                const propertyName = propertyNames.shift()\n                if (propertyName) {\n                    map[propertyName] = {}\n                    extractEmbeddedColumnValue(propertyNames, map[propertyName])\n                    return map\n                }\n                map[this.propertyName] = value\n                return map\n            }\n            return extractEmbeddedColumnValue(propertyNames, {})\n        } else {\n            // no embeds - no problems. Simply return column property name and its value of the entity\n            return { [this.propertyName]: value }\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Builder Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend relation metadata properties.\n     * This builder method should be used only after embedded metadata tree was build.\n     */\n    build() {\n        this.propertyPath = this.buildPropertyPath()\n    }\n\n    /**\n     * Registers given foreign keys in the relation.\n     * This builder method should be used to register foreign key in the relation.\n     */\n    registerForeignKeys(...foreignKeys: ForeignKeyMetadata[]) {\n        this.foreignKeys.push(...foreignKeys)\n    }\n\n    /**\n     * Registers given join columns in the relation.\n     * This builder method should be used to register join column in the relation.\n     */\n    registerJoinColumns(\n        joinColumns: ColumnMetadata[] = [],\n        inverseJoinColumns: ColumnMetadata[] = [],\n    ) {\n        this.joinColumns = joinColumns\n        this.inverseJoinColumns = inverseJoinColumns\n        this.isOwning =\n            this.isManyToOne ||\n            ((this.isManyToMany || this.isOneToOne) &&\n                this.joinColumns.length > 0)\n        this.isOneToOneOwner = this.isOneToOne && this.isOwning\n        this.isOneToOneNotOwner = this.isOneToOne && !this.isOwning\n        this.isManyToManyOwner = this.isManyToMany && this.isOwning\n        this.isManyToManyNotOwner = this.isManyToMany && !this.isOwning\n        this.isWithJoinColumn = this.isManyToOne || this.isOneToOneOwner\n    }\n\n    /**\n     * Registers a given junction entity metadata.\n     * This builder method can be called after junction entity metadata for the many-to-many relation was created.\n     */\n    registerJunctionEntityMetadata(junctionEntityMetadata: EntityMetadata) {\n        this.junctionEntityMetadata = junctionEntityMetadata\n        this.joinTableName = junctionEntityMetadata.tableName\n        if (this.inverseRelation) {\n            this.inverseRelation.junctionEntityMetadata = junctionEntityMetadata\n            this.joinTableName = junctionEntityMetadata.tableName\n        }\n    }\n\n    /**\n     * Builds inverse side property path based on given inverse side property factory.\n     * This builder method should be used only after properties map of the inverse entity metadata was build.\n     */\n    buildInverseSidePropertyPath(): string {\n        if (this.givenInverseSidePropertyFactory) {\n            const ownerEntityPropertiesMap =\n                this.inverseEntityMetadata.propertiesMap\n            if (typeof this.givenInverseSidePropertyFactory === \"function\")\n                return this.givenInverseSidePropertyFactory(\n                    ownerEntityPropertiesMap,\n                )\n\n            if (typeof this.givenInverseSidePropertyFactory === \"string\")\n                return this.givenInverseSidePropertyFactory\n        } else if (\n            this.isTreeParent &&\n            this.entityMetadata.treeChildrenRelation\n        ) {\n            return this.entityMetadata.treeChildrenRelation.propertyName\n        } else if (\n            this.isTreeChildren &&\n            this.entityMetadata.treeParentRelation\n        ) {\n            return this.entityMetadata.treeParentRelation.propertyName\n        }\n\n        return \"\"\n    }\n\n    /**\n     * Builds relation's property path based on its embedded tree.\n     */\n    buildPropertyPath(): string {\n        if (\n            !this.embeddedMetadata ||\n            !this.embeddedMetadata.parentPropertyNames.length\n        )\n            return this.propertyName\n\n        return (\n            this.embeddedMetadata.parentPropertyNames.join(\".\") +\n            \".\" +\n            this.propertyName\n        )\n    }\n}\n"], "sourceRoot": ".."}