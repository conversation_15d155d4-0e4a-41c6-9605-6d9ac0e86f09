import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9803, // finance 微服务端口
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'your_password',
        database: 'finance_service_db', // 独立数据库！
        synchronize: true,
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ['src/modules/finance/entity/**/*.ts'],
      },
    },
  },
  cool: {
    // RPC微服务配置
    rpc: {
      name: "finance-service", // 财务微服务名称
    },
    redis: {
      host: '127.0.0.1',
      password: '',
      port: 6379,
      db: 12, // 独立的Redis数据库
    },
    eps: true,
    initDB: true,
    initMenu: true,
  } as CoolConfig,
  moleculer: {
    namespace: 'cool',
    nodeID: 'finance-service',
    transporter: 'NATS', // 可选: 'Redis'，本地开发推荐 NATS
  },
} as MidwayConfig; 