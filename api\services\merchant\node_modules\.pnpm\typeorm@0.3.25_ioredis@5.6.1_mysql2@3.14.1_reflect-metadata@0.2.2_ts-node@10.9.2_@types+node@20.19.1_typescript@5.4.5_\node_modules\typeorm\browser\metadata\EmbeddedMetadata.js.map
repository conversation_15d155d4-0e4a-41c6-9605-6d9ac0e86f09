{"version": 3, "sources": ["../browser/src/metadata/EmbeddedMetadata.ts"], "names": [], "mappings": "AAUA,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AAEvC;;GAEG;AACH,MAAM,OAAO,gBAAgB;IA+JzB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAGX;QAtID;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QAE9B;;WAEG;QACH,cAAS,GAAuB,EAAE,CAAA;QAElC;;WAEG;QACH,cAAS,GAA6B,EAAE,CAAA;QAExC;;WAEG;QACH,YAAO,GAAoB,EAAE,CAAA;QAE7B;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QAE9B;;WAEG;QACH,gBAAW,GAAyB,EAAE,CAAA;QAEtC;;WAEG;QACH,mBAAc,GAA4B,EAAE,CAAA;QAE5C;;WAEG;QACH,cAAS,GAAuB,EAAE,CAAA;QAElC;;;WAGG;QACH,6BAAwB,GAAY,IAAI,CAAA;QAExC;;;;WAIG;QACH,YAAO,GAAY,KAAK,CAAA;QAgBxB;;;;;;WAMG;QACH,wBAAmB,GAAa,EAAE,CAAA;QAElC;;WAEG;QACH,mBAAc,GAAa,EAAE,CAAA;QAE7B;;;;;WAKG;QACH,yBAAoB,GAAuB,EAAE,CAAA;QAE7C;;;;;WAKG;QACH,oBAAe,GAAqB,EAAE,CAAA;QAEtC;;WAEG;QACH,sBAAiB,GAAuB,EAAE,CAAA;QAE1C;;WAEG;QACH,sBAAiB,GAA6B,EAAE,CAAA;QAEhD;;WAEG;QACH,oBAAe,GAAoB,EAAE,CAAA;QAErC;;WAEG;QACH,oBAAe,GAAqB,EAAE,CAAA;QAEtC;;WAEG;QACH,wBAAmB,GAAyB,EAAE,CAAA;QAE9C;;WAEG;QACH,2BAAsB,GAA4B,EAAE,CAAA;QAUhD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;QAC/B,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAA;QAC7C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAA;IACvC,CAAC;IAED,wEAAwE;IACxE,iBAAiB;IACjB,wEAAwE;IAExE;;OAEG;IACH,MAAM,CAAC,OAAwC;QAC3C,IAAI,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,CAAC,EAAE,CAAC;YACrC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,OAAO,EAAE,gBAAgB,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAC9D,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QAC7C,CAAC;aAAM,CAAC;YACJ,OAAO,IAAK,IAAI,CAAC,IAAY,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,kBAAkB;IAClB,wEAAwE;IAExE,KAAK,CAAC,UAAsB;QACxB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;QAChE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC1C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAC1D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChD,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACtD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,yBAAyB,EAAE,CAAA;QAC5D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACtD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAClD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAC1D,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAEhE,IAAI,UAAU,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC;YAC3C,IAAI,CAAC,wBAAwB;gBACzB,CAAC,UAAU,CAAC,OAAO,CAAC,qBAAqB,CAAA;QACjD,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;IAED,wEAAwE;IACxE,oBAAoB;IACpB,wEAAwE;IAE9D,kBAAkB;QACxB,0EAA0E;QAC1E,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAChE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC9B,CAAC;QAED,sFAAsF;QACtF,IAAI,IAAI,CAAC,YAAY,KAAK,EAAE,IAAI,IAAI,CAAC,YAAY,KAAK,KAAK,EAAE,CAAC;YAC1D,OAAO,EAAE,CAAA;QACb,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;YACxC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;QAC9B,CAAC;QAED,MAAM,IAAI,YAAY,CAClB,mCAAmC,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE,CAC3F,CAAA;IACL,CAAC;IAES,WAAW,CAAC,UAAsB;QACxC,IAAI,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;YAC5C,OAAO,IAAI,CAAC,YAAY,CAAA;QAE5B,MAAM,QAAQ,GAAa,EAAE,CAAA;QAC7B,IAAI,IAAI,CAAC,sBAAsB;YAC3B,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,CAAA;QAEtE,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAA;QAE3C,OAAO,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,CAAC,gDAAgD;IAC9E,CAAC;IAES,wBAAwB;QAC9B,OAAO,IAAI,CAAC,sBAAsB;YAC9B,CAAC,CAAC,IAAI,CAAC,sBAAsB;iBACtB,wBAAwB,EAAE;iBAC1B,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;YAChC,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;IAC7B,CAAC;IAES,mBAAmB;QACzB,OAAO,IAAI,CAAC,sBAAsB;YAC9B,CAAC,CAAC,IAAI,CAAC,sBAAsB;iBACtB,mBAAmB,EAAE;iBACrB,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAA;IACnC,CAAC;IAES,yBAAyB;QAC/B,OAAO,IAAI,CAAC,sBAAsB;YAC9B,CAAC,CAAC,IAAI,CAAC,sBAAsB;iBACtB,yBAAyB,EAAE;iBAC3B,MAAM,CAAC,IAAI,CAAC;YACnB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IAChB,CAAC;IAES,oBAAoB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE,CAClB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,EACnD,IAAI,CAAC,OAAO,CACf,CAAA;IACL,CAAC;IAES,sBAAsB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,EACvD,IAAI,CAAC,SAAS,CACjB,CAAA;IACL,CAAC;IAES,sBAAsB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,sBAAsB,EAAE,CAAC,EACvD,IAAI,CAAC,SAAS,CACjB,CAAA;IACL,CAAC;IAES,oBAAoB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,EACrD,IAAI,CAAC,OAAO,CACf,CAAA;IACL,CAAC;IAES,oBAAoB;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE,CAAC,EACrD,IAAI,CAAC,OAAO,CACf,CAAA;IACL,CAAC;IAES,wBAAwB;QAC9B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,EAAE,CAAC,EACzD,IAAI,CAAC,WAAW,CACnB,CAAA;IACL,CAAC;IAES,2BAA2B;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CACxB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,CACpB,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,2BAA2B,EAAE,CAAC,EAC5D,IAAI,CAAC,cAAc,CACtB,CAAA;IACL,CAAC;CACJ", "file": "EmbeddedMetadata.js", "sourcesContent": ["import { ColumnMetadata } from \"./ColumnMetadata\"\nimport { RelationMetadata } from \"./RelationMetadata\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { EmbeddedMetadataArgs } from \"../metadata-args/EmbeddedMetadataArgs\"\nimport { RelationIdMetadata } from \"./RelationIdMetadata\"\nimport { RelationCountMetadata } from \"./RelationCountMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { EntityListenerMetadata } from \"./EntityListenerMetadata\"\nimport { IndexMetadata } from \"./IndexMetadata\"\nimport { UniqueMetadata } from \"./UniqueMetadata\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Contains all information about entity's embedded property.\n */\nexport class EmbeddedMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata where this embedded is.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Parent embedded in the case if this embedded inside other embedded.\n     */\n    parentEmbeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * Embedded target type.\n     */\n    type: Function | string\n\n    /**\n     * Property name on which this embedded is attached.\n     */\n    propertyName: string\n\n    /**\n     * Gets full path to this embedded property (including embedded property name).\n     * Full path is relevant when embedded is used inside other embeds (one or multiple nested).\n     * For example it will return \"counters.subcounters\".\n     */\n    propertyPath: string\n\n    /**\n     * Columns inside this embed.\n     */\n    columns: ColumnMetadata[] = []\n\n    /**\n     * Relations inside this embed.\n     */\n    relations: RelationMetadata[] = []\n\n    /**\n     * Entity listeners inside this embed.\n     */\n    listeners: EntityListenerMetadata[] = []\n\n    /**\n     * Indices applied to the embed columns.\n     */\n    indices: IndexMetadata[] = []\n\n    /**\n     * Uniques applied to the embed columns.\n     */\n    uniques: UniqueMetadata[] = []\n\n    /**\n     * Relation ids inside this embed.\n     */\n    relationIds: RelationIdMetadata[] = []\n\n    /**\n     * Relation counts inside this embed.\n     */\n    relationCounts: RelationCountMetadata[] = []\n\n    /**\n     * Nested embeddable in this embeddable (which has current embedded as parent embedded).\n     */\n    embeddeds: EmbeddedMetadata[] = []\n\n    /**\n     * Indicates if the entity should be instantiated using the constructor\n     * or via allocating a new object via `Object.create()`.\n     */\n    isAlwaysUsingConstructor: boolean = true\n\n    /**\n     * Indicates if this embedded is in array mode.\n     *\n     * This option works only in mongodb.\n     */\n    isArray: boolean = false\n\n    /**\n     * Prefix of the embedded, used instead of propertyName.\n     * If set to empty string or false, then prefix is not set at all.\n     */\n    customPrefix: string | boolean | undefined\n\n    /**\n     * Gets the prefix of the columns.\n     * By default its a property name of the class where this prefix is.\n     * But if custom prefix is set then it takes its value as a prefix.\n     * However if custom prefix is set to empty string or false, then prefix to column is not applied at all.\n     */\n    prefix: string\n\n    /**\n     * Returns array of property names of current embed and all its parent embeds.\n     *\n     * example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeds\n     * we need to get value of \"id\" column from the post real entity object.\n     * this method will return [\"data\", \"information\", \"counters\"]\n     */\n    parentPropertyNames: string[] = []\n\n    /**\n     * Returns array of prefixes of current embed and all its parent embeds.\n     */\n    parentPrefixes: string[] = []\n\n    /**\n     * Returns embed metadatas from all levels of the parent tree.\n     *\n     * example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeds\n     * this method will return [embed metadata of data, embed metadata of information, embed metadata of counters]\n     */\n    embeddedMetadataTree: EmbeddedMetadata[] = []\n\n    /**\n     * Embed metadatas from all levels of the parent tree.\n     *\n     * example: post[data][information][counters].id where \"data\", \"information\" and \"counters\" are embeds\n     * this method will return [embed metadata of data, embed metadata of information, embed metadata of counters]\n     */\n    columnsFromTree: ColumnMetadata[] = []\n\n    /**\n     * Relations of this embed and all relations from its child embeds.\n     */\n    relationsFromTree: RelationMetadata[] = []\n\n    /**\n     * Relations of this embed and all relations from its child embeds.\n     */\n    listenersFromTree: EntityListenerMetadata[] = []\n\n    /**\n     * Indices of this embed and all indices from its child embeds.\n     */\n    indicesFromTree: IndexMetadata[] = []\n\n    /**\n     * Uniques of this embed and all uniques from its child embeds.\n     */\n    uniquesFromTree: UniqueMetadata[] = []\n\n    /**\n     * Relation ids of this embed and all relation ids from its child embeds.\n     */\n    relationIdsFromTree: RelationIdMetadata[] = []\n\n    /**\n     * Relation counts of this embed and all relation counts from its child embeds.\n     */\n    relationCountsFromTree: RelationCountMetadata[] = []\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        args: EmbeddedMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.type = options.args.type()\n        this.propertyName = options.args.propertyName\n        this.customPrefix = options.args.prefix\n        this.isArray = options.args.isArray\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Creates a new embedded object.\n     */\n    create(options?: { fromDeserializer?: boolean }): any {\n        if (!(typeof this.type === \"function\")) {\n            return {}\n        }\n\n        if (options?.fromDeserializer || !this.isAlwaysUsingConstructor) {\n            return Object.create(this.type.prototype)\n        } else {\n            return new (this.type as any)()\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Builder Methods\n    // ---------------------------------------------------------------------\n\n    build(connection: DataSource): this {\n        this.embeddeds.forEach((embedded) => embedded.build(connection))\n        this.prefix = this.buildPrefix(connection)\n        this.parentPropertyNames = this.buildParentPropertyNames()\n        this.parentPrefixes = this.buildParentPrefixes()\n        this.propertyPath = this.parentPropertyNames.join(\".\")\n        this.embeddedMetadataTree = this.buildEmbeddedMetadataTree()\n        this.columnsFromTree = this.buildColumnsFromTree()\n        this.relationsFromTree = this.buildRelationsFromTree()\n        this.listenersFromTree = this.buildListenersFromTree()\n        this.indicesFromTree = this.buildIndicesFromTree()\n        this.uniquesFromTree = this.buildUniquesFromTree()\n        this.relationIdsFromTree = this.buildRelationIdsFromTree()\n        this.relationCountsFromTree = this.buildRelationCountsFromTree()\n\n        if (connection.options.entitySkipConstructor) {\n            this.isAlwaysUsingConstructor =\n                !connection.options.entitySkipConstructor\n        }\n\n        return this\n    }\n\n    // ---------------------------------------------------------------------\n    // Protected Methods\n    // ---------------------------------------------------------------------\n\n    protected buildPartialPrefix(): string[] {\n        // if prefix option was not set or explicitly set to true - default prefix\n        if (this.customPrefix === undefined || this.customPrefix === true) {\n            return [this.propertyName]\n        }\n\n        // if prefix option was set to empty string or explicity set to false - disable prefix\n        if (this.customPrefix === \"\" || this.customPrefix === false) {\n            return []\n        }\n\n        // use custom prefix\n        if (typeof this.customPrefix === \"string\") {\n            return [this.customPrefix]\n        }\n\n        throw new TypeORMError(\n            `Invalid prefix option given for ${this.entityMetadata.targetName}#${this.propertyName}`,\n        )\n    }\n\n    protected buildPrefix(connection: DataSource): string {\n        if (connection.driver.options.type === \"mongodb\")\n            return this.propertyName\n\n        const prefixes: string[] = []\n        if (this.parentEmbeddedMetadata)\n            prefixes.push(this.parentEmbeddedMetadata.buildPrefix(connection))\n\n        prefixes.push(...this.buildPartialPrefix())\n\n        return prefixes.join(\"_\") // todo: use naming strategy instead of \"_\"  !!!\n    }\n\n    protected buildParentPropertyNames(): string[] {\n        return this.parentEmbeddedMetadata\n            ? this.parentEmbeddedMetadata\n                  .buildParentPropertyNames()\n                  .concat(this.propertyName)\n            : [this.propertyName]\n    }\n\n    protected buildParentPrefixes(): string[] {\n        return this.parentEmbeddedMetadata\n            ? this.parentEmbeddedMetadata\n                  .buildParentPrefixes()\n                  .concat(this.buildPartialPrefix())\n            : this.buildPartialPrefix()\n    }\n\n    protected buildEmbeddedMetadataTree(): EmbeddedMetadata[] {\n        return this.parentEmbeddedMetadata\n            ? this.parentEmbeddedMetadata\n                  .buildEmbeddedMetadataTree()\n                  .concat(this)\n            : [this]\n    }\n\n    protected buildColumnsFromTree(): ColumnMetadata[] {\n        return this.embeddeds.reduce(\n            (columns, embedded) =>\n                columns.concat(embedded.buildColumnsFromTree()),\n            this.columns,\n        )\n    }\n\n    protected buildRelationsFromTree(): RelationMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildRelationsFromTree()),\n            this.relations,\n        )\n    }\n\n    protected buildListenersFromTree(): EntityListenerMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildListenersFromTree()),\n            this.listeners,\n        )\n    }\n\n    protected buildIndicesFromTree(): IndexMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildIndicesFromTree()),\n            this.indices,\n        )\n    }\n\n    protected buildUniquesFromTree(): UniqueMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildUniquesFromTree()),\n            this.uniques,\n        )\n    }\n\n    protected buildRelationIdsFromTree(): RelationIdMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildRelationIdsFromTree()),\n            this.relationIds,\n        )\n    }\n\n    protected buildRelationCountsFromTree(): RelationCountMetadata[] {\n        return this.embeddeds.reduce(\n            (relations, embedded) =>\n                relations.concat(embedded.buildRelationCountsFromTree()),\n            this.relationCounts,\n        )\n    }\n}\n"], "sourceRoot": ".."}