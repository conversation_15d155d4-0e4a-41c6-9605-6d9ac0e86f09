"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayEnvironmentService = void 0;
const interface_1 = require("../interface");
const util_1 = require("../util");
const decorator_1 = require("../decorator");
let MidwayEnvironmentService = class MidwayEnvironmentService {
    constructor() {
        this.moduleLoadType = 'commonjs';
    }
    getCurrentEnvironment() {
        if (!this.environment) {
            this.environment = (0, util_1.getCurrentEnvironment)();
        }
        return this.environment;
    }
    setCurrentEnvironment(environment) {
        this.environment = environment;
    }
    isDevelopmentEnvironment() {
        return (0, util_1.isDevelopmentEnvironment)(this.environment);
    }
    setModuleLoadType(moduleLoadType) {
        this.moduleLoadType = moduleLoadType;
    }
    getModuleLoadType() {
        return this.moduleLoadType;
    }
    isPkgEnvironment() {
        return typeof process['pkg'] !== 'undefined';
    }
};
MidwayEnvironmentService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayEnvironmentService);
exports.MidwayEnvironmentService = MidwayEnvironmentService;
//# sourceMappingURL=environmentService.js.map