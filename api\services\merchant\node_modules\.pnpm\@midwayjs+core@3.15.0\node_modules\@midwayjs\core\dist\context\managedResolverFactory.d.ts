import { IManagedInstance, IManagedResolver, IManagedResolverFactoryCreateOptions, IMidwayContainer, InjectModeEnum, IObjectDefinition, ObjectIdentifier } from '../interface';
export declare class ManagedReference implements IManagedInstance {
    type: string;
    name: string;
    injectMode: InjectModeEnum;
    args?: any;
}
/**
 * 解析工厂
 */
export declare class ManagedResolverFactory {
    private resolvers;
    private creating;
    singletonCache: Map<ObjectIdentifier, any>;
    context: IMidwayContainer;
    constructor(context: IMidwayContainer);
    registerResolver(resolver: IManagedResolver): void;
    resolveManaged(managed: IManagedInstance, originPropertyName: string): any;
    resolveManagedAsync(managed: IManagedInstance, originPropertyName: string): Promise<any>;
    /**
     * 同步创建对象
     * @param opt
     */
    create(opt: IManagedResolverFactoryCreateOptions): any;
    /**
     * 异步创建对象
     * @param opt
     */
    createAsync(opt: IManagedResolverFactoryCreateOptions): Promise<any>;
    destroyCache(): Promise<void>;
    /**
     * 触发单例初始化结束事件
     * @param definition 单例定义
     * @param success 成功 or 失败
     */
    private removeCreateStatus;
    isCreating(definition: IObjectDefinition): boolean;
    private compareAndSetCreateStatus;
    /**
     * 创建对象定义的代理访问逻辑
     * @param definition 对象定义
     */
    private createProxyReference;
    /**
     * 遍历依赖树判断是否循环依赖
     * @param identifier 目标id
     * @param definition 定义描述
     * @param depth
     */
    depthFirstSearch(identifier: string, definition: IObjectDefinition, depth?: string[]): boolean;
    private getObjectEventTarget;
    private checkSingletonInvokeRequest;
    private setInstanceScope;
}
//# sourceMappingURL=managedResolverFactory.d.ts.map