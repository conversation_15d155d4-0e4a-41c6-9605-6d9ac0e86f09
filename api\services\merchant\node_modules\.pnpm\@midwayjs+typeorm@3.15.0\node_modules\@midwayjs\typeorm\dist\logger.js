"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeORMLogger = void 0;
const typeorm_1 = require("typeorm");
class TypeORMLogger extends typeorm_1.FileLogger {
    constructor(typeormLogger) {
        super(true);
        this.typeormLogger = typeormLogger;
    }
    log(level, message, queryRunner) {
        switch (level) {
            case 'log':
                this.typeormLogger.debug(message);
                break;
            case 'info':
                this.typeormLogger.info(message);
                break;
            case 'warn':
                this.typeormLogger.warn(message);
                break;
        }
    }
    write(strings) {
        this.typeormLogger.info(strings);
    }
}
exports.TypeORMLogger = TypeORMLogger;
//# sourceMappingURL=logger.js.map