"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayConfigService = void 0;
const path_1 = require("path");
const interface_1 = require("../interface");
const util_1 = require("../util");
const fs_1 = require("fs");
const util = require("util");
const environmentService_1 = require("./environmentService");
const informationService_1 = require("./informationService");
const extend_1 = require("../util/extend");
const error_1 = require("../error");
const decorator_1 = require("../decorator");
const types_1 = require("../util/types");
const debug = util.debuglog('midway:debug');
let MidwayConfigService = class MidwayConfigService {
    constructor() {
        this.envDirMap = new Map();
        this.aliasMap = {
            prod: 'production',
            unittest: 'test',
        };
        this.configMergeOrder = [];
        this.configuration = {};
        this.isReady = false;
        this.externalObject = [];
        this.configFilterList = [];
    }
    init() {
        this.appInfo = {
            pkg: this.informationService.getPkg(),
            name: this.informationService.getProjectName(),
            baseDir: this.informationService.getBaseDir(),
            appDir: this.informationService.getAppDir(),
            HOME: this.informationService.getHome(),
            root: this.informationService.getRoot(),
            env: this.environmentService.getCurrentEnvironment(),
        };
    }
    add(configFilePaths) {
        for (const dir of configFilePaths) {
            if (typeof dir === 'string') {
                if (/\.\w+$/.test(dir)) {
                    // file
                    const env = this.getConfigEnv(dir);
                    const envSet = this.getEnvSet(env);
                    envSet.add(dir);
                    if (this.aliasMap[env]) {
                        this.getEnvSet(this.aliasMap[env]).add(dir);
                    }
                }
                else {
                    // directory
                    const fileStat = (0, fs_1.statSync)(dir);
                    if (fileStat.isDirectory()) {
                        const files = (0, fs_1.readdirSync)(dir);
                        this.add(files.map(file => {
                            return (0, path_1.join)(dir, file);
                        }));
                    }
                }
            }
            else {
                // object add
                for (const env in dir) {
                    this.getEnvSet(env).add(dir[env]);
                    if (this.aliasMap[env]) {
                        this.getEnvSet(this.aliasMap[env]).add(dir[env]);
                    }
                }
            }
        }
    }
    addObject(obj, reverse = false) {
        if (this.isReady) {
            obj = this.runWithFilter(obj);
            if (!obj) {
                debug('[config]: Filter config and got undefined will be drop it');
                return;
            }
            this.configMergeOrder.push({
                env: 'default',
                extraPath: '',
                value: obj,
            });
            if (reverse) {
                this.configuration = (0, extend_1.extend)(true, obj, this.configuration);
            }
            else {
                (0, extend_1.extend)(true, this.configuration, obj);
            }
        }
        else {
            this.externalObject.push(obj);
        }
    }
    getEnvSet(env) {
        if (!this.envDirMap.has(env)) {
            this.envDirMap.set(env, new Set());
        }
        return this.envDirMap.get(env);
    }
    getConfigEnv(configFilePath) {
        // parse env
        const configFileBaseName = (0, path_1.basename)(configFilePath);
        const splits = configFileBaseName.split('.');
        const suffix = splits.pop();
        if (suffix !== 'js' && suffix !== 'ts') {
            return suffix;
        }
        return splits.pop();
    }
    load() {
        if (this.isReady)
            return;
        // get default
        const defaultSet = this.getEnvSet('default');
        // get current set
        const currentEnvSet = this.getEnvSet(this.environmentService.getCurrentEnvironment());
        // merge set
        const target = {};
        const defaultSetLength = defaultSet.size;
        for (const [idx, filename] of [...defaultSet, ...currentEnvSet].entries()) {
            let config = this.loadConfig(filename);
            if (types_1.Types.isFunction(config)) {
                // eslint-disable-next-line prefer-spread
                config = config.apply(null, [this.appInfo, target]);
            }
            if (!config) {
                continue;
            }
            config = this.runWithFilter(config);
            if (!config) {
                debug('[config]: Filter config and got undefined will be drop it');
                continue;
            }
            if (typeof filename === 'string') {
                debug('[config]: Loaded config %s, %j', filename, config);
            }
            else {
                debug('[config]: Loaded config %j', config);
            }
            this.configMergeOrder.push({
                env: idx < defaultSetLength
                    ? 'default'
                    : this.environmentService.getCurrentEnvironment(),
                extraPath: filename,
                value: config,
            });
            (0, extend_1.extend)(true, target, config);
        }
        if (this.externalObject.length) {
            for (let externalObject of this.externalObject) {
                if (externalObject) {
                    externalObject = this.runWithFilter(externalObject);
                    if (!externalObject) {
                        debug('[config]: Filter config and got undefined will be drop it');
                        continue;
                    }
                    debug('[config]: Loaded external object %j', externalObject);
                    (0, extend_1.extend)(true, target, externalObject);
                    this.configMergeOrder.push({
                        env: 'default',
                        extraPath: '',
                        value: externalObject,
                    });
                }
            }
        }
        this.configuration = target;
        this.isReady = true;
    }
    getConfiguration(configKey) {
        if (configKey) {
            return (0, util_1.safelyGet)(configKey, this.configuration);
        }
        return this.configuration;
    }
    getConfigMergeOrder() {
        return this.configMergeOrder;
    }
    loadConfig(configFilename) {
        let exports = typeof configFilename === 'string'
            ? require(configFilename)
            : configFilename;
        // if es module
        if (exports && exports.__esModule) {
            if (exports && exports.default) {
                if (Object.keys(exports).length > 1) {
                    throw new error_1.MidwayInvalidConfigError(`${configFilename} should not have both a default export and named export`);
                }
                exports = exports.default;
            }
        }
        return exports;
    }
    clearAllConfig() {
        this.configuration = {};
    }
    clearConfigMergeOrder() {
        this.configMergeOrder.length = 0;
    }
    /**
     * add a config filter
     * @param filter
     */
    addFilter(filter) {
        this.configFilterList.push(filter);
    }
    runWithFilter(config) {
        for (const filter of this.configFilterList) {
            debug(`[config]: Filter config by filter = "${filter.name || 'anonymous filter'}"`);
            config = filter(config);
            debug('[config]: Filter config result = %j', config);
        }
        return config;
    }
    getAppInfo() {
        return this.appInfo;
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", environmentService_1.MidwayEnvironmentService)
], MidwayConfigService.prototype, "environmentService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", informationService_1.MidwayInformationService)
], MidwayConfigService.prototype, "informationService", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MidwayConfigService.prototype, "init", null);
MidwayConfigService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayConfigService);
exports.MidwayConfigService = MidwayConfigService;
//# sourceMappingURL=configService.js.map