{"name": "p-event", "version": "2.3.1", "description": "Promisify an event by waiting for it to be emitted", "license": "MIT", "repository": "sindresorhus/p-event", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["promise", "events", "event", "emitter", "eventemitter", "event-emitter", "emit", "emits", "listener", "promisify", "addlistener", "addeventlistener", "wait", "waits", "on", "browser", "dom", "async", "await", "promises", "bluebird"], "dependencies": {"p-timeout": "^2.0.1"}, "devDependencies": {"ava": "^1.2.1", "delay": "^4.1.0", "xo": "^0.24.0"}}