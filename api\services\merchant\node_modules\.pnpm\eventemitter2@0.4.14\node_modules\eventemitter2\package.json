{"name": "eventemitter2", "version": "0.4.14", "description": "A Node.js event emitter implementation with namespaces, wildcards, TTL and browser support.", "keywords": ["event", "events", "emitter", "eventemitter"], "author": "hij1nx <<EMAIL>> http://twitter.com/hij1nx", "contributors": ["<PERSON>", "<PERSON> <<EMAIL>> http://twitter.com/indexzero", "<PERSON> <<EMAIL>> http://twitter.com/<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> <<EMAIL>> http://www.twitter.com/jvduf", "<PERSON><PERSON> <<EMAIL>> http://www.twitter.com/indutny"], "license": "MIT", "repository": "git://github.com/hij1nx/EventEmitter2.git", "devDependencies": {"nodeunit": "*", "benchmark": ">= 0.2.2"}, "main": "./lib/eventemitter2.js", "scripts": {"test": "nodeunit test/simple/ && nodeunit test/wildcardEvents/", "benchmark": "node test/perf/benchmark.js"}, "files": ["lib/eventemitter2.js", "index.js"]}