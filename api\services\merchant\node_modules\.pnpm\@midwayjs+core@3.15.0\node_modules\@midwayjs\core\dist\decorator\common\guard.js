"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Guard = exports.UseGuard = void 0;
const decoratorManager_1 = require("../decoratorManager");
const interface_1 = require("../../interface");
const constant_1 = require("../constant");
const provide_1 = require("./provide");
const objectDef_1 = require("./objectDef");
function UseGuard(guardOrArr) {
    return (target, propertyKey, descriptor) => {
        if (!Array.isArray(guardOrArr)) {
            guardOrArr = [guardOrArr];
        }
        if (propertyKey) {
            (0, decoratorManager_1.savePropertyMetadata)(constant_1.GUARD_KEY, guardOrArr, target, propertyKey);
        }
        else {
            (0, decoratorManager_1.saveClassMetadata)(constant_1.GUARD_KEY, guardOrArr, target);
        }
    };
}
exports.UseGuard = UseGuard;
function Guard() {
    return target => {
        (0, provide_1.Provide)()(target);
        (0, objectDef_1.Scope)(interface_1.ScopeEnum.Singleton)(target);
    };
}
exports.Guard = Guard;
//# sourceMappingURL=guard.js.map