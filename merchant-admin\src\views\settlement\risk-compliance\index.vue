<template>
  <div class="risk-compliance-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <h3>风险与合规管理</h3>
          <p>配置平台风险控制策略和合规要求</p>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="risk-tabs">
        <!-- 风险控制 -->
        <el-tab-pane label="风险控制" name="risk">
          <el-form 
            :model="riskSettings" 
            label-width="140px" 
            class="settings-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="风险等级">
                  <el-select v-model="riskSettings.riskLevel" style="width: 100%">
                    <el-option label="低风险" value="low" />
                    <el-option label="中风险" value="medium" />
                    <el-option label="高风险" value="high" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="日限额">
                  <el-input-number 
                    v-model="riskSettings.dailyLimit" 
                    :min="0" 
                    :max="10000000"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="月限额">
                  <el-input-number 
                    v-model="riskSettings.monthlyLimit" 
                    :min="0" 
                    :max="100000000"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="启用反洗钱">
                  <el-switch v-model="riskSettings.enableAML" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="启用资金托管">
                  <el-switch v-model="riskSettings.enableEscrow" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="KYC等级">
                  <el-select v-model="riskSettings.kycLevel" style="width: 100%">
                    <el-option label="基础认证" value="basic" />
                    <el-option label="标准认证" value="standard" />
                    <el-option label="高级认证" value="advanced" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 合规管理 -->
        <el-tab-pane label="合规管理" name="compliance">
          <el-form 
            :model="complianceSettings" 
            label-width="140px" 
            class="settings-form"
          >
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="税务处理">
                  <el-select v-model="complianceSettings.taxHandling" style="width: 100%">
                    <el-option label="平台代扣" value="platform" />
                    <el-option label="商户自理" value="merchant" />
                    <el-option label="第三方代理" value="third-party" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报告等级">
                  <el-select v-model="complianceSettings.reportingLevel" style="width: 100%">
                    <el-option label="标准报告" value="standard" />
                    <el-option label="详细报告" value="detailed" />
                    <el-option label="完整报告" value="full" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="数据保护">
                  <el-switch v-model="complianceSettings.dataProtection" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="审计日志">
                  <el-switch v-model="complianceSettings.auditLog" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="合规检查项">
              <el-checkbox-group v-model="complianceSettings.checkItems">
                <el-checkbox label="身份验证" value="identity" />
                <el-checkbox label="资金来源" value="funding" />
                <el-checkbox label="交易监控" value="transaction" />
                <el-checkbox label="黑名单检查" value="blacklist" />
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 监控报警 -->
        <el-tab-pane label="监控报警" name="monitoring">
          <el-form 
            :model="monitoringSettings" 
            label-width="140px" 
            class="settings-form"
          >
            <el-form-item label="异常交易阈值">
              <el-input-number 
                v-model="monitoringSettings.abnormalThreshold" 
                :min="0" 
                :max="1000000"
                style="width: 200px"
              />
              <span class="form-text">元</span>
            </el-form-item>

            <el-form-item label="报警方式">
              <el-checkbox-group v-model="monitoringSettings.alertMethods">
                <el-checkbox label="邮件通知" value="email" />
                <el-checkbox label="短信通知" value="sms" />
                <el-checkbox label="系统通知" value="system" />
                <el-checkbox label="钉钉通知" value="dingtalk" />
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="监控频率">
              <el-select v-model="monitoringSettings.frequency" style="width: 200px">
                <el-option label="实时监控" value="realtime" />
                <el-option label="每小时" value="hourly" />
                <el-option label="每日" value="daily" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <div class="form-actions">
        <el-button @click="resetSettings">重置</el-button>
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存配置
        </el-button>
      </div>
    </el-card>

    <!-- 风险统计 -->
    <el-card shadow="never" style="margin-top: 20px;">
      <template #header>
        <h4>风险统计</h4>
      </template>
      
      <el-row :gutter="24">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ riskStats.highRiskCount }}</div>
            <div class="stat-label">高风险交易</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ riskStats.blockedCount }}</div>
            <div class="stat-label">拦截交易</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ riskStats.reviewCount }}</div>
            <div class="stat-label">待审核</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-number">{{ riskStats.complianceRate }}%</div>
            <div class="stat-label">合规率</div>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSettlementStore } from '@/stores/settlement'

defineOptions({ name: 'RiskCompliance' })

const settlementStore = useSettlementStore()
const activeTab = ref('risk')
const saving = ref(false)

// 风险设置
const riskSettings = reactive({
  riskLevel: 'low',
  dailyLimit: 100000,
  monthlyLimit: 3000000,
  enableAML: false,
  enableEscrow: false,
  kycLevel: 'basic'
})

// 合规设置
const complianceSettings = reactive({
  taxHandling: 'platform',
  reportingLevel: 'standard',
  dataProtection: true,
  auditLog: true,
  checkItems: ['identity', 'funding']
})

// 监控设置
const monitoringSettings = reactive({
  abnormalThreshold: 50000,
  alertMethods: ['email', 'system'],
  frequency: 'realtime'
})

// 风险统计
const riskStats = reactive({
  highRiskCount: 12,
  blockedCount: 5,
  reviewCount: 8,
  complianceRate: 98.5
})

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    // 合并所有设置到 store
    Object.assign(settlementStore.riskSettings, riskSettings)
    Object.assign(settlementStore.complianceSettings, complianceSettings)
    
    await settlementStore.saveSettings()
    ElMessage.success('风险与合规配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  settlementStore.resetSettings()
  ElMessage.info('配置已重置')
}

// 加载设置
onMounted(() => {
  settlementStore.loadSettings()
})
</script>

<style scoped lang="scss">
.risk-compliance-page {
  .card-header {
    h3 {
      margin: 0 0 8px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }
  
  .settings-form {
    margin-top: 20px;
    
    .form-text {
      margin-left: 8px;
      color: var(--el-text-color-regular);
    }
  }
  
  .form-actions {
    margin-top: 30px;
    text-align: right;
    border-top: 1px solid var(--el-border-color-light);
    padding-top: 20px;
  }
  
  .stat-card {
    text-align: center;
    padding: 20px;
    border: 1px solid var(--el-border-color-light);
    border-radius: 8px;
    
    .stat-number {
      font-size: 28px;
      font-weight: bold;
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }
    
    .stat-label {
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }
}

.risk-tabs {
  :deep(.el-tabs__content) {
    padding-top: 20px;
  }
}
</style>
