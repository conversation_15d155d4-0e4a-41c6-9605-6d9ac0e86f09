{"version": 3, "sources": ["../../src/metadata-args/EntityRepositoryMetadataArgs.ts"], "names": [], "mappings": "", "file": "EntityRepositoryMetadataArgs.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\n\n/**\n * Arguments for EntityRepositoryMetadata class, helps to construct an EntityRepositoryMetadata object.\n */\nexport interface EntityRepositoryMetadataArgs {\n    /**\n     * Constructor of the custom entity repository.\n     */\n    readonly target: Function\n\n    /**\n     * Entity managed by this custom repository.\n     */\n    readonly entity?: EntityTarget<any>\n}\n"], "sourceRoot": ".."}