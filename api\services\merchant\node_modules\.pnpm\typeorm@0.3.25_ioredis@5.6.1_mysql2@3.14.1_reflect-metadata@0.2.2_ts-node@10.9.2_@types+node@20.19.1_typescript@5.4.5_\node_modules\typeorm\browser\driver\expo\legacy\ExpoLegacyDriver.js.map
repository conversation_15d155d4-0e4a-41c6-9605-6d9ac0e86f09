{"version": 3, "sources": ["../browser/src/driver/expo/legacy/ExpoLegacyDriver.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,4CAA4C,CAAA;AAEjF,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAA;AAK/D,MAAM,OAAO,gBAAiB,SAAQ,oBAAoB;IAGtD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QAEjB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAErC,sBAAsB;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;IACrC,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;gBAC5B,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,CAAA;gBACnC,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;gBACnC,EAAE,EAAE,CAAA;YACR,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,CAAC,CAAA;YACf,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAA;QAEtD,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,wBAAwB;QAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC;gBACD,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,CACxB,CAAA;gBACD;;;kBAGE;gBACF,kBAAkB,CAAC,WAAW,CAC1B,CAAC,GAAQ,EAAE,EAAE;oBACT,GAAG,CAAC,UAAU,CACV,0BAA0B,EAC1B,EAAE,EACF,CAAC,CAAM,EAAE,MAAW,EAAE,EAAE;wBACpB,EAAE,CAAC,kBAAkB,CAAC,CAAA;oBAC1B,CAAC,EACD,CAAC,CAAM,EAAE,GAAQ,EAAE,EAAE;wBACjB,IAAI,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAA;oBACxC,CAAC,CACJ,CAAA;gBACL,CAAC,EACD,CAAC,GAAQ,EAAE,EAAE;oBACT,IAAI,CAAC,GAAG,CAAC,CAAA;gBACb,CAAC,CACJ,CAAA;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACb,IAAI,CAAC,KAAK,CAAC,CAAA;YACf,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;CACJ", "file": "ExpoLegacyDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../../sqlite-abstract/AbstractSqliteDriver\"\nimport { ExpoConnectionOptions } from \"../ExpoConnectionOptions\"\nimport { ExpoLegacyQueryRunner } from \"./ExpoLegacyQueryRunner\"\nimport { QueryRunner } from \"../../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../../data-source/DataSource\"\nimport { ReplicationMode } from \"../../types/ReplicationMode\"\n\nexport class ExpoLegacyDriver extends AbstractSqliteDriver {\n    options: ExpoConnectionOptions\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n\n        this.database = this.options.database\n\n        // load sqlite package\n        this.sqlite = this.options.driver\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            try {\n                this.queryRunner = undefined\n                this.databaseConnection._db.close()\n                this.databaseConnection = undefined\n                ok()\n            } catch (error) {\n                fail(error)\n            }\n        })\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner)\n            this.queryRunner = new ExpoLegacyQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected createDatabaseConnection() {\n        return new Promise<void>((ok, fail) => {\n            try {\n                const databaseConnection = this.sqlite.openDatabase(\n                    this.options.database,\n                )\n                /*\n                // we need to enable foreign keys in sqlite to make sure all foreign key related features\n                // working properly. this also makes onDelete work with sqlite.\n                */\n                databaseConnection.transaction(\n                    (tsx: any) => {\n                        tsx.executeSql(\n                            `PRAGMA foreign_keys = ON`,\n                            [],\n                            (t: any, result: any) => {\n                                ok(databaseConnection)\n                            },\n                            (t: any, err: any) => {\n                                fail({ transaction: t, error: err })\n                            },\n                        )\n                    },\n                    (err: any) => {\n                        fail(err)\n                    },\n                )\n            } catch (error) {\n                fail(error)\n            }\n        })\n    }\n}\n"], "sourceRoot": "../../.."}