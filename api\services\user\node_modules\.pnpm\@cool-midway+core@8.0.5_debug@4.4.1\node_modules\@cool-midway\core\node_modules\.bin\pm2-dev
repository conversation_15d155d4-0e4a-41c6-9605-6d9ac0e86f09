#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/pm2@5.4.3/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../pm2@5.4.3/node_modules/pm2/bin/pm2-dev" "$@"
else
  exec node  "$basedir/../../../../../../pm2@5.4.3/node_modules/pm2/bin/pm2-dev" "$@"
fi
