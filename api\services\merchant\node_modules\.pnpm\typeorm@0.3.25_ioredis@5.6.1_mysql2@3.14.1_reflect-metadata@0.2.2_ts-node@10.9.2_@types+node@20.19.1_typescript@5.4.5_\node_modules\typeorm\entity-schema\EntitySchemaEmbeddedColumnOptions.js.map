{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaEmbeddedColumnOptions.ts"], "names": [], "mappings": ";;;AAEA,MAAa,iCAAiC;CAkB7C;AAlBD,8EAkBC", "file": "EntitySchemaEmbeddedColumnOptions.js", "sourcesContent": ["import { EntitySchema } from \"./EntitySchema\"\n\nexport class EntitySchemaEmbeddedColumnOptions {\n    /**\n     * Schema of embedded entity\n     */\n    schema: EntitySchema\n\n    /**\n     * Embedded column prefix.\n     * If set to empty string or false, then prefix is not set at all.\n     */\n    prefix?: string | boolean\n\n    /**\n     * Indicates if this embedded is in array mode.\n     *\n     * This option works only in mongodb.\n     */\n    array?: boolean\n}\n"], "sourceRoot": ".."}