{"version": 3, "sources": ["../../src/decorator/options/ValueTransformer.ts"], "names": [], "mappings": "", "file": "ValueTransformer.js", "sourcesContent": ["/**\n * Interface for objects that deal with (un)marshalling data.\n */\nexport interface ValueTransformer {\n    /**\n     * Used to marshal data when writing to the database.\n     */\n    to(value: any): any\n\n    /**\n     * Used to unmarshal data when reading from the database.\n     */\n    from(value: any): any\n}\n"], "sourceRoot": "../.."}