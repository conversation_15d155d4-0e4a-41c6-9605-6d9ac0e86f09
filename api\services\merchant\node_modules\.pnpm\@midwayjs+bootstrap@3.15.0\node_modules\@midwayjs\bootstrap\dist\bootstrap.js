"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Bootstrap = exports.BootstrapStarter = void 0;
const core_1 = require("@midwayjs/core");
const path_1 = require("path");
const logger_1 = require("@midwayjs/logger");
const async_hooks_context_manager_1 = require("@midwayjs/async-hooks-context-manager");
const event_bus_1 = require("@midwayjs/event-bus");
const sticky_1 = require("./sticky");
class BootstrapStarter {
    constructor() {
        this.globalOptions = {};
    }
    configure(options = {}) {
        this.globalOptions = options;
        return this;
    }
    async init() {
        this.appDir = this.globalOptions.appDir =
            this.globalOptions.appDir || process.cwd();
        this.baseDir = this.globalOptions.baseDir = this.getBaseDir();
        if (process.env['MIDWAY_FORK_MODE']) {
            if (process.env['MIDWAY_FORK_MODE'] === 'cluster') {
                this.eventBus = new event_bus_1.ChildProcessEventBus({
                    isWorker: true,
                });
            }
            else if (process.env['MIDWAY_FORK_MODE'] === 'thread') {
                this.eventBus = new event_bus_1.ThreadEventBus({
                    isWorker: true,
                });
            }
        }
        if (!this.globalOptions.moduleLoadType) {
            const pkgJSON = await (0, core_1.loadModule)((0, path_1.join)(this.appDir, 'package.json'), {
                safeLoad: true,
                enableCache: false,
            });
            this.globalOptions.moduleLoadType =
                (pkgJSON === null || pkgJSON === void 0 ? void 0 : pkgJSON.type) === 'module' ? 'esm' : 'commonjs';
        }
        this.applicationContext = await (0, core_1.initializeGlobalApplicationContext)({
            asyncContextManager: (0, async_hooks_context_manager_1.createContextManager)(),
            loggerFactory: logger_1.loggers,
            ...this.globalOptions,
        });
        return this.applicationContext;
    }
    async run() {
        this.applicationContext = await this.init();
        if (this.eventBus) {
            await this.eventBus.start();
            if (process.env['MIDWAY_STICKY_MODE'] === 'true') {
                const applicationManager = this.applicationContext.get(core_1.MidwayApplicationManager);
                const io = applicationManager.getApplication('socketIO');
                (0, sticky_1.setupWorker)(io);
            }
        }
        const frameworkService = this.applicationContext.get(core_1.MidwayFrameworkService);
        // check main framework
        if (!frameworkService.getMainApp()) {
            throw new core_1.MidwayMainFrameworkMissingError();
        }
    }
    async stop() {
        if (this.applicationContext) {
            await (0, core_1.destroyGlobalApplicationContext)(this.applicationContext);
        }
        if (this.eventBus) {
            await this.eventBus.stop();
        }
    }
    getApplicationContext() {
        return this.applicationContext;
    }
    getBaseDir() {
        if (this.globalOptions.baseDir) {
            return this.globalOptions.baseDir;
        }
        if ((0, core_1.isTypeScriptEnvironment)()) {
            return (0, path_1.join)(this.appDir, 'src');
        }
        else {
            return (0, path_1.join)(this.appDir, 'dist');
        }
    }
}
exports.BootstrapStarter = BootstrapStarter;
class Bootstrap {
    /**
     * set global configuration for midway
     * @param configuration
     */
    static configure(configuration = {}) {
        this.configured = true;
        if (!this.logger && !configuration.logger) {
            this.logger = this.bootstrapLoggerFactory.createLogger('bootstrap', {
                enableError: false,
                enableFile: false,
                enableConsole: true,
            });
            if (configuration.logger === false) {
                if (this.logger['disableConsole']) {
                    // v2
                    this.logger['disableConsole']();
                }
                else {
                    // v3
                    this.logger['level'] = 'none';
                }
            }
            configuration.logger = this.logger;
        }
        else {
            this.logger = this.logger || configuration.logger;
        }
        // 处理三方框架内部依赖 process.cwd 来查找 node_modules 等问题
        if (configuration.appDir && configuration.appDir !== process.cwd()) {
            process.chdir(configuration.appDir);
        }
        this.getStarter().configure(configuration);
        return this;
    }
    static getStarter() {
        if (!this.starter) {
            this.starter = new BootstrapStarter();
        }
        return this.starter;
    }
    static async run() {
        if (!this.configured) {
            this.configure();
        }
        // https://nodejs.org/api/process.html#process_signal_events
        // https://en.wikipedia.org/wiki/Unix_signal
        // kill(2) Ctrl-C
        process.once('SIGINT', this.onSignal.bind(this, 'SIGINT'));
        // kill(3) Ctrl-\
        process.once('SIGQUIT', this.onSignal.bind(this, 'SIGQUIT'));
        // kill(15) default
        process.once('SIGTERM', this.onSignal.bind(this, 'SIGTERM'));
        process.once('exit', this.onExit.bind(this));
        this.uncaughtExceptionHandler = this.uncaughtExceptionHandler.bind(this);
        process.on('uncaughtException', this.uncaughtExceptionHandler);
        this.unhandledRejectionHandler = this.unhandledRejectionHandler.bind(this);
        process.on('unhandledRejection', this.unhandledRejectionHandler);
        return this.getStarter()
            .run()
            .then(() => {
            this.logger.info('[midway:bootstrap] current app started');
            global['MIDWAY_BOOTSTRAP_APP_READY'] = true;
            return this.getApplicationContext();
        })
            .catch(err => {
            this.logger.error(err);
            process.exit(1);
        });
    }
    static async stop() {
        await this.getStarter().stop();
        process.removeListener('uncaughtException', this.uncaughtExceptionHandler);
        process.removeListener('unhandledRejection', this.unhandledRejectionHandler);
        this.reset();
        global['MIDWAY_BOOTSTRAP_APP_READY'] = false;
    }
    static reset() {
        this.configured = false;
        this.starter = null;
        this.bootstrapLoggerFactory.close();
    }
    /**
     * on bootstrap receive a exit signal
     * @param signal
     */
    static async onSignal(signal) {
        this.logger.info('[midway:bootstrap] receive signal %s, closing', signal);
        try {
            await this.stop();
            this.logger.info('[midway:bootstrap] close done, exiting with code:0');
            process.exit(0);
        }
        catch (err) {
            this.logger.error('[midway:bootstrap] close with error: ', err);
            process.exit(1);
        }
    }
    /**
     * on bootstrap process exit
     * @param code
     */
    static onExit(code) {
        this.logger.info('[midway:bootstrap] exit with code:%s', code);
    }
    static uncaughtExceptionHandler(err) {
        if (!(err instanceof Error)) {
            err = new Error(String(err));
        }
        if (err.name === 'Error') {
            err.name = 'unhandledExceptionError';
        }
        this.logger.error(err);
    }
    static unhandledRejectionHandler(err) {
        if (!(err instanceof Error)) {
            const newError = new Error(String(err));
            // err maybe an object, try to copy the name, message and stack to the new error instance
            if (err) {
                if (err.name)
                    newError.name = err.name;
                if (err.message)
                    newError.message = err.message;
                if (err.stack)
                    newError.stack = err.stack;
            }
            err = newError;
        }
        if (err.name === 'Error') {
            err.name = 'unhandledRejectionError';
        }
        this.logger.error(err);
    }
    static getApplicationContext() {
        return this.getStarter().getApplicationContext();
    }
}
exports.Bootstrap = Bootstrap;
Bootstrap.configured = false;
Bootstrap.bootstrapLoggerFactory = new logger_1.MidwayLoggerContainer();
//# sourceMappingURL=bootstrap.js.map