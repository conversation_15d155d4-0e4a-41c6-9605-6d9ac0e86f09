export declare const analysisDecorator: (cwd: string) => Promise<{}>;
export declare const checkPort: (port: any) => Promise<boolean>;
export declare function waitDebug(port: any): Promise<unknown>;
export declare function getWssUrl(port: any, type?: string, count?: number): Promise<{
    ws: string;
    chrome: string;
}>;
export declare const tsNodeFastEnv: {
    TS_NODE_FILES: string;
    TS_NODE_TRANSPILE_ONLY: string;
};
//# sourceMappingURL=utils.d.ts.map