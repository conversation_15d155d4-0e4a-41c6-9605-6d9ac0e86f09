{"version": 3, "sources": ["../../src/commands/SubscriberCreateCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AAEvB,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uBAAuB;IAApC;QACI,YAAO,GAAG,0BAA0B,CAAA;QACpC,aAAQ,GAAG,6BAA6B,CAAA;IAiD5C,CAAC;IA/CG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAC3B,IAAI,EAAE,QAAQ;YACd,QAAQ,EAAE,6BAA6B;YACvC,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,CAAC;YACD,MAAM,QAAQ,GAAI,IAAI,CAAC,IAAe,CAAC,UAAU,CAAC,GAAG,CAAC;gBAClD,CAAC,CAAE,IAAI,CAAC,IAAe;gBACvB,CAAC,CAAC,cAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,IAAc,CAAC,CAAA;YACtD,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;YACxC,MAAM,WAAW,GAAG,uBAAuB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;YACjE,MAAM,UAAU,GAAG,MAAM,2BAAY,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAA;YAClE,IAAI,UAAU,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,SAAS,QAAQ,qBAAqB,CAAC,CAAA;YAC3D,CAAC;YACD,MAAM,2BAAY,CAAC,UAAU,CAAC,QAAQ,GAAG,KAAK,EAAE,WAAW,CAAC,CAAA;YAC5D,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CACN,cAAc,eAAI,CAAC,IAAI,CAAA,GAAG,QAAQ,KAAK,iCAAiC,CAC3E,CACJ,CAAA;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,6BAAa,CAAC,SAAS,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;YACnE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,2BAA2B;IAC3B,4EAA4E;IAE5E;;OAEG;IACO,MAAM,CAAC,WAAW,CAAC,IAAY;QACrC,OAAO;;;eAGA,IAAI;;;CAGlB,CAAA;IACG,CAAC;CACJ;AAnDD,0DAmDC", "file": "SubscriberCreateCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport yargs from \"yargs\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Generates a new subscriber.\n */\nexport class SubscriberCreateCommand implements yargs.CommandModule {\n    command = \"subscriber:create <path>\"\n    describe = \"Generates a new subscriber.\"\n\n    builder(args: yargs.Argv) {\n        return args.positional(\"path\", {\n            type: \"string\",\n            describe: \"Path of the subscriber file\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        try {\n            const fullPath = (args.path as string).startsWith(\"/\")\n                ? (args.path as string)\n                : path.resolve(process.cwd(), args.path as string)\n            const filename = path.basename(fullPath)\n            const fileContent = SubscriberCreateCommand.getTemplate(filename)\n            const fileExists = await CommandUtils.fileExists(fullPath + \".ts\")\n            if (fileExists) {\n                throw new Error(`File \"${fullPath}.ts\" already exists`)\n            }\n            await CommandUtils.createFile(fullPath + \".ts\", fileContent)\n            console.log(\n                ansi.green(\n                    `Subscriber ${ansi.blue`${fullPath}.ts`} has been created successfully.`,\n                ),\n            )\n        } catch (error) {\n            PlatformTools.logCmdErr(\"Error during subscriber creation:\", error)\n            process.exit(1)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets contents of the entity file.\n     */\n    protected static getTemplate(name: string): string {\n        return `import { EventSubscriber, EntitySubscriberInterface } from \"typeorm\"\n\n@EventSubscriber()\nexport class ${name} implements EntitySubscriberInterface {\n\n}\n`\n    }\n}\n"], "sourceRoot": ".."}