{"version": 3, "sources": ["../../src/query-builder/ReturningResultsEntityUpdator.ts"], "names": [], "mappings": ";;;AAMA,oCAAuC;AAEvC;;GAEG;AACH,MAAa,6BAA6B;IACtC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,WAAwB,EACxB,aAAiC;QADjC,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAoB;IAC5C,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,YAA0B,EAC1B,QAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;QAEvD,MAAM,OAAO,CAAC,GAAG,CACb,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE;YACvC,uIAAuI;YACvI,IACI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CACtD,QAAQ,CACX,EACH,CAAC;gBACC,IACI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;oBAC3C,QAAQ;oBACZ,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;oBAC/B,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EACrD,CAAC;oBACC,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CACtC,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;wBAC9B,MAAM,CACF,IAAI,CAAC,aAAa,CAAC,qBAAqB,CACpC,YAAY,CACf,CAAC,YAAY,CACjB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;wBACd,OAAO,MAAM,CAAA;oBACjB,CAAC,EACD,EAAmB,CACtB,CAAA;gBACL,CAAC;gBACD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;oBAC1C,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;oBAC/B,CAAC,CAAC,YAAY,CAAC,GAAG,CAAA;gBACtB,MAAM,gBAAgB,GAClB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACjD,QAAQ,EACR,MAAM,CACT,CAAA;gBACL,IAAI,gBAAgB,EAAE,CAAC;oBACnB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,MAAM,EACN,gBAAgB,CACnB,CAAA;oBACD,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;gBACrD,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,qHAAqH;gBACrH,MAAM,eAAe,GACjB,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAA;gBAC5C,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7B,iDAAiD;oBACjD,MAAM,QAAQ,GACV,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,cAAc,CACjD,MAAM,CACT,CAAA;oBACL,IAAI,CAAC,QAAQ;wBACT,MAAM,IAAI,oBAAY,CAClB,kEAAkE,CACrE,CAAA;oBAEL,mCAAmC;oBACnC,MAAM,sBAAsB,GACxB,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;yBAC1B,kBAAkB,EAAE;yBACpB,MAAM,CACH,QAAQ,CAAC,cAAc,CAAC,GAAG,CACvB,CAAC,MAAM,EAAE,EAAE,CACP,QAAQ,CAAC,UAAU;wBACnB,GAAG;wBACH,MAAM,CAAC,YAAY,CAC1B,CACJ;yBACA,SAAS,CACN,eAAe,CAAC,GAAG,CACf,CAAC,MAAM,EAAE,EAAE,CACP,QAAQ,CAAC,UAAU;wBACnB,GAAG;wBACH,MAAM,CAAC,YAAY,CAC1B,CACJ;yBACA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;yBAC1C,KAAK,CAAC,QAAQ,CAAC;yBACf,WAAW,EAAE;yBACb,SAAS,CAAC,aAAa,CAAC,CAAC,6IAA6I;yBACtK,MAAM,EAAE,CAAQ,CAAA;oBAEzB,IAAI,sBAAsB,EAAE,CAAC;wBACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,MAAM,EACN,sBAAsB,CACzB,CAAA;wBACD,YAAY,CAAC,aAAa,CAAC,IAAI,CAC3B,sBAAsB,CACzB,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,YAA0B,EAC1B,QAAyB;QAEzB,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;QACvD,IAAI,gBAAgB,GAAG,QAAQ,CAAC,4BAA4B,EAAE,CAAA;QAE9D,+EAA+E;QAC/E,uFAAuF;QACvF,uDAAuD;QACvD,MAAM,oBAAoB,GACtB,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;QACxE,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,WAAW;gBAAE,OAAO,IAAI,CAAA;YACpC,OAAO,oBAAoB,KAAK,IAAI,CAAA;QACxC,CAAC,CAAC,CAAA;QAEF,MAAM,aAAa,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACvD,IACI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;gBAC/B,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,GAAG,CAAC,EACrD,CAAC;gBACC,IACI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,EAC9D,CAAC;oBACC,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CACtC,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;wBAC9B,MAAM,CACF,IAAI,CAAC,aAAa,CAAC,qBAAqB,CACpC,YAAY,CACf,CAAC,YAAY,CACjB,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;wBACd,OAAO,MAAM,CAAA;oBACjB,CAAC,EACD,EAAmB,CACtB,CAAA;gBACL,CAAC;qBAAM,IACH,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;oBAC/C,SAAS,EACX,CAAC;oBACC,YAAY,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBAC1C,CAAC;YACL,CAAC;YAED,gDAAgD;YAChD,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC;gBAC1C,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC;gBAC/B,CAAC,CAAC,YAAY,CAAC,GAAG,CAAA;YAEtB,MAAM,YAAY,GACd,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CACjD,QAAQ,EACR,MAAM,EACN,WAAW,EACX,QAAQ,CAAC,MAAM,CAClB,IAAI,EAAE,CAAA;YAEX,IAAI,WAAW,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;gBACrD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,YAAY,EACZ,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,WAAW,CAAC,CACnD,CAAA;YACL,CAAC;YAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,MAAM,EACN,YAAY,CACf,CAAA;YAED,OAAO,YAAY,CAAA;QACvB,CAAC,CAAC,CAAA;QAEF,kHAAkH;QAClH,qEAAqE;QACrE,IACI,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAC3B,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CACvD,QAAQ,CACX,EACH,CAAC;YACC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACtC,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAE,CAAA;gBAEjD,yFAAyF;gBACzF,wFAAwF;gBACxF,2BAA2B;gBAC3B,IAAI,CAAC,QAAQ;oBACT,MAAM,IAAI,oBAAY,CAClB,kEAAkE,CACrE,CAAA;gBAEL,OAAO,QAAQ,CAAA;YACnB,CAAC,CAAC,CAAA;YAEF,oEAAoE;YACpE,kFAAkF;YAClF,iDAAiD;YACjD,sEAAsE;YACtE,kFAAkF;YAElF,MAAM,eAAe,GAAQ,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;iBACtD,kBAAkB,EAAE;iBACpB,MAAM,CACH,QAAQ,CAAC,cAAc,CAAC,GAAG,CACvB,CAAC,MAAM,EAAE,EAAE,CACP,QAAQ,CAAC,UAAU,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CACtD,CACJ;iBACA,SAAS,CACN,gBAAgB,CAAC,GAAG,CAChB,CAAC,MAAM,EAAE,EAAE,CACP,QAAQ,CAAC,UAAU,GAAG,GAAG,GAAG,MAAM,CAAC,YAAY,CACtD,CACJ;iBACA,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;iBAC1C,KAAK,CAAC,SAAS,CAAC;iBAChB,SAAS,CAAC,aAAa,CAAC,CAAC,6IAA6I;iBACtK,OAAO,EAAE,CAAA;YAEd,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACrC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,aAAa,CAAC,WAAW,CAAC,EAC1B,eAAe,CAAC,WAAW,CAAC,CAC/B,CAAA;gBAED,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAC1B,QAAQ,CAAC,MAAa,EACtB,MAAM,EACN,eAAe,CAAC,WAAW,CAAC,CAC/B,CAAA;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAE,CAAA;YACjD,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACvC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,CAAA;QAC/D,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,2BAA2B;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACxD,CAAC,MAAM,EAAE,EAAE;YACP,OAAO,CACH,MAAM,CAAC,YAAY,KAAK,SAAS;gBACjC,MAAM,CAAC,YAAY;gBACnB,MAAM,CAAC,SAAS,CACnB,CAAA;QACL,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;OAEG;IACH,+BAA+B;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACxD,CAAC,MAAM,EAAE,EAAE;YACP,OAAO,CACH,MAAM,CAAC,YAAY,KAAK,SAAS;gBACjC,MAAM,CAAC,YAAY;gBACnB,MAAM,CAAC,SAAS;gBAChB,MAAM,CAAC,YAAY,CACtB,CAAA;QACL,CAAC,CACJ,CAAA;IACL,CAAC;CACJ;AA1SD,sEA0SC", "file": "ReturningResultsEntityUpdator.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { UpdateResult } from \"./result/UpdateResult\"\nimport { InsertResult } from \"./result/InsertResult\"\nimport { TypeORMError } from \"../error\"\n\n/**\n * Updates entity with returning results in the entity insert and update operations.\n */\nexport class ReturningResultsEntityUpdator {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected queryRunner: QueryRunner,\n        protected expressionMap: QueryExpressionMap,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Updates entities with a special columns after updation query execution.\n     */\n    async update(\n        updateResult: UpdateResult,\n        entities: ObjectLiteral[],\n    ): Promise<void> {\n        const metadata = this.expressionMap.mainAlias!.metadata\n\n        await Promise.all(\n            entities.map(async (entity, entityIndex) => {\n                // if database supports returning/output statement then we already should have updating values in the raw data returned by insert query\n                if (\n                    this.queryRunner.connection.driver.isReturningSqlSupported(\n                        \"update\",\n                    )\n                ) {\n                    if (\n                        this.queryRunner.connection.driver.options.type ===\n                            \"oracle\" &&\n                        Array.isArray(updateResult.raw) &&\n                        this.expressionMap.extraReturningColumns.length > 0\n                    ) {\n                        updateResult.raw = updateResult.raw.reduce(\n                            (newRaw, rawItem, rawItemIndex) => {\n                                newRaw[\n                                    this.expressionMap.extraReturningColumns[\n                                        rawItemIndex\n                                    ].databaseName\n                                ] = rawItem[0]\n                                return newRaw\n                            },\n                            {} as ObjectLiteral,\n                        )\n                    }\n                    const result = Array.isArray(updateResult.raw)\n                        ? updateResult.raw[entityIndex]\n                        : updateResult.raw\n                    const returningColumns =\n                        this.queryRunner.connection.driver.createGeneratedMap(\n                            metadata,\n                            result,\n                        )\n                    if (returningColumns) {\n                        this.queryRunner.manager.merge(\n                            metadata.target as any,\n                            entity,\n                            returningColumns,\n                        )\n                        updateResult.generatedMaps.push(returningColumns)\n                    }\n                } else {\n                    // for driver which do not support returning/output statement we need to perform separate query and load what we need\n                    const updationColumns =\n                        this.expressionMap.extraReturningColumns\n                    if (updationColumns.length > 0) {\n                        // get entity id by which we will get needed data\n                        const entityId =\n                            this.expressionMap.mainAlias!.metadata.getEntityIdMap(\n                                entity,\n                            )\n                        if (!entityId)\n                            throw new TypeORMError(\n                                `Cannot update entity because entity id is not set in the entity.`,\n                            )\n\n                        // execute query to get needed data\n                        const loadedReturningColumns =\n                            (await this.queryRunner.manager\n                                .createQueryBuilder()\n                                .select(\n                                    metadata.primaryColumns.map(\n                                        (column) =>\n                                            metadata.targetName +\n                                            \".\" +\n                                            column.propertyPath,\n                                    ),\n                                )\n                                .addSelect(\n                                    updationColumns.map(\n                                        (column) =>\n                                            metadata.targetName +\n                                            \".\" +\n                                            column.propertyPath,\n                                    ),\n                                )\n                                .from(metadata.target, metadata.targetName)\n                                .where(entityId)\n                                .withDeleted()\n                                .setOption(\"create-pojo\") // use POJO because created object can contain default values, e.g. property = null and those properties might be overridden by merge process\n                                .getOne()) as any\n\n                        if (loadedReturningColumns) {\n                            this.queryRunner.manager.merge(\n                                metadata.target as any,\n                                entity,\n                                loadedReturningColumns,\n                            )\n                            updateResult.generatedMaps.push(\n                                loadedReturningColumns,\n                            )\n                        }\n                    }\n                }\n            }),\n        )\n    }\n\n    /**\n     * Updates entities with a special columns after insertion query execution.\n     */\n    async insert(\n        insertResult: InsertResult,\n        entities: ObjectLiteral[],\n    ): Promise<void> {\n        const metadata = this.expressionMap.mainAlias!.metadata\n        let insertionColumns = metadata.getInsertionReturningColumns()\n\n        // to prevent extra select SQL execution for databases not supporting RETURNING\n        // in the case if we have generated column and it's value returned by underlying driver\n        // we remove this column from the insertionColumns list\n        const needToCheckGenerated =\n            this.queryRunner.connection.driver.isReturningSqlSupported(\"insert\")\n        insertionColumns = insertionColumns.filter((column) => {\n            if (!column.isGenerated) return true\n            return needToCheckGenerated === true\n        })\n\n        const generatedMaps = entities.map((entity, entityIndex) => {\n            if (\n                Array.isArray(insertResult.raw) &&\n                this.expressionMap.extraReturningColumns.length > 0\n            ) {\n                if (\n                    this.queryRunner.connection.driver.options.type === \"oracle\"\n                ) {\n                    insertResult.raw = insertResult.raw.reduce(\n                        (newRaw, rawItem, rawItemIndex) => {\n                            newRaw[\n                                this.expressionMap.extraReturningColumns[\n                                    rawItemIndex\n                                ].databaseName\n                            ] = rawItem[0]\n                            return newRaw\n                        },\n                        {} as ObjectLiteral,\n                    )\n                } else if (\n                    this.queryRunner.connection.driver.options.type ===\n                    \"spanner\"\n                ) {\n                    insertResult.raw = insertResult.raw[0]\n                }\n            }\n\n            // get all values generated by a database for us\n            const result = Array.isArray(insertResult.raw)\n                ? insertResult.raw[entityIndex]\n                : insertResult.raw\n\n            const generatedMap =\n                this.queryRunner.connection.driver.createGeneratedMap(\n                    metadata,\n                    result,\n                    entityIndex,\n                    entities.length,\n                ) || {}\n\n            if (entityIndex in this.expressionMap.locallyGenerated) {\n                this.queryRunner.manager.merge(\n                    metadata.target as any,\n                    generatedMap,\n                    this.expressionMap.locallyGenerated[entityIndex],\n                )\n            }\n\n            this.queryRunner.manager.merge(\n                metadata.target as any,\n                entity,\n                generatedMap,\n            )\n\n            return generatedMap\n        })\n\n        // for postgres and mssql we use returning/output statement to get values of inserted default and generated values\n        // for other drivers we have to re-select this data from the database\n        if (\n            insertionColumns.length > 0 &&\n            !this.queryRunner.connection.driver.isReturningSqlSupported(\n                \"insert\",\n            )\n        ) {\n            const entityIds = entities.map((entity) => {\n                const entityId = metadata.getEntityIdMap(entity)!\n\n                // We have to check for an empty `entityId` - if we don't, the query against the database\n                // effectively drops the `where` clause entirely and the first record will be returned -\n                // not what we want at all.\n                if (!entityId)\n                    throw new TypeORMError(\n                        `Cannot update entity because entity id is not set in the entity.`,\n                    )\n\n                return entityId\n            })\n\n            // to select just inserted entities we need a criteria to select by.\n            // for newly inserted entities in drivers which do not support returning statement\n            // row identifier can only be an increment column\n            // (since its the only thing that can be generated by those databases)\n            // or (and) other primary key which is defined by a user and inserted value has it\n\n            const returningResult: any = await this.queryRunner.manager\n                .createQueryBuilder()\n                .select(\n                    metadata.primaryColumns.map(\n                        (column) =>\n                            metadata.targetName + \".\" + column.propertyPath,\n                    ),\n                )\n                .addSelect(\n                    insertionColumns.map(\n                        (column) =>\n                            metadata.targetName + \".\" + column.propertyPath,\n                    ),\n                )\n                .from(metadata.target, metadata.targetName)\n                .where(entityIds)\n                .setOption(\"create-pojo\") // use POJO because created object can contain default values, e.g. property = null and those properties might be overridden by merge process\n                .getMany()\n\n            entities.forEach((entity, entityIndex) => {\n                this.queryRunner.manager.merge(\n                    metadata.target as any,\n                    generatedMaps[entityIndex],\n                    returningResult[entityIndex],\n                )\n\n                this.queryRunner.manager.merge(\n                    metadata.target as any,\n                    entity,\n                    returningResult[entityIndex],\n                )\n            })\n        }\n\n        entities.forEach((entity, entityIndex) => {\n            const entityId = metadata.getEntityIdMap(entity)!\n            insertResult.identifiers.push(entityId)\n            insertResult.generatedMaps.push(generatedMaps[entityIndex])\n        })\n    }\n\n    /**\n     * Columns we need to be returned from the database when we update entity.\n     */\n    getUpdationReturningColumns(): ColumnMetadata[] {\n        return this.expressionMap.mainAlias!.metadata.columns.filter(\n            (column) => {\n                return (\n                    column.asExpression !== undefined ||\n                    column.isUpdateDate ||\n                    column.isVersion\n                )\n            },\n        )\n    }\n\n    /**\n     * Columns we need to be returned from the database when we soft delete and restore entity.\n     */\n    getSoftDeletionReturningColumns(): ColumnMetadata[] {\n        return this.expressionMap.mainAlias!.metadata.columns.filter(\n            (column) => {\n                return (\n                    column.asExpression !== undefined ||\n                    column.isUpdateDate ||\n                    column.isVersion ||\n                    column.isDeleteDate\n                )\n            },\n        )\n    }\n}\n"], "sourceRoot": ".."}