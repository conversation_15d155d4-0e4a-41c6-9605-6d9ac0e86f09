import { BaseController, CoolCommException } from '@cool-midway/core';
import { Body, Controller, Inject, Post } from '@midwayjs/decorator';
import { Context } from '@midwayjs/koa';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MerchantSysRoleEntity } from '../entity/sys/role';

/**
 * 商户系统角色
 */
@Controller('/merchant/sys/role')
export class MerchantSysRoleController extends BaseController {
  @InjectEntityModel(MerchantSysRoleEntity)
  roleEntity: Repository<MerchantSysRoleEntity>;

  @Inject()
  ctx: Context;

  /**
   * 分页查询
   */
  @Post('/page')
  async page(@Body() body) {
    const { keyWord, status } = body;
    const query = this.roleEntity
      .createQueryBuilder('a')
      .where('1 = 1');

    // 关键字搜索
    if (keyWord) {
      query.andWhere('(a.name LIKE :keyWord OR a.label LIKE :keyWord)', {
        keyWord: `%${keyWord}%`
      });
    }

    // 状态筛选
    if (status !== undefined && status !== null) {
      query.andWhere('a.status = :status', { status });
    }

    const result = await query
      .orderBy('a.createTime', 'DESC')
      .skip((body.page - 1) * body.size)
      .take(body.size)
      .getManyAndCount();

    return {
      list: result[0],
      pagination: {
        page: body.page,
        size: body.size,
        total: result[1]
      }
    };
  }
} 