{"version": 3, "sources": ["../../src/metadata-args/DiscriminatorValueMetadataArgs.ts"], "names": [], "mappings": "", "file": "DiscriminatorValueMetadataArgs.js", "sourcesContent": ["/**\n * DiscriminatorValue properties.\n */\nexport interface DiscriminatorValueMetadataArgs {\n    /**\n     * Class to which discriminator name is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * Discriminator value.\n     */\n    readonly value: any\n}\n"], "sourceRoot": ".."}