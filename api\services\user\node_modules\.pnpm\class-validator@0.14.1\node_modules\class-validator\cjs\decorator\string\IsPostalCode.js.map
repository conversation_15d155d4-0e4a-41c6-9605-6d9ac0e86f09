{"version": 3, "file": "IsPostalCode.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsPostalCode.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,8EAA+D;AAGlD,QAAA,cAAc,GAAG,cAAc,CAAC;AAE7C;;;GAGG;AACH,SAAgB,YAAY,CAAC,KAAc,EAAE,MAA4C;IACvF,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,sBAAqB,EAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC3E,CAAC;AAFD,oCAEC;AAED;;;GAGG;AACH,SAAgB,YAAY,CAC1B,MAA6C,EAC7C,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC;YAC7E,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iCAAiC,EAAE,iBAAiB,CAAC;SAC9G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAfD,oCAeC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isPostalCodeValidator from 'validator/lib/isPostalCode';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_POSTAL_CODE = 'isPostalCode';\n\n/**\n * Check if the string is a postal code, in the specified locale.\n * If given value is not a string, then it returns false.\n */\nexport function isPostalCode(value: unknown, locale: 'any' | ValidatorJS.PostalCodeLocale): boolean {\n  return typeof value === 'string' && isPostalCodeValidator(value, locale);\n}\n\n/**\n * Check if the string is a postal code, in the specified locale.\n * If given value is not a string, then it returns false.\n */\nexport function IsPostalCode(\n  locale?: 'any' | ValidatorJS.PostalCodeLocale,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_POSTAL_CODE,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isPostalCode(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a postal code', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}