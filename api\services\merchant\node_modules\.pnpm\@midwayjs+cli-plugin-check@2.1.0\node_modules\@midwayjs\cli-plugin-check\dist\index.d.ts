import { BasePlugin } from '@midwayjs/command-core';
import { Runner } from '@midwayjs/luckyeye';
import { AnalyzeResult } from '@midwayjs/locate';
declare enum ProjectType {
    FaaS = "faas",
    MigrateToFaaS = "migrateToFaaS"
}
type RunnerItem = (runner: Runner) => void;
export declare class CheckPlugin extends BasePlugin {
    projectType: ProjectType;
    currentGroup: string;
    servicePath: any;
    sourcesInfo: {
        code: string;
        file: string;
        tsSourceFile: string;
    }[];
    errors: any[];
    commands: {
        check: {
            usage: string;
            lifecycleEvents: string[];
        };
    };
    hooks: {
        'check:start': any;
        'check:check': any;
    };
    globalData: {
        cwd: string;
        projectType: ProjectType;
        tsCodeRoot: string;
        locateResult: AnalyzeResult;
    };
    pkg: any;
    pkgContent: string;
    isHooks: boolean;
    start(): Promise<void>;
    check(): Promise<void>;
    getRuleList(): Promise<Array<RunnerItem>>;
    packageJson(): Promise<RunnerItem>;
    projectStruct(): Promise<RunnerItem>;
    ruleFaaS(): Promise<RunnerItem>;
    ruleFYaml(): RunnerItem;
    ruleTSConfig(): RunnerItem;
    ruleIoc(): Promise<(runner: any) => void>;
    private getCheckReporter;
    private checkReporterOutput;
    private getYamlFilePosition;
    private getCwd;
}
export {};
//# sourceMappingURL=index.d.ts.map