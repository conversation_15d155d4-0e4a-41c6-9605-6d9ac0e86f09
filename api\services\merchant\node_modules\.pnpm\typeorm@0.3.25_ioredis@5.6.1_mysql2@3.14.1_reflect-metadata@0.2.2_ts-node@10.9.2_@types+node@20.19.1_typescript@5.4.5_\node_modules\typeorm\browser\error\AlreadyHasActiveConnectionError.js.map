{"version": 3, "sources": ["../browser/src/error/AlreadyHasActiveConnectionError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,+BAAgC,SAAQ,YAAY;IAC7D,YAAY,cAAsB;QAC9B,KAAK,CACD,yCAAyC,cAAc,uCAAuC;YAC1F,4DAA4D,CACnE,CAAA;IACL,CAAC;CACJ", "file": "AlreadyHasActiveConnectionError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to recreate connection with the same name, but previous connection was not closed yet.\n */\nexport class AlreadyHasActiveConnectionError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot create a new connection named \"${connectionName}\", because connection with such name ` +\n                `already exist and it now has an active connection session.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}