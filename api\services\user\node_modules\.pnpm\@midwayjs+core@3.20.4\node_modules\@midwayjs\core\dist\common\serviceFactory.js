"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceFactory = void 0;
const extend_1 = require("../util/extend");
const priorityManager_1 = require("./priorityManager");
const decorator_1 = require("../decorator");
/**
 * 多客户端工厂实现
 */
class ServiceFactory {
    constructor() {
        this.clients = new Map();
        this.options = {};
    }
    async initClients(options = {}) {
        this.options = options;
        // merge options.client to options.clients['default']
        if (options.client) {
            options.clients = options.clients || {};
            options.clients['default'] = options.clients['default'] || {};
            (0, extend_1.extend)(true, options.clients['default'], options.client);
        }
        // multi client
        if (options.clients) {
            for (const id of Object.keys(options.clients)) {
                await this.createInstance(options.clients[id], id);
            }
        }
        // set priority
        this.clientPriority = options.priority || {};
    }
    get(id = 'default') {
        return this.clients.get(id);
    }
    has(id) {
        return this.clients.has(id);
    }
    async createInstance(config, clientName) {
        // options.default will be merge in to options.clients[id]
        config = (0, extend_1.extend)(true, {}, this.options['default'], config);
        const client = await this.createClient(config, clientName);
        if (client) {
            if (clientName) {
                this.clients.set(clientName, client);
            }
            return client;
        }
    }
    async destroyClient(client, clientName) { }
    async stop() {
        for (const [name, value] of this.clients.entries()) {
            await this.destroyClient(value, name);
        }
    }
    getDefaultClientName() {
        return this.options['defaultClientName'];
    }
    getClients() {
        return this.clients;
    }
    getClientKeys() {
        return Array.from(this.clients.keys());
    }
    getClientPriority(name) {
        return this.priorityManager.getPriority(this.clientPriority[name]);
    }
    isHighPriority(name) {
        return this.priorityManager.isHighPriority(this.clientPriority[name]);
    }
    isMediumPriority(name) {
        return this.priorityManager.isMediumPriority(this.clientPriority[name]);
    }
    isLowPriority(name) {
        return this.priorityManager.isLowPriority(this.clientPriority[name]);
    }
}
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", priorityManager_1.MidwayPriorityManager)
], ServiceFactory.prototype, "priorityManager", void 0);
exports.ServiceFactory = ServiceFactory;
//# sourceMappingURL=serviceFactory.js.map