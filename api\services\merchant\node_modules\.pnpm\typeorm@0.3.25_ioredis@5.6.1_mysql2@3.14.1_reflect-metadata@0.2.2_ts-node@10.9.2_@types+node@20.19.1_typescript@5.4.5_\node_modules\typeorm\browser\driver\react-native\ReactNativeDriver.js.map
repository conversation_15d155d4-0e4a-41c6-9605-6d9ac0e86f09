{"version": 3, "sources": ["../browser/src/driver/react-native/ReactNativeDriver.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAA;AAEhD,OAAO,EAAE,kBAAkB,EAAE,MAAM,yCAAyC,CAAA;AAQ5E,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,sBAAsB,EAAE,MAAM,mCAAmC,CAAA;AAE1E,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAA;AAI5D,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAG5D,OAAO,EAAE,sBAAsB,EAAE,MAAM,0BAA0B,CAAA;AAWjE;;GAEG;AACH,MAAM,OAAO,iBAAiB;IA4M1B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QA9KlC;;WAEG;QACH,iBAAY,GAAY,KAAK,CAAA;QAO7B;;WAEG;QACH,gBAAW,GAAG,IAAI,CAAA;QAElB;;WAEG;QACH,uBAAkB,GAAiC,QAAQ,CAAA;QAE3D;;;;;WAKG;QACH,uBAAkB,GAAiB;YAC/B,KAAK;YACL,SAAS;YACT,SAAS;YACT,UAAU;YACV,WAAW;YACX,QAAQ;YACR,kBAAkB;YAClB,MAAM;YACN,MAAM;YACN,SAAS;YACT,WAAW;YACX,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,MAAM;YACN,QAAQ;YACR,kBAAkB;YAClB,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;YACN,MAAM;YACN,UAAU;SACb,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB,CAAC,uBAAuB,CAAC,CAAA;QAE9D;;WAEG;QACH,0BAAqB,GAAiB;YAClC,WAAW;YACX,SAAS;YACT,mBAAmB;YACnB,OAAO;YACP,kBAAkB;YAClB,UAAU;YACV,MAAM;YACN,MAAM;YACN,MAAM;SACT,CAAA;QAED;;WAEG;QACH,iBAAY,GAAiB,EAAE,CAAA;QAE/B;;WAEG;QACH,6BAAwB,GAAiB;YACrC,MAAM;YACN,QAAQ;YACR,kBAAkB;YAClB,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;YACT,MAAM;YACN,MAAM;YACN,UAAU;SACb,CAAA;QAED;;WAEG;QACH,yBAAoB,GAAiB;YACjC,MAAM;YACN,QAAQ;YACR,kBAAkB;YAClB,OAAO;YACP,MAAM;YACN,SAAS;YACT,SAAS;SACZ,CAAA;QAED;;;WAGG;QACH,oBAAe,GAAsB;YACjC,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,UAAU,EAAE,UAAU;YACtB,iBAAiB,EAAE,iBAAiB;YACpC,UAAU,EAAE,UAAU;YACtB,kBAAkB,EAAE,IAAI;YACxB,OAAO,EAAE,SAAS;YAClB,SAAS,EAAE,SAAS;YACpB,WAAW,EAAE,SAAS;YACtB,aAAa,EAAE,SAAS;YACxB,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EAAE,KAAK;YACd,eAAe,EAAE,SAAS;YAC1B,SAAS,EAAE,QAAQ;YACnB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE,MAAM;YAClB,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,SAAS;YACvB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,SAAS;YACzB,aAAa,EAAE,SAAS;YACxB,YAAY,EAAE,SAAS;YACvB,aAAa,EAAE,MAAM;SACxB,CAAA;QAcD,oBAAe,GAAoB;YAC/B,OAAO,EAAE,IAAI;YACb,qBAAqB,EAAE,IAAI;SAC9B,CAAA;QAED,4EAA4E;QAC5E,uBAAuB;QACvB,4EAA4E;QAE5E;;WAEG;QACH,sBAAiB,GAAiB,EAAE,CAAA;QAOhC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAuC,CAAA;QACjE,wEAAwE;QACxE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAErC,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAE5E;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YACjB,IAAI,CAAC,WAAW,GAAG,IAAI,sBAAsB,CAAC,IAAI,CAAC,CAAA;QAEvD,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,IAAI,CAAC,kBAAkB,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;IACnE,CAAC;IAED;;OAEG;IACH,YAAY;QACR,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;YAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;IACN,CAAC;IAED,oBAAoB;QAChB,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,MAAM,CAAA;IACvD,CAAC;IAED,uCAAuC,CAAC,IAAY;QAChD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,EAAE,YAAY,CAAA;IACvD,CAAC;IAED,uCAAuC,CACnC,MAAc;QAEd,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAC7C,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC,MAAM,KAAK,YAAY,CAChD,EAAE,sBAAsB,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,mBAAmB;QACf,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,KAAU,EAAE,cAA8B;QAC7D,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,WAAW,CACtC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YAAE,OAAO,KAAK,CAAA;QAEvD,IACI,cAAc,CAAC,IAAI,KAAK,OAAO;YAC/B,cAAc,CAAC,IAAI,KAAK,SAAS,EACnC,CAAC;YACC,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACjC,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,OAAO,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QACjD,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC,sGAAsG;YACtG,uCAAuC;YACvC,OAAO,SAAS,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAAA;QACxD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,OAAO,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,OAAO,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,oBAAoB,CAAC,KAAU,EAAE,cAA8B;QAC3D,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS;YACrC,OAAO,cAAc,CAAC,WAAW;gBAC7B,CAAC,CAAC,sBAAsB,CAAC,aAAa,CAChC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR;gBACH,CAAC,CAAC,KAAK,CAAA;QAEf,IACI,cAAc,CAAC,IAAI,KAAK,OAAO;YAC/B,cAAc,CAAC,IAAI,KAAK,SAAS,EACnC,CAAC;YACC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;QAChC,CAAC;aAAM,IACH,cAAc,CAAC,IAAI,KAAK,UAAU;YAClC,cAAc,CAAC,IAAI,KAAK,IAAI,EAC9B,CAAC;YACC;;;;;;;;eAQG;YACH,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,+EAA+E;gBAC/E,4CAA4C;gBAC5C,kDAAkD;gBAClD,kDAAkD;gBAClD,6DAA6D;gBAE7D,IAAI,+BAA+B,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC9C,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;gBACnC,CAAC;gBACD,IACI,mDAAmD,CAAC,IAAI,CACpD,KAAK,CACR,EACH,CAAC;oBACC,KAAK,IAAI,GAAG,CAAA;gBAChB,CAAC;YACL,CAAC;YAED,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAA;QAClD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,KAAK,GAAG,SAAS,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC9C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YAChD,KAAK,GAAG,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;QAChD,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YAC/C,KAAK,GAAG,SAAS,CAAC,kBAAkB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;QAC/D,CAAC;aAAM,IAAI,cAAc,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YACxC,8BAA8B;YAC9B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;QACpD,CAAC;QAED,IAAI,cAAc,CAAC,WAAW;YAC1B,KAAK,GAAG,sBAAsB,CAAC,aAAa,CACxC,cAAc,CAAC,WAAW,EAC1B,KAAK,CACR,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;;OAGG;IACH,yBAAyB,CACrB,GAAW,EACX,UAAyB,EACzB,gBAA+B;QAE/B,MAAM,iBAAiB,GAAU,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,GAAG,CAC9D,CAAC,GAAG,EAAE,EAAE;YACJ,yDAAyD;YACzD,IAAI,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE,CAAC;gBAC7C,OAAO,gBAAgB,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;YACjD,CAAC;YAED,IAAI,gBAAgB,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;gBACxC,OAAO,SAAS,CAAC,4BAA4B,CACzC,gBAAgB,CAAC,GAAG,CAAC,CACxB,CAAA;YACL,CAAC;YAED,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAA;QAChC,CAAC,CACJ,CAAA;QAED,IAAI,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YAC9C,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;QAEnC,GAAG,GAAG,GAAG,CAAC,OAAO,CACb,6BAA6B,EAC7B,CAAC,IAAI,EAAE,OAAe,EAAE,GAAW,EAAU,EAAE;YAC3C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC;gBAClC,OAAO,IAAI,CAAA;YACf,CAAC;YAED,MAAM,KAAK,GAAQ,UAAU,CAAC,GAAG,CAAC,CAAA;YAElC,IAAI,OAAO,EAAE,CAAC;gBACV,OAAO,KAAK;qBACP,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE;oBACZ,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;oBACzB,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,EAAE,CAAA;YAClB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACnC,OAAO,MAAM,CAAC,KAAK,CAAC,CAAA;YACxB,CAAC;YAED,mEAAmE;YACnE,eAAe;YACf,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,iBAAiB,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAA;gBAC9B,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;YACL,CAAC;YAED,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;gBACxB,iBAAiB,CAAC,IAAI,CAClB,SAAS,CAAC,4BAA4B,CAAC,KAAK,CAAC,CAChD,CAAA;gBACD,OAAO,IAAI,CAAC,eAAe,CACvB,GAAG,EACH,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAC/B,CAAA;YACL,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC7B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;QAClE,CAAC,CACJ,CAAA,CAAC,kEAAkE;QACpE,OAAO,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,UAAkB;QACrB,OAAO,GAAG,GAAG,UAAU,GAAG,GAAG,CAAA;IACjC,CAAC;IAED;;;;;OAKG;IACH,cAAc,CACV,SAAiB,EACjB,MAAe,EACf,QAAiB;QAEjB,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,cAAc,CACV,MAAgE;QAEhE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAA;QACpC,MAAM,YAAY,GAAG,SAAS,CAAA;QAE9B,IAAI,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAC9B,MAAM,CAAC,MAAM;gBACT,CAAC,CAAC,IAAI,MAAM,CAAC,MAAM,MAAM,MAAM,CAAC,IAAI,GAAG;gBACvC,CAAC,CAAC,MAAM,CAAC,IAAI,CACpB,CAAA;YAED,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC9D,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBACtD,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAA;YAE9D,OAAO;gBACH,QAAQ,EACJ,MAAM,CAAC,kBAAkB;oBACzB,MAAM,CAAC,QAAQ;oBACf,cAAc;gBAClB,MAAM,EACF,MAAM,CAAC,gBAAgB,IAAI,MAAM,CAAC,MAAM,IAAI,YAAY;gBAC5D,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,IAAI,eAAe,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3C,2CAA2C;YAE3C,OAAO;gBACH,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,cAAc;gBAC3C,MAAM,EAAE,MAAM,CAAC,MAAM,IAAI,YAAY;gBACrC,SAAS,EAAE,MAAM,CAAC,SAAS;aAC9B,CAAA;QACL,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAE/B,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO;gBACH,QAAQ,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,cAAc;gBACpC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,YAAY;gBAChC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,QAAQ,GACV,IAAI,CAAC,uCAAuC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtD,cAAc,CAAA;YAClB,OAAO;gBACH,QAAQ,EAAE,QAAQ;gBAClB,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;gBAChB,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;aACtB,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO;gBACH,QAAQ,EAAE,cAAc;gBACxB,MAAM,EAAE,YAAY;gBACpB,SAAS,EAAE,MAAM;aACpB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,MAKb;QACG,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;YAClD,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;YAC9B,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACjC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;YACxC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;YACvC,OAAO,SAAS,CAAA;QACpB,CAAC;aAAM,CAAC;YACJ,OAAQ,MAAM,CAAC,IAAe,IAAI,EAAE,CAAA;QACxC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,cAA8B;QAC3C,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,CAAA;QAE3C,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,EAAE,GAAG,YAAY,CAAA;QAC5B,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,SAAS,EAAE,CAAC;YACpC,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAA;QACnC,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,UAAU,EAAE,CAAC;YACrC,OAAO,YAAY,EAAE,CAAA;QACzB,CAAC;QAED,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE,CAAC;YACnC,OAAO,IAAI,YAAY,GAAG,CAAA;QAC9B,CAAC;QAED,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;YACtD,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,GAAG,YAAY,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,MAAsB;QACpC,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM,CAC9D,CAAA;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,MAAsB;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAmB;QAC9B,IAAI,IAAI,GAAG,MAAM,CAAC,IAAI,CAAA;QACtB,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YACd,OAAO,SAAS,CAAA;QACpB,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,GAAG,GAAG,CAAA;QACrC,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS;YAC9B,MAAM,CAAC,KAAK,KAAK,IAAI;YACrB,MAAM,CAAC,KAAK,KAAK,SAAS,EAC5B,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,GAAG,MAAM,CAAC,KAAK,GAAG,GAAG,CAAA;QAC7D,CAAC;aAAM,IACH,MAAM,CAAC,SAAS,KAAK,IAAI;YACzB,MAAM,CAAC,SAAS,KAAK,SAAS,EAChC,CAAC;YACC,IAAI,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,GAAG,GAAG,CAAA;QACxC,CAAC;QAED,IAAI,MAAM,CAAC,OAAO;YAAE,IAAI,IAAI,QAAQ,CAAA;QAEpC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,sBAAsB;QAClB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;;;OAIG;IACH,qBAAqB;QACjB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,QAAwB,EACxB,YAAiB,EACjB,WAAmB,EACnB,SAAiB;QAEjB,MAAM,YAAY,GAAG,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CACjD,CAAC,GAAG,EAAE,eAAe,EAAE,EAAE;YACrB,IAAI,KAAU,CAAA;YACd,IACI,eAAe,CAAC,kBAAkB,KAAK,WAAW;gBAClD,YAAY,EACd,CAAC;gBACC,+FAA+F;gBAC/F,sCAAsC;gBACtC,KAAK,GAAG,YAAY,GAAG,SAAS,GAAG,WAAW,GAAG,CAAC,CAAA;gBAClD,8DAA8D;gBAC9D,yDAAyD;YAC7D,CAAC;YAED,IAAI,CAAC,KAAK;gBAAE,OAAO,GAAG,CAAA;YACtB,OAAO,QAAQ,CAAC,SAAS,CACrB,GAAG,EACH,eAAe,CAAC,cAAc,CAAC,KAAK,CAAC,CACxC,CAAA;QACL,CAAC,EACD,EAAmB,CACtB,CAAA;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,CAAA;IAC1E,CAAC;IAED;;;OAGG;IACH,kBAAkB,CACd,YAA2B,EAC3B,eAAiC;QAEjC,OAAO,eAAe,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;YAC7C,MAAM,WAAW,GAAG,YAAY,CAAC,IAAI,CACjC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY,CAChD,CAAA;YACD,IAAI,CAAC,WAAW;gBAAE,OAAO,KAAK,CAAA,CAAC,4DAA4D;YAE3F,MAAM,eAAe,GACjB,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,YAAY;gBAChD,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACvD,WAAW,CAAC,MAAM,KAAK,cAAc,CAAC,MAAM;gBAC5C,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,KAAK,KAAK,cAAc,CAAC,KAAK;gBAC1C,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,KAAK,WAAW,CAAC,OAAO;gBAC7D,WAAW,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;gBAClD,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU;gBACpD,WAAW,CAAC,aAAa,KAAK,cAAc,CAAC,aAAa;gBAC1D,WAAW,CAAC,YAAY,KAAK,cAAc,CAAC,YAAY;gBACxD,WAAW,CAAC,QAAQ;oBAChB,IAAI,CAAC,iBAAiB,CAAC,cAAc,CAAC;gBAC1C,CAAC,WAAW,CAAC,IAAI;oBACb,cAAc,CAAC,IAAI;oBACnB,CAAC,QAAQ,CAAC,aAAa,CACnB,WAAW,CAAC,IAAI,EAChB,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAC7C,CAAC;gBACN,CAAC,cAAc,CAAC,kBAAkB,KAAK,MAAM;oBACzC,WAAW,CAAC,WAAW,KAAK,cAAc,CAAC,WAAW,CAAC,CAAA;YAE/D,gBAAgB;YAChB,yBAAyB;YACzB,qEAAqE;YACrE,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,4BAA4B;YAC5B,8CAA8C;YAC9C,QAAQ;YACR,mBAAmB;YACnB,qBAAqB;YACrB,8BAA8B;YAC9B,iCAAiC;YACjC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,qEAAqE;YACrE,mBAAmB;YACnB,sBAAsB;YACtB,iDAAiD;YACjD,kCAAkC;YAClC,QAAQ;YACR,mBAAmB;YACnB,wBAAwB;YACxB,iCAAiC;YACjC,oCAAoC;YACpC,QAAQ;YACR,mBAAmB;YACnB,yBAAyB;YACzB,kCAAkC;YAClC,qCAAqC;YACrC,QAAQ;YACR,mBAAmB;YACnB,4BAA4B;YAC5B,qCAAqC;YACrC,wCAAwC;YACxC,QAAQ;YACR,mBAAmB;YACnB,2BAA2B;YAC3B,oCAAoC;YACpC,uCAAuC;YACvC,QAAQ;YACR,mBAAmB;YACnB,uBAAuB;YACvB,gCAAgC;YAChC,kDAAkD;YAClD,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,8BAA8B;YAC9B,qCAAqC;YACrC,uCAAuC;YACvC,oCAAoC;YACpC,8DAA8D;YAC9D,iBAAiB;YACjB,QAAQ;YACR,mBAAmB;YACnB,0BAA0B;YAC1B,mCAAmC;YACnC,sCAAsC;YACtC,QAAQ;YACR,IAAI;YAEJ,OAAO,eAAe,CAAA;QAC1B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,uBAAuB;QACnB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,yBAAyB;QACrB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B;QACzB,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,aAAqB,EAAE,KAAa;QAChD,4BAA4B;QAC5B,OAAO,GAAG,CAAA;QACV,8BAA8B;IAClC,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,wBAAwB;QAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CACzB,EAAE,EACF;gBACI,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAC3B,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;aAClC,EACD,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAC3B,CAAA;YAED,IAAI,CAAC,MAAM,CAAC,YAAY,CACpB,OAAO,EACP,CAAC,EAAO,EAAE,EAAE;gBACR,MAAM,kBAAkB,GAAG,EAAE,CAAA;gBAE7B,yFAAyF;gBACzF,+DAA+D;gBAC/D,kBAAkB,CAAC,UAAU,CACzB,0BAA0B,EAC1B,EAAE,EACF,CAAC,MAAW,EAAE,EAAE;oBACZ,EAAE,CAAC,kBAAkB,CAAC,CAAA;gBAC1B,CAAC,EACD,CAAC,KAAU,EAAE,EAAE;oBACX,IAAI,CAAC,KAAK,CAAC,CAAA;gBACf,CAAC,CACJ,CAAA;YACL,CAAC,EACD,CAAC,KAAU,EAAE,EAAE;gBACX,IAAI,CAAC,KAAK,CAAC,CAAA;YACf,CAAC,CACJ,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GACR,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,6BAA6B,CAAC,CAAA;YACjE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACxB,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,8BAA8B,CACpC,cAAc,EACd,6BAA6B,CAChC,CAAA;QACL,CAAC;IACL,CAAC;CACJ", "file": "ReactNativeDriver.js", "sourcesContent": ["import { Driver } from \"../Driver\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { ColumnMetadata } from \"../../metadata/ColumnMetadata\"\nimport { DateUtils } from \"../../util/DateUtils\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { RdbmsSchemaBuilder } from \"../../schema-builder/RdbmsSchemaBuilder\"\nimport { CteCapabilities } from \"../types/CteCapabilities\"\nimport { MappedColumnTypes } from \"../types/MappedColumnTypes\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataTypeDefaults } from \"../types/DataTypeDefaults\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { ApplyValueTransformers } from \"../../util/ApplyValueTransformers\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { DriverPackageNotInstalledError } from \"../../error\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { UpsertType } from \"../types/UpsertType\"\nimport { ReactNativeConnectionOptions } from \"./ReactNativeConnectionOptions\"\nimport { ReactNativeQueryRunner } from \"./ReactNativeQueryRunner\"\n\ntype DatabasesMap = Record<\n    string,\n    {\n        attachFilepathAbsolute: string\n        attachFilepathRelative: string\n        attachHandle: string\n    }\n>\n\n/**\n * Organizes communication with sqlite DBMS.\n */\nexport class ReactNativeDriver implements Driver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection used by driver.\n     */\n    connection: DataSource\n\n    /**\n     * Sqlite has a single QueryRunner because it works on a single database connection.\n     */\n    queryRunner?: QueryRunner\n\n    /**\n     * Real database connection with sqlite database.\n     */\n    databaseConnection: any\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: ReactNativeConnectionOptions\n\n    /**\n     * Master database used to perform all write queries.\n     */\n    database?: string\n\n    /**\n     * Indicates if replication is enabled.\n     */\n    isReplicated: boolean = false\n\n    /**\n     * SQLite underlying library.\n     */\n    sqlite: any\n\n    /**\n     * Indicates if tree tables are supported by this driver.\n     */\n    treeSupport = true\n\n    /**\n     * Represent transaction support by this driver\n     */\n    transactionSupport: \"simple\" | \"nested\" | \"none\" = \"nested\"\n\n    /**\n     * Gets list of supported column data types by a driver.\n     *\n     * @see https://www.tutorialspoint.com/sqlite/sqlite_data_types.htm\n     * @see https://sqlite.org/datatype3.html\n     */\n    supportedDataTypes: ColumnType[] = [\n        \"int\",\n        \"integer\",\n        \"tinyint\",\n        \"smallint\",\n        \"mediumint\",\n        \"bigint\",\n        \"unsigned big int\",\n        \"int2\",\n        \"int8\",\n        \"integer\",\n        \"character\",\n        \"varchar\",\n        \"varying character\",\n        \"nchar\",\n        \"native character\",\n        \"nvarchar\",\n        \"text\",\n        \"clob\",\n        \"text\",\n        \"blob\",\n        \"real\",\n        \"double\",\n        \"double precision\",\n        \"float\",\n        \"real\",\n        \"numeric\",\n        \"decimal\",\n        \"boolean\",\n        \"date\",\n        \"time\",\n        \"datetime\",\n    ]\n\n    /**\n     * Returns type of upsert supported by driver if any\n     */\n    supportedUpsertTypes: UpsertType[] = [\"on-conflict-do-update\"]\n\n    /**\n     * Gets list of column data types that support length by a driver.\n     */\n    withLengthColumnTypes: ColumnType[] = [\n        \"character\",\n        \"varchar\",\n        \"varying character\",\n        \"nchar\",\n        \"native character\",\n        \"nvarchar\",\n        \"text\",\n        \"blob\",\n        \"clob\",\n    ]\n\n    /**\n     * Gets list of spatial column data types.\n     */\n    spatialTypes: ColumnType[] = []\n\n    /**\n     * Gets list of column data types that support precision by a driver.\n     */\n    withPrecisionColumnTypes: ColumnType[] = [\n        \"real\",\n        \"double\",\n        \"double precision\",\n        \"float\",\n        \"real\",\n        \"numeric\",\n        \"decimal\",\n        \"date\",\n        \"time\",\n        \"datetime\",\n    ]\n\n    /**\n     * Gets list of column data types that support scale by a driver.\n     */\n    withScaleColumnTypes: ColumnType[] = [\n        \"real\",\n        \"double\",\n        \"double precision\",\n        \"float\",\n        \"real\",\n        \"numeric\",\n        \"decimal\",\n    ]\n\n    /**\n     * Orm has special columns and we need to know what database column types should be for those types.\n     * Column types are driver dependant.\n     */\n    mappedDataTypes: MappedColumnTypes = {\n        createDate: \"datetime\",\n        createDateDefault: \"datetime('now')\",\n        updateDate: \"datetime\",\n        updateDateDefault: \"datetime('now')\",\n        deleteDate: \"datetime\",\n        deleteDateNullable: true,\n        version: \"integer\",\n        treeLevel: \"integer\",\n        migrationId: \"integer\",\n        migrationName: \"varchar\",\n        migrationTimestamp: \"bigint\",\n        cacheId: \"int\",\n        cacheIdentifier: \"varchar\",\n        cacheTime: \"bigint\",\n        cacheDuration: \"int\",\n        cacheQuery: \"text\",\n        cacheResult: \"text\",\n        metadataType: \"varchar\",\n        metadataDatabase: \"varchar\",\n        metadataSchema: \"varchar\",\n        metadataTable: \"varchar\",\n        metadataName: \"varchar\",\n        metadataValue: \"text\",\n    }\n\n    /**\n     * Default values of length, precision and scale depends on column data type.\n     * Used in the cases when length/precision/scale is not specified by user.\n     */\n    dataTypeDefaults: DataTypeDefaults\n\n    /**\n     * No documentation specifying a maximum length for identifiers could be found\n     * for SQLite.\n     */\n    maxAliasLength?: number\n\n    cteCapabilities: CteCapabilities = {\n        enabled: true,\n        requiresRecursiveHint: true,\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Any attached databases (excepting default 'main')\n     */\n    attachedDatabases: DatabasesMap = {}\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        this.connection = connection\n        this.options = connection.options as ReactNativeConnectionOptions\n        // this.database = DriverUtils.buildDriverOptions(this.options).database\n        this.database = this.options.database\n\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Abstract\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner)\n            this.queryRunner = new ReactNativeQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs connection to the database.\n     */\n    async connect(): Promise<void> {\n        this.databaseConnection = await this.createDatabaseConnection()\n    }\n\n    /**\n     * Makes any action after connection (e.g. create extensions in Postgres driver).\n     */\n    afterConnect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.queryRunner = undefined\n            this.databaseConnection.close(ok, fail)\n        })\n    }\n\n    hasAttachedDatabases(): boolean {\n        return !!Object.keys(this.attachedDatabases).length\n    }\n\n    getAttachedDatabaseHandleByRelativePath(path: string): string | undefined {\n        return this.attachedDatabases?.[path]?.attachHandle\n    }\n\n    getAttachedDatabasePathRelativeByHandle(\n        handle: string,\n    ): string | undefined {\n        return Object.values(this.attachedDatabases).find(\n            ({ attachHandle }) => handle === attachHandle,\n        )?.attachFilepathRelative\n    }\n\n    /**\n     * Creates a schema builder used to build and sync a schema.\n     */\n    createSchemaBuilder() {\n        return new RdbmsSchemaBuilder(this.connection)\n    }\n\n    /**\n     * Prepares given value to a value to be persisted, based on its column type and metadata.\n     */\n    preparePersistentValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformTo(\n                columnMetadata.transformer,\n                value,\n            )\n\n        if (value === null || value === undefined) return value\n\n        if (\n            columnMetadata.type === Boolean ||\n            columnMetadata.type === \"boolean\"\n        ) {\n            return value === true ? 1 : 0\n        } else if (columnMetadata.type === \"date\") {\n            return DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            return DateUtils.mixedDateToTimeString(value)\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date\n        ) {\n            // to string conversation needs because SQLite stores date as integer number, when date came as Object\n            // TODO: think about `toUTC` conversion\n            return DateUtils.mixedDateToUtcDatetimeString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            return DateUtils.simpleArrayToString(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            return DateUtils.simpleJsonToString(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            return DateUtils.simpleEnumToString(value)\n        }\n\n        return value\n    }\n\n    /**\n     * Prepares given value to a value to be hydrated, based on its column type or metadata.\n     */\n    prepareHydratedValue(value: any, columnMetadata: ColumnMetadata): any {\n        if (value === null || value === undefined)\n            return columnMetadata.transformer\n                ? ApplyValueTransformers.transformFrom(\n                      columnMetadata.transformer,\n                      value,\n                  )\n                : value\n\n        if (\n            columnMetadata.type === Boolean ||\n            columnMetadata.type === \"boolean\"\n        ) {\n            value = value ? true : false\n        } else if (\n            columnMetadata.type === \"datetime\" ||\n            columnMetadata.type === Date\n        ) {\n            /**\n             * Fix date conversion issue\n             *\n             * If the format of the date string is \"2018-03-14 02:33:33.906\", Safari (and iOS WKWebView) will convert it to an invalid date object.\n             * We need to modify the date string to \"2018-03-14T02:33:33.906Z\" and Safari will convert it correctly.\n             *\n             * ISO 8601\n             * https://www.w3.org/TR/NOTE-datetime\n             */\n            if (value && typeof value === \"string\") {\n                // There are various valid time string formats a sqlite time string might have:\n                // https://www.sqlite.org/lang_datefunc.html\n                // There are two separate fixes we may need to do:\n                //   1) Add 'T' separator if space is used instead\n                //   2) Add 'Z' UTC suffix if no timezone or offset specified\n\n                if (/^\\d\\d\\d\\d-\\d\\d-\\d\\d \\d\\d:\\d\\d/.test(value)) {\n                    value = value.replace(\" \", \"T\")\n                }\n                if (\n                    /^\\d\\d\\d\\d-\\d\\d-\\d\\dT\\d\\d:\\d\\d(:\\d\\d(\\.\\d\\d\\d)?)?$/.test(\n                        value,\n                    )\n                ) {\n                    value += \"Z\"\n                }\n            }\n\n            value = DateUtils.normalizeHydratedDate(value)\n        } else if (columnMetadata.type === \"date\") {\n            value = DateUtils.mixedDateToDateString(value)\n        } else if (columnMetadata.type === \"time\") {\n            value = DateUtils.mixedTimeToString(value)\n        } else if (columnMetadata.type === \"simple-array\") {\n            value = DateUtils.stringToSimpleArray(value)\n        } else if (columnMetadata.type === \"simple-json\") {\n            value = DateUtils.stringToSimpleJson(value)\n        } else if (columnMetadata.type === \"simple-enum\") {\n            value = DateUtils.stringToSimpleEnum(value, columnMetadata)\n        } else if (columnMetadata.type === Number) {\n            // convert to number if number\n            value = !isNaN(+value) ? parseInt(value) : value\n        }\n\n        if (columnMetadata.transformer)\n            value = ApplyValueTransformers.transformFrom(\n                columnMetadata.transformer,\n                value,\n            )\n\n        return value\n    }\n\n    /**\n     * Replaces parameters in the given sql with special escaping character\n     * and an array of parameter names to be passed to a query.\n     */\n    escapeQueryWithParameters(\n        sql: string,\n        parameters: ObjectLiteral,\n        nativeParameters: ObjectLiteral,\n    ): [string, any[]] {\n        const escapedParameters: any[] = Object.keys(nativeParameters).map(\n            (key) => {\n                // Mapping boolean values to their numeric representation\n                if (typeof nativeParameters[key] === \"boolean\") {\n                    return nativeParameters[key] === true ? 1 : 0\n                }\n\n                if (nativeParameters[key] instanceof Date) {\n                    return DateUtils.mixedDateToUtcDatetimeString(\n                        nativeParameters[key],\n                    )\n                }\n\n                return nativeParameters[key]\n            },\n        )\n\n        if (!parameters || !Object.keys(parameters).length)\n            return [sql, escapedParameters]\n\n        sql = sql.replace(\n            /:(\\.\\.\\.)?([A-Za-z0-9_.]+)/g,\n            (full, isArray: string, key: string): string => {\n                if (!parameters.hasOwnProperty(key)) {\n                    return full\n                }\n\n                const value: any = parameters[key]\n\n                if (isArray) {\n                    return value\n                        .map((v: any) => {\n                            escapedParameters.push(v)\n                            return this.createParameter(\n                                key,\n                                escapedParameters.length - 1,\n                            )\n                        })\n                        .join(\", \")\n                }\n\n                if (typeof value === \"function\") {\n                    return value()\n                } else if (typeof value === \"number\") {\n                    return String(value)\n                }\n\n                // Sqlite does not have a boolean data type so we have to transform\n                // it to 1 or 0\n                if (typeof value === \"boolean\") {\n                    escapedParameters.push(+value)\n                    return this.createParameter(\n                        key,\n                        escapedParameters.length - 1,\n                    )\n                }\n\n                if (value instanceof Date) {\n                    escapedParameters.push(\n                        DateUtils.mixedDateToUtcDatetimeString(value),\n                    )\n                    return this.createParameter(\n                        key,\n                        escapedParameters.length - 1,\n                    )\n                }\n\n                escapedParameters.push(value)\n                return this.createParameter(key, escapedParameters.length - 1)\n            },\n        ) // todo: make replace only in value statements, otherwise problems\n        return [sql, escapedParameters]\n    }\n\n    /**\n     * Escapes a column name.\n     */\n    escape(columnName: string): string {\n        return '\"' + columnName + '\"'\n    }\n\n    /**\n     * Build full table name with database name, schema name and table name.\n     * E.g. myDB.mySchema.myTable\n     *\n     * Returns only simple table name because all inherited drivers does not supports schema and database.\n     */\n    buildTableName(\n        tableName: string,\n        schema?: string,\n        database?: string,\n    ): string {\n        return tableName\n    }\n\n    /**\n     * Parse a target table name or other types and return a normalized table definition.\n     */\n    parseTableName(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): { database?: string; schema?: string; tableName: string } {\n        const driverDatabase = this.database\n        const driverSchema = undefined\n\n        if (InstanceChecker.isTable(target) || InstanceChecker.isView(target)) {\n            const parsed = this.parseTableName(\n                target.schema\n                    ? `\"${target.schema}\".\"${target.name}\"`\n                    : target.name,\n            )\n\n            return {\n                database: target.database || parsed.database || driverDatabase,\n                schema: target.schema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isTableForeignKey(target)) {\n            const parsed = this.parseTableName(target.referencedTableName)\n\n            return {\n                database:\n                    target.referencedDatabase ||\n                    parsed.database ||\n                    driverDatabase,\n                schema:\n                    target.referencedSchema || parsed.schema || driverSchema,\n                tableName: parsed.tableName,\n            }\n        }\n\n        if (InstanceChecker.isEntityMetadata(target)) {\n            // EntityMetadata tableName is never a path\n\n            return {\n                database: target.database || driverDatabase,\n                schema: target.schema || driverSchema,\n                tableName: target.tableName,\n            }\n        }\n\n        const parts = target.split(\".\")\n\n        if (parts.length === 3) {\n            return {\n                database: parts[0] || driverDatabase,\n                schema: parts[1] || driverSchema,\n                tableName: parts[2],\n            }\n        } else if (parts.length === 2) {\n            const database =\n                this.getAttachedDatabasePathRelativeByHandle(parts[0]) ??\n                driverDatabase\n            return {\n                database: database,\n                schema: parts[0],\n                tableName: parts[1],\n            }\n        } else {\n            return {\n                database: driverDatabase,\n                schema: driverSchema,\n                tableName: target,\n            }\n        }\n    }\n\n    /**\n     * Creates a database type from a given column metadata.\n     */\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if (column.type === Number || column.type === \"int\") {\n            return \"integer\"\n        } else if (column.type === String) {\n            return \"varchar\"\n        } else if (column.type === Date) {\n            return \"datetime\"\n        } else if (column.type === Boolean) {\n            return \"boolean\"\n        } else if (column.type === \"uuid\") {\n            return \"varchar\"\n        } else if (column.type === \"simple-array\") {\n            return \"text\"\n        } else if (column.type === \"simple-json\") {\n            return \"text\"\n        } else if (column.type === \"simple-enum\") {\n            return \"varchar\"\n        } else {\n            return (column.type as string) || \"\"\n        }\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    normalizeDefault(columnMetadata: ColumnMetadata): string | undefined {\n        const defaultValue = columnMetadata.default\n\n        if (typeof defaultValue === \"number\") {\n            return \"\" + defaultValue\n        }\n\n        if (typeof defaultValue === \"boolean\") {\n            return defaultValue ? \"1\" : \"0\"\n        }\n\n        if (typeof defaultValue === \"function\") {\n            return defaultValue()\n        }\n\n        if (typeof defaultValue === \"string\") {\n            return `'${defaultValue}'`\n        }\n\n        if (defaultValue === null || defaultValue === undefined) {\n            return undefined\n        }\n\n        return `${defaultValue}`\n    }\n\n    /**\n     * Normalizes \"isUnique\" value of the column.\n     */\n    normalizeIsUnique(column: ColumnMetadata): boolean {\n        return column.entityMetadata.uniques.some(\n            (uq) => uq.columns.length === 1 && uq.columns[0] === column,\n        )\n    }\n\n    /**\n     * Calculates column length taking into account the default length values.\n     */\n    getColumnLength(column: ColumnMetadata): string {\n        return column.length ? column.length.toString() : \"\"\n    }\n\n    /**\n     * Normalizes \"default\" value of the column.\n     */\n    createFullType(column: TableColumn): string {\n        let type = column.type\n        if (column.enum) {\n            return \"varchar\"\n        }\n        if (column.length) {\n            type += \"(\" + column.length + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined &&\n            column.scale !== null &&\n            column.scale !== undefined\n        ) {\n            type += \"(\" + column.precision + \",\" + column.scale + \")\"\n        } else if (\n            column.precision !== null &&\n            column.precision !== undefined\n        ) {\n            type += \"(\" + column.precision + \")\"\n        }\n\n        if (column.isArray) type += \" array\"\n\n        return type\n    }\n\n    /**\n     * Obtains a new database connection to a master server.\n     * Used for replication.\n     * If replication is not setup then returns default connection's database connection.\n     */\n    obtainMasterConnection(): Promise<any> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Obtains a new database connection to a slave server.\n     * Used for replication.\n     * If replication is not setup then returns master (default) connection's database connection.\n     */\n    obtainSlaveConnection(): Promise<any> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Creates generated map of values generated or returned by database after INSERT query.\n     */\n    createGeneratedMap(\n        metadata: EntityMetadata,\n        insertResult: any,\n        entityIndex: number,\n        entityNum: number,\n    ) {\n        const generatedMap = metadata.generatedColumns.reduce(\n            (map, generatedColumn) => {\n                let value: any\n                if (\n                    generatedColumn.generationStrategy === \"increment\" &&\n                    insertResult\n                ) {\n                    // NOTE: When INSERT statement is successfully completed, the last inserted row ID is returned.\n                    // see also: SqliteQueryRunner.query()\n                    value = insertResult - entityNum + entityIndex + 1\n                    // } else if (generatedColumn.generationStrategy === \"uuid\") {\n                    //     value = insertValue[generatedColumn.databaseName];\n                }\n\n                if (!value) return map\n                return OrmUtils.mergeDeep(\n                    map,\n                    generatedColumn.createValueMap(value),\n                )\n            },\n            {} as ObjectLiteral,\n        )\n\n        return Object.keys(generatedMap).length > 0 ? generatedMap : undefined\n    }\n\n    /**\n     * Differentiate columns of this table and columns from the given column metadatas columns\n     * and returns only changed.\n     */\n    findChangedColumns(\n        tableColumns: TableColumn[],\n        columnMetadatas: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return columnMetadatas.filter((columnMetadata) => {\n            const tableColumn = tableColumns.find(\n                (c) => c.name === columnMetadata.databaseName,\n            )\n            if (!tableColumn) return false // we don't need new columns, we only need exist and changed\n\n            const isColumnChanged =\n                tableColumn.name !== columnMetadata.databaseName ||\n                tableColumn.type !== this.normalizeType(columnMetadata) ||\n                tableColumn.length !== columnMetadata.length ||\n                tableColumn.precision !== columnMetadata.precision ||\n                tableColumn.scale !== columnMetadata.scale ||\n                this.normalizeDefault(columnMetadata) !== tableColumn.default ||\n                tableColumn.isPrimary !== columnMetadata.isPrimary ||\n                tableColumn.isNullable !== columnMetadata.isNullable ||\n                tableColumn.generatedType !== columnMetadata.generatedType ||\n                tableColumn.asExpression !== columnMetadata.asExpression ||\n                tableColumn.isUnique !==\n                    this.normalizeIsUnique(columnMetadata) ||\n                (tableColumn.enum &&\n                    columnMetadata.enum &&\n                    !OrmUtils.isArraysEqual(\n                        tableColumn.enum,\n                        columnMetadata.enum.map((val) => val + \"\"),\n                    )) ||\n                (columnMetadata.generationStrategy !== \"uuid\" &&\n                    tableColumn.isGenerated !== columnMetadata.isGenerated)\n\n            // DEBUG SECTION\n            // if (isColumnChanged) {\n            //     console.log(\"table:\", columnMetadata.entityMetadata.tableName)\n            //     console.log(\n            //         \"name:\",\n            //         tableColumn.name,\n            //         columnMetadata.databaseName,\n            //     )\n            //     console.log(\n            //         \"type:\",\n            //         tableColumn.type,\n            //         this.normalizeType(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"length:\",\n            //         tableColumn.length,\n            //         columnMetadata.length,\n            //     )\n            //     console.log(\n            //         \"precision:\",\n            //         tableColumn.precision,\n            //         columnMetadata.precision,\n            //     )\n            //     console.log(\"scale:\", tableColumn.scale, columnMetadata.scale)\n            //     console.log(\n            //         \"default:\",\n            //         this.normalizeDefault(columnMetadata),\n            //         columnMetadata.default,\n            //     )\n            //     console.log(\n            //         \"isPrimary:\",\n            //         tableColumn.isPrimary,\n            //         columnMetadata.isPrimary,\n            //     )\n            //     console.log(\n            //         \"isNullable:\",\n            //         tableColumn.isNullable,\n            //         columnMetadata.isNullable,\n            //     )\n            //     console.log(\n            //         \"generatedType:\",\n            //         tableColumn.generatedType,\n            //         columnMetadata.generatedType,\n            //     )\n            //     console.log(\n            //         \"asExpression:\",\n            //         tableColumn.asExpression,\n            //         columnMetadata.asExpression,\n            //     )\n            //     console.log(\n            //         \"isUnique:\",\n            //         tableColumn.isUnique,\n            //         this.normalizeIsUnique(columnMetadata),\n            //     )\n            //     console.log(\n            //         \"enum:\",\n            //         tableColumn.enum &&\n            //             columnMetadata.enum &&\n            //             !OrmUtils.isArraysEqual(\n            //                 tableColumn.enum,\n            //                 columnMetadata.enum.map((val) => val + \"\"),\n            //             ),\n            //     )\n            //     console.log(\n            //         \"isGenerated:\",\n            //         tableColumn.isGenerated,\n            //         columnMetadata.isGenerated,\n            //     )\n            // }\n\n            return isColumnChanged\n        })\n    }\n\n    /**\n     * Returns true if driver supports RETURNING / OUTPUT statement.\n     */\n    isReturningSqlSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports uuid values generation on its own.\n     */\n    isUUIDGenerationSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Returns true if driver supports fulltext indices.\n     */\n    isFullTextColumnTypeSupported(): boolean {\n        return false\n    }\n\n    /**\n     * Creates an escaped parameter.\n     */\n    createParameter(parameterName: string, index: number): string {\n        // return \"$\" + (index + 1);\n        return \"?\"\n        // return \"$\" + parameterName;\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected createDatabaseConnection() {\n        return new Promise<void>((ok, fail) => {\n            const options = Object.assign(\n                {},\n                {\n                    name: this.options.database,\n                    location: this.options.location,\n                },\n                this.options.extra || {},\n            )\n\n            this.sqlite.openDatabase(\n                options,\n                (db: any) => {\n                    const databaseConnection = db\n\n                    // we need to enable foreign keys in sqlite to make sure all foreign key related features\n                    // working properly. this also makes onDelete work with sqlite.\n                    databaseConnection.executeSql(\n                        `PRAGMA foreign_keys = ON`,\n                        [],\n                        (result: any) => {\n                            ok(databaseConnection)\n                        },\n                        (error: any) => {\n                            fail(error)\n                        },\n                    )\n                },\n                (error: any) => {\n                    fail(error)\n                },\n            )\n        })\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const sqlite =\n                this.options.driver || require(\"react-native-sqlite-storage\")\n            this.sqlite = sqlite\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\n                \"React-Native\",\n                \"react-native-sqlite-storage\",\n            )\n        }\n    }\n}\n"], "sourceRoot": "../.."}