export declare const MOLECYLER_KEY = "decorator:cool:rpc";
export type MethodTypes = 'add' | 'delete' | 'update' | 'page' | 'info' | 'list';
export interface FieldEq {
    column: string;
    requestParam: string;
}
export interface LeftJoinOp {
    entity: any;
    alias: string;
    condition: string;
}
export interface CurdOption {
    prefix?: string;
    method: MethodTypes[];
    pageQueryOp?: QueryOp;
    listQueryOp?: QueryOp;
    insertParam?: Function;
    infoIgnoreProperty?: string[];
    entity: {
        entityKey?: any;
        connectionName?: string;
    } | any;
}
export interface QueryOp {
    keyWordLikeFields?: string[];
    where?: Function;
    select?: string[];
    fieldEq?: string[] | FieldEq[];
    addOrderBy?: {};
    leftJoin?: LeftJoinOp[];
}
/**
 * moleculer 微服务配置
 * @param option
 * @returns
 */
export declare function CoolRpcService(option?: CurdOption): ClassDecorator;
