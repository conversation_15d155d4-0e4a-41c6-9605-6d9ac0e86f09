{"version": 3, "sources": ["../browser/src/subscriber/BroadcasterResult.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAA9B;QACI;;WAEG;QACH,UAAK,GAAW,CAAC,CAAA;QAEjB;;WAEG;QACH,aAAQ,GAAmB,EAAE,CAAA;IAYjC,CAAC;IAVG;;OAEG;IACH,KAAK,CAAC,IAAI;QACN,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACpC,CAAC;QAED,OAAO,IAAI,CAAA;IACf,CAAC;CACJ", "file": "BroadcasterResult.js", "sourcesContent": ["/**\n * Broadcaster execution result - promises executed by operations and number of executed listeners and subscribers.\n */\nexport class BroadcasterResult {\n    /**\n     * Number of executed listeners and subscribers.\n     */\n    count: number = 0\n\n    /**\n     * Promises returned by listeners and subscribers which needs to be awaited.\n     */\n    promises: Promise<any>[] = []\n\n    /**\n     * Wait for all promises to settle\n     */\n    async wait(): Promise<BroadcasterResult> {\n        if (this.promises.length > 0) {\n            await Promise.all(this.promises)\n        }\n\n        return this\n    }\n}\n"], "sourceRoot": ".."}