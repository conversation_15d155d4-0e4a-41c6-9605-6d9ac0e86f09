"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.saveYaml = exports.parse = void 0;
const YAML = require("js-yaml");
const fs = require("fs");
const mkdirp = require("mkdirp");
const path = require("path");
const loadYaml = (contents, options) => {
    let data;
    let error;
    try {
        data = YAML.load(contents.toString(), options || {});
    }
    catch (exception) {
        error = exception;
    }
    return { data, error };
};
const parse = (filePath, contents) => {
    // Auto-parse JSON
    if (filePath.endsWith('.json')) {
        return JSON.parse(contents);
    }
    else if (filePath.endsWith('.yml') || filePath.endsWith('.yaml')) {
        const options = {
            filename: filePath,
        };
        const result = loadYaml(contents.toString(), options);
        if (result.error) {
            throw result.error;
        }
        return result.data;
    }
    throw new Error('content could not be string');
};
exports.parse = parse;
const saveYaml = (filePath, target) => {
    const text = YAML.dump(target, {
        skipInvalid: true,
    });
    try {
        mkdirp.sync(path.dirname(filePath));
        fs.writeFileSync(filePath, text);
    }
    catch (err) {
        throw new Error(`generate ${filePath} error, ${err.message}`);
    }
};
exports.saveYaml = saveYaml;
//# sourceMappingURL=parse.js.map