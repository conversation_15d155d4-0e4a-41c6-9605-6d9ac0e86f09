{"version": 3, "file": "no-throw-literal.js", "sourceRoot": "", "sources": ["../../src/rules/no-throw-literal.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,+CAAiC;AAEjC,8CAAgC;AAWhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,0CAA0C;YACvD,WAAW,EAAE,QAAQ;YACrB,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;oBACD,oBAAoB,EAAE;wBACpB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,QAAQ,EAAE;YACR,MAAM,EAAE,wCAAwC;YAChD,KAAK,EAAE,yBAAyB;SACjC;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,IAAI;YACtB,oBAAoB,EAAE,IAAI;SAC3B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACvC,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QAEzC,SAAS,WAAW,CAAC,IAAa;;YAChC,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE;gBACzB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;gBAClB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;aACtC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,KAAK,CAAC;aACd;YAED,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,OAAO,EAAE;gBAChC,MAAM,YAAY,GAAG,MAAA,MAAM,CAAC,eAAe,EAAE,mCAAI,EAAE,CAAC;gBACpD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;oBACtC,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,EAAE,CAAC;oBAC/C,IAAI,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,EAAE;wBAClD,OAAO,IAAI,CAAC;qBACb;iBACF;aACF;YAED,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBACpE,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,YAAY,CAAC,IAAwB,CAAC,EAAE;oBACrE,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;wBACzB,OAAO,IAAI,CAAC;qBACb;iBACF;aACF;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,kBAAkB,CAAC,IAAmB;YAC7C,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAC5C;gBACA,OAAO;aACR;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YAE/C,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE;gBACvC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;gBAC7C,OAAO;aACR;YAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;gBACxD,OAAO;aACR;YAED,IAAI,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;gBAChE,OAAO;aACR;YAED,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;gBACrB,OAAO;aACR;YAED,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;QAChD,CAAC;QAED,OAAO;YACL,cAAc,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACnC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}