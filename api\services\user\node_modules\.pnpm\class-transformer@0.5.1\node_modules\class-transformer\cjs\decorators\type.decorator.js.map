{"version": 3, "file": "type.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/type.decorator.ts"], "names": [], "mappings": ";;;AAAA,wCAAoD;AAGpD;;;;;GAKG;AACH,SAAgB,IAAI,CAClB,YAAmD,EACnD,UAAuB,EAAE;IAEzB,OAAO,UAAU,MAAW,EAAE,YAA6B;QACzD,MAAM,aAAa,GAAI,OAAe,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;QACxF,gCAAsB,CAAC,eAAe,CAAC;YACrC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAsB;YACpC,aAAa;YACb,YAAY;YACZ,OAAO;SACR,CAAC,CAAC;IACL,CAAC,CAAC;AACJ,CAAC;AAdD,oBAcC", "sourcesContent": ["import { defaultMetadataStorage } from '../storage';\nimport { TypeHelpOptions, TypeOptions } from '../interfaces';\n\n/**\n * Specifies a type of the property.\n * The given TypeFunction can return a constructor. A discriminator can be given in the options.\n *\n * Can be applied to properties only.\n */\nexport function Type(\n  typeFunction?: (type?: TypeHelpOptions) => Function,\n  options: TypeOptions = {}\n): PropertyDecorator {\n  return function (target: any, propertyName: string | Symbol): void {\n    const reflectedType = (Reflect as any).getMetadata('design:type', target, propertyName);\n    defaultMetadataStorage.addTypeMetadata({\n      target: target.constructor,\n      propertyName: propertyName as string,\n      reflectedType,\n      typeFunction,\n      options,\n    });\n  };\n}\n"]}