{"version": 3, "file": "wrapWord.js", "sourceRoot": "", "sources": ["../../src/wrapWord.ts"], "names": [], "mappings": ";;;;;;AAAA,4DAA+B;AAC/B,4DAAmC;AAEnC,MAAM,sBAAsB,GAAG,CAAC,KAAa,EAAE,IAAY,EAA0C,EAAE;IACrG,IAAI,OAAO,GAAG,IAAA,oBAAS,EAAC,KAAK,CAAC,CAAC;IAE/B,MAAM,MAAM,GAA4B,EAAE,CAAC;IAE3C,kCAAkC;IAClC,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,wBAAwB,CAAC,CAAC;IAE7I,GAAG;QACD,IAAI,KAAa,CAAC;QAElB,MAAM,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/B,IAAI,KAAK,EAAE;YACT,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEjB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEtC,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,CAAC;YAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,aAAa,CAAC;YAE5C,MAAM,CAAC,IAAI,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YAC/B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAE9B,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;SAChC;KACF,QAAQ,OAAO,CAAC,MAAM,EAAE;IAEzB,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAEK,MAAM,QAAQ,GAAG,CAAC,KAAa,EAAE,IAAY,EAAY,EAAE;IAChE,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE;QAC/D,MAAM,CAAC,IAAI,CAAC,IAAA,oBAAK,EAAC,KAAK,EAAE,UAAU,EAAE,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC;QAE3D,UAAU,IAAI,MAAM,GAAG,MAAM,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAXW,QAAA,QAAQ,YAWnB"}