"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Fields = exports.Queries = exports.RequestIP = exports.RequestPath = exports.Files = exports.File = exports.Headers = exports.Param = exports.Query = exports.Body = exports.Session = exports.createRequestParamDecorator = exports.RouteParamTypes = void 0;
const __1 = require("../");
var RouteParamTypes;
(function (RouteParamTypes) {
    RouteParamTypes["QUERY"] = "query";
    RouteParamTypes["BODY"] = "body";
    RouteParamTypes["PARAM"] = "param";
    RouteParamTypes["HEADERS"] = "headers";
    RouteParamTypes["SESSION"] = "session";
    RouteParamTypes["FILESTREAM"] = "file_stream";
    RouteParamTypes["FILESSTREAM"] = "files_stream";
    RouteParamTypes["NEXT"] = "next";
    RouteParamTypes["REQUEST_PATH"] = "request_path";
    RouteParamTypes["REQUEST_IP"] = "request_ip";
    RouteParamTypes["QUERIES"] = "queries";
    RouteParamTypes["FIELDS"] = "fields";
    RouteParamTypes["CUSTOM"] = "custom";
})(RouteParamTypes = exports.RouteParamTypes || (exports.RouteParamTypes = {}));
const createParamMapping = function (type) {
    return (propertyOrPipes, options = {}) => {
        let propertyData = propertyOrPipes;
        if (Array.isArray(propertyOrPipes) && options.pipes === undefined) {
            options.pipes = propertyOrPipes;
            propertyData = undefined;
        }
        return (0, __1.createCustomParamDecorator)(__1.WEB_ROUTER_PARAM_KEY, {
            type,
            propertyData,
        }, options);
    };
};
const createRequestParamDecorator = function (transform, pipesOrOptions) {
    pipesOrOptions = pipesOrOptions || {};
    if (Array.isArray(pipesOrOptions)) {
        pipesOrOptions = {
            pipes: pipesOrOptions,
        };
    }
    return createParamMapping(RouteParamTypes.CUSTOM)(transform, pipesOrOptions);
};
exports.createRequestParamDecorator = createRequestParamDecorator;
const Session = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.SESSION)(propertyOrPipes, { pipes });
exports.Session = Session;
const Body = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.BODY)(propertyOrPipes, { pipes });
exports.Body = Body;
const Query = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.QUERY)(propertyOrPipes, { pipes });
exports.Query = Query;
const Param = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.PARAM)(propertyOrPipes, { pipes });
exports.Param = Param;
const Headers = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.HEADERS)(propertyOrPipes, { pipes });
exports.Headers = Headers;
const File = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.FILESTREAM)(propertyOrPipes, { pipes });
exports.File = File;
const Files = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.FILESSTREAM)(propertyOrPipes, { pipes });
exports.Files = Files;
const RequestPath = (pipes) => createParamMapping(RouteParamTypes.REQUEST_PATH)(undefined, { pipes });
exports.RequestPath = RequestPath;
const RequestIP = (pipes) => createParamMapping(RouteParamTypes.REQUEST_IP)(undefined, { pipes });
exports.RequestIP = RequestIP;
const Queries = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.QUERIES)(propertyOrPipes, { pipes });
exports.Queries = Queries;
const Fields = (propertyOrPipes, pipes) => createParamMapping(RouteParamTypes.FIELDS)(propertyOrPipes, { pipes });
exports.Fields = Fields;
//# sourceMappingURL=paramMapping.js.map