import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceWalletRecordEntity } from '../../../entity/wallet/record';
import { FinanceWalletUserService } from '../../../service/wallet/user';
import { OrderInfoEntity } from '../../../../order/entity/info';

/**
 * 钱包记录
 */
@CoolController({
  api: ['page'],
  entity: FinanceWalletRecordEntity,
  service: FinanceWalletUserService,
  pageQueryOp: {
    select: ['a.*', 'b.status as orderStatus'],
    where: ctx => {
      const userId = ctx.user.id;
      return [['a.userId =:userId', { userId }]];
    },
    join: [
      {
        alias: 'b',
        entity: OrderInfoEntity,
        condition: 'a.objectId = b.id and a.objectType = 0',
      },
    ],
  },
})
export class AppFinanceWalletRecordController extends BaseController {}
