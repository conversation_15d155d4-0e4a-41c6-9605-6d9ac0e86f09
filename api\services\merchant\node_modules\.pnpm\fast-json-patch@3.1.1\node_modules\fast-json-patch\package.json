{"name": "fast-json-patch", "version": "3.1.1", "description": "Fast implementation of JSON-Patch (RFC-6902) with duplex (observe changes) capabilities", "homepage": "https://github.com/Starcounter-Jack/JSON-Patch", "keywords": ["json", "patch", "http", "rest"], "repository": {"type": "git", "url": "git://github.com/Starcounter-Jack/JSON-Patch.git"}, "bugs": {"url": "https://github.com/Starcounter-Jack/JSON-Patch/issues"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.starcounter.com/"}, "license": "MIT", "main": "index.js", "module": "index.mjs", "typings": "index.d.ts", "devDependencies": {"benchmark": "^2.1.4", "bluebird": "^3.5.5", "bluebird-retry": "^0.11.0", "chalk": "^2.4.2", "event-target-shim": "^5.0.1", "fast-deep-equal": "^2.0.1", "http-server": "^0.12.3", "jasmine": "^3.4.0", "request": "^2.88.0", "sauce-connect-launcher": "^1.2.7", "saucelabs": "^2.1.9", "selenium-webdriver": "^4.0.0-alpha.4", "typescript": "~3.5.2", "webpack": "^4.35.0", "webpack-cli": "^3.3.5"}, "scripts": {"tsc": "npm run tsc-common && npm run tsc-module", "tsc-common": "tsc", "tsc-module": "tsc --module esnext --moduleResolution node --outDir \"module/\" && npm run tsc-to-mjs", "tsc-to-mjs": "bash tsc-to-mjs.sh", "version": "npm run tsc && webpack && git add -A", "build": "npm run tsc && webpack", "serve": "http-server -p 5000 --silent", "tsc-watch": "tsc -w", "test": "npm run tsc && npm run test-core && npm run test-duplex && npm run test-commonjs && npm run test-webpack-import && npm run test-typings", "test-sauce": "npm run build && node test/Sauce/Runner.js", "test-commonjs": "jasmine test/spec/commonjs/requireSpec.js", "test-webpack-import": "webpack --env.NODE_ENV=test && jasmine test/spec/webpack/importSpec.build.js", "test-typings": "tsc test/spec/typings/typingsSpec.ts", "test-duplex": "node --experimental-modules jasmine-run.mjs test/**/*[sS]pec.mjs", "test-core": "node --experimental-modules jasmine-run.mjs 'test/spec/{jsonPatchTestsSpec,coreSpec,validateSpec}.mjs'", "bench": "npm run bench-core && npm run bench-duplex", "bench-core": "node test/spec/coreBenchmark.js", "bench-duplex": "node test/spec/coreBenchmark.js && node test/spec/duplexBenchmark.js"}}