{"version": 3, "sources": ["../browser/src/error/CustomRepositoryDoesNotHaveEntityError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,sCAAuC,SAAQ,YAAY;IACpE,YAAY,UAAe;QACvB,KAAK,CACD,qBACI,OAAO,UAAU,KAAK,UAAU;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IACjC,iCAAiC;YAC7B,uEAAuE,CAC9E,CAAA;IACL,CAAC;CACJ", "file": "CustomRepositoryDoesNotHaveEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if custom repositories that extend AbstractRepository classes does not have managed entity.\n */\nexport class CustomRepositoryDoesNotHaveEntityError extends TypeORMError {\n    constructor(repository: any) {\n        super(\n            `Custom repository ${\n                typeof repository === \"function\"\n                    ? repository.name\n                    : repository.constructor.name\n            } does not have managed entity. ` +\n                `Did you forget to specify entity for it @EntityRepository(MyEntity)? `,\n        )\n    }\n}\n"], "sourceRoot": ".."}