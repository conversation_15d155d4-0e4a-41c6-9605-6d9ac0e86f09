const { join } = require('path');
const { BootstrapStarter } = require('<%=specificStarterName %>');
const starter = new BootstrapStarter();
<% if (preloadFile) { %>const preload = require('./<%-preloadFile %>');<% }%>
module.exports = starter.start({
  appDir: __dirname,
  baseDir: join(__dirname, 'dist'),
  initializeMethodName: '<%=initializer%>',
  <% if (preloadFile) { %>imports: preload.modules,<% }%>
  <% if (aggregationHandlerName) { %>
  aggregationHandlerName: '<%-aggregationHandlerName%>',
  handlerNameMapping: (handlerName, event, context, oldContext) => {
    const allHandlers = <%-JSON.stringify(handlers && handlers[0] && handlers[0].handlers || [], null, 2)%>;
    <%-aggregationBeforeExecScript%>
    return [handlerName, event, context, oldContext];
  },
  <% }%>
});