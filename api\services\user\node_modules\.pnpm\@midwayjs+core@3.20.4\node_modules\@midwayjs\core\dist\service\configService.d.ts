import { IConfigService, MidwayAppInfo } from '../interface';
import { MidwayEnvironmentService } from './environmentService';
import { MidwayInformationService } from './informationService';
interface ConfigMergeInfo {
    value: any;
    env: string;
    extraPath?: string;
}
export declare class MidwayConfigService implements IConfigService {
    private envDirMap;
    private aliasMap;
    private configMergeOrder;
    protected configuration: {};
    protected isReady: boolean;
    protected externalObject: Record<string, unknown>[];
    protected appInfo: MidwayAppInfo;
    protected configFilterList: Array<(config: Record<string, any>) => Record<string, any> | undefined>;
    protected environmentService: MidwayEnvironmentService;
    protected informationService: MidwayInformationService;
    protected init(): void;
    add(configFilePaths: any[]): void;
    addObject(obj: Record<string, unknown>, reverse?: boolean): void;
    private getEnvSet;
    private getConfigEnv;
    load(): void;
    getConfiguration(configKey?: string): any;
    getConfigMergeOrder(): Array<ConfigMergeInfo>;
    private loadConfig;
    clearAllConfig(): void;
    clearConfigMergeOrder(): void;
    /**
     * add a config filter
     * @param filter
     */
    addFilter(filter: (config: Record<string, any>) => Record<string, any>): void;
    protected runWithFilter(config: Record<string, any>): Record<string, any>;
    getAppInfo(): MidwayAppInfo;
}
export {};
//# sourceMappingURL=configService.d.ts.map