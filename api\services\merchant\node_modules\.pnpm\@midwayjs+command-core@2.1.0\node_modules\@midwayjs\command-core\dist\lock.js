"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lock = void 0;
const EventEmitter = require("events");
var LockStatus;
(function (LockStatus) {
    LockStatus[LockStatus["Initial"] = 0] = "Initial";
    LockStatus[LockStatus["Running"] = 1] = "Running";
    LockStatus[LockStatus["Success"] = 2] = "Success";
})(LockStatus || (LockStatus = {}));
const statusMap = {};
class Lock {
    constructor(lockType) {
        this.resolveList = [];
        this.type = lockType;
        statusMap[lockType] = LockStatus.Initial;
        this.event = new EventEmitter();
        this.event.on('success', this.success.bind(this));
    }
    async wait(callback) {
        const status = statusMap[this.type];
        switch (status) {
            case LockStatus.Initial:
                statusMap[this.type] = LockStatus.Running;
                if (callback) {
                    await callback();
                }
                statusMap[this.type] = LockStatus.Success;
                this.event.emit('success');
                break;
            case LockStatus.Running:
                await this.waitLock();
        }
    }
    waitLock() {
        return new Promise(resolve => {
            if (statusMap[this.type] === LockStatus.Success) {
                resolve();
            }
            else {
                this.resolveList.push(resolve);
            }
        });
    }
    success() {
        statusMap[this.type] = LockStatus.Success;
        this.resolveList.forEach(resolve => {
            resolve();
        });
        this.event = null;
    }
}
exports.Lock = Lock;
//# sourceMappingURL=lock.js.map