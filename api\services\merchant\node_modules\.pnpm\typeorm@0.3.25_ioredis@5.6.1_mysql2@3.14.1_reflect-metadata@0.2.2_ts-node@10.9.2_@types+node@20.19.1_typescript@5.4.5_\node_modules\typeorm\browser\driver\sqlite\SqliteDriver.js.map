{"version": 3, "sources": ["../browser/src/driver/sqlite/SqliteDriver.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,aAAa,CAAA;AAC5B,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,8BAA8B,EAAE,MAAM,4CAA4C,CAAA;AAC3F,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAA;AACvD,OAAO,EAAE,aAAa,EAAE,MAAM,8BAA8B,CAAA;AAK5D,OAAO,EAAE,oBAAoB,EAAE,MAAM,yCAAyC,CAAA;AAE9E,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AAEjE;;GAEG;AACH,MAAM,OAAO,YAAa,SAAQ,oBAAoB;IAelD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QACjB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAkC,CAAA;QAC5D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QAErC,sBAAsB;QACtB,IAAI,CAAC,gBAAgB,EAAE,CAAA;IAC3B,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,UAAU;QACZ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;YAC5B,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CACvC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CACzB,CAAA;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAqB;QACnC,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,CAAA;QAErE,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAED,aAAa,CAAC,MAKb;QACG,IAAK,MAAM,CAAC,IAAY,KAAK,MAAM,EAAE,CAAC;YAClC,OAAO,MAAM,CAAA;QACjB,CAAC;QAED,OAAO,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;IACtC,CAAC;IAED,KAAK,CAAC,YAAY;QACd,OAAO,IAAI,CAAC,eAAe,EAAE,CAAA;IACjC,CAAC;IAED;;OAEG;IACH,cAAc,CACV,SAAiB,EACjB,OAAgB,EAChB,QAAiB;QAEjB,IAAI,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAA;QAC/B,IAAI,IAAI,CAAC,uCAAuC,CAAC,QAAQ,CAAC;YACtD,OAAO,GAAG,IAAI,CAAC,uCAAuC,CAClD,QAAQ,CACX,IAAI,SAAS,EAAE,CAAA;QAEpB,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ;YAAE,OAAO,SAAS,CAAA;QAExD,iHAAiH;QACjH,MAAM,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAA;QAC/C,gIAAgI;QAChI,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;YACpC,CAAC,CAAC,QAAQ;YACV,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,CAAC,CAAA;QAErD,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,GAAG;YAC/B,sBAAsB,EAAE,WAAW;YACnC,sBAAsB,EAAE,QAAQ;YAChC,YAAY,EAAE,cAAc;SAC/B,CAAA;QAED,OAAO,GAAG,cAAc,IAAI,SAAS,EAAE,CAAA;IAC3C,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,KAAK,CAAC,wBAAwB;QACpC,IACI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS;YAChC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAC9C,CAAC;YACC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAC7D,CAAC;QAED,MAAM,kBAAkB,GAAQ,MAAM,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YAC3D,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBACnC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,CAAC,GAAQ,EAAE,EAAE;oBACT,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,CAAC,UAAU,CAAC,CAAA;gBAClB,CAAC,CACJ,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CACvC,IAAI,CAAC,OAAO,CAAC,QAAQ,EACrB,IAAI,CAAC,OAAO,CAAC,KAAK,EAClB,CAAC,GAAQ,EAAE,EAAE;oBACT,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,CAAC,UAAU,CAAC,CAAA;gBAClB,CAAC,CACJ,CAAA;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,qFAAqF;QACrF,SAAS,GAAG,CAAC,IAAY;YACrB,OAAO,IAAI,OAAO,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAC5B,kBAAkB,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,GAAQ,EAAE,EAAE;oBACtC,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,EAAE,EAAE,CAAA;gBACR,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;QACD,6DAA6D;QAC7D,sFAAsF;QACtF,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YACnB,MAAM,GAAG,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAA;QACjE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC;YACzB,MAAM,GAAG,CAAC,2BAA2B,CAAC,CAAA;QAC1C,CAAC;QAED,IACI,IAAI,CAAC,OAAO,CAAC,WAAW;YACxB,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,KAAK,QAAQ;YAC5C,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,EAC9B,CAAC;YACC,MAAM,GAAG,CAAC,yBAAyB,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAA;QAClE,CAAC;QAED,yFAAyF;QACzF,kEAAkE;QAClE,MAAM,GAAG,CAAC,0BAA0B,CAAC,CAAA;QAErC,OAAO,kBAAkB,CAAA;IAC7B,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnE,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,OAAO,EAAE,CAAA;QAClC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACT,MAAM,IAAI,8BAA8B,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAA;QACjE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CAAC,QAAgB;QACpD,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAC/D,CAAC;IAED;;;;;OAKG;IACO,KAAK,CAAC,eAAe;QAC3B,kIAAkI;QAClI,IAAI,KAAK,EAAE,MAAM,EACb,YAAY,EACZ,sBAAsB,GACzB,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACzC,MAAM,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,CAAA;YAC1D,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,CACvB,WAAW,sBAAsB,SAAS,YAAY,GAAG,CAC5D,CAAA;QACL,CAAC;IACL,CAAC;IAES,mBAAmB;QACzB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAA;QACvC,OAAO,IAAI,CAAC,OAAO,CACf,UAAU,CAAC,SAAS,CAAC;YACjB,CAAC,CAAC,SAAS;YACX,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAC5C,CAAA;IACL,CAAC;CACJ", "file": "SqliteDriver.js", "sourcesContent": ["import fs from \"fs/promises\"\nimport path from \"path\"\nimport { DriverPackageNotInstalledError } from \"../../error/DriverPackageNotInstalledError\"\nimport { SqliteQueryRunner } from \"./SqliteQueryRunner\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { SqliteConnectionOptions } from \"./SqliteConnectionOptions\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { filepathToName, isAbsolute } from \"../../util/PathUtils\"\n\n/**\n * Organizes communication with sqlite DBMS.\n */\nexport class SqliteDriver extends AbstractSqliteDriver {\n    // -------------------------------------------------------------------------\n    // Public Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Connection options.\n     */\n    options: SqliteConnectionOptions\n\n    /**\n     * SQLite underlying library.\n     */\n    sqlite: any\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(connection: DataSource) {\n        super(connection)\n        this.connection = connection\n        this.options = connection.options as SqliteConnectionOptions\n        this.database = this.options.database\n\n        // load sqlite package\n        this.loadDependencies()\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Closes connection with database.\n     */\n    async disconnect(): Promise<void> {\n        return new Promise<void>((ok, fail) => {\n            this.queryRunner = undefined\n            this.databaseConnection.close((err: any) =>\n                err ? fail(err) : ok(),\n            )\n        })\n    }\n\n    /**\n     * Creates a query runner used to execute database queries.\n     */\n    createQueryRunner(mode: ReplicationMode): QueryRunner {\n        if (!this.queryRunner) this.queryRunner = new SqliteQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    normalizeType(column: {\n        type?: ColumnType\n        length?: number | string\n        precision?: number | null\n        scale?: number\n    }): string {\n        if ((column.type as any) === Buffer) {\n            return \"blob\"\n        }\n\n        return super.normalizeType(column)\n    }\n\n    async afterConnect(): Promise<void> {\n        return this.attachDatabases()\n    }\n\n    /**\n     * For SQLite, the database may be added in the decorator metadata. It will be a filepath to a database file.\n     */\n    buildTableName(\n        tableName: string,\n        _schema?: string,\n        database?: string,\n    ): string {\n        if (!database) return tableName\n        if (this.getAttachedDatabaseHandleByRelativePath(database))\n            return `${this.getAttachedDatabaseHandleByRelativePath(\n                database,\n            )}.${tableName}`\n\n        if (database === this.options.database) return tableName\n\n        // we use the decorated name as supplied when deriving attach handle (ideally without non-portable absolute path)\n        const identifierHash = filepathToName(database)\n        // decorated name will be assumed relative to main database file when non absolute. Paths supplied as absolute won't be portable\n        const absFilepath = isAbsolute(database)\n            ? database\n            : path.join(this.getMainDatabasePath(), database)\n\n        this.attachedDatabases[database] = {\n            attachFilepathAbsolute: absFilepath,\n            attachFilepathRelative: database,\n            attachHandle: identifierHash,\n        }\n\n        return `${identifierHash}.${tableName}`\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates connection with the database.\n     */\n    protected async createDatabaseConnection() {\n        if (\n            this.options.flags === undefined ||\n            !(this.options.flags & this.sqlite.OPEN_URI)\n        ) {\n            await this.createDatabaseDirectory(this.options.database)\n        }\n\n        const databaseConnection: any = await new Promise((ok, fail) => {\n            if (this.options.flags === undefined) {\n                const connection = new this.sqlite.Database(\n                    this.options.database,\n                    (err: any) => {\n                        if (err) return fail(err)\n                        ok(connection)\n                    },\n                )\n            } else {\n                const connection = new this.sqlite.Database(\n                    this.options.database,\n                    this.options.flags,\n                    (err: any) => {\n                        if (err) return fail(err)\n                        ok(connection)\n                    },\n                )\n            }\n        })\n\n        // Internal function to run a command on the connection and fail if an error occured.\n        function run(line: string): Promise<void> {\n            return new Promise((ok, fail) => {\n                databaseConnection.run(line, (err: any) => {\n                    if (err) return fail(err)\n                    ok()\n                })\n            })\n        }\n        // in the options, if encryption key for SQLCipher is setted.\n        // Must invoke key pragma before trying to do any other interaction with the database.\n        if (this.options.key) {\n            await run(`PRAGMA key = ${JSON.stringify(this.options.key)}`)\n        }\n\n        if (this.options.enableWAL) {\n            await run(`PRAGMA journal_mode = WAL`)\n        }\n\n        if (\n            this.options.busyTimeout &&\n            typeof this.options.busyTimeout === \"number\" &&\n            this.options.busyTimeout > 0\n        ) {\n            await run(`PRAGMA busy_timeout = ${this.options.busyTimeout}`)\n        }\n\n        // we need to enable foreign keys in sqlite to make sure all foreign key related features\n        // working properly. this also makes onDelete to work with sqlite.\n        await run(`PRAGMA foreign_keys = ON`)\n\n        return databaseConnection\n    }\n\n    /**\n     * If driver dependency is not given explicitly, then try to load it via \"require\".\n     */\n    protected loadDependencies(): void {\n        try {\n            const sqlite = this.options.driver || PlatformTools.load(\"sqlite3\")\n            this.sqlite = sqlite.verbose()\n        } catch (e) {\n            throw new DriverPackageNotInstalledError(\"SQLite\", \"sqlite3\")\n        }\n    }\n\n    /**\n     * Auto creates database directory if it does not exist.\n     */\n    protected async createDatabaseDirectory(fullPath: string): Promise<void> {\n        await fs.mkdir(path.dirname(fullPath), { recursive: true })\n    }\n\n    /**\n     * Performs the attaching of the database files. The attachedDatabase should have been populated during calls to #buildTableName\n     * during EntityMetadata production (see EntityMetadata#buildTablePath)\n     *\n     * https://sqlite.org/lang_attach.html\n     */\n    protected async attachDatabases() {\n        // @todo - possibly check number of databases (but unqueriable at runtime sadly) - https://www.sqlite.org/limits.html#max_attached\n        for await (const {\n            attachHandle,\n            attachFilepathAbsolute,\n        } of Object.values(this.attachedDatabases)) {\n            await this.createDatabaseDirectory(attachFilepathAbsolute)\n            await this.connection.query(\n                `ATTACH \"${attachFilepathAbsolute}\" AS \"${attachHandle}\"`,\n            )\n        }\n    }\n\n    protected getMainDatabasePath(): string {\n        const optionsDb = this.options.database\n        return path.dirname(\n            isAbsolute(optionsDb)\n                ? optionsDb\n                : path.join(process.cwd(), optionsDb),\n        )\n    }\n}\n"], "sourceRoot": "../.."}