<input type="file" id="input">
<output id="output"></output>
<style>
output::before {
  content: "output:";
}
output {
  display: block;
  padding: 1em;
  margin: 1em;
  outline: 1px solid gray;
  white-space: pre-wrap;
}
</style>
<script src="../dist/md5.min.js"></script>
<script>


function readAsArrayBuffer(file){
  return new Promise(function(resolve) {
    var reader = new FileReader();
    reader.readAsArrayBuffer(file)
    reader.onload = function(e) {
      resolve(e.target.result)
    };
  });
}

input.onchange = function(e) {
  var file = input.files[0];
  readAsArrayBuffer(file)
  .then(buffer => {
    console.log(buffer);
    var now = performance.now();
    var hash = MD5(buffer);
    var after = performance.now() - now;
    output.innerHTML = `
      file: ${file.name}
      size: ${file.size} bytes
      type: ${file.type}
      md5: ${hash}
      duration: ${after.toFixed(2)} ms 
    `;
  })
}


</script>
