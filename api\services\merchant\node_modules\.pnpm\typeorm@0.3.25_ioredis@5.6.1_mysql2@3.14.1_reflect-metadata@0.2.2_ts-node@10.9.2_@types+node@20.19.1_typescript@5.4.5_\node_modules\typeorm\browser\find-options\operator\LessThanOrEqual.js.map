{"version": 3, "sources": ["../browser/src/find-options/operator/LessThanOrEqual.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAI,KAA0B;IACzD,OAAO,IAAI,YAAY,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;AACrD,CAAC", "file": "LessThanOrEqual.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: LessThanOrEqual(10) }\n */\nexport function LessThanOrEqual<T>(value: T | FindOperator<T>) {\n    return new FindOperator(\"lessThanOrEqual\", value)\n}\n"], "sourceRoot": "../.."}