import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 用户提现信息
 */
@Entity('finance_user_draw')
export class FinanceUserDrawEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '姓名' })
  name: string;

  @Column({ comment: '手机号' })
  phone: string;

  @Column({ comment: '对象信息', type: 'json', nullable: true })
  info: {
    // 银行卡
    bank?: {
      // 银行卡号
      cardNo: string;
      // 银行名称
      name: string;
      // 支行名称
      branch: string;
    };
    // 微信
    wechat?: {
      // 二维码
      qrCode: string;
    };
    // 支付宝
    alipay?: {
      // 支付宝账号
      account: string;
      // 二维码
      qrCode: string;
    };
  };
}
