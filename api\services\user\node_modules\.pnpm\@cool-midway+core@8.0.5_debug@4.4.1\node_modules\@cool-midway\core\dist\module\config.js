"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolModuleConfig = void 0;
const core_1 = require("@midwayjs/core");
const fs = require("fs");
const core_2 = require("../exception/core");
const _ = require("lodash");
const location_1 = require("../util/location");
/**
 * 模块配置
 */
let CoolModuleConfig = class CoolModuleConfig {
    async init() {
        const modules = [];
        // 模块路径
        const moduleBasePath = `${location_1.default.getRunPath()}/modules/`;
        if (!fs.existsSync(moduleBasePath)) {
            return;
        }
        if (!this.allConfig['module']) {
            this.allConfig['module'] = {};
        }
        // 全局中间件
        const globalMiddlewareArr = [];
        for (const module of fs.readdirSync(moduleBasePath)) {
            const modulePath = `${moduleBasePath}/${module}`;
            const dirStats = fs.statSync(modulePath);
            if (dirStats.isDirectory()) {
                const configPath = fs.existsSync(`${modulePath}/config.ts`)
                    ? `${modulePath}/config.ts`
                    : `${modulePath}/config.js`;
                if (fs.existsSync(configPath)) {
                    const moduleConfig = require(configPath).default({
                        app: this.app,
                        env: this.app.getEnv(),
                    });
                    modules.push({
                        order: moduleConfig.order || 0,
                        module: module,
                    });
                    await this.moduleConfig(module, moduleConfig);
                    // 处理全局中间件
                    if (!_.isEmpty(moduleConfig.globalMiddlewares)) {
                        globalMiddlewareArr.push({
                            order: moduleConfig.order || 0,
                            data: moduleConfig.globalMiddlewares,
                        });
                    }
                }
                else {
                    throw new core_2.CoolCoreException(`模块【${module}】缺少config.ts配置文件`);
                }
            }
        }
        this.modules = _.orderBy(modules, ['order'], ['desc']).map(e => {
            return e.module;
        });
        await this.globalMiddlewareArr(globalMiddlewareArr);
    }
    /**
     * 模块配置
     * @param module 模块
     * @param config 配置
     */
    async moduleConfig(module, config) {
        // 追加配置
        this.allConfig['module'][module] = config;
    }
    /**
     * 全局中间件
     * @param middleware 中间件
     */
    async globalMiddlewareArr(middlewares) {
        middlewares = _.orderBy(middlewares, ['order'], ['desc']);
        for (const middleware of middlewares) {
            for (const item of middleware.data) {
                this.app.getMiddleware().insertLast(item);
            }
        }
    }
};
exports.CoolModuleConfig = CoolModuleConfig;
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], CoolModuleConfig.prototype, "app", void 0);
__decorate([
    (0, core_1.Config)(core_1.ALL),
    __metadata("design:type", Object)
], CoolModuleConfig.prototype, "allConfig", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoolModuleConfig.prototype, "init", null);
exports.CoolModuleConfig = CoolModuleConfig = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolModuleConfig);
