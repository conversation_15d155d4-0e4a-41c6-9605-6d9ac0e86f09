{"name": "mri", "version": "1.1.4", "description": "Quickly scan for CLI flags and arguments", "repository": "lukeed/mri", "main": "lib/index.js", "license": "MIT", "files": ["lib"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "lukeed.com"}, "engines": {"node": ">=4"}, "scripts": {"bench": "node bench", "test": "tape test/*.js | tap-spec"}, "keywords": ["argv", "arguments", "cli", "minimist", "options", "optimist", "parser", "args"], "devDependencies": {"tap-spec": "^4.1.1", "tape": "^4.6.3"}}