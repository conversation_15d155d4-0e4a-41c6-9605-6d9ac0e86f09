{"version": 3, "file": "compileTypeScript.js", "sourceRoot": "", "sources": ["../../src/utils/compileTypeScript.ts"], "names": [], "mappings": ";;;AAAA,uCAAoC;AACpC,+BAAsC;AAOtC,gCAAuC;AACvC,iCAAgC;AAEzB,MAAM,iBAAiB,GAAG,KAAK,EAAE,OAMvC,EAAE,EAAE;;IACH,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAC5B,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;IAC7D,MAAM,KAAK,GAAG,IAAA,mBAAa,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IACnD,MAAM,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;IAC1B,IAAI,CAAC,SAAS,EAAE;QACd,SAAS,GAAG,MAAM,IAAA,mBAAW,EAAC,OAAO,CAAC,CAAC;KACxC;IACD,IAAI,CAAC,SAAS,CAAC,eAAe,EAAE;QAC9B,SAAS,CAAC,eAAe,GAAG,EAAE,CAAC;KAChC;IACD,SAAS,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CACvC;QACE,sBAAsB,EAAE,IAAI;QAC5B,qBAAqB,EAAE,IAAI;KAC5B,EACD,SAAS,CAAC,eAAe,EACzB,YAAY,CACb,CAAC;IAEF,IAAI,MAAM,EAAE;QACV,SAAS,CAAC,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;KAC3C;IACD,IAAI,SAAS,EAAE;QACb,SAAS,CAAC,eAAe,CAAC,OAAO,GAAG,SAAS,CAAC;KAC/C;IACD,IAAI,CAAC,CAAA,MAAA,SAAS,CAAC,OAAO,0CAAE,MAAM,CAAA,EAAE;QAC9B,SAAS,CAAC,OAAO,GAAG,CAAC,SAAS,IAAI,KAAK,CAAC,CAAC;KAC1C;IAED,MAAM,iBAAiB,GAAG,EAAE,CAAC,0BAA0B,CACrD,SAAS,EACT,EAAE,CAAC,GAAG,EACN,OAAO,CACR,CAAC;IACF,MAAM,IAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;IAC9D,MAAM,SAAS,GAAG,iBAAiB,CAAC,SAAS,CAAC;IAC9C,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,iBAAiB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAC7E,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;IAClC,MAAM,cAAc,GAAG,EAAE;SACtB,qBAAqB,CAAC,OAAO,CAAC;SAC9B,MAAM,CAAC,UAAU,CAAC,WAAW,CAAmC,CAAC;IACpE,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE;QAClC,IAAI,KAAK,CAAC,QAAQ,KAAK,EAAE,CAAC,kBAAkB,CAAC,KAAK,EAAE;YAClD,SAAS;SACV;QACD,MAAM,SAAS,GAAG,IAAA,qBAAa,EAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAEhD,qDAAqD;QACrD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE;YAC7B,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACjC;QACD,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;KACxB;IACD,OAAO;QACL,SAAS;QACT,OAAO,EAAE,iBAAiB,CAAC,OAAO;QAClC,MAAM;QACN,eAAe;KAChB,CAAC;AACJ,CAAC,CAAC;AApEW,QAAA,iBAAiB,qBAoE5B;AAEK,MAAM,aAAa,GAAG,CAC3B,OAAe,EACf,KAAmC,EACA,EAAE;;IACrC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;QACtB,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;KAClC;IACD,MAAM,OAAO,GAAG,yCAAyC,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;IAC7E,IAAI,SAAS,GAAG,EAAE,CAAC;IACnB,oCAAoC;IACpC,IAAI,MAAA,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,IAAI,0CAAE,IAAI,EAAE;QACrB,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/D,SAAS,GAAG,GAAG,IAAA,eAAQ,EAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,MAAM,IAClE,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MACxB,EAAE,CAAC;KACJ;IACD,OAAO;QACL,OAAO;QACP,IAAI,EAAE,SAAS;KAChB,CAAC;AACJ,CAAC,CAAC;AApBW,QAAA,aAAa,iBAoBxB;AAEF,SAAS,yCAAyC,CAChD,KAAsC;IAEtC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,KAAK,CAAC;KACd;IAED,MAAM,GAAG,GAAa,EAAE,CAAC;IAEzB,IAAI,KAAK,CAAC,WAAW,EAAE;QACrB,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KAC7B,CAAC,YAAY;IACd,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;QAC7B,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC,CAAC;KACxE,CAAC,YAAY;IAEd,OAAO,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,CAAC;AAEM,MAAM,QAAQ,GAAG,KAAK,EAAE,IAAY,EAAE,EAAE;IAC7C,IAAI,MAAM,IAAA,aAAM,EAAC,IAAI,CAAC,EAAE;QACtB,IAAI;YACF,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;SAClD;QAAC,WAAM;YACN,OAAO,EAAE,CAAC;SACX;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AATW,QAAA,QAAQ,YASnB;AAEK,MAAM,WAAW,GAAG,KAAK,EAAE,OAAe,EAAE,EAAE;IACnD,MAAM,YAAY,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IACpD,OAAO,IAAA,gBAAQ,EAAC,YAAY,CAAC,CAAC;AAChC,CAAC,CAAC;AAHW,QAAA,WAAW,eAGtB"}