"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayWebRouterService = void 0;
const decorator_1 = require("../decorator");
const util_1 = require("../util");
const error_1 = require("../error");
const util = require("util");
const pathToRegexp_1 = require("../util/pathToRegexp");
const types_1 = require("../util/types");
const interface_1 = require("../interface");
const debug = util.debuglog('midway:debug');
let MidwayWebRouterService = class MidwayWebRouterService {
    constructor(options = {}) {
        this.options = options;
        this.isReady = false;
        this.routes = new Map();
        this.routesPriority = [];
    }
    async analyze() {
        this.analyzeController();
        this.sortPrefixAndRouter();
    }
    analyzeController() {
        const controllerModules = (0, decorator_1.listModule)(decorator_1.CONTROLLER_KEY);
        for (const module of controllerModules) {
            const controllerOption = (0, decorator_1.getClassMetadata)(decorator_1.CONTROLLER_KEY, module);
            this.addController(module, controllerOption, this.options.includeFunctionRouter);
        }
    }
    sortPrefixAndRouter() {
        // filter empty prefix
        this.routesPriority = this.routesPriority.filter(item => {
            const prefixList = this.routes.get(item.prefix);
            if (prefixList.length > 0) {
                return true;
            }
            else {
                this.routes.delete(item.prefix);
                return false;
            }
        });
        // sort router
        for (const prefix of this.routes.keys()) {
            const routerInfo = this.routes.get(prefix);
            this.routes.set(prefix, this.sortRouter(routerInfo));
        }
        // sort prefix
        this.routesPriority = this.routesPriority.sort((routeA, routeB) => {
            return routeB.prefix.length - routeA.prefix.length;
        });
    }
    addController(controllerClz, controllerOption, resourceOptions = {}, functionMeta = false) {
        var _a;
        if (resourceOptions && typeof resourceOptions === 'boolean') {
            functionMeta = resourceOptions;
            resourceOptions = undefined;
        }
        if (!resourceOptions) {
            resourceOptions = {};
        }
        const controllerId = (0, decorator_1.getProviderName)(controllerClz);
        debug(`[core]: Found Controller ${controllerId}.`);
        const id = (0, decorator_1.getProviderUUId)(controllerClz);
        controllerOption.routerOptions = controllerOption.routerOptions || {};
        let priority;
        // implement middleware in controller
        const middleware = controllerOption.routerOptions.middleware;
        const controllerIgnoreGlobalPrefix = !!((_a = controllerOption.routerOptions) === null || _a === void 0 ? void 0 : _a.ignoreGlobalPrefix);
        let prefix = (0, util_1.joinURLPath)(this.options.globalPrefix, controllerOption.prefix || '/');
        const ignorePrefix = controllerOption.prefix || '/';
        // if controller set ignore global prefix, all router will be ignore too.
        if (controllerIgnoreGlobalPrefix) {
            prefix = ignorePrefix;
        }
        if (/\*/.test(prefix)) {
            throw new error_1.MidwayCommonError(`Router prefix ${prefix} can't set string with *`);
        }
        // set prefix
        if (!this.routes.has(prefix)) {
            this.routes.set(prefix, []);
            this.routesPriority.push({
                prefix,
                priority: prefix === '/' && priority === undefined ? -999 : 0,
                middleware,
                routerOptions: controllerOption.routerOptions,
                controllerId,
                routerModule: controllerClz,
            });
        }
        else {
            // 不同的 controller，可能会有相同的 prefix，一旦 options 不同，就要报错
            if (middleware && middleware.length > 0) {
                const originRoute = this.routesPriority.filter(el => {
                    return el.prefix === prefix;
                })[0];
                throw new error_1.MidwayDuplicateControllerOptionsError(prefix, controllerId, originRoute.controllerId);
            }
        }
        // set ignorePrefix
        if (!this.routes.has(ignorePrefix)) {
            this.routes.set(ignorePrefix, []);
            this.routesPriority.push({
                prefix: ignorePrefix,
                priority: ignorePrefix === '/' && priority === undefined ? -999 : 0,
                middleware,
                routerOptions: controllerOption.routerOptions,
                controllerId,
                routerModule: controllerClz,
            });
        }
        const webRouterInfo = (0, decorator_1.getClassMetadata)(decorator_1.WEB_ROUTER_KEY, controllerClz);
        if (webRouterInfo && typeof webRouterInfo[Symbol.iterator] === 'function') {
            for (const webRouter of webRouterInfo) {
                const routeArgsInfo = (0, decorator_1.getPropertyDataFromClass)(decorator_1.WEB_ROUTER_PARAM_KEY, controllerClz, webRouter.method) || [];
                const routerResponseData = (0, decorator_1.getPropertyMetadata)(decorator_1.WEB_RESPONSE_KEY, controllerClz, webRouter.method) || [];
                const data = {
                    id,
                    prefix: webRouter.ignoreGlobalPrefix ? ignorePrefix : prefix,
                    routerName: webRouter.routerName || '',
                    url: webRouter.path,
                    requestMethod: webRouter.requestMethod,
                    method: webRouter.method,
                    description: webRouter.description || '',
                    summary: webRouter.summary || '',
                    handlerName: `${controllerId}.${webRouter.method}`,
                    funcHandlerName: `${controllerId}.${webRouter.method}`,
                    controllerId,
                    controllerClz,
                    middleware: webRouter.middleware || [],
                    controllerMiddleware: middleware || [],
                    requestMetadata: routeArgsInfo,
                    responseMetadata: routerResponseData,
                };
                if (functionMeta) {
                    // get function information
                    data.functionName = controllerId + '-' + webRouter.method;
                    data.functionTriggerName = interface_1.ServerlessTriggerType.HTTP;
                    data.functionTriggerMetadata = {
                        path: (0, util_1.joinURLPath)(prefix, webRouter.path.toString()),
                        method: webRouter.requestMethod,
                    };
                    data.functionMetadata = {
                        functionName: data.functionName,
                    };
                }
                if (resourceOptions.resourceFilter &&
                    !resourceOptions.resourceFilter(data)) {
                    continue;
                }
                this.checkDuplicateAndPush(data.prefix, data);
            }
        }
    }
    /**
     * dynamically add a route to root prefix
     * @param routerFunction
     * @param routerInfoOption
     */
    addRouter(routerFunction, routerInfoOption) {
        const prefix = routerInfoOption.prefix || '';
        routerInfoOption.requestMethod = (routerInfoOption.requestMethod || 'GET').toUpperCase();
        if (!this.routes.has(prefix)) {
            this.routes.set(prefix, []);
            this.routesPriority.push({
                prefix,
                priority: 0,
                middleware: [],
                routerOptions: {},
                controllerId: undefined,
                routerModule: undefined,
            });
        }
        this.checkDuplicateAndPush(prefix, Object.assign(routerInfoOption, {
            method: routerFunction,
        }));
        // sort again
        this.sortPrefixAndRouter();
    }
    sortRouter(urlMatchList) {
        // 1. 绝对路径规则优先级最高如 /ab/cb/e
        // 2. 星号只能出现最后且必须在/后面，如 /ab/cb/**
        // 3. 如果绝对路径和通配都能匹配一个路径时，绝对规则优先级高
        // 4. 有多个通配能匹配一个路径时，最长的规则匹配，如 /ab/** 和 /ab/cd/** 在匹配 /ab/cd/f 时命中 /ab/cd/**
        // 5. 如果 / 与 /* 都能匹配 / ,但 / 的优先级高于 /*
        return urlMatchList
            .map(item => {
            const urlString = item.url.toString();
            const weightArr = types_1.Types.isRegExp(item.url)
                ? urlString.split('\\/')
                : urlString.split('/');
            let weight = 0;
            // 权重，比如通配的不加权，非通配加权，防止通配出现在最前面
            for (const fragment of weightArr) {
                if (fragment === '' ||
                    fragment.includes(':') ||
                    fragment.includes('*')) {
                    weight += 0;
                }
                else {
                    weight += 1;
                }
            }
            let category = 2;
            const paramString = urlString.includes(':')
                ? urlString.replace(/:.+$/, '')
                : '';
            if (paramString) {
                category = 1;
            }
            if (urlString.includes('*')) {
                category = 0;
            }
            return {
                ...item,
                _pureRouter: urlString.replace(/\**$/, '').replace(/:\w+/, '123'),
                _level: urlString.split('/').length - 1,
                _paramString: paramString,
                _category: category,
                _weight: weight,
            };
        })
            .sort((handlerA, handlerB) => {
            // 不同一层级的对比
            if (handlerA._category !== handlerB._category) {
                return handlerB._category - handlerA._category;
            }
            // 不同权重
            if (handlerA._weight !== handlerB._weight) {
                return handlerB._weight - handlerA._weight;
            }
            // 不同长度
            if (handlerA._level === handlerB._level) {
                if (handlerB._pureRouter === handlerA._pureRouter) {
                    return (handlerA.url.toString().length - handlerB.url.toString().length);
                }
                return handlerB._pureRouter.length - handlerA._pureRouter.length;
            }
            return handlerB._level - handlerA._level;
        });
    }
    async getRoutePriorityList() {
        if (!this.isReady) {
            await this.analyze();
            this.isReady = true;
        }
        return this.routesPriority;
    }
    async getRouterTable() {
        if (!this.isReady) {
            await this.analyze();
            this.isReady = true;
        }
        return this.routes;
    }
    async getFlattenRouterTable(options = {}) {
        if (!this.isReady) {
            await this.analyze();
            this.isReady = true;
        }
        let routeArr = [];
        for (const routerPriority of this.routesPriority) {
            routeArr = routeArr.concat(this.routes.get(routerPriority.prefix));
        }
        if (options.compileUrlPattern) {
            // attach match pattern function
            for (const item of routeArr) {
                if (item.fullUrlFlattenString) {
                    item.fullUrlCompiledRegexp = pathToRegexp_1.PathToRegexpUtil.toRegexp(item.fullUrlFlattenString);
                }
            }
        }
        return routeArr;
    }
    async getMatchedRouterInfo(routerUrl, method) {
        const routes = await this.getFlattenRouterTable({
            compileUrlPattern: true,
        });
        let matchedRouterInfo;
        for (const item of routes) {
            if (item.fullUrlCompiledRegexp) {
                const itemRequestMethod = item['requestMethod'].toUpperCase();
                if (('ALL' === itemRequestMethod ||
                    method.toUpperCase() === itemRequestMethod) &&
                    item.fullUrlCompiledRegexp.test(routerUrl)) {
                    matchedRouterInfo = item;
                    break;
                }
            }
        }
        return matchedRouterInfo;
    }
    checkDuplicateAndPush(prefix, routerInfo) {
        const prefixList = this.routes.get(prefix);
        const matched = prefixList.filter(item => {
            return (routerInfo.url &&
                routerInfo.requestMethod &&
                item.url === routerInfo.url &&
                item.requestMethod === routerInfo.requestMethod);
        });
        if (matched && matched.length) {
            throw new error_1.MidwayDuplicateRouteError(`${routerInfo.requestMethod} ${routerInfo.url}`, `${matched[0].handlerName}`, `${routerInfo.handlerName}`);
        }
        // format url
        if (!routerInfo.fullUrlFlattenString &&
            routerInfo.url &&
            typeof routerInfo.url === 'string') {
            routerInfo.fullUrl = (0, util_1.joinURLPath)(prefix, routerInfo.url);
            if (/\*$/.test(routerInfo.fullUrl)) {
                routerInfo.fullUrlFlattenString = routerInfo.fullUrl.replace('*', '(.*)');
            }
            else {
                routerInfo.fullUrlFlattenString = routerInfo.fullUrl;
            }
        }
        prefixList.push(routerInfo);
    }
};
MidwayWebRouterService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayWebRouterService);
exports.MidwayWebRouterService = MidwayWebRouterService;
//# sourceMappingURL=webRouterService.js.map