{"version": 3, "file": "fork.js", "sourceRoot": "", "sources": ["../../src/utils/fork.ts"], "names": [], "mappings": ";;;AAAA,iDAAqC;AACrC,MAAM,MAAM,GAAQ,IAAI,GAAG,EAAE,CAAC;AAC9B,IAAI,OAAO,GAAG,KAAK,CAAC;AACpB,MAAM,SAAS,GAAG,IAAI,CAAC,EAAE;IACvB,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACjB,IAAI,CAAC,OAAO,EAAE;QACZ,OAAO,GAAG,IAAI,CAAC;QACf,IAAI,MAAM,CAAC;QACX,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAU,EAAE,EAAE;YACtD,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;gBACvB,MAAM,GAAG,KAAK,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;YACxB,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACpB;QACH,CAAC,CAAC,CAAC;KACJ;AACH,CAAC,CAAC;AAEK,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,IAAI,GAAG,EAAE,EAAE,UAAe,EAAE,EAAE,EAAE;IACnE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,SAAS,CAAC;IAC3C,MAAM,IAAI,GAAQ,IAAA,oBAAI,EAAC,UAAU,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IAClD,SAAS,CAAC,IAAI,CAAC,CAAC;IAChB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,IAAS,EAAE,EAAE;YAC9B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACpB,IAAI,IAAI,KAAK,CAAC,EAAE;gBACd,MAAM,GAAG,GAAQ,IAAI,KAAK,CACxB,UAAU,GAAG,GAAG,GAAG,IAAI,GAAG,kBAAkB,GAAG,IAAI,CACpD,CAAC;gBACF,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;gBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;aACb;iBAAM;gBACL,OAAO,CAAC,IAAI,CAAC,CAAC;aACf;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAlBW,QAAA,QAAQ,YAkBnB"}