{"version": 3, "sources": ["../browser/src/error/NestedSetMultipleRootError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,0BAA2B,SAAQ,YAAY;IACxD;QACI,KAAK,CAAC,oDAAoD,CAAC,CAAA;IAC/D,CAAC;CACJ", "file": "NestedSetMultipleRootError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class NestedSetMultipleRootError extends TypeORMError {\n    constructor() {\n        super(`Nested sets do not support multiple root entities.`)\n    }\n}\n"], "sourceRoot": ".."}