"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exec = void 0;
const child_process_1 = require("child_process");
const exec = (options) => {
    const { cmd, baseDir, slience, log } = options;
    return new Promise((resolved, rejected) => {
        const execProcess = (0, child_process_1.exec)(cmd, {
            cwd: baseDir,
        }, (err, result) => {
            if (err) {
                return rejected(err);
            }
            resolved(result);
        });
        execProcess.stdout.on('data', data => {
            if (!slience) {
                (log || console.log)(data);
            }
        });
    });
};
exports.exec = exec;
//# sourceMappingURL=exec.js.map