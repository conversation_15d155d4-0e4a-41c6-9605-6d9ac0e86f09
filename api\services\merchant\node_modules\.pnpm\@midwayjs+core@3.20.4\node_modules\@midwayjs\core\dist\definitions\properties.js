"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectProperties = void 0;
class ObjectProperties extends Map {
    propertyKeys() {
        return Array.from(this.keys());
    }
    getProperty(key, defaultValue) {
        if (this.has(key)) {
            return this.get(key);
        }
        return defaultValue;
    }
    setProperty(key, value) {
        return this.set(key, value);
    }
}
exports.ObjectProperties = ObjectProperties;
//# sourceMappingURL=properties.js.map