{"version": 3, "sources": ["../browser/src/persistence/SubjectChangeMap.ts"], "names": [], "mappings": "", "file": "SubjectChangeMap.js", "sourcesContent": ["import { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { Subject } from \"./Subject\"\n\n/**\n * Change for insertion or updation of the column of the subject.\n */\nexport interface SubjectChangeMap {\n    /**\n     * Column that needs to be changed.\n     * Either column, either relation must be set in the change.\n     */\n    column?: ColumnMetadata\n\n    /**\n     * Relation that needs to be changed.\n     * Either column, either relation must be set in the change.\n     */\n    relation?: RelationMetadata\n\n    /**\n     * Value needs to be inserted into given column.\n     * This value can also be another subject, when this column has a referenced column.\n     */\n    value: Subject | any\n}\n"], "sourceRoot": ".."}