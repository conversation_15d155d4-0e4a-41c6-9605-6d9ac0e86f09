{"version": 3, "file": "skipLast.js", "sources": ["../../../src/internal/operators/skipLast.ts"], "names": [], "mappings": ";AACA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,OAAO,EAAE,uBAAuB,EAAE,MAAM,iCAAiC,CAAC;AA0C1E,MAAM,UAAU,QAAQ,CAAI,KAAa;IACvC,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,EAAxC,CAAwC,CAAC;AAC7E,CAAC;AAED;IACE,0BAAoB,UAAkB;QAAlB,eAAU,GAAV,UAAU,CAAQ;QACpC,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,EAAE;YACvB,MAAM,IAAI,uBAAuB,CAAC;SACnC;IACH,CAAC;IAED,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,EAAE;YAGzB,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;SACrD;aAAM;YACL,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SAC9E;IACH,CAAC;IACH,uBAAC;AAAD,CAAC,AAhBD,IAgBC;AAOD;IAAoC,8CAAa;IAI/C,4BAAY,WAA0B,EAAU,UAAkB;QAAlE,YACE,kBAAM,WAAW,CAAC,SAEnB;QAH+C,gBAAU,GAAV,UAAU,CAAQ;QAF1D,YAAM,GAAW,CAAC,CAAC;QAIzB,KAAI,CAAC,KAAK,GAAG,IAAI,KAAK,CAAI,UAAU,CAAC,CAAC;;IACxC,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAE5B,IAAI,KAAK,GAAG,SAAS,EAAE;YACrB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SAC3B;aAAM;YACL,IAAM,YAAY,GAAG,KAAK,GAAG,SAAS,CAAC;YACvC,IAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;YACxB,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC;YAEpC,IAAI,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;YAC3B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SACjC;IACH,CAAC;IACH,yBAAC;AAAD,CAAC,AAxBD,CAAoC,UAAU,GAwB7C"}