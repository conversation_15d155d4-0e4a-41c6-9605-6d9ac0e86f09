{"version": 3, "sources": ["../browser/src/query-builder/relation-count/RelationCountAttribute.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAA;AAIxD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAA;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAA;AAEvD,MAAM,OAAO,sBAAsB;IAuB/B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACY,aAAiC,EACzC,sBAAwD;QADhD,kBAAa,GAAb,aAAa,CAAoB;QAGzC,WAAW,CAAC,MAAM,CAAC,IAAI,EAAE,sBAAsB,IAAI,EAAE,CAAC,CAAA;IAC1D,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,IAAI,uBAAuB;QACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAA;IAC9C,CAAC;IAED;;;;;OAKG;IACH,IAAI,WAAW;QACX,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,YAAY,CAClB,+DAA+D,CAClE,CAAA;QAEL,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED;;;;;;OAMG;IACH,IAAI,gBAAgB;QAChB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,YAAY,CAClB,0DAA0D,CAC7D,CAAA;QAEL,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAC1C,CAAC;IAED,IAAI,aAAa;QACb,MAAM,CAAC,WAAW,EAAE,gBAAgB,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpE,OAAO,WAAW,GAAG,GAAG,GAAG,gBAAgB,GAAG,KAAK,CAAA;IACvD,CAAC;IAED;;;;OAIG;IACH,IAAI,QAAQ;QACR,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,YAAY,CAClB,0DAA0D,CAC7D,CAAA;QAEL,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAChE,MAAM,sBAAsB,GACxB,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;QACnD,MAAM,QAAQ,GACV,sBAAsB,CAAC,QAAQ,CAAC,4BAA4B,CACxD,YAAY,CACf,CAAA;QACL,IAAI,CAAC,QAAQ;YACT,MAAM,IAAI,YAAY,CAClB,+BAA+B,YAAY,2BAA2B,CACzE,CAAA;QACL,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;;OAGG;IACH,IAAI,QAAQ;QACR,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC;YACrD,MAAM,IAAI,YAAY,CAClB,0DAA0D,CAC7D,CAAA;QAEL,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,WAAW,CAAC,CAAA;QACjE,OAAO,SAAS,CAAC,QAAQ,CAAA;IAC7B,CAAC;IAED,IAAI,yBAAyB;QACzB,OAAO,IAAI,CAAC,aAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAC5C,CAAC;CACJ", "file": "RelationCountAttribute.js", "sourcesContent": ["import { EntityMetadata } from \"../../metadata/EntityMetadata\"\nimport { QueryBuilderUtils } from \"../QueryBuilderUtils\"\nimport { RelationMetadata } from \"../../metadata/RelationMetadata\"\nimport { QueryExpressionMap } from \"../QueryExpressionMap\"\nimport { SelectQueryBuilder } from \"../SelectQueryBuilder\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\nimport { TypeORMError } from \"../../error/TypeORMError\"\n\nexport class RelationCountAttribute {\n    /**\n     * Alias of the joined (destination) table.\n     */\n    alias?: string\n\n    /**\n     * Name of relation.\n     */\n    relationName: string\n\n    /**\n     * Property + alias of the object where to joined data should be mapped.\n     */\n    mapToProperty: string\n\n    /**\n     * Extra condition applied to \"ON\" section of join.\n     */\n    queryBuilderFactory?: (\n        qb: SelectQueryBuilder<any>,\n    ) => SelectQueryBuilder<any>\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        private expressionMap: QueryExpressionMap,\n        relationCountAttribute?: Partial<RelationCountAttribute>,\n    ) {\n        ObjectUtils.assign(this, relationCountAttribute || {})\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    get joinInverseSideMetadata(): EntityMetadata {\n        return this.relation.inverseEntityMetadata\n    }\n\n    /**\n     * Alias of the parent of this join.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"post\" is a parent alias.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get parentAlias(): string {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value must be a string representation of alias property`,\n            )\n\n        return this.relationName.split(\".\")[0]\n    }\n\n    /**\n     * Relation property name of the parent.\n     * This is used to understand what is joined.\n     * For example, if we join (\"post.category\", \"categoryAlias\") then \"category\" is a relation property.\n     * This value is extracted from entityOrProperty value.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get relationProperty(): string | undefined {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value is a string representation of alias property`,\n            )\n\n        return this.relationName.split(\".\")[1]\n    }\n\n    get junctionAlias(): string {\n        const [parentAlias, relationProperty] = this.relationName.split(\".\")\n        return parentAlias + \"_\" + relationProperty + \"_rc\"\n    }\n\n    /**\n     * Relation of the parent.\n     * This is used to understand what is joined.\n     * This is available when join was made using \"post.category\" syntax.\n     */\n    get relation(): RelationMetadata {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value is a string representation of alias property`,\n            )\n\n        const [parentAlias, propertyPath] = this.relationName.split(\".\")\n        const relationOwnerSelection =\n            this.expressionMap.findAliasByName(parentAlias)\n        const relation =\n            relationOwnerSelection.metadata.findRelationWithPropertyPath(\n                propertyPath,\n            )\n        if (!relation)\n            throw new TypeORMError(\n                `Relation with property path ${propertyPath} in entity was not found.`,\n            )\n        return relation\n    }\n\n    /**\n     * Metadata of the joined entity.\n     * If table without entity was joined, then it will return undefined.\n     */\n    get metadata(): EntityMetadata {\n        if (!QueryBuilderUtils.isAliasProperty(this.relationName))\n            throw new TypeORMError(\n                `Given value is a string representation of alias property`,\n            )\n\n        const parentAlias = this.relationName.split(\".\")[0]\n        const selection = this.expressionMap.findAliasByName(parentAlias)\n        return selection.metadata\n    }\n\n    get mapToPropertyPropertyName(): string {\n        return this.mapToProperty!.split(\".\")[1]\n    }\n}\n"], "sourceRoot": "../.."}