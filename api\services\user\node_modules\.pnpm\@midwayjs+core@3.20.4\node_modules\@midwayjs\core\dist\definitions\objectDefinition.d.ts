import { IObjectCreator, IObjectDefinition, ObjectIdentifier, ScopeEnum } from '../interface';
import { ObjectProperties } from './properties';
export declare class ObjectDefinition implements IObjectDefinition {
    protected _attrs: Map<ObjectIdentifier, any>;
    protected _asynchronous: boolean;
    scope: ScopeEnum;
    creator: IObjectCreator;
    id: string;
    name: string;
    initMethod: string;
    destroyMethod: string;
    constructMethod: string;
    constructorArgs: any[];
    srcPath: string;
    path: any;
    export: string;
    dependsOn: ObjectIdentifier[];
    properties: ObjectProperties;
    namespace: string;
    handlerProps: any[];
    createFrom: any;
    allowDowngrade: boolean;
    constructor();
    set asynchronous(asynchronous: boolean);
    isAsync(): boolean;
    isSingletonScope(): boolean;
    isRequestScope(): boolean;
    hasDependsOn(): boolean;
    hasConstructorArgs(): boolean;
    getAttr(key: ObjectIdentifier): any;
    hasAttr(key: ObjectIdentifier): boolean;
    setAttr(key: ObjectIdentifier, value: any): void;
}
//# sourceMappingURL=objectDefinition.d.ts.map