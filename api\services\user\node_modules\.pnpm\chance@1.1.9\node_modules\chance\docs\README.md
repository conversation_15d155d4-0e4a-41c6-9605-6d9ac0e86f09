* [Chance](intro.md)
* Usage
  * [bower](usage/bower.md)
  * [browser](usage/browser.md)
  * [cli](usage/cli.md)
  * [node](usage/node.md)
  * [requirejs](usage/requirejs.md)
  * [seed](usage/seed.md)
  * [function](usage/function.md)
* Basics
  * [bool](basics/bool.md)
  * [falsy](basics/falsy.md)
  * [character](basics/character.md)
  * [floating](basics/floating.md)
  * [integer](basics/integer.md)
  * [letter](basics/letter.md)
  * [natural](basics/natural.md)
  * [prime](basics/prime.md)
  * [string](basics/string.md)
  * [template](basics/template.md)
* Text
  * [paragraph](text/paragraph.md)
  * [sentence](text/sentence.md)
  * [syllable](text/syllable.md)
  * [word](text/word.md)
* Person
  * [age](person/age.md)
  * [birthday](person/birthday.md)
  * [cf](person/cf.md)
  * [cpf](person/cpf.md)
  * [first](person/first.md)
  * [gender](person/gender.md)
  * [last](person/last.md)
  * [name](person/name.md)
  * [prefix](person/prefix.md)
  * [ssn](person/ssn.md)
  * [suffix](person/suffix.md)
* Thing
  * [animal](thing/animal.md)
* Mobile
  * [android_id](mobile/android_id.md)
  * [apple_token](mobile/apple_token.md)
  * [bb_pin](mobile/bb_pin.md)
  * [wp7_anid](mobile/wp7_anid.md)
  * [wp8_anid2](mobile/wp8_anid2.md)
* Web
  * [avatar](web/avatar.md)
  * [color](web/color.md)
  * [company](web/company.md)
  * [domain](web/domain.md)
  * [email](web/email.md)
  * [fbid](web/fbid.md)
  * [google_analytics](web/google_analytics.md)
  * [hashtag](web/hashtag.md)
  * [ip](web/ip.md)
  * [ipv6](web/ipv6.md)
  * [klout](web/klout.md)
  * [profession](web/profession.md)
  * [tld](web/tld.md)
  * [twitter](web/twitter.md)
  * [url](web/url.md)
* Location
  * [address](location/address.md)
  * [altitude](location/altitude.md)
  * [areacode](location/areacode.md)
  * [city](location/city.md)
  * [coordinates](location/coordinates.md)
  * [country](location/country.md)
  * [depth](location/depth.md)
  * [geohash](location/geohash.md)
  * [latitude](location/latitude.md)
  * [locale](location/locale.md)
  * [longitude](location/longitude.md)
  * [phone](location/phone.md)
  * [postal](location/postal.md)
  * [postcode](location/postcode.md)
  * [province](location/province.md)
  * [state](location/state.md)
  * [street](location/street.md)
  * [zip](location/zip.md)
* Time
  * [ampm](time/ampm.md)
  * [date](time/date.md)
  * [hammertime](time/hammertime.md)
  * [hour](time/hour.md)
  * [millisecond](time/millisecond.md)
  * [minute](time/minute.md)
  * [month](time/month.md)
  * [second](time/second.md)
  * [timestamp](time/timestamp.md)
  * [timezone](time/timezone.md)
  * [weekday](time/weekday.md)
  * [year](time/year.md)
* Finance
  * [cc](finance/cc.md)
  * [cc_type](finance/cc_type.md)
  * [currency](finance/currency.md)
  * [currency_pair](finance/currency_pair.md)
  * [dollar](finance/dollar.md)
  * [euro](finance/euro.md)
  * [exp](finance/exp.md)
  * [exp_month](finance/exp_month.md)
  * [exp_year](finance/exp_year.md)
* Helpers
  * [capitalize](helpers/capitalize.md)
  * [mixin](helpers/mixin.md)
  * [pad](helpers/pad.md)
  * [pick](helpers/pick.md)
  * [pickone](helpers/pickone.md)
  * [pickset](helpers/pickset.md)
  * [set](helpers/set.md)
  * [shuffle](helpers/shuffle.md)
* Miscellaneous
  * [coin](miscellaneous/coin.md)
  * [dice](miscellaneous/dice.md)
  * [guid](miscellaneous/guid.md)
  * [hash](miscellaneous/hash.md)
  * [hidden](miscellaneous/hidden.md)
  * [n](miscellaneous/n.md)
  * [normal](miscellaneous/normal.md)
  * [radio](miscellaneous/radio.md)
  * [rpg](miscellaneous/rpg.md)
  * [tv](miscellaneous/tv.md)
  * [unique](miscellaneous/unique.md)
  * [weighted](miscellaneous/weighted.md)

<script type="text/javascript" src="chance.js"></script>
