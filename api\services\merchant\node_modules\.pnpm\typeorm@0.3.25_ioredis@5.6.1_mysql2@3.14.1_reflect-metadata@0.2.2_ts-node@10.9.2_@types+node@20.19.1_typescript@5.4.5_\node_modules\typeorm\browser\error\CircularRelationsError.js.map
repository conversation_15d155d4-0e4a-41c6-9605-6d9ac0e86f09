{"version": 3, "sources": ["../browser/src/error/CircularRelationsError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,sBAAuB,SAAQ,YAAY;IACpD,YAAY,IAAY;QACpB,KAAK,CACD,gCAAgC,IAAI,sCAAsC;YACtE,4DAA4D,CACnE,CAAA;IACL,CAAC;CACJ", "file": "CircularRelationsError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when circular relations detected with nullable set to false.\n */\nexport class CircularRelationsError extends TypeORMError {\n    constructor(path: string) {\n        super(\n            `Circular relations detected: ${path}. To resolve this issue you need to ` +\n                `set nullable: true somewhere in this dependency structure.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}