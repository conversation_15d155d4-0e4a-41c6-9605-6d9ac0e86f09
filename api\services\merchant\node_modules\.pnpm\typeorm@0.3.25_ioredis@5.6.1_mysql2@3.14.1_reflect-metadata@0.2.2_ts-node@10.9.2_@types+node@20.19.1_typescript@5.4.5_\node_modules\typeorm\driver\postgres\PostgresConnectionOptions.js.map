{"version": 3, "sources": ["../../src/driver/postgres/PostgresConnectionOptions.ts"], "names": [], "mappings": "", "file": "PostgresConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { PostgresConnectionCredentialsOptions } from \"./PostgresConnectionCredentialsOptions\"\n\n/**\n * Postgres-specific connection options.\n */\nexport interface PostgresConnectionOptions\n    extends BaseDataSourceOptions,\n        PostgresConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"postgres\"\n\n    /**\n     * Schema name.\n     */\n    readonly schema?: string\n\n    /**\n     * The driver object\n     * This defaults to `require(\"pg\")`.\n     */\n    readonly driver?: any\n\n    /**\n     * The driver object\n     * This defaults to `require(\"pg-native\")`.\n     */\n    readonly nativeDriver?: any\n\n    /**\n     * A boolean determining whether to pass time values in UTC or local time. (default: false).\n     */\n    readonly useUTC?: boolean\n\n    /**\n     * Replication setup.\n     */\n    readonly replication?: {\n        /**\n         * Master server used by orm to perform writes.\n         */\n        readonly master: PostgresConnectionCredentialsOptions\n\n        /**\n         * List of read-from servers (slaves).\n         */\n        readonly slaves: PostgresConnectionCredentialsOptions[]\n\n        /**\n         * Default connection pool to use for SELECT queries\n         * @default \"slave\"\n         */\n        readonly defaultMode?: ReplicationMode\n    }\n\n    /**\n     * The milliseconds before a timeout occurs during the initial connection to the postgres\n     * server. If undefined, or set to 0, there is no timeout. Defaults to undefined.\n     */\n    readonly connectTimeoutMS?: number\n\n    /**\n     * The Postgres extension to use to generate UUID columns. Defaults to uuid-ossp.\n     * If pgcrypto is selected, TypeORM will use the gen_random_uuid() function from this extension.\n     * If uuid-ossp is selected, TypeORM will use the uuid_generate_v4() function from this extension.\n     */\n    readonly uuidExtension?: \"pgcrypto\" | \"uuid-ossp\"\n\n    /*\n     * Function handling errors thrown by drivers pool.\n     * Defaults to logging error with `warn` level.\n     */\n    readonly poolErrorHandler?: (err: any) => any\n\n    /**\n     * Include notification messages from Postgres server in client logs\n     */\n    readonly logNotifications?: boolean\n\n    /**\n     * Automatically install postgres extensions\n     */\n    readonly installExtensions?: boolean\n\n    /**\n     * Return 64-bit integers (int8) as JavaScript integers.\n     *\n     * Because JavaScript doesn't have support for 64-bit integers node-postgres cannot confidently\n     * parse int8 data type results as numbers because if you have a huge number it will overflow\n     * and the result you'd get back from node-postgres would not be the result in the database.\n     * That would be a very bad thing so node-postgres just returns int8 results as strings and leaves the parsing up to you.\n     *\n     * Enabling parseInt8 will cause node-postgres to parse int8 results as numbers.\n     * Note: the maximum safe integer in js is: Number.MAX_SAFE_INTEGER (`+2^53`)\n     *\n     * @see [JavaScript Number objects](http://ecma262-5.com/ELS5_HTML.htm#Section_8.5)\n     * @see [node-postgres int8 explanation](https://github.com/brianc/node-pg-types#:~:text=on%20projects%3A%20return-,64%2Dbit%20integers,-(int8)%20as)\n     * @see [node-postgres defaults.parseInt8 implementation](https://github.com/brianc/node-postgres/blob/pg%408.8.0/packages/pg/lib/defaults.js#L80)\n     */\n    readonly parseInt8?: boolean\n}\n"], "sourceRoot": "../.."}