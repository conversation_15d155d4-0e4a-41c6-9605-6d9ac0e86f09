import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 商户系统角色
 */
@Entity('merchant_sys_role')
export class MerchantSysRoleEntity extends BaseEntity {
  @Column({ comment: '角色名称' })
  name: string;

  @Column({ comment: '角色标签' })
  label: string;

  @Column({ comment: '角色备注', nullable: true })
  remark: string;

  @Column({ comment: '状态 0:禁用 1：启用', default: 1 })
  status: number;

  @Column({ comment: '排序', default: 0 })
  relevance: number;

  @Column({ comment: '菜单权限', type: 'simple-array' })
  menuIdList: number[];

  @Column({ comment: '部门权限', type: 'simple-array' })
  departmentIdList: number[];
} 