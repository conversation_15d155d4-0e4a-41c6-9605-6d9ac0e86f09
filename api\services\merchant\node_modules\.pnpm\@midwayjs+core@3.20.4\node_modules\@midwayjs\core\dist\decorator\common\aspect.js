"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Aspect = void 0;
const __1 = require("../");
const objectDef_1 = require("./objectDef");
const interface_1 = require("../../interface");
function Aspect(aspectTarget, match, priority) {
    return function (target) {
        (0, __1.saveModule)(__1.ASPECT_KEY, target);
        const aspectTargets = [].concat(aspectTarget);
        for (const aspectTarget of aspectTargets) {
            (0, __1.attachClassMetadata)(__1.ASPECT_KEY, {
                aspectTarget,
                match,
                priority,
            }, target);
        }
        (0, objectDef_1.Scope)(interface_1.ScopeEnum.Singleton)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Aspect = Aspect;
//# sourceMappingURL=aspect.js.map