<template>
  <div class="internationalization-page">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <h3>国际化管理</h3>
          <p>配置多币种结算和国际化设置</p>
        </div>
      </template>

      <el-tabs v-model="activeTab" class="intl-tabs">
        <!-- 币种管理 -->
        <el-tab-pane label="币种管理" name="currency">
          <div class="currency-section">
            <div class="section-header">
              <h4>基础币种设置</h4>
              <el-button type="primary" size="small" @click="showCurrencyDialog = true">
                添加币种
              </el-button>
            </div>

            <el-form :model="currencySettings" label-width="120px" class="settings-form">
              <el-row :gutter="24">
                <el-col :span="12">
                  <el-form-item label="基础币种">
                    <el-select v-model="currencySettings.baseCurrency" style="width: 100%">
                      <el-option label="人民币 (CNY)" value="CNY" />
                      <el-option label="美元 (USD)" value="USD" />
                      <el-option label="欧元 (EUR)" value="EUR" />
                      <el-option label="日元 (JPY)" value="JPY" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="汇率更新频率">
                    <el-select v-model="currencySettings.exchangeRateFrequency" style="width: 100%">
                      <el-option label="实时" value="realtime" />
                      <el-option label="每小时" value="hourly" />
                      <el-option label="每日" value="daily" />
                      <el-option label="手动" value="manual" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="汇率数据源">
                <el-select v-model="currencySettings.exchangeRateSource" style="width: 300px">
                  <el-option label="中国银行" value="boc" />
                  <el-option label="央行汇率" value="pboc" />
                  <el-option label="第三方接口" value="api" />
                </el-select>
              </el-form-item>
            </el-form>

            <!-- 支持币种列表 -->
            <div class="currency-list">
              <h4>支持币种</h4>
              <el-table :data="supportedCurrencies" style="width: 100%">
                <el-table-column prop="code" label="币种代码" width="100" />
                <el-table-column prop="name" label="币种名称" width="150" />
                <el-table-column prop="symbol" label="符号" width="80" />
                <el-table-column prop="rate" label="汇率" width="120">
                  <template #default="{ row }">
                    {{ row.rate.toFixed(4) }}
                  </template>
                </el-table-column>
                <el-table-column prop="updateTime" label="更新时间" width="180" />
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'">
                      {{ row.status === 'active' ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="150">
                  <template #default="{ row }">
                    <el-button size="small" @click="refreshRate(row)">刷新汇率</el-button>
                    <el-button 
                      size="small" 
                      :type="row.status === 'active' ? 'danger' : 'success'"
                      @click="toggleCurrencyStatus(row)"
                    >
                      {{ row.status === 'active' ? '禁用' : '启用' }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 地区设置 -->
        <el-tab-pane label="地区设置" name="region">
          <el-form :model="regionSettings" label-width="120px" class="settings-form">
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="默认时区">
                  <el-select v-model="regionSettings.defaultTimezone" style="width: 100%">
                    <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
                    <el-option label="东京时间 (UTC+9)" value="Asia/Tokyo" />
                    <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
                    <el-option label="伦敦时间 (UTC+0)" value="Europe/London" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="跨境结算">
                  <el-switch v-model="regionSettings.crossBorderSettlement" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="支持地区">
              <el-checkbox-group v-model="regionSettings.supportedRegions">
                <el-checkbox label="CN" value="CN">中国大陆</el-checkbox>
                <el-checkbox label="HK" value="HK">香港</el-checkbox>
                <el-checkbox label="TW" value="TW">台湾</el-checkbox>
                <el-checkbox label="US" value="US">美国</el-checkbox>
                <el-checkbox label="EU" value="EU">欧盟</el-checkbox>
                <el-checkbox label="JP" value="JP">日本</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="支持语言">
              <el-checkbox-group v-model="regionSettings.supportedLanguages">
                <el-checkbox label="zh-CN" value="zh-CN">简体中文</el-checkbox>
                <el-checkbox label="zh-TW" value="zh-TW">繁体中文</el-checkbox>
                <el-checkbox label="en-US" value="en-US">英语</el-checkbox>
                <el-checkbox label="ja-JP" value="ja-JP">日语</el-checkbox>
                <el-checkbox label="ko-KR" value="ko-KR">韩语</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 汇率监控 -->
        <el-tab-pane label="汇率监控" name="monitoring">
          <div class="rate-monitoring">
            <div class="monitoring-header">
              <h4>汇率监控</h4>
              <el-button type="primary" @click="refreshAllRates" :loading="refreshing">
                刷新所有汇率
              </el-button>
            </div>

            <!-- 汇率图表 -->
            <div class="rate-chart">
              <el-card shadow="never">
                <template #header>
                  <span>汇率趋势图</span>
                </template>
                <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
                  汇率趋势图表区域
                </div>
              </el-card>
            </div>

            <!-- 汇率预警 -->
            <div class="rate-alerts">
              <h4>汇率预警设置</h4>
              <el-table :data="rateAlerts" style="width: 100%">
                <el-table-column prop="currency" label="币种" width="100" />
                <el-table-column prop="threshold" label="预警阈值" width="120">
                  <template #default="{ row }">
                    ±{{ row.threshold }}%
                  </template>
                </el-table-column>
                <el-table-column prop="enabled" label="状态" width="100">
                  <template #default="{ row }">
                    <el-switch v-model="row.enabled" />
                  </template>
                </el-table-column>
                <el-table-column prop="lastAlert" label="最后预警" />
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button size="small" @click="editAlert(row)">编辑</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div class="form-actions">
        <el-button @click="resetSettings">重置</el-button>
        <el-button type="primary" @click="saveSettings" :loading="saving">
          保存配置
        </el-button>
      </div>
    </el-card>

    <!-- 添加币种对话框 -->
    <el-dialog v-model="showCurrencyDialog" title="添加币种" width="500px">
      <el-form :model="newCurrency" label-width="100px">
        <el-form-item label="币种代码">
          <el-input v-model="newCurrency.code" placeholder="如: USD" />
        </el-form-item>
        <el-form-item label="币种名称">
          <el-input v-model="newCurrency.name" placeholder="如: 美元" />
        </el-form-item>
        <el-form-item label="符号">
          <el-input v-model="newCurrency.symbol" placeholder="如: $" />
        </el-form-item>
        <el-form-item label="初始汇率">
          <el-input-number v-model="newCurrency.rate" :precision="4" :min="0" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showCurrencyDialog = false">取消</el-button>
        <el-button type="primary" @click="addCurrency">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useSettlementStore } from '@/stores/settlement'

defineOptions({ name: 'Internationalization' })

const settlementStore = useSettlementStore()
const activeTab = ref('currency')
const saving = ref(false)
const refreshing = ref(false)
const showCurrencyDialog = ref(false)

// 币种设置
const currencySettings = reactive({
  baseCurrency: 'CNY',
  exchangeRateFrequency: 'daily',
  exchangeRateSource: 'boc'
})

// 地区设置
const regionSettings = reactive({
  defaultTimezone: 'Asia/Shanghai',
  supportedRegions: ['CN'],
  supportedLanguages: ['zh-CN'],
  crossBorderSettlement: false
})

// 支持的币种列表
const supportedCurrencies = ref([
  {
    code: 'CNY',
    name: '人民币',
    symbol: '¥',
    rate: 1.0000,
    updateTime: '2024-01-20 10:30:00',
    status: 'active'
  },
  {
    code: 'USD',
    name: '美元',
    symbol: '$',
    rate: 0.1389,
    updateTime: '2024-01-20 10:30:00',
    status: 'active'
  },
  {
    code: 'EUR',
    name: '欧元',
    symbol: '€',
    rate: 0.1276,
    updateTime: '2024-01-20 10:30:00',
    status: 'active'
  }
])

// 汇率预警
const rateAlerts = ref([
  {
    currency: 'USD',
    threshold: 5,
    enabled: true,
    lastAlert: '2024-01-19 15:20:00'
  },
  {
    currency: 'EUR',
    threshold: 3,
    enabled: false,
    lastAlert: '-'
  }
])

// 新币种表单
const newCurrency = reactive({
  code: '',
  name: '',
  symbol: '',
  rate: 0
})

// 刷新汇率
const refreshRate = async (currency: any) => {
  try {
    // 模拟刷新汇率
    currency.rate = (Math.random() * 0.2 + 0.1).toFixed(4)
    currency.updateTime = new Date().toLocaleString()
    ElMessage.success(`${currency.name} 汇率刷新成功`)
  } catch (error) {
    ElMessage.error('汇率刷新失败')
  }
}

// 刷新所有汇率
const refreshAllRates = async () => {
  refreshing.value = true
  try {
    for (const currency of supportedCurrencies.value) {
      if (currency.status === 'active') {
        await refreshRate(currency)
      }
    }
    ElMessage.success('所有汇率刷新完成')
  } finally {
    refreshing.value = false
  }
}

// 切换币种状态
const toggleCurrencyStatus = (currency: any) => {
  currency.status = currency.status === 'active' ? 'inactive' : 'active'
  ElMessage.success(`${currency.name} 已${currency.status === 'active' ? '启用' : '禁用'}`)
}

// 添加币种
const addCurrency = () => {
  if (!newCurrency.code || !newCurrency.name) {
    ElMessage.error('请填写完整信息')
    return
  }
  
  supportedCurrencies.value.push({
    ...newCurrency,
    updateTime: new Date().toLocaleString(),
    status: 'active'
  })
  
  // 重置表单
  Object.assign(newCurrency, {
    code: '',
    name: '',
    symbol: '',
    rate: 0
  })
  
  showCurrencyDialog.value = false
  ElMessage.success('币种添加成功')
}

// 编辑预警
const editAlert = (alert: any) => {
  ElMessage.info('编辑预警功能开发中')
}

// 保存设置
const saveSettings = async () => {
  saving.value = true
  try {
    // 合并设置到 store
    Object.assign(settlementStore.currencySettings, currencySettings)
    Object.assign(settlementStore.regionSettings, regionSettings)
    
    await settlementStore.saveSettings()
    ElMessage.success('国际化配置保存成功')
  } catch (error) {
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saving.value = false
  }
}

// 重置设置
const resetSettings = () => {
  settlementStore.resetSettings()
  ElMessage.info('配置已重置')
}

// 加载设置
onMounted(() => {
  settlementStore.loadSettings()
})
</script>

<style scoped lang="scss">
.internationalization-page {
  .card-header {
    h3 {
      margin: 0 0 8px 0;
      color: var(--el-text-color-primary);
    }
    
    p {
      margin: 0;
      color: var(--el-text-color-regular);
      font-size: 14px;
    }
  }
  
  .settings-form {
    margin-top: 20px;
  }
  
  .currency-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h4 {
        margin: 0;
      }
    }
    
    .currency-list {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 16px;
      }
    }
  }
  
  .rate-monitoring {
    .monitoring-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h4 {
        margin: 0;
      }
    }
    
    .rate-chart {
      margin: 20px 0;
    }
    
    .rate-alerts {
      margin-top: 30px;
      
      h4 {
        margin-bottom: 16px;
      }
    }
  }
  
  .form-actions {
    margin-top: 30px;
    text-align: right;
    border-top: 1px solid var(--el-border-color-light);
    padding-top: 20px;
  }
}

.intl-tabs {
  :deep(.el-tabs__content) {
    padding-top: 20px;
  }
}
</style>
