{"version": 3, "file": "IsLatitude.js", "sourceRoot": "", "sources": ["../../../../src/decorator/common/IsLatitude.ts"], "names": [], "mappings": ";;;AACA,6CAAwD;AACxD,2CAAwC;AAE3B,QAAA,WAAW,GAAG,YAAY,CAAC;AAExC;;GAEG;AACH,SAAgB,UAAU,CAAC,KAAa;IACtC,OAAO,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,IAAI,IAAA,qBAAS,EAAC,GAAG,KAAK,IAAI,CAAC,CAAC;AAC7F,CAAC;AAFD,gCAEC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,iBAAqC;IAC9D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,mBAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC;YACrD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,+CAA+C,EAC1E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,gCAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from './ValidateBy';\nimport { isLatLong } from './IsLatLong';\n\nexport const IS_LATITUDE = 'isLatitude';\n\n/**\n * Checks if a given value is a latitude.\n */\nexport function isLatitude(value: string): boolean {\n  return (typeof value === 'number' || typeof value === 'string') && isLatLong(`${value},0`);\n}\n\n/**\n * Checks if a given value is a latitude.\n */\nexport function IsLatitude(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_LATITUDE,\n      validator: {\n        validate: (value, args): boolean => isLatitude(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a latitude string or number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}