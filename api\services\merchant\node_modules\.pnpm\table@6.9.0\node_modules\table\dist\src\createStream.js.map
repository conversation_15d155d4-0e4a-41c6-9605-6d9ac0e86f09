{"version": 3, "file": "createStream.js", "sourceRoot": "", "sources": ["../../src/createStream.ts"], "names": [], "mappings": ";;;AAAA,qDAE0B;AAC1B,+DAE+B;AAC/B,6CAIsB;AACtB,uCAEmB;AACnB,yDAE4B;AAC5B,qEAEkC;AAClC,iDAEwB;AACxB,6DAE8B;AAC9B,2DAE6B;AAQ7B,mCAEiB;AAEjB,MAAM,WAAW,GAAG,CAAC,IAAW,EAAE,MAAoB,EAAE,EAAE;IACxD,IAAI,IAAI,GAAG,IAAA,uCAAkB,EAAC,IAAI,CAAC,CAAC;IAEpC,IAAI,GAAG,IAAA,qCAAiB,EAAC,IAAI,EAAE,IAAA,wBAAgB,EAAC,MAAM,CAAC,CAAC,CAAC;IAEzD,MAAM,UAAU,GAAG,IAAA,yCAAmB,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAErD,IAAI,GAAG,IAAA,+CAAsB,EAAC,IAAI,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACxD,IAAI,GAAG,IAAA,+BAAc,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IACpC,IAAI,GAAG,IAAA,2BAAY,EAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAElC,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,MAAM,GAAG,CAAC,GAAQ,EAAE,YAAsB,EAAE,MAAoB,EAAE,EAAE;IACxE,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;IAExC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;QACnC,OAAO,IAAA,iBAAO,EAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEZ,IAAI,MAAM,CAAC;IAEX,MAAM,GAAG,EAAE,CAAC;IAEZ,MAAM,IAAI,IAAA,0BAAa,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC9C,MAAM,IAAI,IAAI,CAAC;IACf,MAAM,IAAI,IAAA,6BAAgB,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAEjD,MAAM,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAE1B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEF,MAAM,MAAM,GAAG,CAAC,GAAQ,EAAE,YAAsB,EAAE,MAAoB,EAAE,EAAE;IACxE,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC;IAExC,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;QACnC,OAAO,IAAA,iBAAO,EAAC,UAAU,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAEZ,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,MAAM,MAAM,GAAG,IAAA,6BAAgB,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAEtD,IAAI,MAAM,KAAK,IAAI,EAAE;QACnB,MAAM,GAAG,YAAY,CAAC;KACvB;IAED,MAAM,IAAI,IAAA,2BAAc,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAC/C,MAAM,IAAI,IAAI,CAAC;IACf,MAAM,IAAI,MAAM,CAAC;IAEjB,MAAM,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;IAE1B,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC/B,CAAC,CAAC;AAEK,MAAM,YAAY,GAAG,CAAC,UAA4B,EAAkB,EAAE;IAC3E,MAAM,MAAM,GAAG,IAAA,mCAAgB,EAAC,UAAU,CAAC,CAAC;IAE5C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAChE,OAAO,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,YAAY,CAAC;IACjE,CAAC,CAAC,CAAC;IAEH,IAAI,KAAK,GAAG,IAAI,CAAC;IAEjB,OAAO;QACL,KAAK,EAAE,CAAC,GAAa,EAAE,EAAE;YACvB,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,WAAW,EAAE;gBACrC,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;aAC1E;YAED,IAAI,KAAK,EAAE;gBACT,KAAK,GAAG,KAAK,CAAC;gBAEd,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;aACnC;iBAAM;gBACL,MAAM,CAAC,GAAG,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;aACnC;QACH,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAxBW,QAAA,YAAY,gBAwBvB"}