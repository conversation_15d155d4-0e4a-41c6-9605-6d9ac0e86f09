# Redis store for node cache manager

[![codecov](https://codecov.io/gh/jaredwray/cacheable/graph/badge.svg?token=lWZ9OBQ7GM)](https://codecov.io/gh/jaredwray/cacheable)
[![tests](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml/badge.svg)](https://github.com/jaredwray/cacheable/actions/workflows/tests.yml)
[![license](https://img.shields.io/github/license/jaredwray/cacheable)](https://github.com/jaredwray/cacheable/blob/main/LICENSE)
[![npm](https://img.shields.io/npm/dm/cache-manager-ioredis-yet)](https://npmjs.com/package/cache-manager-ioredis-yet)
![npm](https://img.shields.io/npm/v/cache-manager-ioredis-yet)

Redis cache store for [node-cache-manager](https://github.com/jaredwray/cacheable).

## Installation

```sh
pnpm install cache-manager-ioredis-yet
```

## License

Licensed under the [MIT license](./LICENSE).

Fork from [https://github.com/dabroek/node-cache-manager-redis-store](https://github.com/dabroek/node-cache-manager-redis-store)
