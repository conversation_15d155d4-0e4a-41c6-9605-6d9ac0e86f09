"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayI18nService = exports.MidwayI18nServiceSingleton = void 0;
const core_1 = require("@midwayjs/core");
const interface_1 = require("./interface");
const pm = require("picomatch");
const utils_1 = require("./utils");
let MidwayI18nServiceSingleton = class MidwayI18nServiceSingleton {
    constructor() {
        this.localeTextMap = new Map();
        this.fallbackMatch = [];
        this.localeMatchCache = {};
    }
    async init() {
        this.defaultLocale = (0, utils_1.formatLocale)(this.i18nConfig.defaultLocale);
        for (const lang in this.i18nConfig.localeTable) {
            this.addLocale(lang, getES6Object(this.i18nConfig.localeTable[lang]));
        }
        // fallbacks
        for (const rule in this.i18nConfig.fallbacks) {
            this.fallbackMatch.push({
                pattern: pm((0, utils_1.formatLocale)(rule)),
                locale: (0, utils_1.formatLocale)(this.i18nConfig.fallbacks[rule]),
            });
        }
    }
    /**
     * add a language text mapping
     * @param locale
     * @param localeTextMapping
     */
    addLocale(locale, localeTextMapping) {
        const currentLangMap = getMap(this.localeTextMap, locale, true);
        for (const key in localeTextMapping) {
            if (typeof localeTextMapping[key] === 'string') {
                // set to default
                getMap(currentLangMap, 'default').set(key, localeTextMapping[key]);
            }
            else {
                // set to group
                for (const newKey in localeTextMapping[key]) {
                    getMap(currentLangMap, key).set(newKey, localeTextMapping[key][newKey]);
                }
            }
        }
    }
    /**
     * translate a message
     * @param message
     * @param options
     */
    translate(message, options = {}) {
        var _a, _b, _c;
        const useLocale = (0, utils_1.formatLocale)((_a = options.locale) !== null && _a !== void 0 ? _a : this.defaultLocale);
        const args = (_b = options.args) !== null && _b !== void 0 ? _b : [];
        const group = (_c = options.group) !== null && _c !== void 0 ? _c : 'default';
        let msg = this.getLocaleMappingText(useLocale, message, group, args);
        if (!msg && useLocale !== this.defaultLocale) {
            if (this.fallbackMatch.length) {
                const findRule = this.fallbackMatch.find(rule => {
                    if (rule.pattern(useLocale)) {
                        return true;
                    }
                });
                if (findRule) {
                    msg = this.getLocaleMappingText(findRule.locale, message, group, args);
                }
            }
            if (!msg) {
                msg = this.getLocaleMappingText(this.defaultLocale, message, group, args);
            }
        }
        return msg;
    }
    /**
     * get locale string by find fallback and default, ignore match message
     * @param locale
     * @param group
     */
    getAvailableLocale(locale, group = 'default') {
        locale = (0, utils_1.formatLocale)(locale);
        if (this.localeMatchCache[locale + '_' + group]) {
            return this.localeMatchCache[locale + '_' + group];
        }
        if (this.localeTextMap.has(locale) &&
            this.localeTextMap.get(locale).has(group)) {
            this.localeMatchCache[locale + '_' + group] = locale;
            return locale;
        }
        if (this.fallbackMatch.length) {
            const findRule = this.fallbackMatch.find(rule => {
                if (rule.pattern(locale)) {
                    if (this.localeTextMap.has(rule.locale) &&
                        this.localeTextMap.get(rule.locale).has(group)) {
                        this.localeMatchCache[locale + '_' + group] = rule.locale;
                        return true;
                    }
                }
            });
            if (findRule) {
                return findRule.locale;
            }
        }
        this.localeMatchCache[locale + '_' + group] = this.defaultLocale;
        return this.defaultLocale;
    }
    /**
     * get available local in locale text map, include fallbacks
     * @param locale
     */
    hasAvailableLocale(locale) {
        locale = (0, utils_1.formatLocale)(locale);
        if (this.localeTextMap.has(locale)) {
            return true;
        }
        if (this.fallbackMatch.length) {
            const findRule = this.fallbackMatch.find(rule => {
                if (rule.pattern(locale)) {
                    if (this.localeTextMap.has(rule.locale)) {
                        return true;
                    }
                }
            });
            if (findRule) {
                return true;
            }
        }
        return false;
    }
    /**
     * get mapping by locale
     * @param locale
     * @param group
     */
    getLocaleMapping(locale, group = 'default') {
        locale = (0, utils_1.formatLocale)(locale);
        const langMap = this.localeTextMap.get(locale);
        if (langMap) {
            return langMap.get(group);
        }
    }
    /**
     * get current default language
     */
    getDefaultLocale() {
        return this.defaultLocale;
    }
    getLocaleMappingText(locale, message, group, args) {
        const langMap = this.localeTextMap.get(locale);
        if (langMap) {
            const textMapping = langMap.get(group);
            if (textMapping) {
                const msg = textMapping.get(message);
                if (msg) {
                    return formatText(msg, args);
                }
            }
        }
    }
};
__decorate([
    (0, core_1.Config)('i18n'),
    __metadata("design:type", Object)
], MidwayI18nServiceSingleton.prototype, "i18nConfig", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MidwayI18nServiceSingleton.prototype, "init", null);
MidwayI18nServiceSingleton = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], MidwayI18nServiceSingleton);
exports.MidwayI18nServiceSingleton = MidwayI18nServiceSingleton;
let MidwayI18nService = class MidwayI18nService {
    translate(message, options = {}) {
        if (!options.locale) {
            options.locale = this.ctx.getAttr && this.ctx.getAttr(interface_1.I18N_ATTR_KEY);
        }
        return this.i18nServiceSingleton.translate(message, options);
    }
    /**
     * add a language text mapping
     * @param locale
     * @param localeTextMapping
     */
    addLocale(locale, localeTextMapping) {
        this.i18nServiceSingleton.addLocale(locale, localeTextMapping);
    }
    /**
     * get mapping by lang
     * @param locale
     * @param group
     */
    getLocaleMapping(locale, group = 'default') {
        return this.i18nServiceSingleton.getLocaleMapping(locale, group);
    }
    /**
     * get current default language
     */
    getDefaultLocale() {
        return this.i18nServiceSingleton.getDefaultLocale();
    }
    /**
     * save current context lang to flag, middleware will be set it to cookie
     */
    saveRequestLocale(locale) {
        var _a, _b;
        if (locale) {
            (_a = this.ctx) === null || _a === void 0 ? void 0 : _a.setAttr(interface_1.I18N_ATTR_KEY, (0, utils_1.formatLocale)(locale));
        }
        else {
            (_b = this.ctx) === null || _b === void 0 ? void 0 : _b.setAttr(interface_1.I18N_ATTR_KEY, (0, utils_1.formatLocale)(this.getDefaultLocale()));
        }
    }
    /**
     * get locale string by find fallback and default, ignore match message
     * @param locale
     * @param group
     */
    getAvailableLocale(locale, group = 'default') {
        return this.i18nServiceSingleton.getAvailableLocale(locale, group);
    }
    /**
     * get available local in locale text map, include fallbacks
     * @param locale
     */
    hasAvailableLocale(locale) {
        return this.i18nServiceSingleton.hasAvailableLocale(locale);
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", MidwayI18nServiceSingleton)
], MidwayI18nService.prototype, "i18nServiceSingleton", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", Object)
], MidwayI18nService.prototype, "ctx", void 0);
MidwayI18nService = __decorate([
    (0, core_1.Provide)()
], MidwayI18nService);
exports.MidwayI18nService = MidwayI18nService;
function formatText(message, args) {
    if (Array.isArray(args)) {
        return (0, utils_1.formatWithArray)(message, args);
    }
    else {
        return (0, utils_1.formatWithObject)(message, args);
    }
}
function getES6Object(o) {
    for (const key in o) {
        if (o[key]['default'] && Object.keys(o[key]).length === 1) {
            o[key] = o[key].default;
        }
    }
    return o;
}
function getMap(o, key, formatKey = false) {
    key = formatKey ? (0, utils_1.formatLocale)(key) : key;
    if (!o.has(key)) {
        o.set(key, new Map());
    }
    return o.get(key);
}
//# sourceMappingURL=i18nService.js.map