"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NestedSetMultipleRootError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class NestedSetMultipleRootError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Nested sets do not support multiple root entities.`);
    }
}
exports.NestedSetMultipleRootError = NestedSetMultipleRootError;

//# sourceMappingURL=NestedSetMultipleRootError.js.map
