"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeORMDataSourceManager = void 0;
const core_1 = require("@midwayjs/core");
const typeorm_1 = require("typeorm");
const logger_1 = require("./logger");
let TypeORMDataSourceManager = class TypeORMDataSourceManager extends core_1.DataSourceManager {
    async init() {
        await this.initDataSource(this.typeormConfig, this.baseDir);
    }
    getName() {
        return 'typeorm';
    }
    async createDataSource(config, dataSourceName) {
        if (config['migrations']) {
            delete config['migrations'];
        }
        if (config['logging'] === undefined) {
            config['logger'] = new logger_1.TypeORMLogger(this.loggerService.getLogger('typeormLogger'));
        }
        const dataSource = new typeorm_1.DataSource(config);
        await dataSource.initialize();
        return dataSource;
    }
    async checkConnected(dataSource) {
        return dataSource.isInitialized;
    }
    async destroyDataSource(dataSource) {
        if (dataSource.isInitialized) {
            await dataSource.destroy();
        }
    }
};
__decorate([
    (0, core_1.Config)('typeorm'),
    __metadata("design:type", Object)
], TypeORMDataSourceManager.prototype, "typeormConfig", void 0);
__decorate([
    (0, core_1.ApplicationContext)(),
    __metadata("design:type", Object)
], TypeORMDataSourceManager.prototype, "applicationContext", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", String)
], TypeORMDataSourceManager.prototype, "baseDir", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayLoggerService)
], TypeORMDataSourceManager.prototype, "loggerService", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TypeORMDataSourceManager.prototype, "init", null);
TypeORMDataSourceManager = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], TypeORMDataSourceManager);
exports.TypeORMDataSourceManager = TypeORMDataSourceManager;
//# sourceMappingURL=dataSourceManager.js.map