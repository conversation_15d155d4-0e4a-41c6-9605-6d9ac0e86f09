import { ValidationOptions } from '../ValidationOptions';
export declare const IS_ISRC = "isISRC";
/**
 * Check if the string is a ISRC.
 * If given value is not a string, then it returns false.
 */
export declare function isISRC(value: unknown): boolean;
/**
 * Check if the string is a ISRC.
 * If given value is not a string, then it returns false.
 */
export declare function IsISRC(validationOptions?: ValidationOptions): PropertyDecorator;
