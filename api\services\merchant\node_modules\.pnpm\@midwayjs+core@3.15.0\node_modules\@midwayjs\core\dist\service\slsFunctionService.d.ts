import { MidwayWebRouterService, RouterCollectorOptions, RouterInfo, RouterPriority } from './webRouterService';
import { FaaSMetadata } from '../interface';
export declare class MidwayServerlessFunctionService extends MidwayWebRouterService {
    readonly options: RouterCollectorOptions;
    constructor(options?: RouterCollectorOptions);
    protected analyze(): Promise<void>;
    protected analyzeFunction(): void;
    protected collectFunctionRoute(module: any): void;
    getFunctionList(): Promise<RouterInfo[]>;
    addServerlessFunction(func: (...args: any[]) => Promise<any>, triggerOptions: FaaSMetadata.TriggerMetadata, functionOptions?: FaaSMetadata.ServerlessFunctionOptions): void;
}
/**
 * @deprecated use built-in MidwayWebRouterService first
 */
export declare class WebRouterCollector {
    private baseDir;
    private options;
    private proxy;
    constructor(baseDir?: string, options?: RouterCollectorOptions);
    protected init(): Promise<void>;
    getRoutePriorityList(): Promise<RouterPriority[]>;
    getRouterTable(): Promise<Map<string, RouterInfo[]>>;
    getFlattenRouterTable(): Promise<RouterInfo[]>;
}
//# sourceMappingURL=slsFunctionService.d.ts.map