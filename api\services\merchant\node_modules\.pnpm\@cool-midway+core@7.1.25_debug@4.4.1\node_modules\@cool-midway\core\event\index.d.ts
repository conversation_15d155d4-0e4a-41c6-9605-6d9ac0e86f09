/// <reference types="node" />
import * as Events from "events";
import { IMidwayApplication } from "@midwayjs/core";
export declare const COOL_EVENT_MESSAGE = "cool:event:message";
/**
 * 事件
 */
export declare class CoolEventManager extends Events {
    app: IMidwayApplication;
    keys: string;
    eventData: {
        [key: string]: {
            module: any;
            method: string;
        }[];
    };
    /**
     * 初始化
     */
    init(): Promise<void>;
    /**
     * 发送事件
     * @param event
     * @param args
     * @returns
     */
    emit(event: string | symbol, ...args: any[]): boolean;
    /**
     * 发送全局事件
     * @param event 事件
     * @param random 是否随机一个
     * @param args 参数
     * @returns
     */
    globalEmit(event: string, random?: boolean, ...args: any[]): Promise<void>;
    /**
     * 处理事件
     * @param module
     */
    handlerEvent(module: any): Promise<void>;
    /**
     * 全局事件
     */
    globalEvent(): Promise<void>;
    /**
     * 普通事件
     */
    commEvent(): Promise<void>;
    /**
     * 执行事件
     * @param message
     */
    doAction(message: any): Promise<void>;
}
