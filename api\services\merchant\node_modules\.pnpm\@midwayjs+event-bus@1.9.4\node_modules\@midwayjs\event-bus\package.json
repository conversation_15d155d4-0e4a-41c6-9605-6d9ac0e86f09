{"name": "@midwayjs/event-bus", "version": "1.9.4", "description": "Midway event bus", "main": "dist/index", "typings": "dist/index.d.ts", "scripts": {"build": "rm -rf dist && tsc", "test": "NODE_DEBUG=midway* jest --runInBand", "cov": "jest --coverage", "lint": "mwts lint", "lint:fix": "mwts fix"}, "keywords": ["midway", "event-bus"], "files": ["dist/**/*.js", "dist/**/*.d.ts"], "engines": {"node": ">=12.11.0"}, "license": "MIT", "devDependencies": {"@types/jest": "^29.2.0", "@types/node": "^14.18.32", "benchmark": "^2.1.4", "jest": "^29.2.2", "mwts": "^1.3.0", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.8.4"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/midwayjs/hub.git"}}