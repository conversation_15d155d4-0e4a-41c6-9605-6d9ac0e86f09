{"version": 3, "sources": ["../browser/src/find-options/operator/Or.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C,MAAM,UAAU,EAAE,CAAI,GAAG,MAAyB;IAC9C,OAAO,IAAI,YAAY,CAAC,IAAI,EAAE,MAAa,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAC5D,CAAC", "file": "Or.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\nexport function Or<T>(...values: FindOperator<T>[]): FindOperator<T> {\n    return new FindOperator(\"or\", values as any, true, true)\n}\n"], "sourceRoot": "../.."}