{"processes": {"c19faffc-964b-4370-81a6-9b4942f3c35e": {"parent": null, "children": []}}, "files": {"/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/decorator_utils.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/error_msgs.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/metadata_keys.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/js.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/inversify.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/bindings/binding.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/constants/literal_types.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/id.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/metadata_reader.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/planner.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/bindings/binding_count.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/exceptions.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/serialization.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/context.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/metadata.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/plan.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/reflection_utils.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/lazy_service_identifier.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/target.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/queryable_string.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/planning/request.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/resolution/resolver.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/scope/scope.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/async.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/binding_utils.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/factory_type.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/resolution/instantiation.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_to_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_in_when_on_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_in_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_when_on_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_on_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/binding_when_syntax.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/syntax/constraint_helpers.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container_snapshot.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/lookup.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/utils/clonable.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/module_activation_store.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/container/container_module.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/injectable.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/tagged.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/named.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/inject.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/inject_base.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/optional.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/unmanaged.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/multi_inject.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/target_name.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/post_construct.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/property_event_decorator.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/annotation/pre_destroy.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"], "/Users/<USER>/jsProjects/inversify/InversifyJS/src/interfaces/interfaces.ts": ["c19faffc-964b-4370-81a6-9b4942f3c35e"]}, "externalIds": {}}