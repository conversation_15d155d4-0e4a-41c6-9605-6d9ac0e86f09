"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Match = exports.Catch = void 0;
const decoratorManager_1 = require("../decoratorManager");
const constant_1 = require("../constant");
const objectDef_1 = require("./objectDef");
const provide_1 = require("./provide");
const interface_1 = require("../../interface");
function Catch(catchTarget, options = {}) {
    return function (target) {
        const catchTargets = catchTarget ? [].concat(catchTarget) : undefined;
        (0, decoratorManager_1.saveClassMetadata)(constant_1.CATCH_KEY, {
            catchTargets,
            catchOptions: options,
        }, target);
        (0, objectDef_1.Scope)(interface_1.ScopeEnum.Singleton)(target);
        (0, provide_1.Provide)()(target);
    };
}
exports.Catch = Catch;
function Match(matchPattern = true) {
    return function (target) {
        (0, decoratorManager_1.saveClassMetadata)(constant_1.MATCH_KEY, {
            matchPattern,
        }, target);
        (0, objectDef_1.Scope)(interface_1.ScopeEnum.Singleton)(target);
        (0, provide_1.Provide)()(target);
    };
}
exports.Match = Match;
//# sourceMappingURL=filter.js.map