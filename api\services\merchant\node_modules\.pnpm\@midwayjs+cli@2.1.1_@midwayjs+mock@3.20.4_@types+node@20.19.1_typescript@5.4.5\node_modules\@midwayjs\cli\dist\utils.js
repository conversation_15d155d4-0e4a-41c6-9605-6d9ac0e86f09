"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkUpdate = void 0;
const mod_info_1 = require("mod-info");
const checkUpdate = async (npm) => {
    var _a;
    try {
        const pkg = require('../package.json');
        const info = await (0, mod_info_1.default)(pkg.name, pkg.version, {
            level: ['minor', 'patch'],
        });
        if (info.update) {
            console.log();
            console.log('*********************************************************');
            console.log();
            console.log('   find new version:');
            console.log(`   ${pkg.version} ==> ${info.version}`);
            console.log();
            console.log('   please reinstall @midwayjs/cli module to update.');
            console.log();
            console.log('   npm i @midwayjs/cli');
            console.log();
            console.log('*********************************************************');
            if ((_a = info.tips) === null || _a === void 0 ? void 0 : _a.length) {
                console.log(' Some tips:');
                for (const tip of info.tips) {
                    console.log(`  ${tip}`);
                }
                console.log('*********************************************************');
            }
            console.log();
        }
    }
    catch (err) {
        console.log('[ Midway ] check update error and skip', err.message);
    }
};
exports.checkUpdate = checkUpdate;
//# sourceMappingURL=utils.js.map