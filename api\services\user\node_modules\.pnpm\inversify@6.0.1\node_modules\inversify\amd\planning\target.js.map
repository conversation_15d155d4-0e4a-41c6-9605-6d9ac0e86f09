{"version": 3, "file": "target.js", "sourceRoot": "", "sources": ["../../src/planning/target.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAOA;QAUE,gBACE,IAA2B,EAC3B,UAA2B,EAC3B,iBAA+C,EAC/C,aAAmC;YAGnC,IAAI,CAAC,EAAE,GAAG,IAAA,OAAE,GAAE,CAAC;YACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;YAC3C,IAAM,aAAa,GAAG,OAAO,UAAU,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAA,oCAAoB,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YACrG,IAAI,CAAC,IAAI,GAAG,IAAI,kCAAe,CAAC,aAAa,IAAI,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;YAC7B,IAAI,CAAC,QAAQ,GAAG,IAAI,KAAK,EAAE,CAAC;YAE5B,IAAI,YAAY,GAA+B,IAAI,CAAC;YAGpD,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE;gBACrC,YAAY,GAAG,IAAI,mBAAQ,CAAC,YAAY,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;aACpE;iBAAM,IAAI,aAAa,YAAY,mBAAQ,EAAE;gBAE5C,YAAY,GAAG,aAAa,CAAC;aAC9B;YAGD,IAAI,YAAY,KAAK,IAAI,EAAE;gBACzB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aAClC;QAEH,CAAC;QAEM,uBAAM,GAAb,UAAc,GAAW;YACvB,KAAgB,UAAa,EAAb,KAAA,IAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;gBAA1B,IAAM,CAAC,SAAA;gBACV,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,EAAE;oBACjB,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAEM,wBAAO,GAAd;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QACpD,CAAC;QAEM,6BAAY,GAAnB,UAAoB,IAA2C;YAC7D,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,CAAC;QAC9D,CAAC;QAEM,wBAAO,GAAd;YACE,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;QAC7C,CAAC;QAEM,yBAAQ,GAAf;YACE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CACvB,UAAC,QAAQ,IAAK,OAAA,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAC,GAAG,IAAK,OAAA,QAAQ,CAAC,GAAG,KAAK,GAAG,EAApB,CAAoB,CAAC,EAArE,CAAqE,CACpF,CAAC;QACJ,CAAC;QAEM,2BAAU,GAAjB;YACE,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC;QAC1D,CAAC;QAEM,4BAAW,GAAlB;YACE,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE;gBAClB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CACzB,UAAC,CAAC,IAAK,OAAA,CAAC,CAAC,GAAG,KAAK,YAAY,CAAC,SAAS,EAAhC,CAAgC,CACxC,CAAC,CAAC,CAAgC,CAAC;aACrC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAEM,8BAAa,GAApB;YACE,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE;gBACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CACzB,UAAC,QAAQ,IAAK,OAAA,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAC,GAAG,IAAK,OAAA,QAAQ,CAAC,GAAG,KAAK,GAAG,EAApB,CAAoB,CAAC,EAArE,CAAqE,CACpF,CAAC;aACH;iBAAM;gBACL,OAAO,IAAI,CAAC;aACb;QACH,CAAC;QAEM,gCAAe,GAAtB,UAAuB,IAAY;YACjC,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAEM,2BAAU,GAAjB,UAAkB,GAAW;YAA7B,iBASC;YARC,OAAO,UAAC,KAAc;gBACpB,KAAgB,UAAa,EAAb,KAAA,KAAI,CAAC,QAAQ,EAAb,cAAa,EAAb,IAAa,EAAE;oBAA1B,IAAM,CAAC,SAAA;oBACV,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;wBACtC,OAAO,IAAI,CAAC;qBACb;iBACF;gBACD,OAAO,KAAK,CAAC;YACf,CAAC,CAAC;QACJ,CAAC;QAEH,aAAC;IAAD,CAAC,AA3GD,IA2GC;IAEQ,wBAAM"}