{"version": 3, "file": "binding_on_syntax.js", "sourceRoot": "", "sources": ["../../src/syntax/binding_on_syntax.ts"], "names": [], "mappings": ";;;;IAGA;QAIE,yBAAmB,OAA8B;YAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;QAEM,sCAAY,GAAnB,UAAoB,OAAwC;YAC1D,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,OAAO,CAAC;YACrC,OAAO,IAAI,uCAAiB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC;QAEM,wCAAc,GAArB,UAAsB,OAA0C;YAC9D,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,OAAO,CAAC;YACvC,OAAO,IAAI,uCAAiB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACjD,CAAC;QAEH,sBAAC;IAAD,CAAC,AAlBD,IAkBC;IAEQ,0CAAe"}