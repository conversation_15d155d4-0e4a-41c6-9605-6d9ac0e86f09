import { MiddlewareParamArray } from '../../interface';
export interface WSControllerOption {
    namespace: string;
    routerOptions: {
        connectionMiddleware?: MiddlewareParamArray;
        middleware?: MiddlewareParamArray;
    };
}
export declare function WSController(namespace?: string | RegExp, routerOptions?: WSControllerOption['routerOptions']): ClassDecorator;
//# sourceMappingURL=webSocketController.d.ts.map