import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceWalletRecordEntity } from '../../../entity/wallet/record';
import { UserInfoEntity } from '../../../../user/entity/info';
import { FinanceWalletRecordService } from '../../../service/wallet/record';

/**
 * 钱包记录
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: FinanceWalletRecordEntity,
  service: FinanceWalletRecordService,
  pageQueryOp: {
    keyWordLikeFields: ['a.title', 'b.nickName'],
    fieldEq: ['a.type', 'a.userId'],
    select: ['a.*', 'b.nickName as userName', 'b.avatarUrl'],
    join: [
      {
        entity: UserInfoEntity,
        alias: 'b',
        condition: 'b.id = a.userId',
      },
    ],
  },
})
export class AdminFinanceWalletRecordController extends BaseController {}
