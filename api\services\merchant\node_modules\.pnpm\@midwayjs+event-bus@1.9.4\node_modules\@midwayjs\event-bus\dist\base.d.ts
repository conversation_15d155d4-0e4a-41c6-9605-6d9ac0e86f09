/// <reference types="node" />
import { BroadcastOptions, EventBusOptions, EventCenterMessage, IEventBus, IResponder, Message, PublishOptions, SubscribeOptions, SubscribeTopicListener, WaitCheckOptions } from './interface';
export declare function createWaitHandler(checkHandler: () => boolean, options?: WaitCheckOptions): Promise<void>;
export declare class AckResponder implements IResponder {
    private isEndFlag;
    private dataHandler;
    private errorHandler;
    onData(dataHandler: (data: unknown) => void): void;
    onError(errorHandler: (err: Error) => void): void;
    end(data?: unknown): void;
    protected sendData(data: any): void;
    send(data: unknown): void;
    error(err: Error): void;
    isEnd(): boolean;
}
export declare abstract class AbstractEventBus<T> implements IEventBus<T> {
    protected readonly options: EventBusOptions<T>;
    private isInited;
    protected workers: T[];
    protected stopping: boolean;
    private messageReceiver;
    protected workerReady: Map<string, {
        worker: T;
        ready: boolean;
    }>;
    private listener;
    private topicListener;
    private topicMessageCache;
    private asyncMessageMap;
    protected eventListenerMap: Map<string, any>;
    protected debugLogger: import("util").DebugLogger;
    constructor(options?: EventBusOptions<T>);
    private createDebugger;
    private debugDataflow;
    start(err?: Error): Promise<void>;
    addWorker(worker: T): void;
    private isAllWorkerReady;
    private setupEventBind;
    protected transit(message: EventCenterMessage): void;
    subscribe(listener: SubscribeTopicListener, options?: SubscribeOptions): void;
    subscribeOnce(listener: SubscribeTopicListener, options?: SubscribeOptions): void;
    publish(data: unknown | Error, publishOptions?: PublishOptions): void;
    publishAsync<ResData>(data: unknown, publishOptions?: PublishOptions): Promise<ResData>;
    publishChunk<ResData>(data: unknown, publishOptions?: PublishOptions): AsyncIterable<ResData>;
    protected useTimeout(messageId: any, timeout: number, successHandler: (data: any, isEnd?: boolean) => void, errorHandler: (error: Error) => void): void;
    broadcast(data: unknown, options?: BroadcastOptions): void;
    protected postMessage(message: Message): void;
    protected abstract workerSubscribeMessage(subscribeMessageHandler: (message: Message) => void): any;
    protected abstract workerListenMessage(worker: T, subscribeMessageHandler: (message: Message) => void): any;
    protected abstract workerSendMessage(message: Message): any;
    protected abstract mainSendMessage(worker: T, message: Message): any;
    abstract isMain(): any;
    abstract isWorker(): any;
    abstract getWorkerId(worker?: T): string;
    onInited(listener: (message: Message<unknown>) => void): void;
    onPublish(listener: (message: Message<unknown>) => void): void;
    onSubscribe(listener: (message: Message<unknown>) => void): void;
    onError(listener: (error: Error) => void): void;
    stop(): Promise<void>;
    generateMessageId(): string;
}
//# sourceMappingURL=base.d.ts.map