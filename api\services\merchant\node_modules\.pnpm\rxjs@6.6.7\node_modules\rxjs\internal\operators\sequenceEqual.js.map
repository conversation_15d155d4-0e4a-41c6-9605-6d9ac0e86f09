{"version": 3, "file": "sequenceEqual.js", "sources": ["../../src/internal/operators/sequenceEqual.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA,4CAA2C;AA8D3C,SAAgB,aAAa,CAAI,SAAwB,EACxB,UAAoC;IACnE,OAAO,UAAC,MAAqB,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,IAAI,qBAAqB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,EAA7D,CAA6D,CAAC;AAClG,CAAC;AAHD,sCAGC;AAED;IACE,+BAAoB,SAAwB,EACxB,UAAmC;QADnC,cAAS,GAAT,SAAS,CAAe;QACxB,eAAU,GAAV,UAAU,CAAyB;IACvD,CAAC;IAED,oCAAI,GAAJ,UAAK,UAA+B,EAAE,MAAW;QAC/C,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,uBAAuB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACpG,CAAC;IACH,4BAAC;AAAD,CAAC,AARD,IAQC;AARY,sDAAqB;AAelC;IAAmD,2CAAa;IAK9D,iCAAY,WAAwB,EAChB,SAAwB,EACxB,UAAmC;QAFvD,YAGE,kBAAM,WAAW,CAAC,SAEnB;QAJmB,eAAS,GAAT,SAAS,CAAe;QACxB,gBAAU,GAAV,UAAU,CAAyB;QAN/C,QAAE,GAAQ,EAAE,CAAC;QACb,QAAE,GAAQ,EAAE,CAAC;QACb,kBAAY,GAAG,KAAK,CAAC;QAM1B,KAAI,CAAC,WAA4B,CAAC,GAAG,CAAC,SAAS,CAAC,SAAS,CAAC,IAAI,gCAAgC,CAAC,WAAW,EAAE,KAAI,CAAC,CAAC,CAAC,CAAC;;IACvH,CAAC;IAES,uCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAEM,2CAAS,GAAhB;QACE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,6CAAW,GAAX;QACQ,IAAA,SAA6B,EAA3B,UAAE,EAAE,UAAE,EAAE,0BAAU,CAAU;QACpC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YACrC,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,IAAI;gBACF,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;aACpD;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,QAAQ,EAAE;gBACb,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClB;SACF;IACH,CAAC;IAED,sCAAI,GAAJ,UAAK,KAAc;QACT,IAAA,8BAAW,CAAU;QAC7B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IAED,uCAAK,GAAL,UAAM,KAAQ;QACZ,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC7C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACpB,IAAI,CAAC,WAAW,EAAE,CAAC;SACpB;IACH,CAAC;IAED,2CAAS,GAAT;QACE,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;SACzD;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;SAC1B;IACH,CAAC;IACH,8BAAC;AAAD,CAAC,AArED,CAAmD,uBAAU,GAqE5D;AArEY,0DAAuB;AAuEpC;IAAqD,oDAAa;IAChE,0CAAY,WAAwB,EAAU,MAAqC;QAAnF,YACE,kBAAM,WAAW,CAAC,SACnB;QAF6C,YAAM,GAAN,MAAM,CAA+B;;IAEnF,CAAC;IAES,gDAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAC3B,CAAC;IAES,iDAAM,GAAhB,UAAiB,GAAQ;QACvB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,oDAAS,GAAnB;QACE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACxB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IACH,uCAAC;AAAD,CAAC,AAlBD,CAAqD,uBAAU,GAkB9D"}