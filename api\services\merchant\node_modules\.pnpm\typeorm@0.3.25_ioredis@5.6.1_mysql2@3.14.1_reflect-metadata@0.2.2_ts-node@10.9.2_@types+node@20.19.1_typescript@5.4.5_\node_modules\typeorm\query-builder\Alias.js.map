{"version": 3, "sources": ["../../src/query-builder/Alias.ts"], "names": [], "mappings": ";;;AACA,qDAAiD;AACjD,oCAAuC;AAEvC;GACG;AACH,MAAa,KAAK;IAgBd,YAAY,KAAa;QACrB,yBAAW,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAA;IACzC,CAAC;IAID,IAAI,MAAM;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;IAC/B,CAAC;IAED,IAAI,WAAW;QACX,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAA;IAC3B,CAAC;IAED,IAAI,QAAQ,CAAC,QAAwB;QACjC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;IAC7B,CAAC;IAED,IAAI,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,SAAS;YACf,MAAM,IAAI,oBAAY,CAClB,mDAAmD,IAAI,CAAC,IAAI,GAAG,CAClE,CAAA;QAEL,OAAO,IAAI,CAAC,SAAS,CAAA;IACzB,CAAC;CACJ;AA1CD,sBA0CC", "file": "Alias.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { TypeORMError } from \"../error\"\n\n/**\n */\nexport class Alias {\n    type: \"from\" | \"select\" | \"join\" | \"other\" // todo: make something with \"other\"\n\n    name: string\n\n    /**\n     * Table on which this alias is applied.\n     * Used only for aliases which select custom tables.\n     */\n    tablePath?: string\n\n    /**\n     * If this alias is for sub query.\n     */\n    subQuery?: string\n\n    constructor(alias?: Alias) {\n        ObjectUtils.assign(this, alias || {})\n    }\n\n    private _metadata?: EntityMetadata\n\n    get target(): Function | string {\n        return this.metadata.target\n    }\n\n    get hasMetadata(): boolean {\n        return !!this._metadata\n    }\n\n    set metadata(metadata: EntityMetadata) {\n        this._metadata = metadata\n    }\n\n    get metadata(): EntityMetadata {\n        if (!this._metadata)\n            throw new TypeORMError(\n                `Cannot get entity metadata for the given alias \"${this.name}\"`,\n            )\n\n        return this._metadata\n    }\n}\n"], "sourceRoot": ".."}