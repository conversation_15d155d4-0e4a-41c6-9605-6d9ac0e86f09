"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getPluginClass = exports.filterPluginByCommand = exports.BasePlugin = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
const light_spinner_1 = require("light-spinner");
const npm_1 = require("./npm");
class BasePlugin {
    constructor(core, options) {
        this.name = this.getName();
        this.core = core;
        this.options = options;
        this.commands = {};
        this.hooks = {};
    }
    getName() {
        return this.constructor.name;
    }
    setStore(key, value, isGlobalScope) {
        const scope = isGlobalScope ? 'global' : this.name;
        this.core.store.set(`${scope}:${key}`, value);
    }
    getStore(key, scope) {
        return this.core.store.get(`${scope || this.name}:${key}`);
    }
    setGlobalDependencies(name, version) {
        if (!this.core.service.globalDependencies) {
            this.core.service.globalDependencies = {};
        }
        this.core.service.globalDependencies[name] = version || '*';
    }
}
exports.BasePlugin = BasePlugin;
// 通过命令过滤插件
const filterPluginByCommand = (pluginList, options) => {
    const { command, platform, cwd = process.cwd(), load } = options || {};
    return pluginList.filter(plugin => {
        if (!plugin || !plugin.mod) {
            return false;
        }
        // 如果存在命令匹配
        if (plugin.command) {
            if (Array.isArray(plugin.command)) {
                if (plugin.command.indexOf(command) === -1) {
                    return false;
                }
            }
            else if (plugin.command !== command) {
                return false;
            }
        }
        // 平台不一致
        if (plugin.platform) {
            if (plugin.platform !== platform) {
                return false;
            }
        }
        try {
            const pluginJson = load(plugin.mod + '/plugin.json');
            if (pluginJson.match) {
                // 匹配命令是否一致
                if (pluginJson.match.command) {
                    if (Array.isArray(pluginJson.match.command)) {
                        if (pluginJson.match.command.indexOf(command) === -1) {
                            return false;
                        }
                    }
                    else if (pluginJson.match.command !== command) {
                        return false;
                    }
                }
                // 匹配文件是否存在
                if (pluginJson.match.file) {
                    const filePath = (0, path_1.resolve)(cwd, pluginJson.match.file);
                    if (!(0, fs_1.existsSync)(filePath)) {
                        return false;
                    }
                }
                return true;
            }
        }
        catch (_a) {
            //
        }
        return true;
    });
};
exports.filterPluginByCommand = filterPluginByCommand;
// 获取插件的class列表
const getPluginClass = async (pluginList, options) => {
    const { cwd, npm, load, notAutoInstall } = options;
    const classList = [];
    for (const pluginInfo of pluginList) {
        let mod;
        try {
            mod = load(pluginInfo.mod);
        }
        catch (_a) {
            if (notAutoInstall) {
                continue;
            }
            let userModPath = (0, path_1.resolve)(cwd, 'node_modules', pluginInfo.mod);
            // if plugin not exists, auto install
            if (!(0, fs_1.existsSync)(userModPath)) {
                await autoInstallMod(pluginInfo.mod, {
                    cwd,
                    npm,
                });
            }
            // 避免失败的require cache
            const newPkgPath = (0, path_1.resolve)(userModPath, 'package.json');
            if (!(0, fs_1.existsSync)(newPkgPath)) {
                continue;
            }
            const pkg = JSON.parse((0, fs_1.readFileSync)(newPkgPath).toString());
            if (pkg.main) {
                userModPath = (0, path_1.resolve)(userModPath, pkg.main);
            }
            try {
                mod = load(userModPath);
            }
            catch (e) {
                // no oth doing
            }
        }
        if (!mod) {
            continue;
        }
        if (pluginInfo.name) {
            if (mod[pluginInfo.name]) {
                classList.push(mod[pluginInfo.name]);
            }
        }
        else {
            classList.push(mod);
        }
    }
    return classList;
};
exports.getPluginClass = getPluginClass;
const autoInstallMod = async (modName, options) => {
    console.log(`[ midway ] CLI plugin '${modName}' was not installed, and will be installed automatically`);
    if (!options.npm) {
        console.log('[ midway ] You could use the `--npm` parameter to speed up the installation process');
    }
    const spin = new light_spinner_1.default({ text: 'installing' });
    spin.start();
    try {
        await (0, npm_1.installNpm)({
            moduleName: modName,
            register: options.npm,
            baseDir: options.cwd,
            slience: true,
        });
    }
    catch (e) {
        console.error(`[ midway ] cli plugin '${modName}' install error: ${e === null || e === void 0 ? void 0 : e.message}`);
        console.log(`[ midway ] please manual install '${modName}'`);
    }
    spin.stop();
};
//# sourceMappingURL=plugin.js.map