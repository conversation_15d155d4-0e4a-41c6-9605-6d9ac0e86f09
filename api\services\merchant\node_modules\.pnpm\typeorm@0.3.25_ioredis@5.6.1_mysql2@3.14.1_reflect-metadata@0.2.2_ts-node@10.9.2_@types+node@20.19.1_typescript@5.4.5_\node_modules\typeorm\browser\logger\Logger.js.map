{"version": 3, "sources": ["../browser/src/logger/Logger.ts"], "names": [], "mappings": "", "file": "Logger.js", "sourcesContent": ["import { QueryRunner } from \"../query-runner/QueryRunner\"\n\n/**\n * Performs logging of the events in TypeORM.\n */\nexport interface Logger {\n    /**\n     * Logs query and parameters used in it.\n     */\n    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner): any\n\n    /**\n     * Logs query that is failed.\n     */\n    logQueryError(\n        error: string | Error,\n        query: string,\n        parameters?: any[],\n        queryRunner?: QueryRunner,\n    ): any\n\n    /**\n     * Logs query that is slow.\n     */\n    logQuerySlow(\n        time: number,\n        query: string,\n        parameters?: any[],\n        queryRunner?: QueryRunner,\n    ): any\n\n    /**\n     * Logs events from the schema build process.\n     */\n    logSchemaBuild(message: string, queryRunner?: QueryRunner): any\n\n    /**\n     * Logs events from the migrations run process.\n     */\n    logMigration(message: string, queryRunner?: QueryRunner): any\n\n    /**\n     * Perform logging using given logger, or by default to the console.\n     * Log has its own level and message.\n     */\n    log(\n        level: \"log\" | \"info\" | \"warn\",\n        message: any,\n        queryRunner?: QueryRunner,\n    ): any\n}\n\n/**\n * Log level.\n */\nexport type LogLevel =\n    | \"query\"\n    | \"schema\"\n    | \"error\"\n    | \"warn\"\n    | \"info\"\n    | \"log\"\n    | \"migration\"\n\n/**\n * Log message.\n */\nexport type LogMessage = {\n    type?: LogMessageType\n    prefix?: string\n    message: string | number\n    format?: LogMessageFormat\n    parameters?: any[]\n    additionalInfo?: Record<string, any>\n}\n\n/**\n * Log message format.\n */\nexport type LogMessageFormat = \"sql\"\n\n/**\n * Log message type.\n */\nexport type LogMessageType =\n    | \"log\"\n    | \"info\"\n    | \"warn\"\n    | \"error\"\n    | \"query\"\n    | \"query-error\"\n    | \"query-slow\"\n    | \"schema-build\"\n    | \"migration\"\n\n/**\n * Options for prepare log messages\n */\nexport type PrepareLogMessagesOptions = {\n    highlightSql: boolean\n    formatSql: boolean\n    appendParameterAsComment: boolean\n    addColonToPrefix: boolean\n}\n"], "sourceRoot": ".."}