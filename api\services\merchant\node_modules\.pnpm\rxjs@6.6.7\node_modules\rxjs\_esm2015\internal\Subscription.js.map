{"version": 3, "file": "Subscription.js", "sources": ["../../src/internal/Subscription.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AACzC,OAAO,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAC3C,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAC/C,OAAO,EAAE,mBAAmB,EAAE,MAAM,4BAA4B,CAAC;AAejE,MAAM,OAAO,YAAY;IAsBvB,YAAY,WAAwB;QAX7B,WAAM,GAAY,KAAK,CAAC;QAGrB,qBAAgB,GAAkC,IAAI,CAAC;QAEzD,mBAAc,GAAuB,IAAI,CAAC;QAOhD,IAAI,WAAW,EAAE;YACd,IAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACrC,IAAY,CAAC,YAAY,GAAG,WAAW,CAAC;SAC1C;IACH,CAAC;IAQD,WAAW;QACT,IAAI,MAAa,CAAC;QAElB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO;SACR;QAED,IAAI,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,YAAY,EAAE,cAAc,EAAE,GAAI,IAAY,CAAC;QAEzF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;QAG7B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,gBAAgB,YAAY,YAAY,EAAE;YAC5C,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SAC/B;aAAM,IAAI,gBAAgB,KAAK,IAAI,EAAE;YACpC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBAC5D,MAAM,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;aACrB;SACF;QAED,IAAI,UAAU,CAAC,YAAY,CAAC,EAAE;YAU5B,IAAI,gBAAgB,EAAE;gBACnB,IAAY,CAAC,YAAY,GAAG,SAAS,CAAC;aACxC;YACD,IAAI;gBACF,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACzB;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,GAAG,CAAC,YAAY,mBAAmB,CAAC,CAAC,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aACzF;SACF;QAED,IAAI,OAAO,CAAC,cAAc,CAAC,EAAE;YAC3B,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC;YACf,IAAI,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC;YAEhC,OAAO,EAAE,KAAK,GAAG,GAAG,EAAE;gBACpB,MAAM,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;gBAClC,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;oBACjB,IAAI;wBACF,GAAG,CAAC,WAAW,EAAE,CAAC;qBACnB;oBAAC,OAAO,CAAC,EAAE;wBACV,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;wBACtB,IAAI,CAAC,YAAY,mBAAmB,EAAE;4BACpC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;yBAC/D;6BAAM;4BACL,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBAChB;qBACF;iBACF;aACF;SACF;QAED,IAAI,MAAM,EAAE;YACV,MAAM,IAAI,mBAAmB,CAAC,MAAM,CAAC,CAAC;SACvC;IACH,CAAC;IAsBD,GAAG,CAAC,QAAuB;QACzB,IAAI,YAAY,GAAkB,QAAS,CAAC;QAE5C,IAAI,CAAC,QAAQ,EAAE;YACb,OAAO,YAAY,CAAC,KAAK,CAAC;SAC3B;QAED,QAAQ,OAAO,QAAQ,EAAE;YACvB,KAAK,UAAU;gBACb,YAAY,GAAG,IAAI,YAAY,CAAe,QAAQ,CAAC,CAAC;YAC1D,KAAK,QAAQ;gBACX,IAAI,YAAY,KAAK,IAAI,IAAI,YAAY,CAAC,MAAM,IAAI,OAAO,YAAY,CAAC,WAAW,KAAK,UAAU,EAAE;oBAElG,OAAO,YAAY,CAAC;iBACrB;qBAAM,IAAI,IAAI,CAAC,MAAM,EAAE;oBACtB,YAAY,CAAC,WAAW,EAAE,CAAC;oBAC3B,OAAO,YAAY,CAAC;iBACrB;qBAAM,IAAI,CAAC,CAAC,YAAY,YAAY,YAAY,CAAC,EAAE;oBAClD,MAAM,GAAG,GAAG,YAAY,CAAC;oBACzB,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;oBAClC,YAAY,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;iBACrC;gBACD,MAAM;YACR,OAAO,CAAC,CAAC;gBACP,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,QAAQ,GAAG,yBAAyB,CAAC,CAAC;aAClF;SACF;QAGD,IAAI,EAAE,gBAAgB,EAAE,GAAG,YAAY,CAAC;QACxC,IAAI,gBAAgB,KAAK,IAAI,EAAE;YAG7B,YAAY,CAAC,gBAAgB,GAAG,IAAI,CAAC;SACtC;aAAM,IAAI,gBAAgB,YAAY,YAAY,EAAE;YACnD,IAAI,gBAAgB,KAAK,IAAI,EAAE;gBAE7B,OAAO,YAAY,CAAC;aACrB;YAGD,YAAY,CAAC,gBAAgB,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;SAC1D;aAAM,IAAI,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;YAEhD,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7B;aAAM;YAEL,OAAO,YAAY,CAAC;SACrB;QAGD,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,aAAa,KAAK,IAAI,EAAE;YAC1B,IAAI,CAAC,cAAc,GAAG,CAAC,YAAY,CAAC,CAAC;SACtC;aAAM;YACL,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAClC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAQD,MAAM,CAAC,YAA0B;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,aAAa,EAAE;YACjB,MAAM,iBAAiB,GAAG,aAAa,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YAC9D,IAAI,iBAAiB,KAAK,CAAC,CAAC,EAAE;gBAC5B,aAAa,CAAC,MAAM,CAAC,iBAAiB,EAAE,CAAC,CAAC,CAAC;aAC5C;SACF;IACH,CAAC;;AAtMa,kBAAK,GAAiB,CAAC,UAAS,KAAU;IACtD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC,CAAC,IAAI,YAAY,EAAE,CAAC,CAAC,CAAC;AAsMzB,SAAS,2BAA2B,CAAC,MAAa;IACjD,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,YAAY,mBAAmB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;AAC/G,CAAC"}