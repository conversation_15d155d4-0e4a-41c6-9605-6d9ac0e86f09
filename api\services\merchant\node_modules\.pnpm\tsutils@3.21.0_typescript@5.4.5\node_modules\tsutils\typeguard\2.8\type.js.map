{"version": 3, "file": "type.js", "sourceRoot": "", "sources": ["type.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,SAAgB,iBAAiB,CAAC,IAAa;IAC3C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACzD,CAAC;AAFD,8CAEC;AAED,SAAgB,UAAU,CAAC,IAAa;IACpC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClD,CAAC;AAFD,gCAEC;AAED,SAAgB,aAAa,CAAC,IAAa;IACvC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3C,CAAiB,IAAK,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC;QAC3E,CAAiB,IAAK,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC;AAJD,sCAIC;AAED,SAAgB,mBAAmB,CAAC,IAAa;IAC7C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;AAFD,kDAEC;AAED,SAAgB,kBAAkB,CAAC,IAAa;IAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,CAAC;AAFD,gDAEC;AAED,SAAgB,kBAAkB,CAAC,IAAa;IAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1D,CAAC;AAFD,gDAEC;AAED,SAAgB,eAAe,CAAC,IAAa;IACzC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3C,CAAiB,IAAK,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACpF,CAAC;AAHD,0CAGC;AAED,SAAgB,kBAAkB,CAAC,IAAa;IAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1D,CAAC;AAFD,gDAEC;AAED,SAAgB,aAAa,CAAC,IAAa;IACvC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,qBAAqB,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC;AAClG,CAAC;AAFD,sCAEC;AAED,SAAgB,YAAY,CAAC,IAAa;IACtC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACpD,CAAC;AAFD,oCAEC;AAED,SAAgB,kBAAkB,CAAC,IAAa;IAC5C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1D,CAAC;AAFD,gDAEC;AAED,SAAgB,eAAe,CAAC,IAAa;IACzC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;AAFD,0CAEC;AAED,SAAgB,eAAe,CAAC,IAAa;IACzC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3C,CAAiB,IAAK,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC7E,CAAC;AAHD,0CAGC;AAED,SAAgB,cAAc,CAAC,IAAa;IACxC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AAC1D,CAAC;AAFD,wCAEC;AAED,SAAgB,yBAAyB,CAAC,IAAa;IACnD,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;AACjE,CAAC;AAFD,8DAEC;AAED,SAAgB,WAAW,CAAC,IAAa;IACrC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACnD,CAAC;AAFD,kCAEC;AAED,SAAgB,oBAAoB,CAAC,IAAa;IAC9C,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC5D,CAAC;AAFD,oDAEC"}