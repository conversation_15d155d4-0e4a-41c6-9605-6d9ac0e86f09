{"name": "pidusage", "version": "3.0.2", "description": "Cross-platform process cpu % and memory usage of a PID", "license": "MIT", "homepage": "https://github.com/soyuka/pidusage", "repository": "github:soyuka/pidusage", "bugs": {"url": "https://github.com/soyuka/pidusage/issues"}, "author": "soyuka", "contributors": ["<PERSON> <<EMAIL>> (https://simoneprimarosa.com)"], "main": "index.js", "files": ["lib", "index.js"], "engines": {"node": ">=10"}, "scripts": {"lint": "standard", "test": "nyc ava -m \"!*benchmark*\"", "coverage": "codecov", "bench": "ava -m \"*benchmark*\""}, "dependencies": {"safe-buffer": "^5.2.1"}, "devDependencies": {"ava": "^3.10.1", "codecov": "^3.7.0", "mockdate": "^2.0.5", "mockery": "^2.1.0", "nyc": "^15.1.0", "pify": "^3.0.0", "standard": "^16.0.4", "string-to-stream": "^1.1.1", "through": "^2.3.8", "time-span": "^2.0.0"}, "keywords": ["pid", "usage", "ps", "cpu", "memory", "proc"], "ava": {"verbose": true}, "nyc": {"reporter": ["lcovonly", "text"]}}