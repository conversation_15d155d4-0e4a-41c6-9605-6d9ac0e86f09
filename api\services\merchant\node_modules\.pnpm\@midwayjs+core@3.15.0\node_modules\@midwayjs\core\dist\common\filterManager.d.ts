import { CommonFilterUnion, IMidwayContainer, IMidwayContext } from '../interface';
export declare class FilterManager<CTX extends IMidwayContext = IMidwayContext, R = any, N = any> {
    private errFilterList;
    private successFilterList;
    private exceptionMap;
    private defaultErrFilter;
    private matchFnList;
    private protoMatchList;
    useFilter(Filters: CommonFilterUnion<CTX, R, N>): void;
    init(applicationContext: IMidwayContainer): Promise<void>;
    runErrorFilter(err: Error, ctx: CTX, res?: R, next?: N): Promise<{
        result: any;
        error: any;
    }>;
    runResultFilter(result: any, ctx: CTX, res?: R, next?: N): Promise<{
        result: any;
        error: any;
    }>;
}
//# sourceMappingURL=filterManager.d.ts.map