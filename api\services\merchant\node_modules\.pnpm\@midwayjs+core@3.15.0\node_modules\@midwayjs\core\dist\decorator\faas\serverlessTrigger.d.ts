import { FaaSMetadata, ServerlessTriggerType } from '../../interface';
export declare function ServerlessFunction(options: FaaSMetadata.ServerlessFunctionOptions & Record<string, any>): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.HTTP, metadata: FaaSMetadata.HTTPTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.OS, metadata: FaaSMetadata.OSTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.LOG, metadata: FaaSMetadata.LogTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.TIMER, metadata: FaaSMetadata.TimerTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.MQ, metadata: FaaSMetadata.MQTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.CDN, metadata?: FaaSMetadata.CDNTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.API_GATEWAY, metadata?: FaaSMetadata.APIGatewayTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.HSF, metadata?: FaaSMetadata.HSFTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.MTOP, metadata?: FaaSMetadata.MTopTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.SSR, metadata?: FaaSMetadata.SSRTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: ServerlessTriggerType.EVENT, metadata?: FaaSMetadata.EventTriggerOptions): MethodDecorator;
export declare function ServerlessTrigger(type: string, metadata?: FaaSMetadata.EventTriggerOptions & Record<string, any>): MethodDecorator;
//# sourceMappingURL=serverlessTrigger.d.ts.map