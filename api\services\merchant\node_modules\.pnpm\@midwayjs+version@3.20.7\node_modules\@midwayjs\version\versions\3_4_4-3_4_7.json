{"@midwayjs/faas-typings": "3.3.5", "@midwayjs/fc-starter": ["3.4.7", "3.4.8"], "@midwayjs/serverless-http-parser": "3.4.0", "@midwayjs/async-hooks-context-manager": "3.4.7", "@midwayjs/axios": "3.4.7", "@midwayjs/bootstrap": "3.4.7", "@midwayjs/cache": "3.4.7", "@midwayjs/code-dye": ["3.4.7", "3.4.8"], "@midwayjs/consul": "3.4.7", "@midwayjs/core": "3.4.7", "@midwayjs/cos": "3.4.7", "@midwayjs/cross-domain": ["3.4.7", "3.4.8"], "@midwayjs/decorator": "3.4.4", "@midwayjs/express-session": "3.4.7", "@midwayjs/faas": ["3.4.7", "3.4.8"], "@midwayjs/grpc": "3.4.7", "@midwayjs/http-proxy": ["3.4.7", "3.4.8"], "@midwayjs/i18n": "3.4.7", "@midwayjs/info": "3.4.7", "@midwayjs/jwt": "3.4.7", "@midwayjs/kafka": "3.4.7", "@midwayjs/mikro": "3.4.7", "@midwayjs/mock": "3.4.7", "@midwayjs/mongoose": "3.4.7", "@midwayjs/oss": "3.4.7", "@midwayjs/otel": "3.4.7", "@midwayjs/passport": "3.4.7", "@midwayjs/process-agent": "3.4.7", "@midwayjs/prometheus-socket-io": "3.4.7", "@midwayjs/prometheus": "3.4.7", "@midwayjs/rabbitmq": "3.4.7", "@midwayjs/redis": "3.4.7", "@midwayjs/security": ["3.4.7", "3.4.8"], "@midwayjs/sequelize": "3.4.7", "@midwayjs/session": "3.4.7", "@midwayjs/socketio": "3.4.7", "@midwayjs/static-file": ["3.4.7", "3.4.8"], "@midwayjs/swagger": "3.4.7", "@midwayjs/tablestore": "3.4.7", "@midwayjs/task": "3.4.7", "@midwayjs/typegoose": "3.4.7", "@midwayjs/typeorm": "3.4.7", "@midwayjs/upload": ["3.4.7", "3.4.8"], "@midwayjs/validate": "3.4.7", "@midwayjs/version": ["3.4.7", "3.4.8"], "@midwayjs/view-ejs": "3.4.7", "@midwayjs/view-nunjucks": "3.4.7", "@midwayjs/view": "3.4.7", "@midwayjs/express": "3.4.7", "@midwayjs/koa": "3.4.7", "@midwayjs/web": "3.4.7", "@midwayjs/ws": "3.4.7"}