{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,uCAAiC;AACjC,mCAA8B;AAC9B,2CAAoC;AACpC,uCAA4E;AAoB5E,MAAM,aAAa,GAAG;IACpB,KAAK,EAAE,KAAK;IACZ,OAAO,EAAE,KAAK;IACd,KAAK,EAAE,KAAK;IACZ,cAAc,EAAE,SAAS;IACzB,MAAM,EAAE,SAAS;IACjB,SAAS,EAAE,SAAS;CACrB,CAAC;AAEF,MAAM,cAAc,GAAG,IAAA,mBAAM,EAAC,GAAG,CAAC,CAAC;AAMnC,SAAsB,oBAAoB,CACxC,4BAA4C,aAAa,CAAE;;QAE3D,MAAM,MAAM,GAAG,MAAM,IAAA,uBAAa,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;QAG7B,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC5E,MAAM,WAAW,GAAG;YAClB,GAAG,WAAW,SAAS,MAAM,CAAC,SAAS,EAAE;YACzC,IAAI,WAAW,kBAAkB;SAClC,CAAC;QACF,MAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;QAC7C,MAAM,KAAK,GAAG,IAAA,aAAI,EAAC,WAAW,EAAE;YAC9B,GAAG,EAAE,IAAI;YACT,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QACH,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QAIpC,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,GAAG,CACnC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CACjB,cAAc,CAAC,GAAG,EAAE,CAClB,IAAA,sBAAY,EACV,MAAM,EACN,IAAI,EACJ,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EACzB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,oBAAoB,CAC9B,CACF,CACF,CACF,CAAC;QAGF,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QAExD,MAAM,CAAC,IAAI,CAAC,GAAG,YAAY,uBAAuB,CAAC,CAAC;QACpD,IAAI,OAAO,CAAC,KAAK,EAAE;YACjB,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;YAC9C,MAAM,YAAY,GAAG,IAAA,gBAAK,EAAC,WAAW,CAAC,CAAC;YACxC,MAAM,eAAe,GAAG,IAAA,gBAAK,EAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjD,MAAM,YAAY,GAAG,CAAO,IAAY,EAAE,EAAE,gDAC1C,OAAA,MAAM,IAAA,sBAAY,EAAC,MAAM,EAAE,IAAI,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,CAAC,CAAA,GAAA,CAAC;YAC9D,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;YACrC,YAAY,CAAC,EAAE,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;YACxC,eAAe,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBAChC,MAAM,CAAC,KAAK,EAAE,CAAC;gBACf,YAAY,CAAC,KAAK,EAAE,CAAC;gBACrB,eAAe,CAAC,KAAK,EAAE,CAAC;gBACxB,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;SACJ;QACD,IAAI,OAAO,CAAC,cAAc,EAAE;YAC1B,oBAAoB,iCACf,OAAO,KACV,MAAM,EAAE,OAAO,CAAC,cAAc,EAC9B,cAAc,EAAE,SAAS,EACzB,MAAM,EAAE,MAAM,CAAC,MAAM,EACrB,SAAS,EAAE,SAAS,IACpB,CAAC;SACJ;IACH,CAAC;CAAA;AA/DD,oDA+DC;AAYD,SAAsB,qCAAqC,CACzD,4BAA4C,aAAa,CAAE;;QAE3D,MAAM,MAAM,GAAG,MAAM,IAAA,uBAAa,EAAC,OAAO,CAAC,CAAC;QAE5C,OAAO,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,EAAE,EAAE;YACpC,OAAO,IAAA,4BAAkB,EACvB,MAAM,EACN,QAAQ,EACR,YAAY,EACZ,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,gBAAgB,EACzB,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,oBAAoB,CAC9B,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;CAAA;AAdD,sFAcC", "sourcesContent": ["import { watch } from 'chokidar';\nimport { sync } from 'globby';\nimport { pLimit } from 'plimit-lit';\nimport { prepareConfig, replace<PERSON><PERSON><PERSON>, replace<PERSON>liasString } from './helpers';\nimport {\n  AliasReplacer,\n  AliasReplacerArguments,\n  IConfig,\n  IOutput,\n  IProjectConfig,\n  ReplaceTscAliasPathsOptions\n} from './interfaces';\n\n// export interfaces for api use.\nexport {\n  ReplaceTscAliasPathsOptions,\n  AliasReplacer,\n  AliasReplacerArguments,\n  IConfig,\n  IProjectConfig,\n  IOutput\n};\n\nconst defaultConfig = {\n  watch: false,\n  verbose: false,\n  debug: false,\n  declarationDir: undefined,\n  output: undefined,\n  aliasTrie: undefined\n};\n\nconst OpenFilesLimit = pLimit(500);\n\n/**\n * replaceTscAliasPaths replaces the aliases in the project.\n * @param {ReplaceTscAliasPathsOptions} options tsc-alias options.\n */\nexport async function replaceTscAliasPaths(\n  options: ReplaceTscAliasPathsOptions = { ...defaultConfig }\n) {\n  const config = await prepareConfig(options);\n  const output = config.output;\n\n  // Finding files and changing alias paths\n  const posixOutput = config.outPath.replace(/\\\\/g, '/').replace(/\\/+$/g, '');\n  const globPattern = [\n    `${posixOutput}/**/*.${config.inputGlob}`,\n    `!${posixOutput}/**/node_modules`\n  ];\n  output.debug('Search pattern:', globPattern);\n  const files = sync(globPattern, {\n    dot: true,\n    onlyFiles: true\n  });\n  output.debug('Found files:', files);\n\n  // Make array with promises for file changes\n  // Wait for all promises to resolve\n  const replaceList = await Promise.all(\n    files.map((file) =>\n      OpenFilesLimit(() =>\n        replaceAlias(\n          config,\n          file,\n          options?.resolveFullPaths,\n          options?.resolveFullExtension\n        )\n      )\n    )\n  );\n\n  // Count all changed files\n  const replaceCount = replaceList.filter(Boolean).length;\n\n  output.info(`${replaceCount} files were affected!`);\n  if (options.watch) {\n    output.verbose = true;\n    output.info('[Watching for file changes...]');\n    const filesWatcher = watch(globPattern);\n    const tsconfigWatcher = watch(config.configFile);\n    const onFileChange = async (file: string) =>\n      await replaceAlias(config, file, options?.resolveFullPaths);\n    filesWatcher.on('add', onFileChange);\n    filesWatcher.on('change', onFileChange);\n    tsconfigWatcher.on('change', () => {\n      output.clear();\n      filesWatcher.close();\n      tsconfigWatcher.close();\n      replaceTscAliasPaths(options);\n    });\n  }\n  if (options.declarationDir) {\n    replaceTscAliasPaths({\n      ...options,\n      outDir: options.declarationDir,\n      declarationDir: undefined,\n      output: config.output,\n      aliasTrie: undefined\n    });\n  }\n}\n\nexport type SingleFileReplacer = (input: {\n  fileContents: string;\n  filePath: string;\n}) => string;\n\n/**\n * prepareSingleFileReplaceTscAliasPaths prepares a SingleFileReplacer.\n * @param {ReplaceTscAliasPathsOptions} options tsc-alias options.\n * @returns {Promise<SingleFileReplacer>} a SingleFileReplacer to use for replacing aliases in a single file.\n */\nexport async function prepareSingleFileReplaceTscAliasPaths(\n  options: ReplaceTscAliasPathsOptions = { ...defaultConfig }\n): Promise<SingleFileReplacer> {\n  const config = await prepareConfig(options);\n\n  return ({ fileContents, filePath }) => {\n    return replaceAliasString(\n      config,\n      filePath,\n      fileContents,\n      options?.resolveFullPaths,\n      options?.resolveFullExtension\n    );\n  };\n}\n"]}