{"version": 3, "sources": ["../../src/find-options/operator/ArrayOverlap.ts"], "names": [], "mappings": ";;AAMA,oCAIC;AAVD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,YAAY,CACxB,KAAqC;IAErC,OAAO,IAAI,2BAAY,CAAC,cAAc,EAAE,KAAY,CAAC,CAAA;AACzD,CAAC", "file": "ArrayOverlap.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: ArrayOverlap([...]) }\n */\nexport function ArrayOverlap<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"arrayOverlap\", value as any)\n}\n"], "sourceRoot": "../.."}