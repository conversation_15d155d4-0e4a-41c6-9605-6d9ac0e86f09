{"version": 3, "sources": ["../../src/driver/expo/ExpoConnectionOptions.ts"], "names": [], "mappings": "", "file": "ExpoConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface ExpoConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"expo\"\n\n    /**\n     * Database name.\n     */\n    readonly database: string\n\n    /**\n     * Driver module\n     */\n    readonly driver: any\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}