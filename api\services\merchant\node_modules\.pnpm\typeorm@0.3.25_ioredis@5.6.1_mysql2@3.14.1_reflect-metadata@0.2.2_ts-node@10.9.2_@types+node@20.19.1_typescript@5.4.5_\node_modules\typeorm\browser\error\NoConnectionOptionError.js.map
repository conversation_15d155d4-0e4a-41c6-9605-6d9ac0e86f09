{"version": 3, "sources": ["../browser/src/error/NoConnectionOptionError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,YAAY;IACrD,YAAY,UAAkB;QAC1B,KAAK,CACD,WAAW,UAAU,kDAAkD;YACnE,WAAW,UAAU,uDAAuD,CACnF,CAAA;IACL,CAAC;CACJ", "file": "NoConnectionOptionError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when some option is not set in the connection options.\n */\nexport class NoConnectionOptionError extends TypeORMError {\n    constructor(optionName: string) {\n        super(\n            `Option \"${optionName}\" is not set in your connection options, please ` +\n                `define \"${optionName}\" option in your connection options or ormconfig.json`,\n        )\n    }\n}\n"], "sourceRoot": ".."}