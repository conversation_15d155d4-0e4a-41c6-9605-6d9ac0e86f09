"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseMongoEntity = void 0;
const typeorm_1 = require("typeorm");
const typeorm_2 = require("./typeorm");
/**
 * 模型基类
 */
class BaseMongoEntity extends typeorm_2.CoolBaseEntity {
}
exports.BaseMongoEntity = BaseMongoEntity;
__decorate([
    (0, typeorm_1.ObjectIdColumn)({ comment: "id" }),
    __metadata("design:type", typeof (_a = typeof typeorm_1.ObjectID !== "undefined" && typeorm_1.ObjectID) === "function" ? _a : Object)
], BaseMongoEntity.prototype, "id", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.CreateDateColumn)({ comment: "创建时间" }),
    __metadata("design:type", Date)
], BaseMongoEntity.prototype, "createTime", void 0);
__decorate([
    (0, typeorm_1.Index)(),
    (0, typeorm_1.UpdateDateColumn)({ comment: "更新时间" }),
    __metadata("design:type", Date)
], BaseMongoEntity.prototype, "updateTime", void 0);
