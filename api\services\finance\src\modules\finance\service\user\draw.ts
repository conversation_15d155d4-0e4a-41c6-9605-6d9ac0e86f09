import { Init, Inject, Provide } from '@midwayjs/core';
import { BaseService, CoolCommException } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, In, Repository } from 'typeorm';
import { FinanceUserDrawEntity } from '../../entity/user/draw';
import { CoolRpc } from '@cool-midway/rpc';

/**
 * 用户提现信息
 */
@Provide()
export class FinanceUserDrawService extends BaseService {
  @InjectEntityModel(FinanceUserDrawEntity)
  financeUserDrawEntity: Repository<FinanceUserDrawEntity>;

  @Inject()
  rpc: CoolRpc;

  @Inject()
  ctx;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeUserDrawEntity);
  }

  /**
   * 提交
   * @param data
   * @param code
   */
  async submit(data: FinanceUserDrawEntity, code: string, userId: number) {
    // 通过RPC调用主服务的短信校验接口
    const check = await this.rpc.call('main-service', 'baseCommService', 'checkSmsCode', [data.phone, code]);
    if (!check) {
      throw new CoolCommException('验证码错误');
    }
    return await super.addOrUpdate({
      ...data,
      userId,
    });
  }

  /**
   * 获得账号
   * @param userId
   * @param accountId
   * @returns
   */
  async getAccount(userId: number) {
    return await this.financeUserDrawEntity.findOneBy({
      userId: Equal(userId),
    });
  }

  /**
   * 修改之前
   * @param data
   * @param type
   */
  async modifyBefore(data: any, type: 'add' | 'update' | 'delete') {
    const userId = this.ctx.user?.id;
    // 如果是app用户，只能操作自己的提现信息
    if (userId) {
      if (type == 'update') {
        const info = await this.financeUserDrawEntity.findOneBy({
          id: Equal(data.id),
        });
        if (info.userId != userId) {
          throw new CoolCommException('无权限操作');
        }
      }
      if (type == 'delete') {
        const infos = await this.financeUserDrawEntity.findBy({
          id: In(data),
        });
        if (infos.some(info => info.userId != userId)) {
          throw new CoolCommException('无权限操作');
        }
      }
    }
  }
}
