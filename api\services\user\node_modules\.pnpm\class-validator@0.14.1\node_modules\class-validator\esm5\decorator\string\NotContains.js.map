{"version": 3, "file": "NotContains.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/NotContains.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,IAAM,YAAY,GAAG,aAAa,CAAC;AAE1C;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,KAAc,EAAE,IAAY;IACtD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY,EAAE,iBAAqC;IAC7E,OAAO,UAAU,CACf;QACE,IAAI,EAAE,YAAY;QAClB,WAAW,EAAE,CAAC,IAAI,CAAC;QACnB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,WAAW,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAxC,CAAwC;YAC5E,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,oDAAoD,EAAjE,CAAiE,EAC/E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport containsValidator from 'validator/lib/contains';\n\nexport const NOT_CONTAINS = 'notContains';\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function notContains(value: unknown, seed: string): boolean {\n  return typeof value === 'string' && !containsValidator(value, seed);\n}\n\n/**\n * Checks if the string does not contain the seed.\n * If given value is not a string, then it returns false.\n */\nexport function NotContains(seed: string, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: NOT_CONTAINS,\n      constraints: [seed],\n      validator: {\n        validate: (value, args): boolean => notContains(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property should not contain a $constraint1 string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}