 -- 修复菜单管理的按钮权限
-- 查看当前菜单管理的配置
SELECT id, name, router, perms FROM merchant_sys_menu WHERE name = '菜单管理';

-- 更新菜单管理的权限字段，添加按钮权限
UPDATE merchant_sys_menu 
SET perms = 'add,edit,delete' 
WHERE name = '菜单管理' AND router = '/system/menu';

-- 同时更新其他管理页面的权限
UPDATE merchant_sys_menu 
SET perms = 'add,edit,delete' 
WHERE name = '用户管理' AND router = '/system/user';

UPDATE merchant_sys_menu 
SET perms = 'add,edit,delete' 
WHERE name = '角色管理' AND router = '/system/role';

-- 查询更新后的结果
SELECT id, name, router, perms 
FROM merchant_sys_menu 
WHERE router IN ('/system/menu', '/system/user', '/system/role');

-- 验证所有系统管理菜单
SELECT 
  m1.name AS parent_menu,
  m2.name AS menu_name,
  m2.router,
  m2.perms
FROM merchant_sys_menu m1
JOIN merchant_sys_menu m2 ON m1.id = m2.parentId
WHERE m1.name = '系统管理' AND m2.type = 1;