{"version": 3, "sources": ["../browser/src/driver/nativescript/NativescriptQueryRunner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAC/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAC5D,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAE,yBAAyB,EAAE,MAAM,8CAA8C,CAAA;AAGxF;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,yBAAyB;IAMlE,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAA0B;QAClC,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAC/C,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAA;QAEzC,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAE/C,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YAClC,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,aAAa,CAAA;YAC3D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAEnD,MAAM,OAAO,GAAG,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE;gBACnC,oDAAoD;gBACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;gBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;gBAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;gBAExD,IACI,qBAAqB;oBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;oBACC,UAAU,CAAC,MAAM,CAAC,YAAY,CAC1B,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;gBACL,CAAC;gBAED,IAAI,GAAG,EAAE,CAAC;oBACN,UAAU,CAAC,MAAM,CAAC,aAAa,CAC3B,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBACD,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,CAAA;gBACtD,CAAC;gBAED,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;gBAChC,MAAM,CAAC,GAAG,GAAG,GAAG,CAAA;gBAEhB,IAAI,CAAC,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAA;gBACxB,CAAC;gBAED,IAAI,mBAAmB,EAAE,CAAC;oBACtB,EAAE,CAAC,MAAM,CAAC,CAAA;gBACd,CAAC;qBAAM,CAAC;oBACJ,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;gBAClB,CAAC;YACL,CAAC,CAAA;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAEjC,IAAI,aAAa,EAAE,CAAC;gBAChB,kBAAkB,CAAC,OAAO,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YAC1D,CAAC;iBAAM,CAAC;gBACJ,kBAAkB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,OAAO,CAAC,CAAA;YACtD,CAAC;QACL,CAAC,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,WAAW,CACjB,aAA4B,EAC5B,aAAqB,CAAC;QAEtB,OAAO,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,CAAA;IAC5E,CAAC;CACJ", "file": "NativescriptQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { NativescriptDriver } from \"./NativescriptDriver\"\n\n/**\n * Runs queries on a single sqlite database connection.\n */\nexport class NativescriptQueryRunner extends AbstractSqliteQueryRunner {\n    /**\n     * Database driver used by connection.\n     */\n    driver: NativescriptDriver\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: NativescriptDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    /**\n     * Called before migrations are run.\n     */\n    async beforeMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = OFF`)\n    }\n\n    /**\n     * Called after migrations are run.\n     */\n    async afterMigration(): Promise<void> {\n        await this.query(`PRAGMA foreign_keys = ON`)\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) {\n            throw new QueryRunnerAlreadyReleasedError()\n        }\n\n        const connection = this.driver.connection\n\n        const databaseConnection = await this.connect()\n\n        return new Promise(async (ok, fail) => {\n            const isInsertQuery = query.substr(0, 11) === \"INSERT INTO\"\n            connection.logger.logQuery(query, parameters, this)\n\n            const handler = (err: any, raw: any) => {\n                // log slow queries if maxQueryExecution time is set\n                const maxQueryExecutionTime =\n                    this.driver.options.maxQueryExecutionTime\n                const queryEndTime = Date.now()\n                const queryExecutionTime = queryEndTime - queryStartTime\n\n                if (\n                    maxQueryExecutionTime &&\n                    queryExecutionTime > maxQueryExecutionTime\n                ) {\n                    connection.logger.logQuerySlow(\n                        queryExecutionTime,\n                        query,\n                        parameters,\n                        this,\n                    )\n                }\n\n                if (err) {\n                    connection.logger.logQueryError(\n                        err,\n                        query,\n                        parameters,\n                        this,\n                    )\n                    fail(new QueryFailedError(query, parameters, err))\n                }\n\n                const result = new QueryResult()\n                result.raw = raw\n\n                if (!isInsertQuery && Array.isArray(raw)) {\n                    result.records = raw\n                }\n\n                if (useStructuredResult) {\n                    ok(result)\n                } else {\n                    ok(result.raw)\n                }\n            }\n            const queryStartTime = Date.now()\n\n            if (isInsertQuery) {\n                databaseConnection.execSQL(query, parameters, handler)\n            } else {\n                databaseConnection.all(query, parameters, handler)\n            }\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Parametrizes given object of values. Used to create column=value queries.\n     */\n    protected parametrize(\n        objectLiteral: ObjectLiteral,\n        startIndex: number = 0,\n    ): string[] {\n        return Object.keys(objectLiteral).map((key, index) => `\"${key}\"` + \"=?\")\n    }\n}\n"], "sourceRoot": "../.."}