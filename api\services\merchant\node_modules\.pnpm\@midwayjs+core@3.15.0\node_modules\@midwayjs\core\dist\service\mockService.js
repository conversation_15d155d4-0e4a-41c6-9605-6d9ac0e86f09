"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MidwayMockService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayMockService = void 0;
const interface_1 = require("../interface");
const decorator_1 = require("../decorator");
let MidwayMockService = MidwayMockService_1 = class MidwayMockService {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
        this.mocks = [];
        this.contextMocks = [];
        this.cache = new Map();
        this.simulatorList = [];
    }
    async init() {
        if (MidwayMockService_1.prepareMocks.length > 0) {
            for (const item of MidwayMockService_1.prepareMocks) {
                this.mockProperty(item.obj, item.key, item.value);
            }
            MidwayMockService_1.prepareMocks = [];
        }
    }
    static mockClassProperty(clzz, propertyName, value) {
        this.mockProperty(clzz.prototype, propertyName, value);
    }
    static mockProperty(obj, key, value) {
        this.prepareMocks.push({
            obj,
            key,
            value,
        });
    }
    mockClassProperty(clzz, propertyName, value) {
        return this.mockProperty(clzz.prototype, propertyName, value);
    }
    mockProperty(obj, key, value) {
        // eslint-disable-next-line no-prototype-builtins
        const hasOwnProperty = obj.hasOwnProperty(key);
        this.mocks.push({
            obj,
            key,
            descriptor: Object.getOwnPropertyDescriptor(obj, key),
            // Make sure the key exists on object not the prototype
            hasOwnProperty,
        });
        // Delete the origin key, redefine it below
        if (hasOwnProperty) {
            delete obj[key];
        }
        // Set a flag that checks if it is mocked
        let flag = this.cache.get(obj);
        if (!flag) {
            flag = new Set();
            this.cache.set(obj, flag);
        }
        flag.add(key);
        const descriptor = this.overridePropertyDescriptor(value);
        Object.defineProperty(obj, key, descriptor);
    }
    mockContext(app, key, value) {
        this.contextMocks.push({
            app,
            key,
            value,
        });
    }
    restore() {
        for (let i = this.mocks.length - 1; i >= 0; i--) {
            const m = this.mocks[i];
            if (!m.hasOwnProperty) {
                // Delete the mock key, use key on the prototype
                delete m.obj[m.key];
            }
            else {
                // Redefine the origin key instead of the mock key
                Object.defineProperty(m.obj, m.key, m.descriptor);
            }
        }
        this.mocks = [];
        this.contextMocks = [];
        this.cache.clear();
        this.simulatorList = [];
        MidwayMockService_1.prepareMocks = [];
    }
    isMocked(obj, key) {
        const flag = this.cache.get(obj);
        return flag ? flag.has(key) : false;
    }
    applyContextMocks(app, ctx) {
        if (this.contextMocks.length > 0) {
            for (const mockItem of this.contextMocks) {
                if (mockItem.app === app) {
                    const descriptor = this.overridePropertyDescriptor(mockItem.value);
                    if (typeof mockItem.key === 'string') {
                        Object.defineProperty(ctx, mockItem.key, descriptor);
                    }
                    else {
                        mockItem.key(ctx);
                    }
                }
            }
        }
    }
    getContextMocksSize() {
        return this.contextMocks.length;
    }
    overridePropertyDescriptor(value) {
        const descriptor = {
            configurable: true,
            enumerable: true,
        };
        if (value && (value.get || value.set)) {
            // Default to undefined
            descriptor.get = value.get;
            descriptor.set = value.set;
        }
        else {
            // Without getter/setter mode
            descriptor.value = value;
            descriptor.writable = true;
        }
        return descriptor;
    }
    async initSimulation() {
        const simulationModule = (0, decorator_1.listModule)(decorator_1.MOCK_KEY);
        for (const module of simulationModule) {
            const instance = await this.applicationContext.getAsync(module);
            if (await instance.enableCondition()) {
                this.simulatorList.push(instance);
            }
        }
    }
    async runSimulatorSetup() {
        var _a;
        for (const simulator of this.simulatorList) {
            await ((_a = simulator.setup) === null || _a === void 0 ? void 0 : _a.call(simulator));
        }
    }
    async runSimulatorTearDown() {
        var _a;
        // reverse loop and not change origin simulatorList
        for (let i = this.simulatorList.length - 1; i >= 0; i--) {
            const simulator = this.simulatorList[i];
            await ((_a = simulator.tearDown) === null || _a === void 0 ? void 0 : _a.call(simulator));
        }
    }
    async runSimulatorAppSetup(app) {
        var _a;
        for (const simulator of this.simulatorList) {
            await ((_a = simulator.appSetup) === null || _a === void 0 ? void 0 : _a.call(simulator, app));
        }
    }
    async runSimulatorAppTearDown(app) {
        var _a;
        // reverse loop and not change origin simulatorList
        for (let i = this.simulatorList.length - 1; i >= 0; i--) {
            const simulator = this.simulatorList[i];
            await ((_a = simulator.appTearDown) === null || _a === void 0 ? void 0 : _a.call(simulator, app));
        }
    }
    async runSimulatorContextSetup(ctx, app) {
        var _a;
        for (const simulator of this.simulatorList) {
            await ((_a = simulator.contextSetup) === null || _a === void 0 ? void 0 : _a.call(simulator, ctx, app));
        }
    }
    async runSimulatorContextTearDown(ctx, app) {
        var _a;
        // reverse loop and not change origin simulatorList
        for (let i = this.simulatorList.length - 1; i >= 0; i--) {
            const simulator = this.simulatorList[i];
            await ((_a = simulator === null || simulator === void 0 ? void 0 : simulator.contextTearDown) === null || _a === void 0 ? void 0 : _a.call(simulator, ctx, app));
        }
    }
};
MidwayMockService.prepareMocks = [];
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MidwayMockService.prototype, "init", null);
__decorate([
    (0, decorator_1.Destroy)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MidwayMockService.prototype, "restore", null);
MidwayMockService = MidwayMockService_1 = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayMockService);
exports.MidwayMockService = MidwayMockService;
//# sourceMappingURL=mockService.js.map