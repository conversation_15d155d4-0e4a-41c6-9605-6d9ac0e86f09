import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 商户/店铺
 */
@Entity('merchant')
export class MerchantEntity extends BaseEntity {
  @Column({ comment: '商户类型 0-个人 1-企业', default: 0 })
  type!: number;

  @Column({ comment: '商户名称' })
  name!: string;

  @Column({ comment: '联系人' })
  contact!: string;

  @Column({ comment: '联系电话' })
  phone!: string;

  @Column({ comment: '身份证号', nullable: true })
  idCard!: string;

  @Column({ comment: '身份证正面', nullable: true })
  idCardFront!: string;

  @Column({ comment: '身份证反面', nullable: true })
  idCardBack!: string;

  @Column({ comment: '入驻商品类别（单选）', nullable: true })
  category!: string;

  @Column({ comment: '店铺简介/创作背景', nullable: true })
  intro!: string;

  @Column({ comment: '银行卡号', nullable: true })
  bankAccount!: string;

  @Column({ comment: '开户行', nullable: true })
  bankName!: string;

  @Column({ comment: '持卡人姓名', nullable: true })
  bankUserName!: string;

  @Column({ comment: '银行卡预留手机号', nullable: true })
  bankPhone!: string;

  @Column({ comment: '原创承诺书', nullable: true })
  promise!: string;

  @Column({ comment: '企业名称', nullable: true })
  companyName!: string;

  @Column({ comment: '统一社会信用代码', nullable: true })
  creditCode!: string;

  @Column({ comment: '营业执照图片', nullable: true })
  licenseImg!: string;

  @Column({ comment: '法人姓名', nullable: true })
  legalPerson!: string;

  @Column({ comment: '法人身份证号', nullable: true })
  legalIdCard!: string;

  @Column({ comment: '法人身份证正面', nullable: true })
  legalIdCardFront!: string;

  @Column({ comment: '法人身份证反面', nullable: true })
  legalIdCardBack!: string;

  @Column({ comment: '项目相关非遗认证/授权证明', nullable: true, type: 'json' })
  projectCert!: string[];

  @Column({ comment: '品牌logo', nullable: true })
  brandLogo!: string;

  @Column({ comment: '品牌/机构介绍', nullable: true })
  brandIntro!: string;

  @Column({ comment: '联系邮箱', nullable: true })
  email!: string;

  @Column({ comment: '状态 0-待审核 1-已通过 2-已拒绝', default: 0 })
  status!: number;

  @Column({ comment: '备注', nullable: true })
  remark!: string;
} 