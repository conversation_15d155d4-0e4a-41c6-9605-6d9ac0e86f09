"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CacheConfiguration = exports.getClassMethodDefaultCacheKey = void 0;
const core_1 = require("@midwayjs/core");
const cacheKey_1 = require("./decorator/cacheKey");
const factory_1 = require("./factory");
function getClassMethodDefaultCacheKey(target, methodName) {
    return target.name + '-' + (0, core_1.getProviderUUId)(target) + '-' + methodName;
}
exports.getClassMethodDefaultCacheKey = getClassMethodDefaultCacheKey;
let CacheConfiguration = class CacheConfiguration {
    async onReady(container) {
        // init factory and caching instance
        this.cacheService = await container.getAsync(factory_1.CachingFactory);
        // register @Caching decorator implementation
        this.decoratorService.registerMethodHandler(cacheKey_1.CACHE_DECORATOR_KEY, ({ target, propertyName, metadata }) => {
            if (!metadata.cacheKey) {
                metadata.cacheKey = getClassMethodDefaultCacheKey(target, propertyName);
            }
            return {
                around: async (joinPoint) => {
                    let cacheKey = metadata.cacheKey;
                    const cachingInstance = this.cacheService.get(metadata.cacheInstanceName);
                    if (typeof cacheKey === 'function') {
                        cacheKey = await cacheKey({
                            methodArgs: joinPoint.args,
                            ctx: joinPoint.target[core_1.REQUEST_OBJ_CTX_KEY],
                            target: joinPoint.target,
                        });
                    }
                    if (typeof cacheKey === 'string') {
                        return cachingInstance.methodWrap(cacheKey, joinPoint.proceed, joinPoint.args, metadata.ttl);
                    }
                    else {
                        return joinPoint.proceed(...joinPoint.args);
                    }
                },
            };
        });
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayDecoratorService)
], CacheConfiguration.prototype, "decoratorService", void 0);
CacheConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'cacheManager',
        importConfigs: [
            {
                default: {
                    cacheManager: {},
                },
            },
        ],
    })
], CacheConfiguration);
exports.CacheConfiguration = CacheConfiguration;
//# sourceMappingURL=configuration.js.map