{"version": 3, "sources": ["../browser/src/platform/BrowserDisabledDriversDummy.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AAEH;;;GAGG;AACH,MAAM,OAAO,WAAW;CAAG;AAE3B;;;GAGG;AACH,MAAM,OAAO,gBAAgB;CAAG;AAEhC;;;GAGG;AACH,MAAM,OAAO,kBAAkB;CAAG;AAElC;;;GAGG;AACH,MAAM,OAAO,eAAe;CAAG;AAE/B;;;GAGG;AACH,MAAM,OAAO,cAAc;CAAG;AAE9B;;;GAGG;AACH,MAAM,OAAO,iBAAiB;CAAG;AAEjC;;;GAGG;AACH,MAAM,OAAO,eAAe;CAAG;AAE/B;;;GAGG;AACH,MAAM,OAAO,oBAAoB;CAAG;AAEpC;;;GAGG;AACH,MAAM,OAAO,eAAe;CAAG;AAE/B;;;GAGG;AACH,MAAM,OAAO,SAAS;CAAG;AAEzB;;;GAGG;AACH,MAAM,OAAO,WAAW;CAAG;AAE3B;;;GAGG;AACH,MAAM,OAAO,YAAY;CAAG;AAE5B;;;GAGG;AACH,MAAM,OAAO,YAAY;CAAG;AAE5B;;;GAGG;AACH,MAAM,OAAO,mBAAmB;CAAG", "file": "BrowserDisabledDriversDummy.js", "sourcesContent": ["/**\n * Dummy driver classes for replacement via `package.json` in browser builds.\n * Using those classes reduces the build size by one third.\n *\n * If we don't include those dummy classes (and just disable the driver import\n * with `false` in `package.json`) typeorm will throw an error on runtime and\n * during webpack builds even if those driver are not used.\n */\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class MongoDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class MongoQueryRunner {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class MongoEntityManager {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class MongoRepository {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class PostgresDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class AuroraMysqlDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class CockroachDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class AuroraPostgresDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class SqlServerDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class SapDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class MysqlDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class OracleDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class SqliteDriver {}\n\n/**\n * DO NOT IMPORT THIS CLASS -\n * This is a dummy class for replacement via `package.json` in browser builds\n */\nexport class BetterSqlite3Driver {}\n"], "sourceRoot": ".."}