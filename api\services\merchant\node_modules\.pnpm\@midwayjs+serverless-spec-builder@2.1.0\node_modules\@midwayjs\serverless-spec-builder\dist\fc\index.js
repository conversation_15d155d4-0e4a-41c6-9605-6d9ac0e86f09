"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateComponentSpec = exports.generateFunctionsSpecFile = exports.generateFunctionsSpec = void 0;
const index_1 = require("../index");
const builder_1 = require("./builder");
const generateFunctionsSpec = filePathOrSpecJson => {
    return (0, index_1.transform)(filePathOrSpecJson, builder_1.FCSpecBuilder);
};
exports.generateFunctionsSpec = generateFunctionsSpec;
const generateFunctionsSpecFile = (filePathOrSpecJson, targetFilePath = 'template.yml') => {
    (0, index_1.generate)(filePathOrSpecJson, targetFilePath, builder_1.FCSpecBuilder);
};
exports.generateFunctionsSpecFile = generateFunctionsSpecFile;
const generateComponentSpec = filePathOrSpecJson => {
    return (0, index_1.transform)(filePathOrSpecJson, builder_1.FCComponentSpecBuilder);
};
exports.generateComponentSpec = generateComponentSpec;
//# sourceMappingURL=index.js.map