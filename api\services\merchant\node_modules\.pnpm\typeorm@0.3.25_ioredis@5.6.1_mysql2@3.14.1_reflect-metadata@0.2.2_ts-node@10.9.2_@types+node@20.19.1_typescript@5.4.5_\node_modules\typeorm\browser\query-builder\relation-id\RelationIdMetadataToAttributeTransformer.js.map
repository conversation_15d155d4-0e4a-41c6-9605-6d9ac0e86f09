{"version": 3, "sources": ["../browser/src/query-builder/relation-id/RelationIdMetadataToAttributeTransformer.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAA;AAI3D,MAAM,OAAO,wCAAwC;IACjD,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,aAAiC;QAAjC,kBAAa,GAAb,aAAa,CAAoB;IAAG,CAAC;IAE3D,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,SAAS;QACL,cAAc;QACd,wBAAwB;QACxB,mDAAmD;QACnD,2BAA2B;QAC3B,oDAAoD;QACpD,iCAAiC;QACjC,6EAA6E;QAE7E,oHAAoH;QACpH,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;YAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CACrD,CAAC,UAAU,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CACtC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI,EAClC,UAAU,CACb,CAAA;gBACD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC3D,CAAC,CACJ,CAAA;QACL,CAAC;QAED,mHAAmH;QACnH,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/C,2FAA2F;YAC3F,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU;gBAAE,OAAM;YAEtD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC7C,MAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CACtC,IAAI,CAAC,KAAK,CAAC,IAAI,EACf,UAAU,CACb,CAAA;gBACD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC3D,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED,4EAA4E;IAC5E,kBAAkB;IAClB,4EAA4E;IAEpE,mBAAmB,CACvB,eAAuB,EACvB,UAA8B;QAE9B,OAAO,IAAI,mBAAmB,CAAC,IAAI,CAAC,aAAa,EAAE;YAC/C,YAAY,EACR,eAAe,GAAG,GAAG,GAAG,UAAU,CAAC,QAAQ,CAAC,YAAY,EAAE,kBAAkB;YAChF,aAAa,EAAE,eAAe,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,EAAE,oBAAoB;YACpF,KAAK,EAAE,UAAU,CAAC,KAAK;YACvB,mBAAmB,EAAE,UAAU,CAAC,mBAAmB;SACtD,CAAC,CAAA;IACN,CAAC;CACJ", "file": "RelationIdMetadataToAttributeTransformer.js", "sourcesContent": ["import { RelationIdAttribute } from \"./RelationIdAttribute\"\nimport { QueryExpressionMap } from \"../QueryExpressionMap\"\nimport { RelationIdMetadata } from \"../../metadata/RelationIdMetadata\"\n\nexport class RelationIdMetadataToAttributeTransformer {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected expressionMap: QueryExpressionMap) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    transform() {\n        // by example:\n        // post has relation id:\n        // @RelationId(post => post.categories) categoryIds\n        // category has relation id\n        // @RelationId(category => category.images) imageIds\n        // we load post and join category\n        // we expect post.categoryIds and post.category.imageIds to have relation ids\n\n        // first create relation id attributes for all relation id metadatas of the main selected object (post from example)\n        if (this.expressionMap.mainAlias) {\n            this.expressionMap.mainAlias.metadata.relationIds.forEach(\n                (relationId) => {\n                    const attribute = this.metadataToAttribute(\n                        this.expressionMap.mainAlias!.name,\n                        relationId,\n                    )\n                    this.expressionMap.relationIdAttributes.push(attribute)\n                },\n            )\n        }\n\n        // second create relation id attributes for all relation id metadatas of all joined objects (category from example)\n        this.expressionMap.joinAttributes.forEach((join) => {\n            // ensure this join has a metadata, because relation id can only work for real orm entities\n            if (!join.metadata || join.metadata.isJunction) return\n\n            join.metadata.relationIds.forEach((relationId) => {\n                const attribute = this.metadataToAttribute(\n                    join.alias.name,\n                    relationId,\n                )\n                this.expressionMap.relationIdAttributes.push(attribute)\n            })\n        })\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Methods\n    // -------------------------------------------------------------------------\n\n    private metadataToAttribute(\n        parentAliasName: string,\n        relationId: RelationIdMetadata,\n    ): RelationIdAttribute {\n        return new RelationIdAttribute(this.expressionMap, {\n            relationName:\n                parentAliasName + \".\" + relationId.relation.propertyName, // category.images\n            mapToProperty: parentAliasName + \".\" + relationId.propertyName, // category.imageIds\n            alias: relationId.alias,\n            queryBuilderFactory: relationId.queryBuilderFactory,\n        })\n    }\n}\n"], "sourceRoot": "../.."}