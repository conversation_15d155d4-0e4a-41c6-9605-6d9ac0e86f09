{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;AAAA,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAE3B,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAI7B,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAE,GAAG,EAAE,MAAM,KAAK,CAAC;AAC1B,OAAO,EAAE,KAAK,EAAE,gBAAgB,EAAY,MAAM,YAAY,CAAC;AAC/D,OAAO,KAAK,EAAE,qBAAqB,EAAE,MAAM,kBAAkB,CAAC;AAC9D,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAChE,OAAO,KAAK,EAAE,sBAAsB,EAAE,MAAM,mBAAmB,CAAC;AAChE,OAAO,EAEN,SAAS,IAAI,UAAU,EACvB,YAAY,IAAI,aAAa,EAC7B,MAAM,SAAS,CAAC;AACjB,OAAO,EAEN,eAAe,EACf,kBAAkB,EAClB,MAAM,cAAc,CAAC;AAuBtB,KAAK,SAAS,GAAG,MAAM,OAAO,UAAU,CAAC;AAGzC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC,SAAS,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,GACrD,CAAC,GAEH,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,MAAM,CAAC,EAAE,GAC/B,CAAC,GACD,KAAK,CAAC;AAET,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,IAAI,CAAC,YAAY,GACtD,kBAAkB,GAClB,aAAa,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAChC,qBAAqB,CAAC,EAAE,CAAC,GACzB,sBAAsB,CAAC,EAAE,CAAC,GAC1B,sBAAsB,GAAG;IACxB,gBAAgB,CAAC,EAAE,OAAO,CAAC;CAC3B,CAAC;AAEH;;;;;;;;;;;GAWG;AACH,qBAAa,aAAa,CAAC,GAAG,SAAS,MAAM,CAAE,SAAQ,KAAK;IAC3D,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,SAAS,EAAE,EAAE,CAM7C;IAEF,GAAG,EAAE,GAAG,CAAC;IACT,IAAI,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAChC,KAAK,CAAC,EAAE,QAAQ,CAAC;IACjB,QAAQ,CAAC,EAAE,eAAe,CAAC;IAC3B,YAAY,EAAE,MAAM,CAAC;IACrB,eAAe,CAAC,EAAE,OAAO,CAAC,eAAe,CAAC,CAAC;gBAE/B,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,IAAI,CAAC,EAAE,oBAAoB,CAAC,GAAG,CAAC;IAsB5D,OAAO,CAAC,oBAAoB,CAE1B;IAEF;;;OAGG;IACH,WAAW,IAAI,OAAO,CAAC,eAAe,CAAC;YAWzB,YAAY;IAwC1B;;;;OAIG;YACW,WAAW;IAazB;;OAEG;IACG,OAAO,CACZ,GAAG,EAAE,IAAI,CAAC,aAAa,EACvB,IAAI,EAAE,gBAAgB,GACpB,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,CAAC;CA2GnC"}