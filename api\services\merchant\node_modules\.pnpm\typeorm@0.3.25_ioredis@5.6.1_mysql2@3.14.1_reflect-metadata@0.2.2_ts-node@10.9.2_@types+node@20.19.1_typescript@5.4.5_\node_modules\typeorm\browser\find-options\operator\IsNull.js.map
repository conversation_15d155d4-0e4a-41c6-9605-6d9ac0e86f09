{"version": 3, "sources": ["../browser/src/find-options/operator/IsNull.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,MAAM;IAClB,OAAO,IAAI,YAAY,CAAC,QAAQ,EAAE,SAAgB,EAAE,KAAK,CAAC,CAAA;AAC9D,CAAC", "file": "IsNull.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * Find Options Operator.\n * Example: { someField: IsNull() }\n */\nexport function IsNull() {\n    return new FindOperator(\"isNull\", undefined as any, false)\n}\n"], "sourceRoot": "../.."}