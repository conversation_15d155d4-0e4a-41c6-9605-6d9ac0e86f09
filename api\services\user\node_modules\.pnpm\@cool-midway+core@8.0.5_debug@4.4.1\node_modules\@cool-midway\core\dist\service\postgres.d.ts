import { Application, Context } from '@midwayjs/koa';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { QueryOp } from '../decorator/controller';
import { CoolEventManager } from '../event';
/**
 * 服务基类
 */
export declare abstract class BasePgService {
    private _coolConfig;
    entity: Repository<any>;
    sqlParams: any;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    coolEventManager: CoolEventManager;
    setEntity(entity: any): void;
    setCtx(ctx: Context): void;
    baseApp: Application;
    setApp(app: Application): void;
    baseCtx: Context;
    init(): void;
    /**
     * 设置sql
     * @param condition 条件是否成立
     * @param sql sql语句
     * @param params 参数
     */
    setSql(condition: any, sql: any, params: any): any;
    /**
     * 获得查询个数的SQL
     * @param sql
     */
    getCountSql(sql: any): string;
    /**
     * 参数安全性检查
     * @param params
     */
    paramSafetyCheck(params: any): Promise<boolean>;
    /**
     * 原生查询
     * @param sql
     * @param params
     * @param connectionName
     */
    nativeQuery(sql: any, params?: any, connectionName?: any): Promise<any>;
    /**
     * 获得ORM管理
     *  @param connectionName 连接名称
     */
    getOrmManager(connectionName?: string): import("typeorm").DataSource;
    /**
     * 操作entity获得分页数据，不用写sql
     * @param find QueryBuilder
     * @param query
     * @param autoSort
     * @param connectionName
     */
    entityRenderPage(find: SelectQueryBuilder<any>, query: any, autoSort?: boolean): Promise<{
        list: any[];
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    /**
     * 将mysql语句转换为postgres语句
     * @param sql
     * @returns
     */
    protected convertToPostgres(sql: any): any;
    /**
     * 查询sql中的参数个数
     * @param sql
     * @returns
     */
    protected countDollarSigns(sql: any): any;
    /**
     * 执行SQL并获得分页数据
     * @param sql 执行的sql语句
     * @param query 分页查询条件
     * @param autoSort 是否自动排序
     * @param connectionName 连接名称
     */
    sqlRenderPage(sql: any, query: any, autoSort?: boolean, connectionName?: any): Promise<{
        list: any;
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    /**
     * 检查排序
     * @param sort 排序
     * @returns
     */
    checkSort(sort: any): any;
    /**
     * 获得单个ID
     * @param id ID
     * @param infoIgnoreProperty 忽略返回属性
     */
    info(id: any, infoIgnoreProperty?: string[]): Promise<any>;
    /**
     * 删除
     * @param ids 删除的ID集合 如：[1,2,3] 或者 1,2,3
     */
    delete(ids: any): Promise<void>;
    /**
     * 软删除
     * @param ids 删除的ID数组
     * @param entity 实体
     */
    softDelete(ids: number[], entity?: Repository<any>): Promise<void>;
    /**
     * 新增|修改
     * @param param 数据
     */
    addOrUpdate(param: any | any[], type?: 'add' | 'update'): Promise<void>;
    /**
     * 非分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    list(query: any, option: any, connectionName?: any): Promise<any>;
    /**
     * 分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    page(query: any, option: any, connectionName?: any): Promise<{
        list: any;
        pagination: {
            page: number;
            size: number;
            total: number;
        };
    }>;
    /**
     * 构建查询配置
     * @param query 前端查询
     * @param option
     */
    getOptionFind(query: any, option: QueryOp): Promise<string>;
    /**
     * 替换sql中的表别名
     * @param sql
     * @returns
     */
    replaceOrderByPrefix(sql: any): any;
    /**
     * 筛选的字段匹配
     * @param select 筛选的字段
     * @param field 字段
     * @returns 字段在哪个表中
     */
    protected matchColumn(select: string[], field: string): string;
}
