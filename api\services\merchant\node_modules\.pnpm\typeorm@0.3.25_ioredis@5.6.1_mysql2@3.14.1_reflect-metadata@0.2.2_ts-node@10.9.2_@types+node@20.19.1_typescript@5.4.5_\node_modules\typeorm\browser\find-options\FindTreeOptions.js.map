{"version": 3, "sources": ["../browser/src/find-options/FindTreeOptions.ts"], "names": [], "mappings": "", "file": "FindTreeOptions.js", "sourcesContent": ["/**\n * Defines a special criteria to find specific entities.\n */\nexport interface FindTreeOptions {\n    /**\n     * Indicates what relations of entity should be loaded (simplified left join form).\n     */\n    relations?: string[]\n\n    /**\n     * When loading a tree from a TreeRepository, limits the depth of the descendents loaded\n     */\n    depth?: number\n}\n"], "sourceRoot": ".."}