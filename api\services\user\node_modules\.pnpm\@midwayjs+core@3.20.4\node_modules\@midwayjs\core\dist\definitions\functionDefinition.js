"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionDefinition = void 0;
const interface_1 = require("../interface");
const objectCreator_1 = require("./objectCreator");
class FunctionWrapperCreator extends objectCreator_1.ObjectCreator {
    doConstruct(Clzz, args, context) {
        if (!Clzz) {
            return null;
        }
        return Clzz(context, args);
    }
    async doConstructAsync(Clzz, args, context) {
        if (!Clzz) {
            return null;
        }
        return Clzz(context, args);
    }
}
class FunctionDefinition {
    constructor() {
        this.constructorArgs = [];
        this.namespace = '';
        this.asynchronous = true;
        this.handlerProps = [];
        this.allowDowngrade = false;
        // 函数工厂创建的对象默认不需要自动装配
        this.innerAutowire = false;
        this.innerScope = interface_1.ScopeEnum.Singleton;
        this.creator = new FunctionWrapperCreator(this);
    }
    getAttr(key) { }
    hasAttr(key) {
        return false;
    }
    hasConstructorArgs() {
        return false;
    }
    hasDependsOn() {
        return false;
    }
    isAsync() {
        return this.asynchronous;
    }
    isDirect() {
        return false;
    }
    isExternal() {
        return false;
    }
    set scope(scope) {
        this.innerScope = scope;
    }
    isSingletonScope() {
        return this.innerScope === interface_1.ScopeEnum.Singleton;
    }
    isRequestScope() {
        return this.innerScope === interface_1.ScopeEnum.Request;
    }
    setAttr(key, value) { }
}
exports.FunctionDefinition = FunctionDefinition;
//# sourceMappingURL=functionDefinition.js.map