{"version": 3, "file": "no-unused-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/no-unused-expressions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,uBAAuB,CAAC,CAAC;AAK5D,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,6BAA6B;YAC1C,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,+FAA+F;QAC/F,QAAQ,EAAE,MAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,mCAAI;YAClC,gBAAgB,EACd,wEAAwE;SAC3E;KACF;IACD,cAAc,EAAE;QACd;YACE,iBAAiB,EAAE,KAAK;YACxB,YAAY,EAAE,KAAK;YACnB,oBAAoB,EAAE,KAAK;SAC5B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,iBAAiB,GAAG,KAAK,EAAE,YAAY,GAAG,KAAK,EAAE,CAAC;QACnE,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,iBAAiB,CAAC,IAAmB;YAC5C,IAAI,iBAAiB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBACvE,OAAO,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtC;YACD,IAAI,YAAY,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE;gBACtE,OAAO,CACL,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC;oBACjC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CACnC,CAAC;aACH;YACD,OAAO,CACL,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;gBACzD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,CAC9C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACxD,OAAO;iBACR;gBAED,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}