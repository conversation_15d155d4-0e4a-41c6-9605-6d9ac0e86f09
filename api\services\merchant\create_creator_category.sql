-- 创作者认证分类表创建脚本
-- 请在merchant_service_db数据库中执行此脚本

USE merchant_service_db;

-- 创建创作者认证分类表
CREATE TABLE IF NOT EXISTS creator_category (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(100) NOT NULL COMMENT '分类名称',
  authType tinyint(1) NOT NULL DEFAULT '0' COMMENT '认证类型 0-手工艺人 1-非遗传承人 2-通用',
  description text COMMENT '分类描述',
  requirements text COMMENT '认证要求',
  status tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 0-禁用 1-启用',
  createTime datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updateTime datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (id),
  KEY idx_auth_type (authType),
  KEY idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='创作者认证分类表';

-- 插入初始数据
INSERT INTO creator_category (name, authType, description, requirements, status) VALUES
('陶瓷制作', 0, '传统陶瓷制作工艺认证', '需要提供陶瓷作品集、相关培训证书或师承证明', 1),
('木雕工艺', 0, '传统木雕工艺认证', '需要提供木雕作品集、工艺技能证书', 1),
('蜀绣', 1, '四川蜀绣非遗传承认证', '需要提供非遗传承人证书、蜀绣作品集、师承关系证明', 1),
('景德镇瓷器', 1, '景德镇传统制瓷技艺', '需要提供非遗传承人证书、瓷器作品集、技艺传承证明', 1),
('剪纸艺术', 0, '传统剪纸工艺认证', '需要提供剪纸作品集、相关培训证书', 1);

-- 为认证分类菜单添加基本权限
UPDATE merchant_sys_menu 
SET perms = 'add,edit,delete'
WHERE id = 203 AND name = '认证分类';

-- 查看创建结果
SELECT '=== 创作者认证分类表数据 ===' as info;
SELECT * FROM creator_category ORDER BY id;

SELECT '=== 菜单权限配置 ===' as info;
SELECT id, name, router, perms FROM merchant_sys_menu WHERE id = 203;
