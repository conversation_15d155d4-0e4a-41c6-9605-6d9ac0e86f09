{"version": 3, "sources": ["../browser/src/metadata-builder/RelationJoinColumnBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAA;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,4BAA4B,CAAA;AAC3D,OAAO,EAAE,kBAAkB,EAAE,MAAM,gCAAgC,CAAA;AAInE,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AACnD,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAA;AAE3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,MAAM,OAAO,yBAAyB;IAClC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAE9C,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CACD,WAAqC,EACrC,QAA0B;QAM1B,MAAM,iBAAiB,GAAG,IAAI,CAAC,wBAAwB,CACnD,WAAW,EACX,QAAQ,CACX,CAAA;QACD,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAC/B,WAAW,EACX,QAAQ,EACR,iBAAiB,CACpB,CAAA;QACD,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,2BAA2B;YAClE,OAAO;gBACH,UAAU,EAAE,SAAS;gBACrB,OAAO;gBACP,gBAAgB,EAAE,SAAS;aAC9B,CAAA,CAAC,8GAA8G;QAEpH,MAAM,UAAU,GAAG,IAAI,kBAAkB,CAAC;YACtC,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,EAAE,wBAAwB;YAC9C,cAAc,EAAE,QAAQ,CAAC,cAAc;YACvC,wBAAwB,EAAE,QAAQ,CAAC,qBAAqB;YACxD,cAAc,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc;YAC9C,OAAO;YACP,iBAAiB;YACjB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,UAAU,EAAE,QAAQ,CAAC,UAAU;SAClC,CAAC,CAAA;QAEF,mEAAmE;QACnE,oEAAoE;QACpE,gEAAgE;QAChE,IACI,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC;YAC3C,CAAC,QAAQ,CAAC,UAAU,EACtB,CAAC;YACC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAA;QAC/D,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAC;YACxC,cAAc,EAAE,QAAQ,CAAC,cAAc;YACvC,OAAO,EAAE,UAAU,CAAC,OAAO;YAC3B,IAAI,EAAE;gBACF,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,sBAAsB,CACvD,QAAQ,CAAC,cAAc,CAAC,SAAS,EACjC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAC1D;gBACD,MAAM,EAAE,QAAQ,CAAC,cAAc,CAAC,MAAM;aACzC;SACJ,CAAC,CAAA;QACF,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;QAEtD,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAA;IACpD,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,wBAAwB,CAC9B,WAAqC,EACrC,QAA0B;QAE1B,MAAM,0BAA0B,GAAG,WAAW,CAAC,IAAI,CAC/C,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAC5D,CAAA;QACD,MAAM,0BAA0B,GAC5B,WAAW,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,WAAW,CAAA;QACpD,MAAM,2CAA2C,GAC7C,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,0BAA0B,CAAA;QAEzD,IACI,0BAA0B;YAC1B,2CAA2C,EAC7C,CAAC;YACC,yBAAyB;YACzB,OAAO,QAAQ,CAAC,qBAAqB,CAAC,cAAc,CAAA;QACxD,CAAC;aAAM,CAAC;YACJ,wCAAwC;YACxC,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAClC,MAAM,gBAAgB,GAClB,QAAQ,CAAC,qBAAqB,CAAC,UAAU,CAAC,IAAI,CAC1C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY;oBACnB,UAAU,CAAC,oBAAoB,CACtC,CAAA,CAAC,yCAAyC;gBAC/C,IAAI,CAAC,gBAAgB;oBACjB,MAAM,IAAI,YAAY,CAClB,qBAAqB,UAAU,CAAC,oBAAoB,4BAA4B,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,CACxH,CAAA;gBAEL,OAAO,gBAAgB,CAAA;YAC3B,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACK,cAAc,CAClB,WAAqC,EACrC,QAA0B,EAC1B,iBAAmC;QAEnC,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE;YAC9C,gFAAgF;YAChF,MAAM,qBAAqB,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC1D,OAAO,CACH,CAAC,CAAC,UAAU,CAAC,oBAAoB;oBAC7B,UAAU,CAAC,oBAAoB;wBAC3B,gBAAgB,CAAC,YAAY,CAAC;oBACtC,CAAC,CAAC,UAAU,CAAC,IAAI,CACpB,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,cAAc,GAAG,qBAAqB;gBACxC,CAAC,CAAC,qBAAqB,CAAC,IAAI;gBAC5B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,CAAC,YAAY,EACrB,gBAAgB,CAAC,YAAY,CAChC,CAAA;YAEP,MAAM,iBAAiB,GAAG,QAAQ,CAAC,gBAAgB;gBAC/C,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO;gBACnC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAA;YACxC,IAAI,gBAAgB,GAAG,iBAAiB,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,2BAA2B,KAAK,cAAc,CAC5D,CAAA;YACD,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACpB,gBAAgB,GAAG,IAAI,cAAc,CAAC;oBAClC,UAAU,EAAE,IAAI,CAAC,UAAU;oBAC3B,cAAc,EAAE,QAAQ,CAAC,cAAc;oBACvC,gBAAgB,EAAE,QAAQ,CAAC,gBAAgB;oBAC3C,IAAI,EAAE;wBACF,MAAM,EAAE,EAAE;wBACV,IAAI,EAAE,SAAS;wBACf,YAAY,EAAE,QAAQ,CAAC,YAAY;wBACnC,OAAO,EAAE;4BACL,IAAI,EAAE,cAAc;4BACpB,IAAI,EAAE,gBAAgB,CAAC,IAAI;4BAC3B,MAAM,EACF,CAAC,gBAAgB,CAAC,MAAM;gCACxB,CAAC,WAAW,CAAC,aAAa,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;oCACG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI;wCAC/B,cAAc,CAAC;gCACvB,qGAAqG;gCACrG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAChC,gBAAgB,CACnB,KAAK,MAAM;gCACZ,CAAC,gBAAgB,CAAC,kBAAkB;oCAChC,MAAM;oCACN,gBAAgB,CAAC,IAAI,KAAK,MAAM,CAAC;gCACjC,CAAC,CAAC,IAAI;gCACN,CAAC,CAAC,gBAAgB,CAAC,MAAM,EAAE,qDAAqD;4BACxF,KAAK,EAAE,gBAAgB,CAAC,KAAK;4BAC7B,OAAO,EAAE,gBAAgB,CAAC,OAAO;4BACjC,SAAS,EAAE,gBAAgB,CAAC,SAAS;4BACrC,SAAS,EAAE,gBAAgB,CAAC,SAAS;4BACrC,KAAK,EAAE,gBAAgB,CAAC,KAAK;4BAC7B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;4BACnC,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;4BACnC,OAAO,EAAE,gBAAgB,CAAC,OAAO;4BACjC,IAAI,EAAE,gBAAgB,CAAC,IAAI;4BAC3B,QAAQ,EAAE,gBAAgB,CAAC,QAAQ;4BACnC,OAAO,EAAE,QAAQ,CAAC,SAAS;4BAC3B,QAAQ,EAAE,QAAQ,CAAC,UAAU;yBAChC;qBACJ;iBACJ,CAAC,CAAA;gBACF,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,gBAAgB,CAAC,CAAA;YAC5D,CAAC;iBAAM,IAAI,gBAAgB,CAAC,gBAAgB,EAAE,CAAC;gBAC3C,8EAA8E;gBAC9E,+EAA+E;gBAC/E,8DAA8D;gBAC9D,gBAAgB,GAAG,QAAQ,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAA;YAC7D,CAAC;YACD,gBAAgB,CAAC,gBAAgB,GAAG,gBAAgB,CAAA,CAAC,qGAAqG;YAC1J,gBAAgB,CAAC,IAAI,GAAG,gBAAgB,CAAC,IAAI,CAAA,CAAC,2GAA2G;YACzJ,gBAAgB,CAAC,gBAAgB,GAAG,QAAQ,CAAA;YAC5C,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACvC,OAAO,gBAAgB,CAAA;QAC3B,CAAC,CAAC,CAAA;IACN,CAAC;CACJ", "file": "RelationJoinColumnBuilder.js", "sourcesContent": ["import { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { UniqueMetadata } from \"../metadata/UniqueMetadata\"\nimport { ForeignKeyMetadata } from \"../metadata/ForeignKeyMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { JoinColumnMetadataArgs } from \"../metadata-args/JoinColumnMetadataArgs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { OrmUtils } from \"../util/OrmUtils\"\n\n/**\n * Builds join column for the many-to-one and one-to-one owner relations.\n *\n * Cases it should cover:\n * 1. when join column is set with custom name and without referenced column name\n * we need automatically set referenced column name - primary ids by default\n * @JoinColumn({ name: \"custom_name\" })\n *\n * 2. when join column is set with only referenced column name\n * we need automatically set join column name - relation name + referenced column name\n * @JoinColumn({ referencedColumnName: \"title\" })\n *\n * 3. when join column is set without both referenced column name and join column name\n * we need to automatically set both of them\n * @JoinColumn()\n *\n * 4. when join column is not set at all (as in case of @ManyToOne relation)\n * we need to create join column for it with proper referenced column name and join column name\n *\n * 5. when multiple join columns set none of referencedColumnName and name can be optional\n * both options are required\n * @JoinColumn([\n *      { name: \"category_title\", referencedColumnName: \"type\" },\n *      { name: \"category_title\", referencedColumnName: \"name\" },\n * ])\n *\n * Since for many-to-one relations having JoinColumn decorator is not required,\n * we need to go through each many-to-one relation without join column decorator set\n * and create join column metadata args for them.\n */\nexport class RelationJoinColumnBuilder {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(private connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Builds a foreign key of the many-to-one or one-to-one owner relations.\n     */\n    build(\n        joinColumns: JoinColumnMetadataArgs[],\n        relation: RelationMetadata,\n    ): {\n        foreignKey: ForeignKeyMetadata | undefined\n        columns: ColumnMetadata[]\n        uniqueConstraint: UniqueMetadata | undefined\n    } {\n        const referencedColumns = this.collectReferencedColumns(\n            joinColumns,\n            relation,\n        )\n        const columns = this.collectColumns(\n            joinColumns,\n            relation,\n            referencedColumns,\n        )\n        if (!referencedColumns.length || !relation.createForeignKeyConstraints)\n            return {\n                foreignKey: undefined,\n                columns,\n                uniqueConstraint: undefined,\n            } // this case is possible for one-to-one non owning side and relations with createForeignKeyConstraints = false\n\n        const foreignKey = new ForeignKeyMetadata({\n            name: joinColumns[0]?.foreignKeyConstraintName,\n            entityMetadata: relation.entityMetadata,\n            referencedEntityMetadata: relation.inverseEntityMetadata,\n            namingStrategy: this.connection.namingStrategy,\n            columns,\n            referencedColumns,\n            onDelete: relation.onDelete,\n            onUpdate: relation.onUpdate,\n            deferrable: relation.deferrable,\n        })\n\n        // SQL requires UNIQUE/PK constraints on columns referenced by a FK\n        // Skip creating the unique constraint for the referenced columns if\n        // they are already contained in the PK of the referenced entity\n        if (\n            columns.every((column) => column.isPrimary) ||\n            !relation.isOneToOne\n        ) {\n            return { foreignKey, columns, uniqueConstraint: undefined }\n        }\n\n        const uniqueConstraint = new UniqueMetadata({\n            entityMetadata: relation.entityMetadata,\n            columns: foreignKey.columns,\n            args: {\n                name: this.connection.namingStrategy.relationConstraintName(\n                    relation.entityMetadata.tableName,\n                    foreignKey.columns.map((column) => column.databaseName),\n                ),\n                target: relation.entityMetadata.target,\n            },\n        })\n        uniqueConstraint.build(this.connection.namingStrategy)\n\n        return { foreignKey, columns, uniqueConstraint }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Collects referenced columns from the given join column args.\n     */\n    protected collectReferencedColumns(\n        joinColumns: JoinColumnMetadataArgs[],\n        relation: RelationMetadata,\n    ): ColumnMetadata[] {\n        const hasAnyReferencedColumnName = joinColumns.find(\n            (joinColumnArgs) => !!joinColumnArgs.referencedColumnName,\n        )\n        const manyToOneWithoutJoinColumn =\n            joinColumns.length === 0 && relation.isManyToOne\n        const hasJoinColumnWithoutAnyReferencedColumnName =\n            joinColumns.length > 0 && !hasAnyReferencedColumnName\n\n        if (\n            manyToOneWithoutJoinColumn ||\n            hasJoinColumnWithoutAnyReferencedColumnName\n        ) {\n            // covers case3 and case1\n            return relation.inverseEntityMetadata.primaryColumns\n        } else {\n            // cases with referenced columns defined\n            return joinColumns.map((joinColumn) => {\n                const referencedColumn =\n                    relation.inverseEntityMetadata.ownColumns.find(\n                        (column) =>\n                            column.propertyName ===\n                            joinColumn.referencedColumnName,\n                    ) // todo: can we also search in relations?\n                if (!referencedColumn)\n                    throw new TypeORMError(\n                        `Referenced column ${joinColumn.referencedColumnName} was not found in entity ${relation.inverseEntityMetadata.name}`,\n                    )\n\n                return referencedColumn\n            })\n        }\n    }\n\n    /**\n     * Collects columns from the given join column args.\n     */\n    private collectColumns(\n        joinColumns: JoinColumnMetadataArgs[],\n        relation: RelationMetadata,\n        referencedColumns: ColumnMetadata[],\n    ): ColumnMetadata[] {\n        return referencedColumns.map((referencedColumn) => {\n            // in the case if relation has join column with only name set we need this check\n            const joinColumnMetadataArg = joinColumns.find((joinColumn) => {\n                return (\n                    (!joinColumn.referencedColumnName ||\n                        joinColumn.referencedColumnName ===\n                            referencedColumn.propertyName) &&\n                    !!joinColumn.name\n                )\n            })\n            const joinColumnName = joinColumnMetadataArg\n                ? joinColumnMetadataArg.name\n                : this.connection.namingStrategy.joinColumnName(\n                      relation.propertyName,\n                      referencedColumn.propertyName,\n                  )\n\n            const relationalColumns = relation.embeddedMetadata\n                ? relation.embeddedMetadata.columns\n                : relation.entityMetadata.ownColumns\n            let relationalColumn = relationalColumns.find(\n                (column) =>\n                    column.databaseNameWithoutPrefixes === joinColumnName,\n            )\n            if (!relationalColumn) {\n                relationalColumn = new ColumnMetadata({\n                    connection: this.connection,\n                    entityMetadata: relation.entityMetadata,\n                    embeddedMetadata: relation.embeddedMetadata,\n                    args: {\n                        target: \"\",\n                        mode: \"virtual\",\n                        propertyName: relation.propertyName,\n                        options: {\n                            name: joinColumnName,\n                            type: referencedColumn.type,\n                            length:\n                                !referencedColumn.length &&\n                                (DriverUtils.isMySQLFamily(\n                                    this.connection.driver,\n                                ) ||\n                                    this.connection.driver.options.type ===\n                                        \"aurora-mysql\") &&\n                                // some versions of mariadb support the column type and should not try to provide the length property\n                                this.connection.driver.normalizeType(\n                                    referencedColumn,\n                                ) !== \"uuid\" &&\n                                (referencedColumn.generationStrategy ===\n                                    \"uuid\" ||\n                                    referencedColumn.type === \"uuid\")\n                                    ? \"36\"\n                                    : referencedColumn.length, // fix https://github.com/typeorm/typeorm/issues/3604\n                            width: referencedColumn.width,\n                            charset: referencedColumn.charset,\n                            collation: referencedColumn.collation,\n                            precision: referencedColumn.precision,\n                            scale: referencedColumn.scale,\n                            zerofill: referencedColumn.zerofill,\n                            unsigned: referencedColumn.unsigned,\n                            comment: referencedColumn.comment,\n                            enum: referencedColumn.enum,\n                            enumName: referencedColumn.enumName,\n                            primary: relation.isPrimary,\n                            nullable: relation.isNullable,\n                        },\n                    },\n                })\n                relation.entityMetadata.registerColumn(relationalColumn)\n            } else if (relationalColumn.referencedColumn) {\n                // Clone the relational column to prevent modifying the original when multiple\n                // relations reference the same column. This ensures each relation gets its own\n                // copy with independent referencedColumn and type properties.\n                relationalColumn = OrmUtils.cloneObject(relationalColumn)\n            }\n            relationalColumn.referencedColumn = referencedColumn // its important to set it here because we need to set referenced column for user defined join column\n            relationalColumn.type = referencedColumn.type // also since types of relational column and join column must be equal we override user defined column type\n            relationalColumn.relationMetadata = relation\n            relationalColumn.build(this.connection)\n            return relationalColumn\n        })\n    }\n}\n"], "sourceRoot": ".."}