{"version": 3, "sources": ["../browser/src/find-options/FindOptionsOrder.ts"], "names": [], "mappings": "", "file": "FindOptionsOrder.js", "sourcesContent": ["import { ObjectId } from \"../driver/mongodb/typings\"\n\n/**\n * A single property handler for FindOptionsOrder.\n */\nexport type FindOptionsOrderProperty<Property> = Property extends Promise<\n    infer I\n>\n    ? FindOptionsOrderProperty<NonNullable<I>>\n    : Property extends Array<infer I>\n    ? FindOptionsOrderProperty<NonNullable<I>>\n    : Property extends Function\n    ? never\n    : Property extends string\n    ? FindOptionsOrderValue\n    : Property extends number\n    ? FindOptionsOrderValue\n    : Property extends boolean\n    ? FindOptionsOrderValue\n    : Property extends Buffer\n    ? FindOptionsOrderValue\n    : Property extends Date\n    ? FindOptionsOrderValue\n    : Property extends ObjectId\n    ? FindOptionsOrderValue\n    : Property extends object\n    ? FindOptionsOrder<Property> | FindOptionsOrderValue\n    : FindOptionsOrderValue\n\n/**\n * Order by find options.\n */\nexport type FindOptionsOrder<Entity> = {\n    [P in keyof Entity]?: P extends \"toString\"\n        ? unknown\n        : FindOptionsOrderProperty<NonNullable<Entity[P]>>\n}\n\n/**\n * Value of order by in find options.\n */\nexport type FindOptionsOrderValue =\n    | \"ASC\"\n    | \"DESC\"\n    | \"asc\"\n    | \"desc\"\n    | 1\n    | -1\n    | {\n          direction?: \"asc\" | \"desc\" | \"ASC\" | \"DESC\"\n          nulls?: \"first\" | \"last\" | \"FIRST\" | \"LAST\"\n      }\n"], "sourceRoot": ".."}