{"version": 3, "sources": ["../../src/commands/CacheClearCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,iBAAiB;IAA9B;QACI,YAAO,GAAG,aAAa,CAAA;QACvB,aAAQ,GAAG,+CAA+C,CAAA;IA8C9D,CAAC;IA5CG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;YAC7B,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACN,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,QAAQ,CAAC;aACtB,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBAC/B,6BAAa,CAAC,SAAS,CACnB,2EAA2E,CAC9E,CAAA;gBACD,OAAM;YACV,CAAC;YAED,MAAM,UAAU,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAA;YACzC,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,KAAK,CAAA,gCAAgC,CAAC,CAAA;YAEvD,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAA;YAEzD,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAO,UAAyB,CAAC,OAAO,EAAE,CAAA;YAE9C,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AAhDD,8CAgDC", "file": "CacheClearCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Clear cache command.\n */\nexport class CacheClearCommand implements yargs.CommandModule {\n    command = \"cache:clear\"\n    describe = \"Clears all data stored in query runner cache.\"\n\n    builder(args: yargs.Argv) {\n        return args.option(\"dataSource\", {\n            alias: \"d\",\n            describe:\n                \"Path to the file where your DataSource instance is defined.\",\n            demandOption: true,\n        })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                subscribers: [],\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"schema\"],\n            })\n            await dataSource.initialize()\n\n            if (!dataSource.queryResultCache) {\n                PlatformTools.logCmdErr(\n                    \"Cache is not enabled. To use cache enable it in connection configuration.\",\n                )\n                return\n            }\n\n            await dataSource.queryResultCache.clear()\n            console.log(ansi.green`Cache was successfully cleared`)\n\n            await dataSource.destroy()\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during cache clear.\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await (dataSource as DataSource).destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}