"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpcTransaction = exports.COOL_RPC_TRANSACTION = void 0;
const core_1 = require("@midwayjs/core");
// 装饰器内部的唯一 id
exports.COOL_RPC_TRANSACTION = 'decorator:cool_rpc_transaction';
function CoolRpcTransaction(option) {
    return (0, core_1.createCustomMethodDecorator)(exports.COOL_RPC_TRANSACTION, option);
}
exports.CoolRpcTransaction = CoolRpcTransaction;
