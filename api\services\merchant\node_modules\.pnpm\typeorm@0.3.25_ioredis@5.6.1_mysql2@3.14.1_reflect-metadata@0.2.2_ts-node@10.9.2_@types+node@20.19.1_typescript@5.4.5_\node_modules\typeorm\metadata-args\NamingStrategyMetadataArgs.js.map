{"version": 3, "sources": ["../../src/metadata-args/NamingStrategyMetadataArgs.ts"], "names": [], "mappings": "", "file": "NamingStrategyMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for NamingStrategyMetadata class.\n */\nexport interface NamingStrategyMetadataArgs {\n    /**\n     * Class to which this column is applied.\n     */\n    readonly target: Function\n\n    /**\n     * Strategy name.\n     */\n    readonly name: string\n}\n"], "sourceRoot": ".."}