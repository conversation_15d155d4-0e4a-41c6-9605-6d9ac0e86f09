{"version": 3, "sources": ["../../src/globals.ts"], "names": [], "mappings": ";;AAsBA,wDAcC;AAOD,oDAIC;AAOD,oDAEC;AAmCD,4CASC;AAWD,8CAYC;AAQD,sCAEC;AAQD,gCAEC;AAQD,0CAKC;AASD,0CAKC;AAOD,sCAOC;AAOD,8CAOC;AAOD,kDAOC;AAOD,gDAOC;AAOD,gDAYC;AArPD,6EAAyE;AACzE,4DAAwD;AAExD,kFAA8E;AAC9E,sEAAkE;AAClE,2CAA8C;AAW9C,oDAAgD;AAGhD;;GAEG;AACH,SAAgB,sBAAsB;IAClC,8FAA8F;IAC9F,qGAAqG;IACrG,mGAAmG;IACnG,8EAA8E;IAC9E,6FAA6F;IAC7F,8FAA8F;IAC9F,iFAAiF;IACjF,4FAA4F;IAC5F,MAAM,WAAW,GAAG,6BAAa,CAAC,iBAAiB,EAAE,CAAA;IACrD,IAAI,CAAC,WAAW,CAAC,0BAA0B;QACvC,WAAW,CAAC,0BAA0B,GAAG,IAAI,yCAAmB,EAAE,CAAA;IAEtE,OAAO,WAAW,CAAC,0BAA0B,CAAA;AACjD,CAAC;AAED;;;;GAIG;AACI,KAAK,UAAU,oBAAoB,CACtC,iBAAyB,SAAS;IAElC,OAAO,IAAI,iDAAuB,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;AAC5D,CAAC;AAED;;;;GAIG;AACH,SAAgB,oBAAoB;IAChC,OAAO,IAAA,4BAAgB,EAAC,qCAAiB,CAAC,CAAA;AAC9C,CAAC;AA0BD;;;;;;;;GAQG;AACI,KAAK,UAAU,gBAAgB,CAClC,aAAmB;IAEnB,MAAM,cAAc,GAChB,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS,CAAA;IACjE,MAAM,OAAO,GAAG,yBAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;QAC/C,CAAC,CAAE,aAAmC;QACtC,CAAC,CAAC,MAAM,oBAAoB,CAAC,cAAc,CAAC,CAAA;IAChD,OAAO,oBAAoB,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAA;AAC3D,CAAC;AAED;;;;;;;;GAQG;AACI,KAAK,UAAU,iBAAiB,CACnC,OAA6B;IAE7B,IAAI,CAAC,OAAO;QAAE,OAAO,GAAG,MAAM,IAAI,iDAAuB,EAAE,CAAC,GAAG,EAAE,CAAA;IACjE,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE,CACxC,oBAAoB,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,CACzC,CAAA;IACD,gFAAgF;IAChF,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACnC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;IAC9B,CAAC;IACD,OAAO,WAAW,CAAA;AACtB,CAAC;AAED;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,iBAAyB,SAAS;IAC5D,OAAO,oBAAoB,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;AACrD,CAAC;AAED;;;;;GAKG;AACH,SAAgB,UAAU,CAAC,iBAAyB,SAAS;IACzD,OAAO,oBAAoB,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,OAAO,CAAA;AAC7D,CAAC;AAED;;;;;GAKG;AACH,SAAgB,eAAe,CAC3B,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC;SAC5C,OAA6B,CAAA;AACtC,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,eAAe,CAC3B,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC;SAC5C,OAA6B,CAAA;AACtC,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CACzB,WAAiC,EACjC,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE;SACxB,GAAG,CAAC,cAAc,CAAC;SACnB,aAAa,CAAS,WAAW,CAAC,CAAA;AAC3C,CAAC;AAED;;;;GAIG;AACH,SAAgB,iBAAiB,CAC7B,WAAiC,EACjC,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE;SACxB,GAAG,CAAC,cAAc,CAAC;SACnB,iBAAiB,CAAS,WAAW,CAAC,CAAA;AAC/C,CAAC;AAED;;;;GAIG;AACH,SAAgB,mBAAmB,CAC/B,gBAA+B,EAC/B,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE;SACxB,GAAG,CAAC,cAAc,CAAC;SACnB,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;AAC9C,CAAC;AAED;;;;GAIG;AACH,SAAgB,kBAAkB,CAC9B,WAAiC,EACjC,iBAAyB,SAAS;IAElC,OAAO,oBAAoB,EAAE;SACxB,GAAG,CAAC,cAAc,CAAC;SACnB,kBAAkB,CAAS,WAAW,CAAC,CAAA;AAChD,CAAC;AAED;;;;GAIG;AACH,SAAgB,kBAAkB,CAC9B,WAAkC,EAClC,KAAc,EACd,iBAAyB,SAAS;IAElC,IAAI,WAAW,EAAE,CAAC;QACd,OAAO,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC,kBAAkB,CAChE,KAAK,CACR,CAAA;IACL,CAAC;IAED,OAAO,aAAa,CAAC,cAAc,CAAC,CAAC,kBAAkB,EAAE,CAAA;AAC7D,CAAC", "file": "globals.js", "sourcesContent": ["import { MetadataArgsStorage } from \"./metadata-args/MetadataArgsStorage\"\nimport { PlatformTools } from \"./platform/PlatformTools\"\nimport { DataSourceOptions } from \"./data-source/DataSourceOptions\"\nimport { ConnectionOptionsReader } from \"./connection/ConnectionOptionsReader\"\nimport { ConnectionManager } from \"./connection/ConnectionManager\"\nimport { getFromContainer } from \"./container\"\nimport { DataSource } from \"./data-source/DataSource\"\nimport { EntityManager } from \"./entity-manager/EntityManager\"\nimport { MongoEntityManager } from \"./entity-manager/MongoEntityManager\"\nimport { SqljsEntityManager } from \"./entity-manager/SqljsEntityManager\"\nimport { EntityTarget } from \"./common/EntityTarget\"\nimport { Repository } from \"./repository/Repository\"\nimport { TreeRepository } from \"./repository/TreeRepository\"\nimport { ObjectType } from \"./common/ObjectType\"\nimport { MongoRepository } from \"./repository/MongoRepository\"\nimport { SelectQueryBuilder } from \"./query-builder/SelectQueryBuilder\"\nimport { ObjectUtils } from \"./util/ObjectUtils\"\nimport { ObjectLiteral } from \"./common/ObjectLiteral\"\n\n/**\n * Gets metadata args storage.\n */\nexport function getMetadataArgsStorage(): MetadataArgsStorage {\n    // we should store metadata storage in a global variable otherwise it brings too much problems\n    // one of the problem is that if any entity (or any other) will be imported before consumer will call\n    // useContainer method with his own container implementation, that entity will be registered in the\n    // old old container (default one post probably) and consumer will his entity.\n    // calling useContainer before he imports any entity (or any other) is not always convenient.\n    // another reason is that when we run migrations typeorm is being called from a global package\n    // and it may load entities which register decorators in typeorm of local package\n    // this leads to impossibility of usage of entities in migrations and cli related operations\n    const globalScope = PlatformTools.getGlobalVariable()\n    if (!globalScope.typeormMetadataArgsStorage)\n        globalScope.typeormMetadataArgsStorage = new MetadataArgsStorage()\n\n    return globalScope.typeormMetadataArgsStorage\n}\n\n/**\n * Reads connection options stored in ormconfig configuration file.\n *\n * @deprecated\n */\nexport async function getConnectionOptions(\n    connectionName: string = \"default\",\n): Promise<DataSourceOptions> {\n    return new ConnectionOptionsReader().get(connectionName)\n}\n\n/**\n * Gets a ConnectionManager which creates connections.\n *\n * @deprecated\n */\nexport function getConnectionManager(): ConnectionManager {\n    return getFromContainer(ConnectionManager)\n}\n\n/**\n * Creates a new connection and registers it in the manager.\n * Only one connection from ormconfig will be created (name \"default\" or connection without name).\n *\n * @deprecated\n */\nexport async function createConnection(): Promise<DataSource>\n\n/**\n * Creates a new connection from the ormconfig file with a given name.\n *\n * @deprecated\n */\nexport async function createConnection(name: string): Promise<DataSource>\n\n/**\n * Creates a new connection and registers it in the manager.\n *\n * @deprecated\n */\nexport async function createConnection(\n    options: DataSourceOptions,\n): Promise<DataSource>\n\n/**\n * Creates a new connection and registers it in the manager.\n *\n * If connection options were not specified, then it will try to create connection automatically,\n * based on content of ormconfig (json/js/env) file or environment variables.\n * Only one connection from ormconfig will be created (name \"default\" or connection without name).\n *\n * @deprecated\n */\nexport async function createConnection(\n    optionsOrName?: any,\n): Promise<DataSource> {\n    const connectionName =\n        typeof optionsOrName === \"string\" ? optionsOrName : \"default\"\n    const options = ObjectUtils.isObject(optionsOrName)\n        ? (optionsOrName as DataSourceOptions)\n        : await getConnectionOptions(connectionName)\n    return getConnectionManager().create(options).connect()\n}\n\n/**\n * Creates new connections and registers them in the manager.\n *\n * If connection options were not specified, then it will try to create connection automatically,\n * based on content of ormconfig (json/js/env) file or environment variables.\n * All connections from the ormconfig will be created.\n *\n * @deprecated\n */\nexport async function createConnections(\n    options?: DataSourceOptions[],\n): Promise<DataSource[]> {\n    if (!options) options = await new ConnectionOptionsReader().all()\n    const connections = options.map((options) =>\n        getConnectionManager().create(options),\n    )\n    // Do not use Promise.all or test 8522 will produce a dangling sqlite connection\n    for (const connection of connections) {\n        await connection.connect()\n    }\n    return connections\n}\n\n/**\n * Gets connection from the connection manager.\n * If connection name wasn't specified, then \"default\" connection will be retrieved.\n *\n * @deprecated\n */\nexport function getConnection(connectionName: string = \"default\"): DataSource {\n    return getConnectionManager().get(connectionName)\n}\n\n/**\n * Gets entity manager from the connection.\n * If connection name wasn't specified, then \"default\" connection will be retrieved.\n *\n * @deprecated\n */\nexport function getManager(connectionName: string = \"default\"): EntityManager {\n    return getConnectionManager().get(connectionName).manager\n}\n\n/**\n * Gets MongoDB entity manager from the connection.\n * If connection name wasn't specified, then \"default\" connection will be retrieved.\n *\n * @deprecated\n */\nexport function getMongoManager(\n    connectionName: string = \"default\",\n): MongoEntityManager {\n    return getConnectionManager().get(connectionName)\n        .manager as MongoEntityManager\n}\n\n/**\n * Gets Sqljs entity manager from connection name.\n * \"default\" connection is used, when no name is specified.\n * Only works when Sqljs driver is used.\n *\n * @deprecated\n */\nexport function getSqljsManager(\n    connectionName: string = \"default\",\n): SqljsEntityManager {\n    return getConnectionManager().get(connectionName)\n        .manager as SqljsEntityManager\n}\n\n/**\n * Gets repository for the given entity class.\n *\n * @deprecated\n */\nexport function getRepository<Entity extends ObjectLiteral>(\n    entityClass: EntityTarget<Entity>,\n    connectionName: string = \"default\",\n): Repository<Entity> {\n    return getConnectionManager()\n        .get(connectionName)\n        .getRepository<Entity>(entityClass)\n}\n\n/**\n * Gets tree repository for the given entity class.\n *\n * @deprecated\n */\nexport function getTreeRepository<Entity extends ObjectLiteral>(\n    entityClass: EntityTarget<Entity>,\n    connectionName: string = \"default\",\n): TreeRepository<Entity> {\n    return getConnectionManager()\n        .get(connectionName)\n        .getTreeRepository<Entity>(entityClass)\n}\n\n/**\n * Gets tree repository for the given entity class.\n *\n * @deprecated\n */\nexport function getCustomRepository<T>(\n    customRepository: ObjectType<T>,\n    connectionName: string = \"default\",\n): T {\n    return getConnectionManager()\n        .get(connectionName)\n        .getCustomRepository(customRepository)\n}\n\n/**\n * Gets mongodb repository for the given entity class or name.\n *\n * @deprecated\n */\nexport function getMongoRepository<Entity extends ObjectLiteral>(\n    entityClass: EntityTarget<Entity>,\n    connectionName: string = \"default\",\n): MongoRepository<Entity> {\n    return getConnectionManager()\n        .get(connectionName)\n        .getMongoRepository<Entity>(entityClass)\n}\n\n/**\n * Creates a new query builder.\n *\n * @deprecated\n */\nexport function createQueryBuilder<Entity extends ObjectLiteral>(\n    entityClass?: EntityTarget<Entity>,\n    alias?: string,\n    connectionName: string = \"default\",\n): SelectQueryBuilder<Entity> {\n    if (entityClass) {\n        return getRepository(entityClass, connectionName).createQueryBuilder(\n            alias,\n        )\n    }\n\n    return getConnection(connectionName).createQueryBuilder()\n}\n"], "sourceRoot": "."}