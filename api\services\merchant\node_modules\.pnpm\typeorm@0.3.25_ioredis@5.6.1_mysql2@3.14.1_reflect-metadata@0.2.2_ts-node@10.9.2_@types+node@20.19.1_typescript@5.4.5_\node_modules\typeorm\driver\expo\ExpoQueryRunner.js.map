{"version": 3, "sources": ["../../src/driver/expo/ExpoQueryRunner.ts"], "names": [], "mappings": ";;;AAAA,mEAA+D;AAC/D,iGAA6F;AAC7F,gEAA4D;AAC5D,8DAA0D;AAC1D,0EAAsE;AACtE,4FAAwF;AAGxF,MAAa,eAAgB,SAAQ,qDAAyB;IAG1D,YAAY,MAAkB;QAC1B,KAAK,EAAE,CAAA;QACP,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,IAAI,CAAC,CAAA;IAC5C,CAAC;IAED,KAAK,CAAC,eAAe;QACjB,MAAM,IAAI,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAA;IACjD,CAAC;IAED,KAAK,CAAC,cAAc;QAChB,MAAM,IAAI,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAA;IAChD,CAAC;IAED,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,iEAA+B,EAAE,CAAA;QAEhE,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QAC/C,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;QAEjC,MAAM,SAAS,GAAG,MAAM,kBAAkB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;QAC9D,IAAI,CAAC;YACD,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,CAAA;YAE1D,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;YAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;YAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,SAAS,EACT,SAAS,CACZ,CAAA;YACD,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAE9B,IACI,qBAAqB;gBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;gBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACL,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,yBAAW,EAAE,CAAA;YAChC,MAAM,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAA;YACnC,MAAM,CAAC,OAAO,GAAG,MAAM,SAAS,CAAC,WAAW,EAAE,CAAA;YAC9C,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;gBACxC,CAAC,CAAC,SAAS,CAAC,eAAe;gBAC3B,CAAC,CAAC,MAAM,CAAC,OAAO,CAAA;YAEpB,OAAO,mBAAmB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAA;QACpD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,CAAC,EACD,SAAS,EACT,GAAG,CACN,CAAA;YACD,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAE9B,MAAM,IAAI,mCAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;QACtD,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAC9B,MAAM,SAAS,CAAC,aAAa,EAAE,CAAA;QACnC,CAAC;IACL,CAAC;CACJ;AAjGD,0CAiGC", "file": "ExpoQueryRunner.js", "sourcesContent": ["import { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { AbstractSqliteQueryRunner } from \"../sqlite-abstract/AbstractSqliteQueryRunner\"\nimport { ExpoDriver } from \"./ExpoDriver\"\n\nexport class ExpoQueryRunner extends AbstractSqliteQueryRunner {\n    driver: ExpoDriver\n\n    constructor(driver: ExpoDriver) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n    }\n\n    async beforeMigration(): Promise<void> {\n        await this.query(\"PRAGMA foreign_keys = OFF\")\n    }\n\n    async afterMigration(): Promise<void> {\n        await this.query(\"PRAGMA foreign_keys = ON\")\n    }\n\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const databaseConnection = await this.connect()\n        const broadcasterResult = new BroadcasterResult()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const queryStartTime = Date.now()\n\n        const statement = await databaseConnection.prepareAsync(query)\n        try {\n            const rawResult = await statement.executeAsync(parameters)\n\n            const maxQueryExecutionTime =\n                this.driver.options.maxQueryExecutionTime\n            const queryEndTime = Date.now()\n            const queryExecutionTime = queryEndTime - queryStartTime\n\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                true,\n                queryExecutionTime,\n                rawResult,\n                undefined,\n            )\n            await broadcasterResult.wait()\n\n            if (\n                maxQueryExecutionTime &&\n                queryExecutionTime > maxQueryExecutionTime\n            ) {\n                this.driver.connection.logger.logQuerySlow(\n                    queryExecutionTime,\n                    query,\n                    parameters,\n                    this,\n                )\n            }\n\n            const result = new QueryResult()\n            result.affected = rawResult.changes\n            result.records = await rawResult.getAllAsync()\n            result.raw = query.startsWith(\"INSERT INTO\")\n                ? rawResult.lastInsertRowId\n                : result.records\n\n            return useStructuredResult ? result : result.raw\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                0,\n                undefined,\n                err,\n            )\n            await broadcasterResult.wait()\n\n            throw new QueryFailedError(query, parameters, err)\n        } finally {\n            await broadcasterResult.wait()\n            await statement.finalizeAsync()\n        }\n    }\n}\n"], "sourceRoot": "../.."}