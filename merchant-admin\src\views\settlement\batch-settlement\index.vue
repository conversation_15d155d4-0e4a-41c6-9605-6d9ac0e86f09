<template>
  <div class="batch-settlement-page">
    <!-- 操作区域 -->
    <div class="custom-card art-custom-card filter-card">
      <div class="custom-card-content">
        <el-form :model="filters" :inline="true">
          <el-form-item label="结算日期">
            <el-date-picker
              v-model="filters.settlementDate"
              type="date"
              placeholder="选择结算日期"
              style="width: 180px"
              @change="handleDateChange"
            />
          </el-form-item>
          
          <el-form-item label="商户类型">
            <el-select v-model="filters.merchantType" placeholder="选择类型" style="width: 120px" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="个人商户" value="personal" />
              <el-option label="企业商户" value="enterprise" />
            </el-select>
          </el-form-item>

          <el-form-item label="结算状态">
            <el-select v-model="filters.status" placeholder="选择状态" style="width: 120px" @change="handleSearch">
              <el-option label="全部" value="" />
              <el-option label="待结算" value="pending" />
              <el-option label="结算中" value="processing" />
              <el-option label="已结算" value="completed" />
              <el-option label="结算失败" value="failed" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleBatchSettle" :disabled="selectedRows.length === 0">
              <el-icon><Money /></el-icon>
              批量结算
            </el-button>
            <el-button type="warning" @click="handleExport">
              <el-icon><Download /></el-icon>
              导出结算单
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 结算统计 -->
    <div class="settlement-stats">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon pending">
                  <el-icon><Clock /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.pendingCount }}</div>
                  <div class="stat-label">待结算商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.pendingAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon processing">
                  <el-icon><Loading /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.processingCount }}</div>
                  <div class="stat-label">结算中商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.processingAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon completed">
                  <el-icon><CircleCheck /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.completedCount }}</div>
                  <div class="stat-label">已结算商户</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.completedAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="custom-card art-custom-card stat-card">
            <div class="custom-card-content">
              <div class="stat-item">
                <div class="stat-icon failed">
                  <el-icon><Close /></el-icon>
                </div>
                <div class="stat-content">
                  <div class="stat-value">{{ stats.failedCount }}</div>
                  <div class="stat-label">结算失败</div>
                  <div class="stat-amount">¥{{ formatNumber(stats.failedAmount) }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 结算列表 -->
    <div class="custom-card art-custom-card">
      <div class="custom-card-content">
        <el-table
          :data="settlementList"
          v-loading="loading"
          row-key="id"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="merchantName" label="商户名称" min-width="150" />
          <el-table-column prop="merchantType" label="商户类型" width="100">
            <template #default="{ row }">
              <el-tag :type="row.merchantType === 'personal' ? 'success' : 'primary'">
                {{ row.merchantType === 'personal' ? '个人' : '企业' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="settlementAmount" label="结算金额" width="120">
            <template #default="{ row }">
              <span class="amount-text">¥{{ formatNumber(row.settlementAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="commissionAmount" label="佣金金额" width="120">
            <template #default="{ row }">
              <span class="commission-text">¥{{ formatNumber(row.commissionAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="actualAmount" label="实际到账" width="120">
            <template #default="{ row }">
              <span class="actual-amount">¥{{ formatNumber(row.actualAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="settlementCycle" label="结算周期" width="100">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ getSettlementCycleName(row.settlementCycle) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="结算状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTag(row.status)">
                {{ getStatusName(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="bankAccount" label="收款账户" width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ maskBankAccount(row.bankAccount) }}
            </template>
          </el-table-column>
          <el-table-column prop="settlementTime" label="结算时间" width="160">
            <template #default="{ row }">
              {{ row.settlementTime || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <el-button 
                type="primary" 
                size="small" 
                @click="handleSettle(row)"
                :disabled="row.status !== 'pending'"
              >
                结算
              </el-button>
              <el-button type="info" size="small" @click="viewDetail(row)">
                详情
              </el-button>
              <el-dropdown @command="(command) => handleMoreAction(command, row)">
                <el-button type="text" size="small">
                  更多<el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="retry" :disabled="row.status !== 'failed'">
                      重新结算
                    </el-dropdown-item>
                    <el-dropdown-item command="cancel" :disabled="row.status !== 'pending'">
                      取消结算
                    </el-dropdown-item>
                    <el-dropdown-item command="receipt">
                      结算凭证
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="fetchData"
            @current-change="fetchData"
          />
        </div>
      </div>
    </div>

    <!-- 批量结算确认对话框 -->
    <el-dialog
      v-model="batchDialog.visible"
      title="批量结算确认"
      width="600px"
    >
      <div class="batch-confirm-content">
        <el-alert
          title="请确认以下结算信息"
          type="warning"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <div class="batch-summary">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">结算商户数</div>
                <div class="summary-value">{{ selectedRows.length }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">结算总金额</div>
                <div class="summary-value">¥{{ formatNumber(batchSummary.totalAmount) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="summary-item">
                <div class="summary-label">实际到账</div>
                <div class="summary-value">¥{{ formatNumber(batchSummary.actualAmount) }}</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <el-form :model="batchForm" label-width="100px">
          <el-form-item label="结算备注">
            <el-input
              v-model="batchForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入结算备注（可选）"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <el-button @click="batchDialog.visible = false">取消</el-button>
        <el-button type="primary" @click="confirmBatchSettle" :loading="batchDialog.loading">
          确认结算
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Money, Download, Clock, Loading, CircleCheck, Close, ArrowDown } from '@element-plus/icons-vue'

// 筛选条件
const filters = reactive({
  settlementDate: new Date(),
  merchantType: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0
})

// 统计数据
const stats = reactive({
  pendingCount: 25,
  pendingAmount: 1250000,
  processingCount: 8,
  processingAmount: 380000,
  completedCount: 156,
  completedAmount: 8900000,
  failedCount: 3,
  failedAmount: 45000
})

// 结算列表数据
const settlementList = ref([
  {
    id: 1,
    merchantName: '张三手工艺品店',
    merchantType: 'personal',
    settlementAmount: 15800,
    commissionAmount: 553,
    actualAmount: 15247,
    settlementCycle: 'T1',
    status: 'pending',
    bankAccount: '6222021234567890123',
    settlementTime: null
  },
  {
    id: 2,
    merchantName: '李四非遗工坊',
    merchantType: 'enterprise',
    settlementAmount: 28500,
    commissionAmount: 798,
    actualAmount: 27702,
    settlementCycle: 'T0',
    status: 'processing',
    bankAccount: '6228481234567890456',
    settlementTime: null
  },
  {
    id: 3,
    merchantName: '王五文创店',
    merchantType: 'personal',
    settlementAmount: 12300,
    commissionAmount: 430.5,
    actualAmount: 11869.5,
    settlementCycle: 'T3',
    status: 'completed',
    bankAccount: '6217001234567890789',
    settlementTime: '2025-01-18 14:30:00'
  }
])

const loading = ref(false)
const selectedRows = ref([])

// 批量结算对话框
const batchDialog = reactive({
  visible: false,
  loading: false
})

const batchForm = reactive({
  remark: ''
})

// 批量结算汇总
const batchSummary = computed(() => {
  const totalAmount = selectedRows.value.reduce((sum, row) => sum + row.settlementAmount, 0)
  const actualAmount = selectedRows.value.reduce((sum, row) => sum + row.actualAmount, 0)
  return { totalAmount, actualAmount }
})

// 格式化数字
const formatNumber = (num: number) => {
  return num.toLocaleString()
}

// 获取结算周期名称
const getSettlementCycleName = (cycle: string) => {
  const names = {
    T0: 'T+0',
    T1: 'T+1',
    T3: 'T+3',
    T7: 'T+7',
    monthly: '月结'
  }
  return names[cycle] || '未知'
}

// 获取状态标签
const getStatusTag = (status: string) => {
  const tags = {
    pending: 'warning',
    processing: 'primary',
    completed: 'success',
    failed: 'danger'
  }
  return tags[status] || 'info'
}

// 获取状态名称
const getStatusName = (status: string) => {
  const names = {
    pending: '待结算',
    processing: '结算中',
    completed: '已结算',
    failed: '结算失败'
  }
  return names[status] || '未知'
}

// 银行卡号脱敏
const maskBankAccount = (account: string) => {
  if (!account) return '-'
  return account.replace(/(\d{4})\d+(\d{4})/, '$1****$2')
}

// 查询数据
const fetchData = () => {
  loading.value = true
  setTimeout(() => {
    pagination.total = 50
    loading.value = false
  }, 1000)
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(filters, {
    settlementDate: new Date(),
    merchantType: '',
    status: ''
  })
  handleSearch()
}

// 日期变化
const handleDateChange = () => {
  handleSearch()
}

// 选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection.filter(row => row.status === 'pending')
}

// 单个结算
const handleSettle = (row: any) => {
  ElMessageBox.confirm(`确定要结算商户"${row.merchantName}"吗？`, '确认结算', {
    type: 'warning'
  }).then(() => {
    ElMessage.success('结算请求已提交')
    row.status = 'processing'
  })
}

// 批量结算
const handleBatchSettle = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要结算的商户')
    return
  }
  batchDialog.visible = true
}

// 确认批量结算
const confirmBatchSettle = () => {
  batchDialog.loading = true
  setTimeout(() => {
    batchDialog.loading = false
    batchDialog.visible = false
    ElMessage.success(`批量结算 ${selectedRows.value.length} 个商户成功`)
    
    // 更新状态
    selectedRows.value.forEach(row => {
      row.status = 'processing'
    })
    
    // 重置表单
    batchForm.remark = ''
  }, 2000)
}

// 导出结算单
const handleExport = () => {
  ElMessage.info('正在导出结算单...')
}

// 查看详情
const viewDetail = (row: any) => {
  ElMessage.info(`查看结算详情：${row.merchantName}`)
}

// 更多操作
const handleMoreAction = (command: string, row: any) => {
  switch (command) {
    case 'retry':
      ElMessage.success('重新结算请求已提交')
      row.status = 'processing'
      break
    case 'cancel':
      ElMessageBox.confirm('确定要取消此结算吗？', '确认操作', {
        type: 'warning'
      }).then(() => {
        ElMessage.success('已取消结算')
      })
      break
    case 'receipt':
      ElMessage.info('正在生成结算凭证...')
      break
  }
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="scss">
.batch-settlement-page {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.settlement-stats {
  margin-bottom: 20px;

  .stat-card {
    .stat-item {
      display: flex;
      align-items: center;
      padding: 20px;

      .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        font-size: 20px;
        color: white;

        &.pending {
          background: linear-gradient(135deg, #e6a23c, #ebb563);
        }

        &.processing {
          background: linear-gradient(135deg, #409eff, #66b1ff);
        }

        &.completed {
          background: linear-gradient(135deg, #67c23a, #85ce61);
        }

        &.failed {
          background: linear-gradient(135deg, #f56c6c, #f78989);
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          line-height: 1;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
          margin: 5px 0;
        }

        .stat-amount {
          font-size: 12px;
          color: #67c23a;
          font-weight: bold;
        }
      }
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.amount-text {
  color: #67c23a;
  font-weight: bold;
}

.commission-text {
  color: #e6a23c;
  font-weight: bold;
}

.actual-amount {
  color: #409eff;
  font-weight: bold;
}

.batch-confirm-content {
  .batch-summary {
    background: #f5f7fa;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 20px;

    .summary-item {
      text-align: center;

      .summary-label {
        font-size: 14px;
        color: #909399;
        margin-bottom: 8px;
      }

      .summary-value {
        font-size: 20px;
        font-weight: bold;
        color: #303133;
      }
    }
  }
}
</style>
