{"version": 3, "sources": ["../browser/src/error/UsingJoinColumnOnlyOnOneSideAllowedError.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,wCAAyC,SAAQ,YAAY;IACtE,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,CACD,+EAA+E;YAC3E,QAAQ,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,QAChD,QAAQ,CAAC,qBAAqB,CAAC,IACnC,IAAI,QAAQ,CAAC,eAAgB,CAAC,YAAY,GAAG;YAC7C,uFAAuF,CAC9F,CAAA;IACL,CAAC;CACJ", "file": "UsingJoinColumnOnlyOnOneSideAllowedError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class UsingJoinColumnOnlyOnOneSideAllowedError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super(\n            `Using JoinColumn is allowed only on one side of the one-to-one relationship. ` +\n                `Both ${entityMetadata.name}#${relation.propertyName} and ${\n                    relation.inverseEntityMetadata.name\n                }#${relation.inverseRelation!.propertyName} ` +\n                `has JoinTable decorators. Choose one of them and left JoinTable decorator only on it.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}