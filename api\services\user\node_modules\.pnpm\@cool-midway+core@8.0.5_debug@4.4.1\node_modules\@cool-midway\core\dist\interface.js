"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLOUDTYPE = exports.MODETYPE = void 0;
// 模式
var MODETYPE;
(function (MODETYPE) {
    /** 本地 */
    MODETYPE["LOCAL"] = "local";
    /** 云存储 */
    MODETYPE["CLOUD"] = "cloud";
    /** 其他 */
    MODETYPE["OTHER"] = "other";
})(MODETYPE || (exports.MODETYPE = MODETYPE = {}));
var CLOUDTYPE;
(function (CLOUDTYPE) {
    /** 阿里云存储 */
    CLOUDTYPE["OSS"] = "oss";
    /** 腾讯云存储 */
    CLOUDTYPE["COS"] = "cos";
    /** 七牛云存储 */
    CLOUDTYPE["QINIU"] = "qiniu";
    /** AWS S3 */
    CLOUDTYPE["AWS"] = "aws";
})(CLOUDTYPE || (exports.CLOUDTYPE = CLOUDTYPE = {}));
