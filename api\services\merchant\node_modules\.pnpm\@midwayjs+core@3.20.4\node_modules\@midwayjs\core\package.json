{"name": "@midwayjs/core", "version": "3.20.4", "description": "midway core", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "link": "npm link", "madge": "madge --circular --extensions ts,tsx --exclude 'node_modules|test|dist' src"}, "keywords": ["midway", "IoC", "core"], "files": ["dist/**/*.js", "dist/**/*.d.ts"], "license": "MIT", "devDependencies": {"@midwayjs/logger": "^3.0.0", "eventsource": "2.0.2", "koa": "2.16.1", "mm": "3.4.0", "raw-body": "2.5.2", "sinon": "17.0.2"}, "dependencies": {"@midwayjs/glob": "^1.0.2", "class-transformer": "0.5.1", "picomatch": "2.3.1", "reflect-metadata": "0.2.2"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "engines": {"node": ">=12"}, "gitHead": "c3fb65a7ada8829635f3c6af5ef83c65c3a43d79"}