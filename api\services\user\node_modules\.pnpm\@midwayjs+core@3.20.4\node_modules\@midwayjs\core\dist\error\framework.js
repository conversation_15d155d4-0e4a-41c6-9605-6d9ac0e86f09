"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayEmptyValueError = exports.MidwayInvalidConfigPropertyError = exports.MidwayMainFrameworkMissingError = exports.MidwayCodeInvokeTimeoutError = exports.MidwayInvokeForbiddenError = exports.MidwayRetryExceededMaxTimesError = exports.MidwayDuplicateControllerOptionsError = exports.MidwayDuplicateClassNameError = exports.MidwayInconsistentVersionError = exports.MidwayUtilHttpClientTimeoutError = exports.MidwayMissingImportComponentError = exports.MidwaySingletonInjectRequestError = exports.MidwayUseWrongMethodError = exports.MidwayDuplicateRouteError = exports.MidwayResolverMissingError = exports.MidwayInvalidConfigError = exports.MidwayConfigMissingError = exports.MidwayFeatureNotImplementedError = exports.MidwayFeatureNoLongerSupportedError = exports.MidwayDefinitionNotFoundError = exports.MidwayParameterError = exports.MidwayCommonError = exports.FrameworkErrorEnum = void 0;
const base_1 = require("./base");
exports.FrameworkErrorEnum = (0, base_1.registerErrorCode)('midway', {
    UNKNOWN: 10000,
    COMMON: 10001,
    PARAM_TYPE: 10002,
    DEFINITION_NOT_FOUND: 10003,
    FEATURE_NO_LONGER_SUPPORTED: 10004,
    FEATURE_NOT_IMPLEMENTED: 10004,
    MISSING_CONFIG: 10006,
    MISSING_RESOLVER: 10007,
    DUPLICATE_ROUTER: 10008,
    USE_WRONG_METHOD: 10009,
    SINGLETON_INJECT_REQUEST: 10010,
    MISSING_IMPORTS: 10011,
    UTIL_HTTP_TIMEOUT: 10012,
    INCONSISTENT_VERSION: 10013,
    INVALID_CONFIG: 10014,
    DUPLICATE_CLASS_NAME: 10015,
    DUPLICATE_CONTROLLER_PREFIX_OPTIONS: 10016,
    RETRY_OVER_MAX_TIME: 10017,
    INVOKE_METHOD_FORBIDDEN: 10018,
    CODE_INVOKE_TIMEOUT: 10019,
    MAIN_FRAMEWORK_MISSING: 10020,
    INVALID_CONFIG_PROPERTY: 10021,
    EMPTY_VALUE: 10022,
});
class MidwayCommonError extends base_1.MidwayError {
    constructor(message) {
        super(message, exports.FrameworkErrorEnum.COMMON);
    }
}
exports.MidwayCommonError = MidwayCommonError;
class MidwayParameterError extends base_1.MidwayError {
    constructor(message) {
        super(message !== null && message !== void 0 ? message : 'Parameter type not match', exports.FrameworkErrorEnum.PARAM_TYPE);
    }
}
exports.MidwayParameterError = MidwayParameterError;
class MidwayDefinitionNotFoundError extends base_1.MidwayError {
    static isClosePrototypeOf(ins) {
        return ins
            ? ins[MidwayDefinitionNotFoundError.type] ===
                MidwayDefinitionNotFoundError.type
            : false;
    }
    constructor(identifier) {
        super(`${identifier} is not valid in current context`, exports.FrameworkErrorEnum.DEFINITION_NOT_FOUND);
        this[MidwayDefinitionNotFoundError.type] =
            MidwayDefinitionNotFoundError.type;
    }
    updateErrorMsg(className) {
        const identifier = this.message.split(' is not valid in current context')[0];
        this.message = `${identifier} in class ${className} is not valid in current context`;
    }
}
exports.MidwayDefinitionNotFoundError = MidwayDefinitionNotFoundError;
MidwayDefinitionNotFoundError.type = Symbol.for('#NotFoundError');
class MidwayFeatureNoLongerSupportedError extends base_1.MidwayError {
    constructor(message) {
        super('This feature no longer supported \n' + message, exports.FrameworkErrorEnum.FEATURE_NO_LONGER_SUPPORTED);
    }
}
exports.MidwayFeatureNoLongerSupportedError = MidwayFeatureNoLongerSupportedError;
class MidwayFeatureNotImplementedError extends base_1.MidwayError {
    constructor(message) {
        super('This feature not implemented \n' + message, exports.FrameworkErrorEnum.FEATURE_NOT_IMPLEMENTED);
    }
}
exports.MidwayFeatureNotImplementedError = MidwayFeatureNotImplementedError;
class MidwayConfigMissingError extends base_1.MidwayError {
    constructor(configKey) {
        super(`Can't found config key "${configKey}" in your config, please set it first`, exports.FrameworkErrorEnum.MISSING_CONFIG);
    }
}
exports.MidwayConfigMissingError = MidwayConfigMissingError;
class MidwayInvalidConfigError extends base_1.MidwayError {
    constructor(message) {
        super('Invalid config file \n' + message, exports.FrameworkErrorEnum.INVALID_CONFIG);
    }
}
exports.MidwayInvalidConfigError = MidwayInvalidConfigError;
class MidwayResolverMissingError extends base_1.MidwayError {
    constructor(type) {
        super(`Resolver "${type}" is missing.`, exports.FrameworkErrorEnum.MISSING_RESOLVER);
    }
}
exports.MidwayResolverMissingError = MidwayResolverMissingError;
class MidwayDuplicateRouteError extends base_1.MidwayError {
    constructor(routerUrl, existPos, existPosOther) {
        super(`Duplicate router "${routerUrl}" at "${existPos}" and "${existPosOther}"`, exports.FrameworkErrorEnum.DUPLICATE_ROUTER);
    }
}
exports.MidwayDuplicateRouteError = MidwayDuplicateRouteError;
class MidwayUseWrongMethodError extends base_1.MidwayError {
    constructor(wrongMethod, replacedMethod, describeKey) {
        const text = describeKey
            ? `${describeKey} not valid by ${wrongMethod}, Use ${replacedMethod} instead!`
            : `You should not invoked by ${wrongMethod}, Use ${replacedMethod} instead!`;
        super(text, exports.FrameworkErrorEnum.USE_WRONG_METHOD);
    }
}
exports.MidwayUseWrongMethodError = MidwayUseWrongMethodError;
class MidwaySingletonInjectRequestError extends base_1.MidwayError {
    constructor(singletonScopeName, requestScopeName) {
        const text = `${singletonScopeName} with singleton scope can't implicitly inject ${requestScopeName} with request scope directly, please add "@Scope(ScopeEnum.Request, { allowDowngrade: true })" in ${requestScopeName} or use "ctx.requestContext.getAsync(${requestScopeName})".`;
        super(text, exports.FrameworkErrorEnum.SINGLETON_INJECT_REQUEST);
    }
}
exports.MidwaySingletonInjectRequestError = MidwaySingletonInjectRequestError;
class MidwayMissingImportComponentError extends base_1.MidwayError {
    constructor(originName) {
        const text = `"${originName}" can't inject and maybe forgot add "{imports: [***]}" in @Configuration.`;
        super(text, exports.FrameworkErrorEnum.MISSING_IMPORTS);
    }
}
exports.MidwayMissingImportComponentError = MidwayMissingImportComponentError;
class MidwayUtilHttpClientTimeoutError extends base_1.MidwayError {
    constructor(message) {
        super(message, exports.FrameworkErrorEnum.UTIL_HTTP_TIMEOUT);
    }
}
exports.MidwayUtilHttpClientTimeoutError = MidwayUtilHttpClientTimeoutError;
class MidwayInconsistentVersionError extends base_1.MidwayError {
    constructor() {
        const text = 'We find a latest dependency package installed, please remove the lock file and use "npm update" to upgrade all dependencies first.';
        super(text, exports.FrameworkErrorEnum.INCONSISTENT_VERSION);
    }
}
exports.MidwayInconsistentVersionError = MidwayInconsistentVersionError;
class MidwayDuplicateClassNameError extends base_1.MidwayError {
    constructor(className, existPath, existPathOther) {
        super(`"${className}" duplicated between "${existPath}" and "${existPathOther}"`, exports.FrameworkErrorEnum.DUPLICATE_CLASS_NAME);
    }
}
exports.MidwayDuplicateClassNameError = MidwayDuplicateClassNameError;
class MidwayDuplicateControllerOptionsError extends base_1.MidwayError {
    constructor(prefix, existController, existControllerOther) {
        super(`"Prefix ${prefix}" with duplicated controller options between "${existController}" and "${existControllerOther}"`, exports.FrameworkErrorEnum.DUPLICATE_CONTROLLER_PREFIX_OPTIONS);
    }
}
exports.MidwayDuplicateControllerOptionsError = MidwayDuplicateControllerOptionsError;
class MidwayRetryExceededMaxTimesError extends base_1.MidwayError {
    constructor(methodName, times, err) {
        super(`Invoke "${methodName}" retries exceeded the maximum number of times(${times}), error: ${err.message}`, exports.FrameworkErrorEnum.RETRY_OVER_MAX_TIME, {
            cause: err,
        });
    }
}
exports.MidwayRetryExceededMaxTimesError = MidwayRetryExceededMaxTimesError;
class MidwayInvokeForbiddenError extends base_1.MidwayError {
    constructor(methodName, module) {
        super(`Invoke "${module ? module.name : 'unknown'}.${methodName}" is forbidden.`, exports.FrameworkErrorEnum.INVOKE_METHOD_FORBIDDEN);
    }
}
exports.MidwayInvokeForbiddenError = MidwayInvokeForbiddenError;
class MidwayCodeInvokeTimeoutError extends base_1.MidwayError {
    constructor(methodName, timeout) {
        super(`Invoke "${methodName}" running timeout(${timeout}ms)`, exports.FrameworkErrorEnum.CODE_INVOKE_TIMEOUT);
    }
}
exports.MidwayCodeInvokeTimeoutError = MidwayCodeInvokeTimeoutError;
class MidwayMainFrameworkMissingError extends base_1.MidwayError {
    constructor() {
        super('Main framework missing, please check your configuration.', exports.FrameworkErrorEnum.MAIN_FRAMEWORK_MISSING);
    }
}
exports.MidwayMainFrameworkMissingError = MidwayMainFrameworkMissingError;
class MidwayInvalidConfigPropertyError extends base_1.MidwayError {
    constructor(propertyName, allowTypes) {
        super(`Invalid config property "${propertyName}", ${allowTypes
            ? `only ${allowTypes.join(',')} can be set`
            : 'please check your configuration'}.`, exports.FrameworkErrorEnum.INVALID_CONFIG_PROPERTY);
    }
}
exports.MidwayInvalidConfigPropertyError = MidwayInvalidConfigPropertyError;
class MidwayEmptyValueError extends base_1.MidwayError {
    constructor(msg) {
        super(msg !== null && msg !== void 0 ? msg : 'There is an empty value got and it is not allowed.', exports.FrameworkErrorEnum.EMPTY_VALUE);
    }
}
exports.MidwayEmptyValueError = MidwayEmptyValueError;
//# sourceMappingURL=framework.js.map