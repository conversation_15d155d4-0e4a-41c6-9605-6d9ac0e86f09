"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayLoggerService = void 0;
const decorator_1 = require("../decorator");
const configService_1 = require("./configService");
const serviceFactory_1 = require("../common/serviceFactory");
const interface_1 = require("../interface");
const loggerFactory_1 = require("../common/loggerFactory");
const error_1 = require("../error");
let MidwayLoggerService = class MidwayLoggerService extends serviceFactory_1.ServiceFactory {
    constructor(applicationContext, globalOptions = {}) {
        super();
        this.applicationContext = applicationContext;
        this.globalOptions = globalOptions;
        this.lazyLoggerConfigMap = new Map();
        this.aliasLoggerMap = new Map();
    }
    init() {
        var _a;
        const loggerFactory = this.configService.getConfiguration('loggerFactory');
        // load logger factory from user config first
        this.loggerFactory =
            loggerFactory ||
                this.globalOptions['loggerFactory'] ||
                new loggerFactory_1.DefaultConsoleLoggerFactory();
        // check
        if (!this.loggerFactory.getDefaultMidwayLoggerConfig) {
            throw new error_1.MidwayFeatureNoLongerSupportedError('please upgrade your @midwayjs/logger to latest version');
        }
        const defaultLoggerConfig = this.loggerFactory.getDefaultMidwayLoggerConfig(this.configService.getAppInfo());
        // merge to user config
        this.configService.addObject(defaultLoggerConfig, true);
        // init logger
        this.initClients(this.configService.getConfiguration('midwayLogger'));
        // alias inject logger
        (_a = this.applicationContext) === null || _a === void 0 ? void 0 : _a.registerObject('logger', this.getLogger('appLogger'));
    }
    createClient(config, name) {
        if (config.aliasName) {
            // mapping alias logger name to real logger name
            this.aliasLoggerMap.set(config.aliasName, name);
        }
        if (!config.lazyLoad) {
            this.loggerFactory.createLogger(name, config);
        }
        else {
            delete config['lazyLoad'];
            this.lazyLoggerConfigMap.set(name, config);
        }
    }
    getName() {
        return 'logger';
    }
    createLogger(name, config) {
        delete config['aliasName'];
        return this.loggerFactory.createLogger(name, config);
    }
    getLogger(name) {
        if (this.aliasLoggerMap.has(name)) {
            // get real logger name
            name = this.aliasLoggerMap.get(name);
        }
        const logger = this.loggerFactory.getLogger(name);
        if (logger) {
            return logger;
        }
        if (this.lazyLoggerConfigMap.has(name)) {
            // try to lazy init
            this.createClient(this.lazyLoggerConfigMap.get(name), name);
            this.lazyLoggerConfigMap.delete(name);
        }
        return this.loggerFactory.getLogger(name);
    }
    getCurrentLoggerFactory() {
        return this.loggerFactory;
    }
    createContextLogger(ctx, appLogger, contextOptions) {
        return this.loggerFactory.createContextLogger(ctx, appLogger, contextOptions);
    }
    getClients() {
        return this.clients;
    }
    getClientKeys() {
        return Array.from(this.clients.keys());
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", configService_1.MidwayConfigService)
], MidwayLoggerService.prototype, "configService", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], MidwayLoggerService.prototype, "init", null);
MidwayLoggerService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object, Object])
], MidwayLoggerService);
exports.MidwayLoggerService = MidwayLoggerService;
//# sourceMappingURL=loggerService.js.map