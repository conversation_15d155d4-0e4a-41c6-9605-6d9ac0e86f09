"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CheckPlugin = void 0;
const command_core_1 = require("@midwayjs/command-core");
const luckyeye_1 = require("@midwayjs/luckyeye");
const path_1 = require("path");
const fs_1 = require("fs");
const chalk = require("chalk");
const YAML = require("js-yaml");
const locate_1 = require("@midwayjs/locate");
const utils_1 = require("./utils");
const globby = require("globby");
const midway_version_1 = require("midway-version");
var ProjectType;
(function (ProjectType) {
    ProjectType["FaaS"] = "faas";
    ProjectType["MigrateToFaaS"] = "migrateToFaaS";
})(ProjectType || (ProjectType = {}));
const CHECK_SKIP = 'check_skip';
var CHECK_COLOR;
(function (CHECK_COLOR) {
    CHECK_COLOR["GROUP"] = "#e5e511";
    CHECK_COLOR["ERROR"] = "#f55111";
    CHECK_COLOR["SUCCESS"] = "#23d18b";
    CHECK_COLOR["SKIP"] = "#999999";
})(CHECK_COLOR || (CHECK_COLOR = {}));
class CheckPlugin extends command_core_1.BasePlugin {
    constructor() {
        super(...arguments);
        this.servicePath = this.core.config.servicePath;
        this.sourcesInfo = [];
        this.errors = [];
        this.commands = {
            check: {
                usage: 'find your code bugs',
                lifecycleEvents: ['start', 'check', 'output'],
            },
        };
        this.hooks = {
            'check:start': this.start.bind(this),
            'check:check': this.check.bind(this),
        };
        this.pkg = {};
        this.pkgContent = '';
        this.isHooks = false;
    }
    async start() {
        // check project type
        const fyml = this.getYamlFilePosition();
        if ((0, fs_1.existsSync)(fyml)) {
            const yamlData = (0, fs_1.readFileSync)(fyml).toString();
            if (!/deployType/.test(yamlData)) {
                this.projectType = ProjectType.FaaS;
            }
            else {
                // koa/express/egg 迁移
                this.projectType = ProjectType.MigrateToFaaS;
            }
        }
        const cwd = this.getCwd();
        let tsCodeRoot = (0, path_1.join)(cwd, this.options.sourceDir || 'src');
        let locateResult;
        if (this.projectType === ProjectType.FaaS) {
            const locator = new locate_1.Locator(cwd);
            // midway hooks 支持
            const config = (0, command_core_1.resolveMidwayConfig)(cwd);
            if (config.exist) {
                this.isHooks = true;
                this.options.sourceDir = config.source;
            }
            if (this.options.sourceDir) {
                this.options.sourceDir = (0, utils_1.transformToRelative)(this.servicePath, this.options.sourceDir);
            }
            locateResult = await locator.run({
                tsCodeRoot: this.options.sourceDir &&
                    (0, path_1.join)(this.servicePath, this.options.sourceDir),
            });
            tsCodeRoot = locateResult === null || locateResult === void 0 ? void 0 : locateResult.tsCodeRoot;
        }
        const pkgJsonFile = (0, path_1.join)(cwd, 'package.json');
        if ((0, fs_1.existsSync)(pkgJsonFile)) {
            this.pkgContent = (0, fs_1.readFileSync)(pkgJsonFile).toString();
            this.pkg = JSON.parse(this.pkgContent);
        }
        this.globalData = {
            cwd,
            projectType: this.projectType,
            tsCodeRoot,
            locateResult,
        };
        this.core.debug('globalData', this.globalData);
        if ((0, fs_1.existsSync)(tsCodeRoot)) {
            const tsSourceFileList = await globby(['**/*.ts'], {
                cwd: tsCodeRoot,
            });
            this.sourcesInfo = tsSourceFileList.map(tsSourceFile => {
                const file = (0, path_1.join)(tsCodeRoot, tsSourceFile);
                const code = (0, fs_1.readFileSync)(file).toString();
                return {
                    tsSourceFile,
                    file,
                    code,
                };
            });
        }
        this.setStore('checkGlobalData', this.globalData, true);
    }
    async check() {
        const container = new luckyeye_1.RunnerContainer();
        container.loadRulePackage();
        container.registerReport(this.getCheckReporter());
        const ruleList = await this.getRuleList();
        for (const rule of ruleList) {
            container.addRule(rule);
        }
        await container.run();
    }
    async getRuleList() {
        const ruleList = [
            await this.projectStruct(),
            await this.packageJson(),
            await this.ruleIoc(),
        ];
        if (this.projectType === ProjectType.FaaS) {
            ruleList.push(this.ruleTSConfig(), await this.ruleFaaS());
        }
        if (this.projectType === ProjectType.FaaS ||
            this.projectType === ProjectType.MigrateToFaaS) {
            ruleList.push(this.ruleFYaml());
        }
        const moreCheckRules = this.getStore('checkRules', 'global');
        if (moreCheckRules && Array.isArray(moreCheckRules)) {
            for (const getRule of moreCheckRules) {
                const rule = await getRule();
                ruleList.push(rule);
            }
        }
        return ruleList;
    }
    // package json 校验
    async packageJson() {
        const pkjJson = this.pkg;
        const pkgExists = !!Object.keys(this.pkg).length;
        return runner => {
            runner
                .group('package.json check')
                .check('exists', () => {
                if (!pkgExists) {
                    return [false, 'not exist package.json'];
                }
                return [true];
            })
                .check('no cli deps', () => {
                if (!pkgExists) {
                    return [true];
                }
                const deps = pkjJson['dependencies'] || {};
                const cliDeps = Object.keys(deps).filter(name => {
                    if (name === '@midwayjs/cli') {
                        return true;
                    }
                    if (name.includes('/fcli-plugin-')) {
                        return true;
                    }
                    if (name.endsWith('/faas-cli')) {
                        return true;
                    }
                    if (name.endsWith('/faas-fun')) {
                        return true;
                    }
                    if (name.endsWith('/faas-invoke')) {
                        return true;
                    }
                    if (name.includes('@midwayjs/cli-plugin-')) {
                        return true;
                    }
                    if (name === '@midwayjs/serverless-app') {
                        return true;
                    }
                    if (name === '@midwayjs/serverless-app') {
                        return true;
                    }
                    if (name.startsWith('@serverless-devs/')) {
                        return true;
                    }
                    if (name === '@alicloud/fun') {
                        return true;
                    }
                    return false;
                });
                if (cliDeps.length) {
                    return [
                        false,
                        'dependencies are not allowed to exist ' + cliDeps.join(', '),
                    ];
                }
                return [true];
            })
                .check('@midwayjs/core version', async () => {
                try {
                    const version = (await (0, command_core_1.exec)({
                        cmd: 'npm ls @midwayjs/core',
                        slience: true,
                        baseDir: this.globalData.cwd,
                    }));
                    const reg = /@midwayjs\/core@([\w\-.]*)/g;
                    let matched;
                    const versions = {};
                    while (version && (matched = reg.exec(version))) {
                        versions[matched[1]] = true;
                    }
                    const versionList = Object.keys(versions);
                    if (versionList.length > 1) {
                        return [
                            false,
                            `There are ${versionList.length} versions(${versionList.join(', ')}) of @midwayjs/core`,
                        ];
                    }
                }
                catch (_a) {
                    //
                }
                return [true];
            })
                .check('@midwayjs/decorator version', async () => {
                try {
                    const version = (await (0, command_core_1.exec)({
                        cmd: 'npm ls @midwayjs/decorator',
                        slience: true,
                        baseDir: this.globalData.cwd,
                    }));
                    const reg = /@midwayjs\/decorator@([\w\-.]*)/g;
                    let matched;
                    const versions = {};
                    while (version && (matched = reg.exec(version))) {
                        versions[matched[1]] = true;
                    }
                    const versionList = Object.keys(versions);
                    if (versionList.length > 1) {
                        return [
                            false,
                            `There are ${versionList.length} versions(${versionList.join(', ')}) of @midwayjs/decorator`,
                        ];
                    }
                }
                catch (_a) {
                    //
                }
                return [true];
            })
                .check('midway componnet version', async () => {
                const deps = pkjJson['dependencies'] || {};
                const coreMod = [
                    '@midwayjs/core',
                    '@midwayjs/decorator',
                    '@midwayjs/faas',
                    '@midwayjs/koa',
                    '@midwayjs/web',
                    '@midwayjs/express',
                ].find(modName => {
                    return deps[modName];
                });
                if (!coreMod) {
                    return [CHECK_SKIP];
                }
                const coreModVersion = (0, utils_1.getMainVersion)(deps[coreMod]);
                const components = [
                    'axios',
                    'cache',
                    'cos',
                    'jwt',
                    'orm',
                    'oss',
                    'redis',
                    'swagger',
                    'task',
                    'tablestore',
                    'mongoose',
                ].filter(name => {
                    const modName = `@midwayjs/${name}`;
                    if (!deps[modName]) {
                        return false;
                    }
                    const version = (0, utils_1.getMainVersion)(deps[modName]);
                    if (version !== coreModVersion) {
                        return true;
                    }
                });
                if (components.length) {
                    return [
                        false,
                        `${coreMod}@${coreModVersion} and ${components
                            .map(comp => {
                            const mod = `@midwayjs/${comp}`;
                            return `${mod}@${deps[mod]}`;
                        })
                            .join(', ')} are incompatible, please use ${components
                            .map(comp => `@midwayjs/${comp}@${coreModVersion}`)
                            .join(', ')}`,
                    ];
                }
                return [true];
            })
                .check('midway version', async () => {
                const result = (0, midway_version_1.check)();
                if (Array.isArray(result)) {
                    return [
                        false,
                        result.map(item => {
                            return `${item.name}@${item.current} is not compatible with your project, please use ${item.name}@${[].concat(item.allow)[0]}`;
                        }),
                    ];
                }
                return [true];
            });
        };
    }
    // 校验项目结构
    async projectStruct() {
        const cwd = this.getCwd();
        let tsCodeRootCheckPassed = true;
        return runner => {
            runner
                .group('project struct check')
                .check('node version', () => {
                const v = +process.version.split('.')[0].replace(/[^\d]/g, '');
                if (v < 12) {
                    return [false, 'Node Version shoule >= Node 12'];
                }
                return [true];
            })
                .check('ts root', () => {
                if (!this.globalData.tsCodeRoot) {
                    tsCodeRootCheckPassed = false;
                    return [false, 'no tsCodeRoot, tsconfig.json may not exist'];
                }
                return [true];
            })
                .check('ts root should not be same to cwd', () => {
                if (this.globalData.tsCodeRoot === cwd) {
                    tsCodeRootCheckPassed = false;
                    return [
                        false,
                        'ts file should be in src directory, other ts directory should be configured in tsconfig.json exclude attribute',
                    ];
                }
                return [true];
            })
                .check('project type', async () => {
                var _a;
                if ((this.projectType === ProjectType.FaaS &&
                    !this.globalData.locateResult) ||
                    ((_a = this.globalData.locateResult) === null || _a === void 0 ? void 0 : _a.projectType) === 'unknown') {
                    return [false, 'can not check faas project type'];
                }
                return [true];
            })
                .check('config', () => {
                if (!tsCodeRootCheckPassed) {
                    return [true];
                }
                const existsConfig = (0, fs_1.existsSync)((0, path_1.join)(this.globalData.tsCodeRoot, 'config'));
                const configuration = (0, path_1.join)(this.globalData.tsCodeRoot, 'configuration.ts');
                let configurationData;
                if ((0, fs_1.existsSync)(configuration)) {
                    configurationData = (0, fs_1.readFileSync)(configuration).toString();
                }
                if (!existsConfig) {
                    if (configurationData) {
                        if (configurationData.includes('importConfigs')) {
                            return [false, 'config directory is required'];
                        }
                    }
                    return [true];
                }
                if (!configurationData) {
                    return [false, 'config need to be set in configuration.ts'];
                }
                if (!configurationData.includes('importConfigs')) {
                    return [false, 'config need to be set in configuration.ts'];
                }
                if (configurationData.includes('./config/config.') &&
                    !/\s+from\s+'\.\/config\/config\./.test(configurationData)) {
                    return [
                        false,
                        "please use join(__dirname, './config/') to import config",
                    ];
                }
                return [true];
            })
                .check('config file', () => {
                if (!tsCodeRootCheckPassed) {
                    return [true];
                }
                const existsConfig = (0, fs_1.existsSync)((0, path_1.join)(this.globalData.tsCodeRoot, 'config'));
                if (!existsConfig) {
                    return [true];
                }
                const configLocal = (0, path_1.join)(this.globalData.tsCodeRoot, 'config/config.local.ts');
                const configDaily = (0, path_1.join)(this.globalData.tsCodeRoot, 'config/config.daily.ts');
                const configProd = (0, path_1.join)(this.globalData.tsCodeRoot, 'config/config.prod.ts');
                const configDefault = (0, path_1.join)(this.globalData.tsCodeRoot, 'config/config.default.ts');
                if ((0, fs_1.existsSync)(configDaily) && !(0, fs_1.existsSync)(configLocal)) {
                    return [false, 'only daily env config, can not run on local env'];
                }
                if ((0, fs_1.existsSync)(configLocal) && !(0, fs_1.existsSync)(configDaily)) {
                    return [false, 'only local env config, can not run on daily env'];
                }
                if (!(0, fs_1.existsSync)(configProd) && !(0, fs_1.existsSync)(configDefault)) {
                    return [false, 'no prod or default config'];
                }
                return [true];
            })
                .check('config export', () => {
                if (!tsCodeRootCheckPassed) {
                    return [true];
                }
                const existsConfig = (0, fs_1.existsSync)((0, path_1.join)(this.globalData.tsCodeRoot, 'config'));
                if (!existsConfig) {
                    return [true];
                }
                const configWithExportDefaultAndNamed = [
                    'local',
                    'daily',
                    'pre',
                    'prod',
                    'default',
                ].filter(name => {
                    const configFile = (0, path_1.join)(this.globalData.tsCodeRoot, `config/config.${name}.ts`);
                    if (!(0, fs_1.existsSync)(configFile)) {
                        return;
                    }
                    const code = (0, fs_1.readFileSync)(configFile).toString();
                    return (code.includes('export const ') && code.includes('export default '));
                });
                if (configWithExportDefaultAndNamed.length) {
                    return [
                        false,
                        `default and named export cannot coexist in ${configWithExportDefaultAndNamed.join(' and ')} environment config`,
                    ];
                }
                return [true];
            })
                .check('config inject', () => {
                if (!tsCodeRootCheckPassed) {
                    return [true];
                }
                // 注入的 config 检测
                const configInjectReg = /@config\(\s*(?:['"](\w+)['"])?\s*\)(?:\n|\s)*(\w+)(:|;|\n|\s)/gi;
                let execRes;
                const requiredConfigMap = {};
                for (const { code, tsSourceFile } of this.sourcesInfo) {
                    while ((execRes = configInjectReg.exec(code))) {
                        const configName = execRes[1] || execRes[2];
                        if (configName) {
                            requiredConfigMap[configName] = tsSourceFile;
                        }
                    }
                }
                const allConfigKey = Object.keys(requiredConfigMap);
                const allConfigContent = [
                    'local',
                    'daily',
                    'pre',
                    'prod',
                    'default',
                ].map(env => {
                    const configFile = (0, path_1.join)(this.globalData.tsCodeRoot, `config/config.${env}.ts`);
                    if (!(0, fs_1.existsSync)(configFile)) {
                        return '';
                    }
                    return (0, fs_1.readFileSync)(configFile).toString();
                });
                const notFindConfig = allConfigKey.filter(config => {
                    const reg = new RegExp(`\\s${config}\\s*[=:]|\\.${config}\\s*[=:]`);
                    return !allConfigContent.find(code => reg.test(code));
                });
                if (!notFindConfig.length) {
                    return [true];
                }
                return [
                    false,
                    `config ${notFindConfig.join(', ')} was been injected, but not define in config/config.$env.ts`,
                ];
            })
                .check('hooks import', () => {
                if (!this.isHooks || !this.globalData.tsCodeRoot) {
                    return [CHECK_SKIP];
                }
                const configurationFile = (0, path_1.join)(this.globalData.tsCodeRoot, 'configuration.ts');
                if (!(0, fs_1.existsSync)(configurationFile)) {
                    return [false, 'midway hooks need configutation.ts'];
                }
                const configurationData = (0, fs_1.readFileSync)(configurationFile).toString();
                if (!configurationData.includes('hooks(')) {
                    return [false, 'Need add hooks() to configutation.ts imports list'];
                }
                return [true];
            })
                .check('configuration class', () => {
                if (!this.globalData.tsCodeRoot) {
                    return [CHECK_SKIP];
                }
                const configurationFile = (0, path_1.join)(this.globalData.tsCodeRoot, 'configuration.ts');
                if (!(0, fs_1.existsSync)(configurationFile)) {
                    return [CHECK_SKIP];
                }
                const configurationData = (0, fs_1.readFileSync)(configurationFile).toString();
                const exportClasses = configurationData.split('export class ');
                const classesCount = exportClasses.length - 1;
                if (classesCount !== 1) {
                    return [
                        false,
                        `configuration needs to export 1 class (current ${classesCount})`,
                    ];
                }
                return [true];
            });
        };
    }
    async ruleFaaS() {
        // 校验是否存在 decorator 重名
        // 校验 @Logger 装饰器所在class是否被继承
        return runner => {
            runner
                .group('faas')
                .check('f command', () => {
                var _a;
                if (!((_a = this.pkg.dependencies) === null || _a === void 0 ? void 0 : _a['@midwayjs/cli'])) {
                    return [CHECK_SKIP];
                }
                if (/("|\s)f\s+(invoke|test)/.test(this.pkgContent)) {
                    return [
                        false,
                        'you can no longer use the f command after using @midwayjs/cli',
                    ];
                }
                return [true];
            })
                .check('@Func', () => {
                for (const { code, tsSourceFile } of this.sourcesInfo) {
                    if (code.includes('@ServerlessTrigger') &&
                        (code.includes('@Func') || code.includes('@func'))) {
                        return [
                            false,
                            `@func decorator is deprecated, use @ServerlessTrigger instead.(${tsSourceFile})`,
                        ];
                    }
                }
                return [true];
            });
        };
    }
    // 校验yaml格式
    ruleFYaml() {
        // yaml 配置
        const yamlFile = (0, path_1.join)(this.getCwd(), 'f.yml');
        let yamlObj;
        let error;
        try {
            const contents = (0, fs_1.readFileSync)(yamlFile).toString();
            yamlObj = YAML.load(contents.toString(), {});
        }
        catch (exception) {
            error = exception;
        }
        return runner => {
            runner
                .group('f.yml check')
                .check('format', () => {
                if (error) {
                    return [false, 'Yaml format error: ' + error.message];
                }
                return [true];
            })
                .check('service', () => {
                if (!(yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.service)) {
                    return [false, 'Yaml should have service config'];
                }
                return [true];
            })
                .check('provider', () => {
                var _a;
                if (!(yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.provider)) {
                    return [false, 'Yaml should have provider config'];
                }
                if (!((_a = yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.provider) === null || _a === void 0 ? void 0 : _a.name)) {
                    return [
                        false,
                        'Yaml should have provider.name config, e.g. aliyun',
                    ];
                }
                return [true];
            })
                .check('provider starter', () => {
                var _a;
                const yamlStarter = (_a = yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.provider) === null || _a === void 0 ? void 0 : _a.starter;
                if (!yamlStarter) {
                    return [true];
                }
                const deps = this.pkg.dependencies || {};
                if (!deps[yamlStarter]) {
                    return [
                        false,
                        `${yamlStarter} need to be added to pacakge.json dependencies`,
                    ];
                }
                return [true];
            })
                .check('trigger list', () => {
                if (!(yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.functions)) {
                    return [CHECK_SKIP];
                }
                const allFunc = Object.keys(yamlObj.functions);
                for (const funcName of allFunc) {
                    const funcInfo = yamlObj.functions[funcName];
                    // 允许无触发器配置
                    if (!funcInfo.events) {
                        continue;
                    }
                    if (!Array.isArray(funcInfo.events)) {
                        return [
                            false,
                            `function '${funcName}' events type should be Array`,
                        ];
                    }
                }
                return [true];
            })
                .check('http trigger', () => {
                if (!(yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.functions)) {
                    return [CHECK_SKIP];
                }
                const allFunc = Object.keys(yamlObj.functions);
                for (const funcName of allFunc) {
                    const funcInfo = yamlObj.functions[funcName];
                    if (!funcInfo.events || !Array.isArray(funcInfo.events)) {
                        continue;
                    }
                    const httpTriggers = funcInfo.events.filter(event => {
                        return (event === null || event === void 0 ? void 0 : event.http) || (event === null || event === void 0 ? void 0 : event.apigw);
                    });
                    if (!httpTriggers.length) {
                        continue;
                    }
                    for (const httpTrigger of httpTriggers) {
                        const triggerInfo = httpTrigger.http || httpTrigger.apigw;
                        if (!triggerInfo.path) {
                            return [
                                false,
                                `function '${funcName}' http.trigger need path attribute`,
                            ];
                        }
                        if (triggerInfo.method && !Array.isArray(triggerInfo.method)) {
                            return [
                                false,
                                `function '${funcName}' http.trigger.method type should be Array`,
                            ];
                        }
                    }
                }
                return [true];
            })
                .check('package in/exclude type', () => {
                if (!(yamlObj === null || yamlObj === void 0 ? void 0 : yamlObj.package)) {
                    return [CHECK_SKIP];
                }
                if (yamlObj.package.include &&
                    !Array.isArray(yamlObj.package.include)) {
                    return [false, 'YAML package.include type should be Array'];
                }
                if (yamlObj.package.exclude &&
                    !Array.isArray(yamlObj.package.exclude)) {
                    return [false, 'YAML package.exclude type should be Array'];
                }
                return [true];
            })
                .check('deployType', () => {
                var _a;
                const deps = this.pkg.dependencies || {};
                const deployType = typeof yamlObj.deployType === 'string'
                    ? yamlObj.deployType
                    : (_a = yamlObj.deployType) === null || _a === void 0 ? void 0 : _a.type;
                if (deps['@midwayjs/faas'] || deps['@ali/midway-faas']) {
                    if (deployType) {
                        return [
                            false,
                            'faas does not allow the deployType to be configured in the f.yml file',
                        ];
                    }
                }
                if ((deps['@midwayjs/koa'] || deps['koa']) && deployType !== 'koa') {
                    return [
                        false,
                        'Deploying koa as FAAS requires configuring the deployType as koa in the f.yml file',
                    ];
                }
                if ((deps['@midwayjs/express'] || deps['express']) &&
                    deployType !== 'express') {
                    return [
                        false,
                        'Deploying express as FAAS requires configuring the deployType as express in the f.yml file',
                    ];
                }
                if ((deps['@midwayjs/web'] || deps['egg']) && deployType !== 'egg') {
                    return [
                        false,
                        'Deploying egg as FAAS requires configuring the deployType as egg in the f.yml file',
                    ];
                }
                return [true];
            });
        };
    }
    ruleTSConfig() {
        const tsConfigFile = (0, path_1.join)(this.getCwd(), 'tsconfig.json');
        const exists = (0, fs_1.existsSync)(tsConfigFile);
        let tsconfig;
        return runner => {
            runner
                .group('tsconfig check')
                .check('exists', () => {
                if (!exists) {
                    return [false, 'tsconfig.json not exists'];
                }
                return [true];
            })
                .check('parse', () => {
                if (!exists) {
                    return [CHECK_SKIP];
                }
                try {
                    tsconfig = JSON.parse((0, fs_1.readFileSync)(tsConfigFile).toString());
                }
                catch (e) {
                    return [false, 'tsconfig parse error: ' + e.message];
                }
                return [true];
            })
                .check('compiler target', () => {
                var _a;
                const target = (_a = tsconfig === null || tsconfig === void 0 ? void 0 : tsconfig.compilerOptions) === null || _a === void 0 ? void 0 : _a.target;
                if (!target) {
                    return [CHECK_SKIP];
                }
                const targetMap = {
                    es3: 3,
                    es5: 5,
                    es6: 6,
                    es7: 7,
                    es2015: 6,
                    es2016: 7,
                    es2017: 8,
                    es2018: 9,
                    es2019: 10,
                    es2020: 11,
                    es2021: 12,
                    esnext: 12,
                };
                const targetVersion = targetMap[target.toLowerCase().replace(/\s+/g, '')];
                if (!targetVersion) {
                    return [
                        false,
                        `tsconfig target version(${targetVersion}) is not supported`,
                    ];
                }
                else if (targetVersion > 9) {
                    return [false, 'tsconfig target need ≤ es2018'];
                }
                return [true];
            })
                .check('emitDecoratorMetadata', () => {
                var _a;
                const emitDecoratorMetadata = (_a = tsconfig === null || tsconfig === void 0 ? void 0 : tsconfig.compilerOptions) === null || _a === void 0 ? void 0 : _a.emitDecoratorMetadata;
                if (!emitDecoratorMetadata) {
                    return [false, 'tsconfig emitDecoratorMetadata need true'];
                }
                return [true];
            })
                .check('ts-node', () => {
                var _a, _b;
                if ((_a = this.pkg.dependencies) === null || _a === void 0 ? void 0 : _a['ts-node']) {
                    return [false, 'ts-node needs to be placed in devDependencies'];
                }
                const tsnode = (_b = this.pkg.devDependencies) === null || _b === void 0 ? void 0 : _b['ts-node'];
                if (!tsnode) {
                    return [CHECK_SKIP];
                }
                const version = tsnode.replace(/[^\d.]/g, '').split('.')[0];
                if (version && /^\d+$/.test(version) && version < 10) {
                    return [false, 'ts-node needs to be upgrated to version 10'];
                }
                return [true];
            });
        };
    }
    async ruleIoc() {
        return runner => {
            runner.group('ioc check').check('class define', async () => {
                const classNameMap = {};
                for (const { code, tsSourceFile } of this.sourcesInfo) {
                    // @Provider() export default class xxx extends xxx {}
                    const reg = /@(?:provider|controller)\([^)]*\)(?:\n|\s)*(export)?(\s+default\s+)?\s*class\s+(.*?)\s+/gi;
                    let execRes;
                    while ((execRes = reg.exec(code))) {
                        const className = execRes[3];
                        // export
                        if (!execRes[1]) {
                            return [
                                false,
                                `class ${className} need export in ${tsSourceFile}`,
                            ];
                        }
                        // export default
                        if (execRes[2]) {
                            return [
                                false,
                                `class ${className} can not export "default" in ${tsSourceFile}`,
                            ];
                        }
                        if (classNameMap[className]) {
                            return [
                                false,
                                `there is a duplicate class name(${className}) in ${classNameMap[className]} and ${tsSourceFile}`,
                            ];
                        }
                        classNameMap[className] = tsSourceFile;
                    }
                }
                return [true];
            });
        };
    }
    getCheckReporter() {
        return {
            reportGroup: data => {
                this.currentGroup = data.group;
                this.checkReporterOutput();
                this.checkReporterOutput({
                    msg: data.group,
                    prefix: '◎',
                    color: CHECK_COLOR.GROUP,
                });
                this.checkReporterOutput();
            },
            reportCheck: data => {
                if (data.message === CHECK_SKIP) {
                    this.core.debug('skip check', this.currentGroup, data.title);
                }
                else if (data.message) {
                    this.checkReporterOutput({
                        msg: data.title,
                        prefix: '✔',
                        color: CHECK_COLOR.SUCCESS,
                        ident: 1,
                    });
                }
                else {
                    this.errors.push({
                        group: this.currentGroup,
                        title: data.title,
                        message: data.result,
                    });
                    this.checkReporterOutput({
                        msg: data.title,
                        prefix: '✗',
                        color: CHECK_COLOR.ERROR,
                        ident: 1,
                    });
                }
            },
            reportWarn: () => { },
            reportError: () => { },
            reportEnd: () => {
                if (this.errors.length) {
                    this.checkReporterOutput();
                    this.checkReporterOutput({
                        msg: 'Check Not Passed:',
                        color: CHECK_COLOR.ERROR,
                    });
                    let i = 1;
                    for (const error of this.errors) {
                        const messages = [].concat(error.message || []);
                        for (const message of messages) {
                            this.checkReporterOutput({
                                msg: `${i++}. ${message} [ ${error.group} ]`,
                                color: CHECK_COLOR.ERROR,
                                ident: 1,
                            });
                        }
                    }
                }
                else {
                    this.checkReporterOutput();
                    this.checkReporterOutput({
                        msg: 'All Check Passed',
                        color: CHECK_COLOR.SUCCESS,
                    });
                }
            },
            reportStart: () => { },
            reportInfo: () => { },
            reportSkip: () => { },
        };
    }
    checkReporterOutput(message) {
        if (!message) {
            message = {
                msg: '',
            };
        }
        else if (typeof message === 'string') {
            message = {
                msg: message,
            };
        }
        let msg = message.msg || '';
        if (message.prefix) {
            msg = message.prefix + ' ' + msg;
        }
        if (message.ident) {
            msg = Array(message.ident).fill(' ').join(' ') + msg;
        }
        if (message.color) {
            msg = chalk.hex(message.color)(msg);
        }
        this.core.cli.log(msg);
    }
    getYamlFilePosition() {
        return (0, path_1.join)(this.getCwd(), 'f.yml');
    }
    getCwd() {
        return this.servicePath || this.core.cwd || process.cwd();
    }
}
exports.CheckPlugin = CheckPlugin;
//# sourceMappingURL=index.js.map