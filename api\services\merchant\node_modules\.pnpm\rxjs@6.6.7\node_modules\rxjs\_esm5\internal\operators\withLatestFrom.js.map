{"version": 3, "file": "withLatestFrom.js", "sources": ["../../../src/internal/operators/withLatestFrom.ts"], "names": [], "mappings": ";AAGA,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAC;AAErD,OAAO,EAAE,iBAAiB,EAAE,MAAM,2BAA2B,CAAC;AAiE9D,MAAM,UAAU,cAAc;IAAO,cAAqE;SAArE,UAAqE,EAArE,qBAAqE,EAArE,IAAqE;QAArE,yBAAqE;;IACxG,OAAO,UAAC,MAAqB;QAC3B,IAAI,OAAY,CAAC;QACjB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;YAC/C,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;SACtB;QACD,IAAM,WAAW,GAAsB,IAAI,CAAC;QAC5C,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,sBAAsB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC;AACJ,CAAC;AAED;IACE,gCAAoB,WAA8B,EAC9B,OAA6C;QAD7C,gBAAW,GAAX,WAAW,CAAmB;QAC9B,YAAO,GAAP,OAAO,CAAsC;IACjE,CAAC;IAED,qCAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,wBAAwB,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IACpG,CAAC;IACH,6BAAC;AAAD,CAAC,AARD,IAQC;AAOD;IAA6C,oDAAqB;IAIhE,kCAAY,WAA0B,EAClB,WAA8B,EAC9B,OAA6C;QAFjE,YAGE,kBAAM,WAAW,CAAC,SAYnB;QAdmB,iBAAW,GAAX,WAAW,CAAmB;QAC9B,aAAO,GAAP,OAAO,CAAsC;QAJzD,eAAS,GAAa,EAAE,CAAC;QAM/B,IAAM,GAAG,GAAG,WAAW,CAAC,MAAM,CAAC;QAC/B,KAAI,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAE7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,KAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;SACxB;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,IAAI,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAChC,KAAI,CAAC,GAAG,CAAC,iBAAiB,CAAO,KAAI,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;SACnE;;IACH,CAAC;IAED,6CAAU,GAAV,UAAW,WAAc,EAAE,UAAa,EAC7B,UAAkB;QAC3B,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC;QACrC,IAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACxB,IAAM,KAAK,GAAG,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC5B;SACF;IACH,CAAC;IAED,iDAAc,GAAd;IAEA,CAAC;IAES,wCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,IAAM,IAAI,IAAI,KAAK,SAAK,IAAI,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;aACxB;iBAAM;gBACL,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,IAAI,CAAC,CAAC;aAC9B;SACF;IACH,CAAC;IAEO,8CAAW,GAAnB,UAAoB,IAAW;QAC7B,IAAI,MAAW,CAAC;QAChB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,OAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SAC1C;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,GAAG,CAAC,CAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,MAAM,CAAC,CAAC;IACjC,CAAC;IACH,+BAAC;AAAD,CAAC,AA1DD,CAA6C,eAAe,GA0D3D"}