import { ValidationOptions } from '../ValidationOptions';
export declare const IS_MIME_TYPE = "isMimeType";
/**
 * Check if the string matches to a valid MIME type format
 * If given value is not a string, then it returns false.
 */
export declare function isMimeType(value: unknown): boolean;
/**
 * Check if the string matches to a valid MIME type format
 * If given value is not a string, then it returns false.
 */
export declare function IsMimeType(validationOptions?: ValidationOptions): PropertyDecorator;
