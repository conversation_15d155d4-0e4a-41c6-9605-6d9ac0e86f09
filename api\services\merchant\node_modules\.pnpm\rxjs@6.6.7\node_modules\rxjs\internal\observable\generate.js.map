{"version": 3, "file": "generate.js", "sources": ["../../src/internal/observable/generate.ts"], "names": [], "mappings": ";;AAAA,4CAA2C;AAE3C,6CAA4C;AAE5C,mDAAkD;AA8PlD,SAAgB,QAAQ,CAAO,qBAAgD,EAChD,SAA4B,EAC5B,OAAwB,EACxB,0BAA+D,EAC/D,SAAyB;IAEtD,IAAI,cAAgC,CAAC;IACrC,IAAI,YAAe,CAAC;IAEpB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;QACzB,IAAM,OAAO,GAAG,qBAA8C,CAAC;QAC/D,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;QACpC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAC9B,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAC1B,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,mBAA4B,CAAC;QACxE,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;KAC/B;SAAM,IAAI,0BAA0B,KAAK,SAAS,IAAI,yBAAW,CAAC,0BAA0B,CAAC,EAAE;QAC9F,YAAY,GAAG,qBAA0B,CAAC;QAC1C,cAAc,GAAG,mBAA4B,CAAC;QAC9C,SAAS,GAAG,0BAA2C,CAAC;KACzD;SAAM;QACL,YAAY,GAAG,qBAA0B,CAAC;QAC1C,cAAc,GAAG,0BAA8C,CAAC;KACjE;IAED,OAAO,IAAI,uBAAU,CAAI,UAAA,UAAU;QACjC,IAAI,KAAK,GAAG,YAAY,CAAC;QACzB,IAAI,SAAS,EAAE;YACb,OAAO,SAAS,CAAC,QAAQ,CAAuB,QAAQ,EAAE,CAAC,EAAE;gBAC3D,UAAU,YAAA;gBACV,OAAO,SAAA;gBACP,SAAS,WAAA;gBACT,cAAc,gBAAA;gBACd,KAAK,OAAA;aACN,CAAC,CAAC;SACJ;QAED,GAAG;YACD,IAAI,SAAS,EAAE;gBACb,IAAI,eAAe,SAAS,CAAC;gBAC7B,IAAI;oBACF,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;iBACpC;gBAAC,OAAO,GAAG,EAAE;oBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACtB,OAAO,SAAS,CAAC;iBAClB;gBACD,IAAI,CAAC,eAAe,EAAE;oBACpB,UAAU,CAAC,QAAQ,EAAE,CAAC;oBACtB,MAAM;iBACP;aACF;YACD,IAAI,KAAK,SAAG,CAAC;YACb,IAAI;gBACF,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;aAC/B;YAAC,OAAO,GAAG,EAAE;gBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,OAAO,SAAS,CAAC;aAClB;YACD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACvB,IAAI,UAAU,CAAC,MAAM,EAAE;gBACrB,MAAM;aACP;YACD,IAAI;gBACF,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;aACxB;YAAC,OAAO,GAAG,EAAE;gBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACtB,OAAO,SAAS,CAAC;aAClB;SACF,QAAQ,IAAI,EAAE;QAEf,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAxED,4BAwEC;AAED,SAAS,QAAQ,CAAoD,KAA2B;IACtF,IAAA,6BAAU,EAAE,2BAAS,CAAW;IACxC,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,KAAK,CAAC,WAAW,EAAE;QACrB,IAAI;YACF,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC1C;QAAC,OAAO,GAAG,EAAE;YACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,OAAO,SAAS,CAAC;SAClB;KACF;SAAM;QACL,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;KAC1B;IACD,IAAI,SAAS,EAAE;QACb,IAAI,eAAe,SAAS,CAAC;QAC7B,IAAI;YACF,eAAe,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC1C;QAAC,OAAO,GAAG,EAAE;YACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACtB,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,CAAC,eAAe,EAAE;YACpB,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,UAAU,CAAC,MAAM,EAAE;YACrB,OAAO,SAAS,CAAC;SAClB;KACF;IACD,IAAI,KAAQ,CAAC;IACb,IAAI;QACF,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;KAC3C;IAAC,OAAO,GAAG,EAAE;QACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACtB,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IACD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACvB,IAAI,UAAU,CAAC,MAAM,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC"}