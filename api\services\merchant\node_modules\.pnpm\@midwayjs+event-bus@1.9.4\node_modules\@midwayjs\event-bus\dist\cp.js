"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChildProcessEventBus = void 0;
const base_1 = require("./base");
const cluster = require("cluster");
const process = require("process");
class ChildProcessEventBus extends base_1.AbstractEventBus {
    workerSubscribeMessage(subscribeMessageHandler) {
        process.on('message', subscribeMessageHandler);
    }
    workerListenMessage(worker, subscribeMessageHandler) {
        worker.on('message', subscribeMessageHandler);
    }
    workerSendMessage(message) {
        process.send(message);
    }
    mainSendMessage(worker, message) {
        worker.send(message);
    }
    isMain() {
        return !this.isWorker();
    }
    isWorker() {
        var _a;
        return (_a = this.options.isWorker) !== null && _a !== void 0 ? _a : cluster.isWorker;
    }
    getWorkerId(worker) {
        var _a;
        return String(worker ? (_a = worker.pid) !== null && _a !== void 0 ? _a : worker === null || worker === void 0 ? void 0 : worker['process'].pid : process.pid);
    }
}
exports.ChildProcessEventBus = ChildProcessEventBus;
//# sourceMappingURL=cp.js.map