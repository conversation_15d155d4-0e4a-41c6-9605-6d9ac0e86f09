import { ClassMiddleware, CommonMiddleware, CommonMiddlewareUnion, CompositionMiddleware, IMiddlewareManager, IMidwayContext } from '../interface';
export declare class ContextMiddlewareManager<CTX extends IMidwayContext, R, N> extends Array<CommonMiddleware<CTX, R, N>> implements IMiddlewareManager<CTX, R, N> {
    /**
     * insert a middleware or middleware array to first
     * @param middleware
     */
    insertFirst(middleware: CommonMiddlewareUnion<CTX, R, N>): void;
    /**
     * insert a middleware or middleware array to last
     * @param middleware
     */
    insertLast(middleware: CommonMiddlewareUnion<CTX, R, N>): void;
    /**
     * insert a middleware or middleware array to after another middleware
     * @param middleware
     * @param idxOrBeforeMiddleware
     */
    insertBefore(middleware: CommonMiddlewareUnion<CTX, R, N>, idxOrBeforeMiddleware: CommonMiddleware<CTX, R, N> | string | number): void;
    /**
     * insert a middleware or middleware array to after another middleware
     * @param middleware
     * @param idxOrAfterMiddleware
     */
    insertAfter(middleware: CommonMiddlewareUnion<CTX, R, N>, idxOrAfterMiddleware: CommonMiddleware<CTX, R, N> | string | number): void;
    /**
     * move a middleware after another middleware
     * @param middlewareOrName
     * @param afterMiddleware
     */
    findAndInsertAfter(middlewareOrName: CommonMiddleware<CTX, R, N> | string, afterMiddleware: CommonMiddleware<CTX, R, N> | string | number): void;
    /**
     * move a middleware before another middleware
     * @param middlewareOrName
     * @param beforeMiddleware
     */
    findAndInsertBefore(middlewareOrName: CommonMiddleware<CTX, R, N> | string, beforeMiddleware: CommonMiddleware<CTX, R, N> | string | number): void;
    /**
     * find middleware and move to first
     * @param middlewareOrName
     */
    findAndInsertFirst(middlewareOrName: CommonMiddleware<CTX, R, N> | string): void;
    /**
     * find middleware and move to last
     * @param middlewareOrName
     */
    findAndInsertLast(middlewareOrName: CommonMiddleware<CTX, R, N> | string): void;
    /**
     * find a middleware and return index
     * @param middlewareOrName
     */
    findItemIndex(middlewareOrName: CommonMiddleware<CTX, R, N> | string | number): number;
    findItem(middlewareOrName: CommonMiddleware<CTX, R, N> | string | number): CommonMiddleware<CTX, R, N>;
    /**
     * get name from middleware
     * @param middleware
     */
    getMiddlewareName(middleware: CommonMiddleware<CTX, R, N>): string;
    /**
     * remove a middleware
     * @param middlewareOrNameOrIdx
     */
    remove(middlewareOrNameOrIdx: CommonMiddleware<CTX, R, N> | string | number): CommonMiddleware<CTX, R, N>;
    push(...items: any[]): number;
    unshift(...items: any[]): number;
    /**
     * get middleware name list
     */
    getNames(): string[];
}
/**
 * Get middleware resolve options
 */
type MiddlewareResolveOptions<T, CTX, R, N> = T extends ClassMiddleware<CTX, R, N> ? InstanceType<T> extends {
    resolve: (...args: infer P) => any;
} ? P[1] : any : never;
/**
 * wrap a middleware with options and composition a new middleware
 */
export declare function createMiddleware<CTX extends IMidwayContext, R, N, M extends ClassMiddleware<CTX, R, N>>(middleware: M, options: MiddlewareResolveOptions<M, CTX, R, N>, name?: string): CompositionMiddleware<CTX, R, N>;
export {};
//# sourceMappingURL=middlewareManager.d.ts.map