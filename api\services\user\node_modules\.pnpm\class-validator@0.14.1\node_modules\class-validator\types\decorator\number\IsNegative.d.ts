import { ValidationOptions } from '../ValidationOptions';
export declare const IS_NEGATIVE = "isNegative";
/**
 * Checks if the value is a negative number smaller than zero.
 */
export declare function isNegative(value: unknown): boolean;
/**
 * Checks if the value is a negative number smaller than zero.
 */
export declare function IsNegative(validationOptions?: ValidationOptions): PropertyDecorator;
