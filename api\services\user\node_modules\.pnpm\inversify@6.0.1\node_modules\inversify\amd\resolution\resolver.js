var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
define(["require", "exports", "../constants/error_msgs", "../constants/literal_types", "../planning/planner", "../scope/scope", "../utils/async", "../utils/binding_utils", "../utils/exceptions", "./instantiation"], function (require, exports, ERROR_MSGS, literal_types_1, planner_1, scope_1, async_1, binding_utils_1, exceptions_1, instantiation_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.resolve = void 0;
    ERROR_MSGS = __importStar(ERROR_MSGS);
    var _resolveRequest = function (requestScope) {
        return function (request) {
            request.parentContext.setCurrentRequest(request);
            var bindings = request.bindings;
            var childRequests = request.childRequests;
            var targetIsAnArray = request.target && request.target.isArray();
            var targetParentIsNotAnArray = !request.parentRequest ||
                !request.parentRequest.target ||
                !request.target ||
                !request.parentRequest.target.matchesArray(request.target.serviceIdentifier);
            if (targetIsAnArray && targetParentIsNotAnArray) {
                return childRequests.map(function (childRequest) {
                    var _f = _resolveRequest(requestScope);
                    return _f(childRequest);
                });
            }
            else {
                if (request.target.isOptional() && bindings.length === 0) {
                    return undefined;
                }
                var binding = bindings[0];
                return _resolveBinding(requestScope, request, binding);
            }
        };
    };
    var _resolveFactoryFromBinding = function (binding, context) {
        var factoryDetails = (0, binding_utils_1.getFactoryDetails)(binding);
        return (0, exceptions_1.tryAndThrowErrorIfStackOverflow)(function () { return factoryDetails.factory.bind(binding)(context); }, function () { return new Error(ERROR_MSGS.CIRCULAR_DEPENDENCY_IN_FACTORY(factoryDetails.factoryType, context.currentRequest.serviceIdentifier.toString())); });
    };
    var _getResolvedFromBinding = function (requestScope, request, binding) {
        var result;
        var childRequests = request.childRequests;
        (0, binding_utils_1.ensureFullyBound)(binding);
        switch (binding.type) {
            case literal_types_1.BindingTypeEnum.ConstantValue:
            case literal_types_1.BindingTypeEnum.Function:
                result = binding.cache;
                break;
            case literal_types_1.BindingTypeEnum.Constructor:
                result = binding.implementationType;
                break;
            case literal_types_1.BindingTypeEnum.Instance:
                result = (0, instantiation_1.resolveInstance)(binding, binding.implementationType, childRequests, _resolveRequest(requestScope));
                break;
            default:
                result = _resolveFactoryFromBinding(binding, request.parentContext);
        }
        return result;
    };
    var _resolveInScope = function (requestScope, binding, resolveFromBinding) {
        var result = (0, scope_1.tryGetFromScope)(requestScope, binding);
        if (result !== null) {
            return result;
        }
        result = resolveFromBinding();
        (0, scope_1.saveToScope)(requestScope, binding, result);
        return result;
    };
    var _resolveBinding = function (requestScope, request, binding) {
        return _resolveInScope(requestScope, binding, function () {
            var result = _getResolvedFromBinding(requestScope, request, binding);
            if ((0, async_1.isPromise)(result)) {
                result = result.then(function (resolved) { return _onActivation(request, binding, resolved); });
            }
            else {
                result = _onActivation(request, binding, result);
            }
            return result;
        });
    };
    function _onActivation(request, binding, resolved) {
        var result = _bindingActivation(request.parentContext, binding, resolved);
        var containersIterator = _getContainersIterator(request.parentContext.container);
        var container;
        var containersIteratorResult = containersIterator.next();
        do {
            container = containersIteratorResult.value;
            var context_1 = request.parentContext;
            var serviceIdentifier = request.serviceIdentifier;
            var activationsIterator = _getContainerActivationsForService(container, serviceIdentifier);
            if ((0, async_1.isPromise)(result)) {
                result = _activateContainerAsync(activationsIterator, context_1, result);
            }
            else {
                result = _activateContainer(activationsIterator, context_1, result);
            }
            containersIteratorResult = containersIterator.next();
        } while (containersIteratorResult.done !== true && !(0, planner_1.getBindingDictionary)(container).hasKey(request.serviceIdentifier));
        return result;
    }
    var _bindingActivation = function (context, binding, previousResult) {
        var result;
        if (typeof binding.onActivation === "function") {
            result = binding.onActivation(context, previousResult);
        }
        else {
            result = previousResult;
        }
        return result;
    };
    var _activateContainer = function (activationsIterator, context, result) {
        var activation = activationsIterator.next();
        while (!activation.done) {
            result = activation.value(context, result);
            if ((0, async_1.isPromise)(result)) {
                return _activateContainerAsync(activationsIterator, context, result);
            }
            activation = activationsIterator.next();
        }
        return result;
    };
    var _activateContainerAsync = function (activationsIterator, context, resultPromise) { return __awaiter(void 0, void 0, void 0, function () {
        var result, activation;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4, resultPromise];
                case 1:
                    result = _a.sent();
                    activation = activationsIterator.next();
                    _a.label = 2;
                case 2:
                    if (!!activation.done) return [3, 4];
                    return [4, activation.value(context, result)];
                case 3:
                    result = _a.sent();
                    activation = activationsIterator.next();
                    return [3, 2];
                case 4: return [2, result];
            }
        });
    }); };
    var _getContainerActivationsForService = function (container, serviceIdentifier) {
        var activations = container._activations;
        return activations.hasKey(serviceIdentifier) ? activations.get(serviceIdentifier).values() : [].values();
    };
    var _getContainersIterator = function (container) {
        var containersStack = [container];
        var parent = container.parent;
        while (parent !== null) {
            containersStack.push(parent);
            parent = parent.parent;
        }
        var getNextContainer = function () {
            var nextContainer = containersStack.pop();
            if (nextContainer !== undefined) {
                return { done: false, value: nextContainer };
            }
            else {
                return { done: true, value: undefined };
            }
        };
        var containersIterator = {
            next: getNextContainer,
        };
        return containersIterator;
    };
    function resolve(context) {
        var _f = _resolveRequest(context.plan.rootRequest.requestScope);
        return _f(context.plan.rootRequest);
    }
    exports.resolve = resolve;
});
//# sourceMappingURL=resolver.js.map