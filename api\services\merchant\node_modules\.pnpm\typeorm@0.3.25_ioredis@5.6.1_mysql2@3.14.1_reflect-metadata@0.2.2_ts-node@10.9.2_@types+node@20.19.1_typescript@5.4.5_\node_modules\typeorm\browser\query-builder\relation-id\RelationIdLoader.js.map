{"version": 3, "sources": ["../browser/src/query-builder/relation-id/RelationIdLoader.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,WAAW,EAAE,MAAM,0BAA0B,CAAA;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,0BAA0B,CAAA;AACvD,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAE9C,MAAM,OAAO,gBAAgB;IACzB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,UAAsB,EACtB,WAAoC,EACpC,oBAA2C;QAF3C,eAAU,GAAV,UAAU,CAAY;QACtB,gBAAW,GAAX,WAAW,CAAyB;QACpC,yBAAoB,GAApB,oBAAoB,CAAuB;IACtD,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E,KAAK,CAAC,IAAI,CAAC,WAAkB;QACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAC1C,KAAK,EAAE,cAAc,EAAE,EAAE;YACrB,IACI,cAAc,CAAC,QAAQ,CAAC,WAAW;gBACnC,cAAc,CAAC,QAAQ,CAAC,eAAe,EACzC,CAAC;gBACC,wBAAwB;gBACxB,iDAAiD;gBACjD,iCAAiC;gBAEjC,IAAI,cAAc,CAAC,mBAAmB;oBAClC,MAAM,IAAI,YAAY,CAClB,kFAAkF,CACrF,CAAA;gBAEL,MAAM,UAAU,GAAwC,EAAE,CAAA;gBAC1D,MAAM,OAAO,GAAG,WAAW;qBACtB,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACf,MAAM,MAAM,GAAkB,EAAE,CAAA;oBAChC,MAAM,cAAc,GAAkB,EAAE,CAAA;oBACxC,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CACvC,CAAC,UAAU,EAAE,EAAE;wBACX,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC;4BAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACvC,SAAS,CACL,WAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,cAAc,CAAC,WAAW,EAC1B,UAAU,CAAC,YAAY,CAC1B,CACJ,EACD,UAAU,CAAC,gBAAiB,CAC/B,CAAA;wBACL,MAAM,aAAa,GAAG,GAClB,UAAU,CAAC,YACf,IAAI,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAA;wBACrC,IACI,cAAc,CAAC,OAAO,CAClB,aAAa,CAChB,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;wBACtC,CAAC;oBACL,CAAC,CACJ,CAAA;oBAED,cAAc,CAAC,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CACzD,CAAC,aAAa,EAAE,EAAE;wBACd,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC;4BAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACvC,SAAS,CACL,WAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,cAAc,CAAC,WAAW,EAC1B,aAAa,CAAC,YAAY,CAC7B,CACJ,EACD,aAAa,CAChB,CAAA;wBACL,MAAM,aAAa,GAAG,GAClB,aAAa,CAAC,YAClB,IAAI,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,EAAE,CAAA;wBACxC,IACI,cAAc,CAAC,OAAO,CAClB,aAAa,CAChB,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;wBACtC,CAAC;oBACL,CAAC,CACJ,CAAA;oBAED,cAAc,CAAC,IAAI,EAAE,CAAA;oBACrB,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC3C,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;wBACxB,OAAO,IAAI,CAAA;oBACf,CAAC;oBACD,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;oBAC5B,OAAO,MAAM,CAAA;gBACjB,CAAC,CAAC;qBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;gBAErB,OAAO;oBACH,mBAAmB,EAAE,cAAc;oBACnC,OAAO,EAAE,OAAO;iBACnB,CAAA;YACL,CAAC;iBAAM,IACH,cAAc,CAAC,QAAQ,CAAC,WAAW;gBACnC,cAAc,CAAC,QAAQ,CAAC,kBAAkB,EAC5C,CAAC;gBACC,6BAA6B;gBAC7B,8DAA8D;gBAC9D,6CAA6C;gBAE7C,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAA,CAAC,oBAAoB;gBAC7D,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ;oBACjC,CAAC,CAAC,QAAQ,CAAC,WAAW;oBACtB,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAA;gBAC3C,MAAM,KAAK,GAAG,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAA,CAAC,WAAW;gBAC/D,MAAM,SAAS,GAAG,QAAQ,CAAC,qBAAqB,CAAC,SAAS,CAAA,CAAC,WAAW;gBACtE,MAAM,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,SAAS,CAAA,CAAC,uFAAuF;gBAE5I,MAAM,UAAU,GAAwC,EAAE,CAAA;gBAC1D,MAAM,UAAU,GAAkB,EAAE,CAAA;gBACpC,MAAM,SAAS,GAAG,WAAW;qBACxB,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;oBACtB,MAAM,cAAc,GAAkB,EAAE,CAAA;oBACxC,MAAM,cAAc,GAAkB,EAAE,CAAA;oBACxC,MAAM,SAAS,GAAG,WAAW;yBACxB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;wBAChB,MAAM,aAAa,GACf,UAAU,CAAC,YAAY,GAAG,KAAK,CAAA;wBACnC,MAAM,cAAc,GAChB,SAAS,CACL,WAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,cAAc,CAAC,WAAW,EAC1B,UAAU,CAAC,gBAAiB;6BACvB,YAAY,CACpB,CACJ,CAAA;wBACL,MAAM,aAAa,GAAG,GAAG,UAAU,IAAI,UAAU,CAAC,YAAY,IAAI,cAAc,EAAE,CAAA;wBAClF,IACI,cAAc,CAAC,OAAO,CAClB,aAAa,CAChB,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,OAAO,EAAE,CAAA;wBACb,CAAC;wBACD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;wBAClC,cAAc,CAAC,aAAa,CAAC;4BACzB,cAAc,CAAA;wBAClB,OAAO,CACH,UAAU;4BACV,GAAG;4BACH,UAAU,CAAC,YAAY;4BACvB,MAAM;4BACN,aAAa,CAChB,CAAA;oBACL,CAAC,CAAC;yBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;yBAChB,IAAI,CAAC,OAAO,CAAC,CAAA;oBAClB,cAAc,CAAC,IAAI,EAAE,CAAA;oBACrB,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC3C,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;wBACxB,OAAO,EAAE,CAAA;oBACb,CAAC;oBACD,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;oBAC5B,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;oBACzC,OAAO,SAAS,CAAA;gBACpB,CAAC,CAAC;qBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;qBAChB,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC;qBACzC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAEjB,6FAA6F;gBAC7F,oGAAoG;gBACpG,IAAI,CAAC,SAAS;oBACV,OAAO;wBACH,mBAAmB,EAAE,cAAc;wBACnC,OAAO,EAAE,EAAE;qBACd,CAAA;gBAEL,kBAAkB;gBAClB,0FAA0F;gBAC1F,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACzC,IAAI,CAAC,WAAW,CACnB,CAAA;gBAED,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CACzB;oBACI,GAAG,WAAW;oBACd,GAAG,QAAQ,CAAC,eAAgB,CAAC,cAAc;yBACtC,cAAc;iBACtB,EACD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAClC,CAAA;gBAED,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBAC3B,EAAE,CAAC,SAAS,CACR,UAAU,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,EAC1C,UAAU,CAAC,YAAY,CAC1B,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC;qBACrB,KAAK,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG,CAAC,CAAC,uIAAuI;qBACpK,aAAa,CAAC,UAAU,CAAC,CAAA;gBAE9B,iDAAiD;gBACjD,IAAI,cAAc,CAAC,mBAAmB;oBAClC,cAAc,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAA;gBAE1C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,EAAE,CAAA;gBACrC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;wBAC3B,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;4BACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACvC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAC3B,MAAM,CAAC,gBAAiB,CAC3B,CAAA;oBACT,CAAC,CAAC,CAAA;oBACF,QAAQ,CAAC,eAAgB,CAAC,cAAc,CAAC,cAAc,CAAC,OAAO,CAC3D,CAAC,MAAM,EAAE,EAAE;wBACP,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;4BACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACvC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAC3B,MAAM,CACT,CAAA;oBACT,CAAC,CACJ,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,OAAO;oBACH,mBAAmB,EAAE,cAAc;oBACnC,OAAO;iBACV,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,eAAe;gBACf,6BAA6B;gBAC7B,0EAA0E;gBAC1E,2EAA2E;gBAC3E,yCAAyC;gBAEzC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAA;gBACxC,MAAM,WAAW,GAAG,QAAQ,CAAC,QAAQ;oBACjC,CAAC,CAAC,QAAQ,CAAC,WAAW;oBACtB,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,kBAAkB,CAAA;gBAClD,MAAM,kBAAkB,GAAG,QAAQ,CAAC,QAAQ;oBACxC,CAAC,CAAC,QAAQ,CAAC,kBAAkB;oBAC7B,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAA;gBAC3C,MAAM,aAAa,GAAG,cAAc,CAAC,aAAa,CAAA;gBAClD,MAAM,oBAAoB,GACtB,cAAc,CAAC,uBAAuB,CAAC,SAAS,CAAA;gBACpD,MAAM,qBAAqB,GACvB,cAAc,CAAC,KAAK,IAAI,oBAAoB,CAAA;gBAChD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,QAAQ;oBACvC,CAAC,CAAC,QAAQ,CAAC,sBAAuB,CAAC,SAAS;oBAC5C,CAAC,CAAC,QAAQ,CAAC,eAAgB,CAAC,sBAAuB;yBAC5C,SAAS,CAAA;gBAEpB,MAAM,aAAa,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBAChD,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,EAAE;wBAC1C,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC;4BACxB,SAAS,CACL,WAAW,CAAC,UAAU,CAClB,IAAI,CAAC,UAAU,CAAC,MAAM,EACtB,SAAS,EACT,cAAc,CAAC,WAAW,EAC1B,UAAU,CAAC,gBAAiB;iCACvB,YAAY,CACpB,CACJ,CAAA;wBACL,OAAO,GAAG,CAAA;oBACd,CAAC,EAAE,EAAmB,CAAC,CAAA;gBAC3B,CAAC,CAAC,CAAA;gBAEF,6FAA6F;gBAC7F,oGAAoG;gBACpG,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;oBAC1B,OAAO;wBACH,mBAAmB,EAAE,cAAc;wBACnC,OAAO,EAAE,EAAE;qBACd,CAAA;gBAEL,MAAM,UAAU,GAAkB,EAAE,CAAA;gBACpC,MAAM,UAAU,GAAwC,EAAE,CAAA;gBAC1D,MAAM,oBAAoB,GAAG,aAAa;qBACrC,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;oBACzB,MAAM,cAAc,GAAkB,EAAE,CAAA;oBACxC,MAAM,cAAc,GAAkB,EAAE,CAAA;oBACxC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC;yBACtC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;wBACT,MAAM,aAAa,GAAG,GAAG,GAAG,KAAK,CAAA;wBACjC,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,CAAA;wBACxC,MAAM,aAAa,GAAG,GAAG,aAAa,IAAI,GAAG,IAAI,cAAc,EAAE,CAAA;wBACjE,IACI,cAAc,CAAC,OAAO,CAClB,aAAa,CAChB,KAAK,CAAC,CAAC,EACV,CAAC;4BACC,OAAO,EAAE,CAAA;wBACb,CAAC;wBACD,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;wBAClC,cAAc,CAAC,aAAa,CAAC;4BACzB,cAAc,CAAA;wBAClB,OAAO,CACH,aAAa;4BACb,GAAG;4BACH,GAAG;4BACH,MAAM;4BACN,aAAa,CAChB,CAAA;oBACL,CAAC,CAAC;yBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;yBAChB,IAAI,CAAC,OAAO,CAAC,CAAA;oBAClB,cAAc,CAAC,IAAI,EAAE,CAAA;oBACrB,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAC3C,IAAI,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;wBACxB,OAAO,EAAE,CAAA;oBACb,CAAC;oBACD,UAAU,CAAC,SAAS,CAAC,GAAG,IAAI,CAAA;oBAC5B,MAAM,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;oBACzC,OAAO,SAAS,CAAA;gBACpB,CAAC,CAAC;qBACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAA;gBAErB,MAAM,0BAA0B,GAAG,kBAAkB;qBAChD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;oBAChB,OAAO,CACH,aAAa;wBACb,GAAG;wBACH,UAAU,CAAC,YAAY;wBACvB,KAAK;wBACL,qBAAqB;wBACrB,GAAG;wBACH,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAC5C,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,OAAO,CAAC,CAAA;gBAElB,MAAM,SAAS,GAAG,oBAAoB;qBACjC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBACf,OAAO,CACH,GAAG;wBACH,SAAS;wBACT,OAAO;wBACP,0BAA0B;wBAC1B,GAAG,CACN,CAAA;gBACL,CAAC,CAAC;qBACD,IAAI,CAAC,MAAM,CAAC,CAAA;gBAEjB,MAAM,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CACzC,IAAI,CAAC,WAAW,CACnB,CAAA;gBAED,kBAAkB,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACtC,EAAE,CAAC,SAAS,CACR,aAAa,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,EAC7C,UAAU,CAAC,YAAY,CAC1B,CAAC,UAAU,CACR,aAAa,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,CAChD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBAC/B,EAAE,CAAC,SAAS,CACR,aAAa,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,EAC7C,UAAU,CAAC,YAAY,CAC1B,CAAC,UAAU,CACR,aAAa,GAAG,GAAG,GAAG,UAAU,CAAC,YAAY,CAChD,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,EAAE,CAAC,IAAI,CAAC,oBAAoB,EAAE,qBAAqB,CAAC;qBAC/C,SAAS,CAAC,iBAAiB,EAAE,aAAa,EAAE,SAAS,CAAC;qBACtD,aAAa,CAAC,UAAU,CAAC,CAAA;gBAE9B,iDAAiD;gBACjD,IAAI,cAAc,CAAC,mBAAmB;oBAClC,cAAc,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAA;gBAE1C,MAAM,OAAO,GAAG,MAAM,EAAE,CAAC,UAAU,EAAE,CAAA;gBACrC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACvB,CAAC;oBAAA,CAAC,GAAG,WAAW,EAAE,GAAG,kBAAkB,CAAC,CAAC,OAAO,CAC5C,CAAC,MAAM,EAAE,EAAE;wBACP,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;4BACvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CACvC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAC3B,MAAM,CAAC,gBAAiB,CAC3B,CAAA;oBACT,CAAC,CACJ,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEF,OAAO;oBACH,mBAAmB,EAAE,cAAc;oBACnC,OAAO;iBACV,CAAA;YACL,CAAC;QACL,CAAC,CACJ,CAAA;QAED,OAAO,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAChC,CAAC;CACJ", "file": "RelationIdLoader.js", "sourcesContent": ["import { RelationIdAttribute } from \"./RelationIdAttribute\"\nimport { DataSource } from \"../../data-source/DataSource\"\nimport { RelationIdLoadResult } from \"./RelationIdLoadResult\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DriverUtils } from \"../../driver/DriverUtils\"\nimport { TypeORMError } from \"../../error/TypeORMError\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\n\nexport class RelationIdLoader {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected connection: DataSource,\n        protected queryRunner: QueryRunner | undefined,\n        protected relationIdAttributes: RelationIdAttribute[],\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    async load(rawEntities: any[]): Promise<RelationIdLoadResult[]> {\n        const promises = this.relationIdAttributes.map(\n            async (relationIdAttr) => {\n                if (\n                    relationIdAttr.relation.isManyToOne ||\n                    relationIdAttr.relation.isOneToOneOwner\n                ) {\n                    // example: Post and Tag\n                    // loadRelationIdAndMap(\"post.tagId\", \"post.tag\")\n                    // we expect it to load id of tag\n\n                    if (relationIdAttr.queryBuilderFactory)\n                        throw new TypeORMError(\n                            \"Additional condition can not be used with ManyToOne or OneToOne owner relations.\",\n                        )\n\n                    const duplicates: { [duplicateKey: string]: boolean } = {}\n                    const results = rawEntities\n                        .map((rawEntity) => {\n                            const result: ObjectLiteral = {}\n                            const duplicateParts: Array<string> = []\n                            relationIdAttr.relation.joinColumns.forEach(\n                                (joinColumn) => {\n                                    result[joinColumn.databaseName] =\n                                        this.connection.driver.prepareHydratedValue(\n                                            rawEntity[\n                                                DriverUtils.buildAlias(\n                                                    this.connection.driver,\n                                                    undefined,\n                                                    relationIdAttr.parentAlias,\n                                                    joinColumn.databaseName,\n                                                )\n                                            ],\n                                            joinColumn.referencedColumn!,\n                                        )\n                                    const duplicatePart = `${\n                                        joinColumn.databaseName\n                                    }:${result[joinColumn.databaseName]}`\n                                    if (\n                                        duplicateParts.indexOf(\n                                            duplicatePart,\n                                        ) === -1\n                                    ) {\n                                        duplicateParts.push(duplicatePart)\n                                    }\n                                },\n                            )\n\n                            relationIdAttr.relation.entityMetadata.primaryColumns.forEach(\n                                (primaryColumn) => {\n                                    result[primaryColumn.databaseName] =\n                                        this.connection.driver.prepareHydratedValue(\n                                            rawEntity[\n                                                DriverUtils.buildAlias(\n                                                    this.connection.driver,\n                                                    undefined,\n                                                    relationIdAttr.parentAlias,\n                                                    primaryColumn.databaseName,\n                                                )\n                                            ],\n                                            primaryColumn,\n                                        )\n                                    const duplicatePart = `${\n                                        primaryColumn.databaseName\n                                    }:${result[primaryColumn.databaseName]}`\n                                    if (\n                                        duplicateParts.indexOf(\n                                            duplicatePart,\n                                        ) === -1\n                                    ) {\n                                        duplicateParts.push(duplicatePart)\n                                    }\n                                },\n                            )\n\n                            duplicateParts.sort()\n                            const duplicate = duplicateParts.join(\"::\")\n                            if (duplicates[duplicate]) {\n                                return null\n                            }\n                            duplicates[duplicate] = true\n                            return result\n                        })\n                        .filter((v) => v)\n\n                    return {\n                        relationIdAttribute: relationIdAttr,\n                        results: results,\n                    }\n                } else if (\n                    relationIdAttr.relation.isOneToMany ||\n                    relationIdAttr.relation.isOneToOneNotOwner\n                ) {\n                    // example: Post and Category\n                    // loadRelationIdAndMap(\"post.categoryIds\", \"post.categories\")\n                    // we expect it to load array of category ids\n\n                    const relation = relationIdAttr.relation // \"post.categories\"\n                    const joinColumns = relation.isOwning\n                        ? relation.joinColumns\n                        : relation.inverseRelation!.joinColumns\n                    const table = relation.inverseEntityMetadata.target // category\n                    const tableName = relation.inverseEntityMetadata.tableName // category\n                    const tableAlias = relationIdAttr.alias || tableName // if condition (custom query builder factory) is set then relationIdAttr.alias defined\n\n                    const duplicates: { [duplicateKey: string]: boolean } = {}\n                    const parameters: ObjectLiteral = {}\n                    const condition = rawEntities\n                        .map((rawEntity, index) => {\n                            const duplicateParts: Array<string> = []\n                            const parameterParts: ObjectLiteral = {}\n                            const queryPart = joinColumns\n                                .map((joinColumn) => {\n                                    const parameterName =\n                                        joinColumn.databaseName + index\n                                    const parameterValue =\n                                        rawEntity[\n                                            DriverUtils.buildAlias(\n                                                this.connection.driver,\n                                                undefined,\n                                                relationIdAttr.parentAlias,\n                                                joinColumn.referencedColumn!\n                                                    .databaseName,\n                                            )\n                                        ]\n                                    const duplicatePart = `${tableAlias}:${joinColumn.propertyPath}:${parameterValue}`\n                                    if (\n                                        duplicateParts.indexOf(\n                                            duplicatePart,\n                                        ) !== -1\n                                    ) {\n                                        return \"\"\n                                    }\n                                    duplicateParts.push(duplicatePart)\n                                    parameterParts[parameterName] =\n                                        parameterValue\n                                    return (\n                                        tableAlias +\n                                        \".\" +\n                                        joinColumn.propertyPath +\n                                        \" = :\" +\n                                        parameterName\n                                    )\n                                })\n                                .filter((v) => v)\n                                .join(\" AND \")\n                            duplicateParts.sort()\n                            const duplicate = duplicateParts.join(\"::\")\n                            if (duplicates[duplicate]) {\n                                return \"\"\n                            }\n                            duplicates[duplicate] = true\n                            Object.assign(parameters, parameterParts)\n                            return queryPart\n                        })\n                        .filter((v) => v)\n                        .map((condition) => \"(\" + condition + \")\")\n                        .join(\" OR \")\n\n                    // ensure we won't perform redundant queries for joined data which was not found in selection\n                    // example: if post.category was not found in db then no need to execute query for category.imageIds\n                    if (!condition)\n                        return {\n                            relationIdAttribute: relationIdAttr,\n                            results: [],\n                        }\n\n                    // generate query:\n                    // SELECT category.id, category.postId FROM category category ON category.postId = :postId\n                    const qb = this.connection.createQueryBuilder(\n                        this.queryRunner,\n                    )\n\n                    const columns = OrmUtils.uniq(\n                        [\n                            ...joinColumns,\n                            ...relation.inverseRelation!.entityMetadata\n                                .primaryColumns,\n                        ],\n                        (column) => column.propertyPath,\n                    )\n\n                    columns.forEach((joinColumn) => {\n                        qb.addSelect(\n                            tableAlias + \".\" + joinColumn.propertyPath,\n                            joinColumn.databaseName,\n                        )\n                    })\n\n                    qb.from(table, tableAlias)\n                        .where(\"(\" + condition + \")\") // need brackets because if we have additional condition and no brackets, it looks like (a = 1) OR (a = 2) AND b = 1, that is incorrect\n                        .setParameters(parameters)\n\n                    // apply condition (custom query builder factory)\n                    if (relationIdAttr.queryBuilderFactory)\n                        relationIdAttr.queryBuilderFactory(qb)\n\n                    const results = await qb.getRawMany()\n                    results.forEach((result) => {\n                        joinColumns.forEach((column) => {\n                            result[column.databaseName] =\n                                this.connection.driver.prepareHydratedValue(\n                                    result[column.databaseName],\n                                    column.referencedColumn!,\n                                )\n                        })\n                        relation.inverseRelation!.entityMetadata.primaryColumns.forEach(\n                            (column) => {\n                                result[column.databaseName] =\n                                    this.connection.driver.prepareHydratedValue(\n                                        result[column.databaseName],\n                                        column,\n                                    )\n                            },\n                        )\n                    })\n\n                    return {\n                        relationIdAttribute: relationIdAttr,\n                        results,\n                    }\n                } else {\n                    // many-to-many\n                    // example: Post and Category\n                    // owner side: loadRelationIdAndMap(\"post.categoryIds\", \"post.categories\")\n                    // inverse side: loadRelationIdAndMap(\"category.postIds\", \"category.posts\")\n                    // we expect it to load array of post ids\n\n                    const relation = relationIdAttr.relation\n                    const joinColumns = relation.isOwning\n                        ? relation.joinColumns\n                        : relation.inverseRelation!.inverseJoinColumns\n                    const inverseJoinColumns = relation.isOwning\n                        ? relation.inverseJoinColumns\n                        : relation.inverseRelation!.joinColumns\n                    const junctionAlias = relationIdAttr.junctionAlias\n                    const inverseSideTableName =\n                        relationIdAttr.joinInverseSideMetadata.tableName\n                    const inverseSideTableAlias =\n                        relationIdAttr.alias || inverseSideTableName\n                    const junctionTableName = relation.isOwning\n                        ? relation.junctionEntityMetadata!.tableName\n                        : relation.inverseRelation!.junctionEntityMetadata!\n                              .tableName\n\n                    const mappedColumns = rawEntities.map((rawEntity) => {\n                        return joinColumns.reduce((map, joinColumn) => {\n                            map[joinColumn.propertyPath] =\n                                rawEntity[\n                                    DriverUtils.buildAlias(\n                                        this.connection.driver,\n                                        undefined,\n                                        relationIdAttr.parentAlias,\n                                        joinColumn.referencedColumn!\n                                            .databaseName,\n                                    )\n                                ]\n                            return map\n                        }, {} as ObjectLiteral)\n                    })\n\n                    // ensure we won't perform redundant queries for joined data which was not found in selection\n                    // example: if post.category was not found in db then no need to execute query for category.imageIds\n                    if (mappedColumns.length === 0)\n                        return {\n                            relationIdAttribute: relationIdAttr,\n                            results: [],\n                        }\n\n                    const parameters: ObjectLiteral = {}\n                    const duplicates: { [duplicateKey: string]: boolean } = {}\n                    const joinColumnConditions = mappedColumns\n                        .map((mappedColumn, index) => {\n                            const duplicateParts: Array<string> = []\n                            const parameterParts: ObjectLiteral = {}\n                            const queryPart = Object.keys(mappedColumn)\n                                .map((key) => {\n                                    const parameterName = key + index\n                                    const parameterValue = mappedColumn[key]\n                                    const duplicatePart = `${junctionAlias}:${key}:${parameterValue}`\n                                    if (\n                                        duplicateParts.indexOf(\n                                            duplicatePart,\n                                        ) !== -1\n                                    ) {\n                                        return \"\"\n                                    }\n                                    duplicateParts.push(duplicatePart)\n                                    parameterParts[parameterName] =\n                                        parameterValue\n                                    return (\n                                        junctionAlias +\n                                        \".\" +\n                                        key +\n                                        \" = :\" +\n                                        parameterName\n                                    )\n                                })\n                                .filter((s) => s)\n                                .join(\" AND \")\n                            duplicateParts.sort()\n                            const duplicate = duplicateParts.join(\"::\")\n                            if (duplicates[duplicate]) {\n                                return \"\"\n                            }\n                            duplicates[duplicate] = true\n                            Object.assign(parameters, parameterParts)\n                            return queryPart\n                        })\n                        .filter((s) => s)\n\n                    const inverseJoinColumnCondition = inverseJoinColumns\n                        .map((joinColumn) => {\n                            return (\n                                junctionAlias +\n                                \".\" +\n                                joinColumn.propertyPath +\n                                \" = \" +\n                                inverseSideTableAlias +\n                                \".\" +\n                                joinColumn.referencedColumn!.propertyPath\n                            )\n                        })\n                        .join(\" AND \")\n\n                    const condition = joinColumnConditions\n                        .map((condition) => {\n                            return (\n                                \"(\" +\n                                condition +\n                                \" AND \" +\n                                inverseJoinColumnCondition +\n                                \")\"\n                            )\n                        })\n                        .join(\" OR \")\n\n                    const qb = this.connection.createQueryBuilder(\n                        this.queryRunner,\n                    )\n\n                    inverseJoinColumns.forEach((joinColumn) => {\n                        qb.addSelect(\n                            junctionAlias + \".\" + joinColumn.propertyPath,\n                            joinColumn.databaseName,\n                        ).addOrderBy(\n                            junctionAlias + \".\" + joinColumn.propertyPath,\n                        )\n                    })\n\n                    joinColumns.forEach((joinColumn) => {\n                        qb.addSelect(\n                            junctionAlias + \".\" + joinColumn.propertyPath,\n                            joinColumn.databaseName,\n                        ).addOrderBy(\n                            junctionAlias + \".\" + joinColumn.propertyPath,\n                        )\n                    })\n\n                    qb.from(inverseSideTableName, inverseSideTableAlias)\n                        .innerJoin(junctionTableName, junctionAlias, condition)\n                        .setParameters(parameters)\n\n                    // apply condition (custom query builder factory)\n                    if (relationIdAttr.queryBuilderFactory)\n                        relationIdAttr.queryBuilderFactory(qb)\n\n                    const results = await qb.getRawMany()\n                    results.forEach((result) => {\n                        ;[...joinColumns, ...inverseJoinColumns].forEach(\n                            (column) => {\n                                result[column.databaseName] =\n                                    this.connection.driver.prepareHydratedValue(\n                                        result[column.databaseName],\n                                        column.referencedColumn!,\n                                    )\n                            },\n                        )\n                    })\n\n                    return {\n                        relationIdAttribute: relationIdAttr,\n                        results,\n                    }\n                }\n            },\n        )\n\n        return Promise.all(promises)\n    }\n}\n"], "sourceRoot": "../.."}