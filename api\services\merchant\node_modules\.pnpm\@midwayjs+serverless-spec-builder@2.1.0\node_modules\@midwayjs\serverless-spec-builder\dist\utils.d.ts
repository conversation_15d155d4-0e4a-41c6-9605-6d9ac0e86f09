export interface Ilayer {
    [extName: string]: {
        path: string;
    };
}
export declare function formatLayers(...multiLayers: Ilayer[]): any;
export declare function getLayers(...layersList: any): {
    layerDeps: any[];
    layers: any[];
};
export declare function uppercaseObjectKey(obj: any): any;
export declare function lowercaseObjectKey(obj: any): any;
export declare function removeObjectEmptyAttributes(obj: any): {};
export declare function filterUserDefinedEnv(): {};
export declare const getFaaSPackageVersion: (distDir: any, baseDir: any) => number;
//# sourceMappingURL=utils.d.ts.map