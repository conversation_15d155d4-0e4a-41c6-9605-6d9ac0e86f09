{"version": 3, "sources": ["../browser/src/error/LimitOnUpdateNotSupportedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AAEH,MAAM,OAAO,8BAA+B,SAAQ,YAAY;IAC5D;QACI,KAAK,CAAC,4DAA4D,CAAC,CAAA;IACvE,CAAC;CACJ", "file": "LimitOnUpdateNotSupportedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to build an UPDATE query with LIMIT but the database does not support it.\n */\n\nexport class LimitOnUpdateNotSupportedError extends TypeORMError {\n    constructor() {\n        super(`Your database does not support LIMIT on UPDATE statements.`)\n    }\n}\n"], "sourceRoot": ".."}