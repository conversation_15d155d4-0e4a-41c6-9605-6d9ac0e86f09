{"version": 3, "sources": ["lib/has-to-string-tag-x.js"], "names": ["f", "exports", "module", "define", "amd", "window", "global", "self", "this", "returnExports", "e", "t", "n", "r", "s", "o", "u", "a", "require", "i", "Error", "code", "l", "call", "length", "1", "_dereq_", "Symbol", "toStringTag", "has-symbol-support-x", "2"], "mappings": "CAAA,SAAUA,GAAG,GAAoB,iBAAVC,SAAoC,oBAATC,OAAsBA,OAAOD,QAAQD,SAAS,GAAmB,mBAATG,QAAqBA,OAAOC,IAAKD,UAAUH,OAAO,EAA0B,oBAATK,OAAwBA,OAA+B,oBAATC,OAAwBA,OAA6B,oBAAPC,KAAsBA,KAAYC,MAAOC,cAAgBT,KAAlU,CAAyU,WAAqC,OAAO,SAAUU,EAAEC,EAAEC,EAAEC,GAAG,SAASC,EAAEC,EAAEC,GAAG,IAAIJ,EAAEG,GAAG,CAAC,IAAIJ,EAAEI,GAAG,CAAC,IAAIE,EAAkB,mBAATC,SAAqBA,QAAQ,IAAIF,GAAGC,EAAE,OAAOA,EAAEF,GAAE,GAAI,GAAGI,EAAE,OAAOA,EAAEJ,GAAE,GAAI,IAAIf,EAAE,IAAIoB,MAAM,uBAAuBL,EAAE,KAAK,MAAMf,EAAEqB,KAAK,mBAAmBrB,EAAE,IAAIsB,EAAEV,EAAEG,IAAId,YAAYU,EAAEI,GAAG,GAAGQ,KAAKD,EAAErB,QAAQ,SAASS,GAAG,IAAIE,EAAED,EAAEI,GAAG,GAAGL,GAAG,OAAOI,EAAEF,GAAIF,IAAIY,EAAEA,EAAErB,QAAQS,EAAEC,EAAEC,EAAEC,GAAG,OAAOD,EAAEG,GAAGd,QAAkD,IAAI,IAA1CkB,EAAkB,mBAATD,SAAqBA,QAAgBH,EAAE,EAAEA,EAAEF,EAAEW,OAAOT,IAAID,EAAED,EAAEE,IAAI,OAAOD,EAAvb,EAA4bW,GAAG,SAASC,QAAQxB,OAAOD;;;;;;;;;AAW50B,aAQAC,OAAOD,QAAUyB,QAAQ,yBAAyD,iBAAvBC,OAAOC,cAE/DC,uBAAuB,IAAIC,GAAG,SAASJ,QAAQxB,OAAOD;;;;;;;;AAUzD,aAQAC,OAAOD,QAA4B,mBAAX0B,QAA+C,iBAAfA,OAAO,cAEpD,IAAI"}