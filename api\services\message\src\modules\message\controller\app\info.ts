import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { MsgInfoEntity } from '../../entity/info';
import { MsgInfoService } from '../../service/info';
import { Get, Inject } from '@midwayjs/core';

/**
 * 消息
 */
@CoolController({
  api: ['page'],
  entity: MsgInfoEntity,
  service: MsgInfoService,
  pageQueryOp: {
    where: ctx => {
      const userId = ctx.user?.id;
      return [['a.userId =:userId', { userId }, userId]];
    },
  },
})
export class AppMsgInfoController extends BaseController {
  @Inject()
  msgInfoService: MsgInfoService;

  @Inject()
  ctx;

  /**
   * 未读消息数量
   */
  @Get('/unreadCount', { summary: '未读消息数量' })
  async unreadCount() {
    return this.ok(this.msgInfoService.unreadCount(this.ctx.user?.id));
  }
}
