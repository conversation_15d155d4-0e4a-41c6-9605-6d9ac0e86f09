"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HSF = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
/**
 * @Deprecated
 * @param hsfOption
 * @constructor
 */
function HSF(hsfOption = {}) {
    return (target) => {
        (0, __1.saveModule)(__1.HSF_KEY, target);
        (0, __1.saveClassMetadata)(__1.HSF_KEY, hsfOption, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.HSF = HSF;
//# sourceMappingURL=hsf.js.map