{"@midwayjs/egg-layer": "3.3.4", "@midwayjs/express-layer": "3.3.4", "@midwayjs/faas-typings": "3.1.6", "@midwayjs/koa-layer": "3.3.4", "@midwayjs/runtime-engine": "3.0.4", "@midwayjs/runtime-mock": "3.0.4", "@midwayjs/serverless-app": "3.3.4", "@midwayjs/serverless-aws-starter": "3.1.6", "@midwayjs/serverless-fc-starter": "3.3.4", "@midwayjs/serverless-fc-trigger": "3.3.4", "@midwayjs/serverless-http-parser": "3.1.6", "@midwayjs/serverless-scf-starter": "3.1.6", "@midwayjs/serverless-scf-trigger": "3.3.4", "@midwayjs/serverless-vercel-starter": "3.3.4", "@midwayjs/serverless-vercel-trigger": "3.0.4", "@midwayjs/serverless-worker-starter": "3.3.4", "@midwayjs/static-layer": "3.3.4", "@midwayjs/axios": "3.3.4", "@midwayjs/bootstrap": "3.3.4", "@midwayjs/cache": "3.3.4", "@midwayjs/consul": "3.3.4", "@midwayjs/core": "3.3.4", "@midwayjs/cos": "3.3.4", "@midwayjs/cross-domain": "3.3.4", "@midwayjs/decorator": "3.3.4", "@midwayjs/express-session": "3.3.4", "@midwayjs/faas": "3.3.4", "@midwayjs/grpc": "3.3.4", "@midwayjs/http-proxy": "3.3.4", "@midwayjs/i18n": "3.3.4", "@midwayjs/info": "3.3.4", "@midwayjs/jwt": "3.3.4", "@midwayjs/mock": "3.3.4", "@midwayjs/mongoose": "3.3.4", "@midwayjs/orm": "3.3.4", "@midwayjs/oss": "3.3.4", "@midwayjs/otel": "3.3.4", "@midwayjs/passport": "3.3.4", "@midwayjs/process-agent": "3.3.4", "@midwayjs/prometheus-socket-io": "3.3.4", "@midwayjs/prometheus": "3.3.4", "@midwayjs/rabbitmq": "3.3.4", "@midwayjs/redis": "3.3.4", "@midwayjs/security": "3.3.4", "@midwayjs/sequelize": "3.3.4", "@midwayjs/session": "3.3.4", "@midwayjs/socketio": "3.3.4", "@midwayjs/static-file": "3.3.4", "@midwayjs/swagger": "3.3.4", "@midwayjs/tablestore": "3.3.4", "@midwayjs/task": "3.3.4", "@midwayjs/typegoose": "3.3.4", "@midwayjs/upload": "3.3.4", "@midwayjs/validate": "3.3.4", "@midwayjs/version": "3.3.4", "@midwayjs/view-ejs": "3.3.4", "@midwayjs/view-nunjucks": "3.3.4", "@midwayjs/view": "3.3.4", "@midwayjs/express": "3.3.4", "@midwayjs/koa": "3.3.4", "@midwayjs/web": "3.3.4", "@midwayjs/ws": "3.3.4"}