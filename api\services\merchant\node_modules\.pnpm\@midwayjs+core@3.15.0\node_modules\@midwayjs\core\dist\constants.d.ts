/**
 * 静态参数
 *
 */
export declare const KEYS: {
    OBJECTS_ELEMENT: string;
    OBJECT_ELEMENT: string;
    IMPORT_ELEMENT: string;
    PROPERTY_ELEMENT: string;
    LIST_ELEMENT: string;
    MAP_ELEMENT: string;
    ENTRY_ELEMENT: string;
    VALUE_ELEMENT: string;
    PROPS_ELEMENT: string;
    PROP_ELEMENT: string;
    SET_ELEMENT: string;
    CONSTRUCTOR_ARG_ELEMENT: string;
    REF_ELEMENT: string;
    JSON_ELEMENT: string;
    CONFIGURATION_ELEMENT: string;
    ID_ATTRIBUTE: string;
    PATH_ATTRIBUTE: string;
    DIRECT_ATTRIBUTE: string;
    AUTOWIRE_ATTRIBUTE: string;
    ASYNC_ATTRIBUTE: string;
    NAME_ATTRIBUTE: string;
    REF_ATTRIBUTE: string;
    KEY_ATTRIBUTE: string;
    VALUE_ATTRIBUTE: string;
    TYPE_ATTRIBUTE: string;
    EXTERNAL_ATTRIBUTE: string;
    OBJECT_ATTRIBUTE: string;
    RESOURCE_ATTRIBUTE: string;
    SCOPE_ATTRIBUTE: string;
    ASPECT_ELEMENT: string;
    AROUND_ELEMENT: string;
    EXPRESSION_ATTRIBUTE: string;
    EXECUTE_ATTRIBUTE: string;
};
export declare const FUNCTION_INJECT_KEY = "midway:function_inject_key";
export declare const MIDWAY_LOGGER_WRITEABLE_DIR = "MIDWAY_LOGGER_WRITEABLE_DIR";
export declare const REQUEST_CTX_KEY = "ctx";
export declare const REQUEST_OBJ_CTX_KEY = "_req_ctx";
export declare const CONTAINER_OBJ_SCOPE = "_obj_scope";
export declare const HTTP_SERVER_KEY = "_midway_http_server";
export declare const REQUEST_CTX_LOGGER_CACHE_KEY = "_midway_ctx_logger_cache";
export declare const ASYNC_CONTEXT_KEY: unique symbol;
export declare const ASYNC_CONTEXT_MANAGER_KEY = "MIDWAY_ASYNC_CONTEXT_MANAGER_KEY";
export declare const DEFAULT_PATTERN: string[];
export declare const IGNORE_PATTERN: string[];
export declare const SINGLETON_CONTAINER_CTX: {
    _MAIN_CTX_: boolean;
};
//# sourceMappingURL=constants.d.ts.map