#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules/uuid/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/uuid@9.0.1/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/bin/uuid" "$@"
else
  exec node  "$basedir/../../dist/bin/uuid" "$@"
fi
