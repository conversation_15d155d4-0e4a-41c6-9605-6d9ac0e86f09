{"$id": "shared.json", "$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"columns": {"oneOf": [{"type": "object", "patternProperties": {"^[0-9]+$": {"$ref": "#/definitions/column"}}, "additionalProperties": false}, {"type": "array", "items": {"$ref": "#/definitions/column"}}]}, "column": {"type": "object", "properties": {"alignment": {"$ref": "#/definitions/alignment"}, "verticalAlignment": {"$ref": "#/definitions/verticalAlignment"}, "width": {"type": "integer", "minimum": 1}, "wrapWord": {"type": "boolean"}, "truncate": {"type": "integer"}, "paddingLeft": {"type": "integer"}, "paddingRight": {"type": "integer"}}, "additionalProperties": false}, "borders": {"type": "object", "properties": {"topBody": {"$ref": "#/definitions/border"}, "topJoin": {"$ref": "#/definitions/border"}, "topLeft": {"$ref": "#/definitions/border"}, "topRight": {"$ref": "#/definitions/border"}, "bottomBody": {"$ref": "#/definitions/border"}, "bottomJoin": {"$ref": "#/definitions/border"}, "bottomLeft": {"$ref": "#/definitions/border"}, "bottomRight": {"$ref": "#/definitions/border"}, "bodyLeft": {"$ref": "#/definitions/border"}, "bodyRight": {"$ref": "#/definitions/border"}, "bodyJoin": {"$ref": "#/definitions/border"}, "headerJoin": {"$ref": "#/definitions/border"}, "joinBody": {"$ref": "#/definitions/border"}, "joinLeft": {"$ref": "#/definitions/border"}, "joinRight": {"$ref": "#/definitions/border"}, "joinJoin": {"$ref": "#/definitions/border"}, "joinMiddleUp": {"$ref": "#/definitions/border"}, "joinMiddleDown": {"$ref": "#/definitions/border"}, "joinMiddleLeft": {"$ref": "#/definitions/border"}, "joinMiddleRight": {"$ref": "#/definitions/border"}}, "additionalProperties": false}, "border": {"type": "string"}, "alignment": {"type": "string", "enum": ["left", "right", "center", "justify"]}, "verticalAlignment": {"type": "string", "enum": ["top", "middle", "bottom"]}}}