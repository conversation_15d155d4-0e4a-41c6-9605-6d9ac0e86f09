import { BasePlugin } from '@midwayjs/command-core';
export declare class DevPlugin extends BasePlugin {
    private child;
    private started;
    private restarting;
    private port;
    private processMessageMap;
    private spin;
    private tsconfigJson;
    private childProcesslistenedPort;
    private isInClosing;
    private startTime;
    commands: {
        dev: {
            lifecycleEvents: string[];
            options: {
                baseDir: {
                    usage: string;
                };
                port: {
                    usage: string;
                    shortcut: string;
                };
                debug: {
                    usage: string;
                };
                framework: {
                    usage: string;
                };
                entryFile: {
                    usage: string;
                    shortcut: string;
                };
                notWatch: {
                    usage: string;
                };
                fast: {
                    usage: string;
                };
                sourceDir: {
                    usage: string;
                };
                layers: {
                    usage: string;
                };
                watchFile: {
                    usage: string;
                };
                watchFilePatten: {
                    usage: string;
                };
                unWatchFilePatten: {
                    usage: string;
                };
                watchExt: {
                    usage: string;
                };
                detectPort: {
                    usage: string;
                };
            };
        };
    };
    hooks: {
        'dev:checkEnv': any;
        'dev:run': any;
    };
    checkEnv(): Promise<void>;
    run(): Promise<unknown>;
    protected getOptions(): any;
    private start;
    private getTsNodeRegister;
    private handleClose;
    private restart;
    private getIp;
    private clearTernimal;
    private getSourceDir;
    private startWatch;
    private displayStartTips;
    private log;
    private error;
    private checkTsConfigTsNodeModule;
    private getData;
    private checkTsType;
}
//# sourceMappingURL=index.d.ts.map