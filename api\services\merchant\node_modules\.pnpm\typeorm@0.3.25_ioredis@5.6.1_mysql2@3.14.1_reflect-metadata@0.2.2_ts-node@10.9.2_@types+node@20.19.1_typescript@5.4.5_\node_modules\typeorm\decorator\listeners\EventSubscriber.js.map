{"version": 3, "sources": ["../../src/decorator/listeners/EventSubscriber.ts"], "names": [], "mappings": ";;AAOA,0CAMC;AAbD,2CAAsD;AAGtD;;;GAGG;AACH,SAAgB,eAAe;IAC3B,OAAO,UAAU,MAAgB;QAC7B,IAAA,gCAAsB,GAAE,CAAC,iBAAiB,CAAC,IAAI,CAAC;YAC5C,MAAM,EAAE,MAAM;SACe,CAAC,CAAA;IACtC,CAAC,CAAA;AACL,CAAC", "file": "EventSubscriber.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { EntitySubscriberMetadataArgs } from \"../../metadata-args/EntitySubscriberMetadataArgs\"\n\n/**\n * Classes decorated with this decorator will listen to ORM events and their methods will be triggered when event\n * occurs. Those classes must implement EventSubscriberInterface interface.\n */\nexport function EventSubscriber(): ClassDecorator {\n    return function (target: Function) {\n        getMetadataArgsStorage().entitySubscribers.push({\n            target: target,\n        } as EntitySubscriberMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}