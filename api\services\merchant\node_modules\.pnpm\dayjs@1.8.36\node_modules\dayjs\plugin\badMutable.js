!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):t.dayjs_plugin_badMutable=n()}(this,function(){"use strict";return function(t,n){var i=n.prototype;i.$g=function(t,n,i){return this.$utils().u(t)?this[n]:this.$set(i,t)},i.set=function(t,n){return this.$set(t,n)};var e=i.startOf;i.startOf=function(t,n){return this.$d=e.bind(this)(t,n).toDate(),this.init(),this};var s=i.add;i.add=function(t,n){return this.$d=s.bind(this)(t,n).toDate(),this.init(),this};var r=i.locale;i.locale=function(t,n){return t?(this.$L=r.bind(this)(t,n).$L,this):this.$L};var o=i.daysInMonth;i.daysInMonth=function(){return o.bind(this.clone())()};var u=i.isSame;i.isSame=function(t,n){return u.bind(this.clone())(t,n)};var f=i.isBefore;i.isBefore=function(t,n){return f.bind(this.clone())(t,n)};var d=i.isAfter;i.isAfter=function(t,n){return d.bind(this.clone())(t,n)}}});
