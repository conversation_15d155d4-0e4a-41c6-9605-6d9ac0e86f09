import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceApplyInvoiceEntity } from '../../../entity/apply/invoice';
import { FinanceUserInvoiceService } from '../../../service/user/invoice';
import { UserInfoEntity } from '../../../../user/entity/info';
import { FinanceApplyInvoiceService } from '../../../service/apply/invoice';
import { Body, Inject, Post } from '@midwayjs/core';

/**
 * 发票申请
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: FinanceApplyInvoiceEntity,
  service: FinanceUserInvoiceService,
  pageQueryOp: {
    keyWordLikeFields: ['b.nickName'],
    fieldEq: ['a.status'],
    select: ['a.*', 'b.nickName as userName', 'b.avatarUrl'],
    join: [
      {
        entity: UserInfoEntity,
        alias: 'b',
        condition: 'a.userId = b.id',
      },
    ],
  },
})
export class AdminFinanceUserInvoiceController extends BaseController {
  @Inject()
  financeApplyInvoiceService: FinanceApplyInvoiceService;

  @Post('/auth', { summary: '审核' })
  async auth(
    @Body('id') id: number,
    @Body('status') status: number,
    @Body('remark') remark: string
  ) {
    await this.financeApplyInvoiceService.auth(id, status, remark);
    return this.ok();
  }
}
