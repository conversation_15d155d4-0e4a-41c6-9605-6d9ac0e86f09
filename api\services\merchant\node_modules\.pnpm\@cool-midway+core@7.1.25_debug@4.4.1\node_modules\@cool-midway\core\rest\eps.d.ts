import { MidwayWebRouterService } from "@midwayjs/core";
import { TypeORMDataSourceManager } from "@midwayjs/typeorm";
import { CoolUrlTagData } from "../tag/data";
/**
 * 实体路径
 */
export declare class CoolEps {
    admin: {};
    app: {};
    module: {};
    midwayWebRouterService: MidwayWebRouterService;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    epsConfig: boolean;
    moduleConfig: any;
    coolUrlTagData: CoolUrlTagData;
    init(): Promise<void>;
    /**
     * 模块信息
     * @param module
     */
    modules(module?: string): Promise<any>;
    /**
     * 所有controller
     * @returns
     */
    controller(): Promise<any[]>;
    /**
     * 所有路由
     * @returns
     */
    router(): Promise<any>;
    /**
     * 所有实体
     * @returns
     */
    entity(): Promise<{}>;
}
