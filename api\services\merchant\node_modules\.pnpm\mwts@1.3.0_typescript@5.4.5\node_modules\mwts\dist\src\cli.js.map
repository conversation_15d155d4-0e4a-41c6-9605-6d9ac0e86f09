{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/cli.ts"], "names": [], "mappings": ";;;;AACA,6BAA6B;AAC7B,6BAA6B;AAC7B,kDAAkD;AAClD,iCAA8B;AAC9B,mCAAgC;AAChC,iCAA8C;AAC9C,+BAA+B;AAG/B,8DAA8D;AAC9D,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,CAAgB,CAAC;AACjE,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;AAwBpD,MAAM,MAAM,GAAW,OAAiB,CAAC;AAEzC,MAAM,GAAG,GAAG,IAAI,CAAC;IACf,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;iBAuBS;IACf,KAAK,EAAE;QACL,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QACzB,GAAG,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;QACpC,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE;QACnC,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;QAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;KAC1B;CACF,CAAC,CAAC;AAEH;;;;GAIG;AACH,SAAgB,cAAc;IAC5B,OAAO,OAAO,CAAC,OAAO,CAAC;AACzB,CAAC;AAFD,wCAEC;AAED,SAAgB,gBAAgB;IAC9B,MAAM,WAAW,GAAG,IAAA,eAAQ,EAC1B,OAAO,CAAC,OAAO,CAAC,qBAAqB,CAAC,CACxB,CAAC;IACjB,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AALD,4CAKC;AAED,SAAgB,kBAAkB;IAChC,MAAM,WAAW,GAAG,IAAA,eAAQ,EAC1B,OAAO,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAC1B,CAAC;IACjB,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC;AALD,gDAKC;AAED,SAAS,KAAK,CAAC,GAAY;IACzB,IAAI,GAAG,EAAE;QACP,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KACnB;IACD,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAEM,KAAK,UAAU,GAAG,CAAC,IAAY,EAAE,KAAe;IACrD,+CAA+C;IAC/C,MAAM,gBAAgB,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzE,OAAO,CAAC,GAAG,CAAC,oBAAoB,gBAAgB,EAAE,CAAC,CAAC;IACpD,OAAO,CAAC,GAAG,CAAC,mBAAmB,gBAAgB,EAAE,EAAE,CAAC,CAAC;IACrD,OAAO,CAAC,GAAG,CAAC,qBAAqB,kBAAkB,EAAE,EAAE,CAAC,CAAC;IACzD,IAAI,gBAAgB,GAAG,EAAE,EAAE;QACzB,MAAM,IAAI,KAAK,CACb;QACE,OAAO,CAAC,OAAO;wCACiB,CACnC,CAAC;KACH;IAED,MAAM,OAAO,GAAG;QACd,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK;QACjC,qDAAqD;QACrD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC;QAC7C,aAAa,EAAE,OAAO,CAAC,GAAG,EAAE;QAC5B,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK;QAC1C,EAAE,EAAE,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK;QACxC,MAAM;QACN,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,IAAA,iBAAU,GAAE;KAC1B,CAAC;IACb,qEAAqE;IACrE,2DAA2D;IAC3D,+CAA+C;IAC/C,IAAI,IAAI,KAAK,MAAM,EAAE;QACnB,OAAO,IAAA,WAAI,EAAC,OAAO,CAAC,CAAC;KACtB;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IACvC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;QACtB,KAAK,CAAC,IAAI,CACR,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,iCAAiC,CAClC,CAAC;KACH;IAED,QAAQ,IAAI,EAAE;QACZ,KAAK,MAAM,CAAC;QACZ,KAAK,OAAO,CAAC,CAAC;YACZ,IAAI;gBACF,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE;oBACtC,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,KAAK,CAAC;aACd;SACF;QACD,KAAK,KAAK,CAAC,CAAC;YACV,MAAM,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC;YAC3D,IAAI;gBACF,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC,EAAE;oBAC/C,KAAK,EAAE,SAAS;iBACjB,CAAC,CAAC;gBACH,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACjB,OAAO,KAAK,CAAC;aACd;SACF;QACD,KAAK,OAAO;YACV,OAAO,IAAA,aAAK,EAAC,OAAO,CAAC,CAAC;QACxB;YACE,KAAK,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC;YAC/B,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAxED,kBAwEC;AAED,cAAc,CAAC,EAAE,GAAG,EAAE,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC;AAE9C,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;IACxB,KAAK,EAAE,CAAC;CACT;AAED,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;IACnD,IAAI,CAAC,OAAO,EAAE;QACZ,2CAA2C;QAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KACjB;AACH,CAAC,CAAC,CAAC"}