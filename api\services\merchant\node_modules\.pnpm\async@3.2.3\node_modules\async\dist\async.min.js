(function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.async={})})(this,function(e){'use strict';function t(e,...t){return(...n)=>e(...t,...n)}function n(e){return function(...t){var n=t.pop();return e.call(this,t,n)}}function a(e){setTimeout(e,0)}function i(e){return(t,...n)=>e(()=>t(...n))}function r(e){return u(e)?function(...t){const n=t.pop(),a=e.apply(this,t);return s(a,n)}:n(function(t,n){var a;try{a=e.apply(this,t)}catch(t){return n(t)}return a&&"function"==typeof a.then?s(a,n):void n(null,a)})}function s(e,t){return e.then(e=>{l(t,null,e)},e=>{l(t,e&&e.message?e:new Error(e))})}function l(e,t,n){try{e(t,n)}catch(e){be(t=>{throw t},e)}}function u(e){return"AsyncFunction"===e[Symbol.toStringTag]}function d(e){return"AsyncGenerator"===e[Symbol.toStringTag]}function c(e){return"function"==typeof e[Symbol.asyncIterator]}function p(e){if("function"!=typeof e)throw new Error("expected a function");return u(e)?r(e):e}function o(e,t=e.length){if(!t)throw new Error("arity is undefined");return function(...n){return"function"==typeof n[t-1]?e.apply(this,n):new Promise((a,i)=>{n[t-1]=(e,...t)=>e?i(e):void a(1<t.length?t:t[0]),e.apply(this,n)})}}function h(e){return function(t,...n){const a=o(function(a){var i=this;return e(t,(e,t)=>{p(e).apply(i,n.concat(t))},a)});return a}}function f(e,t,n,a){t=t||[];var i=[],r=0,s=p(n);return e(t,(e,t,n)=>{var a=r++;s(e,(e,t)=>{i[a]=t,n(e)})},e=>{a(e,i)})}function y(e){return e&&"number"==typeof e.length&&0<=e.length&&0==e.length%1}function m(e){function t(...t){if(null!==e){var n=e;e=null,n.apply(this,t)}}return Object.assign(t,e),t}function g(e){return e[Symbol.iterator]&&e[Symbol.iterator]()}function k(e){var t=-1,n=e.length;return function(){return++t<n?{value:e[t],key:t}:null}}function v(e){var t=-1;return function(){var n=e.next();return n.done?null:(t++,{value:n.value,key:t})}}function S(e){var t=e?Object.keys(e):[],n=-1,a=t.length;return function i(){var r=t[++n];return"__proto__"===r?i():n<a?{value:e[r],key:r}:null}}function x(e){if(y(e))return k(e);var t=g(e);return t?v(t):S(e)}function L(e){return function(...t){if(null===e)throw new Error("Callback was already called.");var n=e;e=null,n.apply(this,t)}}function E(e,t,n,a){function i(){c>=t||d||l||(d=!0,e.next().then(({value:e,done:t})=>{if(!(u||l))return d=!1,t?(l=!0,void(0>=c&&a(null))):void(c++,n(e,p,r),p++,i())}).catch(s))}function r(e,t){return c-=1,u?void 0:e?s(e):!1===e?(l=!0,void(u=!0)):t===_e||l&&0>=c?(l=!0,a(null)):void i()}function s(e){u||(d=!1,l=!0,a(e))}let l=!1,u=!1,d=!1,c=0,p=0;i()}function O(e,t,n){function a(e,t){!1===e&&(l=!0);!0===l||(e?n(e):(++r===s||t===_e)&&n(null))}n=m(n);var i=0,r=0,{length:s}=e,l=!1;for(0===s&&n(null);i<s;i++)t(e[i],i,L(a))}function b(e,t,n){return Ae(e,1/0,t,n)}function _(){function e(e,...a){return e?n(e):void t(1<a.length?a:a[0])}let t,n;return e[Ce]=new Promise((e,a)=>{t=e,n=a}),e}function M(e,t,n){function a(e,t){g.push(()=>l(e,t))}function i(){if(!h){if(0===g.length&&0===o)return n(null,c);for(;g.length&&o<t;){var e=g.shift();e()}}}function r(e,t){var n=y[e];n||(n=y[e]=[]),n.push(t)}function s(e){var t=y[e]||[];t.forEach(e=>e()),i()}function l(e,t){if(!f){var a=L((t,...a)=>{if(o--,!1===t)return void(h=!0);if(2>a.length&&([a]=a),t){var i={};if(Object.keys(c).forEach(e=>{i[e]=c[e]}),i[e]=a,f=!0,y=Object.create(null),h)return;n(t,i)}else c[e]=a,s(e)});o++;var i=p(t[t.length-1]);1<t.length?i(c,a):i(a)}}function u(t){var n=[];return Object.keys(e).forEach(a=>{const i=e[a];Array.isArray(i)&&0<=i.indexOf(t)&&n.push(a)}),n}"number"!=typeof t&&(n=t,t=null),n=m(n||_());var d=Object.keys(e).length;if(!d)return n(null);t||(t=d);var c={},o=0,h=!1,f=!1,y=Object.create(null),g=[],k=[],v={};return Object.keys(e).forEach(t=>{var n=e[t];if(!Array.isArray(n))return a(t,[n]),void k.push(t);var i=n.slice(0,n.length-1),s=i.length;return 0===s?(a(t,n),void k.push(t)):void(v[t]=s,i.forEach(l=>{if(!e[l])throw new Error("async.auto task `"+t+"` has a non-existent dependency `"+l+"` in "+i.join(", "));r(l,()=>{s--,0===s&&a(t,n)})}))}),function(){for(var e,t=0;k.length;)e=k.pop(),t++,u(e).forEach(e=>{0==--v[e]&&k.push(e)});if(t!==d)throw new Error("async.auto cannot execute tasks due to a recursive dependency")}(),i(),n[Ce]}function A(e){let t="",n=0,a=e.indexOf("*/");for(;n<e.length;)if("/"===e[n]&&"/"===e[n+1]){let t=e.indexOf("\n",n);n=-1===t?e.length:t}else if(-1!==a&&"/"===e[n]&&"*"===e[n+1]){let i=e.indexOf("*/",n);-1===i?(t+=e[n],n++):(n=i+2,a=e.indexOf("*/",n))}else t+=e[n],n++;return t}function I(e){const t=A(e.toString());let n=t.match(Pe);if(n||(n=t.match(Re)),!n)throw new Error("could not parse args in autoInject\nSource:\n"+t);let[,a]=n;return a.replace(/\s/g,"").split(ze).map(e=>e.replace(Ne,"").trim())}function j(e,t){var n={};return Object.keys(e).forEach(t=>{function a(e,t){var n=i.map(t=>e[t]);n.push(t),p(r)(...n)}var i,r=e[t],s=u(r),l=!s&&1===r.length||s&&0===r.length;if(Array.isArray(r))i=[...r],r=i.pop(),n[t]=i.concat(0<i.length?a:r);else if(l)n[t]=r;else{if(i=I(r),0===r.length&&!s&&0===i.length)throw new Error("autoInject task functions require explicit parameters.");s||i.pop(),n[t]=i.concat(a)}}),M(n,t)}function w(e,t){e.length=1,e.head=e.tail=t}function B(e,t,n){function a(e,t){f[e].push(t)}function i(e,t){const n=(...a)=>{r(e,n),t(...a)};f[e].push(n)}function r(e,t){return e?t?void(f[e]=f[e].filter(e=>e!==t)):f[e]=[]:Object.keys(f).forEach(e=>f[e]=[])}function s(e,...t){f[e].forEach(e=>e(...t))}function l(e,t,n,a){function i(e,...t){return e?n?s(e):r():1>=t.length?r(t[0]):void r(t)}if(null!=a&&"function"!=typeof a)throw new Error("task callback must be a function");k.started=!0;var r,s,l={data:e,callback:n?i:a||i};if(t?k._tasks.unshift(l):k._tasks.push(l),y||(y=!0,be(()=>{y=!1,k.process()})),n||!a)return new Promise((e,t)=>{r=e,s=t})}function u(e){return function(t,...n){o-=1;for(var a=0,r=e.length;a<r;a++){var l=e[a],u=h.indexOf(l);0===u?h.shift():0<u&&h.splice(u,1),l.callback(t,...n),null!=t&&s("error",t,l.data)}o<=k.concurrency-k.buffer&&s("unsaturated"),k.idle()&&s("drain"),k.process()}}function d(e){return!!(0===e.length&&k.idle())&&(be(()=>s("drain")),!0)}if(null==t)t=1;else if(0===t)throw new RangeError("Concurrency must not be zero");var c=p(e),o=0,h=[];const f={error:[],drain:[],saturated:[],unsaturated:[],empty:[]};var y=!1;const m=e=>t=>t?void(r(e),a(e,t)):new Promise((t,n)=>{i(e,(e,a)=>e?n(e):void t(a))});var g=!1,k={_tasks:new Ve,*[Symbol.iterator](){yield*k._tasks[Symbol.iterator]()},concurrency:t,payload:n,buffer:t/4,started:!1,paused:!1,push(e,t){return Array.isArray(e)?d(e)?void 0:e.map(e=>l(e,!1,!1,t)):l(e,!1,!1,t)},pushAsync(e,t){return Array.isArray(e)?d(e)?void 0:e.map(e=>l(e,!1,!0,t)):l(e,!1,!0,t)},kill(){r(),k._tasks.empty()},unshift(e,t){return Array.isArray(e)?d(e)?void 0:e.map(e=>l(e,!0,!1,t)):l(e,!0,!1,t)},unshiftAsync(e,t){return Array.isArray(e)?d(e)?void 0:e.map(e=>l(e,!0,!0,t)):l(e,!0,!0,t)},remove(e){k._tasks.remove(e)},process(){var e=Math.min;if(!g){for(g=!0;!k.paused&&o<k.concurrency&&k._tasks.length;){var t=[],n=[],a=k._tasks.length;k.payload&&(a=e(a,k.payload));for(var r,d=0;d<a;d++)r=k._tasks.shift(),t.push(r),h.push(r),n.push(r.data);o+=1,0===k._tasks.length&&s("empty"),o===k.concurrency&&s("saturated");var p=L(u(t));c(n,p)}g=!1}},length(){return k._tasks.length},running(){return o},workersList(){return h},idle(){return 0===k._tasks.length+o},pause(){k.paused=!0},resume(){!1===k.paused||(k.paused=!1,be(k.process))}};return Object.defineProperties(k,{saturated:{writable:!1,value:m("saturated")},unsaturated:{writable:!1,value:m("unsaturated")},empty:{writable:!1,value:m("empty")},drain:{writable:!1,value:m("drain")},error:{writable:!1,value:m("error")}}),k}function F(e,t){return B(e,1,t)}function T(e,t,n){return B(e,t,n)}function C(...e){var t=e.map(p);return function(...e){var n=this,a=e[e.length-1];return"function"==typeof a?e.pop():a=_(),qe(t,e,(e,t,a)=>{t.apply(n,e.concat((e,...t)=>{a(e,t)}))},(e,t)=>a(e,...t)),a[Ce]}}function P(...e){return C(...e.reverse())}function R(...e){return function(...t){var n=t.pop();return n(null,...e)}}function z(e,t){return(n,a,i,r)=>{var s,l=!1;const u=p(i);n(a,(n,a,i)=>{u(n,(a,r)=>a||!1===a?i(a):e(r)&&!s?(l=!0,s=t(!0,n),i(null,_e)):void i())},e=>e?r(e):void r(null,l?s:t(!1)))}}function N(e){return(t,...n)=>p(t)(...n,(t,...n)=>{"object"==typeof console&&(t?console.error&&console.error(t):console[e]&&n.forEach(t=>console[e](t)))})}function V(e,t,n){const a=p(t);return Xe(e,(...e)=>{const t=e.pop();a(...e,(e,n)=>t(e,!n))},n)}function Y(e){return(t,n,a)=>e(t,a)}function q(e){return u(e)?e:function(...t){var n=t.pop(),a=!0;t.push((...e)=>{a?be(()=>n(...e)):n(...e)}),e.apply(this,t),a=!1}}function D(e,t,n,a){var r=Array(t.length);e(t,(e,t,a)=>{n(e,(e,n)=>{r[t]=!!n,a(e)})},e=>{if(e)return a(e);for(var n=[],s=0;s<t.length;s++)r[s]&&n.push(t[s]);a(null,n)})}function Q(e,t,n,a){var i=[];e(t,(e,t,a)=>{n(e,(n,r)=>n?a(n):void(r&&i.push({index:t,value:e}),a(n)))},e=>e?a(e):void a(null,i.sort((e,t)=>e.index-t.index).map(e=>e.value)))}function U(e,t,n,a){var i=y(t)?D:Q;return i(e,t,p(n),a)}function G(e,t,n){return ut(e,1/0,t,n)}function W(e,t,n){return ut(e,1,t,n)}function H(e,t,n){return ct(e,1/0,t,n)}function J(e,t,n){return ct(e,1,t,n)}function K(e,t=e=>e){var a=Object.create(null),r=Object.create(null),s=p(e),l=n((e,n)=>{var u=t(...e);u in a?be(()=>n(null,...a[u])):u in r?r[u].push(n):(r[u]=[n],s(...e,(e,...t)=>{e||(a[u]=t);var n=r[u];delete r[u];for(var s=0,d=n.length;s<d;s++)n[s](e,...t)}))});return l.memo=a,l.unmemoized=e,l}function X(e,t){return ot(Ie,e,t)}function Z(e,t,n){return ot(Me(t),e,n)}function $(e,t){var n=p(e);return B((e,t)=>{n(e[0],t)},t,1)}function ee(e){return(e<<1)+1}function te(e){return(e+1>>1)-1}function ne(e,t){return e.priority===t.priority?e.pushCount<t.pushCount:e.priority<t.priority}function ae(e,t){var n=$(e,t),a=!1;return n._tasks=new ht,n.push=function(e,t=0,r=()=>{}){if("function"!=typeof r)throw new Error("task callback must be a function");if(n.started=!0,Array.isArray(e)||(e=[e]),0===e.length&&n.idle())return be(()=>n.drain());for(var s,u=0,d=e.length;u<d;u++)s={data:e[u],priority:t,callback:r},n._tasks.push(s);a||(a=!0,be(()=>{a=!1,n.process()}))},delete n.unshift,n}function ie(e,t,n,a){var i=[...e].reverse();return qe(i,t,n,a)}function re(e){var t=p(e);return n(function(e,n){return e.push((e,...t)=>{let a={};if(e&&(a.error=e),0<t.length){var i=t;1>=t.length&&([i]=t),a.value=i}n(null,a)}),t.apply(this,e)})}function se(e){var t;return Array.isArray(e)?t=e.map(re):(t={},Object.keys(e).forEach(n=>{t[n]=re.call(this,e[n])})),t}function le(e,t,n,a){const i=p(n);return U(e,t,(e,t)=>{i(e,(e,n)=>{t(e,!n)})},a)}function ue(e){return function(){return e}}function de(e,t,n){function a(){r((e,...t)=>{!1===e||(e&&s++<i.times&&("function"!=typeof i.errorFilter||i.errorFilter(e))?setTimeout(a,i.intervalFunc(s-1)):n(e,...t))})}var i={times:kt,intervalFunc:ue(vt)};if(3>arguments.length&&"function"==typeof e?(n=t||_(),t=e):(ce(i,e),n=n||_()),"function"!=typeof t)throw new Error("Invalid arguments for async.retry");var r=p(t),s=1;return a(),n[Ce]}function ce(e,n){if("object"==typeof n)e.times=+n.times||kt,e.intervalFunc="function"==typeof n.interval?n.interval:ue(+n.interval||vt),e.errorFilter=n.errorFilter;else if("number"==typeof n||"string"==typeof n)e.times=+n||kt;else throw new Error("Invalid arguments for async.retry")}function pe(e,t){t||(t=e,e=null);let a=e&&e.arity||t.length;u(t)&&(a+=1);var i=p(t);return n((t,n)=>{function r(e){i(...t,e)}return(t.length<a-1||null==n)&&(t.push(n),n=_()),e?de(e,r,n):de(r,n),n[Ce]})}function oe(e,t){return ot(Be,e,t)}function he(e,t,a){var i=p(e);return n((n,r)=>{var s,l=!1;n.push((...e)=>{l||(r(...e),clearTimeout(s))}),s=setTimeout(function(){var t=e.name||"anonymous",n=new Error("Callback function \""+t+"\" timed out.");n.code="ETIMEDOUT",a&&(n.info=a),l=!0,r(n)},t),i(...n)})}function fe(e){for(var t=Array(e);e--;)t[e]=e;return t}function ye(e,t,n,a){var i=p(n);return De(fe(e),t,i,a)}function me(e,t,n){return ye(e,1/0,t,n)}function ge(e,t,n){return ye(e,1,t,n)}function ke(e,t,n,a){3>=arguments.length&&"function"==typeof t&&(a=n,n=t,t=Array.isArray(e)?[]:{}),a=m(a||_());var i=p(n);return Ie(e,(e,n,a)=>{i(t,e,n,a)},e=>a(e,t)),a[Ce]}function ve(e){return(...t)=>(e.unmemoized||e)(...t)}function Se(e,t,n){const a=p(e);return bt(e=>a((t,n)=>e(t,!n)),t,n)}var xe,Le="function"==typeof queueMicrotask&&queueMicrotask,Ee="function"==typeof setImmediate&&setImmediate,Oe="object"==typeof process&&"function"==typeof process.nextTick;xe=Le?queueMicrotask:Ee?setImmediate:Oe?process.nextTick:a;var be=i(xe);const _e={};var Me=e=>(t,n,a)=>{function i(e,t){if(!u)if(p-=1,e)l=!0,a(e);else if(!1===e)l=!0,u=!0;else{if(t===_e||l&&0>=p)return l=!0,a(null);o||r()}}function r(){for(o=!0;p<e&&!l;){var t=s();if(null===t)return l=!0,void(0>=p&&a(null));p+=1,n(t.value,t.key,L(i))}o=!1}if(a=m(a),0>=e)throw new RangeError("concurrency limit cannot be less than 1");if(!t)return a(null);if(d(t))return E(t,e,n,a);if(c(t))return E(t[Symbol.asyncIterator](),e,n,a);var s=x(t),l=!1,u=!1,p=0,o=!1;r()},Ae=o(function(e,t,n,a){return Me(t)(e,p(n),a)},4),Ie=o(function(e,t,n){var a=y(e)?O:b;return a(e,p(t),n)},3),je=o(function(e,t,n){return f(Ie,e,t,n)},3),we=h(je),Be=o(function(e,t,n){return Ae(e,1,t,n)},3),Fe=o(function(e,t,n){return f(Be,e,t,n)},3),Te=h(Fe);const Ce=Symbol("promiseCallback");var Pe=/^(?:async\s+)?(?:function)?\s*\w*\s*\(\s*([^)]+)\s*\)(?:\s*{)/,Re=/^(?:async\s+)?\(?\s*([^)=]+)\s*\)?(?:\s*=>)/,ze=/,/,Ne=/(=.+)?(\s*)$/;class Ve{constructor(){this.head=this.tail=null,this.length=0}removeLink(e){return e.prev?e.prev.next=e.next:this.head=e.next,e.next?e.next.prev=e.prev:this.tail=e.prev,e.prev=e.next=null,this.length-=1,e}empty(){for(;this.head;)this.shift();return this}insertAfter(e,t){t.prev=e,t.next=e.next,e.next?e.next.prev=t:this.tail=t,e.next=t,this.length+=1}insertBefore(e,t){t.prev=e.prev,t.next=e,e.prev?e.prev.next=t:this.head=t,e.prev=t,this.length+=1}unshift(e){this.head?this.insertBefore(this.head,e):w(this,e)}push(e){this.tail?this.insertAfter(this.tail,e):w(this,e)}shift(){return this.head&&this.removeLink(this.head)}pop(){return this.tail&&this.removeLink(this.tail)}toArray(){return[...this]}*[Symbol.iterator](){for(var e=this.head;e;)yield e.data,e=e.next}remove(e){for(var t=this.head;t;){var{next:n}=t;e(t)&&this.removeLink(t),t=n}return this}}var Ye,qe=o(function(e,t,n,a){a=m(a);var r=p(n);return Be(e,(e,n,a)=>{r(t,e,(e,n)=>{t=n,a(e)})},e=>a(e,t))},4),De=o(function(e,t,n,a){return f(Me(t),e,n,a)},4),Qe=o(function(e,t,n,a){var i=p(n);return De(e,t,(e,t)=>{i(e,(e,...n)=>e?t(e):t(e,n))},(e,t)=>{for(var n=[],r=0;r<t.length;r++)t[r]&&(n=n.concat(...t[r]));return a(e,n)})},4),Ue=o(function(e,t,n){return Qe(e,1/0,t,n)},3),Ge=o(function(e,t,n){return Qe(e,1,t,n)},3),We=o(function(e,t,n){return z(e=>e,(e,t)=>t)(Ie,e,t,n)},3),He=o(function(e,t,n,a){return z(e=>e,(e,t)=>t)(Me(t),e,n,a)},4),Je=o(function(e,t,n){return z(e=>e,(e,t)=>t)(Me(1),e,t,n)},3),Ke=N("dir"),Xe=o(function(e,t,n){function a(e,...t){return e?n(e):void(!1===e||(r=t,l(...t,i)))}function i(e,t){return e?n(e):!1===e?void 0:t?void s(a):n(null,...r)}n=L(n);var r,s=p(e),l=p(t);return i(null,!0)},3),Ze=o(function(e,t,n){return Ie(e,Y(p(t)),n)},3),$e=o(function(e,t,n,a){return Me(t)(e,Y(p(n)),a)},4),et=o(function(e,t,n){return $e(e,1,t,n)},3),tt=o(function(e,t,n){return z(e=>!e,e=>!e)(Ie,e,t,n)},3),nt=o(function(e,t,n,a){return z(e=>!e,e=>!e)(Me(t),e,n,a)},4),at=o(function(e,t,n){return z(e=>!e,e=>!e)(Be,e,t,n)},3),it=o(function(e,t,n){return U(Ie,e,t,n)},3),rt=o(function(e,t,n,a){return U(Me(t),e,n,a)},4),st=o(function(e,t,n){return U(Be,e,t,n)},3),lt=o(function(e,t){function n(e){return e?a(e):void(!1===e||i(n))}var a=L(t),i=p(q(e));return n()},2),ut=o(function(e,t,n,a){var i=p(n);return De(e,t,(e,t)=>{i(e,(n,a)=>n?t(n):t(n,{key:a,val:e}))},(e,t)=>{for(var n={},{hasOwnProperty:r}=Object.prototype,s=0;s<t.length;s++)if(t[s]){var{key:l}=t[s],{val:u}=t[s];r.call(n,l)?n[l].push(u):n[l]=[u]}return a(e,n)})},4),dt=N("log"),ct=o(function(e,t,n,a){a=m(a);var i={},r=p(n);return Me(t)(e,(e,t,n)=>{r(e,t,(e,a)=>e?n(e):void(i[t]=a,n(e)))},e=>a(e,i))},4);Ye=Oe?process.nextTick:Ee?setImmediate:a;var pt=i(Ye),ot=o((e,t,n)=>{var a=y(t)?[]:{};e(t,(e,t,n)=>{p(e)((e,...i)=>{2>i.length&&([i]=i),a[t]=i,n(e)})},e=>n(e,a))},3);class ht{constructor(){this.heap=[],this.pushCount=Number.MIN_SAFE_INTEGER}get length(){return this.heap.length}empty(){return this.heap=[],this}percUp(e){for(let n;0<e&&ne(this.heap[e],this.heap[n=te(e)]);){let a=this.heap[e];this.heap[e]=this.heap[n],this.heap[n]=a,e=n}}percDown(e){for(let n,a;(n=ee(e))<this.heap.length&&(n+1<this.heap.length&&ne(this.heap[n+1],this.heap[n])&&++n,!ne(this.heap[e],this.heap[n]));)a=this.heap[e],this.heap[e]=this.heap[n],this.heap[n]=a,e=n}push(e){e.pushCount=++this.pushCount,this.heap.push(e),this.percUp(this.heap.length-1)}unshift(e){return this.heap.push(e)}shift(){let[e]=this.heap;return this.heap[0]=this.heap[this.heap.length-1],this.heap.pop(),this.percDown(0),e}toArray(){return[...this]}*[Symbol.iterator](){for(let e=0;e<this.heap.length;e++)yield this.heap[e].data}remove(e){let t=0;for(let n=0;n<this.heap.length;n++)e(this.heap[n])||(this.heap[t]=this.heap[n],t++);this.heap.splice(t);for(let t=te(this.heap.length-1);0<=t;t--)this.percDown(t);return this}}var ft=o(function(e,t){if(t=m(t),!Array.isArray(e))return t(new TypeError("First argument to race must be an array of functions"));if(!e.length)return t();for(var n=0,a=e.length;n<a;n++)p(e[n])(t)},2),yt=o(function(e,t,n){return le(Ie,e,t,n)},3),mt=o(function(e,t,n,a){return le(Me(t),e,n,a)},4),gt=o(function(e,t,n){return le(Be,e,t,n)},3);const kt=5,vt=0;var St=o(function(e,t,n){return z(Boolean,e=>e)(Ie,e,t,n)},3),xt=o(function(e,t,n,a){return z(Boolean,e=>e)(Me(t),e,n,a)},4),Lt=o(function(e,t,n){return z(Boolean,e=>e)(Be,e,t,n)},3),Et=o(function(e,t,n){function a(e,t){var n=e.criteria,a=t.criteria;return n<a?-1:n>a?1:0}var i=p(t);return je(e,(e,t)=>{i(e,(n,a)=>n?t(n):void t(n,{value:e,criteria:a}))},(e,t)=>e?n(e):void n(null,t.sort(a).map(e=>e.value)))},3),Ot=o(function(e,t){var n,a=null;return et(e,(e,t)=>{p(e)((e,...i)=>!1===e?t(e):void(2>i.length?[n]=i:n=i,a=e,t(e?null:{})))},()=>t(a,n))}),bt=o(function(e,t,n){function a(e,...t){if(e)return n(e);l=t;!1===e||s(i)}function i(e,t){return e?n(e):!1===e?void 0:t?void r(a):n(null,...l)}n=L(n);var r=p(t),s=p(e),l=[];return s(i)},3),_t=o(function(e,t){function n(t){var n=p(e[i++]);n(...t,L(a))}function a(a,...r){return!1===a?void 0:a||i===e.length?t(a,...r):void n(r)}if(t=m(t),!Array.isArray(e))return t(new Error("First argument to waterfall must be an array of functions"));if(!e.length)return t();var i=0;n([])});e.default={apply:t,applyEach:we,applyEachSeries:Te,asyncify:r,auto:M,autoInject:j,cargo:F,cargoQueue:T,compose:P,concat:Ue,concatLimit:Qe,concatSeries:Ge,constant:R,detect:We,detectLimit:He,detectSeries:Je,dir:Ke,doUntil:V,doWhilst:Xe,each:Ze,eachLimit:$e,eachOf:Ie,eachOfLimit:Ae,eachOfSeries:Be,eachSeries:et,ensureAsync:q,every:tt,everyLimit:nt,everySeries:at,filter:it,filterLimit:rt,filterSeries:st,forever:lt,groupBy:G,groupByLimit:ut,groupBySeries:W,log:dt,map:je,mapLimit:De,mapSeries:Fe,mapValues:H,mapValuesLimit:ct,mapValuesSeries:J,memoize:K,nextTick:pt,parallel:X,parallelLimit:Z,priorityQueue:ae,queue:$,race:ft,reduce:qe,reduceRight:ie,reflect:re,reflectAll:se,reject:yt,rejectLimit:mt,rejectSeries:gt,retry:de,retryable:pe,seq:C,series:oe,setImmediate:be,some:St,someLimit:xt,someSeries:Lt,sortBy:Et,timeout:he,times:me,timesLimit:ye,timesSeries:ge,transform:ke,tryEach:Ot,unmemoize:ve,until:Se,waterfall:_t,whilst:bt,all:tt,allLimit:nt,allSeries:at,any:St,anyLimit:xt,anySeries:Lt,find:We,findLimit:He,findSeries:Je,flatMap:Ue,flatMapLimit:Qe,flatMapSeries:Ge,forEach:Ze,forEachSeries:et,forEachLimit:$e,forEachOf:Ie,forEachOfSeries:Be,forEachOfLimit:Ae,inject:qe,foldl:qe,foldr:ie,select:it,selectLimit:rt,selectSeries:st,wrapSync:r,during:bt,doDuring:Xe},e.apply=t,e.applyEach=we,e.applyEachSeries=Te,e.asyncify=r,e.auto=M,e.autoInject=j,e.cargo=F,e.cargoQueue=T,e.compose=P,e.concat=Ue,e.concatLimit=Qe,e.concatSeries=Ge,e.constant=R,e.detect=We,e.detectLimit=He,e.detectSeries=Je,e.dir=Ke,e.doUntil=V,e.doWhilst=Xe,e.each=Ze,e.eachLimit=$e,e.eachOf=Ie,e.eachOfLimit=Ae,e.eachOfSeries=Be,e.eachSeries=et,e.ensureAsync=q,e.every=tt,e.everyLimit=nt,e.everySeries=at,e.filter=it,e.filterLimit=rt,e.filterSeries=st,e.forever=lt,e.groupBy=G,e.groupByLimit=ut,e.groupBySeries=W,e.log=dt,e.map=je,e.mapLimit=De,e.mapSeries=Fe,e.mapValues=H,e.mapValuesLimit=ct,e.mapValuesSeries=J,e.memoize=K,e.nextTick=pt,e.parallel=X,e.parallelLimit=Z,e.priorityQueue=ae,e.queue=$,e.race=ft,e.reduce=qe,e.reduceRight=ie,e.reflect=re,e.reflectAll=se,e.reject=yt,e.rejectLimit=mt,e.rejectSeries=gt,e.retry=de,e.retryable=pe,e.seq=C,e.series=oe,e.setImmediate=be,e.some=St,e.someLimit=xt,e.someSeries=Lt,e.sortBy=Et,e.timeout=he,e.times=me,e.timesLimit=ye,e.timesSeries=ge,e.transform=ke,e.tryEach=Ot,e.unmemoize=ve,e.until=Se,e.waterfall=_t,e.whilst=bt,e.all=tt,e.allLimit=nt,e.allSeries=at,e.any=St,e.anyLimit=xt,e.anySeries=Lt,e.find=We,e.findLimit=He,e.findSeries=Je,e.flatMap=Ue,e.flatMapLimit=Qe,e.flatMapSeries=Ge,e.forEach=Ze,e.forEachSeries=et,e.forEachLimit=$e,e.forEachOf=Ie,e.forEachOfSeries=Be,e.forEachOfLimit=Ae,e.inject=qe,e.foldl=qe,e.foldr=ie,e.select=it,e.selectLimit=rt,e.selectSeries=st,e.wrapSync=r,e.during=bt,e.doDuring=Xe,Object.defineProperty(e,"__esModule",{value:!0})});