"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MoleculerTransactionHandler = void 0;
const core_1 = require("@midwayjs/core");
const __1 = require("..");
/**
 * moleculer 事件处理
 */
let MoleculerTransactionHandler = class MoleculerTransactionHandler {
    /**
     * 注册事件
     * @param params
     */
    async handler(params) {
        const { rpcTransactionId, commit } = params;
        this.coreLogger.info(`\x1B[36m [cool:core] MoleculerTransaction event params: ${JSON.stringify(params)} \x1B[0m`);
        if (global['moleculer.transactions'][rpcTransactionId]) {
            this.coreLogger.info(`\x1B[36m [cool:core] MoleculerTransaction event ${commit ? 'commitTransaction' : 'rollbackTransaction'} ID: ${rpcTransactionId} \x1B[0m`);
            await global['moleculer.transactions'][rpcTransactionId][commit ? 'commitTransaction' : 'rollbackTransaction']();
            await global['moleculer.transactions'][rpcTransactionId].release();
            delete global['moleculer.transactions'][rpcTransactionId];
        }
    }
};
exports.MoleculerTransactionHandler = MoleculerTransactionHandler;
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], MoleculerTransactionHandler.prototype, "coreLogger", void 0);
__decorate([
    (0, __1.CoolRpcEventHandler)('moleculer.transaction') // 唯一参数，eventName，事件名，可不填，默认为方法名
    ,
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], MoleculerTransactionHandler.prototype, "handler", null);
exports.MoleculerTransactionHandler = MoleculerTransactionHandler = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton),
    (0, __1.CoolRpcEvent)()
], MoleculerTransactionHandler);
