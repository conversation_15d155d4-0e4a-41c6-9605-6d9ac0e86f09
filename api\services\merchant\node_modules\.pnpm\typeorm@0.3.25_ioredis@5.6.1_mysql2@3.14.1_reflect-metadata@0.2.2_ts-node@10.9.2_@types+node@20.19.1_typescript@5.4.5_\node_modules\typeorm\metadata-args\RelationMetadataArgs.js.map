{"version": 3, "sources": ["../../src/metadata-args/RelationMetadataArgs.ts"], "names": [], "mappings": "", "file": "RelationMetadataArgs.js", "sourcesContent": ["import { RelationType } from \"../metadata/types/RelationTypes\"\nimport { RelationOptions } from \"../decorator/options/RelationOptions\"\nimport { PropertyTypeFactory } from \"../metadata/types/PropertyTypeInFunction\"\nimport { RelationTypeInFunction } from \"../metadata/types/RelationTypeInFunction\"\n\n/**\n * Arguments for RelationMetadata class.\n */\nexport interface RelationMetadataArgs {\n    /**\n     * Class to which this relation is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * In the case if this relation is without a target, targetId must be specified.\n     * This is used for entity schemas without classes.\n     */\n    // readonly targetId?: string;\n\n    /**\n     * Class's property name to which this relation is applied.\n     */\n    readonly propertyName: string\n\n    /**\n     * Indicates if this relation will be lazily loaded.\n     */\n    readonly isLazy: boolean\n\n    /**\n     * Original (reflected) class's property type.\n     *\n     * todo: this can be empty for relations from entity schemas.\n     */\n    // readonly propertyType?: any;\n\n    /**\n     * Type of relation. Can be one of the value of the RelationTypes class.\n     */\n    readonly relationType: RelationType\n\n    /**\n     * Type of the relation. This type is in function because of language specifics and problems with recursive\n     * referenced classes.\n     */\n    readonly type: RelationTypeInFunction\n\n    /**\n     * Inverse side of the relation.\n     */\n    readonly inverseSideProperty?: PropertyTypeFactory<any>\n\n    /**\n     * Additional relation options.\n     */\n    readonly options: RelationOptions\n\n    /**\n     * Indicates if this is a parent (can be only many-to-one relation) relation in the tree tables.\n     */\n    readonly isTreeParent?: boolean\n\n    /**\n     * Indicates if this is a children (can be only one-to-many relation) relation in the tree tables.\n     */\n    readonly isTreeChildren?: boolean\n}\n"], "sourceRoot": ".."}