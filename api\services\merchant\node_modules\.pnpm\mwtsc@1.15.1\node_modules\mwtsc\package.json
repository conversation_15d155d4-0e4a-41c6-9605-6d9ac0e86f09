{"name": "mwtsc", "version": "1.15.1", "description": "tsc wrapper for midway development", "main": "lib/index.js", "bin": {"mwtsc": "bin/mwtsc.js"}, "dependencies": {"@midwayjs/glob": "^1.1.1", "chokidar": "^3.6.0", "cli-table3": "^0.6.5", "compare-versions": "^6.1.0", "source-map-support": "^0.5.21", "tar": "^7.4.0", "tsc-alias": "^1.8.8"}, "devDependencies": {"jest": "^29.7.0", "mwts": "^1.3.0", "node-fetch": "^2.7.0", "typescript": "^5.4.5"}, "engines": {"node": ">=12.11.0"}, "files": ["lib", "bin"], "keywords": ["midway", "tsc", "tsc-watch"], "scripts": {"test": "jest --testTimeout=30000 --runInBand", "cov": "jest --testTimeout=30000 --coverage --runInBand --forceExit"}, "repository": {"type": "git", "url": "**************:midwayjs/mwtsc.git"}, "license": "MIT"}