{"version": 3, "sources": ["../browser/src/driver/react-native/ReactNativeConnectionOptions.ts"], "names": [], "mappings": "", "file": "ReactNativeConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface ReactNativeConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"react-native\"\n\n    /**\n     * Database name.\n     */\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"react-native-sqlite-storage\")\n     */\n    readonly driver?: any\n\n    /**\n     * Storage Location\n     */\n    readonly location: string\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}