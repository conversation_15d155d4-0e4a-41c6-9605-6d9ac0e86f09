import { ValidationOptions } from '../ValidationOptions';
export declare const IS_RFC_3339 = "isRFC3339";
/**
 * Check if the string is a valid RFC 3339 date.
 * If given value is not a string, then it returns false.
 */
export declare function isRFC3339(value: unknown): boolean;
/**
 * Check if the string is a valid RFC 3339 date.
 * If given value is not a string, then it returns false.
 */
export declare function IsRFC3339(validationOptions?: ValidationOptions): PropertyDecorator;
