{"version": 3, "sources": ["../../src/metadata-args/CheckMetadataArgs.ts"], "names": [], "mappings": "", "file": "CheckMetadataArgs.js", "sourcesContent": ["/**\n * Arguments for CheckMetadata class.\n */\nexport interface CheckMetadataArgs {\n    /**\n     * Class to which index is applied.\n     */\n    target: Function | string\n\n    /**\n     * Check constraint name.\n     */\n    name?: string\n\n    /**\n     * Check expression.\n     */\n    expression: string\n}\n"], "sourceRoot": ".."}