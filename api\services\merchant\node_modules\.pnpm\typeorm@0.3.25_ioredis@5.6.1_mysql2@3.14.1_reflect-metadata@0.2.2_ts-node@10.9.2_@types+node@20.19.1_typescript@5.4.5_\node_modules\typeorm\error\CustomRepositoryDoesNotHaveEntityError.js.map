{"version": 3, "sources": ["../../src/error/CustomRepositoryDoesNotHaveEntityError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,sCAAuC,SAAQ,2BAAY;IACpE,YAAY,UAAe;QACvB,KAAK,CACD,qBACI,OAAO,UAAU,KAAK,UAAU;YAC5B,CAAC,CAAC,UAAU,CAAC,IAAI;YACjB,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,IACjC,iCAAiC;YAC7B,uEAAuE,CAC9E,CAAA;IACL,CAAC;CACJ;AAXD,wFAWC", "file": "CustomRepositoryDoesNotHaveEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown if custom repositories that extend AbstractRepository classes does not have managed entity.\n */\nexport class CustomRepositoryDoesNotHaveEntityError extends TypeORMError {\n    constructor(repository: any) {\n        super(\n            `Custom repository ${\n                typeof repository === \"function\"\n                    ? repository.name\n                    : repository.constructor.name\n            } does not have managed entity. ` +\n                `Did you forget to specify entity for it @EntityRepository(MyEntity)? `,\n        )\n    }\n}\n"], "sourceRoot": ".."}