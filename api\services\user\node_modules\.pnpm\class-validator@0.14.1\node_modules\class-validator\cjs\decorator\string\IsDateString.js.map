{"version": 3, "file": "IsDateString.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsDateString.ts"], "names": [], "mappings": ";;;AACA,qDAAgE;AAEhE,2CAAwC;AAE3B,QAAA,cAAc,GAAG,cAAc,CAAC;AAE7C;;GAEG;AACH,SAAgB,YAAY,CAAC,KAAc,EAAE,OAAsC;IACjF,OAAO,IAAA,qBAAS,EAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACnC,CAAC;AAFD,oCAEC;AAED;;GAEG;AACH,SAAgB,YAAY,CAC1B,OAAsC,EACtC,iBAAqC;IAErC,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,sBAAc;QACpB,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAW,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC;YAC1D,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,gDAAgD,EAC3E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAlBD,oCAkBC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport * as ValidatorJS from 'validator';\nimport { isISO8601 } from './IsISO8601';\n\nexport const IS_DATE_STRING = 'isDateString';\n\n/**\n * Alias for IsISO8601 validator\n */\nexport function isDateString(value: unknown, options?: ValidatorJS.IsISO8601Options): boolean {\n  return isISO8601(value, options);\n}\n\n/**\n * Alias for IsISO8601 validator\n */\nexport function IsDateString(\n  options?: ValidatorJS.IsISO8601Options,\n  validationOptions?: ValidationOptions\n): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_DATE_STRING,\n      constraints: [options],\n      validator: {\n        validate: (value): boolean => isDateString(value, options),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO 8601 date string',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}