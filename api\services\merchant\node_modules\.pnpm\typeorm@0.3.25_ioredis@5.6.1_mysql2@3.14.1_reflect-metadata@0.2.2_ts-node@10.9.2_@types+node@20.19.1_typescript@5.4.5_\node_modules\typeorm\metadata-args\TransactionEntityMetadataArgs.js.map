{"version": 3, "sources": ["../../src/metadata-args/TransactionEntityMetadataArgs.ts"], "names": [], "mappings": "", "file": "TransactionEntityMetadataArgs.js", "sourcesContent": ["/**\n * Used to inject transaction's entity managed into the method wrapped with @Transaction decorator.\n */\nexport interface TransactionEntityMetadataArgs {\n    /**\n     * Target class on which decorator is used.\n     */\n    readonly target: Function\n\n    /**\n     * Method on which decorator is used.\n     */\n    readonly methodName: string\n\n    /**\n     * Index of the parameter on which decorator is used.\n     */\n    readonly index: number\n}\n"], "sourceRoot": ".."}