{"version": 3, "sources": ["../browser/src/find-options/FindOperator.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD,OAAO,EAAE,sBAAsB,EAAE,MAAM,gCAAgC,CAAA;AAIvE;;GAEG;AACH,MAAM,OAAO,YAAY;IAqCrB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,IAAsB,EACtB,KAA0B,EAC1B,eAAwB,IAAI,EAC5B,qBAA8B,KAAK,EACnC,MAAyB,EACzB,uBAAuC;QA9ClC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,CAAA;QAgD/C,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;QACnB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAA;QACjC,IAAI,CAAC,mBAAmB,GAAG,kBAAkB,CAAA;QAC7C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAA;QACrB,IAAI,CAAC,wBAAwB,GAAG,uBAAuB,CAAA;IAC3D,CAAC;IAED,4EAA4E;IAC5E,YAAY;IACZ,4EAA4E;IAE5E;;;OAGG;IACH,IAAI,YAAY;QACZ,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,CAAA;QAEnC,OAAO,IAAI,CAAC,aAAa,CAAA;IAC7B,CAAC;IAED;;;OAGG;IACH,IAAI,kBAAkB;QAClB,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA;QAEzC,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,IAAI,IAAI;QACJ,OAAO,IAAI,CAAC,KAAK,CAAA;IACrB,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACL,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;QAE5B,OAAO,IAAI,CAAC,MAAM,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,IAAI,uBAAuB;QACvB,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAAA;QAE9C,OAAO,IAAI,CAAC,wBAAwB,CAAA;IACxC,CAAC;IAED;;OAEG;IACH,IAAI,KAAK;QACL,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAAE,OAAO,IAAI,CAAC,MAAM,CAAA;QAEnE,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACH,IAAI,MAAM;QACN,IAAI,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA;QAE7B,OAAO,IAAI,CAAC,OAAO,CAAA;IACvB,CAAC;IAED,cAAc,CAAC,WAAkD;QAC7D,IAAI,IAAI,CAAC,MAAM,YAAY,YAAY,EAAE,CAAC;YACtC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC3C,CAAC;aAAM,CAAC;YACJ,IAAI,CAAC,MAAM;gBACP,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,mBAAmB;oBAClD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CACX,CAAC,CAAM,EAAE,EAAE,CACP,WAAW;wBACX,sBAAsB,CAAC,WAAW,CAC9B,WAAW,EACX,CAAC,CACJ,CACR;oBACH,CAAC,CAAC,sBAAsB,CAAC,WAAW,CAC9B,WAAW,EACX,IAAI,CAAC,MAAM,CACd,CAAA;QACf,CAAC;IACL,CAAC;CACJ", "file": "FindOperator.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { FindOperatorType } from \"./FindOperatorType\"\nimport { InstanceChe<PERSON> } from \"../util/InstanceChecker\"\nimport { ValueTransformer } from \"../decorator/options/ValueTransformer\"\nimport { ApplyValueTransformers } from \"../util/ApplyValueTransformers\"\n\ntype SqlGeneratorType = (aliasPath: string) => string\n\n/**\n * Find Operator used in Find Conditions.\n */\nexport class FindOperator<T> {\n    readonly \"@instanceof\" = Symbol.for(\"FindOperator\")\n\n    // -------------------------------------------------------------------------\n    // Private Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Operator type.\n     */\n    private _type: FindOperatorType\n\n    /**\n     * Parameter value.\n     */\n    private _value: T | FindOperator<T>\n\n    /**\n     * ObjectLiteral parameters.\n     */\n    private _objectLiteralParameters: ObjectLiteral | undefined\n\n    /**\n     * Indicates if parameter is used or not for this operator.\n     */\n    private _useParameter: boolean\n\n    /**\n     * Indicates if multiple parameters must be used for this operator.\n     */\n    private _multipleParameters: boolean\n\n    /**\n     * SQL generator\n     */\n    private _getSql: SqlGeneratorType | undefined\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        type: FindOperatorType,\n        value: T | FindOperator<T>,\n        useParameter: boolean = true,\n        multipleParameters: boolean = false,\n        getSql?: SqlGeneratorType,\n        objectLiteralParameters?: ObjectLiteral,\n    ) {\n        this._type = type\n        this._value = value\n        this._useParameter = useParameter\n        this._multipleParameters = multipleParameters\n        this._getSql = getSql\n        this._objectLiteralParameters = objectLiteralParameters\n    }\n\n    // -------------------------------------------------------------------------\n    // Accessors\n    // -------------------------------------------------------------------------\n\n    /**\n     * Indicates if parameter is used or not for this operator.\n     * Extracts final value if value is another find operator.\n     */\n    get useParameter(): boolean {\n        if (InstanceChecker.isFindOperator(this._value))\n            return this._value.useParameter\n\n        return this._useParameter\n    }\n\n    /**\n     * Indicates if multiple parameters must be used for this operator.\n     * Extracts final value if value is another find operator.\n     */\n    get multipleParameters(): boolean {\n        if (InstanceChecker.isFindOperator(this._value))\n            return this._value.multipleParameters\n\n        return this._multipleParameters\n    }\n\n    /**\n     * Gets the Type of this FindOperator\n     */\n    get type(): FindOperatorType {\n        return this._type\n    }\n\n    /**\n     * Gets the final value needs to be used as parameter value.\n     */\n    get value(): T {\n        if (InstanceChecker.isFindOperator(this._value))\n            return this._value.value\n\n        return this._value\n    }\n\n    /**\n     * Gets ObjectLiteral parameters.\n     */\n    get objectLiteralParameters(): ObjectLiteral | undefined {\n        if (InstanceChecker.isFindOperator(this._value))\n            return this._value.objectLiteralParameters\n\n        return this._objectLiteralParameters\n    }\n\n    /**\n     * Gets the child FindOperator if it exists\n     */\n    get child(): FindOperator<T> | undefined {\n        if (InstanceChecker.isFindOperator(this._value)) return this._value\n\n        return undefined\n    }\n\n    /**\n     * Gets the SQL generator\n     */\n    get getSql(): SqlGeneratorType | undefined {\n        if (InstanceChecker.isFindOperator(this._value))\n            return this._value.getSql\n\n        return this._getSql\n    }\n\n    transformValue(transformer: ValueTransformer | ValueTransformer[]) {\n        if (this._value instanceof FindOperator) {\n            this._value.transformValue(transformer)\n        } else {\n            this._value =\n                Array.isArray(this._value) && this._multipleParameters\n                    ? this._value.map(\n                          (v: any) =>\n                              transformer &&\n                              ApplyValueTransformers.transformTo(\n                                  transformer,\n                                  v,\n                              ),\n                      )\n                    : ApplyValueTransformers.transformTo(\n                          transformer,\n                          this._value,\n                      )\n        }\n    }\n}\n"], "sourceRoot": ".."}