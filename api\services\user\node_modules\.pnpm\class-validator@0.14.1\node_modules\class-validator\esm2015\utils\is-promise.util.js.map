{"version": 3, "file": "is-promise.util.js", "sourceRoot": "", "sources": ["../../../src/utils/is-promise.util.ts"], "names": [], "mappings": "AAAA,wGAAwG;AAExG,MAAM,UAAU,SAAS,CAAU,CAAM;IACvC,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC;AAC7E,CAAC", "sourcesContent": ["// https://github.com/TylorS/typed-is-promise/blob/abf1514e1b6961adfc75765476b0debb96b2c3ae/src/index.ts\n\nexport function isPromise<T = any>(p: any): p is Promise<T> {\n  return p !== null && typeof p === 'object' && typeof p.then === 'function';\n}\n"]}