{"version": 3, "sources": ["../../src/decorator/columns/ViewColumn.ts"], "names": [], "mappings": ";;AAOA,gCASC;AAhBD,2CAAsD;AAItD;;GAEG;AACH,SAAgB,UAAU,CAAC,OAA2B;IAClD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO,IAAI,EAAE;SACH,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "ViewColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ViewColumnOptions } from \"../options/ViewColumnOptions\"\n\n/**\n * ViewColumn decorator is used to mark a specific class property as a view column.\n */\nexport function ViewColumn(options?: ViewColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"regular\",\n            options: options || {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}