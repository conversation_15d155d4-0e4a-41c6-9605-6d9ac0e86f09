{"name": "recursive-watch", "version": "1.1.4", "description": "Minimal recursive file watcher", "main": "index.js", "dependencies": {"ttl": "^1.3.0"}, "devDependencies": {"standard": "^8.6.0"}, "scripts": {"test": "standard"}, "bin": {"recursive-watch": "./bin.js"}, "repository": {"type": "git", "url": "https://github.com/mafintosh/recursive-watch.git"}, "author": "<PERSON> (@mafintosh)", "license": "MIT", "bugs": {"url": "https://github.com/mafintosh/recursive-watch/issues"}, "homepage": "https://github.com/mafintosh/recursive-watch"}