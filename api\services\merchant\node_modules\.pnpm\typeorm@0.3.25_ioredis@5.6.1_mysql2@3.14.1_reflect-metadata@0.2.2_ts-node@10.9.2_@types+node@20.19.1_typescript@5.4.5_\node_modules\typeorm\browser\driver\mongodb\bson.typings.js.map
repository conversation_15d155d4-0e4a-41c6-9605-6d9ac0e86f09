{"version": 3, "sources": ["../browser/src/driver/mongodb/bson.typings.ts"], "names": [], "mappings": "AA6JA,OAAO,EAAE,IAAI,EAAE,CAAA", "file": "bson.typings.js", "sourcesContent": ["/**\n * A class representation of the BSON Binary type.\n * @public\n * @category BSONType\n */\nexport declare class Binary extends BSONValue {\n    get _bsontype(): \"Binary\"\n    /* Excluded from this release type: BSON_BINARY_SUBTYPE_DEFAULT */\n    /** Initial buffer default size */\n    static readonly BUFFER_SIZE = 256\n    /** Default BSON type */\n    static readonly SUBTYPE_DEFAULT = 0\n    /** Function BSON type */\n    static readonly SUBTYPE_FUNCTION = 1\n    /** Byte Array BSON type */\n    static readonly SUBTYPE_BYTE_ARRAY = 2\n    /** Deprecated UUID BSON type @deprecated Please use SUBTYPE_UUID */\n    static readonly SUBTYPE_UUID_OLD = 3\n    /** UUID BSON type */\n    static readonly SUBTYPE_UUID = 4\n    /** MD5 BSON type */\n    static readonly SUBTYPE_MD5 = 5\n    /** Encrypted BSON type */\n    static readonly SUBTYPE_ENCRYPTED = 6\n    /** Column BSON type */\n    static readonly SUBTYPE_COLUMN = 7\n    /** User BSON type */\n    static readonly SUBTYPE_USER_DEFINED = 128\n    buffer: Uint8Array\n    sub_type: number\n    position: number\n    /**\n     * Create a new Binary instance.\n     *\n     * This constructor can accept a string as its first argument. In this case,\n     * this string will be encoded using ISO-8859-1, **not** using UTF-8.\n     * This is almost certainly not what you want. Use `new Binary(Buffer.from(string))`\n     * instead to convert the string to a Buffer using UTF-8 first.\n     *\n     * @param buffer - a buffer object containing the binary data.\n     * @param subType - the option binary type.\n     */\n    constructor(buffer?: string | BinarySequence, subType?: number)\n    /**\n     * Updates this binary with byte_value.\n     *\n     * @param byteValue - a single byte we wish to write.\n     */\n    put(byteValue: string | number | Uint8Array | number[]): void\n    /**\n     * Writes a buffer or string to the binary.\n     *\n     * @param sequence - a string or buffer to be written to the Binary BSON object.\n     * @param offset - specify the binary of where to write the content.\n     */\n    write(sequence: string | BinarySequence, offset: number): void\n    /**\n     * Reads **length** bytes starting at **position**.\n     *\n     * @param position - read from the given position in the Binary.\n     * @param length - the number of bytes to read.\n     */\n    read(position: number, length: number): BinarySequence\n    /**\n     * Returns the value of this binary as a string.\n     * @param asRaw - Will skip converting to a string\n     * @remarks\n     * This is handy when calling this function conditionally for some key value pairs and not others\n     */\n    value(asRaw?: boolean): string | BinarySequence\n    /** the length of the binary sequence */\n    length(): number\n    toJSON(): string\n    toString(encoding?: \"hex\" | \"base64\" | \"utf8\" | \"utf-8\"): string\n    /* Excluded from this release type: toExtendedJSON */\n    toUUID(): UUID\n    /** Creates an Binary instance from a hex digit string */\n    static createFromHexString(hex: string, subType?: number): Binary\n    /** Creates an Binary instance from a base64 string */\n    static createFromBase64(base64: string, subType?: number): Binary\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface BinaryExtended {\n    $binary: {\n        subType: string\n        base64: string\n    }\n}\n\n/** @public */\nexport declare interface BinaryExtendedLegacy {\n    $type: string\n    $binary: string\n}\n\n/** @public */\nexport declare type BinarySequence = Uint8Array | number[]\n\ndeclare namespace BSON {\n    export {\n        setInternalBufferSize,\n        serialize,\n        serializeWithBufferAndIndex,\n        deserialize,\n        calculateObjectSize,\n        deserializeStream,\n        UUIDExtended,\n        BinaryExtended,\n        BinaryExtendedLegacy,\n        BinarySequence,\n        CodeExtended,\n        DBRefLike,\n        Decimal128Extended,\n        DoubleExtended,\n        EJSONOptions,\n        Int32Extended,\n        LongExtended,\n        MaxKeyExtended,\n        MinKeyExtended,\n        ObjectIdExtended,\n        ObjectIdLike,\n        BSONRegExpExtended,\n        BSONRegExpExtendedLegacy,\n        BSONSymbolExtended,\n        LongWithoutOverrides,\n        TimestampExtended,\n        TimestampOverrides,\n        LongWithoutOverridesClass,\n        SerializeOptions,\n        DeserializeOptions,\n        Code,\n        BSONSymbol,\n        DBRef,\n        Binary,\n        ObjectId,\n        UUID,\n        Long,\n        Timestamp,\n        Double,\n        Int32,\n        MinKey,\n        MaxKey,\n        BSONRegExp,\n        Decimal128,\n        BSONValue,\n        BSONError,\n        BSONVersionError,\n        BSONRuntimeError,\n        BSONType,\n        EJSON,\n        Document,\n        CalculateObjectSizeOptions,\n    }\n}\nexport { BSON }\n\n/**\n * @public\n * @category Error\n *\n * `BSONError` objects are thrown when BSON ecounters an error.\n *\n * This is the parent class for all the other errors thrown by this library.\n */\nexport declare class BSONError extends Error {\n    /* Excluded from this release type: bsonError */\n    get name(): string\n    constructor(message: string)\n    /**\n     * @public\n     *\n     * All errors thrown from the BSON library inherit from `BSONError`.\n     * This method can assist with determining if an error originates from the BSON library\n     * even if it does not pass an `instanceof` check against this class' constructor.\n     *\n     * @param value - any javascript value that needs type checking\n     */\n    static isBSONError(value: unknown): value is BSONError\n}\n\n/**\n * A class representation of the BSON RegExp type.\n * @public\n * @category BSONType\n */\nexport declare class BSONRegExp extends BSONValue {\n    get _bsontype(): \"BSONRegExp\"\n    pattern: string\n    options: string\n    /**\n     * @param pattern - The regular expression pattern to match\n     * @param options - The regular expression options\n     */\n    constructor(pattern: string, options?: string)\n    static parseOptions(options?: string): string\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface BSONRegExpExtended {\n    $regularExpression: {\n        pattern: string\n        options: string\n    }\n}\n\n/** @public */\nexport declare interface BSONRegExpExtendedLegacy {\n    $regex: string | BSONRegExp\n    $options: string\n}\n\n/**\n * @public\n * @category Error\n *\n * An error generated when BSON functions encounter an unexpected input\n * or reaches an unexpected/invalid internal state\n *\n */\nexport declare class BSONRuntimeError extends BSONError {\n    get name(): \"BSONRuntimeError\"\n    constructor(message: string)\n}\n\n/**\n * A class representation of the BSON Symbol type.\n * @public\n * @category BSONType\n */\nexport declare class BSONSymbol extends BSONValue {\n    get _bsontype(): \"BSONSymbol\"\n    value: string\n    /**\n     * @param value - the string representing the symbol.\n     */\n    constructor(value: string)\n    /** Access the wrapped string value. */\n    valueOf(): string\n    toString(): string\n    inspect(): string\n    toJSON(): string\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n}\n\n/** @public */\nexport declare interface BSONSymbolExtended {\n    $symbol: string\n}\n\n/** @public */\nexport declare const BSONType: Readonly<{\n    readonly double: 1\n    readonly string: 2\n    readonly object: 3\n    readonly array: 4\n    readonly binData: 5\n    readonly undefined: 6\n    readonly objectId: 7\n    readonly bool: 8\n    readonly date: 9\n    readonly null: 10\n    readonly regex: 11\n    readonly dbPointer: 12\n    readonly javascript: 13\n    readonly symbol: 14\n    readonly javascriptWithScope: 15\n    readonly int: 16\n    readonly timestamp: 17\n    readonly long: 18\n    readonly decimal: 19\n    readonly minKey: -1\n    readonly maxKey: 127\n}>\n\n/** @public */\nexport declare type BSONType = (typeof BSONType)[keyof typeof BSONType]\n\n/** @public */\nexport declare abstract class BSONValue {\n    /** @public */\n    abstract get _bsontype(): string\n    /** @public */\n    abstract inspect(): string\n    /* Excluded from this release type: toExtendedJSON */\n}\n\n/**\n * @public\n * @category Error\n */\nexport declare class BSONVersionError extends BSONError {\n    get name(): \"BSONVersionError\"\n    constructor()\n}\n\n/**\n * Calculate the bson size for a passed in Javascript object.\n *\n * @param object - the Javascript object to calculate the BSON byte size for\n * @returns size of BSON object in bytes\n * @public\n */\nexport declare function calculateObjectSize(\n    object: Document,\n    options?: CalculateObjectSizeOptions,\n): number\n\n/** @public */\nexport declare type CalculateObjectSizeOptions = Pick<\n    SerializeOptions,\n    \"serializeFunctions\" | \"ignoreUndefined\"\n>\n\n/**\n * A class representation of the BSON Code type.\n * @public\n * @category BSONType\n */\nexport declare class Code extends BSONValue {\n    get _bsontype(): \"Code\"\n    code: string\n    scope: Document | null\n    /**\n     * @param code - a string or function.\n     * @param scope - an optional scope for the function.\n     */\n    constructor(code: string | Function, scope?: Document | null)\n    toJSON(): {\n        code: string\n        scope?: Document\n    }\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface CodeExtended {\n    $code: string\n    $scope?: Document\n}\n\n/**\n * A class representation of the BSON DBRef type.\n * @public\n * @category BSONType\n */\nexport declare class DBRef extends BSONValue {\n    get _bsontype(): \"DBRef\"\n    collection: string\n    oid: ObjectId\n    db?: string\n    fields: Document\n    /**\n     * @param collection - the collection name.\n     * @param oid - the reference ObjectId.\n     * @param db - optional db name, if omitted the reference is local to the current db.\n     */\n    constructor(\n        collection: string,\n        oid: ObjectId,\n        db?: string,\n        fields?: Document,\n    )\n    /* Excluded from this release type: namespace */\n    /* Excluded from this release type: namespace */\n    toJSON(): DBRefLike & Document\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface DBRefLike {\n    $ref: string\n    $id: ObjectId\n    $db?: string\n}\n\n/**\n * A class representation of the BSON Decimal128 type.\n * @public\n * @category BSONType\n */\nexport declare class Decimal128 extends BSONValue {\n    get _bsontype(): \"Decimal128\"\n    readonly bytes: Uint8Array\n    /**\n     * @param bytes - a buffer containing the raw Decimal128 bytes in little endian order,\n     *                or a string representation as returned by .toString()\n     */\n    constructor(bytes: Uint8Array | string)\n    /**\n     * Create a Decimal128 instance from a string representation\n     *\n     * @param representation - a numeric string representation.\n     */\n    static fromString(representation: string): Decimal128\n    /** Create a string representation of the raw Decimal128 value */\n    toString(): string\n    toJSON(): Decimal128Extended\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface Decimal128Extended {\n    $numberDecimal: string\n}\n\n/**\n * Deserialize data as BSON.\n *\n * @param buffer - the buffer containing the serialized set of BSON documents.\n * @returns returns the deserialized Javascript Object.\n * @public\n */\nexport declare function deserialize(\n    buffer: Uint8Array,\n    options?: DeserializeOptions,\n): Document\n\n/** @public */\nexport declare interface DeserializeOptions {\n    /** when deserializing a Long will return as a BigInt. */\n    useBigInt64?: boolean\n    /** when deserializing a Long will fit it into a Number if it's smaller than 53 bits. */\n    promoteLongs?: boolean\n    /** when deserializing a Binary will return it as a node.js Buffer instance. */\n    promoteBuffers?: boolean\n    /** when deserializing will promote BSON values to their Node.js closest equivalent types. */\n    promoteValues?: boolean\n    /** allow to specify if there what fields we wish to return as unserialized raw buffer. */\n    fieldsAsRaw?: Document\n    /** return BSON regular expressions as BSONRegExp instances. */\n    bsonRegExp?: boolean\n    /** allows the buffer to be larger than the parsed BSON object. */\n    allowObjectSmallerThanBufferSize?: boolean\n    /** Offset into buffer to begin reading document from */\n    index?: number\n    raw?: boolean\n    /** Allows for opt-out utf-8 validation for all keys or\n     * specified keys. Must be all true or all false.\n     *\n     * @example\n     * ```js\n     * // disables validation on all keys\n     *  validation: { utf8: false }\n     *\n     * // enables validation only on specified keys a, b, and c\n     *  validation: { utf8: { a: true, b: true, c: true } }\n     *\n     *  // disables validation only on specified keys a, b\n     *  validation: { utf8: { a: false, b: false } }\n     * ```\n     */\n    validation?: {\n        utf8: boolean | Record<string, true> | Record<string, false>\n    }\n}\n\n/**\n * Deserialize stream data as BSON documents.\n *\n * @param data - the buffer containing the serialized set of BSON documents.\n * @param startIndex - the start index in the data Buffer where the deserialization is to start.\n * @param numberOfDocuments - number of documents to deserialize.\n * @param documents - an array where to store the deserialized documents.\n * @param docStartIndex - the index in the documents array from where to start inserting documents.\n * @param options - additional options used for the deserialization.\n * @returns next index in the buffer after deserialization **x** numbers of documents.\n * @public\n */\nexport declare function deserializeStream(\n    data: Uint8Array | ArrayBuffer,\n    startIndex: number,\n    numberOfDocuments: number,\n    documents: Document[],\n    docStartIndex: number,\n    options: DeserializeOptions,\n): number\n\n/** @public */\nexport declare interface Document {\n    [key: string]: any\n}\n\n/**\n * A class representation of the BSON Double type.\n * @public\n * @category BSONType\n */\nexport declare class Double extends BSONValue {\n    get _bsontype(): \"Double\"\n    value: number\n    /**\n     * Create a Double type\n     *\n     * @param value - the number we want to represent as a double.\n     */\n    constructor(value: number)\n    /**\n     * Access the number value.\n     *\n     * @returns returns the wrapped double number.\n     */\n    valueOf(): number\n    toJSON(): number\n    toString(radix?: number): string\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface DoubleExtended {\n    $numberDouble: string\n}\n\n/** @public */\nexport declare const EJSON: {\n    parse: typeof parse\n    stringify: typeof stringify\n    serialize: typeof EJSONserialize\n    deserialize: typeof EJSONdeserialize\n}\n\n/**\n * Deserializes an Extended JSON object into a plain JavaScript object with native/BSON types\n *\n * @param ejson - The Extended JSON object to deserialize\n * @param options - Optional settings passed to the parse method\n */\ndeclare function EJSONdeserialize(ejson: Document, options?: EJSONOptions): any\n\n/** @public */\nexport declare type EJSONOptions = {\n    /** Output using the Extended JSON v1 spec */\n    legacy?: boolean\n    /** Enable Extended JSON's `relaxed` mode, which attempts to return native JS types where possible, rather than BSON types */\n    relaxed?: boolean\n    /** Enable native bigint support */\n    useBigInt64?: boolean\n}\n\n/**\n * Serializes an object to an Extended JSON string, and reparse it as a JavaScript object.\n *\n * @param value - The object to serialize\n * @param options - Optional settings passed to the `stringify` function\n */\ndeclare function EJSONserialize(value: any, options?: EJSONOptions): Document\n\n/**\n * A class representation of a BSON Int32 type.\n * @public\n * @category BSONType\n */\nexport declare class Int32 extends BSONValue {\n    get _bsontype(): \"Int32\"\n    value: number\n    /**\n     * Create an Int32 type\n     *\n     * @param value - the number we want to represent as an int32.\n     */\n    constructor(value: number | string)\n    /**\n     * Access the number value.\n     *\n     * @returns returns the wrapped int32 number.\n     */\n    valueOf(): number\n    toString(radix?: number): string\n    toJSON(): number\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface Int32Extended {\n    $numberInt: string\n}\n\ndeclare const kId: unique symbol\n\n/**\n * A class representing a 64-bit integer\n * @public\n * @category BSONType\n * @remarks\n * The internal representation of a long is the two given signed, 32-bit values.\n * We use 32-bit pieces because these are the size of integers on which\n * Javascript performs bit-operations.  For operations like addition and\n * multiplication, we split each number into 16 bit pieces, which can easily be\n * multiplied within Javascript's floating-point representation without overflow\n * or change in sign.\n * In the algorithms below, we frequently reduce the negative case to the\n * positive case by negating the input(s) and then post-processing the result.\n * Note that we must ALWAYS check specially whether those values are MIN_VALUE\n * (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n * a positive number, it overflows back into a negative).  Not handling this\n * case would often result in infinite recursion.\n * Common constant values ZERO, ONE, NEG_ONE, etc. are found as static properties on this class.\n */\nexport declare class Long extends BSONValue {\n    get _bsontype(): \"Long\"\n    /** An indicator used to reliably determine if an object is a Long or not. */\n    get __isLong__(): boolean\n    /**\n     * The high 32 bits as a signed value.\n     */\n    high: number\n    /**\n     * The low 32 bits as a signed value.\n     */\n    low: number\n    /**\n     * Whether unsigned or not.\n     */\n    unsigned: boolean\n    /**\n     * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n     *  See the from* functions below for more convenient ways of constructing Longs.\n     *\n     * Acceptable signatures are:\n     * - Long(low, high, unsigned?)\n     * - Long(bigint, unsigned?)\n     * - Long(string, unsigned?)\n     *\n     * @param low - The low (signed) 32 bits of the long\n     * @param high - The high (signed) 32 bits of the long\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     */\n    constructor(\n        low?: number | bigint | string,\n        high?: number | boolean,\n        unsigned?: boolean,\n    )\n    static TWO_PWR_24: Long\n    /** Maximum unsigned value. */\n    static MAX_UNSIGNED_VALUE: Long\n    /** Signed zero */\n    static ZERO: Long\n    /** Unsigned zero. */\n    static UZERO: Long\n    /** Signed one. */\n    static ONE: Long\n    /** Unsigned one. */\n    static UONE: Long\n    /** Signed negative one. */\n    static NEG_ONE: Long\n    /** Maximum signed value. */\n    static MAX_VALUE: Long\n    /** Minimum signed value. */\n    static MIN_VALUE: Long\n    /**\n     * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits.\n     * Each is assumed to use 32 bits.\n     * @param lowBits - The low 32 bits\n     * @param highBits - The high 32 bits\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromBits(lowBits: number, highBits: number, unsigned?: boolean): Long\n    /**\n     * Returns a Long representing the given 32 bit integer value.\n     * @param value - The 32 bit integer in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromInt(value: number, unsigned?: boolean): Long\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @param value - The number in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromNumber(value: number, unsigned?: boolean): Long\n    /**\n     * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n     * @param value - The number in question\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromBigInt(value: bigint, unsigned?: boolean): Long\n    /**\n     * Returns a Long representation of the given string, written using the specified radix.\n     * @param str - The textual representation of the Long\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @param radix - The radix in which the text is written (2-36), defaults to 10\n     * @returns The corresponding Long value\n     */\n    static fromString(str: string, unsigned?: boolean, radix?: number): Long\n    /**\n     * Creates a Long from its byte representation.\n     * @param bytes - Byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @param le - Whether little or big endian, defaults to big endian\n     * @returns The corresponding Long value\n     */\n    static fromBytes(bytes: number[], unsigned?: boolean, le?: boolean): Long\n    /**\n     * Creates a Long from its little endian byte representation.\n     * @param bytes - Little endian byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromBytesLE(bytes: number[], unsigned?: boolean): Long\n    /**\n     * Creates a Long from its big endian byte representation.\n     * @param bytes - Big endian byte representation\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     * @returns The corresponding Long value\n     */\n    static fromBytesBE(bytes: number[], unsigned?: boolean): Long\n    /**\n     * Tests if the specified object is a Long.\n     */\n    static isLong(value: unknown): value is Long\n    /**\n     * Converts the specified value to a Long.\n     * @param unsigned - Whether unsigned or not, defaults to signed\n     */\n    static fromValue(\n        val:\n            | number\n            | string\n            | {\n                  low: number\n                  high: number\n                  unsigned?: boolean\n              },\n        unsigned?: boolean,\n    ): Long\n    /** Returns the sum of this and the specified Long. */\n    add(addend: string | number | Long | Timestamp): Long\n    /**\n     * Returns the sum of this and the specified Long.\n     * @returns Sum\n     */\n    and(other: string | number | Long | Timestamp): Long\n    /**\n     * Compares this Long's value with the specified's.\n     * @returns 0 if they are the same, 1 if the this is greater and -1 if the given one is greater\n     */\n    compare(other: string | number | Long | Timestamp): 0 | 1 | -1\n    /** This is an alias of {@link Long.compare} */\n    comp(other: string | number | Long | Timestamp): 0 | 1 | -1\n    /**\n     * Returns this Long divided by the specified. The result is signed if this Long is signed or unsigned if this Long is unsigned.\n     * @returns Quotient\n     */\n    divide(divisor: string | number | Long | Timestamp): Long\n    /**This is an alias of {@link Long.divide} */\n    div(divisor: string | number | Long | Timestamp): Long\n    /**\n     * Tests if this Long's value equals the specified's.\n     * @param other - Other value\n     */\n    equals(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.equals} */\n    eq(other: string | number | Long | Timestamp): boolean\n    /** Gets the high 32 bits as a signed integer. */\n    getHighBits(): number\n    /** Gets the high 32 bits as an unsigned integer. */\n    getHighBitsUnsigned(): number\n    /** Gets the low 32 bits as a signed integer. */\n    getLowBits(): number\n    /** Gets the low 32 bits as an unsigned integer. */\n    getLowBitsUnsigned(): number\n    /** Gets the number of bits needed to represent the absolute value of this Long. */\n    getNumBitsAbs(): number\n    /** Tests if this Long's value is greater than the specified's. */\n    greaterThan(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.greaterThan} */\n    gt(other: string | number | Long | Timestamp): boolean\n    /** Tests if this Long's value is greater than or equal the specified's. */\n    greaterThanOrEqual(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.greaterThanOrEqual} */\n    gte(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.greaterThanOrEqual} */\n    ge(other: string | number | Long | Timestamp): boolean\n    /** Tests if this Long's value is even. */\n    isEven(): boolean\n    /** Tests if this Long's value is negative. */\n    isNegative(): boolean\n    /** Tests if this Long's value is odd. */\n    isOdd(): boolean\n    /** Tests if this Long's value is positive. */\n    isPositive(): boolean\n    /** Tests if this Long's value equals zero. */\n    isZero(): boolean\n    /** Tests if this Long's value is less than the specified's. */\n    lessThan(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long#lessThan}. */\n    lt(other: string | number | Long | Timestamp): boolean\n    /** Tests if this Long's value is less than or equal the specified's. */\n    lessThanOrEqual(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.lessThanOrEqual} */\n    lte(other: string | number | Long | Timestamp): boolean\n    /** Returns this Long modulo the specified. */\n    modulo(divisor: string | number | Long | Timestamp): Long\n    /** This is an alias of {@link Long.modulo} */\n    mod(divisor: string | number | Long | Timestamp): Long\n    /** This is an alias of {@link Long.modulo} */\n    rem(divisor: string | number | Long | Timestamp): Long\n    /**\n     * Returns the product of this and the specified Long.\n     * @param multiplier - Multiplier\n     * @returns Product\n     */\n    multiply(multiplier: string | number | Long | Timestamp): Long\n    /** This is an alias of {@link Long.multiply} */\n    mul(multiplier: string | number | Long | Timestamp): Long\n    /** Returns the Negation of this Long's value. */\n    negate(): Long\n    /** This is an alias of {@link Long.negate} */\n    neg(): Long\n    /** Returns the bitwise NOT of this Long. */\n    not(): Long\n    /** Tests if this Long's value differs from the specified's. */\n    notEquals(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.notEquals} */\n    neq(other: string | number | Long | Timestamp): boolean\n    /** This is an alias of {@link Long.notEquals} */\n    ne(other: string | number | Long | Timestamp): boolean\n    /**\n     * Returns the bitwise OR of this Long and the specified.\n     */\n    or(other: number | string | Long): Long\n    /**\n     * Returns this Long with bits shifted to the left by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    shiftLeft(numBits: number | Long): Long\n    /** This is an alias of {@link Long.shiftLeft} */\n    shl(numBits: number | Long): Long\n    /**\n     * Returns this Long with bits arithmetically shifted to the right by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    shiftRight(numBits: number | Long): Long\n    /** This is an alias of {@link Long.shiftRight} */\n    shr(numBits: number | Long): Long\n    /**\n     * Returns this Long with bits logically shifted to the right by the given amount.\n     * @param numBits - Number of bits\n     * @returns Shifted Long\n     */\n    shiftRightUnsigned(numBits: Long | number): Long\n    /** This is an alias of {@link Long.shiftRightUnsigned} */\n    shr_u(numBits: number | Long): Long\n    /** This is an alias of {@link Long.shiftRightUnsigned} */\n    shru(numBits: number | Long): Long\n    /**\n     * Returns the difference of this and the specified Long.\n     * @param subtrahend - Subtrahend\n     * @returns Difference\n     */\n    subtract(subtrahend: string | number | Long | Timestamp): Long\n    /** This is an alias of {@link Long.subtract} */\n    sub(subtrahend: string | number | Long | Timestamp): Long\n    /** Converts the Long to a 32 bit integer, assuming it is a 32 bit integer. */\n    toInt(): number\n    /** Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa). */\n    toNumber(): number\n    /** Converts the Long to a BigInt (arbitrary precision). */\n    toBigInt(): bigint\n    /**\n     * Converts this Long to its byte representation.\n     * @param le - Whether little or big endian, defaults to big endian\n     * @returns Byte representation\n     */\n    toBytes(le?: boolean): number[]\n    /**\n     * Converts this Long to its little endian byte representation.\n     * @returns Little endian byte representation\n     */\n    toBytesLE(): number[]\n    /**\n     * Converts this Long to its big endian byte representation.\n     * @returns Big endian byte representation\n     */\n    toBytesBE(): number[]\n    /**\n     * Converts this Long to signed.\n     */\n    toSigned(): Long\n    /**\n     * Converts the Long to a string written in the specified radix.\n     * @param radix - Radix (2-36), defaults to 10\n     * @throws RangeError If `radix` is out of range\n     */\n    toString(radix?: number): string\n    /** Converts this Long to unsigned. */\n    toUnsigned(): Long\n    /** Returns the bitwise XOR of this Long and the given one. */\n    xor(other: Long | number | string): Long\n    /** This is an alias of {@link Long.isZero} */\n    eqz(): boolean\n    /** This is an alias of {@link Long.lessThanOrEqual} */\n    le(other: string | number | Long | Timestamp): boolean\n    toExtendedJSON(options?: EJSONOptions): number | LongExtended\n    static fromExtendedJSON(\n        doc: {\n            $numberLong: string\n        },\n        options?: EJSONOptions,\n    ): number | Long | bigint\n    inspect(): string\n}\n\n/** @public */\nexport declare interface LongExtended {\n    $numberLong: string\n}\n\n/** @public */\nexport declare type LongWithoutOverrides = new (\n    low: unknown,\n    high?: number | boolean,\n    unsigned?: boolean,\n) => {\n    [P in Exclude<keyof Long, TimestampOverrides>]: Long[P]\n}\n\n/** @public */\nexport declare const LongWithoutOverridesClass: LongWithoutOverrides\n\n/**\n * A class representation of the BSON MaxKey type.\n * @public\n * @category BSONType\n */\nexport declare class MaxKey extends BSONValue {\n    get _bsontype(): \"MaxKey\"\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface MaxKeyExtended {\n    $maxKey: 1\n}\n\n/**\n * A class representation of the BSON MinKey type.\n * @public\n * @category BSONType\n */\nexport declare class MinKey extends BSONValue {\n    get _bsontype(): \"MinKey\"\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface MinKeyExtended {\n    $minKey: 1\n}\n\n/**\n * A class representation of the BSON ObjectId type.\n * @public\n * @category BSONType\n */\nexport declare class ObjectId extends BSONValue {\n    get _bsontype(): \"ObjectId\"\n    /* Excluded from this release type: index */\n    static cacheHexString: boolean\n    /* Excluded from this release type: [kId] */\n    /* Excluded from this release type: __id */\n    /**\n     * Create an ObjectId type\n     *\n     * @param inputId - Can be a 24 character hex string, 12 byte binary Buffer, or a number.\n     */\n    constructor(\n        inputId?: string | number | ObjectId | ObjectIdLike | Uint8Array,\n    )\n    /**\n     * The ObjectId bytes\n     * @readonly\n     */\n    get id(): Uint8Array\n    set id(value: Uint8Array)\n    /** Returns the ObjectId id as a 24 character hex string representation */\n    toHexString(): string\n    /* Excluded from this release type: getInc */\n    /**\n     * Generate a 12 byte id buffer used in ObjectId's\n     *\n     * @param time - pass in a second based timestamp.\n     */\n    static generate(time?: number): Uint8Array\n    /**\n     * Converts the id into a 24 character hex string for printing, unless encoding is provided.\n     * @param encoding - hex or base64\n     */\n    toString(encoding?: \"hex\" | \"base64\"): string\n    /** Converts to its JSON the 24 character hex string representation. */\n    toJSON(): string\n    /**\n     * Compares the equality of this ObjectId with `otherID`.\n     *\n     * @param otherId - ObjectId instance to compare against.\n     */\n    equals(otherId: string | ObjectId | ObjectIdLike): boolean\n    /** Returns the generation date (accurate up to the second) that this ID was generated. */\n    getTimestamp(): Date\n    /* Excluded from this release type: createPk */\n    /**\n     * Creates an ObjectId from a second based number, with the rest of the ObjectId zeroed out. Used for comparisons or sorting the ObjectId.\n     *\n     * @param time - an integer number representing a number of seconds.\n     */\n    static createFromTime(time: number): ObjectId\n    /**\n     * Creates an ObjectId from a hex string representation of an ObjectId.\n     *\n     * @param hexString - create a ObjectId from a passed in 24 character hexstring.\n     */\n    static createFromHexString(hexString: string): ObjectId\n    /** Creates an ObjectId instance from a base64 string */\n    static createFromBase64(base64: string): ObjectId\n    /**\n     * Checks if a value is a valid bson ObjectId\n     *\n     * @param id - ObjectId instance to validate.\n     */\n    static isValid(\n        id: string | number | ObjectId | ObjectIdLike | Uint8Array,\n    ): boolean\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface ObjectIdExtended {\n    $oid: string\n}\n\n/** @public */\nexport declare interface ObjectIdLike {\n    id: string | Uint8Array\n    __id?: string\n    toHexString(): string\n}\n\n/**\n * Parse an Extended JSON string, constructing the JavaScript value or object described by that\n * string.\n *\n * @example\n * ```js\n * const { EJSON } = require('bson');\n * const text = '{ \"int32\": { \"$numberInt\": \"10\" } }';\n *\n * // prints { int32: { [String: '10'] _bsontype: 'Int32', value: '10' } }\n * console.log(EJSON.parse(text, { relaxed: false }));\n *\n * // prints { int32: 10 }\n * console.log(EJSON.parse(text));\n * ```\n */\ndeclare function parse(text: string, options?: EJSONOptions): any\n\n/**\n * Serialize a Javascript object.\n *\n * @param object - the Javascript object to serialize.\n * @returns Buffer object containing the serialized object.\n * @public\n */\nexport declare function serialize(\n    object: Document,\n    options?: SerializeOptions,\n): Uint8Array\n\n/** @public */\nexport declare interface SerializeOptions {\n    /** the serializer will check if keys are valid. */\n    checkKeys?: boolean\n    /** serialize the javascript functions **(default:false)**. */\n    serializeFunctions?: boolean\n    /** serialize will not emit undefined fields **(default:true)** */\n    ignoreUndefined?: boolean\n    /* Excluded from this release type: minInternalBufferSize */\n    /** the index in the buffer where we wish to start serializing into */\n    index?: number\n}\n\n/**\n * Serialize a Javascript object using a predefined Buffer and index into the buffer,\n * useful when pre-allocating the space for serialization.\n *\n * @param object - the Javascript object to serialize.\n * @param finalBuffer - the Buffer you pre-allocated to store the serialized BSON object.\n * @returns the index pointing to the last written byte in the buffer.\n * @public\n */\nexport declare function serializeWithBufferAndIndex(\n    object: Document,\n    finalBuffer: Uint8Array,\n    options?: SerializeOptions,\n): number\n\n/**\n * Sets the size of the internal serialization buffer.\n *\n * @param size - The desired size for the internal serialization buffer\n * @public\n */\nexport declare function setInternalBufferSize(size: number): void\n\n/**\n * Converts a BSON document to an Extended JSON string, optionally replacing values if a replacer\n * function is specified or optionally including only the specified properties if a replacer array\n * is specified.\n *\n * @param value - The value to convert to extended JSON\n * @param replacer - A function that alters the behavior of the stringification process, or an array of String and Number objects that serve as a whitelist for selecting/filtering the properties of the value object to be included in the JSON string. If this value is null or not provided, all properties of the object are included in the resulting JSON string\n * @param space - A String or Number object that's used to insert white space into the output JSON string for readability purposes.\n * @param options - Optional settings\n *\n * @example\n * ```js\n * const { EJSON } = require('bson');\n * const Int32 = require('mongodb').Int32;\n * const doc = { int32: new Int32(10) };\n *\n * // prints '{\"int32\":{\"$numberInt\":\"10\"}}'\n * console.log(EJSON.stringify(doc, { relaxed: false }));\n *\n * // prints '{\"int32\":10}'\n * console.log(EJSON.stringify(doc));\n * ```\n */\ndeclare function stringify(\n    value: any,\n    replacer?:\n        | (number | string)[]\n        | ((this: any, key: string, value: any) => any)\n        | EJSONOptions,\n    space?: string | number,\n    options?: EJSONOptions,\n): string\n\n/**\n * @public\n * @category BSONType\n * */\nexport declare class Timestamp extends LongWithoutOverridesClass {\n    get _bsontype(): \"Timestamp\"\n    static readonly MAX_VALUE: Long\n    /**\n     * @param int - A 64-bit bigint representing the Timestamp.\n     */\n    constructor(int: bigint)\n    /**\n     * @param long - A 64-bit Long representing the Timestamp.\n     */\n    constructor(long: Long)\n    /**\n     * @param value - A pair of two values indicating timestamp and increment.\n     */\n    constructor(value: { t: number; i: number })\n    toJSON(): {\n        $timestamp: string\n    }\n    /** Returns a Timestamp represented by the given (32-bit) integer value. */\n    static fromInt(value: number): Timestamp\n    /** Returns a Timestamp representing the given number value, provided that it is a finite number. Otherwise, zero is returned. */\n    static fromNumber(value: number): Timestamp\n    /**\n     * Returns a Timestamp for the given high and low bits. Each is assumed to use 32 bits.\n     *\n     * @param lowBits - the low 32-bits.\n     * @param highBits - the high 32-bits.\n     */\n    static fromBits(lowBits: number, highBits: number): Timestamp\n    /**\n     * Returns a Timestamp from the given string, optionally using the given radix.\n     *\n     * @param str - the textual representation of the Timestamp.\n     * @param optRadix - the radix in which the text is written.\n     */\n    static fromString(str: string, optRadix: number): Timestamp\n    /* Excluded from this release type: toExtendedJSON */\n    /* Excluded from this release type: fromExtendedJSON */\n    inspect(): string\n}\n\n/** @public */\nexport declare interface TimestampExtended {\n    $timestamp: {\n        t: number\n        i: number\n    }\n}\n\n/** @public */\nexport declare type TimestampOverrides =\n    | \"_bsontype\"\n    | \"toExtendedJSON\"\n    | \"fromExtendedJSON\"\n    | \"inspect\"\n\n/**\n * A class representation of the BSON UUID type.\n * @public\n */\nexport declare class UUID extends Binary {\n    static cacheHexString: boolean\n    /* Excluded from this release type: __id */\n    /**\n     * Create an UUID type\n     *\n     * @param input - Can be a 32 or 36 character hex string (dashes excluded/included) or a 16 byte binary Buffer.\n     */\n    constructor(input?: string | Uint8Array | UUID)\n    /**\n     * The UUID bytes\n     * @readonly\n     */\n    get id(): Uint8Array\n    set id(value: Uint8Array)\n    /**\n     * Returns the UUID id as a 32 or 36 character hex string representation, excluding/including dashes (defaults to 36 character dash separated)\n     * @param includeDashes - should the string exclude dash-separators.\n     * */\n    toHexString(includeDashes?: boolean): string\n    /**\n     * Converts the id into a 36 character (dashes included) hex string, unless a encoding is specified.\n     */\n    toString(encoding?: \"hex\" | \"base64\"): string\n    /**\n     * Converts the id into its JSON string representation.\n     * A 36 character (dashes included) hex string in the format: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\n     */\n    toJSON(): string\n    /**\n     * Compares the equality of this UUID with `otherID`.\n     *\n     * @param otherId - UUID instance to compare against.\n     */\n    equals(otherId: string | Uint8Array | UUID): boolean\n    /**\n     * Creates a Binary instance from the current UUID.\n     */\n    toBinary(): Binary\n    /**\n     * Generates a populated buffer containing a v4 uuid\n     */\n    static generate(): Uint8Array\n    /**\n     * Checks if a value is a valid bson UUID\n     * @param input - UUID, string or Buffer to validate.\n     */\n    static isValid(input: string | Uint8Array | UUID): boolean\n    /**\n     * Creates an UUID from a hex string representation of an UUID.\n     * @param hexString - 32 or 36 character hex string (dashes excluded/included).\n     */\n    static createFromHexString(hexString: string): UUID\n    /** Creates an UUID from a base64 string representation of an UUID. */\n    static createFromBase64(base64: string): UUID\n    inspect(): string\n}\n\n/** @public */\nexport declare type UUIDExtended = {\n    $uuid: string\n}\n\nexport {}\n"], "sourceRoot": "../.."}