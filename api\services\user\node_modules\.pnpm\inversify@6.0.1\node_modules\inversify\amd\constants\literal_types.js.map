{"version": 3, "file": "literal_types.js", "sourceRoot": "", "sources": ["../../src/constants/literal_types.ts"], "names": [], "mappings": ";;;;IAEA,IAAM,gBAAgB,GAAgC;QACpD,OAAO,EAAE,SAAS;QAClB,SAAS,EAAE,WAAW;QACtB,SAAS,EAAE,WAAW;KACvB,CAAC;IAmBO,4CAAgB;IAjBzB,IAAM,eAAe,GAA+B;QAClD,aAAa,EAAE,eAAe;QAC9B,WAAW,EAAE,aAAa;QAC1B,YAAY,EAAE,cAAc;QAC5B,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,UAAU;QACpB,QAAQ,EAAE,UAAU;QACpB,OAAO,EAAE,SAAS;QAClB,QAAQ,EAAE,UAAU;KACrB,CAAC;IAQyB,0CAAe;IAN1C,IAAM,cAAc,GAA8B;QAChD,aAAa,EAAE,eAAe;QAC9B,mBAAmB,EAAE,qBAAqB;QAC1C,QAAQ,EAAE,UAAU;KACrB,CAAC;IAE0C,wCAAc"}