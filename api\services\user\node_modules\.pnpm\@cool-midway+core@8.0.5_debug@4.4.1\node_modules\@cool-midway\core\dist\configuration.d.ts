import { ILifeCycle, ILogger, IMidwayApplication, IMidwayContainer, MidwayWebRouterService } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import { CoolEventManager } from './event';
export declare class CoolConfiguration implements ILifeCycle {
    coreLogger: ILogger;
    app: koa.Application;
    coolEventManager: CoolEventManager;
    allConfig: any;
    webRouterService: MidwayWebRouterService;
    onReady(container: IMidwayContainer): Promise<void>;
    onConfigLoad(container: IMidwayContainer, app: IMidwayApplication): Promise<void>;
    onServerReady(container: IMidwayContainer): Promise<void>;
}
