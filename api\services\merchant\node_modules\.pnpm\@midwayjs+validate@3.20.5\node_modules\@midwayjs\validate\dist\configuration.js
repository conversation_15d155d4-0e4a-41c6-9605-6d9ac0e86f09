"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ValidateConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const service_1 = require("./service");
const i18n = require("@midwayjs/i18n");
const pipe_1 = require("./pipe");
const constants_1 = require("./constants");
let ValidateConfiguration = class ValidateConfiguration {
    async init() {
        this.decoratorService.registerParameterHandler(constants_1.VALID_KEY, ({ parameterIndex, originParamType, originArgs, metadata }) => {
            if (!metadata.schema) {
                metadata.schema = this.validateService.getSchema(originParamType);
            }
            return originArgs[parameterIndex];
        });
    }
    async onReady(container) {
        await container.getAsync(pipe_1.ValidationPipe);
        await container.getAsync(pipe_1.ParseIntPipe);
        await container.getAsync(pipe_1.ParseBoolPipe);
        await container.getAsync(pipe_1.ParseFloatPipe);
        await container.getAsync(pipe_1.DecoratorValidPipe);
        // register web param default pipe
        this.decoratorService.registerParameterPipes(core_1.WEB_ROUTER_PARAM_KEY, [
            pipe_1.ValidationPipe,
        ]);
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayDecoratorService)
], ValidateConfiguration.prototype, "decoratorService", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", service_1.ValidateService)
], ValidateConfiguration.prototype, "validateService", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ValidateConfiguration.prototype, "init", null);
ValidateConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'validate',
        imports: [i18n],
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], ValidateConfiguration);
exports.ValidateConfiguration = ValidateConfiguration;
//# sourceMappingURL=configuration.js.map