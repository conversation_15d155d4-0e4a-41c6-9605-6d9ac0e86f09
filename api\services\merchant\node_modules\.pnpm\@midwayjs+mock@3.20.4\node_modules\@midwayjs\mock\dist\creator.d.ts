import { IMidwayApplication, IMidwayFramework } from '@midwayjs/core';
import { ComponentModule, MockAppConfigurationOptions, IBootstrapAppStarter, MockBootstrapOptions } from './interface';
export declare function create<T extends IMidwayFramework<any, any, any, any, any>>(appDir: string | MockAppConfigurationOptions, options?: MockAppConfigurationOptions, customFramework?: {
    new (...args: any[]): T;
} | ComponentModule): Promise<T>;
export declare function createApp<T extends IMidwayFramework<any, any, any, any, any>>(baseDir?: string, options?: MockAppConfigurationOptions, customFramework?: {
    new (...args: any[]): T;
} | ComponentModule): Promise<ReturnType<T['getApplication']>>;
export declare function close(app: IMidwayApplication<any> | {
    close: (...args: any[]) => void;
}, options?: {
    cleanLogsDir?: boolean;
    cleanTempDir?: boolean;
    sleep?: number;
}): Promise<void>;
export declare function createFunctionApp<T extends IMidwayFramework<any, any, any, any, any>, Y = ReturnType<T['getApplication']>>(baseDir?: string | MockAppConfigurationOptions, options?: MockAppConfigurationOptions, customFrameworkModule?: {
    new (...args: any[]): T;
} | ComponentModule): Promise<Y>;
/**
 * Create a real project but not ready or a virtual project
 * @param baseDirOrOptions
 * @param options
 */
export declare function createLightApp(baseDirOrOptions: string | MockAppConfigurationOptions, options?: MockAppConfigurationOptions): Promise<IMidwayApplication>;
export declare function createBootstrap(entryFile: string, options?: MockBootstrapOptions): Promise<IBootstrapAppStarter>;
//# sourceMappingURL=creator.d.ts.map