#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules/midway-version/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules/midway-version/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules/midway-version/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules/midway-version/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/midway-version@1.4.0/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../midway-version@1.4.0/node_modules/midway-version/bin/midway-version.js" "$@"
else
  exec node  "$basedir/../../../../../../midway-version@1.4.0/node_modules/midway-version/bin/midway-version.js" "$@"
fi
