{"version": 3, "sources": ["../../src/error/DriverPackageNotInstalledError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,8BAA+B,SAAQ,2BAAY;IAC5D,YAAY,UAAkB,EAAE,WAAmB;QAC/C,KAAK,CACD,GAAG,UAAU,yCAAyC;YAClD,kCAAkC,WAAW,SAAS,CAC7D,CAAA;IACL,CAAC;CACJ;AAPD,wEAOC", "file": "DriverPackageNotInstalledError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when required driver's package is not installed.\n */\nexport class DriverPackageNotInstalledError extends TypeORMError {\n    constructor(driverName: string, packageName: string) {\n        super(\n            `${driverName} package has not been found installed. ` +\n                `Try to install it: npm install ${packageName} --save`,\n        )\n    }\n}\n"], "sourceRoot": ".."}