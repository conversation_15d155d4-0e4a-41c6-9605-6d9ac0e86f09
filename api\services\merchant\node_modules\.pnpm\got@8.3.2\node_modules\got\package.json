{"name": "got", "version": "8.3.2", "description": "Simplified HTTP requests", "license": "MIT", "repository": "sindresorhus/got", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "github.com/floatdrop"}, {"name": "<PERSON>", "email": "alex.tesfamicha<PERSON>@gmail.com", "url": "alextes.me"}], "engines": {"node": ">=4"}, "scripts": {"test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "files": ["index.js", "errors.js"], "keywords": ["http", "https", "get", "got", "url", "uri", "request", "util", "utility", "simple", "curl", "wget", "fetch", "net", "network", "electron"], "dependencies": {"@sindresorhus/is": "^0.7.0", "cacheable-request": "^2.1.1", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "into-stream": "^3.1.0", "is-retry-allowed": "^1.1.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "mimic-response": "^1.0.0", "p-cancelable": "^0.4.0", "p-timeout": "^2.0.1", "pify": "^3.0.0", "safe-buffer": "^5.1.1", "timed-out": "^4.0.1", "url-parse-lax": "^3.0.0", "url-to-options": "^1.0.1"}, "devDependencies": {"ava": "^0.25.0", "coveralls": "^3.0.0", "form-data": "^2.1.1", "get-port": "^3.0.0", "nyc": "^11.0.2", "p-event": "^1.3.0", "pem": "^1.4.4", "proxyquire": "^1.8.0", "sinon": "^4.0.0", "slow-stream": "0.0.4", "tempfile": "^2.0.0", "tempy": "^0.2.1", "universal-url": "1.0.0-alpha", "xo": "^0.20.0"}, "ava": {"concurrency": 4}, "browser": {"decompress-response": false, "electron": false}}