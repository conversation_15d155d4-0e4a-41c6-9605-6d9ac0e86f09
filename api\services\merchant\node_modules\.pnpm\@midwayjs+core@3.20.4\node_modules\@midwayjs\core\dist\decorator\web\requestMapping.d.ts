import { MiddlewareParamArray } from '../../interface';
export interface RouterOption {
    /**
     * router path, like "/api"
     */
    path?: string | RegExp;
    /**
     * http method, like "get", "post"
     */
    requestMethod: string;
    /**
     * router alias name
     */
    routerName?: string;
    /**
     * which method decorator attached
     */
    method?: string;
    /**
     * middleware array in router
     */
    middleware?: MiddlewareParamArray;
    /**
     * router summary, for swagger
     * @deprecated
     */
    summary?: string;
    /**
     * router description, for swagger
     * @deprecated
     */
    description?: string;
    /**
     * ignore global prefix
     */
    ignoreGlobalPrefix?: boolean;
}
export declare const RequestMethod: {
    GET: string;
    POST: string;
    PUT: string;
    DELETE: string;
    PATCH: string;
    ALL: string;
    OPTIONS: string;
    HEAD: string;
};
export declare const RequestMapping: (metadata?: RouterOption) => MethodDecorator;
/**
 * Routes HTTP POST requests to the specified path.
 */
export declare const Post: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP GET requests to the specified path.
 */
export declare const Get: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP DELETE requests to the specified path.
 */
export declare const Del: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP PUT requests to the specified path.
 */
export declare const Put: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP PATCH requests to the specified path.
 */
export declare const Patch: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP OPTIONS requests to the specified path.
 */
export declare const Options: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes HTTP HEAD requests to the specified path.
 */
export declare const Head: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
/**
 * Routes all HTTP requests to the specified path.
 */
export declare const All: (path?: string | RegExp, routerOptions?: {
    routerName?: string;
    middleware?: MiddlewareParamArray;
    summary?: string;
    description?: string;
    ignoreGlobalPrefix?: boolean;
}) => MethodDecorator;
//# sourceMappingURL=requestMapping.d.ts.map