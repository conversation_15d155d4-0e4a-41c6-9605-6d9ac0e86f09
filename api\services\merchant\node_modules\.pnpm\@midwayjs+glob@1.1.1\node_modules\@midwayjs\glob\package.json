{"name": "@midwayjs/glob", "version": "1.1.1", "main": "dist/index", "typings": "dist/index.d.ts", "homepage": "https://github.com/midwayjs/glob", "repository": {"type": "git", "url": "https://github.com/midwayjs/glob.git"}, "dependencies": {"picomatch": "^2.3.1"}, "files": ["dist"], "devDependencies": {"@types/jest": "^19.2.4", "@types/node": "^16.18.3", "cross-env": "^7.0.3", "jest": "^29.3.1", "ts-jest": "^29.0.3", "typescript": "^4.9.3"}, "license": "MIT", "scripts": {"build": "cross-env rm -rf dist && tsc", "test": "cross-env NODE_ENV=test jest", "cov": "cross-env NODE_ENV=unittest jest --coverage"}}