{"version": 3, "sources": ["../../src/connection/Connection.ts"], "names": [], "mappings": ";;;AAAA,0DAAsD;AAEtD;;;;;;GAMG;AACH,MAAa,UAAW,SAAQ,uBAAU;CAAG;AAA7C,gCAA6C", "file": "Connection.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\n\n/**\n * Connection is a single database ORM connection to a specific database.\n * Its not required to be a database connection, depend on database type it can create connection pool.\n * You can have multiple connections to multiple databases in your application.\n *\n * @deprecated\n */\nexport class Connection extends DataSource {}\n"], "sourceRoot": ".."}