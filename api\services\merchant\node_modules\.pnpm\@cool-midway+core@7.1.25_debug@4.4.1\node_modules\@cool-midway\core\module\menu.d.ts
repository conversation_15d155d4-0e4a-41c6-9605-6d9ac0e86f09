import { ILogger, IMidwayApplication } from "@midwayjs/core";
import { TypeORMDataSourceManager } from "@midwayjs/typeorm";
import { DataSource } from "typeorm";
import { CoolEventManager } from "../event";
import { CoolConfig } from "../interface";
import { CoolModuleConfig } from "./config";
/**
 * 菜单
 */
export declare class CoolModuleMenu {
    coolModuleConfig: CoolModuleConfig;
    coolConfig: CoolConfig;
    app: IMidwayApplication;
    coreLogger: ILogger;
    coolEventManager: CoolEventManager;
    initJudge: "file" | "db";
    ormConfig: any;
    defaultDataSource: DataSource;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    datas: {};
    init(): Promise<void>;
    /**
     * 导入菜单
     * @param module
     * @param lockPath
     */
    importMenu(module: string, metadatas: any, lockPath?: string): Promise<void>;
    /**
     * 获取数据库元数据
     */
    getDbMetadatas(): Promise<any>;
    /**
     * 检查数据是否存在
     * @param module
     * @param metadatas
     */
    checkDbExist(module: string, metadatas: any): Promise<boolean>;
    /**
     * 检查文件是否存在
     * @param module
     */
    checkFileExist(module: string): {
        exist: boolean;
        lockPath: string;
    };
    /**
     * 锁定导入
     * @param module
     * @param metadatas
     * @param lockPath
     * @param time
     */
    lockImportData(module: string, metadatas: any, lockPath: string): Promise<void>;
}
