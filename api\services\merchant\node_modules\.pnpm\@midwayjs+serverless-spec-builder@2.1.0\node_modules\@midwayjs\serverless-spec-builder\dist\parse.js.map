{"version": 3, "file": "parse.js", "sourceRoot": "", "sources": ["../src/parse.ts"], "names": [], "mappings": ";;;AAAA,gCAAgC;AAChC,yBAAyB;AACzB,iCAAiC;AACjC,6BAA6B;AAE7B,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAE;IACrC,IAAI,IAAI,CAAC;IACT,IAAI,KAAK,CAAC;IACV,IAAI;QACF,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,IAAI,EAAE,CAAC,CAAC;KACtD;IAAC,OAAO,SAAS,EAAE;QAClB,KAAK,GAAG,SAAS,CAAC;KACnB;IACD,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACzB,CAAC,CAAC;AAEK,MAAM,KAAK,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;IAC1C,kBAAkB;IAClB,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAC9B,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;KAC7B;SAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QAClE,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,QAAQ;SACnB,CAAC;QACF,MAAM,MAAM,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;QACtD,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,MAAM,MAAM,CAAC,KAAK,CAAC;SACpB;QACD,OAAO,MAAM,CAAC,IAAI,CAAC;KACpB;IACD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;AACjD,CAAC,CAAC;AAfW,QAAA,KAAK,SAehB;AAEK,MAAM,QAAQ,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;IAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAC7B,WAAW,EAAE,IAAI;KAClB,CAAC,CAAC;IACH,IAAI;QACF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpC,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;KAClC;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,YAAY,QAAQ,WAAW,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;KAC/D;AACH,CAAC,CAAC;AAVW,QAAA,QAAQ,YAUnB"}