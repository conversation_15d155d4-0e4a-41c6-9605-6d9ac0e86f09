{"version": 3, "sources": ["../../src/metadata-args/ForeignKeyMetadataArgs.ts"], "names": [], "mappings": "", "file": "ForeignKeyMetadataArgs.js", "sourcesContent": ["import { DeferrableType } from \"../metadata/types/DeferrableType\"\nimport { OnDeleteType } from \"../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../metadata/types/OnUpdateType\"\nimport { PropertyTypeFactory } from \"../metadata/types/PropertyTypeInFunction\"\nimport { RelationTypeInFunction } from \"../metadata/types/RelationTypeInFunction\"\n\n/**\n * Arguments for ForeignKeyMetadata class.\n */\nexport interface ForeignKeyMetadataArgs {\n    /**\n     * Class to which foreign key is applied.\n     */\n    readonly target: Function | string\n\n    /**\n     * Class's property name to which this foreign key is applied.\n     */\n    readonly propertyName?: string\n\n    /**\n     * Type of the relation. This type is in function because of language specifics and problems with recursive\n     * referenced classes.\n     */\n    readonly type: RelationTypeInFunction\n\n    /**\n     * Foreign key constraint name.\n     */\n    readonly name?: string\n\n    /**\n     * Inverse side of the relation.\n     */\n    readonly inverseSide?: PropertyTypeFactory<any>\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    readonly columnNames?: string[]\n\n    /**\n     * Column names which included by this foreign key.\n     */\n    readonly referencedColumnNames?: string[]\n\n    /**\n     * \"ON DELETE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being deleted.\n     */\n    readonly onDelete?: OnDeleteType\n\n    /**\n     * \"ON UPDATE\" of this foreign key, e.g. what action database should perform when\n     * referenced stuff is being updated.\n     */\n    readonly onUpdate?: OnUpdateType\n\n    /**\n     * Set this foreign key constraint as \"DEFERRABLE\" e.g. check constraints at start\n     * or at the end of a transaction\n     */\n    readonly deferrable?: DeferrableType\n}\n"], "sourceRoot": ".."}