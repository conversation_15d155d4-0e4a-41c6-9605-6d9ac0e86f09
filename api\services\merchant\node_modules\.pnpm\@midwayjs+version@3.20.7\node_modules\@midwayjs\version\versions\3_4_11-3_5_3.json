{"@midwayjs/faas-typings": "3.5.0", "@midwayjs/fc-starter": "3.5.3", "@midwayjs/serverless-http-parser": "3.5.3", "@midwayjs/async-hooks-context-manager": "3.5.3", "@midwayjs/axios": "3.5.3", "@midwayjs/bootstrap": "3.5.3", "@midwayjs/cache": "3.5.3", "@midwayjs/code-dye": "3.5.3", "@midwayjs/consul": "3.5.3", "@midwayjs/core": "3.5.3", "@midwayjs/cos": "3.5.3", "@midwayjs/cross-domain": "3.5.3", "@midwayjs/decorator": "3.4.11", "@midwayjs/express-session": "3.5.3", "@midwayjs/faas": "3.5.3", "@midwayjs/grpc": "3.5.3", "@midwayjs/http-proxy": "3.5.3", "@midwayjs/i18n": "3.5.3", "@midwayjs/info": "3.5.3", "@midwayjs/jwt": "3.5.3", "@midwayjs/kafka": "3.5.3", "@midwayjs/mikro": "3.5.3", "@midwayjs/mock": "3.5.3", "@midwayjs/mongoose": "3.5.3", "@midwayjs/oss": "3.5.3", "@midwayjs/otel": "3.5.3", "@midwayjs/passport": "3.5.3", "@midwayjs/process-agent": "3.5.3", "@midwayjs/prometheus-socket-io": "3.5.3", "@midwayjs/prometheus": "3.5.3", "@midwayjs/rabbitmq": "3.5.3", "@midwayjs/redis": "3.5.3", "@midwayjs/security": "3.5.3", "@midwayjs/sequelize": "3.5.3", "@midwayjs/session": "3.5.3", "@midwayjs/socketio": "3.5.3", "@midwayjs/static-file": "3.5.3", "@midwayjs/swagger": "3.5.3", "@midwayjs/tablestore": "3.5.3", "@midwayjs/task": "3.5.3", "@midwayjs/typegoose": "3.5.3", "@midwayjs/typeorm": "3.5.3", "@midwayjs/upload": "3.5.3", "@midwayjs/validate": "3.5.3", "@midwayjs/version": "3.5.3", "@midwayjs/view-ejs": "3.5.3", "@midwayjs/view-nunjucks": "3.5.3", "@midwayjs/view": "3.5.3", "@midwayjs/express": "3.5.3", "@midwayjs/koa": "3.5.3", "@midwayjs/web": "3.5.3", "@midwayjs/ws": "3.5.3"}