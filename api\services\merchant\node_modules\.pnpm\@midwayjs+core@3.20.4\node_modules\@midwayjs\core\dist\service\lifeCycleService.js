"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayLifeCycleService = void 0;
const interface_1 = require("../interface");
const decorator_1 = require("../decorator");
const configuration_1 = require("../functional/configuration");
const frameworkService_1 = require("./frameworkService");
const configService_1 = require("./configService");
const util_1 = require("util");
const mockService_1 = require("./mockService");
const healthService_1 = require("./healthService");
const performanceManager_1 = require("../common/performanceManager");
const debug = (0, util_1.debuglog)('midway:debug');
let MidwayLifeCycleService = class MidwayLifeCycleService {
    constructor(applicationContext) {
        this.applicationContext = applicationContext;
        this.lifecycleInstanceList = [];
    }
    async init() {
        // exec simulator init
        await this.mockService.initSimulation();
        // run lifecycle
        const cycles = (0, decorator_1.listModule)(decorator_1.CONFIGURATION_KEY);
        debug(`[core]: Found Configuration length = ${cycles.length}`);
        for (const cycle of cycles) {
            if (cycle.target instanceof configuration_1.FunctionalConfiguration) {
                // 函数式写法
                cycle.instance = cycle.target;
            }
            else {
                // 普通类写法
                debug(`[core]: Lifecycle run ${cycle.namespace} init`);
                cycle.instance = await this.applicationContext.getAsync(cycle.target);
            }
            cycle.instance && this.lifecycleInstanceList.push(cycle);
        }
        // init health check service
        await this.healthService.init(this.lifecycleInstanceList);
        // bind object lifecycle
        await Promise.all([
            this.runObjectLifeCycle(this.lifecycleInstanceList, 'onBeforeObjectCreated'),
            this.runObjectLifeCycle(this.lifecycleInstanceList, 'onObjectCreated'),
            this.runObjectLifeCycle(this.lifecycleInstanceList, 'onObjectInit'),
            this.runObjectLifeCycle(this.lifecycleInstanceList, 'onBeforeObjectDestroy'),
        ]);
        // bind framework lifecycle
        // onAppError
        // exec onConfigLoad()
        await this.runContainerLifeCycle(this.lifecycleInstanceList, 'onConfigLoad', configData => {
            if (configData) {
                this.configService.addObject(configData);
            }
        });
        await this.mockService.runSimulatorSetup();
        // exec onReady()
        await this.runContainerLifeCycle(this.lifecycleInstanceList, 'onReady');
        // exec framework.run()
        await this.frameworkService.runFramework();
        // exec onServerReady()
        await this.runContainerLifeCycle(this.lifecycleInstanceList, 'onServerReady');
        // clear config merge cache
        if (!this.configService.getConfiguration('debug.recordConfigMergeOrder')) {
            this.configService.clearConfigMergeOrder();
        }
    }
    async stop() {
        await this.mockService.runSimulatorTearDown();
        // stop lifecycle
        const cycles = (0, decorator_1.listModule)(decorator_1.CONFIGURATION_KEY) || [];
        for (const cycle of cycles.reverse()) {
            let inst;
            if (cycle.target instanceof configuration_1.FunctionalConfiguration) {
                // 函数式写法
                inst = cycle.target;
            }
            else {
                inst = await this.applicationContext.getAsync(cycle.target);
            }
            await this.runContainerLifeCycle(inst, 'onStop');
        }
        // stop framework
        await this.frameworkService.stopFramework();
    }
    /**
     * run some lifecycle in configuration
     * @param lifecycleInstanceOrList
     * @param lifecycle
     * @param resultHandler
     */
    async runContainerLifeCycle(lifecycleInstanceOrList, lifecycle, resultHandler) {
        if (Array.isArray(lifecycleInstanceOrList)) {
            for (const cycle of lifecycleInstanceOrList) {
                if (typeof cycle.instance[lifecycle] === 'function') {
                    debug(`[core]: Lifecycle run ${cycle.instance.constructor.name} ${lifecycle}`);
                    performanceManager_1.MidwayInitializerPerformanceManager.lifecycleStart(cycle.namespace, lifecycle);
                    const result = await cycle.instance[lifecycle](this.applicationContext, this.frameworkService.getMainApp());
                    if (resultHandler) {
                        resultHandler(result);
                    }
                    performanceManager_1.MidwayInitializerPerformanceManager.lifecycleEnd(cycle.namespace, lifecycle);
                }
            }
        }
        else {
            if (typeof lifecycleInstanceOrList[lifecycle] === 'function') {
                const name = lifecycleInstanceOrList.constructor.name;
                debug(`[core]: Lifecycle run ${name} ${lifecycle}`);
                performanceManager_1.MidwayInitializerPerformanceManager.lifecycleStart(name, lifecycle);
                const result = await lifecycleInstanceOrList[lifecycle](this.applicationContext, this.frameworkService.getMainApp());
                if (resultHandler) {
                    resultHandler(result);
                }
                performanceManager_1.MidwayInitializerPerformanceManager.lifecycleEnd(name, lifecycle);
            }
        }
    }
    /**
     * run object lifecycle
     * @param lifecycleInstanceList
     * @param lifecycle
     */
    async runObjectLifeCycle(lifecycleInstanceList, lifecycle) {
        for (const cycle of lifecycleInstanceList) {
            if (typeof cycle.instance[lifecycle] === 'function') {
                debug(`[core]: Lifecycle run ${cycle.instance.constructor.name} ${lifecycle}`);
                return await this.applicationContext[lifecycle](cycle.instance[lifecycle].bind(cycle.instance));
            }
        }
    }
    getLifecycleInstanceList() {
        return this.lifecycleInstanceList;
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", frameworkService_1.MidwayFrameworkService)
], MidwayLifeCycleService.prototype, "frameworkService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", configService_1.MidwayConfigService)
], MidwayLifeCycleService.prototype, "configService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", mockService_1.MidwayMockService)
], MidwayLifeCycleService.prototype, "mockService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", healthService_1.MidwayHealthService)
], MidwayLifeCycleService.prototype, "healthService", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MidwayLifeCycleService.prototype, "init", null);
MidwayLifeCycleService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayLifeCycleService);
exports.MidwayLifeCycleService = MidwayLifeCycleService;
//# sourceMappingURL=lifeCycleService.js.map