{"version": 3, "file": "wrapCell.js", "sourceRoot": "", "sources": ["../../src/wrapCell.ts"], "names": [], "mappings": ";;;AAAA,mCAEiB;AACjB,6CAEsB;AACtB,yCAEoB;AAEpB;;;;;;GAMG;AACI,MAAM,QAAQ,GAAG,CAAC,SAAiB,EAAE,SAAiB,EAAE,WAAoB,EAAY,EAAE;IAC/F,kCAAkC;IAClC,MAAM,SAAS,GAAG,IAAA,iBAAS,EAAC,SAAS,CAAC,CAAC;IAEvC,8EAA8E;IAC9E,KAAK,IAAI,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG;QAC/C,IAAI,UAAU,CAAC;QAEf,IAAI,WAAW,EAAE;YACf,UAAU,GAAG,IAAA,mBAAQ,EAAC,SAAS,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;SACrD;aAAM;YACL,UAAU,GAAG,IAAA,uBAAU,EAAC,SAAS,CAAC,MAAM,CAAC,EAAE,SAAS,CAAC,CAAC;SACvD;QAED,yEAAyE;QACzE,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC;QAC3C,MAAM,IAAI,UAAU,CAAC,MAAM,CAAC;KAC7B;IAED,OAAO,SAAS,CAAC;AACnB,CAAC,CAAC;AApBW,QAAA,QAAQ,YAoBnB"}