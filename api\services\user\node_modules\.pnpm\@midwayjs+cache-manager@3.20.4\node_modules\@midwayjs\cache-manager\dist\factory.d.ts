import { IMidwayContainer, ServiceFactory, ServiceFactoryConfigOption } from '@midwayjs/core';
import { CacheManagerOptions, MidwayCache, MidwayMultiCache, MidwayUnionCache } from './interface';
export declare class CachingFactory extends ServiceFactory<MidwayUnionCache> {
    protected cacheManagerConfig: ServiceFactoryConfigOption<CacheManagerOptions>;
    protected applicationContext: IMidwayContainer;
    protected init(): Promise<void>;
    protected createClient(config: CacheManagerOptions<any>, clientName: string): Promise<void | MidwayUnionCache>;
    getName(): string;
    getCaching(cacheKey: string): MidwayCache;
    getMultiCaching(cacheKey: string): MidwayMultiCache;
}
//# sourceMappingURL=factory.d.ts.map