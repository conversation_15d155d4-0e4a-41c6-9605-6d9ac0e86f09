"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DevPlugin = void 0;
const command_core_1 = require("@midwayjs/command-core");
const child_process_1 = require("child_process");
const light_spinner_1 = require("light-spinner");
const chokidar = require("chokidar");
const os_1 = require("os");
const path_1 = require("path");
const fs_1 = require("fs");
const chalk = require("chalk");
const detect = require("detect-port");
const json5_1 = require("json5");
const utils_1 = require("./utils");
class DevPlugin extends command_core_1.BasePlugin {
    constructor() {
        super(...arguments);
        this.started = false;
        this.restarting = false;
        this.processMessageMap = {};
        this.isInClosing = false;
        this.startTime = Date.now();
        this.commands = {
            dev: {
                lifecycleEvents: ['checkEnv', 'run'],
                options: {
                    baseDir: {
                        usage: 'directory of application, default to `process.cwd()`',
                    },
                    port: {
                        usage: 'listening port, default to 7001',
                        shortcut: 'p',
                    },
                    debug: {
                        usage: 'midway debug',
                    },
                    framework: {
                        usage: 'specify framework that can be absolute path or npm package',
                    },
                    entryFile: {
                        usage: 'specify entry file, like bootstrap.js',
                        shortcut: 'f',
                    },
                    notWatch: {
                        usage: 'not watch file change',
                    },
                    fast: {
                        usage: 'fast mode',
                    },
                    sourceDir: {
                        usage: 'ts code source dir',
                    },
                    layers: {
                        usage: 'extend serverless by layer',
                    },
                    watchFile: {
                        usage: 'watch more file',
                    },
                    watchFilePatten: {
                        usage: 'watch more files by glob patten',
                    },
                    unWatchFilePatten: {
                        usage: 'unwatch files by glob patten',
                    },
                    watchExt: {
                        usage: 'watch more extensions',
                    },
                    detectPort: {
                        usage: 'when using entryFile, auto detect port',
                    },
                },
            },
        };
        this.hooks = {
            'dev:checkEnv': this.checkEnv.bind(this),
            'dev:run': this.run.bind(this),
        };
    }
    async checkEnv() {
        this.setStore('dev:getData', this.getData.bind(this), true);
        const defaultPort = this.options.randomPort
            ? Math.ceil(1000 + Math.random() * 9000)
            : 7001;
        this.port = this.options.port;
        // 如果用户通过 --port 传了，就赋值到 MIDWAY_HTTP_PORT 环境变量上面去，此处为兼容 3.x 写法
        if (this.port) {
            process.env.MIDWAY_HTTP_PORT = `${this.port}`;
        }
        // 此处为兼容 2.x 写法，在 2.x 的情况下，没有传递 port 则自动选择一个
        if (!this.port && (!this.options.entryFile || this.options.detectPort)) {
            this.port = await detect(defaultPort);
        }
        const cwd = this.core.cwd;
        if (this.options.ts === undefined) {
            if ((0, fs_1.existsSync)((0, path_1.resolve)(cwd, 'tsconfig.json'))) {
                this.options.ts = true;
            }
        }
        // ts 模式需要校验tsconfig中的ts-node配置是否有module: commonjs
        if (this.options.ts) {
            this.checkTsConfigTsNodeModule();
        }
    }
    async run() {
        process.on('exit', this.handleClose.bind(this, 'exit', false));
        process.on('SIGINT', this.handleClose.bind(this, 'SIGINT', true));
        process.on('SIGTERM', this.handleClose.bind(this, 'SIGTERM', true));
        process.on('disconnect', this.handleClose.bind(this, 'disconnect', true));
        this.setStore('dev:closeApp', this.handleClose.bind(this, 'dev:closeApp'), true);
        const options = this.getOptions();
        await this.start();
        if (!options.notWatch) {
            this.startWatch();
        }
        if (!options.notAwait) {
            return new Promise(() => { });
        }
    }
    getOptions() {
        let framework;
        const layers = this.options.layers
            ? this.options.layers.split(',')
            : [];
        const cwd = this.core.cwd;
        const yamlPath = (0, path_1.resolve)(cwd, 'f.yml');
        if (!this.options.framework && (0, fs_1.existsSync)(yamlPath)) {
            const ymlData = (0, fs_1.readFileSync)(yamlPath).toString();
            if (!/deployType/.test(ymlData)) {
                // MIDWAY_DEV_IS_SERVERLESS 决定了使用 createFunctionApp 来启动
                process.env.MIDWAY_DEV_IS_SERVERLESS = 'true';
                try {
                    // eslint-disable-next-line
                    framework = require.resolve('@midwayjs/serverless-app');
                    process.env.MIDWAY_DEV_IS_SERVERLESS_APP = 'true';
                }
                catch (_a) {
                    //
                }
            }
        }
        return {
            framework,
            baseDir: this.getSourceDir(),
            ...this.options,
            layers,
            port: this.port,
        };
    }
    start() {
        this.isInClosing = false;
        return new Promise(async (resolve) => {
            var _a, _b, _c, _d;
            this.clearTernimal();
            const options = this.getOptions();
            this.core.debug('start options', options);
            if (this.spin) {
                this.spin.stop();
            }
            this.spin = new light_spinner_1.default({
                text: this.started ? 'Midway Restarting' : 'Midway Starting',
            });
            if (!options.silent) {
                this.spin.start();
            }
            let tsNodeFast = {};
            if (options.fast) {
                tsNodeFast = utils_1.tsNodeFastEnv;
            }
            let execArgv = [];
            let MIDWAY_DEV_IS_DEBUG;
            let useIncrementalBuild = false;
            if (options.ts) {
                let fastRegister;
                if (typeof options.fast === 'string' && options.fast !== 'true') {
                    const pluginName = `@midwayjs/cli-plugin-${options.fast}`;
                    this.core.debug('faster pluginName', pluginName);
                    try {
                        const pkg = require.resolve(`${pluginName}/package.json`);
                        fastRegister = (0, path_1.join)(pkg, `../js/${options.fast}-register.js`);
                        this.core.debug('fastRegister', fastRegister);
                        if (!(0, fs_1.existsSync)(fastRegister)) {
                            fastRegister = '';
                        }
                    }
                    catch (_e) {
                        throw new Error(`please install @midwayjs/cli-plugin-${options.fast} to using fast mode '${options.fast}'`);
                    }
                    if (fastRegister) {
                        execArgv = ['-r', fastRegister];
                    }
                }
                if (!fastRegister) {
                    let tsRegister = this.getTsNodeRegister();
                    if ((_b = (_a = this.tsconfigJson) === null || _a === void 0 ? void 0 : _a.compilerOptions) === null || _b === void 0 ? void 0 : _b.incremental) {
                        useIncrementalBuild = true;
                        tsNodeFast = utils_1.tsNodeFastEnv;
                        process.env.MW_CLI_TS_NODE = (0, command_core_1.findNpmModuleByResolve)(this.core.cwd, 'ts-node');
                        process.env.MW_CLI_SOURCE_DIR = this.getSourceDir();
                        tsRegister = (0, path_1.join)(__dirname, '../js/register');
                    }
                    execArgv = ['-r', tsRegister];
                    if ((_d = (_c = this.tsconfigJson) === null || _c === void 0 ? void 0 : _c.compilerOptions) === null || _d === void 0 ? void 0 : _d.baseUrl) {
                        execArgv.push('-r', 'tsconfig-paths/register');
                    }
                }
            }
            if (options.debug) {
                const debugStr = options.debug.toString();
                const port = /^\d+$/.test(debugStr) ? options.debug : '9229';
                this.core.debug('Debug port:', port);
                const portIsUse = await (0, utils_1.checkPort)(port);
                if (portIsUse) {
                    this.core.cli.log(`\n\nDebug port ${port} is in use\n\n`);
                }
                else {
                    MIDWAY_DEV_IS_DEBUG = port;
                    execArgv.push(`--inspect=${port}`);
                }
            }
            this.child = (0, child_process_1.fork)(require.resolve('./child'), [JSON.stringify(options)], {
                cwd: this.core.cwd,
                env: {
                    IN_CHILD_PROCESS: 'true',
                    TS_NODE_FILES: 'true',
                    MIDWAY_DEV_IS_DEBUG,
                    ...tsNodeFast,
                    ...process.env,
                },
                silent: true,
                execArgv,
            });
            if (useIncrementalBuild ||
                (this.options.ts && this.options.fast === 'swc')) {
                this.checkTsType();
            }
            const dataCache = [];
            this.child.stdout.on('data', data => {
                if (this.restarting) {
                    dataCache.push(data);
                }
                else {
                    process.stdout.write(data);
                }
            });
            this.child.stderr.on('data', data => {
                this.error(data.toString());
            });
            this.child.on('message', msg => {
                if (msg.type === 'started') {
                    this.childProcesslistenedPort = msg.port;
                    this.spin.stop();
                    while (dataCache.length) {
                        process.stdout.write(dataCache.shift());
                    }
                    this.restarting = false;
                    if (msg.startSuccess) {
                        if (!this.started) {
                            this.started = true;
                            this.core.debug('start time', Date.now() - this.startTime);
                            this.displayStartTips(options);
                        }
                    }
                    resolve();
                }
                else if (msg.type === 'error') {
                    this.spin.stop();
                    this.error(msg.message || '');
                }
                else if (msg.id) {
                    if (this.processMessageMap[msg.id]) {
                        this.processMessageMap[msg.id](msg.data);
                        delete this.processMessageMap[msg.id];
                    }
                }
            });
        });
    }
    getTsNodeRegister() {
        const tsNodePath = (0, command_core_1.findNpmModuleByResolve)(this.core.cwd, 'ts-node');
        if (tsNodePath) {
            return (0, path_1.join)(tsNodePath, 'register');
        }
        const errorMsg = [
            '!!!',
            '未找到 ts-node 来处理您的 Typescript 代码',
            '请手动安装 ts-node@10 依赖之后再次执行 midway-bin dev --ts',
            '---',
        ].join('\n');
        console.error(errorMsg);
        throw new Error(errorMsg);
    }
    async handleClose(type, isExit, signal) {
        var _a;
        this.core.debug('handleClose', type, isExit, signal, this.isInClosing);
        if (this.isInClosing) {
            return;
        }
        this.isInClosing = true;
        if (this.spin) {
            this.spin.stop();
        }
        if (this.child) {
            const childExitError = 'childExitError';
            const closeChildRes = await new Promise(resolve => {
                if (this.child.connected) {
                    const id = Date.now() + ':exit:' + Math.random();
                    setTimeout(() => {
                        delete this.processMessageMap[id];
                        resolve(childExitError);
                    }, 2000);
                    this.processMessageMap[id] = resolve;
                    this.child.send({ type: 'exit', id });
                }
                else {
                    resolve(void 0);
                }
            });
            // 无论上述close 是否成功关闭，都强行关闭一次
            const isWin = (0, os_1.platform)() === 'win32';
            try {
                if (!isWin) {
                    await (0, command_core_1.exec)({
                        cmd: `kill -9 ${this.child.pid} || true`,
                        slience: true,
                    });
                }
            }
            catch (_b) {
                //
            }
            if ((_a = this.child) === null || _a === void 0 ? void 0 : _a.kill) {
                this.child.kill();
            }
            if (closeChildRes === childExitError) {
                this.log('Pre Process Force Exit.');
            }
            this.child = null;
        }
        if (isExit) {
            process.exit(signal);
        }
        this.isInClosing = false;
    }
    async restart() {
        this.startTime = Date.now();
        await this.handleClose('restart');
        await this.start();
    }
    getIp() {
        const interfaces = (0, os_1.networkInterfaces)(); // 在开发环境中获取局域网中的本机iP地址
        for (const devName in interfaces) {
            const iface = interfaces[devName];
            for (const alias of iface) {
                if (alias.family === 'IPv4' &&
                    alias.address !== '127.0.0.1' &&
                    !alias.internal) {
                    return alias.address;
                }
            }
        }
    }
    clearTernimal() {
        if (this.options.preserveOutput || process.env.MIDWAY_CLI_PRESERVE_OUTPUT) {
            return;
        }
        if (process.platform === 'win32') {
            this.core.cli.log('\u001B[2J\u001B[0f');
        }
        else {
            this.core.cli.log('\u001B[2J\u001B[3J\u001B[H');
        }
    }
    getSourceDir() {
        return (0, path_1.resolve)(this.core.cwd, this.options.sourceDir || 'src');
    }
    // watch file change
    startWatch() {
        const sourceDir = this.getSourceDir();
        const watchAllowExts = (this.options.watchExt ? this.options.watchExt.split(',') : []).concat('.ts', '.yml', '.json');
        const watcher = chokidar.watch(sourceDir, {
            ignored: path => {
                if (path.includes('node_modules')) {
                    return true;
                }
                if ((0, fs_1.existsSync)(path)) {
                    const stat = (0, fs_1.statSync)(path);
                    if (stat.isFile()) {
                        const matchExts = watchAllowExts.find(ext => {
                            return path.endsWith(ext);
                        });
                        if (!matchExts) {
                            return true;
                        }
                    }
                }
            },
            persistent: true,
            cwd: this.core.cwd,
            ignoreInitial: true,
        });
        const fyml = (0, path_1.resolve)(this.core.cwd, 'f.yml');
        if ((0, fs_1.existsSync)(fyml)) {
            watcher.add(fyml);
        }
        if (this.options.watchFile) {
            const watchFileList = this.options.watchFile.split(',');
            watchFileList.forEach(file => {
                const filePath = (0, path_1.resolve)(this.core.cwd, file);
                if ((0, fs_1.existsSync)(filePath)) {
                    this.core.debug('other watch file', filePath);
                    watcher.add(filePath);
                }
                else {
                    this.core.debug('other watch picomatch rule', file);
                    watcher.add(file);
                }
            });
        }
        if (this.options.watchFilePatten) {
            const watchFilePattenList = this.options.watchFilePatten.split(',');
            watchFilePattenList.forEach(file => {
                const filePath = (0, path_1.resolve)(this.core.cwd, file);
                this.core.debug('watch glob patten', filePath);
                watcher.add(filePath);
            });
        }
        if (this.options.unWatchFilePatten) {
            const unWatchFilePattenList = this.options.unWatchFilePatten.split(',');
            unWatchFilePattenList.forEach(file => {
                const filePath = (0, path_1.resolve)(this.core.cwd, file);
                this.core.debug('unwatch glob patten', filePath);
                watcher.unwatch(filePath);
            });
        }
        watcher.on('all', (event, path) => {
            if (this.restarting) {
                return;
            }
            this.restarting = true;
            this.restart().then(() => {
                this.core.debug('restart time', Date.now() - this.startTime);
                this.log(`Auto reload. ${chalk.hex('#666666')(`[${event}] ${(0, path_1.relative)(sourceDir, path)}`)}`);
            });
        });
    }
    displayStartTips(options) {
        this.port = this.childProcesslistenedPort || this.port;
        process.env.MIDWAY_LOCAL_DEV_PORT = String(this.port);
        this.setStore('dev:port', this.port, true);
        if (options.silent || options.notStartLog) {
            return;
        }
        if (!process.env.MIDWAY_LOCAL_DEV_PORT) {
            return;
        }
        const protocol = options.ssl ? 'https' : 'http';
        this.log('Start Server at ', chalk.hex('#9999ff').underline(`${protocol}://127.0.0.1:${this.port}`));
        const lanIp = this.getIp();
        if (lanIp) {
            this.log('Start on LAN', chalk.hex('#9999ff').underline(`${protocol}://${lanIp}:${this.port}`));
        }
        this.core.cli.log('');
        this.core.cli.log('');
    }
    log(...args) {
        this.core.cli.log('[ Midway ]', ...args);
    }
    error(...args) {
        console.error(chalk.hex('#ff0000')(...args));
    }
    // 检测tsconfig中module的配置
    checkTsConfigTsNodeModule() {
        var _a, _b;
        const cwd = this.core.cwd;
        const tsconfig = (0, path_1.resolve)(cwd, 'tsconfig.json');
        if (!(0, fs_1.existsSync)(tsconfig)) {
            return;
        }
        const tsconfigJson = (0, json5_1.parse)((0, fs_1.readFileSync)(tsconfig).toString());
        this.tsconfigJson = tsconfigJson;
        if (((_b = (_a = tsconfigJson === null || tsconfigJson === void 0 ? void 0 : tsconfigJson.compilerOptions) === null || _a === void 0 ? void 0 : _a.module) === null || _b === void 0 ? void 0 : _b.toLowerCase()) === 'commonjs') {
            return;
        }
        if (!tsconfigJson['ts-node']) {
            tsconfigJson['ts-node'] = {};
        }
        if (!tsconfigJson['ts-node'].compilerOptions) {
            tsconfigJson['ts-node'].compilerOptions = {};
        }
        if (tsconfigJson['ts-node'].compilerOptions.module === 'commonjs') {
            return;
        }
        tsconfigJson['ts-node'].compilerOptions.module = 'commonjs';
        (0, fs_1.writeFileSync)(tsconfig, JSON.stringify(tsconfigJson, null, 2));
    }
    async getData(type, data) {
        if (!this.started) {
            throw new Error('not started');
        }
        if (!this.child) {
            throw new Error('child not started');
        }
        return new Promise((resolve, reject) => {
            const id = Date.now() + ':' + Math.random();
            setTimeout(() => {
                delete this.processMessageMap[id];
                reject(new Error('timeout'));
            }, 2000);
            this.processMessageMap[id] = resolve;
            this.child.send({ type, data, id });
        });
    }
    async checkTsType() {
        const cwd = this.core.cwd;
        (0, child_process_1.fork)(require.resolve('../js/typeCheck'), [JSON.stringify({ cwd })], {
            cwd,
        });
    }
}
exports.DevPlugin = DevPlugin;
//# sourceMappingURL=index.js.map