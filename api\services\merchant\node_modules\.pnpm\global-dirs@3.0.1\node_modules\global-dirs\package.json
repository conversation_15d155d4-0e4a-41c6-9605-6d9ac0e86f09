{"name": "global-dirs", "version": "3.0.1", "description": "Get the directory of globally installed packages and binaries", "license": "MIT", "repository": "sindresorhus/global-dirs", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["global", "prefix", "path", "paths", "npm", "yarn", "node", "modules", "node-modules", "package", "packages", "binary", "binaries", "bin", "directory", "directories", "npmrc", "rc", "config", "root", "resolve"], "dependencies": {"ini": "2.0.0"}, "devDependencies": {"ava": "^2.4.0", "execa": "^5.0.0", "import-fresh": "^3.3.0", "tsd": "^0.14.0", "xo": "^0.37.1"}}