{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;AAAA,yDAIgC;AAChC,2BAAgC;AAChC,iCAAiC;AACjC,+BAA4B;AAC5B,2CAAwC;AACxC,MAAM,QAAQ,GAAG,kBAAkB,CAAC;AACpC,MAAa,UAAW,SAAQ,yBAAU;IAA1C;;QACE,aAAQ,GAAG;YACT,IAAI,EAAE;gBACJ,KAAK,EAAE,2BAA2B;gBAClC,eAAe,EAAE,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE;oBACP,GAAG,EAAE;wBACH,KAAK,EAAE,0BAA0B;wBACjC,QAAQ,EAAE,GAAG;qBACd;oBACD,KAAK,EAAE;wBACL,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,GAAG;qBACd;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE,qBAAqB;wBAC5B,QAAQ,EAAE,GAAG;qBACd;oBACD,SAAS,EAAE;wBACT,KAAK,EAAE,YAAY;qBACpB;oBACD,SAAS,EAAE;wBACT,KAAK,EAAE,WAAW;qBACnB;oBACD,KAAK,EAAE;wBACL,KAAK,EAAE,kBAAkB;qBAC1B;oBACD,eAAe,EAAE;wBACf,KAAK,EAAE,mBAAmB;qBAC3B;iBACF;aACF;YACD,GAAG,EAAE;gBACH,KAAK,EAAE,yCAAyC;gBAChD,eAAe,EAAE,CAAC,MAAM,CAAC;gBACzB,OAAO,EAAE;oBACP,KAAK,EAAE;wBACL,KAAK,EAAE,OAAO;wBACd,QAAQ,EAAE,GAAG;qBACd;oBACD,IAAI,EAAE;wBACJ,KAAK,EAAE,qBAAqB;wBAC5B,QAAQ,EAAE,GAAG;qBACd;oBACD,SAAS,EAAE;wBACT,KAAK,EAAE,YAAY;qBACpB;iBACF;aACF;SACF,CAAC;QAEF,UAAK,GAAG;YACN,WAAW,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;YAChC,UAAU,EAAE,KAAK,IAAI,EAAE;gBACrB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC;gBACxB,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;YACnB,CAAC;SACF,CAAC;IAsOJ,CAAC;IApOC,KAAK,CAAC,GAAG;QACP,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,wBAAwB,GAAG,GAAG,CAAC;QAC3C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,IAAA,eAAU,EAAC,IAAA,WAAI,EAAC,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;QACvE,IAAI,SAAS,GAAG,EAAE,CAAC;QACnB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE;YAClB,SAAS,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;SAChD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAsB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC;SAClE;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC;QACxC,MAAM,iBAAiB,GAAG;YACxB,cAAc,EAAE,IAAI;YACpB,QAAQ,EAAE,MAAM;SACjB,CAAC;QACF,gBAAgB;QAChB,IAAI,OAAO,CAAC;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI;gBACF,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;oBACpB,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;iBAC7C;qBAAM;oBACL,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;iBACrC;aACF;YAAC,OAAO,CAAC,EAAE;gBACV,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CACjB,6BAA6B,EAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CACnC,CAAC;gBACF,MAAM,CAAC,CAAC;aACT;SACF;aAAM;YACL,MAAM,aAAa,GAAG,IAAA,qCAAsB,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;YACpE,IAAI,CAAC,aAAa,EAAE;gBAClB,MAAM,QAAQ,GAAG;oBACf,KAAK;oBACL,2CAA2C;oBAC3C,mDAAmD;oBACnD,oBAAoB;oBACpB,0FAA0F;oBAC1F,KAAK;iBACN,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACb,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,CAAC;aAC3B;YAED,OAAO,GAAG,IAAA,WAAI,EAAC,aAAa,EAAE,YAAY,CAAC,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,iBAAiB,OAAO,EAAE,CAAC,CAAC;YAE5C,iBAAiB,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAC;YAC7C,IAAI,IAAI,EAAE;gBACR,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;aACjE;SACF;QAED,MAAM,GAAG,GAAG;YACV,GAAG;YACH,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC;YAClD,QAAQ;SACT,CAAC;QAEF,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACtB,IAAI,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACxD;aAAM;YACL,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;SACvD;QACD,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,YAAY,EAAE,CAAC;QACpB,OAAO,IAAA,uBAAQ,EAAC,OAAO,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,IAAI,EAAE,SAAS;QACtC,MAAM,IAAI,GAAG,EAAE,CAAC;QAEhB,IAAI,OAAO,CAAC;QAEZ,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,GAAG,SAAS,CAAC;SACrB;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE;YACxC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxC;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,IAAI,CAAC,sBAAsB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;SACxD;aAAM;YACL,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;gBAClC,gBAAgB;gBAChB,oBAAoB;aACrB,CAAC,CAAC;YAEH,IAAI,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACtC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;gBAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YACjD,CAAC,CAAC,CAAC;YACH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,4BAA4B,OAAO,EAAE,CAAC,CAAC;gBAC3D,OAAO;aACR;YACD,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,GAAG,KAAK,CAAC,CAAC;SAC3C;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACzB;QAED,MAAM,aAAa,GAAG;YACpB,KAAK;YACL,GAAG;YACH,MAAM;YACN,IAAI;YACJ,KAAK;YACL,OAAO;YACP,iBAAiB;YACjB,GAAG;SACJ,CAAC;QAEF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzC,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAClC,OAAO;aACR;YACD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;YACnC,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBAC9B,IAAI,KAAK,KAAK,IAAI,EAAE;oBAClB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC;iBAC1B;aACF;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,KAAK,MAAM,IAAI,KAAK,EAAE,CAAC,CAAC;aACnC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CACP,YAAY,IAAA,WAAI,EACd,SAAS,EACT,aAAa,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,SAAS,EAAE,CAC/C,EAAE,CACJ,CAAC;QAEF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,IAAI,EAAE,SAAS;QACvC,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,EAAE,CAAC;QAChB,IAAI,IAAI,EAAE;YACR,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC;SAChE;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;YACpB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;gBACpB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC,kBAAkB,EAAE,4BAA4B,CAAC,CAAC;aAChE;YACD,IAAI,IAAI,EAAE;gBACR,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;gBAC5B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACrB;YACD,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;SACzC;aAAM,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACjC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC;SACpD;QACD,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,KAAK,CAAC;QACxE,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE;YAC7B,eAAe;YACf,OAAO,GAAG,KAAK,CAAC;SACjB;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,aAAa,OAAO,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;QAE7D,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC;QACpE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,CAAC;SACrC;QAED,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAEzB,MAAM,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAE3E,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YAC3B,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;SACtD;QAED,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,aAAa,WAAW,EAAE,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,IAAI,OAAO,CAAC;QAEZ,IAAI,CAAC,OAAO,EAAE;YACZ,sBAAsB;YACtB,OAAO,GAAG,SAAS,CAAC;SACrB;QACD,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE;YACxC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACnB,OAAO,GAAG,CAAC,kBAAkB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;SACpD;QACD,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC,CAAC;QACnE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAEnC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,4BAA4B,OAAO,EAAE,CAAC,CAAC;YAC3D,OAAO;SACR;QAED,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAEpB,6CAA6C;QAC7C,MAAM,SAAS,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3E,IAAI,IAAA,eAAU,EAAC,SAAS,CAAC,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;SACzB;QACD,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED,0BAA0B;IAC1B,YAAY;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;YAChC,OAAO;SACR;QACD,IAAA,qBAAS,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3B,CAAC;CACF;AA/RD,gCA+RC"}