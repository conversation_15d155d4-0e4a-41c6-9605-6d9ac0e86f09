"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpcDecorator = void 0;
const core_1 = require("@cool-midway/core");
const core_2 = require("@midwayjs/core");
const typeorm_1 = require("@midwayjs/typeorm");
const transaction_1 = require("./transaction");
const uuid_1 = require("uuid");
/**
 * 装饰器
 */
let CoolRpcDecorator = class CoolRpcDecorator {
    async init() {
        // 事务
        await this.transaction();
    }
    /**
     * 事务
     */
    async transaction() {
        this.decoratorService.registerMethodHandler(transaction_1.COOL_RPC_TRANSACTION, options => {
            return {
                around: async (joinPoint) => {
                    const option = options.metadata;
                    let isCaller = false;
                    let rpcTransactionId;
                    if (joinPoint.args[0]) {
                        isCaller = false;
                        rpcTransactionId = joinPoint.args[0].rpcTransactionId;
                    }
                    // 如果没有事务ID，手动创建
                    if (!rpcTransactionId) {
                        isCaller = true;
                        rpcTransactionId = (0, uuid_1.v1)();
                    }
                    let data;
                    const dataSource = this.typeORMDataSourceManager.getDataSource((option === null || option === void 0 ? void 0 : option.connectionName) || 'default');
                    const queryRunner = dataSource.createQueryRunner();
                    // 使用我们的新queryRunner建立真正的数据库连
                    await queryRunner.connect();
                    if (option && option.isolation) {
                        await queryRunner.startTransaction(option.isolation);
                    }
                    else {
                        await queryRunner.startTransaction();
                    }
                    try {
                        global['moleculer.transactions'][rpcTransactionId] = queryRunner;
                        // 半小时后清除
                        setTimeout(() => {
                            global['moleculer.transactions'][rpcTransactionId].release();
                            delete global['moleculer.transactions'][rpcTransactionId];
                        }, 1800 * 1000);
                        joinPoint.args.push(rpcTransactionId);
                        joinPoint.args.push(queryRunner);
                        data = await joinPoint.proceed(...joinPoint.args);
                        if (isCaller) {
                            global['moleculer:broker'].broadcast('moleculer.transaction', {
                                rpcTransactionId,
                                commit: true,
                            });
                        }
                        //await queryRunner.commitTransaction();
                    }
                    catch (error) {
                        //await queryRunner.rollbackTransaction();
                        if (isCaller) {
                            global['moleculer:broker'].broadcast('moleculer.transaction', {
                                rpcTransactionId,
                                commit: false,
                            });
                        }
                        throw new core_1.CoolCommException(error.message);
                    }
                    return data;
                },
            };
        });
    }
};
exports.CoolRpcDecorator = CoolRpcDecorator;
__decorate([
    (0, core_2.Inject)(),
    __metadata("design:type", core_2.MidwayDecoratorService)
], CoolRpcDecorator.prototype, "decoratorService", void 0);
__decorate([
    (0, core_2.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolRpcDecorator.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_2.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CoolRpcDecorator.prototype, "init", null);
exports.CoolRpcDecorator = CoolRpcDecorator = __decorate([
    (0, core_2.Provide)(),
    (0, core_2.Scope)(core_2.ScopeEnum.Singleton)
], CoolRpcDecorator);
