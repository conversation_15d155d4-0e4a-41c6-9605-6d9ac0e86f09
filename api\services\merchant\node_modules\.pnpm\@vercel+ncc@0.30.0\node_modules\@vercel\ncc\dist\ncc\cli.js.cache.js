(()=>{var __webpack_modules__={306:e=>{"use strict";e.exports=JSON.parse('{"name":"@vercel/ncc","description":"Simple CLI for compiling a Node.js module into a single file, together with all its dependencies, gcc-style.","version":"0.30.0","repository":"vercel/ncc","license":"MIT","main":"./dist/ncc/index.js","bin":{"ncc":"./dist/ncc/cli.js"},"files":["dist"],"scripts":{"build":"node scripts/build.js","build-test-binary":"cd test/binary && node-gyp rebuild && cp build/Release/hello.node ../integration/hello.node","codecov":"codecov","test":"node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest","test-coverage":"node --expose-gc --max_old_space_size=3072 node_modules/.bin/jest --coverage --globals \\"{\\\\\\"coverage\\\\\\":true}\\" && codecov","prepublishOnly":"node scripts/build.js --no-cache"},"devDependencies":{"@azure/cosmos":"^3.12.3","@bugsnag/js":"^7.11.0","@ffmpeg-installer/ffmpeg":"^1.0.17","@google-cloud/bigquery":"^5.7.0","@google-cloud/firestore":"^4.14.0","@sentry/node":"^6.10.0","@slack/web-api":"^6.3.0","@tensorflow/tfjs-node":"^3.8.0","@vercel/webpack-asset-relocator-loader":"1.6.0","analytics-node":"^5.0.0","apollo-server-express":"^2.2.2","arg":"^5.0.0","auth0":"^2.14.0","aws-sdk":"^2.356.0","axios":"^0.21.1","azure-storage":"^2.10.2","browserify-middleware":"^8.1.1","bytes":"^3.0.0","canvas":"^2.2.0","chromeless":"^1.5.2","codecov":"^3.8.3","consolidate":"^0.16.0","copy":"^0.3.2","core-js":"^2.5.7","cowsay":"^1.3.1","esm":"^3.2.22","express":"^4.16.4","fetch-h2":"^3.0.0","firebase":"^6.1.1","firebase-admin":"^9.11.0","fluent-ffmpeg":"^2.1.2","fontkit":"^1.7.7","get-folder-size":"^2.0.0","glob":"^7.1.3","got":"^11.8.2","graceful-fs":"^4.1.15","graphql":"^15.5.1","highlights":"^3.1.1","hot-shots":"^8.5.0","ioredis":"^4.2.0","isomorphic-unfetch":"^3.0.0","jest":"^27.0.6","jimp":"^0.16.1","jugglingdb":"2.0.1","koa":"^2.6.2","leveldown":"^6.0.0","license-webpack-plugin":"2.3.20","lighthouse":"^8.1.0","loopback":"^3.24.0","mailgun":"^0.5.0","mariadb":"^2.0.1-beta","memcached":"^2.2.2","mkdirp":"^1.0.4","mongoose":"^5.3.12","mysql":"^2.16.0","node-gyp":"^8.1.0","npm":"^6.13.4","oracledb":"^4.2.0","passport":"^0.4.0","passport-google-oauth":"^2.0.0","path-platform":"^0.11.15","pdf2json":"^1.1.8","pdfkit":"^0.12.1","pg":"^8.7.1","pug":"^3.0.1","react":"^17.0.2","react-dom":"^17.0.2","redis":"^3.1.1","request":"^2.88.0","rxjs":"^7.3.0","saslprep":"^1.0.2","sequelize":"^6.6.5","sharp":"^0.28.3","shebang-loader":"^0.0.1","socket.io":"^4.1.3","source-map-support":"^0.5.9","stripe":"^8.167.0","swig":"^1.4.2","terser":"^5.6.1","the-answer":"^1.0.0","tiny-json-http":"^7.0.2","ts-loader":"^8.3.0","tsconfig-paths":"^3.7.0","tsconfig-paths-webpack-plugin":"^3.2.0","twilio":"^3.23.2","typescript":"^4.3.5","vm2":"^3.6.6","vue":"^2.5.17","vue-server-renderer":"^2.5.17","web-vitals":"^0.2.4","webpack":"5.44.0","when":"^3.7.8"},"resolutions":{"grpc":"1.24.6"}}')},832:e=>{const t=Symbol("arg flag");class ArgError extends Error{constructor(e,t){super(e);this.name="ArgError";this.code=t;Object.setPrototypeOf(this,ArgError.prototype)}}function arg(e,{argv:r=process.argv.slice(2),permissive:i=false,stopAtPositional:n=false}={}){if(!e){throw new ArgError("argument specification object is required","ARG_CONFIG_NO_SPEC")}const a={_:[]};const s={};const o={};for(const r of Object.keys(e)){if(!r){throw new ArgError("argument key cannot be an empty string","ARG_CONFIG_EMPTY_KEY")}if(r[0]!=="-"){throw new ArgError(`argument key must start with '-' but found: '${r}'`,"ARG_CONFIG_NONOPT_KEY")}if(r.length===1){throw new ArgError(`argument key must have a name; singular '-' keys are not allowed: ${r}`,"ARG_CONFIG_NONAME_KEY")}if(typeof e[r]==="string"){s[r]=e[r];continue}let i=e[r];let n=false;if(Array.isArray(i)&&i.length===1&&typeof i[0]==="function"){const[e]=i;i=(t,r,i=[])=>{i.push(e(t,r,i[i.length-1]));return i};n=e===Boolean||e[t]===true}else if(typeof i==="function"){n=i===Boolean||i[t]===true}else{throw new ArgError(`type missing or not a function or valid array type: ${r}`,"ARG_CONFIG_VAD_TYPE")}if(r[1]!=="-"&&r.length>2){throw new ArgError(`short argument keys (with a single hyphen) must have only one character: ${r}`,"ARG_CONFIG_SHORTOPT_TOOLONG")}o[r]=[i,n]}for(let e=0,t=r.length;e<t;e++){const t=r[e];if(n&&a._.length>0){a._=a._.concat(r.slice(e));break}if(t==="--"){a._=a._.concat(r.slice(e+1));break}if(t.length>1&&t[0]==="-"){const n=t[1]==="-"||t.length===2?[t]:t.slice(1).split("").map((e=>`-${e}`));for(let t=0;t<n.length;t++){const c=n[t];const[l,u]=c[1]==="-"?c.split(/=(.*)/,2):[c,undefined];let h=l;while(h in s){h=s[h]}if(!(h in o)){if(i){a._.push(c);continue}else{throw new ArgError(`unknown or unexpected option: ${l}`,"ARG_UNKNOWN_OPTION")}}const[p,d]=o[h];if(!d&&t+1<n.length){throw new ArgError(`option requires argument (but was followed by another short argument): ${l}`,"ARG_MISSING_REQUIRED_SHORTARG")}if(d){a[h]=p(true,h,a[h])}else if(u===undefined){if(r.length<e+2||r[e+1].length>1&&r[e+1][0]==="-"&&!(r[e+1].match(/^-?\d*(\.(?=\d))?\d*$/)&&(p===Number||typeof BigInt!=="undefined"&&p===BigInt))){const e=l===h?"":` (alias for ${h})`;throw new ArgError(`option requires argument: ${l}${e}`,"ARG_MISSING_REQUIRED_LONGARG")}a[h]=p(r[e+1],h,a[h]);++e}else{a[h]=p(u,h,a[h])}}}else{a._.push(t)}}return a}arg.flag=e=>{e[t]=true;return e};arg.COUNT=arg.flag(((e,t,r)=>(r||0)+1));arg.ArgError=ArgError;e.exports=arg},835:e=>{"use strict";e.exports=balanced;function balanced(e,t,r){if(e instanceof RegExp)e=maybeMatch(e,r);if(t instanceof RegExp)t=maybeMatch(t,r);var i=range(e,t,r);return i&&{start:i[0],end:i[1],pre:r.slice(0,i[0]),body:r.slice(i[0]+e.length,i[1]),post:r.slice(i[1]+t.length)}}function maybeMatch(e,t){var r=t.match(e);return r?r[0]:null}balanced.range=range;function range(e,t,r){var i,n,a,s,o;var c=r.indexOf(e);var l=r.indexOf(t,c+1);var u=c;if(c>=0&&l>0){i=[];a=r.length;while(u>=0&&!o){if(u==c){i.push(u);c=r.indexOf(e,u+1)}else if(i.length==1){o=[i.pop(),l]}else{n=i.pop();if(n<a){a=n;s=l}l=r.indexOf(t,u+1)}u=c<l&&c>=0?c:l}if(i.length){o=[a,s]}}return o}},215:(e,t,r)=>{var i=r(551);var n=r(835);e.exports=expandTop;var a="\0SLASH"+Math.random()+"\0";var s="\0OPEN"+Math.random()+"\0";var o="\0CLOSE"+Math.random()+"\0";var c="\0COMMA"+Math.random()+"\0";var l="\0PERIOD"+Math.random()+"\0";function numeric(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function escapeBraces(e){return e.split("\\\\").join(a).split("\\{").join(s).split("\\}").join(o).split("\\,").join(c).split("\\.").join(l)}function unescapeBraces(e){return e.split(a).join("\\").split(s).join("{").split(o).join("}").split(c).join(",").split(l).join(".")}function parseCommaParts(e){if(!e)return[""];var t=[];var r=n("{","}",e);if(!r)return e.split(",");var i=r.pre;var a=r.body;var s=r.post;var o=i.split(",");o[o.length-1]+="{"+a+"}";var c=parseCommaParts(s);if(s.length){o[o.length-1]+=c.shift();o.push.apply(o,c)}t.push.apply(t,o);return t}function expandTop(e){if(!e)return[];if(e.substr(0,2)==="{}"){e="\\{\\}"+e.substr(2)}return expand(escapeBraces(e),true).map(unescapeBraces)}function identity(e){return e}function embrace(e){return"{"+e+"}"}function isPadded(e){return/^-?0\d/.test(e)}function lte(e,t){return e<=t}function gte(e,t){return e>=t}function expand(e,t){var r=[];var a=n("{","}",e);if(!a||/\$$/.test(a.pre))return[e];var s=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(a.body);var c=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(a.body);var l=s||c;var u=a.body.indexOf(",")>=0;if(!l&&!u){if(a.post.match(/,.*\}/)){e=a.pre+"{"+a.body+o+a.post;return expand(e)}return[e]}var h;if(l){h=a.body.split(/\.\./)}else{h=parseCommaParts(a.body);if(h.length===1){h=expand(h[0],false).map(embrace);if(h.length===1){var p=a.post.length?expand(a.post,false):[""];return p.map((function(e){return a.pre+h[0]+e}))}}}var d=a.pre;var p=a.post.length?expand(a.post,false):[""];var m;if(l){var g=numeric(h[0]);var v=numeric(h[1]);var y=Math.max(h[0].length,h[1].length);var b=h.length==3?Math.abs(numeric(h[2])):1;var _=lte;var w=v<g;if(w){b*=-1;_=gte}var k=h.some(isPadded);m=[];for(var E=g;_(E,v);E+=b){var S;if(c){S=String.fromCharCode(E);if(S==="\\")S=""}else{S=String(E);if(k){var x=y-S.length;if(x>0){var O=new Array(x+1).join("0");if(E<0)S="-"+O+S.slice(1);else S=O+S}}}m.push(S)}}else{m=i(h,(function(e){return expand(e,false)}))}for(var A=0;A<m.length;A++){for(var N=0;N<p.length;N++){var j=d+m[A]+p[N];if(!t||l||j)r.push(j)}}return r}},551:e=>{e.exports=function(e,r){var i=[];for(var n=0;n<e.length;n++){var a=r(e[n],n);if(t(a))i.push.apply(i,a);else i.push(a)}return i};var t=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},909:(e,t,r)=>{e.exports=realpath;realpath.realpath=realpath;realpath.sync=realpathSync;realpath.realpathSync=realpathSync;realpath.monkeypatch=monkeypatch;realpath.unmonkeypatch=unmonkeypatch;var i=r(747);var n=i.realpath;var a=i.realpathSync;var s=process.version;var o=/^v[0-5]\./.test(s);var c=r(411);function newError(e){return e&&e.syscall==="realpath"&&(e.code==="ELOOP"||e.code==="ENOMEM"||e.code==="ENAMETOOLONG")}function realpath(e,t,r){if(o){return n(e,t,r)}if(typeof t==="function"){r=t;t=null}n(e,t,(function(i,n){if(newError(i)){c.realpath(e,t,r)}else{r(i,n)}}))}function realpathSync(e,t){if(o){return a(e,t)}try{return a(e,t)}catch(r){if(newError(r)){return c.realpathSync(e,t)}else{throw r}}}function monkeypatch(){i.realpath=realpath;i.realpathSync=realpathSync}function unmonkeypatch(){i.realpath=n;i.realpathSync=a}},411:(e,t,r)=>{var i=r(622);var n=process.platform==="win32";var a=r(747);var s=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function rethrow(){var e;if(s){var t=new Error;e=debugCallback}else e=missingCallback;return e;function debugCallback(e){if(e){t.message=e.message;e=t;missingCallback(e)}}function missingCallback(e){if(e){if(process.throwDeprecation)throw e;else if(!process.noDeprecation){var t="fs: missing callback "+(e.stack||e.message);if(process.traceDeprecation)console.trace(t);else console.error(t)}}}}function maybeCallback(e){return typeof e==="function"?e:rethrow()}var o=i.normalize;if(n){var c=/(.*?)(?:[\/\\]+|$)/g}else{var c=/(.*?)(?:[\/]+|$)/g}if(n){var l=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/}else{var l=/^[\/]*/}t.realpathSync=function realpathSync(e,t){e=i.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return t[e]}var r=e,s={},o={};var u;var h;var p;var d;start();function start(){var t=l.exec(e);u=t[0].length;h=t[0];p=t[0];d="";if(n&&!o[p]){a.lstatSync(p);o[p]=true}}while(u<e.length){c.lastIndex=u;var m=c.exec(e);d=h;h+=m[0];p=d+m[1];u=c.lastIndex;if(o[p]||t&&t[p]===p){continue}var g;if(t&&Object.prototype.hasOwnProperty.call(t,p)){g=t[p]}else{var v=a.lstatSync(p);if(!v.isSymbolicLink()){o[p]=true;if(t)t[p]=p;continue}var y=null;if(!n){var b=v.dev.toString(32)+":"+v.ino.toString(32);if(s.hasOwnProperty(b)){y=s[b]}}if(y===null){a.statSync(p);y=a.readlinkSync(p)}g=i.resolve(d,y);if(t)t[p]=g;if(!n)s[b]=y}e=i.resolve(g,e.slice(u));start()}if(t)t[r]=e;return e};t.realpath=function realpath(e,t,r){if(typeof r!=="function"){r=maybeCallback(t);t=null}e=i.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return process.nextTick(r.bind(null,null,t[e]))}var s=e,o={},u={};var h;var p;var d;var m;start();function start(){var t=l.exec(e);h=t[0].length;p=t[0];d=t[0];m="";if(n&&!u[d]){a.lstat(d,(function(e){if(e)return r(e);u[d]=true;LOOP()}))}else{process.nextTick(LOOP)}}function LOOP(){if(h>=e.length){if(t)t[s]=e;return r(null,e)}c.lastIndex=h;var i=c.exec(e);m=p;p+=i[0];d=m+i[1];h=c.lastIndex;if(u[d]||t&&t[d]===d){return process.nextTick(LOOP)}if(t&&Object.prototype.hasOwnProperty.call(t,d)){return gotResolvedLink(t[d])}return a.lstat(d,gotStat)}function gotStat(e,i){if(e)return r(e);if(!i.isSymbolicLink()){u[d]=true;if(t)t[d]=d;return process.nextTick(LOOP)}if(!n){var s=i.dev.toString(32)+":"+i.ino.toString(32);if(o.hasOwnProperty(s)){return gotTarget(null,o[s],d)}}a.stat(d,(function(e){if(e)return r(e);a.readlink(d,(function(e,t){if(!n)o[s]=t;gotTarget(e,t)}))}))}function gotTarget(e,n,a){if(e)return r(e);var s=i.resolve(m,n);if(t)t[a]=s;gotResolvedLink(s)}function gotResolvedLink(t){e=i.resolve(t,e.slice(h));start()}}},74:(e,t,r)=>{"use strict";const i=r(747);const n=r(622);const a=r(833);function readSizeRecursive(e,t,r,s){let o;let c;if(!s){o=r;c=null}else{o=s;c=r}i.lstat(t,(function lstat(r,s){let l=!r?s.size||0:0;if(s){if(e.has(s.ino)){return o(null,0)}e.add(s.ino)}if(!r&&s.isDirectory()){i.readdir(t,((r,i)=>{if(r){return o(r)}a(i,5e3,((r,i)=>{readSizeRecursive(e,n.join(t,r),c,((e,t)=>{if(!e){l+=t}i(e)}))}),(e=>{o(e,l)}))}))}else{if(c&&c.test(t)){l=0}o(r,l)}}))}e.exports=(...e)=>{e.unshift(new Set);return readSizeRecursive(...e)}},744:(e,t,r)=>{t.setopts=setopts;t.ownProp=ownProp;t.makeAbs=makeAbs;t.finish=finish;t.mark=mark;t.isIgnored=isIgnored;t.childrenIgnored=childrenIgnored;function ownProp(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var i=r(622);var n=r(642);var a=r(963);var s=n.Minimatch;function alphasort(e,t){return e.localeCompare(t,"en")}function setupIgnores(e,t){e.ignore=t.ignore||[];if(!Array.isArray(e.ignore))e.ignore=[e.ignore];if(e.ignore.length){e.ignore=e.ignore.map(ignoreMap)}}function ignoreMap(e){var t=null;if(e.slice(-3)==="/**"){var r=e.replace(/(\/\*\*)+$/,"");t=new s(r,{dot:true})}return{matcher:new s(e,{dot:true}),gmatcher:t}}function setopts(e,t,r){if(!r)r={};if(r.matchBase&&-1===t.indexOf("/")){if(r.noglobstar){throw new Error("base matching requires globstar")}t="**/"+t}e.silent=!!r.silent;e.pattern=t;e.strict=r.strict!==false;e.realpath=!!r.realpath;e.realpathCache=r.realpathCache||Object.create(null);e.follow=!!r.follow;e.dot=!!r.dot;e.mark=!!r.mark;e.nodir=!!r.nodir;if(e.nodir)e.mark=true;e.sync=!!r.sync;e.nounique=!!r.nounique;e.nonull=!!r.nonull;e.nosort=!!r.nosort;e.nocase=!!r.nocase;e.stat=!!r.stat;e.noprocess=!!r.noprocess;e.absolute=!!r.absolute;e.maxLength=r.maxLength||Infinity;e.cache=r.cache||Object.create(null);e.statCache=r.statCache||Object.create(null);e.symlinks=r.symlinks||Object.create(null);setupIgnores(e,r);e.changedCwd=false;var n=process.cwd();if(!ownProp(r,"cwd"))e.cwd=n;else{e.cwd=i.resolve(r.cwd);e.changedCwd=e.cwd!==n}e.root=r.root||i.resolve(e.cwd,"/");e.root=i.resolve(e.root);if(process.platform==="win32")e.root=e.root.replace(/\\/g,"/");e.cwdAbs=a(e.cwd)?e.cwd:makeAbs(e,e.cwd);if(process.platform==="win32")e.cwdAbs=e.cwdAbs.replace(/\\/g,"/");e.nomount=!!r.nomount;r.nonegate=true;r.nocomment=true;e.minimatch=new s(t,r);e.options=e.minimatch.options}function finish(e){var t=e.nounique;var r=t?[]:Object.create(null);for(var i=0,n=e.matches.length;i<n;i++){var a=e.matches[i];if(!a||Object.keys(a).length===0){if(e.nonull){var s=e.minimatch.globSet[i];if(t)r.push(s);else r[s]=true}}else{var o=Object.keys(a);if(t)r.push.apply(r,o);else o.forEach((function(e){r[e]=true}))}}if(!t)r=Object.keys(r);if(!e.nosort)r=r.sort(alphasort);if(e.mark){for(var i=0;i<r.length;i++){r[i]=e._mark(r[i])}if(e.nodir){r=r.filter((function(t){var r=!/\/$/.test(t);var i=e.cache[t]||e.cache[makeAbs(e,t)];if(r&&i)r=i!=="DIR"&&!Array.isArray(i);return r}))}}if(e.ignore.length)r=r.filter((function(t){return!isIgnored(e,t)}));e.found=r}function mark(e,t){var r=makeAbs(e,t);var i=e.cache[r];var n=t;if(i){var a=i==="DIR"||Array.isArray(i);var s=t.slice(-1)==="/";if(a&&!s)n+="/";else if(!a&&s)n=n.slice(0,-1);if(n!==t){var o=makeAbs(e,n);e.statCache[o]=e.statCache[r];e.cache[o]=e.cache[r]}}return n}function makeAbs(e,t){var r=t;if(t.charAt(0)==="/"){r=i.join(e.root,t)}else if(a(t)||t===""){r=t}else if(e.changedCwd){r=i.resolve(e.cwd,t)}else{r=i.resolve(t)}if(process.platform==="win32")r=r.replace(/\\/g,"/");return r}function isIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some((function(e){return e.matcher.match(t)||!!(e.gmatcher&&e.gmatcher.match(t))}))}function childrenIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some((function(e){return!!(e.gmatcher&&e.gmatcher.match(t))}))}},750:(e,t,r)=>{e.exports=glob;var i=r(747);var n=r(909);var a=r(642);var s=a.Minimatch;var o=r(309);var c=r(614).EventEmitter;var l=r(622);var u=r(357);var h=r(963);var p=r(381);var d=r(744);var m=d.setopts;var g=d.ownProp;var v=r(753);var y=r(669);var b=d.childrenIgnored;var _=d.isIgnored;var w=r(481);function glob(e,t,r){if(typeof t==="function")r=t,t={};if(!t)t={};if(t.sync){if(r)throw new TypeError("callback provided to sync glob");return p(e,t)}return new Glob(e,t,r)}glob.sync=p;var k=glob.GlobSync=p.GlobSync;glob.glob=glob;function extend(e,t){if(t===null||typeof t!=="object"){return e}var r=Object.keys(t);var i=r.length;while(i--){e[r[i]]=t[r[i]]}return e}glob.hasMagic=function(e,t){var r=extend({},t);r.noprocess=true;var i=new Glob(e,r);var n=i.minimatch.set;if(!e)return false;if(n.length>1)return true;for(var a=0;a<n[0].length;a++){if(typeof n[0][a]!=="string")return true}return false};glob.Glob=Glob;o(Glob,c);function Glob(e,t,r){if(typeof t==="function"){r=t;t=null}if(t&&t.sync){if(r)throw new TypeError("callback provided to sync glob");return new k(e,t)}if(!(this instanceof Glob))return new Glob(e,t,r);m(this,e,t);this._didRealPath=false;var i=this.minimatch.set.length;this.matches=new Array(i);if(typeof r==="function"){r=w(r);this.on("error",r);this.on("end",(function(e){r(null,e)}))}var n=this;this._processing=0;this._emitQueue=[];this._processQueue=[];this.paused=false;if(this.noprocess)return this;if(i===0)return done();var a=true;for(var s=0;s<i;s++){this._process(this.minimatch.set[s],s,false,done)}a=false;function done(){--n._processing;if(n._processing<=0){if(a){process.nextTick((function(){n._finish()}))}else{n._finish()}}}}Glob.prototype._finish=function(){u(this instanceof Glob);if(this.aborted)return;if(this.realpath&&!this._didRealpath)return this._realpath();d.finish(this);this.emit("end",this.found)};Glob.prototype._realpath=function(){if(this._didRealpath)return;this._didRealpath=true;var e=this.matches.length;if(e===0)return this._finish();var t=this;for(var r=0;r<this.matches.length;r++)this._realpathSet(r,next);function next(){if(--e===0)t._finish()}};Glob.prototype._realpathSet=function(e,t){var r=this.matches[e];if(!r)return t();var i=Object.keys(r);var a=this;var s=i.length;if(s===0)return t();var o=this.matches[e]=Object.create(null);i.forEach((function(r,i){r=a._makeAbs(r);n.realpath(r,a.realpathCache,(function(i,n){if(!i)o[n]=true;else if(i.syscall==="stat")o[r]=true;else a.emit("error",i);if(--s===0){a.matches[e]=o;t()}}))}))};Glob.prototype._mark=function(e){return d.mark(this,e)};Glob.prototype._makeAbs=function(e){return d.makeAbs(this,e)};Glob.prototype.abort=function(){this.aborted=true;this.emit("abort")};Glob.prototype.pause=function(){if(!this.paused){this.paused=true;this.emit("pause")}};Glob.prototype.resume=function(){if(this.paused){this.emit("resume");this.paused=false;if(this._emitQueue.length){var e=this._emitQueue.slice(0);this._emitQueue.length=0;for(var t=0;t<e.length;t++){var r=e[t];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var i=this._processQueue.slice(0);this._processQueue.length=0;for(var t=0;t<i.length;t++){var n=i[t];this._processing--;this._process(n[0],n[1],n[2],n[3])}}}};Glob.prototype._process=function(e,t,r,i){u(this instanceof Glob);u(typeof i==="function");if(this.aborted)return;this._processing++;if(this.paused){this._processQueue.push([e,t,r,i]);return}var n=0;while(typeof e[n]==="string"){n++}var s;switch(n){case e.length:this._processSimple(e.join("/"),t,i);return;case 0:s=null;break;default:s=e.slice(0,n).join("/");break}var o=e.slice(n);var c;if(s===null)c=".";else if(h(s)||h(e.join("/"))){if(!s||!h(s))s="/"+s;c=s}else c=s;var l=this._makeAbs(c);if(b(this,c))return i();var p=o[0]===a.GLOBSTAR;if(p)this._processGlobStar(s,c,l,o,t,r,i);else this._processReaddir(s,c,l,o,t,r,i)};Glob.prototype._processReaddir=function(e,t,r,i,n,a,s){var o=this;this._readdir(r,a,(function(c,l){return o._processReaddir2(e,t,r,i,n,a,l,s)}))};Glob.prototype._processReaddir2=function(e,t,r,i,n,a,s,o){if(!s)return o();var c=i[0];var u=!!this.minimatch.negate;var h=c._glob;var p=this.dot||h.charAt(0)===".";var d=[];for(var m=0;m<s.length;m++){var g=s[m];if(g.charAt(0)!=="."||p){var v;if(u&&!e){v=!g.match(c)}else{v=g.match(c)}if(v)d.push(g)}}var y=d.length;if(y===0)return o();if(i.length===1&&!this.mark&&!this.stat){if(!this.matches[n])this.matches[n]=Object.create(null);for(var m=0;m<y;m++){var g=d[m];if(e){if(e!=="/")g=e+"/"+g;else g=e+g}if(g.charAt(0)==="/"&&!this.nomount){g=l.join(this.root,g)}this._emitMatch(n,g)}return o()}i.shift();for(var m=0;m<y;m++){var g=d[m];var b;if(e){if(e!=="/")g=e+"/"+g;else g=e+g}this._process([g].concat(i),n,a,o)}o()};Glob.prototype._emitMatch=function(e,t){if(this.aborted)return;if(_(this,t))return;if(this.paused){this._emitQueue.push([e,t]);return}var r=h(t)?t:this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute)t=r;if(this.matches[e][t])return;if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[e][t]=true;var n=this.statCache[r];if(n)this.emit("stat",t,n);this.emit("match",t)};Glob.prototype._readdirInGlobStar=function(e,t){if(this.aborted)return;if(this.follow)return this._readdir(e,false,t);var r="lstat\0"+e;var n=this;var a=v(r,lstatcb_);if(a)i.lstat(e,a);function lstatcb_(r,i){if(r&&r.code==="ENOENT")return t();var a=i&&i.isSymbolicLink();n.symlinks[e]=a;if(!a&&i&&!i.isDirectory()){n.cache[e]="FILE";t()}else n._readdir(e,false,t)}};Glob.prototype._readdir=function(e,t,r){if(this.aborted)return;r=v("readdir\0"+e+"\0"+t,r);if(!r)return;if(t&&!g(this.symlinks,e))return this._readdirInGlobStar(e,r);if(g(this.cache,e)){var n=this.cache[e];if(!n||n==="FILE")return r();if(Array.isArray(n))return r(null,n)}var a=this;i.readdir(e,readdirCb(this,e,r))};function readdirCb(e,t,r){return function(i,n){if(i)e._readdirError(t,i,r);else e._readdirEntries(t,n,r)}}Glob.prototype._readdirEntries=function(e,t,r){if(this.aborted)return;if(!this.mark&&!this.stat){for(var i=0;i<t.length;i++){var n=t[i];if(e==="/")n=e+n;else n=e+"/"+n;this.cache[n]=true}}this.cache[e]=t;return r(null,t)};Glob.prototype._readdirError=function(e,t,r){if(this.aborted)return;switch(t.code){case"ENOTSUP":case"ENOTDIR":var i=this._makeAbs(e);this.cache[i]="FILE";if(i===this.cwdAbs){var n=new Error(t.code+" invalid cwd "+this.cwd);n.path=this.cwd;n.code=t.code;this.emit("error",n);this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict){this.emit("error",t);this.abort()}if(!this.silent)console.error("glob error",t);break}return r()};Glob.prototype._processGlobStar=function(e,t,r,i,n,a,s){var o=this;this._readdir(r,a,(function(c,l){o._processGlobStar2(e,t,r,i,n,a,l,s)}))};Glob.prototype._processGlobStar2=function(e,t,r,i,n,a,s,o){if(!s)return o();var c=i.slice(1);var l=e?[e]:[];var u=l.concat(c);this._process(u,n,false,o);var h=this.symlinks[r];var p=s.length;if(h&&a)return o();for(var d=0;d<p;d++){var m=s[d];if(m.charAt(0)==="."&&!this.dot)continue;var g=l.concat(s[d],c);this._process(g,n,true,o);var v=l.concat(s[d],i);this._process(v,n,true,o)}o()};Glob.prototype._processSimple=function(e,t,r){var i=this;this._stat(e,(function(n,a){i._processSimple2(e,t,n,a,r)}))};Glob.prototype._processSimple2=function(e,t,r,i,n){if(!this.matches[t])this.matches[t]=Object.create(null);if(!i)return n();if(e&&h(e)&&!this.nomount){var a=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=l.join(this.root,e)}else{e=l.resolve(this.root,e);if(a)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e);n()};Glob.prototype._stat=function(e,t){var r=this._makeAbs(e);var n=e.slice(-1)==="/";if(e.length>this.maxLength)return t();if(!this.stat&&g(this.cache,r)){var a=this.cache[r];if(Array.isArray(a))a="DIR";if(!n||a==="DIR")return t(null,a);if(n&&a==="FILE")return t()}var s;var o=this.statCache[r];if(o!==undefined){if(o===false)return t(null,o);else{var c=o.isDirectory()?"DIR":"FILE";if(n&&c==="FILE")return t();else return t(null,c,o)}}var l=this;var u=v("stat\0"+r,lstatcb_);if(u)i.lstat(r,u);function lstatcb_(n,a){if(a&&a.isSymbolicLink()){return i.stat(r,(function(i,n){if(i)l._stat2(e,r,null,a,t);else l._stat2(e,r,i,n,t)}))}else{l._stat2(e,r,n,a,t)}}};Glob.prototype._stat2=function(e,t,r,i,n){if(r&&(r.code==="ENOENT"||r.code==="ENOTDIR")){this.statCache[t]=false;return n()}var a=e.slice(-1)==="/";this.statCache[t]=i;if(t.slice(-1)==="/"&&i&&!i.isDirectory())return n(null,false,i);var s=true;if(i)s=i.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||s;if(a&&s==="FILE")return n();return n(null,s,i)}},381:(e,t,r)=>{e.exports=globSync;globSync.GlobSync=GlobSync;var i=r(747);var n=r(909);var a=r(642);var s=a.Minimatch;var o=r(750).Glob;var c=r(669);var l=r(622);var u=r(357);var h=r(963);var p=r(744);var d=p.setopts;var m=p.ownProp;var g=p.childrenIgnored;var v=p.isIgnored;function globSync(e,t){if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");return new GlobSync(e,t).found}function GlobSync(e,t){if(!e)throw new Error("must provide pattern");if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof GlobSync))return new GlobSync(e,t);d(this,e,t);if(this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var i=0;i<r;i++){this._process(this.minimatch.set[i],i,false)}this._finish()}GlobSync.prototype._finish=function(){u(this instanceof GlobSync);if(this.realpath){var e=this;this.matches.forEach((function(t,r){var i=e.matches[r]=Object.create(null);for(var a in t){try{a=e._makeAbs(a);var s=n.realpathSync(a,e.realpathCache);i[s]=true}catch(t){if(t.syscall==="stat")i[e._makeAbs(a)]=true;else throw t}}}))}p.finish(this)};GlobSync.prototype._process=function(e,t,r){u(this instanceof GlobSync);var i=0;while(typeof e[i]==="string"){i++}var n;switch(i){case e.length:this._processSimple(e.join("/"),t);return;case 0:n=null;break;default:n=e.slice(0,i).join("/");break}var s=e.slice(i);var o;if(n===null)o=".";else if(h(n)||h(e.join("/"))){if(!n||!h(n))n="/"+n;o=n}else o=n;var c=this._makeAbs(o);if(g(this,o))return;var l=s[0]===a.GLOBSTAR;if(l)this._processGlobStar(n,o,c,s,t,r);else this._processReaddir(n,o,c,s,t,r)};GlobSync.prototype._processReaddir=function(e,t,r,i,n,a){var s=this._readdir(r,a);if(!s)return;var o=i[0];var c=!!this.minimatch.negate;var u=o._glob;var h=this.dot||u.charAt(0)===".";var p=[];for(var d=0;d<s.length;d++){var m=s[d];if(m.charAt(0)!=="."||h){var g;if(c&&!e){g=!m.match(o)}else{g=m.match(o)}if(g)p.push(m)}}var v=p.length;if(v===0)return;if(i.length===1&&!this.mark&&!this.stat){if(!this.matches[n])this.matches[n]=Object.create(null);for(var d=0;d<v;d++){var m=p[d];if(e){if(e.slice(-1)!=="/")m=e+"/"+m;else m=e+m}if(m.charAt(0)==="/"&&!this.nomount){m=l.join(this.root,m)}this._emitMatch(n,m)}return}i.shift();for(var d=0;d<v;d++){var m=p[d];var y;if(e)y=[e,m];else y=[m];this._process(y.concat(i),n,a)}};GlobSync.prototype._emitMatch=function(e,t){if(v(this,t))return;var r=this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute){t=r}if(this.matches[e][t])return;if(this.nodir){var i=this.cache[r];if(i==="DIR"||Array.isArray(i))return}this.matches[e][t]=true;if(this.stat)this._stat(t)};GlobSync.prototype._readdirInGlobStar=function(e){if(this.follow)return this._readdir(e,false);var t;var r;var n;try{r=i.lstatSync(e)}catch(e){if(e.code==="ENOENT"){return null}}var a=r&&r.isSymbolicLink();this.symlinks[e]=a;if(!a&&r&&!r.isDirectory())this.cache[e]="FILE";else t=this._readdir(e,false);return t};GlobSync.prototype._readdir=function(e,t){var r;if(t&&!m(this.symlinks,e))return this._readdirInGlobStar(e);if(m(this.cache,e)){var n=this.cache[e];if(!n||n==="FILE")return null;if(Array.isArray(n))return n}try{return this._readdirEntries(e,i.readdirSync(e))}catch(t){this._readdirError(e,t);return null}};GlobSync.prototype._readdirEntries=function(e,t){if(!this.mark&&!this.stat){for(var r=0;r<t.length;r++){var i=t[r];if(e==="/")i=e+i;else i=e+"/"+i;this.cache[i]=true}}this.cache[e]=t;return t};GlobSync.prototype._readdirError=function(e,t){switch(t.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(e);this.cache[r]="FILE";if(r===this.cwdAbs){var i=new Error(t.code+" invalid cwd "+this.cwd);i.path=this.cwd;i.code=t.code;throw i}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict)throw t;if(!this.silent)console.error("glob error",t);break}};GlobSync.prototype._processGlobStar=function(e,t,r,i,n,a){var s=this._readdir(r,a);if(!s)return;var o=i.slice(1);var c=e?[e]:[];var l=c.concat(o);this._process(l,n,false);var u=s.length;var h=this.symlinks[r];if(h&&a)return;for(var p=0;p<u;p++){var d=s[p];if(d.charAt(0)==="."&&!this.dot)continue;var m=c.concat(s[p],o);this._process(m,n,true);var g=c.concat(s[p],i);this._process(g,n,true)}};GlobSync.prototype._processSimple=function(e,t){var r=this._stat(e);if(!this.matches[t])this.matches[t]=Object.create(null);if(!r)return;if(e&&h(e)&&!this.nomount){var i=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=l.join(this.root,e)}else{e=l.resolve(this.root,e);if(i)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e)};GlobSync.prototype._stat=function(e){var t=this._makeAbs(e);var r=e.slice(-1)==="/";if(e.length>this.maxLength)return false;if(!this.stat&&m(this.cache,t)){var n=this.cache[t];if(Array.isArray(n))n="DIR";if(!r||n==="DIR")return n;if(r&&n==="FILE")return false}var a;var s=this.statCache[t];if(!s){var o;try{o=i.lstatSync(t)}catch(e){if(e&&(e.code==="ENOENT"||e.code==="ENOTDIR")){this.statCache[t]=false;return false}}if(o&&o.isSymbolicLink()){try{s=i.statSync(t)}catch(e){s=o}}else{s=o}}this.statCache[t]=s;var n=true;if(s)n=s.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||n;if(r&&n==="FILE")return false;return n};GlobSync.prototype._mark=function(e){return p.mark(this,e)};GlobSync.prototype._makeAbs=function(e){return p.makeAbs(this,e)}},753:(e,t,r)=>{var i=r(687);var n=Object.create(null);var a=r(481);e.exports=i(inflight);function inflight(e,t){if(n[e]){n[e].push(t);return null}else{n[e]=[t];return makeres(e)}}function makeres(e){return a((function RES(){var t=n[e];var r=t.length;var i=slice(arguments);try{for(var a=0;a<r;a++){t[a].apply(null,i)}}finally{if(t.length>r){t.splice(0,r);process.nextTick((function(){RES.apply(null,i)}))}else{delete n[e]}}}))}function slice(e){var t=e.length;var r=[];for(var i=0;i<t;i++)r[i]=e[i];return r}},309:(e,t,r)=>{try{var i=r(669);if(typeof i.inherits!=="function")throw"";e.exports=i.inherits}catch(t){e.exports=r(474)}},474:e=>{if(typeof Object.create==="function"){e.exports=function inherits(e,t){if(t){e.super_=t;e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}})}}}else{e.exports=function inherits(e,t){if(t){e.super_=t;var TempCtor=function(){};TempCtor.prototype=t.prototype;e.prototype=new TempCtor;e.prototype.constructor=e}}}},642:(e,t,r)=>{e.exports=minimatch;minimatch.Minimatch=Minimatch;var i={sep:"/"};try{i=r(622)}catch(e){}var n=minimatch.GLOBSTAR=Minimatch.GLOBSTAR={};var a=r(215);var s={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}};var o="[^/]";var c=o+"*?";var l="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?";var u="(?:(?!(?:\\/|^)\\.).)*?";var h=charSet("().*{}+?[]^$\\!");function charSet(e){return e.split("").reduce((function(e,t){e[t]=true;return e}),{})}var p=/\/+/;minimatch.filter=filter;function filter(e,t){t=t||{};return function(r,i,n){return minimatch(r,e,t)}}function ext(e,t){e=e||{};t=t||{};var r={};Object.keys(t).forEach((function(e){r[e]=t[e]}));Object.keys(e).forEach((function(t){r[t]=e[t]}));return r}minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return minimatch;var t=minimatch;var r=function minimatch(r,i,n){return t.minimatch(r,i,ext(e,n))};r.Minimatch=function Minimatch(r,i){return new t.Minimatch(r,ext(e,i))};return r};Minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return Minimatch;return minimatch.defaults(e).Minimatch};function minimatch(e,t,r){if(typeof t!=="string"){throw new TypeError("glob pattern string required")}if(!r)r={};if(!r.nocomment&&t.charAt(0)==="#"){return false}if(t.trim()==="")return e==="";return new Minimatch(t,r).match(e)}function Minimatch(e,t){if(!(this instanceof Minimatch)){return new Minimatch(e,t)}if(typeof e!=="string"){throw new TypeError("glob pattern string required")}if(!t)t={};e=e.trim();if(i.sep!=="/"){e=e.split(i.sep).join("/")}this.options=t;this.set=[];this.pattern=e;this.regexp=null;this.negate=false;this.comment=false;this.empty=false;this.make()}Minimatch.prototype.debug=function(){};Minimatch.prototype.make=make;function make(){if(this._made)return;var e=this.pattern;var t=this.options;if(!t.nocomment&&e.charAt(0)==="#"){this.comment=true;return}if(!e){this.empty=true;return}this.parseNegate();var r=this.globSet=this.braceExpand();if(t.debug)this.debug=console.error;this.debug(this.pattern,r);r=this.globParts=r.map((function(e){return e.split(p)}));this.debug(this.pattern,r);r=r.map((function(e,t,r){return e.map(this.parse,this)}),this);this.debug(this.pattern,r);r=r.filter((function(e){return e.indexOf(false)===-1}));this.debug(this.pattern,r);this.set=r}Minimatch.prototype.parseNegate=parseNegate;function parseNegate(){var e=this.pattern;var t=false;var r=this.options;var i=0;if(r.nonegate)return;for(var n=0,a=e.length;n<a&&e.charAt(n)==="!";n++){t=!t;i++}if(i)this.pattern=e.substr(i);this.negate=t}minimatch.braceExpand=function(e,t){return braceExpand(e,t)};Minimatch.prototype.braceExpand=braceExpand;function braceExpand(e,t){if(!t){if(this instanceof Minimatch){t=this.options}else{t={}}}e=typeof e==="undefined"?this.pattern:e;if(typeof e==="undefined"){throw new TypeError("undefined pattern")}if(t.nobrace||!e.match(/\{.*\}/)){return[e]}return a(e)}Minimatch.prototype.parse=parse;var d={};function parse(e,t){if(e.length>1024*64){throw new TypeError("pattern is too long")}var r=this.options;if(!r.noglobstar&&e==="**")return n;if(e==="")return"";var i="";var a=!!r.nocase;var l=false;var u=[];var p=[];var m;var g=false;var v=-1;var y=-1;var b=e.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)";var _=this;function clearStateChar(){if(m){switch(m){case"*":i+=c;a=true;break;case"?":i+=o;a=true;break;default:i+="\\"+m;break}_.debug("clearStateChar %j %j",m,i);m=false}}for(var w=0,k=e.length,E;w<k&&(E=e.charAt(w));w++){this.debug("%s\t%s %s %j",e,w,i,E);if(l&&h[E]){i+="\\"+E;l=false;continue}switch(E){case"/":return false;case"\\":clearStateChar();l=true;continue;case"?":case"*":case"+":case"@":case"!":this.debug("%s\t%s %s %j <-- stateChar",e,w,i,E);if(g){this.debug("  in class");if(E==="!"&&w===y+1)E="^";i+=E;continue}_.debug("call clearStateChar %j",m);clearStateChar();m=E;if(r.noext)clearStateChar();continue;case"(":if(g){i+="(";continue}if(!m){i+="\\(";continue}u.push({type:m,start:w-1,reStart:i.length,open:s[m].open,close:s[m].close});i+=m==="!"?"(?:(?!(?:":"(?:";this.debug("plType %j %j",m,i);m=false;continue;case")":if(g||!u.length){i+="\\)";continue}clearStateChar();a=true;var S=u.pop();i+=S.close;if(S.type==="!"){p.push(S)}S.reEnd=i.length;continue;case"|":if(g||!u.length||l){i+="\\|";l=false;continue}clearStateChar();i+="|";continue;case"[":clearStateChar();if(g){i+="\\"+E;continue}g=true;y=w;v=i.length;i+=E;continue;case"]":if(w===y+1||!g){i+="\\"+E;l=false;continue}if(g){var x=e.substring(y+1,w);try{RegExp("["+x+"]")}catch(e){var O=this.parse(x,d);i=i.substr(0,v)+"\\["+O[0]+"\\]";a=a||O[1];g=false;continue}}a=true;g=false;i+=E;continue;default:clearStateChar();if(l){l=false}else if(h[E]&&!(E==="^"&&g)){i+="\\"}i+=E}}if(g){x=e.substr(y+1);O=this.parse(x,d);i=i.substr(0,v)+"\\["+O[0];a=a||O[1]}for(S=u.pop();S;S=u.pop()){var A=i.slice(S.reStart+S.open.length);this.debug("setting tail",i,S);A=A.replace(/((?:\\{2}){0,64})(\\?)\|/g,(function(e,t,r){if(!r){r="\\"}return t+t+r+"|"}));this.debug("tail=%j\n   %s",A,A,S,i);var N=S.type==="*"?c:S.type==="?"?o:"\\"+S.type;a=true;i=i.slice(0,S.reStart)+N+"\\("+A}clearStateChar();if(l){i+="\\\\"}var j=false;switch(i.charAt(0)){case".":case"[":case"(":j=true}for(var G=p.length-1;G>-1;G--){var M=p[G];var T=i.slice(0,M.reStart);var I=i.slice(M.reStart,M.reEnd-8);var R=i.slice(M.reEnd-8,M.reEnd);var C=i.slice(M.reEnd);R+=C;var D=T.split("(").length-1;var q=C;for(w=0;w<D;w++){q=q.replace(/\)[+*?]?/,"")}C=q;var P="";if(C===""&&t!==d){P="$"}var L=T+I+C+P+R;i=L}if(i!==""&&a){i="(?=.)"+i}if(j){i=b+i}if(t===d){return[i,a]}if(!a){return globUnescape(e)}var $=r.nocase?"i":"";try{var F=new RegExp("^"+i+"$",$)}catch(e){return new RegExp("$.")}F._glob=e;F._src=i;return F}minimatch.makeRe=function(e,t){return new Minimatch(e,t||{}).makeRe()};Minimatch.prototype.makeRe=makeRe;function makeRe(){if(this.regexp||this.regexp===false)return this.regexp;var e=this.set;if(!e.length){this.regexp=false;return this.regexp}var t=this.options;var r=t.noglobstar?c:t.dot?l:u;var i=t.nocase?"i":"";var a=e.map((function(e){return e.map((function(e){return e===n?r:typeof e==="string"?regExpEscape(e):e._src})).join("\\/")})).join("|");a="^(?:"+a+")$";if(this.negate)a="^(?!"+a+").*$";try{this.regexp=new RegExp(a,i)}catch(e){this.regexp=false}return this.regexp}minimatch.match=function(e,t,r){r=r||{};var i=new Minimatch(t,r);e=e.filter((function(e){return i.match(e)}));if(i.options.nonull&&!e.length){e.push(t)}return e};Minimatch.prototype.match=match;function match(e,t){this.debug("match",e,this.pattern);if(this.comment)return false;if(this.empty)return e==="";if(e==="/"&&t)return true;var r=this.options;if(i.sep!=="/"){e=e.split(i.sep).join("/")}e=e.split(p);this.debug(this.pattern,"split",e);var n=this.set;this.debug(this.pattern,"set",n);var a;var s;for(s=e.length-1;s>=0;s--){a=e[s];if(a)break}for(s=0;s<n.length;s++){var o=n[s];var c=e;if(r.matchBase&&o.length===1){c=[a]}var l=this.matchOne(c,o,t);if(l){if(r.flipNegate)return true;return!this.negate}}if(r.flipNegate)return false;return this.negate}Minimatch.prototype.matchOne=function(e,t,r){var i=this.options;this.debug("matchOne",{this:this,file:e,pattern:t});this.debug("matchOne",e.length,t.length);for(var a=0,s=0,o=e.length,c=t.length;a<o&&s<c;a++,s++){this.debug("matchOne loop");var l=t[s];var u=e[a];this.debug(t,l,u);if(l===false)return false;if(l===n){this.debug("GLOBSTAR",[t,l,u]);var h=a;var p=s+1;if(p===c){this.debug("** at the end");for(;a<o;a++){if(e[a]==="."||e[a]===".."||!i.dot&&e[a].charAt(0)===".")return false}return true}while(h<o){var d=e[h];this.debug("\nglobstar while",e,h,t,p,d);if(this.matchOne(e.slice(h),t.slice(p),r)){this.debug("globstar found match!",h,o,d);return true}else{if(d==="."||d===".."||!i.dot&&d.charAt(0)==="."){this.debug("dot detected!",e,h,t,p);break}this.debug("globstar swallow a segment, and continue");h++}}if(r){this.debug("\n>>> no match, partial?",e,h,t,p);if(h===o)return true}return false}var m;if(typeof l==="string"){if(i.nocase){m=u.toLowerCase()===l.toLowerCase()}else{m=u===l}this.debug("string match",l,u,m)}else{m=u.match(l);this.debug("pattern match",l,u,m)}if(!m)return false}if(a===o&&s===c){return true}else if(a===o){return r}else if(s===c){var g=a===o-1&&e[a]==="";return g}throw new Error("wtf?")};function globUnescape(e){return e.replace(/\\(.)/g,"$1")}function regExpEscape(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}},485:(e,t,r)=>{const i=r(714);const n=r(43);const{mkdirpNative:a,mkdirpNativeSync:s}=r(783);const{mkdirpManual:o,mkdirpManualSync:c}=r(415);const{useNative:l,useNativeSync:u}=r(54);const mkdirp=(e,t)=>{e=n(e);t=i(t);return l(t)?a(e,t):o(e,t)};const mkdirpSync=(e,t)=>{e=n(e);t=i(t);return u(t)?s(e,t):c(e,t)};mkdirp.sync=mkdirpSync;mkdirp.native=(e,t)=>a(n(e),i(t));mkdirp.manual=(e,t)=>o(n(e),i(t));mkdirp.nativeSync=(e,t)=>s(n(e),i(t));mkdirp.manualSync=(e,t)=>c(n(e),i(t));e.exports=mkdirp},440:(e,t,r)=>{const{dirname:i}=r(622);const findMade=(e,t,r=undefined)=>{if(r===t)return Promise.resolve();return e.statAsync(t).then((e=>e.isDirectory()?r:undefined),(r=>r.code==="ENOENT"?findMade(e,i(t),t):undefined))};const findMadeSync=(e,t,r=undefined)=>{if(r===t)return undefined;try{return e.statSync(t).isDirectory()?r:undefined}catch(r){return r.code==="ENOENT"?findMadeSync(e,i(t),t):undefined}};e.exports={findMade:findMade,findMadeSync:findMadeSync}},415:(e,t,r)=>{const{dirname:i}=r(622);const mkdirpManual=(e,t,r)=>{t.recursive=false;const n=i(e);if(n===e){return t.mkdirAsync(e,t).catch((e=>{if(e.code!=="EISDIR")throw e}))}return t.mkdirAsync(e,t).then((()=>r||e),(i=>{if(i.code==="ENOENT")return mkdirpManual(n,t).then((r=>mkdirpManual(e,t,r)));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;return t.statAsync(e).then((e=>{if(e.isDirectory())return r;else throw i}),(()=>{throw i}))}))};const mkdirpManualSync=(e,t,r)=>{const n=i(e);t.recursive=false;if(n===e){try{return t.mkdirSync(e,t)}catch(e){if(e.code!=="EISDIR")throw e;else return}}try{t.mkdirSync(e,t);return r||e}catch(i){if(i.code==="ENOENT")return mkdirpManualSync(e,t,mkdirpManualSync(n,t,r));if(i.code!=="EEXIST"&&i.code!=="EROFS")throw i;try{if(!t.statSync(e).isDirectory())throw i}catch(e){throw i}}};e.exports={mkdirpManual:mkdirpManual,mkdirpManualSync:mkdirpManualSync}},783:(e,t,r)=>{const{dirname:i}=r(622);const{findMade:n,findMadeSync:a}=r(440);const{mkdirpManual:s,mkdirpManualSync:o}=r(415);const mkdirpNative=(e,t)=>{t.recursive=true;const r=i(e);if(r===e)return t.mkdirAsync(e,t);return n(t,e).then((r=>t.mkdirAsync(e,t).then((()=>r)).catch((r=>{if(r.code==="ENOENT")return s(e,t);else throw r}))))};const mkdirpNativeSync=(e,t)=>{t.recursive=true;const r=i(e);if(r===e)return t.mkdirSync(e,t);const n=a(t,e);try{t.mkdirSync(e,t);return n}catch(r){if(r.code==="ENOENT")return o(e,t);else throw r}};e.exports={mkdirpNative:mkdirpNative,mkdirpNativeSync:mkdirpNativeSync}},714:(e,t,r)=>{const{promisify:i}=r(669);const n=r(747);const optsArg=e=>{if(!e)e={mode:511,fs:n};else if(typeof e==="object")e={mode:511,fs:n,...e};else if(typeof e==="number")e={mode:e,fs:n};else if(typeof e==="string")e={mode:parseInt(e,8),fs:n};else throw new TypeError("invalid options argument");e.mkdir=e.mkdir||e.fs.mkdir||n.mkdir;e.mkdirAsync=i(e.mkdir);e.stat=e.stat||e.fs.stat||n.stat;e.statAsync=i(e.stat);e.statSync=e.statSync||e.fs.statSync||n.statSync;e.mkdirSync=e.mkdirSync||e.fs.mkdirSync||n.mkdirSync;return e};e.exports=optsArg},43:(e,t,r)=>{const i=process.env.__TESTING_MKDIRP_PLATFORM__||process.platform;const{resolve:n,parse:a}=r(622);const pathArg=e=>{if(/\0/.test(e)){throw Object.assign(new TypeError("path must be a string without null bytes"),{path:e,code:"ERR_INVALID_ARG_VALUE"})}e=n(e);if(i==="win32"){const t=/[*|"<>?:]/;const{root:r}=a(e);if(t.test(e.substr(r.length))){throw Object.assign(new Error("Illegal characters in path."),{path:e,code:"EINVAL"})}}return e};e.exports=pathArg},54:(e,t,r)=>{const i=r(747);const n=process.env.__TESTING_MKDIRP_NODE_VERSION__||process.version;const a=n.replace(/^v/,"").split(".");const s=+a[0]>10||+a[0]===10&&+a[1]>=12;const o=!s?()=>false:e=>e.mkdir===i.mkdir;const c=!s?()=>false:e=>e.mkdirSync===i.mkdirSync;e.exports={useNative:o,useNativeSync:c}},481:(e,t,r)=>{var i=r(687);e.exports=i(once);e.exports.strict=i(onceStrict);once.proto=once((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return once(this)},configurable:true});Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return onceStrict(this)},configurable:true})}));function once(e){var f=function(){if(f.called)return f.value;f.called=true;return f.value=e.apply(this,arguments)};f.called=false;return f}function onceStrict(e){var f=function(){if(f.called)throw new Error(f.onceError);f.called=true;return f.value=e.apply(this,arguments)};var t=e.name||"Function wrapped with `once`";f.onceError=t+" shouldn't be called more than once";f.called=false;return f}},963:e=>{"use strict";function posix(e){return e.charAt(0)==="/"}function win32(e){var t=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var r=t.exec(e);var i=r[1]||"";var n=Boolean(i&&i.charAt(1)!==":");return Boolean(r[2]||n)}e.exports=process.platform==="win32"?win32:posix;e.exports.posix=posix;e.exports.win32=win32},8:(e,t,r)=>{e.exports=rimraf;rimraf.sync=rimrafSync;var i=r(357);var n=r(622);var a=r(747);var s=undefined;try{s=r(750)}catch(e){}var o=parseInt("666",8);var c={nosort:true,silent:true};var l=0;var u=process.platform==="win32";function defaults(e){var t=["unlink","chmod","stat","lstat","rmdir","readdir"];t.forEach((function(t){e[t]=e[t]||a[t];t=t+"Sync";e[t]=e[t]||a[t]}));e.maxBusyTries=e.maxBusyTries||3;e.emfileWait=e.emfileWait||1e3;if(e.glob===false){e.disableGlob=true}if(e.disableGlob!==true&&s===undefined){throw Error("glob dependency not found, set `options.disableGlob = true` if intentional")}e.disableGlob=e.disableGlob||false;e.glob=e.glob||c}function rimraf(e,t,r){if(typeof t==="function"){r=t;t={}}i(e,"rimraf: missing path");i.equal(typeof e,"string","rimraf: path should be a string");i.equal(typeof r,"function","rimraf: callback function required");i(t,"rimraf: invalid options argument provided");i.equal(typeof t,"object","rimraf: options should be object");defaults(t);var n=0;var a=null;var o=0;if(t.disableGlob||!s.hasMagic(e))return afterGlob(null,[e]);t.lstat(e,(function(r,i){if(!r)return afterGlob(null,[e]);s(e,t.glob,afterGlob)}));function next(e){a=a||e;if(--o===0)r(a)}function afterGlob(e,i){if(e)return r(e);o=i.length;if(o===0)return r();i.forEach((function(e){rimraf_(e,t,(function CB(r){if(r){if((r.code==="EBUSY"||r.code==="ENOTEMPTY"||r.code==="EPERM")&&n<t.maxBusyTries){n++;var i=n*100;return setTimeout((function(){rimraf_(e,t,CB)}),i)}if(r.code==="EMFILE"&&l<t.emfileWait){return setTimeout((function(){rimraf_(e,t,CB)}),l++)}if(r.code==="ENOENT")r=null}l=0;next(r)}))}))}}function rimraf_(e,t,r){i(e);i(t);i(typeof r==="function");t.lstat(e,(function(i,n){if(i&&i.code==="ENOENT")return r(null);if(i&&i.code==="EPERM"&&u)fixWinEPERM(e,t,i,r);if(n&&n.isDirectory())return rmdir(e,t,i,r);t.unlink(e,(function(i){if(i){if(i.code==="ENOENT")return r(null);if(i.code==="EPERM")return u?fixWinEPERM(e,t,i,r):rmdir(e,t,i,r);if(i.code==="EISDIR")return rmdir(e,t,i,r)}return r(i)}))}))}function fixWinEPERM(e,t,r,n){i(e);i(t);i(typeof n==="function");if(r)i(r instanceof Error);t.chmod(e,o,(function(i){if(i)n(i.code==="ENOENT"?null:r);else t.stat(e,(function(i,a){if(i)n(i.code==="ENOENT"?null:r);else if(a.isDirectory())rmdir(e,t,r,n);else t.unlink(e,n)}))}))}function fixWinEPERMSync(e,t,r){i(e);i(t);if(r)i(r instanceof Error);try{t.chmodSync(e,o)}catch(e){if(e.code==="ENOENT")return;else throw r}try{var n=t.statSync(e)}catch(e){if(e.code==="ENOENT")return;else throw r}if(n.isDirectory())rmdirSync(e,t,r);else t.unlinkSync(e)}function rmdir(e,t,r,n){i(e);i(t);if(r)i(r instanceof Error);i(typeof n==="function");t.rmdir(e,(function(i){if(i&&(i.code==="ENOTEMPTY"||i.code==="EEXIST"||i.code==="EPERM"))rmkids(e,t,n);else if(i&&i.code==="ENOTDIR")n(r);else n(i)}))}function rmkids(e,t,r){i(e);i(t);i(typeof r==="function");t.readdir(e,(function(i,a){if(i)return r(i);var s=a.length;if(s===0)return t.rmdir(e,r);var o;a.forEach((function(i){rimraf(n.join(e,i),t,(function(i){if(o)return;if(i)return r(o=i);if(--s===0)t.rmdir(e,r)}))}))}))}function rimrafSync(e,t){t=t||{};defaults(t);i(e,"rimraf: missing path");i.equal(typeof e,"string","rimraf: path should be a string");i(t,"rimraf: missing options");i.equal(typeof t,"object","rimraf: options should be object");var r;if(t.disableGlob||!s.hasMagic(e)){r=[e]}else{try{t.lstatSync(e);r=[e]}catch(i){r=s.sync(e,t.glob)}}if(!r.length)return;for(var n=0;n<r.length;n++){var e=r[n];try{var a=t.lstatSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM"&&u)fixWinEPERMSync(e,t,r)}try{if(a&&a.isDirectory())rmdirSync(e,t,null);else t.unlinkSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM")return u?fixWinEPERMSync(e,t,r):rmdirSync(e,t,r);if(r.code!=="EISDIR")throw r;rmdirSync(e,t,r)}}}function rmdirSync(e,t,r){i(e);i(t);if(r)i(r instanceof Error);try{t.rmdirSync(e)}catch(i){if(i.code==="ENOENT")return;if(i.code==="ENOTDIR")throw r;if(i.code==="ENOTEMPTY"||i.code==="EEXIST"||i.code==="EPERM")rmkidsSync(e,t)}}function rmkidsSync(e,t){i(e);i(t);t.readdirSync(e).forEach((function(r){rimrafSync(n.join(e,r),t)}));var r=u?100:1;var a=0;do{var s=true;try{var o=t.rmdirSync(e,t);s=false;return o}finally{if(++a<r&&s)continue}}while(true)}},833:e=>{"use strict";e.exports=function eachAsync(e,t,r,i){var n=0;var a=0;var s=e.length-1;var o=false;var c;var l;var u;if(typeof t==="number"){c=t;u=r;l=i||function noop(){}}else{u=t;l=r||function noop(){};c=e.length}if(!e.length){return l()}var h=u.length;var p=function shouldCallNextIterator(){return!o&&n<c&&a<s};var d=function iteratorCallback(e){if(o){return}n--;if(e||a===s&&!n){o=true;l(e)}else if(p()){m(++a)}};var m=function processIterator(){n++;var t=h===2?[e[a],d]:[e[a],a,d];u.apply(null,t);if(p()){processIterator(++a)}};m()}},687:e=>{e.exports=wrappy;function wrappy(e,t){if(e&&t)return wrappy(e)(t);if(typeof e!=="function")throw new TypeError("need wrapper function");Object.keys(e).forEach((function(t){wrapper[t]=e[t]}));return wrapper;function wrapper(){var t=new Array(arguments.length);for(var r=0;r<t.length;r++){t[r]=arguments[r]}var i=e.apply(this,t);var n=t[t.length-1];if(typeof i==="function"&&i!==n){Object.keys(n).forEach((function(e){i[e]=n[e]}))}return i}}},819:(module,__unused_webpack_exports,__webpack_require__)=>{const{resolve:resolve,relative:relative,dirname:dirname,sep:sep}=__webpack_require__(622);const glob=__webpack_require__(750);const shebangRegEx=__webpack_require__(681);const rimraf=__webpack_require__(8);const crypto=__webpack_require__(417);const{writeFileSync:writeFileSync,unlink:unlink,existsSync:existsSync,symlinkSync:symlinkSync}=__webpack_require__(747);const{hasTypeModule:hasTypeModule}=__webpack_require__(664);const mkdirp=__webpack_require__(485);const{version:nccVersion}=__webpack_require__(306);process.noDeprecation=true;const usage=`Usage: ncc <cmd> <opts>\n\nCommands:\n  build <input-file> [opts]\n  run <input-file> [opts]\n  cache clean|dir|size\n  help\n  version\n\nOptions:\n  -o, --out [file]         Output directory for build (defaults to dist)\n  -m, --minify             Minify output\n  -C, --no-cache           Skip build cache population\n  -s, --source-map         Generate source map\n  --no-source-map-register Skip source-map-register source map support\n  -e, --external [mod]     Skip bundling 'mod'. Can be used many times\n  -q, --quiet              Disable build summaries / non-error outputs\n  -w, --watch              Start a watched build\n  -t, --transpile-only     Use transpileOnly option with the ts-loader\n  --v8-cache               Emit a build using the v8 compile cache\n  --license [file]         Adds a file containing licensing information to the output\n  --stats-out [file]       Emit webpack stats as json to the specified output file\n  --target [es]            ECMAScript target to use for output (default: es2015)\n                           Learn more: https://webpack.js.org/configuration/target\n`;let api=false;if(require.main===require.cache[eval("__filename")]){runCmd(process.argv.slice(2),process.stdout,process.stderr).then((e=>{if(!e)process.exit()})).catch((e=>{if(!e.silent)console.error(e.nccError?e.message:e);process.exit(e.exitCode||1)}))}else{module.exports=runCmd;api=true}function renderSummary(e,t,r,i,n,a){if(n&&!n.endsWith(sep))n+=sep;const s=Math.round(Buffer.byteLength(e,"utf8")/1024);const o=t?Math.round(Buffer.byteLength(t,"utf8")/1024):0;const c=Object.create(null);let l=s;let u=8+(t?4:0);for(const e of Object.keys(r)){const t=r[e].source;const i=Math.round((t.byteLength||Buffer.byteLength(t,"utf8"))/1024);c[e]=i;l+=i;if(e.length>u)u=e.length}const h=Object.keys(r).sort(((e,t)=>c[e]>c[t]?1:-1));const p=l.toString().length;let d=`${s.toString().padStart(p," ")}kB  ${n}index${i}`;let m=t?`${o.toString().padStart(p," ")}kB  ${n}index${i}.map`:"";let g="",v=true;for(const e of h){if(v)v=false;else g+="\n";if(s<c[e]&&d){g+=d+"\n";d=null}if(o&&o<c[e]&&m){g+=m+"\n";m=null}g+=`${c[e].toString().padStart(p," ")}kB  ${n}${e}`}if(d){g+=(v?"":"\n")+d;v=false}if(m)g+=(v?"":"\n")+m;g+=`\n${l}kB  [${a}ms] - ncc ${nccVersion}`;return g}function nccError(e,t=1){const r=new Error(e);r.nccError=true;r.exitCode=t;throw r}async function runCmd(argv,stdout,stderr){let args;try{args=__webpack_require__(832)({"--asset-builds":Boolean,"-a":"--asset-builds","--debug":Boolean,"-d":"--debug","--external":[String],"-e":"--external","--out":String,"-o":"--out","--minify":Boolean,"-m":"--minify","--source-map":Boolean,"-s":"--source-map","--no-cache":Boolean,"-C":"--no-cache","--no-asset-builds":Boolean,"--no-source-map-register":Boolean,"--quiet":Boolean,"-q":"--quiet","--watch":Boolean,"-w":"--watch","--v8-cache":Boolean,"--transpile-only":Boolean,"-t":"--transpile-only","--license":String,"--stats-out":String,"--target":String},{permissive:false,argv:argv})}catch(e){if(e.message.indexOf("Unknown or unexpected option")===-1)throw e;nccError(e.message+`\n${usage}`,2)}if(args._.length===0)nccError(`Error: No command specified\n${usage}`,2);let run=false;let outDir=args["--out"];const quiet=args["--quiet"];const statsOutFile=args["--stats-out"];switch(args._[0]){case"cache":if(args._.length>2)errTooManyArguments("cache");const flags=Object.keys(args).filter((e=>e.startsWith("--")));if(flags.length)errFlagNotCompatible(flags[0],"cache");const cacheDir=__webpack_require__(946);switch(args._[1]){case"clean":rimraf.sync(cacheDir);break;case"dir":stdout.write(cacheDir+"\n");break;case"size":__webpack_require__(74)(cacheDir,((e,t)=>{if(e){if(e.code==="ENOENT"){stdout.write("0MB\n");return}throw e}stdout.write(`${(t/1024/1024).toFixed(2)}MB\n`)}));break;default:errInvalidCommand("cache "+args._[1])}break;case"run":if(args._.length>2)errTooManyArguments("run");if(args["--out"])errFlagNotCompatible("--out","run");if(args["--watch"])errFlagNotCompatible("--watch","run");outDir=resolve(__webpack_require__(87).tmpdir(),crypto.createHash("md5").update(resolve(args._[1]||".")).digest("hex"));if(existsSync(outDir))rimraf.sync(outDir);mkdirp.sync(outDir);run=true;case"build":if(args._.length>2)errTooManyArguments("build");let startTime=Date.now();let ps;const buildFile=eval("require.resolve")(resolve(args._[1]||"."));const esm=buildFile.endsWith(".mjs")||!buildFile.endsWith(".cjs")&&hasTypeModule(buildFile);const ext=buildFile.endsWith(".cjs")?".cjs":esm&&(buildFile.endsWith(".mjs")||!hasTypeModule(buildFile))?".mjs":".js";const ncc=__webpack_require__(612)(buildFile,{debugLog:args["--debug"],minify:args["--minify"],externals:args["--external"],sourceMap:args["--source-map"]||run,sourceMapRegister:args["--no-source-map-register"]?false:undefined,assetBuilds:args["--asset-builds"]?true:false,cache:args["--no-cache"]?false:undefined,watch:args["--watch"],v8cache:args["--v8-cache"],transpileOnly:args["--transpile-only"],license:args["--license"],quiet:quiet,target:args["--target"]});async function handler({err:err,code:code,map:map,assets:assets,symlinks:symlinks,stats:stats}){if(err){stderr.write(err+"\n");stdout.write("Watching for changes...\n");return}outDir=outDir||resolve(eval("'dist'"));mkdirp.sync(outDir);await Promise.all((await new Promise(((e,t)=>glob(outDir+"/**/*.(js|cjs)",((r,i)=>r?t(r):e(i)))))).map((e=>new Promise(((t,r)=>unlink(e,(e=>e?r(e):t())))))));writeFileSync(`${outDir}/index${ext}`,code,{mode:code.match(shebangRegEx)?511:438});if(map)writeFileSync(`${outDir}/index${ext}.map`,map);for(const e of Object.keys(assets)){const t=outDir+"/"+e;mkdirp.sync(dirname(t));writeFileSync(t,assets[e].source,{mode:assets[e].permissions})}for(const e of Object.keys(symlinks)){const t=outDir+"/"+e;symlinkSync(symlinks[e],t)}if(!quiet){stdout.write(renderSummary(code,map,assets,ext,run?"":relative(process.cwd(),outDir),Date.now()-startTime)+"\n");if(args["--watch"])stdout.write("Watching for changes...\n")}if(statsOutFile)writeFileSync(statsOutFile,JSON.stringify(stats.toJson()));if(run){const e=resolve("/node_modules");let t=dirname(buildFile)+"/node_modules";do{if(t===e){t=undefined;break}if(existsSync(t))break}while(t=resolve(t,"../../node_modules"));if(t)symlinkSync(t,outDir+"/node_modules","junction");ps=__webpack_require__(129).fork(`${outDir}/index${ext}`,{stdio:api?"pipe":"inherit"});if(api){ps.stdout.pipe(stdout);ps.stderr.pipe(stderr)}return new Promise(((e,t)=>{function exit(r){__webpack_require__(8).sync(outDir);if(r===0)e();else t({silent:true,exitCode:r});process.off("SIGTERM",exit);process.off("SIGINT",exit)}ps.on("exit",exit);process.on("SIGTERM",exit);process.on("SIGINT",exit)}))}}if(args["--watch"]){ncc.handler(handler);ncc.rebuild((()=>{if(ps)ps.kill();startTime=Date.now();stdout.write("File change, rebuilding...\n")}));return true}else{return ncc.then(handler)}break;case"help":nccError(usage,2);case"version":stdout.write(__webpack_require__(306).version+"\n");break;default:errInvalidCommand(args._[0],2)}function errTooManyArguments(e){nccError(`Error: Too many ${e} arguments provided\n${usage}`,2)}function errFlagNotCompatible(e,t){nccError(`Error: ${e} flag is not compatible with ncc ${t}\n${usage}`,2)}function errInvalidCommand(e){nccError(`Error: Invalid command "${e}"\n${usage}`,2)}process.on("unhandledRejection",(e=>{throw e}))}},664:(__unused_webpack_module,exports,__webpack_require__)=>{const{resolve:resolve}=__webpack_require__(622);const{readFileSync:readFileSync}=__webpack_require__(747);exports.hasTypeModule=function hasTypeModule(path){while(path!==(path=resolve(path,".."))){try{return JSON.parse(readFileSync(eval("resolve")(path,"package.json")).toString()).type==="module"}catch(e){if(e.code==="ENOENT")continue;throw e}}}},946:(e,t,r)=>{e.exports=r(87).tmpdir()+"/ncc-cache"},681:e=>{e.exports=/^#![^\n\r]*[\r\n]/},612:e=>{"use strict";e.exports=require("./index.js")},357:e=>{"use strict";e.exports=require("assert")},129:e=>{"use strict";e.exports=require("child_process")},417:e=>{"use strict";e.exports=require("crypto")},614:e=>{"use strict";e.exports=require("events")},747:e=>{"use strict";e.exports=require("fs")},87:e=>{"use strict";e.exports=require("os")},622:e=>{"use strict";e.exports=require("path")},669:e=>{"use strict";e.exports=require("util")}};var __webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(t!==undefined){return t.exports}var r=__webpack_module_cache__[e]={exports:{}};var i=true;try{__webpack_modules__[e](r,r.exports,__webpack_require__);i=false}finally{if(i)delete __webpack_module_cache__[e]}return r.exports}if(typeof __webpack_require__!=="undefined")__webpack_require__.ab=__dirname+"/";var __webpack_exports__=__webpack_require__(819);module.exports=__webpack_exports__})();