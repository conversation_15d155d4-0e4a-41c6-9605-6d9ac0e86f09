{"version": 3, "sources": ["../browser/src/find-options/operator/ArrayOverlap.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAA;AAE9C;;;GAGG;AACH,MAAM,UAAU,YAAY,CACxB,KAAqC;IAErC,OAAO,IAAI,YAAY,CAAC,cAAc,EAAE,KAAY,CAAC,CAAA;AACzD,CAAC", "file": "ArrayOverlap.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: ArrayOverlap([...]) }\n */\nexport function ArrayOverlap<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"arrayOverlap\", value as any)\n}\n"], "sourceRoot": "../.."}