{"name": "redis-commands", "version": "1.7.0", "description": "Redis commands", "main": "index.js", "scripts": {"pretest": "npm run lint", "test": "mocha", "posttest": "npm run coverage && npm run coverage:check", "coverage": "node ./node_modules/istanbul/lib/cli.js cover --preserve-comments ./node_modules/mocha/bin/_mocha -- -R spec", "coverage:check": "node ./node_modules/istanbul/lib/cli.js check-coverage --branch 100 --statement 100", "build": "node tools/build", "lint": "standard --fix --verbose | snazzy"}, "repository": {"type": "git", "url": "https://github.com/NodeRedis/redis-commands.git"}, "keywords": ["redis", "commands", "prefix"], "author": "luin <<EMAIL>> (http://zihua.li)", "license": "MIT", "bugs": {"url": "https://github.com/NodeRedis/redis-commands/issues"}, "homepage": "https://github.com/NodeRedis/redis-commands", "devDependencies": {"chai": "^4.0.1", "codeclimate-test-reporter": "^0.5.1", "ioredis": "^4.9.0", "istanbul": "^0.4.3", "safe-stable-stringify": "^1.0.0", "mocha": "^6.0.0", "snazzy": "^8.0.0", "standard": "^12.0.0"}, "dependencies": {}}