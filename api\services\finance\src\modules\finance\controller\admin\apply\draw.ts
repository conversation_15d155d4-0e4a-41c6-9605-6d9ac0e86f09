import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceApplyDrawEntity } from '../../../entity/apply/draw';
import { FinanceApplyDrawService } from '../../../service/apply/draw';
import { Body, Inject, Post } from '@midwayjs/core';
import { UserInfoEntity } from '../../../../user/entity/info';

/**
 * 提现申请
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'list', 'page'],
  entity: FinanceApplyDrawEntity,
  service: FinanceApplyDrawService,
  pageQueryOp: {
    keyWordLikeFields: ['b.nickName'],
    fieldEq: ['a.status'],
    select: ['a.*', 'b.nickName as userName', 'b.avatarUrl'],
    join: [
      {
        entity: UserInfoEntity,
        alias: 'b',
        condition: 'a.userId = b.id',
      },
    ],
  },
})
export class AdminFinanceApplyDrawController extends BaseController {
  @Inject()
  financeApplyDrawService: FinanceApplyDrawService;

  @Post('/auth', { summary: '审核' })
  async auth(
    @Body('id') id: number,
    @Body('status') status: number,
    @Body('remark') remark: string,
    @Body('fee') fee: number
  ) {
    await this.financeApplyDrawService.auth(id, status, remark, fee);
    return this.ok();
  }
}
