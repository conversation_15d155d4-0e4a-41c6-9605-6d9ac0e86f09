{"version": 3, "sources": ["../../src/query-builder/DeleteQueryBuilder.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAO7C,wDAAoD;AACpD,sGAAkG;AAClG,6DAAyD;AAEzD;;GAEG;AACH,MAAa,kBACT,SAAQ,2BAAoB;IAK5B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACI,wBAAwD,EACxD,WAAyB;QAEzB,KAAK,CAAC,wBAA+B,EAAE,WAAW,CAAC,CAAA;QAV9C,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;QAWrD,IAAI,CAAC,aAAa,CAAC,yBAAyB,GAAG,KAAK,CAAA;IACxD,CAAC;IAED,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAC9B,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACjC,GAAG,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACpC,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAE3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,4DAA4D;YAC5D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,cAAc,EACd,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;YACL,CAAC;YAED,gBAAgB;YAChB,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAClE,MAAM,YAAY,GAAG,2BAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAEnD,2DAA2D;YAC3D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,WAAW,CAAC,SAAS,CACnC,aAAa,EACb,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CACzC,CAAA;YACL,CAAC;YAED,qCAAqC;YACrC,IAAI,sBAAsB;gBAAE,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YAEjE,OAAO,YAAY,CAAA;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;QACL,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,IAAI,CACA,YAA6B,EAC7B,SAAkB;QAElB,YAAY,GAAG,iCAAe,CAAC,cAAc,CAAC,YAAY,CAAC;YACvD,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;YAC3B,CAAC,CAAC,YAAY,CAAA;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,EAAE,SAAS,CAAC,CAAA;QAC/D,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC1C,OAAO,IAAoC,CAAA;IAC/C,CAAC;IAED;;;;;OAKG;IACH,KAAK,CACD,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,CAAA,CAAC,oFAAoF;QACnH,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QAC/C,IAAI,SAAS;YACT,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;gBACxB,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE;aAC3C,CAAA;QACL,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,QAAQ,CACJ,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,KAAK;YACX,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;OAGG;IACH,OAAO,CACH,KAKqB,EACrB,UAA0B;QAE1B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC;YAC3B,IAAI,EAAE,IAAI;YACV,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;SAC3C,CAAC,CAAA;QACF,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAC9C,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,GAAgB;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,GAAgB;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IAC1D,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,GAAgB;QACzB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAA;IACzD,CAAC;IAkBD;;OAEG;IACH,MAAM,CAAC,MAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAmBD;;OAEG;IACH,SAAS,CAAC,SAA4B;QAClC,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,yEAAmC,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,sBAAsB;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAC5D,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;QACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA;QAEpE,IAAI,mBAAmB,KAAK,EAAE,EAAE,CAAC;YAC7B,OAAO,eAAe,SAAS,GAAG,eAAe,EAAE,CAAA;QACvD,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,OAAO,eAAe,SAAS,WAAW,mBAAmB,GAAG,eAAe,EAAE,CAAA;QACrF,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;YACpD,OAAO,eAAe,SAAS,GAAG,eAAe,gBAAgB,mBAAmB,EAAE,CAAA;QAC1F,CAAC;QACD,OAAO,eAAe,SAAS,GAAG,eAAe,cAAc,mBAAmB,EAAE,CAAA;IACxF,CAAC;CACJ;AA1RD,gDA0RC", "file": "DeleteQueryBuilder.js", "sourcesContent": ["import { QueryBuilder } from \"./QueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { WhereExpressionBuilder } from \"./WhereExpressionBuilder\"\nimport { Brackets } from \"./Brackets\"\nimport { DeleteResult } from \"./result/DeleteResult\"\nimport { ReturningStatementNotSupportedError } from \"../error/ReturningStatementNotSupportedError\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport class DeleteQueryBuilder<Entity extends ObjectLiteral>\n    extends QueryBuilder<Entity>\n    implements WhereExpressionBuilder\n{\n    readonly \"@instanceof\" = Symbol.for(\"DeleteQueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        connectionOrQueryBuilder: DataSource | QueryBuilder<any>,\n        queryRunner?: QueryRunner,\n    ) {\n        super(connectionOrQueryBuilder as any, queryRunner)\n        this.expressionMap.aliasNamePrefixingEnabled = false\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        let sql = this.createComment()\n        sql += this.createCteExpression()\n        sql += this.createDeleteExpression()\n        return this.replacePropertyNamesForTheWholeQuery(sql.trim())\n    }\n\n    /**\n     * Executes sql generated by query builder and returns raw database results.\n     */\n    async execute(): Promise<DeleteResult> {\n        const [sql, parameters] = this.getQueryAndParameters()\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            // call before deletion methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                await queryRunner.broadcaster.broadcast(\n                    \"BeforeRemove\",\n                    this.expressionMap.mainAlias!.metadata,\n                )\n            }\n\n            // execute query\n            const queryResult = await queryRunner.query(sql, parameters, true)\n            const deleteResult = DeleteResult.from(queryResult)\n\n            // call after deletion methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                await queryRunner.broadcaster.broadcast(\n                    \"AfterRemove\",\n                    this.expressionMap.mainAlias!.metadata,\n                )\n            }\n\n            // close transaction if we started it\n            if (transactionStartedByUs) await queryRunner.commitTransaction()\n\n            return deleteResult\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Specifies FROM which entity's table select/update/delete will be executed.\n     * Also sets a main string alias of the selection data.\n     */\n    from<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        aliasName?: string,\n    ): DeleteQueryBuilder<T> {\n        entityTarget = InstanceChecker.isEntitySchema(entityTarget)\n            ? entityTarget.options.name\n            : entityTarget\n        const mainAlias = this.createFromAlias(entityTarget, aliasName)\n        this.expressionMap.setMainAlias(mainAlias)\n        return this as any as DeleteQueryBuilder<T>\n    }\n\n    /**\n     * Sets WHERE condition in the query builder.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     * Additionally you can add parameters used in where expression.\n     */\n    where(\n        where:\n            | Brackets\n            | string\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres = [] // don't move this block below since computeWhereParameter can add where expressions\n        const condition = this.getWhereCondition(where)\n        if (condition)\n            this.expressionMap.wheres = [\n                { type: \"simple\", condition: condition },\n            ]\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new AND WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    andWhere(\n        where:\n            | Brackets\n            | string\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"and\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Adds new OR WHERE condition in the query builder.\n     * Additionally you can add parameters used in where expression.\n     */\n    orWhere(\n        where:\n            | Brackets\n            | string\n            | ((qb: this) => string)\n            | ObjectLiteral\n            | ObjectLiteral[],\n        parameters?: ObjectLiteral,\n    ): this {\n        this.expressionMap.wheres.push({\n            type: \"or\",\n            condition: this.getWhereCondition(where),\n        })\n        if (parameters) this.setParameters(parameters)\n        return this\n    }\n\n    /**\n     * Sets WHERE condition in the query builder with a condition for the given ids.\n     * If you had previously WHERE expression defined,\n     * calling this function will override previously set WHERE conditions.\n     */\n    whereInIds(ids: any | any[]): this {\n        return this.where(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new AND WHERE with conditions for the given ids.\n     */\n    andWhereInIds(ids: any | any[]): this {\n        return this.andWhere(this.getWhereInIdsCondition(ids))\n    }\n\n    /**\n     * Adds new OR WHERE with conditions for the given ids.\n     */\n    orWhereInIds(ids: any | any[]): this {\n        return this.orWhere(this.getWhereInIdsCondition(ids))\n    }\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    output(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    output(output: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this {\n        return this.returning(output)\n    }\n\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    returning(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    returning(returning: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this {\n        // not all databases support returning/output cause\n        if (!this.connection.driver.isReturningSqlSupported(\"delete\")) {\n            throw new ReturningStatementNotSupportedError()\n        }\n\n        this.expressionMap.returning = returning\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates DELETE express used to perform query.\n     */\n    protected createDeleteExpression() {\n        const tableName = this.getTableName(this.getMainTableName())\n        const whereExpression = this.createWhereExpression()\n        const returningExpression = this.createReturningExpression(\"delete\")\n\n        if (returningExpression === \"\") {\n            return `DELETE FROM ${tableName}${whereExpression}`\n        }\n        if (this.connection.driver.options.type === \"mssql\") {\n            return `DELETE FROM ${tableName} OUTPUT ${returningExpression}${whereExpression}`\n        }\n        if (this.connection.driver.options.type === \"spanner\") {\n            return `DELETE FROM ${tableName}${whereExpression} THEN RETURN ${returningExpression}`\n        }\n        return `DELETE FROM ${tableName}${whereExpression} RETURNING ${returningExpression}`\n    }\n}\n"], "sourceRoot": ".."}