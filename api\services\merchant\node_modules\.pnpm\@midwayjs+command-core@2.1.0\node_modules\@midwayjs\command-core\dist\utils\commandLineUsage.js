"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.commandLineUsage = void 0;
const commandLineUsage = (options, prefix) => {
    if (Array.isArray(options)) {
        return options
            .map(options => {
            return (0, exports.commandLineUsage)(options, prefix);
        })
            .join('\n');
    }
    const currentPrefix = (prefix === null || prefix === void 0 ? void 0 : prefix.prefix) || '';
    const result = ['\n'];
    if (options.header) {
        result.push(currentPrefix + ((prefix === null || prefix === void 0 ? void 0 : prefix.command) || '') + options.header);
        if (options.content) {
            result.push(currentPrefix + `  ${options.content}`);
        }
        result[result.length - 1] = result[result.length - 1] + '\n';
    }
    const optionsList = [];
    let length = 0;
    if (options.optionList) {
        options.optionList.map(info => {
            const option = `  ${info.alias ? `--${info.alias}, ` : ''}--${info.name}`;
            if (option.length > length) {
                length = option.length + 4;
            }
            optionsList.push({
                option,
                info: info.description || '',
            });
        });
    }
    optionsList.forEach(options => {
        result.push(currentPrefix + options.option.padEnd(length, ' ') + options.info + '\n');
    });
    if (Array.isArray(options.childCommands) && options.childCommands.length) {
        result.push((0, exports.commandLineUsage)(options.childCommands, {
            prefix: currentPrefix + '',
            command: ((prefix === null || prefix === void 0 ? void 0 : prefix.command) || '') +
                (options.header ? `${options.header} ` : ''),
        }));
    }
    return result.join('\n');
};
exports.commandLineUsage = commandLineUsage;
//# sourceMappingURL=commandLineUsage.js.map