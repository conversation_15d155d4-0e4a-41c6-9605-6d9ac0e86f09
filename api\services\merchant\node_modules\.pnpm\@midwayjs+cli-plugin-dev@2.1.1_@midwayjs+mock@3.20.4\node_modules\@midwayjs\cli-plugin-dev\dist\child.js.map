{"version": 3, "file": "child.js", "sourceRoot": "", "sources": ["../src/child.ts"], "names": [], "mappings": ";;AAAA,yDAAgE;AAChE,mCAAuD;AACvD,MAAM,OAAO,GAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AACjD,IAAI,GAAG,CAAC;AACR,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AAE1B,IAAI,UAAU,GAAG,KAAK,CAAC;AACvB,IAAI,QAAQ,GAAQ,GAAG,EAAE,GAAE,CAAC,CAAC;AAC7B,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE;IAC1B,IAAI,UAAU,EAAE;QACd,OAAO;KACR;IACD,UAAU,GAAG,IAAI,CAAC;IAClB,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC;IACpB,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AACD,OAAe,CAAC,IAAI,GAAG,QAAQ,CAAC;AACjC,OAAO,CAAC,EAAE,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;AACnC,4BAA4B;AAC5B,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;AAC/B,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE;IACnC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC;AACzC,CAAC,CAAC,CAAC;AACH,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,CAAC,EAAE;IAClC,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AACH,uCAAuC;AACvC,SAAS;AAET,CAAC,KAAK,IAAI,EAAE;IACV,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE;QACnC,MAAM,IAAA,iBAAS,EAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;KAClD;IACD,MAAM,OAAO,GAAG,IAAA,qCAAsB,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;IACxE,IAAI,CAAC,OAAO,EAAE;QACZ,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAC;KACtE;IACD,MAAM,EACJ,SAAS,EACT,KAAK,EACL,iBAAiB,EACjB,eAAe,GAChB,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;IACrB,QAAQ,GAAG,KAAK,CAAC;IACjB,IAAI,YAAY,GAAG,KAAK,CAAC;IACzB,IAAI;QACF,IAAI,OAAO,CAAC,SAAS,EAAE;YACrB,GAAG,GAAG,MAAM,eAAe,CAAC,OAAO,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;SACzD;aAAM,IAAI,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM,EAAE;YAC1D,GAAG,GAAG,MAAM,iBAAiB,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;SACvD;aAAM;YACL,GAAG,GAAG,MAAM,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;SAClE;QACD,YAAY,GAAG,IAAI,CAAC;KACrB;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;SACpD,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;KAChB;IAED,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,EAAE,GAAQ,EAAE,EAAE;QACvC,IAAI,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACrB,OAAO;SACR;QACD,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE;YAC5B,yCAAyC;YACzC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE;gBAC7C,MAAM,IAAI,GAAG,MAAM,IAAA,yBAAiB,EAAC,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvE,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;aAC7D;SACF;aAAM,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE;YAC9B,MAAM,QAAQ,EAAE,CAAC;YACjB,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;YACtD,OAAO,CAAC,IAAI,EAAE,CAAC;SAChB;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,IAAI,CAAC;QACX,IAAI,EAAE,SAAS;QACf,YAAY;QACZ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB;KACnC,CAAC,CAAC;AACL,CAAC,CAAC,EAAE,CAAC"}