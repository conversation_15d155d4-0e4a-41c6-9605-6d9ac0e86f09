import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 钱包记录
 */
@Entity('finance_wallet_record')
export class FinanceWalletRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '标题' })
  title: string;

  @Column({ comment: '金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '类型 0-收入 1-支出', default: 0 })
  type: number;

  @Column({ comment: '对象ID', nullable: true })
  objectId: number;

  @Column({ comment: '对象类型 0-订单 1-提现 2-充值', nullable: true })
  objectType: number;

  @Column({ comment: '对象信息', type: 'json', nullable: true })
  objectInfo: any;

  @Column({ comment: '备注', nullable: true })
  remark: string;
}
