{"version": 3, "sources": ["../../src/find-options/EqualOperator.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C,MAAa,aAAiB,SAAQ,2BAAe;IAGjD,YAAY,KAA0B;QAClC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAHhB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAIpD,CAAC;CACJ;AAND,sCAMC", "file": "EqualOperator.js", "sourcesContent": ["import { FindOperator } from \"./FindOperator\"\n\nexport class EqualOperator<T> extends FindOperator<T> {\n    readonly \"@instanceof\" = Symbol.for(\"EqualOperator\")\n\n    constructor(value: T | FindOperator<T>) {\n        super(\"equal\", value)\n    }\n}\n"], "sourceRoot": ".."}