{"version": 3, "file": "wrapper.js", "sourceRoot": "", "sources": ["../src/wrapper.ts"], "names": [], "mappings": ";;;AAAA,+BAAqC;AACrC,2BAA6D;AAC7D,6BAA6B;AAC7B,mCAA2D;AAC3D,MAAM;AACN,SAAgB,YAAY,CAAC,OAsB5B;;IACC,MAAM,EACJ,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,KAAK,EACL,WAAW,GAAG,gBAAgB,EAC9B,cAAc,EACd,uBAAuB,EACvB,aAAa,GAAG,EAAE,EAClB,cAAc,GAAG,EAAE,EACnB,eAAe,EACf,UAAU,EACV,UAAU,GAAG,IAAI,EACjB,YAAY,EACZ,QAAQ,EACR,WAAW,EACX,qBAAqB,GAAG,EAAE,EAC1B,2BAA2B,GAAG,EAAE,EAChC,mBAAmB,GAAG,EAAE,EACxB,mBAAmB,GAAG,KAAK,EAC3B,aAAa,GAAG,KAAK,GACtB,GAAG,OAAO,CAAC;IAEZ,MAAM,KAAK,GAAG,EAAE,CAAC;IAEjB,mCAAmC;IACnC,IAAI,WAAgB,CAAC;IACrB,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;IAC1C,KAAK,MAAM,IAAI,IAAI,SAAS,EAAE;QAC5B,MAAM,WAAW,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;QACpC,SAAS;QACT,WAAW,GAAG,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;QAC5D,qBAAqB;QACrB,IAAI,WAAW,CAAC,SAAS,EAAE;YACzB,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,EAAE;gBAC/C,WAAW,GAAG,mBAAmB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YACnE,CAAC,CAAC,CAAC;SACJ;QACD,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxD,IAAI,eAAe,GAAG,WAAW,CAAC,eAAe,IAAI,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACzE,MAAM,IAAI,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACjC,IAAI,aAAa,EAAE;YACjB,eAAe,GAAG,IAAI,CAAC;SACxB;QACD,IAAI,CAAC,KAAK,IAAI,IAAA,eAAU,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,eAAe,GAAG,KAAK,CAAC,CAAC,EAAE;YAChE,gBAAgB;YAChB,SAAS;SACV;QACD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE;YAC3B,KAAK,CAAC,eAAe,CAAC,GAAG;gBACvB,QAAQ,EAAE,EAAE;gBACZ,YAAY,EAAE,EAAE;aACjB,CAAC;SACH;QAED,IAAI,aAAa,EAAE;YACjB,KAAK,CAAC,eAAe,CAAC,CAAC,0BAA0B,GAAG,WAAW,CAAC,OAAO,CAAC;SACzE;QAED,IAAI,WAAW,CAAC,MAAM,IAAI,WAAW,CAAC,MAAM,CAAC,MAAM,EAAE;YACnD,KAAK,CAAC,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC9D;QACD,QAAQ;QACR,IAAI,WAAW,CAAC,cAAc,EAAE;YAC9B,KAAK,CAAC,eAAe,CAAC,CAAC,sBAAsB,GAAG,WAAW,CAAC,OAAO,CAAC;YACpE,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACnC,IAAI;gBACJ,QAAQ,EAAE,yBAAyB,CAAC,WAAW,CAAC,SAAS,CAAC;aAC3D,CAAC,CAAC;SACJ;aAAM;YACL,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACnC,IAAI;gBACJ,OAAO,EAAE,WAAW,CAAC,OAAO;aAC7B,CAAC,CAAC;SACJ;KACF;IAED,IAAI,OAAO,GAAG,YAAY,CAAC;IAC3B,IAAI,CAAC,OAAO,EAAE;QACZ,IAAI,YAAY,GAAG,mBAAmB,CAAC;QACvC,MAAM,eAAe,GAAG,CAAC,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,UAAU,CAAA,CAAC;QAC9C,iBAAiB;QACjB,IAAI,eAAe,EAAE;YACnB,YAAY,GAAG,oBAAoB,CAAC;SACrC;aAAM,IAAI,mBAAmB,EAAE;YAC9B,YAAY,GAAG,4BAA4B,CAAC;SAC7C;aAAM;YACL,MAAM,WAAW,GAAG,IAAA,6BAAqB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC5D,IAAI,WAAW,KAAK,CAAC,EAAE;gBACrB,YAAY,GAAG,mBAAmB,CAAC;aACpC;iBAAM,IAAI,WAAW,KAAK,CAAC,EAAE;gBAC5B,YAAY,GAAG,mBAAmB,CAAC;aACpC;SACF;QACD,OAAO,GAAG,IAAA,cAAO,EAAC,SAAS,EAAE,YAAY,CAAC,CAAC;KAC5C;IAED,MAAM,GAAG,GAAG,IAAA,iBAAY,EAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC;IAE7C,IAAI,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,YAAY,0CAAE,MAAM,EAAE;QACrC,MAAM,MAAM,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,qBAAqB,CAAC,CAAC;QACpD,MAAM,MAAM,GAAG,IAAA,iBAAY,EACzB,IAAA,WAAI,EAAC,SAAS,EAAE,sBAAsB,CAAC,EACvC,OAAO,CACR,CAAC;QAEF,MAAM,OAAO,GAAG,IAAA,YAAM,EAAC,MAAM,EAAE;YAC7B,OAAO,EAAE,MAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,KAAK,0CAAE,OAAO,mCAAI,UAAU;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,IAAA,eAAU,EAAC,MAAM,CAAC,EAAE;YACvB,IAAA,kBAAa,EAAC,MAAM,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;SACvD;KACF;IAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,MAAM,QAAQ,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC;QAC7C,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QAC7B,MAAM,MAAM,GAAG,IAAA,iBAAS,EAAC,OAAO,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QACnE,IAAI,SAAS,GAAG;YACd,OAAO;YACP,aAAa,EAAE,OAAO;YACtB,WAAW,EAAE,WAAW,IAAI,gBAAgB;YAC5C,aAAa;YACb,+CAA+C;YAC/C,UAAU,EAAE,UAAU,IAAI,EAAE;YAC5B,eAAe,EAAE,eAAe,IAAI,aAAa;YACjD,uBAAuB,EAAE,uBAAuB,IAAI,KAAK;YACzD,WAAW,EAAE,cAAc,IAAI,aAAa;YAC5C,QAAQ,EAAE,QAAQ,CAAC,QAAQ;YAC3B,WAAW;YACX,cAAc;YACd,UAAU;YACV,QAAQ,EAAE,QAAQ,IAAI,KAAK;YAC3B,WAAW;YACX,2BAA2B;YAC3B,mBAAmB;YACnB,mBAAmB;YACnB,0BAA0B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,0BAA0B;YAClE,sBAAsB,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,sBAAsB;YAC1D,GAAG,MAAM;SACV,CAAC;QACF,OAAO;QACP,IAAI,OAAO,qBAAqB,KAAK,QAAQ,EAAE;YAC7C,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;SACjD;aAAM,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;YACtD,SAAS,GAAG,qBAAqB,CAAC;gBAChC,IAAI;gBACJ,QAAQ;gBACR,SAAS;aACV,CAAC,CAAC;SACJ;QACD,MAAM,OAAO,GAAG,IAAA,YAAM,EAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QACvC,IAAI,IAAA,eAAU,EAAC,QAAQ,CAAC,EAAE;YACxB,MAAM,UAAU,GAAG,IAAA,iBAAY,EAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;YACrD,IAAI,UAAU,KAAK,OAAO,EAAE;gBAC1B,SAAS;aACV;SACF;QACD,IAAA,kBAAa,EAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KAClC;AACH,CAAC;AAzLD,oCAyLC;AAED,MAAM,mBAAmB,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,EAAE;IACvD,IAAI,WAAW,CAAC,YAAY,EAAE;QAC5B,IAAI,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,YAAY,CAAA,EAAE;YAC9B,WAAW,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAC;SACpC;QACD,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC;YAC5B,YAAY,EAAE,WAAW,CAAC,cAAc;YACxC,eAAe,EAAE,WAAW,CAAC,OAAO;YACpC,gBAAgB,EAAE,WAAW,CAAC,cAAc;YAC5C,QAAQ,EAAE,WAAW,CAAC,QAAQ;SAC/B,CAAC,CAAC;KACJ;IACD,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF,SAAgB,yBAAyB,CAAC,QAAQ;IAChD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QACjC,OAAO,EAAE,CAAC;KACX;IACD,OAAO,QAAQ;SACZ,GAAG,CAAC,OAAO,CAAC,EAAE;QACb,MAAM,EAAE,IAAI,GAAG,EAAE,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;QACzC,IAAI,SAAS,KAAK,MAAM,EAAE;YACxB,OAAO;gBACL,GAAG,OAAO;gBACV,KAAK,EAAE,CAAC,CAAC;aACV,CAAC;SACH;QACD,OAAO;YACL,GAAG,OAAO;YACV,MAAM,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAC3D,MAAM,CAAC,EAAE;gBACP,OAAO,MAAM,CAAC,WAAW,EAAE,CAAC;YAC9B,CAAC,CACF;YACD,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;YACjC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;YACpC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,IAAI,QAAQ;YACtD,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC;YACjC,gBAAgB,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACpD,CAAC;IACJ,CAAC,CAAC;SACD,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC3B,IAAI,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE;YACrC,IAAI,QAAQ,CAAC,KAAK,GAAG,CAAC,EAAE;gBACtB,OAAO,CAAC,CAAC,CAAC;aACX;YACD,IAAI,QAAQ,CAAC,UAAU,KAAK,QAAQ,CAAC,UAAU,EAAE;gBAC/C,OAAO,QAAQ,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;aACxD;YACD,IAAI,QAAQ,CAAC,gBAAgB,KAAK,QAAQ,CAAC,gBAAgB,EAAE;gBAC3D,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;aAChE;YACD,OAAO,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;SAC9D;QACD,OAAO,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;IACzC,CAAC,CAAC,CAAC;AACP,CAAC;AA3CD,8DA2CC"}