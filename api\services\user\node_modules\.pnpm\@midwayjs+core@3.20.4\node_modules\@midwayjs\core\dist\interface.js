"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayProcessTypeEnum = exports.ObjectLifeCycleEvent = exports.ServerlessTriggerType = exports.MidwayFrameworkType = exports.FrameworkType = exports.MSListenerType = exports.MSProviderType = exports.InjectModeEnum = exports.ScopeEnum = void 0;
var ScopeEnum;
(function (ScopeEnum) {
    ScopeEnum["Singleton"] = "Singleton";
    ScopeEnum["Request"] = "Request";
    ScopeEnum["Prototype"] = "Prototype";
})(ScopeEnum = exports.ScopeEnum || (exports.ScopeEnum = {}));
var InjectModeEnum;
(function (InjectModeEnum) {
    InjectModeEnum["Identifier"] = "Identifier";
    InjectModeEnum["Class"] = "Class";
    InjectModeEnum["PropertyName"] = "PropertyName";
})(InjectModeEnum = exports.InjectModeEnum || (exports.InjectModeEnum = {}));
var MSProviderType;
(function (MSProviderType) {
    MSProviderType["DUBBO"] = "dubbo";
    MSProviderType["GRPC"] = "gRPC";
})(MSProviderType = exports.MSProviderType || (exports.MSProviderType = {}));
var MSListenerType;
(function (MSListenerType) {
    MSListenerType["RABBITMQ"] = "rabbitmq";
    MSListenerType["MQTT"] = "mqtt";
    MSListenerType["KAFKA"] = "kafka";
    MSListenerType["REDIS"] = "redis";
})(MSListenerType = exports.MSListenerType || (exports.MSListenerType = {}));
class FrameworkType {
}
exports.FrameworkType = FrameworkType;
class MidwayFrameworkType extends FrameworkType {
    constructor(name) {
        super();
        this.name = name;
    }
}
exports.MidwayFrameworkType = MidwayFrameworkType;
MidwayFrameworkType.WEB = new MidwayFrameworkType('@midwayjs/web');
MidwayFrameworkType.WEB_KOA = new MidwayFrameworkType('@midwayjs/web-koa');
MidwayFrameworkType.WEB_EXPRESS = new MidwayFrameworkType('@midwayjs/express');
MidwayFrameworkType.FAAS = new MidwayFrameworkType('@midwayjs/faas');
MidwayFrameworkType.MS_GRPC = new MidwayFrameworkType('@midwayjs/grpc');
MidwayFrameworkType.MS_RABBITMQ = new MidwayFrameworkType('@midwayjs/rabbitmq');
MidwayFrameworkType.MS_KAFKA = new MidwayFrameworkType('@midwayjs/kafka');
MidwayFrameworkType.WS_IO = new MidwayFrameworkType('@midwayjs/socketio');
MidwayFrameworkType.WS = new MidwayFrameworkType('@midwayjs/ws');
MidwayFrameworkType.SERVERLESS_APP = new MidwayFrameworkType('@midwayjs/serverless-app');
MidwayFrameworkType.CUSTOM = new MidwayFrameworkType('');
MidwayFrameworkType.EMPTY = new MidwayFrameworkType('empty');
MidwayFrameworkType.LIGHT = new MidwayFrameworkType('light');
MidwayFrameworkType.TASK = new MidwayFrameworkType('@midwayjs/task');
var ServerlessTriggerType;
(function (ServerlessTriggerType) {
    ServerlessTriggerType["EVENT"] = "event";
    ServerlessTriggerType["HTTP"] = "http";
    ServerlessTriggerType["API_GATEWAY"] = "apigw";
    ServerlessTriggerType["OS"] = "os";
    ServerlessTriggerType["CDN"] = "cdn";
    ServerlessTriggerType["LOG"] = "log";
    ServerlessTriggerType["TIMER"] = "timer";
    ServerlessTriggerType["MQ"] = "mq";
    ServerlessTriggerType["KAFKA"] = "kafka";
    ServerlessTriggerType["HSF"] = "hsf";
    ServerlessTriggerType["MTOP"] = "mtop";
    ServerlessTriggerType["SSR"] = "ssr";
})(ServerlessTriggerType = exports.ServerlessTriggerType || (exports.ServerlessTriggerType = {}));
var ObjectLifeCycleEvent;
(function (ObjectLifeCycleEvent) {
    ObjectLifeCycleEvent["BEFORE_BIND"] = "beforeBind";
    ObjectLifeCycleEvent["BEFORE_CREATED"] = "beforeObjectCreated";
    ObjectLifeCycleEvent["AFTER_CREATED"] = "afterObjectCreated";
    ObjectLifeCycleEvent["AFTER_INIT"] = "afterObjectInit";
    ObjectLifeCycleEvent["BEFORE_DESTROY"] = "beforeObjectDestroy";
})(ObjectLifeCycleEvent = exports.ObjectLifeCycleEvent || (exports.ObjectLifeCycleEvent = {}));
var MidwayProcessTypeEnum;
(function (MidwayProcessTypeEnum) {
    MidwayProcessTypeEnum["APPLICATION"] = "APPLICATION";
    MidwayProcessTypeEnum["AGENT"] = "AGENT";
})(MidwayProcessTypeEnum = exports.MidwayProcessTypeEnum || (exports.MidwayProcessTypeEnum = {}));
//# sourceMappingURL=interface.js.map