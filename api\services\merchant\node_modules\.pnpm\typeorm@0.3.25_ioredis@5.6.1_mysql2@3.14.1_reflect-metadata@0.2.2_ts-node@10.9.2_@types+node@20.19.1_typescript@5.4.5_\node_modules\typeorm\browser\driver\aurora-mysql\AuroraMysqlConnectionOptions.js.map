{"version": 3, "sources": ["../browser/src/driver/aurora-mysql/AuroraMysqlConnectionOptions.ts"], "names": [], "mappings": "", "file": "AuroraMysqlConnectionOptions.js", "sourcesContent": ["import { AuroraMysqlConnectionCredentialsOptions } from \"./AuroraMysqlConnectionCredentialsOptions\"\nimport { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * MySQL specific connection options.\n *\n * @see https://github.com/mysqljs/mysql#connection-options\n */\nexport interface AuroraMysqlConnectionOptions\n    extends BaseDataSourceOptions,\n        AuroraMysqlConnectionCredentialsOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"aurora-mysql\"\n\n    readonly region: string\n\n    readonly secretArn: string\n\n    readonly resourceArn: string\n\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"typeorm-aurora-data-api-driver\")\n     */\n    readonly driver?: any\n\n    readonly serviceConfigOptions?: { [key: string]: any } // pass optional AWS.ConfigurationOptions here\n\n    readonly formatOptions?: { [key: string]: any; castParameters: boolean }\n\n    /**\n     * Use spatial functions like GeomFromText and AsText which are removed in MySQL 8.\n     * (Default: true)\n     */\n    readonly legacySpatialSupport?: boolean\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}