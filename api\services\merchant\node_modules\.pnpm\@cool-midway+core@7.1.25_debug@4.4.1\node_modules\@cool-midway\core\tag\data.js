"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolUrlTagData = void 0;
const tag_1 = require("./../decorator/tag");
const decorator_1 = require("@midwayjs/decorator");
const tag_2 = require("../decorator/tag");
const _ = require("lodash");
/**
 * URL标签
 */
let CoolUrlTagData = class CoolUrlTagData {
    constructor() {
        this.data = {};
    }
    /**
     * 初始化
     */
    async init() {
        // 类标记
        await this.classTag();
        // 方法标记
        await this.methodTag();
    }
    /**
     * 类标记
     */
    async classTag() {
        const tags = (0, decorator_1.listModule)(tag_2.COOL_URL_TAG_KEY);
        for (const controller of tags) {
            // class的标记
            const controllerOption = (0, decorator_1.getClassMetadata)(decorator_1.CONTROLLER_KEY, controller);
            const tagOption = (0, decorator_1.getClassMetadata)(tag_2.COOL_URL_TAG_KEY, controller);
            if (tagOption === null || tagOption === void 0 ? void 0 : tagOption.key) {
                const data = this.data[tagOption.key] || [];
                this.data[tagOption.key] = _.uniq(data.concat(((tagOption === null || tagOption === void 0 ? void 0 : tagOption.value) || []).map(e => {
                    return controllerOption.prefix + '/' + e;
                })));
            }
        }
    }
    /**
     * 方法标记
     */
    async methodTag() {
        const controllers = (0, decorator_1.listModule)(decorator_1.CONTROLLER_KEY);
        for (const controller of controllers) {
            const controllerOption = (0, decorator_1.getClassMetadata)(decorator_1.CONTROLLER_KEY, controller);
            // 方法标记
            const listPropertyMetas = (0, decorator_1.listPropertyDataFromClass)(tag_1.COOL_METHOD_TAG_KEY, controller);
            const requestMetas = (0, decorator_1.getClassMetadata)(decorator_1.WEB_ROUTER_KEY, controller);
            for (const propertyMeta of listPropertyMetas) {
                const _data = this.data[propertyMeta.tag] || [];
                const requestMeta = _.find(requestMetas, { method: propertyMeta.key });
                if (requestMeta) {
                    this.data[propertyMeta.tag] = _.uniq(_data.concat(controllerOption.prefix + requestMeta.path));
                }
            }
        }
    }
    /**
     * 根据键获得
     * @param key
     * @param type
     * @returns
     */
    byKey(key, type) {
        return this.data[key].filter(e => {
            return type ? _.startsWith(e, `/${type}/`) : true;
        });
    }
};
exports.CoolUrlTagData = CoolUrlTagData;
exports.CoolUrlTagData = CoolUrlTagData = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(decorator_1.ScopeEnum.Singleton)
], CoolUrlTagData);
