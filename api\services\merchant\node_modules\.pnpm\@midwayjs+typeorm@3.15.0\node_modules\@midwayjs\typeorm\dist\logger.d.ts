import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, QueryRunner } from 'typeorm';
export declare class TypeORMLogger extends FileLogger implements Logger {
    readonly typeormLogger: any;
    constructor(typeormLogger: any);
    log(level: 'log' | 'info' | 'warn', message: any, queryRunner?: QueryRunner): void;
    write(strings: string | string[]): void;
}
//# sourceMappingURL=logger.d.ts.map