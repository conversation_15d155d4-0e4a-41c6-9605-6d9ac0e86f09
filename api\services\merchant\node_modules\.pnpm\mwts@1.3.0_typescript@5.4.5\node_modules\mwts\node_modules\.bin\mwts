#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/dist/src/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/dist/src/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules/mwts/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/mwts@1.3.0_typescript@5.4.5/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../dist/src/cli.js" "$@"
else
  exec node  "$basedir/../../dist/src/cli.js" "$@"
fi
