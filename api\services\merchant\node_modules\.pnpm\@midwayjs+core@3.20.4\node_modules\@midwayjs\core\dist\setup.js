"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.prepareGlobalApplicationContext = exports.prepareGlobalApplicationContextAsync = exports.destroyGlobalApplicationContext = exports.initializeGlobalApplicationContext = void 0;
const _1 = require("./");
const config_default_1 = require("./config/config.default");
const decorator_1 = require("./decorator");
const util = require("util");
const slsFunctionService_1 = require("./service/slsFunctionService");
const path_1 = require("path");
const healthService_1 = require("./service/healthService");
const performanceManager_1 = require("./common/performanceManager");
const debug = util.debuglog('midway:debug');
let stepIdx = 1;
function printStepDebugInfo(stepInfo) {
    debug(`\n\nStep ${stepIdx++}: ${stepInfo}\n`);
}
/**
 * midway framework main entry, this method bootstrap all service and framework.
 * @param globalOptions
 */
async function initializeGlobalApplicationContext(globalOptions) {
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.INITIALIZE);
    const applicationContext = await prepareGlobalApplicationContextAsync(globalOptions);
    printStepDebugInfo('Init logger');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.LOGGER_PREPARE);
    // init logger
    const loggerService = await applicationContext.getAsync(_1.MidwayLoggerService, [
        applicationContext,
        globalOptions,
    ]);
    if (loggerService.getLogger('appLogger')) {
        // register global logger
        applicationContext.registerObject('logger', loggerService.getLogger('appLogger'));
    }
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.LOGGER_PREPARE);
    printStepDebugInfo('Init MidwayMockService');
    // mock support
    await applicationContext.getAsync(_1.MidwayMockService, [applicationContext]);
    printStepDebugInfo('Init framework');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.FRAMEWORK_PREPARE);
    // framework/config/plugin/logger/app decorator support
    await applicationContext.getAsync(_1.MidwayFrameworkService, [
        applicationContext,
        globalOptions,
    ]);
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.FRAMEWORK_PREPARE);
    printStepDebugInfo('Init lifecycle');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.LIFECYCLE_PREPARE);
    // lifecycle support
    await applicationContext.getAsync(_1.MidwayLifeCycleService, [
        applicationContext,
    ]);
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.LIFECYCLE_PREPARE);
    printStepDebugInfo('Init preload modules');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.PRELOAD_MODULE_PREPARE);
    // some preload module init
    const modules = (0, decorator_1.listPreloadModule)();
    for (const module of modules) {
        // preload init context
        await applicationContext.getAsync(module);
    }
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.PRELOAD_MODULE_PREPARE);
    printStepDebugInfo('End of initialize and start');
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.INITIALIZE);
    return applicationContext;
}
exports.initializeGlobalApplicationContext = initializeGlobalApplicationContext;
async function destroyGlobalApplicationContext(applicationContext) {
    const loggerService = await applicationContext.getAsync(_1.MidwayLoggerService);
    const loggerFactory = loggerService.getCurrentLoggerFactory();
    // stop lifecycle
    const lifecycleService = await applicationContext.getAsync(_1.MidwayLifeCycleService);
    await lifecycleService.stop();
    // stop container
    await applicationContext.stop();
    (0, decorator_1.clearBindContainer)();
    loggerFactory.close();
    performanceManager_1.MidwayPerformanceManager.cleanAll();
    global['MIDWAY_APPLICATION_CONTEXT'] = undefined;
    global['MIDWAY_MAIN_FRAMEWORK'] = undefined;
}
exports.destroyGlobalApplicationContext = destroyGlobalApplicationContext;
/**
 * prepare applicationContext
 * @param globalOptions
 */
async function prepareGlobalApplicationContextAsync(globalOptions) {
    var _a, _b, _c, _d, _e;
    printStepDebugInfo('Ready to create applicationContext');
    debug('[core]: start "initializeGlobalApplicationContext"');
    debug(`[core]: bootstrap options = ${util.inspect(globalOptions)}`);
    const appDir = (_a = globalOptions.appDir) !== null && _a !== void 0 ? _a : '';
    const baseDir = (_b = globalOptions.baseDir) !== null && _b !== void 0 ? _b : '';
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.METADATA_PREPARE);
    // new container
    const applicationContext = (_c = globalOptions.applicationContext) !== null && _c !== void 0 ? _c : new _1.MidwayContainer();
    // bind container to decoratorManager
    debug('[core]: delegate module map from decoratorManager');
    (0, decorator_1.bindContainer)(applicationContext);
    global['MIDWAY_APPLICATION_CONTEXT'] = applicationContext;
    // register baseDir and appDir
    applicationContext.registerObject('baseDir', baseDir);
    applicationContext.registerObject('appDir', appDir);
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.METADATA_PREPARE);
    debug('[core]: set default file detector');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DETECTOR_PREPARE);
    printStepDebugInfo('Ready module detector');
    if (!globalOptions.moduleLoadType) {
        globalOptions.moduleLoadType = 'commonjs';
    }
    // set module detector
    if (globalOptions.moduleDetector !== false) {
        debug('[core]: set module load type = %s', globalOptions.moduleLoadType);
        // set default entry file
        if (!globalOptions.imports) {
            globalOptions.imports = [
                await (0, _1.loadModule)((0, path_1.join)(baseDir, `configuration${(0, _1.isTypeScriptEnvironment)() ? '.ts' : '.js'}`), {
                    loadMode: globalOptions.moduleLoadType,
                    safeLoad: true,
                }),
            ];
        }
        if (globalOptions.moduleDetector === undefined) {
            if (globalOptions.moduleLoadType === 'esm') {
                applicationContext.setFileDetector(new _1.ESModuleFileDetector({
                    loadDir: baseDir,
                    ignore: (_d = globalOptions.ignore) !== null && _d !== void 0 ? _d : [],
                }));
                globalOptions.moduleLoadType = 'esm';
            }
            else {
                applicationContext.setFileDetector(new _1.CommonJSFileDetector({
                    loadDir: baseDir,
                    ignore: (_e = globalOptions.ignore) !== null && _e !== void 0 ? _e : [],
                }));
            }
        }
    }
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DETECTOR_PREPARE);
    printStepDebugInfo('Binding inner service');
    // bind inner service
    applicationContext.bindClass(_1.MidwayEnvironmentService);
    applicationContext.bindClass(_1.MidwayInformationService);
    applicationContext.bindClass(_1.MidwayAspectService);
    applicationContext.bindClass(_1.MidwayDecoratorService);
    applicationContext.bindClass(_1.MidwayConfigService);
    applicationContext.bindClass(_1.MidwayLoggerService);
    applicationContext.bindClass(_1.MidwayApplicationManager);
    applicationContext.bindClass(_1.MidwayFrameworkService);
    applicationContext.bindClass(_1.MidwayMiddlewareService);
    applicationContext.bindClass(_1.MidwayLifeCycleService);
    applicationContext.bindClass(_1.MidwayMockService);
    applicationContext.bindClass(_1.MidwayWebRouterService);
    applicationContext.bindClass(slsFunctionService_1.MidwayServerlessFunctionService);
    applicationContext.bindClass(healthService_1.MidwayHealthService);
    applicationContext.bindClass(_1.MidwayPriorityManager);
    printStepDebugInfo('Binding preload module');
    // bind preload module
    if (globalOptions.preloadModules && globalOptions.preloadModules.length) {
        for (const preloadModule of globalOptions.preloadModules) {
            applicationContext.bindClass(preloadModule);
        }
    }
    printStepDebugInfo('Init MidwayConfigService, MidwayAspectService and MidwayDecoratorService');
    // init default environment
    const environmentService = applicationContext.get(_1.MidwayEnvironmentService);
    environmentService.setModuleLoadType(globalOptions.moduleLoadType);
    // init default config
    const configService = applicationContext.get(_1.MidwayConfigService);
    configService.add([
        {
            default: config_default_1.default,
        },
    ]);
    // init aop support
    applicationContext.get(_1.MidwayAspectService, [applicationContext]);
    // init decorator service
    applicationContext.get(_1.MidwayDecoratorService, [applicationContext]);
    printStepDebugInfo('Load imports(component) and user code configuration module');
    applicationContext.load([].concat(globalOptions.imports).concat(globalOptions.configurationModule));
    printStepDebugInfo('Run applicationContext ready method');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DEFINITION_PREPARE);
    // bind user code module
    await applicationContext.ready();
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DEFINITION_PREPARE);
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.CONFIG_LOAD);
    if (globalOptions.globalConfig) {
        if (Array.isArray(globalOptions.globalConfig)) {
            configService.add(globalOptions.globalConfig);
        }
        else {
            configService.addObject(globalOptions.globalConfig);
        }
    }
    printStepDebugInfo('Load config file');
    // merge config
    configService.load();
    debug('[core]: Current config = %j', configService.getConfiguration());
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.CONFIG_LOAD);
    // middleware support
    applicationContext.get(_1.MidwayMiddlewareService, [applicationContext]);
    return applicationContext;
}
exports.prepareGlobalApplicationContextAsync = prepareGlobalApplicationContextAsync;
/**
 * prepare applicationContext, it use in egg framework, hooks and serverless function generator
 * @param globalOptions
 */
function prepareGlobalApplicationContext(globalOptions) {
    var _a, _b, _c, _d;
    printStepDebugInfo('Ready to create applicationContext');
    debug('[core]: start "initializeGlobalApplicationContext"');
    debug(`[core]: bootstrap options = ${util.inspect(globalOptions)}`);
    const appDir = (_a = globalOptions.appDir) !== null && _a !== void 0 ? _a : '';
    const baseDir = (_b = globalOptions.baseDir) !== null && _b !== void 0 ? _b : '';
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.METADATA_PREPARE);
    // new container
    const applicationContext = (_c = globalOptions.applicationContext) !== null && _c !== void 0 ? _c : new _1.MidwayContainer();
    // bind container to decoratorManager
    debug('[core]: delegate module map from decoratorManager');
    (0, decorator_1.bindContainer)(applicationContext);
    global['MIDWAY_APPLICATION_CONTEXT'] = applicationContext;
    // register baseDir and appDir
    applicationContext.registerObject('baseDir', baseDir);
    applicationContext.registerObject('appDir', appDir);
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.METADATA_PREPARE);
    printStepDebugInfo('Ready module detector');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DETECTOR_PREPARE);
    if (!globalOptions.moduleLoadType) {
        globalOptions.moduleLoadType = 'commonjs';
    }
    if (globalOptions.moduleDetector !== false) {
        if (globalOptions.moduleDetector === undefined) {
            applicationContext.setFileDetector(new _1.CommonJSFileDetector({
                ignore: (_d = globalOptions.ignore) !== null && _d !== void 0 ? _d : [],
            }));
        }
        else if (globalOptions.moduleDetector) {
            applicationContext.setFileDetector(globalOptions.moduleDetector);
        }
    }
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DETECTOR_PREPARE);
    printStepDebugInfo('Binding inner service');
    // bind inner service
    applicationContext.bindClass(_1.MidwayEnvironmentService);
    applicationContext.bindClass(_1.MidwayInformationService);
    applicationContext.bindClass(_1.MidwayAspectService);
    applicationContext.bindClass(_1.MidwayDecoratorService);
    applicationContext.bindClass(_1.MidwayConfigService);
    applicationContext.bindClass(_1.MidwayLoggerService);
    applicationContext.bindClass(_1.MidwayApplicationManager);
    applicationContext.bindClass(_1.MidwayFrameworkService);
    applicationContext.bindClass(_1.MidwayMiddlewareService);
    applicationContext.bindClass(_1.MidwayLifeCycleService);
    applicationContext.bindClass(_1.MidwayMockService);
    applicationContext.bindClass(_1.MidwayWebRouterService);
    applicationContext.bindClass(slsFunctionService_1.MidwayServerlessFunctionService);
    applicationContext.bindClass(healthService_1.MidwayHealthService);
    applicationContext.bindClass(_1.MidwayPriorityManager);
    printStepDebugInfo('Binding preload module');
    // bind preload module
    if (globalOptions.preloadModules && globalOptions.preloadModules.length) {
        for (const preloadModule of globalOptions.preloadModules) {
            applicationContext.bindClass(preloadModule);
        }
    }
    printStepDebugInfo('Init MidwayConfigService, MidwayAspectService and MidwayDecoratorService');
    // init default environment
    const environmentService = applicationContext.get(_1.MidwayEnvironmentService);
    environmentService.setModuleLoadType(globalOptions.moduleLoadType);
    // init default config
    const configService = applicationContext.get(_1.MidwayConfigService);
    configService.add([
        {
            default: config_default_1.default,
        },
    ]);
    // init aop support
    applicationContext.get(_1.MidwayAspectService, [applicationContext]);
    // init decorator service
    applicationContext.get(_1.MidwayDecoratorService, [applicationContext]);
    printStepDebugInfo('Load imports(component) and user code configuration module');
    if (!globalOptions.imports) {
        globalOptions.imports = [
            (0, _1.safeRequire)((0, path_1.join)(globalOptions.baseDir, 'configuration')),
        ];
    }
    applicationContext.load([].concat(globalOptions.imports).concat(globalOptions.configurationModule));
    printStepDebugInfo('Run applicationContext ready method');
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DEFINITION_PREPARE);
    // bind user code module
    applicationContext.ready();
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.DEFINITION_PREPARE);
    performanceManager_1.MidwayInitializerPerformanceManager.markStart(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.CONFIG_LOAD);
    if (globalOptions.globalConfig) {
        if (Array.isArray(globalOptions.globalConfig)) {
            configService.add(globalOptions.globalConfig);
        }
        else {
            configService.addObject(globalOptions.globalConfig);
        }
    }
    printStepDebugInfo('Load config file');
    // merge config
    configService.load();
    debug('[core]: Current config = %j', configService.getConfiguration());
    performanceManager_1.MidwayInitializerPerformanceManager.markEnd(performanceManager_1.MidwayInitializerPerformanceManager.MEASURE_KEYS.CONFIG_LOAD);
    // middleware support
    applicationContext.get(_1.MidwayMiddlewareService, [applicationContext]);
    return applicationContext;
}
exports.prepareGlobalApplicationContext = prepareGlobalApplicationContext;
//# sourceMappingURL=setup.js.map