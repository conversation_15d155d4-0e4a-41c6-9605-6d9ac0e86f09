import { Init, Inject, Provide } from '@midwayjs/core';
import {
  BaseService,
  CoolCommException,
  CoolTransaction,
} from '@cool-midway/core';
import { CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, QueryRunner, Repository } from 'typeorm';
import { FinanceWalletUserEntity } from '../../entity/wallet/user';
import { FinanceWalletRecordEntity } from '../../entity/wallet/record';
import { FinanceWalletRecordService } from './record';

/**
 * 用户钱包微服务
 */
@Provide()
@CoolRpcService({
  entity: FinanceWalletUserEntity,
  method: ["add", "delete", "update", "info", "list", "page"],
})
export class FinanceWalletUserService extends BaseService {
  @InjectEntityModel(FinanceWalletUserEntity)
  financeWalletUserEntity!: Repository<FinanceWalletUserEntity>;

  @Inject()
  financeWalletRecordService: FinanceWalletRecordService;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeWalletUserEntity);
  }

  /**
   * 更新钱包信息
   * @param param
   */
  async updateWallet(param: any) {
    const wallet = await this.financeWalletUserEntity.findOneBy({
      userId: Equal(param.userId),
    });
    
    if (!wallet) {
      throw new CoolCommException('用户钱包不存在');
    }

    // 需要更新冻结金额和余额
    if (param.freezeAmount != wallet.freezeAmount) {
      // 对比新旧冻结金额，计算差值
      const diff = param.freezeAmount - wallet.freezeAmount;
      // 跟余额比较，看下是否足够
      const validBalance = wallet.balance - diff;
      if (validBalance < 0) {
        throw new CoolCommException('用户余额不足以抵扣追加的冻结金额');
      }
      // 更新冻结金额
      await this.financeWalletUserEntity.update(wallet.id, {
        freezeAmount: param.freezeAmount,
        balance: validBalance,
      });
    } else {
      await this.financeWalletUserEntity.save(param);
    }
  }

  /**
   * 创建钱包（RPC暴露）
   * @param userId
   */
  async createWallet(userId: number) {
    const info = await this.financeWalletUserEntity.findOneBy({
      userId: Equal(userId),
    });
    if (!info) {
      return this.financeWalletUserEntity.save({
        userId,
        balance: 0,
        freezeAmount: 0,
      });
    }
    return info;
  }

  /**
   * 钱包详情（RPC暴露）
   * @param userId
   */
  async getWalletDetail(userId: number) {
    await this.createWallet(userId);
    const info = await this.financeWalletUserEntity.findOneBy({
      userId: Equal(userId),
    });
    return info;
  }

  /**
   * 获得可用金额（RPC暴露）
   * @param userId
   */
  async getValidBalance(userId: number) {
    const info = await this.getWalletDetail(userId);
    return info.balance - info.freezeAmount;
  }

  /**
   * 批量获取用户余额（RPC暴露）
   * @param userIds
   */
  async batchGetBalances(userIds: number[]) {
    const wallets = await this.financeWalletUserEntity.find({
      where: userIds.map(userId => ({ userId }))
    });
    
    return userIds.map(userId => {
      const wallet = wallets.find(w => w.userId === userId);
      return {
        userId,
        balance: wallet?.balance || 0,
        freezeAmount: wallet?.freezeAmount || 0,
        validBalance: (wallet?.balance || 0) - (wallet?.freezeAmount || 0)
      };
    });
  }

  /**
   * 更新余额 需要事务（RPC暴露）
   * @param userId
   * @param amount
   * @param queryRunner
   */
  @CoolTransaction({ isolation: 'SERIALIZABLE' })
  async changeBalance(
    userId: number,
    amount: number,
    queryRunner?: QueryRunner
  ) {
    // 更新余额
    await queryRunner.manager
      .createQueryBuilder()
      .update(FinanceWalletUserEntity)
      .set({ balance: () => `balance + ${amount}` })
      .where({ userId: Equal(userId) })
      .execute();
  }

  /**
   * 更新冻结金额 需要事务（RPC暴露）
   * @param userId
   * @param amount
   * @param queryRunner
   */
  @CoolTransaction({ isolation: 'SERIALIZABLE' })
  async changeFreeze(
    userId: number,
    amount: number,
    queryRunner?: QueryRunner
  ) {
    // 更新冻结金额
    await queryRunner.manager
      .createQueryBuilder()
      .update(FinanceWalletUserEntity)
      .set({ freezeAmount: () => `freezeAmount + ${amount}` })
      .where({ userId: Equal(userId) })
      .execute();
  }

  /**
   * 钱包记录 需要事务（RPC暴露）
   * @param record
   */
  @CoolTransaction({ isolation: 'SERIALIZABLE' })
  async addWalletRecord(record: FinanceWalletRecordEntity, queryRunner?: QueryRunner) {
    // 添加记录
    await queryRunner.manager.insert<FinanceWalletRecordEntity>(
      FinanceWalletRecordEntity,
      record
    );
    // 更新余额
    await queryRunner.manager
      .createQueryBuilder()
      .update(FinanceWalletUserEntity)
      .set({ balance: () => `balance + ${record.amount}` })
      .where({ userId: Equal(record.userId) })
      .execute();
  }

  /**
   * 获取钱包统计信息（RPC暴露）
   */
  async getWalletStats() {
    const stats = await this.financeWalletUserEntity
      .createQueryBuilder('wallet')
      .select([
        'COUNT(wallet.id) as totalUsers',
        'SUM(wallet.balance) as totalBalance',
        'SUM(wallet.freezeAmount) as totalFreezeAmount',
        'AVG(wallet.balance) as avgBalance'
      ])
      .getRawOne();

    return {
      totalUsers: parseInt(stats.totalUsers) || 0,
      totalBalance: parseFloat(stats.totalBalance) || 0,
      totalFreezeAmount: parseFloat(stats.totalFreezeAmount) || 0,
      avgBalance: parseFloat(stats.avgBalance) || 0,
      availableBalance: (parseFloat(stats.totalBalance) || 0) - (parseFloat(stats.totalFreezeAmount) || 0)
    };
  }
}
