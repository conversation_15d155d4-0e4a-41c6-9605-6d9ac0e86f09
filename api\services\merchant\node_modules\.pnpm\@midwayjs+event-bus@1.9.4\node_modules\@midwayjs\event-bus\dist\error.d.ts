import { Message } from './interface';
export declare class EventBusPublishTimeoutError extends <PERSON>rror {
    constructor(messageId: string);
}
export declare class EventBusTimeoutError extends Error {
    constructor();
}
export declare class EventBusMainPostError extends Error {
    constructor(message: Message, err: Error);
}
export declare class EventBusWorkerPostError extends Error {
    constructor(message: Message, err: Error);
}
export declare class EventBusPublishSpecifyWorkerError extends Error {
    constructor(workerId: string);
}
export declare class EventBusDispatchStrategyError extends Error {
    constructor();
}
//# sourceMappingURL=error.d.ts.map