{"name": "keygrip", "version": "1.1.0", "description": "Key signing and verification for rotated credentials", "license": "MIT", "repository": "crypto-utils/keygrip", "dependencies": {"tsscmp": "1.0.6"}, "devDependencies": {"mocha": "6.1.4", "nyc": "14.0.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test"}}