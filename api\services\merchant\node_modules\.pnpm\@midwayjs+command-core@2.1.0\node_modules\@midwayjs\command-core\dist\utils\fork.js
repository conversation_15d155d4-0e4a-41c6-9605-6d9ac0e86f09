"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.forkNode = void 0;
const child_process_1 = require("child_process");
const childs = new Set();
let hadHook = false;
const gracefull = proc => {
    childs.add(proc);
    if (!hadHook) {
        hadHook = true;
        let signal;
        ['SIGINT', 'SIGQUIT', 'SIGTERM'].forEach((event) => {
            process.once(event, () => {
                signal = event;
                process.exit(0);
            });
        });
        process.once('exit', () => {
            for (const child of childs) {
                child.kill(signal);
            }
        });
    }
};
const forkNode = (modulePath, args = [], options = {}) => {
    options.stdio = options.stdio || 'inherit';
    const proc = (0, child_process_1.fork)(modulePath, args, options);
    gracefull(proc);
    return new Promise((resolve, reject) => {
        proc.once('exit', (code) => {
            childs.delete(proc);
            if (code !== 0) {
                const err = new Error(modulePath + ' ' + args + ' exit with code ' + code);
                err.code = code;
                reject(err);
            }
            else {
                resolve(proc);
            }
        });
    });
};
exports.forkNode = forkNode;
//# sourceMappingURL=fork.js.map