"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.clean = void 0;
const chalk = require("chalk");
const util_1 = require("./util");
/**
 * Remove files generated by the build.
 */
async function clean(options) {
    const tsconfig = (await (0, util_1.getTSConfig)(options.targetRootDir));
    if (tsconfig.compilerOptions && tsconfig.compilerOptions.outDir) {
        const outDir = tsconfig.compilerOptions.outDir;
        if (outDir === '.') {
            options.logger.error(`${chalk.red('ERROR:')} ${chalk.gray('compilerOptions.outDir')} ` +
                'cannot use the value ".".  That would delete all of our sources.');
            return false;
        }
        const message = `${chalk.red('Removing')} ${outDir} ...`;
        options.logger.log(message);
        await (0, util_1.rimrafp)(outDir);
        return true;
    }
    else {
        options.logger.error(`${chalk.red('ERROR:')} The ${chalk.gray('clean')} command` +
            ` requires ${chalk.gray('compilerOptions.outDir')} to be defined in ` +
            'tsconfig.json.');
        return false;
    }
}
exports.clean = clean;
//# sourceMappingURL=clean.js.map