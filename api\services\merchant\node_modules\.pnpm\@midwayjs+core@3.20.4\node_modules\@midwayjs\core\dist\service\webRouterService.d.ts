import { ControllerOption } from '../decorator';
export interface RouterInfo {
    /**
     * uuid
     */
    id?: string;
    /**
     * router prefix from controller
     */
    prefix?: string;
    /**
     * router alias name
     */
    routerName?: string;
    /**
     * router path, without prefix
     */
    url: string | RegExp;
    /**
     * request method for http, like get/post/delete
     */
    requestMethod: string;
    /**
     * invoke function method
     */
    method: string | ((...args: any[]) => void);
    /**
     * router description
     */
    description?: string;
    /**
     * @deprecated
     */
    summary?: string;
    /**
     * router handler function key，for IoC container load
     */
    handlerName?: string;
    /**
     *  serverless func load key, will be override by @ServerlessTrigger and @ServerlessFunction
     */
    funcHandlerName?: string;
    /**
     * controller provideId
     */
    controllerId?: string;
    /**
     * controller class
     */
    controllerClz?: new (...args: any[]) => any;
    /**
     * router middleware
     */
    middleware?: any[];
    /**
     * controller middleware in this router
     */
    controllerMiddleware?: any[];
    /**
     * request args metadata
     */
    requestMetadata?: any[];
    /**
     * response data metadata
     */
    responseMetadata?: any[];
    /**
     * serverless function name, will be override by @ServerlessTrigger and @ServerlessFunction
     */
    functionName?: string;
    /**
     * serverless trigger name
     */
    functionTriggerName?: string;
    /**
     * serverless function trigger metadata
     */
    functionTriggerMetadata?: any;
    /**
     * serverless function metadata
     */
    functionMetadata?: any;
    /**
     * url with prefix
     */
    fullUrl?: string;
    /**
     * pattern after path-regexp compile
     */
    fullUrlCompiledRegexp?: RegExp;
    /**
     * url after wildcard and can be path-to-regexp by path-to-regexp v6
     */
    fullUrlFlattenString?: string;
}
export type DynamicRouterInfo = Omit<RouterInfo, 'id' | 'method' | 'controllerId' | 'controllerMiddleware' | 'responseMetadata'>;
export interface RouterPriority {
    prefix: string;
    priority: number;
    middleware: any[];
    routerOptions: any;
    controllerId: string;
    /**
     * 路由控制器或者函数 module 本身
     */
    routerModule: any;
}
export interface RouterCollectorOptions {
    includeFunctionRouter?: boolean;
    globalPrefix?: string;
}
export declare class MidwayWebRouterService {
    readonly options: RouterCollectorOptions;
    private isReady;
    protected routes: Map<string, RouterInfo[]>;
    protected routesPriority: RouterPriority[];
    constructor(options?: RouterCollectorOptions);
    protected analyze(): Promise<void>;
    protected analyzeController(): void;
    protected sortPrefixAndRouter(): void;
    /**
     * dynamically add a controller
     * @param controllerClz
     * @param controllerOption
     * @param functionMeta
     */
    addController(controllerClz: any, controllerOption: ControllerOption, functionMeta?: boolean): any;
    addController(controllerClz: any, controllerOption: ControllerOption, resourceOptions?: {
        resourceFilter: (routerInfo: RouterInfo) => boolean;
    }, functionMeta?: boolean): any;
    /**
     * dynamically add a route to root prefix
     * @param routerFunction
     * @param routerInfoOption
     */
    addRouter(routerFunction: (...args: any[]) => void, routerInfoOption: DynamicRouterInfo): void;
    sortRouter(urlMatchList: RouterInfo[]): {
        _pureRouter: string;
        _level: number;
        _paramString: string;
        _category: number;
        _weight: number;
        /**
         * uuid
         */
        id?: string;
        /**
         * router prefix from controller
         */
        prefix?: string;
        /**
         * router alias name
         */
        routerName?: string;
        /**
         * router path, without prefix
         */
        url: string | RegExp;
        /**
         * request method for http, like get/post/delete
         */
        requestMethod: string;
        /**
         * invoke function method
         */
        method: string | ((...args: any[]) => void);
        /**
         * router description
         */
        description?: string;
        /**
         * @deprecated
         */
        summary?: string;
        /**
         * router handler function key，for IoC container load
         */
        handlerName?: string;
        /**
         *  serverless func load key, will be override by @ServerlessTrigger and @ServerlessFunction
         */
        funcHandlerName?: string;
        /**
         * controller provideId
         */
        controllerId?: string;
        /**
         * controller class
         */
        controllerClz?: new (...args: any[]) => any;
        /**
         * router middleware
         */
        middleware?: any[];
        /**
         * controller middleware in this router
         */
        controllerMiddleware?: any[];
        /**
         * request args metadata
         */
        requestMetadata?: any[];
        /**
         * response data metadata
         */
        responseMetadata?: any[];
        /**
         * serverless function name, will be override by @ServerlessTrigger and @ServerlessFunction
         */
        functionName?: string;
        /**
         * serverless trigger name
         */
        functionTriggerName?: string;
        /**
         * serverless function trigger metadata
         */
        functionTriggerMetadata?: any;
        /**
         * serverless function metadata
         */
        functionMetadata?: any;
        /**
         * url with prefix
         */
        fullUrl?: string;
        /**
         * pattern after path-regexp compile
         */
        fullUrlCompiledRegexp?: RegExp;
        /**
         * url after wildcard and can be path-to-regexp by path-to-regexp v6
         */
        fullUrlFlattenString?: string;
    }[];
    getRoutePriorityList(): Promise<RouterPriority[]>;
    getRouterTable(): Promise<Map<string, RouterInfo[]>>;
    getFlattenRouterTable(options?: {
        compileUrlPattern?: boolean;
    }): Promise<RouterInfo[]>;
    getMatchedRouterInfo(routerUrl: string, method: string): Promise<RouterInfo | undefined>;
    protected checkDuplicateAndPush(prefix: any, routerInfo: RouterInfo): void;
}
//# sourceMappingURL=webRouterService.d.ts.map