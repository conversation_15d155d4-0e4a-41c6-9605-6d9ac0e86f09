{"version": 3, "sources": ["../browser/src/error/MissingDeleteDateColumnError.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,4BAA6B,SAAQ,YAAY;IAC1D,YAAY,cAA8B;QACtC,KAAK,CACD,WAAW,cAAc,CAAC,IAAI,sCAAsC,CACvE,CAAA;IACL,CAAC;CACJ", "file": "MissingDeleteDateColumnError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class MissingDeleteDateColumnError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata) {\n        super(\n            `Entity \"${entityMetadata.name}\" does not have delete date columns.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}