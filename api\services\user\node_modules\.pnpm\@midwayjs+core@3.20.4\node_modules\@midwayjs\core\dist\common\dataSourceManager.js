"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.globModels = exports.formatGlobString = exports.DataSourceManager = void 0;
/**
 * 数据源管理器实现
 */
const extend_1 = require("../util/extend");
const error_1 = require("../error");
const glob_1 = require("@midwayjs/glob");
const path_1 = require("path");
const types_1 = require("../util/types");
const constants_1 = require("../constants");
const util_1 = require("util");
const util_2 = require("../util");
const decorator_1 = require("../decorator");
const environmentService_1 = require("../service/environmentService");
const priorityManager_1 = require("./priorityManager");
const debug = (0, util_1.debuglog)('midway:debug');
class DataSourceManager {
    constructor() {
        this.dataSource = new Map();
        this.options = {};
        this.modelMapping = new WeakMap();
        this.dataSourcePriority = {};
    }
    async initDataSource(dataSourceConfig, baseDirOrOptions) {
        var _a;
        this.options = dataSourceConfig;
        if (!this.options.dataSource) {
            throw new error_1.MidwayParameterError('[DataSourceManager] must set options.dataSource.');
        }
        if (typeof baseDirOrOptions === 'string') {
            baseDirOrOptions = {
                baseDir: baseDirOrOptions,
                entitiesConfigKey: 'entities',
            };
        }
        for (const dataSourceName in dataSourceConfig.dataSource) {
            const dataSourceOptions = dataSourceConfig.dataSource[dataSourceName];
            const userEntities = dataSourceOptions[baseDirOrOptions.entitiesConfigKey];
            if (userEntities) {
                const entities = new Set();
                // loop entities and glob files to model
                for (const entity of userEntities) {
                    if (typeof entity === 'string') {
                        // string will be glob file
                        const models = await globModels(entity, baseDirOrOptions.baseDir, (_a = this.environmentService) === null || _a === void 0 ? void 0 : _a.getModuleLoadType());
                        for (const model of models) {
                            entities.add(model);
                            this.modelMapping.set(model, dataSourceName);
                        }
                    }
                    else {
                        // model will be added to array
                        entities.add(entity);
                        this.modelMapping.set(entity, dataSourceName);
                    }
                }
                dataSourceOptions[baseDirOrOptions.entitiesConfigKey] =
                    Array.from(entities);
                debug(`[core]: DataManager load ${dataSourceOptions[baseDirOrOptions.entitiesConfigKey].length} models from ${dataSourceName}.`);
            }
            // create data source
            const opts = {
                cacheInstance: dataSourceConfig.cacheInstance,
                validateConnection: dataSourceConfig.validateConnection,
            };
            await this.createInstance(dataSourceOptions, dataSourceName, opts);
        }
    }
    /**
     * get a data source instance
     * @param dataSourceName
     */
    getDataSource(dataSourceName) {
        return this.dataSource.get(dataSourceName);
    }
    /**
     * check data source has exists
     * @param dataSourceName
     */
    hasDataSource(dataSourceName) {
        return this.dataSource.has(dataSourceName);
    }
    getDataSourceNames() {
        return Array.from(this.dataSource.keys());
    }
    getAllDataSources() {
        return this.dataSource;
    }
    /**
     * check the data source is connected
     * @param dataSourceName
     */
    async isConnected(dataSourceName) {
        const inst = this.getDataSource(dataSourceName);
        return inst ? this.checkConnected(inst) : false;
    }
    async createInstance(config, clientName, options) {
        const cache = options && typeof options.cacheInstance === 'boolean'
            ? options.cacheInstance
            : true;
        const validateConnection = (options && options.validateConnection) || false;
        // options.clients[id] will be merged with options.default
        const configNow = (0, extend_1.extend)(true, {}, this.options['default'], config);
        const client = await this.createDataSource(configNow, clientName);
        if (cache && clientName && client) {
            this.dataSource.set(clientName, client);
        }
        if (validateConnection) {
            if (!client) {
                throw new error_1.MidwayCommonError(`[DataSourceManager] ${clientName} initialization failed.`);
            }
            const connected = await this.checkConnected(client);
            if (!connected) {
                throw new error_1.MidwayCommonError(`[DataSourceManager] ${clientName} is not connected.`);
            }
        }
        return client;
    }
    /**
     * get data source name by model or repository
     * @param modelOrRepository
     */
    getDataSourceNameByModel(modelOrRepository) {
        return this.modelMapping.get(modelOrRepository);
    }
    /**
     * Call destroyDataSource() on all data sources
     */
    async stop() {
        const arr = Array.from(this.dataSource.values());
        await Promise.all(arr.map(dbh => {
            return this.destroyDataSource(dbh);
        }));
        this.dataSource.clear();
    }
    getDefaultDataSourceName() {
        if (this.innerDefaultDataSourceName === undefined) {
            if (this.options['defaultDataSourceName']) {
                this.innerDefaultDataSourceName = this.options['defaultDataSourceName'];
            }
            else if (this.dataSource.size === 1) {
                // Set the default source name when there is only one data source
                this.innerDefaultDataSourceName = Array.from(this.dataSource.keys())[0];
            }
            else {
                // Set empty string for cache
                this.innerDefaultDataSourceName = '';
            }
        }
        return this.innerDefaultDataSourceName;
    }
    getDataSourcePriority(name) {
        return this.priorityManager.getPriority(this.dataSourcePriority[name]);
    }
    isHighPriority(name) {
        return this.priorityManager.isHighPriority(this.dataSourcePriority[name]);
    }
    isMediumPriority(name) {
        return this.priorityManager.isMediumPriority(this.dataSourcePriority[name]);
    }
    isLowPriority(name) {
        return this.priorityManager.isLowPriority(this.dataSourcePriority[name]);
    }
}
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", String)
], DataSourceManager.prototype, "appDir", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", environmentService_1.MidwayEnvironmentService)
], DataSourceManager.prototype, "environmentService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", priorityManager_1.MidwayPriorityManager)
], DataSourceManager.prototype, "priorityManager", void 0);
exports.DataSourceManager = DataSourceManager;
function formatGlobString(globString) {
    let pattern;
    if (!/^\*/.test(globString)) {
        globString = '/' + globString;
    }
    const parsePattern = (0, path_1.parse)(globString);
    if (parsePattern.base && (/\*/.test(parsePattern.base) || parsePattern.ext)) {
        pattern = [globString];
    }
    else {
        pattern = [...constants_1.DEFAULT_PATTERN.map(p => (0, path_1.join)(globString, p))];
    }
    return pattern;
}
exports.formatGlobString = formatGlobString;
async function globModels(globString, appDir, loadMode) {
    const pattern = formatGlobString(globString);
    const models = [];
    // string will be glob file
    const files = (0, glob_1.run)(pattern, {
        cwd: appDir,
        ignore: constants_1.IGNORE_PATTERN,
    });
    for (const file of files) {
        const exports = await (0, util_2.loadModule)(file, {
            loadMode,
        });
        if (types_1.Types.isClass(exports)) {
            models.push(exports);
        }
        else {
            for (const m in exports) {
                const module = exports[m];
                if (types_1.Types.isClass(module)) {
                    models.push(module);
                }
            }
        }
    }
    return models;
}
exports.globModels = globModels;
//# sourceMappingURL=dataSourceManager.js.map