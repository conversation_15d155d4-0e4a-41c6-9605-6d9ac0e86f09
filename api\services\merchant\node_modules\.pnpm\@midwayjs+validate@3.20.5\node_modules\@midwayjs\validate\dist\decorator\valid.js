"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Valid = void 0;
const core_1 = require("@midwayjs/core");
const constants_1 = require("../constants");
const pipe_1 = require("../pipe");
function Valid(schemaOrPipes, pipes) {
    if (Array.isArray(schemaOrPipes)) {
        pipes = schemaOrPipes;
        schemaOrPipes = undefined;
    }
    else {
        pipes = pipes || [];
    }
    return (0, core_1.createCustomParamDecorator)(constants_1.VALID_KEY, {
        schema: schemaOrPipes,
    }, {
        pipes: [pipe_1.DecoratorValidPipe, ...pipes],
    });
}
exports.Valid = Valid;
//# sourceMappingURL=valid.js.map