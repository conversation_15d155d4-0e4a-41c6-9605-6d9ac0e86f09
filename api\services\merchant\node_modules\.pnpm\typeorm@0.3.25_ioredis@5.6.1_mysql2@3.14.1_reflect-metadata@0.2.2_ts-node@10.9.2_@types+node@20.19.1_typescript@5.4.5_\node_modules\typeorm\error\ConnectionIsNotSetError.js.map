{"version": 3, "sources": ["../../src/error/ConnectionIsNotSetError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uBAAwB,SAAQ,2BAAY;IACrD,YAAY,MAAc;QACtB,KAAK,CACD,mBAAmB,MAAM,+DAA+D,CAC3F,CAAA;IACL,CAAC;CACJ;AAND,0DAMC", "file": "ConnectionIsNotSetError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to execute operation that requires connection to be established.\n */\nexport class ConnectionIsNotSetError extends TypeORMError {\n    constructor(dbType: string) {\n        super(\n            `Connection with ${dbType} database is not established. Check connection configuration.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}