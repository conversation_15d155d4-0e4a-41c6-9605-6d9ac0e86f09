{"version": 3, "file": "IsVariableWidth.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsVariableWidth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,wBAAwB,MAAM,+BAA+B,CAAC;AAErE,MAAM,CAAC,IAAM,iBAAiB,GAAG,iBAAiB,CAAC;AAEnD;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,KAAc;IAC5C,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,eAAe,CAAC,iBAAqC;IACnE,OAAO,UAAU,CACf;QACE,IAAI,EAAE,iBAAiB;QACvB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,eAAe,CAAC,KAAK,CAAC,EAAtB,CAAsB;YAC1D,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,+DAA+D,EAA5E,CAA4E,EAC1F,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isVariableWidthValidator from 'validator/lib/isVariableWidth';\n\nexport const IS_VARIABLE_WIDTH = 'isVariableWidth';\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function isVariableWidth(value: unknown): boolean {\n  return typeof value === 'string' && isVariableWidthValidator(value);\n}\n\n/**\n * Checks if the string contains variable-width chars.\n * If given value is not a string, then it returns false.\n */\nexport function IsVariableWidth(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_VARIABLE_WIDTH,\n      validator: {\n        validate: (value, args): boolean => isVariableWidth(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain a full-width and half-width characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}