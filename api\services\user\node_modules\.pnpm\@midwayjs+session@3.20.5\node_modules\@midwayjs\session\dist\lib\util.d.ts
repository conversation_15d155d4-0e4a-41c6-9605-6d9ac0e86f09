/**
 * Decode the base64 cookie value to an object.
 *
 * @param {String} string
 * @return {Object}
 * @api private
 */
export declare function decode(string: any): any;
/**
 * Encode an object into a base64-encoded JSON string.
 *
 * @param {Object} body
 * @return {String}
 * @api private
 */
export declare function encode(body: any): string;
export declare function hash(sess: any): number;
export declare const COOKIE_EXP_DATE: Date;
export declare const ONE_DAY: number;
//# sourceMappingURL=util.d.ts.map