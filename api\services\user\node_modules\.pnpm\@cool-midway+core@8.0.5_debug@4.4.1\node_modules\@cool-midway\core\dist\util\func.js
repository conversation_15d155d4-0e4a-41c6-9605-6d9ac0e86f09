"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FuncUtil = void 0;
const core_1 = require("@midwayjs/core");
const moment = require("moment");
/**
 * 常用函数处理
 */
let FuncUtil = class FuncUtil {
    async init() {
        Date.prototype.toJSON = function () {
            return moment(this).format('YYYY-MM-DD HH:mm:ss');
        };
        // 新增String支持replaceAll方法
        String.prototype['replaceAll'] = function (s1, s2) {
            return this.replace(new RegExp(s1, 'gm'), s2);
        };
        this.coreLogger.info('\x1B[36m [cool:core] midwayjs cool core func handler \x1B[0m');
    }
};
exports.FuncUtil = FuncUtil;
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], FuncUtil.prototype, "coreLogger", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FuncUtil.prototype, "init", null);
exports.FuncUtil = FuncUtil = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], FuncUtil);
