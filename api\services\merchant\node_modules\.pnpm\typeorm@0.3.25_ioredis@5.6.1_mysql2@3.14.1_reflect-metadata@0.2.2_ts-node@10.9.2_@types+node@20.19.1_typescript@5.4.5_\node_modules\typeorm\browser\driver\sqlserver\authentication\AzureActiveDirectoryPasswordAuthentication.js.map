{"version": 3, "sources": ["../browser/src/driver/sqlserver/authentication/AzureActiveDirectoryPasswordAuthentication.ts"], "names": [], "mappings": "", "file": "AzureActiveDirectoryPasswordAuthentication.js", "sourcesContent": ["export interface AzureActiveDirectoryPasswordAuthentication {\n    type: \"azure-active-directory-password\"\n    options: {\n        /**\n         * A user need to provide `userName` associate to their account.\n         */\n        userName: string\n        /**\n         * A user need to provide `password` associate to their account.\n         */\n        password: string\n\n        /**\n         * Optional parameter for specific Azure tenant ID\n         */\n        domain: string\n    }\n}\n"], "sourceRoot": "../../.."}