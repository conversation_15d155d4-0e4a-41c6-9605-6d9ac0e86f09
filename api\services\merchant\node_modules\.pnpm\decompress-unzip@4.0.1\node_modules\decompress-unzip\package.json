{"name": "decompress-unzip", "version": "4.0.1", "description": "decompress zip plugin", "license": "MIT", "repository": "kevva/decompress-unzip", "author": {"name": "<PERSON>", "email": "kevin<PERSON><PERSON><PERSON>@gmail.com", "url": "https://github.com/kevva"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["decompress", "decompressplugin", "extract", "zip"], "dependencies": {"file-type": "^3.8.0", "get-stream": "^2.2.0", "pify": "^2.3.0", "yauzl": "^2.4.2"}, "devDependencies": {"ava": "*", "is-jpg": "^1.0.0", "xo": "*"}, "xo": {"esnext": true}}