<template>
  <div class="data-sync-demo">
    <div class="page-header">
      <h1>🔗 数据打通演示</h1>
      <p>展示三个页面之间的数据同步效果</p>
    </div>

    <el-row :gutter="24">
      <!-- 风险控制数据 -->
      <el-col :span="8">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon color="#f56c6c"><Warning /></el-icon>
              <span>风险控制数据</span>
            </div>
          </template>
          
          <div class="data-item">
            <label>风险等级:</label>
            <el-select v-model="riskSettings.riskLevel" size="small">
              <el-option label="低风险" value="low" />
              <el-option label="中风险" value="medium" />
              <el-option label="高风险" value="high" />
            </el-select>
          </div>
          
          <div class="data-item">
            <label>单日限额:</label>
            <el-input-number v-model="riskSettings.dailyLimit" size="small" />
          </div>
          
          <div class="data-item">
            <label>反洗钱监测:</label>
            <el-switch v-model="riskSettings.enableAML" size="small" />
          </div>
        </el-card>
      </el-col>

      <!-- 合规要求数据 -->
      <el-col :span="8">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon color="#409eff"><Document /></el-icon>
              <span>合规要求数据</span>
            </div>
          </template>
          
          <div class="data-item">
            <label>KYC等级:</label>
            <el-select v-model="complianceSettings.kycLevel" size="small">
              <el-option label="基础认证" value="basic" />
              <el-option label="标准认证" value="standard" />
              <el-option label="高级认证" value="advanced" />
            </el-select>
          </div>
          
          <div class="data-item">
            <label>税务处理:</label>
            <el-select v-model="complianceSettings.taxHandling" size="small">
              <el-option label="平台代扣" value="platform" />
              <el-option label="商户自理" value="merchant" />
            </el-select>
          </div>
          
          <div class="data-item">
            <label>数据保护:</label>
            <el-switch v-model="complianceSettings.dataProtection" size="small" />
          </div>
        </el-card>
      </el-col>

      <!-- 国际化数据 -->
      <el-col :span="8">
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon color="#67c23a"><Money /></el-icon>
              <span>国际化数据</span>
            </div>
          </template>
          
          <div class="data-item">
            <label>基础币种:</label>
            <el-select v-model="currencySettings.baseCurrency" size="small">
              <el-option label="人民币 (CNY)" value="CNY" />
              <el-option label="美元 (USD)" value="USD" />
              <el-option label="欧元 (EUR)" value="EUR" />
            </el-select>
          </div>
          
          <div class="data-item">
            <label>汇率更新:</label>
            <el-select v-model="currencySettings.exchangeRateFrequency" size="small">
              <el-option label="实时更新" value="realtime" />
              <el-option label="每日更新" value="daily" />
            </el-select>
          </div>
          
          <div class="data-item">
            <label>跨境结算:</label>
            <el-switch v-model="regionSettings.crossBorderSettlement" size="small" />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 原始数据展示 -->
    <el-card shadow="never" style="margin-top: 24px;">
      <template #header>
        <div class="card-header">
          <el-icon><DataBoard /></el-icon>
          <span>原始数据结构</span>
          <el-button type="primary" size="small" @click="saveAllSettings">
            保存所有设置
          </el-button>
        </div>
      </template>
      
      <el-collapse>
        <el-collapse-item title="完整的 settlementForm 数据" name="1">
          <pre>{{ JSON.stringify(settlementForm, null, 2) }}</pre>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 页面跳转测试 -->
    <el-card shadow="never" style="margin-top: 24px;">
      <template #header>
        <div class="card-header">
          <el-icon><Link /></el-icon>
          <span>页面跳转测试</span>
        </div>
      </template>
      
      <el-space>
        <el-button type="primary" @click="goToRiskCompliance">
          跳转到风险与合规页面
        </el-button>
        <el-button type="success" @click="goToInternationalization">
          跳转到国际化页面
        </el-button>
        <el-button type="info" @click="goToRules">
          跳转到结算规则页面
        </el-button>
      </el-space>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Warning, Document, Money, DataBoard, Link } from '@element-plus/icons-vue'
import { useSettlementStore } from '@/stores/settlement'

const router = useRouter()
const settlementStore = useSettlementStore()

// 使用共享的配置数据
const { 
  settlementForm, 
  riskSettings, 
  complianceSettings, 
  currencySettings, 
  regionSettings 
} = settlementStore

// 保存所有设置
const saveAllSettings = async () => {
  const result = await settlementStore.saveSettings()
  if (result.success) {
    ElMessage.success('所有设置已保存')
  } else {
    ElMessage.error(result.message || '保存失败')
  }
}

// 页面跳转
const goToRiskCompliance = () => {
  router.push('/settlement/risk-compliance')
}

const goToInternationalization = () => {
  router.push('/settlement/internationalization')
}

const goToRules = () => {
  router.push('/settlement/rules')
}

onMounted(async () => {
  await settlementStore.loadSettings()
})
</script>

<style scoped lang="scss">
.data-sync-demo {
  padding: 24px;
  background: var(--art-bg-gray-50);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
  
  h1 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--art-text-primary);
  }
  
  p {
    margin: 0;
    color: var(--art-text-gray-600);
    font-size: 14px;
  }
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  
  label {
    font-size: 14px;
    color: var(--art-text-gray-700);
    min-width: 80px;
  }
}

pre {
  background: var(--art-bg-gray-100);
  padding: 16px;
  border-radius: 8px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}
</style>
