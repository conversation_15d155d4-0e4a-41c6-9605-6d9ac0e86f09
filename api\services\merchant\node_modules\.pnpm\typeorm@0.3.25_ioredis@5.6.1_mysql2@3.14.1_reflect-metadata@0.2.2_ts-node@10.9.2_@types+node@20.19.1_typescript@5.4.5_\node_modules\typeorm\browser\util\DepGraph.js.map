{"version": 3, "sources": ["../browser/src/util/DepGraph.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH;;GAEG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AAEvC;;;;;;;;;GASG;AACH,SAAS,SAAS,CAAC,KAAU,EAAE,UAAe,EAAE,MAAW;IACvD,MAAM,WAAW,GAAU,EAAE,CAAA;IAC7B,MAAM,OAAO,GAAQ,EAAE,CAAA;IACvB,OAAO,SAAS,GAAG,CAAC,WAAgB;QAChC,OAAO,CAAC,WAAW,CAAC,GAAG,IAAI,CAAA;QAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC7B,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,UAAU,IAAS;YAC1C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjB,GAAG,CAAC,IAAI,CAAC,CAAA;YACb,CAAC;iBAAM,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACtB,MAAM,IAAI,YAAY,CAClB,2BAA2B,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CACxD,CAAA;YACL,CAAC;QACL,CAAC,CAAC,CAAA;QACF,WAAW,CAAC,GAAG,EAAE,CAAA;QACjB,IACI,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC;YAChD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EACpC,CAAC;YACC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC5B,CAAC;IACL,CAAC,CAAA;AACL,CAAC;AAED,MAAM,OAAO,QAAQ;IAArB;QACI,UAAK,GAAQ,EAAE,CAAA;QACf,kBAAa,GAAQ,EAAE,CAAA,CAAC,4BAA4B;QACpD,kBAAa,GAAQ,EAAE,CAAA,CAAC,2BAA2B;IA0LvD,CAAC;IAxLG;;OAEG;IACH,OAAO,CAAC,IAAS,EAAE,IAAU;QACzB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,kFAAkF;YAClF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;YAC3B,CAAC;iBAAM,CAAC;gBACJ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;YAC3B,CAAC;YACD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;YAC7B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,CAAA;QACjC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAS;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YACvB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAC9B;YAAA,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,UAC9C,QAAQ;gBAER,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,UAAU,GAAQ;oBAC5C,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;oBACvC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;wBACX,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;oBAChC,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,IAAS;QACb,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAS;QACjB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;QAC3B,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,YAAY,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAAS,EAAE,IAAS;QAC5B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;QAC3B,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,YAAY,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,IAAS,EAAE,EAAO;QAC5B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,YAAY,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,YAAY,CAAC,wBAAwB,EAAE,EAAE,CAAC,CAAA;QACxD,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACrC,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACrC,CAAC;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,IAAS,EAAE,EAAO;QAC/B,IAAI,GAAQ,CAAA;QACZ,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;YAC1C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YAC3C,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC;YACnB,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC1C,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;gBACX,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YACzC,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;;;;OAOG;IACH,cAAc,CAAC,IAAS,EAAE,UAAe;QACrC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,MAAM,GAAU,EAAE,CAAA;YACxB,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;YAC7D,GAAG,CAAC,IAAI,CAAC,CAAA;YACT,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YACzB,CAAC;YACD,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,YAAY,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,YAAY,CAAC,IAAS,EAAE,UAAe;QACnC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,MAAM,MAAM,GAAU,EAAE,CAAA;YACxB,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;YAC7D,GAAG,CAAC,IAAI,CAAC,CAAA;YACT,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAChC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC;gBACX,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;YACzB,CAAC;YACD,OAAO,MAAM,CAAA;QACjB,CAAC;aAAM,CAAC;YACJ,MAAM,IAAI,YAAY,CAAC,wBAAwB,IAAI,EAAE,CAAC,CAAA;QAC1D,CAAC;IACL,CAAC;IAED;;;;;;OAMG;IACH,YAAY,CAAC,UAAgB;QACzB,MAAM,IAAI,GAAG,IAAI,CAAA;QACjB,MAAM,MAAM,GAAU,EAAE,CAAA;QACxB,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACpB,OAAO,MAAM,CAAA,CAAC,cAAc;QAChC,CAAC;aAAM,CAAC;YACJ,2EAA2E;YAC3E,mEAAmE;YACnE,MAAM,QAAQ,GAAG,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE,CAAC,CAAA;YACzD,IAAI,CAAC,OAAO,CAAC,UAAU,CAAM;gBACzB,QAAQ,CAAC,CAAC,CAAC,CAAA;YACf,CAAC,CAAC,CAAA;YAEF,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,MAAM,CAAC,CAAA;YAC7D,+EAA+E;YAC/E,sDAAsD;YACtD,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI;gBACtB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAA;YAChD,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;gBAClB,GAAG,CAAC,CAAC,CAAC,CAAA;YACV,CAAC,CAAC,CAAA;YAEF,OAAO,MAAM,CAAA;QACjB,CAAC;IACL,CAAC;CACJ", "file": "DepGraph.js", "sourcesContent": ["/**\n * This source code is from https://github.com/jriecken/dependency-graph\n * Just added \"any\" types here, wrapper everything into exported class.\n * We cant use a package itself because we want to package \"everything-in-it\" for the frontend users of TypeORM.\n */\n\n/**\n * A simple dependency graph\n */\n\nimport { TypeORMError } from \"../error\"\n\n/**\n * Helper for creating a Depth-First-Search on\n * a set of edges.\n *\n * Detects cycles and throws an Error if one is detected.\n *\n * @param edges The set of edges to DFS through\n * @param leavesOnly Whether to only return \"leaf\" nodes (ones who have no edges)\n * @param result An array in which the results will be populated\n */\nfunction createDFS(edges: any, leavesOnly: any, result: any) {\n    const currentPath: any[] = []\n    const visited: any = {}\n    return function DFS(currentNode: any) {\n        visited[currentNode] = true\n        currentPath.push(currentNode)\n        edges[currentNode].forEach(function (node: any) {\n            if (!visited[node]) {\n                DFS(node)\n            } else if (currentPath.indexOf(node) >= 0) {\n                currentPath.push(node)\n                throw new TypeORMError(\n                    `Dependency Cycle Found: ${currentPath.join(\" -> \")}`,\n                )\n            }\n        })\n        currentPath.pop()\n        if (\n            (!leavesOnly || edges[currentNode].length === 0) &&\n            result.indexOf(currentNode) === -1\n        ) {\n            result.push(currentNode)\n        }\n    }\n}\n\nexport class DepGraph {\n    nodes: any = {}\n    outgoingEdges: any = {} // Node -> [Dependency Node]\n    incomingEdges: any = {} // Node -> [Dependant Node]\n\n    /**\n     * Add a node to the dependency graph. If a node already exists, this method will do nothing.\n     */\n    addNode(node: any, data?: any) {\n        if (!this.hasNode(node)) {\n            // Checking the arguments length allows the user to add a node with undefined data\n            if (arguments.length === 2) {\n                this.nodes[node] = data\n            } else {\n                this.nodes[node] = node\n            }\n            this.outgoingEdges[node] = []\n            this.incomingEdges[node] = []\n        }\n    }\n\n    /**\n     * Remove a node from the dependency graph. If a node does not exist, this method will do nothing.\n     */\n    removeNode(node: any) {\n        if (this.hasNode(node)) {\n            delete this.nodes[node]\n            delete this.outgoingEdges[node]\n            delete this.incomingEdges[node]\n            ;[this.incomingEdges, this.outgoingEdges].forEach(function (\n                edgeList,\n            ) {\n                Object.keys(edgeList).forEach(function (key: any) {\n                    const idx = edgeList[key].indexOf(node)\n                    if (idx >= 0) {\n                        edgeList[key].splice(idx, 1)\n                    }\n                })\n            })\n        }\n    }\n\n    /**\n     * Check if a node exists in the graph\n     */\n    hasNode(node: any) {\n        return this.nodes.hasOwnProperty(node)\n    }\n\n    /**\n     * Get the data associated with a node name\n     */\n    getNodeData(node: any) {\n        if (this.hasNode(node)) {\n            return this.nodes[node]\n        } else {\n            throw new TypeORMError(`Node does not exist: ${node}`)\n        }\n    }\n\n    /**\n     * Set the associated data for a given node name. If the node does not exist, this method will throw an error\n     */\n    setNodeData(node: any, data: any) {\n        if (this.hasNode(node)) {\n            this.nodes[node] = data\n        } else {\n            throw new TypeORMError(`Node does not exist: ${node}`)\n        }\n    }\n\n    /**\n     * Add a dependency between two nodes. If either of the nodes does not exist,\n     * an Error will be thrown.\n     */\n    addDependency(from: any, to: any) {\n        if (!this.hasNode(from)) {\n            throw new TypeORMError(`Node does not exist: ${from}`)\n        }\n        if (!this.hasNode(to)) {\n            throw new TypeORMError(`Node does not exist: ${to}`)\n        }\n        if (this.outgoingEdges[from].indexOf(to) === -1) {\n            this.outgoingEdges[from].push(to)\n        }\n        if (this.incomingEdges[to].indexOf(from) === -1) {\n            this.incomingEdges[to].push(from)\n        }\n        return true\n    }\n\n    /**\n     * Remove a dependency between two nodes.\n     */\n    removeDependency(from: any, to: any) {\n        let idx: any\n        if (this.hasNode(from)) {\n            idx = this.outgoingEdges[from].indexOf(to)\n            if (idx >= 0) {\n                this.outgoingEdges[from].splice(idx, 1)\n            }\n        }\n\n        if (this.hasNode(to)) {\n            idx = this.incomingEdges[to].indexOf(from)\n            if (idx >= 0) {\n                this.incomingEdges[to].splice(idx, 1)\n            }\n        }\n    }\n\n    /**\n     * Get an array containing the nodes that the specified node depends on (transitively).\n     *\n     * Throws an Error if the graph has a cycle, or the specified node does not exist.\n     *\n     * If `leavesOnly` is true, only nodes that do not depend on any other nodes will be returned\n     * in the array.\n     */\n    dependenciesOf(node: any, leavesOnly: any) {\n        if (this.hasNode(node)) {\n            const result: any[] = []\n            const DFS = createDFS(this.outgoingEdges, leavesOnly, result)\n            DFS(node)\n            const idx = result.indexOf(node)\n            if (idx >= 0) {\n                result.splice(idx, 1)\n            }\n            return result\n        } else {\n            throw new TypeORMError(`Node does not exist: ${node}`)\n        }\n    }\n\n    /**\n     * get an array containing the nodes that depend on the specified node (transitively).\n     *\n     * Throws an Error if the graph has a cycle, or the specified node does not exist.\n     *\n     * If `leavesOnly` is true, only nodes that do not have any dependants will be returned in the array.\n     */\n    dependantsOf(node: any, leavesOnly: any) {\n        if (this.hasNode(node)) {\n            const result: any[] = []\n            const DFS = createDFS(this.incomingEdges, leavesOnly, result)\n            DFS(node)\n            const idx = result.indexOf(node)\n            if (idx >= 0) {\n                result.splice(idx, 1)\n            }\n            return result\n        } else {\n            throw new TypeORMError(`Node does not exist: ${node}`)\n        }\n    }\n\n    /**\n     * Construct the overall processing order for the dependency graph.\n     *\n     * Throws an Error if the graph has a cycle.\n     *\n     * If `leavesOnly` is true, only nodes that do not depend on any other nodes will be returned.\n     */\n    overallOrder(leavesOnly?: any) {\n        const self = this\n        const result: any[] = []\n        const keys = Object.keys(this.nodes)\n        if (keys.length === 0) {\n            return result // Empty graph\n        } else {\n            // Look for cycles - we run the DFS starting at all the nodes in case there\n            // are several disconnected subgraphs inside this dependency graph.\n            const CycleDFS = createDFS(this.outgoingEdges, false, [])\n            keys.forEach(function (n: any) {\n                CycleDFS(n)\n            })\n\n            const DFS = createDFS(this.outgoingEdges, leavesOnly, result)\n            // Find all potential starting points (nodes with nothing depending on them) an\n            // run a DFS starting at these points to get the order\n            keys.filter(function (node) {\n                return self.incomingEdges[node].length === 0\n            }).forEach(function (n) {\n                DFS(n)\n            })\n\n            return result\n        }\n    }\n}\n"], "sourceRoot": ".."}