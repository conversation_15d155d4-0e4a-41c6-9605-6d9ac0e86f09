"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBorderCharacters = exports.createStream = exports.table = void 0;
const createStream_1 = require("./createStream");
Object.defineProperty(exports, "createStream", { enumerable: true, get: function () { return createStream_1.createStream; } });
const getBorderCharacters_1 = require("./getBorderCharacters");
Object.defineProperty(exports, "getBorderCharacters", { enumerable: true, get: function () { return getBorderCharacters_1.getBorderCharacters; } });
const table_1 = require("./table");
Object.defineProperty(exports, "table", { enumerable: true, get: function () { return table_1.table; } });
__exportStar(require("./types/api"), exports);
//# sourceMappingURL=index.js.map