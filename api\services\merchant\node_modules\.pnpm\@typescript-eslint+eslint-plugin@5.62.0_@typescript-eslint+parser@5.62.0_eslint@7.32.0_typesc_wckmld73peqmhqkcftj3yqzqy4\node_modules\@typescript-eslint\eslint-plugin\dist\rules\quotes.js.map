{"version": 3, "file": "quotes.js", "sourceRoot": "", "sources": ["../../src/rules/quotes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,QAAQ,CAAC,CAAC;AAK7C,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,QAAQ;IACd,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,+FAA+F;QAC/F,QAAQ,EAAE,MAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,mCAAI;YAClC,WAAW,EAAE,mCAAmC;SACjD;QACD,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;KAC7B;IACD,cAAc,EAAE;QACd,QAAQ;QACR;YACE,qBAAqB,EAAE,KAAK;YAC5B,WAAW,EAAE,KAAK;SACnB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,sBAAsB,CAAC,IAAsB;YACpD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAE3B,QAAQ,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,IAAI,EAAE;gBACpB,KAAK,sBAAc,CAAC,0BAA0B,CAAC;gBAC/C,KAAK,sBAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,sBAAc,CAAC,yBAAyB;oBAC3C,OAAO,IAAI,CAAC;gBAEd,KAAK,sBAAc,CAAC,YAAY;oBAC9B,OAAO,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC;gBAE5B,KAAK,sBAAc,CAAC,4BAA4B,CAAC;gBACjD,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC;gBAE7B;oBACE,OAAO,KAAK,CAAC;aAChB;QACH,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,IAAI,MAAM,KAAK,UAAU,IAAI,sBAAsB,CAAC,IAAI,CAAC,EAAE;oBACzD,OAAO;iBACR;gBAED,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;YAED,eAAe,CAAC,IAAI;gBAClB,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}