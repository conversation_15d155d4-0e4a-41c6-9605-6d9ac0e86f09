/// <reference types="node" />
export declare class CoreBaseCLI {
    argv: any;
    providerName: string;
    core: any;
    spec: any;
    specFile: any;
    commands: string[];
    cwd: string;
    constructor(argv: any);
    initCore(): void;
    loadPlugins(): Promise<void>;
    loadCorePlugin(): void;
    loadDefaultPlugin(): void;
    loadPlatformPlugin(): void;
    loadExtensions(): {};
    coverCoreOptions(): {};
    loadLog(): {
        error: (err: any) => void;
        Console: NodeJS.ConsoleConstructor;
        assert(value: any, message?: string, ...optionalParams: any[]): void;
        clear(): void;
        count(label?: string): void;
        countReset(label?: string): void;
        debug(message?: any, ...optionalParams: any[]): void;
        dir(obj: any, options?: NodeJS.InspectOptions): void;
        dirxml(...data: any[]): void;
        group(...label: any[]): void;
        groupCollapsed(): void;
        groupEnd(): void;
        info(message?: any, ...optionalParams: any[]): void;
        log(message?: any, ...optionalParams: any[]): void;
        table(tabularData: any, properties?: readonly string[]): void;
        time(label?: string): void;
        timeEnd(label?: string): void;
        timeLog(label?: string, ...data: any[]): void;
        trace(message?: any, ...optionalParams: any[]): void;
        warn(message?: any, ...optionalParams: any[]): void;
        markTimeline(label?: string): void;
        profile(label?: string): void;
        profileEnd(label?: string): void;
        timeStamp(label?: string): void;
        timeline(label?: string): void;
        timelineEnd(label?: string): void;
    };
    getUsageInfo(commandsArray: any[], usage: any, coreInstance: any, commandInfo?: any): any;
    displayUsage(commandsArray: any, usage: any, coreInstance: any, commandInfo?: any): void;
    error(err: any): void;
    loadRelativePlugin(dirPath: any, path: any): boolean;
    start(): Promise<void>;
}
//# sourceMappingURL=cli.d.ts.map