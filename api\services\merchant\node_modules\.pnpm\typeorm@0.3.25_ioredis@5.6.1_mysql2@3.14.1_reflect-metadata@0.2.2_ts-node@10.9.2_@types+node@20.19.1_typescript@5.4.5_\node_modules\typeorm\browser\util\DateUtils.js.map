{"version": 3, "sources": ["../browser/src/util/DateUtils.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,MAAM,OAAO,CAAA;AAEzB;;GAEG;AACH,MAAM,OAAO,SAAS;IAClB,4EAA4E;IAC5E,wBAAwB;IACxB,4EAA4E;IAE5E;;OAEG;IACH,MAAM,CAAC,qBAAqB,CACxB,SAAoC;QAEpC,IAAI,CAAC,SAAS;YAAE,OAAO,SAAS,CAAA;QAEhC,OAAO,OAAO,SAAS,KAAK,QAAQ;YAChC,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;YACrB,CAAC,CAAE,SAAkB,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,KAAoB;QAC7C,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;YACxB,OAAO,CACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBAChD,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC9C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAC5C,CAAA;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAClB,SAAwB,EACxB,QAAiB,KAAK,EACtB,eAAe,GAAG,IAAI;QAEtB;;;;;;;;;;;;;WAaG;QACH,IAAI,IAAI,GACJ,OAAO,SAAS,KAAK,QAAQ;YACzB,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,MAAM,EAAE;YAC3B,CAAC,CAAC,SAAS,CAAA;QAEnB,IAAI,KAAK;YACL,IAAI,GAAG,IAAI,IAAI,CACX,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,UAAU,EAAE,EACjB,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,kBAAkB,EAAE,CAC5B,CAAA;QAEL,IAAI,CAAC,eAAe;YAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAA;QAEhD,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CACxB,KAAiB,EACjB,cAAuB,KAAK;QAE5B,IAAI,KAAK,YAAY,IAAI;YACrB,OAAO,CACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAC1C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC5C,CAAC,CAAC,WAAW;oBACT,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;oBACpD,CAAC,CAAC,EAAE,CAAC,CACZ,CAAA;QAEL,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,KAAiB;QACpC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAClD,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;YACvB,IAAI,KAAK;gBAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;YACzC,IAAI,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;YAC/C,IAAI,OAAO;gBAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;YAC/C,OAAO,IAAI,CAAA;QACf,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CACpB,KAAmB,EACnB,cAAuB,KAAK;QAE5B,KAAK;YACD,KAAK,YAAY,IAAI;gBACjB,CAAC,CAAC,KAAK,CAAC,QAAQ,EAAE;oBAChB,GAAG;oBACH,KAAK,CAAC,UAAU,EAAE;oBAClB,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBAChD,CAAC,CAAC,KAAK,CAAA;QACf,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,OAAO,KAAK;iBACP,KAAK,CAAC,GAAG,CAAC;iBACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,+DAA+D;iBAC1G,IAAI,CAAC,GAAG,CAAC,CAAA;QAClB,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB,CAC5B,KAAiB,EACjB,eAAyB;QAEzB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;QACD,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;YACxB,IAAI,UAAU,GACV,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;gBAChD,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC9C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;gBACzC,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;gBAC1C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC5C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC,CAAA;YAEhD,IAAI,eAAe;gBACf,UAAU,IAAI,IAAI,IAAI,CAAC,kBAAkB,CACrC,KAAK,CAAC,eAAe,EAAE,CAC1B,EAAE,CAAA;YAEP,KAAK,GAAG,UAAU,CAAA;QACtB,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CAAC,KAAiB;QACjD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;QACD,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;YACxB,OAAO,CACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;gBACnD,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;gBACjD,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,UAAU,EAAE,CAAC;gBAC5C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC7C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBAC/C,GAAG;gBACH,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC;gBAC/C,GAAG;gBACH,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,kBAAkB,EAAE,CAAC,CACtD,CAAA;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,KAAkB;QACzC,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvB,OAAQ,KAAe,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC3D,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,mBAAmB,CAAC,KAAmB;QAC1C,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC5B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;YAC3B,CAAC;iBAAM,CAAC;gBACJ,OAAO,EAAE,CAAA;YACb,CAAC;QACL,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAU;QAChC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;IAChC,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAU;QAChC,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;IAChE,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAU;QAChC,OAAO,EAAE,GAAG,KAAK,CAAA;IACrB,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAU,EAAE,cAA8B;QAChE,IACI,cAAc,CAAC,IAAI;YACnB,CAAC,KAAK,CAAC,KAAK,CAAC;YACb,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EACnD,CAAC;YACC,4DAA4D;YAC5D,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC3B,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED,4EAA4E;IAC5E,yBAAyB;IACzB,4EAA4E;IAE5E;;OAEG;IACK,MAAM,CAAC,mBAAmB,CAAC,KAAa,EAAE,WAAW,GAAG,CAAC;QAC7D,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAAA;QAEnC,OAAO,MAAM,CAAC,GAAG,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACK,MAAM,CAAC,kBAAkB,CAAC,KAAa;QAC3C,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;YACb,OAAO,IAAI,GAAG,KAAK,CAAA;QACvB,CAAC;aAAM,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC;YACrB,OAAO,GAAG,GAAG,KAAK,CAAA;QACtB,CAAC;aAAM,CAAC;YACJ,OAAO,MAAM,CAAC,KAAK,CAAC,CAAA;QACxB,CAAC;IACL,CAAC;CACJ", "file": "DateUtils.js", "sourcesContent": ["import { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport dayjs from \"dayjs\"\n\n/**\n * Provides utilities to transform hydrated and persisted data.\n */\nexport class DateUtils {\n    // -------------------------------------------------------------------------\n    // Public Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Normalizes date object hydrated from the database.\n     */\n    static normalizeHydratedDate(\n        mixedDate: Date | string | undefined,\n    ): Date | string | undefined {\n        if (!mixedDate) return mixedDate\n\n        return typeof mixedDate === \"string\"\n            ? new Date(mixedDate)\n            : (mixedDate as Date)\n    }\n\n    /**\n     * Converts given value into date string in a \"YYYY-MM-DD\" format.\n     */\n    static mixedDateToDateString(value: string | Date): string {\n        if (value instanceof Date) {\n            return (\n                this.formatZerolessValue(value.getFullYear(), 4) +\n                \"-\" +\n                this.formatZerolessValue(value.getMonth() + 1) +\n                \"-\" +\n                this.formatZerolessValue(value.getDate())\n            )\n        }\n\n        return value\n    }\n\n    /**\n     * Converts given value into date object.\n     */\n    static mixedDateToDate(\n        mixedDate: Date | string,\n        toUtc: boolean = false,\n        useMilliseconds = true,\n    ): Date {\n        /**\n         * new Date(ISOString) is not a reliable parser to date strings.\n         * It's better to use 'date-fns' parser to parser string in ISO Format.\n         *\n         * The problem here is with wrong timezone.\n         *\n         * For example:\n         *\n         * ``new Date('2021-04-28')`` will generate `2021-04-28T00:00:00.000Z`\n         * in my timezone, which is not true for my timezone (GMT-0300). It should\n         * be `2021-04-28T03:00:00.000Z` as `new Date(2021, 3, 28)` generates.\n         *\n         * https://stackoverflow.com/a/2587398\n         */\n        let date =\n            typeof mixedDate === \"string\"\n                ? dayjs(mixedDate).toDate()\n                : mixedDate\n\n        if (toUtc)\n            date = new Date(\n                date.getUTCFullYear(),\n                date.getUTCMonth(),\n                date.getUTCDate(),\n                date.getUTCHours(),\n                date.getUTCMinutes(),\n                date.getUTCSeconds(),\n                date.getUTCMilliseconds(),\n            )\n\n        if (!useMilliseconds) date.setUTCMilliseconds(0)\n\n        return date\n    }\n\n    /**\n     * Converts given value into time string in a \"HH:mm:ss\" format.\n     */\n    static mixedDateToTimeString(\n        value: Date | any,\n        skipSeconds: boolean = false,\n    ): string | any {\n        if (value instanceof Date)\n            return (\n                this.formatZerolessValue(value.getHours()) +\n                \":\" +\n                this.formatZerolessValue(value.getMinutes()) +\n                (!skipSeconds\n                    ? \":\" + this.formatZerolessValue(value.getSeconds())\n                    : \"\")\n            )\n\n        return value\n    }\n\n    /**\n     * Converts given value into time string in a \"HH:mm:ss\" format.\n     */\n    static mixedTimeToDate(value: Date | any): string | any {\n        if (typeof value === \"string\") {\n            const [hours, minutes, seconds] = value.split(\":\")\n            const date = new Date()\n            if (hours) date.setHours(parseInt(hours))\n            if (minutes) date.setMinutes(parseInt(minutes))\n            if (seconds) date.setSeconds(parseInt(seconds))\n            return date\n        }\n\n        return value\n    }\n\n    /**\n     * Converts given string value with \"-\" separator into a \"HH:mm:ss\" format.\n     */\n    static mixedTimeToString(\n        value: string | any,\n        skipSeconds: boolean = false,\n    ): string | any {\n        value =\n            value instanceof Date\n                ? value.getHours() +\n                  \":\" +\n                  value.getMinutes() +\n                  (!skipSeconds ? \":\" + value.getSeconds() : \"\")\n                : value\n        if (typeof value === \"string\") {\n            return value\n                .split(\":\")\n                .map((v) => (v.length === 1 ? \"0\" + v : v)) // append zero at beginning if we have a first-zero-less number\n                .join(\":\")\n        }\n\n        return value\n    }\n\n    /**\n     * Converts given value into datetime string in a \"YYYY-MM-DD HH-mm-ss\" format.\n     */\n    static mixedDateToDatetimeString(\n        value: Date | any,\n        useMilliseconds?: boolean,\n    ): string | any {\n        if (typeof value === \"string\") {\n            value = new Date(value)\n        }\n        if (value instanceof Date) {\n            let finalValue =\n                this.formatZerolessValue(value.getFullYear(), 4) +\n                \"-\" +\n                this.formatZerolessValue(value.getMonth() + 1) +\n                \"-\" +\n                this.formatZerolessValue(value.getDate()) +\n                \" \" +\n                this.formatZerolessValue(value.getHours()) +\n                \":\" +\n                this.formatZerolessValue(value.getMinutes()) +\n                \":\" +\n                this.formatZerolessValue(value.getSeconds())\n\n            if (useMilliseconds)\n                finalValue += `.${this.formatMilliseconds(\n                    value.getMilliseconds(),\n                )}`\n\n            value = finalValue\n        }\n\n        return value\n    }\n\n    /**\n     * Converts given value into utc datetime string in a \"YYYY-MM-DD HH-mm-ss.sss\" format.\n     */\n    static mixedDateToUtcDatetimeString(value: Date | any): string | any {\n        if (typeof value === \"string\") {\n            value = new Date(value)\n        }\n        if (value instanceof Date) {\n            return (\n                this.formatZerolessValue(value.getUTCFullYear(), 4) +\n                \"-\" +\n                this.formatZerolessValue(value.getUTCMonth() + 1) +\n                \"-\" +\n                this.formatZerolessValue(value.getUTCDate()) +\n                \" \" +\n                this.formatZerolessValue(value.getUTCHours()) +\n                \":\" +\n                this.formatZerolessValue(value.getUTCMinutes()) +\n                \":\" +\n                this.formatZerolessValue(value.getUTCSeconds()) +\n                \".\" +\n                this.formatMilliseconds(value.getUTCMilliseconds())\n            )\n        }\n\n        return value\n    }\n\n    /**\n     * Converts each item in the given array to string joined by \",\" separator.\n     */\n    static simpleArrayToString(value: any[] | any): string[] | any {\n        if (Array.isArray(value)) {\n            return (value as any[]).map((i) => String(i)).join(\",\")\n        }\n\n        return value\n    }\n\n    /**\n     * Converts given string to simple array split by \",\" separator.\n     */\n    static stringToSimpleArray(value: string | any): string | any {\n        if (typeof value === \"string\") {\n            if (value.length > 0) {\n                return value.split(\",\")\n            } else {\n                return []\n            }\n        }\n\n        return value\n    }\n\n    static simpleJsonToString(value: any): string {\n        return JSON.stringify(value)\n    }\n\n    static stringToSimpleJson(value: any) {\n        return typeof value === \"string\" ? JSON.parse(value) : value\n    }\n\n    static simpleEnumToString(value: any) {\n        return \"\" + value\n    }\n\n    static stringToSimpleEnum(value: any, columnMetadata: ColumnMetadata) {\n        if (\n            columnMetadata.enum &&\n            !isNaN(value) &&\n            columnMetadata.enum.indexOf(parseInt(value)) >= 0\n        ) {\n            // convert to number if that exists in poosible enum options\n            value = parseInt(value)\n        }\n\n        return value\n    }\n\n    // -------------------------------------------------------------------------\n    // Private Static Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Formats given number to \"0x\" format, e.g. if the totalLength = 2 and the value is 1 then it will return \"01\".\n     */\n    private static formatZerolessValue(value: number, totalLength = 2): string {\n        const pad = \"0\".repeat(totalLength)\n\n        return String(`${pad}${value}`).slice(-totalLength)\n    }\n\n    /**\n     * Formats given number to \"0x\" format, e.g. if it is 1 then it will return \"01\".\n     */\n    private static formatMilliseconds(value: number): string {\n        if (value < 10) {\n            return \"00\" + value\n        } else if (value < 100) {\n            return \"0\" + value\n        } else {\n            return String(value)\n        }\n    }\n}\n"], "sourceRoot": ".."}