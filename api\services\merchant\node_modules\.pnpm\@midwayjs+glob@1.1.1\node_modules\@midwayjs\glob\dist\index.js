"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.run = void 0;
const fs_1 = require("fs");
const path_1 = require("path");
const pm = require("picomatch");
const util_1 = require("util");
const os = require("os");
const log = (0, util_1.debuglog)('midway:glob');
function formatWindowsPath(paths) {
    if (os.platform() === 'win32' && paths) {
        return paths.map(p => p.split(path_1.sep).join(path_1.posix.sep));
    }
    return paths;
}
const run = (pattern, options = { cwd: process.cwd(), ignore: [] }) => {
    log(`midway glob pattern = ${pattern}, options = ${JSON.stringify(options)}`);
    const startTime = Date.now();
    const entryDir = options.cwd;
    pattern = formatWindowsPath(pattern) || [];
    log(`after format pattern = ${pattern}`);
    const isMatch = pm(pattern, {
        ignore: formatWindowsPath(options.ignore) || []
    });
    const ignoreMatch = pm('**', {
        ignore: formatWindowsPath(options.ignore) || []
    });
    function globDirectory(dirname, isMatch, ignoreDirMatch, options) {
        if (!(0, fs_1.existsSync)(dirname)) {
            return [];
        }
        const list = (0, fs_1.readdirSync)(dirname);
        const result = [];
        for (let file of list) {
            const resolvePath = (0, path_1.resolve)(dirname, file);
            log(`resolvePath = ${resolvePath}`);
            const fileStat = (0, fs_1.statSync)(resolvePath);
            if (fileStat.isDirectory() && ignoreDirMatch(resolvePath.replace(entryDir, ''))) {
                const childs = globDirectory(resolvePath, isMatch, ignoreDirMatch, options);
                result.push(...childs);
            }
            else if (fileStat.isFile() && isMatch(resolvePath.replace(entryDir, ''))) {
                result.push(resolvePath);
            }
        }
        return result;
    }
    const result = globDirectory(entryDir, isMatch, ignoreMatch, options);
    log(`midway glob timing ${Date.now() - startTime}ms`);
    return result;
};
exports.run = run;
//# sourceMappingURL=data:application/json;base64,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