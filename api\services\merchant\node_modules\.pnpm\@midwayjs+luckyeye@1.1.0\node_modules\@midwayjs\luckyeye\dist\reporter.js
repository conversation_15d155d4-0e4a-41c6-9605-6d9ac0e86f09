"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConsoleReporter = exports.Reporter = void 0;
const util_1 = require("./util");
const ms = require("ms");
const indents = 0;
let n = 0;
function indent() {
    return Array(indents).join('  ');
}
class Reporter {
    reportStart() {
    }
    reportEnd() {
    }
    reportGroup(data) {
    }
    reportInfo(data) {
    }
    reportCheck(data) {
    }
    reportError(data) {
    }
    reportSkip(data) {
    }
    reportWarn(data) {
    }
}
exports.Reporter = Reporter;
class ConsoleReporter extends Reporter {
    constructor() {
        super(...arguments);
        this.passes = 0;
        this.failures = 0;
        this.pending = 0;
        this.warning = 0;
        this.startTime = Date.now();
    }
    reportEnd() {
        console.log();
        // passes
        let fmt = util_1.color('bright pass', ' ') +
            util_1.color('green', ' %d passing') +
            util_1.color('light', ' (%s)');
        console.log(fmt, this.passes, ms(Date.now() - this.startTime));
        // pending
        if (this.pending) {
            fmt = util_1.color('pending', ' ') +
                util_1.color('pending', ' %d pending');
            console.log(fmt, this.pending);
        }
        if (this.warning) {
            fmt = util_1.color('warn', ' ') +
                util_1.color('warn', ' %d warning');
            console.log(fmt, this.warning);
        }
        // failures
        if (this.failures) {
            fmt = util_1.color('fail', '  %d failing');
            console.log(fmt, this.failures);
        }
        console.log();
    }
    reportGroup(data) {
        console.log();
        console.log(util_1.color('suite', '  %s%s'), indent(), data.group);
        console.log();
    }
    reportInfo(data) {
        this.output(data);
    }
    reportCheck(data) {
        this.output(data);
    }
    reportWarn(data) {
        this.output(data);
    }
    output(test) {
        if (test.type === 'check' && !test.message) {
            this.failures++;
            console.log(indent() + util_1.color('fail', '  %d) %s => (%s)'), ++n, test.title, test.result || 'false');
        }
        else {
            if (test.type === 'check') {
                this.passes++;
            }
            if (test.type === 'warn') {
                if (test.message) {
                    this.warning++;
                }
                else {
                    this.passes++;
                }
            }
            let fmt;
            let symbol, checkmarkColor, passColor;
            switch (test.type) {
                case 'info':
                    symbol = util_1.symbols.info;
                    checkmarkColor = 'info';
                    passColor = 'info';
                    break;
                case 'warn':
                    symbol = test.message ? util_1.symbols.warn : util_1.symbols.ok;
                    checkmarkColor = test.message ? 'warn' : 'checkmark';
                    passColor = test.message ? 'warn' : 'pass';
                    break;
                default:
                    symbol = util_1.symbols.ok;
                    checkmarkColor = 'checkmark';
                    passColor = 'pass';
            }
            if (test.speed === 'fast') {
                fmt = indent() +
                    util_1.color(checkmarkColor, '  ' + symbol) +
                    util_1.color(passColor, ' %s');
                if (test.type === 'warn' && test.message) {
                    console.log(fmt, `${test.title} => ${test.result || '?'}`);
                }
                else {
                    console.log(fmt, `${test.title} => ${test.message || '?'}`);
                }
            }
            else {
                fmt = indent() +
                    util_1.color(checkmarkColor, '  ' + symbol) +
                    util_1.color(passColor, ' %s') +
                    util_1.color(test.speed, ' (%dms)');
                if (test.type === 'warn' && test.message) {
                    console.log(fmt, `${test.title} => ${test.result || '?'}`, test.duration);
                }
                else {
                    console.log(fmt, `${test.title} => ${test.message || '?'}`, test.duration);
                }
            }
        }
    }
    reportError(data) {
        console.log(data.message);
    }
    reportSkip(data) {
        this.pending++;
        let fmt = indent() + util_1.color('pending', '  - %s');
        console.log(fmt, data.title);
    }
}
exports.ConsoleReporter = ConsoleReporter;
//# sourceMappingURL=reporter.js.map