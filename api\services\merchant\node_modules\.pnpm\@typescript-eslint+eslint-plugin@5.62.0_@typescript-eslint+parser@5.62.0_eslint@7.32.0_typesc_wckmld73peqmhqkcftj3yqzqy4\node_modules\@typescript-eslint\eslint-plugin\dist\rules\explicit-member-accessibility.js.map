{"version": 3, "file": "explicit-member-accessibility.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-member-accessibility.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,8CAAgC;AA0BhC,MAAM,kBAAkB,GAAG;IACzB,KAAK,EAAE;QACL;YACE,KAAK,EAAE,UAAU;YACjB,WAAW,EAAE,6BAA6B;SAC3C;QACD;YACE,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,yCAAyC;SACvD;QACD;YACE,KAAK,EAAE,KAAK;YACZ,WAAW,EAAE,2CAA2C;SACzD;KACF;CACF,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,0EAA0E;YAC5E,oCAAoC;YACpC,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,sDAAsD;YACxD,2BAA2B,EACzB,qDAAqD;YACvD,wBAAwB,EAAE,yCAAyC;SACpE;QACD,MAAM,EAAE;YACN,KAAK,EAAE;gBACL,kBAAkB;aACnB;YACD,WAAW,EAAE;gBACX;oBACE,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,aAAa,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;wBACrD,SAAS,EAAE;4BACT,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE;gCACV,SAAS,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gCACjD,YAAY,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gCACpD,OAAO,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gCAC/C,UAAU,EAAE,EAAE,IAAI,EAAE,4BAA4B,EAAE;gCAClD,mBAAmB,EAAE;oCACnB,IAAI,EAAE,4BAA4B;iCACnC;6BACF;4BAED,oBAAoB,EAAE,KAAK;yBAC5B;wBACD,kBAAkB,EAAE;4BAClB,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE;gCACL,IAAI,EAAE,QAAQ;6BACf;yBACF;qBACF;oBACD,oBAAoB,EAAE,KAAK;iBAC5B;aACF;YACD,IAAI,EAAE,OAAO;SACd;KACF;IACD,cAAc,EAAE,CAAC,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;IAC/C,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAuB,MAAA,MAAM,CAAC,aAAa,mCAAI,UAAU,CAAC;QACzE,MAAM,SAAS,GAAG,MAAA,MAAM,CAAC,SAAS,mCAAI,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,MAAA,SAAS,CAAC,YAAY,mCAAI,SAAS,CAAC;QACtD,MAAM,aAAa,GAAG,MAAA,SAAS,CAAC,SAAS,mCAAI,SAAS,CAAC;QACvD,MAAM,WAAW,GAAG,MAAA,SAAS,CAAC,OAAO,mCAAI,SAAS,CAAC;QACnD,MAAM,SAAS,GAAG,MAAA,SAAS,CAAC,UAAU,mCAAI,SAAS,CAAC;QACpD,MAAM,cAAc,GAAG,MAAA,SAAS,CAAC,mBAAmB,mCAAI,SAAS,CAAC;QAClE,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC,MAAA,MAAM,CAAC,kBAAkB,mCAAI,EAAE,CAAC,CAAC;QAEpE;;;WAGG;QACH,SAAS,gCAAgC,CACvC,gBAA2C;YAE3C,IAAI,gBAAgB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBAClE,OAAO;aACR;YAED,IAAI,QAAQ,GAAG,mBAAmB,CAAC;YACnC,IAAI,KAAK,GAAG,SAAS,CAAC;YACtB,QAAQ,gBAAgB,CAAC,IAAI,EAAE;gBAC7B,KAAK,QAAQ;oBACX,KAAK,GAAG,WAAW,CAAC;oBACpB,MAAM;gBACR,KAAK,aAAa;oBAChB,KAAK,GAAG,SAAS,CAAC;oBAClB,MAAM;gBACR,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK;oBACR,KAAK,GAAG,aAAa,CAAC;oBACtB,QAAQ,GAAG,GAAG,gBAAgB,CAAC,IAAI,oBAAoB,CAAC;oBACxD,MAAM;aACT;YAED,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC,iBAAiB,CACjD,gBAAgB,EAChB,UAAU,CACX,CAAC;YAEF,IAAI,KAAK,KAAK,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gBACzD,OAAO;aACR;YAED,IACE,KAAK,KAAK,WAAW;gBACrB,gBAAgB,CAAC,aAAa,KAAK,QAAQ,EAC3C;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,6BAA6B;oBACxC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,UAAU;qBACjB;oBACD,GAAG,EAAE,mCAAmC,CAAC,gBAAgB,CAAC;iBAC3D,CAAC,CAAC;aACJ;iBAAM,IAAI,KAAK,KAAK,UAAU,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;gBAClE,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,UAAU;qBACjB;oBACD,OAAO,EAAE,kCAAkC,CAAC,gBAAgB,CAAC;iBAC9D,CAAC,CAAC;aACJ;QACH,CAAC;QAED;;WAEG;QACH,SAAS,mCAAmC,CAC1C,IAKgC;YAEhC,OAAO,UAAU,KAAyB;gBACxC,MAAM,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAC1C,IAAI,aAAiC,CAAC;gBACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBACtC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;oBACxB,IACE,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,OAAO;wBACtC,KAAK,CAAC,KAAK,KAAK,QAAQ,EACxB;wBACA,MAAM,yBAAyB,GAC7B,UAAU,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;wBACrC,IAAI,yBAAyB,CAAC,MAAM,EAAE;4BACpC,sCAAsC;4BACtC,UAAU;4BACV,aAAa,GAAG;gCACd,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gCACd,yBAAyB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;6BACtC,CAAC;4BACF,MAAM;yBACP;6BAAM;4BACL,sBAAsB;4BACtB,UAAU;4BACV,aAAa,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;4BACzD,MAAM;yBACP;qBACF;iBACF;gBACD,OAAO,KAAK,CAAC,WAAW,CAAC,aAAc,CAAC,CAAC;YAC3C,CAAC,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,kCAAkC,CACzC,IAKgC;YAEhC,SAAS,GAAG,CACV,aAAqC,EACrC,KAAyB;;gBAEzB,IAAI,MAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,0CAAE,MAAM,EAAE;oBAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAClE,MAAM,SAAS,GAAG,UAAU,CAAC,aAAa,CAAC,aAAa,CAAE,CAAC;oBAC3D,OAAO,KAAK,CAAC,gBAAgB,CAAC,SAAS,EAAE,GAAG,aAAa,GAAG,CAAC,CAAC;iBAC/D;gBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,GAAG,aAAa,GAAG,CAAC,CAAC;YAC3D,CAAC;YAED,OAAO;gBACL;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACxB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC;iBACnC;gBACD;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBACzB,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC;iBACpC;gBACD;oBACE,SAAS,EAAE,0BAA0B;oBACrC,IAAI,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE;oBAC3B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC;iBACtC;aACF,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,kCAAkC,CACzC,kBAEyC;YAEzC,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBACpE,OAAO;aACR;YAED,MAAM,QAAQ,GAAG,gBAAgB,CAAC;YAElC,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,iBAAiB,CACnD,kBAAkB,EAClB,UAAU,CACX,CAAC;YACF,IACE,SAAS,KAAK,WAAW;gBACzB,kBAAkB,CAAC,aAAa,KAAK,QAAQ,EAC7C;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,6BAA6B;oBACxC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,YAAY;qBACnB;oBACD,GAAG,EAAE,mCAAmC,CAAC,kBAAkB,CAAC;iBAC7D,CAAC,CAAC;aACJ;iBAAM,IACL,SAAS,KAAK,UAAU;gBACxB,CAAC,kBAAkB,CAAC,aAAa,EACjC;gBACA,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,kBAAkB;oBACxB,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,YAAY;qBACnB;oBACD,OAAO,EAAE,kCAAkC,CAAC,kBAAkB,CAAC;iBAChE,CAAC,CAAC;aACJ;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,2CAA2C,CAClD,IAAkC;YAElC,MAAM,QAAQ,GAAG,oBAAoB,CAAC;YACtC,0DAA0D;YAC1D,IACE,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACjD,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EACxD;gBACA,OAAO;aACR;YAED,MAAM,QAAQ,GACZ,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC/C,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;gBACrB,CAAC,CAAC,qDAAqD;oBACpD,IAAI,CAAC,SAAS,CAAC,IAA4B,CAAC,IAAI,CAAC;YAExD,QAAQ,cAAc,EAAE;gBACtB,KAAK,UAAU,CAAC,CAAC;oBACf,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;wBACvB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,sBAAsB;4BACjC,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,QAAQ;6BACf;4BACD,OAAO,EAAE,kCAAkC,CAAC,IAAI,CAAC;yBAClD,CAAC,CAAC;qBACJ;oBACD,MAAM;iBACP;gBACD,KAAK,WAAW,CAAC,CAAC;oBAChB,IAAI,IAAI,CAAC,aAAa,KAAK,QAAQ,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACpD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,6BAA6B;4BACxC,IAAI,EAAE;gCACJ,IAAI,EAAE,QAAQ;gCACd,IAAI,EAAE,QAAQ;6BACf;4BACD,GAAG,EAAE,mCAAmC,CAAC,IAAI,CAAC;yBAC/C,CAAC,CAAC;qBACJ;oBACD,MAAM;iBACP;aACF;QACH,CAAC;QAED,OAAO;YACL,8CAA8C,EAC5C,gCAAgC;YAClC,kDAAkD,EAChD,kCAAkC;YACpC,mBAAmB,EAAE,2CAA2C;SACjE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}