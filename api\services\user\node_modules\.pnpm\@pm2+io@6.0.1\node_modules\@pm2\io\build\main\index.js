"use strict";
const pmx_1 = require("./pmx");
const IO_KEY = Symbol.for('@pm2/io');
const isAlreadyHere = (Object.getOwnPropertySymbols(global).indexOf(IO_KEY) > -1);
const io = isAlreadyHere ? global[IO_KEY] : new pmx_1.default().init();
global[IO_KEY] = io;
module.exports = io;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi9zcmMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtBQUNBLCtCQUF1QjtBQUV2QixNQUFNLE1BQU0sR0FBRyxNQUFNLENBQUMsR0FBRyxDQUFDLFNBQVMsQ0FBQyxDQUFBO0FBQ3BDLE1BQU0sYUFBYSxHQUFHLENBQUMsTUFBTSxDQUFDLHFCQUFxQixDQUFDLE1BQU0sQ0FBQyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFBO0FBRWpGLE1BQU0sRUFBRSxHQUFRLGFBQWEsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBUSxDQUFDLENBQUMsQ0FBQyxJQUFJLGFBQUcsRUFBRSxDQUFDLElBQUksRUFBRSxDQUFBO0FBQ3hFLE1BQU0sQ0FBQyxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUE7QUFFbkIsaUJBQVMsRUFBRSxDQUFBIn0=