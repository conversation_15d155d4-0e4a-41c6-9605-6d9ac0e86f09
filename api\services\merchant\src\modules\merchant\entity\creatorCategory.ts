import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * 创作者认证分类实体
 */
@Entity('creator_category')
export class CreatorCategoryEntity {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ comment: '分类名称' })
  name!: string;

  @Column({ comment: '认证类型 0-手工艺人 1-非遗传承人 2-通用', default: 0 })
  authType!: number;

  @Column({ comment: '分类描述', type: 'text', nullable: true })
  description!: string;

  @Column({ comment: '认证要求', type: 'text', nullable: true })
  requirements!: string;

  @Column({ comment: '状态 0-禁用 1-启用', default: 1 })
  status!: number;

  @CreateDateColumn({ comment: '创建时间' })
  createTime!: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updateTime!: Date;
}
