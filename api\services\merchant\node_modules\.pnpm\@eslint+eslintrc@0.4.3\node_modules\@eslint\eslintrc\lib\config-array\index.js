/**
 * @fileoverview `ConfigArray` class.
 * <AUTHOR> <https://github.com/mysticatea>
 */
"use strict";

const { ConfigArray, getUsedExtractedConfigs } = require("./config-array");
const { ConfigDependency } = require("./config-dependency");
const { ExtractedConfig } = require("./extracted-config");
const { IgnorePattern } = require("./ignore-pattern");
const { OverrideTester } = require("./override-tester");

module.exports = {
    ConfigArray,
    ConfigDependency,
    ExtractedConfig,
    IgnorePattern,
    OverrideTester,
    getUsedExtractedConfigs
};
