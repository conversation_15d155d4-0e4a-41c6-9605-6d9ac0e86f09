{"version": 3, "sources": ["../../src/decorator/entity/Entity.ts"], "names": [], "mappings": ";;AAqBA,wBAyBC;AA9CD,2CAAsD;AAGtD,wDAAoD;AAcpD;;;GAGG;AACH,SAAgB,MAAM,CAClB,aAAsC,EACtC,YAA4B;IAE5B,MAAM,OAAO,GACT,CAAC,yBAAW,CAAC,QAAQ,CAAC,aAAa,CAAC;QAChC,CAAC,CAAE,aAA+B;QAClC,CAAC,CAAC,YAAY,CAAC,IAAI,EAAE,CAAA;IAC7B,MAAM,IAAI,GACN,OAAO,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAA;IAEpE,OAAO,UAAU,MAAM;QACnB,IAAA,gCAAsB,GAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YACjC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;YACtD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACnD,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YACzD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YACnD,WAAW,EAAE,OAAO,CAAC,WAAW;YAChC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;SACpC,CAAC,CAAA;IAC3B,CAAC,CAAA;AACL,CAAC", "file": "Entity.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { TableMetadataArgs } from \"../../metadata-args/TableMetadataArgs\"\nimport { EntityOptions } from \"../options/EntityOptions\"\nimport { ObjectUtils } from \"../../util/ObjectUtils\"\n\n/**\n * This decorator is used to mark classes that will be an entity (table or document depend on database type).\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function Entity(options?: EntityOptions): ClassDecorator\n\n/**\n * This decorator is used to mark classes that will be an entity (table or document depend on database type).\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function Entity(name?: string, options?: EntityOptions): ClassDecorator\n\n/**\n * This decorator is used to mark classes that will be an entity (table or document depend on database type).\n * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.\n */\nexport function Entity(\n    nameOrOptions?: string | EntityOptions,\n    maybeOptions?: EntityOptions,\n): ClassDecorator {\n    const options =\n        (ObjectUtils.isObject(nameOrOptions)\n            ? (nameOrOptions as EntityOptions)\n            : maybeOptions) || {}\n    const name =\n        typeof nameOrOptions === \"string\" ? nameOrOptions : options.name\n\n    return function (target) {\n        getMetadataArgsStorage().tables.push({\n            target: target,\n            name: name,\n            type: \"regular\",\n            orderBy: options.orderBy ? options.orderBy : undefined,\n            engine: options.engine ? options.engine : undefined,\n            database: options.database ? options.database : undefined,\n            schema: options.schema ? options.schema : undefined,\n            synchronize: options.synchronize,\n            withoutRowid: options.withoutRowid,\n            comment: options.comment ? options.comment : undefined,\n        } as TableMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}