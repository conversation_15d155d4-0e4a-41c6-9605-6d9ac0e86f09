{"name": "@midwayjs/cli-plugin-dev", "version": "2.1.1", "main": "dist/index", "typings": "dist/index.d.ts", "dependencies": {"@midwayjs/command-core": "^2.1.0", "@midwayjs/debugger": "^1.0.x", "chalk": "^4.1.0", "chokidar": "^3.4.2", "detect-port": "^1.3.0", "fs-extra": "^8.1.0", "json5": "^2.2.0", "koa": "^2.11.0", "light-spinner": "^1.0.1", "md5": "^2.3.0", "node-fetch": "^2.6.0", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.0", "ws": "^7.2.3"}, "peerDependencies": {"@midwayjs/mock": "*"}, "devDependencies": {"@midwayjs/cli-plugin-swc": "^2.0.6", "@midwayjs/core": "^2.8.0", "@midwayjs/serverless-app": "^2.8.9", "@midwayjs/web": "^2.8.0", "fs-extra": "^8.1.0", "node-fetch": "^2.6.1"}, "engines": {"node": ">= 10"}, "files": ["plugin.json", "dist", "src", "js"], "scripts": {"build": "tsc --build", "lint": "../../node_modules/.bin/eslint .", "test-bugs": "../../node_modules/.bin/jest ./test/bugs*", "test": "../../node_modules/.bin/jest --detectOpenHandles --forceExit --runInBand", "cov": "../../node_modules/.bin/jest --coverage --runInBand --forceExit", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "publishConfig": {"access": "public"}, "license": "MIT", "gitHead": "85c7f7346401fcaaa1eb821211470ac490fffb40"}