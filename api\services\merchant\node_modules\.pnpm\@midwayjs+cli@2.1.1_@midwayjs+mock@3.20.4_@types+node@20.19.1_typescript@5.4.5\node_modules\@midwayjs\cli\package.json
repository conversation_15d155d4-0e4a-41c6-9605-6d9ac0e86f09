{"name": "@midwayjs/cli", "version": "2.1.1", "description": "Midway Command Tools", "main": "index.js", "bin": {"midway-bin": "bin/midway-bin.js", "mw": "bin/midway-bin.js"}, "scripts": {"lint": "../../node_modules/.bin/eslint .", "build": "tsc --build", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov", "postinstall": "node ./postinstall"}, "keywords": ["midway", "bin"], "license": "MIT", "dependencies": {"@midwayjs/cli-plugin-build": "^2.1.0", "@midwayjs/cli-plugin-check": "^2.1.0", "@midwayjs/cli-plugin-clean": "^2.1.0", "@midwayjs/cli-plugin-dev": "^2.1.1", "@midwayjs/cli-plugin-test": "^2.1.0", "@midwayjs/command-core": "^2.1.0", "enquirer": "^2.3.4", "minimist": "^1.2.5", "mod-info": "^1.0.0", "source-map-support": "^0.5.19", "ts-node": "^10.0.0"}, "devDependencies": {"typescript": "^4.1.0"}, "engines": {"node": ">= 10.0.0"}, "files": ["bin", "lib", "dist", "jest", "index.js", "postinstall.js"], "publishConfig": {"access": "public"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "http://github.com/midwayjs/midway.git"}, "gitHead": "85c7f7346401fcaaa1eb821211470ac490fffb40"}