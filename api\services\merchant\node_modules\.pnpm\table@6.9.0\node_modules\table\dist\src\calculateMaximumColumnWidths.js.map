{"version": 3, "file": "calculateMaximumColumnWidths.js", "sourceRoot": "", "sources": ["../../src/calculateMaximumColumnWidths.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAAuC;AAQvC,mCAEiB;AAEV,MAAM,yBAAyB,GAAG,CAAC,IAAU,EAAU,EAAE;IAC9D,OAAO,IAAI,CAAC,GAAG,CACb,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,sBAAW,CAAC,CACrC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,yBAAyB,6BAIpC;AAEF;;GAEG;AACI,MAAM,4BAA4B,GAAG,CAAC,IAAW,EAAE,sBAA4C,EAAE,EAAY,EAAE;IACpH,MAAM,YAAY,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,GAAG,CAAC,gCAAwB,CAAC,CAAC;IAC3E,MAAM,cAAc,GAAG,CAAC,QAAgB,EAAE,WAAmB,EAAW,EAAE;QACxE,OAAO,gBAAgB,CAAC,IAAI,CAAC,CAAC,eAAe,EAAE,EAAE;YAC/C,OAAO,IAAA,qBAAa,EAAC,EAAC,GAAG,EAAE,WAAW;gBACpC,GAAG,EAAE,QAAQ,EAAC,EAAE,eAAe,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;QAC7B,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;YAC9B,IAAI,cAAc,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE;gBACvC,OAAO;aACR;YACD,YAAY,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,IAAA,iCAAyB,EAAC,IAAI,CAAC,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,YAAY,CAAC;AACtB,CAAC,CAAC;AApBW,QAAA,4BAA4B,gCAoBvC"}