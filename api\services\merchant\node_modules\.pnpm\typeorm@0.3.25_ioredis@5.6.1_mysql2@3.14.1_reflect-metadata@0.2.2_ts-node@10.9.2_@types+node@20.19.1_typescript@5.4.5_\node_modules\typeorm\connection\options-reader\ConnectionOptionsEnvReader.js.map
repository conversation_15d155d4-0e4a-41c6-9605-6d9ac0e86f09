{"version": 3, "sources": ["../../src/connection/options-reader/ConnectionOptionsEnvReader.ts"], "names": [], "mappings": ";;;AACA,gEAA4D;AAC5D,kDAA8C;AAE9C;;;;;;GAMG;AACH,MAAa,0BAA0B;IACnC,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,IAAI;QACN,OAAO;YACH;gBACI,IAAI,EACA,6BAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC;oBAClD,CAAC,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC;wBACxC,CAAC,CAAC,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,KAAK,CAC7C,KAAK,CACR,CAAC,CAAC,CAAC;wBACN,CAAC,CAAC,SAAS,CAAC;gBACpB,GAAG,EAAE,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC;gBAChD,IAAI,EAAE,6BAAa,CAAC,cAAc,CAAC,cAAc,CAAC;gBAClD,IAAI,EAAE,IAAI,CAAC,cAAc,CACrB,6BAAa,CAAC,cAAc,CAAC,cAAc,CAAC,CAC/C;gBACD,QAAQ,EAAE,6BAAa,CAAC,cAAc,CAAC,kBAAkB,CAAC;gBAC1D,QAAQ,EAAE,6BAAa,CAAC,cAAc,CAAC,kBAAkB,CAAC;gBAC1D,QAAQ,EAAE,6BAAa,CAAC,cAAc,CAAC,kBAAkB,CAAC;gBAC1D,GAAG,EAAE,6BAAa,CAAC,cAAc,CAAC,aAAa,CAAC;gBAChD,MAAM,EAAE,6BAAa,CAAC,cAAc,CAAC,gBAAgB,CAAC;gBACtD,KAAK,EAAE,6BAAa,CAAC,cAAc,CAAC,sBAAsB,CAAC;oBACvD,CAAC,CAAC,IAAI,CAAC,KAAK,CACN,6BAAa,CAAC,cAAc,CAAC,sBAAsB,CAAC,CACvD;oBACH,CAAC,CAAC,SAAS;gBACf,WAAW,EAAE,mBAAQ,CAAC,SAAS,CAC3B,6BAAa,CAAC,cAAc,CAAC,qBAAqB,CAAC,CACtD;gBACD,UAAU,EAAE,mBAAQ,CAAC,SAAS,CAC1B,6BAAa,CAAC,cAAc,CAAC,qBAAqB,CAAC,CACtD;gBACD,aAAa,EAAE,mBAAQ,CAAC,SAAS,CAC7B,6BAAa,CAAC,cAAc,CAAC,wBAAwB,CAAC,CACzD;gBACD,QAAQ,EAAE,IAAI,CAAC,aAAa,CACxB,6BAAa,CAAC,cAAc,CAAC,kBAAkB,CAAC,CACnD;gBACD,UAAU,EAAE,IAAI,CAAC,aAAa,CAC1B,6BAAa,CAAC,cAAc,CAAC,oBAAoB,CAAC,CACrD;gBACD,mBAAmB,EAAE,6BAAa,CAAC,cAAc,CAC7C,+BAA+B,CAClC;gBACD,iBAAiB,EAAE,6BAAa,CAAC,cAAc,CAC3C,6BAA6B,CAChC;gBACD,WAAW,EAAE,IAAI,CAAC,aAAa,CAC3B,6BAAa,CAAC,cAAc,CAAC,qBAAqB,CAAC,CACtD;gBACD,OAAO,EAAE,IAAI,CAAC,gBAAgB,CAC1B,6BAAa,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAClD;gBACD,MAAM,EAAE,6BAAa,CAAC,cAAc,CAAC,gBAAgB,CAAC;gBACtD,YAAY,EAAE,6BAAa,CAAC,cAAc,CACtC,uBAAuB,CAC1B;gBACD,qBAAqB,EAAE,6BAAa,CAAC,cAAc,CAC/C,kCAAkC,CACrC;gBACD,KAAK,EAAE,6BAAa,CAAC,cAAc,CAAC,eAAe,CAAC;gBACpD,KAAK,EAAE,IAAI,CAAC,gBAAgB,EAAE;gBAC9B,aAAa,EAAE,6BAAa,CAAC,cAAc,CACvC,wBAAwB,CAC3B;aACJ;SACJ,CAAA;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,gBAAgB,CAAC,OAAe;QACtC,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG;YAC3D,OAAO,IAAI,CAAA;QACf,IAAI,OAAO,KAAK,KAAK;YAAE,OAAO,KAAK,CAAA;QAEnC,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACO,gBAAgB;QACtB,MAAM,OAAO,GAAG,6BAAa,CAAC,cAAc,CAAC,eAAe,CAAC,CAAA;QAC7D,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG;YAC3D,OAAO,IAAI,CAAA;QACf,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,OAAO,IAAI,OAAO,KAAK,GAAG;YAC7D,OAAO,KAAK,CAAA;QAChB,IACI,OAAO,KAAK,OAAO;YACnB,OAAO,KAAK,SAAS;YACrB,OAAO,KAAK,UAAU;YAEtB,OAAO;gBACH,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,6BAAa,CAAC,cAAc,CAAC,uBAAuB,CAAC;oBAC1D,CAAC,CAAC,IAAI,CAAC,KAAK,CACN,6BAAa,CAAC,cAAc,CAAC,uBAAuB,CAAC,CACxD;oBACH,CAAC,CAAC,SAAS;gBACf,aAAa,EAAE,6BAAa,CAAC,cAAc,CACvC,8BAA8B,CACjC;gBACD,QAAQ,EAAE,QAAQ,CACd,6BAAa,CAAC,cAAc,CAAC,wBAAwB,CAAC,CACzD;aACJ,CAAA;QAEL,OAAO,SAAS,CAAA;IACpB,CAAC;IAED;;OAEG;IACO,aAAa,CAAC,QAAiB;QACrC,IAAI,CAAC,QAAQ;YAAE,OAAO,EAAE,CAAA;QACxB,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IACvD,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,KAAU;QAC7B,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,OAAO,SAAS,CAAA;QACpB,CAAC;QAED,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAA;IAC1B,CAAC;CACJ;AA7ID,gEA6IC", "file": "ConnectionOptionsEnvReader.js", "sourcesContent": ["import { DataSourceOptions } from \"../../data-source/DataSourceOptions\"\nimport { PlatformTools } from \"../../platform/PlatformTools\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\n\n/**\n * Reads connection options from environment variables.\n * Environment variables can have only a single connection.\n * Its strongly required to define TYPEORM_CONNECTION env variable.\n *\n * @deprecated\n */\nexport class ConnectionOptionsEnvReader {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Reads connection options from environment variables.\n     */\n    async read(): Promise<DataSourceOptions[]> {\n        return [\n            {\n                type:\n                    PlatformTools.getEnvVariable(\"TYPEORM_CONNECTION\") ||\n                    (PlatformTools.getEnvVariable(\"TYPEORM_URL\")\n                        ? PlatformTools.getEnvVariable(\"TYPEORM_URL\").split(\n                              \"://\",\n                          )[0]\n                        : undefined),\n                url: PlatformTools.getEnvVariable(\"TYPEORM_URL\"),\n                host: PlatformTools.getEnvVariable(\"TYPEORM_HOST\"),\n                port: this.stringToNumber(\n                    PlatformTools.getEnvVariable(\"TYPEORM_PORT\"),\n                ),\n                username: PlatformTools.getEnvVariable(\"TYPEORM_USERNAME\"),\n                password: PlatformTools.getEnvVariable(\"TYPEORM_PASSWORD\"),\n                database: PlatformTools.getEnvVariable(\"TYPEORM_DATABASE\"),\n                sid: PlatformTools.getEnvVariable(\"TYPEORM_SID\"),\n                schema: PlatformTools.getEnvVariable(\"TYPEORM_SCHEMA\"),\n                extra: PlatformTools.getEnvVariable(\"TYPEORM_DRIVER_EXTRA\")\n                    ? JSON.parse(\n                          PlatformTools.getEnvVariable(\"TYPEORM_DRIVER_EXTRA\"),\n                      )\n                    : undefined,\n                synchronize: OrmUtils.toBoolean(\n                    PlatformTools.getEnvVariable(\"TYPEORM_SYNCHRONIZE\"),\n                ),\n                dropSchema: OrmUtils.toBoolean(\n                    PlatformTools.getEnvVariable(\"TYPEORM_DROP_SCHEMA\"),\n                ),\n                migrationsRun: OrmUtils.toBoolean(\n                    PlatformTools.getEnvVariable(\"TYPEORM_MIGRATIONS_RUN\"),\n                ),\n                entities: this.stringToArray(\n                    PlatformTools.getEnvVariable(\"TYPEORM_ENTITIES\"),\n                ),\n                migrations: this.stringToArray(\n                    PlatformTools.getEnvVariable(\"TYPEORM_MIGRATIONS\"),\n                ),\n                migrationsTableName: PlatformTools.getEnvVariable(\n                    \"TYPEORM_MIGRATIONS_TABLE_NAME\",\n                ),\n                metadataTableName: PlatformTools.getEnvVariable(\n                    \"TYPEORM_METADATA_TABLE_NAME\",\n                ),\n                subscribers: this.stringToArray(\n                    PlatformTools.getEnvVariable(\"TYPEORM_SUBSCRIBERS\"),\n                ),\n                logging: this.transformLogging(\n                    PlatformTools.getEnvVariable(\"TYPEORM_LOGGING\"),\n                ),\n                logger: PlatformTools.getEnvVariable(\"TYPEORM_LOGGER\"),\n                entityPrefix: PlatformTools.getEnvVariable(\n                    \"TYPEORM_ENTITY_PREFIX\",\n                ),\n                maxQueryExecutionTime: PlatformTools.getEnvVariable(\n                    \"TYPEORM_MAX_QUERY_EXECUTION_TIME\",\n                ),\n                debug: PlatformTools.getEnvVariable(\"TYPEORM_DEBUG\"),\n                cache: this.transformCaching(),\n                uuidExtension: PlatformTools.getEnvVariable(\n                    \"TYPEORM_UUID_EXTENSION\",\n                ),\n            },\n        ]\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Transforms logging string into real logging value connection requires.\n     */\n    protected transformLogging(logging: string): any {\n        if (logging === \"true\" || logging === \"TRUE\" || logging === \"1\")\n            return true\n        if (logging === \"all\") return \"all\"\n\n        return this.stringToArray(logging)\n    }\n\n    /**\n     * Transforms caching option into real caching value option requires.\n     */\n    protected transformCaching(): boolean | object | undefined {\n        const caching = PlatformTools.getEnvVariable(\"TYPEORM_CACHE\")\n        if (caching === \"true\" || caching === \"TRUE\" || caching === \"1\")\n            return true\n        if (caching === \"false\" || caching === \"FALSE\" || caching === \"0\")\n            return false\n        if (\n            caching === \"redis\" ||\n            caching === \"ioredis\" ||\n            caching === \"database\"\n        )\n            return {\n                type: caching,\n                options: PlatformTools.getEnvVariable(\"TYPEORM_CACHE_OPTIONS\")\n                    ? JSON.parse(\n                          PlatformTools.getEnvVariable(\"TYPEORM_CACHE_OPTIONS\"),\n                      )\n                    : undefined,\n                alwaysEnabled: PlatformTools.getEnvVariable(\n                    \"TYPEORM_CACHE_ALWAYS_ENABLED\",\n                ),\n                duration: parseInt(\n                    PlatformTools.getEnvVariable(\"TYPEORM_CACHE_DURATION\"),\n                ),\n            }\n\n        return undefined\n    }\n\n    /**\n     * Converts a string which contains multiple elements split by comma into a string array of strings.\n     */\n    protected stringToArray(variable?: string) {\n        if (!variable) return []\n        return variable.split(\",\").map((str) => str.trim())\n    }\n\n    /**\n     * Converts a string which contains a number into a javascript number\n     */\n    private stringToNumber(value: any): number | undefined {\n        if (!value) {\n            return undefined\n        }\n\n        return parseInt(value)\n    }\n}\n"], "sourceRoot": "../.."}