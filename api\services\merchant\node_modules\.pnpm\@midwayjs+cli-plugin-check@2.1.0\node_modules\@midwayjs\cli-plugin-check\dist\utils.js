"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMainVersion = exports.transformToRelative = void 0;
const path_1 = require("path");
const transformToRelative = (baseDir, targetDir) => {
    if (targetDir) {
        if ((0, path_1.isAbsolute)(targetDir)) {
            return (0, path_1.relative)(baseDir, targetDir);
        }
        return targetDir;
    }
};
exports.transformToRelative = transformToRelative;
const getMainVersion = version => {
    version = (version || '').split('.')[0];
    if (version === 'latest' || version === 'beta') {
        return '3';
    }
    return version.replace(/[^\d]/g, '') || '3';
};
exports.getMainVersion = getMainVersion;
//# sourceMappingURL=utils.js.map