import { IFileDetector } from '../../interface';
export interface IComponentInfo {
    component: any;
    enabledEnvironment?: string[];
}
export interface ResolveFilter {
    pattern: string | RegExp;
    filter: (module: any, filter: any, bindModule: any) => any;
    ignoreRequire?: boolean;
}
export interface InjectionConfigurationOptions {
    imports?: Array<string | IComponentInfo | {
        Configuration: any;
    }>;
    importObjects?: Record<string, unknown>;
    importConfigs?: Array<{
        [environmentName: string]: Record<string, any>;
    }> | Record<string, any>;
    importConfigFilter?: (config: Record<string, any>) => Record<string, any>;
    namespace?: string;
    detector?: IFileDetector | false;
    detectorOptions?: Record<string, any>;
    conflictCheck?: boolean;
}
export declare function Configuration(options?: InjectionConfigurationOptions): ClassDecorator;
//# sourceMappingURL=configuration.d.ts.map