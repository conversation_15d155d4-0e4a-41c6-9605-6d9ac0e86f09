{"version": 3, "sources": ["../../src/find-options/operator/Equal.ts"], "names": [], "mappings": ";;AAWA,sBAEC;AAZD,oDAAgD;AAEhD;;;;;;;GAOG;AACH,SAAgB,KAAK,CAAI,KAA0B;IAC/C,OAAO,IAAI,6BAAa,CAAC,KAAK,CAAC,CAAA;AACnC,CAAC", "file": "Equal.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\nimport { EqualOperator } from \"../EqualOperator\"\n\n/**\n * Find Options Operator.\n * This operator is handy to provide object value for non-relational properties of the Entity.\n *\n * Examples:\n *      { someField: Equal(\"value\") }\n *      { uuid: Equal(new UUID()) }\n */\nexport function Equal<T>(value: T | FindOperator<T>) {\n    return new EqualOperator(value)\n}\n"], "sourceRoot": "../.."}