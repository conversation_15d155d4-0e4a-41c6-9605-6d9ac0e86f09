{"version": 3, "file": "http.js", "sourceRoot": "", "sources": ["../src/http.ts"], "names": [], "mappings": ";;;;;;AAAA,gDAAyB;AACzB,kDAA0B;AAC1B,mCAA8B;AAE9B,kDAAgC;AAEhC,8DAAqC;AACrC,0DAAuC;AACvC,gEAA6C;AAE7C,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,cAAc,CAAC,CAAC;AAuB1C;;GAEG;AACI,MAAM,IAAI,GAAgC,KAAK,EAAE,GAAG,EAAE,IAAI,GAAG,EAAE,EAAE,EAAE;IACzE,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;IAE1B,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;IAExC,gEAAgE;IAChE,mDAAmD;IACnD,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,OAAO,KAAK,CAAC,UAAU,KAAK,QAAQ,EAAE;QACpE,+DAA+D;QAC/D,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;QAC1C,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzC,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;SACrD;QACD,0DAA0D;QAC1D,4CAA4C;QAC5C,MAAM,IAAI,qBAAgB,EAAE,CAAC;KAC7B;IAED,iCAAiC;IACjC,MAAM,YAAY,GACjB,OAAO,IAAI,CAAC,YAAY,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IAC/D,KAAK,CAAC,2BAA2B,EAAE,YAAY,CAAC,CAAC;IAEjD,IAAI,GAAG,CAAC;IACR,IAAI,IAAI,CAAC,IAAI,EAAE;QACd,uDAAuD;QACvD,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;QAChB,KAAK,CAAC,kCAAkC,CAAC,CAAC;KAC1C;SAAM;QACN,GAAG,GAAG,cAAK,CAAC;QACZ,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAClC;IAED,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,EAAE,CAAC;IAE5B,2DAA2D;IAC3D,IAAI,KAAK,EAAE;QACV,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACrB,OAAO,CAAC,OAAO,GAAG,EAAE,CAAC;SACrB;QAED,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;QACpD,IAAI,YAAY,EAAE;YACjB,OAAO,CAAC,OAAO,CAAC,mBAAmB,CAAC,GAAG,YAAY,CAAC;YACpD,KAAK,CAAC,8CAA8C,EAAE,YAAY,CAAC,CAAC;SACpE;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;QAChC,IAAI,IAAI,EAAE;YACT,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,IAAI,CAAC;YACxC,KAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAC;SACxD;KACD;IAED,MAAM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAClC,MAAM,CAAC,GAAG,CAAC,GAA0B,MAAM,IAAA,aAAI,EAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IACjE,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC;IAEjC,2EAA2E;IAC3E,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACtB,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC;IAEjB,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,CAAC;IAE3C,uCAAuC;IACvC,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAE9B,yCAAyC;IACzC,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC;IACtC,IAAI,IAAI,KAAK,CAAC,IAAI,QAAQ,EAAE;QAC3B,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;QACzC,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QAEjC,IAAI,SAAS,CAAC,MAAM,GAAG,YAAY,EAAE;YACpC,KAAK,CAAC,gDAAgD,EAAE,QAAQ,CAAC,CAAC;YAElE,kDAAkD;YAClD,GAAG,CAAC,MAAM,EAAE,CAAC;YAEb,4DAA4D;YAC5D,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAEpB,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAC3C,KAAK,CAAC,2BAA2B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAC;YAEhD,MAAM,IAAI,GAAG,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC;YAC7C,KAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAC;YAExD,+CAA+C;YAC/C,IAAI,MAAM,CAAC,QAAQ,KAAK,GAAG,CAAC,QAAQ,EAAE;gBACrC,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,eAAK,CAAC,CAAC,CAAC,SAAS,CAAC;aAC7D;YAED,OAAO,IAAA,YAAI,EAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SAC1B;KACD;IAED,4EAA4E;IAC5E,IAAI,IAAI,KAAK,CAAC,EAAE;QACf,GAAG,CAAC,MAAM,EAAE,CAAC;QACb,IAAI,IAAI,KAAK,GAAG,EAAE;YACjB,MAAM,IAAI,qBAAgB,EAAE,CAAC;SAC7B;aAAM,IAAI,IAAI,KAAK,GAAG,EAAE;YACxB,MAAM,IAAI,kBAAa,EAAE,CAAC;SAC1B;QACD,yBAAyB;QACzB,MAAM,IAAI,oBAAS,CAAC,IAAI,CAAC,CAAC;KAC1B;IAED,IAAI,IAAI,CAAC,SAAS,EAAE;QACnB,4EAA4E;QAC5E,qEAAqE;QACrE,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;KAC/B;IAED,OAAO,GAAG,CAAC;AACZ,CAAC,CAAC;AArHW,QAAA,IAAI,QAqHf;AAEF;;;;;;;GAOG;AAEH,SAAS,OAAO,CAAC,KAA0B;IAC1C,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,EAAE,EAAE,EAAE,CAAC,CAAC;IACxD,MAAM,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAEpD,IAAI,YAAY,EAAE;QACjB,8EAA8E;QAC9E,KAAK,CAAC,mBAAmB,EAAE,YAAY,CAAC,CAAC;QAEzC,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzB,QAAQ,IAAI,EAAE;gBACb,KAAK,SAAS;oBACb,OAAO;wBACN,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC;oBACtD,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;oBAC7B,IAAI,KAAK,EAAE;wBACV,KAAK,CACJ,yDAAyD,EACzD,IAAI,CACJ,CAAC;qBACF;oBACD,OAAO,KAAK,CAAC;gBACd,KAAK,iBAAiB;oBACrB,oCAAoC;oBACpC,MAAM;gBACP,KAAK,UAAU,CAAC;gBAChB,KAAK,UAAU;oBACd,KAAK,CACJ,yDAAyD,EACzD,IAAI,CACJ,CAAC;oBACF,OAAO,KAAK,CAAC;gBACd;oBACC,6BAA6B;oBAC7B,MAAM;aACP;SACD;KACD;SAAM,IAAI,OAAO,EAAE;QACnB,kEAAkE;QAClE,KAAK,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAC9B,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;QAC7B,IAAI,KAAK,EAAE;YACV,KAAK,CAAC,0DAA0D,CAAC,CAAC;SAClE;QACD,OAAO,KAAK,CAAC;KACb;IAED,OAAO,KAAK,CAAC;AACd,CAAC;AAED;;;;;GAKG;AAEH,SAAS,QAAQ,CAAC,GAAQ,EAAE,KAAoB;IAC/C,IAAI,KAAK,EAAE;QACV,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE;YACnD,OAAO,KAA4B,CAAC;SACpC;QACD,IAAI,KAAK,CAAC,SAAS,EAAE;YACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAChD,MAAM,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,CAAC,EAAE;oBACN,OAAO,CAAwB,CAAC;iBAChC;aACD;SACD;KACD;IACD,OAAO,IAAI,CAAC;AACb,CAAC"}