{"version": 3, "sources": ["../browser/src/query-builder/RelationRemover.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAEjD;;;;GAIG;AACH,MAAM,OAAO,eAAe;IACxB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YACc,YAA+B,EAC/B,aAAiC;QADjC,iBAAY,GAAZ,YAAY,CAAmB;QAC/B,kBAAa,GAAb,aAAa,CAAoB;IAC5C,CAAC;IAEJ,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,KAAkB;QAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA;QAEpD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACvB,8CAA8C;YAC9C,yJAAyJ;YAEzJ,+DAA+D;YAC/D,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YAErD,MAAM,SAAS,GAAkB,EAAE,CAAA;YACnC,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBACrD,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,IAAI,CAAA;YACzC,CAAC,CAAC,CAAA;YAEF,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,EAAE,EAAE;gBACxB,UAAU,CAAC,IAAI,CACX,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;oBAChC,OAAO;wBACH,GAAG,QAAQ,CAAC,eAAgB,CAAC,WAAW,CAAC,GAAG,CACxC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;4BACpB,MAAM,aAAa,GACf,aAAa;gCACb,OAAO;gCACP,GAAG;gCACH,UAAU;gCACV,GAAG;gCACH,WAAW,CAAA;4BACf,UAAU,CAAC,aAAa,CAAC;gCACrB,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;oCACpB,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,EAAE,CACL;oCACH,CAAC,CAAC,EAAE,CAAA;4BACZ,OAAO,GAAG,MAAM,CAAC,YAAY,OAAO,aAAa,EAAE,CAAA;wBACvD,CAAC,CACJ;wBACD,GAAG,QAAQ,CAAC,eAAgB,CAAC,cAAc,CAAC,cAAc,CAAC,GAAG,CAC1D,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;4BACpB,MAAM,aAAa,GACf,gBAAgB;gCAChB,UAAU;gCACV,GAAG;gCACH,UAAU;gCACV,GAAG;gCACH,WAAW,CAAA;4BACf,UAAU,CAAC,aAAa,CAAC;gCACrB,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC;oCACvB,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC;oCAC9B,CAAC,CAAC,KAAK,CAAA;4BACf,OAAO,GAAG,MAAM,CAAC,YAAY,OAAO,aAAa,EAAE,CAAA;wBACvD,CAAC,CACJ;qBACJ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACnB,CAAC,CAAC,CACL,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,SAAS,GAAG,UAAU;iBACvB,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;iBAC7B,IAAI,CAAC,MAAM,CAAC,CAAA;YACjB,IAAI,CAAC,SAAS;gBAAE,OAAM;YAEtB,MAAM,IAAI,CAAC,YAAY;iBAClB,kBAAkB,EAAE;iBACpB,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,MAAM,CAAC;iBAC7C,GAAG,CAAC,SAAS,CAAC;iBACd,KAAK,CAAC,SAAS,CAAC;iBAChB,aAAa,CAAC,UAAU,CAAC;iBACzB,OAAO,EAAE,CAAA;QAClB,CAAC;aAAM,CAAC;YACJ,eAAe;YAEf,MAAM,gBAAgB,GAAG,QAAQ,CAAC,sBAAuB,CAAA;YACzD,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC5C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;gBACvB,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,CAAA;YAC7B,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;YACrD,MAAM,iBAAiB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,CAAA;YACnE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAA;YAEpE,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,UAAU,GAAa,EAAE,CAAA;YAC/B,iBAAiB,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,mBAAmB,EAAE,EAAE;gBAC9D,UAAU,CAAC,IAAI,CACX,GAAG,kBAAkB,CAAC,GAAG,CACrB,CAAC,eAAe,EAAE,oBAAoB,EAAE,EAAE;oBACtC,OAAO;wBACH,GAAG,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAChC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;4BACpB,MAAM,aAAa,GACf,aAAa;gCACb,mBAAmB;gCACnB,GAAG;gCACH,oBAAoB;gCACpB,GAAG;gCACH,WAAW,CAAA;4BACf,UAAU,CAAC,aAAa,CAAC;gCACrB,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC;oCAChC,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,cAAc,CACjB;oCACH,CAAC,CAAC,cAAc,CAAA;4BACxB,OAAO,GAAG,MAAM,CAAC,YAAY,OAAO,aAAa,EAAE,CAAA;wBACvD,CAAC,CACJ;wBACD,GAAG,gBAAgB,CAAC,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;4BACpB,MAAM,aAAa,GACf,cAAc;gCACd,mBAAmB;gCACnB,GAAG;gCACH,oBAAoB;gCACpB,GAAG;gCACH,WAAW,CAAA;4BACf,UAAU,CAAC,aAAa,CAAC;gCACrB,WAAW,CAAC,QAAQ,CAChB,eAAe,CAClB;oCACG,CAAC,CAAC,MAAM,CAAC,gBAAiB,CAAC,cAAc,CACnC,eAAe,CAClB;oCACH,CAAC,CAAC,eAAe,CAAA;4BACzB,OAAO,GAAG,MAAM,CAAC,YAAY,OAAO,aAAa,EAAE,CAAA;wBACvD,CAAC,CACJ;qBACJ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;gBACnB,CAAC,CACJ,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YACF,MAAM,SAAS,GAAG,UAAU;iBACvB,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;iBAC7B,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,MAAM,IAAI,CAAC,YAAY;iBAClB,kBAAkB,EAAE;iBACpB,MAAM,EAAE;iBACR,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;iBAChC,KAAK,CAAC,SAAS,CAAC;iBAChB,aAAa,CAAC,UAAU,CAAC;iBACzB,OAAO,EAAE,CAAA;QAClB,CAAC;IACL,CAAC;CACJ", "file": "RelationRemover.js", "sourcesContent": ["import { QueryBuilder } from \"./QueryBuilder\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { QueryExpressionMap } from \"./QueryExpressionMap\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\n\n/**\n * Allows to work with entity relations and perform specific operations with those relations.\n *\n * todo: add transactions everywhere\n */\nexport class RelationRemover {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(\n        protected queryBuilder: QueryBuilder<any>,\n        protected expressionMap: QueryExpressionMap,\n    ) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Performs remove operation on a relation.\n     */\n    async remove(value: any | any[]): Promise<void> {\n        const relation = this.expressionMap.relationMetadata\n\n        if (relation.isOneToMany) {\n            // if (this.expressionMap.of instanceof Array)\n            //     throw new TypeORMError(`You cannot update relations of multiple entities with the same related object. Provide a single entity into .of method.`);\n\n            // DELETE FROM post WHERE post.categoryId = of AND post.id = id\n            const ofs = Array.isArray(this.expressionMap.of)\n                ? this.expressionMap.of\n                : [this.expressionMap.of]\n            const values = Array.isArray(value) ? value : [value]\n\n            const updateSet: ObjectLiteral = {}\n            relation.inverseRelation!.joinColumns.forEach((column) => {\n                updateSet[column.propertyName] = null\n            })\n\n            const parameters: ObjectLiteral = {}\n            const conditions: string[] = []\n            ofs.forEach((of, ofIndex) => {\n                conditions.push(\n                    ...values.map((value, valueIndex) => {\n                        return [\n                            ...relation.inverseRelation!.joinColumns.map(\n                                (column, columnIndex) => {\n                                    const parameterName =\n                                        \"joinColumn_\" +\n                                        ofIndex +\n                                        \"_\" +\n                                        valueIndex +\n                                        \"_\" +\n                                        columnIndex\n                                    parameters[parameterName] =\n                                        ObjectUtils.isObject(of)\n                                            ? column.referencedColumn!.getEntityValue(\n                                                  of,\n                                              )\n                                            : of\n                                    return `${column.propertyPath} = :${parameterName}`\n                                },\n                            ),\n                            ...relation.inverseRelation!.entityMetadata.primaryColumns.map(\n                                (column, columnIndex) => {\n                                    const parameterName =\n                                        \"primaryColumn_\" +\n                                        valueIndex +\n                                        \"_\" +\n                                        valueIndex +\n                                        \"_\" +\n                                        columnIndex\n                                    parameters[parameterName] =\n                                        ObjectUtils.isObject(value)\n                                            ? column.getEntityValue(value)\n                                            : value\n                                    return `${column.propertyPath} = :${parameterName}`\n                                },\n                            ),\n                        ].join(\" AND \")\n                    }),\n                )\n            })\n            const condition = conditions\n                .map((str) => \"(\" + str + \")\")\n                .join(\" OR \")\n            if (!condition) return\n\n            await this.queryBuilder\n                .createQueryBuilder()\n                .update(relation.inverseEntityMetadata.target)\n                .set(updateSet)\n                .where(condition)\n                .setParameters(parameters)\n                .execute()\n        } else {\n            // many to many\n\n            const junctionMetadata = relation.junctionEntityMetadata!\n            const ofs = Array.isArray(this.expressionMap.of)\n                ? this.expressionMap.of\n                : [this.expressionMap.of]\n            const values = Array.isArray(value) ? value : [value]\n            const firstColumnValues = relation.isManyToManyOwner ? ofs : values\n            const secondColumnValues = relation.isManyToManyOwner ? values : ofs\n\n            const parameters: ObjectLiteral = {}\n            const conditions: string[] = []\n            firstColumnValues.forEach((firstColumnVal, firstColumnValIndex) => {\n                conditions.push(\n                    ...secondColumnValues.map(\n                        (secondColumnVal, secondColumnValIndex) => {\n                            return [\n                                ...junctionMetadata.ownerColumns.map(\n                                    (column, columnIndex) => {\n                                        const parameterName =\n                                            \"firstValue_\" +\n                                            firstColumnValIndex +\n                                            \"_\" +\n                                            secondColumnValIndex +\n                                            \"_\" +\n                                            columnIndex\n                                        parameters[parameterName] =\n                                            ObjectUtils.isObject(firstColumnVal)\n                                                ? column.referencedColumn!.getEntityValue(\n                                                      firstColumnVal,\n                                                  )\n                                                : firstColumnVal\n                                        return `${column.databaseName} = :${parameterName}`\n                                    },\n                                ),\n                                ...junctionMetadata.inverseColumns.map(\n                                    (column, columnIndex) => {\n                                        const parameterName =\n                                            \"secondValue_\" +\n                                            firstColumnValIndex +\n                                            \"_\" +\n                                            secondColumnValIndex +\n                                            \"_\" +\n                                            columnIndex\n                                        parameters[parameterName] =\n                                            ObjectUtils.isObject(\n                                                secondColumnVal,\n                                            )\n                                                ? column.referencedColumn!.getEntityValue(\n                                                      secondColumnVal,\n                                                  )\n                                                : secondColumnVal\n                                        return `${column.databaseName} = :${parameterName}`\n                                    },\n                                ),\n                            ].join(\" AND \")\n                        },\n                    ),\n                )\n            })\n            const condition = conditions\n                .map((str) => \"(\" + str + \")\")\n                .join(\" OR \")\n\n            await this.queryBuilder\n                .createQueryBuilder()\n                .delete()\n                .from(junctionMetadata.tableName)\n                .where(condition)\n                .setParameters(parameters)\n                .execute()\n        }\n    }\n}\n"], "sourceRoot": ".."}