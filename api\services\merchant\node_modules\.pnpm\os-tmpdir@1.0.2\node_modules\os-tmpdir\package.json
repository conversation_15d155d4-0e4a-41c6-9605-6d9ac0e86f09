{"name": "os-tmpdir", "version": "1.0.2", "description": "Node.js os.tmpdir() ponyfill", "license": "MIT", "repository": "sindresorhus/os-tmpdir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["built-in", "core", "ponyfill", "polyfill", "shim", "os", "tmpdir", "tempdir", "tmp", "temp", "dir", "directory", "env", "environment"], "devDependencies": {"ava": "*", "xo": "^0.16.0"}}