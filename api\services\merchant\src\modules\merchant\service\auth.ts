import { Provide } from '@midwayjs/decorator';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository, In } from 'typeorm';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { MerchantSysMenuEntity, MerchantSysRoleEntity, MerchantSysUserEntity } from '../entity/auth';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';

/**
 * 商户权限管理RPC服务
 */
@Provide()
@CoolRpcService({
  entity: MerchantSysUserEntity,
  method: ["add", "delete", "update", "info", "list", "page"],
})
export class MerchantAuthService extends BaseRpcService {
  @InjectEntityModel(MerchantSysMenuEntity)
  merchantSysMenuEntity!: Repository<MerchantSysMenuEntity>;

  @InjectEntityModel(MerchantSysRoleEntity)
  merchantSysRoleEntity!: Repository<MerchantSysRoleEntity>;

  @InjectEntityModel(MerchantSysUserEntity)
  merchantSysUserEntity!: Repository<MerchantSysUserEntity>;

  /**
   * MD5加密
   */
  private md5(str: string): string {
    return crypto.createHash('md5').update(str).digest('hex');
  }

  /**
   * 生成JWT token（与主服务保持一致）
   */
  private generateToken(payload: any, isRefresh: boolean = false): string {
    // 使用与主服务相同的 JWT 配置
    const jwtSecret = process.env.JWT_SECRET || 'cool-admin-xxxxxx';
    const expiresIn = isRefresh ? '15d' : '2h'; // access token 2小时，refresh token 15天

    const tokenPayload = {
      ...payload,
      isRefresh // 标识是否为refresh token
    };

    return jwt.sign(tokenPayload, jwtSecret, { expiresIn });
  }

  /**
   * 用户登录 (严格按照前端 Api.Auth.LoginParams 和 Api.User.UserInfo)
   */
  async login(params: { userName: string; password: string }) {
    const { userName, password } = params;
    const user = await this.merchantSysUserEntity.findOne({
      where: { userName, status: 1 }
    });

    if (!user) {
      throw new Error('用户不存在或已被禁用');
    }

    const encryptPassword = this.md5(password);
    if (user.password !== encryptPassword) {
      throw new Error('用户名或密码错误');
    }

    // 生成JWT token
    const tokenPayload = {
      userId: user.userId,
      userName: user.userName,
      passwordVersion: user.passwordV || 1,
      type: 'merchant' // 标识为商户类型
    };

    const accessToken = this.generateToken(tokenPayload, false);
    const refreshToken = this.generateToken(tokenPayload, true);

    // 返回双token机制
    return {
      token: accessToken,
      refreshToken: refreshToken,
      expire: 2 * 3600, // 2小时（秒）
      refreshExpire: 24 * 3600 * 15 // 15天（秒）
    };
  }

  /**
   * 获取当前用户信息 (严格按照前端 UserService.getUserInfo 机制)
   */
  async getCurrentUserInfo(token: string) {
    // 解析token获取用户ID
    try {
      const jwtSecret = process.env.JWT_SECRET || 'cool-admin-xxxxxx';
      
      // 处理可能的Bearer前缀
      let actualToken = token;
      if (token.startsWith('Bearer ')) {
        actualToken = token.substring(7);
      }
      
      const decoded = jwt.verify(actualToken, jwtSecret) as any;
      const userId = decoded.userId;
      
      const user = await this.merchantSysUserEntity.findOne({
        where: { userId, status: 1 }
      });

      if (!user) {
        throw new Error('用户不存在或已被禁用');
      }

      // 角色格式转换：将后端角色格式转换为前端期望的格式
      let roles: string[] = [];
      if (user.roles) {
        try {
          const parsedRoles = JSON.parse(user.roles);
          roles = parsedRoles.map((role: string) => {
            // 将后端角色转换为前端期望的格式
            switch (role.toLowerCase()) {
              case 'admin':
              case 'super':
                return 'R_SUPER';
              case 'manager':
                return 'R_ADMIN';
              default:
                return role.toUpperCase().startsWith('R_') ? role : `R_${role.toUpperCase()}`;
            }
          });
        } catch (e) {
          roles = [];
        }
      }
      
      // 确保至少有超级管理员权限（admin用户）
      if (user.userName === 'admin' && !roles.includes('R_SUPER')) {
        roles.push('R_SUPER');
      }

      // 严格返回前端期望的 Api.User.UserInfo 格式
      return {
        userId: user.userId,
        userName: user.userName,
        roles: roles,
        buttons: user.buttons ? JSON.parse(user.buttons) : ['add', 'edit', 'delete', 'view'],
        avatar: user.avatar,
        email: user.email,
        phone: user.phone
    };
    } catch (error) {
      console.error('Token解析失败:', error);
      throw new Error('Token无效或已过期');
    }
  }

  /**
   * 获取用户权限菜单
   */
  async getPermMenu(params: { userId: number }) {
    const { userId } = params;
    console.log('🔍 getPermMenu 调试 - 用户ID:', userId);
    
    const user = await this.merchantSysUserEntity.findOne({
      where: { id: userId }
    });

    if (!user) {
      console.error('❌ 用户不存在, userId:', userId);
      throw new Error('用户不存在');
    }
    
    console.log('✅ 找到用户:', {
      id: user.id,
      username: user.username,
      roleIdList: user.roleIdList
    });

    // 临时解决方案：如果是admin用户（ID=1），返回所有菜单
    if (userId === 1) {
      console.log('🔑 Admin用户，返回所有菜单');
      const allMenus = await this.merchantSysMenuEntity.find({
        where: { isShow: true }, // 只显示可见的菜单
        order: { orderNum: 'ASC' }
      });
      
      console.log('📋 查询到的菜单数量:', allMenus.length);
      allMenus.forEach(menu => {
        console.log(`  菜单: ${menu.name} (ID: ${menu.id}, 类型: ${menu.type}, 显示: ${menu.isShow})`);
      });
      
      // 收集权限标识
      const perms: string[] = [];
      allMenus.forEach(menu => {
        if (menu.perms) {
          const menuPerms = menu.perms.split(',').map(p => p.trim()).filter(p => p);
          perms.push(...menuPerms);
        }
      });

      const result = {
        menus: this.buildMenuTree(allMenus),
        perms: [...new Set(perms)]
      };
      
      console.log('🎯 Admin用户最终返回:', {
        menusCount: result.menus.length,
        permsCount: result.perms.length,
        menus: result.menus
      });

      return result;
    }

    let menuIds: number[] = [];
    let perms: string[] = [];

    // 修复：使用 roles 字段而不是 roleIdList 字段
    if (user.roles) {
      let roleIds: number[] = [];
      try {
        roleIds = JSON.parse(user.roles || '[]').map((v: any) => Number(v)).filter((v: any) => !isNaN(v));
      } catch (e) {
        roleIds = [];
      }
      console.log('🔍 解析用户角色ID列表:', roleIds);
      if (!roleIds.length) {
        console.log('❌ 用户没有分配角色，返回空权限');
        return { menus: [], perms: [] };
      }
      const roles = roleIds.length ? await this.merchantSysRoleEntity.find({
        where: { id: In(roleIds) }
      }) : [];
      const allMenuIds = new Set<number>();
      roles.forEach(role => {
        if (role.menuIdList) {
          let menuList: number[] = [];
          try {
            menuList = JSON.parse(role.menuIdList || '[]').map((v: any) => Number(v)).filter((v: any) => !isNaN(v));
          } catch (e) {
            menuList = [];
          }
          menuList.forEach((id: number) => allMenuIds.add(id));
        }
      });
      menuIds = Array.from(allMenuIds);
    }

    if (!menuIds.length) {
      return { menus: [], perms: [] };
    }

    const menus = menuIds.length ? await this.merchantSysMenuEntity.find({
      where: { id: In(menuIds) },
      order: { orderNum: 'ASC' }
    }) : [];

    // 收集权限标识
    menus.forEach(menu => {
      if (menu.perms) {
        // 支持逗号分隔的多个权限
        const menuPerms = menu.perms.split(',').map(p => p.trim()).filter(p => p);
        perms.push(...menuPerms);
      }
    });

    // 返回统一格式：包含 menus 和 perms 字段
    return {
      menus: this.buildMenuTree(menus),
      perms: [...new Set(perms)] // 去重
    };
  }

  /**
   * 构建菜单树
   */
  private buildMenuTree(menus: MerchantSysMenuEntity[], parentId = 0): any[] {
    const result: any[] = [];
    
    menus.forEach(menu => {
      if (menu.parentId === parentId) {
        const children = this.buildMenuTree(menus, menu.id);
        const menuItem = {
          id: menu.id,
          name: menu.name,
          router: menu.router,
          perms: menu.perms,
          type: menu.type,
          icon: menu.icon,
          orderNum: menu.orderNum,
          parentId: menu.parentId,
          keepAlive: menu.keepAlive,
          isShow: menu.isShow,
          children: children.length > 0 ? children : undefined
        };
        result.push(menuItem);
      }
    });

    return result;
  }

  /**
   * 菜单管理 - 列表（平铺结构，用于管理）
   */
  async menuList(params: any = {}) {
    console.log('🔍 [MerchantAuthService] menuList 被调用，参数:', params);
    
    const { name, type } = params;
    const queryBuilder = this.merchantSysMenuEntity.createQueryBuilder('menu');
    
    if (name) {
      queryBuilder.where('menu.name LIKE :name', { name: `%${name}%` });
    }
    
    if (type !== undefined) {
      queryBuilder.andWhere('menu.type = :type', { type });
    }
    
    console.log('📝 SQL查询条件:', { name, type });
    
    const menus = await queryBuilder
      .orderBy('menu.orderNum', 'ASC')
      .addOrderBy('menu.id', 'ASC')
      .getMany();

    console.log('📋 查询到的菜单数量:', menus.length);
    
    if (menus.length === 0) {
      console.log('⚠️ 数据库中没有菜单数据！');
      // 尝试查询表是否存在
      try {
        const count = await this.merchantSysMenuEntity.count();
        console.log('📊 菜单表总记录数:', count);
      } catch (error) {
        console.error('❌ 查询菜单表失败:', error.message);
      }
    } else {
      menus.forEach((menu, index) => {
        console.log(`  ${index + 1}. 菜单: ${menu.name} (ID: ${menu.id}, 类型: ${menu.type})`);
      });
    }

    // 添加父菜单名称
    const result = [];
    for (const menu of menus) {
      const menuWithParent: any = { ...menu };
      if (menu.parentId && menu.parentId > 0) {
        const parent = await this.merchantSysMenuEntity.findOne({ where: { id: menu.parentId } });
        menuWithParent.parentName = parent?.name || '';
      } else {
        menuWithParent.parentName = '';
      }
      result.push(menuWithParent);
    }
    
    console.log('✅ [MerchantAuthService] menuList 返回结果数量:', result.length);
    return result;
  }

  /**
   * 菜单管理 - 树形结构（用于权限分配）
   */
  async menuTree() {
    const menus = await this.merchantSysMenuEntity.find({
      order: { orderNum: 'ASC' }
    });
    return this.buildAppRouteTree(menus);
  }

  /**
   * 菜单管理 - 详情
   */
  async menuInfo(id: number) {
    const menu = await this.merchantSysMenuEntity.findOne({ where: { id } });
    if (!menu) {
      throw new Error('菜单不存在');
    }
    return menu;
  }

  /**
   * 菜单管理 - 添加
   */
  async menuAdd(params: any) {
    console.log('🔧 [商户服务 menuAdd] 接收到的参数:', JSON.stringify(params, null, 2));

    const menuData = {
      parentId: params.parentId || 0,
      name: params.name,
      router: params.router || '',
      component: params.component || '', // 添加组件路径字段
      perms: params.perms || '',
      type: params.type || 0,
      icon: params.icon || '',
      orderNum: params.orderNum || 0,
      keepAlive: params.keepAlive !== false,
      isShow: params.isShow !== false
    };

    console.log('🔧 [商户服务 menuAdd] 处理后的菜单数据:', JSON.stringify(menuData, null, 2));

    const menu = this.merchantSysMenuEntity.create(menuData);
    const result = await this.merchantSysMenuEntity.save(menu);

    console.log('✅ [商户服务 menuAdd] 菜单保存成功:', JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * 菜单管理 - 修改
   */
  async menuUpdate(params: any) {
    console.log('🔧 [menuUpdate] 接收到的参数:', JSON.stringify(params, null, 2));
    
    // 前端发送的数据结构中id就在顶层
    const id = params.id;
    
    // 验证ID参数
    if (!id || (typeof id !== 'number' && typeof id !== 'string')) {
      console.error('❌ [menuUpdate] 无效的ID参数:', id);
      throw new Error('菜单ID不能为空');
    }
    
    // 确保ID是数字类型
    const menuId = typeof id === 'string' ? parseInt(id, 10) : id;
    if (isNaN(menuId)) {
      console.error('❌ [menuUpdate] ID不是有效数字:', id);
      throw new Error('菜单ID必须是有效数字');
    }
    
    console.log('📋 [menuUpdate] 处理菜单ID:', menuId);
    
    // 检查菜单是否存在
    const existMenu = await this.merchantSysMenuEntity.findOne({ where: { id: menuId } });
    if (!existMenu) {
      console.error('❌ [menuUpdate] 菜单不存在, ID:', menuId);
      throw new Error('菜单不存在');
    }
    
    // 构建更新数据，完全按照前端发送的字段结构
    const updateData: any = {};

    if (params.name !== undefined) {
      updateData.name = params.name;
    }
    if (params.router !== undefined) {
      updateData.router = params.router;
    }
    if (params.perms !== undefined) {
      updateData.perms = params.perms;
    }
    if (params.icon !== undefined) {
      updateData.icon = params.icon;
    }
    if (params.keepAlive !== undefined) {
      updateData.keepAlive = params.keepAlive;
    }
    if (params.isShow !== undefined) {
      updateData.isShow = params.isShow;
    }
    if (params.parentId !== undefined) {
      // 确保 parentId 是数字类型，null 或 undefined 转为 0
      updateData.parentId = params.parentId ? Number(params.parentId) : 0;
    }
    
    console.log('📋 [menuUpdate] 构建的更新数据:', JSON.stringify(updateData, null, 2));
    
    // 如果没有需要更新的数据，直接返回现有菜单
    if (Object.keys(updateData).length === 0) {
      console.log('⚠️ [menuUpdate] 没有需要更新的数据，返回现有菜单');
      return existMenu;
    }
    
    // 如果修改父菜单，检查不能设置自己为父菜单
    if (updateData.parentId && updateData.parentId === menuId) {
      throw new Error('不能设置自己为父菜单');
    }
    
    // 如果修改父菜单，检查不能设置子菜单为父菜单
    if (updateData.parentId) {
      const descendants = await this.getMenuDescendants(menuId);
      if (descendants.includes(updateData.parentId)) {
        throw new Error('不能设置子菜单为父菜单');
      }
    }
    
    try {
      console.log('🚀 [menuUpdate] 执行数据库更新...');
      await this.merchantSysMenuEntity.update(menuId, updateData);
      console.log('✅ [menuUpdate] 数据库更新成功');
      
      const updatedMenu = await this.merchantSysMenuEntity.findOne({ where: { id: menuId } });
      console.log('📋 [menuUpdate] 更新后的菜单:', JSON.stringify(updatedMenu, null, 2));
      return updatedMenu;
    } catch (error) {
      console.error('❌ [menuUpdate] 数据库更新失败:', error);
      throw error;
    }
  }

  /**
   * 菜单管理 - 删除
   */
  async menuDelete(ids: number[]) {
    for (const id of ids) {
    // 检查是否有子菜单
    const children = await this.merchantSysMenuEntity.find({ where: { parentId: id } });
    if (children.length > 0) {
        const menu = await this.merchantSysMenuEntity.findOne({ where: { id } });
        throw new Error(`菜单"${menu?.name || id}"下还有子菜单，无法删除`);
      }
      
      // 检查是否有角色在使用此菜单
      const roles = await this.merchantSysRoleEntity
        .createQueryBuilder('role')
        .where('JSON_CONTAINS(role.menuIdList, :menuId)', { menuId: `"${id}"` })
        .getMany();
      
      if (roles.length > 0) {
        const menu = await this.merchantSysMenuEntity.findOne({ where: { id } });
        const roleNames = roles.map(r => r.name).join('、');
        throw new Error(`菜单"${menu?.name || id}"正在被角色"${roleNames}"使用，无法删除`);
      }
    }
    
    await this.merchantSysMenuEntity.delete(ids);
    return true;
  }

  /**
   * 获取菜单的所有后代菜单ID
   */
  private async getMenuDescendants(menuId: number): Promise<number[]> {
    const descendants: number[] = [];
    
    const children = await this.merchantSysMenuEntity.find({ where: { parentId: menuId } });
    for (const child of children) {
      descendants.push(child.id);
      const grandChildren = await this.getMenuDescendants(child.id);
      descendants.push(...grandChildren);
    }
    
    return descendants;
  }

  /**
   * 角色管理 - 分页列表
   */
  async rolePage(params: any) {
    const { page = 1, size = 20, name } = params;
    const queryBuilder = this.merchantSysRoleEntity.createQueryBuilder('role');
    
    if (name) {
      queryBuilder.where('role.name LIKE :name', { name: `%${name}%` });
    }
    
    const [list, total] = await queryBuilder
      .orderBy('role.createTime', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    return {
      list,
      pagination: {
        page,
        size,
        total
      }
    };
  }

  /**
   * 角色管理 - 添加
   */
  async roleAdd(params: any) {
    const roleData: any = {
      name: params.name,
      label: params.label,
      remark: params.remark,
      status: params.status !== undefined ? params.status : 1,  // 修复：处理启用状态
      relevance: params.relevance || 1
    };
    if (params.menuIdList) {
      roleData.menuIdList = JSON.stringify(params.menuIdList);
    }
    if (params.departmentIdList) {
      roleData.departmentIdList = JSON.stringify(params.departmentIdList);
    }
    const role = this.merchantSysRoleEntity.create(roleData);
    return await this.merchantSysRoleEntity.save(role);
  }

  /**
   * 角色管理 - 修改
   */
  async roleUpdate(params: any) {
    console.log('🚀 更新角色，参数:', params);
    
    const updateData: any = {
      name: params.name,
      label: params.label,
      remark: params.remark,
      status: params.status !== undefined ? params.status : 1,  // 修复：处理启用状态
      relevance: params.relevance
    };
    if (params.menuIdList) {
      updateData.menuIdList = JSON.stringify(params.menuIdList);
    }
    if (params.departmentIdList) {
      updateData.departmentIdList = JSON.stringify(params.departmentIdList);
    }
    
    console.log('✅ 更新数据:', updateData);
    await this.merchantSysRoleEntity.update(params.id, updateData);
    const updatedRole = await this.merchantSysRoleEntity.findOne({ where: { id: params.id } });
    console.log('📋 更新后的角色:', updatedRole);
    return updatedRole;
  }

  /**
   * 角色管理 - 删除
   */
  async roleDelete(params: any) {
    // 修复：支持接收对象格式的参数，和 userDelete 保持一致
    let ids: number[];
    if (Array.isArray(params)) {
      // 如果直接传递数组
      ids = params;
    } else if (params && params.ids) {
      // 如果传递的是对象 { ids: [1,2,3] }
      ids = params.ids;
    } else {
      throw new Error('删除角色ID列表不能为空');
    }
    
    console.log('🚀 删除角色，原始参数:', params);
    console.log('🚀 删除角色，解析后的IDs:', ids);
    
    // 参数验证
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error('删除角色ID列表不能为空');
    }
    
    // 确保所有ID都是有效数字
    const validIds = ids.filter(id => id != null && !isNaN(Number(id))).map(id => Number(id));
    if (validIds.length === 0) {
      throw new Error('没有有效的角色ID');
    }
    
    console.log('✅ 有效的角色IDs:', validIds);
    
    // 检查是否有用户关联这些角色
    for (const roleId of validIds) {
      const users = await this.merchantSysUserEntity
        .createQueryBuilder('user')
        .where('JSON_CONTAINS(user.roles, :roleId)', { roleId: `"${roleId}"` })
        .getMany();
      
      if (users.length > 0) {
        const role = await this.merchantSysRoleEntity.findOne({ where: { id: roleId } });
        throw new Error(`角色"${role?.name || roleId}"下还有用户，无法删除`);
      }
    }
    
    // 执行删除操作
    const deleteResult = await this.merchantSysRoleEntity.delete(validIds);
    console.log('✅ 角色删除结果:', deleteResult);
    
    return true;
  }

  /**
   * 用户管理 - 分页列表
   */
  async userPage(params: any) {
    const { page = 1, size = 20, userName, status } = params;
    const queryBuilder = this.merchantSysUserEntity.createQueryBuilder('user');
    
    if (userName) {
      queryBuilder.where('user.userName LIKE :userName', { userName: `%${userName}%` });
    }
    
    if (status !== undefined) {
      queryBuilder.andWhere('user.status = :status', { status });
    }
    
    const [users, total] = await queryBuilder
      .orderBy('user.createTime', 'DESC')
      .skip((page - 1) * size)
      .take(size)
      .getManyAndCount();

    // 转换为前端期望的UserInfo格式
    const list = users.map(user => ({
      id: user.userId,          // 前端期望 id
      userId: user.userId,      // 保持兼容
      username: user.userName,  // 前端期望 username
      userName: user.userName,  // 保持兼容
      nickName: user.userName,  // 使用 userName 作为显示名
      roleIdList: user.roles ? JSON.parse(user.roles) : [], // 前端期望 roleIdList
      roles: user.roles ? JSON.parse(user.roles) : [],
      buttons: user.buttons ? JSON.parse(user.buttons) : [],
      avatar: user.avatar,
      headImg: user.avatar,     // 兼容字段
      email: user.email,
      phone: user.phone,
      remark: (user as any).remark,      // 添加备注字段
      status: user.status,
      createTime: user.createTime,
      updateTime: user.updateTime
    }));

    return {
      list,
      pagination: {
        page,
        size,
        total
      }
    };
  }

  /**
   * 用户管理 - 添加
   */
  async userAdd(params: any) {
    // 检查用户名是否已存在 - 修复字段名
    const existUser = await this.merchantSysUserEntity.findOne({
      where: { userName: params.username }
    });
    if (existUser) {
      throw new Error('用户名已存在');
    }

    // 修复字段映射 - 将前端的username映射为数据库的userName
    const userData: any = {
      userName: params.username,  // 修复：前端username -> 数据库userName
      password: this.md5(params.password),
      passwordV: 1,
      avatar: params.headImg,     // 修复：headImg -> avatar  
      email: params.email,
      phone: params.phone,
      remark: params.remark,      // 添加备注字段
      nickName: params.nickName,  // 添加昵称字段
      status: params.status || 1
    };
    
    // 处理角色权限
    if (params.roleIdList && params.roleIdList.length > 0) {
      userData.roles = JSON.stringify(params.roleIdList);
    }
    
    // 处理按钮权限 - 根据角色自动分配
    if (params.roleIdList && params.roleIdList.length > 0) {
      // 根据角色ID获取对应的权限按钮
      const defaultButtons = ["view", "add", "edit", "delete"];
      userData.buttons = JSON.stringify(defaultButtons);
    }
    
    console.log('🚀 创建用户数据:', userData);
    const user = this.merchantSysUserEntity.create(userData);
    const savedUser = await this.merchantSysUserEntity.save(user);
    console.log('✅ 用户创建成功:', savedUser);
    
    // 返回前端期望的格式 - 关键修复！
    return {
      id: savedUser.userId,        // 前端期望 id，数据库是 userId
      username: savedUser.userName, // 前端期望 username，数据库是 userName
      nickName: savedUser.userName, // 使用 userName 作为显示名
      phone: savedUser.phone,
      email: savedUser.email,
      headImg: savedUser.avatar,
      status: savedUser.status,
      roleIdList: params.roleIdList || [],
      createTime: savedUser.createTime
    };
  }

  /**
   * 用户管理 - 修改
   */
  async userUpdate(params: any) {
    console.log('🚀 更新用户，参数:', params);
    
    // 修复字段映射，确保与实体类字段一致
    const updateData: any = {
      userName: params.username,        // 修复：username -> userName
      nickName: params.nickName,        // 现在支持nickName
      avatar: params.headImg,           // 修复：headImg -> avatar
      email: params.email,
      phone: params.phone,
      remark: params.remark,            // 现在支持remark
      status: params.status,
      roles: params.roleIdList ? JSON.stringify(params.roleIdList) : null  // 修复：roleIdList -> roles
    };
    
    // 移除 undefined 的字段
    Object.keys(updateData).forEach(key => {
      if (updateData[key] === undefined) {
        delete updateData[key];
      }
    });
    
    console.log('✅ 更新数据:', updateData);
    
    // 修复：使用正确的主键字段 userId
    await this.merchantSysUserEntity.update({ userId: params.id }, updateData);
    
    // 修复：查询时也使用正确的主键字段
    const updatedUser = await this.merchantSysUserEntity.findOne({ where: { userId: params.id } });
    console.log('📋 更新后的用户:', updatedUser);
    
    // 返回前端期望的格式
    if (updatedUser) {
      return {
        id: updatedUser.userId,          // 前端期望 id
        userId: updatedUser.userId,
        username: updatedUser.userName,  // 前端期望 username
        userName: updatedUser.userName,
        nickName: params.nickName || updatedUser.userName,  // 使用传入的nickName或userName
        roleIdList: updatedUser.roles ? JSON.parse(updatedUser.roles) : [],
        roles: updatedUser.roles ? JSON.parse(updatedUser.roles) : [],
        avatar: updatedUser.avatar,
        headImg: updatedUser.avatar,
        email: updatedUser.email,
        phone: updatedUser.phone,
        status: updatedUser.status,
        remark: params.remark,           // 使用传入的remark
        createTime: updatedUser.createTime,
        updateTime: updatedUser.updateTime
      };
    }
    
    return null;
  }

  /**
   * 用户管理 - 删除
   */
  async userDelete(params: any) {
    // 修复：支持接收对象格式的参数
    let ids: number[];
    if (Array.isArray(params)) {
      // 如果直接传递数组
      ids = params;
    } else if (params && params.ids) {
      // 如果传递的是对象 { ids: [1,2,3] }
      ids = params.ids;
    } else {
      throw new Error('删除用户ID列表不能为空');
    }
    
    console.log('🚀 删除用户，原始参数:', params);
    console.log('🚀 删除用户，解析后的IDs:', ids);
    
    // 参数验证
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      throw new Error('删除用户ID列表不能为空');
    }
    
    // 确保所有ID都是有效数字
    const validIds = ids.filter(id => id != null && !isNaN(Number(id))).map(id => Number(id));
    if (validIds.length === 0) {
      throw new Error('没有有效的用户ID');
    }
    
    console.log('✅ 有效的用户IDs:', validIds);
    
    // 检查是否有用户存在
    const existingUsers = await this.merchantSysUserEntity.find({
      where: { userId: In(validIds) }
    });
    
    console.log('📋 找到的用户:', existingUsers.length);
    
    if (existingUsers.length === 0) {
      throw new Error('要删除的用户不存在');
    }
    
    // 检查是否包含admin用户（假设userId=1是admin）
    if (validIds.includes(1)) {
      throw new Error('不能删除管理员用户');
    }
    
    // 使用正确的主键条件删除
    const deleteResult = await this.merchantSysUserEntity.delete({ userId: In(validIds) });
    console.log('✅ 删除结果:', deleteResult);
    
    return true;
  }

  /**
   * 获取所有角色列表
   */
  async roleList() {
    return await this.merchantSysRoleEntity.find({
      order: { createTime: 'DESC' }
    });
  }

  /**
   * 用户详情
   */
  async userInfo(id: number) {
    // 修复：使用正确的主键字段 userId
    const user = await this.merchantSysUserEntity.findOne({ where: { userId: id } });
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 获取用户角色信息
    let roleDetails: any[] = [];
    if (user.roles) {  // 修复：使用正确的字段名 roles
      try {
        const roleIds = JSON.parse(user.roles || '[]').map((v: any) => Number(v)).filter((v: any) => !isNaN(v));
        if (roleIds.length > 0) {
          roleDetails = await this.merchantSysRoleEntity.find({
            where: { id: In(roleIds) },
            select: ['id', 'name', 'label']
          });
        }
      } catch (e) {
        roleDetails = [];
      }
    }
    
    // 不返回密码等敏感信息，并转换为前端期望的格式
    const { password, passwordV, ...userInfo } = user;
    return {
      ...userInfo,
      id: user.userId,           // 前端期望 id
      username: user.userName,   // 前端期望 username
      roleIdList: user.roles ? JSON.parse(user.roles || '[]') : [], // 前端期望 roleIdList
      roles: roleDetails
    };
  }

  /**
   * 用户管理 - 重置密码
   */
  async userResetPassword(params: { id: number; password: string }) {
    console.log('🚀 重置用户密码，参数:', params);
    
    const { id, password } = params;
    
    // 检查用户是否存在
    const user = await this.merchantSysUserEntity.findOne({ where: { userId: id } });
    if (!user) {
      throw new Error('用户不存在');
    }
    
    // 更新密码
    const updateData = {
      password: this.md5(password),
      passwordV: user.passwordV + 1 // 增加密码版本号
    };
    
    await this.merchantSysUserEntity.update({ userId: id }, updateData);
    console.log('✅ 用户密码重置成功');
    
    return true;
  }

  /**
   * 角色详情
   */
  async roleInfo(id: number) {
    console.log('🚀 获取角色详情，ID:', id);
    
    const role = await this.merchantSysRoleEntity.findOne({ where: { id } });
    if (!role) {
      throw new Error('角色不存在');
    }
    
    console.log('✅ 角色详情数据:', role);
    
    // 解析菜单ID列表
    let menuIds: number[] = [];
    if (role.menuIdList) {
      try {
        menuIds = JSON.parse(role.menuIdList || '[]').map((v: any) => Number(v)).filter((v: any) => !isNaN(v));
      } catch (e) {
        menuIds = [];
      }
    }
    
    return {
      ...role,
      menuIds
    };
  }

  /**
   * 更新角色权限
   */
  async roleUpdatePerms(params: { id: number; menuIdList: number[] }) {
    const { id, menuIdList } = params;
    const updateData = {
      menuIdList: JSON.stringify(menuIdList || [])
    };
    await this.merchantSysRoleEntity.update(id, updateData);
    return await this.merchantSysRoleEntity.findOne({ where: { id } });
  }

  /**
   * 用户状态切换
   */
  async userToggleStatus(params: { id: number; status: number }) {
    const { id, status } = params;
    await this.merchantSysUserEntity.update(id, { status });
    return true;
  }

  /**
   * 批量删除用户
   */
  async userBatchDelete(ids: number[]) {
    await this.merchantSysUserEntity.delete(ids);
    return true;
  }

  /**
   * 批量删除角色
   */
  async roleBatchDelete(ids: number[]) {
    // 检查是否有用户关联这些角色
    for (const roleId of ids) {
      const users = await this.merchantSysUserEntity
        .createQueryBuilder('user')
        .where('JSON_CONTAINS(user.roleIdList, :roleId)', { roleId: `"${roleId}"` })
        .getMany();
      
      if (users.length > 0) {
        const role = await this.merchantSysRoleEntity.findOne({ where: { id: roleId } });
        throw new Error(`角色"${role?.name || roleId}"下还有用户，无法删除`);
      }
    }
    
    await this.merchantSysRoleEntity.delete(ids);
    return true;
  }

  /**
   * 将数据库菜单实体转换为前端期望的AppRouteRecord格式
   */
  private transformMenuToAppRouteRecord(menu: MerchantSysMenuEntity): any {
    // 处理权限列表：将字符串转换为数组格式
    let authList: Array<{ title: string; auth_mark: string }> = [];
    if (menu.perms) {
      // 支持逗号分隔的权限标识
      const permsArray = menu.perms.split(',').map(p => p.trim()).filter(p => p);
      
      // 权限标识映射为友好的中文名称
      const permissionNameMap: { [key: string]: string } = {
        'add': '新增',
        'edit': '编辑',
        'delete': '删除',
        'view': '查看',
        'export': '导出',
        'import': '导入',
        'audit': '审核'
      };
      
      authList = permsArray.map(perm => ({
        title: permissionNameMap[perm] || perm, // 使用映射的中文名称，如果没有映射则使用原始值
        auth_mark: perm
      }));
    }

    // 生成组件路径：优先使用数据库中的 component 字段
    let component = '';

    if (menu.component) {
      // 如果数据库中有 component 字段，直接使用（这是 RoutesAlias 的 key）
      component = menu.component;
      console.log(`🔧 使用数据库 component 字段: ${menu.name} -> ${component}`);
    } else if (menu.router) {
      // 如果没有 component 字段，按照原版 art-design-pro_02 的标准格式生成
      const routerPath = menu.router.startsWith('/') ? menu.router : `/${menu.router}`;

      if (!routerPath || routerPath === '/') {
        component = '/index/index';
      } else {
        // 判断是否为顶级路由（没有父级菜单）
        const isTopLevel = !menu.parentId || menu.parentId === 0;

        if (isTopLevel) {
          // 顶级路由统一使用布局容器（与原版 asyncRoutes 保持一致）
          component = '/index/index';
        } else {
          // 子路由使用完整的路径作为组件路径
          // 例如：/dashboard/console -> /dashboard/console
          component = routerPath;
        }
      }
      console.log(`🔧 自动生成 component 路径: ${menu.name} -> ${component}`);
    }

    return {
      id: menu.id,
      name: this.generateRouteName(menu.router, menu.name), // 生成路由名称
      path: menu.router || '',
      component: component,
      parentId: menu.parentId, // 添加 parentId 字段
      // 添加时间字段
      createTime: menu.createTime,
      updateTime: menu.updateTime,
      meta: {
        title: menu.name,
        icon: menu.icon || '',
        keepAlive: menu.keepAlive,
        isHide: !menu.isShow, // 注意：逻辑相反
        isMenu: menu.type === 1, // 根据后端 type 字段设置：0=目录(false)，1=菜单(true)
        authList: authList,
        roles: [] // 角色权限可以根据需要从其他地方获取
      }
    };
  }

  /**
   * 生成路由名称（驼峰命名）
   */
  private generateRouteName(router: string, fallbackName: string): string {
    if (!router) return fallbackName;
    
    // 移除开头的斜杠，按斜杠分割
    const parts = router.replace(/^\//, '').split('/').filter(p => p);
    
    if (parts.length === 0) return fallbackName;
    
    // 转换为驼峰命名
    return parts.map((part, index) => {
      // 首字母大写
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    }).join('');
  }

  /**
   * 构建符合前端格式的菜单树
   */
  private buildAppRouteTree(menus: MerchantSysMenuEntity[], parentId = 0): any[] {
    const result: any[] = [];
    
    menus.forEach(menu => {
      if (menu.parentId === parentId && menu.type !== 2) { // 按钮类型(type=2)不作为独立菜单项
        const children = this.buildAppRouteTree(menus, menu.id);
        const menuItem = this.transformMenuToAppRouteRecord(menu);
        
        // 查找当前菜单的按钮权限（type=2的子菜单）
        const buttonPermissions = menus.filter(m => m.parentId === menu.id && m.type === 2);
        if (buttonPermissions.length > 0) {
          // 将按钮权限添加到authList
          const authList = buttonPermissions.map(btn => ({
            title: btn.name,
            auth_mark: btn.perms || btn.name.toLowerCase()
          }));
          
          // 合并已有的authList
          if (menuItem.meta.authList && menuItem.meta.authList.length > 0) {
            menuItem.meta.authList = [...menuItem.meta.authList, ...authList];
          } else {
            menuItem.meta.authList = authList;
          }
        }
        
        // 只有当有子菜单时才添加children属性
        if (children.length > 0) {
          menuItem.children = children;
        }
        
        result.push(menuItem);
      }
    });

    return result;
  }

  /**
   * 刷新token
   */
  async refreshToken(refreshToken: string) {
    try {
      const jwtSecret = process.env.JWT_SECRET || 'cool-admin-xxxxxx';

      // 处理可能的Bearer前缀
      let actualToken = refreshToken;
      if (refreshToken.startsWith('Bearer ')) {
        actualToken = refreshToken.substring(7);
      }

      const decoded = jwt.verify(actualToken, jwtSecret) as any;

      // 验证是否为refresh token
      if (!decoded.isRefresh) {
        throw new Error('无效的refresh token');
      }

      // 验证用户是否仍然有效
      const user = await this.merchantSysUserEntity.findOne({
        where: { userId: decoded.userId, status: 1 }
      });

      if (!user) {
        throw new Error('用户不存在或已被禁用');
      }

      // 验证密码版本（如果用户修改了密码，旧token应该失效）
      if (decoded.passwordVersion !== (user.passwordV || 1)) {
        throw new Error('token已失效，请重新登录');
      }

      // 生成新的token对
      const tokenPayload = {
        userId: user.userId,
        userName: user.userName,
        passwordVersion: user.passwordV || 1,
        type: 'merchant'
      };

      const newAccessToken = this.generateToken(tokenPayload, false);
      const newRefreshToken = this.generateToken(tokenPayload, true);

      return {
        token: newAccessToken,
        refreshToken: newRefreshToken,
        expire: 2 * 3600, // 2小时（秒）
        refreshExpire: 24 * 3600 * 15 // 15天（秒）
      };
    } catch (error: any) {
      console.error('❌ [MerchantAuthService] 刷新token失败:', error.message);
      throw new Error('刷新token失败，请重新登录');
    }
  }
}