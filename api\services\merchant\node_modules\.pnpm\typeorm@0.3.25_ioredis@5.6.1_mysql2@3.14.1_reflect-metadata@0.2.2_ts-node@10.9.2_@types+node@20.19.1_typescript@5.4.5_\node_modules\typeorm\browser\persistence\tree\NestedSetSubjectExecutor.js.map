{"version": 3, "sources": ["../browser/src/persistence/tree/NestedSetSubjectExecutor.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AAInF,MAAM,YAAY;CAGjB;AAED;;GAEG;AACH,MAAM,OAAO,wBAAwB;IACjC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,WAAwB;QAAxB,gBAAW,GAAX,WAAW,CAAa;IAAG,CAAC;IAElD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QAC/D,MAAM,cAAc,GAAG,MAAM,CACzB,OAAO,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY,CACrD,CAAA;QACD,MAAM,eAAe,GAAG,MAAM,CAC1B,OAAO,CAAC,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CACtD,CAAA;QAED,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC5D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YAChE,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBAC3C,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,gBAAgB;gBACxC,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QACtC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAExD,IAAI,aAAa,GAAuB,SAAS,CAAA;QACjD,IAAI,QAAQ,EAAE,CAAC;YACX,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO;iBACzC,kBAAkB,EAAE;iBACpB,MAAM,CACH,OAAO,CAAC,QAAQ,CAAC,UAAU;gBACvB,GAAG;gBACH,OAAO,CAAC,QAAQ,CAAC,oBAAqB,CAAC,YAAY,EACvD,OAAO,CACV;iBACA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;iBAC1D,UAAU,CAAC,QAAQ,CAAC;iBACpB,SAAS,EAAE;iBACX,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACb,MAAM,KAAK,GAAQ,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;gBACvD,8CAA8C;gBAC9C,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;YAC9D,CAAC,CAAC,CAAA;QACV,CAAC;QAED,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,UAAU,SAAS,OAAO;gBACtB,GAAG,cAAc,gBAAgB,cAAc,MAAM,aAAa,SAAS,cAAc,aAAa,cAAc,OAAO;gBAC3H,GAAG,eAAe,MAAM,eAAe,OAAO;gBAC9C,SAAS,eAAe,OAAO,aAAa,EAAE,CACrD,CAAA;YAED,QAAQ,CAAC,SAAS,CACd,OAAO,CAAC,gBAAgB,EACxB,OAAO,CAAC,QAAQ,CAAC,mBAAoB,CAAC,cAAc,CAChD,aAAa,CAChB,EACD,OAAO,CAAC,QAAQ,CAAC,oBAAqB,CAAC,cAAc,CACjD,aAAa,GAAG,CAAC,CACpB,CACJ,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAEnE,iEAAiE;YACjE,IAAI,CAAC,YAAY;gBAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;YAEzD,QAAQ,CAAC,SAAS,CACd,OAAO,CAAC,gBAAgB,EACxB,OAAO,CAAC,QAAQ,CAAC,mBAAoB,CAAC,cAAc,CAAC,CAAC,CAAC,EACvD,OAAO,CAAC,QAAQ,CAAC,oBAAqB,CAAC,cAAc,CAAC,CAAC,CAAC,CAC3D,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,OAAgB;QACzB,IAAI,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CAC5D,OAAO,CAAC,MAAO,CAClB,CAAA,CAAC,oCAAoC;QACtC,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM;YAChE,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,aAAa,CAAC,MAAM,CAAA;QAEzC,IAAI,MAAM,GAAG,OAAO,CAAC,cAAc,CAAA,CAAC,oCAAoC;QACxE,IAAI,CAAC,MAAM,IAAI,MAAM;YACjB,sCAAsC;YACtC,MAAM,GAAG,OAAO,CAAC,QAAQ;iBACpB,oBAAqB,CAAC,cAAc,CAAC,MAAM,CAAC;iBAC5C,IAAI,CAAC,CAAC,KAAU,EAAE,EAAE;gBACjB,OAAO,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAW,CAAC,CAAC,KAAK,CAC5C,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,CACzC,CAAA;YACL,CAAC,CAAC,CAAA;QAEV,mDAAmD;QACnD,IAAI,MAAM,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC/C,OAAM;QACV,CAAC;QAED,MAAM,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC,kBAAmB,CAAC,cAAc,CACjE,MAAO,CACV,CAAA;QACD,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAA;QAC9D,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAExD,+CAA+C;QAC/C,IAAI,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;YAC7C,OAAM;QACV,CAAC;QAED,IAAI,MAAM,EAAE,CAAC;YACT,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAC/D,MAAM,cAAc,GAAG,MAAM,CACzB,OAAO,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY,CACrD,CAAA;YACD,MAAM,eAAe,GAAG,MAAM,CAC1B,OAAO,CAAC,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CACtD,CAAA;YAED,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAExD,IAAI,QAAQ,GAA6B,SAAS,CAAA;YAClD,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,GAAG,CACP,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACzD,CAAC,CAAC,CAAC,CAAA;YACR,CAAC;YAED,IAAI,QAAQ,GAA6B,SAAS,CAAA;YAClD,IAAI,QAAQ,EAAE,CAAC;gBACX,QAAQ,GAAG,CACP,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACzD,CAAC,CAAC,CAAC,CAAA;YACR,CAAC;YAED,IAAI,QAAQ,KAAK,SAAS,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;gBACnD,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;gBAChD,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,GAAG,CAAC,CAAA;gBAEnD,IAAI,UAAkB,CAAA;gBACtB,IAAI,UAAU,EAAE,CAAC;oBACb,UAAU,GAAG,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAA;gBAC/C,CAAC;qBAAM,CAAC;oBACJ,UAAU,GAAG,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAA;gBAC/C,CAAC;gBAED,qBAAqB;gBACrB,MAAM,cAAc,GAChB,QAAQ,cAAc,OAAO,QAAQ,CAAC,IAAI,OAAO;oBACjD,GAAG,cAAc,MAAM,QAAQ,CAAC,KAAK,GAAG;oBACxC,QAAQ,cAAc,MAAM,UAAU,GAAG,CAAA;gBAE7C,MAAM,eAAe,GACjB,QAAQ,eAAe,MAAM,QAAQ,CAAC,IAAI,OAAO;oBACjD,GAAG,eAAe,OAAO,QAAQ,CAAC,KAAK,GAAG;oBAC1C,QAAQ,eAAe,MAAM,UAAU,GAAG,CAAA;gBAE9C,kCAAkC;gBAClC,IAAI,UAAU,EAAE,CAAC;oBACb,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,UAAU,SAAS,GAAG;wBAClB,OAAO,cAAc,UAAU;wBAC/B,QAAQ,cAAc,MAAM,QAAQ,CAAC,KAAK,OAAO;wBACjD,GAAG,cAAc,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACxC,QAAQ,cAAc,MAAM,QAAQ,GAAG;wBACvC,cAAc;wBACd,QAAQ,cAAc,GAAG;wBACzB,OAAO;wBACP,GAAG,eAAe,UAAU;wBAC5B,QAAQ,eAAe,MAAM,QAAQ,CAAC,KAAK,OAAO;wBAClD,GAAG,eAAe,MAAM,QAAQ,CAAC,IAAI,GAAG;wBACxC,QAAQ,eAAe,MAAM,QAAQ,GAAG;wBACxC,eAAe;wBACf,QAAQ,eAAe,GAAG;wBAC1B,KAAK,CACZ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,UAAU,SAAS,GAAG;wBAClB,OAAO,cAAc,UAAU;wBAC/B,QAAQ,cAAc,MAAM,QAAQ,CAAC,IAAI,OAAO;wBAChD,GAAG,cAAc,MAAM,QAAQ,CAAC,KAAK,GAAG;wBACxC,QAAQ,cAAc,MAAM,QAAQ,GAAG;wBACvC,cAAc;wBACd,QAAQ,cAAc,GAAG;wBACzB,OAAO;wBACP,GAAG,eAAe,UAAU;wBAC5B,QAAQ,eAAe,MAAM,QAAQ,CAAC,IAAI,OAAO;wBACjD,GAAG,eAAe,OAAO,QAAQ,CAAC,KAAK,GAAG;wBAC1C,QAAQ,eAAe,MAAM,QAAQ,GAAG;wBACxC,eAAe;wBACf,QAAQ,eAAe,GAAG;wBAC1B,KAAK,CACZ,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;YAEnE,iEAAiE;YACjE,IAAI,CAAC,YAAY;gBAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CAAC,QAA6B;QACtC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;YAAE,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAA;QAEnD,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAA;QAErC,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QACvD,MAAM,cAAc,GAAG,MAAM,CACzB,QAAQ,CAAC,mBAAoB,CAAC,YAAY,CAC7C,CAAA;QACD,MAAM,eAAe,GAAG,MAAM,CAC1B,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CAC9C,CAAA;QAED,MAAM,WAAW,GAAoB,EAAE,CAAA;QACvC,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,QAAQ,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAExD,IAAI,QAAQ,EAAE,CAAC;gBACX,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YAC9B,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAEpE,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;YAC9B,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,GAAG,CAAC,CAAA;YAE/C,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACxB,UAAU,SAAS,GAAG;gBAClB,OAAO,cAAc,UAAU;gBAC/B,QAAQ,cAAc,MAAM,MAAM,CAAC,IAAI,SAAS,cAAc,MAAM,QAAQ,GAAG;gBAC/E,QAAQ,cAAc,GAAG;gBACzB,OAAO;gBACP,GAAG,eAAe,UAAU;gBAC5B,QAAQ,eAAe,MAAM,MAAM,CAAC,KAAK,SAAS,eAAe,MAAM,QAAQ,GAAG;gBAClF,QAAQ,eAAe,GAAG;gBAC1B,KAAK,CACZ,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,eAAe,CACrB,QAAwB,EACxB,GAAoC;QAEpC,MAAM,MAAM,GAAG;YACX,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,IACxB,QAAQ,CAAC,mBAAoB,CAAC,YAClC,EAAE;YACF,KAAK,EAAE,GAAG,QAAQ,CAAC,UAAU,IACzB,QAAQ,CAAC,oBAAqB,CAAC,YACnC,EAAE;SACL,CAAA;QAED,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAA;QAElE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC5C,YAAY,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QAEF,OAAO,YAAY;aACd,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;aAC1C,UAAU,CAAC,GAAG,CAAC;aACf,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC;aAC7B,UAAU,EAAE;aACZ,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE;YACd,MAAM,IAAI,GAAmB,EAAE,CAAA;YAE/B,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,MAAM,KAAK,GAAQ,EAAE,CAAA;gBACrB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;oBACpC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;oBAE9C,8CAA8C;oBAC9C,KAAK,CAAC,GAAG,CAAC;wBACN,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;gBAC3D,CAAC;gBACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YACpB,CAAC;YAED,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACV,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAC5B,OAAgB,EAChB,MAAW;QAEX,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;QAC/D,MAAM,UAAU,GAAU,EAAE,CAAA;QAC5B,MAAM,cAAc,GAAG,OAAO,CAAC,QAAQ;aAClC,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YAC5C,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAA;YAC9C,MAAM,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAE/C,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACpB,OAAO,GAAG,UAAU,UAAU,CAAA;YAClC,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC1B,MAAM,aAAa,GACf,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe,CAC9C,SAAS,GAAG,MAAM,CAAC,YAAY,EAC/B,UAAU,CAAC,MAAM,GAAG,CAAC,CACxB,CAAA;YACL,OAAO,GAAG,UAAU,MAAM,aAAa,EAAE,CAAA;QAC7C,CAAC,CAAC;aACD,IAAI,CAAC,OAAO,CAAC,CAAA;QAElB,MAAM,UAAU,GAAG,OAAO,CAAA;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,CACvC,sBAAsB,MAAM,CACxB,UAAU,CACb,SAAS,SAAS,UAAU,cAAc,EAAE,EAC7C,UAAU,EACV,IAAI,CACP,CAAA;QAED,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,CAAA;IACxD,CAAC;IAED;;;OAGG;IACO,YAAY,CAAC,SAAiB;QACpC,OAAO,SAAS;aACX,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACP,sMAAsM;YACtM,OAAO,CAAC,KAAK,EAAE;gBACX,CAAC,CAAC,CAAC;gBACH,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QACtD,CAAC,CAAC;aACD,IAAI,CAAC,GAAG,CAAC,CAAA;IAClB,CAAC;CACJ", "file": "NestedSetSubjectExecutor.js", "sourcesContent": ["import { Subject } from \"../Subject\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { NestedSetMultipleRootError } from \"../../error/NestedSetMultipleRootError\"\nimport { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { EntityMetadata } from \"../../metadata/EntityMetadata\"\n\nclass NestedSetIds {\n    left: number\n    right: number\n}\n\n/**\n * Executes subject operations for nested set tree entities.\n */\nexport class NestedSetSubjectExecutor {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected queryRunner: QueryRunner) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Executes operations when subject is being inserted.\n     */\n    async insert(subject: Subject): Promise<void> {\n        const escape = (alias: string) =>\n            this.queryRunner.connection.driver.escape(alias)\n        const tableName = this.getTableName(subject.metadata.tablePath)\n        const leftColumnName = escape(\n            subject.metadata.nestedSetLeftColumn!.databaseName,\n        )\n        const rightColumnName = escape(\n            subject.metadata.nestedSetRightColumn!.databaseName,\n        )\n\n        let parent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!parent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            parent = subject.parentSubject.insertedValueSet\n                ? subject.parentSubject.insertedValueSet\n                : subject.parentSubject.entity\n        const parentId = subject.metadata.getEntityIdMap(parent)\n\n        let parentNsRight: number | undefined = undefined\n        if (parentId) {\n            parentNsRight = await this.queryRunner.manager\n                .createQueryBuilder()\n                .select(\n                    subject.metadata.targetName +\n                        \".\" +\n                        subject.metadata.nestedSetRightColumn!.propertyPath,\n                    \"right\",\n                )\n                .from(subject.metadata.target, subject.metadata.targetName)\n                .whereInIds(parentId)\n                .getRawOne()\n                .then((result) => {\n                    const value: any = result ? result[\"right\"] : undefined\n                    // CockroachDB returns numeric types as string\n                    return typeof value === \"string\" ? parseInt(value) : value\n                })\n        }\n\n        if (parentNsRight !== undefined) {\n            await this.queryRunner.query(\n                `UPDATE ${tableName} SET ` +\n                    `${leftColumnName} = CASE WHEN ${leftColumnName} > ${parentNsRight} THEN ${leftColumnName} + 2 ELSE ${leftColumnName} END,` +\n                    `${rightColumnName} = ${rightColumnName} + 2 ` +\n                    `WHERE ${rightColumnName} >= ${parentNsRight}`,\n            )\n\n            OrmUtils.mergeDeep(\n                subject.insertedValueSet,\n                subject.metadata.nestedSetLeftColumn!.createValueMap(\n                    parentNsRight,\n                ),\n                subject.metadata.nestedSetRightColumn!.createValueMap(\n                    parentNsRight + 1,\n                ),\n            )\n        } else {\n            const isUniqueRoot = await this.isUniqueRootEntity(subject, parent)\n\n            // Validate if a root entity already exits and throw an exception\n            if (!isUniqueRoot) throw new NestedSetMultipleRootError()\n\n            OrmUtils.mergeDeep(\n                subject.insertedValueSet,\n                subject.metadata.nestedSetLeftColumn!.createValueMap(1),\n                subject.metadata.nestedSetRightColumn!.createValueMap(2),\n            )\n        }\n    }\n\n    /**\n     * Executes operations when subject is being updated.\n     */\n    async update(subject: Subject): Promise<void> {\n        let parent = subject.metadata.treeParentRelation!.getEntityValue(\n            subject.entity!,\n        ) // if entity was attached via parent\n        if (!parent && subject.parentSubject && subject.parentSubject.entity)\n            // if entity was attached via children\n            parent = subject.parentSubject.entity\n\n        let entity = subject.databaseEntity // if entity was attached via parent\n        if (!entity && parent)\n            // if entity was attached via children\n            entity = subject.metadata\n                .treeChildrenRelation!.getEntityValue(parent)\n                .find((child: any) => {\n                    return Object.entries(subject.identifier!).every(\n                        ([key, value]) => child[key] === value,\n                    )\n                })\n\n        // Exit if the parent or the entity where never set\n        if (entity === undefined || parent === undefined) {\n            return\n        }\n\n        const oldParent = subject.metadata.treeParentRelation!.getEntityValue(\n            entity!,\n        )\n        const oldParentId = subject.metadata.getEntityIdMap(oldParent)\n        const parentId = subject.metadata.getEntityIdMap(parent)\n\n        // Exit if the new and old parents are the same\n        if (OrmUtils.compareIds(oldParentId, parentId)) {\n            return\n        }\n\n        if (parent) {\n            const escape = (alias: string) =>\n                this.queryRunner.connection.driver.escape(alias)\n            const tableName = this.getTableName(subject.metadata.tablePath)\n            const leftColumnName = escape(\n                subject.metadata.nestedSetLeftColumn!.databaseName,\n            )\n            const rightColumnName = escape(\n                subject.metadata.nestedSetRightColumn!.databaseName,\n            )\n\n            const entityId = subject.metadata.getEntityIdMap(entity)\n\n            let entityNs: NestedSetIds | undefined = undefined\n            if (entityId) {\n                entityNs = (\n                    await this.getNestedSetIds(subject.metadata, entityId)\n                )[0]\n            }\n\n            let parentNs: NestedSetIds | undefined = undefined\n            if (parentId) {\n                parentNs = (\n                    await this.getNestedSetIds(subject.metadata, parentId)\n                )[0]\n            }\n\n            if (entityNs !== undefined && parentNs !== undefined) {\n                const isMovingUp = parentNs.left > entityNs.left\n                const treeSize = entityNs.right - entityNs.left + 1\n\n                let entitySize: number\n                if (isMovingUp) {\n                    entitySize = parentNs.left - entityNs.right\n                } else {\n                    entitySize = parentNs.right - entityNs.left\n                }\n\n                // Moved entity logic\n                const updateLeftSide =\n                    `WHEN ${leftColumnName} >= ${entityNs.left} AND ` +\n                    `${leftColumnName} < ${entityNs.right} ` +\n                    `THEN ${leftColumnName} + ${entitySize} `\n\n                const updateRightSide =\n                    `WHEN ${rightColumnName} > ${entityNs.left} AND ` +\n                    `${rightColumnName} <= ${entityNs.right} ` +\n                    `THEN ${rightColumnName} + ${entitySize} `\n\n                // Update the surrounding entities\n                if (isMovingUp) {\n                    await this.queryRunner.query(\n                        `UPDATE ${tableName} ` +\n                            `SET ${leftColumnName} = CASE ` +\n                            `WHEN ${leftColumnName} > ${entityNs.right} AND ` +\n                            `${leftColumnName} <= ${parentNs.left} ` +\n                            `THEN ${leftColumnName} - ${treeSize} ` +\n                            updateLeftSide +\n                            `ELSE ${leftColumnName} ` +\n                            `END, ` +\n                            `${rightColumnName} = CASE ` +\n                            `WHEN ${rightColumnName} > ${entityNs.right} AND ` +\n                            `${rightColumnName} < ${parentNs.left} ` +\n                            `THEN ${rightColumnName} - ${treeSize} ` +\n                            updateRightSide +\n                            `ELSE ${rightColumnName} ` +\n                            `END`,\n                    )\n                } else {\n                    await this.queryRunner.query(\n                        `UPDATE ${tableName} ` +\n                            `SET ${leftColumnName} = CASE ` +\n                            `WHEN ${leftColumnName} < ${entityNs.left} AND ` +\n                            `${leftColumnName} > ${parentNs.right} ` +\n                            `THEN ${leftColumnName} + ${treeSize} ` +\n                            updateLeftSide +\n                            `ELSE ${leftColumnName} ` +\n                            `END, ` +\n                            `${rightColumnName} = CASE ` +\n                            `WHEN ${rightColumnName} < ${entityNs.left} AND ` +\n                            `${rightColumnName} >= ${parentNs.right} ` +\n                            `THEN ${rightColumnName} + ${treeSize} ` +\n                            updateRightSide +\n                            `ELSE ${rightColumnName} ` +\n                            `END`,\n                    )\n                }\n            }\n        } else {\n            const isUniqueRoot = await this.isUniqueRootEntity(subject, parent)\n\n            // Validate if a root entity already exits and throw an exception\n            if (!isUniqueRoot) throw new NestedSetMultipleRootError()\n        }\n    }\n\n    /**\n     * Executes operations when subject is being removed.\n     */\n    async remove(subjects: Subject | Subject[]): Promise<void> {\n        if (!Array.isArray(subjects)) subjects = [subjects]\n\n        const metadata = subjects[0].metadata\n\n        const escape = (alias: string) =>\n            this.queryRunner.connection.driver.escape(alias)\n        const tableName = this.getTableName(metadata.tablePath)\n        const leftColumnName = escape(\n            metadata.nestedSetLeftColumn!.databaseName,\n        )\n        const rightColumnName = escape(\n            metadata.nestedSetRightColumn!.databaseName,\n        )\n\n        const entitiesIds: ObjectLiteral[] = []\n        for (const subject of subjects) {\n            const entityId = metadata.getEntityIdMap(subject.entity)\n\n            if (entityId) {\n                entitiesIds.push(entityId)\n            }\n        }\n\n        const entitiesNs = await this.getNestedSetIds(metadata, entitiesIds)\n\n        for (const entity of entitiesNs) {\n            const treeSize = entity.right - entity.left + 1\n\n            await this.queryRunner.query(\n                `UPDATE ${tableName} ` +\n                    `SET ${leftColumnName} = CASE ` +\n                    `WHEN ${leftColumnName} > ${entity.left} THEN ${leftColumnName} - ${treeSize} ` +\n                    `ELSE ${leftColumnName} ` +\n                    `END, ` +\n                    `${rightColumnName} = CASE ` +\n                    `WHEN ${rightColumnName} > ${entity.right} THEN ${rightColumnName} - ${treeSize} ` +\n                    `ELSE ${rightColumnName} ` +\n                    `END`,\n            )\n        }\n    }\n\n    /**\n     * Get the nested set ids for a given entity\n     */\n    protected getNestedSetIds(\n        metadata: EntityMetadata,\n        ids: ObjectLiteral | ObjectLiteral[],\n    ): Promise<NestedSetIds[]> {\n        const select = {\n            left: `${metadata.targetName}.${\n                metadata.nestedSetLeftColumn!.propertyPath\n            }`,\n            right: `${metadata.targetName}.${\n                metadata.nestedSetRightColumn!.propertyPath\n            }`,\n        }\n\n        const queryBuilder = this.queryRunner.manager.createQueryBuilder()\n\n        Object.entries(select).forEach(([key, value]) => {\n            queryBuilder.addSelect(value, key)\n        })\n\n        return queryBuilder\n            .from(metadata.target, metadata.targetName)\n            .whereInIds(ids)\n            .orderBy(select.right, \"DESC\")\n            .getRawMany()\n            .then((results) => {\n                const data: NestedSetIds[] = []\n\n                for (const result of results) {\n                    const entry: any = {}\n                    for (const key of Object.keys(select)) {\n                        const value = result ? result[key] : undefined\n\n                        // CockroachDB returns numeric types as string\n                        entry[key] =\n                            typeof value === \"string\" ? parseInt(value) : value\n                    }\n                    data.push(entry)\n                }\n\n                return data\n            })\n    }\n\n    private async isUniqueRootEntity(\n        subject: Subject,\n        parent: any,\n    ): Promise<boolean> {\n        const escape = (alias: string) =>\n            this.queryRunner.connection.driver.escape(alias)\n        const tableName = this.getTableName(subject.metadata.tablePath)\n        const parameters: any[] = []\n        const whereCondition = subject.metadata\n            .treeParentRelation!.joinColumns.map((column) => {\n                const columnName = escape(column.databaseName)\n                const parameter = column.getEntityValue(parent)\n\n                if (parameter == null) {\n                    return `${columnName} IS NULL`\n                }\n\n                parameters.push(parameter)\n                const parameterName =\n                    this.queryRunner.connection.driver.createParameter(\n                        \"entity_\" + column.databaseName,\n                        parameters.length - 1,\n                    )\n                return `${columnName} = ${parameterName}`\n            })\n            .join(\" AND \")\n\n        const countAlias = \"count\"\n        const result = await this.queryRunner.query(\n            `SELECT COUNT(1) AS ${escape(\n                countAlias,\n            )} FROM ${tableName} WHERE ${whereCondition}`,\n            parameters,\n            true,\n        )\n\n        return parseInt(result.records[0][countAlias]) === 0\n    }\n\n    /**\n     * Gets escaped table name with schema name if SqlServer or Postgres driver used with custom\n     * schema name, otherwise returns escaped table name.\n     */\n    protected getTableName(tablePath: string): string {\n        return tablePath\n            .split(\".\")\n            .map((i) => {\n                // this condition need because in SQL Server driver when custom database name was specified and schema name was not, we got `dbName..tableName` string, and doesn't need to escape middle empty string\n                return i === \"\"\n                    ? i\n                    : this.queryRunner.connection.driver.escape(i)\n            })\n            .join(\".\")\n    }\n}\n"], "sourceRoot": "../.."}