"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebControllerGenerator = void 0;
/**
 * wrap controller string to middleware function
 * @param controllerMapping like FooController.index
 * @param routeArgsInfo
 * @param routerResponseData
 */
const decorator_1 = require("../decorator");
const util = require("util");
const interface_1 = require("../interface");
const error_1 = require("../error");
const middlewareService_1 = require("../service/middlewareService");
const debug = util.debuglog('midway:debug');
class WebControllerGenerator {
    constructor(app, midwayWebRouterService) {
        this.app = app;
        this.midwayWebRouterService = midwayWebRouterService;
    }
    /**
     * wrap controller string to middleware function
     * @param routeInfo
     */
    generateKoaController(routeInfo) {
        return async (ctx, next) => {
            if (routeInfo.controllerClz && typeof routeInfo.method === 'string') {
                const isPassed = await this.app
                    .getFramework()
                    .runGuard(ctx, routeInfo.controllerClz, routeInfo.method);
                if (!isPassed) {
                    throw new error_1.httpError.ForbiddenError();
                }
            }
            const args = [ctx, next];
            let result;
            if (typeof routeInfo.method !== 'string') {
                result = await routeInfo.method(ctx, next);
            }
            else {
                const controller = await ctx.requestContext.getAsync(routeInfo.id);
                // eslint-disable-next-line prefer-spread
                result = await controller[routeInfo.method].apply(controller, args);
            }
            if (result !== undefined) {
                if (result === null) {
                    // 这样设置可以绕过 koa 的 _explicitStatus 赋值机制
                    ctx.response._body = null;
                    ctx.response._midwayControllerNullBody = true;
                }
                else {
                    ctx.body = result;
                }
            }
            // implement response decorator
            if (Array.isArray(routeInfo.responseMetadata) &&
                routeInfo.responseMetadata.length) {
                for (const routerRes of routeInfo.responseMetadata) {
                    switch (routerRes.type) {
                        case decorator_1.WEB_RESPONSE_HTTP_CODE:
                            ctx.status = routerRes.code;
                            break;
                        case decorator_1.WEB_RESPONSE_HEADER:
                            for (const key in (routerRes === null || routerRes === void 0 ? void 0 : routerRes.setHeaders) || {}) {
                                ctx.set(key, routerRes.setHeaders[key]);
                            }
                            break;
                        case decorator_1.WEB_RESPONSE_CONTENT_TYPE:
                            ctx.type = routerRes.contentType;
                            break;
                        case decorator_1.WEB_RESPONSE_REDIRECT:
                            ctx.status = routerRes.code;
                            ctx.redirect(routerRes.url);
                            return;
                    }
                }
            }
        };
    }
    async loadMidwayController(routerHandler) {
        var _a, _b;
        const routerTable = await this.midwayWebRouterService.getRouterTable();
        const routerList = await this.midwayWebRouterService.getRoutePriorityList();
        const applicationContext = this.app.getApplicationContext();
        const logger = this.app.getCoreLogger();
        const middlewareService = applicationContext.get(middlewareService_1.MidwayMiddlewareService);
        for (const routerInfo of routerList) {
            // bind controller first
            applicationContext.bindClass(routerInfo.routerModule);
            logger.debug(`Load Controller "${routerInfo.controllerId}", prefix=${routerInfo.prefix}`);
            debug(`[core]: Load Controller "${routerInfo.controllerId}", prefix=${routerInfo.prefix}`);
            // new router
            const newRouter = this.createRouter({
                prefix: routerInfo.prefix,
                ...routerInfo.routerOptions,
            });
            // add router middleware
            routerInfo.middleware = (_a = routerInfo.middleware) !== null && _a !== void 0 ? _a : [];
            if (routerInfo.middleware.length) {
                const routerMiddlewareFn = await middlewareService.compose(routerInfo.middleware, this.app);
                newRouter.use(routerMiddlewareFn);
            }
            // add route
            const routes = routerTable.get(routerInfo.prefix);
            for (const routeInfo of routes) {
                // get middleware
                const methodMiddlewares = [];
                routeInfo.middleware = (_b = routeInfo.middleware) !== null && _b !== void 0 ? _b : [];
                if (routeInfo.middleware.length) {
                    const routeMiddlewareFn = await middlewareService.compose(routeInfo.middleware, this.app);
                    methodMiddlewares.push(routeMiddlewareFn);
                }
                if (this.app.getFrameworkType() === interface_1.MidwayFrameworkType.WEB_KOA) {
                    // egg use path-to-regexp v1 but koa use v6
                    if (typeof routeInfo.url === 'string' && /\*$/.test(routeInfo.url)) {
                        routeInfo.url = routeInfo.url.replace('*', '(.*)');
                    }
                }
                const routerArgs = [
                    routeInfo.routerName,
                    routeInfo.url,
                    ...methodMiddlewares,
                    this.generateController(routeInfo),
                ];
                logger.debug(`Load Router "${routeInfo.requestMethod.toUpperCase()} ${routeInfo.url}"`);
                debug(`[core]: Load Router "${routeInfo.requestMethod.toUpperCase()} ${routeInfo.url}"`);
                // apply controller from request context
                // eslint-disable-next-line prefer-spread
                newRouter[routeInfo.requestMethod.toLowerCase()].apply(newRouter, routerArgs);
            }
            routerHandler && routerHandler(newRouter);
        }
    }
}
exports.WebControllerGenerator = WebControllerGenerator;
//# sourceMappingURL=webGenerator.js.map