{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../../src/storage.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAAG,IAAI,eAAe,EAAE,CAAC", "sourcesContent": ["import { MetadataStorage } from './MetadataStorage';\n\n/**\n * Default metadata storage is used as singleton and can be used to storage all metadatas.\n */\nexport const defaultMetadataStorage = new MetadataStorage();\n"]}