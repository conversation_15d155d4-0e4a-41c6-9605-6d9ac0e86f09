{"name": "tv4", "version": "1.3.0", "author": "<PERSON><PERSON><PERSON>", "description": "A public domain JSON Schema validator for JavaScript", "keywords": ["json-schema", "schema", "validator", "tv4"], "maintainers": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "https://github.com/geraintluff/"}], "main": "tv4.js", "repository": {"type": "git", "url": "https://github.com/geraintluff/tv4.git"}, "license": [{"type": "Public Domain", "url": "http://geraintluff.github.io/tv4/LICENSE.txt"}, {"type": "MIT", "url": "http://jsonary.com/LICENSE.txt"}], "devDependencies": {"grunt": "~0.4.1", "grunt-cli": "~0.1.9", "grunt-component-io": "~0.1.0", "grunt-concat-sourcemap": "~0.2", "grunt-contrib-clean": "~0.4.1", "grunt-contrib-copy": "~0.4.1", "grunt-contrib-jshint": "~0.6.2", "grunt-contrib-uglify": "~0.2.2", "grunt-markdown": "~0.3.0", "grunt-mocha": "~0.4", "grunt-mocha-test": "~0.5.0", "grunt-push-release": "~0.1.1", "grunt-regex-replace": "~0.2.5", "jshint-path-reporter": "~0.1", "mocha": "~1.11.0", "mocha-unfunk-reporter": "~0.2", "proclaim": "1.4", "requirejs": "~2.1.11", "source-map-support": "~0.1"}, "engines": {"node": ">= 0.8.0"}, "scripts": {"test": "grunt test", "prepublish": "grunt prepublish"}}