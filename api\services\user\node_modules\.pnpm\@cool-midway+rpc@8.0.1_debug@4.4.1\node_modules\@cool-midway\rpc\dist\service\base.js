"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseRpcService = void 0;
const core_1 = require("@midwayjs/core");
const core_2 = require("@midwayjs/core");
const mysql_1 = require("./mysql");
const postgres_1 = require("./postgres");
const core_3 = require("@cool-midway/core");
const typeorm_1 = require("@midwayjs/typeorm");
const sqlite_1 = require("./sqlite");
/**
 * 服务基类
 */
let BaseRpcService = class BaseRpcService {
    // 设置模型
    setModel(entity) {
        this.entity = entity;
    }
    setCurdOption(curdOption) {
        this.curdOption = curdOption;
    }
    async init() {
        const services = {
            mysql: this.baseMysqlService,
            mariadb: this.baseMysqlService,
            postgres: this.basePgService,
            sqlite: this.baseSqliteService,
        };
        this.service = services[this.ormType];
        if (!this.service)
            throw new core_3.CoolCoreException('暂不支持当前数据库类型');
        this.sqlParams = this.service.sqlParams;
        await this.service.init();
    }
    // 设置模型
    setEntity(entity) {
        this.entity = entity;
        this.service.setEntity(entity);
    }
    // 设置请求上下文
    setCtx(ctx) {
        this.baseCtx = ctx;
        this.service.setCtx(ctx);
    }
    // 设置应用对象
    setApp(app) {
        this.baseApp = app;
        this.service.setApp(app);
    }
    /**
     * 设置sql
     * @param condition 条件是否成立
     * @param sql sql语句
     * @param params 参数
     */
    setSql(condition, sql, params) {
        return this.service.setSql(condition, sql, params);
    }
    /**
     * 获得查询个数的SQL
     * @param sql
     */
    getCountSql(sql) {
        return this.service.getCountSql(sql);
    }
    /**
     * 参数安全性检查
     * @param params
     */
    async paramSafetyCheck(params) {
        return await this.service.paramSafetyCheck(params);
    }
    /**
     * 原生查询
     * @param sql
     * @param params
     * @param connectionName
     */
    async nativeQuery(sql, params, connectionName) {
        return await this.service.nativeQuery(sql, params, connectionName);
    }
    /**
     * 获得ORM管理
     *  @param connectionName 连接名称
     */
    getOrmManager(connectionName = 'default') {
        return this.service.getOrmManager(connectionName);
    }
    /**
     * 操作entity获得分页数据，不用写sql
     * @param find QueryBuilder
     * @param query
     * @param autoSort
     * @param connectionName
     */
    async entityRenderPage(find, query, autoSort = true) {
        return await this.service.entityRenderPage(find, query, autoSort);
    }
    /**
     * 执行SQL并获得分页数据
     * @param sql 执行的sql语句
     * @param query 分页查询条件
     * @param autoSort 是否自动排序
     * @param connectionName 连接名称
     */
    async sqlRenderPage(sql, query, autoSort = true, connectionName) {
        return await this.service.sqlRenderPage(sql, query, autoSort, connectionName);
    }
    /**
     * 获得单个ID
     * @param id ID
     * @param infoIgnoreProperty 忽略返回属性
     */
    async info(id, infoIgnoreProperty) {
        this.service.setEntity(this.entity);
        return await this.service.info(id, infoIgnoreProperty);
    }
    /**
     * 删除
     * @param ids 删除的ID集合 如：[1,2,3] 或者 1,2,3
     */
    async delete(ids) {
        this.service.setEntity(this.entity);
        await this.modifyBefore(ids, 'delete');
        await this.service.delete(ids);
        await this.modifyAfter(ids, 'delete');
    }
    /**
     * 软删除
     * @param ids 删除的ID数组
     * @param entity 实体
     */
    async softDelete(ids, entity) {
        this.service.setEntity(this.entity);
        await this.service.softDelete(ids, entity);
    }
    /**
     * 修改
     * @param param 数据
     */
    async update(param) {
        this.service.setEntity(this.entity);
        if (!this.entity)
            throw new core_3.CoolValidateException(core_3.ERRINFO.NOENTITY);
        if (!param.id && !(param instanceof Array))
            throw new core_3.CoolValidateException(core_3.ERRINFO.NOID);
        await this.addOrUpdate(param, 'update');
    }
    /**
     * 新增
     * @param param 数据
     */
    async add(param) {
        if (!this.entity)
            throw new core_3.CoolValidateException(core_3.ERRINFO.NOENTITY);
        delete param.id;
        await this.addOrUpdate(param, 'add');
        return {
            id: param instanceof Array
                ? param.map(e => {
                    return e.id ? e.id : e._id;
                })
                : param.id
                    ? param.id
                    : param._id,
        };
    }
    /**
     * 新增|修改
     * @param param 数据
     */
    async addOrUpdate(param, type = 'add') {
        this.service.setEntity(this.entity);
        await this.modifyBefore(param, type);
        await this.service.addOrUpdate(param, type);
        await this.modifyAfter(param, type);
    }
    /**
     * 非分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    async list(query, option, connectionName) {
        this.service.setEntity(this.entity);
        return await this.service.list(query, option, connectionName);
    }
    /**
     * 分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    async page(query, option, connectionName) {
        this.service.setEntity(this.entity);
        return await this.service.page(query, option, connectionName);
    }
    /**
     * 构建查询配置
     * @param query 前端查询
     * @param option
     */
    async getOptionFind(query, option) {
        this.service.setEntity(this.entity);
        return await this.service.getOptionFind(query, option);
    }
    /**
     * 新增|修改|删除 之后的操作
     * @param data 对应数据
     */
    async modifyAfter(data, type) { }
    /**
     * 新增|修改|删除 之前的操作
     * @param data 对应数据
     */
    async modifyBefore(data, type) { }
};
exports.BaseRpcService = BaseRpcService;
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", mysql_1.BaseMysqlService)
], BaseRpcService.prototype, "baseMysqlService", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", postgres_1.BasePgService)
], BaseRpcService.prototype, "basePgService", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", sqlite_1.BaseSqliteService)
], BaseRpcService.prototype, "baseSqliteService", void 0);
__decorate([
    (0, core_1.Config)('typeorm.dataSource.default.type'),
    __metadata("design:type", Object)
], BaseRpcService.prototype, "ormType", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], BaseRpcService.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_3.CoolEventManager)
], BaseRpcService.prototype, "coolEventManager", void 0);
__decorate([
    (0, core_1.Inject)('ctx'),
    __metadata("design:type", Object)
], BaseRpcService.prototype, "baseCtx", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BaseRpcService.prototype, "init", null);
exports.BaseRpcService = BaseRpcService = __decorate([
    (0, core_1.Provide)(),
    (0, core_2.Scope)(core_2.ScopeEnum.Request, { allowDowngrade: true })
], BaseRpcService);
