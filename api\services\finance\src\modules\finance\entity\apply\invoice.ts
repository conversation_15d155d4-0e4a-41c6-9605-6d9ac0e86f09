import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';
import { FinanceUserInvoiceEntity } from '../user/invoice';

/**
 * 开票申请
 */
@Entity('finance_apply_invoice')
export class FinanceApplyInvoiceEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '金额', type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ comment: '状态 0-申请中 1-成功 2-失败', default: 0 })
  status: number;

  @Column({ comment: '备注', nullable: true })
  remark: string;

  @Column({ comment: '开票信息', type: 'json' })
  invoice: FinanceUserInvoiceEntity;

  @Column({ comment: '订单ID', type: 'json', nullable: true })
  orderIds: number[];
}
