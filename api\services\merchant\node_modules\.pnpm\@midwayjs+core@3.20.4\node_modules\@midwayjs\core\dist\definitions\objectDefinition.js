"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ObjectDefinition = void 0;
const interface_1 = require("../interface");
const properties_1 = require("./properties");
const objectCreator_1 = require("./objectCreator");
/* tslint:disable:variable-name */
class ObjectDefinition {
    constructor() {
        this._attrs = new Map();
        this._asynchronous = false;
        this.scope = interface_1.ScopeEnum.Singleton;
        this.creator = null;
        this.id = null;
        this.name = null;
        this.initMethod = null;
        this.destroyMethod = null;
        this.constructMethod = null;
        this.constructorArgs = [];
        this.path = null;
        this.export = null;
        this.dependsOn = [];
        this.properties = new properties_1.ObjectProperties();
        this.namespace = '';
        this.handlerProps = [];
        this.allowDowngrade = false;
        this.creator = new objectCreator_1.ObjectCreator(this);
    }
    set asynchronous(asynchronous) {
        this._asynchronous = asynchronous;
    }
    isAsync() {
        return this._asynchronous;
    }
    isSingletonScope() {
        return this.scope === interface_1.ScopeEnum.Singleton;
    }
    isRequestScope() {
        return this.scope === interface_1.ScopeEnum.Request;
    }
    hasDependsOn() {
        return this.dependsOn.length > 0;
    }
    hasConstructorArgs() {
        return this.constructorArgs.length > 0;
    }
    getAttr(key) {
        return this._attrs.get(key);
    }
    hasAttr(key) {
        return this._attrs.has(key);
    }
    setAttr(key, value) {
        this._attrs.set(key, value);
    }
}
exports.ObjectDefinition = ObjectDefinition;
/* tslint:enable:variable-name */
//# sourceMappingURL=objectDefinition.js.map