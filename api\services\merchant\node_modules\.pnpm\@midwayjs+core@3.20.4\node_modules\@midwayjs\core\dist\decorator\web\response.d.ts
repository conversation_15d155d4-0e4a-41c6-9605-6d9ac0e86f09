export declare function Redirect(url: string, code?: number): (target: any, key: any, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function HttpCode(code: number): (target: any, key: any, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function SetHeader(headerKey: string | Record<string, any>, value?: string): (target: any, key: any, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function ContentType(contentType: string): (target: any, key: any, descriptor: PropertyDescriptor) => PropertyDescriptor;
export declare function createRender(RenderEngine: {
    render: () => string;
    renderString: () => string;
}): (templateName: string) => (target: any, key: any, descriptor: PropertyDescriptor) => PropertyDescriptor;
//# sourceMappingURL=response.d.ts.map