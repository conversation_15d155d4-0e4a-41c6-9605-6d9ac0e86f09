#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules/blessed/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules/blessed/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules/blessed/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules/blessed/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/blessed@0.1.81/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/tput.js" "$@"
else
  exec node  "$basedir/../../bin/tput.js" "$@"
fi
