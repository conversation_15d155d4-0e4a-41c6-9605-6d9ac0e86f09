"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebRouterCollector = exports.MidwayServerlessFunctionService = void 0;
const decorator_1 = require("../decorator");
const webRouterService_1 = require("./webRouterService");
const container_1 = require("../context/container");
const fileDetector_1 = require("../common/fileDetector");
const contextUtil_1 = require("../util/contextUtil");
const interface_1 = require("../interface");
let MidwayServerlessFunctionService = class MidwayServerlessFunctionService extends webRouterService_1.MidwayWebRouterService {
    constructor(options = {}) {
        super(Object.assign({}, options, {
            includeFunctionRouter: true,
        }));
        this.options = options;
    }
    async analyze() {
        this.analyzeController();
        this.analyzeFunction();
        this.sortPrefixAndRouter();
        // requestMethod all transform to other method
        for (const routerInfo of this.routes.values()) {
            for (const info of routerInfo) {
                if (info.requestMethod === 'all') {
                    info.functionTriggerMetadata = info.functionTriggerMetadata || {};
                    info.functionTriggerMetadata.method = [
                        'get',
                        'post',
                        'put',
                        'delete',
                        'head',
                        'patch',
                        'options',
                    ];
                }
            }
        }
    }
    analyzeFunction() {
        const fnModules = (0, decorator_1.listModule)(decorator_1.FUNC_KEY);
        for (const module of fnModules) {
            this.collectFunctionRoute(module);
        }
    }
    collectFunctionRoute(module) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
        // serverlessTrigger metadata
        const webRouterInfo = (0, decorator_1.getClassMetadata)(decorator_1.FUNC_KEY, module);
        const controllerId = (0, decorator_1.getProviderName)(module);
        const id = (0, decorator_1.getProviderUUId)(module);
        const prefix = '/';
        if (!this.routes.has(prefix)) {
            this.routes.set(prefix, []);
            this.routesPriority.push({
                prefix,
                priority: -999,
                middleware: [],
                routerOptions: {},
                controllerId,
                routerModule: module,
            });
        }
        for (const webRouter of webRouterInfo) {
            // 新的 @ServerlessTrigger 写法
            if ((_a = webRouter['metadata']) === null || _a === void 0 ? void 0 : _a['path']) {
                const routeArgsInfo = (0, decorator_1.getPropertyDataFromClass)(decorator_1.WEB_ROUTER_PARAM_KEY, module, webRouter['methodName']) || [];
                const routerResponseData = (0, decorator_1.getPropertyMetadata)(decorator_1.WEB_RESPONSE_KEY, module, webRouter['methodName']) || [];
                // 新 http/api gateway 函数
                const data = {
                    id,
                    prefix,
                    routerName: '',
                    url: webRouter['metadata']['path'],
                    requestMethod: (_c = (_b = webRouter['metadata']) === null || _b === void 0 ? void 0 : _b['method']) !== null && _c !== void 0 ? _c : 'get',
                    method: webRouter['methodName'],
                    description: '',
                    summary: '',
                    handlerName: `${controllerId}.${webRouter['methodName']}`,
                    funcHandlerName: `${controllerId}.${webRouter['methodName']}`,
                    controllerId,
                    middleware: ((_d = webRouter['metadata']) === null || _d === void 0 ? void 0 : _d['middleware']) || [],
                    controllerMiddleware: [],
                    requestMetadata: routeArgsInfo,
                    responseMetadata: routerResponseData,
                };
                const functionMeta = (0, decorator_1.getPropertyMetadata)(decorator_1.SERVERLESS_FUNC_KEY, module, webRouter['methodName']) || {};
                const functionName = (_g = (_e = functionMeta['functionName']) !== null && _e !== void 0 ? _e : (_f = webRouter === null || webRouter === void 0 ? void 0 : webRouter['metadata']) === null || _f === void 0 ? void 0 : _f['functionName']) !== null && _g !== void 0 ? _g : createFunctionName(module, webRouter['methodName']);
                const funcHandlerName = (_k = (_h = functionMeta['handlerName']) !== null && _h !== void 0 ? _h : (_j = webRouter === null || webRouter === void 0 ? void 0 : webRouter['metadata']) === null || _j === void 0 ? void 0 : _j['handlerName']) !== null && _k !== void 0 ? _k : data.funcHandlerName;
                data.functionName = functionName;
                data.funcHandlerName = funcHandlerName;
                data.functionTriggerName = webRouter['type'];
                data.functionTriggerMetadata = webRouter['metadata'];
                data.functionMetadata = {
                    functionName,
                    ...functionMeta,
                };
                this.checkDuplicateAndPush(prefix, data);
            }
            else {
                const functionMeta = (0, decorator_1.getPropertyMetadata)(decorator_1.SERVERLESS_FUNC_KEY, module, webRouter['methodName']) || {};
                const functionName = (_o = (_l = functionMeta['functionName']) !== null && _l !== void 0 ? _l : (_m = webRouter === null || webRouter === void 0 ? void 0 : webRouter['metadata']) === null || _m === void 0 ? void 0 : _m['functionName']) !== null && _o !== void 0 ? _o : createFunctionName(module, webRouter['methodName']);
                const funcHandlerName = (_r = (_p = functionMeta['handlerName']) !== null && _p !== void 0 ? _p : (_q = webRouter === null || webRouter === void 0 ? void 0 : webRouter['metadata']) === null || _q === void 0 ? void 0 : _q['handlerName']) !== null && _r !== void 0 ? _r : `${controllerId}.${webRouter['methodName']}`;
                // 其他类型的函数
                this.checkDuplicateAndPush(prefix, {
                    id,
                    prefix,
                    routerName: '',
                    url: '',
                    requestMethod: '',
                    method: webRouter['methodName'],
                    description: '',
                    summary: '',
                    handlerName: `${controllerId}.${webRouter['methodName']}`,
                    funcHandlerName: funcHandlerName,
                    controllerId,
                    middleware: ((_s = webRouter['metadata']) === null || _s === void 0 ? void 0 : _s['middleware']) || [],
                    controllerMiddleware: [],
                    requestMetadata: [],
                    responseMetadata: [],
                    functionName,
                    functionTriggerName: webRouter['type'],
                    functionTriggerMetadata: webRouter['metadata'],
                    functionMetadata: {
                        functionName,
                        ...functionMeta,
                    },
                });
            }
        }
    }
    async getFunctionList() {
        return this.getFlattenRouterTable({
            compileUrlPattern: true,
        });
    }
    addServerlessFunction(func, triggerOptions, functionOptions = {}) {
        var _a, _b;
        const prefix = '';
        if (!this.routes.has(prefix)) {
            this.routes.set(prefix, []);
            this.routesPriority.push({
                prefix,
                priority: 0,
                middleware: [],
                routerOptions: {},
                controllerId: undefined,
                routerModule: undefined,
            });
        }
        const functionName = (_a = triggerOptions.functionName) !== null && _a !== void 0 ? _a : functionOptions.functionName;
        this.checkDuplicateAndPush(prefix, {
            id: null,
            method: func,
            url: triggerOptions.metadata['path'] || '',
            requestMethod: triggerOptions.metadata['method'] || '',
            description: '',
            summary: '',
            handlerName: '',
            funcHandlerName: triggerOptions.handlerName || functionOptions.handlerName,
            controllerId: '',
            middleware: ((_b = triggerOptions.metadata) === null || _b === void 0 ? void 0 : _b.middleware) || [],
            controllerMiddleware: [],
            requestMetadata: [],
            responseMetadata: [],
            functionName,
            functionTriggerName: triggerOptions.metadata.name,
            functionTriggerMetadata: triggerOptions.metadata,
            functionMetadata: {
                functionName,
                ...functionOptions,
            },
        });
    }
};
MidwayServerlessFunctionService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object])
], MidwayServerlessFunctionService);
exports.MidwayServerlessFunctionService = MidwayServerlessFunctionService;
function createFunctionName(target, functionName) {
    return (0, decorator_1.getProviderName)(target).replace(/[:#]/g, '-') + '-' + functionName;
}
/**
 * @deprecated use built-in MidwayWebRouterService first
 */
class WebRouterCollector {
    constructor(baseDir = '', options = {}) {
        this.baseDir = baseDir;
        this.options = options;
    }
    async init() {
        if (!this.proxy) {
            if (this.baseDir) {
                const container = new container_1.MidwayContainer();
                (0, decorator_1.bindContainer)(container);
                container.setFileDetector(new fileDetector_1.CommonJSFileDetector({
                    loadDir: this.baseDir,
                }));
                await container.ready();
            }
            if (this.options.includeFunctionRouter) {
                if ((0, contextUtil_1.getCurrentMainFramework)()) {
                    this.proxy = await (0, contextUtil_1.getCurrentMainFramework)()
                        .getApplicationContext()
                        .getAsync(MidwayServerlessFunctionService, [this.options]);
                }
                else {
                    this.proxy = new MidwayServerlessFunctionService(this.options);
                }
            }
            else {
                if ((0, contextUtil_1.getCurrentMainFramework)()) {
                    this.proxy = await (0, contextUtil_1.getCurrentMainFramework)()
                        .getApplicationContext()
                        .getAsync(webRouterService_1.MidwayWebRouterService, [this.options]);
                }
                else {
                    this.proxy = new webRouterService_1.MidwayWebRouterService(this.options);
                }
            }
        }
    }
    async getRoutePriorityList() {
        await this.init();
        return this.proxy.getRoutePriorityList();
    }
    async getRouterTable() {
        await this.init();
        return this.proxy.getRouterTable();
    }
    async getFlattenRouterTable() {
        await this.init();
        return this.proxy.getFlattenRouterTable();
    }
}
exports.WebRouterCollector = WebRouterCollector;
//# sourceMappingURL=slsFunctionService.js.map