{"version": 3, "file": "is-iso4217-currency-code.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/is-iso4217-currency-code.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,wEAAyD;AAE5C,QAAA,wBAAwB,GAAG,uBAAuB,CAAC;AAEhE;;GAEG;AACH,SAAgB,qBAAqB,CAAC,KAAc;IAClD,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,mBAAkB,EAAC,KAAK,CAAC,CAAC;AAChE,CAAC;AAFD,sDAEC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CAAC,iBAAqC;IACzE,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gCAAwB;QAC9B,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,qBAAqB,CAAC,KAAK,CAAC;YAChE,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,iDAAiD,EAC5E,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,sDAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isISO4217Validator from 'validator/lib/isISO4217';\n\nexport const IS_ISO4217_CURRENCY_CODE = 'isISO4217CurrencyCode';\n\n/**\n * Check if the string is a valid [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) officially assigned currency code.\n */\nexport function isISO4217CurrencyCode(value: unknown): boolean {\n  return typeof value === 'string' && isISO4217Validator(value);\n}\n\n/**\n * Check if the string is a valid [ISO 4217](https://en.wikipedia.org/wiki/ISO_4217) officially assigned currency code.\n */\nexport function IsISO4217CurrencyCode(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ISO4217_CURRENCY_CODE,\n      validator: {\n        validate: (value, args): boolean => isISO4217CurrencyCode(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a valid ISO4217 currency code',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}