{"version": 3, "sources": ["../../src/entity-schema/EntitySchemaEmbeddedError.ts"], "names": [], "mappings": ";;;AAAA,oCAAuC;AAEvC,MAAa,yBAA0B,SAAQ,oBAAY;IACvD,MAAM,CAAC,qCAAqC,CACxC,KAAa;QAEb,OAAO,IAAI,yBAAyB,CAChC,gCAAgC,KAAK,iBAAiB,CACzD,CAAA;IACL,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,KAAa;QACvC,OAAO,IAAI,yBAAyB,CAChC,gCAAgC,KAAK,wBAAwB,CAChE,CAAA;IACL,CAAC;IAED,YAAY,OAAe;QACvB,KAAK,CAAC,OAAO,CAAC,CAAA;IAClB,CAAC;CACJ;AAlBD,8DAkBC", "file": "EntitySchemaEmbeddedError.js", "sourcesContent": ["import { TypeORMError } from \"../error\"\n\nexport class EntitySchemaEmbeddedError extends TypeORMError {\n    static createEntitySchemaIsRequiredException(\n        field: string,\n    ): EntitySchemaEmbeddedError {\n        return new EntitySchemaEmbeddedError(\n            `EntitySchema is required for ${field} embedded field`,\n        )\n    }\n\n    static createTargetIsRequired(field: string): EntitySchemaEmbeddedError {\n        return new EntitySchemaEmbeddedError(\n            `Target field is required for ${field} embedded EntitySchema`,\n        )\n    }\n\n    constructor(message: string) {\n        super(message)\n    }\n}\n"], "sourceRoot": ".."}