import { PipeTransform, TransformOptions } from '@midwayjs/core';
import { ValidateService } from './service';
import { ValidateOptions } from './decorator/validate';
import * as Jo<PERSON> from 'joi';
export declare abstract class AbstractValidationPipe implements PipeTransform {
    protected validateService: ValidateService;
    abstract transform(value: any, options: TransformOptions): any;
    validateWithSchema(value: any, options: TransformOptions, schema: Joi.AnySchema): any;
    validate(value: any, options: TransformOptions): any;
    protected parseValidationOptions(options: TransformOptions): ValidateOptions;
    protected getSchema(): any;
}
export declare class ValidationPipe extends AbstractValidationPipe {
    transform(value: any, options: TransformOptions): any;
}
export declare abstract class ParsePipe extends AbstractValidationPipe {
    transform(value: any, options: TransformOptions): any;
}
export declare class DecoratorValidPipe extends ParsePipe {
}
export declare class ParseIntPipe extends ParsePipe {
    getSchema(): Joi.AnySchema<any>;
}
export declare class ParseBoolPipe extends ParsePipe {
    getSchema(): Joi.AnySchema<any>;
}
export declare class ParseFloatPipe extends ParsePipe {
    getSchema(): Joi.AnySchema<any>;
}
export declare class DefaultValuePipe<T = any, R = any> implements PipeTransform<T, R> {
    protected readonly defaultValue: R;
    constructor(defaultValue: R);
    transform(value: any, options: any): any;
}
//# sourceMappingURL=pipe.d.ts.map