const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: '127.0.0.1',
  port: 3306,
  user: 'root',
  password: 'wap.336101',
  database: 'merchant_service_db',
  charset: 'utf8mb4'
};

// 菜单数据
const menuData = [
  // 一级菜单
  { id: 1, parentId: 0, name: '首页', router: '/home', perms: '', type: 1, icon: 'home', orderNum: 1, keepAlive: 1, isShow: 1 },
  { id: 2, parentId: 0, name: '商户入驻', router: '/merchant/settle-in', perms: 'merchant:settle-in', type: 1, icon: 'user-add', orderNum: 2, keepAlive: 1, isShow: 1 },
  { id: 3, parentId: 0, name: '商户管理', router: '/merchant', perms: '', type: 0, icon: 'user', orderNum: 3, keepAlive: 1, isShow: 1 },
  { id: 4, parentId: 0, name: '非遗人认证', router: '/heritage-auth', perms: 'heritage:auth', type: 1, icon: 'safety-certificate', orderNum: 4, keepAlive: 1, isShow: 1 },
  { id: 5, parentId: 0, name: '商户认证', router: '/merchant-auth', perms: 'merchant:auth', type: 1, icon: 'solution', orderNum: 5, keepAlive: 1, isShow: 1 },
  { id: 6, parentId: 0, name: '系统管理', router: '/system', perms: '', type: 0, icon: 'setting', orderNum: 6, keepAlive: 1, isShow: 1 },

  // 商户管理子菜单
  { id: 30, parentId: 3, name: '个人商户列表', router: '/merchant/personal/list', perms: 'merchant:personal:list', type: 1, icon: 'user', orderNum: 1, keepAlive: 1, isShow: 1 },
  { id: 31, parentId: 3, name: '企业商户列表', router: '/merchant/company/list', perms: 'merchant:company:list', type: 1, icon: 'bank', orderNum: 2, keepAlive: 1, isShow: 1 },
  { id: 32, parentId: 3, name: '数据统计', router: '/merchant/statistics', perms: 'merchant:statistics', type: 1, icon: 'bar-chart', orderNum: 3, keepAlive: 1, isShow: 1 },

  // 系统管理子菜单
  { id: 60, parentId: 6, name: '菜单管理', router: '/system/menu', perms: 'system:menu', type: 1, icon: 'menu', orderNum: 1, keepAlive: 1, isShow: 1 },
  { id: 61, parentId: 6, name: '角色管理', router: '/system/role', perms: 'system:role', type: 1, icon: 'team', orderNum: 2, keepAlive: 1, isShow: 1 },
  { id: 62, parentId: 6, name: '用户管理', router: '/system/user', perms: 'system:user', type: 1, icon: 'user', orderNum: 3, keepAlive: 1, isShow: 1 },
];

// 权限数据
const permissionData = [
  // 菜单管理操作权限
  { id: 600, parentId: 60, name: '菜单查看', router: '', perms: 'system:menu:list', type: 2, icon: '', orderNum: 1, keepAlive: 1, isShow: 0 },
  { id: 601, parentId: 60, name: '菜单新增', router: '', perms: 'system:menu:add', type: 2, icon: '', orderNum: 2, keepAlive: 1, isShow: 0 },
  { id: 602, parentId: 60, name: '菜单编辑', router: '', perms: 'system:menu:update', type: 2, icon: '', orderNum: 3, keepAlive: 1, isShow: 0 },
  { id: 603, parentId: 60, name: '菜单删除', router: '', perms: 'system:menu:delete', type: 2, icon: '', orderNum: 4, keepAlive: 1, isShow: 0 },

  // 角色管理操作权限
  { id: 610, parentId: 61, name: '角色查看', router: '', perms: 'system:role:page', type: 2, icon: '', orderNum: 1, keepAlive: 1, isShow: 0 },
  { id: 611, parentId: 61, name: '角色新增', router: '', perms: 'system:role:add', type: 2, icon: '', orderNum: 2, keepAlive: 1, isShow: 0 },
  { id: 612, parentId: 61, name: '角色编辑', router: '', perms: 'system:role:update', type: 2, icon: '', orderNum: 3, keepAlive: 1, isShow: 0 },
  { id: 613, parentId: 61, name: '角色删除', router: '', perms: 'system:role:delete', type: 2, icon: '', orderNum: 4, keepAlive: 1, isShow: 0 },
  { id: 614, parentId: 61, name: '分配权限', router: '', perms: 'system:role:updatePerms', type: 2, icon: '', orderNum: 5, keepAlive: 1, isShow: 0 },

  // 用户管理操作权限
  { id: 620, parentId: 62, name: '用户查看', router: '', perms: 'system:user:page', type: 2, icon: '', orderNum: 1, keepAlive: 1, isShow: 0 },
  { id: 621, parentId: 62, name: '用户新增', router: '', perms: 'system:user:add', type: 2, icon: '', orderNum: 2, keepAlive: 1, isShow: 0 },
  { id: 622, parentId: 62, name: '用户编辑', router: '', perms: 'system:user:update', type: 2, icon: '', orderNum: 3, keepAlive: 1, isShow: 0 },
  { id: 623, parentId: 62, name: '用户删除', router: '', perms: 'system:user:delete', type: 2, icon: '', orderNum: 4, keepAlive: 1, isShow: 0 },
  { id: 624, parentId: 62, name: '重置密码', router: '', perms: 'system:user:resetPassword', type: 2, icon: '', orderNum: 5, keepAlive: 1, isShow: 0 },
];

// 初始化函数
async function initMenuData() {
  let connection;
  
  try {
    console.log('🔄 正在连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 创建数据库（如果不存在）
    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`merchant_service_db\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.execute(`USE \`merchant_service_db\``);

    // 检查表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'merchant_sys_menu'");
    if (tables.length === 0) {
      console.log('📋 创建 merchant_sys_menu 表...');
      await connection.execute(`
        CREATE TABLE merchant_sys_menu (
          id int NOT NULL AUTO_INCREMENT,
          name varchar(255) NOT NULL COMMENT '菜单名称',
          router varchar(255) DEFAULT NULL COMMENT '路由地址',
          perms varchar(255) DEFAULT NULL COMMENT '权限标识',
          type int NOT NULL DEFAULT '0' COMMENT '菜单类型 0-目录 1-菜单 2-按钮',
          icon varchar(255) DEFAULT NULL COMMENT '图标',
          orderNum int NOT NULL DEFAULT '0' COMMENT '排序',
          parentId int NOT NULL DEFAULT '0' COMMENT '上级菜单ID',
          keepAlive tinyint NOT NULL DEFAULT '1' COMMENT '路由缓存',
          isShow tinyint NOT NULL DEFAULT '1' COMMENT '是否显示',
          createTime datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) COMMENT '创建时间',
          updateTime datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6) COMMENT '更新时间',
          PRIMARY KEY (id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
    }

    // 清空现有菜单数据
    console.log('🗑️ 清空现有菜单数据...');
    await connection.execute('DELETE FROM merchant_sys_menu');

    // 重置自增ID
    await connection.execute('ALTER TABLE merchant_sys_menu AUTO_INCREMENT = 1');

    // 插入菜单数据
    console.log('📝 插入菜单数据...');
    const allMenuData = [...menuData, ...permissionData];
    
    for (const menu of allMenuData) {
      await connection.execute(
        `INSERT INTO merchant_sys_menu 
         (id, parentId, name, router, perms, type, icon, orderNum, keepAlive, isShow, createTime, updateTime) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [menu.id, menu.parentId, menu.name, menu.router, menu.perms, menu.type, menu.icon, menu.orderNum, menu.keepAlive, menu.isShow]
      );
    }

    console.log('✅ 菜单数据初始化完成！');
    
    // 验证数据
    const [result] = await connection.execute('SELECT COUNT(*) as count FROM merchant_sys_menu');
    console.log(`📊 菜单表中共有 ${result[0].count} 条记录`);

  } catch (error) {
    console.error('❌ 初始化失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔚 数据库连接已关闭');
    }
  }
}

// 执行初始化
if (require.main === module) {
  initMenuData();
}

module.exports = { initMenuData }; 