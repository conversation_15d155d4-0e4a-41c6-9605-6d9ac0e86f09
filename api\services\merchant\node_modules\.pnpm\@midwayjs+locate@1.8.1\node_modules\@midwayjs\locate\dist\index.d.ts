export declare enum ProjectType {
    UNKNOWN = "unknown",
    MIDWAY = "midway",
    MIDWAY_FRONT_MONOREPO = "midway_front_monorepo",
    MIDWAY_FRONT_integration = "midway_front_integration",
    MIDWAY_FAAS = "midway_faas",
    MIDWAY_FAAS_FRONT_MONOREPO = "midway_faas_front_monorepo",
    MIDWAY_FAAS_FRONT_integration = "midway_faas_front_integration"
}
export interface AnalyzeResult {
    cwd: string;
    midwayRoot: string;
    tsCodeRoot: string;
    tsConfigFilePath: string;
    tsBuildRoot: string;
    integrationProject: boolean;
    projectType: ProjectType;
    usingDependencies: string[];
    usingDependenciesVersion: {
        valid: object;
        unValid: string[];
    };
}
export declare class Locator {
    cwd: string;
    root: string;
    tsCodeRoot: string;
    tsBuildRoot: string;
    tsConfigFilePath: string;
    usingDependencies: string[];
    usingDependenciesVersion: {
        valid: object;
        unValid: string[];
    };
    integrationProject: boolean;
    projectType: ProjectType;
    isMidwayProject: boolean;
    isMidwayFaaSProject: boolean;
    constructor(cwd: any);
    run(options?: {
        root?: string;
        tsCodeRoot?: string;
        tsBuildRoot?: string;
    }): Promise<AnalyzeResult>;
    private analyzeCWD;
    private formatOptions;
    /**
     * 分析 midway 系列项目根目录
     */
    private analyzeRoot;
    /**
     * 分析 ts 代码的根目录，比如 src，或者其他
     */
    private analyzeTSCodeRoot;
    /**
     * 分析构建后的根目录
     */
    private analyzeTSBuildRoot;
    /**
     * 分析用到的依赖
     */
    private analyzeUsingDependencies;
    private analyzeUsingDependenciesVersion;
    private analyzeIntegrationProject;
    private formatAbsolutePath;
}
