#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/bin/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules/pm2/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/pm2@5.4.3/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/pm2-runtime" "$@"
else
  exec node  "$basedir/../../bin/pm2-runtime" "$@"
fi
