# @keyv/serialize [<img width="100" align="right" src="https://jaredwray.com/images/keyv-symbol.svg" alt="keyv">](https://github.com/jaredwra/keyv)

> Serialization functionality for [Keyv](https://github.com/jaredwray/keyv)


[![build](https://github.com/jaredwray/keyv/actions/workflows/tests.yaml/badge.svg)](https://github.com/jaredwray/keyv/actions/workflows/tests.yaml)
[![codecov](https://codecov.io/gh/jaredwray/keyv/branch/main/graph/badge.svg?token=bRzR3RyOXZ)](https://codecov.io/gh/jaredwray/keyv)
[![GitHub license](https://img.shields.io/github/license/jaredwray/keyv)](https://github.com/jaredwray/keyv/blob/main/LICENSE)
[![npm](https://img.shields.io/npm/dm/@keyv/serialize)](https://npmjs.com/package/@keyv/serialize)

This is the serialization functionality for [Keyv](https://github.com/jaredwray/keyv/tree/main/packages/keyv). It is used to serialize and deserialize data for storage and retrieval. You can also create your own [custom serialization functions](https://github.com/jaredwray/keyv/tree/main/packages/keyv#custom-serializers).

## License

[MIT © Jared Wray](LISCENCE)
