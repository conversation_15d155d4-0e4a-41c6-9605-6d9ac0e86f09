import { CoolConfig } from '@cool-midway/core';
import { MidwayConfig } from '@midwayjs/core';

export default {
  koa: {
    port: 9802, // merchant 微服务端口（修正为9802）
  },
  typeorm: {
    dataSource: {
      default: {
        type: 'mysql',
        host: '127.0.0.1',
        port: 3306,
        username: 'root',
        password: 'wap.336101',
        database: 'merchant_service_db',
        synchronize: false, // 关闭自动同步，手动建表
        logging: false,
        charset: 'utf8mb4',
        cache: true,
        entities: ["**/modules/*/entity"],
      },
    },
  },
  cool: {
    // RPC微服务配置
    rpc: {
      name: "merchant-service", // 商户微服务名称
    },
    redis: {
      host: '127.0.0.1',
      password: '',
      port: 6379,
      db: 10, // 修改为与API网关相同的db
    },
    eps: false, // 关闭EPS，避免路由冲突
    initDB: false, // 关闭自动初始化，我们手动建表了
    initMenu: false, // 关闭菜单初始化
  } as CoolConfig,
} as MidwayConfig; 