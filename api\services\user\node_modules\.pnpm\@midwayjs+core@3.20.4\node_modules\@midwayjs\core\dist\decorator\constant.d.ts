export declare const ALL = "common:all_value_key";
export declare const SCHEDULE_KEY = "common:schedule";
export declare const CONFIGURATION_KEY = "common:configuration";
export declare const FRAMEWORK_KEY = "common:framework";
export declare const ASPECT_KEY = "common:aspect";
export declare const CATCH_KEY = "common:catch";
export declare const MATCH_KEY = "common:match";
export declare const GUARD_KEY = "common:guard";
export declare const MOCK_KEY = "common:mock";
export declare const FACTORY_SERVICE_CLIENT_KEY = "common:service_factory:client";
export declare const FUNC_KEY = "faas:func";
export declare const SERVERLESS_FUNC_KEY = "faas:serverless:function";
export declare const CONTROLLER_KEY = "web:controller";
export declare const WEB_ROUTER_KEY = "web:router";
export declare const WEB_ROUTER_PARAM_KEY = "web:router_param";
export declare const WEB_RESPONSE_KEY = "web:response";
export declare const WEB_RESPONSE_HTTP_CODE = "web:response_http_code";
export declare const WEB_RESPONSE_REDIRECT = "web:response_redirect";
export declare const WEB_RESPONSE_HEADER = "web:response_header";
export declare const WEB_RESPONSE_CONTENT_TYPE = "web:response_content_type";
export declare const WEB_RESPONSE_RENDER = "web:response_render";
export declare const MODULE_TASK_KEY = "task:task";
export declare const MODULE_TASK_METADATA = "task:task:options";
export declare const MODULE_TASK_TASK_LOCAL_KEY = "task:task:task_local";
export declare const MODULE_TASK_TASK_LOCAL_OPTIONS = "task:task:task_local:options";
export declare const MODULE_TASK_QUEUE_KEY = "task:task:queue";
export declare const MODULE_TASK_QUEUE_OPTIONS = "task:task:queue:options";
export declare const WS_CONTROLLER_KEY = "ws:controller";
export declare const WS_EVENT_KEY = "ws:event";
export declare const HSF_KEY = "rpc:hsf";
export declare const RPC_GRPC_KEY = "rpc:grpc";
export declare const RPC_DUBBO_KEY = "rpc:dubbo";
export declare const MS_CONSUMER_KEY = "ms:consumer";
export declare const MS_PRODUCER_KEY = "ms:producer";
export declare const MS_PROVIDER_KEY = "ms:provider";
export declare const MS_GRPC_METHOD_KEY = "ms:grpc:method";
export declare const MS_DUBBO_METHOD_KEY = "ms:dubbo:method";
export declare const MS_HSF_METHOD_KEY = "ms:hsf:method";
export declare const CONFIG_KEY = "config";
export declare const PLUGIN_KEY = "plugin";
export declare const LOGGER_KEY = "logger";
export declare const APPLICATION_KEY = "__midway_framework_app__";
export declare const APPLICATION_CONTEXT_KEY = "__midway_application_context__";
export declare const CLASS_KEY_CONSTRUCTOR = "midway:class_key_constructor";
export declare const NAMED_TAG = "named";
export declare const INJECT_TAG = "inject";
export declare const INJECT_CUSTOM_PROPERTY = "inject_custom_property";
export declare const INJECT_CUSTOM_METHOD = "inject_custom_method";
export declare const INJECT_CUSTOM_PARAM = "inject_custom_param";
export declare const TAGGED_CLS = "injection:tagged_class";
export declare const TAGGED_FUN = "injection:tagged_function";
export declare const OBJ_DEF_CLS = "injection:object_definition_class";
export declare const PIPELINE_IDENTIFIER = "__pipeline_identifier__";
export declare const LIFECYCLE_IDENTIFIER_PREFIX = "__lifecycle__";
export declare const MAIN_MODULE_KEY = "__main__";
export declare const PRIVATE_META_DATA_KEY = "__midway_private_meta_data__";
//# sourceMappingURL=constant.d.ts.map