"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ManagedResolverFactory = exports.ManagedReference = void 0;
/**
 * 管理对象解析构建
 */
const constants_1 = require("../constants");
const interface_1 = require("../interface");
const util = require("util");
const error_1 = require("../error");
const debug = util.debuglog('midway:managedresolver');
const debugLog = util.debuglog('midway:debug');
class ManagedReference {
    constructor() {
        this.type = constants_1.KEYS.REF_ELEMENT;
    }
}
exports.ManagedReference = ManagedReference;
/**
 * 解析ref
 */
class RefResolver {
    constructor(factory) {
        this.factory = factory;
    }
    get type() {
        return constants_1.KEYS.REF_ELEMENT;
    }
    resolve(managed, originName) {
        var _a;
        const mr = managed;
        if (mr.injectMode === interface_1.InjectModeEnum.Class &&
            !((_a = this.factory.context.parent) !== null && _a !== void 0 ? _a : this.factory.context).hasDefinition(mr.name)) {
            if (originName === 'loggerService') {
                throw new error_1.MidwayInconsistentVersionError();
            }
            else {
                throw new error_1.MidwayMissingImportComponentError(originName);
            }
        }
        return this.factory.context.get(mr.name, mr.args, {
            originName,
        });
    }
    async resolveAsync(managed, originName) {
        var _a;
        const mr = managed;
        if (mr.injectMode === interface_1.InjectModeEnum.Class &&
            !((_a = this.factory.context.parent) !== null && _a !== void 0 ? _a : this.factory.context).hasDefinition(mr.name)) {
            if (originName === 'loggerService') {
                throw new error_1.MidwayInconsistentVersionError();
            }
            else {
                throw new error_1.MidwayMissingImportComponentError(originName);
            }
        }
        return this.factory.context.getAsync(mr.name, mr.args, {
            originName,
        });
    }
}
/**
 * 解析工厂
 */
class ManagedResolverFactory {
    constructor(context) {
        this.resolvers = {};
        this.creating = new Map();
        this.singletonCache = new Map();
        this.context = context;
        // 初始化解析器
        this.resolvers = {
            ref: new RefResolver(this),
        };
    }
    registerResolver(resolver) {
        this.resolvers[resolver.type] = resolver;
    }
    resolveManaged(managed, originPropertyName) {
        const resolver = this.resolvers[managed.type];
        if (!resolver || resolver.type !== managed.type) {
            throw new error_1.MidwayResolverMissingError(managed.type);
        }
        return resolver.resolve(managed, originPropertyName);
    }
    async resolveManagedAsync(managed, originPropertyName) {
        const resolver = this.resolvers[managed.type];
        if (!resolver || resolver.type !== managed.type) {
            throw new error_1.MidwayResolverMissingError(managed.type);
        }
        return resolver.resolveAsync(managed, originPropertyName);
    }
    /**
     * 同步创建对象
     * @param opt
     */
    create(opt) {
        const { definition, args } = opt;
        if (definition.isSingletonScope() &&
            this.singletonCache.has(definition.id)) {
            return this.singletonCache.get(definition.id);
        }
        // 如果非 null 表示已经创建 proxy
        let inst = this.createProxyReference(definition);
        if (inst) {
            return inst;
        }
        this.compareAndSetCreateStatus(definition);
        // 预先初始化依赖
        if (definition.hasDependsOn()) {
            for (const dep of definition.dependsOn) {
                this.context.get(dep, args);
            }
        }
        debugLog(`[core]: Create id = "${definition.name}" ${definition.id}.`);
        const Clzz = definition.creator.load();
        let constructorArgs = [];
        if (args && Array.isArray(args) && args.length > 0) {
            constructorArgs = args;
        }
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.BEFORE_CREATED, Clzz, {
            constructorArgs,
            definition,
            context: this.context,
        });
        inst = definition.creator.doConstruct(Clzz, constructorArgs, this.context);
        // binding ctx object
        if (definition.isRequestScope() &&
            definition.constructor.name === 'ObjectDefinition') {
            Object.defineProperty(inst, constants_1.REQUEST_OBJ_CTX_KEY, {
                value: this.context.get(constants_1.REQUEST_CTX_KEY),
                writable: false,
                enumerable: false,
            });
        }
        if (definition.properties) {
            const keys = definition.properties.propertyKeys();
            for (const key of keys) {
                this.checkSingletonInvokeRequest(definition, key);
                try {
                    inst[key] = this.resolveManaged(definition.properties.get(key), key);
                }
                catch (error) {
                    if (error_1.MidwayDefinitionNotFoundError.isClosePrototypeOf(error)) {
                        const className = definition.path.name;
                        error.updateErrorMsg(className);
                    }
                    this.removeCreateStatus(definition, true);
                    throw error;
                }
            }
        }
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.AFTER_CREATED, inst, {
            context: this.context,
            definition,
            replaceCallback: ins => {
                inst = ins;
            },
        });
        // after properties set then do init
        definition.creator.doInit(inst);
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.AFTER_INIT, inst, {
            context: this.context,
            definition,
        });
        if (definition.id) {
            if (definition.isSingletonScope()) {
                this.singletonCache.set(definition.id, inst);
                this.setInstanceScope(inst, interface_1.ScopeEnum.Singleton);
            }
            else if (definition.isRequestScope()) {
                this.context.registerObject(definition.id, inst);
                this.setInstanceScope(inst, interface_1.ScopeEnum.Request);
            }
            else {
                this.setInstanceScope(inst, interface_1.ScopeEnum.Prototype);
            }
        }
        this.removeCreateStatus(definition, true);
        return inst;
    }
    /**
     * 异步创建对象
     * @param opt
     */
    async createAsync(opt) {
        const { definition, args } = opt;
        if (definition.isSingletonScope() &&
            this.singletonCache.has(definition.id)) {
            debug(`id = ${definition.id}(${definition.name}) get from singleton cache.`);
            return this.singletonCache.get(definition.id);
        }
        // 如果非 null 表示已经创建 proxy
        let inst = this.createProxyReference(definition);
        if (inst) {
            debug(`id = ${definition.id}(${definition.name}) from proxy reference.`);
            return inst;
        }
        this.compareAndSetCreateStatus(definition);
        // 预先初始化依赖
        if (definition.hasDependsOn()) {
            for (const dep of definition.dependsOn) {
                debug('id = %s init depend %s.', definition.id, dep);
                await this.context.getAsync(dep, args);
            }
        }
        debugLog(`[core]: Create id = "${definition.name}" ${definition.id}.`);
        const Clzz = definition.creator.load();
        let constructorArgs = [];
        if (args && Array.isArray(args) && args.length > 0) {
            constructorArgs = args;
        }
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.BEFORE_CREATED, Clzz, {
            constructorArgs,
            context: this.context,
        });
        inst = await definition.creator.doConstructAsync(Clzz, constructorArgs, this.context);
        if (!inst) {
            this.removeCreateStatus(definition, false);
            throw new error_1.MidwayCommonError(`${definition.id} construct return undefined`);
        }
        // binding ctx object
        if (definition.isRequestScope() &&
            definition.constructor.name === 'ObjectDefinition') {
            debug('id = %s inject ctx', definition.id);
            // set related ctx
            Object.defineProperty(inst, constants_1.REQUEST_OBJ_CTX_KEY, {
                value: this.context.get(constants_1.REQUEST_CTX_KEY),
                writable: false,
                enumerable: false,
            });
        }
        if (definition.properties) {
            const keys = definition.properties.propertyKeys();
            for (const key of keys) {
                this.checkSingletonInvokeRequest(definition, key);
                try {
                    inst[key] = await this.resolveManagedAsync(definition.properties.get(key), key);
                }
                catch (error) {
                    if (error_1.MidwayDefinitionNotFoundError.isClosePrototypeOf(error)) {
                        const className = definition.path.name;
                        error.updateErrorMsg(className);
                    }
                    this.removeCreateStatus(definition, false);
                    throw error;
                }
            }
        }
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.AFTER_CREATED, inst, {
            context: this.context,
            definition,
            replaceCallback: ins => {
                inst = ins;
            },
        });
        // after properties set then do init
        await definition.creator.doInitAsync(inst);
        this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.AFTER_INIT, inst, {
            context: this.context,
            definition,
        });
        if (definition.id) {
            if (definition.isSingletonScope()) {
                debug(`id = ${definition.id}(${definition.name}) set to singleton cache`);
                this.singletonCache.set(definition.id, inst);
                this.setInstanceScope(inst, interface_1.ScopeEnum.Singleton);
            }
            else if (definition.isRequestScope()) {
                debug(`id = ${definition.id}(${definition.name}) set to register object`);
                this.context.registerObject(definition.id, inst);
                this.setInstanceScope(inst, interface_1.ScopeEnum.Request);
            }
            else {
                this.setInstanceScope(inst, interface_1.ScopeEnum.Prototype);
            }
        }
        this.removeCreateStatus(definition, true);
        return inst;
    }
    async destroyCache() {
        for (const key of this.singletonCache.keys()) {
            const definition = this.context.registry.getDefinition(key);
            if (definition.creator) {
                const inst = this.singletonCache.get(key);
                this.getObjectEventTarget().emit(interface_1.ObjectLifeCycleEvent.BEFORE_DESTROY, inst, {
                    context: this.context,
                    definition,
                });
                await definition.creator.doDestroyAsync(inst);
            }
        }
        this.singletonCache.clear();
        this.creating.clear();
    }
    /**
     * 触发单例初始化结束事件
     * @param definition 单例定义
     * @param success 成功 or 失败
     */
    removeCreateStatus(definition, success) {
        // 如果map中存在表示需要设置状态
        if (this.creating.has(definition.id)) {
            this.creating.set(definition.id, false);
        }
        return true;
    }
    isCreating(definition) {
        return this.creating.has(definition.id) && this.creating.get(definition.id);
    }
    compareAndSetCreateStatus(definition) {
        if (!this.creating.has(definition.id) ||
            !this.creating.get(definition.id)) {
            this.creating.set(definition.id, true);
        }
    }
    /**
     * 创建对象定义的代理访问逻辑
     * @param definition 对象定义
     */
    createProxyReference(definition) {
        if (this.isCreating(definition)) {
            debug('create proxy for %s.', definition.id);
            // 非循环依赖的允许重新创建对象
            if (!this.depthFirstSearch(definition.id, definition)) {
                debug('id = %s after dfs return null', definition.id);
                return null;
            }
            // 创建代理对象
            return new Proxy({ __is_proxy__: true, __target_id__: definition.id }, {
                get: (obj, prop) => {
                    let target;
                    if (definition.isRequestScope()) {
                        target = this.context.registry.getObject(definition.id);
                    }
                    else if (definition.isSingletonScope()) {
                        target = this.singletonCache.get(definition.id);
                    }
                    else {
                        target = this.context.get(definition.id);
                    }
                    if (target) {
                        if (typeof target[prop] === 'function') {
                            return target[prop].bind(target);
                        }
                        return target[prop];
                    }
                    return undefined;
                },
            });
        }
        return null;
    }
    /**
     * 遍历依赖树判断是否循环依赖
     * @param identifier 目标id
     * @param definition 定义描述
     * @param depth
     */
    depthFirstSearch(identifier, definition, depth) {
        var _a;
        if (definition) {
            debug('dfs for %s == %s start.', identifier, definition.id);
            if (definition.properties) {
                const keys = definition.properties.propertyKeys();
                if (keys.indexOf(identifier) > -1) {
                    debug('dfs exist in properties %s == %s.', identifier, definition.id);
                    return true;
                }
                for (const key of keys) {
                    if (!Array.isArray(depth)) {
                        depth = [identifier];
                    }
                    let iden = key;
                    const ref = definition.properties.get(key);
                    if (ref && ref.name) {
                        iden =
                            (_a = this.context.identifierMapping.getRelation(ref.name)) !== null && _a !== void 0 ? _a : ref.name;
                    }
                    if (iden === identifier) {
                        debug('dfs exist in properties key %s == %s.', identifier, definition.id);
                        return true;
                    }
                    if (depth.indexOf(iden) > -1) {
                        debug('dfs depth circular %s == %s, %s, %j.', identifier, definition.id, iden, depth);
                        continue;
                    }
                    else {
                        depth.push(iden);
                        debug('dfs depth push %s == %s, %j.', identifier, iden, depth);
                    }
                    let subDefinition = this.context.registry.getDefinition(iden);
                    if (!subDefinition && this.context.parent) {
                        subDefinition = this.context.parent.registry.getDefinition(iden);
                    }
                    if (this.depthFirstSearch(identifier, subDefinition, depth)) {
                        debug('dfs exist in sub tree %s == %s subId = %s.', identifier, definition.id, subDefinition.id);
                        return true;
                    }
                }
            }
            debug('dfs for %s == %s end.', identifier, definition.id);
        }
        return false;
    }
    getObjectEventTarget() {
        if (this.context.parent) {
            return this.context.parent.objectCreateEventTarget;
        }
        return this.context.objectCreateEventTarget;
    }
    checkSingletonInvokeRequest(definition, key) {
        if (definition.isSingletonScope()) {
            const managedRef = definition.properties.get(key);
            if (this.context.hasDefinition(managedRef === null || managedRef === void 0 ? void 0 : managedRef.name)) {
                const propertyDefinition = this.context.registry.getDefinition(managedRef.name);
                if (propertyDefinition.isRequestScope() &&
                    !propertyDefinition.allowDowngrade) {
                    throw new error_1.MidwaySingletonInjectRequestError(definition.path.name, propertyDefinition.path.name);
                }
            }
        }
        return true;
    }
    setInstanceScope(inst, scope) {
        if (typeof inst === 'object') {
            if (scope === interface_1.ScopeEnum.Request &&
                inst[constants_1.REQUEST_OBJ_CTX_KEY] === constants_1.SINGLETON_CONTAINER_CTX) {
                scope = interface_1.ScopeEnum.Singleton;
            }
            Object.defineProperty(inst, constants_1.CONTAINER_OBJ_SCOPE, {
                value: scope,
                writable: false,
                enumerable: false,
                configurable: false,
            });
        }
    }
}
exports.ManagedResolverFactory = ManagedResolverFactory;
//# sourceMappingURL=managedResolverFactory.js.map