{"version": 3, "sources": ["../browser/src/platform/BrowserDirectoryExportedClassesLoader.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAIH;;GAEG;AACH,MAAM,UAAU,4BAA4B,CAAC,MAAc,EAAE,WAAqB,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC;IAChH,OAAO,EAAE,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,0BAA0B,CAAC,WAAqB,EAAE,MAAM,GAAG,OAAO;IAC9E,OAAO,EAAE,CAAC;AACd,CAAC", "file": "BrowserDirectoryExportedClassesLoader.js", "sourcesContent": ["/**\n * Dummy functions for replacement via `package.json` in browser builds.\n *\n * If we don't include these functions typeorm will throw an error on runtime\n * as well as during webpack builds.\n */\n\nimport {Logger} from \"../logger/Logger\";\n\n/**\n * Loads all exported classes from the given directory.\n */\nexport function importClassesFromDirectories(logger: Logger, directories: string[], formats = [\".js\", \".cjs\", \".ts\"]): Function[] {\n    return [];\n}\n\n/**\n * Loads all json files from the given directory.\n */\nexport function importJsonsFromDirectories(directories: string[], format = \".json\"): any[] {\n    return [];\n}\n"], "sourceRoot": ".."}