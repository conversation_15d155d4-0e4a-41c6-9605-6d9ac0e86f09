{"acl": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "append": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "asking": {"arity": 1, "flags": ["fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "auth": {"arity": -2, "flags": ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "bgrewriteaof": {"arity": 1, "flags": ["admin", "noscript", "no_async_loading"], "keyStart": 0, "keyStop": 0, "step": 0}, "bgsave": {"arity": -1, "flags": ["admin", "noscript", "no_async_loading"], "keyStart": 0, "keyStop": 0, "step": 0}, "bitcount": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "bitfield": {"arity": -2, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "bitfield_ro": {"arity": -2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "bitop": {"arity": -4, "flags": ["write", "denyoom"], "keyStart": 2, "keyStop": -1, "step": 1}, "bitpos": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "blmove": {"arity": 6, "flags": ["write", "denyoom", "noscript", "blocking"], "keyStart": 1, "keyStop": 2, "step": 1}, "blmpop": {"arity": -5, "flags": ["write", "blocking", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "blpop": {"arity": -3, "flags": ["write", "noscript", "blocking"], "keyStart": 1, "keyStop": -2, "step": 1}, "brpop": {"arity": -3, "flags": ["write", "noscript", "blocking"], "keyStart": 1, "keyStop": -2, "step": 1}, "brpoplpush": {"arity": 4, "flags": ["write", "denyoom", "noscript", "blocking"], "keyStart": 1, "keyStop": 2, "step": 1}, "bzmpop": {"arity": -5, "flags": ["write", "blocking", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "bzpopmax": {"arity": -3, "flags": ["write", "noscript", "blocking", "fast"], "keyStart": 1, "keyStop": -2, "step": 1}, "bzpopmin": {"arity": -3, "flags": ["write", "noscript", "blocking", "fast"], "keyStart": 1, "keyStop": -2, "step": 1}, "client": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "cluster": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "command": {"arity": -1, "flags": ["loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "config": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "copy": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 2, "step": 1}, "dbsize": {"arity": 1, "flags": ["readonly", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "debug": {"arity": -2, "flags": ["admin", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "decr": {"arity": 2, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "decrby": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "del": {"arity": -2, "flags": ["write"], "keyStart": 1, "keyStop": -1, "step": 1}, "discard": {"arity": 1, "flags": ["noscript", "loading", "stale", "fast", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "dump": {"arity": 2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "echo": {"arity": 2, "flags": ["fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "eval": {"arity": -3, "flags": ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "eval_ro": {"arity": -3, "flags": ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "evalsha": {"arity": -3, "flags": ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "evalsha_ro": {"arity": -3, "flags": ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "exec": {"arity": 1, "flags": ["noscript", "loading", "stale", "skip_slowlog"], "keyStart": 0, "keyStop": 0, "step": 0}, "exists": {"arity": -2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": -1, "step": 1}, "expire": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "expireat": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "expiretime": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "failover": {"arity": -1, "flags": ["admin", "noscript", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "fcall": {"arity": -3, "flags": ["noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "fcall_ro": {"arity": -3, "flags": ["readonly", "noscript", "stale", "skip_monitor", "no_mandatory_keys", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "flushall": {"arity": -1, "flags": ["write"], "keyStart": 0, "keyStop": 0, "step": 0}, "flushdb": {"arity": -1, "flags": ["write"], "keyStart": 0, "keyStop": 0, "step": 0}, "function": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "geoadd": {"arity": -5, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "geodist": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "geohash": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "geopos": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "georadius": {"arity": -6, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "georadius_ro": {"arity": -6, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "georadiusbymember": {"arity": -5, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "georadiusbymember_ro": {"arity": -5, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "geosearch": {"arity": -7, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "geosearchstore": {"arity": -8, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 2, "step": 1}, "get": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "getbit": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "getdel": {"arity": 2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "getex": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "getrange": {"arity": 4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "getset": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hdel": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hello": {"arity": -1, "flags": ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "hexists": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hget": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hgetall": {"arity": 2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "hincrby": {"arity": 4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hincrbyfloat": {"arity": 4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hkeys": {"arity": 2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "hlen": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hmget": {"arity": -3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hmset": {"arity": -4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hrandfield": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "hscan": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "hset": {"arity": -4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hsetnx": {"arity": 4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hstrlen": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "hvals": {"arity": 2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "incr": {"arity": 2, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "incrby": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "incrbyfloat": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "info": {"arity": -1, "flags": ["loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "keys": {"arity": 2, "flags": ["readonly"], "keyStart": 0, "keyStop": 0, "step": 0}, "lastsave": {"arity": 1, "flags": ["loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "latency": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "lcs": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 2, "step": 1}, "lindex": {"arity": 3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "linsert": {"arity": 5, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "llen": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "lmove": {"arity": 5, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 2, "step": 1}, "lmpop": {"arity": -4, "flags": ["write", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "lolwut": {"arity": -1, "flags": ["readonly", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "lpop": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "lpos": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "lpush": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "lpushx": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "lrange": {"arity": 4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "lrem": {"arity": 4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "lset": {"arity": 4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "ltrim": {"arity": 4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "memory": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "mget": {"arity": -2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": -1, "step": 1}, "migrate": {"arity": -6, "flags": ["write", "movablekeys"], "keyStart": 3, "keyStop": 3, "step": 1}, "module": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "monitor": {"arity": 1, "flags": ["admin", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "move": {"arity": 3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "mset": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 2}, "msetnx": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 2}, "multi": {"arity": 1, "flags": ["noscript", "loading", "stale", "fast", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "object": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "persist": {"arity": 2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "pexpire": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "pexpireat": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "pexpiretime": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "pfadd": {"arity": -2, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "pfcount": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": -1, "step": 1}, "pfdebug": {"arity": 3, "flags": ["write", "denyoom", "admin"], "keyStart": 2, "keyStop": 2, "step": 1}, "pfmerge": {"arity": -2, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 1}, "pfselftest": {"arity": 1, "flags": ["admin"], "keyStart": 0, "keyStop": 0, "step": 0}, "ping": {"arity": -1, "flags": ["fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "psetex": {"arity": 4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "psubscribe": {"arity": -2, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "psync": {"arity": -3, "flags": ["admin", "noscript", "no_async_loading", "no_multi"], "keyStart": 0, "keyStop": 0, "step": 0}, "pttl": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "publish": {"arity": 3, "flags": ["pubsub", "loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "pubsub": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "punsubscribe": {"arity": -1, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "quit": {"arity": -1, "flags": ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "randomkey": {"arity": 1, "flags": ["readonly"], "keyStart": 0, "keyStop": 0, "step": 0}, "readonly": {"arity": 1, "flags": ["loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "readwrite": {"arity": 1, "flags": ["loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "rename": {"arity": 3, "flags": ["write"], "keyStart": 1, "keyStop": 2, "step": 1}, "renamenx": {"arity": 3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 2, "step": 1}, "replconf": {"arity": -1, "flags": ["admin", "noscript", "loading", "stale", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "replicaof": {"arity": 3, "flags": ["admin", "noscript", "stale", "no_async_loading"], "keyStart": 0, "keyStop": 0, "step": 0}, "reset": {"arity": 1, "flags": ["noscript", "loading", "stale", "fast", "no_auth", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "restore": {"arity": -4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "restore-asking": {"arity": -4, "flags": ["write", "denyoom", "asking"], "keyStart": 1, "keyStop": 1, "step": 1}, "role": {"arity": 1, "flags": ["noscript", "loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "rpop": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "rpoplpush": {"arity": 3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 2, "step": 1}, "rpush": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "rpushx": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "sadd": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "save": {"arity": 1, "flags": ["admin", "noscript", "no_async_loading", "no_multi"], "keyStart": 0, "keyStop": 0, "step": 0}, "scan": {"arity": -2, "flags": ["readonly"], "keyStart": 0, "keyStop": 0, "step": 0}, "scard": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "script": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "sdiff": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": -1, "step": 1}, "sdiffstore": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 1}, "select": {"arity": 2, "flags": ["loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "set": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "setbit": {"arity": 4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "setex": {"arity": 4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "setnx": {"arity": 3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "setrange": {"arity": 4, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 1, "step": 1}, "shutdown": {"arity": -1, "flags": ["admin", "noscript", "loading", "stale", "no_multi", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "sinter": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": -1, "step": 1}, "sintercard": {"arity": -3, "flags": ["readonly", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "sinterstore": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 1}, "sismember": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "slaveof": {"arity": 3, "flags": ["admin", "noscript", "stale", "no_async_loading"], "keyStart": 0, "keyStop": 0, "step": 0}, "slowlog": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "smembers": {"arity": 2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "smismember": {"arity": -3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "smove": {"arity": 4, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 2, "step": 1}, "sort": {"arity": -2, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "sort_ro": {"arity": -2, "flags": ["readonly", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "spop": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "spublish": {"arity": 3, "flags": ["pubsub", "loading", "stale", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "srandmember": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "srem": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "sscan": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "ssubscribe": {"arity": -2, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 1, "keyStop": -1, "step": 1}, "strlen": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "subscribe": {"arity": -2, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "substr": {"arity": 4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "sunion": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": -1, "step": 1}, "sunionstore": {"arity": -3, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": -1, "step": 1}, "sunsubscribe": {"arity": -1, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 1, "keyStop": -1, "step": 1}, "swapdb": {"arity": 3, "flags": ["write", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "sync": {"arity": 1, "flags": ["admin", "noscript", "no_async_loading", "no_multi"], "keyStart": 0, "keyStop": 0, "step": 0}, "time": {"arity": 1, "flags": ["loading", "stale", "fast"], "keyStart": 0, "keyStop": 0, "step": 0}, "touch": {"arity": -2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": -1, "step": 1}, "ttl": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "type": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "unlink": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": -1, "step": 1}, "unsubscribe": {"arity": -1, "flags": ["pubsub", "noscript", "loading", "stale"], "keyStart": 0, "keyStop": 0, "step": 0}, "unwatch": {"arity": 1, "flags": ["noscript", "loading", "stale", "fast", "allow_busy"], "keyStart": 0, "keyStop": 0, "step": 0}, "wait": {"arity": 3, "flags": ["noscript"], "keyStart": 0, "keyStop": 0, "step": 0}, "watch": {"arity": -2, "flags": ["noscript", "loading", "stale", "fast", "allow_busy"], "keyStart": 1, "keyStop": -1, "step": 1}, "xack": {"arity": -4, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xadd": {"arity": -5, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xautoclaim": {"arity": -6, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xclaim": {"arity": -6, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xdel": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xgroup": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "xinfo": {"arity": -2, "flags": [], "keyStart": 0, "keyStop": 0, "step": 0}, "xlen": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xpending": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "xrange": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "xread": {"arity": -4, "flags": ["readonly", "blocking", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "xreadgroup": {"arity": -7, "flags": ["write", "blocking", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "xrevrange": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "xsetid": {"arity": -3, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "xtrim": {"arity": -4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "zadd": {"arity": -4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zcard": {"arity": 2, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zcount": {"arity": 4, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zdiff": {"arity": -3, "flags": ["readonly", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "zdiffstore": {"arity": -4, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "zincrby": {"arity": 4, "flags": ["write", "denyoom", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zinter": {"arity": -3, "flags": ["readonly", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "zintercard": {"arity": -3, "flags": ["readonly", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "zinterstore": {"arity": -4, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}, "zlexcount": {"arity": 4, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zmpop": {"arity": -4, "flags": ["write", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "zmscore": {"arity": -3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zpopmax": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zpopmin": {"arity": -2, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrandmember": {"arity": -2, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrange": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrangebylex": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrangebyscore": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrangestore": {"arity": -5, "flags": ["write", "denyoom"], "keyStart": 1, "keyStop": 2, "step": 1}, "zrank": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrem": {"arity": -3, "flags": ["write", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zremrangebylex": {"arity": 4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "zremrangebyrank": {"arity": 4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "zremrangebyscore": {"arity": 4, "flags": ["write"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrevrange": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrevrangebylex": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrevrangebyscore": {"arity": -4, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zrevrank": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zscan": {"arity": -3, "flags": ["readonly"], "keyStart": 1, "keyStop": 1, "step": 1}, "zscore": {"arity": 3, "flags": ["readonly", "fast"], "keyStart": 1, "keyStop": 1, "step": 1}, "zunion": {"arity": -3, "flags": ["readonly", "movablekeys"], "keyStart": 0, "keyStop": 0, "step": 0}, "zunionstore": {"arity": -4, "flags": ["write", "denyoom", "movablekeys"], "keyStart": 1, "keyStop": 1, "step": 1}}