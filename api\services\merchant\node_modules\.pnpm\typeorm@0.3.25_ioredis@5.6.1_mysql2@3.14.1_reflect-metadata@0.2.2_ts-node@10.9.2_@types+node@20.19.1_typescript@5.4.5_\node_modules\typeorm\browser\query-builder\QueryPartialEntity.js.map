{"version": 3, "sources": ["../browser/src/query-builder/QueryPartialEntity.ts"], "names": [], "mappings": "", "file": "QueryPartialEntity.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\n\n/**\n * Make all properties in T optional\n */\nexport type QueryPartialEntity<T> = {\n    [P in keyof T]?: T[P] | (() => string)\n}\n\n/**\n * Make all properties in T optional. Deep version.\n */\nexport type QueryDeepPartialEntity<T> = _QueryDeepPartialEntity<\n    ObjectLiteral extends T ? unknown : T\n>\n\ntype _QueryDeepPartialEntity<T> = {\n    [P in keyof T]?:\n        | (T[P] extends Array<infer U>\n              ? Array<_QueryDeepPartialEntity<U>>\n              : T[P] extends ReadonlyArray<infer U>\n              ? ReadonlyArray<_QueryDeepPartialEntity<U>>\n              : _QueryDeepPartialEntity<T[P]>)\n        | (() => string)\n}\n"], "sourceRoot": ".."}