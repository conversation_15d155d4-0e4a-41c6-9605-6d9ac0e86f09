"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EqualOperator = void 0;
const FindOperator_1 = require("./FindOperator");
class EqualOperator extends FindOperator_1.FindOperator {
    constructor(value) {
        super("equal", value);
        this["@instanceof"] = Symbol.for("EqualOperator");
    }
}
exports.EqualOperator = EqualOperator;

//# sourceMappingURL=EqualOperator.js.map
