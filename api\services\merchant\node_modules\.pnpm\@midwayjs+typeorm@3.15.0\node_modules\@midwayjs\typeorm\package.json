{"name": "@midwayjs/typeorm", "version": "3.15.0", "main": "dist/index.js", "typings": "index.d.ts", "bin": {"mwtypeorm": "./cli.js"}, "files": ["dist/**/*.js", "dist/**/*.d.ts", "cli.js", "index.d.ts"], "devDependencies": {"@midwayjs/core": "^3.15.0", "@midwayjs/mock": "^3.15.0", "sqlite3": "5.1.7", "typeorm": "0.3.20"}, "author": {"name": "czy88840616", "email": "<EMAIL>"}, "engines": {"node": ">=12"}, "license": "MIT", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit"}, "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "gitHead": "be0a091f940aa60965d9fabfbdcbf0fe2830e9c4"}