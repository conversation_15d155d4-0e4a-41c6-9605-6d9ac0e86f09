import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 用户开票信息
 */
@Entity('finance_user_invoice')
export class FinanceUserInvoiceEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '类型 0-普票 1-专票', default: 0 })
  type: number;

  @Column({ comment: '邮箱' })
  email: string;

  @Column({ comment: '抬头类型 0-个人 1-公司', default: 0 })
  headerType: number;

  @Column({ comment: '发票抬头' })
  header: string;

  @Column({ comment: '税号', nullable: true })
  taxNo: string;

  @Column({ comment: '注册地址', nullable: true })
  registerAddress: string;

  @Column({ comment: '注册电话', nullable: true })
  registerPhone: string;

  @Column({ comment: '开户行', nullable: true })
  bank: string;

  @Column({ comment: '银行账号', nullable: true })
  bankAccount: string;
}
