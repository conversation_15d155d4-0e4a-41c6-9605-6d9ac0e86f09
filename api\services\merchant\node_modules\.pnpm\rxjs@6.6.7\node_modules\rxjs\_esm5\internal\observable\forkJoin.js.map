{"version": 3, "file": "forkJoin.js", "sources": ["../../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAsI9B,MAAM,UAAU,QAAQ;IACtB,iBAAiB;SAAjB,UAAiB,EAAjB,qBAAiB,EAAjB,IAAiB;QAAjB,4BAAiB;;IAEjB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,IAAM,OAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,OAAO,CAAC,OAAK,CAAC,EAAE;YAClB,OAAO,gBAAgB,CAAC,OAAK,EAAE,IAAI,CAAC,CAAC;SACtC;QAED,IAAI,QAAQ,CAAC,OAAK,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,OAAK,CAAC,KAAK,MAAM,CAAC,SAAS,EAAE;YACxE,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAK,CAAC,CAAC;YAChC,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,GAAG,IAAI,OAAA,OAAK,CAAC,GAAG,CAAC,EAAV,CAAU,CAAC,EAAE,IAAI,CAAC,CAAC;SAC5D;KACF;IAGD,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QACrD,IAAM,gBAAc,GAAG,OAAO,CAAC,GAAG,EAAc,CAAC;QACjD,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/E,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CACzC,GAAG,CAAC,UAAC,IAAW,IAAK,OAAA,gBAAc,eAAI,IAAI,GAAtB,CAAuB,CAAC,CAC9C,CAAC;KACH;IAED,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,gBAAgB,CAAC,OAA+B,EAAE,IAAqB;IAC9E,OAAO,IAAI,UAAU,CAAC,UAAA,UAAU;QAC9B,IAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC;gCACP,CAAC;YACR,IAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC9B,IAAI,EAAE,UAAA,KAAK;oBACT,IAAI,CAAC,QAAQ,EAAE;wBACb,QAAQ,GAAG,IAAI,CAAC;wBAChB,OAAO,EAAE,CAAC;qBACX;oBACD,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpB,CAAC;gBACD,KAAK,EAAE,UAAA,GAAG,IAAI,OAAA,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,EAArB,CAAqB;gBACnC,QAAQ,EAAE;oBACR,SAAS,EAAE,CAAC;oBACZ,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;wBAClC,IAAI,OAAO,KAAK,GAAG,EAAE;4BACnB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCACpB,IAAI,CAAC,MAAM,CAAC,UAAC,MAAM,EAAE,GAAG,EAAE,CAAC,IAAK,OAAA,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAjC,CAAiC,EAAE,EAAE,CAAC,CAAC,CAAC;gCACxE,MAAM,CAAC,CAAC;yBACX;wBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;qBACvB;gBACH,CAAC;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAxBD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;oBAAnB,CAAC;SAwBT;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}