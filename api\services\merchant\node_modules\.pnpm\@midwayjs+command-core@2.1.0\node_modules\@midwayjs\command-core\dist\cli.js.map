{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../src/cli.ts"], "names": [], "mappings": ";;;AAAA,qCAAqC;AACrC,+BAA4B;AAC5B,+DAA4D;AAC5D,iCAAqC;AACrC,2BAA8C;AAE9C,MAAa,WAAW;IAStB,YAAY,IAAI;QAFhB,QAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAGlB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACrC;aAAM;YACL,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;QACD,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,IAAI,CAAC,QAAQ,EAAE,CAAC;IAClB,CAAC;IAED,QAAQ;QACN,IAAI,CAAC,IAAI,GAAG,IAAI,kBAAW,CAAC;YAC1B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,IAAI,CAAC,IAAI;YAClB,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE;YACnB,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;YAC1C,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE;YACjC,GAAG,IAAI,CAAC,gBAAgB,EAAE;SAC3B,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW;QACf,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;QAC5B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC/B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAED,cAAc,KAAI,CAAC;IAEnB,SAAS;IACT,iBAAiB;;QACf,MAAM,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACrB,MAAM,eAAe,GAAG,IAAA,WAAI,EAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAClD,IAAI,CAAC,IAAA,eAAU,EAAC,eAAe,CAAC,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE,eAAe,CAAC,CAAC;YACzD,OAAO;SACR;QACD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QACzE,MAAM,IAAI,GAAG,CAAA,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAG,YAAY,CAAC,0CAAE,OAAO,KAAI,EAAE,CAAC;QACxD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,OAAO,CAAC;YACZ,IAAI;gBACF,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;aAClD;YAAC,OAAO,KAAK,EAAE;gBACd,MAAM,IAAI,KAAK,CAAC,+BAA+B,GAAG,eAAe,CAAC,CAAC;aACpE;YACD,IAAI;gBACF,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;gBAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;aAC1B;YAAC,OAAO,CAAC,EAAE;gBACV,CAAC,CAAC,OAAO,GAAG,8BAA8B,CAAC,CAAC,OAAO,EAAE,CAAC;gBACtD,MAAM,CAAC,CAAC;aACT;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,UAAU;IACV,kBAAkB,KAAI,CAAC;IAEvB,UAAU;IACV,cAAc;QACZ,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,qBAAqB;IACrB,gBAAgB;QACd,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,aAAa;IACb,OAAO;QACL,OAAO,EAAE,GAAG,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;IAC3C,CAAC;IAED,YAAY,CAAC,aAAoB,EAAE,KAAK,EAAE,YAAY,EAAE,WAAY;QAClE,IAAI,WAAW,GAAQ,EAAE,CAAC;QAC1B,IAAI,aAAa,IAAI,aAAa,CAAC,MAAM,EAAE;YACzC,WAAW,GAAG;gBACZ,MAAM,EACJ,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC;oBACvB,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,EAAC,CAAC,CAAC,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrD,OAAO,EAAE,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK;gBAC3B,UAAU,EAAE,KAAK;oBACf,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;wBAC5B,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;wBACpC,OAAO;4BACL,IAAI;4BACJ,WAAW,EAAE,SAAS,CAAC,KAAK;4BAC5B,KAAK,EAAE,SAAS,CAAC,QAAQ;yBAC1B,CAAC;oBACJ,CAAC,CAAC;oBACJ,CAAC,CAAC,EAAE;gBACN,aAAa,EAAE,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ;oBAClC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;wBAC/C,MAAM,gBAAgB,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACxD,OAAO,IAAI,CAAC,YAAY,CACtB,CAAC,OAAO,CAAC,EACT,gBAAgB,CAAC,OAAO,EACxB,YAAY,EACZ,gBAAgB,CACjB,CAAC;oBACJ,CAAC,CAAC;oBACJ,CAAC,CAAC,IAAI;aACT,CAAC;SACH;aAAM;YACL,WAAW,GAAG,EAAE,CAAC;YACjB,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACtC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;oBACpB,OAAO;iBACR;gBACD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC7C,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;oBAC7C,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;wBAChD,OAAO;qBACR;oBACD,WAAW,CAAC,IAAI,CAAC;wBACf,MAAM,EAAE,OAAO;wBACf,OAAO,EAAE,WAAW,CAAC,KAAK;wBAC1B,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;4BAC5D,MAAM,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BAClD,OAAO;gCACL,IAAI;gCACJ,WAAW,EAAE,SAAS,CAAC,KAAK;gCAC5B,KAAK,EAAE,SAAS,CAAC,QAAQ;6BAC1B,CAAC;wBACJ,CAAC,CAAC;wBACF,aAAa,EAAE,WAAW,CAAC,QAAQ;4BACjC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;gCAC9C,MAAM,gBAAgB,GAAG,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gCACvD,OAAO,IAAI,CAAC,YAAY,CACtB,CAAC,OAAO,CAAC,EACT,gBAAgB,CAAC,OAAO,EACxB,YAAY,EACZ,gBAAgB,CACjB,CAAC;4BACJ,CAAC,CAAC;4BACJ,CAAC,CAAC,IAAI;qBACT,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;SACJ;QACD,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,SAAS;IACT,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,WAAY;QAC3D,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CACnC,aAAa,EACb,KAAK,EACL,YAAY,EACZ,WAAW,CACZ,CAAC;QACF,GAAG,CAAC,GAAG,CAAC,IAAA,mCAAgB,EAAC,WAAW,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CAAC,GAAG;QACP,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;QAC3C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,IAAI;QAC9B,IAAI;YACF,MAAM,WAAW,GAAG,OAAO,CAAC,IAAA,WAAI,EAAC,IAAI,CAAC,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;YAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;QACzB,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QACxB,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;IAC3B,CAAC;CACF;AA1LD,kCA0LC"}