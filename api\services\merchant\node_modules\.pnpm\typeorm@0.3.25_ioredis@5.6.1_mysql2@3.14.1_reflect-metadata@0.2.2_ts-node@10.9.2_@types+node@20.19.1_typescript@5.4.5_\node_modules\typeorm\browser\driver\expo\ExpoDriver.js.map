{"version": 3, "sources": ["../browser/src/driver/expo/ExpoDriver.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,yCAAyC,CAAA;AAE9E,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAA;AAInD,MAAM,OAAO,UAAW,SAAQ,oBAAoB;IAGhD,YAAY,UAAsB;QAC9B,KAAK,CAAC,UAAU,CAAC,CAAA;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;IACrC,CAAC;IAED,KAAK,CAAC,UAAU;QACZ,IAAI,CAAC,WAAW,GAAG,SAAS,CAAA;QAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC,UAAU,EAAE,CAAA;QAC1C,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;IACvC,CAAC;IAED,iBAAiB;QACb,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAA;QAEnE,OAAO,IAAI,CAAC,WAAW,CAAA;IAC3B,CAAC;IAES,KAAK,CAAC,wBAAwB;QACpC,IAAI,CAAC,kBAAkB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CACzD,IAAI,CAAC,OAAO,CAAC,QAAQ,CACxB,CAAA;QACD,MAAM,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,0BAA0B,CAAC,CAAA;QAClE,OAAO,IAAI,CAAC,kBAAkB,CAAA;IAClC,CAAC;CACJ", "file": "ExpoDriver.js", "sourcesContent": ["import { AbstractSqliteDriver } from \"../sqlite-abstract/AbstractSqliteDriver\"\nimport { ExpoConnectionOptions } from \"./ExpoConnectionOptions\"\nimport { ExpoQueryRunner } from \"./ExpoQueryRunner\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { DataSource } from \"../../data-source/DataSource\"\n\nexport class ExpoDriver extends AbstractSqliteDriver {\n    options: ExpoConnectionOptions\n\n    constructor(connection: DataSource) {\n        super(connection)\n        this.sqlite = this.options.driver\n    }\n\n    async disconnect(): Promise<void> {\n        this.queryRunner = undefined\n        await this.databaseConnection.closeAsync()\n        this.databaseConnection = undefined\n    }\n\n    createQueryRunner(): QueryRunner {\n        if (!this.queryRunner) this.queryRunner = new ExpoQueryRunner(this)\n\n        return this.queryRunner\n    }\n\n    protected async createDatabaseConnection() {\n        this.databaseConnection = await this.sqlite.openDatabaseAsync(\n            this.options.database,\n        )\n        await this.databaseConnection.runAsync(\"PRAGMA foreign_keys = ON\")\n        return this.databaseConnection\n    }\n}\n"], "sourceRoot": "../.."}