-- =====================================================
-- 商户微服务数据库修复脚本
-- 解决索引冲突和表不存在问题
-- =====================================================

USE `merchant_service_db`;

-- ======================================
-- 第一步：清理有问题的表和索引
-- ======================================

-- 删除有问题的用户表
DROP TABLE IF EXISTS `merchant_sys_user`;
DROP TABLE IF EXISTS `merchant_sys_role`;
DROP TABLE IF EXISTS `merchant_sys_menu`;
DROP TABLE IF EXISTS `merchant`;

-- ======================================
-- 第二步：创建商户表（核心业务表）
-- ======================================

CREATE TABLE `merchant` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '商户类型 0-个人 1-企业',
  `name` varchar(100) DEFAULT NULL COMMENT '商户名称/个人姓名',
  `contact` varchar(100) DEFAULT NULL COMMENT '联系人',
  `phone` varchar(20) NOT NULL COMMENT '联系电话',
  `idCard` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `idCardFront` varchar(500) DEFAULT NULL COMMENT '身份证正面',
  `idCardBack` varchar(500) DEFAULT NULL COMMENT '身份证反面',
  `category` json DEFAULT NULL COMMENT '入驻商品类别',
  `intro` text COMMENT '店铺简介/创作背景',
  `bankAccount` varchar(50) DEFAULT NULL COMMENT '银行卡号',
  `bankName` varchar(100) DEFAULT NULL COMMENT '开户行',
  `bankUserName` varchar(100) DEFAULT NULL COMMENT '持卡人姓名',
  `promise` varchar(500) DEFAULT NULL COMMENT '原创承诺书',
  `companyName` varchar(200) DEFAULT NULL COMMENT '企业名称',
  `creditCode` varchar(50) DEFAULT NULL COMMENT '统一社会信用代码',
  `licenseImg` varchar(500) DEFAULT NULL COMMENT '营业执照图片',
  `legalPerson` varchar(100) DEFAULT NULL COMMENT '法人姓名',
  `legalIdCard` varchar(18) DEFAULT NULL COMMENT '法人身份证号',
  `legalIdCardFront` varchar(500) DEFAULT NULL COMMENT '法人身份证正面',
  `legalIdCardBack` varchar(500) DEFAULT NULL COMMENT '法人身份证反面',
  `projectCert` json DEFAULT NULL COMMENT '项目相关非遗认证/授权证明',
  `brandLogo` varchar(500) DEFAULT NULL COMMENT '品牌logo',
  `brandIntro` text COMMENT '品牌/机构介绍',
  `email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态 0-待审核 1-营业中 2-已拒绝 3-已注销 4-已封禁',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_phone` (`phone`),
  KEY `idx_createTime` (`createTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户表';

-- ======================================
-- 第三步：创建用户管理表
-- ======================================

CREATE TABLE `merchant_sys_user` (
  `userId` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `userName` varchar(100) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `passwordV` int(11) NOT NULL DEFAULT '1' COMMENT '密码版本',
  `roles` text COMMENT '角色权限数组',
  `buttons` text COMMENT '按钮权限数组',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态 1-启用 0-禁用',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_userName` (`userName`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统用户表';

-- ======================================
-- 第四步：创建角色表
-- ======================================

CREATE TABLE `merchant_sys_role` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `name` varchar(100) NOT NULL COMMENT '角色名称',
  `label` varchar(100) NOT NULL COMMENT '角色标识',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注描述',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用 0-禁用',
  `relevance` int(11) NOT NULL DEFAULT '1' COMMENT '数据权限',
  `menuIdList` text COMMENT '菜单权限ID列表',
  `departmentIdList` text COMMENT '部门权限ID列表',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_label` (`label`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统角色表';

-- ======================================
-- 第五步：创建菜单表
-- ======================================

CREATE TABLE `merchant_sys_menu` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `parentId` int(11) NOT NULL DEFAULT '0' COMMENT '父菜单ID',
  `name` varchar(100) NOT NULL COMMENT '菜单名称',
  `router` varchar(200) DEFAULT NULL COMMENT '路由地址',
  `perms` varchar(200) DEFAULT NULL COMMENT '权限标识',
  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '菜单类型 0-目录 1-菜单 2-按钮',
  `icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `orderNum` int(11) NOT NULL DEFAULT '0' COMMENT '显示顺序',
  `keepAlive` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否缓存',
  `isShow` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否显示',
  `createTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parentId` (`parentId`),
  KEY `idx_type` (`type`),
  KEY `idx_orderNum` (`orderNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户系统菜单表';

-- ======================================
-- 第六步：插入初始数据
-- ======================================

-- 插入测试用户 (admin/admin -> 21232f297a57a5a743894a0e4a801fc3)
INSERT INTO `merchant_sys_user` (`userId`, `userName`, `password`, `passwordV`, `roles`, `buttons`, `avatar`, `email`, `phone`, `status`) VALUES
(1, 'admin', '21232f297a57a5a743894a0e4a801fc3', 1, '["admin","super"]', '["add","edit","delete","view"]', NULL, '<EMAIL>', '13800138000', 1);

-- 插入角色数据
INSERT INTO `merchant_sys_role` (`id`, `name`, `label`, `remark`, `status`, `relevance`, `menuIdList`, `departmentIdList`) VALUES
(1, '超级管理员', 'admin', '系统超级管理员，拥有所有权限', 1, 1, '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30]', '[]'),
(2, '商户管理员', 'merchant_admin', '商户管理员，负责商户相关业务', 1, 1, '[1,2,20,21,22,23]', '[]');

-- 插入菜单数据
INSERT INTO `merchant_sys_menu` (`id`, `parentId`, `name`, `router`, `perms`, `type`, `icon`, `orderNum`, `keepAlive`, `isShow`) VALUES
-- 一级菜单
(1, 0, '首页', '/dashboard', 'dashboard:view', 1, '&#xe7ae;', 1, 1, 1),
(2, 0, '商户管理', '/merchant', 'merchant:view', 0, '&#xe7b4;', 2, 0, 1),
(3, 0, '系统管理', '/system', 'system:view', 0, '&#xe7ca;', 3, 0, 1),

-- 商户管理子菜单
(20, 2, '商户入驻', '/merchant/settle-in', 'merchant:settle-in', 1, '', 1, 1, 1),
(21, 2, '非遗人认证', '/merchant/heritage-auth', 'merchant:heritage-auth', 1, '', 2, 1, 1),
(22, 2, '个人商户', '/merchant/personal', 'merchant:personal', 1, '', 3, 1, 1),
(23, 2, '企业商户', '/merchant/company', 'merchant:company', 1, '', 4, 1, 1),

-- 系统管理子菜单
(30, 3, '用户管理', '/system/user', 'system:user', 1, '', 1, 1, 1),
(31, 3, '角色管理', '/system/role', 'system:role', 1, '', 2, 1, 1),
(32, 3, '菜单管理', '/system/menu', 'system:menu', 1, '', 3, 1, 1);

-- 插入测试商户数据
INSERT INTO `merchant` (`id`, `type`, `name`, `phone`, `idCard`, `category`, `intro`, `status`, `createTime`) VALUES
(1, 0, '张三', '13800138001', '110101199001011234', '["工艺品","文具"]', '专注传统手工艺品制作', 1, NOW()),
(2, 0, '李四', '13800138002', '110101199002021234', '["饰品","文玩"]', '传统饰品设计师', 1, NOW()),
(3, 1, '传统工艺有限公司', '13800138003', NULL, '["工艺品","礼品"]', '传统工艺品生产企业', 1, NOW());

INSERT INTO `merchant` (`id`, `type`, `companyName`, `phone`, `creditCode`, `legalPerson`, `category`, `brandIntro`, `status`, `createTime`) VALUES
(4, 1, '非遗文化传承有限公司', '13800138004', '91110000123456789X', '王五', '["非遗产品","文化用品"]', '致力于非遗文化传承与创新', 1, NOW());

-- ======================================
-- 第七步：验证创建结果
-- ======================================

-- 查看表是否创建成功
SHOW TABLES;

-- 查看数据是否插入成功
SELECT COUNT(*) as merchant_count FROM merchant;
SELECT COUNT(*) as user_count FROM merchant_sys_user;
SELECT COUNT(*) as role_count FROM merchant_sys_role;
SELECT COUNT(*) as menu_count FROM merchant_sys_menu;

-- 完成
SELECT '数据库修复完成！' as result; 