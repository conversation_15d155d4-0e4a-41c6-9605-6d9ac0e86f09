"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AbstractForkManager = void 0;
const os = require("os");
const util = require("util");
const util_1 = require("../util");
const events_1 = require("events");
const util_2 = require("util");
const debug = (0, util_2.debuglog)('midway:bootstrap');
class AbstractForkManager {
    constructor(options) {
        this.options = options;
        this.reforks = [];
        this.disconnectCount = 0;
        this.unexpectedCount = 0;
        this.disconnects = {};
        this.hub = new events_1.EventEmitter();
        this.workers = new Map();
        this.isClosing = false;
        options.count = options.count || os.cpus().length - 1;
        options.refork = options.refork !== false;
        options.limit = options.limit || 60;
        options.duration = options.duration || 60000; // 1 min
        options.logger = options.logger || console;
        options.workerInitTimeout = options.workerInitTimeout || 30000;
        this.eventBus = this.createEventBus({
            initTimeout: options.workerInitTimeout,
        });
    }
    async start() {
        debug('Start manager with options: %j', this.options);
        this.bindWorkerDisconnect(worker => {
            debug(' - worker(%s): trigger event = disconnect', this.getWorkerId(worker));
            const log = this.options.logger[worker['disableRefork'] ? 'info' : 'error'];
            this.disconnectCount++;
            const isDead = this.isWorkerDead(worker);
            if (isDead) {
                debug(' - worker(%s): worker is dead', this.getWorkerId(worker));
                // worker has terminated before disconnect
                this.options.logger.info("[%s] [bootstrap:master:%s] don't fork, because worker:%s exit event emit before disconnect", (0, util_1.logDate)(), process.pid, this.getWorkerId(worker));
                return;
            }
            if (worker['disableRefork']) {
                debug(' - worker(%s): worker is disableRefork(maybe terminated by master)', this.getWorkerId(worker));
                // worker has terminated by master
                log("[%s] [bootstrap:master:%s] don't fork, because worker:%s will be kill soon", (0, util_1.logDate)(), process.pid, this.getWorkerId(worker));
                return;
            }
            this.disconnects[this.getWorkerId(worker)] = (0, util_1.logDate)();
            this.tryToRefork(worker);
        });
        this.bindWorkerExit((worker, code, signal) => {
            debug(' - worker(%s): trigger event = exit', this.getWorkerId(worker));
            // remove worker
            this.workers.delete(this.getWorkerId(worker));
            if (worker['disableRefork']) {
                return;
            }
            const isExpected = !!this.disconnects[this.getWorkerId(worker)];
            debug(' - worker(%s): isExpected=%s', this.getWorkerId(worker), isExpected);
            if (isExpected) {
                delete this.disconnects[this.getWorkerId(worker)];
                // worker disconnect first, exit expected
                return;
            }
            debug(' - worker(%s): isWorkerDead=%s', this.getWorkerId(worker), this.isWorkerDead(worker));
            if (this.isWorkerDead(worker)) {
                return;
            }
            debug(' - worker(%s): unexpectedCount will add');
            this.unexpectedCount++;
            this.tryToRefork(worker);
            this.onUnexpected(worker, code, signal);
        });
        this.bindClose();
        this.hub.on('reachReforkLimit', this.onReachReforkLimit.bind(this));
        // defer to set the listeners
        // so you can listen this by your own
        setImmediate(() => {
            if (process.listeners('uncaughtException').length === 0) {
                process.on('uncaughtException', this.onerror.bind(this));
            }
        });
        for (let i = 0; i < this.options.count; i++) {
            const w = this.createWorker();
            debug(' - worker(%s) created', this.getWorkerId(w));
            this.eventBus.addWorker(w);
            this.workers.set(this.getWorkerId(w), w);
        }
        await this.eventBus.start();
    }
    tryToRefork(oldWorker) {
        if (this.allowRefork()) {
            debug(' - worker(%s): allow refork and will fork new', this.getWorkerId(oldWorker));
            const newWorker = this.createWorker(oldWorker);
            this.workers.set(this.getWorkerId(newWorker), newWorker);
            this.options.logger.info('[%s] [bootstrap:master:%s] new worker:%s fork (state: %s)', (0, util_1.logDate)(), process.pid, this.getWorkerId(newWorker), newWorker['state']);
            this.eventBus.addWorker(newWorker);
        }
        else {
            debug(' - worker(%s): forbidden refork and will stop', this.getWorkerId(oldWorker));
            this.options.logger.info("[%s] [bootstrap:master:%s] don't fork new work (refork: %s)", (0, util_1.logDate)(), process.pid, this.options.refork);
        }
    }
    /**
     * allow refork
     */
    allowRefork() {
        if (!this.options.refork || this.isClosing) {
            return false;
        }
        const times = this.reforks.push(Date.now());
        if (times > this.options.limit) {
            this.reforks.shift();
        }
        const span = this.reforks[this.reforks.length - 1] - this.reforks[0];
        const canFork = this.reforks.length < this.options.limit || span > this.options.duration;
        if (!canFork) {
            this.hub.emit('reachReforkLimit');
        }
        return canFork;
    }
    /**
     * uncaughtException default handler
     */
    onerror(err) {
        if (!err) {
            return;
        }
        this.options.logger.error('[%s] [bootstrap:master:%s] master uncaughtException: %s', (0, util_1.logDate)(), process.pid, err.stack);
        this.options.logger.error(err);
        this.options.logger.error('(total %d disconnect, %d unexpected exit)', this.disconnectCount, this.unexpectedCount);
    }
    /**
     * unexpectedExit default handler
     */
    onUnexpected(worker, code, signal) {
        // eslint-disable-next-line no-prototype-builtins
        const propertyName = worker.hasOwnProperty('exitedAfterDisconnect')
            ? 'exitedAfterDisconnect'
            : 'suicide';
        const err = new Error(util.format('worker:%s died unexpected (code: %s, signal: %s, %s: %s, state: %s)', this.getWorkerId(worker), code, signal, propertyName, worker[propertyName], worker['state']));
        err.name = 'WorkerDiedUnexpectedError';
        this.options.logger.error('[%s] [bootstrap:master:%s] (total %d disconnect, %d unexpected exit) %s', (0, util_1.logDate)(), process.pid, this.disconnectCount, this.unexpectedCount, err.stack);
    }
    /**
     * reachReforkLimit default handler
     */
    onReachReforkLimit() {
        this.options.logger.error('[%s] [bootstrap:master:%s] worker died too fast (total %d disconnect, %d unexpected exit)', (0, util_1.logDate)(), process.pid, this.disconnectCount, this.unexpectedCount);
    }
    async killWorker(worker, timeout) {
        // kill process, if SIGTERM not work, try SIGKILL
        await this.closeWorker(worker);
        await Promise.race([(0, events_1.once)(worker, 'exit'), (0, util_1.sleep)(timeout)]);
        if (worker.killed)
            return;
        // SIGKILL: http://man7.org/linux/man-pages/man7/signal.7.html
        // worker: https://github.com/nodejs/node/blob/master/lib/internal/cluster/worker.js#L22
        // subProcess.kill is wrapped to subProcess.destroy, it will wait to disconnected.
        (worker.process || worker).kill('SIGKILL');
    }
    async stop(timeout = 2000) {
        debug('run close');
        this.isClosing = true;
        await this.eventBus.stop();
        for (const worker of this.workers.values()) {
            worker['disableRefork'] = true;
            await this.killWorker(worker, timeout);
        }
        if (this.exitListener) {
            await this.exitListener();
        }
    }
    hasWorker(workerId) {
        return this.workers.has(workerId);
    }
    getWorker(workerId) {
        return this.workers.get(workerId);
    }
    getWorkerIds() {
        return Array.from(this.workers.keys());
    }
    onStop(exitListener) {
        this.exitListener = exitListener;
    }
    bindClose() {
        // kill(2) Ctrl-C
        process.once('SIGINT', this.onSignal.bind(this, 'SIGINT'));
        // kill(3) Ctrl-\
        process.once('SIGQUIT', this.onSignal.bind(this, 'SIGQUIT'));
        // kill(15) default
        process.once('SIGTERM', this.onSignal.bind(this, 'SIGTERM'));
        process.once('exit', this.onMasterExit.bind(this));
    }
    /**
     * on bootstrap receive a exit signal
     * @param signal
     */
    async onSignal(signal) {
        if (!this.isClosing) {
            this.options.logger.info('[bootstrap:master] receive signal %s, closing', signal);
            try {
                await this.stop();
                this.options.logger.info('[bootstrap:master] close done, exiting with code:0');
                process.exit(0);
            }
            catch (err) {
                this.options.logger.error('[midway:master] close with error: ', err);
                process.exit(1);
            }
        }
    }
    /**
     * on bootstrap process exit
     * @param code
     */
    onMasterExit(code) {
        this.options.logger.info('[bootstrap:master] exit with code:%s', code);
    }
}
exports.AbstractForkManager = AbstractForkManager;
//# sourceMappingURL=base.js.map