{"version": 3, "sources": ["../browser/src/error/NoVersionOrUpdateDateColumnError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,gCAAiC,SAAQ,YAAY;IAC9D,YAAY,MAAc;QACtB,KAAK,CAAC,UAAU,MAAM,gDAAgD,CAAC,CAAA;IAC3E,CAAC;CACJ", "file": "NoVersionOrUpdateDateColumnError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when an entity does not have no version and no update date column.\n */\nexport class NoVersionOrUpdateDateColumnError extends TypeORMError {\n    constructor(entity: string) {\n        super(`Entity ${entity} does not have version or update date columns.`)\n    }\n}\n"], "sourceRoot": ".."}