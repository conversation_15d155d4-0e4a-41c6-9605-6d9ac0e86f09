{"name": "eslint-plugin-node", "version": "11.1.0", "description": "Additional ESLint's rules for Node.js", "engines": {"node": ">=8.10.0"}, "main": "lib/index.js", "files": ["lib"], "peerDependencies": {"eslint": ">=5.16.0"}, "dependencies": {"eslint-plugin-es": "^3.0.0", "eslint-utils": "^2.0.0", "ignore": "^5.1.1", "minimatch": "^3.0.4", "resolve": "^1.10.1", "semver": "^6.1.0"}, "devDependencies": {"@mysticatea/eslint-plugin": "^10.0.3", "codecov": "^3.3.0", "eslint": "^6.3.0", "eslint-plugin-node": "file:.", "fast-glob": "^2.2.6", "globals": "^11.12.0", "mocha": "^6.1.4", "nyc": "^14.0.0", "opener": "^1.5.1", "punycode": "^2.1.1", "rimraf": "^2.6.3"}, "scripts": {"build": "node scripts/update", "clean": "rimraf .nyc_output coverage", "codecov": "nyc report --reporter text-lcov | codecov --pipe --disable=gcov -t $CODECOV_TOKEN", "coverage": "opener ./coverage/lcov-report/index.html", "lint": "eslint lib scripts tests/lib .eslintrc.js", "new": "node scripts/new-rule", "pretest": "npm run -s lint", "test": "nyc npm run -s test:_mocha", "test:ci": "nyc npm run -s test:_mocha", "test:_mocha": "_mocha \"tests/lib/**/*.js\" --reporter progress --timeout 4000", "preversion": "npm t && npm run -s build", "version": "eslint lib/rules --fix && git add lib/rules", "postversion": "git push && git push --tags", "watch": "npm run test:_mocha -- --watch --growl"}, "repository": {"type": "git", "url": "git+https://github.com/mysticatea/eslint-plugin-node.git"}, "keywords": ["eslint", "eslintplugin", "eslint-plugin", "node", "nodejs", "ecmascript", "shebang", "file", "path", "import", "require"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mysticatea/eslint-plugin-node/issues"}, "homepage": "https://github.com/mysticatea/eslint-plugin-node#readme"}