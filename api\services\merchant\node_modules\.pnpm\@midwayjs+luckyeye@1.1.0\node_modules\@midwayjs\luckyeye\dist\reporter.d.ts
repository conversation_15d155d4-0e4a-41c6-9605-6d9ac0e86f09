export declare class Reporter {
    reportStart(): void;
    reportEnd(): void;
    reportGroup(data: any): void;
    reportInfo(data: any): void;
    reportCheck(data: any): void;
    reportError(data: any): void;
    reportSkip(data: any): void;
    reportWarn(data: any): void;
}
export declare class ConsoleReporter extends Reporter {
    passes: number;
    failures: number;
    pending: number;
    warning: number;
    startTime: number;
    reportEnd(): void;
    reportGroup(data: any): void;
    reportInfo(data: any): void;
    reportCheck(data: any): void;
    reportWarn(data: any): void;
    output(test: any): void;
    reportError(data: any): void;
    reportSkip(data: any): void;
}
//# sourceMappingURL=reporter.d.ts.map