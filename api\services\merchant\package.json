{"name": "merchant-service", "version": "1.0.0", "description": "商户微服务 - 独立部署版本", "main": "main.ts", "license": "MIT", "scripts": {"start": "NODE_ENV=production node ./bootstrap.js", "dev": "cross-env NODE_ENV=local TS_NODE_TYPE_CHECK=false TS_NODE_TRANSPILE_ONLY=true midway-bin dev --ts", "build": "midway-bin build -c", "test": "midway-bin test --ts", "cov": "midway-bin cov --ts", "lint": "mwts check", "lint:fix": "mwts fix"}, "dependencies": {"@cool-midway/core": "^7.1.14", "@cool-midway/rpc": "7.0.0", "@midwayjs/bootstrap": "3.15.0", "@midwayjs/cache-manager": "^3.15.5", "@midwayjs/core": "3.20.4", "@midwayjs/cross-domain": "^3.15.2", "@midwayjs/koa": "3.15.0", "@midwayjs/typeorm": "3.15.0", "@midwayjs/validate": "^3.15.2", "cache-manager-ioredis-yet": "^2.0.2", "ioredis": "^5.3.2", "moleculer": "^0.14.35", "mysql2": "^3.14.1", "typeorm": "^0.3.20"}, "devDependencies": {"@midwayjs/cli": "^2.1.1", "@midwayjs/mock": "^3.15.2", "@types/jsonwebtoken": "^9.0.10", "@types/node": "20", "cross-env": "^7.0.3", "mwts": "^1.3.0", "mwtsc": "^1.7.2", "typescript": "~5.4.5"}, "engines": {"node": ">=12.0.0"}}