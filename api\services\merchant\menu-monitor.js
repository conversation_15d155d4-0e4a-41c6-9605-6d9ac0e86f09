const mysql = require('mysql2/promise');

async function monitorMenu(menuId) {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'wap.336101',
      database: 'merchant_service_db'
    });

    if (menuId === 'schema') {
      // 查看表结构
      const [schema] = await connection.execute(`DESCRIBE merchant_sys_menu`);
      console.log('\n📋 merchant_sys_menu 表结构:');
      console.table(schema);
      await connection.end();
      return;
    }

    if (menuId === 'fix') {
      // 执行修复脚本
      console.log('🔧 开始修复 component 字段...');

      try {
        // 添加 component 字段
        await connection.execute(`
          ALTER TABLE merchant_sys_menu
          ADD COLUMN component VARCHAR(200) NULL COMMENT '组件路径'
          AFTER router
        `);
        console.log('✅ component 字段添加成功');
      } catch (error) {
        if (error.message.includes('Duplicate column name')) {
          console.log('ℹ️  component 字段已存在，跳过添加');
        } else {
          throw error;
        }
      }

      // 更新监控相关菜单的 component 值
      await connection.execute(`UPDATE merchant_sys_menu SET component = 'MonitorDashboard' WHERE router = '/monitor/dashboard'`);
      await connection.execute(`UPDATE merchant_sys_menu SET component = 'MonitorRiskAlert' WHERE router = '/monitor/risk-alert'`);
      await connection.execute(`UPDATE merchant_sys_menu SET component = 'AnalyticsOverview' WHERE router = '/analytics/overview'`);
      await connection.execute(`UPDATE merchant_sys_menu SET component = 'SettlementRules' WHERE router = '/settlement/rules'`);
      await connection.execute(`UPDATE merchant_sys_menu SET component = 'SettlementBatch' WHERE router = '/settlement/batch-settlement'`);

      console.log('✅ 监控相关菜单的 component 值更新成功');

      // 验证修复结果
      const [result] = await connection.execute(`
        SELECT id, name, router, component, type, isShow
        FROM merchant_sys_menu
        WHERE router IN ('/monitor/dashboard', '/monitor/risk-alert', '/analytics/overview')
        ORDER BY id
      `);

      console.log('\n📋 修复后的菜单数据:');
      console.table(result);

      await connection.end();
      return;
    }

    console.log(`🔍 监控菜单 ID: ${menuId}`);
    
    // 查询指定菜单的详细信息
    const [menuDetail] = await connection.execute(`
      SELECT
        m1.id,
        m1.name,
        m1.parentId,
        m2.name as parentName,
        m1.router,
        m1.component,
        m1.type,
        m1.isShow,
        m1.keepAlive,
        m1.icon,
        m1.orderNum,
        m1.updateTime
      FROM merchant_sys_menu m1
      LEFT JOIN merchant_sys_menu m2 ON m1.parentId = m2.id
      WHERE m1.id = ?
    `, [menuId]);
    
    if (menuDetail.length > 0) {
      console.log('\n📋 菜单详情:');
      console.table(menuDetail);
      
      // 查询该菜单的子菜单
      const [children] = await connection.execute(`
        SELECT id, name, router, component, type, isShow
        FROM merchant_sys_menu
        WHERE parentId = ?
        ORDER BY orderNum, id
      `, [menuId]);
      
      if (children.length > 0) {
        console.log('\n👶 子菜单:');
        console.table(children);
      } else {
        console.log('\n👶 无子菜单');
      }
    } else {
      console.log(`❌ 未找到菜单 ID: ${menuId}`);
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

// 获取命令行参数
const menuId = process.argv[2];
if (!menuId) {
  console.log('用法: node menu-monitor.js <菜单ID>');
  console.log('例如: node menu-monitor.js 11  (查看控制台菜单)');
  process.exit(1);
}

monitorMenu(menuId);
