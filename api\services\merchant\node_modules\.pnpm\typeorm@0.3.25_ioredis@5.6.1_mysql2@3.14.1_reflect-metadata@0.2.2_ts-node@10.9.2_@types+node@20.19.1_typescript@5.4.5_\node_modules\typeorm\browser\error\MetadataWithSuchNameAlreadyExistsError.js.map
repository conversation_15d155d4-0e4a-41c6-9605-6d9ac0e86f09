{"version": 3, "sources": ["../browser/src/error/MetadataWithSuchNameAlreadyExistsError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,sCAAuC,SAAQ,YAAY;IACpE,YAAY,YAAoB,EAAE,IAAY;QAC1C,KAAK,CACD,YAAY;YACR,2BAA2B;YAC3B,IAAI;YACJ,mBAAmB;YACnB,8DAA8D,CACrE,CAAA;IACL,CAAC;CACJ", "file": "MetadataWithSuchNameAlreadyExistsError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\nexport class MetadataWithSuchNameAlreadyExistsError extends TypeORMError {\n    constructor(metadataType: string, name: string) {\n        super(\n            metadataType +\n                \" metadata with such name \" +\n                name +\n                \" already exists. \" +\n                \"Do you apply decorator twice? Or maybe try to change a name?\",\n        )\n    }\n}\n"], "sourceRoot": ".."}