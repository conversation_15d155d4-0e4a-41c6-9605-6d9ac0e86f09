{"version": 3, "sources": ["../../src/commands/InitCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,iDAAoC;AACpC,wDAAuB;AAEvB,oCAAuC;AACvC,6DAAyD;AACzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,WAAW;IAAxB;QACI,YAAO,GAAG,MAAM,CAAA;QAChB,aAAQ,GACJ,+CAA+C;YAC/C,wEAAwE;YACxE,mEAAmE,CAAA;IA2tB3E,CAAC;IAztBG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,MAAM,CAAC,GAAG,EAAE;YACT,KAAK,EAAE,MAAM;YACb,QAAQ,EAAE,gCAAgC;SAC7C,CAAC;aACD,MAAM,CAAC,IAAI,EAAE;YACV,KAAK,EAAE,UAAU;YACjB,QAAQ,EAAE,2CAA2C;SACxD,CAAC;aACD,MAAM,CAAC,SAAS,EAAE;YACf,QAAQ,EACJ,8FAA8F;SACrG,CAAC;aACD,MAAM,CAAC,QAAQ,EAAE;YACd,QAAQ,EACJ,4EAA4E;SACnF,CAAC;aACD,MAAM,CAAC,IAAI,EAAE;YACV,KAAK,EAAE,SAAS;YAChB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC;YACxB,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,oDAAoD;SACjE,CAAC;aACD,MAAM,CAAC,IAAI,EAAE;YACV,KAAK,EAAE,QAAQ;YACf,OAAO,EAAE,CAAC,UAAU,EAAE,KAAK,CAAC;YAC5B,OAAO,EAAE,UAAU;YACnB,QAAQ,EACJ,wEAAwE;SAC/E,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,CAAC;YACD,MAAM,QAAQ,GAAY,IAAI,CAAC,QAAgB,IAAI,UAAU,CAAA;YAC7D,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;YAC3D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;YACzD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;YACnE,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI;gBACzB,CAAC,CAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAW,CAAC;gBACjC,CAAC,CAAC,SAAS,CAAA;YACf,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;YACpD,MAAM,YAAY,GAAG,IAAI,CAAC,EAAE,KAAK,KAAK,CAAA;YACtC,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,eAAe,EAC1B,WAAW,CAAC,sBAAsB,CAAC,WAAW,EAAE,YAAY,CAAC,EAC7D,KAAK,CACR,CAAA;YACD,IAAI,QAAQ;gBACR,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,qBAAqB,EAChC,WAAW,CAAC,wBAAwB,CAAC,QAAQ,CAAC,EAC9C,KAAK,CACR,CAAA;YACL,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,aAAa,EACxB,WAAW,CAAC,gBAAgB,EAAE,CACjC,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,YAAY,EACvB,WAAW,CAAC,iBAAiB,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,EACnD,KAAK,CACR,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,gBAAgB,EAC3B,WAAW,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAChD,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,qBAAqB,EAChC,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAC9C,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,qBAAqB,EAChC,WAAW,CAAC,wBAAwB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAC/D,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,eAAe,EAC1B,WAAW,CAAC,mBAAmB,CAAC,SAAS,EAAE,YAAY,CAAC,CAC3D,CAAA;YACD,MAAM,2BAAY,CAAC,iBAAiB,CAAC,QAAQ,GAAG,gBAAgB,CAAC,CAAA;YAEjE,+CAA+C;YAC/C,IAAI,SAAS,EAAE,CAAC;gBACZ,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,gBAAgB,EAC3B,WAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAC9C,CAAA;gBACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,mCAAmC,EAC9C,WAAW,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAClD,CAAA;YACL,CAAC;YAED,MAAM,mBAAmB,GAAG,MAAM,2BAAY,CAAC,QAAQ,CACnD,QAAQ,GAAG,eAAe,CAC7B,CAAA;YACD,MAAM,2BAAY,CAAC,UAAU,CACzB,QAAQ,GAAG,eAAe,EAC1B,WAAW,CAAC,iBAAiB,CACzB,mBAAmB,EACnB,QAAQ,EACR,SAAS,EACT,YAAY,CACf,CACJ,CAAA;YAED,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,0BAA0B,eAAI,CAAC,IAAI,CACzC,QAAQ,CACX,aAAa,CACjB,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,2CAA2C,CACxD,CAAA;YACL,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,KAAK,CAAA,yCAAyC,CAAC,CAAA;YAChE,IAAI,IAAI,CAAC,EAAE,IAAI,UAAU,EAAE,CAAC;gBACxB,MAAM,WAAW,CAAC,cAAc,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;YAC7D,CAAC;iBAAM,CAAC;gBACJ,MAAM,WAAW,CAAC,cAAc,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAA;YAC9D,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,KAAK,CAAA,yCAAyC,CAAC,CAAA;QACpE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,sCAAsC,EAAE,GAAG,CAAC,CAAA;YACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,2BAA2B;IAC3B,4EAA4E;IAElE,MAAM,CAAC,cAAc,CAAC,OAAe,EAAE,GAAW;QACxD,OAAO,IAAI,OAAO,CAAS,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACpC,IAAA,oBAAI,EAAC,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,KAAU,EAAE,MAAW,EAAE,MAAW,EAAE,EAAE;gBAC5D,IAAI,MAAM;oBAAE,OAAO,EAAE,CAAC,MAAM,CAAC,CAAA;gBAC7B,IAAI,MAAM;oBAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAA;gBAC/B,IAAI,KAAK;oBAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC7B,EAAE,CAAC,EAAE,CAAC,CAAA;YACV,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,wBAAwB,CACrC,KAAc,EACd,QAAgB;QAEhB,IAAI,UAAU,GAAG,EAAE,CAAA;QACnB,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,OAAO;gBACR,UAAU,GAAG;;;;;sBAKP,CAAA;gBACN,MAAK;YACT,KAAK,SAAS;gBACV,UAAU,GAAG;;;;;sBAKP,CAAA;gBACN,MAAK;YACT,KAAK,QAAQ;gBACT,UAAU,GAAG;iCACI,CAAA;gBACjB,MAAK;YACT,KAAK,gBAAgB;gBACjB,UAAU,GAAG;iCACI,CAAA;gBACjB,MAAK;YACT,KAAK,UAAU;gBACX,UAAU,GAAG;;;;;sBAKP,CAAA;gBACN,MAAK;YACT,KAAK,aAAa;gBACd,UAAU,GAAG;;;;;2BAKF,CAAA;gBACX,MAAK;YACT,KAAK,OAAO;gBACR,UAAU,GAAG;;;;wBAIL,CAAA;gBACR,MAAK;YACT,KAAK,QAAQ;gBACT,UAAU,GAAG;;;;;yBAKJ,CAAA;gBACT,MAAK;YACT,KAAK,SAAS;gBACV,UAAU,GAAG;sBACP,CAAA;gBACN,MAAK;YACT,KAAK,SAAS;gBACV,UAAU,GAAG;;;wBAGL,CAAA;gBACR,MAAK;QACb,CAAC;QACD,OAAO;;qCAEsB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;MAGjD,UAAU;;;;;;;CAOf,CAAA;IACG,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,mBAAmB,CAAC,SAAkB;QACnD,IAAI,SAAS;YACT,OAAO,IAAI,CAAC,SAAS,CACjB;gBACI,eAAe,EAAE;oBACb,GAAG,EAAE,CAAC,QAAQ,CAAC;oBACf,MAAM,EAAE,QAAQ;oBAChB,MAAM,EAAE,QAAQ;oBAChB,gBAAgB,EAAE,MAAM;oBACxB,4BAA4B,EAAE,IAAI;oBAClC,MAAM,EAAE,SAAS;oBACjB,qBAAqB,EAAE,IAAI;oBAC3B,sBAAsB,EAAE,IAAI;oBAC5B,SAAS,EAAE,IAAI;iBAClB;aACJ,EACD,SAAS,EACT,CAAC,CACJ,CAAA;;YAED,OAAO,IAAI,CAAC,SAAS,CACjB;gBACI,eAAe,EAAE;oBACb,GAAG,EAAE,CAAC,QAAQ,CAAC;oBACf,MAAM,EAAE,QAAQ;oBAChB,MAAM,EAAE,UAAU;oBAClB,gBAAgB,EAAE,MAAM;oBACxB,MAAM,EAAE,SAAS;oBACjB,qBAAqB,EAAE,IAAI;oBAC3B,sBAAsB,EAAE,IAAI;oBAC5B,SAAS,EAAE,IAAI;iBAClB;aACJ,EACD,SAAS,EACT,CAAC,CACJ,CAAA;IACT,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,gBAAgB;QAC7B,OAAO;;;;;MAKT,CAAA;IACF,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,qBAAqB,CAAC,QAAgB;QACnD,OAAO,oBACH,QAAQ,KAAK,SAAS;YAClB,CAAC,CAAC,0BAA0B;YAC5B,CAAC,CAAC,wBACV;;;;;MAMA,QAAQ,KAAK,SAAS;YAClB,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,2BACV;UACM,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ;;;;;;;;;;;;CAYvD,CAAA;IACG,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,iBAAiB,CAAC,KAAc;QAC7C,OAAO,8DACH,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EACpB;;;;;;;;;;;;;;;;;;;;;;GAsBL,CAAA;IACC,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,qBAAqB,CAAC,KAAc;QACjD,OAAO,gDACH,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EACpB;;sCAE8B,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkDtD,CAAA;IACE,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,mBAAmB,CAChC,OAAgB,EAChB,KAAc;QAEd,IAAI,OAAO,EAAE,CAAC;YACV,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;SACzC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;;8CAEgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;kCAC9B,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;qCACf,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CtD,CAAA;QACO,CAAC;aAAM,CAAC;YACJ,OAAO,+CACH,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EACpB;qCACyB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;;;;;;CAmBtD,CAAA;QACO,CAAC;IACL,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,sBAAsB,CACnC,WAAoB,EACpB,YAAsB;QAEtB,OAAO,IAAI,CAAC,SAAS,CACjB;YACI,IAAI,EAAE,WAAW,IAAI,gBAAgB;YACrC,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,yCAAyC;YACtD,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU;YAC1C,eAAe,EAAE,EAAE;YACnB,YAAY,EAAE,EAAE;YAChB,OAAO,EAAE,EAAE;SACd,EACD,SAAS,EACT,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,wBAAwB,CAAC,QAAgB;QACtD,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,OAAO;gBACR,OAAO;;;;;;;;;;;;CAYtB,CAAA;YACW,KAAK,SAAS;gBACV,OAAO;;;;;;;;;;;;CAYtB,CAAA;YACW,KAAK,UAAU;gBACX,OAAO;;;;;;;;;;;CAWtB,CAAA;YACW,KAAK,aAAa;gBACd,OAAO;;;;;;;;CAQtB,CAAA;YACW,KAAK,QAAQ,CAAC;YACd,KAAK,gBAAgB;gBACjB,OAAO;CACtB,CAAA;YACW,KAAK,QAAQ;gBACT,MAAM,IAAI,oBAAY,CAClB,oEAAoE,CACvE,CAAA,CAAC,qCAAqC;YAE3C,KAAK,OAAO;gBACR,OAAO;;;;;;;;;;CAUtB,CAAA;YACW,KAAK,SAAS;gBACV,OAAO;;;;;;;;CAQtB,CAAA;YACW,KAAK,SAAS;gBACV,OAAO;;;;;;;;CAQtB,CAAA;QACO,CAAC;QACD,OAAO,EAAE,CAAA;IACb,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,iBAAiB,CAAC,OAA4B;QAC3D,IAAI,QAAQ,GAAG;;;;;CAKtB,CAAA;QAEO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACjB,QAAQ,IAAI;CACvB,CAAA;QACO,CAAC;aAAM,CAAC;YACJ,QAAQ,IAAI;CACvB,CAAA;QACO,CAAC;QAED,QAAQ,IAAI;CACnB,CAAA;QACO,OAAO,QAAQ,CAAA;IACnB,CAAC;IAED;;OAEG;IACO,MAAM,CAAC,iBAAiB,CAC9B,mBAA2B,EAC3B,QAAgB,EAChB,OAAgB,EAChB,YAAqB,CAAC,qBAAqB;QAE3C,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;QAEnD,IAAI,CAAC,WAAW,CAAC,eAAe;YAAE,WAAW,CAAC,eAAe,GAAG,EAAE,CAAA;QAClE,WAAW,CAAC,eAAe,GAAG;YAC1B,aAAa,EAAE,WAAW;YAC1B,SAAS,EAAE,SAAS;YACpB,UAAU,EAAE,QAAQ;YACpB,GAAG,WAAW,CAAC,eAAe;SACjC,CAAA;QAED,IAAI,CAAC,WAAW,CAAC,YAAY;YAAE,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;QAC5D,WAAW,CAAC,YAAY,GAAG;YACvB,GAAG,WAAW,CAAC,YAAY;YAC3B,kBAAkB,EAAE,QAAQ;YAC5B,OAAO,EACH,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,KAAK,OAAO;gBAC1C,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC,+CAA+C;gBACpF,CAAC,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,yBAAyB;SAC9E,CAAA;QAED,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACV,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAA;gBAC9C,MAAK;YACT,KAAK,UAAU,CAAC;YAChB,KAAK,aAAa;gBACd,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAA;gBAC1C,MAAK;YACT,KAAK,QAAQ;gBACT,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAA;gBAC9C,MAAK;YACT,KAAK,gBAAgB;gBACjB,WAAW,CAAC,YAAY,CAAC,gBAAgB,CAAC,GAAG,QAAQ,CAAA;gBACrD,MAAK;YACT,KAAK,QAAQ;gBACT,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,QAAQ,CAAA;gBAC/C,MAAK;YACT,KAAK,OAAO;gBACR,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,SAAS,CAAA;gBAC7C,MAAK;YACT,KAAK,SAAS;gBACV,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;gBAC/C,MAAK;YACT,KAAK,SAAS;gBACV,WAAW,CAAC,YAAY,CAAC,uBAAuB,CAAC,GAAG,UAAU,CAAA;gBAC9D,MAAK;QACb,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;YAC/C,WAAW,CAAC,YAAY,CAAC,aAAa,CAAC,GAAG,SAAS,CAAA;QACvD,CAAC;QAED,IAAI,CAAC,WAAW,CAAC,OAAO;YAAE,WAAW,CAAC,OAAO,GAAG,EAAE,CAAA;QAElD,IAAI,YAAY;YACZ,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE;gBAC/B,KAAK,EAAE,8CAA8C,CAAC,wCAAwC;gBAC9F,OAAO,EAAE,qBAAqB;aACjC,CAAC,CAAA;;YAEF,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE;gBAC/B,KAAK,EAAE,8CAA8C,CAAC,sBAAsB;gBAC5E,OAAO,EAAE,0BAA0B;aACtC,CAAC,CAAA;QAEN,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IACpD,CAAC;CACJ;AAhuBD,kCAguBC", "file": "InitCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport { exec } from \"child_process\"\nimport path from \"path\"\nimport yargs from \"yargs\"\nimport { TypeORMError } from \"../error\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Generates a new project with TypeORM.\n */\nexport class InitCommand implements yargs.CommandModule {\n    command = \"init\"\n    describe =\n        \"Generates initial TypeORM project structure. \" +\n        \"If name specified then creates files inside directory called as name. \" +\n        \"If its not specified then creates files inside current directory.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .option(\"n\", {\n                alias: \"name\",\n                describe: \"Name of the project directory.\",\n            })\n            .option(\"db\", {\n                alias: \"database\",\n                describe: \"Database type you'll use in your project.\",\n            })\n            .option(\"express\", {\n                describe:\n                    \"Indicates if express server sample code should be included in the project. False by default.\",\n            })\n            .option(\"docker\", {\n                describe:\n                    \"Set to true if docker-compose must be generated as well. False by default.\",\n            })\n            .option(\"pm\", {\n                alias: \"manager\",\n                choices: [\"npm\", \"yarn\"],\n                default: \"npm\",\n                describe: \"Install packages, expected values are npm or yarn.\",\n            })\n            .option(\"ms\", {\n                alias: \"module\",\n                choices: [\"commonjs\", \"esm\"],\n                default: \"commonjs\",\n                describe:\n                    \"Module system to use for project, expected values are commonjs or esm.\",\n            })\n    }\n\n    async handler(args: yargs.Arguments) {\n        try {\n            const database: string = (args.database as any) || \"postgres\"\n            const isExpress = args.express !== undefined ? true : false\n            const isDocker = args.docker !== undefined ? true : false\n            const basePath = process.cwd() + (args.name ? \"/\" + args.name : \"\")\n            const projectName = args.name\n                ? path.basename(args.name as any)\n                : undefined\n            const installNpm = args.pm === \"yarn\" ? false : true\n            const projectIsEsm = args.ms === \"esm\"\n            await CommandUtils.createFile(\n                basePath + \"/package.json\",\n                InitCommand.getPackageJsonTemplate(projectName, projectIsEsm),\n                false,\n            )\n            if (isDocker)\n                await CommandUtils.createFile(\n                    basePath + \"/docker-compose.yml\",\n                    InitCommand.getDockerComposeTemplate(database),\n                    false,\n                )\n            await CommandUtils.createFile(\n                basePath + \"/.gitignore\",\n                InitCommand.getGitIgnoreFile(),\n            )\n            await CommandUtils.createFile(\n                basePath + \"/README.md\",\n                InitCommand.getReadmeTemplate({ docker: isDocker }),\n                false,\n            )\n            await CommandUtils.createFile(\n                basePath + \"/tsconfig.json\",\n                InitCommand.getTsConfigTemplate(projectIsEsm),\n            )\n            await CommandUtils.createFile(\n                basePath + \"/src/entity/User.ts\",\n                InitCommand.getUserEntityTemplate(database),\n            )\n            await CommandUtils.createFile(\n                basePath + \"/src/data-source.ts\",\n                InitCommand.getAppDataSourceTemplate(projectIsEsm, database),\n            )\n            await CommandUtils.createFile(\n                basePath + \"/src/index.ts\",\n                InitCommand.getAppIndexTemplate(isExpress, projectIsEsm),\n            )\n            await CommandUtils.createDirectories(basePath + \"/src/migration\")\n\n            // generate extra files for express application\n            if (isExpress) {\n                await CommandUtils.createFile(\n                    basePath + \"/src/routes.ts\",\n                    InitCommand.getRoutesTemplate(projectIsEsm),\n                )\n                await CommandUtils.createFile(\n                    basePath + \"/src/controller/UserController.ts\",\n                    InitCommand.getControllerTemplate(projectIsEsm),\n                )\n            }\n\n            const packageJsonContents = await CommandUtils.readFile(\n                basePath + \"/package.json\",\n            )\n            await CommandUtils.createFile(\n                basePath + \"/package.json\",\n                InitCommand.appendPackageJson(\n                    packageJsonContents,\n                    database,\n                    isExpress,\n                    projectIsEsm,\n                ),\n            )\n\n            if (args.name) {\n                console.log(\n                    ansi.green`Project created inside ${ansi.blue(\n                        basePath,\n                    )} directory.`,\n                )\n            } else {\n                console.log(\n                    ansi.green`Project created inside current directory.`,\n                )\n            }\n\n            console.log(ansi.green`Please wait, installing dependencies...`)\n            if (args.pm && installNpm) {\n                await InitCommand.executeCommand(\"npm install\", basePath)\n            } else {\n                await InitCommand.executeCommand(\"yarn install\", basePath)\n            }\n\n            console.log(ansi.green`Done! Start playing with a new project!`)\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during project initialization:\", err)\n            process.exit(1)\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Static Methods\n    // -------------------------------------------------------------------------\n\n    protected static executeCommand(command: string, cwd: string) {\n        return new Promise<string>((ok, fail) => {\n            exec(command, { cwd }, (error: any, stdout: any, stderr: any) => {\n                if (stdout) return ok(stdout)\n                if (stderr) return fail(stderr)\n                if (error) return fail(error)\n                ok(\"\")\n            })\n        })\n    }\n\n    /**\n     * Gets contents of the ormconfig file.\n     */\n    protected static getAppDataSourceTemplate(\n        isEsm: boolean,\n        database: string,\n    ): string {\n        let dbSettings = \"\"\n        switch (database) {\n            case \"mysql\":\n                dbSettings = `type: \"mysql\",\n    host: \"localhost\",\n    port: 3306,\n    username: \"test\",\n    password: \"test\",\n    database: \"test\",`\n                break\n            case \"mariadb\":\n                dbSettings = `type: \"mariadb\",\n    host: \"localhost\",\n    port: 3306,\n    username: \"test\",\n    password: \"test\",\n    database: \"test\",`\n                break\n            case \"sqlite\":\n                dbSettings = `type: \"sqlite\",\n    database: \"database.sqlite\",`\n                break\n            case \"better-sqlite3\":\n                dbSettings = `type: \"better-sqlite3\",\n    database: \"database.sqlite\",`\n                break\n            case \"postgres\":\n                dbSettings = `type: \"postgres\",\n    host: \"localhost\",\n    port: 5432,\n    username: \"test\",\n    password: \"test\",\n    database: \"test\",`\n                break\n            case \"cockroachdb\":\n                dbSettings = `type: \"cockroachdb\",\n    host: \"localhost\",\n    port: 26257,\n    username: \"root\",\n    password: \"\",\n    database: \"defaultdb\",`\n                break\n            case \"mssql\":\n                dbSettings = `type: \"mssql\",\n    host: \"localhost\",\n    username: \"sa\",\n    password: \"Admin12345\",\n    database: \"tempdb\",`\n                break\n            case \"oracle\":\n                dbSettings = `type: \"oracle\",\nhost: \"localhost\",\nusername: \"system\",\npassword: \"oracle\",\nport: 1521,\nsid: \"xe.oracle.docker\",`\n                break\n            case \"mongodb\":\n                dbSettings = `type: \"mongodb\",\n    database: \"test\",`\n                break\n            case \"spanner\":\n                dbSettings = `type: \"spanner\",\n    projectId: \"test\",\n    instanceId: \"test\",\n    databaseId: \"test\",`\n                break\n        }\n        return `import \"reflect-metadata\"\nimport { DataSource } from \"typeorm\"\nimport { User } from \"./entity/User${isEsm ? \".js\" : \"\"}\"\n\nexport const AppDataSource = new DataSource({\n    ${dbSettings}\n    synchronize: true,\n    logging: false,\n    entities: [User],\n    migrations: [],\n    subscribers: [],\n})\n`\n    }\n\n    /**\n     * Gets contents of the ormconfig file.\n     */\n    protected static getTsConfigTemplate(esmModule: boolean): string {\n        if (esmModule)\n            return JSON.stringify(\n                {\n                    compilerOptions: {\n                        lib: [\"es2021\"],\n                        target: \"es2021\",\n                        module: \"es2022\",\n                        moduleResolution: \"node\",\n                        allowSyntheticDefaultImports: true,\n                        outDir: \"./build\",\n                        emitDecoratorMetadata: true,\n                        experimentalDecorators: true,\n                        sourceMap: true,\n                    },\n                },\n                undefined,\n                3,\n            )\n        else\n            return JSON.stringify(\n                {\n                    compilerOptions: {\n                        lib: [\"es2021\"],\n                        target: \"es2021\",\n                        module: \"commonjs\",\n                        moduleResolution: \"node\",\n                        outDir: \"./build\",\n                        emitDecoratorMetadata: true,\n                        experimentalDecorators: true,\n                        sourceMap: true,\n                    },\n                },\n                undefined,\n                3,\n            )\n    }\n\n    /**\n     * Gets contents of the .gitignore file.\n     */\n    protected static getGitIgnoreFile(): string {\n        return `.idea/\n.vscode/\nnode_modules/\nbuild/\ntmp/\ntemp/`\n    }\n\n    /**\n     * Gets contents of the user entity.\n     */\n    protected static getUserEntityTemplate(database: string): string {\n        return `import { Entity, ${\n            database === \"mongodb\"\n                ? \"ObjectIdColumn, ObjectId\"\n                : \"PrimaryGeneratedColumn\"\n        }, Column } from \"typeorm\"\n\n@Entity()\nexport class User {\n\n    ${\n        database === \"mongodb\"\n            ? \"@ObjectIdColumn()\"\n            : \"@PrimaryGeneratedColumn()\"\n    }\n    id: ${database === \"mongodb\" ? \"ObjectId\" : \"number\"}\n\n    @Column()\n    firstName: string\n\n    @Column()\n    lastName: string\n\n    @Column()\n    age: number\n\n}\n`\n    }\n\n    /**\n     * Gets contents of the route file (used when express is enabled).\n     */\n    protected static getRoutesTemplate(isEsm: boolean): string {\n        return `import { UserController } from \"./controller/UserController${\n            isEsm ? \".js\" : \"\"\n        }\"\n\nexport const Routes = [{\n    method: \"get\",\n    route: \"/users\",\n    controller: UserController,\n    action: \"all\"\n}, {\n    method: \"get\",\n    route: \"/users/:id\",\n    controller: UserController,\n    action: \"one\"\n}, {\n    method: \"post\",\n    route: \"/users\",\n    controller: UserController,\n    action: \"save\"\n}, {\n    method: \"delete\",\n    route: \"/users/:id\",\n    controller: UserController,\n    action: \"remove\"\n}]`\n    }\n\n    /**\n     * Gets contents of the user controller file (used when express is enabled).\n     */\n    protected static getControllerTemplate(isEsm: boolean): string {\n        return `import { AppDataSource } from \"../data-source${\n            isEsm ? \".js\" : \"\"\n        }\"\nimport { NextFunction, Request, Response } from \"express\"\nimport { User } from \"../entity/User${isEsm ? \".js\" : \"\"}\"\n\nexport class UserController {\n\n    private userRepository = AppDataSource.getRepository(User)\n\n    async all(request: Request, response: Response, next: NextFunction) {\n        return this.userRepository.find()\n    }\n\n    async one(request: Request, response: Response, next: NextFunction) {\n        const id = parseInt(request.params.id)\n\n\n        const user = await this.userRepository.findOne({\n            where: { id }\n        })\n\n        if (!user) {\n            return \"unregistered user\"\n        }\n        return user\n    }\n\n    async save(request: Request, response: Response, next: NextFunction) {\n        const { firstName, lastName, age } = request.body;\n\n        const user = Object.assign(new User(), {\n            firstName,\n            lastName,\n            age\n        })\n\n        return this.userRepository.save(user)\n    }\n\n    async remove(request: Request, response: Response, next: NextFunction) {\n        const id = parseInt(request.params.id)\n\n        let userToRemove = await this.userRepository.findOneBy({ id })\n\n        if (!userToRemove) {\n            return \"this user not exist\"\n        }\n\n        await this.userRepository.remove(userToRemove)\n\n        return \"user has been removed\"\n    }\n\n}`\n    }\n\n    /**\n     * Gets contents of the main (index) application file.\n     */\n    protected static getAppIndexTemplate(\n        express: boolean,\n        isEsm: boolean,\n    ): string {\n        if (express) {\n            return `import ${!isEsm ? \"* as \" : \"\"}express from \"express\"\nimport ${!isEsm ? \"* as \" : \"\"}bodyParser from \"body-parser\"\nimport { Request, Response } from \"express\"\nimport { AppDataSource } from \"./data-source${isEsm ? \".js\" : \"\"}\"\nimport { Routes } from \"./routes${isEsm ? \".js\" : \"\"}\"\nimport { User } from \"./entity/User${isEsm ? \".js\" : \"\"}\"\n\nAppDataSource.initialize().then(async () => {\n\n    // create express app\n    const app = express()\n    app.use(bodyParser.json())\n\n    // register express routes from defined application routes\n    Routes.forEach(route => {\n        (app as any)[route.method](route.route, (req: Request, res: Response, next: Function) => {\n            const result = (new (route.controller as any))[route.action](req, res, next)\n            if (result instanceof Promise) {\n                result.then(result => result !== null && result !== undefined ? res.send(result) : undefined)\n\n            } else if (result !== null && result !== undefined) {\n                res.json(result)\n            }\n        })\n    })\n\n    // setup express app here\n    // ...\n\n    // start express server\n    app.listen(3000)\n\n    // insert new users for test\n    await AppDataSource.manager.save(\n        AppDataSource.manager.create(User, {\n            firstName: \"Timber\",\n            lastName: \"Saw\",\n            age: 27\n        })\n    )\n\n    await AppDataSource.manager.save(\n        AppDataSource.manager.create(User, {\n            firstName: \"Phantom\",\n            lastName: \"Assassin\",\n            age: 24\n        })\n    )\n\n    console.log(\"Express server has started on port 3000. Open http://localhost:3000/users to see results\")\n\n}).catch(error => console.log(error))\n`\n        } else {\n            return `import { AppDataSource } from \"./data-source${\n                isEsm ? \".js\" : \"\"\n            }\"\nimport { User } from \"./entity/User${isEsm ? \".js\" : \"\"}\"\n\nAppDataSource.initialize().then(async () => {\n\n    console.log(\"Inserting a new user into the database...\")\n    const user = new User()\n    user.firstName = \"Timber\"\n    user.lastName = \"Saw\"\n    user.age = 25\n    await AppDataSource.manager.save(user)\n    console.log(\"Saved a new user with id: \" + user.id)\n\n    console.log(\"Loading users from the database...\")\n    const users = await AppDataSource.manager.find(User)\n    console.log(\"Loaded users: \", users)\n\n    console.log(\"Here you can setup and run express / fastify / any other framework.\")\n\n}).catch(error => console.log(error))\n`\n        }\n    }\n\n    /**\n     * Gets contents of the new package.json file.\n     */\n    protected static getPackageJsonTemplate(\n        projectName?: string,\n        projectIsEsm?: boolean,\n    ): string {\n        return JSON.stringify(\n            {\n                name: projectName || \"typeorm-sample\",\n                version: \"0.0.1\",\n                description: \"Awesome project developed with TypeORM.\",\n                type: projectIsEsm ? \"module\" : \"commonjs\",\n                devDependencies: {},\n                dependencies: {},\n                scripts: {},\n            },\n            undefined,\n            3,\n        )\n    }\n\n    /**\n     * Gets contents of the new docker-compose.yml file.\n     */\n    protected static getDockerComposeTemplate(database: string): string {\n        switch (database) {\n            case \"mysql\":\n                return `services:\n\n  mysql:\n    image: \"mysql:9.2.0\"\n    ports:\n      - \"3306:3306\"\n    environment:\n      MYSQL_ROOT_PASSWORD: \"admin\"\n      MYSQL_USER: \"test\"\n      MYSQL_PASSWORD: \"test\"\n      MYSQL_DATABASE: \"test\"\n\n`\n            case \"mariadb\":\n                return `services:\n\n  mariadb:\n    image: \"mariadb:11.7.2\"\n    ports:\n      - \"3306:3306\"\n    environment:\n      MYSQL_ROOT_PASSWORD: \"admin\"\n      MYSQL_USER: \"test\"\n      MYSQL_PASSWORD: \"test\"\n      MYSQL_DATABASE: \"test\"\n\n`\n            case \"postgres\":\n                return `services:\n\n  postgres:\n    image: \"postgres:17.2\"\n    ports:\n      - \"5432:5432\"\n    environment:\n      POSTGRES_USER: \"test\"\n      POSTGRES_PASSWORD: \"test\"\n      POSTGRES_DB: \"test\"\n\n`\n            case \"cockroachdb\":\n                return `services:\n\n  cockroachdb:\n    image: \"cockroachdb/cockroach:v25.1.2\"\n    command: start --insecure\n    ports:\n      - \"26257:26257\"\n\n`\n            case \"sqlite\":\n            case \"better-sqlite3\":\n                return `services:\n`\n            case \"oracle\":\n                throw new TypeORMError(\n                    `You cannot initialize a project with docker for Oracle driver yet.`,\n                ) // todo: implement for oracle as well\n\n            case \"mssql\":\n                return `services:\n\n  mssql:\n    image: \"mcr.microsoft.com/mssql/server:2022-latest\"\n    ports:\n      - \"1433:1433\"\n    environment:\n      SA_PASSWORD: \"Admin12345\"\n      ACCEPT_EULA: \"Y\"\n\n`\n            case \"mongodb\":\n                return `services:\n\n  mongodb:\n    image: \"mongo:8.0.5\"\n    container_name: \"typeorm-mongodb\"\n    ports:\n      - \"27017:27017\"\n\n`\n            case \"spanner\":\n                return `services:\n\n  spanner:\n    image: gcr.io/cloud-spanner-emulator/emulator:1.5.30\n    ports:\n      - \"9010:9010\"\n      - \"9020:9020\"\n\n`\n        }\n        return \"\"\n    }\n\n    /**\n     * Gets contents of the new readme.md file.\n     */\n    protected static getReadmeTemplate(options: { docker: boolean }): string {\n        let template = `# Awesome Project Build with TypeORM\n\nSteps to run this project:\n\n1. Run \\`npm i\\` command\n`\n\n        if (options.docker) {\n            template += `2. Run \\`docker-compose up\\` command\n`\n        } else {\n            template += `2. Setup database settings inside \\`data-source.ts\\` file\n`\n        }\n\n        template += `3. Run \\`npm start\\` command\n`\n        return template\n    }\n\n    /**\n     * Appends to a given package.json template everything needed.\n     */\n    protected static appendPackageJson(\n        packageJsonContents: string,\n        database: string,\n        express: boolean,\n        projectIsEsm: boolean /*, docker: boolean*/,\n    ): string {\n        const packageJson = JSON.parse(packageJsonContents)\n\n        if (!packageJson.devDependencies) packageJson.devDependencies = {}\n        packageJson.devDependencies = {\n            \"@types/node\": \"^22.13.10\",\n            \"ts-node\": \"^10.9.2\",\n            typescript: \"^5.8.2\",\n            ...packageJson.devDependencies,\n        }\n\n        if (!packageJson.dependencies) packageJson.dependencies = {}\n        packageJson.dependencies = {\n            ...packageJson.dependencies,\n            \"reflect-metadata\": \"^0.2.2\",\n            typeorm:\n                require(\"../package.json\").version !== \"0.0.0\"\n                    ? require(\"../package.json\").version // install version from package.json if present\n                    : require(\"../package.json\").installFrom, // else use custom source\n        }\n\n        switch (database) {\n            case \"mysql\":\n            case \"mariadb\":\n                packageJson.dependencies[\"mysql2\"] = \"^3.14.0\"\n                break\n            case \"postgres\":\n            case \"cockroachdb\":\n                packageJson.dependencies[\"pg\"] = \"^8.14.1\"\n                break\n            case \"sqlite\":\n                packageJson.dependencies[\"sqlite3\"] = \"^5.1.7\"\n                break\n            case \"better-sqlite3\":\n                packageJson.dependencies[\"better-sqlite3\"] = \"^8.7.0\"\n                break\n            case \"oracle\":\n                packageJson.dependencies[\"oracledb\"] = \"^6.8.0\"\n                break\n            case \"mssql\":\n                packageJson.dependencies[\"mssql\"] = \"^10.0.4\"\n                break\n            case \"mongodb\":\n                packageJson.dependencies[\"mongodb\"] = \"^6.15.0\"\n                break\n            case \"spanner\":\n                packageJson.dependencies[\"@google-cloud/spanner\"] = \"^7.19.1 \"\n                break\n        }\n\n        if (express) {\n            packageJson.dependencies[\"express\"] = \"^4.21.2\"\n            packageJson.dependencies[\"body-parser\"] = \"^1.20.3\"\n        }\n\n        if (!packageJson.scripts) packageJson.scripts = {}\n\n        if (projectIsEsm)\n            Object.assign(packageJson.scripts, {\n                start: /*(docker ? \"docker-compose up && \" : \"\") + */ \"node --loader ts-node/esm src/index.ts\",\n                typeorm: \"typeorm-ts-node-esm\",\n            })\n        else\n            Object.assign(packageJson.scripts, {\n                start: /*(docker ? \"docker-compose up && \" : \"\") + */ \"ts-node src/index.ts\",\n                typeorm: \"typeorm-ts-node-commonjs\",\n            })\n\n        return JSON.stringify(packageJson, undefined, 3)\n    }\n}\n"], "sourceRoot": ".."}