{"name": "is-object", "version": "1.0.2", "description": "Checks whether a value is an object", "keywords": [], "author": "Raynos <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "repository": "git://github.com/inspect-js/is-object.git", "main": "index", "homepage": "https://github.com/inspect-js/is-object", "contributors": [{"name": "<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ljharb"}], "bugs": {"url": "https://github.com/inspect-js/is-object/issues", "email": "<EMAIL>"}, "dependencies": {}, "devDependencies": {"@ljharb/eslint-config": "^17.3.0", "aud": "^1.1.3", "auto-changelog": "^2.2.1", "eslint": "^7.14.0", "nyc": "^10.3.2", "safe-publish-latest": "^1.1.4", "tape": "^5.0.1"}, "license": "MIT", "licenses": [{"type": "MIT", "url": "http://github.com/inspect-js/is-object/raw/master/LICENSE"}], "scripts": {"prepublish": "safe-publish-latest", "pretest": "npm run lint", "lint": "eslint --ext=.js,.mjs .", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "aud --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "testling": {"files": "test/index.js", "browsers": ["ie/6..latest", "firefox/3..6", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/10.0", "opera/11..latest", "opera/next", "safari/4..latest", "ipad/6.0..latest", "iphone/6.0..latest"]}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}}