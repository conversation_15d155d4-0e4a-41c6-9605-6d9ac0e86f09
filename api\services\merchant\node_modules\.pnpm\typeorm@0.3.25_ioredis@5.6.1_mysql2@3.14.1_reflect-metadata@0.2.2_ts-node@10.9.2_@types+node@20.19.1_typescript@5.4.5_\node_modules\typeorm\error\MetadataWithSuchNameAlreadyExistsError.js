"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MetadataWithSuchNameAlreadyExistsError = void 0;
const TypeORMError_1 = require("./TypeORMError");
class MetadataWithSuchNameAlreadyExistsError extends TypeORMError_1.TypeORMError {
    constructor(metadataType, name) {
        super(metadataType +
            " metadata with such name " +
            name +
            " already exists. " +
            "Do you apply decorator twice? Or maybe try to change a name?");
    }
}
exports.MetadataWithSuchNameAlreadyExistsError = MetadataWithSuchNameAlreadyExistsError;

//# sourceMappingURL=MetadataWithSuchNameAlreadyExistsError.js.map
