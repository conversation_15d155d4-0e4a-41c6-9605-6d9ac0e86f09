"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomModuleDetector = exports.ESModuleFileDetector = exports.CommonJSFileDetector = exports.AbstractFileDetector = void 0;
const types_1 = require("../util/types");
const glob_1 = require("@midwayjs/glob");
const error_1 = require("../error");
const constants_1 = require("../constants");
const decorator_1 = require("../decorator");
const util_1 = require("../util");
class AbstractFileDetector {
    constructor(options) {
        this.options = options;
        this.extraDetectorOptions = {};
    }
    setExtraDetectorOptions(detectorOptions) {
        this.extraDetectorOptions = detectorOptions;
    }
}
exports.AbstractFileDetector = AbstractFileDetector;
const DEFAULT_GLOB_PATTERN = ['**/**.tsx'].concat(constants_1.DEFAULT_PATTERN);
const DEFAULT_IGNORE_PATTERN = [
    '**/logs/**',
    '**/run/**',
    '**/public/**',
    '**/app/view/**',
    '**/app/views/**',
    '**/app/extend/**',
    '**/node_modules/**',
    '**/**.test.ts',
    '**/**.test.js',
    '**/__test__/**',
].concat(constants_1.IGNORE_PATTERN);
/**
 * CommonJS module loader
 */
class CommonJSFileDetector extends AbstractFileDetector {
    constructor() {
        super(...arguments);
        this.duplicateModuleCheckSet = new Map();
    }
    run(container) {
        if (this.getType() === 'commonjs') {
            return this.loadSync(container);
        }
        else {
            return this.loadAsync(container);
        }
    }
    loadSync(container) {
        var _a;
        this.options = this.options || {};
        const loadDirs = [].concat((_a = this.options.loadDir) !== null && _a !== void 0 ? _a : container.get('baseDir'));
        for (const dir of loadDirs) {
            const fileResults = (0, glob_1.run)(DEFAULT_GLOB_PATTERN.concat(this.options.pattern || []).concat(this.extraDetectorOptions.pattern || []), {
                cwd: dir,
                ignore: DEFAULT_IGNORE_PATTERN.concat(this.options.ignore || []).concat(this.extraDetectorOptions.ignore || []),
            });
            // 检查重复模块
            const checkDuplicatedHandler = (module, options) => {
                if ((this.options.conflictCheck ||
                    this.extraDetectorOptions.conflictCheck) &&
                    types_1.Types.isClass(module)) {
                    const name = (0, decorator_1.getProviderName)(module);
                    if (name) {
                        if (this.duplicateModuleCheckSet.has(name)) {
                            throw new error_1.MidwayDuplicateClassNameError(name, options.srcPath, this.duplicateModuleCheckSet.get(name));
                        }
                        else {
                            this.duplicateModuleCheckSet.set(name, options.srcPath);
                        }
                    }
                }
            };
            for (const file of fileResults) {
                const exports = require(file);
                // add module to set
                container.bindClass(exports, {
                    namespace: this.options.namespace,
                    srcPath: file,
                    createFrom: 'file',
                    bindHook: checkDuplicatedHandler,
                });
            }
        }
        // check end
        this.duplicateModuleCheckSet.clear();
    }
    async loadAsync(container) {
        var _a;
        this.options = this.options || {};
        const loadDirs = [].concat((_a = this.options.loadDir) !== null && _a !== void 0 ? _a : container.get('baseDir'));
        for (const dir of loadDirs) {
            const fileResults = (0, glob_1.run)(DEFAULT_GLOB_PATTERN.concat(this.options.pattern || []).concat(this.extraDetectorOptions.pattern || []), {
                cwd: dir,
                ignore: DEFAULT_IGNORE_PATTERN.concat(this.options.ignore || []).concat(this.extraDetectorOptions.ignore || []),
            });
            // 检查重复模块
            const checkDuplicatedHandler = (module, options) => {
                if ((this.options.conflictCheck ||
                    this.extraDetectorOptions.conflictCheck) &&
                    types_1.Types.isClass(module)) {
                    const name = (0, decorator_1.getProviderName)(module);
                    if (name) {
                        if (this.duplicateModuleCheckSet.has(name)) {
                            throw new error_1.MidwayDuplicateClassNameError(name, options.srcPath, this.duplicateModuleCheckSet.get(name));
                        }
                        else {
                            this.duplicateModuleCheckSet.set(name, options.srcPath);
                        }
                    }
                }
            };
            for (const file of fileResults) {
                const exports = await (0, util_1.loadModule)(file, {
                    loadMode: 'esm',
                });
                // add module to set
                container.bindClass(exports, {
                    namespace: this.options.namespace,
                    srcPath: file,
                    createFrom: 'file',
                    bindHook: checkDuplicatedHandler,
                });
            }
        }
        // check end
        this.duplicateModuleCheckSet.clear();
    }
    getType() {
        return 'commonjs';
    }
}
exports.CommonJSFileDetector = CommonJSFileDetector;
/**
 * ES module loader
 */
class ESModuleFileDetector extends CommonJSFileDetector {
    getType() {
        return 'module';
    }
}
exports.ESModuleFileDetector = ESModuleFileDetector;
class CustomModuleDetector extends AbstractFileDetector {
    async run(container) {
        for (const module of this.options.modules) {
            container.bindClass(module, {
                namespace: this.options.namespace,
                createFrom: 'module',
            });
        }
    }
}
exports.CustomModuleDetector = CustomModuleDetector;
//# sourceMappingURL=fileDetector.js.map