"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LockNotSupportedOnGivenDriverError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when selected sql driver does not supports locking.
 */
class LockNotSupportedOnGivenDriverError extends TypeORMError_1.TypeORMError {
    constructor() {
        super(`Locking not supported on given driver.`);
    }
}
exports.LockNotSupportedOnGivenDriverError = LockNotSupportedOnGivenDriverError;

//# sourceMappingURL=LockNotSupportedOnGivenDriverError.js.map
