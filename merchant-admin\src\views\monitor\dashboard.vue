<template>
  <div class="monitor-dashboard">
    <h1>监控总览</h1>
    <p>✅ 页面加载成功！</p>

    <div class="stats">
      <div class="stat-item">
        <h3>{{ riskMerchants }}</h3>
        <p>风险商户</p>
      </div>
      <div class="stat-item">
        <h3>{{ activeMerchants }}</h3>
        <p>活跃商户</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

defineOptions({
  name: 'MonitorDashboard'
})

const riskMerchants = ref(12)
const activeMerchants = ref(1580)

onMounted(() => {
  console.log('✅ 监控总览页面加载成功!')
})
</script>

<style scoped>
.monitor-dashboard {
  padding-bottom: 20px;
}

.stats {
  display: flex;
  gap: 20px;
  margin-top: 20px;
}

.stat-item {
  background: var(--art-main-bg-color);
  border: 1px solid var(--art-border-color);
  padding: 20px;
  border-radius: calc(var(--custom-radius) + 4px);
  box-shadow: none;
  text-align: center;
  min-width: 150px;
}

.stat-item h3 {
  font-size: 32px;
  margin: 0 0 8px 0;
  color: #409eff;
}

.stat-item p {
  margin: 0;
  color: #666;
}
</style>
