import { Configuration } from '@midwayjs/core';
import * as koa from '@midwayjs/koa';
import * as typeorm from '@midwayjs/typeorm';
import * as cool from '@cool-midway/core';
import * as rpc from '@cool-midway/rpc';

@Configuration({
  imports: [
    koa,
    typeorm,
    cool,
    rpc,
  ],
  importConfigs: [
    {
      default: {
        keys: '8ddc47ff6b5f4fa2b5a33f9c0d91c7b5',
        koa: {
          port: 9803,
        },
        rpc: {
          name: 'finance-service',
        },
      },
    },
  ],
})
export class FinanceServiceConfiguration {} 