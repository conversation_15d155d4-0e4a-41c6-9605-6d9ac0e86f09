"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postInstallModule = exports.postInstall = void 0;
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
const npm_1 = require("../npm");
const postInstall = async () => {
    // init cwd
    if (!process.env.INIT_CWD) {
        return;
    }
    const cwd = process.env.INIT_CWD;
    const pkgJsonFile = (0, path_1.join)(cwd, 'package.json');
    if (!(0, fs_extra_1.existsSync)(pkgJsonFile)) {
        return;
    }
    const pkg = JSON.parse((0, fs_extra_1.readFileSync)(pkgJsonFile, 'utf-8'));
    const deps = Object.assign({}, pkg.dependencies, pkg.devDependencies);
    return {
        cwd,
        pkg,
        deps,
        pkgJsonFile,
    };
};
exports.postInstall = postInstall;
const postInstallModule = async (moduleList) => {
    var _a, _b;
    const info = await (0, exports.postInstall)();
    if (!info) {
        return;
    }
    const { cwd, pkg } = info;
    const { registry, npm } = (0, npm_1.findNpm)();
    const modules = [];
    for (const { name, version } of moduleList) {
        if (((_a = pkg === null || pkg === void 0 ? void 0 : pkg.dependencies) === null || _a === void 0 ? void 0 : _a[name]) || ((_b = pkg === null || pkg === void 0 ? void 0 : pkg.devDependencies) === null || _b === void 0 ? void 0 : _b[name])) {
            continue;
        }
        console.log('[midway] auto install', name);
        modules.push(name + '@' + version);
    }
    if (!modules.length) {
        return;
    }
    const installingLock = (0, path_1.join)(cwd, `node_modules/.midwayjs-cli/postInstallLock/${modules
        .join('_')
        .replace(/\//g, '_')}.lock`);
    if ((0, fs_extra_1.existsSync)(installingLock)) {
        return;
    }
    await (0, fs_extra_1.ensureFile)(installingLock);
    await (0, fs_extra_1.writeFile)(installingLock, JSON.stringify({ cwd, npm, registry }));
    await (0, npm_1.installNpm)({
        baseDir: cwd,
        mode: ['save-dev'],
        register: ['yarn'].includes(npm) ? 'npm' : npm,
        registerPath: registry,
        moduleName: modules.join(' '),
        slience: true,
    });
    console.log('[midway] auto install complete');
    return;
};
exports.postInstallModule = postInstallModule;
//# sourceMappingURL=postInstall.js.map