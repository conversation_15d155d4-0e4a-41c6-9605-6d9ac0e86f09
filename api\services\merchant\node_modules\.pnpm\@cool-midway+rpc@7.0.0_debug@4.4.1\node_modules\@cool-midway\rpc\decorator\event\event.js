"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpcEvent = exports.COOL_RPC_EVENT_KEY = void 0;
const decorator_1 = require("@midwayjs/decorator");
exports.COOL_RPC_EVENT_KEY = 'decorator:cool:rpc:event';
function CoolRpcEvent() {
    return (target) => {
        // 将装饰的类，绑定到该装饰器，用于后续能获取到 class
        (0, decorator_1.saveModule)(exports.COOL_RPC_EVENT_KEY, target);
        // 保存一些元数据信息，任意你希望存的东西
        (0, decorator_1.saveClassMetadata)(exports.COOL_RPC_EVENT_KEY, {}, target);
        // 指定 IoC 容器创建实例的作用域
        (0, decorator_1.Scope)(decorator_1.ScopeEnum.Singleton)(target);
    };
}
exports.CoolRpcEvent = CoolRpcEvent;
