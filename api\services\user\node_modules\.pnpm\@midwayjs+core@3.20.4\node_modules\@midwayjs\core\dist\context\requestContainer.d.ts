import { MidwayContainer } from './container';
import { IMidwayContainer } from '../interface';
export declare class MidwayRequestContainer extends MidwayContainer {
    private readonly applicationContext;
    constructor(ctx: any, applicationContext: IMidwayContainer);
    init(): void;
    get<T = any>(identifier: any, args?: any): T;
    getAsync<T = any>(identifier: any, args?: any): Promise<T>;
    ready(): Promise<void>;
    getContext(): {
        _MAIN_CTX_: boolean;
    };
}
//# sourceMappingURL=requestContainer.d.ts.map