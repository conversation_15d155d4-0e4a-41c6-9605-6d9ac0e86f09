export interface CommonSchedule {
    exec(ctx?: any): any;
}
export interface ScheduleOpts {
    type: string;
    cron?: string;
    interval?: number | string;
    immediate?: boolean;
    disable?: boolean;
    env?: string[];
    cronOptions?: {
        currentDate?: string | number | Date;
        startDate?: string | number | Date;
        endDate?: string | number | Date;
        iterator?: boolean;
        utc?: boolean;
        tz?: string;
    };
}
export declare function Schedule(scheduleOpts: ScheduleOpts | string): (target: any) => void;
//# sourceMappingURL=schedule.d.ts.map