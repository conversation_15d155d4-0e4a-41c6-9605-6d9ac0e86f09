{"version": 3, "sources": ["../browser/src/find-options/FindOptionsWhere.ts"], "names": [], "mappings": "", "file": "FindOptionsWhere.js", "sourcesContent": ["import { FindOperator } from \"./FindOperator\"\nimport { ObjectId } from \"../driver/mongodb/typings\"\nimport { EqualOperator } from \"./EqualOperator\"\n\n/**\n * A single property handler for FindOptionsWhere.\n *\n * The reason why we have both \"PropertyToBeNarrowed\" and \"Property\" is that Union is narrowed down when extends is used.\n * It means the result of FindOptionsWhereProperty<1 | 2> doesn't include FindOperator<1 | 2> but FindOperator<1> | FindOperator<2>.\n * So we keep the original Union as Original and pass it to the FindOperator too. Original remains Union as extends is not used for it.\n */\nexport type FindOptionsWhereProperty<\n    PropertyToBeNarrowed,\n    Property = PropertyToBeNarrowed,\n> = PropertyToBeNarrowed extends Promise<infer I>\n    ? FindOptionsWhereProperty<NonNullable<I>>\n    : PropertyToBeNarrowed extends Array<infer I>\n    ? FindOptionsWhereProperty<NonNullable<I>>\n    : PropertyToBeNarrowed extends Function\n    ? never\n    : PropertyToBeNarrowed extends Buffer\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends Date\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends ObjectId\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends string\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends number\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends boolean\n    ? Property | FindOperator<Property>\n    : PropertyToBeNarrowed extends object\n    ?\n          | FindOptionsWhere<Property>\n          | FindOptionsWhere<Property>[]\n          | EqualOperator<Property>\n          | FindOperator<any>\n          | boolean\n          | Property\n    : Property | FindOperator<Property>\n\n/**\n * Used for find operations.\n */\nexport type FindOptionsWhere<Entity> = {\n    [P in keyof Entity]?: P extends \"toString\"\n        ? unknown\n        : FindOptionsWhereProperty<NonNullable<Entity[P]>>\n}\n"], "sourceRoot": ".."}