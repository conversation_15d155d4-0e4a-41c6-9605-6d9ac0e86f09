"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OnConnection = exports.OnDisConnection = exports.Emit = exports.OnMessage = exports.WSBroadCast = exports.WSEmit = exports.OnWSMessage = exports.OnWSDisConnection = exports.OnWSConnection = exports.WSEventTypeEnum = void 0;
const __1 = require("../");
var WSEventTypeEnum;
(function (WSEventTypeEnum) {
    WSEventTypeEnum["ON_CONNECTION"] = "ws:onConnection";
    WSEventTypeEnum["ON_DISCONNECTION"] = "ws:onDisconnection";
    WSEventTypeEnum["ON_MESSAGE"] = "ws:onMessage";
    WSEventTypeEnum["ON_SOCKET_ERROR"] = "ws:onSocketError";
    WSEventTypeEnum["EMIT"] = "ws:Emit";
    WSEventTypeEnum["BROADCAST"] = "ws:broadcast";
})(WSEventTypeEnum = exports.WSEventTypeEnum || (exports.WSEventTypeEnum = {}));
function OnWSConnection(eventOptions = {}) {
    return (target, propertyKey, descriptor) => {
        (0, __1.attachClassMetadata)(__1.WS_EVENT_KEY, {
            eventType: WSEventTypeEnum.ON_CONNECTION,
            propertyName: propertyKey,
            eventOptions,
            descriptor,
        }, target.constructor);
    };
}
exports.OnWSConnection = OnWSConnection;
function OnWSDisConnection() {
    return (target, propertyKey, descriptor) => {
        (0, __1.attachClassMetadata)(__1.WS_EVENT_KEY, {
            eventType: WSEventTypeEnum.ON_DISCONNECTION,
            propertyName: propertyKey,
            descriptor,
        }, target.constructor);
    };
}
exports.OnWSDisConnection = OnWSDisConnection;
function OnWSMessage(eventName, eventOptions = {}) {
    return (target, propertyKey, descriptor) => {
        (0, __1.attachClassMetadata)(__1.WS_EVENT_KEY, {
            eventType: WSEventTypeEnum.ON_MESSAGE,
            messageEventName: eventName,
            propertyName: propertyKey,
            eventOptions,
            descriptor,
        }, target.constructor);
    };
}
exports.OnWSMessage = OnWSMessage;
function WSEmit(messageName, roomName = []) {
    return (target, propertyKey, descriptor) => {
        (0, __1.attachClassMetadata)(__1.WS_EVENT_KEY, {
            eventType: WSEventTypeEnum.EMIT,
            propertyName: propertyKey,
            messageEventName: messageName,
            roomName: [].concat(roomName),
            descriptor,
        }, target.constructor);
    };
}
exports.WSEmit = WSEmit;
function WSBroadCast(messageName = '', roomName = []) {
    return (target, propertyKey, descriptor) => {
        (0, __1.attachClassMetadata)(__1.WS_EVENT_KEY, {
            eventType: WSEventTypeEnum.BROADCAST,
            propertyName: propertyKey,
            messageEventName: messageName,
            roomName: [].concat(roomName),
            descriptor,
        }, target.constructor);
    };
}
exports.WSBroadCast = WSBroadCast;
/**
 * @deprecated please use @OnWSMessage
 */
exports.OnMessage = OnWSMessage;
/**
 * @deprecated please use @WSEmit
 */
exports.Emit = WSEmit;
/**
 * @deprecated please use @OnWSDisConnection
 */
exports.OnDisConnection = OnWSDisConnection;
/**
 * @deprecated please use @OnWSConnection
 */
exports.OnConnection = OnWSConnection;
//# sourceMappingURL=webSocketEvent.js.map