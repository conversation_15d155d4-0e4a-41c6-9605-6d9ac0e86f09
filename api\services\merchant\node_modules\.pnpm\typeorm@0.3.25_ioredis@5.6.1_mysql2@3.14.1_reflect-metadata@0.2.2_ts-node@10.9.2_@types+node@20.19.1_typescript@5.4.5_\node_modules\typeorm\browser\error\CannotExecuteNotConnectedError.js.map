{"version": 3, "sources": ["../browser/src/error/CannotExecuteNotConnectedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,8BAA+B,SAAQ,YAAY;IAC5D,YAAY,cAAsB;QAC9B,KAAK,CACD,gCAAgC,cAAc,yDAAyD,CAC1G,CAAA;IACL,CAAC;CACJ", "file": "CannotExecuteNotConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to execute operation allowed only if connection is opened.\n */\nexport class CannotExecuteNotConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot execute operation on \"${connectionName}\" connection because connection is not yet established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}