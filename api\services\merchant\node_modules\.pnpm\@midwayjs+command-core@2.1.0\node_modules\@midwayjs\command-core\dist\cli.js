"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoreBaseCLI = void 0;
const minimist = require("minimist");
const path_1 = require("path");
const commandLineUsage_1 = require("./utils/commandLineUsage");
const core_1 = require("./core");
const fs_1 = require("fs");
class CoreBaseCLI {
    constructor(argv) {
        this.cwd = process.cwd();
        if (Array.isArray(argv)) {
            this.argv = minimist(argv.slice(2));
        }
        else {
            this.argv = argv;
        }
        this.commands = [].concat(this.argv._);
        this.initCore();
    }
    initCore() {
        this.core = new core_1.CommandCore({
            commands: this.commands,
            options: this.argv,
            log: this.loadLog(),
            displayUsage: this.displayUsage.bind(this),
            extensions: this.loadExtensions(),
            ...this.coverCoreOptions(),
        });
    }
    async loadPlugins() {
        await this.loadCorePlugin();
        await this.loadDefaultPlugin();
        await this.loadPlatformPlugin();
    }
    loadCorePlugin() { }
    // 加载默认插件
    loadDefaultPlugin() {
        var _a;
        const { cwd } = this;
        const packageJsonFile = (0, path_1.join)(cwd, 'package.json');
        if (!(0, fs_1.existsSync)(packageJsonFile)) {
            this.core.debug('no user package.json', packageJsonFile);
            return;
        }
        const packageJson = JSON.parse((0, fs_1.readFileSync)(packageJsonFile).toString());
        const deps = ((_a = packageJson === null || packageJson === void 0 ? void 0 : packageJson['midway-cli']) === null || _a === void 0 ? void 0 : _a.plugins) || [];
        this.core.debug('mw plugin', deps);
        deps.forEach(dep => {
            let npmPath;
            try {
                npmPath = require.resolve(dep, { paths: [cwd] });
            }
            catch (error) {
                throw new Error(`Auto load mw plugin error: '${dep}' not install`);
            }
            try {
                const mod = require(npmPath);
                this.core.addPlugin(mod);
            }
            catch (e) {
                e.message = `Auto load mw plugin error: ${e.message}`;
                throw e;
            }
        });
    }
    // 加载平台方插件
    loadPlatformPlugin() { }
    // 加载cli拓展
    loadExtensions() {
        return {};
    }
    // 覆盖默认的 core options
    coverCoreOptions() {
        return {};
    }
    // 加载命令行输出及报错
    loadLog() {
        return { ...console, error: this.error };
    }
    getUsageInfo(commandsArray, usage, coreInstance, commandInfo) {
        let commandList = {};
        if (commandsArray && commandsArray.length) {
            commandList = {
                header: commandsArray.join(' ') +
                    ((commandInfo === null || commandInfo === void 0 ? void 0 : commandInfo.alias) ? `/${commandInfo.alias}` : ''),
                content: commandInfo === null || commandInfo === void 0 ? void 0 : commandInfo.usage,
                optionList: usage
                    ? Object.keys(usage).map(name => {
                        const usageInfo = usage[name] || {};
                        return {
                            name,
                            description: usageInfo.usage,
                            alias: usageInfo.shortcut,
                        };
                    })
                    : [],
                childCommands: (commandInfo === null || commandInfo === void 0 ? void 0 : commandInfo.commands)
                    ? Object.keys(commandInfo === null || commandInfo === void 0 ? void 0 : commandInfo.commands).map(command => {
                        const childCommandInfo = commandInfo === null || commandInfo === void 0 ? void 0 : commandInfo.commands[command];
                        return this.getUsageInfo([command], childCommandInfo.options, coreInstance, childCommandInfo);
                    })
                    : null,
            };
        }
        else {
            commandList = [];
            coreInstance.instances.forEach(plugin => {
                if (!plugin.commands) {
                    return;
                }
                Object.keys(plugin.commands).forEach(command => {
                    const commandInfo = plugin.commands[command];
                    if (!commandInfo || !commandInfo.lifecycleEvents) {
                        return;
                    }
                    commandList.push({
                        header: command,
                        content: commandInfo.usage,
                        optionList: Object.keys(commandInfo.options || {}).map(name => {
                            const usageInfo = commandInfo.options[name] || {};
                            return {
                                name,
                                description: usageInfo.usage,
                                alias: usageInfo.shortcut,
                            };
                        }),
                        childCommands: commandInfo.commands
                            ? Object.keys(commandInfo.commands).map(command => {
                                const childCommandInfo = commandInfo.commands[command];
                                return this.getUsageInfo([command], childCommandInfo.options, coreInstance, childCommandInfo);
                            })
                            : null,
                    });
                });
            });
        }
        return commandList;
    }
    // 展示帮助信息
    displayUsage(commandsArray, usage, coreInstance, commandInfo) {
        const log = this.loadLog();
        const commandList = this.getUsageInfo(commandsArray, usage, coreInstance, commandInfo);
        log.log((0, commandLineUsage_1.commandLineUsage)(commandList));
    }
    error(err) {
        console.error((err && err.message) || err);
        process.exit(1);
    }
    loadRelativePlugin(dirPath, path) {
        try {
            const localPlugin = require((0, path_1.join)(this.cwd, dirPath, path));
            this.core.addPlugin(localPlugin);
            return true;
        }
        catch (e) {
            return false;
        }
    }
    async start() {
        await this.loadPlugins();
        await this.core.ready();
        await this.core.invoke();
    }
}
exports.CoreBaseCLI = CoreBaseCLI;
//# sourceMappingURL=cli.js.map