import { getDynamicPackageVersion, getNpmList, getPackageVersion, hasPackage, versionCompare } from './util';
export declare class RunnerContainer {
    reporters: any;
    baseDir: any;
    queue: any;
    mc: any;
    constructor(options?: any);
    loadRulePackage(): void;
    run(): Promise<void>;
    registerReport(reporter: any): void;
    addRule(rule: any): void;
    report(data: any): void;
    reportError(err: any): void;
    getBaseDir(): any;
    getQueue(): any;
    getMessageCenter(): any;
}
export declare class Runner {
    skip: boolean;
    innerGroup: any;
    baseDir: any;
    queue: any;
    mc: any;
    runnerContainer: any;
    utils: {
        getDynamicPackageVersion: typeof getDynamicPackageVersion;
        getPackageVersion: typeof getPackageVersion;
        getNpmList: typeof getNpmList;
        hasPackage: typeof hasPackage;
        versionCompare: typeof versionCompare;
    };
    constructor(runnerContainer: any);
    getGroup(): any;
    group(value: any): this;
    invoke(title: any, fn: any, dataHandler: any): void;
    info(title: any, fn: any): this;
    check(title: any, assertFn: any): this;
    warn(title: any, assertFn: any): this;
    skipWhen(checkFn: any): this;
}
//# sourceMappingURL=runner.d.ts.map