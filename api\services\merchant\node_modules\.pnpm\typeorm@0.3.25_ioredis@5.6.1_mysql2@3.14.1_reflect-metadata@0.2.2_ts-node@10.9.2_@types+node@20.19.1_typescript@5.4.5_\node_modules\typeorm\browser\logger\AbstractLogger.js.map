{"version": 3, "sources": ["../browser/src/logger/AbstractLogger.ts"], "names": [], "mappings": "AASA,OAAO,EAAE,aAAa,EAAE,MAAM,2BAA2B,CAAA;AAEzD,MAAM,OAAgB,cAAc;IAChC,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,OAAuB;QAAvB,YAAO,GAAP,OAAO,CAAgB;IAAG,CAAC;IAEjD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,QAAQ,CAAC,KAAa,EAAE,UAAkB,EAAE,WAAyB;QACjE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,QAAQ,CACT,OAAO,EACP;YACI,IAAI,EAAE,OAAO;YACb,MAAM,EAAE,OAAO;YACf,OAAO,EAAE,KAAK;YACd,MAAM,EAAE,KAAK;YACb,UAAU;SACb,EACD,WAAW,CACd,CAAA;IACL,CAAC;IAED;;OAEG;IACH,aAAa,CACT,KAAa,EACb,KAAa,EACb,UAAkB,EAClB,WAAyB;QAEzB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE,CAAC;YACvC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,QAAQ,CACT,MAAM,EACN;YACI;gBACI,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,UAAU;aACb;YACD;gBACI,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,OAAO;gBACf,OAAO,EAAE,KAAK;aACjB;SACJ,EACD,WAAW,CACd,CAAA;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CACR,IAAY,EACZ,KAAa,EACb,UAAkB,EAClB,WAAyB;QAEzB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,EAAE,CAAC;YACtC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,QAAQ,CACT,MAAM,EACN;YACI;gBACI,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,eAAe;gBACvB,OAAO,EAAE,KAAK;gBACd,MAAM,EAAE,KAAK;gBACb,UAAU;gBACV,cAAc,EAAE;oBACZ,IAAI;iBACP;aACJ;YACD;gBACI,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,gBAAgB;gBACxB,OAAO,EAAE,IAAI;aAChB;SACJ,EACD,WAAW,CACd,CAAA;IACL,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,OAAe,EAAE,WAAyB;QACrD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC;YACxC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,QAAQ,CACT,QAAQ,EACR;YACI,IAAI,EAAE,cAAc;YACpB,OAAO;SACV,EACD,WAAW,CACd,CAAA;IACL,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,OAAe,EAAE,WAAyB;QACnD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,OAAM;QACV,CAAC;QAED,IAAI,CAAC,QAAQ,CACT,KAAK,EACL;YACI,IAAI,EAAE,WAAW;YACjB,OAAO;SACV,EACD,WAAW,CACd,CAAA;IACL,CAAC;IAED;;;OAGG;IACH,GAAG,CACC,KAA8B,EAC9B,OAAY,EACZ,WAAyB;QAEzB,QAAQ,KAAK,EAAE,CAAC;YACZ,KAAK,KAAK;gBACN,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;oBAC/B,OAAM;gBACV,CAAC;gBAED,IAAI,CAAC,QAAQ,CACT,KAAK,EACL;oBACI,IAAI,EAAE,KAAK;oBACX,OAAO;iBACV,EACD,WAAW,CACd,CAAA;gBACD,MAAK;YAET,KAAK,MAAM;gBACP,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;oBAChC,OAAM;gBACV,CAAC;gBAED,IAAI,CAAC,QAAQ,CACT,MAAM,EACN;oBACI,IAAI,EAAE,MAAM;oBACZ,MAAM,EAAE,MAAM;oBACd,OAAO;iBACV,EACD,WAAW,CACd,CAAA;gBACD,MAAK;YAET,KAAK,MAAM;gBACP,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC;oBAChC,OAAM;gBACV,CAAC;gBAED,IAAI,CAAC,QAAQ,CACT,MAAM,EACN;oBACI,IAAI,EAAE,MAAM;oBACZ,OAAO;iBACV,EACD,WAAW,CACd,CAAA;gBACD,MAAK;QACb,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,eAAe,CAAC,IAAgC;QACtD,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO;gBACR,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,IAAI,CAAC,OAAO,KAAK,IAAI;oBACrB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5C,CAAA;YAEL,KAAK,OAAO,CAAC;YACb,KAAK,aAAa;gBACd,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,IAAI,CAAC,OAAO,KAAK,IAAI;oBACrB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAC5C,CAAA;YAEL,KAAK,YAAY;gBACb,OAAO,IAAI,CAAA;YAEf,KAAK,QAAQ,CAAC;YACd,KAAK,cAAc;gBACf,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAC7C,CAAA;YAEL,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAA;YAEf,KAAK,KAAK;gBACN,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAC1C,CAAA;YAEL,KAAK,MAAM;gBACP,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3C,CAAA;YAEL,KAAK,MAAM;gBACP,OAAO,CACH,IAAI,CAAC,OAAO,KAAK,KAAK;oBACtB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;wBACxB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3C,CAAA;YAEL;gBACI,OAAO,KAAK,CAAA;QACpB,CAAC;IACL,CAAC;IAeD;;OAEG;IACO,kBAAkB,CACxB,UAIsC,EACtC,OAA4C,EAC5C,WAAyB;QAEzB,OAAO,GAAG;YACN,GAAG;gBACC,gBAAgB,EAAE,IAAI;gBACtB,wBAAwB,EAAE,IAAI;gBAC9B,YAAY,EAAE,IAAI;gBAClB,SAAS,EAAE,KAAK;aACnB;YACD,GAAG,OAAO;SACb,CAAA;QACD,MAAM,QAAQ,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAA;QAEtE,KAAK,IAAI,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC3B,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC9B,OAAO,GAAG;oBACN,OAAO;iBACV,CAAA;YACL,CAAC;YAED,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE,CAAC;gBAC3B,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA;gBAEjC,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;oBACpB,GAAG,GAAG,aAAa,CAAC,SAAS,CACzB,GAAG,EACH,WAAW,EAAE,UAAU,EAAE,OAAO,CAAC,IAAI,CACxC,CAAA;gBACL,CAAC;gBAED,IACI,OAAO,CAAC,wBAAwB;oBAChC,OAAO,CAAC,UAAU;oBAClB,OAAO,CAAC,UAAU,CAAC,MAAM,EAC3B,CAAC;oBACC,GAAG,IAAI,mBAAmB,IAAI,CAAC,eAAe,CAC1C,OAAO,CAAC,UAAU,CACrB,EAAE,CAAA;gBACP,CAAC;gBAED,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;oBACvB,GAAG,GAAG,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,CAAA;gBACzC,CAAC;gBAED,OAAO,CAAC,OAAO,GAAG,GAAG,CAAA;YACzB,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC7C,OAAO,CAAC,MAAM,IAAI,GAAG,CAAA;YACzB,CAAC;QACL,CAAC;QAED,OAAO,QAAwB,CAAA;IACnC,CAAC;IAED;;;OAGG;IACO,eAAe,CAAC,UAAiB;QACvC,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,+CAA+C;YAC/C,OAAO,UAAU,CAAA;QACrB,CAAC;IACL,CAAC;CACJ", "file": "AbstractLogger.js", "sourcesContent": ["import {\n    Logger,\n    Log<PERSON><PERSON>l,\n    Log<PERSON><PERSON>age,\n    LogMessageType,\n    PrepareLogMessagesOptions,\n} from \"./Logger\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { LoggerOptions } from \"./LoggerOptions\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\n\nexport abstract class AbstractLogger implements Logger {\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected options?: LoggerOptions) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Logs query and parameters used in it.\n     */\n    logQuery(query: string, parameters?: any[], queryRunner?: QueryRunner) {\n        if (!this.isLogEnabledFor(\"query\")) {\n            return\n        }\n\n        this.writeLog(\n            \"query\",\n            {\n                type: \"query\",\n                prefix: \"query\",\n                message: query,\n                format: \"sql\",\n                parameters,\n            },\n            queryRunner,\n        )\n    }\n\n    /**\n     * Logs query that is failed.\n     */\n    logQueryError(\n        error: string,\n        query: string,\n        parameters?: any[],\n        queryRunner?: QueryRunner,\n    ) {\n        if (!this.isLogEnabledFor(\"query-error\")) {\n            return\n        }\n\n        this.writeLog(\n            \"warn\",\n            [\n                {\n                    type: \"query-error\",\n                    prefix: \"query failed\",\n                    message: query,\n                    format: \"sql\",\n                    parameters,\n                },\n                {\n                    type: \"query-error\",\n                    prefix: \"error\",\n                    message: error,\n                },\n            ],\n            queryRunner,\n        )\n    }\n\n    /**\n     * Logs query that is slow.\n     */\n    logQuerySlow(\n        time: number,\n        query: string,\n        parameters?: any[],\n        queryRunner?: QueryRunner,\n    ) {\n        if (!this.isLogEnabledFor(\"query-slow\")) {\n            return\n        }\n\n        this.writeLog(\n            \"warn\",\n            [\n                {\n                    type: \"query-slow\",\n                    prefix: \"query is slow\",\n                    message: query,\n                    format: \"sql\",\n                    parameters,\n                    additionalInfo: {\n                        time,\n                    },\n                },\n                {\n                    type: \"query-slow\",\n                    prefix: \"execution time\",\n                    message: time,\n                },\n            ],\n            queryRunner,\n        )\n    }\n\n    /**\n     * Logs events from the schema build process.\n     */\n    logSchemaBuild(message: string, queryRunner?: QueryRunner) {\n        if (!this.isLogEnabledFor(\"schema-build\")) {\n            return\n        }\n\n        this.writeLog(\n            \"schema\",\n            {\n                type: \"schema-build\",\n                message,\n            },\n            queryRunner,\n        )\n    }\n\n    /**\n     * Logs events from the migration run process.\n     */\n    logMigration(message: string, queryRunner?: QueryRunner) {\n        if (!this.isLogEnabledFor(\"migration\")) {\n            return\n        }\n\n        this.writeLog(\n            \"log\",\n            {\n                type: \"migration\",\n                message,\n            },\n            queryRunner,\n        )\n    }\n\n    /**\n     * Perform logging using given logger, or by default to the console.\n     * Log has its own level and message.\n     */\n    log(\n        level: \"log\" | \"info\" | \"warn\",\n        message: any,\n        queryRunner?: QueryRunner,\n    ) {\n        switch (level) {\n            case \"log\":\n                if (!this.isLogEnabledFor(\"log\")) {\n                    return\n                }\n\n                this.writeLog(\n                    \"log\",\n                    {\n                        type: \"log\",\n                        message,\n                    },\n                    queryRunner,\n                )\n                break\n\n            case \"info\":\n                if (!this.isLogEnabledFor(\"info\")) {\n                    return\n                }\n\n                this.writeLog(\n                    \"info\",\n                    {\n                        type: \"info\",\n                        prefix: \"info\",\n                        message,\n                    },\n                    queryRunner,\n                )\n                break\n\n            case \"warn\":\n                if (!this.isLogEnabledFor(\"warn\")) {\n                    return\n                }\n\n                this.writeLog(\n                    \"warn\",\n                    {\n                        type: \"warn\",\n                        message,\n                    },\n                    queryRunner,\n                )\n                break\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Check is logging for level or message type is enabled.\n     */\n    protected isLogEnabledFor(type?: LogLevel | LogMessageType) {\n        switch (type) {\n            case \"query\":\n                return (\n                    this.options === \"all\" ||\n                    this.options === true ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"query\") !== -1)\n                )\n\n            case \"error\":\n            case \"query-error\":\n                return (\n                    this.options === \"all\" ||\n                    this.options === true ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"error\") !== -1)\n                )\n\n            case \"query-slow\":\n                return true\n\n            case \"schema\":\n            case \"schema-build\":\n                return (\n                    this.options === \"all\" ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"schema\") !== -1)\n                )\n\n            case \"migration\":\n                return true\n\n            case \"log\":\n                return (\n                    this.options === \"all\" ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"log\") !== -1)\n                )\n\n            case \"info\":\n                return (\n                    this.options === \"all\" ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"info\") !== -1)\n                )\n\n            case \"warn\":\n                return (\n                    this.options === \"all\" ||\n                    (Array.isArray(this.options) &&\n                        this.options.indexOf(\"warn\") !== -1)\n                )\n\n            default:\n                return false\n        }\n    }\n\n    /**\n     * Write log to specific output.\n     */\n    protected abstract writeLog(\n        level: LogLevel,\n        message:\n            | LogMessage\n            | string\n            | number\n            | (LogMessage | string | number)[],\n        queryRunner?: QueryRunner,\n    ): void\n\n    /**\n     * Prepare and format log messages\n     */\n    protected prepareLogMessages(\n        logMessage:\n            | LogMessage\n            | string\n            | number\n            | (LogMessage | string | number)[],\n        options?: Partial<PrepareLogMessagesOptions>,\n        queryRunner?: QueryRunner,\n    ): LogMessage[] {\n        options = {\n            ...{\n                addColonToPrefix: true,\n                appendParameterAsComment: true,\n                highlightSql: true,\n                formatSql: false,\n            },\n            ...options,\n        }\n        const messages = Array.isArray(logMessage) ? logMessage : [logMessage]\n\n        for (let message of messages) {\n            if (typeof message !== \"object\") {\n                message = {\n                    message,\n                }\n            }\n\n            if (message.format === \"sql\") {\n                let sql = String(message.message)\n\n                if (options.formatSql) {\n                    sql = PlatformTools.formatSql(\n                        sql,\n                        queryRunner?.connection?.options.type,\n                    )\n                }\n\n                if (\n                    options.appendParameterAsComment &&\n                    message.parameters &&\n                    message.parameters.length\n                ) {\n                    sql += ` -- PARAMETERS: ${this.stringifyParams(\n                        message.parameters,\n                    )}`\n                }\n\n                if (options.highlightSql) {\n                    sql = PlatformTools.highlightSql(sql)\n                }\n\n                message.message = sql\n            }\n\n            if (options.addColonToPrefix && message.prefix) {\n                message.prefix += \":\"\n            }\n        }\n\n        return messages as LogMessage[]\n    }\n\n    /**\n     * Converts parameters to a string.\n     * Sometimes parameters can have circular objects and therefor we are handle this case too.\n     */\n    protected stringifyParams(parameters: any[]) {\n        try {\n            return JSON.stringify(parameters)\n        } catch (error) {\n            // most probably circular objects in parameters\n            return parameters\n        }\n    }\n}\n"], "sourceRoot": ".."}