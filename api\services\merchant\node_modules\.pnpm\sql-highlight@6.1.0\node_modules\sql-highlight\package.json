{"name": "sql-highlight", "version": "6.1.0", "description": "A simple and lightweight library for highlighting SQL queries written in pure JavaScript", "main": "lib/index.js", "types": "./lib/index.d.ts", "repository": {"type": "git", "url": "**************:scriptcoded/sql-highlight.git"}, "files": ["/lib"], "engines": {"node": ">=14"}, "scripts": {"pretest": "npm run lint", "test": "jest", "test:coverage": "jest --coverage", "lint": "biome check", "lint:fix": "biome check --write"}, "keywords": ["sql", "syntax", "highlight", "highlighter"], "author": "<PERSON> <<EMAIL>>", "contributors": ["poma<PERSON><PERSON>"], "funding": ["https://github.com/scriptcoded/sql-highlight?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/scriptcoded"}], "license": "MIT", "devDependencies": {"@biomejs/biome": "1.9.4", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "jest": "^29.7.0", "semantic-release": "^24.2.5"}}