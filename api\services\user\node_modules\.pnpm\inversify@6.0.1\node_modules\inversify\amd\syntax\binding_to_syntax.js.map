{"version": 3, "file": "binding_to_syntax.js", "sourceRoot": "", "sources": ["../../src/syntax/binding_to_syntax.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;IAMA;QAIE,yBAAmB,OAA8B;YAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;QAEM,4BAAE,GAAT,UAAU,WAAwC;YAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,WAAW,CAAC;YAC/C,OAAO,IAAI,iDAAqB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;QAEM,gCAAM,GAAb;YACE,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,KAAK,UAAU,EAAE;gBACzD,MAAM,IAAI,KAAK,CAAC,KAAG,UAAU,CAAC,qBAAuB,CAAC,CAAC;aACxD;YACD,IAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;YAC7C,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QAEM,yCAAe,GAAtB,UAAuB,KAAQ;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,aAAa,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,wCAAc,GAArB,UAAsB,IAAgC;YACpD,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,YAAY,CAAC;YAClD,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,IAAI,CAAC;YACxC,OAAO,IAAI,iDAAqB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,CAAC;QAEM,uCAAa,GAApB,UAAyB,WAAmC;YAC1D,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,WAAW,CAAC;YACjD,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,WAA2B,CAAC;YAC/D,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,mCAAS,GAAhB,UAAqB,OAAsC;YACzD,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,OAAO,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;YAChC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,oCAAU,GAAjB,UAAkB,IAAO;YAEvB,IAAI,OAAO,IAAI,KAAK,UAAU,EAAE;gBAAE,MAAM,IAAI,KAAK,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;aAAE;YACzF,IAAM,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,mBAAmB,CAAC;QAC7B,CAAC;QAEM,uCAAa,GAApB,UAAyB,iBAAmD;YAC1E,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,OAAO,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAC,OAAO;gBAC9B,IAAM,WAAW,GAAG,cAAM,OAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAK,iBAAiB,CAAC,EAA5C,CAA4C,CAAC;gBACvE,OAAO,WAAW,CAAC;YACrB,CAAC,CAAC;YACF,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,4CAAkB,GAAzB,UAA8B,iBAAmD;YAC/E,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,OAAO,CAAC;YAC7C,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,UAAC,OAAO;gBAC9B,OAAO,UAAC,KAAc,IAAK,OAAA,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAK,iBAAiB,EAAE,KAAe,CAAC,EAAlE,CAAkE,CAAC;YAChG,CAAC,CAAC;YACF,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,oCAAU,GAAjB,UAAsB,QAAwC;YAC5D,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,+BAAe,CAAC,QAAQ,CAAC;YAC9C,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,gCAAgB,CAAC,SAAS,CAAC;YACjD,OAAO,IAAI,4CAAmB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QACnD,CAAC;QAEM,mCAAS,GAAhB,UAAiB,OAAyE;YACxF,IAAI,CAAC,cAAc,CACjB,UAAC,OAAO,IAAK,OAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAI,OAAO,CAAC,EAAjC,CAAiC,CAC/C,CAAC;QACJ,CAAC;QAEH,sBAAC;IAAD,CAAC,AA7FD,IA6FC;IAEQ,0CAAe"}