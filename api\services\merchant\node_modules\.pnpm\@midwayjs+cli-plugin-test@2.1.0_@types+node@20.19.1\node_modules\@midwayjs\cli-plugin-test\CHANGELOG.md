# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.1.0](https://github.com/midwayjs/cli/compare/serverless-v1.2.39...serverless-v2.1.0) (2023-05-30)



## 2.0.14 (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))



## 2.0.6 (2022-12-09)



## 2.0.5 (2022-12-01)



## 2.0.4 (2022-11-23)



## 2.0.3 (2022-11-21)



## 2.0.2 (2022-11-17)


### Bug Fixes

* dev pack 302 redirect ([#302](https://github.com/midwayjs/cli/issues/302)) ([3fc3160](https://github.com/midwayjs/cli/commit/3fc3160dc65fcc8208611ecdd123e8a9db167f0c)), closes [midwayjs/midway#2473](https://github.com/midwayjs/midway/issues/2473) [midwayjs/midway#2010](https://github.com/midwayjs/midway/issues/2010)



## 2.0.1 (2022-10-28)



## 2.0.1-beta.1 (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))



# 2.0.0 (2022-10-13)



# 2.0.0-beta.1 (2022-10-10)



## 1.3.14 (2022-10-10)



## 1.3.13 (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))



## 1.3.13-beta.3 (2022-09-07)



## 1.3.7 (2022-07-06)


### Bug Fixes

* common http daya type ([#286](https://github.com/midwayjs/cli/issues/286)) ([9afe43c](https://github.com/midwayjs/cli/commit/9afe43c09a1c73edf9d7112aed0ea5271d7ab3f7))
* **test:** avoid passing ignoreTypeCheck to jest ([#276](https://github.com/midwayjs/cli/issues/276)) ([7e76397](https://github.com/midwayjs/cli/commit/7e763979c80d0e4f2cfdf36bd7e7a074f5cd10a7))



## 1.3.5 (2022-05-25)



## 1.3.5-beta.3 (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))
* upgrade ts-node to 10 ([#282](https://github.com/midwayjs/cli/issues/282)) ([7a1bbb1](https://github.com/midwayjs/cli/commit/7a1bbb11c89e74f5c31dad3e9b7fba2f22cd03da))



## 1.3.4 (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))



## 1.3.3 (2022-04-20)



## 1.3.1 (2022-03-10)



## 1.3.1-beta.1 (2022-03-10)



## 1.2.97 (2022-01-24)



## 1.2.95 (2022-01-20)



## 1.2.94 (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))



## 1.2.93 (2021-12-29)



## 1.2.92 (2021-12-03)



## 1.2.91 (2021-11-26)



## 1.2.86 (2021-11-04)



## 1.2.85 (2021-10-21)



## 1.2.84 (2021-09-27)



## 1.2.82 (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))



## 1.2.79 (2021-08-11)



## 1.2.76 (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))



## 1.2.73 (2021-06-25)



## 1.2.72 (2021-06-18)


### Bug Fixes

* copy js file when execute build command ([#120](https://github.com/midwayjs/cli/issues/120)) ([404aa40](https://github.com/midwayjs/cli/commit/404aa4074e3866a0914d918e96a922bbb267bad9))
* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))



## 1.2.69 (2021-06-01)



## 1.2.68 (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))



## 1.2.65 (2021-04-23)



## 1.2.62 (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))



## 1.2.60 (2021-04-08)


### Bug Fixes

* support serverless dev ([#80](https://github.com/midwayjs/cli/issues/80)) ([62268ed](https://github.com/midwayjs/cli/commit/62268edda31881babedb5762fe55ca8d48fd0bab))



## 1.2.56 (2021-03-26)


### Bug Fixes

* test node env ([#81](https://github.com/midwayjs/cli/issues/81)) ([99e780d](https://github.com/midwayjs/cli/commit/99e780ddbfa97ed67aeafb417f1cd049d640d597))



## 1.2.54 (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))



## 1.2.51 (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))



## 1.2.50 (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))



## 1.2.48 (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))



## 1.2.46 (2021-03-05)



## 1.2.45 (2021-03-04)



## 1.2.41 (2021-02-17)



## 1.2.40 (2021-02-03)



## 1.2.39-beta.5 (2021-02-03)





## [2.0.14](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))





## [2.0.14-beta.4](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.4) (2023-03-03)


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))





## [2.0.14-beta.3](https://github.com/midwayjs/cli/compare/v2.0.14-beta.2...v2.0.14-beta.3) (2023-02-15)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.14-beta.2](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.2) (2023-02-14)


### Bug Fixes

* prevent log ([2a60bc6](https://github.com/midwayjs/cli/commit/2a60bc6e9a5f75edff826e817565cb6822faba0e))





## [2.0.6](https://github.com/midwayjs/cli/compare/v2.0.6-beta.3...v2.0.6) (2022-12-09)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.4](https://github.com/midwayjs/cli/compare/v2.0.3...v2.0.4) (2022-11-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.3](https://github.com/midwayjs/cli/compare/v2.0.2...v2.0.3) (2022-11-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.2](https://github.com/midwayjs/cli/compare/v2.0.1...v2.0.2) (2022-11-17)


### Bug Fixes

* dev pack 302 redirect ([#302](https://github.com/midwayjs/cli/issues/302)) ([3fc3160](https://github.com/midwayjs/cli/commit/3fc3160dc65fcc8208611ecdd123e8a9db167f0c)), closes [midwayjs/midway#2473](https://github.com/midwayjs/midway/issues/2473) [midwayjs/midway#2010](https://github.com/midwayjs/midway/issues/2010)





## [2.0.1](https://github.com/midwayjs/cli/compare/v2.0.1-beta.1...v2.0.1) (2022-10-28)

**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [2.0.1-beta.1](https://github.com/midwayjs/cli/compare/v2.0.0...v2.0.1-beta.1) (2022-10-26)


### Features

* upgrade jest to 29 ([#300](https://github.com/midwayjs/cli/issues/300)) ([e2b211a](https://github.com/midwayjs/cli/commit/e2b211a43345131ec6c89a9a4263f57403c26474))





# [2.0.0](https://github.com/midwayjs/cli/compare/v1.3.15...v2.0.0) (2022-10-13)



# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





# [2.0.0](https://github.com/midwayjs/cli/compare/v2.0.0-beta.1...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





# [2.0.0-beta.1](https://github.com/midwayjs/cli/compare/v1.3.14...v2.0.0-beta.1) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14) (2022-10-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14-beta.10](https://github.com/midwayjs/cli/compare/v1.3.14-beta.9...v1.3.14-beta.10) (2022-09-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14-beta.9](https://github.com/midwayjs/cli/compare/v1.3.14-beta.8...v1.3.14-beta.9) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14-beta.8](https://github.com/midwayjs/cli/compare/v1.3.14-beta.7...v1.3.14-beta.8) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14-beta.7](https://github.com/midwayjs/cli/compare/v1.3.14-beta.6...v1.3.14-beta.7) (2022-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.14-beta.6](https://github.com/midwayjs/cli/compare/v1.3.14-beta.5...v1.3.14-beta.6) (2022-09-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13-beta.4](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13-beta.4) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.13-beta.3](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.13-beta.3) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13) (2022-09-07)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.13-beta.2](https://github.com/midwayjs/cli/compare/v1.3.13-beta.1...v1.3.13-beta.2) (2022-09-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.13-beta.1](https://github.com/midwayjs/cli/compare/v1.3.11...v1.3.13-beta.1) (2022-08-29)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.12](https://github.com/midwayjs/cli/compare/v1.3.12-beta.3...v1.3.12) (2022-08-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.7](https://github.com/midwayjs/cli/compare/v1.3.6...v1.3.7) (2022-07-06)


### Bug Fixes

* common http daya type ([#286](https://github.com/midwayjs/cli/issues/286)) ([9afe43c](https://github.com/midwayjs/cli/commit/9afe43c09a1c73edf9d7112aed0ea5271d7ab3f7))
* **test:** avoid passing ignoreTypeCheck to jest ([#276](https://github.com/midwayjs/cli/issues/276)) ([7e76397](https://github.com/midwayjs/cli/commit/7e763979c80d0e4f2cfdf36bd7e7a074f5cd10a7))





## [1.3.7-beta.1](https://github.com/midwayjs/cli/compare/v1.3.6...v1.3.7-beta.1) (2022-07-06)


### Bug Fixes

* common http daya type ([3498985](https://github.com/midwayjs/cli/commit/3498985c10fc21b47dbacf07b44068bb82c6b815))
* mocha reporter ([d886999](https://github.com/midwayjs/cli/commit/d8869996c54a52efb0774a4a11f91489a4a8aaec))
* **test:** avoid passing ignoreTypeCheck to jest ([#276](https://github.com/midwayjs/cli/issues/276)) ([7e76397](https://github.com/midwayjs/cli/commit/7e763979c80d0e4f2cfdf36bd7e7a074f5cd10a7))





## [1.3.5](https://github.com/midwayjs/cli/compare/v1.3.5-beta.3...v1.3.5) (2022-05-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.5-beta.3](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.3) (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))
* upgrade ts-node to 10 ([#282](https://github.com/midwayjs/cli/issues/282)) ([7a1bbb1](https://github.com/midwayjs/cli/commit/7a1bbb11c89e74f5c31dad3e9b7fba2f22cd03da))





## [1.3.5-beta.2](https://github.com/midwayjs/cli/compare/v1.3.5-beta.1...v1.3.5-beta.2) (2022-05-24)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.4](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4) (2022-05-11)


### Bug Fixes

* cli time tick ([#278](https://github.com/midwayjs/cli/issues/278)) ([b80a15b](https://github.com/midwayjs/cli/commit/b80a15b62d826359ae4b36dc3b6cfc21f2e4ba18))





## [1.3.4-beta.1](https://github.com/midwayjs/cli/compare/v1.3.3...v1.3.4-beta.1) (2022-04-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.3](https://github.com/midwayjs/cli/compare/v1.3.2...v1.3.3) (2022-04-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.1](https://github.com/midwayjs/cli/compare/v1.3.1-beta.1...v1.3.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.3.1-beta.1](https://github.com/midwayjs/cli/compare/v1.3.0...v1.3.1-beta.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.97](https://github.com/midwayjs/cli/compare/v1.2.96...v1.2.97) (2022-01-24)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.95](https://github.com/midwayjs/cli/compare/v1.2.94...v1.2.95) (2022-01-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.94](https://github.com/midwayjs/cli/compare/v1.2.93...v1.2.94) (2022-01-11)


### Bug Fixes

* ignore optional ([#252](https://github.com/midwayjs/cli/issues/252)) ([841fbbc](https://github.com/midwayjs/cli/commit/841fbbcbca85b5b0a14089395477bf10da57fa1d))





## [1.2.93](https://github.com/midwayjs/cli/compare/v1.2.92...v1.2.93) (2021-12-29)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.92](https://github.com/midwayjs/cli/compare/v1.2.91...v1.2.92) (2021-12-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.91](https://github.com/midwayjs/cli/compare/v1.2.90...v1.2.91) (2021-11-26)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.86](https://github.com/midwayjs/cli/compare/v1.2.85...v1.2.86) (2021-11-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.85](https://github.com/midwayjs/cli/compare/v1.2.84...v1.2.85) (2021-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.84](https://github.com/midwayjs/cli/compare/v1.2.83...v1.2.84) (2021-09-27)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.82](https://github.com/midwayjs/cli/compare/v1.2.81...v1.2.82) (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))





## [1.2.79](https://github.com/midwayjs/cli/compare/v1.2.78...v1.2.79) (2021-08-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.76](https://github.com/midwayjs/cli/compare/v1.2.75...v1.2.76) (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))





## [1.2.73](https://github.com/midwayjs/cli/compare/v1.2.73-beta.1...v1.2.73) (2021-06-25)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.72](https://github.com/midwayjs/cli/compare/v1.2.71...v1.2.72) (2021-06-18)


### Bug Fixes

* copy js file when execute build command ([#120](https://github.com/midwayjs/cli/issues/120)) ([404aa40](https://github.com/midwayjs/cli/commit/404aa4074e3866a0914d918e96a922bbb267bad9))
* deploy with env ([#128](https://github.com/midwayjs/cli/issues/128)) ([bcfbff8](https://github.com/midwayjs/cli/commit/bcfbff8e70f9707695f2a837f4c6f7c55cb56d38))





## [1.2.69](https://github.com/midwayjs/cli/compare/v1.2.68...v1.2.69) (2021-06-01)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.68](https://github.com/midwayjs/cli/compare/v1.2.67...v1.2.68) (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))





## [1.2.65](https://github.com/midwayjs/cli/compare/v1.2.63...v1.2.65) (2021-04-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.62](https://github.com/midwayjs/cli/compare/v1.2.61...v1.2.62) (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))





## [1.2.60](https://github.com/midwayjs/cli/compare/v1.2.59...v1.2.60) (2021-04-08)


### Bug Fixes

* support serverless dev ([#80](https://github.com/midwayjs/cli/issues/80)) ([62268ed](https://github.com/midwayjs/cli/commit/62268edda31881babedb5762fe55ca8d48fd0bab))





## [1.2.56](https://github.com/midwayjs/cli/compare/v1.2.55...v1.2.56) (2021-03-26)


### Bug Fixes

* test node env ([#81](https://github.com/midwayjs/cli/issues/81)) ([99e780d](https://github.com/midwayjs/cli/commit/99e780ddbfa97ed67aeafb417f1cd049d640d597))





## [1.2.54](https://github.com/midwayjs/cli/compare/v1.2.53...v1.2.54) (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))





## [1.2.51](https://github.com/midwayjs/cli/compare/v1.2.50...v1.2.51) (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))





## [1.2.50](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.50) (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))







**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.49](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.49) (2021-03-06)

**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.48](https://github.com/midwayjs/cli/compare/v1.2.46...v1.2.48) (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))





## [1.2.46](https://github.com/midwayjs/cli/compare/v1.2.45...v1.2.46) (2021-03-05)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.45](https://github.com/midwayjs/cli/compare/v1.2.44...v1.2.45) (2021-03-04)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.41](https://github.com/midwayjs/cli/compare/v1.2.40...v1.2.41) (2021-02-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.40](https://github.com/midwayjs/cli/compare/v1.2.39-beta.5...v1.2.40) (2021-02-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.37](https://github.com/midwayjs/cli/compare/v1.2.35...v1.2.37) (2021-01-08)


### Bug Fixes

* support package diagnostics & tsConfig config ([#38](https://github.com/midwayjs/cli/issues/38)) ([c499d14](https://github.com/midwayjs/cli/commit/c499d145f9cabf427877ec8ea65aea8ead42b9cd))





## [1.2.35](https://github.com/midwayjs/cli/compare/v1.2.33...v1.2.35) (2020-12-24)


### Bug Fixes

* copy file error catch ([8d2097c](https://github.com/midwayjs/cli/commit/8d2097c538f22ed6050c85d1c250436e0c2c71c1))





## [1.2.34](https://github.com/midwayjs/cli/compare/v1.2.34-beta.2...v1.2.34) (2020-12-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.33](https://github.com/midwayjs/cli/compare/v1.2.32...v1.2.33) (2020-12-17)


### Features

* jest match pattern support full name with dot ([#34](https://github.com/midwayjs/cli/issues/34)) ([04b50a9](https://github.com/midwayjs/cli/commit/04b50a97e6e1b952ccccfaca9b10a75a7897ec0f))





## [1.2.32](https://github.com/midwayjs/cli/compare/v1.2.32-beta...v1.2.32) (2020-12-08)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.31](https://github.com/midwayjs/cli/compare/v1.2.30...v1.2.31) (2020-12-03)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.30](https://github.com/midwayjs/cli/compare/v1.2.30-beta...v1.2.30) (2020-11-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.29](https://github.com/midwayjs/cli/compare/serverless-v1.2.28...serverless-v1.2.29) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.28](https://github.com/midwayjs/cli/compare/serverless-v1.2.27...serverless-v1.2.28) (2020-11-18)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.27](https://github.com/midwayjs/cli/compare/serverless-v1.2.26...serverless-v1.2.27) (2020-11-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.26](https://github.com/midwayjs/cli/compare/serverless-v1.2.21...serverless-v1.2.26) (2020-11-17)



## 1.2.25 (2020-11-12)



## 1.2.25-beta.1 (2020-11-12)



## 1.2.24 (2020-11-12)


### Bug Fixes

* support copy static file ([#20](https://github.com/midwayjs/cli/issues/20)) ([39021c4](https://github.com/midwayjs/cli/commit/39021c4b82f962d970a0fc5b9a96b8ae8d66dba1))



## 1.2.23 (2020-11-11)



## 1.2.23-beta.3 (2020-11-10)



## 1.2.23-beta.2 (2020-10-30)



## 1.2.23-beta.1 (2020-10-26)


### Bug Fixes

* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.25](https://github.com/midwayjs/cli/compare/v1.2.25-beta.1...v1.2.25) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.25-beta.1](https://github.com/midwayjs/cli/compare/v1.2.24-beta.1...v1.2.25-beta.1) (2020-11-12)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.24](https://github.com/midwayjs/cli/compare/v1.2.23...v1.2.24) (2020-11-12)


### Bug Fixes

* support copy static file ([#20](https://github.com/midwayjs/cli/issues/20)) ([39021c4](https://github.com/midwayjs/cli/commit/39021c4b82f962d970a0fc5b9a96b8ae8d66dba1))





## [1.2.23](https://github.com/midwayjs/cli/compare/v1.2.23-beta.3...v1.2.23) (2020-11-11)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.23-beta.3](https://github.com/midwayjs/cli/compare/v1.2.23-beta.2...v1.2.23-beta.3) (2020-11-10)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.23-beta.2](https://github.com/midwayjs/cli/compare/v1.2.23-beta.1...v1.2.23-beta.2) (2020-10-30)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.23-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.23-beta.1) (2020-10-26)


### Bug Fixes

* fcli create ([#15](https://github.com/midwayjs/cli/issues/15)) ([eb8a673](https://github.com/midwayjs/cli/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))





## [1.2.22](https://github.com/midwayjs/cli/compare/v1.2.22-beta.1...v1.2.22) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.22-beta.1](https://github.com/midwayjs/cli/compare/v1.2.20...v1.2.22-beta.1) (2020-10-21)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.21](https://github.com/midwayjs/cli/compare/serverless-v1.2.19...serverless-v1.2.21) (2020-10-20)



## 1.2.20 (2020-10-19)



## 1.2.20-beta.5 (2020-10-19)



## 1.2.20-beta.4 (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20](https://github.com/midwayjs/cli/compare/v1.2.20-beta.5...v1.2.20) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.20-beta.5](https://github.com/midwayjs/cli/compare/v1.2.20-beta.4...v1.2.20-beta.5) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.20-beta.4](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.4) (2020-10-19)


### Bug Fixes

* starter-in-runtime-extension ([#13](https://github.com/midwayjs/cli/issues/13)) ([8dd40c1](https://github.com/midwayjs/cli/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))
* test deps ([#8](https://github.com/midwayjs/cli/issues/8)) ([60df64a](https://github.com/midwayjs/cli/commit/60df64a54953cf8ddc76596fcbbb5ae5fac976b8))





## [1.2.20-beta.3](https://github.com/midwayjs/cli/compare/v1.2.20-beta.2...v1.2.20-beta.3) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.20-beta.2](https://github.com/midwayjs/cli/compare/v1.2.20-beta.1...v1.2.20-beta.2) (2020-10-19)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.20-beta.1](https://github.com/midwayjs/cli/compare/v1.0.4...v1.2.20-beta.1) (2020-10-19)


### Bug Fixes

* test deps ([#8](https://github.com/midwayjs/cli/issues/8)) ([60df64a](https://github.com/midwayjs/cli/commit/60df64a54953cf8ddc76596fcbbb5ae5fac976b8))





## [1.2.19](https://github.com/midwayjs/cli/compare/serverless-v1.2.18...serverless-v1.2.19) (2020-09-24)


### Bug Fixes

* test deps ([#8](https://github.com/midwayjs/cli/issues/8)) ([60df64a](https://github.com/midwayjs/cli/commit/60df64a54953cf8ddc76596fcbbb5ae5fac976b8))





## [1.2.18](https://github.com/midwayjs/cli/compare/serverless-v1.2.17...serverless-v1.2.18) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.17](https://github.com/midwayjs/cli/compare/serverless-v1.2.16...serverless-v1.2.17) (2020-09-23)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.2.16](https://github.com/midwayjs/cli/compare/serverless-v1.2.15...serverless-v1.2.16) (2020-09-22)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## 1.2.15 (2020-09-22)



## 1.0.4 (2020-09-20)



## 1.0.3 (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))



## 1.0.2 (2020-09-17)


### Bug Fixes

* test support forceExit ([1b19cea](https://github.com/midwayjs/cli/commit/1b19cea5bfa1aac32b4e8b0fb2ce6313f663a7c4))



## 1.0.1 (2020-09-17)


### Bug Fixes

* build tsconfig error ([a364d73](https://github.com/midwayjs/cli/commit/a364d73d4162bbec76512b968b6f370668225154))


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))



# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





## [1.0.4](https://github.com/midwayjs/cli/compare/v1.0.3...v1.0.4) (2020-09-20)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))







**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test





## [1.0.3](https://github.com/midwayjs/cli/compare/v1.0.2...v1.0.3) (2020-09-20)


### Bug Fixes

* dev start output ([e7c330e](https://github.com/midwayjs/cli/commit/e7c330e1f3625acd0583ff08e34c672ab57f6b87))





## [1.0.2](https://github.com/midwayjs/cli/compare/v1.0.1...v1.0.2) (2020-09-17)


### Bug Fixes

* test support forceExit ([1b19cea](https://github.com/midwayjs/cli/commit/1b19cea5bfa1aac32b4e8b0fb2ce6313f663a7c4))





## [1.0.1](https://github.com/midwayjs/cli/compare/v1.0.0...v1.0.1) (2020-09-17)


### Bug Fixes

* build tsconfig error ([a364d73](https://github.com/midwayjs/cli/commit/a364d73d4162bbec76512b968b6f370668225154))


### Features

* publish config ([970d678](https://github.com/midwayjs/cli/commit/970d678989024814b7c4437aad2a8c92d7a8c931))





# 1.0.0 (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))





# [1.0.0](https://github.com/midwayjs/cli/compare/v1.1.0...v1.0.0) (2020-09-17)


### Features

* change name to cli ([d845637](https://github.com/midwayjs/cli/commit/d845637511c606d581f72800e70567a95e93040c))







**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test







**Note:** Version bump only for package @midwayjs/cli-plugin-test





# [1.0.0](https://github.com/midwayjs/bin/compare/v1.1.0...v1.0.0) (2020-09-17)

**Note:** Version bump only for package @midwayjs/cli-plugin-test





# 1.1.0 (2020-09-16)


### Features

* dev ([7e2dd87](https://github.com/midwayjs/bin/commit/7e2dd8773c2bd79de93a4aea7a41c0c74663b6bc))
* new ([7ca760c](https://github.com/midwayjs/bin/commit/7ca760c059715220c738a46a78d09d288a767f6d))
* test ([0854afa](https://github.com/midwayjs/bin/commit/0854afa8638b366a408023a38d0682837e51b9eb))
* test complete ([8aca5e2](https://github.com/midwayjs/bin/commit/8aca5e26d2207e1a095c8c3b14a4a00c1af530c2))
