# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [2.1.0](https://github.com/midwayjs/cli/compare/serverless-v1.2.39...serverless-v2.1.0) (2023-05-30)



## 2.0.14 (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))



## 2.0.10 (2023-01-09)



## 2.0.9 (2022-12-30)


### Bug Fixes

* suppoert fc layers ([#312](https://github.com/midwayjs/cli/issues/312)) ([382ac30](https://github.com/midwayjs/cli/commit/382ac308d253ef0d52b23d7a5ece75fca627b28c))



## 2.0.8 (2022-12-27)


### Bug Fixes

* dev random port ([#311](https://github.com/midwayjs/cli/issues/311)) ([f61c9ef](https://github.com/midwayjs/cli/commit/f61c9efede8055e110dce7a458da1d6c5dd304f8))



## 2.0.6 (2022-12-09)



## 2.0.5 (2022-12-01)



## 2.0.2 (2022-11-17)


### Features

* check midway version ([#303](https://github.com/midwayjs/cli/issues/303)) ([cb6934a](https://github.com/midwayjs/cli/commit/cb6934ae27eac9d346d9e0bf251c2d168e27ed0c))



# 2.0.0 (2022-10-13)



## 1.3.14 (2022-10-10)



## 1.3.13 (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))



## 1.3.13-beta.3 (2022-09-07)



## 1.3.11 (2022-08-04)


### Features

* check configuration export class ([#289](https://github.com/midwayjs/cli/issues/289)) ([cab92b4](https://github.com/midwayjs/cli/commit/cab92b4f9e6e7b2bb6967ea155f68a826388c19b))



## 1.3.9 (2022-07-27)


### Bug Fixes

* wrapper initialize in handler ([#288](https://github.com/midwayjs/cli/issues/288)) ([ba8cda2](https://github.com/midwayjs/cli/commit/ba8cda26c6283215289e2117087c47f82cb6d382))



## 1.3.8 (2022-07-21)


### Features

* new faas & check ([#287](https://github.com/midwayjs/cli/issues/287)) ([2d62fdc](https://github.com/midwayjs/cli/commit/2d62fdc4d91d87ff60c92a0fd9060301e10a3fa5))



## 1.3.6 (2022-06-02)


### Bug Fixes

* aggregationBeforeExecScript ([#283](https://github.com/midwayjs/cli/issues/283)) ([83528f4](https://github.com/midwayjs/cli/commit/83528f4d3b13500879e4b1ad68efa31654fbcb63))



## 1.3.5 (2022-05-25)



## 1.3.5-beta.3 (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))



## 1.3.1 (2022-03-10)



## 1.3.1-beta.1 (2022-03-10)


### Bug Fixes

* emitDecoratorMetadata ([#269](https://github.com/midwayjs/cli/issues/269)) ([b08e6c6](https://github.com/midwayjs/cli/commit/b08e6c6ec581e5dfb1231962384afc035db516d6))



# 1.3.0 (2022-02-23)


### Features

* add aliyun FC layers configuration ([#267](https://github.com/midwayjs/cli/issues/267)) ([4caacd2](https://github.com/midwayjs/cli/commit/4caacd255297f2b2b08a5bc30b94c278d3588cf8))
* zip compression ([#265](https://github.com/midwayjs/cli/issues/265)) ([0f83ac5](https://github.com/midwayjs/cli/commit/0f83ac5fba0a2764dd0454f666d285d2b2c6b084))



## 1.2.99 (2022-02-16)


### Bug Fixes

* fast dev ([#263](https://github.com/midwayjs/cli/issues/263)) ([89c5009](https://github.com/midwayjs/cli/commit/89c5009e1e6356ae9a4c58b39564211882594274))



## 1.2.95 (2022-01-20)



## 1.2.93 (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))



## 1.2.86 (2021-11-04)



## 1.2.85 (2021-10-21)


### Features

* f.yml add AsyncConfiguration ([#213](https://github.com/midwayjs/cli/issues/213)) ([30ae12b](https://github.com/midwayjs/cli/commit/30ae12b754fc384d7a9118a154f045da3f6a09e8))
* more fc config ([#211](https://github.com/midwayjs/cli/issues/211)) ([1f7913d](https://github.com/midwayjs/cli/commit/1f7913df1ba306134fb59e3dcca492f43861ad41))



## 1.2.84 (2021-09-27)


### Bug Fixes

* opti check ([#201](https://github.com/midwayjs/cli/issues/201)) ([bfffdc6](https://github.com/midwayjs/cli/commit/bfffdc60d22d3732591b868bb863918ec086b7fb))



## 1.2.83 (2021-09-13)



## 1.2.82 (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))



## 1.2.80 (2021-08-16)


### Features

* support fc authType、 timeout、 initTimeout ([#178](https://github.com/midwayjs/cli/issues/178)) ([baca74c](https://github.com/midwayjs/cli/commit/baca74c971bd916803594c7900769a291dc5cb6f))



## 1.2.79 (2021-08-11)


### Bug Fixes

* usage support child commands ([#174](https://github.com/midwayjs/cli/issues/174)) ([b50a49d](https://github.com/midwayjs/cli/commit/b50a49d1ffac39dd72cd439bcc8b01aa728836d7))
* vercel entry baseDir ([#173](https://github.com/midwayjs/cli/issues/173)) ([fc11e1b](https://github.com/midwayjs/cli/commit/fc11e1bec2bc0d3c252c58d16e1a4d3e8d25ae6b))



## 1.2.78 (2021-08-09)


### Features

* support vercel ([#165](https://github.com/midwayjs/cli/issues/165)) ([2a82748](https://github.com/midwayjs/cli/commit/2a82748a0b289e823a9ee6708078f9c66b4d769d))



## 1.2.76 (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))



## 1.2.73 (2021-06-25)



## 1.2.68 (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))



## 1.2.67 (2021-05-07)


### Bug Fixes

* deploy filter method ([#101](https://github.com/midwayjs/cli/issues/101)) ([c660a53](https://github.com/midwayjs/cli/commit/c660a535d044105e04e38fa0c134542e826f1560))



## 1.2.65 (2021-04-23)


### Bug Fixes

* more serverless dev options ([#95](https://github.com/midwayjs/cli/issues/95)) ([c3b9a4f](https://github.com/midwayjs/cli/commit/c3b9a4fa7f9f0bf6d19420ff38bf1abb23e74e32))



## 1.2.63 (2021-04-16)



## 1.2.62 (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))



## 1.2.60 (2021-04-08)


### Bug Fixes

* support serverless dev ([#80](https://github.com/midwayjs/cli/issues/80)) ([62268ed](https://github.com/midwayjs/cli/commit/62268edda31881babedb5762fe55ca8d48fd0bab))



## 1.2.56 (2021-03-26)


### Bug Fixes

* v2 template ([#83](https://github.com/midwayjs/cli/issues/83)) ([b63e2b5](https://github.com/midwayjs/cli/commit/b63e2b596291870ef0b9a86f86d22985c55a6eac))



## 1.2.54 (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))



## 1.2.52 (2021-03-17)


### Bug Fixes

* wrapper template path ([#71](https://github.com/midwayjs/cli/issues/71)) ([827f0a3](https://github.com/midwayjs/cli/commit/827f0a33407db592479d07b2f769e124cbe00ae4))



## 1.2.51 (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))



## 1.2.50 (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))



## 1.2.48 (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))



## 1.2.45 (2021-03-04)



## 1.2.41 (2021-02-17)



## 1.2.40 (2021-02-03)



## 1.2.39-beta.5 (2021-02-03)


### Bug Fixes

* dot file ([#48](https://github.com/midwayjs/cli/issues/48)) ([d603284](https://github.com/midwayjs/cli/commit/d60328443e179eca09fb9c410d33affd68950fd9))





## [2.0.14](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14) (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))
* support lock ([#326](https://github.com/midwayjs/cli/issues/326)) ([49dedc6](https://github.com/midwayjs/cli/commit/49dedc64f37cf997ad1212beb2bdfcd16f002ff3))





## [2.0.14-beta.4](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.4) (2023-03-03)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))


### Features

* output level ([#320](https://github.com/midwayjs/cli/issues/320)) ([f83750a](https://github.com/midwayjs/cli/commit/f83750a0a217cf38d86626e130070caa4fac5c79))





## [2.0.14-beta.3](https://github.com/midwayjs/cli/compare/v2.0.14-beta.2...v2.0.14-beta.3) (2023-02-15)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [2.0.14-beta.2](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.2) (2023-02-14)


### Bug Fixes

* concurrency ([#324](https://github.com/midwayjs/cli/issues/324)) ([47e3986](https://github.com/midwayjs/cli/commit/47e39865bdf6a426bd5bf163f7ebdb3ec188a67c))





## [2.0.14-beta.1](https://github.com/midwayjs/cli/compare/v2.0.13...v2.0.14-beta.1) (2023-02-07)


### Bug Fixes

* concurrency ([38046b6](https://github.com/midwayjs/cli/commit/38046b65387bd08f27365d3f269e0101d8acd724))





## [2.0.10](https://github.com/midwayjs/cli/compare/v2.0.9...v2.0.10) (2023-01-09)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [2.0.10-beta.2](https://github.com/midwayjs/cli/compare/v2.0.10-beta.1...v2.0.10-beta.2) (2023-01-06)


### Bug Fixes

* v3 entry support preload module ([d2ecac4](https://github.com/midwayjs/cli/commit/d2ecac491b445343f8f49e932ff78144b4460b47))





## [2.0.9](https://github.com/midwayjs/cli/compare/v2.0.8...v2.0.9) (2022-12-30)


### Bug Fixes

* suppoert fc layers ([#312](https://github.com/midwayjs/cli/issues/312)) ([382ac30](https://github.com/midwayjs/cli/commit/382ac308d253ef0d52b23d7a5ece75fca627b28c))





## [2.0.9-beta.1](https://github.com/midwayjs/cli/compare/v2.0.8...v2.0.9-beta.1) (2022-12-29)


### Bug Fixes

* suppoert fc layers ([44bd845](https://github.com/midwayjs/cli/commit/44bd8459703371980fcfd64c1c325d46629e8e97))





## [2.0.8](https://github.com/midwayjs/cli/compare/v2.0.7...v2.0.8) (2022-12-27)


### Bug Fixes

* dev random port ([#311](https://github.com/midwayjs/cli/issues/311)) ([f61c9ef](https://github.com/midwayjs/cli/commit/f61c9efede8055e110dce7a458da1d6c5dd304f8))





## [2.0.8-beta.1](https://github.com/midwayjs/cli/compare/v2.0.7...v2.0.8-beta.1) (2022-12-26)


### Bug Fixes

* dev random port ([7571f17](https://github.com/midwayjs/cli/commit/7571f17e47b7548a7b8c582ee6fcced62f03230d))





## [2.0.6](https://github.com/midwayjs/cli/compare/v2.0.6-beta.3...v2.0.6) (2022-12-09)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder







**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [2.0.5](https://github.com/midwayjs/cli/compare/v2.0.5-beta.1...v2.0.5) (2022-12-01)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [2.0.2](https://github.com/midwayjs/cli/compare/v2.0.1...v2.0.2) (2022-11-17)


### Features

* check midway version ([#303](https://github.com/midwayjs/cli/issues/303)) ([cb6934a](https://github.com/midwayjs/cli/commit/cb6934ae27eac9d346d9e0bf251c2d168e27ed0c))





# [2.0.0](https://github.com/midwayjs/cli/compare/v1.3.15...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





# [2.0.0](https://github.com/midwayjs/cli/compare/v2.0.0-beta.1...v2.0.0) (2022-10-13)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14) (2022-10-10)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.10](https://github.com/midwayjs/cli/compare/v1.3.14-beta.9...v1.3.14-beta.10) (2022-09-30)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.9](https://github.com/midwayjs/cli/compare/v1.3.14-beta.8...v1.3.14-beta.9) (2022-09-27)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.8](https://github.com/midwayjs/cli/compare/v1.3.14-beta.7...v1.3.14-beta.8) (2022-09-27)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.7](https://github.com/midwayjs/cli/compare/v1.3.14-beta.6...v1.3.14-beta.7) (2022-09-27)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.6](https://github.com/midwayjs/cli/compare/v1.3.14-beta.5...v1.3.14-beta.6) (2022-09-26)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.5](https://github.com/midwayjs/cli/compare/v1.3.14-beta.4...v1.3.14-beta.5) (2022-09-26)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.14-beta.4](https://github.com/midwayjs/cli/compare/v1.3.14-beta.3...v1.3.14-beta.4) (2022-09-22)


### Bug Fixes

* isDefaultFunc wrapper ([e151543](https://github.com/midwayjs/cli/commit/e151543c55c1590c00988658de321fdb6cb49752))





## [1.3.14-beta.3](https://github.com/midwayjs/cli/compare/v1.3.14-beta.2...v1.3.14-beta.3) (2022-09-20)


### Bug Fixes

* defaultFunctionHandlerName ([be785cb](https://github.com/midwayjs/cli/commit/be785cbbe477e33f7bf6c283916a37d85d62e8b5))
* resume defaultFunctionHandlerName support ([30b5eb5](https://github.com/midwayjs/cli/commit/30b5eb5465fea4e5632cff0ac253d22e3bf094c7))





## [1.3.14-beta.2](https://github.com/midwayjs/cli/compare/v1.3.14-beta.1...v1.3.14-beta.2) (2022-09-07)


### Bug Fixes

* bundle ncc & hcc ([50954a0](https://github.com/midwayjs/cli/commit/50954a0cd3e5ae8dbc1ec19715102e5672029adc))





## [1.3.14-beta.1](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.14-beta.1) (2022-09-07)


### Bug Fixes

* bundle ([bf7da31](https://github.com/midwayjs/cli/commit/bf7da31e024bb2bc61a0265cf8e741272b80828c))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13) (2022-09-07)


### Bug Fixes

* package resume to  webRouterCollector ([#296](https://github.com/midwayjs/cli/issues/296)) ([8588bbd](https://github.com/midwayjs/cli/commit/8588bbd87968a1497b0076dedbde5ed18e82b0be))





## [1.3.13-beta.4](https://github.com/midwayjs/cli/compare/v1.3.13-beta.3...v1.3.13-beta.4) (2022-09-07)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.13-beta.3](https://github.com/midwayjs/cli/compare/v1.3.13...v1.3.13-beta.3) (2022-09-07)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.11](https://github.com/midwayjs/cli/compare/v1.3.9...v1.3.11) (2022-08-04)


### Features

* check configuration export class ([#289](https://github.com/midwayjs/cli/issues/289)) ([cab92b4](https://github.com/midwayjs/cli/commit/cab92b4f9e6e7b2bb6967ea155f68a826388c19b))





## [1.3.11-beta.1](https://github.com/midwayjs/cli/compare/v1.3.9...v1.3.11-beta.1) (2022-08-04)


### Features

* check configuration export class ([#289](https://github.com/midwayjs/cli/issues/289)) ([cab92b4](https://github.com/midwayjs/cli/commit/cab92b4f9e6e7b2bb6967ea155f68a826388c19b))





## [1.3.9](https://github.com/midwayjs/cli/compare/v1.3.8...v1.3.9) (2022-07-27)


### Bug Fixes

* wrapper initialize in handler ([#288](https://github.com/midwayjs/cli/issues/288)) ([ba8cda2](https://github.com/midwayjs/cli/commit/ba8cda26c6283215289e2117087c47f82cb6d382))





## [1.3.9-beta.2](https://github.com/midwayjs/cli/compare/v1.3.9-beta.1...v1.3.9-beta.2) (2022-07-26)


### Bug Fixes

* uptime ([c01abc7](https://github.com/midwayjs/cli/commit/c01abc77bf482bd9a236103d03cc24ddd2ea0969))





## [1.3.9-beta.1](https://github.com/midwayjs/cli/compare/v1.3.8...v1.3.9-beta.1) (2022-07-26)


### Bug Fixes

* init status ([69ff47c](https://github.com/midwayjs/cli/commit/69ff47cfaecf4cb527943b089449c5d039405669))
* initializationError ([e42a47f](https://github.com/midwayjs/cli/commit/e42a47f070afc67c9e75636966b3a2c18ff0c316))
* initialized ([0077b48](https://github.com/midwayjs/cli/commit/0077b4818949e5aa224001a96c95b19c32b91698))
* invoke ([47b596a](https://github.com/midwayjs/cli/commit/47b596a7a4de2a78878da9ca7854c877fbea9105))
* resume v1 ([1066642](https://github.com/midwayjs/cli/commit/10666428a62598f2172d79c503c3ed2aab5406af))
* word spelling ([510857f](https://github.com/midwayjs/cli/commit/510857f0cc237ebde172efe118b7dfc0d86e6e20))
* wrapper error ([946f98b](https://github.com/midwayjs/cli/commit/946f98b186802afe4028edd0202870252459dc1c))
* wrapper initialize in handler ([81d4918](https://github.com/midwayjs/cli/commit/81d49187ae37b653d4d5d5929dc38ca43fdbbb21))





## [1.3.8](https://github.com/midwayjs/cli/compare/v1.3.7...v1.3.8) (2022-07-21)


### Features

* new faas & check ([#287](https://github.com/midwayjs/cli/issues/287)) ([2d62fdc](https://github.com/midwayjs/cli/commit/2d62fdc4d91d87ff60c92a0fd9060301e10a3fa5))





## [1.3.8-beta.4](https://github.com/midwayjs/cli/compare/v1.3.8-beta.3...v1.3.8-beta.4) (2022-07-16)


### Bug Fixes

* aggregation handler name ([7ed1d48](https://github.com/midwayjs/cli/commit/7ed1d483b86cf24c619026ffa9d1e02f861cea39))





## [1.3.8-beta.3](https://github.com/midwayjs/cli/compare/v1.3.8-beta.2...v1.3.8-beta.3) (2022-07-16)


### Bug Fixes

* aggregation wrapper ([74b4560](https://github.com/midwayjs/cli/commit/74b4560f8d1903d2c33e50e28dd16e45219c4617))





## [1.3.8-beta.2](https://github.com/midwayjs/cli/compare/v1.3.8-beta.1...v1.3.8-beta.2) (2022-07-12)


### Bug Fixes

* v3 starter ([1af272c](https://github.com/midwayjs/cli/commit/1af272cb9efaf32681604ceaa112ce746c8ca0cb))





## [1.3.8-beta.1](https://github.com/midwayjs/cli/compare/v1.3.7...v1.3.8-beta.1) (2022-07-12)


### Features

* new faas & check ([9eb6133](https://github.com/midwayjs/cli/commit/9eb61337ca000b201b70c16f85ab6e9f1f74db4a))





## [1.3.6](https://github.com/midwayjs/cli/compare/v1.3.5...v1.3.6) (2022-06-02)


### Bug Fixes

* aggregationBeforeExecScript ([#283](https://github.com/midwayjs/cli/issues/283)) ([83528f4](https://github.com/midwayjs/cli/commit/83528f4d3b13500879e4b1ad68efa31654fbcb63))





## [1.3.6-beta.1](https://github.com/midwayjs/cli/compare/v1.3.5...v1.3.6-beta.1) (2022-06-01)


### Bug Fixes

* aggregationBeforeExecScript ([3e336df](https://github.com/midwayjs/cli/commit/3e336dfe6261c9f39c37268315080b99a4261752))





## [1.3.5](https://github.com/midwayjs/cli/compare/v1.3.5-beta.3...v1.3.5) (2022-05-25)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.5-beta.3](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.3) (2022-05-25)


### Features

* support event trigger aggregation ([#281](https://github.com/midwayjs/cli/issues/281)) ([84b4789](https://github.com/midwayjs/cli/commit/84b478901ed1b49d6fb558be2f1ca0267027d2da))





## [1.3.5-beta.2](https://github.com/midwayjs/cli/compare/v1.3.5-beta.1...v1.3.5-beta.2) (2022-05-24)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.5-beta.1](https://github.com/midwayjs/cli/compare/v1.3.4...v1.3.5-beta.1) (2022-05-24)


### Features

* support event trigger aggregation ([80a91a3](https://github.com/midwayjs/cli/commit/80a91a394bb30c883f448be24b35dbec03ed76d4))





## [1.3.1](https://github.com/midwayjs/cli/compare/v1.3.1-beta.1...v1.3.1) (2022-03-10)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.3.1-beta.1](https://github.com/midwayjs/cli/compare/v1.3.0...v1.3.1-beta.1) (2022-03-10)


### Bug Fixes

* emitDecoratorMetadata ([#269](https://github.com/midwayjs/cli/issues/269)) ([b08e6c6](https://github.com/midwayjs/cli/commit/b08e6c6ec581e5dfb1231962384afc035db516d6))





# [1.3.0](https://github.com/midwayjs/cli/compare/v1.2.99...v1.3.0) (2022-02-23)


### Features

* add aliyun FC layers configuration ([#267](https://github.com/midwayjs/cli/issues/267)) ([4caacd2](https://github.com/midwayjs/cli/commit/4caacd255297f2b2b08a5bc30b94c278d3588cf8))
* zip compression ([#265](https://github.com/midwayjs/cli/issues/265)) ([0f83ac5](https://github.com/midwayjs/cli/commit/0f83ac5fba0a2764dd0454f666d285d2b2c6b084))





# [1.3.0-beta.1](https://github.com/midwayjs/cli/compare/v1.2.99...v1.3.0-beta.1) (2022-02-21)


### Bug Fixes

* fc deploy domain ([735f183](https://github.com/midwayjs/cli/commit/735f18361e9d029ae688402c64059e578183b35b))





## [1.2.99](https://github.com/midwayjs/cli/compare/v1.2.98...v1.2.99) (2022-02-16)


### Bug Fixes

* fast dev ([#263](https://github.com/midwayjs/cli/issues/263)) ([89c5009](https://github.com/midwayjs/cli/commit/89c5009e1e6356ae9a4c58b39564211882594274))





## [1.2.99-beta.2](https://github.com/midwayjs/cli/compare/v1.2.99-beta.1...v1.2.99-beta.2) (2022-02-16)


### Bug Fixes

* aliyun fc default runtime version upgrade to nodejs.14 ([e8d226b](https://github.com/midwayjs/cli/commit/e8d226b1b3a6be2ec2a8cb5164cd56a45642d204))
* faas initializeContext ([aa6c480](https://github.com/midwayjs/cli/commit/aa6c4806ad1dd81983edeef66acf95923e7459d7))
* serverlessdevs spec transform ([defb788](https://github.com/midwayjs/cli/commit/defb7888ac42d1945142163d46d5772de01456e1))
* v3 global config default ([d22ffb7](https://github.com/midwayjs/cli/commit/d22ffb7c316eb09ace242e131d7ac70047da90e4))





## [1.2.95](https://github.com/midwayjs/cli/compare/v1.2.94...v1.2.95) (2022-01-20)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.93](https://github.com/midwayjs/cli/compare/v1.2.92...v1.2.93) (2021-12-29)


### Features

* auto hooks import ([#239](https://github.com/midwayjs/cli/issues/239)) ([a4557f9](https://github.com/midwayjs/cli/commit/a4557f9e1b9f54e42af08ba503a47f7d11a3d59d))





## [1.2.86](https://github.com/midwayjs/cli/compare/v1.2.85...v1.2.86) (2021-11-04)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.85](https://github.com/midwayjs/cli/compare/v1.2.84...v1.2.85) (2021-10-21)


### Features

* f.yml add AsyncConfiguration ([#213](https://github.com/midwayjs/cli/issues/213)) ([30ae12b](https://github.com/midwayjs/cli/commit/30ae12b754fc384d7a9118a154f045da3f6a09e8))
* more fc config ([#211](https://github.com/midwayjs/cli/issues/211)) ([1f7913d](https://github.com/midwayjs/cli/commit/1f7913df1ba306134fb59e3dcca492f43861ad41))





## [1.2.84](https://github.com/midwayjs/cli/compare/v1.2.83...v1.2.84) (2021-09-27)


### Bug Fixes

* opti check ([#201](https://github.com/midwayjs/cli/issues/201)) ([bfffdc6](https://github.com/midwayjs/cli/commit/bfffdc60d22d3732591b868bb863918ec086b7fb))





## [1.2.83](https://github.com/midwayjs/cli/compare/v1.2.82...v1.2.83) (2021-09-13)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.82](https://github.com/midwayjs/cli/compare/v1.2.81...v1.2.82) (2021-08-31)


### Features

* use file dector ([#168](https://github.com/midwayjs/cli/issues/168)) ([4b1cfdb](https://github.com/midwayjs/cli/commit/4b1cfdb1418a3883570035fea815c69c2e331a3d))





## [1.2.80](https://github.com/midwayjs/cli/compare/v1.2.79...v1.2.80) (2021-08-16)


### Features

* support fc authType、 timeout、 initTimeout ([#178](https://github.com/midwayjs/cli/issues/178)) ([baca74c](https://github.com/midwayjs/cli/commit/baca74c971bd916803594c7900769a291dc5cb6f))





## [1.2.79](https://github.com/midwayjs/cli/compare/v1.2.78...v1.2.79) (2021-08-11)


### Bug Fixes

* usage support child commands ([#174](https://github.com/midwayjs/cli/issues/174)) ([b50a49d](https://github.com/midwayjs/cli/commit/b50a49d1ffac39dd72cd439bcc8b01aa728836d7))
* vercel entry baseDir ([#173](https://github.com/midwayjs/cli/issues/173)) ([fc11e1b](https://github.com/midwayjs/cli/commit/fc11e1bec2bc0d3c252c58d16e1a4d3e8d25ae6b))





## [1.2.78](https://github.com/midwayjs/cli/compare/v1.2.77...v1.2.78) (2021-08-09)


### Features

* support vercel ([#165](https://github.com/midwayjs/cli/issues/165)) ([2a82748](https://github.com/midwayjs/cli/commit/2a82748a0b289e823a9ee6708078f9c66b4d769d))





## [1.2.76](https://github.com/midwayjs/cli/compare/v1.2.75...v1.2.76) (2021-07-22)


### Bug Fixes

* dev restart not change([#156](https://github.com/midwayjs/cli/issues/156)) ([505813e](https://github.com/midwayjs/cli/commit/505813eb1434292d1fcc799d582344ff5fde7fc9))


### Features

* mocha test support ([#155](https://github.com/midwayjs/cli/issues/155)) ([d363607](https://github.com/midwayjs/cli/commit/d3636076ee0391a5ddd45c3864a13010b8f01e78))





## [1.2.73](https://github.com/midwayjs/cli/compare/v1.2.73-beta.1...v1.2.73) (2021-06-25)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.68](https://github.com/midwayjs/cli/compare/v1.2.67...v1.2.68) (2021-05-13)


### Features

* upgrade mwcc 0.7.0 ([3cd2902](https://github.com/midwayjs/cli/commit/3cd2902821167905d789df5a153bed2224920613))





## [1.2.67](https://github.com/midwayjs/cli/compare/v1.2.66...v1.2.67) (2021-05-07)


### Bug Fixes

* deploy filter method ([#101](https://github.com/midwayjs/cli/issues/101)) ([c660a53](https://github.com/midwayjs/cli/commit/c660a535d044105e04e38fa0c134542e826f1560))





## [1.2.65](https://github.com/midwayjs/cli/compare/v1.2.63...v1.2.65) (2021-04-23)


### Bug Fixes

* more serverless dev options ([#95](https://github.com/midwayjs/cli/issues/95)) ([c3b9a4f](https://github.com/midwayjs/cli/commit/c3b9a4fa7f9f0bf6d19420ff38bf1abb23e74e32))





## [1.2.63](https://github.com/midwayjs/cli/compare/v1.2.62...v1.2.63) (2021-04-16)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.62](https://github.com/midwayjs/cli/compare/v1.2.61...v1.2.62) (2021-04-14)


### Bug Fixes

* dev not close ([#93](https://github.com/midwayjs/cli/issues/93)) ([f6f330c](https://github.com/midwayjs/cli/commit/f6f330c2e568dfbe112c03740218a55f715c4fb9))





## [1.2.60](https://github.com/midwayjs/cli/compare/v1.2.59...v1.2.60) (2021-04-08)


### Bug Fixes

* support serverless dev ([#80](https://github.com/midwayjs/cli/issues/80)) ([62268ed](https://github.com/midwayjs/cli/commit/62268edda31881babedb5762fe55ca8d48fd0bab))





## [1.2.56](https://github.com/midwayjs/cli/compare/v1.2.55...v1.2.56) (2021-03-26)


### Bug Fixes

* v2 template ([#83](https://github.com/midwayjs/cli/issues/83)) ([b63e2b5](https://github.com/midwayjs/cli/commit/b63e2b596291870ef0b9a86f86d22985c55a6eac))





## [1.2.54](https://github.com/midwayjs/cli/compare/v1.2.53...v1.2.54) (2021-03-22)


### Bug Fixes

* http url match support params ([#74](https://github.com/midwayjs/cli/issues/74)) ([0d366e0](https://github.com/midwayjs/cli/commit/0d366e047de64ad578e48d717bc488333c0f044f))





## [1.2.52](https://github.com/midwayjs/cli/compare/v1.2.51...v1.2.52) (2021-03-17)


### Bug Fixes

* wrapper template path ([#71](https://github.com/midwayjs/cli/issues/71)) ([827f0a3](https://github.com/midwayjs/cli/commit/827f0a33407db592479d07b2f769e124cbe00ae4))





## [1.2.51](https://github.com/midwayjs/cli/compare/v1.2.50...v1.2.51) (2021-03-12)


### Bug Fixes

* dev pack transform & layers support ([#59](https://github.com/midwayjs/cli/issues/59)) ([6fc1ffc](https://github.com/midwayjs/cli/commit/6fc1ffc2effa76604c65a5d4f57f0efa60e29133))





## [1.2.50](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.50) (2021-03-09)


### Bug Fixes

* check update view ([#57](https://github.com/midwayjs/cli/issues/57)) ([415ed17](https://github.com/midwayjs/cli/commit/415ed17451475a59ffbce1a19ffb807c9812efd8))
* support path param ([#58](https://github.com/midwayjs/cli/issues/58)) ([eb5e680](https://github.com/midwayjs/cli/commit/eb5e680240a9cd47760d0e1640755e5d8e189033))







**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.49](https://github.com/midwayjs/cli/compare/v1.2.48...v1.2.49) (2021-03-06)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder







**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.48](https://github.com/midwayjs/cli/compare/v1.2.46...v1.2.48) (2021-03-06)


### Bug Fixes

* new list ([#56](https://github.com/midwayjs/cli/issues/56)) ([22019c0](https://github.com/midwayjs/cli/commit/22019c047dc716ad263fb74085523c65a35500b2))





## [1.2.45](https://github.com/midwayjs/cli/compare/v1.2.44...v1.2.45) (2021-03-04)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.41](https://github.com/midwayjs/cli/compare/v1.2.40...v1.2.41) (2021-02-17)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.40](https://github.com/midwayjs/cli/compare/v1.2.39-beta.5...v1.2.40) (2021-02-03)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.37](https://github.com/midwayjs/cli/compare/v1.2.35...v1.2.37) (2021-01-08)


### Bug Fixes

* support package diagnostics & tsConfig config ([#38](https://github.com/midwayjs/cli/issues/38)) ([c499d14](https://github.com/midwayjs/cli/commit/c499d145f9cabf427877ec8ea65aea8ead42b9cd))





## [1.2.35](https://github.com/midwayjs/cli/compare/v1.2.33...v1.2.35) (2020-12-24)


### Bug Fixes

* copy file error catch ([8d2097c](https://github.com/midwayjs/cli/commit/8d2097c538f22ed6050c85d1c250436e0c2c71c1))





## [1.2.34](https://github.com/midwayjs/cli/compare/v1.2.34-beta.2...v1.2.34) (2020-12-20)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.33](https://github.com/midwayjs/cli/compare/v1.2.32...v1.2.33) (2020-12-17)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.32](https://github.com/midwayjs/cli/compare/v1.2.32-beta...v1.2.32) (2020-12-08)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.30](https://github.com/midwayjs/cli/compare/v1.2.30-beta...v1.2.30) (2020-11-30)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.29](https://github.com/midwayjs/cli/compare/serverless-v1.2.28...serverless-v1.2.29) (2020-11-18)


### Bug Fixes

* multi function invoke same time ([#26](https://github.com/midwayjs/cli/issues/26)) ([818ae86](https://github.com/midwayjs/cli/commit/818ae861d5e009396a60bee0d8bd0ef71093daa2))





## [1.2.28](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.27...serverless-v1.2.28) (2020-11-18)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.27](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.26...serverless-v1.2.27) (2020-11-17)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.25](https://github.com/midwayjs/midway-faas/compare/v1.2.25-beta.1...v1.2.25) (2020-11-12)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.25-beta.1](https://github.com/midwayjs/midway-faas/compare/v1.2.24-beta.1...v1.2.25-beta.1) (2020-11-12)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.24-beta.1](https://github.com/midwayjs/midway-faas/compare/v1.2.24...v1.2.24-beta.1) (2020-11-12)


### Bug Fixes

* midway2 bootstrap ([#22](https://github.com/midwayjs/midway-faas/issues/22)) ([5673fc6](https://github.com/midwayjs/midway-faas/commit/5673fc6de1f384f88bd2376e51358d1570f06d43))





## [1.2.23](https://github.com/midwayjs/midway-faas/compare/v1.2.23-beta.3...v1.2.23) (2020-11-11)


### Bug Fixes

* egg migrate ([#21](https://github.com/midwayjs/midway-faas/issues/21)) ([9f94dea](https://github.com/midwayjs/midway-faas/commit/9f94deac3e1b89dc49a46f101e85d5219a19c384))





## [1.2.23-beta.3](https://github.com/midwayjs/midway-faas/compare/v1.2.23-beta.2...v1.2.23-beta.3) (2020-11-10)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.23-beta.2](https://github.com/midwayjs/midway-faas/compare/v1.2.23-beta.1...v1.2.23-beta.2) (2020-10-30)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.23-beta.1](https://github.com/midwayjs/midway-faas/compare/v1.2.20...v1.2.23-beta.1) (2020-10-26)


### Bug Fixes

* entry afterRuntimeStart to  beforeFunctionStart ([#14](https://github.com/midwayjs/midway-faas/issues/14)) ([fd2c048](https://github.com/midwayjs/midway-faas/commit/fd2c0487c70ba2f84ef2e63405b29d2cd149ccca))
* fcli create ([#15](https://github.com/midwayjs/midway-faas/issues/15)) ([eb8a673](https://github.com/midwayjs/midway-faas/commit/eb8a67315cfecc8131d9947bf0e79fa71ec57e46))


### Features

* support hooks middleware ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([2fe708e](https://github.com/midwayjs/midway-faas/commit/2fe708ec66f4b999286295fbe6c0aa54749eac0a))





## [1.2.22](https://github.com/midwayjs/midway-faas/compare/v1.2.22-beta.1...v1.2.22) (2020-10-21)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.22-beta.1](https://github.com/midwayjs/midway-faas/compare/v1.2.20...v1.2.22-beta.1) (2020-10-21)


### Bug Fixes

* entry afterRuntimeStart to  beforeFunctionStart ([#14](https://github.com/midwayjs/midway-faas/issues/14)) ([fd2c048](https://github.com/midwayjs/midway-faas/commit/fd2c0487c70ba2f84ef2e63405b29d2cd149ccca))





## [1.2.21](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.19...serverless-v1.2.21) (2020-10-20)


### Bug Fixes

* entry afterRuntimeStart to  beforeFunctionStart ([#14](https://github.com/midwayjs/midway-faas/issues/14)) ([fd2c048](https://github.com/midwayjs/midway-faas/commit/fd2c0487c70ba2f84ef2e63405b29d2cd149ccca))



## 1.2.20 (2020-10-19)



## 1.2.20-beta.5 (2020-10-19)



## 1.2.20-beta.4 (2020-10-19)


### Bug Fixes

* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/midway-faas/issues/12)) ([543ca3c](https://github.com/midwayjs/midway-faas/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))
* starter-in-runtime-extension ([#13](https://github.com/midwayjs/midway-faas/issues/13)) ([8dd40c1](https://github.com/midwayjs/midway-faas/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20](https://github.com/midwayjs/midway-faas/compare/v1.2.20-beta.5...v1.2.20) (2020-10-19)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.20-beta.5](https://github.com/midwayjs/midway-faas/compare/v1.2.20-beta.4...v1.2.20-beta.5) (2020-10-19)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.20-beta.4](https://github.com/midwayjs/midway-faas/compare/v1.0.4...v1.2.20-beta.4) (2020-10-19)


### Bug Fixes

* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/midway-faas/issues/12)) ([543ca3c](https://github.com/midwayjs/midway-faas/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))
* starter-in-runtime-extension ([#13](https://github.com/midwayjs/midway-faas/issues/13)) ([8dd40c1](https://github.com/midwayjs/midway-faas/commit/8dd40c1ba4f1bbefe16863c7057c8ccfc8436b56))





## [1.2.20-beta.3](https://github.com/midwayjs/midway-faas/compare/v1.2.20-beta.2...v1.2.20-beta.3) (2020-10-19)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.20-beta.2](https://github.com/midwayjs/midway-faas/compare/v1.2.20-beta.1...v1.2.20-beta.2) (2020-10-19)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.20-beta.1](https://github.com/midwayjs/midway-faas/compare/v1.0.4...v1.2.20-beta.1) (2020-10-19)


### Bug Fixes

* remove core global deps & faas deps command check ([#12](https://github.com/midwayjs/midway-faas/issues/12)) ([543ca3c](https://github.com/midwayjs/midway-faas/commit/543ca3cc7097967d858381a928ee7dce5f3b129a))
* starter-in-runtime-extension ([3e77f6a](https://github.com/midwayjs/midway-faas/commit/3e77f6a9e4e9e2a793b23cfc786fef313401071e))





## [1.2.17](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.16...serverless-v1.2.17) (2020-09-23)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.16](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.15...serverless-v1.2.16) (2020-09-22)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## 1.2.15 (2020-09-22)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.2.12](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.11...serverless-v1.2.12) (2020-09-04)


### Bug Fixes

* 支持字符串 args 传参 ([#628](https://github.com/midwayjs/midway-faas/issues/628)) ([0f8f8ce](https://github.com/midwayjs/midway-faas/commit/0f8f8ced40b4b38c517346c5682a20b6da990f64))





## [1.2.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.2.9...serverless-v1.2.10) (2020-08-30)


### Bug Fixes

* add lock init for egg app ([#622](https://github.com/midwayjs/midway-faas/issues/622)) ([ccb5fe5](https://github.com/midwayjs/midway-faas/commit/ccb5fe52778f19b3e66dc1d727cc38e09f2c3ed6))





# [1.2.0](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.21...serverless-v1.2.0) (2020-08-17)


### Features

* add hooks ([#601](https://github.com/midwayjs/midway-faas/issues/601)) ([e9973f1](https://github.com/midwayjs/midway-faas/commit/e9973f110e3654d619ff7bc4020608c2082e47ae))





## [1.1.21](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.20...serverless-v1.1.21) (2020-08-12)


### Bug Fixes

* xss bug ([#598](https://github.com/midwayjs/midway-faas/issues/598)) ([0a265f3](https://github.com/midwayjs/midway-faas/commit/0a265f3bb2086f2ee805a174794e82bc68bb2765))





## [1.1.18](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.17...serverless-v1.1.18) (2020-08-11)


### Bug Fixes

* fix windows word when output ([#591](https://github.com/midwayjs/midway-faas/issues/591)) ([1ab9a7f](https://github.com/midwayjs/midway-faas/commit/1ab9a7f009d5b80db077e500ebc3302fa92c2664))
* isAppMode ([#590](https://github.com/midwayjs/midway-faas/issues/590)) ([016e430](https://github.com/midwayjs/midway-faas/commit/016e43068e109dac49c8c0a1b6465ffec564ae0e))





## [1.1.16](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.15...serverless-v1.1.16) (2020-08-05)


### Bug Fixes

* remove layer console ([#586](https://github.com/midwayjs/midway-faas/issues/586)) ([51b144b](https://github.com/midwayjs/midway-faas/commit/51b144b8d18b15cc31b31d8a2eacf7b16c12cdd1))





## [1.1.15](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.14...serverless-v1.1.15) (2020-08-05)


### Bug Fixes

* layer name ([#585](https://github.com/midwayjs/midway-faas/issues/585)) ([9a774b7](https://github.com/midwayjs/midway-faas/commit/9a774b7e6bb5652a595954c63518ef4747d85259))





## [1.1.14](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.13...serverless-v1.1.14) (2020-08-04)


### Bug Fixes

* add default path for fc ([#584](https://github.com/midwayjs/midway-faas/issues/584)) ([064d3f0](https://github.com/midwayjs/midway-faas/commit/064d3f08fa7e74420bdcac111f3fda3cf3ef6ec1))
* layer wrapper ([#583](https://github.com/midwayjs/midway-faas/issues/583)) ([1c9966d](https://github.com/midwayjs/midway-faas/commit/1c9966d6d05108451745183efbc4a15ae1caae6b))





## [1.1.13](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.12...serverless-v1.1.13) (2020-08-03)


### Bug Fixes

* args path ([#578](https://github.com/midwayjs/midway-faas/issues/578)) ([f0de617](https://github.com/midwayjs/midway-faas/commit/f0de617087419ecb433d430b6cb3b1d9e3028d47))
* fc auto domain ([#580](https://github.com/midwayjs/midway-faas/issues/580)) ([f8bf9e0](https://github.com/midwayjs/midway-faas/commit/f8bf9e05922b154285ee29f1a65b59d9d448cf5b))





## [1.1.12](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.11...serverless-v1.1.12) (2020-07-29)


### Bug Fixes

* support default fp ([#575](https://github.com/midwayjs/midway-faas/issues/575)) ([6c3d59c](https://github.com/midwayjs/midway-faas/commit/6c3d59c1737db901baadb386b75d49d0a711991f))





## [1.1.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.9...serverless-v1.1.10) (2020-07-28)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [1.1.9](https://github.com/midwayjs/midway-faas/compare/v1.1.4...v1.1.9) (2020-07-28)


### Bug Fixes

* 参数获取不正确 & ctx 符合格式 ([#556](https://github.com/midwayjs/midway-faas/issues/556)) ([106bfac](https://github.com/midwayjs/midway-faas/commit/106bfacff0f762fe561e026f883158f0bd7c3cdc))
* aggregation path ([#567](https://github.com/midwayjs/midway-faas/issues/567)) ([11a6d8a](https://github.com/midwayjs/midway-faas/commit/11a6d8aab2e18a70ec1195090df04beff8e08760))
* faas middle http ([#570](https://github.com/midwayjs/midway-faas/issues/570)) ([27de83b](https://github.com/midwayjs/midway-faas/commit/27de83b7f0e726e99f8edd153ae585a38bd5f517))
* fp args ([#561](https://github.com/midwayjs/midway-faas/issues/561)) ([e91983e](https://github.com/midwayjs/midway-faas/commit/e91983e6ee1d9e725eaed061b80cde083bdf7e1d))
* register faas ctx ([#559](https://github.com/midwayjs/midway-faas/issues/559)) ([d9e1764](https://github.com/midwayjs/midway-faas/commit/d9e1764d55c053e74f9cad2b70641ba7b8ab70c3))
* use ts analyze cache ([#560](https://github.com/midwayjs/midway-faas/issues/560)) ([06dbcb5](https://github.com/midwayjs/midway-faas/commit/06dbcb52210f6a43ed8d54d5288b09c1243d0665))
* wrapper compare ([#553](https://github.com/midwayjs/midway-faas/issues/553)) ([426aa65](https://github.com/midwayjs/midway-faas/commit/426aa65f2eb0d9f4312f5375ea39f9186f2a083c))


### Features

* add  name for fc event ([#569](https://github.com/midwayjs/midway-faas/issues/569)) ([41797e7](https://github.com/midwayjs/midway-faas/commit/41797e77da4b5217c6367ca616f7cc9c874aea5b))





## [1.1.8](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.7...serverless-v1.1.8) (2020-07-27)


### Bug Fixes

* fp args ([#561](https://github.com/midwayjs/midway-faas/issues/561)) ([e91983e](https://github.com/midwayjs/midway-faas/commit/e91983e6ee1d9e725eaed061b80cde083bdf7e1d))





## [1.1.7](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.6...serverless-v1.1.7) (2020-07-26)


### Bug Fixes

* use ts analyze cache ([#560](https://github.com/midwayjs/midway-faas/issues/560)) ([06dbcb5](https://github.com/midwayjs/midway-faas/commit/06dbcb52210f6a43ed8d54d5288b09c1243d0665))





## [1.1.6](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.5...serverless-v1.1.6) (2020-07-26)


### Bug Fixes

* 参数获取不正确 & ctx 符合格式 ([#556](https://github.com/midwayjs/midway-faas/issues/556)) ([106bfac](https://github.com/midwayjs/midway-faas/commit/106bfacff0f762fe561e026f883158f0bd7c3cdc))
* register faas ctx ([#559](https://github.com/midwayjs/midway-faas/issues/559)) ([d9e1764](https://github.com/midwayjs/midway-faas/commit/d9e1764d55c053e74f9cad2b70641ba7b8ab70c3))





## [1.1.5](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.3...serverless-v1.1.5) (2020-07-24)


### Bug Fixes

* wrapper compare ([#553](https://github.com/midwayjs/midway-faas/issues/553)) ([426aa65](https://github.com/midwayjs/midway-faas/commit/426aa65f2eb0d9f4312f5375ea39f9186f2a083c))



## 1.1.4 (2020-07-24)





## [1.1.4](https://github.com/midwayjs/midway-faas/compare/v1.0.8...v1.1.4) (2020-07-24)


### Bug Fixes

* skip compile when tsconfig not found ([#549](https://github.com/midwayjs/midway-faas/issues/549)) ([f40dbae](https://github.com/midwayjs/midway-faas/commit/f40dbaec4883f60801b895d4929fa09f47bb2506))
* ts-mode ([#552](https://github.com/midwayjs/midway-faas/issues/552)) ([d8f231c](https://github.com/midwayjs/midway-faas/commit/d8f231c8a0ad5c50d42809e915c1669b67902305))
* wrapper support middleware ([#529](https://github.com/midwayjs/midway-faas/issues/529)) ([eade6a6](https://github.com/midwayjs/midway-faas/commit/eade6a6fd41494517154398674ab76cbcc2b3e1a))


### Features

* progressive ([#551](https://github.com/midwayjs/midway-faas/issues/551)) ([7b0060e](https://github.com/midwayjs/midway-faas/commit/7b0060e642de8e62ee07d9f4ca8c9aa569f3f34f))
* Support application layer ([#534](https://github.com/midwayjs/midway-faas/issues/534)) ([7a141c0](https://github.com/midwayjs/midway-faas/commit/7a141c0c9404dc20d4d146a14e01dff404943142))
* support initTimeout in fc provider ([#530](https://github.com/midwayjs/midway-faas/issues/530)) ([ddfe654](https://github.com/midwayjs/midway-faas/commit/ddfe65484e0c1dc79c12e855bd098dceb075a22c))





## [1.1.3](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.2...serverless-v1.1.3) (2020-07-24)


### Bug Fixes

* ts-mode ([#552](https://github.com/midwayjs/midway-faas/issues/552)) ([d8f231c](https://github.com/midwayjs/midway-faas/commit/d8f231c8a0ad5c50d42809e915c1669b67902305))


### Features

* progressive ([#551](https://github.com/midwayjs/midway-faas/issues/551)) ([7b0060e](https://github.com/midwayjs/midway-faas/commit/7b0060e642de8e62ee07d9f4ca8c9aa569f3f34f))





## [1.1.2](https://github.com/midwayjs/midway-faas/compare/serverless-v1.1.1...serverless-v1.1.2) (2020-07-23)


### Bug Fixes

* skip compile when tsconfig not found ([#549](https://github.com/midwayjs/midway-faas/issues/549)) ([f40dbae](https://github.com/midwayjs/midway-faas/commit/f40dbaec4883f60801b895d4929fa09f47bb2506))





# [1.1.0](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.11...serverless-v1.1.0) (2020-07-21)


### Features

* Support application layer ([#534](https://github.com/midwayjs/midway-faas/issues/534)) ([7a141c0](https://github.com/midwayjs/midway-faas/commit/7a141c0c9404dc20d4d146a14e01dff404943142))





## [1.0.10](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.9...serverless-v1.0.10) (2020-07-16)


### Bug Fixes

* wrapper support middleware ([#529](https://github.com/midwayjs/midway-faas/issues/529)) ([eade6a6](https://github.com/midwayjs/midway-faas/commit/eade6a6fd41494517154398674ab76cbcc2b3e1a))


### Features

* support initTimeout in fc provider ([#530](https://github.com/midwayjs/midway-faas/issues/530)) ([ddfe654](https://github.com/midwayjs/midway-faas/commit/ddfe65484e0c1dc79c12e855bd098dceb075a22c))





## 1.0.7 (2020-07-14)


### Bug Fixes

* add fc service properties definition ([d2a30ca](https://github.com/midwayjs/midway-faas/commit/d2a30ca5526d5cd2932b2623e71a70444544ec2a))
* aggregation pattern ([#71](https://github.com/midwayjs/midway-faas/issues/71)) ([938c4b3](https://github.com/midwayjs/midway-faas/commit/938c4b338adc5826954c514a913996a1b5abbd49))
* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix hanlder to handler ([#56](https://github.com/midwayjs/midway-faas/issues/56)) ([c272bd7](https://github.com/midwayjs/midway-faas/commit/c272bd78aae12b430553afd041af9223e07b9910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* http support method match ([#524](https://github.com/midwayjs/midway-faas/issues/524)) ([bce48b5](https://github.com/midwayjs/midway-faas/commit/bce48b505dc006e1ff36a78674213c41d9ab5489))
* invoke bug ([#106](https://github.com/midwayjs/midway-faas/issues/106)) ([d45ff3f](https://github.com/midwayjs/midway-faas/commit/d45ff3f3c41764d08c6968dbdd676d174b695d96))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* support typescript run ([#139](https://github.com/midwayjs/midway-faas/issues/139)) ([472c985](https://github.com/midwayjs/midway-faas/commit/472c985d054f452ed79b496d348a95adc754663e))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* wrapper ejs ([#522](https://github.com/midwayjs/midway-faas/issues/522)) ([659b051](https://github.com/midwayjs/midway-faas/commit/659b051173209747d74cb32b41cdf718e894bd6f))
* wrapper generator ([#156](https://github.com/midwayjs/midway-faas/issues/156)) ([be2d5a1](https://github.com/midwayjs/midway-faas/commit/be2d5a1a2c9e6404ded49b78e98f65b50dfe36d5))
* wrapper support faasStarterName ([fc56129](https://github.com/midwayjs/midway-faas/commit/fc561297e1c3f257ca8942e65ad15dbdec977f8a))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add tecent def ([#135](https://github.com/midwayjs/midway-faas/issues/135)) ([8a5a28d](https://github.com/midwayjs/midway-faas/commit/8a5a28df17e3a1b28cf8e7f32aacaf791bf048e8))
* aws support ([#526](https://github.com/midwayjs/midway-faas/issues/526)) ([9da022e](https://github.com/midwayjs/midway-faas/commit/9da022ecdf1e7770c21705131679940adc67ff3c))
* support koa application ([#162](https://github.com/midwayjs/midway-faas/issues/162)) ([364d62b](https://github.com/midwayjs/midway-faas/commit/364d62b48242d2ee86f97f087f912e640e8ff6e7))
* support yaml with ${env.variable} ([#157](https://github.com/midwayjs/midway-faas/issues/157)) ([53f40ef](https://github.com/midwayjs/midway-faas/commit/53f40ef62b2b35ede3c9b667079c5126d41e6804))





## [1.0.6](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.5...serverless-v1.0.6) (2020-07-13)


### Bug Fixes

* wrapper support faasStarterName ([fc56129](https://github.com/midwayjs/midway-faas/commit/fc561297e1c3f257ca8942e65ad15dbdec977f8a))





## [1.0.4](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.3...serverless-v1.0.4) (2020-07-08)


### Bug Fixes

* http support method match ([#524](https://github.com/midwayjs/midway-faas/issues/524)) ([bce48b5](https://github.com/midwayjs/midway-faas/commit/bce48b505dc006e1ff36a78674213c41d9ab5489))





## [1.0.3](https://github.com/midwayjs/midway-faas/compare/serverless-v1.0.2...serverless-v1.0.3) (2020-07-07)


### Bug Fixes

* wrapper ejs ([#522](https://github.com/midwayjs/midway-faas/issues/522)) ([659b051](https://github.com/midwayjs/midway-faas/commit/659b051173209747d74cb32b41cdf718e894bd6f))





## 1.0.1 (2020-07-06)


### Bug Fixes

* add fc service properties definition ([d2a30ca](https://github.com/midwayjs/midway-faas/commit/d2a30ca5526d5cd2932b2623e71a70444544ec2a))
* aggregation pattern ([#71](https://github.com/midwayjs/midway-faas/issues/71)) ([938c4b3](https://github.com/midwayjs/midway-faas/commit/938c4b338adc5826954c514a913996a1b5abbd49))
* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix hanlder to handler ([#56](https://github.com/midwayjs/midway-faas/issues/56)) ([c272bd7](https://github.com/midwayjs/midway-faas/commit/c272bd78aae12b430553afd041af9223e07b9910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* invoke bug ([#106](https://github.com/midwayjs/midway-faas/issues/106)) ([d45ff3f](https://github.com/midwayjs/midway-faas/commit/d45ff3f3c41764d08c6968dbdd676d174b695d96))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* support typescript run ([#139](https://github.com/midwayjs/midway-faas/issues/139)) ([472c985](https://github.com/midwayjs/midway-faas/commit/472c985d054f452ed79b496d348a95adc754663e))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* wrapper generator ([#156](https://github.com/midwayjs/midway-faas/issues/156)) ([be2d5a1](https://github.com/midwayjs/midway-faas/commit/be2d5a1a2c9e6404ded49b78e98f65b50dfe36d5))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add tecent def ([#135](https://github.com/midwayjs/midway-faas/issues/135)) ([8a5a28d](https://github.com/midwayjs/midway-faas/commit/8a5a28df17e3a1b28cf8e7f32aacaf791bf048e8))
* support koa application ([#162](https://github.com/midwayjs/midway-faas/issues/162)) ([364d62b](https://github.com/midwayjs/midway-faas/commit/364d62b48242d2ee86f97f087f912e640e8ff6e7))
* support yaml with ${env.variable} ([#157](https://github.com/midwayjs/midway-faas/issues/157)) ([53f40ef](https://github.com/midwayjs/midway-faas/commit/53f40ef62b2b35ede3c9b667079c5126d41e6804))





# 1.0.0 (2020-07-02)


### Bug Fixes

* add fc service properties definition ([d2a30ca](https://github.com/midwayjs/midway-faas/commit/d2a30ca5526d5cd2932b2623e71a70444544ec2a))
* aggregation pattern ([#71](https://github.com/midwayjs/midway-faas/issues/71)) ([938c4b3](https://github.com/midwayjs/midway-faas/commit/938c4b338adc5826954c514a913996a1b5abbd49))
* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))
* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))
* fix hanlder to handler ([#56](https://github.com/midwayjs/midway-faas/issues/56)) ([c272bd7](https://github.com/midwayjs/midway-faas/commit/c272bd78aae12b430553afd041af9223e07b9910))
* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))
* invoke bug ([#106](https://github.com/midwayjs/midway-faas/issues/106)) ([d45ff3f](https://github.com/midwayjs/midway-faas/commit/d45ff3f3c41764d08c6968dbdd676d174b695d96))
* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))
* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)
* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))
* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))
* support typescript run ([#139](https://github.com/midwayjs/midway-faas/issues/139)) ([472c985](https://github.com/midwayjs/midway-faas/commit/472c985d054f452ed79b496d348a95adc754663e))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))
* wrapper generator ([#156](https://github.com/midwayjs/midway-faas/issues/156)) ([be2d5a1](https://github.com/midwayjs/midway-faas/commit/be2d5a1a2c9e6404ded49b78e98f65b50dfe36d5))


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))
* add tecent def ([#135](https://github.com/midwayjs/midway-faas/issues/135)) ([8a5a28d](https://github.com/midwayjs/midway-faas/commit/8a5a28df17e3a1b28cf8e7f32aacaf791bf048e8))
* support koa application ([#162](https://github.com/midwayjs/midway-faas/issues/162)) ([364d62b](https://github.com/midwayjs/midway-faas/commit/364d62b48242d2ee86f97f087f912e640e8ff6e7))
* support yaml with ${env.variable} ([#157](https://github.com/midwayjs/midway-faas/issues/157)) ([53f40ef](https://github.com/midwayjs/midway-faas/commit/53f40ef62b2b35ede3c9b667079c5126d41e6804))





## [0.3.3](https://github.com/midwayjs/midway-faas/compare/v0.3.2...v0.3.3) (2020-06-16)


### Bug Fixes

* Refactor/invoke ([#178](https://github.com/midwayjs/midway-faas/issues/178)) ([37dd34f](https://github.com/midwayjs/midway-faas/commit/37dd34feab822900af61d7515bc0a4cbed7b20f8))





# [0.3.0](https://github.com/midwayjs/midway-faas/compare/v0.2.99...v0.3.0) (2020-05-26)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.97](https://github.com/midwayjs/midway-faas/compare/v0.2.96...v0.2.97) (2020-05-16)


### Bug Fixes

* fix windows path when invoke ([#169](https://github.com/midwayjs/midway-faas/issues/169)) ([e637a0a](https://github.com/midwayjs/midway-faas/commit/e637a0ab05a769a3797e2dccf0612bbbf650d074))





## [0.2.95](https://github.com/midwayjs/midway-faas/compare/v0.2.94...v0.2.95) (2020-05-15)


### Features

* support koa application ([#162](https://github.com/midwayjs/midway-faas/issues/162)) ([364d62b](https://github.com/midwayjs/midway-faas/commit/364d62b48242d2ee86f97f087f912e640e8ff6e7))





## [0.2.94](https://github.com/midwayjs/midway-faas/compare/v0.2.93...v0.2.94) (2020-05-06)


### Bug Fixes

* wrapper generator ([#156](https://github.com/midwayjs/midway-faas/issues/156)) ([be2d5a1](https://github.com/midwayjs/midway-faas/commit/be2d5a1a2c9e6404ded49b78e98f65b50dfe36d5))


### Features

* support yaml with ${env.variable} ([#157](https://github.com/midwayjs/midway-faas/issues/157)) ([53f40ef](https://github.com/midwayjs/midway-faas/commit/53f40ef62b2b35ede3c9b667079c5126d41e6804))





## [0.2.92](https://github.com/midwayjs/midway-faas/compare/v0.2.91...v0.2.92) (2020-05-05)


### Bug Fixes

* fix error control in fc ([#153](https://github.com/midwayjs/midway-faas/issues/153)) ([f7dd007](https://github.com/midwayjs/midway-faas/commit/f7dd0070f9c1b7f07e628c8d2052d273a8133910))





## [0.2.92-beta.1](https://github.com/midwayjs/midway-faas/compare/v0.2.91...v0.2.92-beta.1) (2020-05-04)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.89](https://github.com/midwayjs/midway-faas/compare/v0.2.88...v0.2.89) (2020-04-28)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.88](https://github.com/midwayjs/midway-faas/compare/v0.2.87...v0.2.88) (2020-04-26)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.87](https://github.com/midwayjs/midway-faas/compare/v0.2.86...v0.2.87) (2020-04-26)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.82](https://github.com/midwayjs/midway-faas/compare/v0.2.81...v0.2.82) (2020-04-23)


### Bug Fixes

* support typescript run ([#139](https://github.com/midwayjs/midway-faas/issues/139)) ([472c985](https://github.com/midwayjs/midway-faas/commit/472c985d054f452ed79b496d348a95adc754663e))





## [0.2.79](https://github.com/midwayjs/midway-faas/compare/v0.2.78...v0.2.79) (2020-04-19)


### Features

* add tecent def ([#135](https://github.com/midwayjs/midway-faas/issues/135)) ([8a5a28d](https://github.com/midwayjs/midway-faas/commit/8a5a28df17e3a1b28cf8e7f32aacaf791bf048e8))





## [0.2.78](https://github.com/midwayjs/midway-faas/compare/v0.2.77...v0.2.78) (2020-04-18)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.77](https://github.com/midwayjs/midway-faas/compare/v0.2.76...v0.2.77) (2020-04-18)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.76](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.76) (2020-04-16)


### Bug Fixes

* add fc service properties definition ([d2a30ca](https://github.com/midwayjs/midway-faas/commit/d2a30ca5526d5cd2932b2623e71a70444544ec2a))
* tencent trigger ([#131](https://github.com/midwayjs/midway-faas/issues/131)) ([0e93057](https://github.com/midwayjs/midway-faas/commit/0e93057205c2b761d1ee6fcf7e9c5d35bab349a7))





## [0.2.75](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.75) (2020-04-15)


### Bug Fixes

* add fc service properties definition ([d2a30ca](https://github.com/midwayjs/midway-faas/commit/d2a30ca5526d5cd2932b2623e71a70444544ec2a))





## [0.2.74](https://github.com/midwayjs/midway-faas/compare/v0.2.73...v0.2.74) (2020-04-13)


### Bug Fixes

* add fc service properties definition ([3cbdfe6](https://github.com/midwayjs/midway-faas/commit/3cbdfe6ad793d5a553a097587bb3883680f1a4f2))





## [0.2.73](https://github.com/midwayjs/midway-faas/compare/v0.2.73-alpha.0...v0.2.73) (2020-04-11)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.72](https://github.com/midwayjs/midway-faas/compare/v0.2.71...v0.2.72) (2020-04-11)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.66](https://github.com/midwayjs/midway-faas/compare/v0.2.65...v0.2.66) (2020-04-06)


### Bug Fixes

* invoke getFuncList and core auto load plugin ([#112](https://github.com/midwayjs/midway-faas/issues/112)) ([54e4d11](https://github.com/midwayjs/midway-faas/commit/54e4d1151942075b86d187c46fc107b9ff1d816b))





## [0.2.65](https://github.com/midwayjs/midway-faas/compare/v0.2.64...v0.2.65) (2020-04-05)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.64](https://github.com/midwayjs/midway-faas/compare/v0.2.63...v0.2.64) (2020-04-05)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.63](https://github.com/midwayjs/midway-faas/compare/v0.2.62...v0.2.63) (2020-04-03)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.62](https://github.com/midwayjs/midway-faas/compare/v0.2.61...v0.2.62) (2020-04-03)


### Bug Fixes

* code ana package ([#108](https://github.com/midwayjs/midway-faas/issues/108)) ([6e11d0f](https://github.com/midwayjs/midway-faas/commit/6e11d0f588e10b41551256a23a7d4fd6b8133c93))
* invoke bug ([#106](https://github.com/midwayjs/midway-faas/issues/106)) ([d45ff3f](https://github.com/midwayjs/midway-faas/commit/d45ff3f3c41764d08c6968dbdd676d174b695d96))





## [0.2.61](https://github.com/midwayjs/midway-faas/compare/v0.2.60...v0.2.61) (2020-03-31)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.59](https://github.com/midwayjs/midway-faas/compare/v0.2.58...v0.2.59) (2020-03-30)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.55](https://github.com/midwayjs/midway-faas/compare/v0.2.54...v0.2.55) (2020-03-20)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.49](https://github.com/midwayjs/midway-faas/compare/v0.2.48...v0.2.49) (2020-03-14)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.40](https://github.com/midwayjs/midway-faas/compare/v0.2.39...v0.2.40) (2020-03-02)


### Bug Fixes

* aggregation pattern ([#71](https://github.com/midwayjs/midway-faas/issues/71)) ([938c4b3](https://github.com/midwayjs/midway-faas/commit/938c4b338adc5826954c514a913996a1b5abbd49))





## [0.2.34](https://github.com/midwayjs/midway-faas/compare/v0.2.33...v0.2.34) (2020-02-26)


### Bug Fixes

* file compare & npm options ([#62](https://github.com/midwayjs/midway-faas/issues/62)) ([beb50f8](https://github.com/midwayjs/midway-faas/commit/beb50f85106cd627aac7b2ab0317ed29ae830e33))





## [0.2.29](https://github.com/midwayjs/midway-faas/compare/v0.2.28...v0.2.29) (2020-02-22)


### Bug Fixes

* fix hanlder to handler ([#56](https://github.com/midwayjs/midway-faas/issues/56)) ([c272bd7](https://github.com/midwayjs/midway-faas/commit/c272bd78aae12b430553afd041af9223e07b9910))





## [0.2.27](https://github.com/midwayjs/midway-faas/compare/v0.2.26...v0.2.27) (2020-02-21)


### Bug Fixes

* invoke source map ([#52](https://github.com/midwayjs/midway-faas/issues/52)) ([9149d2a](https://github.com/midwayjs/midway-faas/commit/9149d2a9a3f3d9ba975588b61c6f9bbeec2e8d86)), closes [#51](https://github.com/midwayjs/midway-faas/issues/51)





## [0.2.20](https://github.com/midwayjs/midway-faas/compare/v0.2.19...v0.2.20) (2020-02-11)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.2.10](https://github.com/midwayjs/midway-faas/compare/v0.2.9...v0.2.10) (2020-01-16)


### Bug Fixes

* refactor appregation & pass process env to invoke debug ([#24](https://github.com/midwayjs/midway-faas/issues/24)) ([f8cd981](https://github.com/midwayjs/midway-faas/commit/f8cd98118e91d3e1b15c2b37d1aaad6b15282f26))





## [0.2.2](https://github.com/midwayjs/midway-faas/compare/v0.2.1...v0.2.2) (2020-01-08)


### Features

* add cli deploy plugins ([#11](https://github.com/midwayjs/midway-faas/issues/11)) ([f8dbaf8](https://github.com/midwayjs/midway-faas/commit/f8dbaf8f0010731faeda48e1c30be72f2f912791))







**Note:** Version bump only for package @midwayjs/serverless-spec-builder





# [0.2.0](https://github.com/midwayjs/midway-faas/compare/v0.1.12...v0.2.0) (2020-01-05)

**Note:** Version bump only for package @midwayjs/serverless-spec-builder





## [0.1.5](https://github.com/midwayjs/midway-faas/compare/v0.1.4...v0.1.5) (2019-12-22)

**Note:** Version bump only for package @midwayjs/spec-builder





## [0.1.3](https://github.com/midwayjs/midway-faas/compare/v0.1.2...v0.1.3) (2019-12-18)

**Note:** Version bump only for package @midwayjs/spec-builder





## [0.1.2](https://github.com/midwayjs/midway-faas/compare/v0.1.1...v0.1.2) (2019-12-13)

**Note:** Version bump only for package @midwayjs/spec-builder





## [0.1.1](https://github.com/midwayjs/midway-faas/compare/v0.1.0...v0.1.1) (2019-12-13)

**Note:** Version bump only for package @midwayjs/spec-builder





# [0.1.0](https://github.com/midwayjs/midway-faas/compare/v0.0.10...v0.1.0) (2019-12-13)


### Bug Fixes

* aggregation custom domain configure ([5c92e5b](https://github.com/midwayjs/midway-faas/commit/5c92e5b0b9725f5507e2d692f8839b47586f71d9))


### Features

* aggregation deploy ([d97d3a1](https://github.com/midwayjs/midway-faas/commit/d97d3a1d6211395c19d38b0af4b9de72cd81f23b))
* aggregation for tencent ([600d8cd](https://github.com/midwayjs/midway-faas/commit/600d8cdbe60701d465f632181a3af520717f6564))
* support os and log event ([c2e2a60](https://github.com/midwayjs/midway-faas/commit/c2e2a6087ac4ae5d79def5d27d161d666e9d421e))
