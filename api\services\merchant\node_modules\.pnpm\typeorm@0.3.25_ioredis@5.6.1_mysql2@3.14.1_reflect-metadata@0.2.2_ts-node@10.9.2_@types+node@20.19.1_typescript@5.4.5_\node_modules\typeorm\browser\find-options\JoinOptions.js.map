{"version": 3, "sources": ["../browser/src/find-options/JoinOptions.ts"], "names": [], "mappings": "", "file": "JoinOptions.js", "sourcesContent": ["/**\n * Used to specify what entity relations should be loaded.\n *\n * Example:\n *  const options: JoinOptions = {\n *     alias: \"photo\",\n *     leftJoin: {\n *         author: \"photo.author\",\n *         categories: \"categories\",\n *         user: \"categories.user\",\n *         profile: \"user.profile\"\n *     },\n *     innerJoin: {\n *         author: \"photo.author\",\n *         categories: \"categories\",\n *         user: \"categories.user\",\n *         profile: \"user.profile\"\n *     },\n *     leftJoinAndSelect: {\n *         author: \"photo.author\",\n *         categories: \"categories\",\n *         user: \"categories.user\",\n *         profile: \"user.profile\"\n *     },\n *     innerJoinAndSelect: {\n *         author: \"photo.author\",\n *         categories: \"categories\",\n *         user: \"categories.user\",\n *         profile: \"user.profile\"\n *     }\n * };\n *\n * @deprecated\n */\nexport interface JoinOptions {\n    /**\n     * Alias of the main entity.\n     */\n    alias: string\n\n    /**\n     * Object where each key represents the LEFT JOIN alias,\n     * and the corresponding value represents the relation path.\n     *\n     * The columns of the joined table are included in the selection.\n     */\n    leftJoinAndSelect?: { [key: string]: string }\n\n    /**\n     * Object where each key represents the INNER JOIN alias,\n     * and the corresponding value represents the relation path.\n     *\n     * The columns of the joined table are included in the selection.\n     */\n    innerJoinAndSelect?: { [key: string]: string }\n\n    /**\n     * Object where each key represents the LEFT JOIN alias,\n     * and the corresponding value represents the relation path.\n     *\n     * This method does not select the columns of the joined table.\n     */\n    leftJoin?: { [key: string]: string }\n\n    /**\n     * Object where each key represents the INNER JOIN alias,\n     * and the corresponding value represents the relation path.\n     *\n     * This method does not select the columns of the joined table.\n     */\n    innerJoin?: { [key: string]: string }\n}\n"], "sourceRoot": ".."}