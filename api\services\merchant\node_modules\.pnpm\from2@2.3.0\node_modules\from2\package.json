{"name": "from2", "description": "Convenience wrapper for ReadableStream, with an API lifted from \"from\" and \"through2\"", "version": "2.3.0", "main": "index.js", "scripts": {"test": "node test"}, "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}, "devDependencies": {"tape": "^4.0.0"}, "author": "<PERSON> <<EMAIL>> (http://hughsk.io/)", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "git://github.com/hughsk/from2"}, "bugs": {"url": "https://github.com/hughsk/from2/issues"}, "homepage": "https://github.com/hughsk/from2", "keywords": ["from", "stream", "readable", "pull", "convenience", "wrapper"]}