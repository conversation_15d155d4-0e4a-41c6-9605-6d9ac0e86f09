"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CachingFactory = void 0;
const core_1 = require("@midwayjs/core");
const base_1 = require("./base");
let CachingFactory = class CachingFactory extends core_1.ServiceFactory {
    async init() {
        await this.initClients(this.cacheManagerConfig);
    }
    async createClient(config, clientName) {
        // multi cache
        if (Array.isArray(config.store)) {
            const newFactory = [];
            for (const storeConfig of config.store) {
                if (typeof storeConfig === 'string') {
                    if (!this.has(storeConfig)) {
                        throw new core_1.MidwayCommonError(`cache instance "${storeConfig}" not found in "${clientName}", please check your configuration.`);
                    }
                    newFactory.push(this.get(storeConfig));
                }
                else if (typeof storeConfig === 'function') {
                    newFactory.push(await storeConfig());
                }
                else if (storeConfig['wrap']) {
                    // wrap is a caching object method
                    newFactory.push(storeConfig['wrap']);
                }
                else if (typeof storeConfig === 'object') {
                    if (typeof storeConfig.store === 'function') {
                        storeConfig.store = await storeConfig.store(storeConfig['options'] || {}, this.applicationContext);
                    }
                    if (!storeConfig.store) {
                        throw new core_1.MidwayCommonError(`cache instance "${clientName}" store is undefined, please check your configuration.`);
                    }
                    newFactory.push(await (0, base_1.caching)(storeConfig.store, storeConfig['options']));
                }
                else {
                    throw new core_1.MidwayCommonError('invalid cache config');
                }
            }
            return await (0, base_1.multiCaching)(newFactory);
        }
        else {
            // single cache
            if (typeof config.store === 'function') {
                config.store = await config.store(config['options'] || {}, this.applicationContext);
            }
            if (!config.store) {
                throw new core_1.MidwayCommonError(`cache instance "${clientName}" store is undefined, please check your configuration.`);
            }
            return await (0, base_1.caching)(config.store, config['options']);
        }
    }
    getName() {
        return 'cache-manager';
    }
    getCaching(cacheKey) {
        return this.get(cacheKey);
    }
    getMultiCaching(cacheKey) {
        return this.get(cacheKey);
    }
};
__decorate([
    (0, core_1.Config)('cacheManager'),
    __metadata("design:type", Object)
], CachingFactory.prototype, "cacheManagerConfig", void 0);
__decorate([
    (0, core_1.ApplicationContext)(),
    __metadata("design:type", Object)
], CachingFactory.prototype, "applicationContext", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CachingFactory.prototype, "init", null);
CachingFactory = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CachingFactory);
exports.CachingFactory = CachingFactory;
//# sourceMappingURL=factory.js.map