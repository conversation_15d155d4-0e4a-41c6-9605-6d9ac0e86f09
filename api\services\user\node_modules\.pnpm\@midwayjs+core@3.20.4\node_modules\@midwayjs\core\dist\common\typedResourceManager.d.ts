import { ClassType } from '../interface';
export declare class TypedResourceManager<Resource = any, ResourceInitializeConfig = any, ResourceProviderType = any> {
    protected typedResourceInitializerOptions: {
        initializeValue: {
            [resourceName: string]: ResourceInitializeConfig;
        };
        initializeClzProvider: {
            [resourceName: string]: ClassType<ResourceProviderType>;
        };
        resourceInitialize: (resourceInitializeConfig: ResourceInitializeConfig, resourceName: string) => Promise<Resource>;
        resourceBinding: (ClzProvider: ClassType<ResourceProviderType>, resourceInitializeConfig: ResourceInitializeConfig, resource: Resource, resourceName: string) => Promise<any>;
        resourceStart: (resource: Resource, resourceInitializeConfig: ResourceInitializeConfig, resourceBindingResult?: any) => Promise<void>;
        resourceDestroy: (resource: Resource, resourceInitializeConfig: ResourceInitializeConfig) => Promise<void>;
    };
    private resourceMap;
    private resourceBindingMap;
    constructor(typedResourceInitializerOptions: {
        initializeValue: {
            [resourceName: string]: ResourceInitializeConfig;
        };
        initializeClzProvider: {
            [resourceName: string]: ClassType<ResourceProviderType>;
        };
        resourceInitialize: (resourceInitializeConfig: ResourceInitializeConfig, resourceName: string) => Promise<Resource>;
        resourceBinding: (ClzProvider: ClassType<ResourceProviderType>, resourceInitializeConfig: ResourceInitializeConfig, resource: Resource, resourceName: string) => Promise<any>;
        resourceStart: (resource: Resource, resourceInitializeConfig: ResourceInitializeConfig, resourceBindingResult?: any) => Promise<void>;
        resourceDestroy: (resource: Resource, resourceInitializeConfig: ResourceInitializeConfig) => Promise<void>;
    });
    createResource(resourceName: string, resourceInitializeConfig: ResourceInitializeConfig): Promise<Resource>;
    init(): Promise<void>;
    startParallel(): Promise<void>;
    start(): Promise<void>;
    destroyParallel(): Promise<void>;
    destroy(): Promise<void>;
    getResource(resourceName: string): any;
}
//# sourceMappingURL=typedResourceManager.d.ts.map