{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,yDAIgC;AAChC,iDAAyC;AACzC,+BAA4B;AAC5B,uCAAuC;AAEvC,0CAAwB;AACxB,MAAM,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;AAErC,uDAAiD;AAAxC,uGAAA,OAAO,OAAA;AAChB,OAAO,CAAC,GAAG,CAAC,eAAe,GAAG,IAAA,WAAI,EAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AACrD,MAAa,GAAI,SAAQ,0BAAW;IAClC,KAAK,CAAC,iBAAiB;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAClD,6BAA6B;QAC7B,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpC,OAAO;SACR;QACD,MAAM,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAChC,IAAI,QAAQ,GAAG,oBAAU,CAAC;QAC1B,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,OAAO,EAAE;YAC3B,QAAQ,GAAG,IAAA,oCAAqB,EAAC,oBAAU,EAAE;gBAC3C,OAAO;gBACP,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG;gBAClB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;aACxB,CAAC,CAAC;SACJ;QACD,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YACpB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;YACxB,QAAQ,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,2BAA2B,EAAE,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,KAAK,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;QACzC,MAAM,cAAc,GAAG,MAAM,IAAA,6BAAc,EAAC,QAAQ,EAAE;YACpD,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG;YAClB,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;YACvB,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG;YAClB,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC5B,CAAC,CAAC;QACH,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACnC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,SAAS;IACT,cAAc;QACZ,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;YAC5B,QAAQ;SACT,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,GAAG;QACP,IAAI,GAAG,IAAI,GAAG,CAAC,OAAO,EAAE;YACtB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACzB,MAAM,GAAG,CAAC;SACX;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;SACtB;IACH,CAAC;IAED,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,KAAK,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3C,MAAM,KAAK,CAAC,WAAW,EAAE,CAAC;QAC1B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACxB,OAAO;SACR;QAED,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACpC,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;aAAM;YACL,qBAAqB;YACrB,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC;SACpB;IACH,CAAC;IAED,KAAK,CAAC,GAAG,IAAI;QACX,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE;YACvE,OAAO;SACR;QACD,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,cAAc;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC3B,IAAI;YACF,MAAM,WAAW,GAAG,IAAA,wBAAQ,EAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YACrE,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,CAAC;SAC7C;QAAC,WAAM;YACN,EAAE;SACH;QAED,IAAI;YACF,sBAAsB;YACtB,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,CAAC,OAAO,CAAC;YACtD,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,UAAU,EAAE,CAAC,CAAC;SACxD;QAAC,WAAM;YACN,EAAE;SACH;IACH,CAAC;IAED,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,WAAY;QAC3D,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC;IACtE,CAAC;CACF;AApGD,kBAoGC"}