"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolRpcConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const rpc_1 = require("./rpc");
const decorator_1 = require("./decorator");
let CoolRpcConfiguration = class CoolRpcConfiguration {
    async onReady(container) {
        global['moleculer.transactions'] = {};
        (await container.getAsync(rpc_1.CoolRpc)).init();
        // 装饰器
        await container.getAsync(decorator_1.CoolRpcDecorator);
    }
    async onStop(container) {
        (await container.getAsync(rpc_1.CoolRpc)).stop();
    }
};
exports.CoolRpcConfiguration = CoolRpcConfiguration;
exports.CoolRpcConfiguration = CoolRpcConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'cool:rpc',
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], CoolRpcConfiguration);
