{"version": 3, "sources": ["../browser/src/error/EntityNotFoundError.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AAEzD;;GAEG;AACH,MAAM,OAAO,mBAAoB,SAAQ,YAAY;IAIjD,YAAY,WAA8B,EAAE,QAAa;QACrD,KAAK,EAAE,CAAA;QAEP,IAAI,CAAC,WAAW,GAAG,WAAW,CAAA;QAC9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAExB,IAAI,CAAC,OAAO;YACR,sCAAsC,IAAI,CAAC,eAAe,CACtD,WAAW,CACd,IAAI,GAAG,aAAa,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,CAAA;IAC/D,CAAC;IAEO,eAAe,CAAC,MAAyB;QAC7C,IAAI,eAAe,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAA;QAC9B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;aAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,IAAK,MAAc,EAAE,CAAC;YACnE,OAAQ,MAAc,CAAC,IAAI,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,OAAO,MAAa,CAAA;QACxB,CAAC;IACL,CAAC;IAEO,iBAAiB,CAAC,QAAa;QACnC,IAAI,CAAC;YACD,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAA;QAC5C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC,CAAA,CAAC;QACd,OAAO,EAAE,GAAG,QAAQ,CAAA;IACxB,CAAC;CACJ", "file": "EntityNotFoundError.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\nimport { TypeORMError } from \"./TypeORMError\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\n/**\n * Thrown when no result could be found in methods which are not allowed to return undefined or an empty set.\n */\nexport class EntityNotFoundError extends TypeORMError {\n    public readonly entityClass: EntityTarget<any>\n    public readonly criteria: any\n\n    constructor(entityClass: EntityTarget<any>, criteria: any) {\n        super()\n\n        this.entityClass = entityClass\n        this.criteria = criteria\n\n        this.message =\n            `Could not find any entity of type \"${this.stringifyTarget(\n                entityClass,\n            )}\" ` + `matching: ${this.stringifyCriteria(criteria)}`\n    }\n\n    private stringifyTarget(target: EntityTarget<any>): string {\n        if (InstanceChecker.isEntitySchema(target)) {\n            return target.options.name\n        } else if (typeof target === \"function\") {\n            return target.name\n        } else if (ObjectUtils.isObject(target) && \"name\" in (target as any)) {\n            return (target as any).name\n        } else {\n            return target as any\n        }\n    }\n\n    private stringifyCriteria(criteria: any): string {\n        try {\n            return JSON.stringify(criteria, null, 4)\n        } catch (e) {}\n        return \"\" + criteria\n    }\n}\n"], "sourceRoot": ".."}