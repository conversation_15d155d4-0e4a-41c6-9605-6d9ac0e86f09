"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InjectDataSource = exports.EventSubscriberModel = exports.InjectEntityModel = exports.ORM_DATA_SOURCE_KEY = exports.ORM_MODEL_KEY = exports.EVENT_SUBSCRIBER_KEY = exports.ENTITY_MODEL_KEY = void 0;
const core_1 = require("@midwayjs/core");
const typeorm_1 = require("typeorm");
exports.ENTITY_MODEL_KEY = 'typeorm:entity_model_key';
exports.EVENT_SUBSCRIBER_KEY = 'typeorm:event_subscriber_key';
exports.ORM_MODEL_KEY = 'typeorm:orm_model_key';
exports.ORM_DATA_SOURCE_KEY = 'typeorm:data_source_key';
function InjectEntityModel(modelKey, connectionName) {
    return (0, core_1.createCustomPropertyDecorator)(exports.ORM_MODEL_KEY, {
        modelKey,
        connectionName,
    });
}
exports.InjectEntityModel = InjectEntityModel;
/**
 * EventSubscriber - typeorm
 * implements EntitySubscriberInterface
 */
function EventSubscriberModel() {
    return function (target) {
        (0, core_1.Provide)()(target);
        (0, core_1.Scope)(core_1.ScopeEnum.Singleton)(target);
        (0, typeorm_1.EventSubscriber)()(target);
    };
}
exports.EventSubscriberModel = EventSubscriberModel;
function InjectDataSource(dataSourceName) {
    return (0, core_1.createCustomPropertyDecorator)(exports.ORM_DATA_SOURCE_KEY, {
        dataSourceName,
    });
}
exports.InjectDataSource = InjectDataSource;
//# sourceMappingURL=decorator.js.map