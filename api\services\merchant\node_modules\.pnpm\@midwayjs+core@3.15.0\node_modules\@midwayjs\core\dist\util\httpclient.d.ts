/// <reference types="node" />
/// <reference types="node" />
/// <reference types="node" />
import http = require('http');
import https = require('https');
export type HttpClientMimeType = 'text' | 'json' | undefined;
export interface HttpClientOptions<Data = any> extends https.RequestOptions {
    headers?: any;
    contentType?: HttpClientMimeType;
    dataType?: HttpClientMimeType;
    data?: Data;
    timeout?: number;
}
export interface HttpClientResponse<ResType = any> extends http.IncomingMessage {
    status: number;
    data: Buffer | string | ResType;
}
export declare function makeHttpRequest<ResType>(url: string, options?: HttpClientOptions): Promise<HttpClientResponse<ResType>>;
/**
 * A simple http client
 */
export declare class HttpClient {
    readonly defaultOptions: Pick<HttpClientOptions, 'headers' | 'timeout' | 'method'>;
    constructor(defaultOptions?: Pick<HttpClientOptions, 'headers' | 'timeout' | 'method'>);
    request(url: string, options?: HttpClientOptions): Promise<HttpClientResponse>;
}
//# sourceMappingURL=httpclient.d.ts.map