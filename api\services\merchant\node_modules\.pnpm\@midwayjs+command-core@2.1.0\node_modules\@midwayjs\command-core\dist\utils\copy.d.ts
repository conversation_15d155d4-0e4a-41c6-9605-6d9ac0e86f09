export declare const copyFiles: (options: ICopyOptions) => Promise<void>;
export declare const copyStaticFiles: ({ sourceDir, targetDir, log }: {
    sourceDir: any;
    targetDir: any;
    log: any;
}) => Promise<void>;
export interface ICopyOptions {
    sourceDir: string;
    targetDir: string;
    defaultInclude?: string[];
    include?: string[];
    exclude?: string[];
    log?: (path: string) => void;
}
export declare const exists: (path: string) => Promise<boolean>;
//# sourceMappingURL=copy.d.ts.map