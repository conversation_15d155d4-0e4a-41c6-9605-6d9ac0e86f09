{"version": 3, "file": "switchMap.js", "sources": ["../../../src/internal/operators/switchMap.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,GAAG,EAAE,MAAM,OAAO,CAAC;AAC5B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAwEjG,MAAM,UAAU,SAAS,CACvB,OAAuC,EACvC,cAA6G;IAE7G,IAAI,OAAO,cAAc,KAAK,UAAU,EAAE;QACxC,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC3C,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAC1C,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAC5C,CAAC,CACH,CAAC;KACH;IACD,OAAO,CAAC,MAAqB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;AAChF,CAAC;AAED,MAAM,iBAAiB;IACrB,YAAoB,OAAwD;QAAxD,YAAO,GAAP,OAAO,CAAiD;IAC5E,CAAC;IAED,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAC7E,CAAC;CACF;AAOD,MAAM,mBAA0B,SAAQ,qBAA2B;IAIjE,YAAY,WAA0B,EAClB,OAAwD;QAC1E,KAAK,CAAC,WAAW,CAAC,CAAC;QADD,YAAO,GAAP,OAAO,CAAiD;QAJpE,UAAK,GAAG,CAAC,CAAC;IAMlB,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,IAAI,MAA0B,CAAC;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,KAAM,CAAC,KAAK,CAAC,CAAC;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IACzB,CAAC;IAEO,SAAS,CAAC,MAA0B;QAC1C,MAAM,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACjD,IAAI,iBAAiB,EAAE;YACrB,iBAAiB,CAAC,WAAW,EAAE,CAAC;SACjC;QACD,MAAM,eAAe,GAAG,IAAI,qBAAqB,CAAC,IAAI,CAAC,CAAC;QACxD,MAAM,WAAW,GAAG,IAAI,CAAC,WAA2B,CAAC;QACrD,WAAW,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QACjC,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAIjE,IAAI,IAAI,CAAC,iBAAiB,KAAK,eAAe,EAAE;YAC9C,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;SACzC;IACH,CAAC;IAES,SAAS;QACjB,MAAM,EAAC,iBAAiB,EAAC,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,EAAE;YAClD,KAAK,CAAC,SAAS,EAAE,CAAC;SACnB;QACD,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAES,YAAY;QACpB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;IACrC,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACnC,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,KAAK,CAAC,SAAS,EAAE,CAAC;SACnB;IACH,CAAC;IAED,UAAU,CAAC,UAAa;QACpB,IAAI,CAAC,WAAW,CAAC,IAAK,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;CACF"}