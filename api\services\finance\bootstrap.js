const { Framework } = require('@midwayjs/koa');
const { bootstrap } = require('@midwayjs/core');

// 财务微服务启动文件
bootstrap(Framework, {
  globalPrefix: '/api',
})
  .then(() => {
    console.log('✅ 财务微服务启动成功！');
    console.log('🚀 服务端口: 9803');
    console.log('📋 服务名称: finance-service');
    console.log('💰 财务数据独立管理');
    console.log('🔗 RPC通信已启用');
  })
  .catch(err => {
    console.error('❌ 财务微服务启动失败:', err);
    process.exit(1);
  }); 