{"version": 3, "sources": ["../../src/util/PathUtils.ts"], "names": [], "mappings": ";;AAKA,wCAYC;AAMD,wCAGC;AAKD,gCAEC;AAjCD,+CAAoC;AAEpC,MAAM,mBAAmB,GAAG,iBAAiB,CAAA;AAC7C,MAAM,uBAAuB,GAAG,mBAAmB,CAAA;AAEnD,SAAgB,cAAc,CAAC,QAAgB;IAC3C,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO;QAAE,OAAO,QAAQ,CAAA;IAEjD,IAAI,QAAQ,CAAC,KAAK,CAAC,mBAAmB,CAAC;QACnC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;SACtD,IAAI,QAAQ,CAAC,KAAK,CAAC,uBAAuB,CAAC;QAC5C,QAAQ,GAAG,QAAQ,CAAC,OAAO,CACvB,uBAAuB,EACvB,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CACtD,CAAA;IAEL,OAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;AACvC,CAAC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,QAAgB;IAC3C,MAAM,IAAI,GAAG,cAAc,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAA;IACnD,OAAO,IAAA,kBAAI,EAAC,IAAI,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC,CAAA;AACrC,CAAC;AAED;;GAEG;AACH,SAAgB,UAAU,CAAC,QAAgB;IACvC,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAA;AACpD,CAAC", "file": "PathUtils.js", "sourcesContent": ["import { hash } from \"./StringUtils\"\n\nconst WINDOWS_PATH_REGEXP = /^([a-zA-Z]:.*)$/\nconst UNC_WINDOWS_PATH_REGEXP = /^\\\\\\\\(\\.\\\\)?(.*)$/\n\nexport function toPortablePath(filepath: string): string {\n    if (process.platform !== `win32`) return filepath\n\n    if (filepath.match(WINDOWS_PATH_REGEXP))\n        filepath = filepath.replace(WINDOWS_PATH_REGEXP, `/$1`)\n    else if (filepath.match(UNC_WINDOWS_PATH_REGEXP))\n        filepath = filepath.replace(\n            UNC_WINDOWS_PATH_REGEXP,\n            (match, p1, p2) => `/unc/${p1 ? `.dot/` : ``}${p2}`,\n        )\n\n    return filepath.replace(/\\\\/g, `/`)\n}\n\n/**\n * Create deterministic valid database name (class, database) of fixed length from any filepath. Equivalent paths for windows/posix systems should\n * be equivalent to enable portability\n */\nexport function filepathToName(filepath: string): string {\n    const uniq = toPortablePath(filepath).toLowerCase()\n    return hash(uniq, { length: 63 })\n}\n\n/**\n * Cross platform isAbsolute\n */\nexport function isAbsolute(filepath: string): boolean {\n    return !!filepath.match(/^(?:[a-z]:|[\\\\]|[/])/i)\n}\n"], "sourceRoot": ".."}