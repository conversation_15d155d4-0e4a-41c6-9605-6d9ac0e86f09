{"name": "@midwayjs/serverless-spec-builder", "version": "2.1.0", "main": "dist/index", "typings": "dist/index.d.ts", "dependencies": {"ejs": "^3.1.3", "js-yaml": "^4.1.0", "mkdirp": "^0.5.1"}, "devDependencies": {"@midwayjs/serverless-fc-starter": "^2.14.11", "@types/lodash.get": "^4.4.6", "fs-extra": "^8.1.0", "mm": "3"}, "engines": {"node": ">= 10"}, "files": ["dist", "fc", "scf", "aws", "hooks_runtime.ejs", "wrapper_app.ejs", "wrapper_v1.ejs", "wrapper_v2.ejs", "wrapper_v3.ejs", "wrapper_v3_specific.ejs"], "scripts": {"build": "tsc --build", "lint": "../../node_modules/.bin/eslint .", "test": "../../node_modules/.bin/jest", "cov": "../../node_modules/.bin/jest --coverage", "ci-test-only": "TESTS=test/lib/cmd/cov.test.js npm run test-local", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "license": "MIT", "gitHead": "8b6ce5b6bebd4d31140af0e9a51871ab12692b14"}