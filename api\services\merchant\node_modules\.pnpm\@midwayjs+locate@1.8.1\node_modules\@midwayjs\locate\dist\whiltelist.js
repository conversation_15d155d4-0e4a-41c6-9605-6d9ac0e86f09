"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.inclueDBDriver = exports.includeDependencies = void 0;
function includeDependencies(usingDependencies, pkgDeps) {
    const result = new Set(usingDependencies);
    // whitelist for sequelize
    if (usingDependencies.includes('sequelize-typescript')) {
        result.add('sequelize');
    }
    // whitelist for request
    if (usingDependencies.includes('request-promise')) {
        if (pkgDeps['request']) {
            result.add('request');
        }
    }
    for (const key of Object.keys(pkgDeps)) {
        // add for egg plugin
        if (/egg/.test(key)) {
            result.add(key);
        }
        // add for midway package
        if (/midway/.test(key) && !/build-plugin-/.test(key) && !/cli-plugin-/.test(key)) {
            result.add(key);
        }
        // add for db driver
        if (inclueDBDriver(key)) {
            result.add(key);
        }
    }
    return Array.from(result);
}
exports.includeDependencies = includeDependencies;
function inclueDBDriver(key) {
    return [
        'mysql',
        'mysql2',
        'pg',
        'sqlite3',
        'mssql',
        'sql.js',
        'oracledb',
        '@sap/hana-client',
        'mongodb',
        'mongoose',
    ].includes(key);
}
exports.inclueDBDriver = inclueDBDriver;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoid2hpbHRlbGlzdC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uL3NyYy93aGlsdGVsaXN0LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiI7OztBQUFBLFNBQWdCLG1CQUFtQixDQUFDLGlCQUFpQixFQUFFLE9BQU87SUFDNUQsTUFBTSxNQUFNLEdBQWdCLElBQUksR0FBRyxDQUFDLGlCQUFpQixDQUFDLENBQUM7SUFDdkQsMEJBQTBCO0lBQzFCLElBQUksaUJBQWlCLENBQUMsUUFBUSxDQUFDLHNCQUFzQixDQUFDLEVBQUU7UUFDdEQsTUFBTSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsQ0FBQztLQUN6QjtJQUNELHdCQUF3QjtJQUN4QixJQUFJLGlCQUFpQixDQUFDLFFBQVEsQ0FBQyxpQkFBaUIsQ0FBQyxFQUFFO1FBQ2pELElBQUksT0FBTyxDQUFDLFNBQVMsQ0FBQyxFQUFFO1lBQ3RCLE1BQU0sQ0FBQyxHQUFHLENBQUMsU0FBUyxDQUFDLENBQUM7U0FDdkI7S0FDRjtJQUVELEtBQUssTUFBTSxHQUFHLElBQUksTUFBTSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsRUFBRTtRQUN0QyxxQkFBcUI7UUFDckIsSUFBSSxLQUFLLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxFQUFFO1lBQ25CLE1BQU0sQ0FBQyxHQUFHLENBQUMsR0FBRyxDQUFDLENBQUM7U0FDakI7UUFFRCx5QkFBeUI7UUFDekIsSUFBSSxRQUFRLENBQUMsSUFBSSxDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUMsZUFBZSxDQUFDLElBQUksQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUU7WUFDaEYsTUFBTSxDQUFDLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQztTQUNqQjtRQUVELG9CQUFvQjtRQUNwQixJQUFJLGNBQWMsQ0FBQyxHQUFHLENBQUMsRUFBRTtZQUN2QixNQUFNLENBQUMsR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDO1NBQ2pCO0tBQ0Y7SUFDRCxPQUFPLEtBQUssQ0FBQyxJQUFJLENBQUMsTUFBTSxDQUFDLENBQUM7QUFDNUIsQ0FBQztBQTlCRCxrREE4QkM7QUFFRCxTQUFnQixjQUFjLENBQUMsR0FBRztJQUNoQyxPQUFPO1FBQ0wsT0FBTztRQUNQLFFBQVE7UUFDUixJQUFJO1FBQ0osU0FBUztRQUNULE9BQU87UUFDUCxRQUFRO1FBQ1IsVUFBVTtRQUNWLGtCQUFrQjtRQUNsQixTQUFTO1FBQ1QsVUFBVTtLQUNYLENBQUMsUUFBUSxDQUFDLEdBQUcsQ0FBQyxDQUFDO0FBQ2xCLENBQUM7QUFiRCx3Q0FhQyJ9