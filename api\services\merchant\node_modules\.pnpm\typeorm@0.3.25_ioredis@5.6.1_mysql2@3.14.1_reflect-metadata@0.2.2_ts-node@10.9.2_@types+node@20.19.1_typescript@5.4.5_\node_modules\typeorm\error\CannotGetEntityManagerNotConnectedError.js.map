{"version": 3, "sources": ["../../src/error/CannotGetEntityManagerNotConnectedError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,uCAAwC,SAAQ,2BAAY;IACrE,YAAY,cAAsB;QAC9B,KAAK,CACD,kCAAkC,cAAc,yDAAyD,CAC5G,CAAA;IACL,CAAC;CACJ;AAND,0FAMC", "file": "CannotGetEntityManagerNotConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to access entity manager before connection is established.\n */\nexport class CannotGetEntityManagerNotConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot get entity manager for \"${connectionName}\" connection because connection is not yet established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}