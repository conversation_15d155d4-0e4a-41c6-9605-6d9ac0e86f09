"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CannotConnectAlreadyConnectedError = void 0;
const TypeORMError_1 = require("./TypeORMError");
/**
 * Thrown when consumer tries to connect when he already connected.
 */
class CannotConnectAlreadyConnectedError extends TypeORMError_1.TypeORMError {
    constructor(connectionName) {
        super(`Cannot create a "${connectionName}" connection because connection to the database already established.`);
    }
}
exports.CannotConnectAlreadyConnectedError = CannotConnectAlreadyConnectedError;

//# sourceMappingURL=CannotConnectAlreadyConnectedError.js.map
