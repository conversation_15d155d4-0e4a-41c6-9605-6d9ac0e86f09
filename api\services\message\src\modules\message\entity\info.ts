import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 用户ID
 */
@Entity('msg_info')
export class MsgInfoEntity extends BaseEntity {
  @Index()
  @Column({ comment: '用户ID' })
  userId: number;

  @Column({ comment: '标题' })
  title: string;

  @Column({ comment: '内容' })
  content: string;

  @Column({ comment: '类型 0-系统消息 1-用户消息', default: 0 })
  type: number;

  @Column({ comment: '状态 0-未读 1-已读', default: 0 })
  status: number;

  @Column({ comment: '对象', type: 'json', nullable: true })
  obj: {
    // 类型 0-订单 1-商品
    type: number;
    // ID
    id: number;
  };
}
