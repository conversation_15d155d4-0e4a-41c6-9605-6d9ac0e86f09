"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ONE_DAY = exports.COOKIE_EXP_DATE = exports.hash = exports.encode = exports.decode = void 0;
const crc_1 = require("./crc");
/**
 * Decode the base64 cookie value to an object.
 *
 * @param {String} string
 * @return {Object}
 * @api private
 */
function decode(string) {
    const body = Buffer.from(string, 'base64').toString('utf8');
    return JSON.parse(body);
}
exports.decode = decode;
/**
 * Encode an object into a base64-encoded JSON string.
 *
 * @param {Object} body
 * @return {String}
 * @api private
 */
function encode(body) {
    body = JSON.stringify(body);
    return Buffer.from(body).toString('base64');
}
exports.encode = encode;
function hash(sess) {
    return (0, crc_1.crc32)(JSON.stringify(sess));
}
exports.hash = hash;
exports.COOKIE_EXP_DATE = new Date('Thu, 01 Jan 1970 00:00:00 GMT');
exports.ONE_DAY = 24 * 60 * 60 * 1000;
//# sourceMappingURL=util.js.map