export declare const DEFAULT_PRIORITY: {
    L1: string;
    L2: string;
    L3: string;
};
export declare class MidwayPriorityManager {
    private priorityList;
    private defaultPriority;
    getCurrentPriorityList(): Record<string, string>;
    getDefaultPriority(): string;
    isHighPriority(priority?: string): boolean;
    isMediumPriority(priority?: string): boolean;
    isLowPriority(priority?: string): boolean;
    getPriority(priority: string): string;
}
//# sourceMappingURL=priorityManager.d.ts.map