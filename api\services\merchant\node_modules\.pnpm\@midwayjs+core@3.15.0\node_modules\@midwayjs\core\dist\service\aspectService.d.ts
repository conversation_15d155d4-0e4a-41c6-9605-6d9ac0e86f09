import { IMidwayContainer, AspectMetadata, IMethodAspect } from '../interface';
export declare class MidwayAspectService {
    readonly applicationContext: IMidwayContainer;
    constructor(applicationContext: IMidwayContainer);
    /**
     * load aspect method for container
     */
    loadAspect(): Promise<void>;
    addAspect(aspectIns: IMethodAspect, aspectData: AspectMetadata): Promise<void>;
    /**
     * intercept class method in prototype
     * @param Clz class you want to intercept
     * @param methodName method name you want to intercept
     * @param aspectObject aspect object, before, round, etc.
     */
    interceptPrototypeMethod(Clz: new (...args: any[]) => any, methodName: string, aspectObject: IMethodAspect | (() => IMethodAspect)): void;
}
//# sourceMappingURL=aspectService.d.ts.map