{"name": "koa-convert", "version": "2.0.0", "description": "convert modern Koa legacy generator-based middleware to promise-based middleware", "main": "index.js", "files": ["index.js"], "repository": {"type": "git", "url": "git+https://github.com/gyson/koa-convert.git"}, "standard": {"ignore": ["index.spec.js"]}, "scripts": {"lint": "standard", "pretest": "npm run lint", "test": "mocha index.spec.js --exit", "precoverage": "rimraf .nyc_output coverage", "coverage": "nyc npm run test", "ci": "npm run coverage"}, "keywords": ["koa", "middleware", "convert", "back", "generator", "promise", "generator-based-middleware", "promise-based-middleware", "support"], "author": "gyson <<EMAIL>>", "contributors": ["gyson <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>> (https://www.3imed-jaberi.com)"], "license": "MIT", "dependencies": {"co": "^4.6.0", "koa-compose": "^4.1.0"}, "devDependencies": {"koa": "^2.13.0", "koa-v1": "npm:koa@1.7.0", "mocha": "^7.1.1", "nyc": "^15.1.0", "rimraf": "^3.0.2", "standard": "^14.3.4", "supertest": "^4.0.2"}, "engines": {"node": ">= 10"}, "bugs": {"url": "https://github.com/gyson/koa-convert/issues"}, "homepage": "https://github.com/gyson/koa-convert#readme"}