export declare const getCoreBaseDir: () => string;
interface INpmInstallOptions {
    baseDir?: string;
    register?: string;
    installCmd?: string;
    registerPath?: string;
    moduleName?: string;
    mode?: string[];
    slience?: boolean;
    isLerna?: boolean;
    omitDev?: boolean;
    debugLog?: (...args: any[]) => void;
}
export declare function installNpm(options: INpmInstallOptions): Promise<string>;
export declare function loadNpm(scope: any, npmName: string, npmRegistry?: string): Promise<void>;
export declare const findNpmModuleByResolve: (cwd: any, modName: any) => string;
export declare const findNpmModule: (cwd: any, modName: any) => any;
export declare const resolveMidwayConfig: (cwd: string) => {
    exist: boolean;
    source: string;
};
export declare const findNpm: (argv?: {
    npm?: string;
    registry?: string;
    skipAutoFindNpm?: boolean;
}) => {
    cmd: string;
    npm: string;
    registry: string;
};
export declare const formatInstallNpmCommand: (options: INpmInstallOptions) => string;
export declare const formatModuleVersion: (version?: any) => {
    major: string;
    minor: string;
    patch: string;
};
export declare const findMidwayVersion: (cwd: any) => any;
export {};
//# sourceMappingURL=npm.d.ts.map