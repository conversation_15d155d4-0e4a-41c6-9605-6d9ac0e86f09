{"version": 3, "file": "no-unnecessary-qualifier.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-qualifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,0BAA0B;IAChC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,0DAA0D;SAC7D;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,iBAAiB,GAAc,EAAE,CAAC;QACxC,IAAI,gCAAgC,GAAyB,IAAI,CAAC;QAClE,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,qBAAqB,GAAG,cAAc,CAAC,qBAAqB,CAAC;QACnE,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;QACvC,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,SAAS,mBAAmB,CAC1B,MAAiB,EACjB,OAAuB;YAEvB,OAAO,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC1D,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBAClC,CAAC,CAAC,IAAI,CAAC;QACX,CAAC;QAED,SAAS,wBAAwB,CAAC,MAAiB;;YACjD,MAAM,kBAAkB,GAAG,MAAA,MAAM,CAAC,eAAe,EAAE,mCAAI,EAAE,CAAC;YAE1D,IACE,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC7B,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,CAC1C,EACD;gBACA,OAAO,IAAI,CAAC;aACb;YAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAEnD,OAAO,KAAK,IAAI,IAAI,IAAI,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAa,EACb,KAAqB,EACrB,IAAY;YAEZ,yEAAyE;YACzE,MAAM,KAAK,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;YAErD,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;QAC9D,CAAC;QAED,SAAS,eAAe,CAAC,QAAmB,EAAE,OAAkB;YAC9D,OAAO,QAAQ,KAAK,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;QAED,SAAS,sBAAsB,CAC7B,SAA0D,EAC1D,IAAyB;YAEzB,MAAM,WAAW,GAAG,qBAAqB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACzD,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAE/C,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YAEjE,IACE,eAAe,KAAK,SAAS;gBAC7B,CAAC,wBAAwB,CAAC,eAAe,CAAC,EAC1C;gBACA,OAAO,KAAK,CAAC;aACd;YAED,MAAM,cAAc,GAAG,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAE3D,IAAI,cAAc,KAAK,SAAS,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,mEAAmE;YACnE,MAAM,SAAS,GAAG,gBAAgB,CAChC,WAAW,EACX,cAAc,CAAC,KAAK,EACpB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CACzB,CAAC;YAEF,OAAO,CACL,SAAS,KAAK,SAAS,IAAI,eAAe,CAAC,cAAc,EAAE,SAAS,CAAC,CACtE,CAAC;QACJ,CAAC;QAED,SAAS,oBAAoB,CAC3B,IAAmB,EACnB,SAA0D,EAC1D,IAAyB;YAEzB,0FAA0F;YAC1F,IACE,CAAC,gCAAgC;gBACjC,sBAAsB,CAAC,SAAS,EAAE,IAAI,CAAC,EACvC;gBACA,gCAAgC,GAAG,IAAI,CAAC;gBACxC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,sBAAsB;oBACjC,IAAI,EAAE;wBACJ,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;qBAC/B;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,CAAC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,gBAAgB,CACvB,IAGmC;YAEnC,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,eAAe;YACtB,iBAAiB,CAAC,GAAG,EAAE,CAAC;QAC1B,CAAC;QAED,SAAS,+BAA+B,CAAC,IAAmB;YAC1D,IAAI,IAAI,KAAK,gCAAgC,EAAE;gBAC7C,gCAAgC,GAAG,IAAI,CAAC;aACzC;QACH,CAAC;QAED,SAAS,0BAA0B,CACjC,IAAmB;YAEnB,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACzE,CAAC;QAED,SAAS,sBAAsB,CAC7B,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACvC,CAAC,0BAA0B,CAAC,IAAI,CAAC;oBAC/B,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CACvC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,gBAAgB;YACrC,iBAAiB,EAAE,gBAAgB;YACnC,gEAAgE,EAC9D,gBAAgB;YAClB,8DAA8D,EAC5D,gBAAgB;YAClB,0BAA0B,EAAE,eAAe;YAC3C,wBAAwB,EAAE,eAAe;YACzC,qEAAqE,EACnE,eAAe;YACjB,mEAAmE,EACjE,eAAe;YACjB,eAAe,CAAC,IAA8B;gBAC5C,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;YACpD,CAAC;YACD,kCAAkC,EAAE,UAClC,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,QAA+B,CAAC;gBACtD,IAAI,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACvC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBACnD;YACH,CAAC;YACD,sBAAsB,EAAE,+BAA+B;YACvD,uBAAuB,EAAE,+BAA+B;SACzD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}