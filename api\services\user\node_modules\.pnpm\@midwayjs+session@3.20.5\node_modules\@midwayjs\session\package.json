{"name": "@midwayjs/session", "description": "midway session component for koa and faas", "version": "3.20.5", "main": "dist/index.js", "typings": "index.d.ts", "files": ["dist/**/*.js", "dist/**/*.d.ts", "index.d.ts"], "devDependencies": {"@midwayjs/core": "^3.20.4", "@midwayjs/mock": "^3.20.4"}, "dependencies": {"@midwayjs/cookies": "^1.3.0"}, "keywords": ["midway", "web", "koa", "faas", "session"], "author": "czy88840616 <<EMAIL>>", "license": "MIT", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit", "ci": "npm run test", "lint": "mwts check"}, "engines": {"node": ">=12"}, "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "gitHead": "7ce57281bd3ef5d18dc50b47ff9bffb8a27c071e"}