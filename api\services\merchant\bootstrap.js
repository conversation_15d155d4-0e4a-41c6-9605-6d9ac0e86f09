// 商户微服务启动文件
const { Bootstrap } = require('@midwayjs/bootstrap');

async function bootstrap() {
  try {
    console.log('🚀 启动商户微服务...');
    const app = await Bootstrap.run();
    console.log('✅ 商户微服务启动成功');
    console.log('📡 服务端口: 9802');
    console.log('💾 数据库: merchant_service_db');
    console.log('🌐 服务地址: http://127.0.0.1:9802');
  } catch (error) {
    console.error('❌ 商户微服务启动失败:', error);
    process.exit(1);
  }
}

bootstrap(); 