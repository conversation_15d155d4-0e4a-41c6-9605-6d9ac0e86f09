#!/bin/bash

# 商户微服务启动脚本

echo "正在启动商户微服务..."

# 检查MySQL是否可连接
echo "检查数据库连接..."
mysql -h127.0.0.1 -uroot -pwap.336101 -e "SELECT 1;" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "数据库连接成功"
else
    echo "数据库连接失败，请检查MySQL服务是否启动"
    exit 1
fi

# 初始化数据库（如果需要）
echo "检查数据库是否已初始化..."
DB_EXISTS=$(mysql -h127.0.0.1 -uroot -pwap.336101 -e "SHOW DATABASES LIKE 'merchant_service_db';" | grep merchant_service_db)
if [ -z "$DB_EXISTS" ]; then
    echo "数据库不存在，正在初始化..."
    mysql -h127.0.0.1 -uroot -pwap.336101 < init_merchant_data.sql
    echo "数据库初始化完成"
else
    echo "数据库已存在，跳过初始化"
fi

# 检查Redis是否可连接
echo "检查Redis连接..."
redis-cli -h 127.0.0.1 -p 6379 ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "Redis连接成功"
else
    echo "Redis连接失败，请检查Redis服务是否启动"
    exit 1
fi

# 安装依赖
echo "安装依赖包..."
npm install

# 启动服务
echo "启动商户微服务..."
npm run dev 