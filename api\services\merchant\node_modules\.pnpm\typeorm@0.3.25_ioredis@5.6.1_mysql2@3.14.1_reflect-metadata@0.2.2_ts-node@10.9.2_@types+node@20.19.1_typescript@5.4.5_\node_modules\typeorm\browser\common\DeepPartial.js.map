{"version": 3, "sources": ["../browser/src/common/DeepPartial.ts"], "names": [], "mappings": "", "file": "DeepPartial.js", "sourcesContent": ["/**\n * Same as Partial<T> but goes deeper and makes Partial<T> all its properties and sub-properties.\n */\nexport type DeepPartial<T> =\n    | T\n    | (T extends Array<infer U>\n          ? DeepPartial<U>[]\n          : T extends Map<infer K, infer V>\n          ? Map<DeepPartial<K>, DeepPartial<V>>\n          : T extends Set<infer M>\n          ? Set<DeepPartial<M>>\n          : T extends object\n          ? { [K in keyof T]?: DeepPartial<T[K]> }\n          : T)\n"], "sourceRoot": ".."}