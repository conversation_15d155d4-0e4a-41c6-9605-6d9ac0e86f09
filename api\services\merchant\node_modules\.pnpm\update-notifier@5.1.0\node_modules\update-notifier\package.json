{"name": "update-notifier", "version": "5.1.0", "description": "Update notifications for your CLI app", "license": "BSD-2-<PERSON><PERSON>", "repository": "yeoman/update-notifier", "funding": "https://github.com/yeoman/update-notifier?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava --timeout=20s --serial"}, "files": ["index.js", "check.js"], "keywords": ["npm", "update", "updater", "notify", "notifier", "check", "checker", "cli", "module", "package", "version"], "dependencies": {"boxen": "^5.0.0", "chalk": "^4.1.0", "configstore": "^5.0.1", "has-yarn": "^2.1.0", "import-lazy": "^2.1.0", "is-ci": "^2.0.0", "is-installed-globally": "^0.4.0", "is-npm": "^5.0.0", "is-yarn-global": "^0.3.0", "latest-version": "^5.1.0", "pupa": "^2.1.1", "semver": "^7.3.4", "semver-diff": "^3.1.1", "xdg-basedir": "^4.0.0"}, "devDependencies": {"ava": "^2.4.0", "clear-module": "^4.1.1", "fixture-stdout": "^0.2.1", "mock-require": "^3.0.3", "strip-ansi": "^6.0.0", "xo": "^0.37.1"}}