# 菜单文件选择器组件

## 功能说明

这个组件为商户管理系统添加了类似主后台的自动路由功能，允许用户：

1. **手动输入路径**：保持原有的手动输入功能
2. **选择文件**：点击"选择文件"按钮，从已存在的 Vue 页面文件中选择
3. **自动补全**：选择文件后自动填充路径和菜单名称

## 使用方法

### 1. 基本用法

```vue
<template>
  <FileSelector 
    v-model="routePath" 
    @change="handlePathChange"
  />
</template>

<script setup>
import FileSelector from '@/components/menu/FileSelector.vue'

const routePath = ref('')

const handlePathChange = (path) => {
  console.log('路径变化:', path)
}
</script>
```

### 2. 在表单中使用

```vue
<template>
  <ElForm :model="form">
    <FileSelector 
      v-model="form.path" 
      @change="handlePathChange"
    />
  </ElForm>
</template>
```

## 功能特性

### ✅ 已实现功能

- **文件扫描**：自动扫描 `src/views/` 目录下的所有 `.vue` 文件
- **搜索过滤**：支持按文件名和路径搜索
- **路径转换**：自动将文件路径转换为路由路径
- **名称映射**：根据路径自动生成中文菜单名称
- **双向绑定**：支持 v-model 双向数据绑定

### 🔄 文件路径转换规则

| 文件路径 | 路由路径 | 菜单名称 |
|---------|---------|---------|
| `views/merchant/settle-in.vue` | `/merchant/settle-in` | 商户入驻 |
| `views/dashboard/console.vue` | `/dashboard/console` | 控制台 |
| `views/system/user/index.vue` | `/system/user` | 用户管理 |

### 🎯 名称映射表

组件内置了常用路径的中文名称映射：

```typescript
const nameMap: Record<string, string> = {
  'settle-in': '商户入驻',
  'personal': '个人商户',
  'company': '企业商户',
  'category': '类目管理',
  'heritage-auth': '非遗认证',
  'dashboard': '仪表盘',
  'console': '控制台',
  'analysis': '数据分析',
  'user': '用户管理',
  'role': '角色管理',
  'menu': '菜单管理'
}
```

## 扩展功能

### 1. 添加新的名称映射

修改 `handlePathChange` 函数中的 `nameMap` 对象：

```typescript
const nameMap: Record<string, string> = {
  // 现有映射...
  'new-page': '新页面名称'
}
```

### 2. 自定义文件过滤

可以修改 `scanViewFiles` 函数来过滤特定文件：

```typescript
const scanViewFiles = () => {
  const modules = import.meta.glob('../../views/**/*.vue')
  const files: FileInfo[] = []

  for (const path in modules) {
    // 跳过特定文件
    if (path.includes('components') || path.includes('test')) {
      continue
    }
    
    // 处理文件...
  }
}
```

## 与主后台的对比

| 功能 | 主后台 | 商户管理系统（改进后） |
|------|--------|---------------------|
| 文件选择 | ✅ | ✅ |
| 自动扫描 | ✅ | ✅ |
| 快速创建 | ✅ | ❌（可扩展） |
| 权限生成 | ✅ | ❌（可扩展） |
| 手动输入 | ✅ | ✅ |

## 注意事项

1. **文件路径**：确保页面文件放在 `src/views/` 目录下
2. **命名规范**：建议使用 kebab-case 命名文件和目录
3. **性能考虑**：文件扫描在组件挂载时执行，大量文件可能影响性能
4. **路径格式**：生成的路由路径以 `/` 开头，符合 Vue Router 规范

## 未来扩展

可以考虑添加以下功能：

- **快速创建页面**：类似主后台的自动生成页面功能
- **API权限生成**：根据后端API自动生成权限配置
- **模板选择**：提供不同的页面模板
- **批量操作**：支持批量创建菜单
