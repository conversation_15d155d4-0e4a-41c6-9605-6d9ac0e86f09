"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommandCore = void 0;
// 内核，用于加载并按照生命周期执行插件逻辑
const path_1 = require("path");
const exec_1 = require("./utils/exec");
const fs_1 = require("fs");
const light_spinner_1 = require("light-spinner");
const commandCore_1 = require("./interface/commandCore");
const errorMap_1 = require("./errorMap");
const npm_1 = require("./npm");
// npm:providerName:pkgName
const RegProviderNpm = /^npm:([\w]*):(.*)$/i;
// local:providerName:pkgPath
const RegProviderLocal = /^local:([\w]*):(.*)$/i;
class CommandCore {
    constructor(options) {
        this.instances = []; // 插件实例列表
        this.commands = {}; // 命令列表
        this.hooks = {}; // 命令生命周期
        this.providers = {};
        this.npmPlugin = []; // npm类型插件
        this.execId = Math.ceil(Math.random() * 1000); // 当前执行ID，用以在多次执行时进行区分
        this.userLifecycle = {}; // 用户自定义生命周期钩子，便于项目级扩展
        this.stopLifecycles = [];
        this.loadNpm = npm_1.loadNpm.bind(null, this);
        this.timeTicks = [];
        this.outputLevel = commandCore_1.CLIOutputLevel.All;
        this.store = new Map();
        this.options = options;
        this.cwd = this.options.cwd || process.cwd();
        if (!this.options.options) {
            this.options.options = {};
        }
        if (!this.options.config) {
            this.options.config = {};
        }
        if (!this.options.config.servicePath) {
            this.options.config.servicePath = this.cwd;
        }
        if (this.options.outputLevel !== undefined) {
            this.outputLevel = +this.options.outputLevel;
        }
        this.loadNpm = npm_1.loadNpm.bind(null, this);
        this.coreInstance = this.getCoreInstance();
        if (!this.options.disableAutoLoad) {
            this.autoLoadPlugins();
        }
    }
    // 添加插件
    addPlugin(Plugin) {
        var _a, _b;
        if (typeof Plugin === 'object') {
            return Object.keys(Plugin).forEach(key => {
                this.addPlugin(Plugin[key]);
            });
        }
        const provider = ((_b = (_a = this.options.service) === null || _a === void 0 ? void 0 : _a.provider) === null || _b === void 0 ? void 0 : _b.name) || this.options.provider;
        this.debug('Current Provider', provider);
        const coreInstance = this.coreInstance;
        let pluginProvider = '';
        // 支持加载npm 或 本地插件（绝对地址）
        if (typeof Plugin === 'string') {
            if (RegProviderNpm.test(Plugin)) {
                const npmProviderMatch = RegProviderNpm.exec(Plugin);
                pluginProvider = npmProviderMatch[1];
                if (pluginProvider && pluginProvider !== provider) {
                    return;
                }
                this.npmPlugin.push(npmProviderMatch[2]);
            }
            else if (RegProviderLocal.test(Plugin)) {
                const localProviderMatch = RegProviderLocal.exec(Plugin);
                pluginProvider = localProviderMatch[1];
                if (pluginProvider && pluginProvider !== provider) {
                    return;
                }
                this.loadLocalPlugin(localProviderMatch[2]);
            }
            else {
                this.error('pluginType', Plugin);
            }
            return;
        }
        // 非class不加载
        if (typeof Plugin !== 'function') {
            return;
        }
        const instance = new Plugin(coreInstance, this.options.options);
        if (instance.provider) {
            if (typeof instance.provider === 'string') {
                pluginProvider = instance.provider;
            }
            else if (Array.isArray(instance.provider)) {
                // provider is list
                if (instance.provider.indexOf(provider) === -1) {
                    this.debug('Code skip load plugin', Plugin === null || Plugin === void 0 ? void 0 : Plugin.name, '[provide not match]');
                    return;
                }
            }
            else {
                pluginProvider = instance.provider.constructor.getProviderName();
            }
        }
        // 不支持的provider
        if (pluginProvider && pluginProvider !== provider) {
            this.debug('Code skip load plugin', Plugin === null || Plugin === void 0 ? void 0 : Plugin.name, '[provide not match]');
            return;
        }
        // 避免多次加载
        if (this.instances.length) {
            for (const plugin of this.instances) {
                if (plugin instanceof Plugin) {
                    return;
                }
            }
        }
        this.debug('Core Load plugin', Plugin.name);
        this.loadCommands(instance, this.commands, instance.commands);
        this.loadHooks(instance.hooks);
        this.instances.push(instance);
    }
    /*
      commandsArray 为多级命令，如 [invoke, local] 则执行 invoke的二级子命令 local
      allowEntryPoints 为是否可以调用 entryPoints
      */
    async invoke(commandsArray, allowEntryPoints, options) {
        if (commandsArray == null) {
            commandsArray = this.options.commands;
        }
        if (!Array.isArray(commandsArray)) {
            commandsArray = [].concat(commandsArray || []);
        }
        if (options) {
            Object.assign(this.options.options, options);
        }
        const displayHelp = this.options.options.h || this.options.options.help;
        if (!commandsArray.length && displayHelp) {
            return this.displayHelp();
        }
        const commandInfo = this.getCommand(commandsArray, allowEntryPoints);
        const lifecycleEvents = this.loadLifecycle(commandInfo.commandName, commandInfo.command.lifecycleEvents, commandInfo.parentCommandList);
        if (this.options.point) {
            this.options.point('invoke', commandsArray, commandInfo, this);
        }
        // 展示帮助
        if (displayHelp) {
            return this.displayHelp(commandsArray, commandInfo.usage, commandInfo.command);
        }
        await this.execLifecycle(lifecycleEvents);
    }
    async execLifecycle(lifecycleEvents) {
        for (const lifecycle of lifecycleEvents) {
            const hooks = this.hooks[lifecycle] || [];
            if (this.userLifecycle && this.userLifecycle[lifecycle]) {
                const tickUser = this.tickTime(`user:${lifecycle}`, {
                    hooks: this.userLifecycle[lifecycle].length,
                });
                const userCmd = this.userLifecycle[lifecycle];
                this.debug('User Lifecycle', lifecycle);
                let spin;
                const slience = this.outputLevel < commandCore_1.CLIOutputLevel.All;
                if (!slience) {
                    spin = new light_spinner_1.default({ text: `Executing: ${userCmd}` });
                    spin.start();
                }
                try {
                    await (0, exec_1.exec)({
                        cmd: userCmd,
                        baseDir: this.cwd,
                        slience: slience,
                        log: this.getLog().log,
                    });
                }
                catch (e) {
                    spin && spin.stop();
                    this.debug('User Lifecycle Hook Error', userCmd, e);
                    tickUser(e);
                    throw e;
                }
                tickUser();
                spin && spin.stop();
            }
            const tick = this.tickTime(lifecycle, { hooks: hooks.length });
            this.debug('Core Lifecycle', lifecycle, hooks.length);
            for (const hook of hooks) {
                try {
                    await hook();
                }
                catch (e) {
                    this.debug('Core Lifecycle Hook Error', e);
                    tick(e);
                    throw e;
                }
            }
            tick();
        }
    }
    tickTime(type, info = {}) {
        const start = Date.now();
        const time = {
            type,
            usage: 0,
            ...info,
        };
        this.timeTicks.push(time);
        return (error) => {
            time.usage = Date.now() - start;
            if (error) {
                time.error =
                    typeof error === 'string' ? error : (error === null || error === void 0 ? void 0 : error.message) || 'unknown';
            }
        };
    }
    // resume stop licycle execute
    async resume(options) {
        if (options) {
            Object.assign(this.options.options, options);
        }
        await this.execLifecycle(this.stopLifecycles);
    }
    // spawn('aliyun:invoke')
    async spawn(commandsArray, options) {
        let commands = [];
        if (typeof commandsArray === 'string') {
            commands = commandsArray.split(':');
        }
        else {
            commands = commandsArray;
        }
        await this.invoke(commands, true, options);
    }
    getCommands() {
        return this.commands;
    }
    async ready() {
        await this.asyncInit();
        await this.loadNpmPlugins();
        await this.loadUserLifecycleExtends();
    }
    autoLoadPlugins() {
        if (this.options.service && this.options.service.plugins) {
            this.options.service.plugins.forEach(plugin => {
                this.debug('Auto Plugin', plugin);
                this.addPlugin(plugin);
            });
        }
    }
    getTimeTicks() {
        return this.timeTicks;
    }
    // 获取核心instance
    getCoreInstance() {
        const { provider, service, config, extensions, commands } = this.options;
        const serviceData = service || {};
        if (!serviceData.provider) {
            serviceData.provider = { name: provider };
        }
        if (!serviceData.service) {
            serviceData.service = service;
        }
        if (provider) {
            serviceData.provider.name = provider;
        }
        return {
            ...(extensions || {}),
            classes: {
                Error,
            },
            store: this.store,
            cli: this.getLog(),
            config: config || {},
            getProvider: this.getProvider.bind(this),
            invoke: this.invoke.bind(this),
            spawn: this.spawn.bind(this),
            debug: this.debug.bind(this),
            processedInput: {
                options: {},
                commands: commands || [],
            },
            coreOptions: this.options,
            cwd: this.cwd,
            pluginManager: this,
            setProvider: this.setProvider.bind(this),
            service: serviceData,
            addPlugin: this.addPlugin.bind(this),
        };
    }
    // 设置 provider
    setProvider(providerName, providerInstance) {
        this.providers[providerName] = providerInstance;
    }
    // 获取 provider
    getProvider(providerName) {
        return this.providers[providerName];
    }
    // 加载命令,支持子命令
    loadCommands(instance, commandsMap, commands, parentCommandList) {
        if (!commands) {
            return;
        }
        Object.keys(commands).forEach((command) => {
            const commandInstance = commands[command];
            if (!commandsMap[command]) {
                commandsMap[command] = {
                    usage: '',
                    type: commandInstance.type || 'command',
                    lifecycleEvents: [],
                    rank: -1,
                    options: {},
                    passingCommand: commandInstance.passingCommand,
                    origin: [],
                    alias: commandInstance.alias,
                    commands: {},
                };
            }
            const currentCommand = commandsMap[command];
            commandsMap[command].origin.push(commandInstance);
            const currentRank = commandInstance.rank || 0;
            // 如果当前插件的rank比当前命令的rank大，则会覆盖
            if (currentRank > currentCommand.rank) {
                currentCommand.rank = currentRank;
                currentCommand.lifecycleEvents = commandInstance.lifecycleEvents;
                if (commandInstance.usage) {
                    currentCommand.usage = commandInstance.usage;
                }
                commandsMap[command].alias = commandInstance.alias || '';
            }
            // 加载子命令
            if (commandInstance.commands) {
                this.loadCommands(instance, commandsMap[command].commands, commandInstance.commands, (parentCommandList || []).concat(command));
            }
            // 合并 options
            currentCommand.options = Object.assign(currentCommand.options, commandInstance.options);
        });
    }
    // 加载hooks
    loadHooks(hooks) {
        if (!hooks) {
            return;
        }
        for (const hookName in hooks) {
            if (!this.hooks[hookName]) {
                this.hooks[hookName] = [];
            }
            this.hooks[hookName].push(hooks[hookName]);
        }
    }
    loadLifecycle(command, lifecycleEvents, parentCommandList) {
        const allLifecycles = [];
        let isStop = false;
        const { stopLifecycle } = this.options;
        const parentCommand = parentCommandList && parentCommandList.length
            ? `${parentCommandList.join(':')}:`
            : '';
        if (lifecycleEvents) {
            for (const life of lifecycleEvents) {
                const liftCycles = isStop ? this.stopLifecycles : allLifecycles;
                const tmpLife = `${parentCommand}${command}:${life}`;
                liftCycles.push(`before:${tmpLife}`);
                liftCycles.push(tmpLife);
                liftCycles.push(`after:${tmpLife}`);
                if (stopLifecycle === tmpLife) {
                    isStop = true;
                }
            }
        }
        return allLifecycles;
    }
    getCommand(commandsArray, allowEntryPoints) {
        var _a;
        let command = '';
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        let cmdObj = this;
        const commandPath = [];
        const parentCommandList = [];
        const usage = {};
        // command 透传
        for (command of commandsArray) {
            if (commandPath.length) {
                parentCommandList.push(commandPath[commandPath.length - 1]);
            }
            const cmdInfo = (_a = cmdObj === null || cmdObj === void 0 ? void 0 : cmdObj.commands) === null || _a === void 0 ? void 0 : _a[command];
            if (!cmdInfo) {
                // support command alias alias
                const aliasCommand = Object.keys((cmdObj === null || cmdObj === void 0 ? void 0 : cmdObj.commands) || {}).find(commandName => {
                    return cmdObj.commands[commandName].alias === command;
                });
                if (!aliasCommand) {
                    this.error('commandNotFound', { command, commandPath });
                }
                else {
                    command = aliasCommand;
                }
            }
            commandPath.push(command);
            cmdObj = cmdObj.commands[command];
            if (cmdObj) {
                if (cmdObj.options) {
                    this.commandOptions(cmdObj.options, usage);
                }
                if (cmdObj === null || cmdObj === void 0 ? void 0 : cmdObj.passingCommand) {
                    break;
                }
            }
        }
        if (!cmdObj) {
            this.error('commandNotFound', { command, commandPath });
        }
        if ((cmdObj === null || cmdObj === void 0 ? void 0 : cmdObj.type) === 'entrypoint' && !allowEntryPoints) {
            this.error('commandIsEntrypoint', { command, commandPath });
        }
        return {
            commandName: command,
            command: cmdObj,
            usage,
            parentCommandList,
        };
    }
    // 加载本地插件
    loadLocalPlugin(localPath) {
        try {
            if (this.options.config &&
                this.options.config.servicePath &&
                /^\./.test(localPath)) {
                localPath = (0, path_1.resolve)(this.options.config.servicePath, localPath);
            }
            this.debug('Core Local Plugin', localPath);
            const plugin = require(localPath);
            this.addPlugin(plugin);
        }
        catch (e) {
            this.error('localPlugin', { path: localPath, err: e });
        }
    }
    // 加载npm包插件
    async loadNpmPlugins() {
        for (const npmPath of this.npmPlugin) {
            await this.loadNpm(npmPath, this.options.options.npm || this.options.npm);
        }
    }
    // 插件的异步初始化
    async asyncInit() {
        for (const instance of this.instances) {
            if (instance.asyncInit) {
                await instance.asyncInit();
            }
        }
    }
    // 获取用户的生命周期扩展
    async loadUserLifecycleExtends() {
        const pkgJsonFile = (0, path_1.resolve)(this.cwd, 'package.json');
        if (!(0, fs_1.existsSync)(pkgJsonFile)) {
            return;
        }
        try {
            const pkgJson = JSON.parse((0, fs_1.readFileSync)(pkgJsonFile).toString());
            if (pkgJson &&
                pkgJson['midway-integration'] &&
                pkgJson['midway-integration'].lifecycle) {
                this.userLifecycle = pkgJson['midway-integration'].lifecycle;
            }
        }
        catch (e) {
            return;
        }
    }
    commandOptions(commandOptions, usage) {
        for (const option in commandOptions) {
            const optionInfo = commandOptions[option];
            usage[option] = optionInfo;
            if (optionInfo === null || optionInfo === void 0 ? void 0 : optionInfo.shortcut) {
                if (this.options.options[optionInfo.shortcut]) {
                    this.options.options[option] =
                        this.options.options[optionInfo.shortcut];
                }
            }
            this.coreInstance.processedInput.options[option] =
                this.options.options[option];
        }
    }
    displayHelp(commandsArray, usage, commandInfo) {
        if (this.options.displayUsage) {
            this.options.displayUsage(commandsArray || [], usage || {}, this, commandInfo);
        }
    }
    getLog() {
        const log = {
            ...console,
            ...this.options.log,
        };
        if (this.outputLevel < commandCore_1.CLIOutputLevel.All) {
            log.debug = () => { };
        }
        if (this.outputLevel < commandCore_1.CLIOutputLevel.Info) {
            log.log = () => { };
        }
        if (this.outputLevel < commandCore_1.CLIOutputLevel.Warn) {
            log.warn = () => { };
        }
        if (this.outputLevel < commandCore_1.CLIOutputLevel.Error) {
            log.error = () => { };
        }
        return log;
    }
    error(type, info) {
        const errObj = (0, errorMap_1.default)(type, info);
        const { cli } = this.coreInstance;
        if (cli && cli.error) {
            cli.error(errObj);
        }
        throw new Error(errObj.message);
    }
    debug(...args) {
        const verbose = this.options.options.V ||
            this.options.options.verbose ||
            process.env.MIDWAY_FAAS_VERBOSE;
        if (!verbose || this.outputLevel < commandCore_1.CLIOutputLevel.Info) {
            return;
        }
        const now = Date.now();
        if (!this.preDebugTime) {
            this.preDebugTime = now;
        }
        const { type, path, line } = this.getStackTrace();
        let stack = '';
        if (type) {
            stack = `(${type}:${path}:${line})`;
        }
        const diffTime = Number((now - this.preDebugTime) / 1000).toFixed(2);
        this.preDebugTime = now;
        this.getLog().debug('[Verbose]', this.execId, `+${diffTime}s`, ...args, stack);
    }
    getStackTrace() {
        if (!Error.captureStackTrace) {
            return {};
        }
        const obj = {};
        Error.captureStackTrace(obj, this.getStackTrace);
        if (!obj.stack || !obj.stack.split) {
            return {};
        }
        const stackStr = obj.stack.split('\n');
        if (!stackStr || !stackStr[2]) {
            return {};
        }
        const matchReg = /(?:-plugin-|\/command-core)(\w+)\/(.*?):(\d+):\d+/;
        if (!matchReg.test(stackStr[2])) {
            return {};
        }
        const matchResult = matchReg.exec(stackStr[2]);
        return {
            type: matchResult[1],
            path: matchResult[2],
            line: matchResult[3],
        };
    }
}
exports.CommandCore = CommandCore;
//# sourceMappingURL=core.js.map