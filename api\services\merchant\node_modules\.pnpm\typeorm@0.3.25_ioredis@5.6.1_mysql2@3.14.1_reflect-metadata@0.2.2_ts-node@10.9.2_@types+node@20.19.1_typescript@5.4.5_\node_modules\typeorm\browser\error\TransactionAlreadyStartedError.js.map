{"version": 3, "sources": ["../browser/src/error/TransactionAlreadyStartedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,8BAA+B,SAAQ,YAAY;IAC5D;QACI,KAAK,CACD,6GAA6G,CAChH,CAAA;IACL,CAAC;CACJ", "file": "TransactionAlreadyStartedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when transaction is already started and user tries to run it again.\n */\nexport class TransactionAlreadyStartedError extends TypeORMError {\n    constructor() {\n        super(\n            `Transaction already started for the given connection, commit current transaction before starting a new one.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}