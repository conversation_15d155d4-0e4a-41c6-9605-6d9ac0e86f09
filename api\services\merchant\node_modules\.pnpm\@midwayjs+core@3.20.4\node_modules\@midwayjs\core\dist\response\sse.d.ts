/// <reference types="node" />
import { Transform } from 'stream';
import { IMidwayContext, ServerSendEventStreamOptions } from '../interface';
interface MessageEvent {
    data?: string | object;
    event?: string;
    id?: string;
    retry?: number;
}
export declare class ServerSendEventStream<CTX extends IMidwayContext> extends Transform {
    private readonly ctx;
    private isActive;
    private readonly closeEvent;
    private options;
    constructor(ctx: any, options?: ServerSendEventStreamOptions<CTX>);
    _transform(chunk: any, encoding: any, callback: any): void;
    sendError(error: Error): void;
    sendEnd(message?: MessageEvent): void;
    send(message: MessageEvent): void;
    private handleClose;
}
export {};
//# sourceMappingURL=sse.d.ts.map