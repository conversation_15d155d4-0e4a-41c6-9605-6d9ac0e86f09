"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolValidateException = void 0;
const global_1 = require("../constant/global");
const base_1 = require("./base");
/**
 * 校验异常
 */
class CoolValidateException extends base_1.BaseException {
    constructor(message, statusCode) {
        const { RESCODE, RESMESSAGE } = global_1.GlobalConfig.getInstance();
        super('CoolValidateException', RESCODE.VALIDATEFAIL, message ? message : RESMESSAGE.VALIDATEFAIL, statusCode);
    }
}
exports.CoolValidateException = CoolValidateException;
