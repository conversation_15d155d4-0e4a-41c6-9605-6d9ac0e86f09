{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../src/util.ts"], "names": [], "mappings": ";;;AAAA,yBAAyB;AACzB,6BAA6B;AAC7B,iCAAiC;AACjC,+BAAiC;AACjC,2BAA2B;AAC3B,qDAAqD;AACrD,+BAA+B;AAElB,QAAA,SAAS,GAAG,IAAA,gBAAS,EAAC,EAAE,CAAC,QAAQ,CAAc,CAAC;AAChD,QAAA,OAAO,GAAG,IAAA,gBAAS,EAAC,MAAM,CAAC,CAAC;AAC5B,QAAA,gBAAgB,GAAG,IAAA,gBAAS,EAAC,eAAe,CAAC,CAAC;AAC9C,QAAA,IAAI,GAAG,IAAA,gBAAS,EAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAYhC,KAAK,UAAU,SAAS,CAAC,QAAgB;IAC9C,MAAM,QAAQ,GAAG,MAAM,IAAA,iBAAS,EAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACnD,OAAO,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,CAAC;AAHD,8BAGC;AAMD,SAAgB,GAAG;IACjB,WAAW;AACb,CAAC;AAFD,kBAEC;AAED,SAAgB,SAAS,CAAC,GAAY;IACpC,IAAI,GAAG,IAAI,IAAI,EAAE;QACf,OAAO,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC;KAC9B;IACD,IAAI,GAAG,YAAY,KAAK,EAAE;QACxB,OAAO,GAAG,CAAC;KACZ;IACD,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;AAC7B,CAAC;AARD,8BAQC;AAED;;;;;;;;GAQG;AACH,KAAK,UAAU,OAAO,CACpB,QAAgB,EAChB,eAAsC,EACtC,SAAsB,EACtB,UAAkB;IAElB,eAAe,GAAG,eAAe,IAAI,iBAAS,CAAC;IAE/C,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;IAE9C,0EAA0E;IAC1E,iBAAiB;IACjB,IAAI,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;QAC3B,MAAM,IAAI,KAAK,CAAC,yBAAyB,QAAQ,EAAE,CAAC,CAAC;KACtD;IACD,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxB,IAAI;QACF,MAAM,IAAI,GAAG,MAAM,eAAe,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;QACrD,IAAI,QAAoB,CAAC;QACzB,IAAI;YACF,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAC9B;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;YAC3B,GAAG,CAAC,OAAO,GAAG,mBAAmB,QAAQ,MAAM,GAAG,CAAC,OAAO,EAAE,CAAC;YAC7D,MAAM,GAAG,CAAC;SACX;QAED,IAAI,OAAO,QAAQ,CAAC,OAAO,KAAK,QAAQ,EAAE;YACxC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAC5B,QAAQ,CAAC,OAAO,EAChB,eAAe,EACf,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CACvB,CAAC;YACF,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAChD;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC1C,KAAK,MAAM,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACrC,MAAM,QAAQ,GAAG,MAAM,OAAO,CAC5B,MAAM,EACN,eAAe,EACf,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CACvB,CAAC;gBACF,QAAQ,GAAG,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;aAChD;SACF;QAED,OAAO,QAAQ,CAAC;KACjB;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAC3B,GAAG,CAAC,OAAO,GAAG,UAAU,QAAQ,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;QACnD,MAAM,GAAG,CAAC;KACX;AACH,CAAC;AAED;;;;GAIG;AACH,SAAS,eAAe,CAAC,IAAgB,EAAE,SAAqB;IAC9D,MAAM,MAAM,GAAe,EAAE,eAAe,EAAE,EAAE,EAAE,CAAC;IAEnD,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;IACvC,MAAM,CAAC,MAAM,CACX,MAAM,CAAC,eAAe,EACtB,IAAI,CAAC,eAAe,EACpB,SAAS,CAAC,eAAe,CAC1B,CAAC;IACF,OAAO,MAAM,CAAC,OAAO,CAAC;IACtB,OAAO,MAAM,CAAC;AAChB,CAAC;AAaD;;;;GAIG;AACH,SAAgB,UAAU,CAAC,UAAU,GAAG,EAAE,CAAC,UAAU;IACnD,IAAI,UAAU,CAAC,mBAAmB,CAAC,EAAE;QACnC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,UAAU,CAAC,WAAW,CAAC,CAAC;AACjC,CAAC;AALD,gCAKC;AAED,SAAgB,oBAAoB,CAAC,UAAoB;IACvD,OAAO,CACL,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAC7E,CAAC;AACJ,CAAC;AAJD,oDAIC;AAED;;;;;;GAMG;AACI,KAAK,UAAU,WAAW,CAC/B,OAAe,EACf,eAA2B;IAE3B,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAC;IAClC,OAAO,OAAO,CAAC,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACrE,CAAC;AAND,kCAMC;AAED,SAAgB,QAAQ,CAAC,QAAgB;IACvC,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IAClD,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC5B;IAAC,OAAO,GAAG,EAAE;QACZ,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM,IAAI,KAAK,CACb,8BAA8B,OAAO,UAAU,GAAG,CAAC,OAAO,EAAE,CAC7D,CAAC;KACH;AACH,CAAC;AAVD,4BAUC"}