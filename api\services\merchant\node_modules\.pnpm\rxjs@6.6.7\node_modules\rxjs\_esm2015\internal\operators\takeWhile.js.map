{"version": 3, "file": "takeWhile.js", "sources": ["../../../src/internal/operators/takeWhile.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAmD3C,MAAM,UAAU,SAAS,CACrB,SAA+C,EAC/C,SAAS,GAAG,KAAK;IACnB,OAAO,CAAC,MAAqB,EAAE,EAAE,CACtB,MAAM,CAAC,IAAI,CAAC,IAAI,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;AACtE,CAAC;AAED,MAAM,iBAAiB;IACrB,YACY,SAA+C,EAC/C,SAAkB;QADlB,cAAS,GAAT,SAAS,CAAsC;QAC/C,cAAS,GAAT,SAAS,CAAS;IAAG,CAAC;IAElC,IAAI,CAAC,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CACnB,IAAI,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAC3E,CAAC;CACF;AAOD,MAAM,mBAAuB,SAAQ,UAAa;IAGhD,YACI,WAA0B,EAClB,SAA+C,EAC/C,SAAkB;QAC5B,KAAK,CAAC,WAAW,CAAC,CAAC;QAFT,cAAS,GAAT,SAAS,CAAsC;QAC/C,cAAS,GAAT,SAAS,CAAS;QALtB,UAAK,GAAW,CAAC,CAAC;IAO1B,CAAC;IAES,KAAK,CAAC,KAAQ;QACtB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,MAAe,CAAC;QACpB,IAAI;YACF,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SAC9C;QAAC,OAAO,GAAG,EAAE;YACZ,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACvB,OAAO;SACR;QACD,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAEO,cAAc,CAAC,KAAQ,EAAE,eAAwB;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,OAAO,CAAC,eAAe,CAAC,EAAE;YAC5B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SACzB;aAAM;YACL,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACzB;YACD,WAAW,CAAC,QAAQ,EAAE,CAAC;SACxB;IACH,CAAC;CACF"}