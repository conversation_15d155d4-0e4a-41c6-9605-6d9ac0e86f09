import { HealthResults, IMidwayContainer } from '../interface';
import { MidwayConfigService } from './configService';
export declare class MidwayHealthService {
    protected configService: MidwayConfigService;
    protected applicationContext: IMidwayContainer;
    private healthCheckTimeout;
    private healthCheckMethods;
    init(lifeCycleInstanceList: Array<{
        target: any;
        namespace: string;
        instance?: any;
    }>): Promise<void>;
    getStatus(): Promise<HealthResults>;
    setCheckTimeout(timeout: number): void;
}
//# sourceMappingURL=healthService.d.ts.map