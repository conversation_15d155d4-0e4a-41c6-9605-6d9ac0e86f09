"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Schedule = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Schedule(scheduleOpts) {
    return function (target) {
        (0, __1.saveModule)(__1.SCHEDULE_KEY, target);
        (0, __1.saveClassMetadata)(__1.SCHEDULE_KEY, scheduleOpts, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Request)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Schedule = Schedule;
//# sourceMappingURL=schedule.js.map