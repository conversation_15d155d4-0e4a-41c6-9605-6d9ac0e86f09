"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpServerResponse = void 0;
const fs_1 = require("fs");
const base_1 = require("./base");
const sse_1 = require("./sse");
const stream_1 = require("./stream");
const path_1 = require("path");
class HttpServerResponse extends base_1.ServerResponse {
    constructor(ctx) {
        super(ctx);
    }
    status(code) {
        this.ctx.res.statusCode = code;
        return this;
    }
    header(key, value) {
        this.ctx.res.setHeader(key, value);
        return this;
    }
    headers(headers) {
        if (this.ctx.res.setHeaders) {
            this.ctx.res.setHeaders(new Map(Object.entries(headers)));
        }
        else {
            for (const key in headers) {
                this.header(key, headers[key]);
            }
        }
        return this;
    }
    json(data) {
        this.header('Content-Type', 'application/json');
        return Object.getPrototypeOf(this).constructor.JSON_TPL(data, this.isSuccess, this.ctx);
    }
    text(data) {
        this.header('Content-Type', 'text/plain');
        return Object.getPrototypeOf(this).constructor.TEXT_TPL(data, this.isSuccess, this.ctx);
    }
    file(filePath, mimeType) {
        this.header('Content-Type', mimeType || 'application/octet-stream');
        this.header('Content-Disposition', `attachment; filename=${(0, path_1.basename)(filePath)}`);
        return Object.getPrototypeOf(this).constructor.FILE_TPL(typeof filePath === 'string' ? (0, fs_1.createReadStream)(filePath) : filePath, this.isSuccess, this.ctx);
    }
    blob(data, mimeType) {
        this.header('Content-Type', mimeType || 'application/octet-stream');
        return Object.getPrototypeOf(this).constructor.BLOB_TPL(data, this.isSuccess, this.ctx);
    }
    html(data) {
        this.header('Content-Type', 'text/html');
        return Object.getPrototypeOf(this).constructor.HTML_TPL(data, this.isSuccess, this.ctx);
    }
    redirect(url, status = 302) {
        this.status(status);
        if (this.ctx.redirect) {
            return this.ctx.redirect(url);
        }
        else if (this.ctx.res.redirect) {
            return this.ctx.res.redirect(url);
        }
        else {
            this.header('Location', url);
        }
    }
    sse(options = {}) {
        return new sse_1.ServerSendEventStream(this.ctx, {
            tpl: Object.getPrototypeOf(this).constructor.SSE_TPL,
            ...options,
        });
    }
    stream(options = {}) {
        return new stream_1.HttpStreamResponse(this.ctx, {
            tpl: Object.getPrototypeOf(this).constructor.STREAM_TPL,
            ...options,
        });
    }
}
exports.HttpServerResponse = HttpServerResponse;
HttpServerResponse.FILE_TPL = (data, isSuccess, ctx) => {
    return data;
};
HttpServerResponse.SSE_TPL = (data, ctx) => {
    return data;
};
HttpServerResponse.STREAM_TPL = (data, ctx) => {
    return data;
};
HttpServerResponse.HTML_TPL = (data, isSuccess, ctx) => {
    return data;
};
//# sourceMappingURL=http.js.map