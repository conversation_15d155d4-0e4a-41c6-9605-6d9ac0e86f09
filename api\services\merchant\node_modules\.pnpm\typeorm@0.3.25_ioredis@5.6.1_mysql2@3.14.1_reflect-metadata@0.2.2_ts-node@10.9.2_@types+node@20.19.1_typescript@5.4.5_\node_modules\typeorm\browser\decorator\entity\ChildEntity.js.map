{"version": 3, "sources": ["../browser/src/decorator/entity/ChildEntity.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAA;AAItD;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,kBAAwB;IAChD,OAAO,UAAU,MAAgB;QAC7B,4BAA4B;QAC5B,sBAAsB,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YACjC,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,cAAc;SACF,CAAC,CAAA;QAEvB,kDAAkD;QAClD,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE,CAAC;YAC5C,sBAAsB,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC;gBAC9C,MAAM,EAAE,MAAM;gBACd,KAAK,EAAE,kBAAkB;aACM,CAAC,CAAA;QACxC,CAAC;IACL,CAAC,CAAA;AACL,CAAC", "file": "ChildEntity.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { TableMetadataArgs } from \"../../metadata-args/TableMetadataArgs\"\nimport { DiscriminatorValueMetadataArgs } from \"../../metadata-args/DiscriminatorValueMetadataArgs\"\n\n/**\n * Special type of the table used in the single-table inherited tables.\n */\nexport function ChildEntity(discriminatorValue?: any): ClassDecorator {\n    return function (target: Function) {\n        // register a table metadata\n        getMetadataArgsStorage().tables.push({\n            target: target,\n            type: \"entity-child\",\n        } as TableMetadataArgs)\n\n        // register discriminator value if it was provided\n        if (typeof discriminatorValue !== \"undefined\") {\n            getMetadataArgsStorage().discriminatorValues.push({\n                target: target,\n                value: discriminatorValue,\n            } as DiscriminatorValueMetadataArgs)\n        }\n    }\n}\n"], "sourceRoot": "../.."}