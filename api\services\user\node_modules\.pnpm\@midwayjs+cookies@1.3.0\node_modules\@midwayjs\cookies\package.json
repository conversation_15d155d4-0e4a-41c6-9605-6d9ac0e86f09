{"name": "@midwayjs/cookies", "description": "midway cookies", "version": "1.3.0", "main": "dist/index", "typings": "dist/index.d.ts", "files": ["dist/**/*.js", "dist/**/*.d.ts"], "dependencies": {"scmp": "^2.1.0", "should-send-same-site-none": "^2.0.5"}, "devDependencies": {"@types/jest": "^26.0.10", "@types/node": "^17.0.0", "jest": "^26.6.0", "mwts": "^1.0.5", "ts-jest": "^26.5.5", "ts-node": "^10.0.0", "typescript": "^4.1.0"}, "keywords": ["midway", "cookies"], "author": "czy88840616 <<EMAIL>>", "license": "MIT", "scripts": {"build": "rm -rf dist && tsc", "test": "jest", "cov": "jest --coverage", "ci": "npm run test", "lint": "mwts check", "lint:fix": "mwts fix"}, "engines": {"node": ">=10"}, "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}}