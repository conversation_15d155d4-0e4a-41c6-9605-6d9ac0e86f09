{"name": "defer-to-connect", "version": "1.1.3", "description": "The safe way to handle the `connect` socket event", "main": "dist", "files": ["dist"], "scripts": {"build": "del-cli dist && tsc", "prepublishOnly": "npm run build", "test": "xo && nyc ava", "coveralls": "nyc report --reporter=text-lcov | coveralls"}, "keywords": ["socket", "connect", "event"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/szmarczak/defer-to-connect.git"}, "bugs": {"url": "https://github.com/szmarczak/defer-to-connect/issues"}, "homepage": "https://github.com/szmarczak/defer-to-connect#readme", "xo": {"extends": "xo-typescript", "extensions": ["ts"], "rules": {"ava/no-ignored-test-files": "off"}}, "devDependencies": {"@sindresorhus/tsconfig": "^0.5.0", "@types/node": "^12.12.4", "@typescript-eslint/eslint-plugin": "^1.11.0", "@typescript-eslint/parser": "^1.11.0", "ava": "^2.1.0", "coveralls": "^3.0.7", "create-cert": "^1.0.6", "del-cli": "^3.0.0", "eslint-config-xo-typescript": "^0.15.0", "nyc": "^14.0.0", "p-event": "^4.1.0", "ts-node": "^8.1.0", "typescript": "^3.6.4", "xo": "^0.25.3"}, "nyc": {"extension": [".ts"]}, "ava": {"babel": false, "compileEnhancements": false, "extensions": ["ts"], "require": ["ts-node/register"], "files": ["!dist/tests/test.d.ts"]}, "types": "dist"}