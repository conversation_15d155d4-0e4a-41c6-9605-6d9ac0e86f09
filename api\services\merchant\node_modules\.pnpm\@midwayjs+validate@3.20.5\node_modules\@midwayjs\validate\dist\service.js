"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSchema = exports.ValidateService = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const constants_1 = require("./constants");
const Joi = require("joi");
const i18n_1 = require("@midwayjs/i18n");
const error_1 = require("./error");
let ValidateService = class ValidateService {
    constructor() {
        this.messages = {};
    }
    async init() {
        const locales = Object.keys(DefaultConfig.i18n.localeTable);
        locales.forEach(locale => {
            this.messages[(0, i18n_1.formatLocale)(locale)] = Object.fromEntries(this.i18nService.getLocaleMapping(locale, 'validate'));
        });
    }
    validate(ClzType, value, options = {}) {
        const objectSchema = this.getSchema(ClzType);
        return this.validateWithSchema(objectSchema, value, options);
    }
    validateWithSchema(schema, value, options = {}) {
        var _a, _b, _c;
        if (!schema) {
            return undefined;
        }
        options.validationOptions = options.validationOptions || {};
        options.validationOptions.errors = options.validationOptions.errors || {};
        options.validationOptions.errors.language = (0, i18n_1.formatLocale)(this.i18nService.getAvailableLocale(options.validationOptions.errors.language ||
            options.locale ||
            this.i18nConfig.defaultLocale, 'validate'));
        const result = schema.validate(value, Object.assign({}, (_a = this.validateConfig.validationOptions) !== null && _a !== void 0 ? _a : {}, {
            messages: this.messages,
        }, (_b = options.validationOptions) !== null && _b !== void 0 ? _b : {}));
        if (result.error) {
            throw new error_1.MidwayValidationError(result.error.message, (_c = options === null || options === void 0 ? void 0 : options.errorStatus) !== null && _c !== void 0 ? _c : this.validateConfig.errorStatus, result.error);
        }
        else {
            return result;
        }
    }
    getSchema(ClzType) {
        return getSchema(ClzType);
    }
};
__decorate([
    (0, core_1.Config)('validate'),
    __metadata("design:type", Object)
], ValidateService.prototype, "validateConfig", void 0);
__decorate([
    (0, core_1.Config)('i18n'),
    __metadata("design:type", Object)
], ValidateService.prototype, "i18nConfig", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", i18n_1.MidwayI18nServiceSingleton)
], ValidateService.prototype, "i18nService", void 0);
__decorate([
    (0, core_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ValidateService.prototype, "init", null);
ValidateService = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], ValidateService);
exports.ValidateService = ValidateService;
function getSchema(ClzType) {
    const rules = (0, core_1.getClassExtendedMetadata)(constants_1.RULES_KEY, ClzType);
    if (rules) {
        return Joi.object(rules);
    }
}
exports.getSchema = getSchema;
//# sourceMappingURL=service.js.map