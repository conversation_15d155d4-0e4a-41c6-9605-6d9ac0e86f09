{"version": 3, "file": "postinstall.js", "sourceRoot": "", "sources": ["../src/postinstall.ts"], "names": [], "mappings": ";;;AAAA,+BAA4B;AAC5B,2BAA8C;AAC9C,iDAAyC;AACzC,uCAAuC;AACvC,yDAA6D;AAC7D,MAAM,QAAQ,GAAG,gDAAgD,CAAC;AAC3D,MAAM,WAAW,GAAG,KAAK,EAAE,OAAe,EAAE,EAAE;IACnD,MAAM,OAAO,GAAG,OAAO,CAAC,IAAA,WAAI,EAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC,CAAC,OAAO,IAAI,GAAG,CAAC;IAC3E,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC;IACpC,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI,OAAO,EAAE;QACX,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3B;IACD,IAAI,OAAO,GAAG,KAAK,CAAC;IACpB,gBAAgB;IAChB,IAAI,IAAA,eAAU,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC,EAAE;QAC3C,MAAM,iBAAiB,GAAG,oBAAoB,EAAE,CAAC;QACjD,WAAW,CAAC,IAAI,CAAC,GAAG,iBAAiB,CAAC,CAAC;QACvC,OAAO,GAAG,IAAI,CAAC;KAChB;IACD,MAAM,MAAM,GAAG,EAAE,CAAC;IAClB,MAAM,eAAe,GAAG,EAAE,CAAC;IAC3B,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAC5B,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO;SACR;QACD,MAAM,CAAC,MAAM,CACX,eAAe,EACf,OAAO,CAAC,YAAY,EACpB,OAAO,CAAC,eAAe,CACxB,CAAC;QACF,IAAI,OAAO,CAAC,OAAO,EAAE;YACnB,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAC5C,MAAM,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACpC,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACpC,MAAM,GAAG,GAAG,IAAA,sBAAO,EAAC,EAAE,eAAe,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;IACnD,IAAI,OAAO,CAAC,MAAM,EAAE;QAClB,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC;KACxD;IACD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;QAC1B,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;QAC3C,MAAM,IAAA,yBAAU,EAAC;YACf,OAAO;YACP,QAAQ,EAAE,GAAG;YACb,UAAU,EAAE,GAAG,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;YAC9C,OAAO,EAAE,IAAI;YACb,OAAO;SACR,CAAC,CAAC;KACJ;IACD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;AAChD,CAAC,CAAC;AAjDW,QAAA,WAAW,eAiDtB;AAEF,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,MAAM,EAAE,eAAe,EAAE,EAAE;IACxD,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;QACtB,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,GAAG,GAAG,oBAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACrC,OAAO,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,CAAC;QACzD,CAAC,CAAC,CAAC;QACH,IAAI,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE;YACpC,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACpB,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBAC5B,IAAI,eAAe,CAAC,OAAO,CAAC,EAAE;oBAC5B,OAAO;iBACR;gBACD,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE;oBACpB,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;iBACxB;YACH,CAAC,CAAC,CAAC;SACJ;KACF;AACH,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,GAAG,EAAE;IAChC,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,IAAI;QACF,MAAM,UAAU,GAAG,IAAA,wBAAQ,EAAC,qBAAqB,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9D,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAC/C,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YACpC,MAAM,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACjD,IAAI,OAAO,EAAE;gBACX,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;aAC3B;QACH,CAAC,CAAC,CAAC;KACJ;IAAC,WAAM;QACN,SAAS;KACV;IACD,OAAO,WAAW,CAAC;AACrB,CAAC,CAAC;AAEF,MAAM,UAAU,GAAG,CAAC,OAAe,EAAE,EAAE;IACrC,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IAC9C,IAAI,CAAC,IAAA,eAAU,EAAC,OAAO,CAAC,EAAE;QACxB,OAAO;KACR;IACD,OAAO,IAAI,CAAC,KAAK,CAAC,IAAA,iBAAY,EAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;AACtD,CAAC,CAAC"}