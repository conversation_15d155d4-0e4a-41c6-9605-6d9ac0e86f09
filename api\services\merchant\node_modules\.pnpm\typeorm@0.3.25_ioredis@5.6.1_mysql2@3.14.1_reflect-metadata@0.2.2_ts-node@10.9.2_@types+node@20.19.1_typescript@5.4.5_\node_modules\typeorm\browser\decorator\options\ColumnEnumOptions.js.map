{"version": 3, "sources": ["../browser/src/decorator/options/ColumnEnumOptions.ts"], "names": [], "mappings": "", "file": "ColumnEnumOptions.js", "sourcesContent": ["/**\n * Column options for enum-typed columns.\n */\nexport interface ColumnEnumOptions {\n    /**\n     * Array of possible enumerated values.\n     */\n    enum?: any[] | Object\n    /**\n     * Exact name of enum\n     */\n    enumName?: string\n}\n"], "sourceRoot": "../.."}