import { BaseEntity } from '@cool-midway/core';
import { Column, Entity, Index } from 'typeorm';

/**
 * 结算规则实体
 * 对标国际前沿结算体系标准
 */
@Entity('settlement_rule')
export class SettlementRuleEntity extends BaseEntity {
  @Column({ comment: '规则名称' })
  name: string;

  @Column({ comment: '规则描述', type: 'text', nullable: true })
  description: string;

  // === 基础配置 ===
  @Index()
  @Column({ comment: '适用商户类型: all,personal,enterprise' })
  merchantType: string;

  @Column({ comment: '行业分类: all,handicraft,heritage,digital,education' })
  industry: string;

  @Column({ comment: '商户等级: all,newbie,standard,vip,diamond' })
  merchantLevel: string;

  @Column({ comment: '结算周期: T0,T1,T3,T7,weekly,monthly' })
  settlementCycle: string;

  @Column({ comment: '优先级', default: 10 })
  priority: number;

  @Column({ comment: '状态: 0-禁用 1-启用', default: 1 })
  status: number;

  // === 费率配置 ===
  @Column({ comment: '基础佣金率(%)', type: 'decimal', precision: 5, scale: 2, default: 3.0 })
  commissionRate: number;

  @Column({ comment: '最低佣金(元)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  minCommission: number;

  @Column({ comment: '最高佣金(元)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  maxCommission: number;

  @Column({ comment: '交易手续费(元/笔)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  transactionFee: number;

  @Column({ comment: '提现手续费(元/笔)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  withdrawFee: number;

  @Column({ comment: '服务费(元/月)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  serviceFee: number;

  @Column({ comment: '最小结算金额(元)', type: 'decimal', precision: 10, scale: 2, default: 100 })
  minAmount: number;

  @Column({ comment: '最大结算金额(元)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  maxAmount: number;

  // === 阶梯费率 ===
  @Column({ comment: '启用阶梯费率', default: false })
  enableLadder: boolean;

  @Column({ comment: '阶梯规则配置', type: 'json', nullable: true })
  ladderRules: Array<{
    name: string;
    minAmount: number;
    maxAmount: number;
    rate: number;
  }>;

  // === 特殊规则 ===
  @Column({ comment: '新商户优惠配置', type: 'json', nullable: true })
  newMerchantDiscount: {
    enabled: boolean;
    duration: number; // 月数
    rate: number; // 优惠费率
  };

  @Column({ comment: '节假日规则配置', type: 'json', nullable: true })
  holidayRule: {
    enabled: boolean;
    action: 'delay' | 'normal' | 'pause'; // 延迟|正常|暂停
  };

  @Column({ comment: '活动规则配置', type: 'json', nullable: true })
  promotionRule: {
    enabled: boolean;
    rate: number;
    startTime: Date;
    endTime: Date;
  };

  // === 风控与合规 ===
  @Column({ comment: '风险等级: low,medium,high', default: 'low' })
  riskLevel: string;

  @Column({ comment: '单日限额(元)', type: 'decimal', precision: 15, scale: 2, default: 100000 })
  dailyLimit: number;

  @Column({ comment: '单月限额(元)', type: 'decimal', precision: 15, scale: 2, default: 3000000 })
  monthlyLimit: number;

  @Column({ comment: '启用反洗钱检测', default: false })
  enableAML: boolean;

  @Column({ comment: '启用资金托管', default: false })
  enableEscrow: boolean;

  @Column({ comment: 'KYC等级: basic,standard,advanced', default: 'basic' })
  kycLevel: string;

  @Column({ comment: '税务处理: platform,merchant,exempt', default: 'platform' })
  taxHandling: string;

  @Column({ comment: '监管报告级别: standard,detailed,realtime', default: 'standard' })
  reportingLevel: string;

  // === 智能定价 ===
  @Column({ comment: '启用动态定价', default: false })
  enableDynamicPricing: boolean;

  @Column({ comment: '定价调整频率: realtime,daily,weekly,monthly', default: 'daily' })
  pricingFrequency: string;

  @Column({ comment: '交易量权重(%)', default: 25 })
  volumeWeight: number;

  @Column({ comment: '信用评分权重(%)', default: 25 })
  creditWeight: number;

  @Column({ comment: '市场竞争权重(%)', default: 25 })
  marketWeight: number;

  @Column({ comment: '风险评估权重(%)', default: 25 })
  riskWeight: number;

  @Column({ comment: '启用竞争性定价', default: false })
  enableCompetitivePricing: boolean;

  @Column({ comment: '价格匹配策略: lowest,average,premium', default: 'average' })
  priceMatchStrategy: string;

  // === 国际化支持 ===
  @Column({ comment: '基础货币', default: 'CNY' })
  baseCurrency: string;

  @Column({ comment: '支持币种', type: 'json', nullable: true })
  supportedCurrencies: string[];

  @Column({ comment: '汇率更新方式: realtime,daily,fixed', default: 'daily' })
  exchangeRateUpdate: string;

  @Column({ comment: '启用汇率对冲', default: false })
  enableCurrencyHedging: boolean;

  @Column({ comment: '对冲策略: full,partial,dynamic', default: 'partial' })
  hedgingStrategy: string;

  @Column({ comment: '跨境通道: swift,blockchain,digital,thirdparty', default: 'swift' })
  crossBorderChannel: string;

  @Column({ comment: '跨境结算时效: instant,T1,T3,T5', default: 'T3' })
  crossBorderSettlement: string;

  @Column({ comment: '跨境手续费(%)', type: 'decimal', precision: 5, scale: 2, default: 0.5 })
  crossBorderFee: number;

  @Column({ comment: '适用地区', type: 'json', nullable: true })
  applicableRegions: string[];

  @Column({ comment: '时区处理: platform,merchant,user', default: 'platform' })
  timezoneHandling: string;

  // === 高级功能 ===
  @Column({ comment: '启用AI智能优化', default: false })
  enableAIOptimization: boolean;

  @Column({ comment: '优化目标: revenue,risk,satisfaction,balanced', default: 'balanced' })
  optimizationTarget: string;

  @Column({ comment: '学习周期(天)', default: 30 })
  learningPeriod: number;

  @Column({ comment: '调整幅度(%)', type: 'decimal', precision: 5, scale: 1, default: 10.0 })
  adjustmentRange: number;

  @Column({ comment: '置信度阈值(%)', type: 'decimal', precision: 5, scale: 1, default: 80.0 })
  confidenceThreshold: number;

  @Column({ comment: '启用区块链结算', default: false })
  enableBlockchain: boolean;

  @Column({ comment: '区块链网络: ethereum,bsc,polygon,solana', nullable: true })
  blockchainNetwork: string;

  @Column({ comment: '智能合约地址', nullable: true })
  smartContract: string;

  @Column({ comment: 'Gas费策略: standard,fast,economy', default: 'standard' })
  gasStrategy: string;

  @Column({ comment: '确认数', default: 12 })
  confirmations: number;

  // === 审计字段 ===
  @Column({ comment: '创建人ID', nullable: true })
  createdBy: number;

  @Column({ comment: '最后修改人ID', nullable: true })
  updatedBy: number;

  @Column({ comment: '生效时间', type: 'datetime', nullable: true })
  effectiveTime: Date;

  @Column({ comment: '失效时间', type: 'datetime', nullable: true })
  expireTime: Date;

  @Column({ comment: '版本号', default: 1 })
  version: number;

  @Column({ comment: '审批状态: draft,pending,approved,rejected', default: 'draft' })
  approvalStatus: string;

  @Column({ comment: '审批人ID', nullable: true })
  approvedBy: number;

  @Column({ comment: '审批时间', type: 'datetime', nullable: true })
  approvedAt: Date;

  @Column({ comment: '审批备注', nullable: true })
  approvalRemark: string;

  // === 统计字段 ===
  @Column({ comment: '应用次数', default: 0 })
  applyCount: number;

  @Column({ comment: '总处理金额(元)', type: 'decimal', precision: 20, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ comment: '总佣金收入(元)', type: 'decimal', precision: 20, scale: 2, default: 0 })
  totalCommission: number;

  @Column({ comment: '最后应用时间', type: 'datetime', nullable: true })
  lastAppliedAt: Date;

  // === 扩展字段 ===
  @Column({ comment: '扩展配置', type: 'json', nullable: true })
  extendConfig: Record<string, any>;

  @Column({ comment: '备注', type: 'text', nullable: true })
  remark: string;
}

/**
 * 结算记录实体
 */
@Entity('settlement_record')
export class SettlementRecordEntity extends BaseEntity {
  @Index()
  @Column({ comment: '商户ID' })
  merchantId: number;

  @Index()
  @Column({ comment: '订单ID' })
  orderId: number;

  @Column({ comment: '订单号' })
  orderNum: string;

  @Index()
  @Column({ comment: '结算规则ID' })
  ruleId: number;

  @Column({ comment: '原始金额(元)', type: 'decimal', precision: 15, scale: 2 })
  originalAmount: number;

  @Column({ comment: '转换后金额(元)', type: 'decimal', precision: 15, scale: 2 })
  convertedAmount: number;

  @Column({ comment: '原始货币' })
  currency: string;

  @Column({ comment: '基础货币' })
  baseCurrency: string;

  @Column({ comment: '汇率', type: 'decimal', precision: 10, scale: 6, default: 1 })
  exchangeRate: number;

  @Column({ comment: '应用费率(%)', type: 'decimal', precision: 5, scale: 2 })
  appliedRate: number;

  @Column({ comment: '佣金(元)', type: 'decimal', precision: 15, scale: 2 })
  commission: number;

  @Column({ comment: '交易手续费(元)', type: 'decimal', precision: 15, scale: 2, default: 0 })
  transactionFee: number;

  @Column({ comment: '其他费用(元)', type: 'decimal', precision: 15, scale: 2, default: 0 })
  otherFees: number;

  @Column({ comment: '总费用(元)', type: 'decimal', precision: 15, scale: 2 })
  totalFees: number;

  @Column({ comment: '净结算金额(元)', type: 'decimal', precision: 15, scale: 2 })
  netAmount: number;

  @Column({ comment: '结算状态: pending,processing,completed,failed', default: 'pending' })
  status: string;

  @Column({ comment: '预计结算时间', type: 'datetime' })
  estimatedSettlementTime: Date;

  @Column({ comment: '实际结算时间', type: 'datetime', nullable: true })
  actualSettlementTime: Date;

  @Column({ comment: '风控检查结果', type: 'json', nullable: true })
  riskCheckResult: Record<string, any>;

  @Column({ comment: '合规检查结果', type: 'json', nullable: true })
  complianceCheckResult: Record<string, any>;

  @Column({ comment: '区块链交易哈希', nullable: true })
  blockchainTxHash: string;

  @Column({ comment: '区块链网络', nullable: true })
  blockchainNetwork: string;

  @Column({ comment: '区块链确认数', default: 0 })
  blockchainConfirmations: number;

  @Column({ comment: '计算元数据', type: 'json', nullable: true })
  calculationMetadata: Record<string, any>;

  @Column({ comment: '错误信息', nullable: true })
  errorMessage: string;

  @Column({ comment: '重试次数', default: 0 })
  retryCount: number;

  @Column({ comment: '备注', nullable: true })
  remark: string;
}

/**
 * 结算批次实体
 */
@Entity('settlement_batch')
export class SettlementBatchEntity extends BaseEntity {
  @Column({ comment: '批次号', unique: true })
  batchNumber: string;

  @Column({ comment: '批次类型: daily,weekly,monthly,manual' })
  batchType: string;

  @Column({ comment: '结算日期', type: 'date' })
  settlementDate: Date;

  @Column({ comment: '商户数量' })
  merchantCount: number;

  @Column({ comment: '订单数量' })
  orderCount: number;

  @Column({ comment: '总金额(元)', type: 'decimal', precision: 20, scale: 2 })
  totalAmount: number;

  @Column({ comment: '总佣金(元)', type: 'decimal', precision: 20, scale: 2 })
  totalCommission: number;

  @Column({ comment: '净结算金额(元)', type: 'decimal', precision: 20, scale: 2 })
  totalNetAmount: number;

  @Column({ comment: '批次状态: created,processing,completed,failed', default: 'created' })
  status: string;

  @Column({ comment: '开始处理时间', type: 'datetime', nullable: true })
  startTime: Date;

  @Column({ comment: '完成时间', type: 'datetime', nullable: true })
  completedTime: Date;

  @Column({ comment: '成功数量', default: 0 })
  successCount: number;

  @Column({ comment: '失败数量', default: 0 })
  failedCount: number;

  @Column({ comment: '处理进度(%)', type: 'decimal', precision: 5, scale: 2, default: 0 })
  progress: number;

  @Column({ comment: '创建人ID' })
  createdBy: number;

  @Column({ comment: '备注', nullable: true })
  remark: string;
}
