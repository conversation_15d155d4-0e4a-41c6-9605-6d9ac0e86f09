{"name": "url-to-options", "description": "Convert a WHATWG URL to an http(s).request options object.", "version": "1.0.1", "license": "MIT", "author": "<PERSON> <<EMAIL>> (https://www.svachon.com/)", "repository": "stevenvachon/url-to-options", "devDependencies": {"universal-url": "^1.0.0-alpha"}, "engines": {"node": ">= 4"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["http", "https", "url", "whatwg"]}