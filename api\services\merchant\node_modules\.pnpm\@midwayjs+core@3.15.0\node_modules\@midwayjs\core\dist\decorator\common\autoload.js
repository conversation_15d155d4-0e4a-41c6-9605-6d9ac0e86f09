"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Autoload = void 0;
const decoratorManager_1 = require("../decoratorManager");
const provide_1 = require("./provide");
function Autoload() {
    return function (target) {
        (0, decoratorManager_1.savePreloadModule)(target);
        (0, provide_1.Provide)()(target);
    };
}
exports.Autoload = Autoload;
//# sourceMappingURL=autoload.js.map