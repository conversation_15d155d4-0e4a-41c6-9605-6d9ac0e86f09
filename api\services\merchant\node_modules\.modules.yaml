hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/code-frame@7.12.11':
    '@babel/code-frame': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/highlight@7.25.9':
    '@babel/highlight': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@cool-midway/cache-manager-fs-hash@7.0.0':
    '@cool-midway/cache-manager-fs-hash': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@eslint-community/eslint-utils@4.7.0(eslint@7.32.0)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/eslintrc@0.4.3':
    '@eslint/eslintrc': public
  '@hapi/bourne@3.0.0':
    '@hapi/bourne': private
  '@hapi/hoek@9.3.0':
    '@hapi/hoek': private
  '@hapi/topo@5.1.0':
    '@hapi/topo': private
  '@humanwhocodes/config-array@0.5.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/object-schema@1.2.1':
    '@humanwhocodes/object-schema': private
  '@ioredis/commands@1.2.0':
    '@ioredis/commands': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@isaacs/fs-minipass@4.0.1':
    '@isaacs/fs-minipass': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@keyv/serialize@1.0.3':
    '@keyv/serialize': private
  '@koa/router@12.0.2':
    '@koa/router': private
  '@midwayjs/async-hooks-context-manager@3.20.4':
    '@midwayjs/async-hooks-context-manager': private
  '@midwayjs/cache@3.14.0':
    '@midwayjs/cache': private
  '@midwayjs/cli-plugin-build@2.1.0':
    '@midwayjs/cli-plugin-build': private
  '@midwayjs/cli-plugin-check@2.1.0':
    '@midwayjs/cli-plugin-check': private
  '@midwayjs/cli-plugin-clean@2.1.0':
    '@midwayjs/cli-plugin-clean': private
  '@midwayjs/cli-plugin-dev@2.1.1(@midwayjs/mock@3.20.4)':
    '@midwayjs/cli-plugin-dev': private
  '@midwayjs/cli-plugin-test@2.1.0(@types/node@20.19.1)':
    '@midwayjs/cli-plugin-test': private
  '@midwayjs/command-core@2.1.0':
    '@midwayjs/command-core': private
  '@midwayjs/cookies@1.3.0':
    '@midwayjs/cookies': private
  '@midwayjs/debugger@1.0.9':
    '@midwayjs/debugger': private
  '@midwayjs/event-bus@1.9.4':
    '@midwayjs/event-bus': private
  '@midwayjs/glob@1.1.1':
    '@midwayjs/glob': private
  '@midwayjs/i18n@3.20.5':
    '@midwayjs/i18n': private
  '@midwayjs/locate@1.8.1':
    '@midwayjs/locate': private
  '@midwayjs/luckyeye@1.1.0':
    '@midwayjs/luckyeye': private
  '@midwayjs/serverless-spec-builder@2.1.0':
    '@midwayjs/serverless-spec-builder': private
  '@midwayjs/session@3.20.5':
    '@midwayjs/session': private
  '@midwayjs/version@3.20.7':
    '@midwayjs/version': private
  '@noble/hashes@1.8.0':
    '@noble/hashes': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@paralleldrive/cuid2@2.2.2':
    '@paralleldrive/cuid2': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@pm2/agent@2.0.4':
    '@pm2/agent': private
  '@pm2/io@6.0.1':
    '@pm2/io': private
  '@pm2/js-api@0.8.0':
    '@pm2/js-api': private
  '@pm2/pm2-version-check@1.0.4':
    '@pm2/pm2-version-check': private
  '@sideway/address@4.1.5':
    '@sideway/address': private
  '@sideway/formula@3.0.1':
    '@sideway/formula': private
  '@sideway/pinpoint@2.0.0':
    '@sideway/pinpoint': private
  '@sindresorhus/is@0.7.0':
    '@sindresorhus/is': private
  '@sqltools/formatter@1.2.5':
    '@sqltools/formatter': private
  '@szmarczak/http-timer@1.1.2':
    '@szmarczak/http-timer': private
  '@tootallnate/quickjs-emscripten@0.23.0':
    '@tootallnate/quickjs-emscripten': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/accepts@1.3.7':
    '@types/accepts': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/content-disposition@0.5.9':
    '@types/content-disposition': private
  '@types/cookiejar@2.1.5':
    '@types/cookiejar': private
  '@types/cookies@0.9.1':
    '@types/cookies': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/express@5.0.3':
    '@types/express': private
  '@types/glob@7.2.0':
    '@types/glob': private
  '@types/http-assert@1.5.6':
    '@types/http-assert': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/keygrip@1.0.6':
    '@types/keygrip': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/koa-compose@3.2.8':
    '@types/koa-compose': private
  '@types/koa@2.14.0':
    '@types/koa': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/minimatch@5.1.2':
    '@types/minimatch': private
  '@types/minimist@1.2.5':
    '@types/minimist': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/normalize-package-data@2.4.4':
    '@types/normalize-package-data': private
  '@types/qs@6.9.18':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/semver@7.7.0':
    '@types/semver': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/superagent@4.1.14':
    '@types/superagent': private
  '@types/supertest@2.0.16':
    '@types/supertest': private
  '@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@7.32.0)(typescript@5.4.5))(eslint@7.32.0)(typescript@5.4.5)':
    '@typescript-eslint/eslint-plugin': public
  '@typescript-eslint/parser@5.62.0(eslint@7.32.0)(typescript@5.4.5)':
    '@typescript-eslint/parser': public
  '@typescript-eslint/scope-manager@5.62.0':
    '@typescript-eslint/scope-manager': public
  '@typescript-eslint/type-utils@5.62.0(eslint@7.32.0)(typescript@5.4.5)':
    '@typescript-eslint/type-utils': public
  '@typescript-eslint/types@5.62.0':
    '@typescript-eslint/types': public
  '@typescript-eslint/typescript-estree@5.62.0(typescript@5.4.5)':
    '@typescript-eslint/typescript-estree': public
  '@typescript-eslint/utils@5.62.0(eslint@7.32.0)(typescript@5.4.5)':
    '@typescript-eslint/utils': public
  '@typescript-eslint/visitor-keys@5.62.0':
    '@typescript-eslint/visitor-keys': public
  '@vercel/ncc@0.30.0':
    '@vercel/ncc': private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@7.4.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  address@1.2.2:
    address: private
  agent-base@7.1.3:
    agent-base: private
  ajv@6.12.6:
    ajv: private
  amp-message@0.1.2:
    amp-message: private
  amp@0.3.1:
    amp: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  ansis@3.17.0:
    ansis: private
  anymatch@3.1.3:
    anymatch: private
  app-root-path@3.1.0:
    app-root-path: private
  archive-type@4.0.0:
    archive-type: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  args@5.0.3:
    args: private
  array-union@2.1.0:
    array-union: private
  arrify@1.0.1:
    arrify: private
  asap@2.0.6:
    asap: private
  ast-types@0.13.4:
    ast-types: private
  astral-regex@2.0.0:
    astral-regex: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  aws-ssl-profiles@1.1.2:
    aws-ssl-profiles: private
  axios@1.10.0(debug@4.4.1):
    axios: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  basic-ftp@5.0.5:
    basic-ftp: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@1.2.3:
    bl: private
  blessed@0.1.81:
    blessed: private
  bodec@0.1.0:
    bodec: private
  boxen@5.1.2:
    boxen: private
  brace-expansion@2.0.2:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-alloc-unsafe@1.1.0:
    buffer-alloc-unsafe: private
  buffer-alloc@1.2.0:
    buffer-alloc: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  buffer-equal-constant-time@1.0.1:
    buffer-equal-constant-time: private
  buffer-fill@1.0.0:
    buffer-fill: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  bytes@3.1.2:
    bytes: private
  cache-content-type@1.0.1:
    cache-content-type: private
  cache-manager@7.0.0:
    cache-manager: private
  cacheable-request@2.1.4:
    cacheable-request: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-keys@6.2.2:
    camelcase-keys: private
  camelcase@5.0.0:
    camelcase: private
  chalk@4.1.2:
    chalk: private
  chardet@0.7.0:
    chardet: private
  charenc@0.0.2:
    charenc: private
  charm@0.1.2:
    charm: private
  chokidar@3.6.0:
    chokidar: private
  chownr@3.0.0:
    chownr: private
  ci-info@2.0.0:
    ci-info: private
  class-transformer@0.5.1:
    class-transformer: private
  cli-boxes@2.2.1:
    cli-boxes: private
  cli-cursor@3.1.0:
    cli-cursor: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-tableau@2.0.1:
    cli-tableau: private
  cli-width@3.0.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  clone-response@1.0.2:
    clone-response: private
  cluster-key-slot@1.1.2:
    cluster-key-slot: private
  co-body@6.2.0:
    co-body: private
  co@4.6.0:
    co: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.15.1:
    commander: private
  compare-versions@6.1.1:
    compare-versions: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  configstore@5.0.1:
    configstore: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookiejar@2.1.4:
    cookiejar: private
  cookies@0.9.1:
    cookies: private
  copy-to@2.0.1:
    copy-to: private
  core-util-is@1.0.3:
    core-util-is: private
  create-require@1.1.1:
    create-require: private
  croner@4.1.97:
    croner: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypt@0.0.2:
    crypt: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  culvert@0.1.2:
    culvert: private
  data-uri-to-buffer@6.0.2:
    data-uri-to-buffer: private
  dayjs@1.11.13:
    dayjs: private
  debug@4.4.1:
    debug: private
  decamelize-keys@1.1.1:
    decamelize-keys: private
  decamelize@1.2.0:
    decamelize: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  decompress-response@3.3.0:
    decompress-response: private
  decompress-tar@4.1.1:
    decompress-tar: private
  decompress-tarbz2@4.1.1:
    decompress-tarbz2: private
  decompress-targz@4.1.1:
    decompress-targz: private
  decompress-unzip@4.0.1:
    decompress-unzip: private
  decompress@4.2.1:
    decompress: private
  dedent@1.6.0:
    dedent: private
  deep-equal@1.0.1:
    deep-equal: private
  deep-extend@0.6.0:
    deep-extend: private
  deep-is@0.1.4:
    deep-is: private
  defer-to-connect@1.1.3:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  degenerator@5.0.1:
    degenerator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  delegates@1.0.0:
    delegates: private
  denque@2.1.0:
    denque: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  detect-port@1.6.1:
    detect-port: private
  dezalgo@1.0.4:
    dezalgo: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dot-prop@5.3.0:
    dot-prop: private
  dotenv@16.5.0:
    dotenv: private
  download@8.0.0:
    download: private
  dunder-proto@1.0.1:
    dunder-proto: private
  duplexer3@0.1.5:
    duplexer3: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ecdsa-sig-formatter@1.0.11:
    ecdsa-sig-formatter: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@1.0.2:
    encodeurl: private
  end-of-stream@1.4.5:
    end-of-stream: private
  enquirer@2.4.1:
    enquirer: private
  error-ex@1.3.2:
    error-ex: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  escalade@3.2.0:
    escalade: private
  escape-goat@2.1.1:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  escodegen@2.1.0:
    escodegen: private
  eslint-config-prettier@8.10.0(eslint@7.32.0):
    eslint-config-prettier: public
  eslint-plugin-es@3.0.1(eslint@7.32.0):
    eslint-plugin-es: public
  eslint-plugin-node@11.1.0(eslint@7.32.0):
    eslint-plugin-node: public
  eslint-plugin-prettier@3.4.1(eslint-config-prettier@8.10.0(eslint@7.32.0))(eslint@7.32.0)(prettier@2.8.8):
    eslint-plugin-prettier: public
  eslint-scope@5.1.1:
    eslint-scope: public
  eslint-utils@2.1.0:
    eslint-utils: public
  eslint-visitor-keys@2.1.0:
    eslint-visitor-keys: public
  eslint@7.32.0:
    eslint: public
  espree@7.3.1:
    espree: private
  esprima@4.0.1:
    esprima: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@4.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  eventemitter2@6.4.9:
    eventemitter2: private
  execa@5.1.1:
    execa: private
  ext-list@2.2.2:
    ext-list: private
  ext-name@5.0.0:
    ext-name: private
  external-editor@3.1.0:
    external-editor: private
  extrareqp2@1.0.0(debug@4.3.7):
    extrareqp2: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-patch@3.1.1:
    fast-json-patch: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-safe-stringify@2.1.1:
    fast-safe-stringify: private
  fast-uri@3.0.6:
    fast-uri: private
  fastest-validator@1.19.1:
    fastest-validator: private
  fastq@1.19.1:
    fastq: private
  fclone@1.0.11:
    fclone: private
  fd-slicer@1.1.0:
    fd-slicer: private
  figures@3.2.0:
    figures: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-type@11.1.0:
    file-type: private
  filelist@1.0.4:
    filelist: private
  filename-reserved-regex@2.0.0:
    filename-reserved-regex: private
  filenamify@3.0.0:
    filenamify: private
  fill-range@7.1.1:
    fill-range: private
  find-root@1.1.0:
    find-root: private
  find-up@4.1.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.9(debug@4.4.1):
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.3:
    form-data: private
  formidable@2.1.5:
    formidable: private
  fresh@0.5.2:
    fresh: private
  from2@2.3.0:
    from2: private
  fs-constants@1.0.0:
    fs-constants: private
  fs-extra@8.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  functional-red-black-tree@1.0.1:
    functional-red-black-tree: private
  generate-function@2.3.1:
    generate-function: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-stream@4.1.0:
    get-stream: private
  get-tsconfig@4.10.1:
    get-tsconfig: private
  get-uri@6.0.4:
    get-uri: private
  git-node-fs@1.0.0(js-git@0.7.8):
    git-node-fs: private
  git-sha1@0.1.2:
    git-sha1: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  global-dirs@3.0.1:
    global-dirs: private
  globals@13.24.0:
    globals: private
  globby@10.0.2:
    globby: private
  gopd@1.2.0:
    gopd: private
  got@8.3.2:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  hard-rejection@2.1.0:
    hard-rejection: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-symbol-support-x@1.4.2:
    has-symbol-support-x: private
  has-symbols@1.1.0:
    has-symbols: private
  has-to-string-tag-x@1.4.1:
    has-to-string-tag-x: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-yarn@2.1.0:
    has-yarn: private
  hasown@2.0.2:
    hasown: private
  hosted-git-info@4.1.0:
    hosted-git-info: private
  http-assert@1.5.0:
    http-assert: private
  http-cache-semantics@3.8.1:
    http-cache-semantics: private
  http-errors@2.0.0:
    http-errors: private
  http-proxy-agent@7.0.2:
    http-proxy-agent: private
  https-proxy-agent@7.0.6:
    https-proxy-agent: private
  human-signals@2.1.0:
    human-signals: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  import-lazy@2.1.0:
    import-lazy: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@4.0.0:
    indent-string: private
  inflation@2.1.0:
    inflation: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  ini@1.3.8:
    ini: private
  inquirer@7.3.3:
    inquirer: private
  into-stream@3.1.0:
    into-stream: private
  ip-address@9.0.5:
    ip-address: private
  ipaddr.js@2.2.0:
    ipaddr.js: private
  is-arrayish@0.2.1:
    is-arrayish: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-ci@2.0.0:
    is-ci: private
  is-core-module@2.16.1:
    is-core-module: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-installed-globally@0.4.0:
    is-installed-globally: private
  is-natural-number@4.0.1:
    is-natural-number: private
  is-npm@5.0.0:
    is-npm: private
  is-number@7.0.0:
    is-number: private
  is-obj@2.0.0:
    is-obj: private
  is-object@1.0.2:
    is-object: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-property@1.0.2:
    is-property: private
  is-regex@1.2.1:
    is-regex: private
  is-retry-allowed@1.2.0:
    is-retry-allowed: private
  is-stream@2.0.1:
    is-stream: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-typedarray@1.0.0:
    is-typedarray: private
  is-yarn-global@0.3.0:
    is-yarn-global: private
  isarray@1.0.0:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isurl@1.0.0:
    isurl: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  joi@17.13.3:
    joi: private
  js-git@0.7.8:
    js-git: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  json-buffer@3.0.0:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  json5@2.2.3:
    json5: private
  jsonfile@4.0.0:
    jsonfile: private
  jsonwebtoken@9.0.2:
    jsonwebtoken: private
  jwa@1.4.2:
    jwa: private
  jws@3.2.2:
    jws: private
  keygrip@1.1.0:
    keygrip: private
  keyv@5.3.4:
    keyv: private
  kind-of@6.0.3:
    kind-of: private
  kleur@4.1.5:
    kleur: private
  koa-bodyparser@4.4.1:
    koa-bodyparser: private
  koa-compose@4.1.0:
    koa-compose: private
  koa-convert@2.0.0:
    koa-convert: private
  koa@2.15.0:
    koa: private
  latest-version@5.1.0:
    latest-version: private
  lazy@1.0.11:
    lazy: private
  leven@2.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  light-spinner@1.0.4:
    light-spinner: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  locate-path@5.0.0:
    locate-path: private
  lockfile@1.0.4:
    lockfile: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.defaults@4.2.0:
    lodash.defaults: private
  lodash.flatten@4.4.0:
    lodash.flatten: private
  lodash.includes@4.3.0:
    lodash.includes: private
  lodash.isarguments@3.1.0:
    lodash.isarguments: private
  lodash.isboolean@3.0.3:
    lodash.isboolean: private
  lodash.isinteger@4.0.4:
    lodash.isinteger: private
  lodash.isnumber@3.0.3:
    lodash.isnumber: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.isstring@4.0.1:
    lodash.isstring: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.once@4.1.1:
    lodash.once: private
  lodash.truncate@4.4.2:
    lodash.truncate: private
  lodash@4.17.21:
    lodash: private
  long@5.3.2:
    long: private
  lowercase-keys@1.0.1:
    lowercase-keys: private
  lru-cache@7.18.3:
    lru-cache: private
  lru.min@1.1.2:
    lru.min: private
  make-dir@1.3.0:
    make-dir: private
  make-error@1.3.6:
    make-error: private
  map-obj@4.3.0:
    map-obj: private
  map-or-similar@1.5.0:
    map-or-similar: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  md5@2.3.0:
    md5: private
  media-typer@0.3.0:
    media-typer: private
  memoizerific@1.11.3:
    memoizerific: private
  meow@9.0.0:
    meow: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromatch@4.0.8:
    micromatch: private
  midway-version@1.4.0:
    midway-version: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@2.6.0:
    mime: private
  mimic-fn@2.1.0:
    mimic-fn: private
  mimic-response@1.0.1:
    mimic-response: private
  min-indent@1.0.1:
    min-indent: private
  minimatch@3.1.2:
    minimatch: private
  minimist-options@4.1.0:
    minimist-options: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  minizlib@3.0.2:
    minizlib: private
  mkdirp@1.0.4:
    mkdirp: private
  mod-info@1.0.2:
    mod-info: private
  module-details-from-path@1.0.4:
    module-details-from-path: private
  moment@2.30.1:
    moment: private
  mri@1.1.4:
    mri: private
  ms@2.1.3:
    ms: private
  mute-stream@0.0.8:
    mute-stream: private
  mylas@2.1.13:
    mylas: private
  named-placeholders@1.1.3:
    named-placeholders: private
  natural-compare-lite@1.4.0:
    natural-compare-lite: private
  natural-compare@1.4.0:
    natural-compare: private
  ncp@2.0.0:
    ncp: private
  needle@2.4.0:
    needle: private
  negotiator@0.6.3:
    negotiator: private
  netmask@2.0.2:
    netmask: private
  node-fetch@2.7.0:
    node-fetch: private
  normalize-package-data@3.0.3:
    normalize-package-data: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-url@2.0.1:
    normalize-url: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nssocket@0.6.0:
    nssocket: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  only@0.0.2:
    only: private
  optionator@0.9.4:
    optionator: private
  os-tmpdir@1.0.2:
    os-tmpdir: private
  p-cancelable@0.4.1:
    p-cancelable: private
  p-event@2.3.1:
    p-event: private
  p-finally@1.0.0:
    p-finally: private
  p-is-promise@1.1.0:
    p-is-promise: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@4.1.0:
    p-locate: private
  p-map@2.1.0:
    p-map: private
  p-timeout@2.0.1:
    p-timeout: private
  p-try@2.2.0:
    p-try: private
  pac-proxy-agent@7.2.0:
    pac-proxy-agent: private
  pac-resolver@7.0.1:
    pac-resolver: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-json@6.5.0:
    package-json: private
  pako@0.2.9:
    pako: private
  parent-module@1.0.1:
    parent-module: private
  parse-json@5.2.0:
    parse-json: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pend@1.2.0:
    pend: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pidusage@3.0.2:
    pidusage: private
  pify@2.3.0:
    pify: private
  pinkie-promise@2.0.1:
    pinkie-promise: private
  pinkie@2.0.4:
    pinkie: private
  plimit-lit@1.6.1:
    plimit-lit: private
  pm2-axon-rpc@0.7.1:
    pm2-axon-rpc: private
  pm2-axon@4.0.1:
    pm2-axon: private
  pm2-deploy@1.0.2:
    pm2-deploy: private
  pm2-multimeter@0.1.2:
    pm2-multimeter: private
  pm2-sysmonit@1.2.8:
    pm2-sysmonit: private
  pm2@5.4.3:
    pm2: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prepend-http@2.0.0:
    prepend-http: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: public
  prettier@2.8.8:
    prettier: public
  process-nextick-args@2.0.1:
    process-nextick-args: private
  progress@2.0.3:
    progress: private
  promptly@2.2.0:
    promptly: private
  proxy-agent@6.3.1:
    proxy-agent: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  pump@3.0.3:
    pump: private
  punycode@2.3.1:
    punycode: private
  pupa@2.1.1:
    pupa: private
  qs@6.14.0:
    qs: private
  query-string@5.1.1:
    query-string: private
  queue-lit@1.5.2:
    queue-lit: private
  queue-microtask@1.2.3:
    queue-microtask: private
  queue@6.0.2:
    queue: private
  quick-lru@4.0.1:
    quick-lru: private
  raw-body@2.5.2:
    raw-body: private
  rc@1.2.8:
    rc: private
  read-pkg-up@7.0.1:
    read-pkg-up: private
  read-pkg@5.2.0:
    read-pkg: private
  read@1.0.7:
    read: private
  readable-stream@2.3.8:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  recursive-watch@1.1.4:
    recursive-watch: private
  redent@3.0.0:
    redent: private
  redis-commands@1.7.0:
    redis-commands: private
  redis-errors@1.2.0:
    redis-errors: private
  redis-parser@3.0.0:
    redis-parser: private
  reflect-metadata@0.2.2:
    reflect-metadata: private
  regexpp@3.2.0:
    regexpp: private
  registry-auth-token@4.2.2:
    registry-auth-token: private
  registry-url@5.1.0:
    registry-url: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  require-in-the-middle@5.2.0:
    require-in-the-middle: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  responselike@1.0.2:
    responselike: private
  restore-cursor@3.1.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  run-async@2.4.1:
    run-async: private
  run-parallel@1.2.0:
    run-parallel: private
  run-series@1.1.9:
    run-series: private
  rxjs@6.6.7:
    rxjs: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  sax@1.4.1:
    sax: private
  scmp@2.1.0:
    scmp: private
  seek-bzip@1.0.6:
    seek-bzip: private
  semver-diff@3.1.1:
    semver-diff: private
  semver@7.7.2:
    semver: private
  seq-queue@0.0.5:
    seq-queue: private
  set-function-length@1.2.2:
    set-function-length: private
  setprototypeof@1.2.0:
    setprototypeof: private
  sha.js@2.4.11:
    sha.js: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  shimmer@1.2.1:
    shimmer: private
  should-send-same-site-none@2.0.5:
    should-send-same-site-none: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  slash@3.0.0:
    slash: private
  slice-ansi@4.0.0:
    slice-ansi: private
  smart-buffer@4.2.0:
    smart-buffer: private
  socks-proxy-agent@8.0.5:
    socks-proxy-agent: private
  socks@2.8.5:
    socks: private
  sort-keys-length@1.0.1:
    sort-keys-length: private
  sort-keys@1.1.2:
    sort-keys: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  spdx-correct@3.2.0:
    spdx-correct: private
  spdx-exceptions@2.5.0:
    spdx-exceptions: private
  spdx-expression-parse@3.0.1:
    spdx-expression-parse: private
  spdx-license-ids@3.0.21:
    spdx-license-ids: private
  sprintf-js@1.1.2:
    sprintf-js: private
  sql-highlight@6.1.0:
    sql-highlight: private
  sqlstring@2.3.3:
    sqlstring: private
  standard-as-callback@2.1.0:
    standard-as-callback: private
  statuses@1.5.0:
    statuses: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string_decoder@1.1.1:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-dirs@2.1.0:
    strip-dirs: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-indent@3.0.0:
    strip-indent: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-outer@1.0.1:
    strip-outer: private
  superagent@8.1.2:
    superagent: private
  supertest@6.3.3:
    supertest: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  systeminformation@5.27.6:
    systeminformation: private
  table@6.9.0:
    table: private
  tar-stream@1.6.2:
    tar-stream: private
  tar@7.4.3:
    tar: private
  telejson@7.2.0:
    telejson: private
  text-table@0.2.0:
    text-table: private
  through@2.3.8:
    through: private
  timed-out@4.0.1:
    timed-out: private
  tmp@0.0.33:
    tmp: private
  to-buffer@1.2.1:
    to-buffer: private
  to-readable-stream@1.0.0:
    to-readable-stream: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  trim-newlines@3.0.1:
    trim-newlines: private
  trim-repeated@1.0.0:
    trim-repeated: private
  ts-node@10.9.2(@types/node@20.19.1)(typescript@5.4.5):
    ts-node: private
  tsc-alias@1.8.16:
    tsc-alias: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  tsscmp@1.0.6:
    tsscmp: private
  tsutils@3.21.0(typescript@5.4.5):
    tsutils: private
  ttl@1.3.1:
    ttl: private
  tv4@1.3.0:
    tv4: private
  tx2@1.0.5:
    tx2: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.18.1:
    type-fest: private
  type-is@1.6.18:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typedarray-to-buffer@3.1.5:
    typedarray-to-buffer: private
  unbzip2-stream@1.4.3:
    unbzip2-stream: private
  undici-types@6.21.0:
    undici-types: private
  unique-string@2.0.0:
    unique-string: private
  universalify@0.1.2:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  update-notifier@5.1.0:
    update-notifier: private
  uri-js@4.4.1:
    uri-js: private
  url-parse-lax@3.0.0:
    url-parse-lax: private
  url-to-options@1.0.1:
    url-to-options: private
  util-deprecate@1.0.2:
    util-deprecate: private
  uuid@9.0.1:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  v8-compile-cache@2.4.0:
    v8-compile-cache: private
  validate-npm-package-license@3.0.4:
    validate-npm-package-license: private
  vary@1.1.2:
    vary: private
  vizion@2.2.1:
    vizion: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  widest-line@3.1.0:
    widest-line: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  write-file-atomic@3.0.3:
    write-file-atomic: private
  ws@8.18.2:
    ws: private
  xdg-basedir@4.0.0:
    xdg-basedir: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@4.0.0:
    yallist: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yauzl@2.10.0:
    yauzl: private
  ylru@1.4.0:
    ylru: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.7.0
pendingBuilds: []
prunedAt: Wed, 16 Jul 2025 09:56:45 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v3
virtualStoreDir: D:\jiangrenjie_demo\api\services\merchant\node_modules\.pnpm
virtualStoreDirMaxLength: 120
