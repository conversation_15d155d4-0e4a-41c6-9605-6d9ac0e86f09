import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '@cool-midway/core';

/**
 * 国际化结算规则实体
 * 支持多国家/地区的本地化结算配置
 */
@Entity('settlement_international_rule')
export class SettlementInternationalRuleEntity extends BaseEntity {
  @Column({ comment: '规则名称' })
  name: string;

  @Column({ comment: '规则描述', type: 'text', nullable: true })
  description: string;

  // === 地区配置 ===
  @Index()
  @Column({ comment: '国家代码: CN,US,JP,KR,GB,DE,FR,SG,AU,CA' })
  countryCode: string;

  @Column({ comment: '国家名称' })
  countryName: string;

  @Column({ comment: '地区代码: Asia,Europe,Americas,Oceania' })
  regionCode: string;

  @Column({ comment: '时区', default: 'UTC' })
  timezone: string;

  // === 币种配置 ===
  @Column({ comment: '本地货币代码' })
  localCurrency: string;

  @Column({ comment: '基础货币代码', default: 'USD' })
  baseCurrency: string;

  @Column({ comment: '支持的货币', type: 'json' })
  supportedCurrencies: string[];

  @Column({ comment: '汇率更新频率: realtime,hourly,daily', default: 'daily' })
  exchangeRateFrequency: string;

  @Column({ comment: '汇率数据源: central_bank,api,manual', default: 'api' })
  exchangeRateSource: string;

  // === 结算配置 ===
  @Column({ comment: '适用商户类型: all,personal,enterprise' })
  merchantType: string;

  @Column({ comment: '结算周期: T0,T1,T3,T7,weekly,monthly' })
  settlementCycle: string;

  @Column({ comment: '本地结算周期: 考虑当地银行工作日' })
  localSettlementCycle: string;

  @Column({ comment: '跨境结算周期: 国际转账时效' })
  crossBorderCycle: string;

  // === 费率配置 ===
  @Column({ comment: '基础佣金比例(%)', type: 'decimal', precision: 5, scale: 2 })
  baseCommissionRate: number;

  @Column({ comment: '本地交易费率(%)', type: 'decimal', precision: 5, scale: 2 })
  localTransactionRate: number;

  @Column({ comment: '跨境交易费率(%)', type: 'decimal', precision: 5, scale: 2 })
  crossBorderRate: number;

  @Column({ comment: '汇率转换费(%)', type: 'decimal', precision: 5, scale: 2, default: 0.5 })
  currencyConversionFee: number;

  @Column({ comment: '银行手续费(固定)', type: 'decimal', precision: 10, scale: 2, default: 0 })
  bankProcessingFee: number;

  // === 合规配置 ===
  @Column({ comment: '合规等级: simplified,standard,strict' })
  complianceLevel: string;

  @Column({ comment: 'KYC要求等级: basic,standard,enhanced' })
  kycLevel: string;

  @Column({ comment: '是否需要税务申报', default: false })
  requireTaxReporting: boolean;

  @Column({ comment: '税率(%)', type: 'decimal', precision: 5, scale: 2, default: 0 })
  taxRate: number;

  @Column({ comment: '数据保护法规: GDPR,CCPA,PIPEDA,LGPD', nullable: true })
  dataProtectionLaw: string;

  @Column({ comment: '反洗钱要求', default: false })
  amlRequired: boolean;

  // === 支付方式配置 ===
  @Column({ comment: '支持的支付方式', type: 'json' })
  supportedPaymentMethods: string[];

  @Column({ comment: '本地银行合作伙伴', type: 'json', nullable: true })
  localBankPartners: string[];

  @Column({ comment: '第三方支付集成', type: 'json', nullable: true })
  thirdPartyIntegrations: string[];

  // === 限额配置 ===
  @Column({ comment: '单笔最小金额(本地货币)', type: 'decimal', precision: 15, scale: 2 })
  minTransactionAmount: number;

  @Column({ comment: '单笔最大金额(本地货币)', type: 'decimal', precision: 15, scale: 2 })
  maxTransactionAmount: number;

  @Column({ comment: '日交易限额(本地货币)', type: 'decimal', precision: 15, scale: 2 })
  dailyLimit: number;

  @Column({ comment: '月交易限额(本地货币)', type: 'decimal', precision: 15, scale: 2 })
  monthlyLimit: number;

  // === 本地化配置 ===
  @Column({ comment: '默认语言代码' })
  defaultLanguage: string;

  @Column({ comment: '支持的语言', type: 'json' })
  supportedLanguages: string[];

  @Column({ comment: '本地化要求', type: 'text', nullable: true })
  localizationRequirements: string;

  @Column({ comment: '客服工作时间' })
  supportHours: string;

  // === 风险配置 ===
  @Column({ comment: '风险等级: low,medium,high' })
  riskLevel: string;

  @Column({ comment: '风险评估模型: basic,advanced,ai' })
  riskAssessmentModel: string;

  @Column({ comment: '欺诈检测阈值', type: 'decimal', precision: 5, scale: 2, default: 80 })
  fraudDetectionThreshold: number;

  // === 技术配置 ===
  @Column({ comment: 'API端点URL', nullable: true })
  apiEndpoint: string;

  @Column({ comment: 'Webhook URL', nullable: true })
  webhookUrl: string;

  @Column({ comment: '加密要求: basic,advanced', default: 'basic' })
  encryptionLevel: string;

  // === 状态配置 ===
  @Column({ comment: '优先级', default: 10 })
  priority: number;

  @Column({ comment: '状态: 0-禁用 1-启用 2-测试', default: 1 })
  status: number;

  @Column({ comment: '生效时间', type: 'datetime' })
  effectiveDate: Date;

  @Column({ comment: '失效时间', type: 'datetime', nullable: true })
  expiryDate: Date;

  // === 审计字段 ===
  @Column({ comment: '创建人' })
  createdBy: string;

  @Column({ comment: '最后修改人', nullable: true })
  updatedBy: string;

  @Column({ comment: '审核状态: pending,approved,rejected', default: 'pending' })
  auditStatus: string;

  @Column({ comment: '审核人', nullable: true })
  auditedBy: string;

  @Column({ comment: '审核时间', type: 'datetime', nullable: true })
  auditedAt: Date;

  @Column({ comment: '备注', type: 'text', nullable: true })
  remarks: string;
}

/**
 * 国际化商户配置实体
 */
@Entity('settlement_international_merchant')
export class SettlementInternationalMerchantEntity extends BaseEntity {
  @Index()
  @Column({ comment: '商户ID' })
  merchantId: number;

  @Index()
  @Column({ comment: '国家代码' })
  countryCode: string;

  @Column({ comment: '商户类型: personal,enterprise' })
  merchantType: string;

  // === 身份信息 ===
  @Column({ comment: '法人姓名/企业名称' })
  legalName: string;

  @Column({ comment: '商业注册号', nullable: true })
  businessRegistrationNumber: string;

  @Column({ comment: '税务识别号', nullable: true })
  taxIdentificationNumber: string;

  @Column({ comment: '注册地址', type: 'text' })
  registeredAddress: string;

  // === KYC信息 ===
  @Column({ comment: 'KYC状态: pending,approved,rejected,expired' })
  kycStatus: string;

  @Column({ comment: 'KYC等级: basic,standard,enhanced' })
  kycLevel: string;

  @Column({ comment: 'KYC完成时间', type: 'datetime', nullable: true })
  kycCompletedAt: Date;

  @Column({ comment: 'KYC过期时间', type: 'datetime', nullable: true })
  kycExpiryDate: Date;

  @Column({ comment: 'KYC文档', type: 'json', nullable: true })
  kycDocuments: Record<string, any>;

  // === 银行信息 ===
  @Column({ comment: '银行名称' })
  bankName: string;

  @Column({ comment: '银行代码/SWIFT代码' })
  bankCode: string;

  @Column({ comment: '账户号码' })
  accountNumber: string;

  @Column({ comment: '账户持有人姓名' })
  accountHolderName: string;

  @Column({ comment: 'IBAN号码', nullable: true })
  ibanNumber: string;

  // === 合规状态 ===
  @Column({ comment: '合规状态: compliant,non_compliant,under_review' })
  complianceStatus: string;

  @Column({ comment: '最后合规检查时间', type: 'datetime', nullable: true })
  lastComplianceCheck: Date;

  @Column({ comment: '合规到期时间', type: 'datetime', nullable: true })
  complianceExpiryDate: Date;

  // === 业务配置 ===
  @Column({ comment: '适用的结算规则ID' })
  settlementRuleId: number;

  @Column({ comment: '本地货币' })
  localCurrency: string;

  @Column({ comment: '首选结算货币' })
  preferredSettlementCurrency: string;

  @Column({ comment: '月交易量(本地货币)', type: 'decimal', precision: 15, scale: 2, default: 0 })
  monthlyVolume: number;

  @Column({ comment: '风险评分', type: 'decimal', precision: 5, scale: 2, default: 50 })
  riskScore: number;

  // === 状态信息 ===
  @Column({ comment: '商户状态: active,inactive,suspended,terminated' })
  merchantStatus: string;

  @Column({ comment: '激活时间', type: 'datetime', nullable: true })
  activatedAt: Date;

  @Column({ comment: '最后交易时间', type: 'datetime', nullable: true })
  lastTransactionAt: Date;
}

/**
 * 国际化汇率实体
 */
@Entity('settlement_exchange_rate')
export class SettlementExchangeRateEntity extends BaseEntity {
  @Index()
  @Column({ comment: '基础货币' })
  baseCurrency: string;

  @Index()
  @Column({ comment: '目标货币' })
  targetCurrency: string;

  @Column({ comment: '汇率', type: 'decimal', precision: 12, scale: 6 })
  rate: number;

  @Column({ comment: '买入价', type: 'decimal', precision: 12, scale: 6 })
  bidRate: number;

  @Column({ comment: '卖出价', type: 'decimal', precision: 12, scale: 6 })
  askRate: number;

  @Column({ comment: '数据源: central_bank,api,manual' })
  source: string;

  @Column({ comment: '更新时间', type: 'datetime' })
  updatedAt: Date;

  @Column({ comment: '有效期', type: 'datetime' })
  validUntil: Date;

  @Column({ comment: '状态: active,expired,invalid' })
  status: string;
}
