import { AbstractEventBus } from './base';
import { LocalEventBusOptions, Message } from './interface';
declare class LocalWorker {
    protected pid: number;
    handler: any;
    constructor(pid: number);
    onMessage(handler: (message: Message) => void): void;
    on(): void;
    getWorkerId(): number;
    terminate(): void;
}
export declare class LocalEventBus extends AbstractEventBus<LocalWorker> {
    private worker;
    protected options: LocalEventBusOptions;
    constructor(options?: LocalEventBusOptions);
    protected workerSubscribeMessage(subscribeMessageHandler: (message: Message) => void): void;
    protected workerListenMessage(worker: any, subscribeMessageHandler: (message: Message) => void): void;
    protected workerSendMessage(message: Message): void;
    protected mainSendMessage(worker: any, message: Message): void;
    getWorkerId(worker?: any): string;
    isMain(): boolean;
    isWorker(): boolean;
    start(err?: Error): Promise<void>;
    stop(): Promise<void>;
}
export {};
//# sourceMappingURL=local.d.ts.map