(()=>{var __webpack_modules__={901:(module,__unused_webpack_exports,__webpack_require__)=>{module.exports=(()=>{var __webpack_modules__={4259:(e,t,r)=>{"use strict";const s=r(9406);e.exports=function(e){const t=e.acorn||r(390);const a=t.tokTypes;e=s(e);return class extends e{_maybeParseFieldValue(e){if(this.eat(a.eq)){const t=this._inFieldValue;this._inFieldValue=true;if(this.type===a.name&&this.value==="await"&&(this.inAsync||this.options.allowAwaitOutsideFunction)){e.value=this.parseAwait()}else e.value=this.parseExpression();this._inFieldValue=t}else e.value=null}parseClassElement(e){if(this.options.ecmaVersion>=8&&(this.type==a.name||this.type.keyword||this.type==this.privateIdentifierToken||this.type==a.bracketL||this.type==a.string||this.type==a.num)){const e=this._branch();if(e.type==a.bracketL){let t=0;do{if(e.eat(a.bracketL))++t;else if(e.eat(a.bracketR))--t;else e.next()}while(t>0)}else e.next(true);let t=e.type==a.eq||e.type==a.semi;if(!t&&e.canInsertSemicolon()){t=e.type!=a.parenL}if(t){const e=this.startNode();if(this.type==this.privateIdentifierToken){this.parsePrivateClassElementName(e)}else{this.parsePropertyName(e)}if(e.key.type==="Identifier"&&e.key.name==="constructor"||e.key.type==="Literal"&&e.key.value==="constructor"){this.raise(e.key.start,"Classes may not have a field called constructor")}this.enterScope(64|2|1);this._maybeParseFieldValue(e);this.exitScope();this.finishNode(e,"PropertyDefinition");this.semicolon();return e}}return super.parseClassElement.apply(this,arguments)}parseIdent(e,t){const r=super.parseIdent(e,t);if(this._inFieldValue&&r.name=="arguments")this.raise(r.start,"A class field initializer may not contain arguments");return r}}}},9406:(e,t,r)=>{"use strict";const s=Object.getPrototypeOf||(e=>e.__proto__);const getAcorn=e=>{if(e.acorn)return e.acorn;const t=r(390);if(t.version.indexOf("6.")!=0&&t.version.indexOf("6.0.")==0&&t.version.indexOf("7.")!=0){throw new Error(`acorn-private-class-elements requires acorn@^6.1.0 or acorn@7.0.0, not ${t.version}`)}for(let r=e;r&&r!==t.Parser;r=s(r)){if(r!==t.Parser){throw new Error("acorn-private-class-elements does not support mixing different acorn copies")}}return t};e.exports=function(e){if(e.prototype.parsePrivateName){return e}const t=getAcorn(e);e=class extends e{_branch(){this.__branch=this.__branch||new e({ecmaVersion:this.options.ecmaVersion},this.input);this.__branch.end=this.end;this.__branch.pos=this.pos;this.__branch.type=this.type;this.__branch.value=this.value;this.__branch.containsEsc=this.containsEsc;return this.__branch}parsePrivateClassElementName(e){e.computed=false;e.key=this.parsePrivateName();if(e.key.name=="constructor")this.raise(e.key.start,"Classes may not have a private element named constructor");const t={get:"set",set:"get"}[e.kind];const r=this._privateBoundNames;if(Object.prototype.hasOwnProperty.call(r,e.key.name)&&r[e.key.name]!==t){this.raise(e.start,"Duplicate private element")}r[e.key.name]=e.kind||true;delete this._unresolvedPrivateNames[e.key.name];return e.key}parsePrivateName(){const e=this.startNode();e.name=this.value;this.next();this.finishNode(e,"PrivateIdentifier");if(this.options.allowReserved=="never")this.checkUnreserved(e);return e}getTokenFromCode(e){if(e===35){++this.pos;const e=this.readWord1();return this.finishToken(this.privateIdentifierToken,e)}return super.getTokenFromCode(e)}parseClass(e,t){const r=this._outerPrivateBoundNames;this._outerPrivateBoundNames=this._privateBoundNames;this._privateBoundNames=Object.create(this._privateBoundNames||null);const s=this._outerUnresolvedPrivateNames;this._outerUnresolvedPrivateNames=this._unresolvedPrivateNames;this._unresolvedPrivateNames=Object.create(null);const a=super.parseClass(e,t);const o=this._unresolvedPrivateNames;this._privateBoundNames=this._outerPrivateBoundNames;this._outerPrivateBoundNames=r;this._unresolvedPrivateNames=this._outerUnresolvedPrivateNames;this._outerUnresolvedPrivateNames=s;if(!this._unresolvedPrivateNames){const e=Object.keys(o);if(e.length){e.sort(((e,t)=>o[e]-o[t]));this.raise(o[e[0]],"Usage of undeclared private name")}}else Object.assign(this._unresolvedPrivateNames,o);return a}parseClassSuper(e){const t=this._privateBoundNames;this._privateBoundNames=this._outerPrivateBoundNames;const r=this._unresolvedPrivateNames;this._unresolvedPrivateNames=this._outerUnresolvedPrivateNames;const s=super.parseClassSuper(e);this._privateBoundNames=t;this._unresolvedPrivateNames=r;return s}parseSubscript(e,r,s,a,o,u){const c=this.options.ecmaVersion>=11&&t.tokTypes.questionDot;const h=this._branch();if(!((h.eat(t.tokTypes.dot)||c&&h.eat(t.tokTypes.questionDot))&&h.type==this.privateIdentifierToken)){return super.parseSubscript.apply(this,arguments)}let p=false;if(!this.eat(t.tokTypes.dot)){this.expect(t.tokTypes.questionDot);p=true}let d=this.startNodeAt(r,s);d.object=e;d.computed=false;if(c){d.optional=p}if(this.type==this.privateIdentifierToken){if(e.type=="Super"){this.raise(this.start,"Cannot access private element on super")}d.property=this.parsePrivateName();if(!this._privateBoundNames||!this._privateBoundNames[d.property.name]){if(!this._unresolvedPrivateNames){this.raise(d.property.start,"Usage of undeclared private name")}this._unresolvedPrivateNames[d.property.name]=d.property.start}}else{d.property=this.parseIdent(true)}return this.finishNode(d,"MemberExpression")}parseMaybeUnary(e,t){const r=super.parseMaybeUnary(e,t);if(r.operator=="delete"){if(r.argument.type=="MemberExpression"&&r.argument.property.type=="PrivateIdentifier"){this.raise(r.start,"Private elements may not be deleted")}}return r}};e.prototype.privateIdentifierToken=new t.TokenType("privateIdentifier");return e}},104:(e,t,r)=>{"use strict";const s=r(9406);e.exports=function(e){const t=s(e);const a=e.acorn||r(390);const o=a.tokTypes;return class extends t{_maybeParseFieldValue(e){if(this.eat(o.eq)){const t=this._inStaticFieldScope;this._inStaticFieldScope=this.currentThisScope();e.value=this.parseExpression();this._inStaticFieldScope=t}else e.value=null}parseClassElement(e){if(this.options.ecmaVersion<8||!this.isContextual("static")){return super.parseClassElement.apply(this,arguments)}const t=this._branch();t.next();if([o.name,o.bracketL,o.string,o.num,this.privateIdentifierToken].indexOf(t.type)==-1&&!t.type.keyword){return super.parseClassElement.apply(this,arguments)}if(t.type==o.bracketL){let e=0;do{if(t.eat(o.bracketL))++e;else if(t.eat(o.bracketR))--e;else t.next()}while(e>0)}else t.next();if(t.type!=o.eq&&!t.canInsertSemicolon()&&t.type!=o.semi){return super.parseClassElement.apply(this,arguments)}const r=this.startNode();r.static=this.eatContextual("static");if(this.type==this.privateIdentifierToken){this.parsePrivateClassElementName(r)}else{this.parsePropertyName(r)}if(r.key.type==="Identifier"&&r.key.name==="constructor"||r.key.type==="Literal"&&!r.computed&&r.key.value==="constructor"){this.raise(r.key.start,"Classes may not have a field called constructor")}if((r.key.name||r.key.value)==="prototype"&&!r.computed){this.raise(r.key.start,"Classes may not have a static property named prototype")}this.enterScope(64|2|1);this._maybeParseFieldValue(r);this.exitScope();this.finishNode(r,"PropertyDefinition");this.semicolon();return r}parsePropertyName(e){if(e.static&&this.type==this.privateIdentifierToken){this.parsePrivateClassElementName(e)}else{super.parsePropertyName(e)}}parseIdent(e,t){const r=super.parseIdent(e,t);if(this._inStaticFieldScope&&this.currentThisScope()===this._inStaticFieldScope&&r.name=="arguments"){this.raise(r.start,"A static class field initializer may not contain arguments")}return r}}}},390:function(e,t){(function(e,r){true?r(t):0})(this,(function(e){"use strict";var t={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"};var r="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this";var s={5:r,"5module":r+" export import",6:r+" const class extends export import super"};var a=/^in(stanceof)?$/;var o="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ";var u="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";var c=new RegExp("["+o+"]");var h=new RegExp("["+o+u+"]");o=u=null;var p=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938];var d=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function isInAstralSet(e,t){var r=65536;for(var s=0;s<t.length;s+=2){r+=t[s];if(r>e){return false}r+=t[s+1];if(r>=e){return true}}}function isIdentifierStart(e,t){if(e<65){return e===36}if(e<91){return true}if(e<97){return e===95}if(e<123){return true}if(e<=65535){return e>=170&&c.test(String.fromCharCode(e))}if(t===false){return false}return isInAstralSet(e,p)}function isIdentifierChar(e,t){if(e<48){return e===36}if(e<58){return true}if(e<65){return false}if(e<91){return true}if(e<97){return e===95}if(e<123){return true}if(e<=65535){return e>=170&&h.test(String.fromCharCode(e))}if(t===false){return false}return isInAstralSet(e,p)||isInAstralSet(e,d)}var v=function TokenType(e,t){if(t===void 0)t={};this.label=e;this.keyword=t.keyword;this.beforeExpr=!!t.beforeExpr;this.startsExpr=!!t.startsExpr;this.isLoop=!!t.isLoop;this.isAssign=!!t.isAssign;this.prefix=!!t.prefix;this.postfix=!!t.postfix;this.binop=t.binop||null;this.updateContext=null};function binop(e,t){return new v(e,{beforeExpr:true,binop:t})}var m={beforeExpr:true},g={startsExpr:true};var y={};function kw(e,t){if(t===void 0)t={};t.keyword=e;return y[e]=new v(e,t)}var _={num:new v("num",g),regexp:new v("regexp",g),string:new v("string",g),name:new v("name",g),privateId:new v("privateId",g),eof:new v("eof"),bracketL:new v("[",{beforeExpr:true,startsExpr:true}),bracketR:new v("]"),braceL:new v("{",{beforeExpr:true,startsExpr:true}),braceR:new v("}"),parenL:new v("(",{beforeExpr:true,startsExpr:true}),parenR:new v(")"),comma:new v(",",m),semi:new v(";",m),colon:new v(":",m),dot:new v("."),question:new v("?",m),questionDot:new v("?."),arrow:new v("=>",m),template:new v("template"),invalidTemplate:new v("invalidTemplate"),ellipsis:new v("...",m),backQuote:new v("`",g),dollarBraceL:new v("${",{beforeExpr:true,startsExpr:true}),eq:new v("=",{beforeExpr:true,isAssign:true}),assign:new v("_=",{beforeExpr:true,isAssign:true}),incDec:new v("++/--",{prefix:true,postfix:true,startsExpr:true}),prefix:new v("!/~",{beforeExpr:true,prefix:true,startsExpr:true}),logicalOR:binop("||",1),logicalAND:binop("&&",2),bitwiseOR:binop("|",3),bitwiseXOR:binop("^",4),bitwiseAND:binop("&",5),equality:binop("==/!=/===/!==",6),relational:binop("</>/<=/>=",7),bitShift:binop("<</>>/>>>",8),plusMin:new v("+/-",{beforeExpr:true,binop:9,prefix:true,startsExpr:true}),modulo:binop("%",10),star:binop("*",10),slash:binop("/",10),starstar:new v("**",{beforeExpr:true}),coalesce:binop("??",1),_break:kw("break"),_case:kw("case",m),_catch:kw("catch"),_continue:kw("continue"),_debugger:kw("debugger"),_default:kw("default",m),_do:kw("do",{isLoop:true,beforeExpr:true}),_else:kw("else",m),_finally:kw("finally"),_for:kw("for",{isLoop:true}),_function:kw("function",g),_if:kw("if"),_return:kw("return",m),_switch:kw("switch"),_throw:kw("throw",m),_try:kw("try"),_var:kw("var"),_const:kw("const"),_while:kw("while",{isLoop:true}),_with:kw("with"),_new:kw("new",{beforeExpr:true,startsExpr:true}),_this:kw("this",g),_super:kw("super",g),_class:kw("class",g),_extends:kw("extends",m),_export:kw("export"),_import:kw("import",g),_null:kw("null",g),_true:kw("true",g),_false:kw("false",g),_in:kw("in",{beforeExpr:true,binop:7}),_instanceof:kw("instanceof",{beforeExpr:true,binop:7}),_typeof:kw("typeof",{beforeExpr:true,prefix:true,startsExpr:true}),_void:kw("void",{beforeExpr:true,prefix:true,startsExpr:true}),_delete:kw("delete",{beforeExpr:true,prefix:true,startsExpr:true})};var E=/\r\n?|\n|\u2028|\u2029/;var x=new RegExp(E.source,"g");function isNewLine(e,t){return e===10||e===13||!t&&(e===8232||e===8233)}var w=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/;var D=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g;var C=Object.prototype;var A=C.hasOwnProperty;var S=C.toString;function has(e,t){return A.call(e,t)}var k=Array.isArray||function(e){return S.call(e)==="[object Array]"};function wordsRegexp(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}var F=function Position(e,t){this.line=e;this.column=t};F.prototype.offset=function offset(e){return new F(this.line,this.column+e)};var R=function SourceLocation(e,t,r){this.start=t;this.end=r;if(e.sourceFile!==null){this.source=e.sourceFile}};function getLineInfo(e,t){for(var r=1,s=0;;){x.lastIndex=s;var a=x.exec(e);if(a&&a.index<t){++r;s=a.index+a[0].length}else{return new F(r,t-s)}}}var T={ecmaVersion:null,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:false,allowImportExportEverywhere:false,allowAwaitOutsideFunction:null,allowHashBang:false,locations:false,onToken:null,onComment:null,ranges:false,program:null,sourceFile:null,directSourceFile:null,preserveParens:false};var I=false;function getOptions(e){var t={};for(var r in T){t[r]=e&&has(e,r)?e[r]:T[r]}if(t.ecmaVersion==="latest"){t.ecmaVersion=1e8}else if(t.ecmaVersion==null){if(!I&&typeof console==="object"&&console.warn){I=true;console.warn("Since Acorn 8.0.0, options.ecmaVersion is required.\nDefaulting to 2020, but this will stop working in the future.")}t.ecmaVersion=11}else if(t.ecmaVersion>=2015){t.ecmaVersion-=2009}if(t.allowReserved==null){t.allowReserved=t.ecmaVersion<5}if(t.allowAwaitOutsideFunction==null){t.allowAwaitOutsideFunction=t.ecmaVersion>=13}if(k(t.onToken)){var s=t.onToken;t.onToken=function(e){return s.push(e)}}if(k(t.onComment)){t.onComment=pushComment(t,t.onComment)}return t}function pushComment(e,t){return function(r,s,a,o,u,c){var h={type:r?"Block":"Line",value:s,start:a,end:o};if(e.locations){h.loc=new R(this,u,c)}if(e.ranges){h.range=[a,o]}t.push(h)}}var B=1,N=2,O=B|N,P=4,L=8,j=16,M=32,V=64,q=128;function functionFlags(e,t){return N|(e?P:0)|(t?L:0)}var U=0,$=1,H=2,G=3,W=4,z=5;var K=function Parser(e,r,a){this.options=e=getOptions(e);this.sourceFile=e.sourceFile;this.keywords=wordsRegexp(s[e.ecmaVersion>=6?6:e.sourceType==="module"?"5module":5]);var o="";if(e.allowReserved!==true){o=t[e.ecmaVersion>=6?6:e.ecmaVersion===5?5:3];if(e.sourceType==="module"){o+=" await"}}this.reservedWords=wordsRegexp(o);var u=(o?o+" ":"")+t.strict;this.reservedWordsStrict=wordsRegexp(u);this.reservedWordsStrictBind=wordsRegexp(u+" "+t.strictBind);this.input=String(r);this.containsEsc=false;if(a){this.pos=a;this.lineStart=this.input.lastIndexOf("\n",a-1)+1;this.curLine=this.input.slice(0,this.lineStart).split(E).length}else{this.pos=this.lineStart=0;this.curLine=1}this.type=_.eof;this.value=null;this.start=this.end=this.pos;this.startLoc=this.endLoc=this.curPosition();this.lastTokEndLoc=this.lastTokStartLoc=null;this.lastTokStart=this.lastTokEnd=this.pos;this.context=this.initialContext();this.exprAllowed=true;this.inModule=e.sourceType==="module";this.strict=this.inModule||this.strictDirective(this.pos);this.potentialArrowAt=-1;this.potentialArrowInForAwait=false;this.yieldPos=this.awaitPos=this.awaitIdentPos=0;this.labels=[];this.undefinedExports=Object.create(null);if(this.pos===0&&e.allowHashBang&&this.input.slice(0,2)==="#!"){this.skipLineComment(2)}this.scopeStack=[];this.enterScope(B);this.regexpState=null;this.privateNameStack=[]};var Q={inFunction:{configurable:true},inGenerator:{configurable:true},inAsync:{configurable:true},allowSuper:{configurable:true},allowDirectSuper:{configurable:true},treatFunctionsAsVar:{configurable:true},inNonArrowFunction:{configurable:true}};K.prototype.parse=function parse(){var e=this.options.program||this.startNode();this.nextToken();return this.parseTopLevel(e)};Q.inFunction.get=function(){return(this.currentVarScope().flags&N)>0};Q.inGenerator.get=function(){return(this.currentVarScope().flags&L)>0&&!this.currentVarScope().inClassFieldInit};Q.inAsync.get=function(){return(this.currentVarScope().flags&P)>0&&!this.currentVarScope().inClassFieldInit};Q.allowSuper.get=function(){var e=this.currentThisScope();var t=e.flags;var r=e.inClassFieldInit;return(t&V)>0||r};Q.allowDirectSuper.get=function(){return(this.currentThisScope().flags&q)>0};Q.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())};Q.inNonArrowFunction.get=function(){var e=this.currentThisScope();var t=e.flags;var r=e.inClassFieldInit;return(t&N)>0||r};K.extend=function extend(){var e=[],t=arguments.length;while(t--)e[t]=arguments[t];var r=this;for(var s=0;s<e.length;s++){r=e[s](r)}return r};K.parse=function parse(e,t){return new this(t,e).parse()};K.parseExpressionAt=function parseExpressionAt(e,t,r){var s=new this(r,e,t);s.nextToken();return s.parseExpression()};K.tokenizer=function tokenizer(e,t){return new this(t,e)};Object.defineProperties(K.prototype,Q);var X=K.prototype;var J=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;X.strictDirective=function(e){for(;;){D.lastIndex=e;e+=D.exec(this.input)[0].length;var t=J.exec(this.input.slice(e));if(!t){return false}if((t[1]||t[2])==="use strict"){D.lastIndex=e+t[0].length;var r=D.exec(this.input),s=r.index+r[0].length;var a=this.input.charAt(s);return a===";"||a==="}"||E.test(r[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(a)||a==="!"&&this.input.charAt(s+1)==="=")}e+=t[0].length;D.lastIndex=e;e+=D.exec(this.input)[0].length;if(this.input[e]===";"){e++}}};X.eat=function(e){if(this.type===e){this.next();return true}else{return false}};X.isContextual=function(e){return this.type===_.name&&this.value===e&&!this.containsEsc};X.eatContextual=function(e){if(!this.isContextual(e)){return false}this.next();return true};X.expectContextual=function(e){if(!this.eatContextual(e)){this.unexpected()}};X.canInsertSemicolon=function(){return this.type===_.eof||this.type===_.braceR||E.test(this.input.slice(this.lastTokEnd,this.start))};X.insertSemicolon=function(){if(this.canInsertSemicolon()){if(this.options.onInsertedSemicolon){this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc)}return true}};X.semicolon=function(){if(!this.eat(_.semi)&&!this.insertSemicolon()){this.unexpected()}};X.afterTrailingComma=function(e,t){if(this.type===e){if(this.options.onTrailingComma){this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc)}if(!t){this.next()}return true}};X.expect=function(e){this.eat(e)||this.unexpected()};X.unexpected=function(e){this.raise(e!=null?e:this.start,"Unexpected token")};function DestructuringErrors(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}X.checkPatternErrors=function(e,t){if(!e){return}if(e.trailingComma>-1){this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element")}var r=t?e.parenthesizedAssign:e.parenthesizedBind;if(r>-1){this.raiseRecoverable(r,"Parenthesized pattern")}};X.checkExpressionErrors=function(e,t){if(!e){return false}var r=e.shorthandAssign;var s=e.doubleProto;if(!t){return r>=0||s>=0}if(r>=0){this.raise(r,"Shorthand property assignments are valid only in destructuring patterns")}if(s>=0){this.raiseRecoverable(s,"Redefinition of __proto__ property")}};X.checkYieldAwaitInDefaultParams=function(){if(this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)){this.raise(this.yieldPos,"Yield expression cannot be a default value")}if(this.awaitPos){this.raise(this.awaitPos,"Await expression cannot be a default value")}};X.isSimpleAssignTarget=function(e){if(e.type==="ParenthesizedExpression"){return this.isSimpleAssignTarget(e.expression)}return e.type==="Identifier"||e.type==="MemberExpression"};var Z=K.prototype;Z.parseTopLevel=function(e){var t=Object.create(null);if(!e.body){e.body=[]}while(this.type!==_.eof){var r=this.parseStatement(null,true,t);e.body.push(r)}if(this.inModule){for(var s=0,a=Object.keys(this.undefinedExports);s<a.length;s+=1){var o=a[s];this.raiseRecoverable(this.undefinedExports[o].start,"Export '"+o+"' is not defined")}}this.adaptDirectivePrologue(e.body);this.next();e.sourceType=this.options.sourceType;return this.finishNode(e,"Program")};var Y={kind:"loop"},ee={kind:"switch"};Z.isLet=function(e){if(this.options.ecmaVersion<6||!this.isContextual("let")){return false}D.lastIndex=this.pos;var t=D.exec(this.input);var r=this.pos+t[0].length,s=this.input.charCodeAt(r);if(s===91||s===92||s>55295&&s<56320){return true}if(e){return false}if(s===123){return true}if(isIdentifierStart(s,true)){var o=r+1;while(isIdentifierChar(s=this.input.charCodeAt(o),true)){++o}if(s===92||s>55295&&s<56320){return true}var u=this.input.slice(r,o);if(!a.test(u)){return true}}return false};Z.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async")){return false}D.lastIndex=this.pos;var e=D.exec(this.input);var t=this.pos+e[0].length,r;return!E.test(this.input.slice(this.pos,t))&&this.input.slice(t,t+8)==="function"&&(t+8===this.input.length||!(isIdentifierChar(r=this.input.charCodeAt(t+8))||r>55295&&r<56320))};Z.parseStatement=function(e,t,r){var s=this.type,a=this.startNode(),o;if(this.isLet(e)){s=_._var;o="let"}switch(s){case _._break:case _._continue:return this.parseBreakContinueStatement(a,s.keyword);case _._debugger:return this.parseDebuggerStatement(a);case _._do:return this.parseDoStatement(a);case _._for:return this.parseForStatement(a);case _._function:if(e&&(this.strict||e!=="if"&&e!=="label")&&this.options.ecmaVersion>=6){this.unexpected()}return this.parseFunctionStatement(a,false,!e);case _._class:if(e){this.unexpected()}return this.parseClass(a,true);case _._if:return this.parseIfStatement(a);case _._return:return this.parseReturnStatement(a);case _._switch:return this.parseSwitchStatement(a);case _._throw:return this.parseThrowStatement(a);case _._try:return this.parseTryStatement(a);case _._const:case _._var:o=o||this.value;if(e&&o!=="var"){this.unexpected()}return this.parseVarStatement(a,o);case _._while:return this.parseWhileStatement(a);case _._with:return this.parseWithStatement(a);case _.braceL:return this.parseBlock(true,a);case _.semi:return this.parseEmptyStatement(a);case _._export:case _._import:if(this.options.ecmaVersion>10&&s===_._import){D.lastIndex=this.pos;var u=D.exec(this.input);var c=this.pos+u[0].length,h=this.input.charCodeAt(c);if(h===40||h===46){return this.parseExpressionStatement(a,this.parseExpression())}}if(!this.options.allowImportExportEverywhere){if(!t){this.raise(this.start,"'import' and 'export' may only appear at the top level")}if(!this.inModule){this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")}}return s===_._import?this.parseImport(a):this.parseExport(a,r);default:if(this.isAsyncFunction()){if(e){this.unexpected()}this.next();return this.parseFunctionStatement(a,true,!e)}var p=this.value,d=this.parseExpression();if(s===_.name&&d.type==="Identifier"&&this.eat(_.colon)){return this.parseLabeledStatement(a,p,d,e)}else{return this.parseExpressionStatement(a,d)}}};Z.parseBreakContinueStatement=function(e,t){var r=t==="break";this.next();if(this.eat(_.semi)||this.insertSemicolon()){e.label=null}else if(this.type!==_.name){this.unexpected()}else{e.label=this.parseIdent();this.semicolon()}var s=0;for(;s<this.labels.length;++s){var a=this.labels[s];if(e.label==null||a.name===e.label.name){if(a.kind!=null&&(r||a.kind==="loop")){break}if(e.label&&r){break}}}if(s===this.labels.length){this.raise(e.start,"Unsyntactic "+t)}return this.finishNode(e,r?"BreakStatement":"ContinueStatement")};Z.parseDebuggerStatement=function(e){this.next();this.semicolon();return this.finishNode(e,"DebuggerStatement")};Z.parseDoStatement=function(e){this.next();this.labels.push(Y);e.body=this.parseStatement("do");this.labels.pop();this.expect(_._while);e.test=this.parseParenExpression();if(this.options.ecmaVersion>=6){this.eat(_.semi)}else{this.semicolon()}return this.finishNode(e,"DoWhileStatement")};Z.parseForStatement=function(e){this.next();var t=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;this.labels.push(Y);this.enterScope(0);this.expect(_.parenL);if(this.type===_.semi){if(t>-1){this.unexpected(t)}return this.parseFor(e,null)}var r=this.isLet();if(this.type===_._var||this.type===_._const||r){var s=this.startNode(),a=r?"let":this.value;this.next();this.parseVar(s,true,a);this.finishNode(s,"VariableDeclaration");if((this.type===_._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&s.declarations.length===1){if(this.options.ecmaVersion>=9){if(this.type===_._in){if(t>-1){this.unexpected(t)}}else{e.await=t>-1}}return this.parseForIn(e,s)}if(t>-1){this.unexpected(t)}return this.parseFor(e,s)}var o=new DestructuringErrors;var u=this.parseExpression(t>-1?"await":true,o);if(this.type===_._in||this.options.ecmaVersion>=6&&this.isContextual("of")){if(this.options.ecmaVersion>=9){if(this.type===_._in){if(t>-1){this.unexpected(t)}}else{e.await=t>-1}}this.toAssignable(u,false,o);this.checkLValPattern(u);return this.parseForIn(e,u)}else{this.checkExpressionErrors(o,true)}if(t>-1){this.unexpected(t)}return this.parseFor(e,u)};Z.parseFunctionStatement=function(e,t,r){this.next();return this.parseFunction(e,re|(r?0:ie),false,t)};Z.parseIfStatement=function(e){this.next();e.test=this.parseParenExpression();e.consequent=this.parseStatement("if");e.alternate=this.eat(_._else)?this.parseStatement("if"):null;return this.finishNode(e,"IfStatement")};Z.parseReturnStatement=function(e){if(!this.inFunction&&!this.options.allowReturnOutsideFunction){this.raise(this.start,"'return' outside of function")}this.next();if(this.eat(_.semi)||this.insertSemicolon()){e.argument=null}else{e.argument=this.parseExpression();this.semicolon()}return this.finishNode(e,"ReturnStatement")};Z.parseSwitchStatement=function(e){this.next();e.discriminant=this.parseParenExpression();e.cases=[];this.expect(_.braceL);this.labels.push(ee);this.enterScope(0);var t;for(var r=false;this.type!==_.braceR;){if(this.type===_._case||this.type===_._default){var s=this.type===_._case;if(t){this.finishNode(t,"SwitchCase")}e.cases.push(t=this.startNode());t.consequent=[];this.next();if(s){t.test=this.parseExpression()}else{if(r){this.raiseRecoverable(this.lastTokStart,"Multiple default clauses")}r=true;t.test=null}this.expect(_.colon)}else{if(!t){this.unexpected()}t.consequent.push(this.parseStatement(null))}}this.exitScope();if(t){this.finishNode(t,"SwitchCase")}this.next();this.labels.pop();return this.finishNode(e,"SwitchStatement")};Z.parseThrowStatement=function(e){this.next();if(E.test(this.input.slice(this.lastTokEnd,this.start))){this.raise(this.lastTokEnd,"Illegal newline after throw")}e.argument=this.parseExpression();this.semicolon();return this.finishNode(e,"ThrowStatement")};var te=[];Z.parseTryStatement=function(e){this.next();e.block=this.parseBlock();e.handler=null;if(this.type===_._catch){var t=this.startNode();this.next();if(this.eat(_.parenL)){t.param=this.parseBindingAtom();var r=t.param.type==="Identifier";this.enterScope(r?M:0);this.checkLValPattern(t.param,r?W:H);this.expect(_.parenR)}else{if(this.options.ecmaVersion<10){this.unexpected()}t.param=null;this.enterScope(0)}t.body=this.parseBlock(false);this.exitScope();e.handler=this.finishNode(t,"CatchClause")}e.finalizer=this.eat(_._finally)?this.parseBlock():null;if(!e.handler&&!e.finalizer){this.raise(e.start,"Missing catch or finally clause")}return this.finishNode(e,"TryStatement")};Z.parseVarStatement=function(e,t){this.next();this.parseVar(e,false,t);this.semicolon();return this.finishNode(e,"VariableDeclaration")};Z.parseWhileStatement=function(e){this.next();e.test=this.parseParenExpression();this.labels.push(Y);e.body=this.parseStatement("while");this.labels.pop();return this.finishNode(e,"WhileStatement")};Z.parseWithStatement=function(e){if(this.strict){this.raise(this.start,"'with' in strict mode")}this.next();e.object=this.parseParenExpression();e.body=this.parseStatement("with");return this.finishNode(e,"WithStatement")};Z.parseEmptyStatement=function(e){this.next();return this.finishNode(e,"EmptyStatement")};Z.parseLabeledStatement=function(e,t,r,s){for(var a=0,o=this.labels;a<o.length;a+=1){var u=o[a];if(u.name===t){this.raise(r.start,"Label '"+t+"' is already declared")}}var c=this.type.isLoop?"loop":this.type===_._switch?"switch":null;for(var h=this.labels.length-1;h>=0;h--){var p=this.labels[h];if(p.statementStart===e.start){p.statementStart=this.start;p.kind=c}else{break}}this.labels.push({name:t,kind:c,statementStart:this.start});e.body=this.parseStatement(s?s.indexOf("label")===-1?s+"label":s:"label");this.labels.pop();e.label=r;return this.finishNode(e,"LabeledStatement")};Z.parseExpressionStatement=function(e,t){e.expression=t;this.semicolon();return this.finishNode(e,"ExpressionStatement")};Z.parseBlock=function(e,t,r){if(e===void 0)e=true;if(t===void 0)t=this.startNode();t.body=[];this.expect(_.braceL);if(e){this.enterScope(0)}while(this.type!==_.braceR){var s=this.parseStatement(null);t.body.push(s)}if(r){this.strict=false}this.next();if(e){this.exitScope()}return this.finishNode(t,"BlockStatement")};Z.parseFor=function(e,t){e.init=t;this.expect(_.semi);e.test=this.type===_.semi?null:this.parseExpression();this.expect(_.semi);e.update=this.type===_.parenR?null:this.parseExpression();this.expect(_.parenR);e.body=this.parseStatement("for");this.exitScope();this.labels.pop();return this.finishNode(e,"ForStatement")};Z.parseForIn=function(e,t){var r=this.type===_._in;this.next();if(t.type==="VariableDeclaration"&&t.declarations[0].init!=null&&(!r||this.options.ecmaVersion<8||this.strict||t.kind!=="var"||t.declarations[0].id.type!=="Identifier")){this.raise(t.start,(r?"for-in":"for-of")+" loop variable declaration may not have an initializer")}e.left=t;e.right=r?this.parseExpression():this.parseMaybeAssign();this.expect(_.parenR);e.body=this.parseStatement("for");this.exitScope();this.labels.pop();return this.finishNode(e,r?"ForInStatement":"ForOfStatement")};Z.parseVar=function(e,t,r){e.declarations=[];e.kind=r;for(;;){var s=this.startNode();this.parseVarId(s,r);if(this.eat(_.eq)){s.init=this.parseMaybeAssign(t)}else if(r==="const"&&!(this.type===_._in||this.options.ecmaVersion>=6&&this.isContextual("of"))){this.unexpected()}else if(s.id.type!=="Identifier"&&!(t&&(this.type===_._in||this.isContextual("of")))){this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value")}else{s.init=null}e.declarations.push(this.finishNode(s,"VariableDeclarator"));if(!this.eat(_.comma)){break}}return e};Z.parseVarId=function(e,t){e.id=this.parseBindingAtom();this.checkLValPattern(e.id,t==="var"?$:H,false)};var re=1,ie=2,ne=4;Z.parseFunction=function(e,t,r,s){this.initFunction(e);if(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s){if(this.type===_.star&&t&ie){this.unexpected()}e.generator=this.eat(_.star)}if(this.options.ecmaVersion>=8){e.async=!!s}if(t&re){e.id=t&ne&&this.type!==_.name?null:this.parseIdent();if(e.id&&!(t&ie)){this.checkLValSimple(e.id,this.strict||e.generator||e.async?this.treatFunctionsAsVar?$:H:G)}}var a=this.yieldPos,o=this.awaitPos,u=this.awaitIdentPos;this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;this.enterScope(functionFlags(e.async,e.generator));if(!(t&re)){e.id=this.type===_.name?this.parseIdent():null}this.parseFunctionParams(e);this.parseFunctionBody(e,r,false);this.yieldPos=a;this.awaitPos=o;this.awaitIdentPos=u;return this.finishNode(e,t&re?"FunctionDeclaration":"FunctionExpression")};Z.parseFunctionParams=function(e){this.expect(_.parenL);e.params=this.parseBindingList(_.parenR,false,this.options.ecmaVersion>=8);this.checkYieldAwaitInDefaultParams()};Z.parseClass=function(e,t){this.next();var r=this.strict;this.strict=true;this.parseClassId(e,t);this.parseClassSuper(e);var s=this.enterClassBody();var a=this.startNode();var o=false;a.body=[];this.expect(_.braceL);while(this.type!==_.braceR){var u=this.parseClassElement(e.superClass!==null);if(u){a.body.push(u);if(u.type==="MethodDefinition"&&u.kind==="constructor"){if(o){this.raise(u.start,"Duplicate constructor in the same class")}o=true}else if(u.key.type==="PrivateIdentifier"&&isPrivateNameConflicted(s,u)){this.raiseRecoverable(u.key.start,"Identifier '#"+u.key.name+"' has already been declared")}}}this.strict=r;this.next();e.body=this.finishNode(a,"ClassBody");this.exitClassBody();return this.finishNode(e,t?"ClassDeclaration":"ClassExpression")};Z.parseClassElement=function(e){if(this.eat(_.semi)){return null}var t=this.options.ecmaVersion;var r=this.startNode();var s="";var a=false;var o=false;var u="method";r.static=false;if(this.eatContextual("static")){if(this.isClassElementNameStart()||this.type===_.star){r.static=true}else{s="static"}}if(!s&&t>=8&&this.eatContextual("async")){if((this.isClassElementNameStart()||this.type===_.star)&&!this.canInsertSemicolon()){o=true}else{s="async"}}if(!s&&(t>=9||!o)&&this.eat(_.star)){a=true}if(!s&&!o&&!a){var c=this.value;if(this.eatContextual("get")||this.eatContextual("set")){if(this.isClassElementNameStart()){u=c}else{s=c}}}if(s){r.computed=false;r.key=this.startNodeAt(this.lastTokStart,this.lastTokStartLoc);r.key.name=s;this.finishNode(r.key,"Identifier")}else{this.parseClassElementName(r)}if(t<13||this.type===_.parenL||u!=="method"||a||o){var h=!r.static&&checkKeyName(r,"constructor");var p=h&&e;if(h&&u!=="method"){this.raise(r.key.start,"Constructor can't have get/set modifier")}r.kind=h?"constructor":u;this.parseClassMethod(r,a,o,p)}else{this.parseClassField(r)}return r};Z.isClassElementNameStart=function(){return this.type===_.name||this.type===_.privateId||this.type===_.num||this.type===_.string||this.type===_.bracketL||this.type.keyword};Z.parseClassElementName=function(e){if(this.type===_.privateId){if(this.value==="constructor"){this.raise(this.start,"Classes can't have an element named '#constructor'")}e.computed=false;e.key=this.parsePrivateIdent()}else{this.parsePropertyName(e)}};Z.parseClassMethod=function(e,t,r,s){var a=e.key;if(e.kind==="constructor"){if(t){this.raise(a.start,"Constructor can't be a generator")}if(r){this.raise(a.start,"Constructor can't be an async method")}}else if(e.static&&checkKeyName(e,"prototype")){this.raise(a.start,"Classes may not have a static property named prototype")}var o=e.value=this.parseMethod(t,r,s);if(e.kind==="get"&&o.params.length!==0){this.raiseRecoverable(o.start,"getter should have no params")}if(e.kind==="set"&&o.params.length!==1){this.raiseRecoverable(o.start,"setter should have exactly one param")}if(e.kind==="set"&&o.params[0].type==="RestElement"){this.raiseRecoverable(o.params[0].start,"Setter cannot use rest params")}return this.finishNode(e,"MethodDefinition")};Z.parseClassField=function(e){if(checkKeyName(e,"constructor")){this.raise(e.key.start,"Classes can't have a field named 'constructor'")}else if(e.static&&checkKeyName(e,"prototype")){this.raise(e.key.start,"Classes can't have a static field named 'prototype'")}if(this.eat(_.eq)){var t=this.currentThisScope();var r=t.inClassFieldInit;t.inClassFieldInit=true;e.value=this.parseMaybeAssign();t.inClassFieldInit=r}else{e.value=null}this.semicolon();return this.finishNode(e,"PropertyDefinition")};Z.parseClassId=function(e,t){if(this.type===_.name){e.id=this.parseIdent();if(t){this.checkLValSimple(e.id,H,false)}}else{if(t===true){this.unexpected()}e.id=null}};Z.parseClassSuper=function(e){e.superClass=this.eat(_._extends)?this.parseExprSubscripts():null};Z.enterClassBody=function(){var e={declared:Object.create(null),used:[]};this.privateNameStack.push(e);return e.declared};Z.exitClassBody=function(){var e=this.privateNameStack.pop();var t=e.declared;var r=e.used;var s=this.privateNameStack.length;var a=s===0?null:this.privateNameStack[s-1];for(var o=0;o<r.length;++o){var u=r[o];if(!has(t,u.name)){if(a){a.used.push(u)}else{this.raiseRecoverable(u.start,"Private field '#"+u.name+"' must be declared in an enclosing class")}}}};function isPrivateNameConflicted(e,t){var r=t.key.name;var s=e[r];var a="true";if(t.type==="MethodDefinition"&&(t.kind==="get"||t.kind==="set")){a=(t.static?"s":"i")+t.kind}if(s==="iget"&&a==="iset"||s==="iset"&&a==="iget"||s==="sget"&&a==="sset"||s==="sset"&&a==="sget"){e[r]="true";return false}else if(!s){e[r]=a;return false}else{return true}}function checkKeyName(e,t){var r=e.computed;var s=e.key;return!r&&(s.type==="Identifier"&&s.name===t||s.type==="Literal"&&s.value===t)}Z.parseExport=function(e,t){this.next();if(this.eat(_.star)){if(this.options.ecmaVersion>=11){if(this.eatContextual("as")){e.exported=this.parseIdent(true);this.checkExport(t,e.exported.name,this.lastTokStart)}else{e.exported=null}}this.expectContextual("from");if(this.type!==_.string){this.unexpected()}e.source=this.parseExprAtom();this.semicolon();return this.finishNode(e,"ExportAllDeclaration")}if(this.eat(_._default)){this.checkExport(t,"default",this.lastTokStart);var r;if(this.type===_._function||(r=this.isAsyncFunction())){var s=this.startNode();this.next();if(r){this.next()}e.declaration=this.parseFunction(s,re|ne,false,r)}else if(this.type===_._class){var a=this.startNode();e.declaration=this.parseClass(a,"nullableID")}else{e.declaration=this.parseMaybeAssign();this.semicolon()}return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement()){e.declaration=this.parseStatement(null);if(e.declaration.type==="VariableDeclaration"){this.checkVariableExport(t,e.declaration.declarations)}else{this.checkExport(t,e.declaration.id.name,e.declaration.id.start)}e.specifiers=[];e.source=null}else{e.declaration=null;e.specifiers=this.parseExportSpecifiers(t);if(this.eatContextual("from")){if(this.type!==_.string){this.unexpected()}e.source=this.parseExprAtom()}else{for(var o=0,u=e.specifiers;o<u.length;o+=1){var c=u[o];this.checkUnreserved(c.local);this.checkLocalExport(c.local)}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")};Z.checkExport=function(e,t,r){if(!e){return}if(has(e,t)){this.raiseRecoverable(r,"Duplicate export '"+t+"'")}e[t]=true};Z.checkPatternExport=function(e,t){var r=t.type;if(r==="Identifier"){this.checkExport(e,t.name,t.start)}else if(r==="ObjectPattern"){for(var s=0,a=t.properties;s<a.length;s+=1){var o=a[s];this.checkPatternExport(e,o)}}else if(r==="ArrayPattern"){for(var u=0,c=t.elements;u<c.length;u+=1){var h=c[u];if(h){this.checkPatternExport(e,h)}}}else if(r==="Property"){this.checkPatternExport(e,t.value)}else if(r==="AssignmentPattern"){this.checkPatternExport(e,t.left)}else if(r==="RestElement"){this.checkPatternExport(e,t.argument)}else if(r==="ParenthesizedExpression"){this.checkPatternExport(e,t.expression)}};Z.checkVariableExport=function(e,t){if(!e){return}for(var r=0,s=t;r<s.length;r+=1){var a=s[r];this.checkPatternExport(e,a.id)}};Z.shouldParseExportStatement=function(){return this.type.keyword==="var"||this.type.keyword==="const"||this.type.keyword==="class"||this.type.keyword==="function"||this.isLet()||this.isAsyncFunction()};Z.parseExportSpecifiers=function(e){var t=[],r=true;this.expect(_.braceL);while(!this.eat(_.braceR)){if(!r){this.expect(_.comma);if(this.afterTrailingComma(_.braceR)){break}}else{r=false}var s=this.startNode();s.local=this.parseIdent(true);s.exported=this.eatContextual("as")?this.parseIdent(true):s.local;this.checkExport(e,s.exported.name,s.exported.start);t.push(this.finishNode(s,"ExportSpecifier"))}return t};Z.parseImport=function(e){this.next();if(this.type===_.string){e.specifiers=te;e.source=this.parseExprAtom()}else{e.specifiers=this.parseImportSpecifiers();this.expectContextual("from");e.source=this.type===_.string?this.parseExprAtom():this.unexpected()}this.semicolon();return this.finishNode(e,"ImportDeclaration")};Z.parseImportSpecifiers=function(){var e=[],t=true;if(this.type===_.name){var r=this.startNode();r.local=this.parseIdent();this.checkLValSimple(r.local,H);e.push(this.finishNode(r,"ImportDefaultSpecifier"));if(!this.eat(_.comma)){return e}}if(this.type===_.star){var s=this.startNode();this.next();this.expectContextual("as");s.local=this.parseIdent();this.checkLValSimple(s.local,H);e.push(this.finishNode(s,"ImportNamespaceSpecifier"));return e}this.expect(_.braceL);while(!this.eat(_.braceR)){if(!t){this.expect(_.comma);if(this.afterTrailingComma(_.braceR)){break}}else{t=false}var a=this.startNode();a.imported=this.parseIdent(true);if(this.eatContextual("as")){a.local=this.parseIdent()}else{this.checkUnreserved(a.imported);a.local=a.imported}this.checkLValSimple(a.local,H);e.push(this.finishNode(a,"ImportSpecifier"))}return e};Z.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t){e[t].directive=e[t].expression.raw.slice(1,-1)}};Z.isDirectiveCandidate=function(e){return e.type==="ExpressionStatement"&&e.expression.type==="Literal"&&typeof e.expression.value==="string"&&(this.input[e.start]==='"'||this.input[e.start]==="'")};var se=K.prototype;se.toAssignable=function(e,t,r){if(this.options.ecmaVersion>=6&&e){switch(e.type){case"Identifier":if(this.inAsync&&e.name==="await"){this.raise(e.start,"Cannot use 'await' as identifier inside an async function")}break;case"ObjectPattern":case"ArrayPattern":case"AssignmentPattern":case"RestElement":break;case"ObjectExpression":e.type="ObjectPattern";if(r){this.checkPatternErrors(r,true)}for(var s=0,a=e.properties;s<a.length;s+=1){var o=a[s];this.toAssignable(o,t);if(o.type==="RestElement"&&(o.argument.type==="ArrayPattern"||o.argument.type==="ObjectPattern")){this.raise(o.argument.start,"Unexpected token")}}break;case"Property":if(e.kind!=="init"){this.raise(e.key.start,"Object pattern can't contain getter or setter")}this.toAssignable(e.value,t);break;case"ArrayExpression":e.type="ArrayPattern";if(r){this.checkPatternErrors(r,true)}this.toAssignableList(e.elements,t);break;case"SpreadElement":e.type="RestElement";this.toAssignable(e.argument,t);if(e.argument.type==="AssignmentPattern"){this.raise(e.argument.start,"Rest elements cannot have a default value")}break;case"AssignmentExpression":if(e.operator!=="="){this.raise(e.left.end,"Only '=' operator can be used for specifying default value.")}e.type="AssignmentPattern";delete e.operator;this.toAssignable(e.left,t);break;case"ParenthesizedExpression":this.toAssignable(e.expression,t,r);break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!t){break}default:this.raise(e.start,"Assigning to rvalue")}}else if(r){this.checkPatternErrors(r,true)}return e};se.toAssignableList=function(e,t){var r=e.length;for(var s=0;s<r;s++){var a=e[s];if(a){this.toAssignable(a,t)}}if(r){var o=e[r-1];if(this.options.ecmaVersion===6&&t&&o&&o.type==="RestElement"&&o.argument.type!=="Identifier"){this.unexpected(o.argument.start)}}return e};se.parseSpread=function(e){var t=this.startNode();this.next();t.argument=this.parseMaybeAssign(false,e);return this.finishNode(t,"SpreadElement")};se.parseRestBinding=function(){var e=this.startNode();this.next();if(this.options.ecmaVersion===6&&this.type!==_.name){this.unexpected()}e.argument=this.parseBindingAtom();return this.finishNode(e,"RestElement")};se.parseBindingAtom=function(){if(this.options.ecmaVersion>=6){switch(this.type){case _.bracketL:var e=this.startNode();this.next();e.elements=this.parseBindingList(_.bracketR,true,true);return this.finishNode(e,"ArrayPattern");case _.braceL:return this.parseObj(true)}}return this.parseIdent()};se.parseBindingList=function(e,t,r){var s=[],a=true;while(!this.eat(e)){if(a){a=false}else{this.expect(_.comma)}if(t&&this.type===_.comma){s.push(null)}else if(r&&this.afterTrailingComma(e)){break}else if(this.type===_.ellipsis){var o=this.parseRestBinding();this.parseBindingListItem(o);s.push(o);if(this.type===_.comma){this.raise(this.start,"Comma is not permitted after the rest element")}this.expect(e);break}else{var u=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(u);s.push(u)}}return s};se.parseBindingListItem=function(e){return e};se.parseMaybeDefault=function(e,t,r){r=r||this.parseBindingAtom();if(this.options.ecmaVersion<6||!this.eat(_.eq)){return r}var s=this.startNodeAt(e,t);s.left=r;s.right=this.parseMaybeAssign();return this.finishNode(s,"AssignmentPattern")};se.checkLValSimple=function(e,t,r){if(t===void 0)t=U;var s=t!==U;switch(e.type){case"Identifier":if(this.strict&&this.reservedWordsStrictBind.test(e.name)){this.raiseRecoverable(e.start,(s?"Binding ":"Assigning to ")+e.name+" in strict mode")}if(s){if(t===H&&e.name==="let"){this.raiseRecoverable(e.start,"let is disallowed as a lexically bound name")}if(r){if(has(r,e.name)){this.raiseRecoverable(e.start,"Argument name clash")}r[e.name]=true}if(t!==z){this.declareName(e.name,t,e.start)}}break;case"ChainExpression":this.raiseRecoverable(e.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(s){this.raiseRecoverable(e.start,"Binding member expression")}break;case"ParenthesizedExpression":if(s){this.raiseRecoverable(e.start,"Binding parenthesized expression")}return this.checkLValSimple(e.expression,t,r);default:this.raise(e.start,(s?"Binding":"Assigning to")+" rvalue")}};se.checkLValPattern=function(e,t,r){if(t===void 0)t=U;switch(e.type){case"ObjectPattern":for(var s=0,a=e.properties;s<a.length;s+=1){var o=a[s];this.checkLValInnerPattern(o,t,r)}break;case"ArrayPattern":for(var u=0,c=e.elements;u<c.length;u+=1){var h=c[u];if(h){this.checkLValInnerPattern(h,t,r)}}break;default:this.checkLValSimple(e,t,r)}};se.checkLValInnerPattern=function(e,t,r){if(t===void 0)t=U;switch(e.type){case"Property":this.checkLValInnerPattern(e.value,t,r);break;case"AssignmentPattern":this.checkLValPattern(e.left,t,r);break;case"RestElement":this.checkLValPattern(e.argument,t,r);break;default:this.checkLValPattern(e,t,r)}};var ae=K.prototype;ae.checkPropClash=function(e,t,r){if(this.options.ecmaVersion>=9&&e.type==="SpreadElement"){return}if(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand)){return}var s=e.key;var a;switch(s.type){case"Identifier":a=s.name;break;case"Literal":a=String(s.value);break;default:return}var o=e.kind;if(this.options.ecmaVersion>=6){if(a==="__proto__"&&o==="init"){if(t.proto){if(r){if(r.doubleProto<0){r.doubleProto=s.start}}else{this.raiseRecoverable(s.start,"Redefinition of __proto__ property")}}t.proto=true}return}a="$"+a;var u=t[a];if(u){var c;if(o==="init"){c=this.strict&&u.init||u.get||u.set}else{c=u.init||u[o]}if(c){this.raiseRecoverable(s.start,"Redefinition of property")}}else{u=t[a]={init:false,get:false,set:false}}u[o]=true};ae.parseExpression=function(e,t){var r=this.start,s=this.startLoc;var a=this.parseMaybeAssign(e,t);if(this.type===_.comma){var o=this.startNodeAt(r,s);o.expressions=[a];while(this.eat(_.comma)){o.expressions.push(this.parseMaybeAssign(e,t))}return this.finishNode(o,"SequenceExpression")}return a};ae.parseMaybeAssign=function(e,t,r){if(this.isContextual("yield")){if(this.inGenerator){return this.parseYield(e)}else{this.exprAllowed=false}}var s=false,a=-1,o=-1;if(t){a=t.parenthesizedAssign;o=t.trailingComma;t.parenthesizedAssign=t.trailingComma=-1}else{t=new DestructuringErrors;s=true}var u=this.start,c=this.startLoc;if(this.type===_.parenL||this.type===_.name){this.potentialArrowAt=this.start;this.potentialArrowInForAwait=e==="await"}var h=this.parseMaybeConditional(e,t);if(r){h=r.call(this,h,u,c)}if(this.type.isAssign){var p=this.startNodeAt(u,c);p.operator=this.value;if(this.type===_.eq){h=this.toAssignable(h,false,t)}if(!s){t.parenthesizedAssign=t.trailingComma=t.doubleProto=-1}if(t.shorthandAssign>=h.start){t.shorthandAssign=-1}if(this.type===_.eq){this.checkLValPattern(h)}else{this.checkLValSimple(h)}p.left=h;this.next();p.right=this.parseMaybeAssign(e);return this.finishNode(p,"AssignmentExpression")}else{if(s){this.checkExpressionErrors(t,true)}}if(a>-1){t.parenthesizedAssign=a}if(o>-1){t.trailingComma=o}return h};ae.parseMaybeConditional=function(e,t){var r=this.start,s=this.startLoc;var a=this.parseExprOps(e,t);if(this.checkExpressionErrors(t)){return a}if(this.eat(_.question)){var o=this.startNodeAt(r,s);o.test=a;o.consequent=this.parseMaybeAssign();this.expect(_.colon);o.alternate=this.parseMaybeAssign(e);return this.finishNode(o,"ConditionalExpression")}return a};ae.parseExprOps=function(e,t){var r=this.start,s=this.startLoc;var a=this.parseMaybeUnary(t,false);if(this.checkExpressionErrors(t)){return a}return a.start===r&&a.type==="ArrowFunctionExpression"?a:this.parseExprOp(a,r,s,-1,e)};ae.parseExprOp=function(e,t,r,s,a){var o=this.type.binop;if(o!=null&&(!a||this.type!==_._in)){if(o>s){var u=this.type===_.logicalOR||this.type===_.logicalAND;var c=this.type===_.coalesce;if(c){o=_.logicalAND.binop}var h=this.value;this.next();var p=this.start,d=this.startLoc;var v=this.parseExprOp(this.parseMaybeUnary(null,false),p,d,o,a);var m=this.buildBinary(t,r,e,v,h,u||c);if(u&&this.type===_.coalesce||c&&(this.type===_.logicalOR||this.type===_.logicalAND)){this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses")}return this.parseExprOp(m,t,r,s,a)}}return e};ae.buildBinary=function(e,t,r,s,a,o){var u=this.startNodeAt(e,t);u.left=r;u.operator=a;u.right=s;return this.finishNode(u,o?"LogicalExpression":"BinaryExpression")};ae.parseMaybeUnary=function(e,t,r){var s=this.start,a=this.startLoc,o;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)){o=this.parseAwait();t=true}else if(this.type.prefix){var u=this.startNode(),c=this.type===_.incDec;u.operator=this.value;u.prefix=true;this.next();u.argument=this.parseMaybeUnary(null,true,c);this.checkExpressionErrors(e,true);if(c){this.checkLValSimple(u.argument)}else if(this.strict&&u.operator==="delete"&&u.argument.type==="Identifier"){this.raiseRecoverable(u.start,"Deleting local variable in strict mode")}else if(u.operator==="delete"&&isPrivateFieldAccess(u.argument)){this.raiseRecoverable(u.start,"Private fields can not be deleted")}else{t=true}o=this.finishNode(u,c?"UpdateExpression":"UnaryExpression")}else{o=this.parseExprSubscripts(e);if(this.checkExpressionErrors(e)){return o}while(this.type.postfix&&!this.canInsertSemicolon()){var h=this.startNodeAt(s,a);h.operator=this.value;h.prefix=false;h.argument=o;this.checkLValSimple(o);this.next();o=this.finishNode(h,"UpdateExpression")}}if(!r&&this.eat(_.starstar)){if(t){this.unexpected(this.lastTokStart)}else{return this.buildBinary(s,a,o,this.parseMaybeUnary(null,false),"**",false)}}else{return o}};function isPrivateFieldAccess(e){return e.type==="MemberExpression"&&e.property.type==="PrivateIdentifier"||e.type==="ChainExpression"&&isPrivateFieldAccess(e.expression)}ae.parseExprSubscripts=function(e){var t=this.start,r=this.startLoc;var s=this.parseExprAtom(e);if(s.type==="ArrowFunctionExpression"&&this.input.slice(this.lastTokStart,this.lastTokEnd)!==")"){return s}var a=this.parseSubscripts(s,t,r);if(e&&a.type==="MemberExpression"){if(e.parenthesizedAssign>=a.start){e.parenthesizedAssign=-1}if(e.parenthesizedBind>=a.start){e.parenthesizedBind=-1}if(e.trailingComma>=a.start){e.trailingComma=-1}}return a};ae.parseSubscripts=function(e,t,r,s){var a=this.options.ecmaVersion>=8&&e.type==="Identifier"&&e.name==="async"&&this.lastTokEnd===e.end&&!this.canInsertSemicolon()&&e.end-e.start===5&&this.potentialArrowAt===e.start;var o=false;while(true){var u=this.parseSubscript(e,t,r,s,a,o);if(u.optional){o=true}if(u===e||u.type==="ArrowFunctionExpression"){if(o){var c=this.startNodeAt(t,r);c.expression=u;u=this.finishNode(c,"ChainExpression")}return u}e=u}};ae.parseSubscript=function(e,t,r,s,a,o){var u=this.options.ecmaVersion>=11;var c=u&&this.eat(_.questionDot);if(s&&c){this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions")}var h=this.eat(_.bracketL);if(h||c&&this.type!==_.parenL&&this.type!==_.backQuote||this.eat(_.dot)){var p=this.startNodeAt(t,r);p.object=e;if(h){p.property=this.parseExpression();this.expect(_.bracketR)}else if(this.type===_.privateId&&e.type!=="Super"){p.property=this.parsePrivateIdent()}else{p.property=this.parseIdent(this.options.allowReserved!=="never")}p.computed=!!h;if(u){p.optional=c}e=this.finishNode(p,"MemberExpression")}else if(!s&&this.eat(_.parenL)){var d=new DestructuringErrors,v=this.yieldPos,m=this.awaitPos,g=this.awaitIdentPos;this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;var y=this.parseExprList(_.parenR,this.options.ecmaVersion>=8,false,d);if(a&&!c&&!this.canInsertSemicolon()&&this.eat(_.arrow)){this.checkPatternErrors(d,false);this.checkYieldAwaitInDefaultParams();if(this.awaitIdentPos>0){this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function")}this.yieldPos=v;this.awaitPos=m;this.awaitIdentPos=g;return this.parseArrowExpression(this.startNodeAt(t,r),y,true)}this.checkExpressionErrors(d,true);this.yieldPos=v||this.yieldPos;this.awaitPos=m||this.awaitPos;this.awaitIdentPos=g||this.awaitIdentPos;var E=this.startNodeAt(t,r);E.callee=e;E.arguments=y;if(u){E.optional=c}e=this.finishNode(E,"CallExpression")}else if(this.type===_.backQuote){if(c||o){this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions")}var x=this.startNodeAt(t,r);x.tag=e;x.quasi=this.parseTemplate({isTagged:true});e=this.finishNode(x,"TaggedTemplateExpression")}return e};ae.parseExprAtom=function(e){if(this.type===_.slash){this.readRegexp()}var t,r=this.potentialArrowAt===this.start;switch(this.type){case _._super:if(!this.allowSuper){this.raise(this.start,"'super' keyword outside a method")}t=this.startNode();this.next();if(this.type===_.parenL&&!this.allowDirectSuper){this.raise(t.start,"super() call outside constructor of a subclass")}if(this.type!==_.dot&&this.type!==_.bracketL&&this.type!==_.parenL){this.unexpected()}return this.finishNode(t,"Super");case _._this:t=this.startNode();this.next();return this.finishNode(t,"ThisExpression");case _.name:var s=this.start,a=this.startLoc,o=this.containsEsc;var u=this.parseIdent(false);if(this.options.ecmaVersion>=8&&!o&&u.name==="async"&&!this.canInsertSemicolon()&&this.eat(_._function)){return this.parseFunction(this.startNodeAt(s,a),0,false,true)}if(r&&!this.canInsertSemicolon()){if(this.eat(_.arrow)){return this.parseArrowExpression(this.startNodeAt(s,a),[u],false)}if(this.options.ecmaVersion>=8&&u.name==="async"&&this.type===_.name&&!o&&(!this.potentialArrowInForAwait||this.value!=="of"||this.containsEsc)){u=this.parseIdent(false);if(this.canInsertSemicolon()||!this.eat(_.arrow)){this.unexpected()}return this.parseArrowExpression(this.startNodeAt(s,a),[u],true)}}return u;case _.regexp:var c=this.value;t=this.parseLiteral(c.value);t.regex={pattern:c.pattern,flags:c.flags};return t;case _.num:case _.string:return this.parseLiteral(this.value);case _._null:case _._true:case _._false:t=this.startNode();t.value=this.type===_._null?null:this.type===_._true;t.raw=this.type.keyword;this.next();return this.finishNode(t,"Literal");case _.parenL:var h=this.start,p=this.parseParenAndDistinguishExpression(r);if(e){if(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(p)){e.parenthesizedAssign=h}if(e.parenthesizedBind<0){e.parenthesizedBind=h}}return p;case _.bracketL:t=this.startNode();this.next();t.elements=this.parseExprList(_.bracketR,true,true,e);return this.finishNode(t,"ArrayExpression");case _.braceL:return this.parseObj(false,e);case _._function:t=this.startNode();this.next();return this.parseFunction(t,0);case _._class:return this.parseClass(this.startNode(),false);case _._new:return this.parseNew();case _.backQuote:return this.parseTemplate();case _._import:if(this.options.ecmaVersion>=11){return this.parseExprImport()}else{return this.unexpected()}default:this.unexpected()}};ae.parseExprImport=function(){var e=this.startNode();if(this.containsEsc){this.raiseRecoverable(this.start,"Escape sequence in keyword import")}var t=this.parseIdent(true);switch(this.type){case _.parenL:return this.parseDynamicImport(e);case _.dot:e.meta=t;return this.parseImportMeta(e);default:this.unexpected()}};ae.parseDynamicImport=function(e){this.next();e.source=this.parseMaybeAssign();if(!this.eat(_.parenR)){var t=this.start;if(this.eat(_.comma)&&this.eat(_.parenR)){this.raiseRecoverable(t,"Trailing comma is not allowed in import()")}else{this.unexpected(t)}}return this.finishNode(e,"ImportExpression")};ae.parseImportMeta=function(e){this.next();var t=this.containsEsc;e.property=this.parseIdent(true);if(e.property.name!=="meta"){this.raiseRecoverable(e.property.start,"The only valid meta property for import is 'import.meta'")}if(t){this.raiseRecoverable(e.start,"'import.meta' must not contain escaped characters")}if(this.options.sourceType!=="module"&&!this.options.allowImportExportEverywhere){this.raiseRecoverable(e.start,"Cannot use 'import.meta' outside a module")}return this.finishNode(e,"MetaProperty")};ae.parseLiteral=function(e){var t=this.startNode();t.value=e;t.raw=this.input.slice(this.start,this.end);if(t.raw.charCodeAt(t.raw.length-1)===110){t.bigint=t.raw.slice(0,-1).replace(/_/g,"")}this.next();return this.finishNode(t,"Literal")};ae.parseParenExpression=function(){this.expect(_.parenL);var e=this.parseExpression();this.expect(_.parenR);return e};ae.parseParenAndDistinguishExpression=function(e){var t=this.start,r=this.startLoc,s,a=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var o=this.start,u=this.startLoc;var c=[],h=true,p=false;var d=new DestructuringErrors,v=this.yieldPos,m=this.awaitPos,g;this.yieldPos=0;this.awaitPos=0;while(this.type!==_.parenR){h?h=false:this.expect(_.comma);if(a&&this.afterTrailingComma(_.parenR,true)){p=true;break}else if(this.type===_.ellipsis){g=this.start;c.push(this.parseParenItem(this.parseRestBinding()));if(this.type===_.comma){this.raise(this.start,"Comma is not permitted after the rest element")}break}else{c.push(this.parseMaybeAssign(false,d,this.parseParenItem))}}var y=this.start,E=this.startLoc;this.expect(_.parenR);if(e&&!this.canInsertSemicolon()&&this.eat(_.arrow)){this.checkPatternErrors(d,false);this.checkYieldAwaitInDefaultParams();this.yieldPos=v;this.awaitPos=m;return this.parseParenArrowList(t,r,c)}if(!c.length||p){this.unexpected(this.lastTokStart)}if(g){this.unexpected(g)}this.checkExpressionErrors(d,true);this.yieldPos=v||this.yieldPos;this.awaitPos=m||this.awaitPos;if(c.length>1){s=this.startNodeAt(o,u);s.expressions=c;this.finishNodeAt(s,"SequenceExpression",y,E)}else{s=c[0]}}else{s=this.parseParenExpression()}if(this.options.preserveParens){var x=this.startNodeAt(t,r);x.expression=s;return this.finishNode(x,"ParenthesizedExpression")}else{return s}};ae.parseParenItem=function(e){return e};ae.parseParenArrowList=function(e,t,r){return this.parseArrowExpression(this.startNodeAt(e,t),r)};var oe=[];ae.parseNew=function(){if(this.containsEsc){this.raiseRecoverable(this.start,"Escape sequence in keyword new")}var e=this.startNode();var t=this.parseIdent(true);if(this.options.ecmaVersion>=6&&this.eat(_.dot)){e.meta=t;var r=this.containsEsc;e.property=this.parseIdent(true);if(e.property.name!=="target"){this.raiseRecoverable(e.property.start,"The only valid meta property for new is 'new.target'")}if(r){this.raiseRecoverable(e.start,"'new.target' must not contain escaped characters")}if(!this.inNonArrowFunction){this.raiseRecoverable(e.start,"'new.target' can only be used in functions")}return this.finishNode(e,"MetaProperty")}var s=this.start,a=this.startLoc,o=this.type===_._import;e.callee=this.parseSubscripts(this.parseExprAtom(),s,a,true);if(o&&e.callee.type==="ImportExpression"){this.raise(s,"Cannot use new with import()")}if(this.eat(_.parenL)){e.arguments=this.parseExprList(_.parenR,this.options.ecmaVersion>=8,false)}else{e.arguments=oe}return this.finishNode(e,"NewExpression")};ae.parseTemplateElement=function(e){var t=e.isTagged;var r=this.startNode();if(this.type===_.invalidTemplate){if(!t){this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal")}r.value={raw:this.value,cooked:null}}else{r.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value}}this.next();r.tail=this.type===_.backQuote;return this.finishNode(r,"TemplateElement")};ae.parseTemplate=function(e){if(e===void 0)e={};var t=e.isTagged;if(t===void 0)t=false;var r=this.startNode();this.next();r.expressions=[];var s=this.parseTemplateElement({isTagged:t});r.quasis=[s];while(!s.tail){if(this.type===_.eof){this.raise(this.pos,"Unterminated template literal")}this.expect(_.dollarBraceL);r.expressions.push(this.parseExpression());this.expect(_.braceR);r.quasis.push(s=this.parseTemplateElement({isTagged:t}))}this.next();return this.finishNode(r,"TemplateLiteral")};ae.isAsyncProp=function(e){return!e.computed&&e.key.type==="Identifier"&&e.key.name==="async"&&(this.type===_.name||this.type===_.num||this.type===_.string||this.type===_.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===_.star)&&!E.test(this.input.slice(this.lastTokEnd,this.start))};ae.parseObj=function(e,t){var r=this.startNode(),s=true,a={};r.properties=[];this.next();while(!this.eat(_.braceR)){if(!s){this.expect(_.comma);if(this.options.ecmaVersion>=5&&this.afterTrailingComma(_.braceR)){break}}else{s=false}var o=this.parseProperty(e,t);if(!e){this.checkPropClash(o,a,t)}r.properties.push(o)}return this.finishNode(r,e?"ObjectPattern":"ObjectExpression")};ae.parseProperty=function(e,t){var r=this.startNode(),s,a,o,u;if(this.options.ecmaVersion>=9&&this.eat(_.ellipsis)){if(e){r.argument=this.parseIdent(false);if(this.type===_.comma){this.raise(this.start,"Comma is not permitted after the rest element")}return this.finishNode(r,"RestElement")}if(this.type===_.parenL&&t){if(t.parenthesizedAssign<0){t.parenthesizedAssign=this.start}if(t.parenthesizedBind<0){t.parenthesizedBind=this.start}}r.argument=this.parseMaybeAssign(false,t);if(this.type===_.comma&&t&&t.trailingComma<0){t.trailingComma=this.start}return this.finishNode(r,"SpreadElement")}if(this.options.ecmaVersion>=6){r.method=false;r.shorthand=false;if(e||t){o=this.start;u=this.startLoc}if(!e){s=this.eat(_.star)}}var c=this.containsEsc;this.parsePropertyName(r);if(!e&&!c&&this.options.ecmaVersion>=8&&!s&&this.isAsyncProp(r)){a=true;s=this.options.ecmaVersion>=9&&this.eat(_.star);this.parsePropertyName(r,t)}else{a=false}this.parsePropertyValue(r,e,s,a,o,u,t,c);return this.finishNode(r,"Property")};ae.parsePropertyValue=function(e,t,r,s,a,o,u,c){if((r||s)&&this.type===_.colon){this.unexpected()}if(this.eat(_.colon)){e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(false,u);e.kind="init"}else if(this.options.ecmaVersion>=6&&this.type===_.parenL){if(t){this.unexpected()}e.kind="init";e.method=true;e.value=this.parseMethod(r,s)}else if(!t&&!c&&this.options.ecmaVersion>=5&&!e.computed&&e.key.type==="Identifier"&&(e.key.name==="get"||e.key.name==="set")&&(this.type!==_.comma&&this.type!==_.braceR&&this.type!==_.eq)){if(r||s){this.unexpected()}e.kind=e.key.name;this.parsePropertyName(e);e.value=this.parseMethod(false);var h=e.kind==="get"?0:1;if(e.value.params.length!==h){var p=e.value.start;if(e.kind==="get"){this.raiseRecoverable(p,"getter should have no params")}else{this.raiseRecoverable(p,"setter should have exactly one param")}}else{if(e.kind==="set"&&e.value.params[0].type==="RestElement"){this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}}}else if(this.options.ecmaVersion>=6&&!e.computed&&e.key.type==="Identifier"){if(r||s){this.unexpected()}this.checkUnreserved(e.key);if(e.key.name==="await"&&!this.awaitIdentPos){this.awaitIdentPos=a}e.kind="init";if(t){e.value=this.parseMaybeDefault(a,o,this.copyNode(e.key))}else if(this.type===_.eq&&u){if(u.shorthandAssign<0){u.shorthandAssign=this.start}e.value=this.parseMaybeDefault(a,o,this.copyNode(e.key))}else{e.value=this.copyNode(e.key)}e.shorthand=true}else{this.unexpected()}};ae.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(_.bracketL)){e.computed=true;e.key=this.parseMaybeAssign();this.expect(_.bracketR);return e.key}else{e.computed=false}}return e.key=this.type===_.num||this.type===_.string?this.parseExprAtom():this.parseIdent(this.options.allowReserved!=="never")};ae.initFunction=function(e){e.id=null;if(this.options.ecmaVersion>=6){e.generator=e.expression=false}if(this.options.ecmaVersion>=8){e.async=false}};ae.parseMethod=function(e,t,r){var s=this.startNode(),a=this.yieldPos,o=this.awaitPos,u=this.awaitIdentPos;this.initFunction(s);if(this.options.ecmaVersion>=6){s.generator=e}if(this.options.ecmaVersion>=8){s.async=!!t}this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;this.enterScope(functionFlags(t,s.generator)|V|(r?q:0));this.expect(_.parenL);s.params=this.parseBindingList(_.parenR,false,this.options.ecmaVersion>=8);this.checkYieldAwaitInDefaultParams();this.parseFunctionBody(s,false,true);this.yieldPos=a;this.awaitPos=o;this.awaitIdentPos=u;return this.finishNode(s,"FunctionExpression")};ae.parseArrowExpression=function(e,t,r){var s=this.yieldPos,a=this.awaitPos,o=this.awaitIdentPos;this.enterScope(functionFlags(r,false)|j);this.initFunction(e);if(this.options.ecmaVersion>=8){e.async=!!r}this.yieldPos=0;this.awaitPos=0;this.awaitIdentPos=0;e.params=this.toAssignableList(t,true);this.parseFunctionBody(e,true,false);this.yieldPos=s;this.awaitPos=a;this.awaitIdentPos=o;return this.finishNode(e,"ArrowFunctionExpression")};ae.parseFunctionBody=function(e,t,r){var s=t&&this.type!==_.braceL;var a=this.strict,o=false;if(s){e.body=this.parseMaybeAssign();e.expression=true;this.checkParams(e,false)}else{var u=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);if(!a||u){o=this.strictDirective(this.end);if(o&&u){this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list")}}var c=this.labels;this.labels=[];if(o){this.strict=true}this.checkParams(e,!a&&!o&&!t&&!r&&this.isSimpleParamList(e.params));if(this.strict&&e.id){this.checkLValSimple(e.id,z)}e.body=this.parseBlock(false,undefined,o&&!a);e.expression=false;this.adaptDirectivePrologue(e.body.body);this.labels=c}this.exitScope()};ae.isSimpleParamList=function(e){for(var t=0,r=e;t<r.length;t+=1){var s=r[t];if(s.type!=="Identifier"){return false}}return true};ae.checkParams=function(e,t){var r=Object.create(null);for(var s=0,a=e.params;s<a.length;s+=1){var o=a[s];this.checkLValInnerPattern(o,$,t?null:r)}};ae.parseExprList=function(e,t,r,s){var a=[],o=true;while(!this.eat(e)){if(!o){this.expect(_.comma);if(t&&this.afterTrailingComma(e)){break}}else{o=false}var u=void 0;if(r&&this.type===_.comma){u=null}else if(this.type===_.ellipsis){u=this.parseSpread(s);if(s&&this.type===_.comma&&s.trailingComma<0){s.trailingComma=this.start}}else{u=this.parseMaybeAssign(false,s)}a.push(u)}return a};ae.checkUnreserved=function(e){var t=e.start;var r=e.end;var s=e.name;if(this.inGenerator&&s==="yield"){this.raiseRecoverable(t,"Cannot use 'yield' as identifier inside a generator")}if(this.inAsync&&s==="await"){this.raiseRecoverable(t,"Cannot use 'await' as identifier inside an async function")}if(this.currentThisScope().inClassFieldInit&&s==="arguments"){this.raiseRecoverable(t,"Cannot use 'arguments' in class field initializer")}if(this.keywords.test(s)){this.raise(t,"Unexpected keyword '"+s+"'")}if(this.options.ecmaVersion<6&&this.input.slice(t,r).indexOf("\\")!==-1){return}var a=this.strict?this.reservedWordsStrict:this.reservedWords;if(a.test(s)){if(!this.inAsync&&s==="await"){this.raiseRecoverable(t,"Cannot use keyword 'await' outside an async function")}this.raiseRecoverable(t,"The keyword '"+s+"' is reserved")}};ae.parseIdent=function(e,t){var r=this.startNode();if(this.type===_.name){r.name=this.value}else if(this.type.keyword){r.name=this.type.keyword;if((r.name==="class"||r.name==="function")&&(this.lastTokEnd!==this.lastTokStart+1||this.input.charCodeAt(this.lastTokStart)!==46)){this.context.pop()}}else{this.unexpected()}this.next(!!e);this.finishNode(r,"Identifier");if(!e){this.checkUnreserved(r);if(r.name==="await"&&!this.awaitIdentPos){this.awaitIdentPos=r.start}}return r};ae.parsePrivateIdent=function(){var e=this.startNode();if(this.type===_.privateId){e.name=this.value}else{this.unexpected()}this.next();this.finishNode(e,"PrivateIdentifier");if(this.privateNameStack.length===0){this.raise(e.start,"Private field '#"+e.name+"' must be declared in an enclosing class")}else{this.privateNameStack[this.privateNameStack.length-1].used.push(e)}return e};ae.parseYield=function(e){if(!this.yieldPos){this.yieldPos=this.start}var t=this.startNode();this.next();if(this.type===_.semi||this.canInsertSemicolon()||this.type!==_.star&&!this.type.startsExpr){t.delegate=false;t.argument=null}else{t.delegate=this.eat(_.star);t.argument=this.parseMaybeAssign(e)}return this.finishNode(t,"YieldExpression")};ae.parseAwait=function(){if(!this.awaitPos){this.awaitPos=this.start}var e=this.startNode();this.next();e.argument=this.parseMaybeUnary(null,true);return this.finishNode(e,"AwaitExpression")};var ue=K.prototype;ue.raise=function(e,t){var r=getLineInfo(this.input,e);t+=" ("+r.line+":"+r.column+")";var s=new SyntaxError(t);s.pos=e;s.loc=r;s.raisedAt=this.pos;throw s};ue.raiseRecoverable=ue.raise;ue.curPosition=function(){if(this.options.locations){return new F(this.curLine,this.pos-this.lineStart)}};var le=K.prototype;var ce=function Scope(e){this.flags=e;this.var=[];this.lexical=[];this.functions=[];this.inClassFieldInit=false};le.enterScope=function(e){this.scopeStack.push(new ce(e))};le.exitScope=function(){this.scopeStack.pop()};le.treatFunctionsAsVarInScope=function(e){return e.flags&N||!this.inModule&&e.flags&B};le.declareName=function(e,t,r){var s=false;if(t===H){var a=this.currentScope();s=a.lexical.indexOf(e)>-1||a.functions.indexOf(e)>-1||a.var.indexOf(e)>-1;a.lexical.push(e);if(this.inModule&&a.flags&B){delete this.undefinedExports[e]}}else if(t===W){var o=this.currentScope();o.lexical.push(e)}else if(t===G){var u=this.currentScope();if(this.treatFunctionsAsVar){s=u.lexical.indexOf(e)>-1}else{s=u.lexical.indexOf(e)>-1||u.var.indexOf(e)>-1}u.functions.push(e)}else{for(var c=this.scopeStack.length-1;c>=0;--c){var h=this.scopeStack[c];if(h.lexical.indexOf(e)>-1&&!(h.flags&M&&h.lexical[0]===e)||!this.treatFunctionsAsVarInScope(h)&&h.functions.indexOf(e)>-1){s=true;break}h.var.push(e);if(this.inModule&&h.flags&B){delete this.undefinedExports[e]}if(h.flags&O){break}}}if(s){this.raiseRecoverable(r,"Identifier '"+e+"' has already been declared")}};le.checkLocalExport=function(e){if(this.scopeStack[0].lexical.indexOf(e.name)===-1&&this.scopeStack[0].var.indexOf(e.name)===-1){this.undefinedExports[e.name]=e}};le.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]};le.currentVarScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&O){return t}}};le.currentThisScope=function(){for(var e=this.scopeStack.length-1;;e--){var t=this.scopeStack[e];if(t.flags&O&&!(t.flags&j)){return t}}};var fe=function Node(e,t,r){this.type="";this.start=t;this.end=0;if(e.options.locations){this.loc=new R(e,r)}if(e.options.directSourceFile){this.sourceFile=e.options.directSourceFile}if(e.options.ranges){this.range=[t,0]}};var he=K.prototype;he.startNode=function(){return new fe(this,this.start,this.startLoc)};he.startNodeAt=function(e,t){return new fe(this,e,t)};function finishNodeAt(e,t,r,s){e.type=t;e.end=r;if(this.options.locations){e.loc.end=s}if(this.options.ranges){e.range[1]=r}return e}he.finishNode=function(e,t){return finishNodeAt.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)};he.finishNodeAt=function(e,t,r,s){return finishNodeAt.call(this,e,t,r,s)};he.copyNode=function(e){var t=new fe(this,e.start,this.startLoc);for(var r in e){t[r]=e[r]}return t};var pe=function TokContext(e,t,r,s,a){this.token=e;this.isExpr=!!t;this.preserveSpace=!!r;this.override=s;this.generator=!!a};var de={b_stat:new pe("{",false),b_expr:new pe("{",true),b_tmpl:new pe("${",false),p_stat:new pe("(",false),p_expr:new pe("(",true),q_tmpl:new pe("`",true,true,(function(e){return e.tryReadTemplateToken()})),f_stat:new pe("function",false),f_expr:new pe("function",true),f_expr_gen:new pe("function",true,false,null,true),f_gen:new pe("function",false,false,null,true)};var ve=K.prototype;ve.initialContext=function(){return[de.b_stat]};ve.braceIsBlock=function(e){var t=this.curContext();if(t===de.f_expr||t===de.f_stat){return true}if(e===_.colon&&(t===de.b_stat||t===de.b_expr)){return!t.isExpr}if(e===_._return||e===_.name&&this.exprAllowed){return E.test(this.input.slice(this.lastTokEnd,this.start))}if(e===_._else||e===_.semi||e===_.eof||e===_.parenR||e===_.arrow){return true}if(e===_.braceL){return t===de.b_stat}if(e===_._var||e===_._const||e===_.name){return false}return!this.exprAllowed};ve.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if(t.token==="function"){return t.generator}}return false};ve.updateContext=function(e){var t,r=this.type;if(r.keyword&&e===_.dot){this.exprAllowed=false}else if(t=r.updateContext){t.call(this,e)}else{this.exprAllowed=r.beforeExpr}};_.parenR.updateContext=_.braceR.updateContext=function(){if(this.context.length===1){this.exprAllowed=true;return}var e=this.context.pop();if(e===de.b_stat&&this.curContext().token==="function"){e=this.context.pop()}this.exprAllowed=!e.isExpr};_.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?de.b_stat:de.b_expr);this.exprAllowed=true};_.dollarBraceL.updateContext=function(){this.context.push(de.b_tmpl);this.exprAllowed=true};_.parenL.updateContext=function(e){var t=e===_._if||e===_._for||e===_._with||e===_._while;this.context.push(t?de.p_stat:de.p_expr);this.exprAllowed=true};_.incDec.updateContext=function(){};_._function.updateContext=_._class.updateContext=function(e){if(e.beforeExpr&&e!==_._else&&!(e===_.semi&&this.curContext()!==de.p_stat)&&!(e===_._return&&E.test(this.input.slice(this.lastTokEnd,this.start)))&&!((e===_.colon||e===_.braceL)&&this.curContext()===de.b_stat)){this.context.push(de.f_expr)}else{this.context.push(de.f_stat)}this.exprAllowed=false};_.backQuote.updateContext=function(){if(this.curContext()===de.q_tmpl){this.context.pop()}else{this.context.push(de.q_tmpl)}this.exprAllowed=false};_.star.updateContext=function(e){if(e===_._function){var t=this.context.length-1;if(this.context[t]===de.f_expr){this.context[t]=de.f_expr_gen}else{this.context[t]=de.f_gen}}this.exprAllowed=true};_.name.updateContext=function(e){var t=false;if(this.options.ecmaVersion>=6&&e!==_.dot){if(this.value==="of"&&!this.exprAllowed||this.value==="yield"&&this.inGeneratorContext()){t=true}}this.exprAllowed=t};var me="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS";var ge=me+" Extended_Pictographic";var be=ge;var ye=be+" EBase EComp EMod EPres ExtPict";var _e={9:me,10:ge,11:be,12:ye};var Ee="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu";var xe="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb";var we=xe+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd";var De=we+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho";var Ce=De+" Chorasmian Chrs Diak Dives_Akuru Khitan_Small_Script Kits Yezi Yezidi";var Ae={9:xe,10:we,11:De,12:Ce};var Se={};function buildUnicodeData(e){var t=Se[e]={binary:wordsRegexp(_e[e]+" "+Ee),nonBinary:{General_Category:wordsRegexp(Ee),Script:wordsRegexp(Ae[e])}};t.nonBinary.Script_Extensions=t.nonBinary.Script;t.nonBinary.gc=t.nonBinary.General_Category;t.nonBinary.sc=t.nonBinary.Script;t.nonBinary.scx=t.nonBinary.Script_Extensions}buildUnicodeData(9);buildUnicodeData(10);buildUnicodeData(11);buildUnicodeData(12);var ke=K.prototype;var Fe=function RegExpValidationState(e){this.parser=e;this.validFlags="gim"+(e.options.ecmaVersion>=6?"uy":"")+(e.options.ecmaVersion>=9?"s":"")+(e.options.ecmaVersion>=13?"d":"");this.unicodeProperties=Se[e.options.ecmaVersion>=12?12:e.options.ecmaVersion];this.source="";this.flags="";this.start=0;this.switchU=false;this.switchN=false;this.pos=0;this.lastIntValue=0;this.lastStringValue="";this.lastAssertionIsQuantifiable=false;this.numCapturingParens=0;this.maxBackReference=0;this.groupNames=[];this.backReferenceNames=[]};Fe.prototype.reset=function reset(e,t,r){var s=r.indexOf("u")!==-1;this.start=e|0;this.source=t+"";this.flags=r;this.switchU=s&&this.parser.options.ecmaVersion>=6;this.switchN=s&&this.parser.options.ecmaVersion>=9};Fe.prototype.raise=function raise(e){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+e)};Fe.prototype.at=function at(e,t){if(t===void 0)t=false;var r=this.source;var s=r.length;if(e>=s){return-1}var a=r.charCodeAt(e);if(!(t||this.switchU)||a<=55295||a>=57344||e+1>=s){return a}var o=r.charCodeAt(e+1);return o>=56320&&o<=57343?(a<<10)+o-56613888:a};Fe.prototype.nextIndex=function nextIndex(e,t){if(t===void 0)t=false;var r=this.source;var s=r.length;if(e>=s){return s}var a=r.charCodeAt(e),o;if(!(t||this.switchU)||a<=55295||a>=57344||e+1>=s||(o=r.charCodeAt(e+1))<56320||o>57343){return e+1}return e+2};Fe.prototype.current=function current(e){if(e===void 0)e=false;return this.at(this.pos,e)};Fe.prototype.lookahead=function lookahead(e){if(e===void 0)e=false;return this.at(this.nextIndex(this.pos,e),e)};Fe.prototype.advance=function advance(e){if(e===void 0)e=false;this.pos=this.nextIndex(this.pos,e)};Fe.prototype.eat=function eat(e,t){if(t===void 0)t=false;if(this.current(t)===e){this.advance(t);return true}return false};function codePointToString(e){if(e<=65535){return String.fromCharCode(e)}e-=65536;return String.fromCharCode((e>>10)+55296,(e&1023)+56320)}ke.validateRegExpFlags=function(e){var t=e.validFlags;var r=e.flags;for(var s=0;s<r.length;s++){var a=r.charAt(s);if(t.indexOf(a)===-1){this.raise(e.start,"Invalid regular expression flag")}if(r.indexOf(a,s+1)>-1){this.raise(e.start,"Duplicate regular expression flag")}}};ke.validateRegExpPattern=function(e){this.regexp_pattern(e);if(!e.switchN&&this.options.ecmaVersion>=9&&e.groupNames.length>0){e.switchN=true;this.regexp_pattern(e)}};ke.regexp_pattern=function(e){e.pos=0;e.lastIntValue=0;e.lastStringValue="";e.lastAssertionIsQuantifiable=false;e.numCapturingParens=0;e.maxBackReference=0;e.groupNames.length=0;e.backReferenceNames.length=0;this.regexp_disjunction(e);if(e.pos!==e.source.length){if(e.eat(41)){e.raise("Unmatched ')'")}if(e.eat(93)||e.eat(125)){e.raise("Lone quantifier brackets")}}if(e.maxBackReference>e.numCapturingParens){e.raise("Invalid escape")}for(var t=0,r=e.backReferenceNames;t<r.length;t+=1){var s=r[t];if(e.groupNames.indexOf(s)===-1){e.raise("Invalid named capture referenced")}}};ke.regexp_disjunction=function(e){this.regexp_alternative(e);while(e.eat(124)){this.regexp_alternative(e)}if(this.regexp_eatQuantifier(e,true)){e.raise("Nothing to repeat")}if(e.eat(123)){e.raise("Lone quantifier brackets")}};ke.regexp_alternative=function(e){while(e.pos<e.source.length&&this.regexp_eatTerm(e)){}};ke.regexp_eatTerm=function(e){if(this.regexp_eatAssertion(e)){if(e.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(e)){if(e.switchU){e.raise("Invalid quantifier")}}return true}if(e.switchU?this.regexp_eatAtom(e):this.regexp_eatExtendedAtom(e)){this.regexp_eatQuantifier(e);return true}return false};ke.regexp_eatAssertion=function(e){var t=e.pos;e.lastAssertionIsQuantifiable=false;if(e.eat(94)||e.eat(36)){return true}if(e.eat(92)){if(e.eat(66)||e.eat(98)){return true}e.pos=t}if(e.eat(40)&&e.eat(63)){var r=false;if(this.options.ecmaVersion>=9){r=e.eat(60)}if(e.eat(61)||e.eat(33)){this.regexp_disjunction(e);if(!e.eat(41)){e.raise("Unterminated group")}e.lastAssertionIsQuantifiable=!r;return true}}e.pos=t;return false};ke.regexp_eatQuantifier=function(e,t){if(t===void 0)t=false;if(this.regexp_eatQuantifierPrefix(e,t)){e.eat(63);return true}return false};ke.regexp_eatQuantifierPrefix=function(e,t){return e.eat(42)||e.eat(43)||e.eat(63)||this.regexp_eatBracedQuantifier(e,t)};ke.regexp_eatBracedQuantifier=function(e,t){var r=e.pos;if(e.eat(123)){var s=0,a=-1;if(this.regexp_eatDecimalDigits(e)){s=e.lastIntValue;if(e.eat(44)&&this.regexp_eatDecimalDigits(e)){a=e.lastIntValue}if(e.eat(125)){if(a!==-1&&a<s&&!t){e.raise("numbers out of order in {} quantifier")}return true}}if(e.switchU&&!t){e.raise("Incomplete quantifier")}e.pos=r}return false};ke.regexp_eatAtom=function(e){return this.regexp_eatPatternCharacters(e)||e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)};ke.regexp_eatReverseSolidusAtomEscape=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatAtomEscape(e)){return true}e.pos=t}return false};ke.regexp_eatUncapturingGroup=function(e){var t=e.pos;if(e.eat(40)){if(e.eat(63)&&e.eat(58)){this.regexp_disjunction(e);if(e.eat(41)){return true}e.raise("Unterminated group")}e.pos=t}return false};ke.regexp_eatCapturingGroup=function(e){if(e.eat(40)){if(this.options.ecmaVersion>=9){this.regexp_groupSpecifier(e)}else if(e.current()===63){e.raise("Invalid group")}this.regexp_disjunction(e);if(e.eat(41)){e.numCapturingParens+=1;return true}e.raise("Unterminated group")}return false};ke.regexp_eatExtendedAtom=function(e){return e.eat(46)||this.regexp_eatReverseSolidusAtomEscape(e)||this.regexp_eatCharacterClass(e)||this.regexp_eatUncapturingGroup(e)||this.regexp_eatCapturingGroup(e)||this.regexp_eatInvalidBracedQuantifier(e)||this.regexp_eatExtendedPatternCharacter(e)};ke.regexp_eatInvalidBracedQuantifier=function(e){if(this.regexp_eatBracedQuantifier(e,true)){e.raise("Nothing to repeat")}return false};ke.regexp_eatSyntaxCharacter=function(e){var t=e.current();if(isSyntaxCharacter(t)){e.lastIntValue=t;e.advance();return true}return false};function isSyntaxCharacter(e){return e===36||e>=40&&e<=43||e===46||e===63||e>=91&&e<=94||e>=123&&e<=125}ke.regexp_eatPatternCharacters=function(e){var t=e.pos;var r=0;while((r=e.current())!==-1&&!isSyntaxCharacter(r)){e.advance()}return e.pos!==t};ke.regexp_eatExtendedPatternCharacter=function(e){var t=e.current();if(t!==-1&&t!==36&&!(t>=40&&t<=43)&&t!==46&&t!==63&&t!==91&&t!==94&&t!==124){e.advance();return true}return false};ke.regexp_groupSpecifier=function(e){if(e.eat(63)){if(this.regexp_eatGroupName(e)){if(e.groupNames.indexOf(e.lastStringValue)!==-1){e.raise("Duplicate capture group name")}e.groupNames.push(e.lastStringValue);return}e.raise("Invalid group")}};ke.regexp_eatGroupName=function(e){e.lastStringValue="";if(e.eat(60)){if(this.regexp_eatRegExpIdentifierName(e)&&e.eat(62)){return true}e.raise("Invalid capture group name")}return false};ke.regexp_eatRegExpIdentifierName=function(e){e.lastStringValue="";if(this.regexp_eatRegExpIdentifierStart(e)){e.lastStringValue+=codePointToString(e.lastIntValue);while(this.regexp_eatRegExpIdentifierPart(e)){e.lastStringValue+=codePointToString(e.lastIntValue)}return true}return false};ke.regexp_eatRegExpIdentifierStart=function(e){var t=e.pos;var r=this.options.ecmaVersion>=11;var s=e.current(r);e.advance(r);if(s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)){s=e.lastIntValue}if(isRegExpIdentifierStart(s)){e.lastIntValue=s;return true}e.pos=t;return false};function isRegExpIdentifierStart(e){return isIdentifierStart(e,true)||e===36||e===95}ke.regexp_eatRegExpIdentifierPart=function(e){var t=e.pos;var r=this.options.ecmaVersion>=11;var s=e.current(r);e.advance(r);if(s===92&&this.regexp_eatRegExpUnicodeEscapeSequence(e,r)){s=e.lastIntValue}if(isRegExpIdentifierPart(s)){e.lastIntValue=s;return true}e.pos=t;return false};function isRegExpIdentifierPart(e){return isIdentifierChar(e,true)||e===36||e===95||e===8204||e===8205}ke.regexp_eatAtomEscape=function(e){if(this.regexp_eatBackReference(e)||this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)||e.switchN&&this.regexp_eatKGroupName(e)){return true}if(e.switchU){if(e.current()===99){e.raise("Invalid unicode escape")}e.raise("Invalid escape")}return false};ke.regexp_eatBackReference=function(e){var t=e.pos;if(this.regexp_eatDecimalEscape(e)){var r=e.lastIntValue;if(e.switchU){if(r>e.maxBackReference){e.maxBackReference=r}return true}if(r<=e.numCapturingParens){return true}e.pos=t}return false};ke.regexp_eatKGroupName=function(e){if(e.eat(107)){if(this.regexp_eatGroupName(e)){e.backReferenceNames.push(e.lastStringValue);return true}e.raise("Invalid named reference")}return false};ke.regexp_eatCharacterEscape=function(e){return this.regexp_eatControlEscape(e)||this.regexp_eatCControlLetter(e)||this.regexp_eatZero(e)||this.regexp_eatHexEscapeSequence(e)||this.regexp_eatRegExpUnicodeEscapeSequence(e,false)||!e.switchU&&this.regexp_eatLegacyOctalEscapeSequence(e)||this.regexp_eatIdentityEscape(e)};ke.regexp_eatCControlLetter=function(e){var t=e.pos;if(e.eat(99)){if(this.regexp_eatControlLetter(e)){return true}e.pos=t}return false};ke.regexp_eatZero=function(e){if(e.current()===48&&!isDecimalDigit(e.lookahead())){e.lastIntValue=0;e.advance();return true}return false};ke.regexp_eatControlEscape=function(e){var t=e.current();if(t===116){e.lastIntValue=9;e.advance();return true}if(t===110){e.lastIntValue=10;e.advance();return true}if(t===118){e.lastIntValue=11;e.advance();return true}if(t===102){e.lastIntValue=12;e.advance();return true}if(t===114){e.lastIntValue=13;e.advance();return true}return false};ke.regexp_eatControlLetter=function(e){var t=e.current();if(isControlLetter(t)){e.lastIntValue=t%32;e.advance();return true}return false};function isControlLetter(e){return e>=65&&e<=90||e>=97&&e<=122}ke.regexp_eatRegExpUnicodeEscapeSequence=function(e,t){if(t===void 0)t=false;var r=e.pos;var s=t||e.switchU;if(e.eat(117)){if(this.regexp_eatFixedHexDigits(e,4)){var a=e.lastIntValue;if(s&&a>=55296&&a<=56319){var o=e.pos;if(e.eat(92)&&e.eat(117)&&this.regexp_eatFixedHexDigits(e,4)){var u=e.lastIntValue;if(u>=56320&&u<=57343){e.lastIntValue=(a-55296)*1024+(u-56320)+65536;return true}}e.pos=o;e.lastIntValue=a}return true}if(s&&e.eat(123)&&this.regexp_eatHexDigits(e)&&e.eat(125)&&isValidUnicode(e.lastIntValue)){return true}if(s){e.raise("Invalid unicode escape")}e.pos=r}return false};function isValidUnicode(e){return e>=0&&e<=1114111}ke.regexp_eatIdentityEscape=function(e){if(e.switchU){if(this.regexp_eatSyntaxCharacter(e)){return true}if(e.eat(47)){e.lastIntValue=47;return true}return false}var t=e.current();if(t!==99&&(!e.switchN||t!==107)){e.lastIntValue=t;e.advance();return true}return false};ke.regexp_eatDecimalEscape=function(e){e.lastIntValue=0;var t=e.current();if(t>=49&&t<=57){do{e.lastIntValue=10*e.lastIntValue+(t-48);e.advance()}while((t=e.current())>=48&&t<=57);return true}return false};ke.regexp_eatCharacterClassEscape=function(e){var t=e.current();if(isCharacterClassEscape(t)){e.lastIntValue=-1;e.advance();return true}if(e.switchU&&this.options.ecmaVersion>=9&&(t===80||t===112)){e.lastIntValue=-1;e.advance();if(e.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(e)&&e.eat(125)){return true}e.raise("Invalid property name")}return false};function isCharacterClassEscape(e){return e===100||e===68||e===115||e===83||e===119||e===87}ke.regexp_eatUnicodePropertyValueExpression=function(e){var t=e.pos;if(this.regexp_eatUnicodePropertyName(e)&&e.eat(61)){var r=e.lastStringValue;if(this.regexp_eatUnicodePropertyValue(e)){var s=e.lastStringValue;this.regexp_validateUnicodePropertyNameAndValue(e,r,s);return true}}e.pos=t;if(this.regexp_eatLoneUnicodePropertyNameOrValue(e)){var a=e.lastStringValue;this.regexp_validateUnicodePropertyNameOrValue(e,a);return true}return false};ke.regexp_validateUnicodePropertyNameAndValue=function(e,t,r){if(!has(e.unicodeProperties.nonBinary,t)){e.raise("Invalid property name")}if(!e.unicodeProperties.nonBinary[t].test(r)){e.raise("Invalid property value")}};ke.regexp_validateUnicodePropertyNameOrValue=function(e,t){if(!e.unicodeProperties.binary.test(t)){e.raise("Invalid property name")}};ke.regexp_eatUnicodePropertyName=function(e){var t=0;e.lastStringValue="";while(isUnicodePropertyNameCharacter(t=e.current())){e.lastStringValue+=codePointToString(t);e.advance()}return e.lastStringValue!==""};function isUnicodePropertyNameCharacter(e){return isControlLetter(e)||e===95}ke.regexp_eatUnicodePropertyValue=function(e){var t=0;e.lastStringValue="";while(isUnicodePropertyValueCharacter(t=e.current())){e.lastStringValue+=codePointToString(t);e.advance()}return e.lastStringValue!==""};function isUnicodePropertyValueCharacter(e){return isUnicodePropertyNameCharacter(e)||isDecimalDigit(e)}ke.regexp_eatLoneUnicodePropertyNameOrValue=function(e){return this.regexp_eatUnicodePropertyValue(e)};ke.regexp_eatCharacterClass=function(e){if(e.eat(91)){e.eat(94);this.regexp_classRanges(e);if(e.eat(93)){return true}e.raise("Unterminated character class")}return false};ke.regexp_classRanges=function(e){while(this.regexp_eatClassAtom(e)){var t=e.lastIntValue;if(e.eat(45)&&this.regexp_eatClassAtom(e)){var r=e.lastIntValue;if(e.switchU&&(t===-1||r===-1)){e.raise("Invalid character class")}if(t!==-1&&r!==-1&&t>r){e.raise("Range out of order in character class")}}}};ke.regexp_eatClassAtom=function(e){var t=e.pos;if(e.eat(92)){if(this.regexp_eatClassEscape(e)){return true}if(e.switchU){var r=e.current();if(r===99||isOctalDigit(r)){e.raise("Invalid class escape")}e.raise("Invalid escape")}e.pos=t}var s=e.current();if(s!==93){e.lastIntValue=s;e.advance();return true}return false};ke.regexp_eatClassEscape=function(e){var t=e.pos;if(e.eat(98)){e.lastIntValue=8;return true}if(e.switchU&&e.eat(45)){e.lastIntValue=45;return true}if(!e.switchU&&e.eat(99)){if(this.regexp_eatClassControlLetter(e)){return true}e.pos=t}return this.regexp_eatCharacterClassEscape(e)||this.regexp_eatCharacterEscape(e)};ke.regexp_eatClassControlLetter=function(e){var t=e.current();if(isDecimalDigit(t)||t===95){e.lastIntValue=t%32;e.advance();return true}return false};ke.regexp_eatHexEscapeSequence=function(e){var t=e.pos;if(e.eat(120)){if(this.regexp_eatFixedHexDigits(e,2)){return true}if(e.switchU){e.raise("Invalid escape")}e.pos=t}return false};ke.regexp_eatDecimalDigits=function(e){var t=e.pos;var r=0;e.lastIntValue=0;while(isDecimalDigit(r=e.current())){e.lastIntValue=10*e.lastIntValue+(r-48);e.advance()}return e.pos!==t};function isDecimalDigit(e){return e>=48&&e<=57}ke.regexp_eatHexDigits=function(e){var t=e.pos;var r=0;e.lastIntValue=0;while(isHexDigit(r=e.current())){e.lastIntValue=16*e.lastIntValue+hexToInt(r);e.advance()}return e.pos!==t};function isHexDigit(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102}function hexToInt(e){if(e>=65&&e<=70){return 10+(e-65)}if(e>=97&&e<=102){return 10+(e-97)}return e-48}ke.regexp_eatLegacyOctalEscapeSequence=function(e){if(this.regexp_eatOctalDigit(e)){var t=e.lastIntValue;if(this.regexp_eatOctalDigit(e)){var r=e.lastIntValue;if(t<=3&&this.regexp_eatOctalDigit(e)){e.lastIntValue=t*64+r*8+e.lastIntValue}else{e.lastIntValue=t*8+r}}else{e.lastIntValue=t}return true}return false};ke.regexp_eatOctalDigit=function(e){var t=e.current();if(isOctalDigit(t)){e.lastIntValue=t-48;e.advance();return true}e.lastIntValue=0;return false};function isOctalDigit(e){return e>=48&&e<=55}ke.regexp_eatFixedHexDigits=function(e,t){var r=e.pos;e.lastIntValue=0;for(var s=0;s<t;++s){var a=e.current();if(!isHexDigit(a)){e.pos=r;return false}e.lastIntValue=16*e.lastIntValue+hexToInt(a);e.advance()}return true};var Re=function Token(e){this.type=e.type;this.value=e.value;this.start=e.start;this.end=e.end;if(e.options.locations){this.loc=new R(e,e.startLoc,e.endLoc)}if(e.options.ranges){this.range=[e.start,e.end]}};var Te=K.prototype;Te.next=function(e){if(!e&&this.type.keyword&&this.containsEsc){this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword)}if(this.options.onToken){this.options.onToken(new Re(this))}this.lastTokEnd=this.end;this.lastTokStart=this.start;this.lastTokEndLoc=this.endLoc;this.lastTokStartLoc=this.startLoc;this.nextToken()};Te.getToken=function(){this.next();return new Re(this)};if(typeof Symbol!=="undefined"){Te[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===_.eof,value:t}}}}}Te.curContext=function(){return this.context[this.context.length-1]};Te.nextToken=function(){var e=this.curContext();if(!e||!e.preserveSpace){this.skipSpace()}this.start=this.pos;if(this.options.locations){this.startLoc=this.curPosition()}if(this.pos>=this.input.length){return this.finishToken(_.eof)}if(e.override){return e.override(this)}else{this.readToken(this.fullCharCodeAtPos())}};Te.readToken=function(e){if(isIdentifierStart(e,this.options.ecmaVersion>=6)||e===92){return this.readWord()}return this.getTokenFromCode(e)};Te.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=56320){return e}var t=this.input.charCodeAt(this.pos+1);return t<=56319||t>=57344?e:(e<<10)+t-56613888};Te.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition();var t=this.pos,r=this.input.indexOf("*/",this.pos+=2);if(r===-1){this.raise(this.pos-2,"Unterminated comment")}this.pos=r+2;if(this.options.locations){x.lastIndex=t;var s;while((s=x.exec(this.input))&&s.index<this.pos){++this.curLine;this.lineStart=s.index+s[0].length}}if(this.options.onComment){this.options.onComment(true,this.input.slice(t+2,r),t,this.pos,e,this.curPosition())}};Te.skipLineComment=function(e){var t=this.pos;var r=this.options.onComment&&this.curPosition();var s=this.input.charCodeAt(this.pos+=e);while(this.pos<this.input.length&&!isNewLine(s)){s=this.input.charCodeAt(++this.pos)}if(this.options.onComment){this.options.onComment(false,this.input.slice(t+e,this.pos),t,this.pos,r,this.curPosition())}};Te.skipSpace=function(){e:while(this.pos<this.input.length){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:if(this.input.charCodeAt(this.pos+1)===10){++this.pos}case 10:case 8232:case 8233:++this.pos;if(this.options.locations){++this.curLine;this.lineStart=this.pos}break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(e>8&&e<14||e>=5760&&w.test(String.fromCharCode(e))){++this.pos}else{break e}}}};Te.finishToken=function(e,t){this.end=this.pos;if(this.options.locations){this.endLoc=this.curPosition()}var r=this.type;this.type=e;this.value=t;this.updateContext(r)};Te.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57){return this.readNumber(true)}var t=this.input.charCodeAt(this.pos+2);if(this.options.ecmaVersion>=6&&e===46&&t===46){this.pos+=3;return this.finishToken(_.ellipsis)}else{++this.pos;return this.finishToken(_.dot)}};Te.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);if(this.exprAllowed){++this.pos;return this.readRegexp()}if(e===61){return this.finishOp(_.assign,2)}return this.finishOp(_.slash,1)};Te.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1);var r=1;var s=e===42?_.star:_.modulo;if(this.options.ecmaVersion>=7&&e===42&&t===42){++r;s=_.starstar;t=this.input.charCodeAt(this.pos+2)}if(t===61){return this.finishOp(_.assign,r+1)}return this.finishOp(s,r)};Te.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(this.options.ecmaVersion>=12){var r=this.input.charCodeAt(this.pos+2);if(r===61){return this.finishOp(_.assign,3)}}return this.finishOp(e===124?_.logicalOR:_.logicalAND,2)}if(t===61){return this.finishOp(_.assign,2)}return this.finishOp(e===124?_.bitwiseOR:_.bitwiseAND,1)};Te.readToken_caret=function(){var e=this.input.charCodeAt(this.pos+1);if(e===61){return this.finishOp(_.assign,2)}return this.finishOp(_.bitwiseXOR,1)};Te.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===e){if(t===45&&!this.inModule&&this.input.charCodeAt(this.pos+2)===62&&(this.lastTokEnd===0||E.test(this.input.slice(this.lastTokEnd,this.pos)))){this.skipLineComment(3);this.skipSpace();return this.nextToken()}return this.finishOp(_.incDec,2)}if(t===61){return this.finishOp(_.assign,2)}return this.finishOp(_.plusMin,1)};Te.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1);var r=1;if(t===e){r=e===62&&this.input.charCodeAt(this.pos+2)===62?3:2;if(this.input.charCodeAt(this.pos+r)===61){return this.finishOp(_.assign,r+1)}return this.finishOp(_.bitShift,r)}if(t===33&&e===60&&!this.inModule&&this.input.charCodeAt(this.pos+2)===45&&this.input.charCodeAt(this.pos+3)===45){this.skipLineComment(4);this.skipSpace();return this.nextToken()}if(t===61){r=2}return this.finishOp(_.relational,r)};Te.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);if(t===61){return this.finishOp(_.equality,this.input.charCodeAt(this.pos+2)===61?3:2)}if(e===61&&t===62&&this.options.ecmaVersion>=6){this.pos+=2;return this.finishToken(_.arrow)}return this.finishOp(e===61?_.eq:_.prefix,1)};Te.readToken_question=function(){var e=this.options.ecmaVersion;if(e>=11){var t=this.input.charCodeAt(this.pos+1);if(t===46){var r=this.input.charCodeAt(this.pos+2);if(r<48||r>57){return this.finishOp(_.questionDot,2)}}if(t===63){if(e>=12){var s=this.input.charCodeAt(this.pos+2);if(s===61){return this.finishOp(_.assign,3)}}return this.finishOp(_.coalesce,2)}}return this.finishOp(_.question,1)};Te.readToken_numberSign=function(){var e=this.options.ecmaVersion;var t=35;if(e>=13){++this.pos;t=this.fullCharCodeAtPos();if(isIdentifierStart(t,true)||t===92){return this.finishToken(_.privateId,this.readWord1())}}this.raise(this.pos,"Unexpected character '"+codePointToString$1(t)+"'")};Te.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:++this.pos;return this.finishToken(_.parenL);case 41:++this.pos;return this.finishToken(_.parenR);case 59:++this.pos;return this.finishToken(_.semi);case 44:++this.pos;return this.finishToken(_.comma);case 91:++this.pos;return this.finishToken(_.bracketL);case 93:++this.pos;return this.finishToken(_.bracketR);case 123:++this.pos;return this.finishToken(_.braceL);case 125:++this.pos;return this.finishToken(_.braceR);case 58:++this.pos;return this.finishToken(_.colon);case 96:if(this.options.ecmaVersion<6){break}++this.pos;return this.finishToken(_.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(t===120||t===88){return this.readRadixNumber(16)}if(this.options.ecmaVersion>=6){if(t===111||t===79){return this.readRadixNumber(8)}if(t===98||t===66){return this.readRadixNumber(2)}}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(false);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 63:return this.readToken_question();case 126:return this.finishOp(_.prefix,1);case 35:return this.readToken_numberSign()}this.raise(this.pos,"Unexpected character '"+codePointToString$1(e)+"'")};Te.finishOp=function(e,t){var r=this.input.slice(this.pos,this.pos+t);this.pos+=t;return this.finishToken(e,r)};Te.readRegexp=function(){var e,t,r=this.pos;for(;;){if(this.pos>=this.input.length){this.raise(r,"Unterminated regular expression")}var s=this.input.charAt(this.pos);if(E.test(s)){this.raise(r,"Unterminated regular expression")}if(!e){if(s==="["){t=true}else if(s==="]"&&t){t=false}else if(s==="/"&&!t){break}e=s==="\\"}else{e=false}++this.pos}var a=this.input.slice(r,this.pos);++this.pos;var o=this.pos;var u=this.readWord1();if(this.containsEsc){this.unexpected(o)}var c=this.regexpState||(this.regexpState=new Fe(this));c.reset(r,a,u);this.validateRegExpFlags(c);this.validateRegExpPattern(c);var h=null;try{h=new RegExp(a,u)}catch(e){}return this.finishToken(_.regexp,{pattern:a,flags:u,value:h})};Te.readInt=function(e,t,r){var s=this.options.ecmaVersion>=12&&t===undefined;var a=r&&this.input.charCodeAt(this.pos)===48;var o=this.pos,u=0,c=0;for(var h=0,p=t==null?Infinity:t;h<p;++h,++this.pos){var d=this.input.charCodeAt(this.pos),v=void 0;if(s&&d===95){if(a){this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals")}if(c===95){this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore")}if(h===0){this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits")}c=d;continue}if(d>=97){v=d-97+10}else if(d>=65){v=d-65+10}else if(d>=48&&d<=57){v=d-48}else{v=Infinity}if(v>=e){break}c=d;u=u*e+v}if(s&&c===95){this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits")}if(this.pos===o||t!=null&&this.pos-o!==t){return null}return u};function stringToNumber(e,t){if(t){return parseInt(e,8)}return parseFloat(e.replace(/_/g,""))}function stringToBigInt(e){if(typeof BigInt!=="function"){return null}return BigInt(e.replace(/_/g,""))}Te.readRadixNumber=function(e){var t=this.pos;this.pos+=2;var r=this.readInt(e);if(r==null){this.raise(this.start+2,"Expected number in radix "+e)}if(this.options.ecmaVersion>=11&&this.input.charCodeAt(this.pos)===110){r=stringToBigInt(this.input.slice(t,this.pos));++this.pos}else if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}return this.finishToken(_.num,r)};Te.readNumber=function(e){var t=this.pos;if(!e&&this.readInt(10,undefined,true)===null){this.raise(t,"Invalid number")}var r=this.pos-t>=2&&this.input.charCodeAt(t)===48;if(r&&this.strict){this.raise(t,"Invalid number")}var s=this.input.charCodeAt(this.pos);if(!r&&!e&&this.options.ecmaVersion>=11&&s===110){var a=stringToBigInt(this.input.slice(t,this.pos));++this.pos;if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}return this.finishToken(_.num,a)}if(r&&/[89]/.test(this.input.slice(t,this.pos))){r=false}if(s===46&&!r){++this.pos;this.readInt(10);s=this.input.charCodeAt(this.pos)}if((s===69||s===101)&&!r){s=this.input.charCodeAt(++this.pos);if(s===43||s===45){++this.pos}if(this.readInt(10)===null){this.raise(t,"Invalid number")}}if(isIdentifierStart(this.fullCharCodeAtPos())){this.raise(this.pos,"Identifier directly after number")}var o=stringToNumber(this.input.slice(t,this.pos),r);return this.finishToken(_.num,o)};Te.readCodePoint=function(){var e=this.input.charCodeAt(this.pos),t;if(e===123){if(this.options.ecmaVersion<6){this.unexpected()}var r=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos);++this.pos;if(t>1114111){this.invalidStringToken(r,"Code point out of bounds")}}else{t=this.readHexChar(4)}return t};function codePointToString$1(e){if(e<=65535){return String.fromCharCode(e)}e-=65536;return String.fromCharCode((e>>10)+55296,(e&1023)+56320)}Te.readString=function(e){var t="",r=++this.pos;for(;;){if(this.pos>=this.input.length){this.raise(this.start,"Unterminated string constant")}var s=this.input.charCodeAt(this.pos);if(s===e){break}if(s===92){t+=this.input.slice(r,this.pos);t+=this.readEscapedChar(false);r=this.pos}else{if(isNewLine(s,this.options.ecmaVersion>=10)){this.raise(this.start,"Unterminated string constant")}++this.pos}}t+=this.input.slice(r,this.pos++);return this.finishToken(_.string,t)};var Ie={};Te.tryReadTemplateToken=function(){this.inTemplateElement=true;try{this.readTmplToken()}catch(e){if(e===Ie){this.readInvalidTemplateToken()}else{throw e}}this.inTemplateElement=false};Te.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9){throw Ie}else{this.raise(e,t)}};Te.readTmplToken=function(){var e="",t=this.pos;for(;;){if(this.pos>=this.input.length){this.raise(this.start,"Unterminated template")}var r=this.input.charCodeAt(this.pos);if(r===96||r===36&&this.input.charCodeAt(this.pos+1)===123){if(this.pos===this.start&&(this.type===_.template||this.type===_.invalidTemplate)){if(r===36){this.pos+=2;return this.finishToken(_.dollarBraceL)}else{++this.pos;return this.finishToken(_.backQuote)}}e+=this.input.slice(t,this.pos);return this.finishToken(_.template,e)}if(r===92){e+=this.input.slice(t,this.pos);e+=this.readEscapedChar(true);t=this.pos}else if(isNewLine(r)){e+=this.input.slice(t,this.pos);++this.pos;switch(r){case 13:if(this.input.charCodeAt(this.pos)===10){++this.pos}case 10:e+="\n";break;default:e+=String.fromCharCode(r);break}if(this.options.locations){++this.curLine;this.lineStart=this.pos}t=this.pos}else{++this.pos}}};Te.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++){switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if(this.input[this.pos+1]!=="{"){break}case"`":return this.finishToken(_.invalidTemplate,this.input.slice(this.start,this.pos))}}this.raise(this.start,"Unterminated template")};Te.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);++this.pos;switch(t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return codePointToString$1(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:if(this.input.charCodeAt(this.pos)===10){++this.pos}case 10:if(this.options.locations){this.lineStart=this.pos;++this.curLine}return"";case 56:case 57:if(this.strict){this.invalidStringToken(this.pos-1,"Invalid escape sequence")}if(e){var r=this.pos-1;this.invalidStringToken(r,"Invalid escape sequence in template string");return null}default:if(t>=48&&t<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0];var a=parseInt(s,8);if(a>255){s=s.slice(0,-1);a=parseInt(s,8)}this.pos+=s.length-1;t=this.input.charCodeAt(this.pos);if((s!=="0"||t===56||t===57)&&(this.strict||e)){this.invalidStringToken(this.pos-1-s.length,e?"Octal literal in template string":"Octal literal in strict mode")}return String.fromCharCode(a)}if(isNewLine(t)){return""}return String.fromCharCode(t)}};Te.readHexChar=function(e){var t=this.pos;var r=this.readInt(16,e);if(r===null){this.invalidStringToken(t,"Bad character escape sequence")}return r};Te.readWord1=function(){this.containsEsc=false;var e="",t=true,r=this.pos;var s=this.options.ecmaVersion>=6;while(this.pos<this.input.length){var a=this.fullCharCodeAtPos();if(isIdentifierChar(a,s)){this.pos+=a<=65535?1:2}else if(a===92){this.containsEsc=true;e+=this.input.slice(r,this.pos);var o=this.pos;if(this.input.charCodeAt(++this.pos)!==117){this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX")}++this.pos;var u=this.readCodePoint();if(!(t?isIdentifierStart:isIdentifierChar)(u,s)){this.invalidStringToken(o,"Invalid Unicode escape")}e+=codePointToString$1(u);r=this.pos}else{break}t=false}return e+this.input.slice(r,this.pos)};Te.readWord=function(){var e=this.readWord1();var t=_.name;if(this.keywords.test(e)){t=y[e]}return this.finishToken(t,e)};var Be="8.3.0";K.acorn={Parser:K,version:Be,defaultOptions:T,Position:F,SourceLocation:R,getLineInfo:getLineInfo,Node:fe,TokenType:v,tokTypes:_,keywordTypes:y,TokContext:pe,tokContexts:de,isIdentifierChar:isIdentifierChar,isIdentifierStart:isIdentifierStart,Token:Re,isNewLine:isNewLine,lineBreak:E,lineBreakG:x,nonASCIIwhitespace:w};function parse(e,t){return K.parse(e,t)}function parseExpressionAt(e,t,r){return K.parseExpressionAt(e,t,r)}function tokenizer(e,t){return K.tokenizer(e,t)}e.Node=fe;e.Parser=K;e.Position=F;e.SourceLocation=R;e.TokContext=pe;e.Token=Re;e.TokenType=v;e.defaultOptions=T;e.getLineInfo=getLineInfo;e.isIdentifierChar=isIdentifierChar;e.isIdentifierStart=isIdentifierStart;e.isNewLine=isNewLine;e.keywordTypes=y;e.lineBreak=E;e.lineBreakG=x;e.nonASCIIwhitespace=w;e.parse=parse;e.parseExpressionAt=parseExpressionAt;e.tokContexts=de;e.tokTypes=_;e.tokenizer=tokenizer;e.version=Be;Object.defineProperty(e,"__esModule",{value:true})}))},1083:(e,t,r)=>{"use strict";t.TrackerGroup=r(660);t.Tracker=r(8074);t.TrackerStream=r(1375)},165:(e,t,r)=>{"use strict";var s=r(8614).EventEmitter;var a=r(1669);var o=0;var u=e.exports=function(e){s.call(this);this.id=++o;this.name=e};a.inherits(u,s)},660:(e,t,r)=>{"use strict";var s=r(1669);var a=r(165);var o=r(8074);var u=r(1375);var c=e.exports=function(e){a.call(this,e);this.parentGroup=null;this.trackers=[];this.completion={};this.weight={};this.totalWeight=0;this.finished=false;this.bubbleChange=bubbleChange(this)};s.inherits(c,a);function bubbleChange(e){return function(t,r,s){e.completion[s.id]=r;if(e.finished)return;e.emit("change",t||e.name,e.completed(),e)}}c.prototype.nameInTree=function(){var e=[];var t=this;while(t){e.unshift(t.name);t=t.parentGroup}return e.join("/")};c.prototype.addUnit=function(e,t){if(e.addUnit){var r=this;while(r){if(e===r){throw new Error("Attempted to add tracker group "+e.name+" to tree that already includes it "+this.nameInTree(this))}r=r.parentGroup}e.parentGroup=this}this.weight[e.id]=t||1;this.totalWeight+=this.weight[e.id];this.trackers.push(e);this.completion[e.id]=e.completed();e.on("change",this.bubbleChange);if(!this.finished)this.emit("change",e.name,this.completion[e.id],e);return e};c.prototype.completed=function(){if(this.trackers.length===0)return 0;var e=1/this.totalWeight;var t=0;for(var r=0;r<this.trackers.length;r++){var s=this.trackers[r].id;t+=e*this.weight[s]*this.completion[s]}return t};c.prototype.newGroup=function(e,t){return this.addUnit(new c(e),t)};c.prototype.newItem=function(e,t,r){return this.addUnit(new o(e,t),r)};c.prototype.newStream=function(e,t,r){return this.addUnit(new u(e,t),r)};c.prototype.finish=function(){this.finished=true;if(!this.trackers.length)this.addUnit(new o,1,true);for(var e=0;e<this.trackers.length;e++){var t=this.trackers[e];t.finish();t.removeListener("change",this.bubbleChange)}this.emit("change",this.name,1,this)};var h="                                  ";c.prototype.debug=function(e){e=e||0;var t=e?h.substr(0,e):"";var r=t+(this.name||"top")+": "+this.completed()+"\n";this.trackers.forEach((function(s){if(s instanceof c){r+=s.debug(e+1)}else{r+=t+" "+s.name+": "+s.completed()+"\n"}}));return r}},1375:(e,t,r)=>{"use strict";var s=r(1669);var a=r(1642);var o=r(1318);var u=r(8074);var c=e.exports=function(e,t,r){a.Transform.call(this,r);this.tracker=new u(e,t);this.name=e;this.id=this.tracker.id;this.tracker.on("change",delegateChange(this))};s.inherits(c,a.Transform);function delegateChange(e){return function(t,r,s){e.emit("change",t,r,e)}}c.prototype._transform=function(e,t,r){this.tracker.completeWork(e.length?e.length:1);this.push(e);r()};c.prototype._flush=function(e){this.tracker.finish();e()};o(c.prototype,"tracker").method("completed").method("addWork").method("finish")},8074:(e,t,r)=>{"use strict";var s=r(1669);var a=r(165);var o=e.exports=function(e,t){a.call(this,e);this.workDone=0;this.workTodo=t||0};s.inherits(o,a);o.prototype.completed=function(){return this.workTodo===0?0:this.workDone/this.workTodo};o.prototype.addWork=function(e){this.workTodo+=e;this.emit("change",this.name,this.completed(),this)};o.prototype.completeWork=function(e){this.workDone+=e;if(this.workDone>this.workTodo)this.workDone=this.workTodo;this.emit("change",this.name,this.completed(),this)};o.prototype.finish=function(){this.workTodo=this.workDone=1;this.emit("change",this.name,1,this)}},9417:e=>{"use strict";e.exports=balanced;function balanced(e,t,r){if(e instanceof RegExp)e=maybeMatch(e,r);if(t instanceof RegExp)t=maybeMatch(t,r);var s=range(e,t,r);return s&&{start:s[0],end:s[1],pre:r.slice(0,s[0]),body:r.slice(s[0]+e.length,s[1]),post:r.slice(s[1]+t.length)}}function maybeMatch(e,t){var r=t.match(e);return r?r[0]:null}balanced.range=range;function range(e,t,r){var s,a,o,u,c;var h=r.indexOf(e);var p=r.indexOf(t,h+1);var d=h;if(h>=0&&p>0){s=[];o=r.length;while(d>=0&&!c){if(d==h){s.push(d);h=r.indexOf(e,d+1)}else if(s.length==1){c=[s.pop(),p]}else{a=s.pop();if(a<o){o=a;u=p}p=r.indexOf(t,d+1)}d=h<p&&h>=0?h:p}if(s.length){c=[o,u]}}return c}},8738:function(e){(function(t){"use strict";var r,s=20,a=1,o=1e6,u=1e6,c=-7,h=21,p="[big.js] ",d=p+"Invalid ",v=d+"decimal places",m=d+"rounding mode",g=p+"Division by zero",y={},_=void 0,E=/^-?(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i;function _Big_(){function Big(e){var t=this;if(!(t instanceof Big))return e===_?_Big_():new Big(e);if(e instanceof Big){t.s=e.s;t.e=e.e;t.c=e.c.slice()}else{parse(t,e)}t.constructor=Big}Big.prototype=y;Big.DP=s;Big.RM=a;Big.NE=c;Big.PE=h;Big.version="5.2.2";return Big}function parse(e,t){var r,s,a;if(t===0&&1/t<0)t="-0";else if(!E.test(t+=""))throw Error(d+"number");e.s=t.charAt(0)=="-"?(t=t.slice(1),-1):1;if((r=t.indexOf("."))>-1)t=t.replace(".","");if((s=t.search(/e/i))>0){if(r<0)r=s;r+=+t.slice(s+1);t=t.substring(0,s)}else if(r<0){r=t.length}a=t.length;for(s=0;s<a&&t.charAt(s)=="0";)++s;if(s==a){e.c=[e.e=0]}else{for(;a>0&&t.charAt(--a)=="0";);e.e=r-s-1;e.c=[];for(r=0;s<=a;)e.c[r++]=+t.charAt(s++)}return e}function round(e,t,r,s){var a=e.c,o=e.e+t+1;if(o<a.length){if(r===1){s=a[o]>=5}else if(r===2){s=a[o]>5||a[o]==5&&(s||o<0||a[o+1]!==_||a[o-1]&1)}else if(r===3){s=s||!!a[0]}else{s=false;if(r!==0)throw Error(m)}if(o<1){a.length=1;if(s){e.e=-t;a[0]=1}else{a[0]=e.e=0}}else{a.length=o--;if(s){for(;++a[o]>9;){a[o]=0;if(!o--){++e.e;a.unshift(1)}}}for(o=a.length;!a[--o];)a.pop()}}else if(r<0||r>3||r!==~~r){throw Error(m)}return e}function stringify(e,t,r,s){var a,u,c=e.constructor,h=!e.c[0];if(r!==_){if(r!==~~r||r<(t==3)||r>o){throw Error(t==3?d+"precision":v)}e=new c(e);r=s-e.e;if(e.c.length>++s)round(e,r,c.RM);if(t==2)s=e.e+r+1;for(;e.c.length<s;)e.c.push(0)}a=e.e;u=e.c.join("");r=u.length;if(t!=2&&(t==1||t==3&&s<=a||a<=c.NE||a>=c.PE)){u=u.charAt(0)+(r>1?"."+u.slice(1):"")+(a<0?"e":"e+")+a}else if(a<0){for(;++a;)u="0"+u;u="0."+u}else if(a>0){if(++a>r)for(a-=r;a--;)u+="0";else if(a<r)u=u.slice(0,a)+"."+u.slice(a)}else if(r>1){u=u.charAt(0)+"."+u.slice(1)}return e.s<0&&(!h||t==4)?"-"+u:u}y.abs=function(){var e=new this.constructor(this);e.s=1;return e};y.cmp=function(e){var t,r=this,s=r.c,a=(e=new r.constructor(e)).c,o=r.s,u=e.s,c=r.e,h=e.e;if(!s[0]||!a[0])return!s[0]?!a[0]?0:-u:o;if(o!=u)return o;t=o<0;if(c!=h)return c>h^t?1:-1;u=(c=s.length)<(h=a.length)?c:h;for(o=-1;++o<u;){if(s[o]!=a[o])return s[o]>a[o]^t?1:-1}return c==h?0:c>h^t?1:-1};y.div=function(e){var t=this,r=t.constructor,s=t.c,a=(e=new r(e)).c,u=t.s==e.s?1:-1,c=r.DP;if(c!==~~c||c<0||c>o)throw Error(v);if(!a[0])throw Error(g);if(!s[0])return new r(u*0);var h,p,d,m,y,E=a.slice(),x=h=a.length,w=s.length,D=s.slice(0,h),C=D.length,A=e,S=A.c=[],k=0,F=c+(A.e=t.e-e.e)+1;A.s=u;u=F<0?0:F;E.unshift(0);for(;C++<h;)D.push(0);do{for(d=0;d<10;d++){if(h!=(C=D.length)){m=h>C?1:-1}else{for(y=-1,m=0;++y<h;){if(a[y]!=D[y]){m=a[y]>D[y]?1:-1;break}}}if(m<0){for(p=C==h?a:E;C;){if(D[--C]<p[C]){y=C;for(;y&&!D[--y];)D[y]=9;--D[y];D[C]+=10}D[C]-=p[C]}for(;!D[0];)D.shift()}else{break}}S[k++]=m?d:++d;if(D[0]&&m)D[C]=s[x]||0;else D=[s[x]]}while((x++<w||D[0]!==_)&&u--);if(!S[0]&&k!=1){S.shift();A.e--}if(k>F)round(A,c,r.RM,D[0]!==_);return A};y.eq=function(e){return!this.cmp(e)};y.gt=function(e){return this.cmp(e)>0};y.gte=function(e){return this.cmp(e)>-1};y.lt=function(e){return this.cmp(e)<0};y.lte=function(e){return this.cmp(e)<1};y.minus=y.sub=function(e){var t,r,s,a,o=this,u=o.constructor,c=o.s,h=(e=new u(e)).s;if(c!=h){e.s=-h;return o.plus(e)}var p=o.c.slice(),d=o.e,v=e.c,m=e.e;if(!p[0]||!v[0]){return v[0]?(e.s=-h,e):new u(p[0]?o:0)}if(c=d-m){if(a=c<0){c=-c;s=p}else{m=d;s=v}s.reverse();for(h=c;h--;)s.push(0);s.reverse()}else{r=((a=p.length<v.length)?p:v).length;for(c=h=0;h<r;h++){if(p[h]!=v[h]){a=p[h]<v[h];break}}}if(a){s=p;p=v;v=s;e.s=-e.s}if((h=(r=v.length)-(t=p.length))>0)for(;h--;)p[t++]=0;for(h=t;r>c;){if(p[--r]<v[r]){for(t=r;t&&!p[--t];)p[t]=9;--p[t];p[r]+=10}p[r]-=v[r]}for(;p[--h]===0;)p.pop();for(;p[0]===0;){p.shift();--m}if(!p[0]){e.s=1;p=[m=0]}e.c=p;e.e=m;return e};y.mod=function(e){var t,r=this,s=r.constructor,a=r.s,o=(e=new s(e)).s;if(!e.c[0])throw Error(g);r.s=e.s=1;t=e.cmp(r)==1;r.s=a;e.s=o;if(t)return new s(r);a=s.DP;o=s.RM;s.DP=s.RM=0;r=r.div(e);s.DP=a;s.RM=o;return this.minus(r.times(e))};y.plus=y.add=function(e){var t,r=this,s=r.constructor,a=r.s,o=(e=new s(e)).s;if(a!=o){e.s=-o;return r.minus(e)}var u=r.e,c=r.c,h=e.e,p=e.c;if(!c[0]||!p[0])return p[0]?e:new s(c[0]?r:a*0);c=c.slice();if(a=u-h){if(a>0){h=u;t=p}else{a=-a;t=c}t.reverse();for(;a--;)t.push(0);t.reverse()}if(c.length-p.length<0){t=p;p=c;c=t}a=p.length;for(o=0;a;c[a]%=10)o=(c[--a]=c[a]+p[a]+o)/10|0;if(o){c.unshift(o);++h}for(a=c.length;c[--a]===0;)c.pop();e.c=c;e.e=h;return e};y.pow=function(e){var t=this,r=new t.constructor(1),s=r,a=e<0;if(e!==~~e||e<-u||e>u)throw Error(d+"exponent");if(a)e=-e;for(;;){if(e&1)s=s.times(t);e>>=1;if(!e)break;t=t.times(t)}return a?r.div(s):s};y.round=function(e,t){var r=this.constructor;if(e===_)e=0;else if(e!==~~e||e<-o||e>o)throw Error(v);return round(new r(this),e,t===_?r.RM:t)};y.sqrt=function(){var e,t,r,s=this,a=s.constructor,o=s.s,u=s.e,c=new a(.5);if(!s.c[0])return new a(s);if(o<0)throw Error(p+"No square root");o=Math.sqrt(s+"");if(o===0||o===1/0){t=s.c.join("");if(!(t.length+u&1))t+="0";o=Math.sqrt(t);u=((u+1)/2|0)-(u<0||u&1);e=new a((o==1/0?"1e":(o=o.toExponential()).slice(0,o.indexOf("e")+1))+u)}else{e=new a(o)}u=e.e+(a.DP+=4);do{r=e;e=c.times(r.plus(s.div(r)))}while(r.c.slice(0,u).join("")!==e.c.slice(0,u).join(""));return round(e,a.DP-=4,a.RM)};y.times=y.mul=function(e){var t,r=this,s=r.constructor,a=r.c,o=(e=new s(e)).c,u=a.length,c=o.length,h=r.e,p=e.e;e.s=r.s==e.s?1:-1;if(!a[0]||!o[0])return new s(e.s*0);e.e=h+p;if(u<c){t=a;a=o;o=t;p=u;u=c;c=p}for(t=new Array(p=u+c);p--;)t[p]=0;for(h=c;h--;){c=0;for(p=u+h;p>h;){c=t[p]+o[h]*a[p-h-1]+c;t[p--]=c%10;c=c/10|0}t[p]=(t[p]+c)%10}if(c)++e.e;else t.shift();for(h=t.length;!t[--h];)t.pop();e.c=t;return e};y.toExponential=function(e){return stringify(this,1,e,e)};y.toFixed=function(e){return stringify(this,2,e,this.e+e)};y.toPrecision=function(e){return stringify(this,3,e,e-1)};y.toString=function(){return stringify(this)};y.valueOf=y.toJSON=function(){return stringify(this,4)};r=_Big_();r["default"]=r.Big=r;if(typeof define==="function"&&define.amd){define((function(){return r}))}else if(true&&e.exports){e.exports=r}else{t.Big=r}})(this)},8384:(module,exports,__nested_webpack_require_255586__)=>{var fs=__nested_webpack_require_255586__(5747),path=__nested_webpack_require_255586__(5622),fileURLToPath=__nested_webpack_require_255586__(912),join=path.join,dirname=path.dirname,exists=fs.accessSync&&function(e){try{fs.accessSync(e)}catch(e){return false}return true}||fs.existsSync||path.existsSync,defaults={arrow:process.env.NODE_BINDINGS_ARROW||" → ",compiled:process.env.NODE_BINDINGS_COMPILED_DIR||"compiled",platform:process.platform,arch:process.arch,nodePreGyp:"node-v"+process.versions.modules+"-"+process.platform+"-"+process.arch,version:process.versions.node,bindings:"bindings.node",try:[["module_root","build","bindings"],["module_root","build","Debug","bindings"],["module_root","build","Release","bindings"],["module_root","out","Debug","bindings"],["module_root","Debug","bindings"],["module_root","out","Release","bindings"],["module_root","Release","bindings"],["module_root","build","default","bindings"],["module_root","compiled","version","platform","arch","bindings"],["module_root","addon-build","release","install-root","bindings"],["module_root","addon-build","debug","install-root","bindings"],["module_root","addon-build","default","install-root","bindings"],["module_root","lib","binding","nodePreGyp","bindings"]]};function bindings(opts){if(typeof opts=="string"){opts={bindings:opts}}else if(!opts){opts={}}Object.keys(defaults).map((function(e){if(!(e in opts))opts[e]=defaults[e]}));if(!opts.module_root){opts.module_root=exports.getRoot(exports.getFileName())}if(path.extname(opts.bindings)!=".node"){opts.bindings+=".node"}var requireFunc=true?eval("require"):0;var tries=[],i=0,l=opts.try.length,n,b,err;for(;i<l;i++){n=join.apply(null,opts.try[i].map((function(e){return opts[e]||e})));tries.push(n);try{b=opts.path?requireFunc.resolve(n):requireFunc(n);if(!opts.path){b.path=n}return b}catch(e){if(e.code!=="MODULE_NOT_FOUND"&&e.code!=="QUALIFIED_PATH_RESOLUTION_FAILED"&&!/not find/i.test(e.message)){throw e}}}err=new Error("Could not locate the bindings file. Tried:\n"+tries.map((function(e){return opts.arrow+e})).join("\n"));err.tries=tries;throw err}module.exports=exports=bindings;exports.getFileName=function getFileName(e){var t=Error.prepareStackTrace,r=Error.stackTraceLimit,s={},a;Error.stackTraceLimit=10;Error.prepareStackTrace=function(t,r){for(var s=0,o=r.length;s<o;s++){a=r[s].getFileName();if(a!==__filename){if(e){if(a!==e){return}}else{return}}}};Error.captureStackTrace(s);s.stack;Error.prepareStackTrace=t;Error.stackTraceLimit=r;var o="file://";if(a.indexOf(o)===0){a=fileURLToPath(a)}return a};exports.getRoot=function getRoot(e){var t=dirname(e),r;while(true){if(t==="."){t=process.cwd()}if(exists(join(t,"package.json"))||exists(join(t,"node_modules"))){return t}if(r===t){throw new Error('Could not find module root given file: "'+e+'". Do you have a `package.json` file? ')}r=t;t=join(t,"..")}}},3717:(e,t,r)=>{var s=r(6891);var a=r(9417);e.exports=expandTop;var o="\0SLASH"+Math.random()+"\0";var u="\0OPEN"+Math.random()+"\0";var c="\0CLOSE"+Math.random()+"\0";var h="\0COMMA"+Math.random()+"\0";var p="\0PERIOD"+Math.random()+"\0";function numeric(e){return parseInt(e,10)==e?parseInt(e,10):e.charCodeAt(0)}function escapeBraces(e){return e.split("\\\\").join(o).split("\\{").join(u).split("\\}").join(c).split("\\,").join(h).split("\\.").join(p)}function unescapeBraces(e){return e.split(o).join("\\").split(u).join("{").split(c).join("}").split(h).join(",").split(p).join(".")}function parseCommaParts(e){if(!e)return[""];var t=[];var r=a("{","}",e);if(!r)return e.split(",");var s=r.pre;var o=r.body;var u=r.post;var c=s.split(",");c[c.length-1]+="{"+o+"}";var h=parseCommaParts(u);if(u.length){c[c.length-1]+=h.shift();c.push.apply(c,h)}t.push.apply(t,c);return t}function expandTop(e){if(!e)return[];if(e.substr(0,2)==="{}"){e="\\{\\}"+e.substr(2)}return expand(escapeBraces(e),true).map(unescapeBraces)}function identity(e){return e}function embrace(e){return"{"+e+"}"}function isPadded(e){return/^-?0\d/.test(e)}function lte(e,t){return e<=t}function gte(e,t){return e>=t}function expand(e,t){var r=[];var o=a("{","}",e);if(!o||/\$$/.test(o.pre))return[e];var u=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(o.body);var h=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(o.body);var p=u||h;var d=o.body.indexOf(",")>=0;if(!p&&!d){if(o.post.match(/,.*\}/)){e=o.pre+"{"+o.body+c+o.post;return expand(e)}return[e]}var v;if(p){v=o.body.split(/\.\./)}else{v=parseCommaParts(o.body);if(v.length===1){v=expand(v[0],false).map(embrace);if(v.length===1){var m=o.post.length?expand(o.post,false):[""];return m.map((function(e){return o.pre+v[0]+e}))}}}var g=o.pre;var m=o.post.length?expand(o.post,false):[""];var y;if(p){var _=numeric(v[0]);var E=numeric(v[1]);var x=Math.max(v[0].length,v[1].length);var w=v.length==3?Math.abs(numeric(v[2])):1;var D=lte;var C=E<_;if(C){w*=-1;D=gte}var A=v.some(isPadded);y=[];for(var S=_;D(S,E);S+=w){var k;if(h){k=String.fromCharCode(S);if(k==="\\")k=""}else{k=String(S);if(A){var F=x-k.length;if(F>0){var R=new Array(F+1).join("0");if(S<0)k="-"+R+k.slice(1);else k=R+k}}}y.push(k)}}else{y=s(v,(function(e){return expand(e,false)}))}for(var T=0;T<y.length;T++){for(var I=0;I<m.length;I++){var B=g+y[T]+m[I];if(!t||p||B)r.push(B)}}return r}},8929:e=>{"use strict";e.exports=function(e,t){if(e===null||e===undefined){throw TypeError()}e=String(e);var r=e.length;var s=t?Number(t):0;if(Number.isNaN(s)){s=0}if(s<0||s>=r){return undefined}var a=e.charCodeAt(s);if(a>=55296&&a<=56319&&r>s+1){var o=e.charCodeAt(s+1);if(o>=56320&&o<=57343){return(a-55296)*1024+o-56320+65536}}return a}},6891:e=>{e.exports=function(e,r){var s=[];for(var a=0;a<e.length;a++){var o=r(e[a],a);if(t(o))s.push.apply(s,o);else s.push(o)}return s};var t=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},3645:(e,t)=>{"use strict";var r="[";t.up=function up(e){return r+(e||"")+"A"};t.down=function down(e){return r+(e||"")+"B"};t.forward=function forward(e){return r+(e||"")+"C"};t.back=function back(e){return r+(e||"")+"D"};t.nextLine=function nextLine(e){return r+(e||"")+"E"};t.previousLine=function previousLine(e){return r+(e||"")+"F"};t.horizontalAbsolute=function horizontalAbsolute(e){if(e==null)throw new Error("horizontalAboslute requires a column to position to");return r+e+"G"};t.eraseData=function eraseData(){return r+"J"};t.eraseLine=function eraseLine(){return r+"K"};t.goto=function(e,t){return r+t+";"+e+"H"};t.gotoSOL=function(){return"\r"};t.beep=function(){return""};t.hideCursor=function hideCursor(){return r+"?25l"};t.showCursor=function showCursor(){return r+"?25h"};var s={reset:0,bold:1,italic:3,underline:4,inverse:7,stopBold:22,stopItalic:23,stopUnderline:24,stopInverse:27,white:37,black:30,blue:34,cyan:36,green:32,magenta:35,red:31,yellow:33,bgWhite:47,bgBlack:40,bgBlue:44,bgCyan:46,bgGreen:42,bgMagenta:45,bgRed:41,bgYellow:43,grey:90,brightBlack:90,brightRed:91,brightGreen:92,brightYellow:93,brightBlue:94,brightMagenta:95,brightCyan:96,brightWhite:97,bgGrey:100,bgBrightBlack:100,bgBrightRed:101,bgBrightGreen:102,bgBrightYellow:103,bgBrightBlue:104,bgBrightMagenta:105,bgBrightCyan:106,bgBrightWhite:107};t.color=function color(e){if(arguments.length!==1||!Array.isArray(e)){e=Array.prototype.slice.call(arguments)}return r+e.map(colorNameToCode).join(";")+"m"};function colorNameToCode(e){if(s[e]!=null)return s[e];throw new Error("Unknown color or style name: "+e)}},5898:(e,t)=>{function isArray(e){if(Array.isArray){return Array.isArray(e)}return objectToString(e)==="[object Array]"}t.isArray=isArray;function isBoolean(e){return typeof e==="boolean"}t.isBoolean=isBoolean;function isNull(e){return e===null}t.isNull=isNull;function isNullOrUndefined(e){return e==null}t.isNullOrUndefined=isNullOrUndefined;function isNumber(e){return typeof e==="number"}t.isNumber=isNumber;function isString(e){return typeof e==="string"}t.isString=isString;function isSymbol(e){return typeof e==="symbol"}t.isSymbol=isSymbol;function isUndefined(e){return e===void 0}t.isUndefined=isUndefined;function isRegExp(e){return objectToString(e)==="[object RegExp]"}t.isRegExp=isRegExp;function isObject(e){return typeof e==="object"&&e!==null}t.isObject=isObject;function isDate(e){return objectToString(e)==="[object Date]"}t.isDate=isDate;function isError(e){return objectToString(e)==="[object Error]"||e instanceof Error}t.isError=isError;function isFunction(e){return typeof e==="function"}t.isFunction=isFunction;function isPrimitive(e){return e===null||typeof e==="boolean"||typeof e==="number"||typeof e==="string"||typeof e==="symbol"||typeof e==="undefined"}t.isPrimitive=isPrimitive;t.isBuffer=Buffer.isBuffer;function objectToString(e){return Object.prototype.toString.call(e)}},1318:e=>{e.exports=Delegator;function Delegator(e,t){if(!(this instanceof Delegator))return new Delegator(e,t);this.proto=e;this.target=t;this.methods=[];this.getters=[];this.setters=[];this.fluents=[]}Delegator.prototype.method=function(e){var t=this.proto;var r=this.target;this.methods.push(e);t[e]=function(){return this[r][e].apply(this[r],arguments)};return this};Delegator.prototype.access=function(e){return this.getter(e).setter(e)};Delegator.prototype.getter=function(e){var t=this.proto;var r=this.target;this.getters.push(e);t.__defineGetter__(e,(function(){return this[r][e]}));return this};Delegator.prototype.setter=function(e){var t=this.proto;var r=this.target;this.setters.push(e);t.__defineSetter__(e,(function(t){return this[r][e]=t}));return this};Delegator.prototype.fluent=function(e){var t=this.proto;var r=this.target;this.fluents.push(e);t[e]=function(t){if("undefined"!=typeof t){this[r][e]=t;return this}else{return this[r][e]}};return this}},4889:(e,t,r)=>{"use strict";var s=r(2087).platform();var a=r(3129).spawnSync;var o=r(5747).readdirSync;var u="glibc";var c="musl";var h={encoding:"utf8",env:process.env};if(!a){a=function(){return{status:126,stdout:"",stderr:""}}}function contains(e){return function(t){return t.indexOf(e)!==-1}}function versionFromMuslLdd(e){return e.split(/[\r\n]+/)[1].trim().split(/\s/)[1]}function safeReaddirSync(e){try{return o(e)}catch(e){}return[]}var p="";var d="";var v="";if(s==="linux"){var m=a("getconf",["GNU_LIBC_VERSION"],h);if(m.status===0){p=u;d=m.stdout.trim().split(" ")[1];v="getconf"}else{var g=a("ldd",["--version"],h);if(g.status===0&&g.stdout.indexOf(c)!==-1){p=c;d=versionFromMuslLdd(g.stdout);v="ldd"}else if(g.status===1&&g.stderr.indexOf(c)!==-1){p=c;d=versionFromMuslLdd(g.stderr);v="ldd"}else{var y=safeReaddirSync("/lib");if(y.some(contains("-linux-gnu"))){p=u;v="filesystem"}else if(y.some(contains("libc.musl-"))){p=c;v="filesystem"}else if(y.some(contains("ld-musl-"))){p=c;v="filesystem"}else{var _=safeReaddirSync("/usr/sbin");if(_.some(contains("glibc"))){p=u;v="filesystem"}}}}}var E=p!==""&&p!==u;e.exports={GLIBC:u,MUSL:c,family:p,version:d,method:v,isNonGlibcLinux:E}},3887:e=>{e.exports=["🀄","🃏","🅰","🅱","🅾","🅿","🆎","🆑","🆒","🆓","🆔","🆕","🆖","🆗","🆘","🆙","🆚","🇦🇨","🇦🇩","🇦🇪","🇦🇫","🇦🇬","🇦🇮","🇦🇱","🇦🇲","🇦🇴","🇦🇶","🇦🇷","🇦🇸","🇦🇹","🇦🇺","🇦🇼","🇦🇽","🇦🇿","🇦","🇧🇦","🇧🇧","🇧🇩","🇧🇪","🇧🇫","🇧🇬","🇧🇭","🇧🇮","🇧🇯","🇧🇱","🇧🇲","🇧🇳","🇧🇴","🇧🇶","🇧🇷","🇧🇸","🇧🇹","🇧🇻","🇧🇼","🇧🇾","🇧🇿","🇧","🇨🇦","🇨🇨","🇨🇩","🇨🇫","🇨🇬","🇨🇭","🇨🇮","🇨🇰","🇨🇱","🇨🇲","🇨🇳","🇨🇴","🇨🇵","🇨🇷","🇨🇺","🇨🇻","🇨🇼","🇨🇽","🇨🇾","🇨🇿","🇨","🇩🇪","🇩🇬","🇩🇯","🇩🇰","🇩🇲","🇩🇴","🇩🇿","🇩","🇪🇦","🇪🇨","🇪🇪","🇪🇬","🇪🇭","🇪🇷","🇪🇸","🇪🇹","🇪🇺","🇪","🇫🇮","🇫🇯","🇫🇰","🇫🇲","🇫🇴","🇫🇷","🇫","🇬🇦","🇬🇧","🇬🇩","🇬🇪","🇬🇫","🇬🇬","🇬🇭","🇬🇮","🇬🇱","🇬🇲","🇬🇳","🇬🇵","🇬🇶","🇬🇷","🇬🇸","🇬🇹","🇬🇺","🇬🇼","🇬🇾","🇬","🇭🇰","🇭🇲","🇭🇳","🇭🇷","🇭🇹","🇭🇺","🇭","🇮🇨","🇮🇩","🇮🇪","🇮🇱","🇮🇲","🇮🇳","🇮🇴","🇮🇶","🇮🇷","🇮🇸","🇮🇹","🇮","🇯🇪","🇯🇲","🇯🇴","🇯🇵","🇯","🇰🇪","🇰🇬","🇰🇭","🇰🇮","🇰🇲","🇰🇳","🇰🇵","🇰🇷","🇰🇼","🇰🇾","🇰🇿","🇰","🇱🇦","🇱🇧","🇱🇨","🇱🇮","🇱🇰","🇱🇷","🇱🇸","🇱🇹","🇱🇺","🇱🇻","🇱🇾","🇱","🇲🇦","🇲🇨","🇲🇩","🇲🇪","🇲🇫","🇲🇬","🇲🇭","🇲🇰","🇲🇱","🇲🇲","🇲🇳","🇲🇴","🇲🇵","🇲🇶","🇲🇷","🇲🇸","🇲🇹","🇲🇺","🇲🇻","🇲🇼","🇲🇽","🇲🇾","🇲🇿","🇲","🇳🇦","🇳🇨","🇳🇪","🇳🇫","🇳🇬","🇳🇮","🇳🇱","🇳🇴","🇳🇵","🇳🇷","🇳🇺","🇳🇿","🇳","🇴🇲","🇴","🇵🇦","🇵🇪","🇵🇫","🇵🇬","🇵🇭","🇵🇰","🇵🇱","🇵🇲","🇵🇳","🇵🇷","🇵🇸","🇵🇹","🇵🇼","🇵🇾","🇵","🇶🇦","🇶","🇷🇪","🇷🇴","🇷🇸","🇷🇺","🇷🇼","🇷","🇸🇦","🇸🇧","🇸🇨","🇸🇩","🇸🇪","🇸🇬","🇸🇭","🇸🇮","🇸🇯","🇸🇰","🇸🇱","🇸🇲","🇸🇳","🇸🇴","🇸🇷","🇸🇸","🇸🇹","🇸🇻","🇸🇽","🇸🇾","🇸🇿","🇸","🇹🇦","🇹🇨","🇹🇩","🇹🇫","🇹🇬","🇹🇭","🇹🇯","🇹🇰","🇹🇱","🇹🇲","🇹🇳","🇹🇴","🇹🇷","🇹🇹","🇹🇻","🇹🇼","🇹🇿","🇹","🇺🇦","🇺🇬","🇺🇲","🇺🇳","🇺🇸","🇺🇾","🇺🇿","🇺","🇻🇦","🇻🇨","🇻🇪","🇻🇬","🇻🇮","🇻🇳","🇻🇺","🇻","🇼🇫","🇼🇸","🇼","🇽🇰","🇽","🇾🇪","🇾🇹","🇾","🇿🇦","🇿🇲","🇿🇼","🇿","🈁","🈂","🈚","🈯","🈲","🈳","🈴","🈵","🈶","🈷","🈸","🈹","🈺","🉐","🉑","🌀","🌁","🌂","🌃","🌄","🌅","🌆","🌇","🌈","🌉","🌊","🌋","🌌","🌍","🌎","🌏","🌐","🌑","🌒","🌓","🌔","🌕","🌖","🌗","🌘","🌙","🌚","🌛","🌜","🌝","🌞","🌟","🌠","🌡","🌤","🌥","🌦","🌧","🌨","🌩","🌪","🌫","🌬","🌭","🌮","🌯","🌰","🌱","🌲","🌳","🌴","🌵","🌶","🌷","🌸","🌹","🌺","🌻","🌼","🌽","🌾","🌿","🍀","🍁","🍂","🍃","🍄","🍅","🍆","🍇","🍈","🍉","🍊","🍋","🍌","🍍","🍎","🍏","🍐","🍑","🍒","🍓","🍔","🍕","🍖","🍗","🍘","🍙","🍚","🍛","🍜","🍝","🍞","🍟","🍠","🍡","🍢","🍣","🍤","🍥","🍦","🍧","🍨","🍩","🍪","🍫","🍬","🍭","🍮","🍯","🍰","🍱","🍲","🍳","🍴","🍵","🍶","🍷","🍸","🍹","🍺","🍻","🍼","🍽","🍾","🍿","🎀","🎁","🎂","🎃","🎄","🎅🏻","🎅🏼","🎅🏽","🎅🏾","🎅🏿","🎅","🎆","🎇","🎈","🎉","🎊","🎋","🎌","🎍","🎎","🎏","🎐","🎑","🎒","🎓","🎖","🎗","🎙","🎚","🎛","🎞","🎟","🎠","🎡","🎢","🎣","🎤","🎥","🎦","🎧","🎨","🎩","🎪","🎫","🎬","🎭","🎮","🎯","🎰","🎱","🎲","🎳","🎴","🎵","🎶","🎷","🎸","🎹","🎺","🎻","🎼","🎽","🎾","🎿","🏀","🏁","🏂🏻","🏂🏼","🏂🏽","🏂🏾","🏂🏿","🏂","🏃🏻‍♀️","🏃🏻‍♂️","🏃🏻","🏃🏼‍♀️","🏃🏼‍♂️","🏃🏼","🏃🏽‍♀️","🏃🏽‍♂️","🏃🏽","🏃🏾‍♀️","🏃🏾‍♂️","🏃🏾","🏃🏿‍♀️","🏃🏿‍♂️","🏃🏿","🏃‍♀️","🏃‍♂️","🏃","🏄🏻‍♀️","🏄🏻‍♂️","🏄🏻","🏄🏼‍♀️","🏄🏼‍♂️","🏄🏼","🏄🏽‍♀️","🏄🏽‍♂️","🏄🏽","🏄🏾‍♀️","🏄🏾‍♂️","🏄🏾","🏄🏿‍♀️","🏄🏿‍♂️","🏄🏿","🏄‍♀️","🏄‍♂️","🏄","🏅","🏆","🏇🏻","🏇🏼","🏇🏽","🏇🏾","🏇🏿","🏇","🏈","🏉","🏊🏻‍♀️","🏊🏻‍♂️","🏊🏻","🏊🏼‍♀️","🏊🏼‍♂️","🏊🏼","🏊🏽‍♀️","🏊🏽‍♂️","🏊🏽","🏊🏾‍♀️","🏊🏾‍♂️","🏊🏾","🏊🏿‍♀️","🏊🏿‍♂️","🏊🏿","🏊‍♀️","🏊‍♂️","🏊","🏋🏻‍♀️","🏋🏻‍♂️","🏋🏻","🏋🏼‍♀️","🏋🏼‍♂️","🏋🏼","🏋🏽‍♀️","🏋🏽‍♂️","🏋🏽","🏋🏾‍♀️","🏋🏾‍♂️","🏋🏾","🏋🏿‍♀️","🏋🏿‍♂️","🏋🏿","🏋️‍♀️","🏋️‍♂️","🏋","🏌🏻‍♀️","🏌🏻‍♂️","🏌🏻","🏌🏼‍♀️","🏌🏼‍♂️","🏌🏼","🏌🏽‍♀️","🏌🏽‍♂️","🏌🏽","🏌🏾‍♀️","🏌🏾‍♂️","🏌🏾","🏌🏿‍♀️","🏌🏿‍♂️","🏌🏿","🏌️‍♀️","🏌️‍♂️","🏌","🏍","🏎","🏏","🏐","🏑","🏒","🏓","🏔","🏕","🏖","🏗","🏘","🏙","🏚","🏛","🏜","🏝","🏞","🏟","🏠","🏡","🏢","🏣","🏤","🏥","🏦","🏧","🏨","🏩","🏪","🏫","🏬","🏭","🏮","🏯","🏰","🏳️‍🌈","🏳","🏴‍☠️","🏴","🏵","🏷","🏸","🏹","🏺","🏻","🏼","🏽","🏾","🏿","🐀","🐁","🐂","🐃","🐄","🐅","🐆","🐇","🐈","🐉","🐊","🐋","🐌","🐍","🐎","🐏","🐐","🐑","🐒","🐓","🐔","🐕","🐖","🐗","🐘","🐙","🐚","🐛","🐜","🐝","🐞","🐟","🐠","🐡","🐢","🐣","🐤","🐥","🐦","🐧","🐨","🐩","🐪","🐫","🐬","🐭","🐮","🐯","🐰","🐱","🐲","🐳","🐴","🐵","🐶","🐷","🐸","🐹","🐺","🐻","🐼","🐽","🐾","🐿","👀","👁‍🗨","👁","👂🏻","👂🏼","👂🏽","👂🏾","👂🏿","👂","👃🏻","👃🏼","👃🏽","👃🏾","👃🏿","👃","👄","👅","👆🏻","👆🏼","👆🏽","👆🏾","👆🏿","👆","👇🏻","👇🏼","👇🏽","👇🏾","👇🏿","👇","👈🏻","👈🏼","👈🏽","👈🏾","👈🏿","👈","👉🏻","👉🏼","👉🏽","👉🏾","👉🏿","👉","👊🏻","👊🏼","👊🏽","👊🏾","👊🏿","👊","👋🏻","👋🏼","👋🏽","👋🏾","👋🏿","👋","👌🏻","👌🏼","👌🏽","👌🏾","👌🏿","👌","👍🏻","👍🏼","👍🏽","👍🏾","👍🏿","👍","👎🏻","👎🏼","👎🏽","👎🏾","👎🏿","👎","👏🏻","👏🏼","👏🏽","👏🏾","👏🏿","👏","👐🏻","👐🏼","👐🏽","👐🏾","👐🏿","👐","👑","👒","👓","👔","👕","👖","👗","👘","👙","👚","👛","👜","👝","👞","👟","👠","👡","👢","👣","👤","👥","👦🏻","👦🏼","👦🏽","👦🏾","👦🏿","👦","👧🏻","👧🏼","👧🏽","👧🏾","👧🏿","👧","👨🏻‍🌾","👨🏻‍🍳","👨🏻‍🎓","👨🏻‍🎤","👨🏻‍🎨","👨🏻‍🏫","👨🏻‍🏭","👨🏻‍💻","👨🏻‍💼","👨🏻‍🔧","👨🏻‍🔬","👨🏻‍🚀","👨🏻‍🚒","👨🏻‍⚕️","👨🏻‍⚖️","👨🏻‍✈️","👨🏻","👨🏼‍🌾","👨🏼‍🍳","👨🏼‍🎓","👨🏼‍🎤","👨🏼‍🎨","👨🏼‍🏫","👨🏼‍🏭","👨🏼‍💻","👨🏼‍💼","👨🏼‍🔧","👨🏼‍🔬","👨🏼‍🚀","👨🏼‍🚒","👨🏼‍⚕️","👨🏼‍⚖️","👨🏼‍✈️","👨🏼","👨🏽‍🌾","👨🏽‍🍳","👨🏽‍🎓","👨🏽‍🎤","👨🏽‍🎨","👨🏽‍🏫","👨🏽‍🏭","👨🏽‍💻","👨🏽‍💼","👨🏽‍🔧","👨🏽‍🔬","👨🏽‍🚀","👨🏽‍🚒","👨🏽‍⚕️","👨🏽‍⚖️","👨🏽‍✈️","👨🏽","👨🏾‍🌾","👨🏾‍🍳","👨🏾‍🎓","👨🏾‍🎤","👨🏾‍🎨","👨🏾‍🏫","👨🏾‍🏭","👨🏾‍💻","👨🏾‍💼","👨🏾‍🔧","👨🏾‍🔬","👨🏾‍🚀","👨🏾‍🚒","👨🏾‍⚕️","👨🏾‍⚖️","👨🏾‍✈️","👨🏾","👨🏿‍🌾","👨🏿‍🍳","👨🏿‍🎓","👨🏿‍🎤","👨🏿‍🎨","👨🏿‍🏫","👨🏿‍🏭","👨🏿‍💻","👨🏿‍💼","👨🏿‍🔧","👨🏿‍🔬","👨🏿‍🚀","👨🏿‍🚒","👨🏿‍⚕️","👨🏿‍⚖️","👨🏿‍✈️","👨🏿","👨‍🌾","👨‍🍳","👨‍🎓","👨‍🎤","👨‍🎨","👨‍🏫","👨‍🏭","👨‍👦‍👦","👨‍👦","👨‍👧‍👦","👨‍👧‍👧","👨‍👧","👨‍👨‍👦‍👦","👨‍👨‍👦","👨‍👨‍👧‍👦","👨‍👨‍👧‍👧","👨‍👨‍👧","👨‍👩‍👦‍👦","👨‍👩‍👦","👨‍👩‍👧‍👦","👨‍👩‍👧‍👧","👨‍👩‍👧","👨‍💻","👨‍💼","👨‍🔧","👨‍🔬","👨‍🚀","👨‍🚒","👨‍⚕️","👨‍⚖️","👨‍✈️","👨‍❤️‍👨","👨‍❤️‍💋‍👨","👨","👩🏻‍🌾","👩🏻‍🍳","👩🏻‍🎓","👩🏻‍🎤","👩🏻‍🎨","👩🏻‍🏫","👩🏻‍🏭","👩🏻‍💻","👩🏻‍💼","👩🏻‍🔧","👩🏻‍🔬","👩🏻‍🚀","👩🏻‍🚒","👩🏻‍⚕️","👩🏻‍⚖️","👩🏻‍✈️","👩🏻","👩🏼‍🌾","👩🏼‍🍳","👩🏼‍🎓","👩🏼‍🎤","👩🏼‍🎨","👩🏼‍🏫","👩🏼‍🏭","👩🏼‍💻","👩🏼‍💼","👩🏼‍🔧","👩🏼‍🔬","👩🏼‍🚀","👩🏼‍🚒","👩🏼‍⚕️","👩🏼‍⚖️","👩🏼‍✈️","👩🏼","👩🏽‍🌾","👩🏽‍🍳","👩🏽‍🎓","👩🏽‍🎤","👩🏽‍🎨","👩🏽‍🏫","👩🏽‍🏭","👩🏽‍💻","👩🏽‍💼","👩🏽‍🔧","👩🏽‍🔬","👩🏽‍🚀","👩🏽‍🚒","👩🏽‍⚕️","👩🏽‍⚖️","👩🏽‍✈️","👩🏽","👩🏾‍🌾","👩🏾‍🍳","👩🏾‍🎓","👩🏾‍🎤","👩🏾‍🎨","👩🏾‍🏫","👩🏾‍🏭","👩🏾‍💻","👩🏾‍💼","👩🏾‍🔧","👩🏾‍🔬","👩🏾‍🚀","👩🏾‍🚒","👩🏾‍⚕️","👩🏾‍⚖️","👩🏾‍✈️","👩🏾","👩🏿‍🌾","👩🏿‍🍳","👩🏿‍🎓","👩🏿‍🎤","👩🏿‍🎨","👩🏿‍🏫","👩🏿‍🏭","👩🏿‍💻","👩🏿‍💼","👩🏿‍🔧","👩🏿‍🔬","👩🏿‍🚀","👩🏿‍🚒","👩🏿‍⚕️","👩🏿‍⚖️","👩🏿‍✈️","👩🏿","👩‍🌾","👩‍🍳","👩‍🎓","👩‍🎤","👩‍🎨","👩‍🏫","👩‍🏭","👩‍👦‍👦","👩‍👦","👩‍👧‍👦","👩‍👧‍👧","👩‍👧","👩‍👩‍👦‍👦","👩‍👩‍👦","👩‍👩‍👧‍👦","👩‍👩‍👧‍👧","👩‍👩‍👧","👩‍💻","👩‍💼","👩‍🔧","👩‍🔬","👩‍🚀","👩‍🚒","👩‍⚕️","👩‍⚖️","👩‍✈️","👩‍❤️‍👨","👩‍❤️‍👩","👩‍❤️‍💋‍👨","👩‍❤️‍💋‍👩","👩","👪🏻","👪🏼","👪🏽","👪🏾","👪🏿","👪","👫🏻","👫🏼","👫🏽","👫🏾","👫🏿","👫","👬🏻","👬🏼","👬🏽","👬🏾","👬🏿","👬","👭🏻","👭🏼","👭🏽","👭🏾","👭🏿","👭","👮🏻‍♀️","👮🏻‍♂️","👮🏻","👮🏼‍♀️","👮🏼‍♂️","👮🏼","👮🏽‍♀️","👮🏽‍♂️","👮🏽","👮🏾‍♀️","👮🏾‍♂️","👮🏾","👮🏿‍♀️","👮🏿‍♂️","👮🏿","👮‍♀️","👮‍♂️","👮","👯🏻‍♀️","👯🏻‍♂️","👯🏻","👯🏼‍♀️","👯🏼‍♂️","👯🏼","👯🏽‍♀️","👯🏽‍♂️","👯🏽","👯🏾‍♀️","👯🏾‍♂️","👯🏾","👯🏿‍♀️","👯🏿‍♂️","👯🏿","👯‍♀️","👯‍♂️","👯","👰🏻","👰🏼","👰🏽","👰🏾","👰🏿","👰","👱🏻‍♀️","👱🏻‍♂️","👱🏻","👱🏼‍♀️","👱🏼‍♂️","👱🏼","👱🏽‍♀️","👱🏽‍♂️","👱🏽","👱🏾‍♀️","👱🏾‍♂️","👱🏾","👱🏿‍♀️","👱🏿‍♂️","👱🏿","👱‍♀️","👱‍♂️","👱","👲🏻","👲🏼","👲🏽","👲🏾","👲🏿","👲","👳🏻‍♀️","👳🏻‍♂️","👳🏻","👳🏼‍♀️","👳🏼‍♂️","👳🏼","👳🏽‍♀️","👳🏽‍♂️","👳🏽","👳🏾‍♀️","👳🏾‍♂️","👳🏾","👳🏿‍♀️","👳🏿‍♂️","👳🏿","👳‍♀️","👳‍♂️","👳","👴🏻","👴🏼","👴🏽","👴🏾","👴🏿","👴","👵🏻","👵🏼","👵🏽","👵🏾","👵🏿","👵","👶🏻","👶🏼","👶🏽","👶🏾","👶🏿","👶","👷🏻‍♀️","👷🏻‍♂️","👷🏻","👷🏼‍♀️","👷🏼‍♂️","👷🏼","👷🏽‍♀️","👷🏽‍♂️","👷🏽","👷🏾‍♀️","👷🏾‍♂️","👷🏾","👷🏿‍♀️","👷🏿‍♂️","👷🏿","👷‍♀️","👷‍♂️","👷","👸🏻","👸🏼","👸🏽","👸🏾","👸🏿","👸","👹","👺","👻","👼🏻","👼🏼","👼🏽","👼🏾","👼🏿","👼","👽","👾","👿","💀","💁🏻‍♀️","💁🏻‍♂️","💁🏻","💁🏼‍♀️","💁🏼‍♂️","💁🏼","💁🏽‍♀️","💁🏽‍♂️","💁🏽","💁🏾‍♀️","💁🏾‍♂️","💁🏾","💁🏿‍♀️","💁🏿‍♂️","💁🏿","💁‍♀️","💁‍♂️","💁","💂🏻‍♀️","💂🏻‍♂️","💂🏻","💂🏼‍♀️","💂🏼‍♂️","💂🏼","💂🏽‍♀️","💂🏽‍♂️","💂🏽","💂🏾‍♀️","💂🏾‍♂️","💂🏾","💂🏿‍♀️","💂🏿‍♂️","💂🏿","💂‍♀️","💂‍♂️","💂","💃🏻","💃🏼","💃🏽","💃🏾","💃🏿","💃","💄","💅🏻","💅🏼","💅🏽","💅🏾","💅🏿","💅","💆🏻‍♀️","💆🏻‍♂️","💆🏻","💆🏼‍♀️","💆🏼‍♂️","💆🏼","💆🏽‍♀️","💆🏽‍♂️","💆🏽","💆🏾‍♀️","💆🏾‍♂️","💆🏾","💆🏿‍♀️","💆🏿‍♂️","💆🏿","💆‍♀️","💆‍♂️","💆","💇🏻‍♀️","💇🏻‍♂️","💇🏻","💇🏼‍♀️","💇🏼‍♂️","💇🏼","💇🏽‍♀️","💇🏽‍♂️","💇🏽","💇🏾‍♀️","💇🏾‍♂️","💇🏾","💇🏿‍♀️","💇🏿‍♂️","💇🏿","💇‍♀️","💇‍♂️","💇","💈","💉","💊","💋","💌","💍","💎","💏","💐","💑","💒","💓","💔","💕","💖","💗","💘","💙","💚","💛","💜","💝","💞","💟","💠","💡","💢","💣","💤","💥","💦","💧","💨","💩","💪🏻","💪🏼","💪🏽","💪🏾","💪🏿","💪","💫","💬","💭","💮","💯","💰","💱","💲","💳","💴","💵","💶","💷","💸","💹","💺","💻","💼","💽","💾","💿","📀","📁","📂","📃","📄","📅","📆","📇","📈","📉","📊","📋","📌","📍","📎","📏","📐","📑","📒","📓","📔","📕","📖","📗","📘","📙","📚","📛","📜","📝","📞","📟","📠","📡","📢","📣","📤","📥","📦","📧","📨","📩","📪","📫","📬","📭","📮","📯","📰","📱","📲","📳","📴","📵","📶","📷","📸","📹","📺","📻","📼","📽","📿","🔀","🔁","🔂","🔃","🔄","🔅","🔆","🔇","🔈","🔉","🔊","🔋","🔌","🔍","🔎","🔏","🔐","🔑","🔒","🔓","🔔","🔕","🔖","🔗","🔘","🔙","🔚","🔛","🔜","🔝","🔞","🔟","🔠","🔡","🔢","🔣","🔤","🔥","🔦","🔧","🔨","🔩","🔪","🔫","🔬","🔭","🔮","🔯","🔰","🔱","🔲","🔳","🔴","🔵","🔶","🔷","🔸","🔹","🔺","🔻","🔼","🔽","🕉","🕊","🕋","🕌","🕍","🕎","🕐","🕑","🕒","🕓","🕔","🕕","🕖","🕗","🕘","🕙","🕚","🕛","🕜","🕝","🕞","🕟","🕠","🕡","🕢","🕣","🕤","🕥","🕦","🕧","🕯","🕰","🕳","🕴🏻","🕴🏼","🕴🏽","🕴🏾","🕴🏿","🕴","🕵🏻‍♀️","🕵🏻‍♂️","🕵🏻","🕵🏼‍♀️","🕵🏼‍♂️","🕵🏼","🕵🏽‍♀️","🕵🏽‍♂️","🕵🏽","🕵🏾‍♀️","🕵🏾‍♂️","🕵🏾","🕵🏿‍♀️","🕵🏿‍♂️","🕵🏿","🕵️‍♀️","🕵️‍♂️","🕵","🕶","🕷","🕸","🕹","🕺🏻","🕺🏼","🕺🏽","🕺🏾","🕺🏿","🕺","🖇","🖊","🖋","🖌","🖍","🖐🏻","🖐🏼","🖐🏽","🖐🏾","🖐🏿","🖐","🖕🏻","🖕🏼","🖕🏽","🖕🏾","🖕🏿","🖕","🖖🏻","🖖🏼","🖖🏽","🖖🏾","🖖🏿","🖖","🖤","🖥","🖨","🖱","🖲","🖼","🗂","🗃","🗄","🗑","🗒","🗓","🗜","🗝","🗞","🗡","🗣","🗨","🗯","🗳","🗺","🗻","🗼","🗽","🗾","🗿","😀","😁","😂","😃","😄","😅","😆","😇","😈","😉","😊","😋","😌","😍","😎","😏","😐","😑","😒","😓","😔","😕","😖","😗","😘","😙","😚","😛","😜","😝","😞","😟","😠","😡","😢","😣","😤","😥","😦","😧","😨","😩","😪","😫","😬","😭","😮","😯","😰","😱","😲","😳","😴","😵","😶","😷","😸","😹","😺","😻","😼","😽","😾","😿","🙀","🙁","🙂","🙃","🙄","🙅🏻‍♀️","🙅🏻‍♂️","🙅🏻","🙅🏼‍♀️","🙅🏼‍♂️","🙅🏼","🙅🏽‍♀️","🙅🏽‍♂️","🙅🏽","🙅🏾‍♀️","🙅🏾‍♂️","🙅🏾","🙅🏿‍♀️","🙅🏿‍♂️","🙅🏿","🙅‍♀️","🙅‍♂️","🙅","🙆🏻‍♀️","🙆🏻‍♂️","🙆🏻","🙆🏼‍♀️","🙆🏼‍♂️","🙆🏼","🙆🏽‍♀️","🙆🏽‍♂️","🙆🏽","🙆🏾‍♀️","🙆🏾‍♂️","🙆🏾","🙆🏿‍♀️","🙆🏿‍♂️","🙆🏿","🙆‍♀️","🙆‍♂️","🙆","🙇🏻‍♀️","🙇🏻‍♂️","🙇🏻","🙇🏼‍♀️","🙇🏼‍♂️","🙇🏼","🙇🏽‍♀️","🙇🏽‍♂️","🙇🏽","🙇🏾‍♀️","🙇🏾‍♂️","🙇🏾","🙇🏿‍♀️","🙇🏿‍♂️","🙇🏿","🙇‍♀️","🙇‍♂️","🙇","🙈","🙉","🙊","🙋🏻‍♀️","🙋🏻‍♂️","🙋🏻","🙋🏼‍♀️","🙋🏼‍♂️","🙋🏼","🙋🏽‍♀️","🙋🏽‍♂️","🙋🏽","🙋🏾‍♀️","🙋🏾‍♂️","🙋🏾","🙋🏿‍♀️","🙋🏿‍♂️","🙋🏿","🙋‍♀️","🙋‍♂️","🙋","🙌🏻","🙌🏼","🙌🏽","🙌🏾","🙌🏿","🙌","🙍🏻‍♀️","🙍🏻‍♂️","🙍🏻","🙍🏼‍♀️","🙍🏼‍♂️","🙍🏼","🙍🏽‍♀️","🙍🏽‍♂️","🙍🏽","🙍🏾‍♀️","🙍🏾‍♂️","🙍🏾","🙍🏿‍♀️","🙍🏿‍♂️","🙍🏿","🙍‍♀️","🙍‍♂️","🙍","🙎🏻‍♀️","🙎🏻‍♂️","🙎🏻","🙎🏼‍♀️","🙎🏼‍♂️","🙎🏼","🙎🏽‍♀️","🙎🏽‍♂️","🙎🏽","🙎🏾‍♀️","🙎🏾‍♂️","🙎🏾","🙎🏿‍♀️","🙎🏿‍♂️","🙎🏿","🙎‍♀️","🙎‍♂️","🙎","🙏🏻","🙏🏼","🙏🏽","🙏🏾","🙏🏿","🙏","🚀","🚁","🚂","🚃","🚄","🚅","🚆","🚇","🚈","🚉","🚊","🚋","🚌","🚍","🚎","🚏","🚐","🚑","🚒","🚓","🚔","🚕","🚖","🚗","🚘","🚙","🚚","🚛","🚜","🚝","🚞","🚟","🚠","🚡","🚢","🚣🏻‍♀️","🚣🏻‍♂️","🚣🏻","🚣🏼‍♀️","🚣🏼‍♂️","🚣🏼","🚣🏽‍♀️","🚣🏽‍♂️","🚣🏽","🚣🏾‍♀️","🚣🏾‍♂️","🚣🏾","🚣🏿‍♀️","🚣🏿‍♂️","🚣🏿","🚣‍♀️","🚣‍♂️","🚣","🚤","🚥","🚦","🚧","🚨","🚩","🚪","🚫","🚬","🚭","🚮","🚯","🚰","🚱","🚲","🚳","🚴🏻‍♀️","🚴🏻‍♂️","🚴🏻","🚴🏼‍♀️","🚴🏼‍♂️","🚴🏼","🚴🏽‍♀️","🚴🏽‍♂️","🚴🏽","🚴🏾‍♀️","🚴🏾‍♂️","🚴🏾","🚴🏿‍♀️","🚴🏿‍♂️","🚴🏿","🚴‍♀️","🚴‍♂️","🚴","🚵🏻‍♀️","🚵🏻‍♂️","🚵🏻","🚵🏼‍♀️","🚵🏼‍♂️","🚵🏼","🚵🏽‍♀️","🚵🏽‍♂️","🚵🏽","🚵🏾‍♀️","🚵🏾‍♂️","🚵🏾","🚵🏿‍♀️","🚵🏿‍♂️","🚵🏿","🚵‍♀️","🚵‍♂️","🚵","🚶🏻‍♀️","🚶🏻‍♂️","🚶🏻","🚶🏼‍♀️","🚶🏼‍♂️","🚶🏼","🚶🏽‍♀️","🚶🏽‍♂️","🚶🏽","🚶🏾‍♀️","🚶🏾‍♂️","🚶🏾","🚶🏿‍♀️","🚶🏿‍♂️","🚶🏿","🚶‍♀️","🚶‍♂️","🚶","🚷","🚸","🚹","🚺","🚻","🚼","🚽","🚾","🚿","🛀🏻","🛀🏼","🛀🏽","🛀🏾","🛀🏿","🛀","🛁","🛂","🛃","🛄","🛅","🛋","🛌🏻","🛌🏼","🛌🏽","🛌🏾","🛌🏿","🛌","🛍","🛎","🛏","🛐","🛑","🛒","🛠","🛡","🛢","🛣","🛤","🛥","🛩","🛫","🛬","🛰","🛳","🛴","🛵","🛶","🤐","🤑","🤒","🤓","🤔","🤕","🤖","🤗","🤘🏻","🤘🏼","🤘🏽","🤘🏾","🤘🏿","🤘","🤙🏻","🤙🏼","🤙🏽","🤙🏾","🤙🏿","🤙","🤚🏻","🤚🏼","🤚🏽","🤚🏾","🤚🏿","🤚","🤛🏻","🤛🏼","🤛🏽","🤛🏾","🤛🏿","🤛","🤜🏻","🤜🏼","🤜🏽","🤜🏾","🤜🏿","🤜","🤝🏻","🤝🏼","🤝🏽","🤝🏾","🤝🏿","🤝","🤞🏻","🤞🏼","🤞🏽","🤞🏾","🤞🏿","🤞","🤠","🤡","🤢","🤣","🤤","🤥","🤦🏻‍♀️","🤦🏻‍♂️","🤦🏻","🤦🏼‍♀️","🤦🏼‍♂️","🤦🏼","🤦🏽‍♀️","🤦🏽‍♂️","🤦🏽","🤦🏾‍♀️","🤦🏾‍♂️","🤦🏾","🤦🏿‍♀️","🤦🏿‍♂️","🤦🏿","🤦‍♀️","🤦‍♂️","🤦","🤧","🤰🏻","🤰🏼","🤰🏽","🤰🏾","🤰🏿","🤰","🤳🏻","🤳🏼","🤳🏽","🤳🏾","🤳🏿","🤳","🤴🏻","🤴🏼","🤴🏽","🤴🏾","🤴🏿","🤴","🤵🏻","🤵🏼","🤵🏽","🤵🏾","🤵🏿","🤵","🤶🏻","🤶🏼","🤶🏽","🤶🏾","🤶🏿","🤶","🤷🏻‍♀️","🤷🏻‍♂️","🤷🏻","🤷🏼‍♀️","🤷🏼‍♂️","🤷🏼","🤷🏽‍♀️","🤷🏽‍♂️","🤷🏽","🤷🏾‍♀️","🤷🏾‍♂️","🤷🏾","🤷🏿‍♀️","🤷🏿‍♂️","🤷🏿","🤷‍♀️","🤷‍♂️","🤷","🤸🏻‍♀️","🤸🏻‍♂️","🤸🏻","🤸🏼‍♀️","🤸🏼‍♂️","🤸🏼","🤸🏽‍♀️","🤸🏽‍♂️","🤸🏽","🤸🏾‍♀️","🤸🏾‍♂️","🤸🏾","🤸🏿‍♀️","🤸🏿‍♂️","🤸🏿","🤸‍♀️","🤸‍♂️","🤸","🤹🏻‍♀️","🤹🏻‍♂️","🤹🏻","🤹🏼‍♀️","🤹🏼‍♂️","🤹🏼","🤹🏽‍♀️","🤹🏽‍♂️","🤹🏽","🤹🏾‍♀️","🤹🏾‍♂️","🤹🏾","🤹🏿‍♀️","🤹🏿‍♂️","🤹🏿","🤹‍♀️","🤹‍♂️","🤹","🤺","🤼🏻‍♀️","🤼🏻‍♂️","🤼🏻","🤼🏼‍♀️","🤼🏼‍♂️","🤼🏼","🤼🏽‍♀️","🤼🏽‍♂️","🤼🏽","🤼🏾‍♀️","🤼🏾‍♂️","🤼🏾","🤼🏿‍♀️","🤼🏿‍♂️","🤼🏿","🤼‍♀️","🤼‍♂️","🤼","🤽🏻‍♀️","🤽🏻‍♂️","🤽🏻","🤽🏼‍♀️","🤽🏼‍♂️","🤽🏼","🤽🏽‍♀️","🤽🏽‍♂️","🤽🏽","🤽🏾‍♀️","🤽🏾‍♂️","🤽🏾","🤽🏿‍♀️","🤽🏿‍♂️","🤽🏿","🤽‍♀️","🤽‍♂️","🤽","🤾🏻‍♀️","🤾🏻‍♂️","🤾🏻","🤾🏼‍♀️","🤾🏼‍♂️","🤾🏼","🤾🏽‍♀️","🤾🏽‍♂️","🤾🏽","🤾🏾‍♀️","🤾🏾‍♂️","🤾🏾","🤾🏿‍♀️","🤾🏿‍♂️","🤾🏿","🤾‍♀️","🤾‍♂️","🤾","🥀","🥁","🥂","🥃","🥄","🥅","🥇","🥈","🥉","🥊","🥋","🥐","🥑","🥒","🥓","🥔","🥕","🥖","🥗","🥘","🥙","🥚","🥛","🥜","🥝","🥞","🦀","🦁","🦂","🦃","🦄","🦅","🦆","🦇","🦈","🦉","🦊","🦋","🦌","🦍","🦎","🦏","🦐","🦑","🧀","‼","⁉","™","ℹ","↔","↕","↖","↗","↘","↙","↩","↪","#⃣","⌚","⌛","⌨","⏏","⏩","⏪","⏫","⏬","⏭","⏮","⏯","⏰","⏱","⏲","⏳","⏸","⏹","⏺","Ⓜ","▪","▫","▶","◀","◻","◼","◽","◾","☀","☁","☂","☃","☄","☎","☑","☔","☕","☘","☝🏻","☝🏼","☝🏽","☝🏾","☝🏿","☝","☠","☢","☣","☦","☪","☮","☯","☸","☹","☺","♀","♂","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","♠","♣","♥","♦","♨","♻","♿","⚒","⚓","⚔","⚕","⚖","⚗","⚙","⚛","⚜","⚠","⚡","⚪","⚫","⚰","⚱","⚽","⚾","⛄","⛅","⛈","⛎","⛏","⛑","⛓","⛔","⛩","⛪","⛰","⛱","⛲","⛳","⛴","⛵","⛷🏻","⛷🏼","⛷🏽","⛷🏾","⛷🏿","⛷","⛸","⛹🏻‍♀️","⛹🏻‍♂️","⛹🏻","⛹🏼‍♀️","⛹🏼‍♂️","⛹🏼","⛹🏽‍♀️","⛹🏽‍♂️","⛹🏽","⛹🏾‍♀️","⛹🏾‍♂️","⛹🏾","⛹🏿‍♀️","⛹🏿‍♂️","⛹🏿","⛹️‍♀️","⛹️‍♂️","⛹","⛺","⛽","✂","✅","✈","✉","✊🏻","✊🏼","✊🏽","✊🏾","✊🏿","✊","✋🏻","✋🏼","✋🏽","✋🏾","✋🏿","✋","✌🏻","✌🏼","✌🏽","✌🏾","✌🏿","✌","✍🏻","✍🏼","✍🏽","✍🏾","✍🏿","✍","✏","✒","✔","✖","✝","✡","✨","✳","✴","❄","❇","❌","❎","❓","❔","❕","❗","❣","❤","➕","➖","➗","➡","➰","➿","⤴","⤵","*⃣","⬅","⬆","⬇","⬛","⬜","⭐","⭕","0⃣","〰","〽","1⃣","2⃣","㊗","㊙","3⃣","4⃣","5⃣","6⃣","7⃣","8⃣","9⃣","©","®",""]},6465:function(e,t){(function(e,r){true?r(t):0})(this,(function(e){"use strict";function walk(e,{enter:t,leave:r}){visit(e,null,t,r)}let t=false;const r={skip:()=>t=true};const s={};const a=Object.prototype.toString;function isArray(e){return a.call(e)==="[object Array]"}function visit(e,a,o,u,c,h){if(!e)return;if(o){const s=t;t=false;o.call(r,e,a,c,h);const u=t;t=s;if(u)return}const p=e.type&&s[e.type]||(s[e.type]=Object.keys(e).filter((t=>typeof e[t]==="object")));for(let t=0;t<p.length;t+=1){const r=p[t];const s=e[r];if(isArray(s)){for(let t=0;t<s.length;t+=1){s[t]&&s[t].type&&visit(s[t],e,o,u,r,t)}}else if(s&&s.type){visit(s,e,o,u,r,null)}}if(u){u(e,a,c,h)}}e.walk=walk;e.childKeys=s;Object.defineProperty(e,"__esModule",{value:true})}))},912:(e,t,r)=>{var s=r(5622).sep||"/";e.exports=fileUriToPath;function fileUriToPath(e){if("string"!=typeof e||e.length<=7||"file://"!=e.substring(0,7)){throw new TypeError("must pass in a file:// URI to convert to a file path")}var t=decodeURI(e.substring(7));var r=t.indexOf("/");var a=t.substring(0,r);var o=t.substring(r+1);if("localhost"==a)a="";if(a){a=s+s+a}o=o.replace(/^(.+)\|/,"$1:");if(s=="\\"){o=o.replace(/\//g,"\\")}if(/^.+\:/.test(o)){}else{o=s+o}return a+o}},6863:(e,t,r)=>{e.exports=realpath;realpath.realpath=realpath;realpath.sync=realpathSync;realpath.realpathSync=realpathSync;realpath.monkeypatch=monkeypatch;realpath.unmonkeypatch=unmonkeypatch;var s=r(5747);var a=s.realpath;var o=s.realpathSync;var u=process.version;var c=/^v[0-5]\./.test(u);var h=r(1734);function newError(e){return e&&e.syscall==="realpath"&&(e.code==="ELOOP"||e.code==="ENOMEM"||e.code==="ENAMETOOLONG")}function realpath(e,t,r){if(c){return a(e,t,r)}if(typeof t==="function"){r=t;t=null}a(e,t,(function(s,a){if(newError(s)){h.realpath(e,t,r)}else{r(s,a)}}))}function realpathSync(e,t){if(c){return o(e,t)}try{return o(e,t)}catch(r){if(newError(r)){return h.realpathSync(e,t)}else{throw r}}}function monkeypatch(){s.realpath=realpath;s.realpathSync=realpathSync}function unmonkeypatch(){s.realpath=a;s.realpathSync=o}},1734:(e,t,r)=>{var s=r(5622);var a=process.platform==="win32";var o=r(5747);var u=process.env.NODE_DEBUG&&/fs/.test(process.env.NODE_DEBUG);function rethrow(){var e;if(u){var t=new Error;e=debugCallback}else e=missingCallback;return e;function debugCallback(e){if(e){t.message=e.message;e=t;missingCallback(e)}}function missingCallback(e){if(e){if(process.throwDeprecation)throw e;else if(!process.noDeprecation){var t="fs: missing callback "+(e.stack||e.message);if(process.traceDeprecation)console.trace(t);else console.error(t)}}}}function maybeCallback(e){return typeof e==="function"?e:rethrow()}var c=s.normalize;if(a){var h=/(.*?)(?:[\/\\]+|$)/g}else{var h=/(.*?)(?:[\/]+|$)/g}if(a){var p=/^(?:[a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/][^\\\/]+)?[\\\/]*/}else{var p=/^[\/]*/}t.realpathSync=function realpathSync(e,t){e=s.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return t[e]}var r=e,u={},c={};var d;var v;var m;var g;start();function start(){var t=p.exec(e);d=t[0].length;v=t[0];m=t[0];g="";if(a&&!c[m]){o.lstatSync(m);c[m]=true}}while(d<e.length){h.lastIndex=d;var y=h.exec(e);g=v;v+=y[0];m=g+y[1];d=h.lastIndex;if(c[m]||t&&t[m]===m){continue}var _;if(t&&Object.prototype.hasOwnProperty.call(t,m)){_=t[m]}else{var E=o.lstatSync(m);if(!E.isSymbolicLink()){c[m]=true;if(t)t[m]=m;continue}var x=null;if(!a){var w=E.dev.toString(32)+":"+E.ino.toString(32);if(u.hasOwnProperty(w)){x=u[w]}}if(x===null){o.statSync(m);x=o.readlinkSync(m)}_=s.resolve(g,x);if(t)t[m]=_;if(!a)u[w]=x}e=s.resolve(_,e.slice(d));start()}if(t)t[r]=e;return e};t.realpath=function realpath(e,t,r){if(typeof r!=="function"){r=maybeCallback(t);t=null}e=s.resolve(e);if(t&&Object.prototype.hasOwnProperty.call(t,e)){return process.nextTick(r.bind(null,null,t[e]))}var u=e,c={},d={};var v;var m;var g;var y;start();function start(){var t=p.exec(e);v=t[0].length;m=t[0];g=t[0];y="";if(a&&!d[g]){o.lstat(g,(function(e){if(e)return r(e);d[g]=true;LOOP()}))}else{process.nextTick(LOOP)}}function LOOP(){if(v>=e.length){if(t)t[u]=e;return r(null,e)}h.lastIndex=v;var s=h.exec(e);y=m;m+=s[0];g=y+s[1];v=h.lastIndex;if(d[g]||t&&t[g]===g){return process.nextTick(LOOP)}if(t&&Object.prototype.hasOwnProperty.call(t,g)){return gotResolvedLink(t[g])}return o.lstat(g,gotStat)}function gotStat(e,s){if(e)return r(e);if(!s.isSymbolicLink()){d[g]=true;if(t)t[g]=g;return process.nextTick(LOOP)}if(!a){var u=s.dev.toString(32)+":"+s.ino.toString(32);if(c.hasOwnProperty(u)){return gotTarget(null,c[u],g)}}o.stat(g,(function(e){if(e)return r(e);o.readlink(g,(function(e,t){if(!a)c[u]=t;gotTarget(e,t)}))}))}function gotTarget(e,a,o){if(e)return r(e);var u=s.resolve(y,a);if(t)t[o]=u;gotResolvedLink(u)}function gotResolvedLink(t){e=s.resolve(t,e.slice(v));start()}}},4369:(e,t,r)=>{"use strict";var s=r(5543);var a=r(6834);e.exports={activityIndicator:function(e,t,r){if(e.spun==null)return;return s(t,e.spun)},progressbar:function(e,t,r){if(e.completed==null)return;return a(t,r,e.completed)}}},7291:(e,t,r)=>{"use strict";var s=r(1669);var a=t.User=function User(e){var t=new Error(e);Error.captureStackTrace(t,User);t.code="EGAUGE";return t};t.MissingTemplateValue=function MissingTemplateValue(e,t){var r=new a(s.format('Missing template value "%s"',e.type));Error.captureStackTrace(r,MissingTemplateValue);r.template=e;r.values=t;return r};t.Internal=function Internal(e){var t=new Error(e);Error.captureStackTrace(t,Internal);t.code="EGAUGEINTERNAL";return t}},5586:e=>{"use strict";e.exports=isWin32()||isColorTerm();function isWin32(){return process.platform==="win32"}function isColorTerm(){var e=/^screen|^xterm|^vt100|color|ansi|cygwin|linux/i;return!!process.env.COLORTERM||e.test(process.env.TERM)}},1800:(e,t,r)=>{"use strict";var s=r(7305);var a=r(5885);var o=r(5586);var u=r(4931);var c=r(6605);var h=r(5121);var p=r(9279);var d=r(6806);e.exports=Gauge;function callWith(e,t){return function(){return t.call(e)}}function Gauge(e,t){var r,a;if(e&&e.write){a=e;r=t||{}}else if(t&&t.write){a=t;r=e||{}}else{a=p.stderr;r=e||t||{}}this._status={spun:0,section:"",subsection:""};this._paused=false;this._disabled=true;this._showing=false;this._onScreen=false;this._needsRedraw=false;this._hideCursor=r.hideCursor==null?true:r.hideCursor;this._fixedFramerate=r.fixedFramerate==null?!/^v0\.8\./.test(p.version):r.fixedFramerate;this._lastUpdateAt=null;this._updateInterval=r.updateInterval==null?50:r.updateInterval;this._themes=r.themes||c;this._theme=r.theme;var o=this._computeTheme(r.theme);var u=r.template||[{type:"progressbar",length:20},{type:"activityIndicator",kerning:1,length:1},{type:"section",kerning:1,default:""},{type:"subsection",kerning:1,default:""}];this.setWriteTo(a,r.tty);var h=r.Plumbing||s;this._gauge=new h(o,u,this.getWidth());this._$$doRedraw=callWith(this,this._doRedraw);this._$$handleSizeChange=callWith(this,this._handleSizeChange);this._cleanupOnExit=r.cleanupOnExit==null||r.cleanupOnExit;this._removeOnExit=null;if(r.enabled||r.enabled==null&&this._tty&&this._tty.isTTY){this.enable()}else{this.disable()}}Gauge.prototype={};Gauge.prototype.isEnabled=function(){return!this._disabled};Gauge.prototype.setTemplate=function(e){this._gauge.setTemplate(e);if(this._showing)this._requestRedraw()};Gauge.prototype._computeTheme=function(e){if(!e)e={};if(typeof e==="string"){e=this._themes.getTheme(e)}else if(e&&(Object.keys(e).length===0||e.hasUnicode!=null||e.hasColor!=null)){var t=e.hasUnicode==null?a():e.hasUnicode;var r=e.hasColor==null?o:e.hasColor;e=this._themes.getDefault({hasUnicode:t,hasColor:r,platform:e.platform})}return e};Gauge.prototype.setThemeset=function(e){this._themes=e;this.setTheme(this._theme)};Gauge.prototype.setTheme=function(e){this._gauge.setTheme(this._computeTheme(e));if(this._showing)this._requestRedraw();this._theme=e};Gauge.prototype._requestRedraw=function(){this._needsRedraw=true;if(!this._fixedFramerate)this._doRedraw()};Gauge.prototype.getWidth=function(){return(this._tty&&this._tty.columns||80)-1};Gauge.prototype.setWriteTo=function(e,t){var r=!this._disabled;if(r)this.disable();this._writeTo=e;this._tty=t||e===p.stderr&&p.stdout.isTTY&&p.stdout||e.isTTY&&e||this._tty;if(this._gauge)this._gauge.setWidth(this.getWidth());if(r)this.enable()};Gauge.prototype.enable=function(){if(!this._disabled)return;this._disabled=false;if(this._tty)this._enableEvents();if(this._showing)this.show()};Gauge.prototype.disable=function(){if(this._disabled)return;if(this._showing){this._lastUpdateAt=null;this._showing=false;this._doRedraw();this._showing=true}this._disabled=true;if(this._tty)this._disableEvents()};Gauge.prototype._enableEvents=function(){if(this._cleanupOnExit){this._removeOnExit=u(callWith(this,this.disable))}this._tty.on("resize",this._$$handleSizeChange);if(this._fixedFramerate){this.redrawTracker=h(this._$$doRedraw,this._updateInterval);if(this.redrawTracker.unref)this.redrawTracker.unref()}};Gauge.prototype._disableEvents=function(){this._tty.removeListener("resize",this._$$handleSizeChange);if(this._fixedFramerate)clearInterval(this.redrawTracker);if(this._removeOnExit)this._removeOnExit()};Gauge.prototype.hide=function(e){if(this._disabled)return e&&p.nextTick(e);if(!this._showing)return e&&p.nextTick(e);this._showing=false;this._doRedraw();e&&d(e)};Gauge.prototype.show=function(e,t){this._showing=true;if(typeof e==="string"){this._status.section=e}else if(typeof e==="object"){var r=Object.keys(e);for(var s=0;s<r.length;++s){var a=r[s];this._status[a]=e[a]}}if(t!=null)this._status.completed=t;if(this._disabled)return;this._requestRedraw()};Gauge.prototype.pulse=function(e){this._status.subsection=e||"";this._status.spun++;if(this._disabled)return;if(!this._showing)return;this._requestRedraw()};Gauge.prototype._handleSizeChange=function(){this._gauge.setWidth(this._tty.columns-1);this._requestRedraw()};Gauge.prototype._doRedraw=function(){if(this._disabled||this._paused)return;if(!this._fixedFramerate){var e=Date.now();if(this._lastUpdateAt&&e-this._lastUpdateAt<this._updateInterval)return;this._lastUpdateAt=e}if(!this._showing&&this._onScreen){this._onScreen=false;var t=this._gauge.hide();if(this._hideCursor){t+=this._gauge.showCursor()}return this._writeTo.write(t)}if(!this._showing&&!this._onScreen)return;if(this._showing&&!this._onScreen){this._onScreen=true;this._needsRedraw=true;if(this._hideCursor){this._writeTo.write(this._gauge.hideCursor())}}if(!this._needsRedraw)return;if(!this._writeTo.write(this._gauge.show(this._status))){this._paused=true;this._writeTo.on("drain",callWith(this,(function(){this._paused=false;this._doRedraw()})))}}},4186:e=>{"use strict";function isArguments(e){return e!=null&&typeof e==="object"&&e.hasOwnProperty("callee")}var t={"*":{label:"any",check:function(){return true}},A:{label:"array",check:function(e){return Array.isArray(e)||isArguments(e)}},S:{label:"string",check:function(e){return typeof e==="string"}},N:{label:"number",check:function(e){return typeof e==="number"}},F:{label:"function",check:function(e){return typeof e==="function"}},O:{label:"object",check:function(e){return typeof e==="object"&&e!=null&&!t.A.check(e)&&!t.E.check(e)}},B:{label:"boolean",check:function(e){return typeof e==="boolean"}},E:{label:"error",check:function(e){return e instanceof Error}},Z:{label:"null",check:function(e){return e==null}}};function addSchema(e,t){var r=t[e.length]=t[e.length]||[];if(r.indexOf(e)===-1)r.push(e)}var r=e.exports=function(e,r){if(arguments.length!==2)throw wrongNumberOfArgs(["SA"],arguments.length);if(!e)throw missingRequiredArg(0,"rawSchemas");if(!r)throw missingRequiredArg(1,"args");if(!t.S.check(e))throw invalidType(0,["string"],e);if(!t.A.check(r))throw invalidType(1,["array"],r);var s=e.split("|");var a={};s.forEach((function(e){for(var r=0;r<e.length;++r){var s=e[r];if(!t[s])throw unknownType(r,s)}if(/E.*E/.test(e))throw moreThanOneError(e);addSchema(e,a);if(/E/.test(e)){addSchema(e.replace(/E.*$/,"E"),a);addSchema(e.replace(/E/,"Z"),a);if(e.length===1)addSchema("",a)}}));var o=a[r.length];if(!o){throw wrongNumberOfArgs(Object.keys(a),r.length)}for(var u=0;u<r.length;++u){var c=o.filter((function(e){var s=e[u];var a=t[s].check;return a(r[u])}));if(!c.length){var h=o.map((function(e){return t[e[u]].label})).filter((function(e){return e!=null}));throw invalidType(u,h,r[u])}o=c}};function missingRequiredArg(e){return newException("EMISSINGARG","Missing required argument #"+(e+1))}function unknownType(e,t){return newException("EUNKNOWNTYPE","Unknown type "+t+" in argument #"+(e+1))}function invalidType(e,r,s){var a;Object.keys(t).forEach((function(e){if(t[e].check(s))a=t[e].label}));return newException("EINVALIDTYPE","Argument #"+(e+1)+": Expected "+englishList(r)+" but got "+a)}function englishList(e){return e.join(", ").replace(/, ([^,]+)$/," or $1")}function wrongNumberOfArgs(e,t){var r=englishList(e);var s=e.every((function(e){return e.length===1}))?"argument":"arguments";return newException("EWRONGARGCOUNT","Expected "+r+" "+s+" but got "+t)}function moreThanOneError(e){return newException("ETOOMANYERRORTYPES",'Only one error type per argument signature is allowed, more than one found in "'+e+'"')}function newException(e,t){var s=new Error(t);s.code=e;if(Error.captureStackTrace)Error.captureStackTrace(s,r);return s}},3737:(e,t,r)=>{"use strict";var s=r(6325);e.exports=function(e){if(s(e)){return false}if(e>=4352&&(e<=4447||9001===e||9002===e||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141)){return true}return false}},6529:(e,t,r)=>{"use strict";var s=r(5591);var a=r(8929);var o=r(3737);e.exports=function(e){if(typeof e!=="string"||e.length===0){return 0}var t=0;e=s(e);for(var r=0;r<e.length;r++){var u=a(e,r);if(u<=31||u>=127&&u<=159){continue}if(u>=65536){r++}if(o(u)){t+=2}else{t++}}return t}},7305:(e,t,r)=>{"use strict";var s=r(3645);var a=r(3444);var o=r(4186);var u=e.exports=function(e,t,r){if(!r)r=80;o("OAN",[e,t,r]);this.showing=false;this.theme=e;this.width=r;this.template=t};u.prototype={};u.prototype.setTheme=function(e){o("O",[e]);this.theme=e};u.prototype.setTemplate=function(e){o("A",[e]);this.template=e};u.prototype.setWidth=function(e){o("N",[e]);this.width=e};u.prototype.hide=function(){return s.gotoSOL()+s.eraseLine()};u.prototype.hideCursor=s.hideCursor;u.prototype.showCursor=s.showCursor;u.prototype.show=function(e){var t=Object.create(this.theme);for(var r in e){t[r]=e[r]}return a(this.width,this.template,t).trim()+s.color("reset")+s.eraseLine()+s.gotoSOL()}},9279:e=>{"use strict";e.exports=process},6834:(e,t,r)=>{"use strict";var s=r(4186);var a=r(3444);var o=r(8413);var u=r(6529);e.exports=function(e,t,r){s("ONN",[e,t,r]);if(r<0)r=0;if(r>1)r=1;if(t<=0)return"";var o=Math.round(t*r);var u=t-o;var c=[{type:"complete",value:repeat(e.complete,o),length:o},{type:"remaining",value:repeat(e.remaining,u),length:u}];return a(t,c,e)};function repeat(e,t){var r="";var s=t;do{if(s%2){r+=e}s=Math.floor(s/2);e+=e}while(s&&u(r)<t);return o(r,t)}},3444:(e,t,r)=>{"use strict";var s=r(8034);var a=r(4186);var o=r(7426);var u=r(8413);var c=r(7291);var h=r(2131);function renderValueWithValues(e){return function(t){return renderValue(t,e)}}var p=e.exports=function(e,t,r){var a=prepareItems(e,t,r);var o=a.map(renderValueWithValues(r)).join("");return s.left(u(o,e),e)};function preType(e){var t=e.type[0].toUpperCase()+e.type.slice(1);return"pre"+t}function postType(e){var t=e.type[0].toUpperCase()+e.type.slice(1);return"post"+t}function hasPreOrPost(e,t){if(!e.type)return;return t[preType(e)]||t[postType(e)]}function generatePreAndPost(e,t){var r=o({},e);var s=Object.create(t);var a=[];var u=preType(r);var c=postType(r);if(s[u]){a.push({value:s[u]});s[u]=null}r.minLength=null;r.length=null;r.maxLength=null;a.push(r);s[r.type]=s[r.type];if(s[c]){a.push({value:s[c]});s[c]=null}return function(e,t,r){return p(r,a,s)}}function prepareItems(e,t,r){function cloneAndObjectify(t,s,a){var o=new h(t,e);var u=o.type;if(o.value==null){if(!(u in r)){if(o.default==null){throw new c.MissingTemplateValue(o,r)}else{o.value=o.default}}else{o.value=r[u]}}if(o.value==null||o.value==="")return null;o.index=s;o.first=s===0;o.last=s===a.length-1;if(hasPreOrPost(o,r))o.value=generatePreAndPost(o,r);return o}var s=t.map(cloneAndObjectify).filter((function(e){return e!=null}));var a=0;var o=e;var u=s.length;function consumeSpace(e){if(e>o)e=o;a+=e;o-=e}function finishSizing(e,t){if(e.finished)throw new c.Internal("Tried to finish template item that was already finished");if(t===Infinity)throw new c.Internal("Length of template item cannot be infinity");if(t!=null)e.length=t;e.minLength=null;e.maxLength=null;--u;e.finished=true;if(e.length==null)e.length=e.getBaseLength();if(e.length==null)throw new c.Internal("Finished template items must have a length");consumeSpace(e.getLength())}s.forEach((function(e){if(!e.kerning)return;var t=e.first?0:s[e.index-1].padRight;if(!e.first&&t<e.kerning)e.padLeft=e.kerning-t;if(!e.last)e.padRight=e.kerning}));s.forEach((function(e){if(e.getBaseLength()==null)return;finishSizing(e)}));var p=0;var d;var v;do{d=false;v=Math.round(o/u);s.forEach((function(e){if(e.finished)return;if(!e.maxLength)return;if(e.getMaxLength()<v){finishSizing(e,e.maxLength);d=true}}))}while(d&&p++<s.length);if(d)throw new c.Internal("Resize loop iterated too many times while determining maxLength");p=0;do{d=false;v=Math.round(o/u);s.forEach((function(e){if(e.finished)return;if(!e.minLength)return;if(e.getMinLength()>=v){finishSizing(e,e.minLength);d=true}}))}while(d&&p++<s.length);if(d)throw new c.Internal("Resize loop iterated too many times while determining minLength");v=Math.round(o/u);s.forEach((function(e){if(e.finished)return;finishSizing(e,v)}));return s}function renderFunction(e,t,r){a("OON",arguments);if(e.type){return e.value(t,t[e.type+"Theme"]||{},r)}else{return e.value(t,{},r)}}function renderValue(e,t){var r=e.getBaseLength();var a=typeof e.value==="function"?renderFunction(e,t,r):e.value;if(a==null||a==="")return"";var o=s[e.align]||s.left;var c=e.padLeft?s.left("",e.padLeft):"";var h=e.padRight?s.right("",e.padRight):"";var p=u(String(a),r);var d=o(p,r);return c+d+h}},6806:(e,t,r)=>{"use strict";var s=r(9279);try{e.exports=setImmediate}catch(t){e.exports=s.nextTick}},5121:e=>{"use strict";e.exports=setInterval},5543:e=>{"use strict";e.exports=function spin(e,t){return e[t%e.length]}},2131:(e,t,r)=>{"use strict";var s=r(6529);e.exports=TemplateItem;function isPercent(e){if(typeof e!=="string")return false;return e.slice(-1)==="%"}function percent(e){return Number(e.slice(0,-1))/100}function TemplateItem(e,t){this.overallOutputLength=t;this.finished=false;this.type=null;this.value=null;this.length=null;this.maxLength=null;this.minLength=null;this.kerning=null;this.align="left";this.padLeft=0;this.padRight=0;this.index=null;this.first=null;this.last=null;if(typeof e==="string"){this.value=e}else{for(var r in e)this[r]=e[r]}if(isPercent(this.length)){this.length=Math.round(this.overallOutputLength*percent(this.length))}if(isPercent(this.minLength)){this.minLength=Math.round(this.overallOutputLength*percent(this.minLength))}if(isPercent(this.maxLength)){this.maxLength=Math.round(this.overallOutputLength*percent(this.maxLength))}return this}TemplateItem.prototype={};TemplateItem.prototype.getBaseLength=function(){var e=this.length;if(e==null&&typeof this.value==="string"&&this.maxLength==null&&this.minLength==null){e=s(this.value)}return e};TemplateItem.prototype.getLength=function(){var e=this.getBaseLength();if(e==null)return null;return e+this.padLeft+this.padRight};TemplateItem.prototype.getMaxLength=function(){if(this.maxLength==null)return null;return this.maxLength+this.padLeft+this.padRight};TemplateItem.prototype.getMinLength=function(){if(this.minLength==null)return null;return this.minLength+this.padLeft+this.padRight}},1519:(e,t,r)=>{"use strict";var s=r(7426);e.exports=function(){return a.newThemeSet()};var a={};a.baseTheme=r(4369);a.newTheme=function(e,t){if(!t){t=e;e=this.baseTheme}return s({},e,t)};a.getThemeNames=function(){return Object.keys(this.themes)};a.addTheme=function(e,t,r){this.themes[e]=this.newTheme(t,r)};a.addToAllThemes=function(e){var t=this.themes;Object.keys(t).forEach((function(r){s(t[r],e)}));s(this.baseTheme,e)};a.getTheme=function(e){if(!this.themes[e])throw this.newMissingThemeError(e);return this.themes[e]};a.setDefault=function(e,t){if(t==null){t=e;e={}}var r=e.platform==null?"fallback":e.platform;var s=!!e.hasUnicode;var a=!!e.hasColor;if(!this.defaults[r])this.defaults[r]={true:{},false:{}};this.defaults[r][s][a]=t};a.getDefault=function(e){if(!e)e={};var t=e.platform||process.platform;var r=this.defaults[t]||this.defaults.fallback;var a=!!e.hasUnicode;var o=!!e.hasColor;if(!r)throw this.newMissingDefaultThemeError(t,a,o);if(!r[a][o]){if(a&&o&&r[!a][o]){a=false}else if(a&&o&&r[a][!o]){o=false}else if(a&&o&&r[!a][!o]){a=false;o=false}else if(a&&!o&&r[!a][o]){a=false}else if(!a&&o&&r[a][!o]){o=false}else if(r===this.defaults.fallback){throw this.newMissingDefaultThemeError(t,a,o)}}if(r[a][o]){return this.getTheme(r[a][o])}else{return this.getDefault(s({},e,{platform:"fallback"}))}};a.newMissingThemeError=function newMissingThemeError(e){var t=new Error('Could not find a gauge theme named "'+e+'"');Error.captureStackTrace.call(t,newMissingThemeError);t.theme=e;t.code="EMISSINGTHEME";return t};a.newMissingDefaultThemeError=function newMissingDefaultThemeError(e,t,r){var s=new Error("Could not find a gauge theme for your platform/unicode/color use combo:\n"+"    platform = "+e+"\n"+"    hasUnicode = "+t+"\n"+"    hasColor = "+r);Error.captureStackTrace.call(s,newMissingDefaultThemeError);s.platform=e;s.hasUnicode=t;s.hasColor=r;s.code="EMISSINGTHEME";return s};a.newThemeSet=function(){var themeset=function(e){return themeset.getDefault(e)};return s(themeset,a,{themes:s({},this.themes),baseTheme:s({},this.baseTheme),defaults:JSON.parse(JSON.stringify(this.defaults||{}))})}},6605:(e,t,r)=>{"use strict";var s=r(3645);var a=r(1519);var o=e.exports=new a;o.addTheme("ASCII",{preProgressbar:"[",postProgressbar:"]",progressbarTheme:{complete:"#",remaining:"."},activityIndicatorTheme:"-\\|/",preSubsection:">"});o.addTheme("colorASCII",o.getTheme("ASCII"),{progressbarTheme:{preComplete:s.color("inverse"),complete:" ",postComplete:s.color("stopInverse"),preRemaining:s.color("brightBlack"),remaining:".",postRemaining:s.color("reset")}});o.addTheme("brailleSpinner",{preProgressbar:"⸨",postProgressbar:"⸩",progressbarTheme:{complete:"░",remaining:"⠂"},activityIndicatorTheme:"⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏",preSubsection:">"});o.addTheme("colorBrailleSpinner",o.getTheme("brailleSpinner"),{progressbarTheme:{preComplete:s.color("inverse"),complete:" ",postComplete:s.color("stopInverse"),preRemaining:s.color("brightBlack"),remaining:"░",postRemaining:s.color("reset")}});o.setDefault({},"ASCII");o.setDefault({hasColor:true},"colorASCII");o.setDefault({platform:"darwin",hasUnicode:true},"brailleSpinner");o.setDefault({platform:"darwin",hasUnicode:true,hasColor:true},"colorBrailleSpinner")},8413:(e,t,r)=>{"use strict";var s=r(6529);var a=r(5591);e.exports=wideTruncate;function wideTruncate(e,t){if(s(e)===0)return e;if(t<=0)return"";if(s(e)<=t)return e;var r=a(e);var o=e.length+r.length;var u=e.slice(0,t+o);while(s(u)>t){u=u.slice(0,-1)}return u}},7625:(e,t,r)=>{t.alphasort=alphasort;t.alphasorti=alphasorti;t.setopts=setopts;t.ownProp=ownProp;t.makeAbs=makeAbs;t.finish=finish;t.mark=mark;t.isIgnored=isIgnored;t.childrenIgnored=childrenIgnored;function ownProp(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var s=r(5622);var a=r(3973);var o=r(8714);var u=a.Minimatch;function alphasorti(e,t){return e.toLowerCase().localeCompare(t.toLowerCase())}function alphasort(e,t){return e.localeCompare(t)}function setupIgnores(e,t){e.ignore=t.ignore||[];if(!Array.isArray(e.ignore))e.ignore=[e.ignore];if(e.ignore.length){e.ignore=e.ignore.map(ignoreMap)}}function ignoreMap(e){var t=null;if(e.slice(-3)==="/**"){var r=e.replace(/(\/\*\*)+$/,"");t=new u(r,{dot:true})}return{matcher:new u(e,{dot:true}),gmatcher:t}}function setopts(e,t,r){if(!r)r={};if(r.matchBase&&-1===t.indexOf("/")){if(r.noglobstar){throw new Error("base matching requires globstar")}t="**/"+t}e.silent=!!r.silent;e.pattern=t;e.strict=r.strict!==false;e.realpath=!!r.realpath;e.realpathCache=r.realpathCache||Object.create(null);e.follow=!!r.follow;e.dot=!!r.dot;e.mark=!!r.mark;e.nodir=!!r.nodir;if(e.nodir)e.mark=true;e.sync=!!r.sync;e.nounique=!!r.nounique;e.nonull=!!r.nonull;e.nosort=!!r.nosort;e.nocase=!!r.nocase;e.stat=!!r.stat;e.noprocess=!!r.noprocess;e.absolute=!!r.absolute;e.maxLength=r.maxLength||Infinity;e.cache=r.cache||Object.create(null);e.statCache=r.statCache||Object.create(null);e.symlinks=r.symlinks||Object.create(null);setupIgnores(e,r);e.changedCwd=false;var a=process.cwd();if(!ownProp(r,"cwd"))e.cwd=a;else{e.cwd=s.resolve(r.cwd);e.changedCwd=e.cwd!==a}e.root=r.root||s.resolve(e.cwd,"/");e.root=s.resolve(e.root);if(process.platform==="win32")e.root=e.root.replace(/\\/g,"/");e.cwdAbs=o(e.cwd)?e.cwd:makeAbs(e,e.cwd);if(process.platform==="win32")e.cwdAbs=e.cwdAbs.replace(/\\/g,"/");e.nomount=!!r.nomount;r.nonegate=true;r.nocomment=true;e.minimatch=new u(t,r);e.options=e.minimatch.options}function finish(e){var t=e.nounique;var r=t?[]:Object.create(null);for(var s=0,a=e.matches.length;s<a;s++){var o=e.matches[s];if(!o||Object.keys(o).length===0){if(e.nonull){var u=e.minimatch.globSet[s];if(t)r.push(u);else r[u]=true}}else{var c=Object.keys(o);if(t)r.push.apply(r,c);else c.forEach((function(e){r[e]=true}))}}if(!t)r=Object.keys(r);if(!e.nosort)r=r.sort(e.nocase?alphasorti:alphasort);if(e.mark){for(var s=0;s<r.length;s++){r[s]=e._mark(r[s])}if(e.nodir){r=r.filter((function(t){var r=!/\/$/.test(t);var s=e.cache[t]||e.cache[makeAbs(e,t)];if(r&&s)r=s!=="DIR"&&!Array.isArray(s);return r}))}}if(e.ignore.length)r=r.filter((function(t){return!isIgnored(e,t)}));e.found=r}function mark(e,t){var r=makeAbs(e,t);var s=e.cache[r];var a=t;if(s){var o=s==="DIR"||Array.isArray(s);var u=t.slice(-1)==="/";if(o&&!u)a+="/";else if(!o&&u)a=a.slice(0,-1);if(a!==t){var c=makeAbs(e,a);e.statCache[c]=e.statCache[r];e.cache[c]=e.cache[r]}}return a}function makeAbs(e,t){var r=t;if(t.charAt(0)==="/"){r=s.join(e.root,t)}else if(o(t)||t===""){r=t}else if(e.changedCwd){r=s.resolve(e.cwd,t)}else{r=s.resolve(t)}if(process.platform==="win32")r=r.replace(/\\/g,"/");return r}function isIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some((function(e){return e.matcher.match(t)||!!(e.gmatcher&&e.gmatcher.match(t))}))}function childrenIgnored(e,t){if(!e.ignore.length)return false;return e.ignore.some((function(e){return!!(e.gmatcher&&e.gmatcher.match(t))}))}},1957:(e,t,r)=>{e.exports=glob;var s=r(5747);var a=r(6863);var o=r(3973);var u=o.Minimatch;var c=r(4124);var h=r(8614).EventEmitter;var p=r(5622);var d=r(2357);var v=r(8714);var m=r(9010);var g=r(7625);var y=g.alphasort;var _=g.alphasorti;var E=g.setopts;var x=g.ownProp;var w=r(2492);var D=r(1669);var C=g.childrenIgnored;var A=g.isIgnored;var S=r(1223);function glob(e,t,r){if(typeof t==="function")r=t,t={};if(!t)t={};if(t.sync){if(r)throw new TypeError("callback provided to sync glob");return m(e,t)}return new Glob(e,t,r)}glob.sync=m;var k=glob.GlobSync=m.GlobSync;glob.glob=glob;function extend(e,t){if(t===null||typeof t!=="object"){return e}var r=Object.keys(t);var s=r.length;while(s--){e[r[s]]=t[r[s]]}return e}glob.hasMagic=function(e,t){var r=extend({},t);r.noprocess=true;var s=new Glob(e,r);var a=s.minimatch.set;if(!e)return false;if(a.length>1)return true;for(var o=0;o<a[0].length;o++){if(typeof a[0][o]!=="string")return true}return false};glob.Glob=Glob;c(Glob,h);function Glob(e,t,r){if(typeof t==="function"){r=t;t=null}if(t&&t.sync){if(r)throw new TypeError("callback provided to sync glob");return new k(e,t)}if(!(this instanceof Glob))return new Glob(e,t,r);E(this,e,t);this._didRealPath=false;var s=this.minimatch.set.length;this.matches=new Array(s);if(typeof r==="function"){r=S(r);this.on("error",r);this.on("end",(function(e){r(null,e)}))}var a=this;this._processing=0;this._emitQueue=[];this._processQueue=[];this.paused=false;if(this.noprocess)return this;if(s===0)return done();var o=true;for(var u=0;u<s;u++){this._process(this.minimatch.set[u],u,false,done)}o=false;function done(){--a._processing;if(a._processing<=0){if(o){process.nextTick((function(){a._finish()}))}else{a._finish()}}}}Glob.prototype._finish=function(){d(this instanceof Glob);if(this.aborted)return;if(this.realpath&&!this._didRealpath)return this._realpath();g.finish(this);this.emit("end",this.found)};Glob.prototype._realpath=function(){if(this._didRealpath)return;this._didRealpath=true;var e=this.matches.length;if(e===0)return this._finish();var t=this;for(var r=0;r<this.matches.length;r++)this._realpathSet(r,next);function next(){if(--e===0)t._finish()}};Glob.prototype._realpathSet=function(e,t){var r=this.matches[e];if(!r)return t();var s=Object.keys(r);var o=this;var u=s.length;if(u===0)return t();var c=this.matches[e]=Object.create(null);s.forEach((function(r,s){r=o._makeAbs(r);a.realpath(r,o.realpathCache,(function(s,a){if(!s)c[a]=true;else if(s.syscall==="stat")c[r]=true;else o.emit("error",s);if(--u===0){o.matches[e]=c;t()}}))}))};Glob.prototype._mark=function(e){return g.mark(this,e)};Glob.prototype._makeAbs=function(e){return g.makeAbs(this,e)};Glob.prototype.abort=function(){this.aborted=true;this.emit("abort")};Glob.prototype.pause=function(){if(!this.paused){this.paused=true;this.emit("pause")}};Glob.prototype.resume=function(){if(this.paused){this.emit("resume");this.paused=false;if(this._emitQueue.length){var e=this._emitQueue.slice(0);this._emitQueue.length=0;for(var t=0;t<e.length;t++){var r=e[t];this._emitMatch(r[0],r[1])}}if(this._processQueue.length){var s=this._processQueue.slice(0);this._processQueue.length=0;for(var t=0;t<s.length;t++){var a=s[t];this._processing--;this._process(a[0],a[1],a[2],a[3])}}}};Glob.prototype._process=function(e,t,r,s){d(this instanceof Glob);d(typeof s==="function");if(this.aborted)return;this._processing++;if(this.paused){this._processQueue.push([e,t,r,s]);return}var a=0;while(typeof e[a]==="string"){a++}var u;switch(a){case e.length:this._processSimple(e.join("/"),t,s);return;case 0:u=null;break;default:u=e.slice(0,a).join("/");break}var c=e.slice(a);var h;if(u===null)h=".";else if(v(u)||v(e.join("/"))){if(!u||!v(u))u="/"+u;h=u}else h=u;var p=this._makeAbs(h);if(C(this,h))return s();var m=c[0]===o.GLOBSTAR;if(m)this._processGlobStar(u,h,p,c,t,r,s);else this._processReaddir(u,h,p,c,t,r,s)};Glob.prototype._processReaddir=function(e,t,r,s,a,o,u){var c=this;this._readdir(r,o,(function(h,p){return c._processReaddir2(e,t,r,s,a,o,p,u)}))};Glob.prototype._processReaddir2=function(e,t,r,s,a,o,u,c){if(!u)return c();var h=s[0];var d=!!this.minimatch.negate;var v=h._glob;var m=this.dot||v.charAt(0)===".";var g=[];for(var y=0;y<u.length;y++){var _=u[y];if(_.charAt(0)!=="."||m){var E;if(d&&!e){E=!_.match(h)}else{E=_.match(h)}if(E)g.push(_)}}var x=g.length;if(x===0)return c();if(s.length===1&&!this.mark&&!this.stat){if(!this.matches[a])this.matches[a]=Object.create(null);for(var y=0;y<x;y++){var _=g[y];if(e){if(e!=="/")_=e+"/"+_;else _=e+_}if(_.charAt(0)==="/"&&!this.nomount){_=p.join(this.root,_)}this._emitMatch(a,_)}return c()}s.shift();for(var y=0;y<x;y++){var _=g[y];var w;if(e){if(e!=="/")_=e+"/"+_;else _=e+_}this._process([_].concat(s),a,o,c)}c()};Glob.prototype._emitMatch=function(e,t){if(this.aborted)return;if(A(this,t))return;if(this.paused){this._emitQueue.push([e,t]);return}var r=v(t)?t:this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute)t=r;if(this.matches[e][t])return;if(this.nodir){var s=this.cache[r];if(s==="DIR"||Array.isArray(s))return}this.matches[e][t]=true;var a=this.statCache[r];if(a)this.emit("stat",t,a);this.emit("match",t)};Glob.prototype._readdirInGlobStar=function(e,t){if(this.aborted)return;if(this.follow)return this._readdir(e,false,t);var r="lstat\0"+e;var a=this;var o=w(r,lstatcb_);if(o)s.lstat(e,o);function lstatcb_(r,s){if(r&&r.code==="ENOENT")return t();var o=s&&s.isSymbolicLink();a.symlinks[e]=o;if(!o&&s&&!s.isDirectory()){a.cache[e]="FILE";t()}else a._readdir(e,false,t)}};Glob.prototype._readdir=function(e,t,r){if(this.aborted)return;r=w("readdir\0"+e+"\0"+t,r);if(!r)return;if(t&&!x(this.symlinks,e))return this._readdirInGlobStar(e,r);if(x(this.cache,e)){var a=this.cache[e];if(!a||a==="FILE")return r();if(Array.isArray(a))return r(null,a)}var o=this;s.readdir(e,readdirCb(this,e,r))};function readdirCb(e,t,r){return function(s,a){if(s)e._readdirError(t,s,r);else e._readdirEntries(t,a,r)}}Glob.prototype._readdirEntries=function(e,t,r){if(this.aborted)return;if(!this.mark&&!this.stat){for(var s=0;s<t.length;s++){var a=t[s];if(e==="/")a=e+a;else a=e+"/"+a;this.cache[a]=true}}this.cache[e]=t;return r(null,t)};Glob.prototype._readdirError=function(e,t,r){if(this.aborted)return;switch(t.code){case"ENOTSUP":case"ENOTDIR":var s=this._makeAbs(e);this.cache[s]="FILE";if(s===this.cwdAbs){var a=new Error(t.code+" invalid cwd "+this.cwd);a.path=this.cwd;a.code=t.code;this.emit("error",a);this.abort()}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict){this.emit("error",t);this.abort()}if(!this.silent)console.error("glob error",t);break}return r()};Glob.prototype._processGlobStar=function(e,t,r,s,a,o,u){var c=this;this._readdir(r,o,(function(h,p){c._processGlobStar2(e,t,r,s,a,o,p,u)}))};Glob.prototype._processGlobStar2=function(e,t,r,s,a,o,u,c){if(!u)return c();var h=s.slice(1);var p=e?[e]:[];var d=p.concat(h);this._process(d,a,false,c);var v=this.symlinks[r];var m=u.length;if(v&&o)return c();for(var g=0;g<m;g++){var y=u[g];if(y.charAt(0)==="."&&!this.dot)continue;var _=p.concat(u[g],h);this._process(_,a,true,c);var E=p.concat(u[g],s);this._process(E,a,true,c)}c()};Glob.prototype._processSimple=function(e,t,r){var s=this;this._stat(e,(function(a,o){s._processSimple2(e,t,a,o,r)}))};Glob.prototype._processSimple2=function(e,t,r,s,a){if(!this.matches[t])this.matches[t]=Object.create(null);if(!s)return a();if(e&&v(e)&&!this.nomount){var o=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=p.join(this.root,e)}else{e=p.resolve(this.root,e);if(o)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e);a()};Glob.prototype._stat=function(e,t){var r=this._makeAbs(e);var a=e.slice(-1)==="/";if(e.length>this.maxLength)return t();if(!this.stat&&x(this.cache,r)){var o=this.cache[r];if(Array.isArray(o))o="DIR";if(!a||o==="DIR")return t(null,o);if(a&&o==="FILE")return t()}var u;var c=this.statCache[r];if(c!==undefined){if(c===false)return t(null,c);else{var h=c.isDirectory()?"DIR":"FILE";if(a&&h==="FILE")return t();else return t(null,h,c)}}var p=this;var d=w("stat\0"+r,lstatcb_);if(d)s.lstat(r,d);function lstatcb_(a,o){if(o&&o.isSymbolicLink()){return s.stat(r,(function(s,a){if(s)p._stat2(e,r,null,o,t);else p._stat2(e,r,s,a,t)}))}else{p._stat2(e,r,a,o,t)}}};Glob.prototype._stat2=function(e,t,r,s,a){if(r&&(r.code==="ENOENT"||r.code==="ENOTDIR")){this.statCache[t]=false;return a()}var o=e.slice(-1)==="/";this.statCache[t]=s;if(t.slice(-1)==="/"&&s&&!s.isDirectory())return a(null,false,s);var u=true;if(s)u=s.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||u;if(o&&u==="FILE")return a();return a(null,u,s)}},9010:(e,t,r)=>{e.exports=globSync;globSync.GlobSync=GlobSync;var s=r(5747);var a=r(6863);var o=r(3973);var u=o.Minimatch;var c=r(1957).Glob;var h=r(1669);var p=r(5622);var d=r(2357);var v=r(8714);var m=r(7625);var g=m.alphasort;var y=m.alphasorti;var _=m.setopts;var E=m.ownProp;var x=m.childrenIgnored;var w=m.isIgnored;function globSync(e,t){if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");return new GlobSync(e,t).found}function GlobSync(e,t){if(!e)throw new Error("must provide pattern");if(typeof t==="function"||arguments.length===3)throw new TypeError("callback provided to sync glob\n"+"See: https://github.com/isaacs/node-glob/issues/167");if(!(this instanceof GlobSync))return new GlobSync(e,t);_(this,e,t);if(this.noprocess)return this;var r=this.minimatch.set.length;this.matches=new Array(r);for(var s=0;s<r;s++){this._process(this.minimatch.set[s],s,false)}this._finish()}GlobSync.prototype._finish=function(){d(this instanceof GlobSync);if(this.realpath){var e=this;this.matches.forEach((function(t,r){var s=e.matches[r]=Object.create(null);for(var o in t){try{o=e._makeAbs(o);var u=a.realpathSync(o,e.realpathCache);s[u]=true}catch(t){if(t.syscall==="stat")s[e._makeAbs(o)]=true;else throw t}}}))}m.finish(this)};GlobSync.prototype._process=function(e,t,r){d(this instanceof GlobSync);var s=0;while(typeof e[s]==="string"){s++}var a;switch(s){case e.length:this._processSimple(e.join("/"),t);return;case 0:a=null;break;default:a=e.slice(0,s).join("/");break}var u=e.slice(s);var c;if(a===null)c=".";else if(v(a)||v(e.join("/"))){if(!a||!v(a))a="/"+a;c=a}else c=a;var h=this._makeAbs(c);if(x(this,c))return;var p=u[0]===o.GLOBSTAR;if(p)this._processGlobStar(a,c,h,u,t,r);else this._processReaddir(a,c,h,u,t,r)};GlobSync.prototype._processReaddir=function(e,t,r,s,a,o){var u=this._readdir(r,o);if(!u)return;var c=s[0];var h=!!this.minimatch.negate;var d=c._glob;var v=this.dot||d.charAt(0)===".";var m=[];for(var g=0;g<u.length;g++){var y=u[g];if(y.charAt(0)!=="."||v){var _;if(h&&!e){_=!y.match(c)}else{_=y.match(c)}if(_)m.push(y)}}var E=m.length;if(E===0)return;if(s.length===1&&!this.mark&&!this.stat){if(!this.matches[a])this.matches[a]=Object.create(null);for(var g=0;g<E;g++){var y=m[g];if(e){if(e.slice(-1)!=="/")y=e+"/"+y;else y=e+y}if(y.charAt(0)==="/"&&!this.nomount){y=p.join(this.root,y)}this._emitMatch(a,y)}return}s.shift();for(var g=0;g<E;g++){var y=m[g];var x;if(e)x=[e,y];else x=[y];this._process(x.concat(s),a,o)}};GlobSync.prototype._emitMatch=function(e,t){if(w(this,t))return;var r=this._makeAbs(t);if(this.mark)t=this._mark(t);if(this.absolute){t=r}if(this.matches[e][t])return;if(this.nodir){var s=this.cache[r];if(s==="DIR"||Array.isArray(s))return}this.matches[e][t]=true;if(this.stat)this._stat(t)};GlobSync.prototype._readdirInGlobStar=function(e){if(this.follow)return this._readdir(e,false);var t;var r;var a;try{r=s.lstatSync(e)}catch(e){if(e.code==="ENOENT"){return null}}var o=r&&r.isSymbolicLink();this.symlinks[e]=o;if(!o&&r&&!r.isDirectory())this.cache[e]="FILE";else t=this._readdir(e,false);return t};GlobSync.prototype._readdir=function(e,t){var r;if(t&&!E(this.symlinks,e))return this._readdirInGlobStar(e);if(E(this.cache,e)){var a=this.cache[e];if(!a||a==="FILE")return null;if(Array.isArray(a))return a}try{return this._readdirEntries(e,s.readdirSync(e))}catch(t){this._readdirError(e,t);return null}};GlobSync.prototype._readdirEntries=function(e,t){if(!this.mark&&!this.stat){for(var r=0;r<t.length;r++){var s=t[r];if(e==="/")s=e+s;else s=e+"/"+s;this.cache[s]=true}}this.cache[e]=t;return t};GlobSync.prototype._readdirError=function(e,t){switch(t.code){case"ENOTSUP":case"ENOTDIR":var r=this._makeAbs(e);this.cache[r]="FILE";if(r===this.cwdAbs){var s=new Error(t.code+" invalid cwd "+this.cwd);s.path=this.cwd;s.code=t.code;throw s}break;case"ENOENT":case"ELOOP":case"ENAMETOOLONG":case"UNKNOWN":this.cache[this._makeAbs(e)]=false;break;default:this.cache[this._makeAbs(e)]=false;if(this.strict)throw t;if(!this.silent)console.error("glob error",t);break}};GlobSync.prototype._processGlobStar=function(e,t,r,s,a,o){var u=this._readdir(r,o);if(!u)return;var c=s.slice(1);var h=e?[e]:[];var p=h.concat(c);this._process(p,a,false);var d=u.length;var v=this.symlinks[r];if(v&&o)return;for(var m=0;m<d;m++){var g=u[m];if(g.charAt(0)==="."&&!this.dot)continue;var y=h.concat(u[m],c);this._process(y,a,true);var _=h.concat(u[m],s);this._process(_,a,true)}};GlobSync.prototype._processSimple=function(e,t){var r=this._stat(e);if(!this.matches[t])this.matches[t]=Object.create(null);if(!r)return;if(e&&v(e)&&!this.nomount){var s=/[\/\\]$/.test(e);if(e.charAt(0)==="/"){e=p.join(this.root,e)}else{e=p.resolve(this.root,e);if(s)e+="/"}}if(process.platform==="win32")e=e.replace(/\\/g,"/");this._emitMatch(t,e)};GlobSync.prototype._stat=function(e){var t=this._makeAbs(e);var r=e.slice(-1)==="/";if(e.length>this.maxLength)return false;if(!this.stat&&E(this.cache,t)){var a=this.cache[t];if(Array.isArray(a))a="DIR";if(!r||a==="DIR")return a;if(r&&a==="FILE")return false}var o;var u=this.statCache[t];if(!u){var c;try{c=s.lstatSync(t)}catch(e){if(e&&(e.code==="ENOENT"||e.code==="ENOTDIR")){this.statCache[t]=false;return false}}if(c&&c.isSymbolicLink()){try{u=s.statSync(t)}catch(e){u=c}}else{u=c}}this.statCache[t]=u;var a=true;if(u)a=u.isDirectory()?"DIR":"FILE";this.cache[t]=this.cache[t]||a;if(r&&a==="FILE")return false;return a};GlobSync.prototype._mark=function(e){return m.mark(this,e)};GlobSync.prototype._makeAbs=function(e){return m.makeAbs(this,e)}},7356:e=>{"use strict";e.exports=clone;function clone(e){if(e===null||typeof e!=="object")return e;if(e instanceof Object)var t={__proto__:e.__proto__};else var t=Object.create(null);Object.getOwnPropertyNames(e).forEach((function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(e,r))}));return t}},7758:(e,t,r)=>{var s=r(5747);var a=r(263);var o=r(3086);var u=r(7356);var c=[];var h=r(1669);function noop(){}var p=noop;if(h.debuglog)p=h.debuglog("gfs4");else if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||""))p=function(){var e=h.format.apply(h,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: ");console.error(e)};if(/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")){process.on("exit",(function(){p(c);r(2357).equal(c.length,0)}))}e.exports=patch(u(s));if(process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!s.__patched){e.exports=patch(s);s.__patched=true}e.exports.close=function(e){return function(t,r){return e.call(s,t,(function(e){if(!e)retry();if(typeof r==="function")r.apply(this,arguments)}))}}(s.close);e.exports.closeSync=function(e){return function(t){var r=e.apply(s,arguments);retry();return r}}(s.closeSync);if(!/\bgraceful-fs\b/.test(s.closeSync.toString())){s.closeSync=e.exports.closeSync;s.close=e.exports.close}function patch(e){a(e);e.gracefulify=patch;e.FileReadStream=ReadStream;e.FileWriteStream=WriteStream;e.createReadStream=createReadStream;e.createWriteStream=createWriteStream;var t=e.readFile;e.readFile=readFile;function readFile(e,r,s){if(typeof r==="function")s=r,r=null;return go$readFile(e,r,s);function go$readFile(e,r,s){return t(e,r,(function(t){if(t&&(t.code==="EMFILE"||t.code==="ENFILE"))enqueue([go$readFile,[e,r,s]]);else{if(typeof s==="function")s.apply(this,arguments);retry()}}))}}var r=e.writeFile;e.writeFile=writeFile;function writeFile(e,t,s,a){if(typeof s==="function")a=s,s=null;return go$writeFile(e,t,s,a);function go$writeFile(e,t,s,a){return r(e,t,s,(function(r){if(r&&(r.code==="EMFILE"||r.code==="ENFILE"))enqueue([go$writeFile,[e,t,s,a]]);else{if(typeof a==="function")a.apply(this,arguments);retry()}}))}}var s=e.appendFile;if(s)e.appendFile=appendFile;function appendFile(e,t,r,a){if(typeof r==="function")a=r,r=null;return go$appendFile(e,t,r,a);function go$appendFile(e,t,r,a){return s(e,t,r,(function(s){if(s&&(s.code==="EMFILE"||s.code==="ENFILE"))enqueue([go$appendFile,[e,t,r,a]]);else{if(typeof a==="function")a.apply(this,arguments);retry()}}))}}var u=e.readdir;e.readdir=readdir;function readdir(e,t,r){var s=[e];if(typeof t!=="function"){s.push(t)}else{r=t}s.push(go$readdir$cb);return go$readdir(s);function go$readdir$cb(e,t){if(t&&t.sort)t.sort();if(e&&(e.code==="EMFILE"||e.code==="ENFILE"))enqueue([go$readdir,[s]]);else{if(typeof r==="function")r.apply(this,arguments);retry()}}}function go$readdir(t){return u.apply(e,t)}if(process.version.substr(0,4)==="v0.8"){var c=o(e);ReadStream=c.ReadStream;WriteStream=c.WriteStream}var h=e.ReadStream;if(h){ReadStream.prototype=Object.create(h.prototype);ReadStream.prototype.open=ReadStream$open}var p=e.WriteStream;if(p){WriteStream.prototype=Object.create(p.prototype);WriteStream.prototype.open=WriteStream$open}e.ReadStream=ReadStream;e.WriteStream=WriteStream;function ReadStream(e,t){if(this instanceof ReadStream)return h.apply(this,arguments),this;else return ReadStream.apply(Object.create(ReadStream.prototype),arguments)}function ReadStream$open(){var e=this;open(e.path,e.flags,e.mode,(function(t,r){if(t){if(e.autoClose)e.destroy();e.emit("error",t)}else{e.fd=r;e.emit("open",r);e.read()}}))}function WriteStream(e,t){if(this instanceof WriteStream)return p.apply(this,arguments),this;else return WriteStream.apply(Object.create(WriteStream.prototype),arguments)}function WriteStream$open(){var e=this;open(e.path,e.flags,e.mode,(function(t,r){if(t){e.destroy();e.emit("error",t)}else{e.fd=r;e.emit("open",r)}}))}function createReadStream(e,t){return new ReadStream(e,t)}function createWriteStream(e,t){return new WriteStream(e,t)}var d=e.open;e.open=open;function open(e,t,r,s){if(typeof r==="function")s=r,r=null;return go$open(e,t,r,s);function go$open(e,t,r,s){return d(e,t,r,(function(a,o){if(a&&(a.code==="EMFILE"||a.code==="ENFILE"))enqueue([go$open,[e,t,r,s]]);else{if(typeof s==="function")s.apply(this,arguments);retry()}}))}}return e}function enqueue(e){p("ENQUEUE",e[0].name,e[1]);c.push(e)}function retry(){var e=c.shift();if(e){p("RETRY",e[0].name,e[1]);e[0].apply(null,e[1])}}},3086:(e,t,r)=>{var s=r(2413).Stream;e.exports=legacy;function legacy(e){return{ReadStream:ReadStream,WriteStream:WriteStream};function ReadStream(t,r){if(!(this instanceof ReadStream))return new ReadStream(t,r);s.call(this);var a=this;this.path=t;this.fd=null;this.readable=true;this.paused=false;this.flags="r";this.mode=438;this.bufferSize=64*1024;r=r||{};var o=Object.keys(r);for(var u=0,c=o.length;u<c;u++){var h=o[u];this[h]=r[h]}if(this.encoding)this.setEncoding(this.encoding);if(this.start!==undefined){if("number"!==typeof this.start){throw TypeError("start must be a Number")}if(this.end===undefined){this.end=Infinity}else if("number"!==typeof this.end){throw TypeError("end must be a Number")}if(this.start>this.end){throw new Error("start must be <= end")}this.pos=this.start}if(this.fd!==null){process.nextTick((function(){a._read()}));return}e.open(this.path,this.flags,this.mode,(function(e,t){if(e){a.emit("error",e);a.readable=false;return}a.fd=t;a.emit("open",t);a._read()}))}function WriteStream(t,r){if(!(this instanceof WriteStream))return new WriteStream(t,r);s.call(this);this.path=t;this.fd=null;this.writable=true;this.flags="w";this.encoding="binary";this.mode=438;this.bytesWritten=0;r=r||{};var a=Object.keys(r);for(var o=0,u=a.length;o<u;o++){var c=a[o];this[c]=r[c]}if(this.start!==undefined){if("number"!==typeof this.start){throw TypeError("start must be a Number")}if(this.start<0){throw new Error("start must be >= zero")}this.pos=this.start}this.busy=false;this._queue=[];if(this.fd===null){this._open=e.open;this._queue.push([this._open,this.path,this.flags,this.mode,undefined]);this.flush()}}}},263:(e,t,r)=>{var s=r(7619);var a=process.cwd;var o=null;var u=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){if(!o)o=a.call(process);return o};try{process.cwd()}catch(e){}var c=process.chdir;process.chdir=function(e){o=null;c.call(process,e)};e.exports=patch;function patch(e){if(s.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)){patchLchmod(e)}if(!e.lutimes){patchLutimes(e)}e.chown=chownFix(e.chown);e.fchown=chownFix(e.fchown);e.lchown=chownFix(e.lchown);e.chmod=chmodFix(e.chmod);e.fchmod=chmodFix(e.fchmod);e.lchmod=chmodFix(e.lchmod);e.chownSync=chownFixSync(e.chownSync);e.fchownSync=chownFixSync(e.fchownSync);e.lchownSync=chownFixSync(e.lchownSync);e.chmodSync=chmodFixSync(e.chmodSync);e.fchmodSync=chmodFixSync(e.fchmodSync);e.lchmodSync=chmodFixSync(e.lchmodSync);e.stat=statFix(e.stat);e.fstat=statFix(e.fstat);e.lstat=statFix(e.lstat);e.statSync=statFixSync(e.statSync);e.fstatSync=statFixSync(e.fstatSync);e.lstatSync=statFixSync(e.lstatSync);if(!e.lchmod){e.lchmod=function(e,t,r){if(r)process.nextTick(r)};e.lchmodSync=function(){}}if(!e.lchown){e.lchown=function(e,t,r,s){if(s)process.nextTick(s)};e.lchownSync=function(){}}if(u==="win32"){e.rename=function(t){return function(r,s,a){var o=Date.now();var u=0;t(r,s,(function CB(c){if(c&&(c.code==="EACCES"||c.code==="EPERM")&&Date.now()-o<6e4){setTimeout((function(){e.stat(s,(function(e,o){if(e&&e.code==="ENOENT")t(r,s,CB);else a(c)}))}),u);if(u<100)u+=10;return}if(a)a(c)}))}}(e.rename)}e.read=function(t){return function(r,s,a,o,u,c){var h;if(c&&typeof c==="function"){var p=0;h=function(d,v,m){if(d&&d.code==="EAGAIN"&&p<10){p++;return t.call(e,r,s,a,o,u,h)}c.apply(this,arguments)}}return t.call(e,r,s,a,o,u,h)}}(e.read);e.readSync=function(t){return function(r,s,a,o,u){var c=0;while(true){try{return t.call(e,r,s,a,o,u)}catch(e){if(e.code==="EAGAIN"&&c<10){c++;continue}throw e}}}}(e.readSync);function patchLchmod(e){e.lchmod=function(t,r,a){e.open(t,s.O_WRONLY|s.O_SYMLINK,r,(function(t,s){if(t){if(a)a(t);return}e.fchmod(s,r,(function(t){e.close(s,(function(e){if(a)a(t||e)}))}))}))};e.lchmodSync=function(t,r){var a=e.openSync(t,s.O_WRONLY|s.O_SYMLINK,r);var o=true;var u;try{u=e.fchmodSync(a,r);o=false}finally{if(o){try{e.closeSync(a)}catch(e){}}else{e.closeSync(a)}}return u}}function patchLutimes(e){if(s.hasOwnProperty("O_SYMLINK")){e.lutimes=function(t,r,a,o){e.open(t,s.O_SYMLINK,(function(t,s){if(t){if(o)o(t);return}e.futimes(s,r,a,(function(t){e.close(s,(function(e){if(o)o(t||e)}))}))}))};e.lutimesSync=function(t,r,a){var o=e.openSync(t,s.O_SYMLINK);var u;var c=true;try{u=e.futimesSync(o,r,a);c=false}finally{if(c){try{e.closeSync(o)}catch(e){}}else{e.closeSync(o)}}return u}}else{e.lutimes=function(e,t,r,s){if(s)process.nextTick(s)};e.lutimesSync=function(){}}}function chmodFix(t){if(!t)return t;return function(r,s,a){return t.call(e,r,s,(function(e){if(chownErOk(e))e=null;if(a)a.apply(this,arguments)}))}}function chmodFixSync(t){if(!t)return t;return function(r,s){try{return t.call(e,r,s)}catch(e){if(!chownErOk(e))throw e}}}function chownFix(t){if(!t)return t;return function(r,s,a,o){return t.call(e,r,s,a,(function(e){if(chownErOk(e))e=null;if(o)o.apply(this,arguments)}))}}function chownFixSync(t){if(!t)return t;return function(r,s,a){try{return t.call(e,r,s,a)}catch(e){if(!chownErOk(e))throw e}}}function statFix(t){if(!t)return t;return function(r,s){return t.call(e,r,(function(e,t){if(!t)return s.apply(this,arguments);if(t.uid<0)t.uid+=4294967296;if(t.gid<0)t.gid+=4294967296;if(s)s.apply(this,arguments)}))}}function statFixSync(t){if(!t)return t;return function(r){var s=t.call(e,r);if(s.uid<0)s.uid+=4294967296;if(s.gid<0)s.gid+=4294967296;return s}}function chownErOk(e){if(!e)return true;if(e.code==="ENOSYS")return true;var t=!process.getuid||process.getuid()!==0;if(t){if(e.code==="EINVAL"||e.code==="EPERM")return true}return false}}},5885:(e,t,r)=>{"use strict";var s=r(2087);var a=e.exports=function(){if(s.type()=="Windows_NT"){return false}var e=/UTF-?8$/i;var t=process.env.LC_ALL||process.env.LC_CTYPE||process.env.LANG;return e.test(t)}},2492:(e,t,r)=>{var s=r(2940);var a=Object.create(null);var o=r(1223);e.exports=s(inflight);function inflight(e,t){if(a[e]){a[e].push(t);return null}else{a[e]=[t];return makeres(e)}}function makeres(e){return o((function RES(){var t=a[e];var r=t.length;var s=slice(arguments);try{for(var o=0;o<r;o++){t[o].apply(null,s)}}finally{if(t.length>r){t.splice(0,r);process.nextTick((function(){RES.apply(null,s)}))}else{delete a[e]}}}))}function slice(e){var t=e.length;var r=[];for(var s=0;s<t;s++)r[s]=e[s];return r}},4124:(e,t,r)=>{try{var s=r(1669);if(typeof s.inherits!=="function")throw"";e.exports=s.inherits}catch(t){e.exports=r(8544)}},8544:e=>{if(typeof Object.create==="function"){e.exports=function inherits(e,t){if(t){e.super_=t;e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:false,writable:true,configurable:true}})}}}else{e.exports=function inherits(e,t){if(t){e.super_=t;var TempCtor=function(){};TempCtor.prototype=t.prototype;e.prototype=new TempCtor;e.prototype.constructor=e}}}},4882:e=>{"use strict";e.exports=e=>{if(Number.isNaN(e)){return false}if(e>=4352&&(e<=4447||e===9001||e===9002||11904<=e&&e<=12871&&e!==12351||12880<=e&&e<=19903||19968<=e&&e<=42182||43360<=e&&e<=43388||44032<=e&&e<=55203||63744<=e&&e<=64255||65040<=e&&e<=65049||65072<=e&&e<=65131||65281<=e&&e<=65376||65504<=e&&e<=65510||110592<=e&&e<=110593||127488<=e&&e<=127569||131072<=e&&e<=262141)){return true}return false}},893:e=>{var t={}.toString;e.exports=Array.isArray||function(e){return t.call(e)=="[object Array]"}},6904:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});var s=r(7583);var a=_interopRequireDefault(s);var o=r(749);var u=_interopRequireDefault(o);function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}t.default={parse:a.default,stringify:u.default};e.exports=t["default"]},7583:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});var s=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=parse;var a=r(7393);var o=_interopRequireWildcard(a);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}var u=void 0;var c=void 0;var h=void 0;var p=void 0;var d=void 0;var v=void 0;var m=void 0;var g=void 0;var y=void 0;function parse(e,t){u=String(e);c="start";h=[];p=0;d=1;v=0;m=undefined;g=undefined;y=undefined;do{m=lex();A[c]()}while(m.type!=="eof");if(typeof t==="function"){return internalize({"":y},"",t)}return y}function internalize(e,t,r){var a=e[t];if(a!=null&&(typeof a==="undefined"?"undefined":s(a))==="object"){for(var o in a){var u=internalize(a,o,r);if(u===undefined){delete a[o]}else{a[o]=u}}}return r.call(e,t,a)}var _=void 0;var E=void 0;var x=void 0;var w=void 0;var D=void 0;function lex(){_="default";E="";x=false;w=1;for(;;){D=peek();var e=C[_]();if(e){return e}}}function peek(){if(u[p]){return String.fromCodePoint(u.codePointAt(p))}}function read(){var e=peek();if(e==="\n"){d++;v=0}else if(e){v+=e.length}else{v++}if(e){p+=e.length}return e}var C={default:function _default(){switch(D){case"\t":case"\v":case"\f":case" ":case" ":case"\ufeff":case"\n":case"\r":case"\u2028":case"\u2029":read();return;case"/":read();_="comment";return;case undefined:read();return newToken("eof")}if(o.isSpaceSeparator(D)){read();return}return C[c]()},comment:function comment(){switch(D){case"*":read();_="multiLineComment";return;case"/":read();_="singleLineComment";return}throw invalidChar(read())},multiLineComment:function multiLineComment(){switch(D){case"*":read();_="multiLineCommentAsterisk";return;case undefined:throw invalidChar(read())}read()},multiLineCommentAsterisk:function multiLineCommentAsterisk(){switch(D){case"*":read();return;case"/":read();_="default";return;case undefined:throw invalidChar(read())}read();_="multiLineComment"},singleLineComment:function singleLineComment(){switch(D){case"\n":case"\r":case"\u2028":case"\u2029":read();_="default";return;case undefined:read();return newToken("eof")}read()},value:function value(){switch(D){case"{":case"[":return newToken("punctuator",read());case"n":read();literal("ull");return newToken("null",null);case"t":read();literal("rue");return newToken("boolean",true);case"f":read();literal("alse");return newToken("boolean",false);case"-":case"+":if(read()==="-"){w=-1}_="sign";return;case".":E=read();_="decimalPointLeading";return;case"0":E=read();_="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":E=read();_="decimalInteger";return;case"I":read();literal("nfinity");return newToken("numeric",Infinity);case"N":read();literal("aN");return newToken("numeric",NaN);case'"':case"'":x=read()==='"';E="";_="string";return}throw invalidChar(read())},identifierNameStartEscape:function identifierNameStartEscape(){if(D!=="u"){throw invalidChar(read())}read();var e=unicodeEscape();switch(e){case"$":case"_":break;default:if(!o.isIdStartChar(e)){throw invalidIdentifier()}break}E+=e;_="identifierName"},identifierName:function identifierName(){switch(D){case"$":case"_":case"‌":case"‍":E+=read();return;case"\\":read();_="identifierNameEscape";return}if(o.isIdContinueChar(D)){E+=read();return}return newToken("identifier",E)},identifierNameEscape:function identifierNameEscape(){if(D!=="u"){throw invalidChar(read())}read();var e=unicodeEscape();switch(e){case"$":case"_":case"‌":case"‍":break;default:if(!o.isIdContinueChar(e)){throw invalidIdentifier()}break}E+=e;_="identifierName"},sign:function sign(){switch(D){case".":E=read();_="decimalPointLeading";return;case"0":E=read();_="zero";return;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":E=read();_="decimalInteger";return;case"I":read();literal("nfinity");return newToken("numeric",w*Infinity);case"N":read();literal("aN");return newToken("numeric",NaN)}throw invalidChar(read())},zero:function zero(){switch(D){case".":E+=read();_="decimalPoint";return;case"e":case"E":E+=read();_="decimalExponent";return;case"x":case"X":E+=read();_="hexadecimal";return}return newToken("numeric",w*0)},decimalInteger:function decimalInteger(){switch(D){case".":E+=read();_="decimalPoint";return;case"e":case"E":E+=read();_="decimalExponent";return}if(o.isDigit(D)){E+=read();return}return newToken("numeric",w*Number(E))},decimalPointLeading:function decimalPointLeading(){if(o.isDigit(D)){E+=read();_="decimalFraction";return}throw invalidChar(read())},decimalPoint:function decimalPoint(){switch(D){case"e":case"E":E+=read();_="decimalExponent";return}if(o.isDigit(D)){E+=read();_="decimalFraction";return}return newToken("numeric",w*Number(E))},decimalFraction:function decimalFraction(){switch(D){case"e":case"E":E+=read();_="decimalExponent";return}if(o.isDigit(D)){E+=read();return}return newToken("numeric",w*Number(E))},decimalExponent:function decimalExponent(){switch(D){case"+":case"-":E+=read();_="decimalExponentSign";return}if(o.isDigit(D)){E+=read();_="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentSign:function decimalExponentSign(){if(o.isDigit(D)){E+=read();_="decimalExponentInteger";return}throw invalidChar(read())},decimalExponentInteger:function decimalExponentInteger(){if(o.isDigit(D)){E+=read();return}return newToken("numeric",w*Number(E))},hexadecimal:function hexadecimal(){if(o.isHexDigit(D)){E+=read();_="hexadecimalInteger";return}throw invalidChar(read())},hexadecimalInteger:function hexadecimalInteger(){if(o.isHexDigit(D)){E+=read();return}return newToken("numeric",w*Number(E))},string:function string(){switch(D){case"\\":read();E+=escape();return;case'"':if(x){read();return newToken("string",E)}E+=read();return;case"'":if(!x){read();return newToken("string",E)}E+=read();return;case"\n":case"\r":throw invalidChar(read());case"\u2028":case"\u2029":separatorChar(D);break;case undefined:throw invalidChar(read())}E+=read()},start:function start(){switch(D){case"{":case"[":return newToken("punctuator",read())}_="value"},beforePropertyName:function beforePropertyName(){switch(D){case"$":case"_":E=read();_="identifierName";return;case"\\":read();_="identifierNameStartEscape";return;case"}":return newToken("punctuator",read());case'"':case"'":x=read()==='"';_="string";return}if(o.isIdStartChar(D)){E+=read();_="identifierName";return}throw invalidChar(read())},afterPropertyName:function afterPropertyName(){if(D===":"){return newToken("punctuator",read())}throw invalidChar(read())},beforePropertyValue:function beforePropertyValue(){_="value"},afterPropertyValue:function afterPropertyValue(){switch(D){case",":case"}":return newToken("punctuator",read())}throw invalidChar(read())},beforeArrayValue:function beforeArrayValue(){if(D==="]"){return newToken("punctuator",read())}_="value"},afterArrayValue:function afterArrayValue(){switch(D){case",":case"]":return newToken("punctuator",read())}throw invalidChar(read())},end:function end(){throw invalidChar(read())}};function newToken(e,t){return{type:e,value:t,line:d,column:v}}function literal(e){var t=true;var r=false;var s=undefined;try{for(var a=e[Symbol.iterator](),o;!(t=(o=a.next()).done);t=true){var u=o.value;var c=peek();if(c!==u){throw invalidChar(read())}read()}}catch(e){r=true;s=e}finally{try{if(!t&&a.return){a.return()}}finally{if(r){throw s}}}}function escape(){var e=peek();switch(e){case"b":read();return"\b";case"f":read();return"\f";case"n":read();return"\n";case"r":read();return"\r";case"t":read();return"\t";case"v":read();return"\v";case"0":read();if(o.isDigit(peek())){throw invalidChar(read())}return"\0";case"x":read();return hexEscape();case"u":read();return unicodeEscape();case"\n":case"\u2028":case"\u2029":read();return"";case"\r":read();if(peek()==="\n"){read()}return"";case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":throw invalidChar(read());case undefined:throw invalidChar(read())}return read()}function hexEscape(){var e="";var t=peek();if(!o.isHexDigit(t)){throw invalidChar(read())}e+=read();t=peek();if(!o.isHexDigit(t)){throw invalidChar(read())}e+=read();return String.fromCodePoint(parseInt(e,16))}function unicodeEscape(){var e="";var t=4;while(t-- >0){var r=peek();if(!o.isHexDigit(r)){throw invalidChar(read())}e+=read()}return String.fromCodePoint(parseInt(e,16))}var A={start:function start(){if(m.type==="eof"){throw invalidEOF()}push()},beforePropertyName:function beforePropertyName(){switch(m.type){case"identifier":case"string":g=m.value;c="afterPropertyName";return;case"punctuator":pop();return;case"eof":throw invalidEOF()}},afterPropertyName:function afterPropertyName(){if(m.type==="eof"){throw invalidEOF()}c="beforePropertyValue"},beforePropertyValue:function beforePropertyValue(){if(m.type==="eof"){throw invalidEOF()}push()},beforeArrayValue:function beforeArrayValue(){if(m.type==="eof"){throw invalidEOF()}if(m.type==="punctuator"&&m.value==="]"){pop();return}push()},afterPropertyValue:function afterPropertyValue(){if(m.type==="eof"){throw invalidEOF()}switch(m.value){case",":c="beforePropertyName";return;case"}":pop()}},afterArrayValue:function afterArrayValue(){if(m.type==="eof"){throw invalidEOF()}switch(m.value){case",":c="beforeArrayValue";return;case"]":pop()}},end:function end(){}};function push(){var e=void 0;switch(m.type){case"punctuator":switch(m.value){case"{":e={};break;case"[":e=[];break}break;case"null":case"boolean":case"numeric":case"string":e=m.value;break}if(y===undefined){y=e}else{var t=h[h.length-1];if(Array.isArray(t)){t.push(e)}else{t[g]=e}}if(e!==null&&(typeof e==="undefined"?"undefined":s(e))==="object"){h.push(e);if(Array.isArray(e)){c="beforeArrayValue"}else{c="beforePropertyName"}}else{var r=h[h.length-1];if(r==null){c="end"}else if(Array.isArray(r)){c="afterArrayValue"}else{c="afterPropertyValue"}}}function pop(){h.pop();var e=h[h.length-1];if(e==null){c="end"}else if(Array.isArray(e)){c="afterArrayValue"}else{c="afterPropertyValue"}}function invalidChar(e){if(e===undefined){return syntaxError("JSON5: invalid end of input at "+d+":"+v)}return syntaxError("JSON5: invalid character '"+formatChar(e)+"' at "+d+":"+v)}function invalidEOF(){return syntaxError("JSON5: invalid end of input at "+d+":"+v)}function invalidIdentifier(){v-=5;return syntaxError("JSON5: invalid identifier character at "+d+":"+v)}function separatorChar(e){console.warn("JSON5: '"+e+"' is not valid ECMAScript; consider escaping")}function formatChar(e){var t={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};if(t[e]){return t[e]}if(e<" "){var r=e.charCodeAt(0).toString(16);return"\\x"+("00"+r).substring(r.length)}return e}function syntaxError(e){var t=new SyntaxError(e);t.lineNumber=d;t.columnNumber=v;return t}e.exports=t["default"]},749:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});var s=typeof Symbol==="function"&&typeof Symbol.iterator==="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol==="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.default=stringify;var a=r(7393);var o=_interopRequireWildcard(a);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function stringify(e,t,r){var a=[];var u="";var c=void 0;var h=void 0;var p="";var d=void 0;if(t!=null&&(typeof t==="undefined"?"undefined":s(t))==="object"&&!Array.isArray(t)){r=t.space;d=t.quote;t=t.replacer}if(typeof t==="function"){h=t}else if(Array.isArray(t)){c=[];var v=true;var m=false;var g=undefined;try{for(var y=t[Symbol.iterator](),_;!(v=(_=y.next()).done);v=true){var E=_.value;var x=void 0;if(typeof E==="string"){x=E}else if(typeof E==="number"||E instanceof String||E instanceof Number){x=String(E)}if(x!==undefined&&c.indexOf(x)<0){c.push(x)}}}catch(e){m=true;g=e}finally{try{if(!v&&y.return){y.return()}}finally{if(m){throw g}}}}if(r instanceof Number){r=Number(r)}else if(r instanceof String){r=String(r)}if(typeof r==="number"){if(r>0){r=Math.min(10,Math.floor(r));p="          ".substr(0,r)}}else if(typeof r==="string"){p=r.substr(0,10)}return serializeProperty("",{"":e});function serializeProperty(e,t){var r=t[e];if(r!=null){if(typeof r.toJSON5==="function"){r=r.toJSON5(e)}else if(typeof r.toJSON==="function"){r=r.toJSON(e)}}if(h){r=h.call(t,e,r)}if(r instanceof Number){r=Number(r)}else if(r instanceof String){r=String(r)}else if(r instanceof Boolean){r=r.valueOf()}switch(r){case null:return"null";case true:return"true";case false:return"false"}if(typeof r==="string"){return quoteString(r,false)}if(typeof r==="number"){return String(r)}if((typeof r==="undefined"?"undefined":s(r))==="object"){return Array.isArray(r)?serializeArray(r):serializeObject(r)}return undefined}function quoteString(e){var t={"'":.1,'"':.2};var r={"'":"\\'",'"':'\\"',"\\":"\\\\","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","\t":"\\t","\v":"\\v","\0":"\\0","\u2028":"\\u2028","\u2029":"\\u2029"};var s="";var a=true;var o=false;var u=undefined;try{for(var c=e[Symbol.iterator](),h;!(a=(h=c.next()).done);a=true){var p=h.value;switch(p){case"'":case'"':t[p]++;s+=p;continue}if(r[p]){s+=r[p];continue}if(p<" "){var v=p.charCodeAt(0).toString(16);s+="\\x"+("00"+v).substring(v.length);continue}s+=p}}catch(e){o=true;u=e}finally{try{if(!a&&c.return){c.return()}}finally{if(o){throw u}}}var m=d||Object.keys(t).reduce((function(e,r){return t[e]<t[r]?e:r}));s=s.replace(new RegExp(m,"g"),r[m]);return m+s+m}function serializeObject(e){if(a.indexOf(e)>=0){throw TypeError("Converting circular structure to JSON5")}a.push(e);var t=u;u=u+p;var r=c||Object.keys(e);var s=[];var o=true;var h=false;var d=undefined;try{for(var v=r[Symbol.iterator](),m;!(o=(m=v.next()).done);o=true){var g=m.value;var y=serializeProperty(g,e);if(y!==undefined){var _=serializeKey(g)+":";if(p!==""){_+=" "}_+=y;s.push(_)}}}catch(e){h=true;d=e}finally{try{if(!o&&v.return){v.return()}}finally{if(h){throw d}}}var E=void 0;if(s.length===0){E="{}"}else{var x=void 0;if(p===""){x=s.join(",");E="{"+x+"}"}else{var w=",\n"+u;x=s.join(w);E="{\n"+u+x+",\n"+t+"}"}}a.pop();u=t;return E}function serializeKey(e){if(e.length===0){return quoteString(e,true)}var t=String.fromCodePoint(e.codePointAt(0));if(!o.isIdStartChar(t)){return quoteString(e,true)}for(var r=t.length;r<e.length;r++){if(!o.isIdContinueChar(String.fromCodePoint(e.codePointAt(r)))){return quoteString(e,true)}}return e}function serializeArray(e){if(a.indexOf(e)>=0){throw TypeError("Converting circular structure to JSON5")}a.push(e);var t=u;u=u+p;var r=[];for(var s=0;s<e.length;s++){var o=serializeProperty(String(s),e);r.push(o!==undefined?o:"null")}var c=void 0;if(r.length===0){c="[]"}else{if(p===""){var h=r.join(",");c="["+h+"]"}else{var d=",\n"+u;var v=r.join(d);c="[\n"+u+v+",\n"+t+"]"}}a.pop();u=t;return c}}e.exports=t["default"]},1927:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});var r=t.Space_Separator=/[\u1680\u2000-\u200A\u202F\u205F\u3000]/;var s=t.ID_Start=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u08B6-\u08BD\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/;var a=t.ID_Continue=/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08B6-\u08BD\u08D4-\u08E1\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C80-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D54-\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19D9\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1C80-\u1C88\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFB-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u2E2F\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099\u309A\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AE\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C5\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE3E\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC00-\uDC4A\uDC50-\uDC59\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC36\uDC38-\uDC40\uDC50-\uDC59\uDC72-\uDC8F\uDC92-\uDCA7\uDCA9-\uDCB6]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD81C-\uD820\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F\uDFE0]|\uD821[\uDC00-\uDFEC]|\uD822[\uDC00-\uDEF2]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD838[\uDC00-\uDC06\uDC08-\uDC18\uDC1B-\uDC21\uDC23\uDC24\uDC26-\uDC2A]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6\uDD00-\uDD4A\uDD50-\uDD59]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/},7393:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});t.isSpaceSeparator=isSpaceSeparator;t.isIdStartChar=isIdStartChar;t.isIdContinueChar=isIdContinueChar;t.isDigit=isDigit;t.isHexDigit=isHexDigit;var s=r(1927);var a=_interopRequireWildcard(s);function _interopRequireWildcard(e){if(e&&e.__esModule){return e}else{var t={};if(e!=null){for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t[r]=e[r]}}t.default=e;return t}}function isSpaceSeparator(e){return a.Space_Separator.test(e)}function isIdStartChar(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="$"||e==="_"||a.ID_Start.test(e)}function isIdContinueChar(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="$"||e==="_"||e==="‌"||e==="‍"||a.ID_Continue.test(e)}function isDigit(e){return/[0-9]/.test(e)}function isHexDigit(e){return/[0-9A-Fa-f]/.test(e)}},2821:e=>{"use strict";function getCurrentRequest(e){if(e.currentRequest){return e.currentRequest}const t=e.loaders.slice(e.loaderIndex).map((e=>e.request)).concat([e.resource]);return t.join("!")}e.exports=getCurrentRequest},3567:(e,t,r)=>{"use strict";const s={26:"abcdefghijklmnopqrstuvwxyz",32:"123456789abcdefghjkmnpqrstuvwxyz",36:"**********abcdefghijklmnopqrstuvwxyz",49:"abcdefghijkmnopqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ",52:"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",58:"**********************************************************",62:"**********abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",64:"**********abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"};function encodeBufferToBase(e,t){const a=s[t];if(!a){throw new Error("Unknown encoding base"+t)}const o=e.length;const u=r(8738);u.RM=u.DP=0;let c=new u(0);for(let t=o-1;t>=0;t--){c=c.times(256).plus(e[t])}let h="";while(c.gt(0)){h=a[c.mod(t)]+h;c=c.div(t)}u.DP=20;u.RM=1;return h}function getHashDigest(e,t,s,a){t=t||"md5";a=a||9999;const o=r(6417).createHash(t);o.update(e);if(s==="base26"||s==="base32"||s==="base36"||s==="base49"||s==="base52"||s==="base58"||s==="base62"||s==="base64"){return encodeBufferToBase(o.digest(),s.substr(4)).substr(0,a)}else{return o.digest(s||"hex").substr(0,a)}}e.exports=getHashDigest},6445:(e,t,r)=>{"use strict";const s=r(5867);function getOptions(e){const t=e.query;if(typeof t==="string"&&t!==""){return s(e.query)}if(!t||typeof t!=="object"){return null}return t}e.exports=getOptions},8715:e=>{"use strict";function getRemainingRequest(e){if(e.remainingRequest){return e.remainingRequest}const t=e.loaders.slice(e.loaderIndex+1).map((e=>e.request)).concat([e.resource]);return t.join("!")}e.exports=getRemainingRequest},3432:(e,t,r)=>{"use strict";const s=r(6445);const a=r(5867);const o=r(4252);const u=r(8715);const c=r(2821);const h=r(507);const p=r(2685);const d=r(5784);const v=r(3567);const m=r(939);t.getOptions=s;t.parseQuery=a;t.stringifyRequest=o;t.getRemainingRequest=u;t.getCurrentRequest=c;t.isUrlRequest=h;t.urlToRequest=p;t.parseString=d;t.getHashDigest=v;t.interpolateName=m},939:(e,t,r)=>{"use strict";const s=r(5622);const a=r(3887);const o=r(3567);const u=/[\uD800-\uDFFF]./;const c=a.filter((e=>u.test(e)));const h={};function encodeStringToEmoji(e,t){if(h[e]){return h[e]}t=t||1;const r=[];do{if(!c.length){throw new Error("Ran out of emoji")}const e=Math.floor(Math.random()*c.length);r.push(c[e]);c.splice(e,1)}while(--t>0);const s=r.join("");h[e]=s;return s}function interpolateName(e,t,r){let a;if(typeof t==="function"){a=t(e.resourcePath)}else{a=t||"[hash].[ext]"}const u=r.context;const c=r.content;const h=r.regExp;let p="bin";let d="file";let v="";let m="";if(e.resourcePath){const t=s.parse(e.resourcePath);let r=e.resourcePath;if(t.ext){p=t.ext.substr(1)}if(t.dir){d=t.name;r=t.dir+s.sep}if(typeof u!=="undefined"){v=s.relative(u,r+"_").replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1");v=v.substr(0,v.length-1)}else{v=r.replace(/\\/g,"/").replace(/\.\.(\/)?/g,"_$1")}if(v.length===1){v=""}else if(v.length>1){m=s.basename(v)}}let g=a;if(c){g=g.replace(/\[(?:([^:\]]+):)?(?:hash|contenthash)(?::([a-z]+\d*))?(?::(\d+))?\]/gi,((e,t,r,s)=>o(c,t,r,parseInt(s,10)))).replace(/\[emoji(?::(\d+))?\]/gi,((e,t)=>encodeStringToEmoji(c,parseInt(t,10))))}g=g.replace(/\[ext\]/gi,(()=>p)).replace(/\[name\]/gi,(()=>d)).replace(/\[path\]/gi,(()=>v)).replace(/\[folder\]/gi,(()=>m));if(h&&e.resourcePath){const t=e.resourcePath.match(new RegExp(h));t&&t.forEach(((e,t)=>{g=g.replace(new RegExp("\\["+t+"\\]","ig"),e)}))}if(typeof e.options==="object"&&typeof e.options.customInterpolateName==="function"){g=e.options.customInterpolateName.call(e,g,t,r)}return g}e.exports=interpolateName},507:(e,t,r)=>{"use strict";const s=r(5622);function isUrlRequest(e,t){if(/^[a-z][a-z0-9+.-]*:/i.test(e)&&!s.win32.isAbsolute(e)){return false}if(/^\/\//.test(e)){return false}if(/^[{}[\]#*;,'§$%&(=?`´^°<>]/.test(e)){return false}if((t===undefined||t===false)&&/^\//.test(e)){return false}return true}e.exports=isUrlRequest},5867:(e,t,r)=>{"use strict";const s=r(6904);const a={null:null,true:true,false:false};function parseQuery(e){if(e.substr(0,1)!=="?"){throw new Error("A valid query string passed to parseQuery should begin with '?'")}e=e.substr(1);if(!e){return{}}if(e.substr(0,1)==="{"&&e.substr(-1)==="}"){return s.parse(e)}const t=e.split(/[,&]/g);const r={};t.forEach((e=>{const t=e.indexOf("=");if(t>=0){let s=e.substr(0,t);let o=decodeURIComponent(e.substr(t+1));if(a.hasOwnProperty(o)){o=a[o]}if(s.substr(-2)==="[]"){s=decodeURIComponent(s.substr(0,s.length-2));if(!Array.isArray(r[s])){r[s]=[]}r[s].push(o)}else{s=decodeURIComponent(s);r[s]=o}}else{if(e.substr(0,1)==="-"){r[decodeURIComponent(e.substr(1))]=false}else if(e.substr(0,1)==="+"){r[decodeURIComponent(e.substr(1))]=true}else{r[decodeURIComponent(e)]=true}}}));return r}e.exports=parseQuery},5784:e=>{"use strict";function parseString(e){try{if(e[0]==='"'){return JSON.parse(e)}if(e[0]==="'"&&e.substr(e.length-1)==="'"){return parseString(e.replace(/\\.|"/g,(e=>e==='"'?'\\"':e)).replace(/^'|'$/g,'"'))}return JSON.parse('"'+e+'"')}catch(t){return e}}e.exports=parseString},4252:(e,t,r)=>{"use strict";const s=r(5622);const a=/^\.\.?[/\\]/;function isAbsolutePath(e){return s.posix.isAbsolute(e)||s.win32.isAbsolute(e)}function isRelativePath(e){return a.test(e)}function stringifyRequest(e,t){const r=t.split("!");const a=e.context||e.options&&e.options.context;return JSON.stringify(r.map((e=>{const t=e.match(/^(.*?)(\?.*)/);const r=t?t[2]:"";let o=t?t[1]:e;if(isAbsolutePath(o)&&a){o=s.relative(a,o);if(isAbsolutePath(o)){return o+r}if(isRelativePath(o)===false){o="./"+o}}return o.replace(/\\/g,"/")+r})).join("!"))}e.exports=stringifyRequest},2685:e=>{"use strict";const t=/^[A-Z]:[/\\]|^\\\\/i;function urlToRequest(e,r){if(e===""){return""}const s=/^[^?]*~/;let a;if(t.test(e)){a=e}else if(r!==undefined&&r!==false&&/^\//.test(e)){switch(typeof r){case"string":if(s.test(r)){a=r.replace(/([^~/])$/,"$1/")+e.slice(1)}else{a=r+e}break;case"boolean":a=e;break;default:throw new Error("Unexpected parameters to loader-utils 'urlToRequest': url = "+e+", root = "+r+".")}}else if(/^\.\.?\//.test(e)){a=e}else{a="./"+e}if(s.test(a)){a=a.replace(s,"")}return a}e.exports=urlToRequest},5734:(e,t,r)=>{"use strict";var s=r(4957);var a=function Chunk(e,t,r){this.start=e;this.end=t;this.original=r;this.intro="";this.outro="";this.content=r;this.storeName=false;this.edited=false;Object.defineProperties(this,{previous:{writable:true,value:null},next:{writable:true,value:null}})};a.prototype.appendLeft=function appendLeft(e){this.outro+=e};a.prototype.appendRight=function appendRight(e){this.intro=this.intro+e};a.prototype.clone=function clone(){var e=new a(this.start,this.end,this.original);e.intro=this.intro;e.outro=this.outro;e.content=this.content;e.storeName=this.storeName;e.edited=this.edited;return e};a.prototype.contains=function contains(e){return this.start<e&&e<this.end};a.prototype.eachNext=function eachNext(e){var t=this;while(t){e(t);t=t.next}};a.prototype.eachPrevious=function eachPrevious(e){var t=this;while(t){e(t);t=t.previous}};a.prototype.edit=function edit(e,t,r){this.content=e;if(!r){this.intro="";this.outro=""}this.storeName=t;this.edited=true;return this};a.prototype.prependLeft=function prependLeft(e){this.outro=e+this.outro};a.prototype.prependRight=function prependRight(e){this.intro=e+this.intro};a.prototype.split=function split(e){var t=e-this.start;var r=this.original.slice(0,t);var s=this.original.slice(t);this.original=r;var o=new a(e,this.end,s);o.outro=this.outro;this.outro="";this.end=e;if(this.edited){o.edit("",false);this.content=""}else{this.content=r}o.next=this.next;if(o.next){o.next.previous=o}o.previous=this;this.next=o;return o};a.prototype.toString=function toString(){return this.intro+this.content+this.outro};a.prototype.trimEnd=function trimEnd(e){this.outro=this.outro.replace(e,"");if(this.outro.length){return true}var t=this.content.replace(e,"");if(t.length){if(t!==this.content){this.split(this.start+t.length).edit("",undefined,true)}return true}else{this.edit("",undefined,true);this.intro=this.intro.replace(e,"");if(this.intro.length){return true}}};a.prototype.trimStart=function trimStart(e){this.intro=this.intro.replace(e,"");if(this.intro.length){return true}var t=this.content.replace(e,"");if(t.length){if(t!==this.content){this.split(this.end-t.length);this.edit("",undefined,true)}return true}else{this.edit("",undefined,true);this.outro=this.outro.replace(e,"");if(this.outro.length){return true}}};var btoa=function(){throw new Error("Unsupported environment: `window.btoa` or `Buffer` should be supported.")};if(typeof window!=="undefined"&&typeof window.btoa==="function"){btoa=function(e){return window.btoa(unescape(encodeURIComponent(e)))}}else if(typeof Buffer==="function"){btoa=function(e){return Buffer.from(e,"utf-8").toString("base64")}}var o=function SourceMap(e){this.version=3;this.file=e.file;this.sources=e.sources;this.sourcesContent=e.sourcesContent;this.names=e.names;this.mappings=s.encode(e.mappings)};o.prototype.toString=function toString(){return JSON.stringify(this)};o.prototype.toUrl=function toUrl(){return"data:application/json;charset=utf-8;base64,"+btoa(this.toString())};function guessIndent(e){var t=e.split("\n");var r=t.filter((function(e){return/^\t+/.test(e)}));var s=t.filter((function(e){return/^ {2,}/.test(e)}));if(r.length===0&&s.length===0){return null}if(r.length>=s.length){return"\t"}var a=s.reduce((function(e,t){var r=/^ +/.exec(t)[0].length;return Math.min(r,e)}),Infinity);return new Array(a+1).join(" ")}function getRelativePath(e,t){var r=e.split(/[/\\]/);var s=t.split(/[/\\]/);r.pop();while(r[0]===s[0]){r.shift();s.shift()}if(r.length){var a=r.length;while(a--){r[a]=".."}}return r.concat(s).join("/")}var u=Object.prototype.toString;function isObject(e){return u.call(e)==="[object Object]"}function getLocator(e){var t=e.split("\n");var r=[];for(var s=0,a=0;s<t.length;s++){r.push(a);a+=t[s].length+1}return function locate(e){var t=0;var s=r.length;while(t<s){var a=t+s>>1;if(e<r[a]){s=a}else{t=a+1}}var o=t-1;var u=e-r[o];return{line:o,column:u}}}var c=function Mappings(e){this.hires=e;this.generatedCodeLine=0;this.generatedCodeColumn=0;this.raw=[];this.rawSegments=this.raw[this.generatedCodeLine]=[];this.pending=null};c.prototype.addEdit=function addEdit(e,t,r,s){if(t.length){var a=[this.generatedCodeColumn,e,r.line,r.column];if(s>=0){a.push(s)}this.rawSegments.push(a)}else if(this.pending){this.rawSegments.push(this.pending)}this.advance(t);this.pending=null};c.prototype.addUneditedChunk=function addUneditedChunk(e,t,r,s,a){var o=t.start;var u=true;while(o<t.end){if(this.hires||u||a[o]){this.rawSegments.push([this.generatedCodeColumn,e,s.line,s.column])}if(r[o]==="\n"){s.line+=1;s.column=0;this.generatedCodeLine+=1;this.raw[this.generatedCodeLine]=this.rawSegments=[];this.generatedCodeColumn=0}else{s.column+=1;this.generatedCodeColumn+=1}o+=1;u=false}this.pending=[this.generatedCodeColumn,e,s.line,s.column]};c.prototype.advance=function advance(e){if(!e){return}var t=e.split("\n");if(t.length>1){for(var r=0;r<t.length-1;r++){this.generatedCodeLine++;this.raw[this.generatedCodeLine]=this.rawSegments=[]}this.generatedCodeColumn=0}this.generatedCodeColumn+=t[t.length-1].length};var h="\n";var p={insertLeft:false,insertRight:false,storeName:false};var d=function MagicString(e,t){if(t===void 0)t={};var r=new a(0,e.length,e);Object.defineProperties(this,{original:{writable:true,value:e},outro:{writable:true,value:""},intro:{writable:true,value:""},firstChunk:{writable:true,value:r},lastChunk:{writable:true,value:r},lastSearchedChunk:{writable:true,value:r},byStart:{writable:true,value:{}},byEnd:{writable:true,value:{}},filename:{writable:true,value:t.filename},indentExclusionRanges:{writable:true,value:t.indentExclusionRanges},sourcemapLocations:{writable:true,value:{}},storedNames:{writable:true,value:{}},indentStr:{writable:true,value:guessIndent(e)}});this.byStart[0]=r;this.byEnd[e.length]=r};d.prototype.addSourcemapLocation=function addSourcemapLocation(e){this.sourcemapLocations[e]=true};d.prototype.append=function append(e){if(typeof e!=="string"){throw new TypeError("outro content must be a string")}this.outro+=e;return this};d.prototype.appendLeft=function appendLeft(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byEnd[e];if(r){r.appendLeft(t)}else{this.intro+=t}return this};d.prototype.appendRight=function appendRight(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byStart[e];if(r){r.appendRight(t)}else{this.outro+=t}return this};d.prototype.clone=function clone(){var e=new d(this.original,{filename:this.filename});var t=this.firstChunk;var r=e.firstChunk=e.lastSearchedChunk=t.clone();while(t){e.byStart[r.start]=r;e.byEnd[r.end]=r;var s=t.next;var a=s&&s.clone();if(a){r.next=a;a.previous=r;r=a}t=s}e.lastChunk=r;if(this.indentExclusionRanges){e.indentExclusionRanges=this.indentExclusionRanges.slice()}Object.keys(this.sourcemapLocations).forEach((function(t){e.sourcemapLocations[t]=true}));return e};d.prototype.generateDecodedMap=function generateDecodedMap(e){var t=this;e=e||{};var r=0;var s=Object.keys(this.storedNames);var a=new c(e.hires);var o=getLocator(this.original);if(this.intro){a.advance(this.intro)}this.firstChunk.eachNext((function(e){var u=o(e.start);if(e.intro.length){a.advance(e.intro)}if(e.edited){a.addEdit(r,e.content,u,e.storeName?s.indexOf(e.original):-1)}else{a.addUneditedChunk(r,e,t.original,u,t.sourcemapLocations)}if(e.outro.length){a.advance(e.outro)}}));return{file:e.file?e.file.split(/[/\\]/).pop():null,sources:[e.source?getRelativePath(e.file||"",e.source):null],sourcesContent:e.includeContent?[this.original]:[null],names:s,mappings:a.raw}};d.prototype.generateMap=function generateMap(e){return new o(this.generateDecodedMap(e))};d.prototype.getIndentString=function getIndentString(){return this.indentStr===null?"\t":this.indentStr};d.prototype.indent=function indent(e,t){var r=/^[^\r\n]/gm;if(isObject(e)){t=e;e=undefined}e=e!==undefined?e:this.indentStr||"\t";if(e===""){return this}t=t||{};var s={};if(t.exclude){var a=typeof t.exclude[0]==="number"?[t.exclude]:t.exclude;a.forEach((function(e){for(var t=e[0];t<e[1];t+=1){s[t]=true}}))}var o=t.indentStart!==false;var replacer=function(t){if(o){return""+e+t}o=true;return t};this.intro=this.intro.replace(r,replacer);var u=0;var c=this.firstChunk;while(c){var h=c.end;if(c.edited){if(!s[u]){c.content=c.content.replace(r,replacer);if(c.content.length){o=c.content[c.content.length-1]==="\n"}}}else{u=c.start;while(u<h){if(!s[u]){var p=this.original[u];if(p==="\n"){o=true}else if(p!=="\r"&&o){o=false;if(u===c.start){c.prependRight(e)}else{this._splitChunk(c,u);c=c.next;c.prependRight(e)}}}u+=1}}u=c.end;c=c.next}this.outro=this.outro.replace(r,replacer);return this};d.prototype.insert=function insert(){throw new Error("magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)")};d.prototype.insertLeft=function insertLeft(e,t){if(!p.insertLeft){console.warn("magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead");p.insertLeft=true}return this.appendLeft(e,t)};d.prototype.insertRight=function insertRight(e,t){if(!p.insertRight){console.warn("magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead");p.insertRight=true}return this.prependRight(e,t)};d.prototype.move=function move(e,t,r){if(r>=e&&r<=t){throw new Error("Cannot move a selection inside itself")}this._split(e);this._split(t);this._split(r);var s=this.byStart[e];var a=this.byEnd[t];var o=s.previous;var u=a.next;var c=this.byStart[r];if(!c&&a===this.lastChunk){return this}var h=c?c.previous:this.lastChunk;if(o){o.next=u}if(u){u.previous=o}if(h){h.next=s}if(c){c.previous=a}if(!s.previous){this.firstChunk=a.next}if(!a.next){this.lastChunk=s.previous;this.lastChunk.next=null}s.previous=h;a.next=c||null;if(!h){this.firstChunk=s}if(!c){this.lastChunk=a}return this};d.prototype.overwrite=function overwrite(e,t,r,s){if(typeof r!=="string"){throw new TypeError("replacement content must be a string")}while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}if(t>this.original.length){throw new Error("end is out of bounds")}if(e===t){throw new Error("Cannot overwrite a zero-length range – use appendLeft or prependRight instead")}this._split(e);this._split(t);if(s===true){if(!p.storeName){console.warn("The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string");p.storeName=true}s={storeName:true}}var o=s!==undefined?s.storeName:false;var u=s!==undefined?s.contentOnly:false;if(o){var c=this.original.slice(e,t);this.storedNames[c]=true}var h=this.byStart[e];var d=this.byEnd[t];if(h){if(t>h.end&&h.next!==this.byStart[h.end]){throw new Error("Cannot overwrite across a split point")}h.edit(r,o,u);if(h!==d){var v=h.next;while(v!==d){v.edit("",false);v=v.next}v.edit("",false)}}else{var m=new a(e,t,"").edit(r,o);d.next=m;m.previous=d}return this};d.prototype.prepend=function prepend(e){if(typeof e!=="string"){throw new TypeError("outro content must be a string")}this.intro=e+this.intro;return this};d.prototype.prependLeft=function prependLeft(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byEnd[e];if(r){r.prependLeft(t)}else{this.intro=t+this.intro}return this};d.prototype.prependRight=function prependRight(e,t){if(typeof t!=="string"){throw new TypeError("inserted content must be a string")}this._split(e);var r=this.byStart[e];if(r){r.prependRight(t)}else{this.outro=t+this.outro}return this};d.prototype.remove=function remove(e,t){while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}if(e===t){return this}if(e<0||t>this.original.length){throw new Error("Character is out of bounds")}if(e>t){throw new Error("end must be greater than start")}this._split(e);this._split(t);var r=this.byStart[e];while(r){r.intro="";r.outro="";r.edit("");r=t>r.end?this.byStart[r.end]:null}return this};d.prototype.lastChar=function lastChar(){if(this.outro.length){return this.outro[this.outro.length-1]}var e=this.lastChunk;do{if(e.outro.length){return e.outro[e.outro.length-1]}if(e.content.length){return e.content[e.content.length-1]}if(e.intro.length){return e.intro[e.intro.length-1]}}while(e=e.previous);if(this.intro.length){return this.intro[this.intro.length-1]}return""};d.prototype.lastLine=function lastLine(){var e=this.outro.lastIndexOf(h);if(e!==-1){return this.outro.substr(e+1)}var t=this.outro;var r=this.lastChunk;do{if(r.outro.length>0){e=r.outro.lastIndexOf(h);if(e!==-1){return r.outro.substr(e+1)+t}t=r.outro+t}if(r.content.length>0){e=r.content.lastIndexOf(h);if(e!==-1){return r.content.substr(e+1)+t}t=r.content+t}if(r.intro.length>0){e=r.intro.lastIndexOf(h);if(e!==-1){return r.intro.substr(e+1)+t}t=r.intro+t}}while(r=r.previous);e=this.intro.lastIndexOf(h);if(e!==-1){return this.intro.substr(e+1)+t}return this.intro+t};d.prototype.slice=function slice(e,t){if(e===void 0)e=0;if(t===void 0)t=this.original.length;while(e<0){e+=this.original.length}while(t<0){t+=this.original.length}var r="";var s=this.firstChunk;while(s&&(s.start>e||s.end<=e)){if(s.start<t&&s.end>=t){return r}s=s.next}if(s&&s.edited&&s.start!==e){throw new Error("Cannot use replaced character "+e+" as slice start anchor.")}var a=s;while(s){if(s.intro&&(a!==s||s.start===e)){r+=s.intro}var o=s.start<t&&s.end>=t;if(o&&s.edited&&s.end!==t){throw new Error("Cannot use replaced character "+t+" as slice end anchor.")}var u=a===s?e-s.start:0;var c=o?s.content.length+t-s.end:s.content.length;r+=s.content.slice(u,c);if(s.outro&&(!o||s.end===t)){r+=s.outro}if(o){break}s=s.next}return r};d.prototype.snip=function snip(e,t){var r=this.clone();r.remove(0,e);r.remove(t,r.original.length);return r};d.prototype._split=function _split(e){if(this.byStart[e]||this.byEnd[e]){return}var t=this.lastSearchedChunk;var r=e>t.end;while(t){if(t.contains(e)){return this._splitChunk(t,e)}t=r?this.byStart[t.end]:this.byEnd[t.start]}};d.prototype._splitChunk=function _splitChunk(e,t){if(e.edited&&e.content.length){var r=getLocator(this.original)(t);throw new Error("Cannot split a chunk that has already been edited ("+r.line+":"+r.column+' – "'+e.original+'")')}var s=e.split(t);this.byEnd[t]=e;this.byStart[t]=s;this.byEnd[s.end]=s;if(e===this.lastChunk){this.lastChunk=s}this.lastSearchedChunk=e;return true};d.prototype.toString=function toString(){var e=this.intro;var t=this.firstChunk;while(t){e+=t.toString();t=t.next}return e+this.outro};d.prototype.isEmpty=function isEmpty(){var e=this.firstChunk;do{if(e.intro.length&&e.intro.trim()||e.content.length&&e.content.trim()||e.outro.length&&e.outro.trim()){return false}}while(e=e.next);return true};d.prototype.length=function length(){var e=this.firstChunk;var length=0;do{length+=e.intro.length+e.content.length+e.outro.length}while(e=e.next);return length};d.prototype.trimLines=function trimLines(){return this.trim("[\\r\\n]")};d.prototype.trim=function trim(e){return this.trimStart(e).trimEnd(e)};d.prototype.trimEndAborted=function trimEndAborted(e){var t=new RegExp((e||"\\s")+"+$");this.outro=this.outro.replace(t,"");if(this.outro.length){return true}var r=this.lastChunk;do{var s=r.end;var a=r.trimEnd(t);if(r.end!==s){if(this.lastChunk===r){this.lastChunk=r.next}this.byEnd[r.end]=r;this.byStart[r.next.start]=r.next;this.byEnd[r.next.end]=r.next}if(a){return true}r=r.previous}while(r);return false};d.prototype.trimEnd=function trimEnd(e){this.trimEndAborted(e);return this};d.prototype.trimStartAborted=function trimStartAborted(e){var t=new RegExp("^"+(e||"\\s")+"+");this.intro=this.intro.replace(t,"");if(this.intro.length){return true}var r=this.firstChunk;do{var s=r.end;var a=r.trimStart(t);if(r.end!==s){if(r===this.lastChunk){this.lastChunk=r.next}this.byEnd[r.end]=r;this.byStart[r.next.start]=r.next;this.byEnd[r.next.end]=r.next}if(a){return true}r=r.next}while(r);return false};d.prototype.trimStart=function trimStart(e){this.trimStartAborted(e);return this};var v=Object.prototype.hasOwnProperty;var m=function Bundle(e){if(e===void 0)e={};this.intro=e.intro||"";this.separator=e.separator!==undefined?e.separator:"\n";this.sources=[];this.uniqueSources=[];this.uniqueSourceIndexByFilename={}};m.prototype.addSource=function addSource(e){if(e instanceof d){return this.addSource({content:e,filename:e.filename,separator:this.separator})}if(!isObject(e)||!e.content){throw new Error("bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`")}["filename","indentExclusionRanges","separator"].forEach((function(t){if(!v.call(e,t)){e[t]=e.content[t]}}));if(e.separator===undefined){e.separator=this.separator}if(e.filename){if(!v.call(this.uniqueSourceIndexByFilename,e.filename)){this.uniqueSourceIndexByFilename[e.filename]=this.uniqueSources.length;this.uniqueSources.push({filename:e.filename,content:e.content.original})}else{var t=this.uniqueSources[this.uniqueSourceIndexByFilename[e.filename]];if(e.content.original!==t.content){throw new Error("Illegal source: same filename ("+e.filename+"), different contents")}}}this.sources.push(e);return this};m.prototype.append=function append(e,t){this.addSource({content:new d(e),separator:t&&t.separator||""});return this};m.prototype.clone=function clone(){var e=new m({intro:this.intro,separator:this.separator});this.sources.forEach((function(t){e.addSource({filename:t.filename,content:t.content.clone(),separator:t.separator})}));return e};m.prototype.generateDecodedMap=function generateDecodedMap(e){var t=this;if(e===void 0)e={};var r=[];this.sources.forEach((function(e){Object.keys(e.content.storedNames).forEach((function(e){if(!~r.indexOf(e)){r.push(e)}}))}));var s=new c(e.hires);if(this.intro){s.advance(this.intro)}this.sources.forEach((function(e,a){if(a>0){s.advance(t.separator)}var o=e.filename?t.uniqueSourceIndexByFilename[e.filename]:-1;var u=e.content;var c=getLocator(u.original);if(u.intro){s.advance(u.intro)}u.firstChunk.eachNext((function(t){var a=c(t.start);if(t.intro.length){s.advance(t.intro)}if(e.filename){if(t.edited){s.addEdit(o,t.content,a,t.storeName?r.indexOf(t.original):-1)}else{s.addUneditedChunk(o,t,u.original,a,u.sourcemapLocations)}}else{s.advance(t.content)}if(t.outro.length){s.advance(t.outro)}}));if(u.outro){s.advance(u.outro)}}));return{file:e.file?e.file.split(/[/\\]/).pop():null,sources:this.uniqueSources.map((function(t){return e.file?getRelativePath(e.file,t.filename):t.filename})),sourcesContent:this.uniqueSources.map((function(t){return e.includeContent?t.content:null})),names:r,mappings:s.raw}};m.prototype.generateMap=function generateMap(e){return new o(this.generateDecodedMap(e))};m.prototype.getIndentString=function getIndentString(){var e={};this.sources.forEach((function(t){var r=t.content.indentStr;if(r===null){return}if(!e[r]){e[r]=0}e[r]+=1}));return Object.keys(e).sort((function(t,r){return e[t]-e[r]}))[0]||"\t"};m.prototype.indent=function indent(e){var t=this;if(!arguments.length){e=this.getIndentString()}if(e===""){return this}var r=!this.intro||this.intro.slice(-1)==="\n";this.sources.forEach((function(s,a){var o=s.separator!==undefined?s.separator:t.separator;var u=r||a>0&&/\r?\n$/.test(o);s.content.indent(e,{exclude:s.indentExclusionRanges,indentStart:u});r=s.content.lastChar()==="\n"}));if(this.intro){this.intro=e+this.intro.replace(/^[^\n]/gm,(function(t,r){return r>0?e+t:t}))}return this};m.prototype.prepend=function prepend(e){this.intro=e+this.intro;return this};m.prototype.toString=function toString(){var e=this;var t=this.sources.map((function(t,r){var s=t.separator!==undefined?t.separator:e.separator;var a=(r>0?s:"")+t.content.toString();return a})).join("");return this.intro+t};m.prototype.isEmpty=function isEmpty(){if(this.intro.length&&this.intro.trim()){return false}if(this.sources.some((function(e){return!e.content.isEmpty()}))){return false}return true};m.prototype.length=function length(){return this.sources.reduce((function(e,t){return e+t.content.length()}),this.intro.length)};m.prototype.trimLines=function trimLines(){return this.trim("[\\r\\n]")};m.prototype.trim=function trim(e){return this.trimStart(e).trimEnd(e)};m.prototype.trimStart=function trimStart(e){var t=new RegExp("^"+(e||"\\s")+"+");this.intro=this.intro.replace(t,"");if(!this.intro){var r;var s=0;do{r=this.sources[s++];if(!r){break}}while(!r.content.trimStartAborted(e))}return this};m.prototype.trimEnd=function trimEnd(e){var t=new RegExp((e||"\\s")+"+$");var r;var s=this.sources.length-1;do{r=this.sources[s--];if(!r){this.intro=this.intro.replace(t,"");break}}while(!r.content.trimEndAborted(e));return this};d.Bundle=m;d.default=d;e.exports=d},3973:(e,t,r)=>{e.exports=minimatch;minimatch.Minimatch=Minimatch;var s={sep:"/"};try{s=r(5622)}catch(e){}var a=minimatch.GLOBSTAR=Minimatch.GLOBSTAR={};var o=r(3717);var u={"!":{open:"(?:(?!(?:",close:"))[^/]*?)"},"?":{open:"(?:",close:")?"},"+":{open:"(?:",close:")+"},"*":{open:"(?:",close:")*"},"@":{open:"(?:",close:")"}};var c="[^/]";var h=c+"*?";var p="(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?";var d="(?:(?!(?:\\/|^)\\.).)*?";var v=charSet("().*{}+?[]^$\\!");function charSet(e){return e.split("").reduce((function(e,t){e[t]=true;return e}),{})}var m=/\/+/;minimatch.filter=filter;function filter(e,t){t=t||{};return function(r,s,a){return minimatch(r,e,t)}}function ext(e,t){e=e||{};t=t||{};var r={};Object.keys(t).forEach((function(e){r[e]=t[e]}));Object.keys(e).forEach((function(t){r[t]=e[t]}));return r}minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return minimatch;var t=minimatch;var r=function minimatch(r,s,a){return t.minimatch(r,s,ext(e,a))};r.Minimatch=function Minimatch(r,s){return new t.Minimatch(r,ext(e,s))};return r};Minimatch.defaults=function(e){if(!e||!Object.keys(e).length)return Minimatch;return minimatch.defaults(e).Minimatch};function minimatch(e,t,r){if(typeof t!=="string"){throw new TypeError("glob pattern string required")}if(!r)r={};if(!r.nocomment&&t.charAt(0)==="#"){return false}if(t.trim()==="")return e==="";return new Minimatch(t,r).match(e)}function Minimatch(e,t){if(!(this instanceof Minimatch)){return new Minimatch(e,t)}if(typeof e!=="string"){throw new TypeError("glob pattern string required")}if(!t)t={};e=e.trim();if(s.sep!=="/"){e=e.split(s.sep).join("/")}this.options=t;this.set=[];this.pattern=e;this.regexp=null;this.negate=false;this.comment=false;this.empty=false;this.make()}Minimatch.prototype.debug=function(){};Minimatch.prototype.make=make;function make(){if(this._made)return;var e=this.pattern;var t=this.options;if(!t.nocomment&&e.charAt(0)==="#"){this.comment=true;return}if(!e){this.empty=true;return}this.parseNegate();var r=this.globSet=this.braceExpand();if(t.debug)this.debug=console.error;this.debug(this.pattern,r);r=this.globParts=r.map((function(e){return e.split(m)}));this.debug(this.pattern,r);r=r.map((function(e,t,r){return e.map(this.parse,this)}),this);this.debug(this.pattern,r);r=r.filter((function(e){return e.indexOf(false)===-1}));this.debug(this.pattern,r);this.set=r}Minimatch.prototype.parseNegate=parseNegate;function parseNegate(){var e=this.pattern;var t=false;var r=this.options;var s=0;if(r.nonegate)return;for(var a=0,o=e.length;a<o&&e.charAt(a)==="!";a++){t=!t;s++}if(s)this.pattern=e.substr(s);this.negate=t}minimatch.braceExpand=function(e,t){return braceExpand(e,t)};Minimatch.prototype.braceExpand=braceExpand;function braceExpand(e,t){if(!t){if(this instanceof Minimatch){t=this.options}else{t={}}}e=typeof e==="undefined"?this.pattern:e;if(typeof e==="undefined"){throw new TypeError("undefined pattern")}if(t.nobrace||!e.match(/\{.*\}/)){return[e]}return o(e)}Minimatch.prototype.parse=parse;var g={};function parse(e,t){if(e.length>1024*64){throw new TypeError("pattern is too long")}var r=this.options;if(!r.noglobstar&&e==="**")return a;if(e==="")return"";var s="";var o=!!r.nocase;var p=false;var d=[];var m=[];var y;var _=false;var E=-1;var x=-1;var w=e.charAt(0)==="."?"":r.dot?"(?!(?:^|\\/)\\.{1,2}(?:$|\\/))":"(?!\\.)";var D=this;function clearStateChar(){if(y){switch(y){case"*":s+=h;o=true;break;case"?":s+=c;o=true;break;default:s+="\\"+y;break}D.debug("clearStateChar %j %j",y,s);y=false}}for(var C=0,A=e.length,S;C<A&&(S=e.charAt(C));C++){this.debug("%s\t%s %s %j",e,C,s,S);if(p&&v[S]){s+="\\"+S;p=false;continue}switch(S){case"/":return false;case"\\":clearStateChar();p=true;continue;case"?":case"*":case"+":case"@":case"!":this.debug("%s\t%s %s %j <-- stateChar",e,C,s,S);if(_){this.debug("  in class");if(S==="!"&&C===x+1)S="^";s+=S;continue}D.debug("call clearStateChar %j",y);clearStateChar();y=S;if(r.noext)clearStateChar();continue;case"(":if(_){s+="(";continue}if(!y){s+="\\(";continue}d.push({type:y,start:C-1,reStart:s.length,open:u[y].open,close:u[y].close});s+=y==="!"?"(?:(?!(?:":"(?:";this.debug("plType %j %j",y,s);y=false;continue;case")":if(_||!d.length){s+="\\)";continue}clearStateChar();o=true;var k=d.pop();s+=k.close;if(k.type==="!"){m.push(k)}k.reEnd=s.length;continue;case"|":if(_||!d.length||p){s+="\\|";p=false;continue}clearStateChar();s+="|";continue;case"[":clearStateChar();if(_){s+="\\"+S;continue}_=true;x=C;E=s.length;s+=S;continue;case"]":if(C===x+1||!_){s+="\\"+S;p=false;continue}if(_){var F=e.substring(x+1,C);try{RegExp("["+F+"]")}catch(e){var R=this.parse(F,g);s=s.substr(0,E)+"\\["+R[0]+"\\]";o=o||R[1];_=false;continue}}o=true;_=false;s+=S;continue;default:clearStateChar();if(p){p=false}else if(v[S]&&!(S==="^"&&_)){s+="\\"}s+=S}}if(_){F=e.substr(x+1);R=this.parse(F,g);s=s.substr(0,E)+"\\["+R[0];o=o||R[1]}for(k=d.pop();k;k=d.pop()){var T=s.slice(k.reStart+k.open.length);this.debug("setting tail",s,k);T=T.replace(/((?:\\{2}){0,64})(\\?)\|/g,(function(e,t,r){if(!r){r="\\"}return t+t+r+"|"}));this.debug("tail=%j\n   %s",T,T,k,s);var I=k.type==="*"?h:k.type==="?"?c:"\\"+k.type;o=true;s=s.slice(0,k.reStart)+I+"\\("+T}clearStateChar();if(p){s+="\\\\"}var B=false;switch(s.charAt(0)){case".":case"[":case"(":B=true}for(var N=m.length-1;N>-1;N--){var O=m[N];var P=s.slice(0,O.reStart);var L=s.slice(O.reStart,O.reEnd-8);var j=s.slice(O.reEnd-8,O.reEnd);var M=s.slice(O.reEnd);j+=M;var V=P.split("(").length-1;var q=M;for(C=0;C<V;C++){q=q.replace(/\)[+*?]?/,"")}M=q;var U="";if(M===""&&t!==g){U="$"}var $=P+L+M+U+j;s=$}if(s!==""&&o){s="(?=.)"+s}if(B){s=w+s}if(t===g){return[s,o]}if(!o){return globUnescape(e)}var H=r.nocase?"i":"";try{var G=new RegExp("^"+s+"$",H)}catch(e){return new RegExp("$.")}G._glob=e;G._src=s;return G}minimatch.makeRe=function(e,t){return new Minimatch(e,t||{}).makeRe()};Minimatch.prototype.makeRe=makeRe;function makeRe(){if(this.regexp||this.regexp===false)return this.regexp;var e=this.set;if(!e.length){this.regexp=false;return this.regexp}var t=this.options;var r=t.noglobstar?h:t.dot?p:d;var s=t.nocase?"i":"";var o=e.map((function(e){return e.map((function(e){return e===a?r:typeof e==="string"?regExpEscape(e):e._src})).join("\\/")})).join("|");o="^(?:"+o+")$";if(this.negate)o="^(?!"+o+").*$";try{this.regexp=new RegExp(o,s)}catch(e){this.regexp=false}return this.regexp}minimatch.match=function(e,t,r){r=r||{};var s=new Minimatch(t,r);e=e.filter((function(e){return s.match(e)}));if(s.options.nonull&&!e.length){e.push(t)}return e};Minimatch.prototype.match=match;function match(e,t){this.debug("match",e,this.pattern);if(this.comment)return false;if(this.empty)return e==="";if(e==="/"&&t)return true;var r=this.options;if(s.sep!=="/"){e=e.split(s.sep).join("/")}e=e.split(m);this.debug(this.pattern,"split",e);var a=this.set;this.debug(this.pattern,"set",a);var o;var u;for(u=e.length-1;u>=0;u--){o=e[u];if(o)break}for(u=0;u<a.length;u++){var c=a[u];var h=e;if(r.matchBase&&c.length===1){h=[o]}var p=this.matchOne(h,c,t);if(p){if(r.flipNegate)return true;return!this.negate}}if(r.flipNegate)return false;return this.negate}Minimatch.prototype.matchOne=function(e,t,r){var s=this.options;this.debug("matchOne",{this:this,file:e,pattern:t});this.debug("matchOne",e.length,t.length);for(var o=0,u=0,c=e.length,h=t.length;o<c&&u<h;o++,u++){this.debug("matchOne loop");var p=t[u];var d=e[o];this.debug(t,p,d);if(p===false)return false;if(p===a){this.debug("GLOBSTAR",[t,p,d]);var v=o;var m=u+1;if(m===h){this.debug("** at the end");for(;o<c;o++){if(e[o]==="."||e[o]===".."||!s.dot&&e[o].charAt(0)===".")return false}return true}while(v<c){var g=e[v];this.debug("\nglobstar while",e,v,t,m,g);if(this.matchOne(e.slice(v),t.slice(m),r)){this.debug("globstar found match!",v,c,g);return true}else{if(g==="."||g===".."||!s.dot&&g.charAt(0)==="."){this.debug("dot detected!",e,v,t,m);break}this.debug("globstar swallow a segment, and continue");v++}}if(r){this.debug("\n>>> no match, partial?",e,v,t,m);if(v===c)return true}return false}var y;if(typeof p==="string"){if(s.nocase){y=d.toLowerCase()===p.toLowerCase()}else{y=d===p}this.debug("string match",p,d,y)}else{y=d.match(p);this.debug("pattern match",p,d,y)}if(!y)return false}if(o===c&&u===h){return true}else if(o===c){return r}else if(u===h){var _=o===c-1&&e[o]==="";return _}throw new Error("wtf?")};function globUnescape(e){return e.replace(/\\(.)/g,"$1")}function regExpEscape(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}},4090:(module,__unused_webpack_exports,__nested_webpack_require_520315__)=>{var fs=__nested_webpack_require_520315__(5747);var path=__nested_webpack_require_520315__(5622);var os=__nested_webpack_require_520315__(2087);var runtimeRequire=true?eval("require"):0;var vars=process.config&&process.config.variables||{};var prebuildsOnly=!!process.env.PREBUILDS_ONLY;var abi=process.versions.modules;var runtime=isElectron()?"electron":"node";var arch=os.arch();var platform=os.platform();var libc=process.env.LIBC||(isAlpine(platform)?"musl":"glibc");var armv=process.env.ARM_VERSION||(arch==="arm64"?"8":vars.arm_version)||"";var uv=(process.versions.uv||"").split(".")[0];module.exports=load;function load(e){return runtimeRequire(load.path(e))}load.path=function(e){e=path.resolve(e||".");try{var t=runtimeRequire(path.join(e,"package.json")).name.toUpperCase().replace(/-/g,"_");if(process.env[t+"_PREBUILD"])e=process.env[t+"_PREBUILD"]}catch(e){}if(!prebuildsOnly){var r=getFirst(path.join(e,"build/Release"),matchBuild);if(r)return r;var s=getFirst(path.join(e,"build/Debug"),matchBuild);if(s)return s}var a=resolve(e);if(a)return a;var o=resolve(path.dirname(process.execPath));if(o)return o;var u=["platform="+platform,"arch="+arch,"runtime="+runtime,"abi="+abi,"uv="+uv,armv?"armv="+armv:"","libc="+libc].filter(Boolean).join(" ");throw new Error("No native build was found for "+u);function resolve(e){var t=path.join(e,"prebuilds",platform+"-"+arch);var r=readdirSync(t).map(parseTags);var s=r.filter(matchTags(runtime,abi));var a=s.sort(compareTags(runtime))[0];if(a)return path.join(t,a.file)}};function readdirSync(e){try{return fs.readdirSync(e)}catch(e){return[]}}function getFirst(e,t){var r=readdirSync(e).filter(t);return r[0]&&path.join(e,r[0])}function matchBuild(e){return/\.node$/.test(e)}function parseTags(e){var t=e.split(".");var r=t.pop();var s={file:e,specificity:0};if(r!=="node")return;for(var a=0;a<t.length;a++){var o=t[a];if(o==="node"||o==="electron"||o==="node-webkit"){s.runtime=o}else if(o==="napi"){s.napi=true}else if(o.slice(0,3)==="abi"){s.abi=o.slice(3)}else if(o.slice(0,2)==="uv"){s.uv=o.slice(2)}else if(o.slice(0,4)==="armv"){s.armv=o.slice(4)}else if(o==="glibc"||o==="musl"){s.libc=o}else{continue}s.specificity++}return s}function matchTags(e,t){return function(r){if(r==null)return false;if(r.runtime!==e&&!runtimeAgnostic(r))return false;if(r.abi!==t&&!r.napi)return false;if(r.uv&&r.uv!==uv)return false;if(r.armv&&r.armv!==armv)return false;if(r.libc&&r.libc!==libc)return false;return true}}function runtimeAgnostic(e){return e.runtime==="node"&&e.napi}function compareTags(e){return function(t,r){if(t.runtime!==r.runtime){return t.runtime===e?-1:1}else if(t.abi!==r.abi){return t.abi?-1:1}else if(t.specificity!==r.specificity){return t.specificity>r.specificity?-1:1}else{return 0}}}function isElectron(){if(process.versions&&process.versions.electron)return true;if(process.env.ELECTRON_RUN_AS_NODE)return true;return typeof window!=="undefined"&&window.process&&window.process.type==="renderer"}function isAlpine(e){return e==="linux"&&fs.existsSync("/etc/alpine-release")}load.parseTags=parseTags;load.matchTags=matchTags;load.compareTags=compareTags},480:(e,t,r)=>{"use strict";var s=r(5747);var a=r(4959);var o=r(4314);e.exports=t;var u=process.version.substr(1).replace(/-.*$/,"").split(".").map((function(e){return+e}));var c=["build","clean","configure","package","publish","reveal","testbinary","testpackage","unpublish"];var h="napi_build_version=";e.exports.get_napi_version=function(e){var t=process.versions.napi;if(!t){if(u[0]===9&&u[1]>=3)t=2;else if(u[0]===8)t=1}return t};e.exports.get_napi_version_as_string=function(t){var r=e.exports.get_napi_version(t);return r?""+r:""};e.exports.validate_package_json=function(t,r){var s=t.binary;var a=pathOK(s.module_path);var o=pathOK(s.remote_path);var u=pathOK(s.package_name);var c=e.exports.get_napi_build_versions(t,r,true);var h=e.exports.get_napi_build_versions_raw(t);if(c){c.forEach((function(e){if(!(parseInt(e,10)===e&&e>0)){throw new Error("All values specified in napi_versions must be positive integers.")}}))}if(c&&(!a||!o&&!u)){throw new Error("When napi_versions is specified; module_path and either remote_path or "+"package_name must contain the substitution string '{napi_build_version}`.")}if((a||o||u)&&!h){throw new Error("When the substitution string '{napi_build_version}` is specified in "+"module_path, remote_path, or package_name; napi_versions must also be specified.")}if(c&&!e.exports.get_best_napi_build_version(t,r)&&e.exports.build_napi_only(t)){throw new Error("The N-API version of this Node instance is "+e.exports.get_napi_version(r?r.target:undefined)+". "+"This module supports N-API version(s) "+e.exports.get_napi_build_versions_raw(t)+". "+"This Node instance cannot run this module.")}if(h&&!c&&e.exports.build_napi_only(t)){throw new Error("The N-API version of this Node instance is "+e.exports.get_napi_version(r?r.target:undefined)+". "+"This module supports N-API version(s) "+e.exports.get_napi_build_versions_raw(t)+". "+"This Node instance cannot run this module.")}};function pathOK(e){return e&&(e.indexOf("{napi_build_version}")!==-1||e.indexOf("{node_napi_label}")!==-1)}e.exports.expand_commands=function(t,r,s){var a=[];var o=e.exports.get_napi_build_versions(t,r);s.forEach((function(s){if(o&&s.name==="install"){var u=e.exports.get_best_napi_build_version(t,r);var p=u?[h+u]:[];a.push({name:s.name,args:p})}else if(o&&c.indexOf(s.name)!==-1){o.forEach((function(e){var t=s.args.slice();t.push(h+e);a.push({name:s.name,args:t})}))}else{a.push(s)}}));return a};e.exports.get_napi_build_versions=function(t,r,s){var a=[];var u=e.exports.get_napi_version(r?r.target:undefined);if(t.binary&&t.binary.napi_versions){t.binary.napi_versions.forEach((function(e){var t=a.indexOf(e)!==-1;if(!t&&u&&e<=u){a.push(e)}else if(s&&!t&&u){o.info("This Node instance does not support builds for N-API version",e)}}))}if(r&&r["build-latest-napi-version-only"]){var c=0;a.forEach((function(e){if(e>c)c=e}));a=c?[c]:[]}return a.length?a:undefined};e.exports.get_napi_build_versions_raw=function(e){var t=[];if(e.binary&&e.binary.napi_versions){e.binary.napi_versions.forEach((function(e){if(t.indexOf(e)===-1){t.push(e)}}))}return t.length?t:undefined};e.exports.get_command_arg=function(e){return h+e};e.exports.get_napi_build_version_from_command_args=function(e){for(var t=0;t<e.length;t++){var r=e[t];if(r.indexOf(h)===0){return parseInt(r.substr(h.length),10)}}return undefined};e.exports.swap_build_dir_out=function(t){if(t){a.sync(e.exports.get_build_dir(t));s.renameSync("build",e.exports.get_build_dir(t))}};e.exports.swap_build_dir_in=function(t){if(t){a.sync("build");s.renameSync(e.exports.get_build_dir(t),"build")}};e.exports.get_build_dir=function(e){return"build-tmp-napi-v"+e};e.exports.get_best_napi_build_version=function(t,r){var s=0;var a=e.exports.get_napi_build_versions(t,r);if(a){var o=e.exports.get_napi_version(r?r.target:undefined);a.forEach((function(e){if(e>s&&e<=o){s=e}}))}return s===0?undefined:s};e.exports.build_napi_only=function(e){return e.binary&&e.binary.package_name&&e.binary.package_name.indexOf("{node_napi_label}")===-1}},887:(e,t,r)=>{"use strict";e.exports=t;var s=r(5622);var a=r(5911);var o=r(8835);var u=r(4889);var c=r(480);var h;if(process.env.NODE_PRE_GYP_ABI_CROSSWALK){h=require(process.env.NODE_PRE_GYP_ABI_CROSSWALK)}else{h=r(282)}var p={};Object.keys(h).forEach((function(e){var t=e.split(".")[0];if(!p[t]){p[t]=e}}));function get_electron_abi(e,t){if(!e){throw new Error("get_electron_abi requires valid runtime arg")}if(typeof t==="undefined"){throw new Error("Empty target version is not supported if electron is the target.")}var r=a.parse(t);return e+"-v"+r.major+"."+r.minor}e.exports.get_electron_abi=get_electron_abi;function get_node_webkit_abi(e,t){if(!e){throw new Error("get_node_webkit_abi requires valid runtime arg")}if(typeof t==="undefined"){throw new Error("Empty target version is not supported if node-webkit is the target.")}return e+"-v"+t}e.exports.get_node_webkit_abi=get_node_webkit_abi;function get_node_abi(e,t){if(!e){throw new Error("get_node_abi requires valid runtime arg")}if(!t){throw new Error("get_node_abi requires valid process.versions object")}var r=a.parse(t.node);if(r.major===0&&r.minor%2){return e+"-v"+t.node}else{return t.modules?e+"-v"+ +t.modules:"v8-"+t.v8.split(".").slice(0,2).join(".")}}e.exports.get_node_abi=get_node_abi;function get_runtime_abi(e,t){if(!e){throw new Error("get_runtime_abi requires valid runtime arg")}if(e==="node-webkit"){return get_node_webkit_abi(e,t||process.versions["node-webkit"])}else if(e==="electron"){return get_electron_abi(e,t||process.versions.electron)}else{if(e!="node"){throw new Error("Unknown Runtime: '"+e+"'")}if(!t){return get_node_abi(e,process.versions)}else{var r;if(h[t]){r=h[t]}else{var s=t.split(".").map((function(e){return+e}));if(s.length!=3){throw new Error("Unknown target version: "+t)}var a=s[0];var o=s[1];var u=s[2];if(a===1){while(true){if(o>0)--o;if(u>0)--u;var c=""+a+"."+o+"."+u;if(h[c]){r=h[c];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+c+" as ABI compatible target");break}if(o===0&&u===0){break}}}else if(a>=2){if(p[a]){r=h[p[a]];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+p[a]+" as ABI compatible target")}}else if(a===0){if(s[1]%2===0){while(--u>0){var d=""+a+"."+o+"."+u;if(h[d]){r=h[d];console.log("Warning: node-pre-gyp could not find exact match for "+t);console.log("Warning: but node-pre-gyp successfully choose "+d+" as ABI compatible target");break}}}}}if(!r){throw new Error("Unsupported target version: "+t)}var v={node:t,v8:r.v8+".0",modules:r.node_abi>1?r.node_abi:undefined};return get_node_abi(e,v)}}}e.exports.get_runtime_abi=get_runtime_abi;var d=["module_name","module_path","host"];function validate_config(e,t){var r=e.name+" package.json is not node-pre-gyp ready:\n";var s=[];if(!e.main){s.push("main")}if(!e.version){s.push("version")}if(!e.name){s.push("name")}if(!e.binary){s.push("binary")}var a=e.binary;d.forEach((function(e){if(s.indexOf("binary")>-1){s.pop("binary")}if(!a||a[e]===undefined||a[e]===""){s.push("binary."+e)}}));if(s.length>=1){throw new Error(r+"package.json must declare these properties: \n"+s.join("\n"))}if(a){var u=o.parse(a.host).protocol;if(u==="http:"){throw new Error("'host' protocol ("+u+") is invalid - only 'https:' is accepted")}}c.validate_package_json(e,t)}e.exports.validate_config=validate_config;function eval_template(e,t){Object.keys(t).forEach((function(r){var s="{"+r+"}";while(e.indexOf(s)>-1){e=e.replace(s,t[r])}}));return e}function fix_slashes(e){if(e.slice(-1)!="/"){return e+"/"}return e}function drop_double_slashes(e){return e.replace(/\/\//g,"/")}function get_process_runtime(e){var t="node";if(e["node-webkit"]){t="node-webkit"}else if(e.electron){t="electron"}return t}e.exports.get_process_runtime=get_process_runtime;var v="{module_name}-v{version}-{node_abi}-{platform}-{arch}.tar.gz";var m="";e.exports.evaluate=function(e,t,r){t=t||{};validate_config(e,t);var h=e.version;var p=a.parse(h);var d=t.runtime||get_process_runtime(process.versions);var g={name:e.name,configuration:Boolean(t.debug)?"Debug":"Release",debug:t.debug,module_name:e.binary.module_name,version:p.version,prerelease:p.prerelease.length?p.prerelease.join("."):"",build:p.build.length?p.build.join("."):"",major:p.major,minor:p.minor,patch:p.patch,runtime:d,node_abi:get_runtime_abi(d,t.target),node_abi_napi:c.get_napi_version(t.target)?"napi":get_runtime_abi(d,t.target),napi_version:c.get_napi_version(t.target),napi_build_version:r||"",node_napi_label:r?"napi-v"+r:get_runtime_abi(d,t.target),target:t.target||"",platform:t.target_platform||process.platform,target_platform:t.target_platform||process.platform,arch:t.target_arch||process.arch,target_arch:t.target_arch||process.arch,libc:t.target_libc||u.family||"unknown",module_main:e.main,toolset:t.toolset||""};var y=process.env["npm_config_"+g.module_name+"_binary_host_mirror"]||e.binary.host;g.host=fix_slashes(eval_template(y,g));g.module_path=eval_template(e.binary.module_path,g);if(t.module_root){g.module_path=s.join(t.module_root,g.module_path)}else{g.module_path=s.resolve(g.module_path)}g.module=s.join(g.module_path,g.module_name+".node");g.remote_path=e.binary.remote_path?drop_double_slashes(fix_slashes(eval_template(e.binary.remote_path,g))):m;var _=e.binary.package_name?e.binary.package_name:v;g.package_name=eval_template(_,g);g.staged_tarball=s.join("build/stage",g.remote_path,g.package_name);g.hosted_path=o.resolve(g.host,g.remote_path);g.hosted_tarball=o.resolve(g.hosted_path,g.package_name);return g}},4314:(e,t,r)=>{"use strict";var s=r(1083);var a=r(1800);var o=r(8614).EventEmitter;var u=t=e.exports=new o;var c=r(1669);var h=r(9344);var p=r(3645);h(true);var d=process.stderr;Object.defineProperty(u,"stream",{set:function(e){d=e;if(this.gauge)this.gauge.setWriteTo(d,d)},get:function(){return d}});var v;u.useColor=function(){return v!=null?v:d.isTTY};u.enableColor=function(){v=true;this.gauge.setTheme({hasColor:v,hasUnicode:m})};u.disableColor=function(){v=false;this.gauge.setTheme({hasColor:v,hasUnicode:m})};u.level="info";u.gauge=new a(d,{enabled:false,theme:{hasColor:u.useColor()},template:[{type:"progressbar",length:20},{type:"activityIndicator",kerning:1,length:1},{type:"section",default:""},":",{type:"logline",kerning:1,default:""}]});u.tracker=new s.TrackerGroup;u.progressEnabled=u.gauge.isEnabled();var m;u.enableUnicode=function(){m=true;this.gauge.setTheme({hasColor:this.useColor(),hasUnicode:m})};u.disableUnicode=function(){m=false;this.gauge.setTheme({hasColor:this.useColor(),hasUnicode:m})};u.setGaugeThemeset=function(e){this.gauge.setThemeset(e)};u.setGaugeTemplate=function(e){this.gauge.setTemplate(e)};u.enableProgress=function(){if(this.progressEnabled)return;this.progressEnabled=true;this.tracker.on("change",this.showProgress);if(this._pause)return;this.gauge.enable()};u.disableProgress=function(){if(!this.progressEnabled)return;this.progressEnabled=false;this.tracker.removeListener("change",this.showProgress);this.gauge.disable()};var g=["newGroup","newItem","newStream"];var mixinLog=function(e){Object.keys(u).forEach((function(t){if(t[0]==="_")return;if(g.filter((function(e){return e===t})).length)return;if(e[t])return;if(typeof u[t]!=="function")return;var r=u[t];e[t]=function(){return r.apply(u,arguments)}}));if(e instanceof s.TrackerGroup){g.forEach((function(t){var r=e[t];e[t]=function(){return mixinLog(r.apply(e,arguments))}}))}return e};g.forEach((function(e){u[e]=function(){return mixinLog(this.tracker[e].apply(this.tracker,arguments))}}));u.clearProgress=function(e){if(!this.progressEnabled)return e&&process.nextTick(e);this.gauge.hide(e)};u.showProgress=function(e,t){if(!this.progressEnabled)return;var r={};if(e)r.section=e;var s=u.record[u.record.length-1];if(s){r.subsection=s.prefix;var a=u.disp[s.level]||s.level;var o=this._format(a,u.style[s.level]);if(s.prefix)o+=" "+this._format(s.prefix,this.prefixStyle);o+=" "+s.message.split(/\r?\n/)[0];r.logline=o}r.completed=t||this.tracker.completed();this.gauge.show(r)}.bind(u);u.pause=function(){this._paused=true;if(this.progressEnabled)this.gauge.disable()};u.resume=function(){if(!this._paused)return;this._paused=false;var e=this._buffer;this._buffer=[];e.forEach((function(e){this.emitLog(e)}),this);if(this.progressEnabled)this.gauge.enable()};u._buffer=[];var y=0;u.record=[];u.maxRecordSize=1e4;u.log=function(e,t,r){var s=this.levels[e];if(s===undefined){return this.emit("error",new Error(c.format("Undefined log level: %j",e)))}var a=new Array(arguments.length-2);var o=null;for(var u=2;u<arguments.length;u++){var h=a[u-2]=arguments[u];if(typeof h==="object"&&h&&h instanceof Error&&h.stack){Object.defineProperty(h,"stack",{value:o=h.stack+"",enumerable:true,writable:true})}}if(o)a.unshift(o+"\n");r=c.format.apply(c,a);var p={id:y++,level:e,prefix:String(t||""),message:r,messageRaw:a};this.emit("log",p);this.emit("log."+e,p);if(p.prefix)this.emit(p.prefix,p);this.record.push(p);var d=this.maxRecordSize;var v=this.record.length-d;if(v>d/10){var m=Math.floor(d*.9);this.record=this.record.slice(-1*m)}this.emitLog(p)}.bind(u);u.emitLog=function(e){if(this._paused){this._buffer.push(e);return}if(this.progressEnabled)this.gauge.pulse(e.prefix);var t=this.levels[e.level];if(t===undefined)return;if(t<this.levels[this.level])return;if(t>0&&!isFinite(t))return;var r=u.disp[e.level]!=null?u.disp[e.level]:e.level;this.clearProgress();e.message.split(/\r?\n/).forEach((function(t){if(this.heading){this.write(this.heading,this.headingStyle);this.write(" ")}this.write(r,u.style[e.level]);var s=e.prefix||"";if(s)this.write(" ");this.write(s,this.prefixStyle);this.write(" "+t+"\n")}),this);this.showProgress()};u._format=function(e,t){if(!d)return;var r="";if(this.useColor()){t=t||{};var s=[];if(t.fg)s.push(t.fg);if(t.bg)s.push("bg"+t.bg[0].toUpperCase()+t.bg.slice(1));if(t.bold)s.push("bold");if(t.underline)s.push("underline");if(t.inverse)s.push("inverse");if(s.length)r+=p.color(s);if(t.beep)r+=p.beep()}r+=e;if(this.useColor()){r+=p.color("reset")}return r};u.write=function(e,t){if(!d)return;d.write(this._format(e,t))};u.addLevel=function(e,t,r,s){if(s==null)s=e;this.levels[e]=t;this.style[e]=r;if(!this[e]){this[e]=function(){var t=new Array(arguments.length+1);t[0]=e;for(var r=0;r<arguments.length;r++){t[r+1]=arguments[r]}return this.log.apply(this,t)}.bind(this)}this.disp[e]=s};u.prefixStyle={fg:"magenta"};u.headingStyle={fg:"white",bg:"black"};u.style={};u.levels={};u.disp={};u.addLevel("silly",-Infinity,{inverse:true},"sill");u.addLevel("verbose",1e3,{fg:"blue",bg:"black"},"verb");u.addLevel("info",2e3,{fg:"green"});u.addLevel("timing",2500,{fg:"green",bg:"black"});u.addLevel("http",3e3,{fg:"green",bg:"black"});u.addLevel("notice",3500,{fg:"blue",bg:"black"});u.addLevel("warn",4e3,{fg:"black",bg:"yellow"},"WARN");u.addLevel("error",5e3,{fg:"red",bg:"black"},"ERR!");u.addLevel("silent",Infinity);u.on("error",(function(){}))},6325:e=>{"use strict";e.exports=Number.isNaN||function(e){return e!==e}},7426:e=>{"use strict";
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var t=Object.getOwnPropertySymbols;var r=Object.prototype.hasOwnProperty;var s=Object.prototype.propertyIsEnumerable;function toObject(e){if(e===null||e===undefined){throw new TypeError("Object.assign cannot be called with null or undefined")}return Object(e)}function shouldUseNative(){try{if(!Object.assign){return false}var e=new String("abc");e[5]="de";if(Object.getOwnPropertyNames(e)[0]==="5"){return false}var t={};for(var r=0;r<10;r++){t["_"+String.fromCharCode(r)]=r}var s=Object.getOwnPropertyNames(t).map((function(e){return t[e]}));if(s.join("")!=="**********"){return false}var a={};"abcdefghijklmnopqrst".split("").forEach((function(e){a[e]=e}));if(Object.keys(Object.assign({},a)).join("")!=="abcdefghijklmnopqrst"){return false}return true}catch(e){return false}}e.exports=shouldUseNative()?Object.assign:function(e,a){var o;var u=toObject(e);var c;for(var h=1;h<arguments.length;h++){o=Object(arguments[h]);for(var p in o){if(r.call(o,p)){u[p]=o[p]}}if(t){c=t(o);for(var d=0;d<c.length;d++){if(s.call(o,c[d])){u[c[d]]=o[c[d]]}}}}return u}},1223:(e,t,r)=>{var s=r(2940);e.exports=s(once);e.exports.strict=s(onceStrict);once.proto=once((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return once(this)},configurable:true});Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return onceStrict(this)},configurable:true})}));function once(e){var f=function(){if(f.called)return f.value;f.called=true;return f.value=e.apply(this,arguments)};f.called=false;return f}function onceStrict(e){var f=function(){if(f.called)throw new Error(f.onceError);f.called=true;return f.value=e.apply(this,arguments)};var t=e.name||"Function wrapped with `once`";f.onceError=t+" shouldn't be called more than once";f.called=false;return f}},8714:e=>{"use strict";function posix(e){return e.charAt(0)==="/"}function win32(e){var t=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var r=t.exec(e);var s=r[1]||"";var a=Boolean(s&&s.charAt(1)!==":");return Boolean(r[2]||a)}e.exports=process.platform==="win32"?win32:posix;e.exports.posix=posix;e.exports.win32=win32},5980:e=>{"use strict";var t=process.platform==="win32";var r=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/;var s=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/;var a={};function win32SplitPath(e){var t=r.exec(e),a=(t[1]||"")+(t[2]||""),o=t[3]||"";var u=s.exec(o),c=u[1],h=u[2],p=u[3];return[a,c,h,p]}a.parse=function(e){if(typeof e!=="string"){throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e)}var t=win32SplitPath(e);if(!t||t.length!==4){throw new TypeError("Invalid path '"+e+"'")}return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};var o=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;var u={};function posixSplitPath(e){return o.exec(e).slice(1)}u.parse=function(e){if(typeof e!=="string"){throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e)}var t=posixSplitPath(e);if(!t||t.length!==4){throw new TypeError("Invalid path '"+e+"'")}t[1]=t[1]||"";t[2]=t[2]||"";t[3]=t[3]||"";return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};if(t)e.exports=a.parse;else e.exports=u.parse;e.exports.posix=u.parse;e.exports.win32=a.parse},7810:e=>{"use strict";if(!process.version||process.version.indexOf("v0.")===0||process.version.indexOf("v1.")===0&&process.version.indexOf("v1.8.")!==0){e.exports={nextTick:nextTick}}else{e.exports=process}function nextTick(e,t,r,s){if(typeof e!=="function"){throw new TypeError('"callback" argument must be a function')}var a=arguments.length;var o,u;switch(a){case 0:case 1:return process.nextTick(e);case 2:return process.nextTick((function afterTickOne(){e.call(null,t)}));case 3:return process.nextTick((function afterTickTwo(){e.call(null,t,r)}));case 4:return process.nextTick((function afterTickThree(){e.call(null,t,r,s)}));default:o=new Array(a-1);u=0;while(u<o.length){o[u++]=arguments[u]}return process.nextTick((function afterTick(){e.apply(null,o)}))}}},1359:(e,t,r)=>{"use strict";var s=r(7810);var a=Object.keys||function(e){var t=[];for(var r in e){t.push(r)}return t};e.exports=Duplex;var o=r(5898);o.inherits=r(4124);var u=r(1433);var c=r(6993);o.inherits(Duplex,u);{var h=a(c.prototype);for(var p=0;p<h.length;p++){var d=h[p];if(!Duplex.prototype[d])Duplex.prototype[d]=c.prototype[d]}}function Duplex(e){if(!(this instanceof Duplex))return new Duplex(e);u.call(this,e);c.call(this,e);if(e&&e.readable===false)this.readable=false;if(e&&e.writable===false)this.writable=false;this.allowHalfOpen=true;if(e&&e.allowHalfOpen===false)this.allowHalfOpen=false;this.once("end",onend)}Object.defineProperty(Duplex.prototype,"writableHighWaterMark",{enumerable:false,get:function(){return this._writableState.highWaterMark}});function onend(){if(this.allowHalfOpen||this._writableState.ended)return;s.nextTick(onEndNT,this)}function onEndNT(e){e.end()}Object.defineProperty(Duplex.prototype,"destroyed",{get:function(){if(this._readableState===undefined||this._writableState===undefined){return false}return this._readableState.destroyed&&this._writableState.destroyed},set:function(e){if(this._readableState===undefined||this._writableState===undefined){return}this._readableState.destroyed=e;this._writableState.destroyed=e}});Duplex.prototype._destroy=function(e,t){this.push(null);this.end();s.nextTick(t,e)}},1542:(e,t,r)=>{"use strict";e.exports=PassThrough;var s=r(4415);var a=r(5898);a.inherits=r(4124);a.inherits(PassThrough,s);function PassThrough(e){if(!(this instanceof PassThrough))return new PassThrough(e);s.call(this,e)}PassThrough.prototype._transform=function(e,t,r){r(null,e)}},1433:(e,t,r)=>{"use strict";var s=r(7810);e.exports=Readable;var a=r(893);var o;Readable.ReadableState=ReadableState;var u=r(8614).EventEmitter;var EElistenerCount=function(e,t){return e.listeners(t).length};var c=r(2387);var h=r(1867).Buffer;var p=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return h.from(e)}function _isUint8Array(e){return h.isBuffer(e)||e instanceof p}var d=r(5898);d.inherits=r(4124);var v=r(1669);var m=void 0;if(v&&v.debuglog){m=v.debuglog("stream")}else{m=function(){}}var g=r(7053);var y=r(7049);var _;d.inherits(Readable,c);var E=["error","close","destroy","pause","resume"];function prependListener(e,t,r){if(typeof e.prependListener==="function")return e.prependListener(t,r);if(!e._events||!e._events[t])e.on(t,r);else if(a(e._events[t]))e._events[t].unshift(r);else e._events[t]=[r,e._events[t]]}function ReadableState(e,t){o=o||r(1359);e=e||{};var s=t instanceof o;this.objectMode=!!e.objectMode;if(s)this.objectMode=this.objectMode||!!e.readableObjectMode;var a=e.highWaterMark;var u=e.readableHighWaterMark;var c=this.objectMode?16:16*1024;if(a||a===0)this.highWaterMark=a;else if(s&&(u||u===0))this.highWaterMark=u;else this.highWaterMark=c;this.highWaterMark=Math.floor(this.highWaterMark);this.buffer=new g;this.length=0;this.pipes=null;this.pipesCount=0;this.flowing=null;this.ended=false;this.endEmitted=false;this.reading=false;this.sync=true;this.needReadable=false;this.emittedReadable=false;this.readableListening=false;this.resumeScheduled=false;this.destroyed=false;this.defaultEncoding=e.defaultEncoding||"utf8";this.awaitDrain=0;this.readingMore=false;this.decoder=null;this.encoding=null;if(e.encoding){if(!_)_=r(4841).s;this.decoder=new _(e.encoding);this.encoding=e.encoding}}function Readable(e){o=o||r(1359);if(!(this instanceof Readable))return new Readable(e);this._readableState=new ReadableState(e,this);this.readable=true;if(e){if(typeof e.read==="function")this._read=e.read;if(typeof e.destroy==="function")this._destroy=e.destroy}c.call(this)}Object.defineProperty(Readable.prototype,"destroyed",{get:function(){if(this._readableState===undefined){return false}return this._readableState.destroyed},set:function(e){if(!this._readableState){return}this._readableState.destroyed=e}});Readable.prototype.destroy=y.destroy;Readable.prototype._undestroy=y.undestroy;Readable.prototype._destroy=function(e,t){this.push(null);t(e)};Readable.prototype.push=function(e,t){var r=this._readableState;var s;if(!r.objectMode){if(typeof e==="string"){t=t||r.defaultEncoding;if(t!==r.encoding){e=h.from(e,t);t=""}s=true}}else{s=true}return readableAddChunk(this,e,t,false,s)};Readable.prototype.unshift=function(e){return readableAddChunk(this,e,null,true,false)};function readableAddChunk(e,t,r,s,a){var o=e._readableState;if(t===null){o.reading=false;onEofChunk(e,o)}else{var u;if(!a)u=chunkInvalid(o,t);if(u){e.emit("error",u)}else if(o.objectMode||t&&t.length>0){if(typeof t!=="string"&&!o.objectMode&&Object.getPrototypeOf(t)!==h.prototype){t=_uint8ArrayToBuffer(t)}if(s){if(o.endEmitted)e.emit("error",new Error("stream.unshift() after end event"));else addChunk(e,o,t,true)}else if(o.ended){e.emit("error",new Error("stream.push() after EOF"))}else{o.reading=false;if(o.decoder&&!r){t=o.decoder.write(t);if(o.objectMode||t.length!==0)addChunk(e,o,t,false);else maybeReadMore(e,o)}else{addChunk(e,o,t,false)}}}else if(!s){o.reading=false}}return needMoreData(o)}function addChunk(e,t,r,s){if(t.flowing&&t.length===0&&!t.sync){e.emit("data",r);e.read(0)}else{t.length+=t.objectMode?1:r.length;if(s)t.buffer.unshift(r);else t.buffer.push(r);if(t.needReadable)emitReadable(e)}maybeReadMore(e,t)}function chunkInvalid(e,t){var r;if(!_isUint8Array(t)&&typeof t!=="string"&&t!==undefined&&!e.objectMode){r=new TypeError("Invalid non-string/buffer chunk")}return r}function needMoreData(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||e.length===0)}Readable.prototype.isPaused=function(){return this._readableState.flowing===false};Readable.prototype.setEncoding=function(e){if(!_)_=r(4841).s;this._readableState.decoder=new _(e);this._readableState.encoding=e;return this};var x=8388608;function computeNewHighWaterMark(e){if(e>=x){e=x}else{e--;e|=e>>>1;e|=e>>>2;e|=e>>>4;e|=e>>>8;e|=e>>>16;e++}return e}function howMuchToRead(e,t){if(e<=0||t.length===0&&t.ended)return 0;if(t.objectMode)return 1;if(e!==e){if(t.flowing&&t.length)return t.buffer.head.data.length;else return t.length}if(e>t.highWaterMark)t.highWaterMark=computeNewHighWaterMark(e);if(e<=t.length)return e;if(!t.ended){t.needReadable=true;return 0}return t.length}Readable.prototype.read=function(e){m("read",e);e=parseInt(e,10);var t=this._readableState;var r=e;if(e!==0)t.emittedReadable=false;if(e===0&&t.needReadable&&(t.length>=t.highWaterMark||t.ended)){m("read: emitReadable",t.length,t.ended);if(t.length===0&&t.ended)endReadable(this);else emitReadable(this);return null}e=howMuchToRead(e,t);if(e===0&&t.ended){if(t.length===0)endReadable(this);return null}var s=t.needReadable;m("need readable",s);if(t.length===0||t.length-e<t.highWaterMark){s=true;m("length less than watermark",s)}if(t.ended||t.reading){s=false;m("reading or ended",s)}else if(s){m("do read");t.reading=true;t.sync=true;if(t.length===0)t.needReadable=true;this._read(t.highWaterMark);t.sync=false;if(!t.reading)e=howMuchToRead(r,t)}var a;if(e>0)a=fromList(e,t);else a=null;if(a===null){t.needReadable=true;e=0}else{t.length-=e}if(t.length===0){if(!t.ended)t.needReadable=true;if(r!==e&&t.ended)endReadable(this)}if(a!==null)this.emit("data",a);return a};function onEofChunk(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();if(r&&r.length){t.buffer.push(r);t.length+=t.objectMode?1:r.length}}t.ended=true;emitReadable(e)}function emitReadable(e){var t=e._readableState;t.needReadable=false;if(!t.emittedReadable){m("emitReadable",t.flowing);t.emittedReadable=true;if(t.sync)s.nextTick(emitReadable_,e);else emitReadable_(e)}}function emitReadable_(e){m("emit readable");e.emit("readable");flow(e)}function maybeReadMore(e,t){if(!t.readingMore){t.readingMore=true;s.nextTick(maybeReadMore_,e,t)}}function maybeReadMore_(e,t){var r=t.length;while(!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark){m("maybeReadMore read 0");e.read(0);if(r===t.length)break;else r=t.length}t.readingMore=false}Readable.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))};Readable.prototype.pipe=function(e,t){var r=this;var a=this._readableState;switch(a.pipesCount){case 0:a.pipes=e;break;case 1:a.pipes=[a.pipes,e];break;default:a.pipes.push(e);break}a.pipesCount+=1;m("pipe count=%d opts=%j",a.pipesCount,t);var o=(!t||t.end!==false)&&e!==process.stdout&&e!==process.stderr;var u=o?onend:unpipe;if(a.endEmitted)s.nextTick(u);else r.once("end",u);e.on("unpipe",onunpipe);function onunpipe(e,t){m("onunpipe");if(e===r){if(t&&t.hasUnpiped===false){t.hasUnpiped=true;cleanup()}}}function onend(){m("onend");e.end()}var c=pipeOnDrain(r);e.on("drain",c);var h=false;function cleanup(){m("cleanup");e.removeListener("close",onclose);e.removeListener("finish",onfinish);e.removeListener("drain",c);e.removeListener("error",onerror);e.removeListener("unpipe",onunpipe);r.removeListener("end",onend);r.removeListener("end",unpipe);r.removeListener("data",ondata);h=true;if(a.awaitDrain&&(!e._writableState||e._writableState.needDrain))c()}var p=false;r.on("data",ondata);function ondata(t){m("ondata");p=false;var s=e.write(t);if(false===s&&!p){if((a.pipesCount===1&&a.pipes===e||a.pipesCount>1&&indexOf(a.pipes,e)!==-1)&&!h){m("false write response, pause",r._readableState.awaitDrain);r._readableState.awaitDrain++;p=true}r.pause()}}function onerror(t){m("onerror",t);unpipe();e.removeListener("error",onerror);if(EElistenerCount(e,"error")===0)e.emit("error",t)}prependListener(e,"error",onerror);function onclose(){e.removeListener("finish",onfinish);unpipe()}e.once("close",onclose);function onfinish(){m("onfinish");e.removeListener("close",onclose);unpipe()}e.once("finish",onfinish);function unpipe(){m("unpipe");r.unpipe(e)}e.emit("pipe",r);if(!a.flowing){m("pipe resume");r.resume()}return e};function pipeOnDrain(e){return function(){var t=e._readableState;m("pipeOnDrain",t.awaitDrain);if(t.awaitDrain)t.awaitDrain--;if(t.awaitDrain===0&&EElistenerCount(e,"data")){t.flowing=true;flow(e)}}}Readable.prototype.unpipe=function(e){var t=this._readableState;var r={hasUnpiped:false};if(t.pipesCount===0)return this;if(t.pipesCount===1){if(e&&e!==t.pipes)return this;if(!e)e=t.pipes;t.pipes=null;t.pipesCount=0;t.flowing=false;if(e)e.emit("unpipe",this,r);return this}if(!e){var s=t.pipes;var a=t.pipesCount;t.pipes=null;t.pipesCount=0;t.flowing=false;for(var o=0;o<a;o++){s[o].emit("unpipe",this,r)}return this}var u=indexOf(t.pipes,e);if(u===-1)return this;t.pipes.splice(u,1);t.pipesCount-=1;if(t.pipesCount===1)t.pipes=t.pipes[0];e.emit("unpipe",this,r);return this};Readable.prototype.on=function(e,t){var r=c.prototype.on.call(this,e,t);if(e==="data"){if(this._readableState.flowing!==false)this.resume()}else if(e==="readable"){var a=this._readableState;if(!a.endEmitted&&!a.readableListening){a.readableListening=a.needReadable=true;a.emittedReadable=false;if(!a.reading){s.nextTick(nReadingNextTick,this)}else if(a.length){emitReadable(this)}}}return r};Readable.prototype.addListener=Readable.prototype.on;function nReadingNextTick(e){m("readable nexttick read 0");e.read(0)}Readable.prototype.resume=function(){var e=this._readableState;if(!e.flowing){m("resume");e.flowing=true;resume(this,e)}return this};function resume(e,t){if(!t.resumeScheduled){t.resumeScheduled=true;s.nextTick(resume_,e,t)}}function resume_(e,t){if(!t.reading){m("resume read 0");e.read(0)}t.resumeScheduled=false;t.awaitDrain=0;e.emit("resume");flow(e);if(t.flowing&&!t.reading)e.read(0)}Readable.prototype.pause=function(){m("call pause flowing=%j",this._readableState.flowing);if(false!==this._readableState.flowing){m("pause");this._readableState.flowing=false;this.emit("pause")}return this};function flow(e){var t=e._readableState;m("flow",t.flowing);while(t.flowing&&e.read()!==null){}}Readable.prototype.wrap=function(e){var t=this;var r=this._readableState;var s=false;e.on("end",(function(){m("wrapped end");if(r.decoder&&!r.ended){var e=r.decoder.end();if(e&&e.length)t.push(e)}t.push(null)}));e.on("data",(function(a){m("wrapped data");if(r.decoder)a=r.decoder.write(a);if(r.objectMode&&(a===null||a===undefined))return;else if(!r.objectMode&&(!a||!a.length))return;var o=t.push(a);if(!o){s=true;e.pause()}}));for(var a in e){if(this[a]===undefined&&typeof e[a]==="function"){this[a]=function(t){return function(){return e[t].apply(e,arguments)}}(a)}}for(var o=0;o<E.length;o++){e.on(E[o],this.emit.bind(this,E[o]))}this._read=function(t){m("wrapped _read",t);if(s){s=false;e.resume()}};return this};Object.defineProperty(Readable.prototype,"readableHighWaterMark",{enumerable:false,get:function(){return this._readableState.highWaterMark}});Readable._fromList=fromList;function fromList(e,t){if(t.length===0)return null;var r;if(t.objectMode)r=t.buffer.shift();else if(!e||e>=t.length){if(t.decoder)r=t.buffer.join("");else if(t.buffer.length===1)r=t.buffer.head.data;else r=t.buffer.concat(t.length);t.buffer.clear()}else{r=fromListPartial(e,t.buffer,t.decoder)}return r}function fromListPartial(e,t,r){var s;if(e<t.head.data.length){s=t.head.data.slice(0,e);t.head.data=t.head.data.slice(e)}else if(e===t.head.data.length){s=t.shift()}else{s=r?copyFromBufferString(e,t):copyFromBuffer(e,t)}return s}function copyFromBufferString(e,t){var r=t.head;var s=1;var a=r.data;e-=a.length;while(r=r.next){var o=r.data;var u=e>o.length?o.length:e;if(u===o.length)a+=o;else a+=o.slice(0,e);e-=u;if(e===0){if(u===o.length){++s;if(r.next)t.head=r.next;else t.head=t.tail=null}else{t.head=r;r.data=o.slice(u)}break}++s}t.length-=s;return a}function copyFromBuffer(e,t){var r=h.allocUnsafe(e);var s=t.head;var a=1;s.data.copy(r);e-=s.data.length;while(s=s.next){var o=s.data;var u=e>o.length?o.length:e;o.copy(r,r.length-e,0,u);e-=u;if(e===0){if(u===o.length){++a;if(s.next)t.head=s.next;else t.head=t.tail=null}else{t.head=s;s.data=o.slice(u)}break}++a}t.length-=a;return r}function endReadable(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');if(!t.endEmitted){t.ended=true;s.nextTick(endReadableNT,t,e)}}function endReadableNT(e,t){if(!e.endEmitted&&e.length===0){e.endEmitted=true;t.readable=false;t.emit("end")}}function indexOf(e,t){for(var r=0,s=e.length;r<s;r++){if(e[r]===t)return r}return-1}},4415:(e,t,r)=>{"use strict";e.exports=Transform;var s=r(1359);var a=r(5898);a.inherits=r(4124);a.inherits(Transform,s);function afterTransform(e,t){var r=this._transformState;r.transforming=false;var s=r.writecb;if(!s){return this.emit("error",new Error("write callback called multiple times"))}r.writechunk=null;r.writecb=null;if(t!=null)this.push(t);s(e);var a=this._readableState;a.reading=false;if(a.needReadable||a.length<a.highWaterMark){this._read(a.highWaterMark)}}function Transform(e){if(!(this instanceof Transform))return new Transform(e);s.call(this,e);this._transformState={afterTransform:afterTransform.bind(this),needTransform:false,transforming:false,writecb:null,writechunk:null,writeencoding:null};this._readableState.needReadable=true;this._readableState.sync=false;if(e){if(typeof e.transform==="function")this._transform=e.transform;if(typeof e.flush==="function")this._flush=e.flush}this.on("prefinish",prefinish)}function prefinish(){var e=this;if(typeof this._flush==="function"){this._flush((function(t,r){done(e,t,r)}))}else{done(this,null,null)}}Transform.prototype.push=function(e,t){this._transformState.needTransform=false;return s.prototype.push.call(this,e,t)};Transform.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")};Transform.prototype._write=function(e,t,r){var s=this._transformState;s.writecb=r;s.writechunk=e;s.writeencoding=t;if(!s.transforming){var a=this._readableState;if(s.needTransform||a.needReadable||a.length<a.highWaterMark)this._read(a.highWaterMark)}};Transform.prototype._read=function(e){var t=this._transformState;if(t.writechunk!==null&&t.writecb&&!t.transforming){t.transforming=true;this._transform(t.writechunk,t.writeencoding,t.afterTransform)}else{t.needTransform=true}};Transform.prototype._destroy=function(e,t){var r=this;s.prototype._destroy.call(this,e,(function(e){t(e);r.emit("close")}))};function done(e,t,r){if(t)return e.emit("error",t);if(r!=null)e.push(r);if(e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}},6993:(e,t,r)=>{"use strict";var s=r(7810);e.exports=Writable;function WriteReq(e,t,r){this.chunk=e;this.encoding=t;this.callback=r;this.next=null}function CorkedRequest(e){var t=this;this.next=null;this.entry=null;this.finish=function(){onCorkedFinish(t,e)}}var a=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:s.nextTick;var o;Writable.WritableState=WritableState;var u=r(5898);u.inherits=r(4124);var c={deprecate:r(5278)};var h=r(2387);var p=r(1867).Buffer;var d=global.Uint8Array||function(){};function _uint8ArrayToBuffer(e){return p.from(e)}function _isUint8Array(e){return p.isBuffer(e)||e instanceof d}var v=r(7049);u.inherits(Writable,h);function nop(){}function WritableState(e,t){o=o||r(1359);e=e||{};var s=t instanceof o;this.objectMode=!!e.objectMode;if(s)this.objectMode=this.objectMode||!!e.writableObjectMode;var a=e.highWaterMark;var u=e.writableHighWaterMark;var c=this.objectMode?16:16*1024;if(a||a===0)this.highWaterMark=a;else if(s&&(u||u===0))this.highWaterMark=u;else this.highWaterMark=c;this.highWaterMark=Math.floor(this.highWaterMark);this.finalCalled=false;this.needDrain=false;this.ending=false;this.ended=false;this.finished=false;this.destroyed=false;var h=e.decodeStrings===false;this.decodeStrings=!h;this.defaultEncoding=e.defaultEncoding||"utf8";this.length=0;this.writing=false;this.corked=0;this.sync=true;this.bufferProcessing=false;this.onwrite=function(e){onwrite(t,e)};this.writecb=null;this.writelen=0;this.bufferedRequest=null;this.lastBufferedRequest=null;this.pendingcb=0;this.prefinished=false;this.errorEmitted=false;this.bufferedRequestCount=0;this.corkedRequestsFree=new CorkedRequest(this)}WritableState.prototype.getBuffer=function getBuffer(){var e=this.bufferedRequest;var t=[];while(e){t.push(e);e=e.next}return t};(function(){try{Object.defineProperty(WritableState.prototype,"buffer",{get:c.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer "+"instead.","DEP0003")})}catch(e){}})();var m;if(typeof Symbol==="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]==="function"){m=Function.prototype[Symbol.hasInstance];Object.defineProperty(Writable,Symbol.hasInstance,{value:function(e){if(m.call(this,e))return true;if(this!==Writable)return false;return e&&e._writableState instanceof WritableState}})}else{m=function(e){return e instanceof this}}function Writable(e){o=o||r(1359);if(!m.call(Writable,this)&&!(this instanceof o)){return new Writable(e)}this._writableState=new WritableState(e,this);this.writable=true;if(e){if(typeof e.write==="function")this._write=e.write;if(typeof e.writev==="function")this._writev=e.writev;if(typeof e.destroy==="function")this._destroy=e.destroy;if(typeof e.final==="function")this._final=e.final}h.call(this)}Writable.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))};function writeAfterEnd(e,t){var r=new Error("write after end");e.emit("error",r);s.nextTick(t,r)}function validChunk(e,t,r,a){var o=true;var u=false;if(r===null){u=new TypeError("May not write null values to stream")}else if(typeof r!=="string"&&r!==undefined&&!t.objectMode){u=new TypeError("Invalid non-string/buffer chunk")}if(u){e.emit("error",u);s.nextTick(a,u);o=false}return o}Writable.prototype.write=function(e,t,r){var s=this._writableState;var a=false;var o=!s.objectMode&&_isUint8Array(e);if(o&&!p.isBuffer(e)){e=_uint8ArrayToBuffer(e)}if(typeof t==="function"){r=t;t=null}if(o)t="buffer";else if(!t)t=s.defaultEncoding;if(typeof r!=="function")r=nop;if(s.ended)writeAfterEnd(this,r);else if(o||validChunk(this,s,e,r)){s.pendingcb++;a=writeOrBuffer(this,s,o,e,t,r)}return a};Writable.prototype.cork=function(){var e=this._writableState;e.corked++};Writable.prototype.uncork=function(){var e=this._writableState;if(e.corked){e.corked--;if(!e.writing&&!e.corked&&!e.finished&&!e.bufferProcessing&&e.bufferedRequest)clearBuffer(this,e)}};Writable.prototype.setDefaultEncoding=function setDefaultEncoding(e){if(typeof e==="string")e=e.toLowerCase();if(!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);this._writableState.defaultEncoding=e;return this};function decodeChunk(e,t,r){if(!e.objectMode&&e.decodeStrings!==false&&typeof t==="string"){t=p.from(t,r)}return t}Object.defineProperty(Writable.prototype,"writableHighWaterMark",{enumerable:false,get:function(){return this._writableState.highWaterMark}});function writeOrBuffer(e,t,r,s,a,o){if(!r){var u=decodeChunk(t,s,a);if(s!==u){r=true;a="buffer";s=u}}var c=t.objectMode?1:s.length;t.length+=c;var h=t.length<t.highWaterMark;if(!h)t.needDrain=true;if(t.writing||t.corked){var p=t.lastBufferedRequest;t.lastBufferedRequest={chunk:s,encoding:a,isBuf:r,callback:o,next:null};if(p){p.next=t.lastBufferedRequest}else{t.bufferedRequest=t.lastBufferedRequest}t.bufferedRequestCount+=1}else{doWrite(e,t,false,c,s,a,o)}return h}function doWrite(e,t,r,s,a,o,u){t.writelen=s;t.writecb=u;t.writing=true;t.sync=true;if(r)e._writev(a,t.onwrite);else e._write(a,o,t.onwrite);t.sync=false}function onwriteError(e,t,r,a,o){--t.pendingcb;if(r){s.nextTick(o,a);s.nextTick(finishMaybe,e,t);e._writableState.errorEmitted=true;e.emit("error",a)}else{o(a);e._writableState.errorEmitted=true;e.emit("error",a);finishMaybe(e,t)}}function onwriteStateUpdate(e){e.writing=false;e.writecb=null;e.length-=e.writelen;e.writelen=0}function onwrite(e,t){var r=e._writableState;var s=r.sync;var o=r.writecb;onwriteStateUpdate(r);if(t)onwriteError(e,r,s,t,o);else{var u=needFinish(r);if(!u&&!r.corked&&!r.bufferProcessing&&r.bufferedRequest){clearBuffer(e,r)}if(s){a(afterWrite,e,r,u,o)}else{afterWrite(e,r,u,o)}}}function afterWrite(e,t,r,s){if(!r)onwriteDrain(e,t);t.pendingcb--;s();finishMaybe(e,t)}function onwriteDrain(e,t){if(t.length===0&&t.needDrain){t.needDrain=false;e.emit("drain")}}function clearBuffer(e,t){t.bufferProcessing=true;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var s=t.bufferedRequestCount;var a=new Array(s);var o=t.corkedRequestsFree;o.entry=r;var u=0;var c=true;while(r){a[u]=r;if(!r.isBuf)c=false;r=r.next;u+=1}a.allBuffers=c;doWrite(e,t,true,t.length,a,"",o.finish);t.pendingcb++;t.lastBufferedRequest=null;if(o.next){t.corkedRequestsFree=o.next;o.next=null}else{t.corkedRequestsFree=new CorkedRequest(t)}t.bufferedRequestCount=0}else{while(r){var h=r.chunk;var p=r.encoding;var d=r.callback;var v=t.objectMode?1:h.length;doWrite(e,t,false,v,h,p,d);r=r.next;t.bufferedRequestCount--;if(t.writing){break}}if(r===null)t.lastBufferedRequest=null}t.bufferedRequest=r;t.bufferProcessing=false}Writable.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))};Writable.prototype._writev=null;Writable.prototype.end=function(e,t,r){var s=this._writableState;if(typeof e==="function"){r=e;e=null;t=null}else if(typeof t==="function"){r=t;t=null}if(e!==null&&e!==undefined)this.write(e,t);if(s.corked){s.corked=1;this.uncork()}if(!s.ending&&!s.finished)endWritable(this,s,r)};function needFinish(e){return e.ending&&e.length===0&&e.bufferedRequest===null&&!e.finished&&!e.writing}function callFinal(e,t){e._final((function(r){t.pendingcb--;if(r){e.emit("error",r)}t.prefinished=true;e.emit("prefinish");finishMaybe(e,t)}))}function prefinish(e,t){if(!t.prefinished&&!t.finalCalled){if(typeof e._final==="function"){t.pendingcb++;t.finalCalled=true;s.nextTick(callFinal,e,t)}else{t.prefinished=true;e.emit("prefinish")}}}function finishMaybe(e,t){var r=needFinish(t);if(r){prefinish(e,t);if(t.pendingcb===0){t.finished=true;e.emit("finish")}}return r}function endWritable(e,t,r){t.ending=true;finishMaybe(e,t);if(r){if(t.finished)s.nextTick(r);else e.once("finish",r)}t.ended=true;e.writable=false}function onCorkedFinish(e,t,r){var s=e.entry;e.entry=null;while(s){var a=s.callback;t.pendingcb--;a(r);s=s.next}if(t.corkedRequestsFree){t.corkedRequestsFree.next=e}else{t.corkedRequestsFree=e}}Object.defineProperty(Writable.prototype,"destroyed",{get:function(){if(this._writableState===undefined){return false}return this._writableState.destroyed},set:function(e){if(!this._writableState){return}this._writableState.destroyed=e}});Writable.prototype.destroy=v.destroy;Writable.prototype._undestroy=v.undestroy;Writable.prototype._destroy=function(e,t){this.end();t(e)}},7053:(e,t,r)=>{"use strict";function _classCallCheck(e,t){if(!(e instanceof t)){throw new TypeError("Cannot call a class as a function")}}var s=r(1867).Buffer;var a=r(1669);function copyBuffer(e,t,r){e.copy(t,r)}e.exports=function(){function BufferList(){_classCallCheck(this,BufferList);this.head=null;this.tail=null;this.length=0}BufferList.prototype.push=function push(e){var t={data:e,next:null};if(this.length>0)this.tail.next=t;else this.head=t;this.tail=t;++this.length};BufferList.prototype.unshift=function unshift(e){var t={data:e,next:this.head};if(this.length===0)this.tail=t;this.head=t;++this.length};BufferList.prototype.shift=function shift(){if(this.length===0)return;var e=this.head.data;if(this.length===1)this.head=this.tail=null;else this.head=this.head.next;--this.length;return e};BufferList.prototype.clear=function clear(){this.head=this.tail=null;this.length=0};BufferList.prototype.join=function join(e){if(this.length===0)return"";var t=this.head;var r=""+t.data;while(t=t.next){r+=e+t.data}return r};BufferList.prototype.concat=function concat(e){if(this.length===0)return s.alloc(0);if(this.length===1)return this.head.data;var t=s.allocUnsafe(e>>>0);var r=this.head;var a=0;while(r){copyBuffer(r.data,t,a);a+=r.data.length;r=r.next}return t};return BufferList}();if(a&&a.inspect&&a.inspect.custom){e.exports.prototype[a.inspect.custom]=function(){var e=a.inspect({length:this.length});return this.constructor.name+" "+e}}},7049:(e,t,r)=>{"use strict";var s=r(7810);function destroy(e,t){var r=this;var a=this._readableState&&this._readableState.destroyed;var o=this._writableState&&this._writableState.destroyed;if(a||o){if(t){t(e)}else if(e&&(!this._writableState||!this._writableState.errorEmitted)){s.nextTick(emitErrorNT,this,e)}return this}if(this._readableState){this._readableState.destroyed=true}if(this._writableState){this._writableState.destroyed=true}this._destroy(e||null,(function(e){if(!t&&e){s.nextTick(emitErrorNT,r,e);if(r._writableState){r._writableState.errorEmitted=true}}else if(t){t(e)}}));return this}function undestroy(){if(this._readableState){this._readableState.destroyed=false;this._readableState.reading=false;this._readableState.ended=false;this._readableState.endEmitted=false}if(this._writableState){this._writableState.destroyed=false;this._writableState.ended=false;this._writableState.ending=false;this._writableState.finished=false;this._writableState.errorEmitted=false}}function emitErrorNT(e,t){e.emit("error",t)}e.exports={destroy:destroy,undestroy:undestroy}},2387:(e,t,r)=>{e.exports=r(2413)},1642:(e,t,r)=>{var s=r(2413);if(process.env.READABLE_STREAM==="disable"&&s){e.exports=s;t=e.exports=s.Readable;t.Readable=s.Readable;t.Writable=s.Writable;t.Duplex=s.Duplex;t.Transform=s.Transform;t.PassThrough=s.PassThrough;t.Stream=s}else{t=e.exports=r(1433);t.Stream=s||t;t.Readable=t;t.Writable=r(6993);t.Duplex=r(1359);t.Transform=r(4415);t.PassThrough=r(1542)}},9283:(e,t,r)=>{var s=r(6226);var a=r(2125);a.core=s;a.isCore=function isCore(e){return s[e]};a.sync=r(5284);t=a;e.exports=a},2125:(e,t,r)=>{var s=r(6226);var a=r(5747);var o=r(5622);var u=r(6155);var c=r(3265);var h=r(7990);var p=function isFile(e,t){a.stat(e,(function(e,r){if(!e){return t(null,r.isFile()||r.isFIFO())}if(e.code==="ENOENT"||e.code==="ENOTDIR")return t(null,false);return t(e)}))};e.exports=function resolve(e,t,r){var d=r;var v=t;if(typeof t==="function"){d=v;v={}}if(typeof e!=="string"){var m=new TypeError("Path must be a string.");return process.nextTick((function(){d(m)}))}v=h(e,v);var g=v.isFile||p;var y=v.readFile||a.readFile;var _=v.extensions||[".js"];var E=v.basedir||o.dirname(u());var x=v.filename||E;v.paths=v.paths||[];var w=o.resolve(E);if(v.preserveSymlinks===false){a.realpath(w,(function(e,t){if(e&&e.code!=="ENOENT")d(m);else init(e?w:t)}))}else{init(w)}var D;function init(t){if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){D=o.resolve(t,e);if(e===".."||e.slice(-1)==="/")D+="/";if(/\/$/.test(e)&&D===t){loadAsDirectory(D,v.package,onfile)}else loadAsFile(D,v.package,onfile)}else loadNodeModules(e,t,(function(t,r,a){if(t)d(t);else if(r)d(null,r,a);else if(s[e])return d(null,e);else{var o=new Error("Cannot find module '"+e+"' from '"+x+"'");o.code="MODULE_NOT_FOUND";d(o)}}))}function onfile(t,r,s){if(t)d(t);else if(r)d(null,r,s);else loadAsDirectory(D,(function(t,r,s){if(t)d(t);else if(r)d(null,r,s);else{var a=new Error("Cannot find module '"+e+"' from '"+x+"'");a.code="MODULE_NOT_FOUND";d(a)}}))}function loadAsFile(e,t,r){var s=t;var a=r;if(typeof s==="function"){a=s;s=undefined}var u=[""].concat(_);load(u,e,s);function load(e,t,r){if(e.length===0)return a(null,undefined,r);var s=t+e[0];var u=r;if(u)onpkg(null,u);else loadpkg(o.dirname(s),onpkg);function onpkg(r,c,h){u=c;if(r)return a(r);if(h&&u&&v.pathFilter){var p=o.relative(h,s);var d=p.slice(0,p.length-e[0].length);var m=v.pathFilter(u,t,d);if(m)return load([""].concat(_.slice()),o.resolve(h,m),u)}g(s,onex)}function onex(r,o){if(r)return a(r);if(o)return a(null,s,u);load(e.slice(1),t,u)}}}function loadpkg(e,t){if(e===""||e==="/")return t(null);if(process.platform==="win32"&&/^\w:[/\\]*$/.test(e)){return t(null)}if(/[/\\]node_modules[/\\]*$/.test(e))return t(null);var r=o.join(e,"package.json");g(r,(function(s,a){if(!a)return loadpkg(o.dirname(e),t);y(r,(function(s,a){if(s)t(s);try{var o=JSON.parse(a)}catch(e){}if(o&&v.packageFilter){o=v.packageFilter(o,r)}t(null,o,e)}))}))}function loadAsDirectory(e,t,r){var s=r;var a=t;if(typeof a==="function"){s=a;a=v.package}var u=o.join(e,"package.json");g(u,(function(t,r){if(t)return s(t);if(!r)return loadAsFile(o.join(e,"index"),a,s);y(u,(function(t,r){if(t)return s(t);try{var a=JSON.parse(r)}catch(e){}if(v.packageFilter){a=v.packageFilter(a,u)}if(a.main){if(typeof a.main!=="string"){var c=new TypeError("package “"+a.name+"” `main` must be a string");c.code="INVALID_PACKAGE_MAIN";return s(c)}if(a.main==="."||a.main==="./"){a.main="index"}loadAsFile(o.resolve(e,a.main),a,(function(t,r,a){if(t)return s(t);if(r)return s(null,r,a);if(!a)return loadAsFile(o.join(e,"index"),a,s);var u=o.resolve(e,a.main);loadAsDirectory(u,a,(function(t,r,a){if(t)return s(t);if(r)return s(null,r,a);loadAsFile(o.join(e,"index"),a,s)}))}));return}loadAsFile(o.join(e,"/index"),a,s)}))}))}function processDirs(t,r){if(r.length===0)return t(null,undefined);var s=r[0];var a=o.join(s,e);loadAsFile(a,v.package,onfile);function onfile(r,a,u){if(r)return t(r);if(a)return t(null,a,u);loadAsDirectory(o.join(s,e),v.package,ondir)}function ondir(e,s,a){if(e)return t(e);if(s)return t(null,s,a);processDirs(t,r.slice(1))}}function loadNodeModules(e,t,r){processDirs(r,c(t,v,e))}}},6155:e=>{e.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t};var t=(new Error).stack;Error.prepareStackTrace=e;return t[2].getFileName()}},6226:(e,t,r)=>{var s=process.versions&&process.versions.node&&process.versions.node.split(".")||[];function specifierIncluded(e){var t=e.split(" ");var r=t.length>1?t[0]:"=";var a=(t.length>1?t[1]:t[0]).split(".");for(var o=0;o<3;++o){var u=Number(s[o]||0);var c=Number(a[o]||0);if(u===c){continue}if(r==="<"){return u<c}else if(r===">="){return u>=c}else{return false}}return r===">="}function matchesRange(e){var t=e.split(/ ?&& ?/);if(t.length===0){return false}for(var r=0;r<t.length;++r){if(!specifierIncluded(t[r])){return false}}return true}function versionIncluded(e){if(typeof e==="boolean"){return e}if(e&&typeof e==="object"){for(var t=0;t<e.length;++t){if(matchesRange(e[t])){return true}}return false}return matchesRange(e)}var a=r(5537);var o={};for(var u in a){if(Object.prototype.hasOwnProperty.call(a,u)){o[u]=versionIncluded(a[u])}}e.exports=o},3265:(e,t,r)=>{var s=r(5622);var a=s.parse||r(5980);var o=function getNodeModulesDirs(e,t){var r="/";if(/^([A-Za-z]:)/.test(e)){r=""}else if(/^\\\\/.test(e)){r="\\\\"}var o=[e];var u=a(e);while(u.dir!==o[o.length-1]){o.push(u.dir);u=a(u.dir)}return o.reduce((function(e,a){return e.concat(t.map((function(e){return s.join(r,a,e)})))}),[])};e.exports=function nodeModulesPaths(e,t,r){var s=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"];if(t&&typeof t.paths==="function"){return t.paths(r,e,(function(){return o(e,s)}),t)}var a=o(e,s);return t&&t.paths?a.concat(t.paths):a}},7990:e=>{e.exports=function(e,t){return t||{}}},5284:(e,t,r)=>{var s=r(6226);var a=r(5747);var o=r(5622);var u=r(6155);var c=r(3265);var h=r(7990);var p=function isFile(e){try{var t=a.statSync(e)}catch(e){if(e&&(e.code==="ENOENT"||e.code==="ENOTDIR"))return false;throw e}return t.isFile()||t.isFIFO()};e.exports=function(e,t){if(typeof e!=="string"){throw new TypeError("Path must be a string.")}var r=h(e,t);var d=r.isFile||p;var v=r.readFileSync||a.readFileSync;var m=r.extensions||[".js"];var g=r.basedir||o.dirname(u());var y=r.filename||g;r.paths=r.paths||[];var _=o.resolve(g);if(r.preserveSymlinks===false){try{_=a.realpathSync(_)}catch(e){if(e.code!=="ENOENT"){throw e}}}if(/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var E=o.resolve(_,e);if(e===".."||e.slice(-1)==="/")E+="/";var x=loadAsFileSync(E)||loadAsDirectorySync(E);if(x)return x}else{var w=loadNodeModulesSync(e,_);if(w)return w}if(s[e])return e;var D=new Error("Cannot find module '"+e+"' from '"+y+"'");D.code="MODULE_NOT_FOUND";throw D;function loadAsFileSync(e){var t=loadpkg(o.dirname(e));if(t&&t.dir&&t.pkg&&r.pathFilter){var s=o.relative(t.dir,e);var a=r.pathFilter(t.pkg,e,s);if(a){e=o.resolve(t.dir,a)}}if(d(e)){return e}for(var u=0;u<m.length;u++){var c=e+m[u];if(d(c)){return c}}}function loadpkg(e){if(e===""||e==="/")return;if(process.platform==="win32"&&/^\w:[/\\]*$/.test(e)){return}if(/[/\\]node_modules[/\\]*$/.test(e))return;var t=o.join(e,"package.json");if(!d(t)){return loadpkg(o.dirname(e))}var s=v(t);try{var a=JSON.parse(s)}catch(e){}if(a&&r.packageFilter){a=r.packageFilter(a,e)}return{pkg:a,dir:e}}function loadAsDirectorySync(e){var t=o.join(e,"/package.json");if(d(t)){try{var s=v(t,"UTF8");var a=JSON.parse(s)}catch(e){}if(r.packageFilter){a=r.packageFilter(a,e)}if(a.main){if(typeof a.main!=="string"){var u=new TypeError("package “"+a.name+"” `main` must be a string");u.code="INVALID_PACKAGE_MAIN";throw u}if(a.main==="."||a.main==="./"){a.main="index"}try{var c=loadAsFileSync(o.resolve(e,a.main));if(c)return c;var h=loadAsDirectorySync(o.resolve(e,a.main));if(h)return h}catch(e){}}}return loadAsFileSync(o.join(e,"/index"))}function loadNodeModulesSync(e,t){var s=c(t,r,e);for(var a=0;a<s.length;a++){var u=s[a];var h=loadAsFileSync(o.join(u,"/",e));if(h)return h;var p=loadAsDirectorySync(o.join(u,"/",e));if(p)return p}}}},4959:(e,t,r)=>{e.exports=rimraf;rimraf.sync=rimrafSync;var s=r(2357);var a=r(5622);var o=r(5747);var u=r(1957);var c=parseInt("666",8);var h={nosort:true,silent:true};var p=0;var d=process.platform==="win32";function defaults(e){var t=["unlink","chmod","stat","lstat","rmdir","readdir"];t.forEach((function(t){e[t]=e[t]||o[t];t=t+"Sync";e[t]=e[t]||o[t]}));e.maxBusyTries=e.maxBusyTries||3;e.emfileWait=e.emfileWait||1e3;if(e.glob===false){e.disableGlob=true}e.disableGlob=e.disableGlob||false;e.glob=e.glob||h}function rimraf(e,t,r){if(typeof t==="function"){r=t;t={}}s(e,"rimraf: missing path");s.equal(typeof e,"string","rimraf: path should be a string");s.equal(typeof r,"function","rimraf: callback function required");s(t,"rimraf: invalid options argument provided");s.equal(typeof t,"object","rimraf: options should be object");defaults(t);var a=0;var o=null;var c=0;if(t.disableGlob||!u.hasMagic(e))return afterGlob(null,[e]);t.lstat(e,(function(r,s){if(!r)return afterGlob(null,[e]);u(e,t.glob,afterGlob)}));function next(e){o=o||e;if(--c===0)r(o)}function afterGlob(e,s){if(e)return r(e);c=s.length;if(c===0)return r();s.forEach((function(e){rimraf_(e,t,(function CB(r){if(r){if((r.code==="EBUSY"||r.code==="ENOTEMPTY"||r.code==="EPERM")&&a<t.maxBusyTries){a++;var s=a*100;return setTimeout((function(){rimraf_(e,t,CB)}),s)}if(r.code==="EMFILE"&&p<t.emfileWait){return setTimeout((function(){rimraf_(e,t,CB)}),p++)}if(r.code==="ENOENT")r=null}p=0;next(r)}))}))}}function rimraf_(e,t,r){s(e);s(t);s(typeof r==="function");t.lstat(e,(function(s,a){if(s&&s.code==="ENOENT")return r(null);if(s&&s.code==="EPERM"&&d)fixWinEPERM(e,t,s,r);if(a&&a.isDirectory())return rmdir(e,t,s,r);t.unlink(e,(function(s){if(s){if(s.code==="ENOENT")return r(null);if(s.code==="EPERM")return d?fixWinEPERM(e,t,s,r):rmdir(e,t,s,r);if(s.code==="EISDIR")return rmdir(e,t,s,r)}return r(s)}))}))}function fixWinEPERM(e,t,r,a){s(e);s(t);s(typeof a==="function");if(r)s(r instanceof Error);t.chmod(e,c,(function(s){if(s)a(s.code==="ENOENT"?null:r);else t.stat(e,(function(s,o){if(s)a(s.code==="ENOENT"?null:r);else if(o.isDirectory())rmdir(e,t,r,a);else t.unlink(e,a)}))}))}function fixWinEPERMSync(e,t,r){s(e);s(t);if(r)s(r instanceof Error);try{t.chmodSync(e,c)}catch(e){if(e.code==="ENOENT")return;else throw r}try{var a=t.statSync(e)}catch(e){if(e.code==="ENOENT")return;else throw r}if(a.isDirectory())rmdirSync(e,t,r);else t.unlinkSync(e)}function rmdir(e,t,r,a){s(e);s(t);if(r)s(r instanceof Error);s(typeof a==="function");t.rmdir(e,(function(s){if(s&&(s.code==="ENOTEMPTY"||s.code==="EEXIST"||s.code==="EPERM"))rmkids(e,t,a);else if(s&&s.code==="ENOTDIR")a(r);else a(s)}))}function rmkids(e,t,r){s(e);s(t);s(typeof r==="function");t.readdir(e,(function(s,o){if(s)return r(s);var u=o.length;if(u===0)return t.rmdir(e,r);var c;o.forEach((function(s){rimraf(a.join(e,s),t,(function(s){if(c)return;if(s)return r(c=s);if(--u===0)t.rmdir(e,r)}))}))}))}function rimrafSync(e,t){t=t||{};defaults(t);s(e,"rimraf: missing path");s.equal(typeof e,"string","rimraf: path should be a string");s(t,"rimraf: missing options");s.equal(typeof t,"object","rimraf: options should be object");var r;if(t.disableGlob||!u.hasMagic(e)){r=[e]}else{try{t.lstatSync(e);r=[e]}catch(s){r=u.sync(e,t.glob)}}if(!r.length)return;for(var a=0;a<r.length;a++){var e=r[a];try{var o=t.lstatSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM"&&d)fixWinEPERMSync(e,t,r)}try{if(o&&o.isDirectory())rmdirSync(e,t,null);else t.unlinkSync(e)}catch(r){if(r.code==="ENOENT")return;if(r.code==="EPERM")return d?fixWinEPERMSync(e,t,r):rmdirSync(e,t,r);if(r.code!=="EISDIR")throw r;rmdirSync(e,t,r)}}}function rmdirSync(e,t,r){s(e);s(t);if(r)s(r instanceof Error);try{t.rmdirSync(e)}catch(s){if(s.code==="ENOENT")return;if(s.code==="ENOTDIR")throw r;if(s.code==="ENOTEMPTY"||s.code==="EEXIST"||s.code==="EPERM")rmkidsSync(e,t)}}function rmkidsSync(e,t){s(e);s(t);t.readdirSync(e).forEach((function(r){rimrafSync(a.join(e,r),t)}));var r=d?100:1;var o=0;do{var u=true;try{var c=t.rmdirSync(e,t);u=false;return c}finally{if(++o<r&&u)continue}}while(true)}},5648:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:true});function _interopDefault(e){return e&&typeof e==="object"&&"default"in e?e["default"]:e}var s=r(5622);var a=_interopDefault(s);var o=r(6465);var u=_interopDefault(r(1669));const c=function addExtension(e,t=".js"){if(!s.extname(e))e+=t;return e};const h={ArrayPattern(e,t){for(const r of t.elements){if(r)h[r.type](e,r)}},AssignmentPattern(e,t){h[t.left.type](e,t.left)},Identifier(e,t){e.push(t.name)},MemberExpression(){},ObjectPattern(e,t){for(const r of t.properties){if(r.type==="RestElement"){h.RestElement(e,r)}else{h[r.value.type](e,r.value)}}},RestElement(e,t){h[t.argument.type](e,t.argument)}};const p=function extractAssignedNames(e){const t=[];h[e.type](t,e);return t};const d={const:true,let:true};class Scope{constructor(e={}){this.parent=e.parent;this.isBlockScope=!!e.block;this.declarations=Object.create(null);if(e.params){e.params.forEach((e=>{p(e).forEach((e=>{this.declarations[e]=true}))}))}}addDeclaration(e,t,r){if(!t&&this.isBlockScope){this.parent.addDeclaration(e,t,r)}else if(e.id){p(e.id).forEach((e=>{this.declarations[e]=true}))}}contains(e){return this.declarations[e]||(this.parent?this.parent.contains(e):false)}}const v=function attachScopes(e,t="scope"){let r=new Scope;o.walk(e,{enter(e,s){if(/(Function|Class)Declaration/.test(e.type)){r.addDeclaration(e,false,false)}if(e.type==="VariableDeclaration"){const t=e.kind;const s=d[t];e.declarations.forEach((e=>{r.addDeclaration(e,s,true)}))}let a;if(/Function/.test(e.type)){a=new Scope({parent:r,block:false,params:e.params});if(e.type==="FunctionExpression"&&e.id){a.addDeclaration(e,false,false)}}if(e.type==="BlockStatement"&&!/Function/.test(s.type)){a=new Scope({parent:r,block:true})}if(e.type==="CatchClause"){a=new Scope({parent:r,params:e.param?[e.param]:[],block:true})}if(a){Object.defineProperty(e,t,{value:a,configurable:true});r=a}},leave(e){if(e[t])r=r.parent}});return r};function createCommonjsModule(e,t){return t={exports:{}},e(t,t.exports),t.exports}var m=createCommonjsModule((function(e,t){t.isInteger=e=>{if(typeof e==="number"){return Number.isInteger(e)}if(typeof e==="string"&&e.trim()!==""){return Number.isInteger(Number(e))}return false};t.find=(e,t)=>e.nodes.find((e=>e.type===t));t.exceedsLimit=(e,r,s=1,a)=>{if(a===false)return false;if(!t.isInteger(e)||!t.isInteger(r))return false;return(Number(r)-Number(e))/Number(s)>=a};t.escapeNode=(e,t=0,r)=>{let s=e.nodes[t];if(!s)return;if(r&&s.type===r||s.type==="open"||s.type==="close"){if(s.escaped!==true){s.value="\\"+s.value;s.escaped=true}}};t.encloseBrace=e=>{if(e.type!=="brace")return false;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}return false};t.isInvalidBrace=e=>{if(e.type!=="brace")return false;if(e.invalid===true||e.dollar)return true;if(e.commas>>0+e.ranges>>0===0){e.invalid=true;return true}if(e.open!==true||e.close!==true){e.invalid=true;return true}return false};t.isOpenOrClose=e=>{if(e.type==="open"||e.type==="close"){return true}return e.open===true||e.close===true};t.reduce=e=>e.reduce(((e,t)=>{if(t.type==="text")e.push(t.value);if(t.type==="range")t.type="text";return e}),[]);t.flatten=(...e)=>{const t=[];const flat=e=>{for(let r=0;r<e.length;r++){let s=e[r];Array.isArray(s)?flat(s,t):s!==void 0&&t.push(s)}return t};flat(e);return t}}));var g=m.isInteger;var y=m.find;var _=m.exceedsLimit;var E=m.escapeNode;var x=m.encloseBrace;var w=m.isInvalidBrace;var D=m.isOpenOrClose;var C=m.reduce;var A=m.flatten;var stringify=(e,t={})=>{let stringify=(e,r={})=>{let s=t.escapeInvalid&&m.isInvalidBrace(r);let a=e.invalid===true&&t.escapeInvalid===true;let o="";if(e.value){if((s||a)&&m.isOpenOrClose(e)){return"\\"+e.value}return e.value}if(e.value){return e.value}if(e.nodes){for(let t of e.nodes){o+=stringify(t)}}return o};return stringify(e)};
/*!
 * is-number <https://github.com/jonschlinkert/is-number>
 *
 * Copyright (c) 2014-present, Jon Schlinkert.
 * Released under the MIT License.
 */var isNumber=function(e){if(typeof e==="number"){return e-e===0}if(typeof e==="string"&&e.trim()!==""){return Number.isFinite?Number.isFinite(+e):isFinite(+e)}return false};const toRegexRange=(e,t,r)=>{if(isNumber(e)===false){throw new TypeError("toRegexRange: expected the first argument to be a number")}if(t===void 0||e===t){return String(e)}if(isNumber(t)===false){throw new TypeError("toRegexRange: expected the second argument to be a number.")}let s=Object.assign({relaxZeros:true},r);if(typeof s.strictZeros==="boolean"){s.relaxZeros=s.strictZeros===false}let a=String(s.relaxZeros);let o=String(s.shorthand);let u=String(s.capture);let c=String(s.wrap);let h=e+":"+t+"="+a+o+u+c;if(toRegexRange.cache.hasOwnProperty(h)){return toRegexRange.cache[h].result}let p=Math.min(e,t);let d=Math.max(e,t);if(Math.abs(p-d)===1){let r=e+"|"+t;if(s.capture){return`(${r})`}if(s.wrap===false){return r}return`(?:${r})`}let v=hasPadding(e)||hasPadding(t);let m={min:e,max:t,a:p,b:d};let g=[];let y=[];if(v){m.isPadded=v;m.maxLen=String(m.max).length}if(p<0){let e=d<0?Math.abs(d):1;y=splitToPatterns(e,Math.abs(p),m,s);p=m.a=0}if(d>=0){g=splitToPatterns(p,d,m,s)}m.negatives=y;m.positives=g;m.result=collatePatterns(y,g,s);if(s.capture===true){m.result=`(${m.result})`}else if(s.wrap!==false&&g.length+y.length>1){m.result=`(?:${m.result})`}toRegexRange.cache[h]=m;return m.result};function collatePatterns(e,t,r){let s=filterPatterns(e,t,"-",false,r)||[];let a=filterPatterns(t,e,"",false,r)||[];let o=filterPatterns(e,t,"-?",true,r)||[];let u=s.concat(o).concat(a);return u.join("|")}function splitToRanges(e,t){let r=1;let s=1;let a=countNines(e,r);let o=new Set([t]);while(e<=a&&a<=t){o.add(a);r+=1;a=countNines(e,r)}a=countZeros(t+1,s)-1;while(e<a&&a<=t){o.add(a);s+=1;a=countZeros(t+1,s)-1}o=[...o];o.sort(compare);return o}function rangeToPattern(e,t,r){if(e===t){return{pattern:e,count:[],digits:0}}let s=zip(e,t);let a=s.length;let o="";let u=0;for(let e=0;e<a;e++){let[t,a]=s[e];if(t===a){o+=t}else if(t!=="0"||a!=="9"){o+=toCharacterClass(t,a,r)}else{u++}}if(u){o+=r.shorthand===true?"\\d":"[0-9]"}return{pattern:o,count:[u],digits:a}}function splitToPatterns(e,t,r,s){let a=splitToRanges(e,t);let o=[];let u=e;let c;for(let e=0;e<a.length;e++){let t=a[e];let h=rangeToPattern(String(u),String(t),s);let p="";if(!r.isPadded&&c&&c.pattern===h.pattern){if(c.count.length>1){c.count.pop()}c.count.push(h.count[0]);c.string=c.pattern+toQuantifier(c.count);u=t+1;continue}if(r.isPadded){p=padZeros(t,r,s)}h.string=p+h.pattern+toQuantifier(h.count);o.push(h);u=t+1;c=h}return o}function filterPatterns(e,t,r,s,a){let o=[];for(let a of e){let{string:e}=a;if(!s&&!contains(t,"string",e)){o.push(r+e)}if(s&&contains(t,"string",e)){o.push(r+e)}}return o}function zip(e,t){let r=[];for(let s=0;s<e.length;s++)r.push([e[s],t[s]]);return r}function compare(e,t){return e>t?1:t>e?-1:0}function contains(e,t,r){return e.some((e=>e[t]===r))}function countNines(e,t){return Number(String(e).slice(0,-t)+"9".repeat(t))}function countZeros(e,t){return e-e%Math.pow(10,t)}function toQuantifier(e){let[t=0,r=""]=e;if(r||t>1){return`{${t+(r?","+r:"")}}`}return""}function toCharacterClass(e,t,r){return`[${e}${t-e===1?"":"-"}${t}]`}function hasPadding(e){return/^-?(0+)\d/.test(e)}function padZeros(e,t,r){if(!t.isPadded){return e}let s=Math.abs(t.maxLen-String(e).length);let a=r.relaxZeros!==false;switch(s){case 0:return"";case 1:return a?"0?":"0";case 2:return a?"0{0,2}":"00";default:{return a?`0{0,${s}}`:`0{${s}}`}}}toRegexRange.cache={};toRegexRange.clearCache=()=>toRegexRange.cache={};var S=toRegexRange;const isObject=e=>e!==null&&typeof e==="object"&&!Array.isArray(e);const transform=e=>t=>e===true?Number(t):String(t);const isValidValue=e=>typeof e==="number"||typeof e==="string"&&e!=="";const isNumber$1=e=>Number.isInteger(+e);const zeros=e=>{let t=`${e}`;let r=-1;if(t[0]==="-")t=t.slice(1);if(t==="0")return false;while(t[++r]==="0");return r>0};const stringify$1=(e,t,r)=>{if(typeof e==="string"||typeof t==="string"){return true}return r.stringify===true};const pad=(e,t,r)=>{if(t>0){let r=e[0]==="-"?"-":"";if(r)e=e.slice(1);e=r+e.padStart(r?t-1:t,"0")}if(r===false){return String(e)}return e};const toMaxLen=(e,t)=>{let r=e[0]==="-"?"-":"";if(r){e=e.slice(1);t--}while(e.length<t)e="0"+e;return r?"-"+e:e};const toSequence=(e,t)=>{e.negatives.sort(((e,t)=>e<t?-1:e>t?1:0));e.positives.sort(((e,t)=>e<t?-1:e>t?1:0));let r=t.capture?"":"?:";let s="";let a="";let o;if(e.positives.length){s=e.positives.join("|")}if(e.negatives.length){a=`-(${r}${e.negatives.join("|")})`}if(s&&a){o=`${s}|${a}`}else{o=s||a}if(t.wrap){return`(${r}${o})`}return o};const toRange=(e,t,r,s)=>{if(r){return S(e,t,Object.assign({wrap:false},s))}let a=String.fromCharCode(e);if(e===t)return a;let o=String.fromCharCode(t);return`[${a}-${o}]`};const toRegex=(e,t,r)=>{if(Array.isArray(e)){let t=r.wrap===true;let s=r.capture?"":"?:";return t?`(${s}${e.join("|")})`:e.join("|")}return S(e,t,r)};const rangeError=(...e)=>new RangeError("Invalid range arguments: "+u.inspect(...e));const invalidRange=(e,t,r)=>{if(r.strictRanges===true)throw rangeError([e,t]);return[]};const invalidStep=(e,t)=>{if(t.strictRanges===true){throw new TypeError(`Expected step "${e}" to be a number`)}return[]};const fillNumbers=(e,t,r=1,s={})=>{let a=Number(e);let o=Number(t);if(!Number.isInteger(a)||!Number.isInteger(o)){if(s.strictRanges===true)throw rangeError([e,t]);return[]}if(a===0)a=0;if(o===0)o=0;let u=a>o;let c=String(e);let h=String(t);let p=String(r);r=Math.max(Math.abs(r),1);let d=zeros(c)||zeros(h)||zeros(p);let v=d?Math.max(c.length,h.length,p.length):0;let m=d===false&&stringify$1(e,t,s)===false;let g=s.transform||transform(m);if(s.toRegex&&r===1){return toRange(toMaxLen(e,v),toMaxLen(t,v),true,s)}let y={negatives:[],positives:[]};let push=e=>y[e<0?"negatives":"positives"].push(Math.abs(e));let _=[];let E=0;while(u?a>=o:a<=o){if(s.toRegex===true&&r>1){push(a)}else{_.push(pad(g(a,E),v,m))}a=u?a-r:a+r;E++}if(s.toRegex===true){return r>1?toSequence(y,s):toRegex(_,null,Object.assign({wrap:false},s))}return _};const fillLetters=(e,t,r=1,s={})=>{if(!isNumber$1(e)&&e.length>1||!isNumber$1(t)&&t.length>1){return invalidRange(e,t,s)}let a=s.transform||(e=>String.fromCharCode(e));let o=`${e}`.charCodeAt(0);let u=`${t}`.charCodeAt(0);let c=o>u;let h=Math.min(o,u);let p=Math.max(o,u);if(s.toRegex&&r===1){return toRange(h,p,false,s)}let d=[];let v=0;while(c?o>=u:o<=u){d.push(a(o,v));o=c?o-r:o+r;v++}if(s.toRegex===true){return toRegex(d,null,{wrap:false,options:s})}return d};const fill=(e,t,r,s={})=>{if(t==null&&isValidValue(e)){return[e]}if(!isValidValue(e)||!isValidValue(t)){return invalidRange(e,t,s)}if(typeof r==="function"){return fill(e,t,1,{transform:r})}if(isObject(r)){return fill(e,t,0,r)}let a=Object.assign({},s);if(a.capture===true)a.wrap=true;r=r||a.step||1;if(!isNumber$1(r)){if(r!=null&&!isObject(r))return invalidStep(r,a);return fill(e,t,1,r)}if(isNumber$1(e)&&isNumber$1(t)){return fillNumbers(e,t,r,a)}return fillLetters(e,t,Math.max(Math.abs(r),1),a)};var k=fill;const compile=(e,t={})=>{let walk=(e,r={})=>{let s=m.isInvalidBrace(r);let a=e.invalid===true&&t.escapeInvalid===true;let o=s===true||a===true;let u=t.escapeInvalid===true?"\\":"";let c="";if(e.isOpen===true){return u+e.value}if(e.isClose===true){return u+e.value}if(e.type==="open"){return o?u+e.value:"("}if(e.type==="close"){return o?u+e.value:")"}if(e.type==="comma"){return e.prev.type==="comma"?"":o?e.value:"|"}if(e.value){return e.value}if(e.nodes&&e.ranges>0){let r=m.reduce(e.nodes);let s=k(...r,Object.assign({},t,{wrap:false,toRegex:true}));if(s.length!==0){return r.length>1&&s.length>1?`(${s})`:s}}if(e.nodes){for(let t of e.nodes){c+=walk(t,e)}}return c};return walk(e)};var F=compile;const append=(e="",t="",r=false)=>{let s=[];e=[].concat(e);t=[].concat(t);if(!t.length)return e;if(!e.length){return r?m.flatten(t).map((e=>`{${e}}`)):t}for(let a of e){if(Array.isArray(a)){for(let e of a){s.push(append(e,t,r))}}else{for(let e of t){if(r===true&&typeof e==="string")e=`{${e}}`;s.push(Array.isArray(e)?append(a,e,r):a+e)}}}return m.flatten(s)};const expand=(e,t={})=>{let r=t.rangeLimit===void 0?1e3:t.rangeLimit;let walk=(e,s={})=>{e.queue=[];let a=s;let o=s.queue;while(a.type!=="brace"&&a.type!=="root"&&a.parent){a=a.parent;o=a.queue}if(e.invalid||e.dollar){o.push(append(o.pop(),stringify(e,t)));return}if(e.type==="brace"&&e.invalid!==true&&e.nodes.length===2){o.push(append(o.pop(),["{}"]));return}if(e.nodes&&e.ranges>0){let s=m.reduce(e.nodes);if(m.exceedsLimit(...s,t.step,r)){throw new RangeError("expanded array length exceeds range limit. Use options.rangeLimit to increase or disable the limit.")}let a=k(...s,t);if(a.length===0){a=stringify(e,t)}o.push(append(o.pop(),a));e.nodes=[];return}let u=m.encloseBrace(e);let c=e.queue;let h=e;while(h.type!=="brace"&&h.type!=="root"&&h.parent){h=h.parent;c=h.queue}for(let t=0;t<e.nodes.length;t++){let r=e.nodes[t];if(r.type==="comma"&&e.type==="brace"){if(t===1)c.push("");c.push("");continue}if(r.type==="close"){o.push(append(o.pop(),c,u));continue}if(r.value&&r.type!=="open"){c.push(append(c.pop(),r.value));continue}if(r.nodes){walk(r,e)}}return c};return m.flatten(walk(e))};var R=expand;var T={MAX_LENGTH:1024*64,CHAR_0:"0",CHAR_9:"9",CHAR_UPPERCASE_A:"A",CHAR_LOWERCASE_A:"a",CHAR_UPPERCASE_Z:"Z",CHAR_LOWERCASE_Z:"z",CHAR_LEFT_PARENTHESES:"(",CHAR_RIGHT_PARENTHESES:")",CHAR_ASTERISK:"*",CHAR_AMPERSAND:"&",CHAR_AT:"@",CHAR_BACKSLASH:"\\",CHAR_BACKTICK:"`",CHAR_CARRIAGE_RETURN:"\r",CHAR_CIRCUMFLEX_ACCENT:"^",CHAR_COLON:":",CHAR_COMMA:",",CHAR_DOLLAR:"$",CHAR_DOT:".",CHAR_DOUBLE_QUOTE:'"',CHAR_EQUAL:"=",CHAR_EXCLAMATION_MARK:"!",CHAR_FORM_FEED:"\f",CHAR_FORWARD_SLASH:"/",CHAR_HASH:"#",CHAR_HYPHEN_MINUS:"-",CHAR_LEFT_ANGLE_BRACKET:"<",CHAR_LEFT_CURLY_BRACE:"{",CHAR_LEFT_SQUARE_BRACKET:"[",CHAR_LINE_FEED:"\n",CHAR_NO_BREAK_SPACE:" ",CHAR_PERCENT:"%",CHAR_PLUS:"+",CHAR_QUESTION_MARK:"?",CHAR_RIGHT_ANGLE_BRACKET:">",CHAR_RIGHT_CURLY_BRACE:"}",CHAR_RIGHT_SQUARE_BRACKET:"]",CHAR_SEMICOLON:";",CHAR_SINGLE_QUOTE:"'",CHAR_SPACE:" ",CHAR_TAB:"\t",CHAR_UNDERSCORE:"_",CHAR_VERTICAL_LINE:"|",CHAR_ZERO_WIDTH_NOBREAK_SPACE:"\ufeff"};const{MAX_LENGTH:I,CHAR_BACKSLASH:B,CHAR_BACKTICK:N,CHAR_COMMA:O,CHAR_DOT:P,CHAR_LEFT_PARENTHESES:L,CHAR_RIGHT_PARENTHESES:j,CHAR_LEFT_CURLY_BRACE:M,CHAR_RIGHT_CURLY_BRACE:V,CHAR_LEFT_SQUARE_BRACKET:q,CHAR_RIGHT_SQUARE_BRACKET:U,CHAR_DOUBLE_QUOTE:$,CHAR_SINGLE_QUOTE:H,CHAR_NO_BREAK_SPACE:G,CHAR_ZERO_WIDTH_NOBREAK_SPACE:W}=T;const parse=(e,t={})=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}let r=t||{};let s=typeof r.maxLength==="number"?Math.min(I,r.maxLength):I;if(e.length>s){throw new SyntaxError(`Input length (${e.length}), exceeds max characters (${s})`)}let a={type:"root",input:e,nodes:[]};let o=[a];let u=a;let c=a;let h=0;let p=e.length;let d=0;let v=0;let m;const advance=()=>e[d++];const push=e=>{if(e.type==="text"&&c.type==="dot"){c.type="text"}if(c&&c.type==="text"&&e.type==="text"){c.value+=e.value;return}u.nodes.push(e);e.parent=u;e.prev=c;c=e;return e};push({type:"bos"});while(d<p){u=o[o.length-1];m=advance();if(m===W||m===G){continue}if(m===B){push({type:"text",value:(t.keepEscaping?m:"")+advance()});continue}if(m===U){push({type:"text",value:"\\"+m});continue}if(m===q){h++;let e;while(d<p&&(e=advance())){m+=e;if(e===q){h++;continue}if(e===B){m+=advance();continue}if(e===U){h--;if(h===0){break}}}push({type:"text",value:m});continue}if(m===L){u=push({type:"paren",nodes:[]});o.push(u);push({type:"text",value:m});continue}if(m===j){if(u.type!=="paren"){push({type:"text",value:m});continue}u=o.pop();push({type:"text",value:m});u=o[o.length-1];continue}if(m===$||m===H||m===N){let e=m;let r;if(t.keepQuotes!==true){m=""}while(d<p&&(r=advance())){if(r===B){m+=r+advance();continue}if(r===e){if(t.keepQuotes===true)m+=r;break}m+=r}push({type:"text",value:m});continue}if(m===M){v++;let e=c.value&&c.value.slice(-1)==="$"||u.dollar===true;let t={type:"brace",open:true,close:false,dollar:e,depth:v,commas:0,ranges:0,nodes:[]};u=push(t);o.push(u);push({type:"open",value:m});continue}if(m===V){if(u.type!=="brace"){push({type:"text",value:m});continue}let e="close";u=o.pop();u.close=true;push({type:e,value:m});v--;u=o[o.length-1];continue}if(m===O&&v>0){if(u.ranges>0){u.ranges=0;let e=u.nodes.shift();u.nodes=[e,{type:"text",value:stringify(u)}]}push({type:"comma",value:m});u.commas++;continue}if(m===P&&v>0&&u.commas===0){let e=u.nodes;if(v===0||e.length===0){push({type:"text",value:m});continue}if(c.type==="dot"){u.range=[];c.value+=m;c.type="range";if(u.nodes.length!==3&&u.nodes.length!==5){u.invalid=true;u.ranges=0;c.type="text";continue}u.ranges++;u.args=[];continue}if(c.type==="range"){e.pop();let t=e[e.length-1];t.value+=c.value+m;c=t;u.ranges--;continue}push({type:"dot",value:m});continue}push({type:"text",value:m})}do{u=o.pop();if(u.type!=="root"){u.nodes.forEach((e=>{if(!e.nodes){if(e.type==="open")e.isOpen=true;if(e.type==="close")e.isClose=true;if(!e.nodes)e.type="text";e.invalid=true}}));let e=o[o.length-1];let t=e.nodes.indexOf(u);e.nodes.splice(t,1,...u.nodes)}}while(o.length>0);push({type:"eos"});return a};var z=parse;const braces=(e,t={})=>{let r=[];if(Array.isArray(e)){for(let s of e){let e=braces.create(s,t);if(Array.isArray(e)){r.push(...e)}else{r.push(e)}}}else{r=[].concat(braces.create(e,t))}if(t&&t.expand===true&&t.nodupes===true){r=[...new Set(r)]}return r};braces.parse=(e,t={})=>z(e,t);braces.stringify=(e,t={})=>{if(typeof e==="string"){return stringify(braces.parse(e,t),t)}return stringify(e,t)};braces.compile=(e,t={})=>{if(typeof e==="string"){e=braces.parse(e,t)}return F(e,t)};braces.expand=(e,t={})=>{if(typeof e==="string"){e=braces.parse(e,t)}let r=R(e,t);if(t.noempty===true){r=r.filter(Boolean)}if(t.nodupes===true){r=[...new Set(r)]}return r};braces.create=(e,t={})=>{if(e===""||e.length<3){return[e]}return t.expand!==true?braces.compile(e,t):braces.expand(e,t)};var K=braces;const Q="\\\\/";const X=`[^${Q}]`;const J="\\.";const Z="\\+";const Y="\\?";const ee="\\/";const te="(?=.)";const re="[^/]";const ie=`(?:${ee}|$)`;const ne=`(?:^|${ee})`;const se=`${J}{1,2}${ie}`;const ae=`(?!${J})`;const oe=`(?!${ne}${se})`;const ue=`(?!${J}{0,1}${ie})`;const le=`(?!${se})`;const ce=`[^.${ee}]`;const fe=`${re}*?`;const he={DOT_LITERAL:J,PLUS_LITERAL:Z,QMARK_LITERAL:Y,SLASH_LITERAL:ee,ONE_CHAR:te,QMARK:re,END_ANCHOR:ie,DOTS_SLASH:se,NO_DOT:ae,NO_DOTS:oe,NO_DOT_SLASH:ue,NO_DOTS_SLASH:le,QMARK_NO_DOT:ce,STAR:fe,START_ANCHOR:ne};const pe=Object.assign({},he,{SLASH_LITERAL:`[${Q}]`,QMARK:X,STAR:`${X}*?`,DOTS_SLASH:`${J}{1,2}(?:[${Q}]|$)`,NO_DOT:`(?!${J})`,NO_DOTS:`(?!(?:^|[${Q}])${J}{1,2}(?:[${Q}]|$))`,NO_DOT_SLASH:`(?!${J}{0,1}(?:[${Q}]|$))`,NO_DOTS_SLASH:`(?!${J}{1,2}(?:[${Q}]|$))`,QMARK_NO_DOT:`[^.${Q}]`,START_ANCHOR:`(?:^|[${Q}])`,END_ANCHOR:`(?:[${Q}]|$)`});const de={alnum:"a-zA-Z0-9",alpha:"a-zA-Z",ascii:"\\x00-\\x7F",blank:" \\t",cntrl:"\\x00-\\x1F\\x7F",digit:"0-9",graph:"\\x21-\\x7E",lower:"a-z",print:"\\x20-\\x7E ",punct:"\\-!\"#$%&'()\\*+,./:;<=>?@[\\]^_`{|}~",space:" \\t\\r\\n\\v\\f",upper:"A-Z",word:"A-Za-z0-9_",xdigit:"A-Fa-f0-9"};var ve={MAX_LENGTH:1024*64,POSIX_REGEX_SOURCE:de,REGEX_BACKSLASH:/\\(?![*+?^${}(|)[\]])/g,REGEX_NON_SPECIAL_CHAR:/^[^@![\].,$*+?^{}()|\\/]+/,REGEX_SPECIAL_CHARS:/[-*+?.^${}(|)[\]]/,REGEX_SPECIAL_CHARS_BACKREF:/(\\?)((\W)(\3*))/g,REGEX_SPECIAL_CHARS_GLOBAL:/([-*+?.^${}(|)[\]])/g,REGEX_REMOVE_BACKSLASH:/(?:\[.*?[^\\]\]|\\(?=.))/g,REPLACEMENTS:{"***":"*","**/**":"**","**/**/**":"**"},CHAR_0:48,CHAR_9:57,CHAR_UPPERCASE_A:65,CHAR_LOWERCASE_A:97,CHAR_UPPERCASE_Z:90,CHAR_LOWERCASE_Z:122,CHAR_LEFT_PARENTHESES:40,CHAR_RIGHT_PARENTHESES:41,CHAR_ASTERISK:42,CHAR_AMPERSAND:38,CHAR_AT:64,CHAR_BACKWARD_SLASH:92,CHAR_CARRIAGE_RETURN:13,CHAR_CIRCUMFLEX_ACCENT:94,CHAR_COLON:58,CHAR_COMMA:44,CHAR_DOT:46,CHAR_DOUBLE_QUOTE:34,CHAR_EQUAL:61,CHAR_EXCLAMATION_MARK:33,CHAR_FORM_FEED:12,CHAR_FORWARD_SLASH:47,CHAR_GRAVE_ACCENT:96,CHAR_HASH:35,CHAR_HYPHEN_MINUS:45,CHAR_LEFT_ANGLE_BRACKET:60,CHAR_LEFT_CURLY_BRACE:123,CHAR_LEFT_SQUARE_BRACKET:91,CHAR_LINE_FEED:10,CHAR_NO_BREAK_SPACE:160,CHAR_PERCENT:37,CHAR_PLUS:43,CHAR_QUESTION_MARK:63,CHAR_RIGHT_ANGLE_BRACKET:62,CHAR_RIGHT_CURLY_BRACE:125,CHAR_RIGHT_SQUARE_BRACKET:93,CHAR_SEMICOLON:59,CHAR_SINGLE_QUOTE:39,CHAR_SPACE:32,CHAR_TAB:9,CHAR_UNDERSCORE:95,CHAR_VERTICAL_LINE:124,CHAR_ZERO_WIDTH_NOBREAK_SPACE:65279,SEP:a.sep,extglobChars(e){return{"!":{type:"negate",open:"(?:(?!(?:",close:`))${e.STAR})`},"?":{type:"qmark",open:"(?:",close:")?"},"+":{type:"plus",open:"(?:",close:")+"},"*":{type:"star",open:"(?:",close:")*"},"@":{type:"at",open:"(?:",close:")"}}},globChars(e){return e===true?pe:he}};var me=createCommonjsModule((function(e,t){const r=process.platform==="win32";const{REGEX_SPECIAL_CHARS:s,REGEX_SPECIAL_CHARS_GLOBAL:o,REGEX_REMOVE_BACKSLASH:u}=ve;t.isObject=e=>e!==null&&typeof e==="object"&&!Array.isArray(e);t.hasRegexChars=e=>s.test(e);t.isRegexChar=e=>e.length===1&&t.hasRegexChars(e);t.escapeRegex=e=>e.replace(o,"\\$1");t.toPosixSlashes=e=>e.replace(/\\/g,"/");t.removeBackslashes=e=>e.replace(u,(e=>e==="\\"?"":e));t.supportsLookbehinds=()=>{let e=process.version.slice(1).split(".");if(e.length===3&&+e[0]>=9||+e[0]===8&&+e[1]>=10){return true}return false};t.isWindows=e=>{if(e&&typeof e.windows==="boolean"){return e.windows}return r===true||a.sep==="\\"};t.escapeLast=(e,r,s)=>{let a=e.lastIndexOf(r,s);if(a===-1)return e;if(e[a-1]==="\\")return t.escapeLast(e,r,a-1);return e.slice(0,a)+"\\"+e.slice(a)}}));var ge=me.isObject;var be=me.hasRegexChars;var ye=me.isRegexChar;var _e=me.escapeRegex;var Ee=me.toPosixSlashes;var xe=me.removeBackslashes;var we=me.supportsLookbehinds;var De=me.isWindows;var Ce=me.escapeLast;const{CHAR_ASTERISK:Ae,CHAR_AT:Se,CHAR_BACKWARD_SLASH:ke,CHAR_COMMA:Fe,CHAR_DOT:Re,CHAR_EXCLAMATION_MARK:Te,CHAR_FORWARD_SLASH:Ie,CHAR_LEFT_CURLY_BRACE:Be,CHAR_LEFT_PARENTHESES:Ne,CHAR_LEFT_SQUARE_BRACKET:Oe,CHAR_PLUS:Pe,CHAR_QUESTION_MARK:Le,CHAR_RIGHT_CURLY_BRACE:je,CHAR_RIGHT_PARENTHESES:Me,CHAR_RIGHT_SQUARE_BRACKET:Ve}=ve;const isPathSeparator=e=>e===Ie||e===ke;var scan=(e,t)=>{let r=t||{};let s=e.length-1;let a=-1;let o=0;let u=0;let c=false;let h=false;let p=false;let d=0;let v;let m;let g=false;let eos=()=>a>=s;let advance=()=>{v=m;return e.charCodeAt(++a)};while(a<s){m=advance();let t;if(m===ke){h=true;t=advance();if(t===Be){g=true}continue}if(g===true||m===Be){d++;while(!eos()&&(t=advance())){if(t===ke){h=true;t=advance();continue}if(t===Be){d++;continue}if(!g&&t===Re&&(t=advance())===Re){c=true;break}if(!g&&t===Fe){c=true;break}if(t===je){d--;if(d===0){g=false;break}}}}if(m===Ie){if(v===Re&&a===o+1){o+=2;continue}u=a+1;continue}if(m===Ae){c=true;break}if(m===Ae||m===Le){c=true;break}if(m===Oe){while(!eos()&&(t=advance())){if(t===ke){h=true;t=advance();continue}if(t===Ve){c=true;break}}}let r=m===Pe||m===Se||m===Te;if(r&&e.charCodeAt(a+1)===Ne){c=true;break}if(m===Te&&a===o){p=true;o++;continue}if(m===Ne){while(!eos()&&(t=advance())){if(t===ke){h=true;t=advance();continue}if(t===Me){c=true;break}}}if(c){break}}let y="";let _=e;let E=e;let x="";if(o>0){y=e.slice(0,o);e=e.slice(o);u-=o}if(E&&c===true&&u>0){E=e.slice(0,u);x=e.slice(u)}else if(c===true){E="";x=e}else{E=e}if(E&&E!==""&&E!=="/"&&E!==e){if(isPathSeparator(E.charCodeAt(E.length-1))){E=E.slice(0,-1)}}if(r.unescape===true){if(x)x=me.removeBackslashes(x);if(E&&h===true){E=me.removeBackslashes(E)}}return{prefix:y,input:_,base:E,glob:x,negated:p,isGlob:c}};const{MAX_LENGTH:qe,POSIX_REGEX_SOURCE:Ue,REGEX_NON_SPECIAL_CHAR:$e,REGEX_SPECIAL_CHARS_BACKREF:He,REPLACEMENTS:Ge}=ve;const expandRange=(e,t)=>{if(typeof t.expandRange==="function"){return t.expandRange(...e,t)}e.sort();let r=`[${e.join("-")}]`;try{}catch(t){return e.map((e=>me.escapeRegex(e))).join("..")}return r};const negate=e=>{let t=1;while(e.peek()==="!"&&(e.peek(2)!=="("||e.peek(3)==="?")){e.advance();e.start++;t++}if(t%2===0){return false}e.negated=true;e.start++;return true};const syntaxError=(e,t)=>`Missing ${e}: "${t}" - use "\\\\${t}" to match literal characters`;const parse$1=(e,t)=>{if(typeof e!=="string"){throw new TypeError("Expected a string")}e=Ge[e]||e;let r=Object.assign({},t);let s=typeof r.maxLength==="number"?Math.min(qe,r.maxLength):qe;let a=e.length;if(a>s){throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${s}`)}let o={type:"bos",value:"",output:r.prepend||""};let u=[o];let c=r.capture?"":"?:";let h=me.isWindows(t);const p=ve.globChars(h);const d=ve.extglobChars(p);const{DOT_LITERAL:v,PLUS_LITERAL:m,SLASH_LITERAL:g,ONE_CHAR:y,DOTS_SLASH:_,NO_DOT:E,NO_DOT_SLASH:x,NO_DOTS_SLASH:w,QMARK:D,QMARK_NO_DOT:C,STAR:A,START_ANCHOR:S}=p;const globstar=e=>`(${c}(?:(?!${S}${e.dot?_:v}).)*?)`;let k=r.dot?"":E;let F=r.bash===true?globstar(r):A;let R=r.dot?D:C;if(r.capture){F=`(${F})`}if(typeof r.noext==="boolean"){r.noextglob=r.noext}let T={index:-1,start:0,consumed:"",output:"",backtrack:false,brackets:0,braces:0,parens:0,quotes:0,tokens:u};let I=[];let B=[];let N=o;let O;const eos=()=>T.index===a-1;const P=T.peek=(t=1)=>e[T.index+t];const L=T.advance=()=>e[++T.index];const append=e=>{T.output+=e.output!=null?e.output:e.value;T.consumed+=e.value||""};const increment=e=>{T[e]++;B.push(e)};const decrement=e=>{T[e]--;B.pop()};const push=e=>{if(N.type==="globstar"){let t=T.braces>0&&(e.type==="comma"||e.type==="brace");let r=I.length&&(e.type==="pipe"||e.type==="paren");if(e.type!=="slash"&&e.type!=="paren"&&!t&&!r){T.output=T.output.slice(0,-N.output.length);N.type="star";N.value="*";N.output=F;T.output+=N.output}}if(I.length&&e.type!=="paren"&&!d[e.value]){I[I.length-1].inner+=e.value}if(e.value||e.output)append(e);if(N&&N.type==="text"&&e.type==="text"){N.value+=e.value;return}e.prev=N;u.push(e);N=e};const extglobOpen=(e,t)=>{let s=Object.assign({},d[t],{conditions:1,inner:""});s.prev=N;s.parens=T.parens;s.output=T.output;let a=(r.capture?"(":"")+s.open;push({type:e,value:t,output:T.output?"":y});push({type:"paren",extglob:true,value:L(),output:a});increment("parens");I.push(s)};const extglobClose=t=>{let s=t.close+(r.capture?")":"");if(t.type==="negate"){let a=F;if(t.inner&&t.inner.length>1&&t.inner.includes("/")){a=globstar(r)}if(a!==F||eos()||/^\)+$/.test(e.slice(T.index+1))){s=t.close=")$))"+a}if(t.prev.type==="bos"&&eos()){T.negatedExtglob=true}}push({type:"paren",extglob:true,value:O,output:s});decrement("parens")};if(r.fastpaths!==false&&!/(^[*!]|[/{[()\]}"])/.test(e)){let t=false;let s=e.replace(He,((e,r,s,a,o,u)=>{if(a==="\\"){t=true;return e}if(a==="?"){if(r){return r+a+(o?D.repeat(o.length):"")}if(u===0){return R+(o?D.repeat(o.length):"")}return D.repeat(s.length)}if(a==="."){return v.repeat(s.length)}if(a==="*"){if(r){return r+a+(o?F:"")}return F}return r?e:"\\"+e}));if(t===true){if(r.unescape===true){s=s.replace(/\\/g,"")}else{s=s.replace(/\\+/g,(e=>e.length%2===0?"\\\\":e?"\\":""))}}T.output=s;return T}while(!eos()){O=L();if(O==="\0"){continue}if(O==="\\"){let t=P();if(t==="/"&&r.bash!==true){continue}if(t==="."||t===";"){continue}if(!t){O+="\\";push({type:"text",value:O});continue}let s=/^\\+/.exec(e.slice(T.index+1));let a=0;if(s&&s[0].length>2){a=s[0].length;T.index+=a;if(a%2!==0){O+="\\"}}if(r.unescape===true){O=L()||""}else{O+=L()||""}if(T.brackets===0){push({type:"text",value:O});continue}}if(T.brackets>0&&(O!=="]"||N.value==="["||N.value==="[^")){if(r.posix!==false&&O===":"){let e=N.value.slice(1);if(e.includes("[")){N.posix=true;if(e.includes(":")){let e=N.value.lastIndexOf("[");let t=N.value.slice(0,e);let r=N.value.slice(e+2);let s=Ue[r];if(s){N.value=t+s;T.backtrack=true;L();if(!o.output&&u.indexOf(N)===1){o.output=y}continue}}}}if(O==="["&&P()!==":"||O==="-"&&P()==="]"){O="\\"+O}if(O==="]"&&(N.value==="["||N.value==="[^")){O="\\"+O}if(r.posix===true&&O==="!"&&N.value==="["){O="^"}N.value+=O;append({value:O});continue}if(T.quotes===1&&O!=='"'){O=me.escapeRegex(O);N.value+=O;append({value:O});continue}if(O==='"'){T.quotes=T.quotes===1?0:1;if(r.keepQuotes===true){push({type:"text",value:O})}continue}if(O==="("){push({type:"paren",value:O});increment("parens");continue}if(O===")"){if(T.parens===0&&r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","("))}let e=I[I.length-1];if(e&&T.parens===e.parens+1){extglobClose(I.pop());continue}push({type:"paren",value:O,output:T.parens?")":"\\)"});decrement("parens");continue}if(O==="["){if(r.nobracket===true||!e.slice(T.index+1).includes("]")){if(r.nobracket!==true&&r.strictBrackets===true){throw new SyntaxError(syntaxError("closing","]"))}O="\\"+O}else{increment("brackets")}push({type:"bracket",value:O});continue}if(O==="]"){if(r.nobracket===true||N&&N.type==="bracket"&&N.value.length===1){push({type:"text",value:O,output:"\\"+O});continue}if(T.brackets===0){if(r.strictBrackets===true){throw new SyntaxError(syntaxError("opening","["))}push({type:"text",value:O,output:"\\"+O});continue}decrement("brackets");let e=N.value.slice(1);if(N.posix!==true&&e[0]==="^"&&!e.includes("/")){O="/"+O}N.value+=O;append({value:O});if(r.literalBrackets===false||me.hasRegexChars(e)){continue}let t=me.escapeRegex(N.value);T.output=T.output.slice(0,-N.value.length);if(r.literalBrackets===true){T.output+=t;N.value=t;continue}N.value=`(${c}${t}|${N.value})`;T.output+=N.value;continue}if(O==="{"&&r.nobrace!==true){push({type:"brace",value:O,output:"("});increment("braces");continue}if(O==="}"){if(r.nobrace===true||T.braces===0){push({type:"text",value:O,output:"\\"+O});continue}let e=")";if(T.dots===true){let t=u.slice();let s=[];for(let e=t.length-1;e>=0;e--){u.pop();if(t[e].type==="brace"){break}if(t[e].type!=="dots"){s.unshift(t[e].value)}}e=expandRange(s,r);T.backtrack=true}push({type:"brace",value:O,output:e});decrement("braces");continue}if(O==="|"){if(I.length>0){I[I.length-1].conditions++}push({type:"text",value:O});continue}if(O===","){let e=O;if(T.braces>0&&B[B.length-1]==="braces"){e="|"}push({type:"comma",value:O,output:e});continue}if(O==="/"){if(N.type==="dot"&&T.index===1){T.start=T.index+1;T.consumed="";T.output="";u.pop();N=o;continue}push({type:"slash",value:O,output:g});continue}if(O==="."){if(T.braces>0&&N.type==="dot"){if(N.value===".")N.output=v;N.type="dots";N.output+=O;N.value+=O;T.dots=true;continue}push({type:"dot",value:O,output:v});continue}if(O==="?"){if(N&&N.type==="paren"){let e=P();let t=O;if(e==="<"&&!me.supportsLookbehinds()){throw new Error("Node.js v10 or higher is required for regex lookbehinds")}if(N.value==="("&&!/[!=<:]/.test(e)||e==="<"&&!/[!=]/.test(P(2))){t="\\"+O}push({type:"text",value:O,output:t});continue}if(r.noextglob!==true&&P()==="("&&P(2)!=="?"){extglobOpen("qmark",O);continue}if(r.dot!==true&&(N.type==="slash"||N.type==="bos")){push({type:"qmark",value:O,output:C});continue}push({type:"qmark",value:O,output:D});continue}if(O==="!"){if(r.noextglob!==true&&P()==="("){if(P(2)!=="?"||!/[!=<:]/.test(P(3))){extglobOpen("negate",O);continue}}if(r.nonegate!==true&&T.index===0){negate(T);continue}}if(O==="+"){if(r.noextglob!==true&&P()==="("&&P(2)!=="?"){extglobOpen("plus",O);continue}if(N&&(N.type==="bracket"||N.type==="paren"||N.type==="brace")){let e=N.extglob===true?"\\"+O:O;push({type:"plus",value:O,output:e});continue}if(T.parens>0&&r.regex!==false){push({type:"plus",value:O});continue}push({type:"plus",value:m});continue}if(O==="@"){if(r.noextglob!==true&&P()==="("&&P(2)!=="?"){push({type:"at",value:O,output:""});continue}push({type:"text",value:O});continue}if(O!=="*"){if(O==="$"||O==="^"){O="\\"+O}let t=$e.exec(e.slice(T.index+1));if(t){O+=t[0];T.index+=t[0].length}push({type:"text",value:O});continue}if(N&&(N.type==="globstar"||N.star===true)){N.type="star";N.star=true;N.value+=O;N.output=F;T.backtrack=true;T.consumed+=O;continue}if(r.noextglob!==true&&P()==="("&&P(2)!=="?"){extglobOpen("star",O);continue}if(N.type==="star"){if(r.noglobstar===true){T.consumed+=O;continue}let t=N.prev;let s=t.prev;let a=t.type==="slash"||t.type==="bos";let o=s&&(s.type==="star"||s.type==="globstar");if(r.bash===true&&(!a||!eos()&&P()!=="/")){push({type:"star",value:O,output:""});continue}let u=T.braces>0&&(t.type==="comma"||t.type==="brace");let c=I.length&&(t.type==="pipe"||t.type==="paren");if(!a&&t.type!=="paren"&&!u&&!c){push({type:"star",value:O,output:""});continue}while(e.slice(T.index+1,T.index+4)==="/**"){let t=e[T.index+4];if(t&&t!=="/"){break}T.consumed+="/**";T.index+=3}if(t.type==="bos"&&eos()){N.type="globstar";N.value+=O;N.output=globstar(r);T.output=N.output;T.consumed+=O;continue}if(t.type==="slash"&&t.prev.type!=="bos"&&!o&&eos()){T.output=T.output.slice(0,-(t.output+N.output).length);t.output="(?:"+t.output;N.type="globstar";N.output=globstar(r)+"|$)";N.value+=O;T.output+=t.output+N.output;T.consumed+=O;continue}let h=P();if(t.type==="slash"&&t.prev.type!=="bos"&&h==="/"){let e=P(2)!==void 0?"|$":"";T.output=T.output.slice(0,-(t.output+N.output).length);t.output="(?:"+t.output;N.type="globstar";N.output=`${globstar(r)}${g}|${g}${e})`;N.value+=O;T.output+=t.output+N.output;T.consumed+=O+L();push({type:"slash",value:O,output:""});continue}if(t.type==="bos"&&h==="/"){N.type="globstar";N.value+=O;N.output=`(?:^|${g}|${globstar(r)}${g})`;T.output=N.output;T.consumed+=O+L();push({type:"slash",value:O,output:""});continue}T.output=T.output.slice(0,-N.output.length);N.type="globstar";N.output=globstar(r);N.value+=O;T.output+=N.output;T.consumed+=O;continue}let t={type:"star",value:O,output:F};if(r.bash===true){t.output=".*?";if(N.type==="bos"||N.type==="slash"){t.output=k+t.output}push(t);continue}if(N&&(N.type==="bracket"||N.type==="paren")&&r.regex===true){t.output=O;push(t);continue}if(T.index===T.start||N.type==="slash"||N.type==="dot"){if(N.type==="dot"){T.output+=x;N.output+=x}else if(r.dot===true){T.output+=w;N.output+=w}else{T.output+=k;N.output+=k}if(P()!=="*"){T.output+=y;N.output+=y}}push(t)}while(T.brackets>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","]"));T.output=me.escapeLast(T.output,"[");decrement("brackets")}while(T.parens>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing",")"));T.output=me.escapeLast(T.output,"(");decrement("parens")}while(T.braces>0){if(r.strictBrackets===true)throw new SyntaxError(syntaxError("closing","}"));T.output=me.escapeLast(T.output,"{");decrement("braces")}if(r.strictSlashes!==true&&(N.type==="star"||N.type==="bracket")){push({type:"maybe_slash",value:"",output:`${g}?`})}if(T.backtrack===true){T.output="";for(let e of T.tokens){T.output+=e.output!=null?e.output:e.value;if(e.suffix){T.output+=e.suffix}}}return T};parse$1.fastpaths=(e,t)=>{let r=Object.assign({},t);let s=typeof r.maxLength==="number"?Math.min(qe,r.maxLength):qe;let a=e.length;if(a>s){throw new SyntaxError(`Input length: ${a}, exceeds maximum allowed length: ${s}`)}e=Ge[e]||e;let o=me.isWindows(t);const{DOT_LITERAL:u,SLASH_LITERAL:c,ONE_CHAR:h,DOTS_SLASH:p,NO_DOT:d,NO_DOTS:v,NO_DOTS_SLASH:m,STAR:g,START_ANCHOR:y}=ve.globChars(o);let _=r.capture?"":"?:";let E=r.bash===true?".*?":g;let x=r.dot?v:d;let w=r.dot?m:d;if(r.capture){E=`(${E})`}const globstar=e=>`(${_}(?:(?!${y}${e.dot?p:u}).)*?)`;const create=e=>{switch(e){case"*":return`${x}${h}${E}`;case".*":return`${u}${h}${E}`;case"*.*":return`${x}${E}${u}${h}${E}`;case"*/*":return`${x}${E}${c}${h}${w}${E}`;case"**":return x+globstar(r);case"**/*":return`(?:${x}${globstar(r)}${c})?${w}${h}${E}`;case"**/*.*":return`(?:${x}${globstar(r)}${c})?${w}${E}${u}${h}${E}`;case"**/.*":return`(?:${x}${globstar(r)}${c})?${u}${h}${E}`;default:{let r=/^(.*?)\.(\w+)$/.exec(e);if(!r)return;let s=create(r[1],t);if(!s)return;return s+u+r[2]}}};let D=create(e);if(D&&r.strictSlashes!==true){D+=`${c}?`}return D};var We=parse$1;const picomatch=(e,t,r=false)=>{if(Array.isArray(e)){let s=e.map((e=>picomatch(e,t,r)));return e=>{for(let t of s){let r=t(e);if(r)return r}return false}}if(typeof e!=="string"||e===""){throw new TypeError("Expected pattern to be a non-empty string")}let s=t||{};let a=me.isWindows(t);let o=picomatch.makeRe(e,t,false,true);let u=o.state;delete o.state;let isIgnored=()=>false;if(s.ignore){let e=Object.assign({},t,{ignore:null,onMatch:null,onResult:null});isIgnored=picomatch(s.ignore,e,r)}const matcher=(r,c=false)=>{let{isMatch:h,match:p,output:d}=picomatch.test(r,o,t,{glob:e,posix:a});let v={glob:e,state:u,regex:o,posix:a,input:r,output:d,match:p,isMatch:h};if(typeof s.onResult==="function"){s.onResult(v)}if(h===false){v.isMatch=false;return c?v:false}if(isIgnored(r)){if(typeof s.onIgnore==="function"){s.onIgnore(v)}v.isMatch=false;return c?v:false}if(typeof s.onMatch==="function"){s.onMatch(v)}return c?v:true};if(r){matcher.state=u}return matcher};picomatch.test=(e,t,r,{glob:s,posix:a}={})=>{if(typeof e!=="string"){throw new TypeError("Expected input to be a string")}if(e===""){return{isMatch:false,output:""}}let o=r||{};let u=o.format||(a?me.toPosixSlashes:null);let c=e===s;let h=c&&u?u(e):e;if(c===false){h=u?u(e):e;c=h===s}if(c===false||o.capture===true){if(o.matchBase===true||o.basename===true){c=picomatch.matchBase(e,t,r,a)}else{c=t.exec(h)}}return{isMatch:!!c,match:c,output:h}};picomatch.matchBase=(e,t,r,s=me.isWindows(r))=>{let o=t instanceof RegExp?t:picomatch.makeRe(t,r);return o.test(a.basename(e))};picomatch.isMatch=(e,t,r)=>picomatch(t,r)(e);picomatch.parse=(e,t)=>We(e,t);picomatch.scan=(e,t)=>scan(e,t);picomatch.makeRe=(e,t,r=false,s=false)=>{if(!e||typeof e!=="string"){throw new TypeError("Expected a non-empty string")}let a=t||{};let o=a.contains?"":"^";let u=a.contains?"":"$";let c={negated:false,fastpaths:true};let h="";let p;if(e.startsWith("./")){e=e.slice(2);h=c.prefix="./"}if(a.fastpaths!==false&&(e[0]==="."||e[0]==="*")){p=We.fastpaths(e,t)}if(p===void 0){c=picomatch.parse(e,t);c.prefix=h+(c.prefix||"");p=c.output}if(r===true){return p}let d=`${o}(?:${p})${u}`;if(c&&c.negated===true){d=`^(?!${d}).*$`}let v=picomatch.toRegex(d,t);if(s===true){v.state=c}return v};picomatch.toRegex=(e,t)=>{try{let r=t||{};return new RegExp(e,r.flags||(r.nocase?"i":""))}catch(e){if(t&&t.debug===true)throw e;return/$^/}};picomatch.constants=ve;var ze=picomatch;var Ke=ze;const isEmptyString=e=>typeof e==="string"&&(e===""||e==="./");const micromatch=(e,t,r)=>{t=[].concat(t);e=[].concat(e);let s=new Set;let a=new Set;let o=new Set;let u=0;let onResult=e=>{o.add(e.output);if(r&&r.onResult){r.onResult(e)}};for(let o=0;o<t.length;o++){let c=Ke(String(t[o]),Object.assign({},r,{onResult:onResult}),true);let h=c.state.negated||c.state.negatedExtglob;if(h)u++;for(let t of e){let e=c(t,true);let r=h?!e.isMatch:e.isMatch;if(!r)continue;if(h){s.add(e.output)}else{s.delete(e.output);a.add(e.output)}}}let c=u===t.length?[...o]:[...a];let h=c.filter((e=>!s.has(e)));if(r&&h.length===0){if(r.failglob===true){throw new Error(`No matches found for "${t.join(", ")}"`)}if(r.nonull===true||r.nullglob===true){return r.unescape?t.map((e=>e.replace(/\\/g,""))):t}}return h};micromatch.match=micromatch;micromatch.matcher=(e,t)=>Ke(e,t);micromatch.isMatch=(e,t,r)=>Ke(t,r)(e);micromatch.any=micromatch.isMatch;micromatch.not=(e,t,r={})=>{t=[].concat(t).map(String);let s=new Set;let a=[];let onResult=e=>{if(r.onResult)r.onResult(e);a.push(e.output)};let o=micromatch(e,t,Object.assign({},r,{onResult:onResult}));for(let e of a){if(!o.includes(e)){s.add(e)}}return[...s]};micromatch.contains=(e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${u.inspect(e)}"`)}if(Array.isArray(t)){return t.some((t=>micromatch.contains(e,t,r)))}if(typeof t==="string"){if(isEmptyString(e)||isEmptyString(t)){return false}if(e.includes(t)||e.startsWith("./")&&e.slice(2).includes(t)){return true}}return micromatch.isMatch(e,t,Object.assign({},r,{contains:true}))};micromatch.matchKeys=(e,t,r)=>{if(!me.isObject(e)){throw new TypeError("Expected the first argument to be an object")}let s=micromatch(Object.keys(e),t,r);let a={};for(let t of s)a[t]=e[t];return a};micromatch.some=(e,t,r)=>{let s=[].concat(e);for(let e of[].concat(t)){let t=Ke(String(e),r);if(s.some((e=>t(e)))){return true}}return false};micromatch.every=(e,t,r)=>{let s=[].concat(e);for(let e of[].concat(t)){let t=Ke(String(e),r);if(!s.every((e=>t(e)))){return false}}return true};micromatch.all=(e,t,r)=>{if(typeof e!=="string"){throw new TypeError(`Expected a string: "${u.inspect(e)}"`)}return[].concat(t).every((t=>Ke(t,r)(e)))};micromatch.capture=(e,t,r)=>{let s=me.isWindows(r);let a=Ke.makeRe(String(e),Object.assign({},r,{capture:true}));let o=a.exec(s?me.toPosixSlashes(t):t);if(o){return o.slice(1).map((e=>e===void 0?"":e))}};micromatch.makeRe=(...e)=>Ke.makeRe(...e);micromatch.scan=(...e)=>Ke.scan(...e);micromatch.parse=(e,t)=>{let r=[];for(let s of[].concat(e||[])){for(let e of K(String(s),t)){r.push(Ke.parse(e,t))}}return r};micromatch.braces=(e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");if(t&&t.nobrace===true||!/\{.*\}/.test(e)){return[e]}return K(e,t)};micromatch.braceExpand=(e,t)=>{if(typeof e!=="string")throw new TypeError("Expected a string");return micromatch.braces(e,Object.assign({},t,{expand:true}))};var Qe=micromatch;function ensureArray(e){if(Array.isArray(e))return e;if(e==undefined)return[];return[e]}function getMatcherString(e,t){if(t===false){return e}return s.resolve(...typeof t==="string"?[t,e]:[e])}const Xe=function createFilter(e,t,r){const a=r&&r.resolve;const getMatcher=e=>e instanceof RegExp?e:{test:Qe.matcher(getMatcherString(e,a).split(s.sep).join("/"),{dot:true})};const o=ensureArray(e).map(getMatcher);const u=ensureArray(t).map(getMatcher);return function(e){if(typeof e!=="string")return false;if(/\0/.test(e))return false;e=e.split(s.sep).join("/");for(let t=0;t<u.length;++t){const r=u[t];if(r.test(e))return false}for(let t=0;t<o.length;++t){const r=o[t];if(r.test(e))return true}return!o.length}};const Je="break case class catch const continue debugger default delete do else export extends finally for function if import in instanceof let new return super switch this throw try typeof var void while with yield enum await implements package protected static interface private public";const Ze="arguments Infinity NaN undefined null true false eval uneval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Symbol Error EvalError InternalError RangeError ReferenceError SyntaxError TypeError URIError Number Math Date String RegExp Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array Map Set WeakMap WeakSet SIMD ArrayBuffer DataView JSON Promise Generator GeneratorFunction Reflect Proxy Intl";const Ye=new Set(`${Je} ${Ze}`.split(" "));Ye.add("");const et=function makeLegalIdentifier(e){e=e.replace(/-(\w)/g,((e,t)=>t.toUpperCase())).replace(/[^$_a-zA-Z0-9]/g,"_");if(/\d/.test(e[0])||Ye.has(e)){e=`_${e}`}return e||"_"};function stringify$2(e){return(JSON.stringify(e)||"undefined").replace(/[\u2028\u2029]/g,(e=>`\\u${("000"+e.charCodeAt(0).toString(16)).slice(-4)}`))}function serializeArray(e,t,r){let s="[";const a=t?"\n"+r+t:"";for(let o=0;o<e.length;o++){const u=e[o];s+=`${o>0?",":""}${a}${serialize(u,t,r+t)}`}return s+`${t?"\n"+r:""}]`}function serializeObject(e,t,r){let s="{";const a=t?"\n"+r+t:"";const o=Object.keys(e);for(let u=0;u<o.length;u++){const c=o[u];const h=et(c)===c?c:stringify$2(c);s+=`${u>0?",":""}${a}${h}:${t?" ":""}${serialize(e[c],t,r+t)}`}return s+`${t?"\n"+r:""}}`}function serialize(e,t,r){if(e===Infinity)return"Infinity";if(e===-Infinity)return"-Infinity";if(e===0&&1/e===-Infinity)return"-0";if(e instanceof Date)return"new Date("+e.getTime()+")";if(e instanceof RegExp)return e.toString();if(e!==e)return"NaN";if(Array.isArray(e))return serializeArray(e,t,r);if(e===null)return"null";if(typeof e==="object")return serializeObject(e,t,r);return stringify$2(e)}const tt=function dataToEsm(e,t={}){const r=t.compact?"":"indent"in t?t.indent:"\t";const s=t.compact?"":" ";const a=t.compact?"":"\n";const o=t.preferConst?"const":"var";if(t.namedExports===false||typeof e!=="object"||Array.isArray(e)||e instanceof Date||e instanceof RegExp||e===null){const a=serialize(e,t.compact?null:r,"");const o=s||(/^[{[\-\/]/.test(a)?"":" ");return`export default${o}${a};`}let u="";const c=[];const h=Object.keys(e);for(let p=0;p<h.length;p++){const d=h[p];if(d===et(d)){if(t.objectShorthand)c.push(d);else c.push(`${d}:${s}${d}`);u+=`export ${o} ${d}${s}=${s}${serialize(e[d],t.compact?null:r,"")};${a}`}else{c.push(`${stringify$2(d)}:${s}${serialize(e[d],t.compact?null:r,"")}`)}}return u+`export default${s}{${a}${r}${c.join(`,${a}${r}`)}${a}};${a}`};t.addExtension=c;t.attachScopes=v;t.createFilter=Xe;t.dataToEsm=tt;t.extractAssignedNames=p;t.makeLegalIdentifier=et},1867:(e,t,r)=>{var s=r(4293);var a=s.Buffer;function copyProps(e,t){for(var r in e){t[r]=e[r]}}if(a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow){e.exports=s}else{copyProps(s,t);t.Buffer=SafeBuffer}function SafeBuffer(e,t,r){return a(e,t,r)}copyProps(a,SafeBuffer);SafeBuffer.from=function(e,t,r){if(typeof e==="number"){throw new TypeError("Argument must not be a number")}return a(e,t,r)};SafeBuffer.alloc=function(e,t,r){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}var s=a(e);if(t!==undefined){if(typeof r==="string"){s.fill(t,r)}else{s.fill(t)}}else{s.fill(0)}return s};SafeBuffer.allocUnsafe=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return a(e)};SafeBuffer.allocUnsafeSlow=function(e){if(typeof e!=="number"){throw new TypeError("Argument must be a number")}return s.SlowBuffer(e)}},5911:(e,t)=>{t=e.exports=SemVer;var r;if(typeof process==="object"&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)){r=function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER");console.log.apply(console,e)}}else{r=function(){}}t.SEMVER_SPEC_VERSION="2.0.0";var s=256;var a=Number.MAX_SAFE_INTEGER||9007199254740991;var o=16;var u=t.re=[];var c=t.src=[];var h=0;var p=h++;c[p]="0|[1-9]\\d*";var d=h++;c[d]="[0-9]+";var v=h++;c[v]="\\d*[a-zA-Z-][a-zA-Z0-9-]*";var m=h++;c[m]="("+c[p]+")\\."+"("+c[p]+")\\."+"("+c[p]+")";var g=h++;c[g]="("+c[d]+")\\."+"("+c[d]+")\\."+"("+c[d]+")";var y=h++;c[y]="(?:"+c[p]+"|"+c[v]+")";var _=h++;c[_]="(?:"+c[d]+"|"+c[v]+")";var E=h++;c[E]="(?:-("+c[y]+"(?:\\."+c[y]+")*))";var x=h++;c[x]="(?:-?("+c[_]+"(?:\\."+c[_]+")*))";var w=h++;c[w]="[0-9A-Za-z-]+";var D=h++;c[D]="(?:\\+("+c[w]+"(?:\\."+c[w]+")*))";var C=h++;var A="v?"+c[m]+c[E]+"?"+c[D]+"?";c[C]="^"+A+"$";var S="[v=\\s]*"+c[g]+c[x]+"?"+c[D]+"?";var k=h++;c[k]="^"+S+"$";var F=h++;c[F]="((?:<|>)?=?)";var R=h++;c[R]=c[d]+"|x|X|\\*";var T=h++;c[T]=c[p]+"|x|X|\\*";var I=h++;c[I]="[v=\\s]*("+c[T]+")"+"(?:\\.("+c[T]+")"+"(?:\\.("+c[T]+")"+"(?:"+c[E]+")?"+c[D]+"?"+")?)?";var B=h++;c[B]="[v=\\s]*("+c[R]+")"+"(?:\\.("+c[R]+")"+"(?:\\.("+c[R]+")"+"(?:"+c[x]+")?"+c[D]+"?"+")?)?";var N=h++;c[N]="^"+c[F]+"\\s*"+c[I]+"$";var O=h++;c[O]="^"+c[F]+"\\s*"+c[B]+"$";var P=h++;c[P]="(?:^|[^\\d])"+"(\\d{1,"+o+"})"+"(?:\\.(\\d{1,"+o+"}))?"+"(?:\\.(\\d{1,"+o+"}))?"+"(?:$|[^\\d])";var L=h++;c[L]="(?:~>?)";var j=h++;c[j]="(\\s*)"+c[L]+"\\s+";u[j]=new RegExp(c[j],"g");var M="$1~";var V=h++;c[V]="^"+c[L]+c[I]+"$";var q=h++;c[q]="^"+c[L]+c[B]+"$";var U=h++;c[U]="(?:\\^)";var $=h++;c[$]="(\\s*)"+c[U]+"\\s+";u[$]=new RegExp(c[$],"g");var H="$1^";var G=h++;c[G]="^"+c[U]+c[I]+"$";var W=h++;c[W]="^"+c[U]+c[B]+"$";var z=h++;c[z]="^"+c[F]+"\\s*("+S+")$|^$";var K=h++;c[K]="^"+c[F]+"\\s*("+A+")$|^$";var Q=h++;c[Q]="(\\s*)"+c[F]+"\\s*("+S+"|"+c[I]+")";u[Q]=new RegExp(c[Q],"g");var X="$1$2$3";var J=h++;c[J]="^\\s*("+c[I]+")"+"\\s+-\\s+"+"("+c[I]+")"+"\\s*$";var Z=h++;c[Z]="^\\s*("+c[B]+")"+"\\s+-\\s+"+"("+c[B]+")"+"\\s*$";var Y=h++;c[Y]="(<|>)?=?\\s*\\*";for(var ee=0;ee<h;ee++){r(ee,c[ee]);if(!u[ee]){u[ee]=new RegExp(c[ee])}}t.parse=parse;function parse(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof SemVer){return e}if(typeof e!=="string"){return null}if(e.length>s){return null}var r=t.loose?u[k]:u[C];if(!r.test(e)){return null}try{return new SemVer(e,t)}catch(e){return null}}t.valid=valid;function valid(e,t){var r=parse(e,t);return r?r.version:null}t.clean=clean;function clean(e,t){var r=parse(e.trim().replace(/^[=v]+/,""),t);return r?r.version:null}t.SemVer=SemVer;function SemVer(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof SemVer){if(e.loose===t.loose){return e}else{e=e.version}}else if(typeof e!=="string"){throw new TypeError("Invalid Version: "+e)}if(e.length>s){throw new TypeError("version is longer than "+s+" characters")}if(!(this instanceof SemVer)){return new SemVer(e,t)}r("SemVer",e,t);this.options=t;this.loose=!!t.loose;var o=e.trim().match(t.loose?u[k]:u[C]);if(!o){throw new TypeError("Invalid Version: "+e)}this.raw=e;this.major=+o[1];this.minor=+o[2];this.patch=+o[3];if(this.major>a||this.major<0){throw new TypeError("Invalid major version")}if(this.minor>a||this.minor<0){throw new TypeError("Invalid minor version")}if(this.patch>a||this.patch<0){throw new TypeError("Invalid patch version")}if(!o[4]){this.prerelease=[]}else{this.prerelease=o[4].split(".").map((function(e){if(/^[0-9]+$/.test(e)){var t=+e;if(t>=0&&t<a){return t}}return e}))}this.build=o[5]?o[5].split("."):[];this.format()}SemVer.prototype.format=function(){this.version=this.major+"."+this.minor+"."+this.patch;if(this.prerelease.length){this.version+="-"+this.prerelease.join(".")}return this.version};SemVer.prototype.toString=function(){return this.version};SemVer.prototype.compare=function(e){r("SemVer.compare",this.version,this.options,e);if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}return this.compareMain(e)||this.comparePre(e)};SemVer.prototype.compareMain=function(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}return compareIdentifiers(this.major,e.major)||compareIdentifiers(this.minor,e.minor)||compareIdentifiers(this.patch,e.patch)};SemVer.prototype.comparePre=function(e){if(!(e instanceof SemVer)){e=new SemVer(e,this.options)}if(this.prerelease.length&&!e.prerelease.length){return-1}else if(!this.prerelease.length&&e.prerelease.length){return 1}else if(!this.prerelease.length&&!e.prerelease.length){return 0}var t=0;do{var s=this.prerelease[t];var a=e.prerelease[t];r("prerelease compare",t,s,a);if(s===undefined&&a===undefined){return 0}else if(a===undefined){return 1}else if(s===undefined){return-1}else if(s===a){continue}else{return compareIdentifiers(s,a)}}while(++t)};SemVer.prototype.inc=function(e,t){switch(e){case"premajor":this.prerelease.length=0;this.patch=0;this.minor=0;this.major++;this.inc("pre",t);break;case"preminor":this.prerelease.length=0;this.patch=0;this.minor++;this.inc("pre",t);break;case"prepatch":this.prerelease.length=0;this.inc("patch",t);this.inc("pre",t);break;case"prerelease":if(this.prerelease.length===0){this.inc("patch",t)}this.inc("pre",t);break;case"major":if(this.minor!==0||this.patch!==0||this.prerelease.length===0){this.major++}this.minor=0;this.patch=0;this.prerelease=[];break;case"minor":if(this.patch!==0||this.prerelease.length===0){this.minor++}this.patch=0;this.prerelease=[];break;case"patch":if(this.prerelease.length===0){this.patch++}this.prerelease=[];break;case"pre":if(this.prerelease.length===0){this.prerelease=[0]}else{var r=this.prerelease.length;while(--r>=0){if(typeof this.prerelease[r]==="number"){this.prerelease[r]++;r=-2}}if(r===-1){this.prerelease.push(0)}}if(t){if(this.prerelease[0]===t){if(isNaN(this.prerelease[1])){this.prerelease=[t,0]}}else{this.prerelease=[t,0]}}break;default:throw new Error("invalid increment argument: "+e)}this.format();this.raw=this.version;return this};t.inc=inc;function inc(e,t,r,s){if(typeof r==="string"){s=r;r=undefined}try{return new SemVer(e,r).inc(t,s).version}catch(e){return null}}t.diff=diff;function diff(e,t){if(eq(e,t)){return null}else{var r=parse(e);var s=parse(t);var a="";if(r.prerelease.length||s.prerelease.length){a="pre";var o="prerelease"}for(var u in r){if(u==="major"||u==="minor"||u==="patch"){if(r[u]!==s[u]){return a+u}}}return o}}t.compareIdentifiers=compareIdentifiers;var te=/^[0-9]+$/;function compareIdentifiers(e,t){var r=te.test(e);var s=te.test(t);if(r&&s){e=+e;t=+t}return e===t?0:r&&!s?-1:s&&!r?1:e<t?-1:1}t.rcompareIdentifiers=rcompareIdentifiers;function rcompareIdentifiers(e,t){return compareIdentifiers(t,e)}t.major=major;function major(e,t){return new SemVer(e,t).major}t.minor=minor;function minor(e,t){return new SemVer(e,t).minor}t.patch=patch;function patch(e,t){return new SemVer(e,t).patch}t.compare=compare;function compare(e,t,r){return new SemVer(e,r).compare(new SemVer(t,r))}t.compareLoose=compareLoose;function compareLoose(e,t){return compare(e,t,true)}t.rcompare=rcompare;function rcompare(e,t,r){return compare(t,e,r)}t.sort=sort;function sort(e,r){return e.sort((function(e,s){return t.compare(e,s,r)}))}t.rsort=rsort;function rsort(e,r){return e.sort((function(e,s){return t.rcompare(e,s,r)}))}t.gt=gt;function gt(e,t,r){return compare(e,t,r)>0}t.lt=lt;function lt(e,t,r){return compare(e,t,r)<0}t.eq=eq;function eq(e,t,r){return compare(e,t,r)===0}t.neq=neq;function neq(e,t,r){return compare(e,t,r)!==0}t.gte=gte;function gte(e,t,r){return compare(e,t,r)>=0}t.lte=lte;function lte(e,t,r){return compare(e,t,r)<=0}t.cmp=cmp;function cmp(e,t,r,s){switch(t){case"===":if(typeof e==="object")e=e.version;if(typeof r==="object")r=r.version;return e===r;case"!==":if(typeof e==="object")e=e.version;if(typeof r==="object")r=r.version;return e!==r;case"":case"=":case"==":return eq(e,r,s);case"!=":return neq(e,r,s);case">":return gt(e,r,s);case">=":return gte(e,r,s);case"<":return lt(e,r,s);case"<=":return lte(e,r,s);default:throw new TypeError("Invalid operator: "+t)}}t.Comparator=Comparator;function Comparator(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof Comparator){if(e.loose===!!t.loose){return e}else{e=e.value}}if(!(this instanceof Comparator)){return new Comparator(e,t)}r("comparator",e,t);this.options=t;this.loose=!!t.loose;this.parse(e);if(this.semver===re){this.value=""}else{this.value=this.operator+this.semver.version}r("comp",this)}var re={};Comparator.prototype.parse=function(e){var t=this.options.loose?u[z]:u[K];var r=e.match(t);if(!r){throw new TypeError("Invalid comparator: "+e)}this.operator=r[1];if(this.operator==="="){this.operator=""}if(!r[2]){this.semver=re}else{this.semver=new SemVer(r[2],this.options.loose)}};Comparator.prototype.toString=function(){return this.value};Comparator.prototype.test=function(e){r("Comparator.test",e,this.options.loose);if(this.semver===re){return true}if(typeof e==="string"){e=new SemVer(e,this.options)}return cmp(e,this.operator,this.semver,this.options)};Comparator.prototype.intersects=function(e,t){if(!(e instanceof Comparator)){throw new TypeError("a Comparator is required")}if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}var r;if(this.operator===""){r=new Range(e.value,t);return satisfies(this.value,r,t)}else if(e.operator===""){r=new Range(this.value,t);return satisfies(e.semver,r,t)}var s=(this.operator===">="||this.operator===">")&&(e.operator===">="||e.operator===">");var a=(this.operator==="<="||this.operator==="<")&&(e.operator==="<="||e.operator==="<");var o=this.semver.version===e.semver.version;var u=(this.operator===">="||this.operator==="<=")&&(e.operator===">="||e.operator==="<=");var c=cmp(this.semver,"<",e.semver,t)&&((this.operator===">="||this.operator===">")&&(e.operator==="<="||e.operator==="<"));var h=cmp(this.semver,">",e.semver,t)&&((this.operator==="<="||this.operator==="<")&&(e.operator===">="||e.operator===">"));return s||a||o&&u||c||h};t.Range=Range;function Range(e,t){if(!t||typeof t!=="object"){t={loose:!!t,includePrerelease:false}}if(e instanceof Range){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease){return e}else{return new Range(e.raw,t)}}if(e instanceof Comparator){return new Range(e.value,t)}if(!(this instanceof Range)){return new Range(e,t)}this.options=t;this.loose=!!t.loose;this.includePrerelease=!!t.includePrerelease;this.raw=e;this.set=e.split(/\s*\|\|\s*/).map((function(e){return this.parseRange(e.trim())}),this).filter((function(e){return e.length}));if(!this.set.length){throw new TypeError("Invalid SemVer Range: "+e)}this.format()}Range.prototype.format=function(){this.range=this.set.map((function(e){return e.join(" ").trim()})).join("||").trim();return this.range};Range.prototype.toString=function(){return this.range};Range.prototype.parseRange=function(e){var t=this.options.loose;e=e.trim();var s=t?u[Z]:u[J];e=e.replace(s,hyphenReplace);r("hyphen replace",e);e=e.replace(u[Q],X);r("comparator trim",e,u[Q]);e=e.replace(u[j],M);e=e.replace(u[$],H);e=e.split(/\s+/).join(" ");var a=t?u[z]:u[K];var o=e.split(" ").map((function(e){return parseComparator(e,this.options)}),this).join(" ").split(/\s+/);if(this.options.loose){o=o.filter((function(e){return!!e.match(a)}))}o=o.map((function(e){return new Comparator(e,this.options)}),this);return o};Range.prototype.intersects=function(e,t){if(!(e instanceof Range)){throw new TypeError("a Range is required")}return this.set.some((function(r){return r.every((function(r){return e.set.some((function(e){return e.every((function(e){return r.intersects(e,t)}))}))}))}))};t.toComparators=toComparators;function toComparators(e,t){return new Range(e,t).set.map((function(e){return e.map((function(e){return e.value})).join(" ").trim().split(" ")}))}function parseComparator(e,t){r("comp",e,t);e=replaceCarets(e,t);r("caret",e);e=replaceTildes(e,t);r("tildes",e);e=replaceXRanges(e,t);r("xrange",e);e=replaceStars(e,t);r("stars",e);return e}function isX(e){return!e||e.toLowerCase()==="x"||e==="*"}function replaceTildes(e,t){return e.trim().split(/\s+/).map((function(e){return replaceTilde(e,t)})).join(" ")}function replaceTilde(e,t){var s=t.loose?u[q]:u[V];return e.replace(s,(function(t,s,a,o,u){r("tilde",e,t,s,a,o,u);var c;if(isX(s)){c=""}else if(isX(a)){c=">="+s+".0.0 <"+(+s+1)+".0.0"}else if(isX(o)){c=">="+s+"."+a+".0 <"+s+"."+(+a+1)+".0"}else if(u){r("replaceTilde pr",u);c=">="+s+"."+a+"."+o+"-"+u+" <"+s+"."+(+a+1)+".0"}else{c=">="+s+"."+a+"."+o+" <"+s+"."+(+a+1)+".0"}r("tilde return",c);return c}))}function replaceCarets(e,t){return e.trim().split(/\s+/).map((function(e){return replaceCaret(e,t)})).join(" ")}function replaceCaret(e,t){r("caret",e,t);var s=t.loose?u[W]:u[G];return e.replace(s,(function(t,s,a,o,u){r("caret",e,t,s,a,o,u);var c;if(isX(s)){c=""}else if(isX(a)){c=">="+s+".0.0 <"+(+s+1)+".0.0"}else if(isX(o)){if(s==="0"){c=">="+s+"."+a+".0 <"+s+"."+(+a+1)+".0"}else{c=">="+s+"."+a+".0 <"+(+s+1)+".0.0"}}else if(u){r("replaceCaret pr",u);if(s==="0"){if(a==="0"){c=">="+s+"."+a+"."+o+"-"+u+" <"+s+"."+a+"."+(+o+1)}else{c=">="+s+"."+a+"."+o+"-"+u+" <"+s+"."+(+a+1)+".0"}}else{c=">="+s+"."+a+"."+o+"-"+u+" <"+(+s+1)+".0.0"}}else{r("no pr");if(s==="0"){if(a==="0"){c=">="+s+"."+a+"."+o+" <"+s+"."+a+"."+(+o+1)}else{c=">="+s+"."+a+"."+o+" <"+s+"."+(+a+1)+".0"}}else{c=">="+s+"."+a+"."+o+" <"+(+s+1)+".0.0"}}r("caret return",c);return c}))}function replaceXRanges(e,t){r("replaceXRanges",e,t);return e.split(/\s+/).map((function(e){return replaceXRange(e,t)})).join(" ")}function replaceXRange(e,t){e=e.trim();var s=t.loose?u[O]:u[N];return e.replace(s,(function(t,s,a,o,u,c){r("xRange",e,t,s,a,o,u,c);var h=isX(a);var p=h||isX(o);var d=p||isX(u);var v=d;if(s==="="&&v){s=""}if(h){if(s===">"||s==="<"){t="<0.0.0"}else{t="*"}}else if(s&&v){if(p){o=0}u=0;if(s===">"){s=">=";if(p){a=+a+1;o=0;u=0}else{o=+o+1;u=0}}else if(s==="<="){s="<";if(p){a=+a+1}else{o=+o+1}}t=s+a+"."+o+"."+u}else if(p){t=">="+a+".0.0 <"+(+a+1)+".0.0"}else if(d){t=">="+a+"."+o+".0 <"+a+"."+(+o+1)+".0"}r("xRange return",t);return t}))}function replaceStars(e,t){r("replaceStars",e,t);return e.trim().replace(u[Y],"")}function hyphenReplace(e,t,r,s,a,o,u,c,h,p,d,v,m){if(isX(r)){t=""}else if(isX(s)){t=">="+r+".0.0"}else if(isX(a)){t=">="+r+"."+s+".0"}else{t=">="+t}if(isX(h)){c=""}else if(isX(p)){c="<"+(+h+1)+".0.0"}else if(isX(d)){c="<"+h+"."+(+p+1)+".0"}else if(v){c="<="+h+"."+p+"."+d+"-"+v}else{c="<="+c}return(t+" "+c).trim()}Range.prototype.test=function(e){if(!e){return false}if(typeof e==="string"){e=new SemVer(e,this.options)}for(var t=0;t<this.set.length;t++){if(testSet(this.set[t],e,this.options)){return true}}return false};function testSet(e,t,s){for(var a=0;a<e.length;a++){if(!e[a].test(t)){return false}}if(t.prerelease.length&&!s.includePrerelease){for(a=0;a<e.length;a++){r(e[a].semver);if(e[a].semver===re){continue}if(e[a].semver.prerelease.length>0){var o=e[a].semver;if(o.major===t.major&&o.minor===t.minor&&o.patch===t.patch){return true}}}return false}return true}t.satisfies=satisfies;function satisfies(e,t,r){try{t=new Range(t,r)}catch(e){return false}return t.test(e)}t.maxSatisfying=maxSatisfying;function maxSatisfying(e,t,r){var s=null;var a=null;try{var o=new Range(t,r)}catch(e){return null}e.forEach((function(e){if(o.test(e)){if(!s||a.compare(e)===-1){s=e;a=new SemVer(s,r)}}}));return s}t.minSatisfying=minSatisfying;function minSatisfying(e,t,r){var s=null;var a=null;try{var o=new Range(t,r)}catch(e){return null}e.forEach((function(e){if(o.test(e)){if(!s||a.compare(e)===1){s=e;a=new SemVer(s,r)}}}));return s}t.minVersion=minVersion;function minVersion(e,t){e=new Range(e,t);var r=new SemVer("0.0.0");if(e.test(r)){return r}r=new SemVer("0.0.0-0");if(e.test(r)){return r}r=null;for(var s=0;s<e.set.length;++s){var a=e.set[s];a.forEach((function(e){var t=new SemVer(e.semver.version);switch(e.operator){case">":if(t.prerelease.length===0){t.patch++}else{t.prerelease.push(0)}t.raw=t.format();case"":case">=":if(!r||gt(r,t)){r=t}break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+e.operator)}}))}if(r&&e.test(r)){return r}return null}t.validRange=validRange;function validRange(e,t){try{return new Range(e,t).range||"*"}catch(e){return null}}t.ltr=ltr;function ltr(e,t,r){return outside(e,t,"<",r)}t.gtr=gtr;function gtr(e,t,r){return outside(e,t,">",r)}t.outside=outside;function outside(e,t,r,s){e=new SemVer(e,s);t=new Range(t,s);var a,o,u,c,h;switch(r){case">":a=gt;o=lte;u=lt;c=">";h=">=";break;case"<":a=lt;o=gte;u=gt;c="<";h="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(satisfies(e,t,s)){return false}for(var p=0;p<t.set.length;++p){var d=t.set[p];var v=null;var m=null;d.forEach((function(e){if(e.semver===re){e=new Comparator(">=0.0.0")}v=v||e;m=m||e;if(a(e.semver,v.semver,s)){v=e}else if(u(e.semver,m.semver,s)){m=e}}));if(v.operator===c||v.operator===h){return false}if((!m.operator||m.operator===c)&&o(e,m.semver)){return false}else if(m.operator===h&&u(e,m.semver)){return false}}return true}t.prerelease=prerelease;function prerelease(e,t){var r=parse(e,t);return r&&r.prerelease.length?r.prerelease:null}t.intersects=intersects;function intersects(e,t,r){e=new Range(e,r);t=new Range(t,r);return e.intersects(t)}t.coerce=coerce;function coerce(e){if(e instanceof SemVer){return e}if(typeof e!=="string"){return null}var t=e.match(u[P]);if(t==null){return null}return parse(t[1]+"."+(t[2]||"0")+"."+(t[3]||"0"))}},9344:e=>{e.exports=function(e){[process.stdout,process.stderr].forEach((function(t){if(t._handle&&t.isTTY&&typeof t._handle.setBlocking==="function"){t._handle.setBlocking(e)}}))}},4931:(e,t,r)=>{var s=r(2357);var a=r(3710);var o=r(8614);if(typeof o!=="function"){o=o.EventEmitter}var u;if(process.__signal_exit_emitter__){u=process.__signal_exit_emitter__}else{u=process.__signal_exit_emitter__=new o;u.count=0;u.emitted={}}if(!u.infinite){u.setMaxListeners(Infinity);u.infinite=true}e.exports=function(e,t){s.equal(typeof e,"function","a callback must be provided for exit handler");if(h===false){load()}var r="exit";if(t&&t.alwaysLast){r="afterexit"}var remove=function(){u.removeListener(r,e);if(u.listeners("exit").length===0&&u.listeners("afterexit").length===0){unload()}};u.on(r,e);return remove};e.exports.unload=unload;function unload(){if(!h){return}h=false;a.forEach((function(e){try{process.removeListener(e,c[e])}catch(e){}}));process.emit=d;process.reallyExit=p;u.count-=1}function emit(e,t,r){if(u.emitted[e]){return}u.emitted[e]=true;u.emit(e,t,r)}var c={};a.forEach((function(e){c[e]=function listener(){var t=process.listeners(e);if(t.length===u.count){unload();emit("exit",null,e);emit("afterexit",null,e);process.kill(process.pid,e)}}}));e.exports.signals=function(){return a};e.exports.load=load;var h=false;function load(){if(h){return}h=true;u.count+=1;a=a.filter((function(e){try{process.on(e,c[e]);return true}catch(e){return false}}));process.emit=processEmit;process.reallyExit=processReallyExit}var p=process.reallyExit;function processReallyExit(e){process.exitCode=e||0;emit("exit",process.exitCode,null);emit("afterexit",process.exitCode,null);p.call(process,process.exitCode)}var d=process.emit;function processEmit(e,t){if(e==="exit"){if(t!==undefined){process.exitCode=t}var r=d.apply(this,arguments);emit("exit",process.exitCode,null);emit("afterexit",process.exitCode,null);return r}else{return d.apply(this,arguments)}}},3710:e=>{e.exports=["SIGABRT","SIGALRM","SIGHUP","SIGINT","SIGTERM"];if(process.platform!=="win32"){e.exports.push("SIGVTALRM","SIGXCPU","SIGXFSZ","SIGUSR2","SIGTRAP","SIGSYS","SIGQUIT","SIGIOT")}if(process.platform==="linux"){e.exports.push("SIGIO","SIGPOLL","SIGPWR","SIGSTKFLT","SIGUNUSED")}},4957:function(e,t){(function(e,r){true?r(t):0})(this,(function(e){"use strict";var t={};var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/=";for(var s=0;s<r.length;s++){t[r.charCodeAt(s)]=s}function decode(e){var r=0;var s=0;var a=0;var o=0;var u=0;var c=[];var h=[];var p=[];for(var d=0,v=0,m=0,g=0,y=e.length;d<y;d++){var _=e.charCodeAt(d);if(_===44){if(p.length)h.push(new Int32Array(p));p=[];v=0}else if(_===59){if(p.length)h.push(new Int32Array(p));p=[];v=0;c.push(h);h=[];r=0}else{var E=t[_];if(E===undefined){throw new Error("Invalid character ("+String.fromCharCode(_)+")")}var x=E&32;E&=31;g+=E<<m;if(x){m+=5}else{var w=g&1;g>>=1;var D=w?-g:g;if(v==0){r+=D;p.push(r)}else if(v===1){s+=D;p.push(s)}else if(v===2){a+=D;p.push(a)}else if(v===3){o+=D;p.push(o)}else if(v===4){u+=D;p.push(u)}v++;g=m=0}}}if(p.length)h.push(new Int32Array(p));c.push(h);return c}function encode(e){var t=0;var r=0;var s=0;var a=0;var o="";for(var u=0;u<e.length;u++){var c=e[u];if(u>0)o+=";";if(c.length===0)continue;var h=0;var p=[];for(var d=0,v=c;d<v.length;d++){var m=v[d];var g=encodeInteger(m[0]-h);h=m[0];if(m.length>1){g+=encodeInteger(m[1]-t)+encodeInteger(m[2]-r)+encodeInteger(m[3]-s);t=m[1];r=m[2];s=m[3]}if(m.length===5){g+=encodeInteger(m[4]-a);a=m[4]}p.push(g)}o+=p.join(",")}return o}function encodeInteger(e){var t="";e=e<0?-e<<1|1:e<<1;do{var s=e&31;e>>=5;if(e>0){s|=32}t+=r[s]}while(e>0);return t}e.decode=decode;e.encode=encode;Object.defineProperty(e,"__esModule",{value:true})}))},2577:(e,t,r)=>{"use strict";const s=r(3520);const a=r(4882);e.exports=e=>{if(typeof e!=="string"||e.length===0){return 0}e=s(e);let t=0;for(let r=0;r<e.length;r++){const s=e.codePointAt(r);if(s<=31||s>=127&&s<=159){continue}if(s>=768&&s<=879){continue}if(s>65535){r++}t+=a(s)?2:1}return t}},9139:e=>{"use strict";e.exports=()=>{const e=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[a-zA-Z\\d]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PRZcf-ntqry=><~]))"].join("|");return new RegExp(e,"g")}},3520:(e,t,r)=>{"use strict";const s=r(9139);e.exports=e=>typeof e==="string"?e.replace(s(),""):e},4841:(e,t,r)=>{"use strict";var s=r(1867).Buffer;var a=s.isEncoding||function(e){e=""+e;switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return true;default:return false}};function _normalizeEncoding(e){if(!e)return"utf8";var t;while(true){switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase();t=true}}}function normalizeEncoding(e){var t=_normalizeEncoding(e);if(typeof t!=="string"&&(s.isEncoding===a||!a(e)))throw new Error("Unknown encoding: "+e);return t||e}t.s=StringDecoder;function StringDecoder(e){this.encoding=normalizeEncoding(e);var t;switch(this.encoding){case"utf16le":this.text=utf16Text;this.end=utf16End;t=4;break;case"utf8":this.fillLast=utf8FillLast;t=4;break;case"base64":this.text=base64Text;this.end=base64End;t=3;break;default:this.write=simpleWrite;this.end=simpleEnd;return}this.lastNeed=0;this.lastTotal=0;this.lastChar=s.allocUnsafe(t)}StringDecoder.prototype.write=function(e){if(e.length===0)return"";var t;var r;if(this.lastNeed){t=this.fillLast(e);if(t===undefined)return"";r=this.lastNeed;this.lastNeed=0}else{r=0}if(r<e.length)return t?t+this.text(e,r):this.text(e,r);return t||""};StringDecoder.prototype.end=utf8End;StringDecoder.prototype.text=utf8Text;StringDecoder.prototype.fillLast=function(e){if(this.lastNeed<=e.length){e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length);this.lastNeed-=e.length};function utf8CheckByte(e){if(e<=127)return 0;else if(e>>5===6)return 2;else if(e>>4===14)return 3;else if(e>>3===30)return 4;return e>>6===2?-1:-2}function utf8CheckIncomplete(e,t,r){var s=t.length-1;if(s<r)return 0;var a=utf8CheckByte(t[s]);if(a>=0){if(a>0)e.lastNeed=a-1;return a}if(--s<r||a===-2)return 0;a=utf8CheckByte(t[s]);if(a>=0){if(a>0)e.lastNeed=a-2;return a}if(--s<r||a===-2)return 0;a=utf8CheckByte(t[s]);if(a>=0){if(a>0){if(a===2)a=0;else e.lastNeed=a-3}return a}return 0}function utf8CheckExtraBytes(e,t,r){if((t[0]&192)!==128){e.lastNeed=0;return"�"}if(e.lastNeed>1&&t.length>1){if((t[1]&192)!==128){e.lastNeed=1;return"�"}if(e.lastNeed>2&&t.length>2){if((t[2]&192)!==128){e.lastNeed=2;return"�"}}}}function utf8FillLast(e){var t=this.lastTotal-this.lastNeed;var r=utf8CheckExtraBytes(this,e,t);if(r!==undefined)return r;if(this.lastNeed<=e.length){e.copy(this.lastChar,t,0,this.lastNeed);return this.lastChar.toString(this.encoding,0,this.lastTotal)}e.copy(this.lastChar,t,0,e.length);this.lastNeed-=e.length}function utf8Text(e,t){var r=utf8CheckIncomplete(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var s=e.length-(r-this.lastNeed);e.copy(this.lastChar,0,s);return e.toString("utf8",t,s)}function utf8End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+"�";return t}function utf16Text(e,t){if((e.length-t)%2===0){var r=e.toString("utf16le",t);if(r){var s=r.charCodeAt(r.length-1);if(s>=55296&&s<=56319){this.lastNeed=2;this.lastTotal=4;this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1];return r.slice(0,-1)}}return r}this.lastNeed=1;this.lastTotal=2;this.lastChar[0]=e[e.length-1];return e.toString("utf16le",t,e.length-1)}function utf16End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function base64Text(e,t){var r=(e.length-t)%3;if(r===0)return e.toString("base64",t);this.lastNeed=3-r;this.lastTotal=3;if(r===1){this.lastChar[0]=e[e.length-1]}else{this.lastChar[0]=e[e.length-2];this.lastChar[1]=e[e.length-1]}return e.toString("base64",t,e.length-r)}function base64End(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed)return t+this.lastChar.toString("base64",0,3-this.lastNeed);return t}function simpleWrite(e){return e.toString(this.encoding)}function simpleEnd(e){return e&&e.length?this.write(e):""}},5591:(e,t,r)=>{"use strict";var s=r(5465)();e.exports=function(e){return typeof e==="string"?e.replace(s,""):e}},5465:e=>{"use strict";e.exports=function(){return/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-PRZcf-nqry=><]/g}},5278:(e,t,r)=>{e.exports=r(1669).deprecate},8034:(e,t,r)=>{"use strict";var s=r(2577);t.center=alignCenter;t.left=alignLeft;t.right=alignRight;function createPadding(e){var t="";var r=" ";var s=e;do{if(s%2){t+=r}s=Math.floor(s/2);r+=r}while(s);return t}function alignLeft(e,t){var r=e.trimRight();if(r.length===0&&e.length>=t)return e;var a="";var o=s(r);if(o<t){a=createPadding(t-o)}return r+a}function alignRight(e,t){var r=e.trimLeft();if(r.length===0&&e.length>=t)return e;var a="";var o=s(r);if(o<t){a=createPadding(t-o)}return a+r}function alignCenter(e,t){var r=e.trim();if(r.length===0&&e.length>=t)return e;var a="";var o="";var u=s(r);if(u<t){var c=parseInt((t-u)/2,10);a=createPadding(c);o=createPadding(t-(u+c))}return a+r+o}},2940:e=>{e.exports=wrappy;function wrappy(e,t){if(e&&t)return wrappy(e)(t);if(typeof e!=="function")throw new TypeError("need wrapper function");Object.keys(e).forEach((function(t){wrapper[t]=e[t]}));return wrapper;function wrapper(){var t=new Array(arguments.length);for(var r=0;r<t.length;r++){t[r]=arguments[r]}var s=e.apply(this,t);var a=t[t.length-1];if(typeof s==="function"&&s!==a){Object.keys(a).forEach((function(e){s[e]=a[e]}))}return s}}},6265:(e,t,r)=>{const s=r(5622);const{readFileSync:a,readFile:o,stat:u,lstat:c,readlink:h,statSync:p}=r(7758);const{walk:d}=r(6465);const v=r(5734);const{attachScopes:m}=r(5648);const g=r(798);let y=r(390);const _=r(8384);const E=r(8357);const x=r(1331);const w=r(1957);const D=r(8011);const C=r(3953);const{pregyp:A,nbind:S}=r(5277);const k=r(9646);const{getOptions:F}=r(3432);const R=r(9283);const T=r(4723);const I=r(2087);const B=r(4090);const{pathToFileURL:N,fileURLToPath:O}=r(8835);y=y.Parser.extend(r(4259),r(104),r(9406));const P=[".js",".json",".node"];const{UNKNOWN:L,FUNCTION:j,WILDCARD:M,wildcardRegEx:V}=g;function isIdentifierRead(e,t){switch(t.type){case"ObjectPattern":case"ArrayPattern":return false;case"AssignmentExpression":return t.right===e;case"MemberExpression":return t.computed||e===t.object;case"Property":return e===t.value;case"MethodDefinition":return false;case"VariableDeclarator":return t.id!==e;case"ExportSpecifier":return false;case"FunctionExpression":case"FunctionDeclaration":case"ArrowFunctionExpression":return false;default:return true}}function isVarLoop(e){return e.type==="ForStatement"||e.type==="ForInStatement"||e.type==="ForOfStatement"}function isLoop(e){return e.type==="ForStatement"||e.type==="ForInStatement"||e.type==="ForOfStatement"||e.type==="WhileStatement"||e.type==="DoWhileStatement"}const q=new Map;let U;let $=0;function getAssetState(e,t){let r=q.get(t);if(!r){q.set(t,r={stateId:++$,entryIds:getEntryIds(t),assets:Object.create(null),assetNames:Object.create(null),assetMeta:Object.create(null),assetSymlinks:Object.create(null),hadOptions:false})}if(!r.hadOptions){r.hadOptions=true;if(e&&e.existingAssetNames){e.existingAssetNames.forEach((e=>{r.assetNames[e]=true}))}}return U=r}const flattenArray=e=>Array.prototype.concat.apply([],e);function getEntryIds(e){if(e.options.entry){if(typeof e.options.entry==="string"){try{return[R.sync(e.options.entry,{extensions:P})]}catch(e){return}}else if(typeof e.options.entry==="object"){try{return flattenArray(Object.values(e.options.entry).map((e=>{if(typeof e==="string"){return[e]}if(e&&Array.isArray(e.import)){return e.import}return[]}))).map((e=>R.sync(e,{extensions:P})))}catch(e){return}}}}function assetBase(e){if(!e)return"";if(e.endsWith("/")||e.endsWith("\\"))return e;return e+"/"}const H={cwd:()=>se,env:{NODE_ENV:L,[L]:true},[L]:true};const G=Symbol();const W=Symbol();const z=Symbol();const K=Symbol();const Q=Symbol();const X=Symbol();const J=Symbol();const Z={access:K,accessSync:K,createReadStream:K,exists:K,existsSync:K,fstat:K,fstatSync:K,lstat:K,lstatSync:K,open:K,readFile:K,readFileSync:K,stat:K,statSync:K};const Y=Object.assign(Object.create(null),{bindings:{default:X},express:{default:function(){return{[L]:true,set:G,engine:W}}},fs:{default:Z,...Z},process:{default:H,...H},path:{default:{}},os:{default:I,...I},"node-pre-gyp":A,"node-pre-gyp/lib/pre-binding":A,"node-pre-gyp/lib/pre-binding.js":A,"@mapbox/node-pre-gyp":A,"@mapbox/node-pre-gyp/lib/pre-binding":A,"@mapbox/node-pre-gyp/lib/pre-binding.js":A,"node-gyp-build":{default:J},nbind:{init:z,default:{init:z}},"resolve-from":{default:Q}});const ee={MONGOOSE_DRIVER_PATH:undefined,URL:URL};ee.global=ee.GLOBAL=ee.globalThis=ee;const te=Symbol();A.find[te]=true;const re=Y.path;Object.keys(s).forEach((e=>{const t=s[e];if(typeof t==="function"){const fn=function(){return t.apply(this,arguments)};fn[te]=true;re[e]=re.default[e]=fn}else{re[e]=re.default[e]=t}}));re.resolve=re.default.resolve=function(...e){return s.resolve.apply(this,[se,...e])};re.resolve[te]=true;const ie=new Set([".h",".cmake",".c",".cpp"]);const ne=new Set(["CHANGELOG.md","README.md","readme.md","changelog.md"]);let se;function backtrack(e,t){if(!t||t.type!=="ArrayExpression")return e.skip()}const ae=/^\/[^\/]+|^[a-z]:[\\/][^\\/]+/i;function isAbsolutePathOrUrl(e){if(e instanceof URL)return e.protocol==="file:";if(typeof e==="string"){if(e.startsWith("file:")){try{new URL(e);return true}catch{return false}}return ae.test(e)}return false}const oe=Symbol();function generateWildcardRequire(e,t,r,a,o){const u=a.length;const c=t.endsWith(M);const h=t.indexOf(M);const p=t.substr(0,h);const d=t.substr(h+1);const v=d?"?(.@(js|json|node))":".@(js|json|node)";if(o)console.log("Generating wildcard requires for "+t.replace(M,"*"));let m=w.sync(p+"**"+d+v,{mark:true,ignore:"node_modules/**/*"});if(!m.length)return;const g=m.map(((t,r)=>{const a=JSON.stringify(t.substring(p.length,t.lastIndexOf(d)));let o=s.relative(e,t).replace(/\\/g,"/");if(!o.startsWith("../"))o="./"+o;let u=r===0?"  ":"  else ";if(c&&a.endsWith('.js"'))u+=`if (arg === ${a} || arg === ${a.substr(0,a.length-4)}")`;else if(c&&a.endsWith('.json"'))u+=`if (arg === ${a} || arg === ${a.substr(0,a.length-6)}")`;else if(c&&a.endsWith('.node"'))u+=`if (arg === ${a} || arg === ${a.substr(0,a.length-6)}")`;else u+=`if (arg === ${a})`;u+=` return require(${JSON.stringify(o)});`;return u})).join("\n");a.push(`function __ncc_wildcard$${u} (arg) {\n${g}\n}`);return`__ncc_wildcard$${u}(${r})`}const ue=new WeakSet;function injectPathHook(e,t){const r=e.outputOptions.module;const{mainTemplate:a}=e;if(!ue.has(a)){ue.add(a);a.hooks.requireExtensions.tap("asset-relocator-loader",((e,a)=>{let o="";if(a.name){o=s.relative(s.dirname(a.name),".").replace(/\\/g,"/");if(o.length)o="/"+o}return`${e}\nif (typeof __webpack_require__ !== 'undefined') __webpack_require__.ab = ${r?"new URL('.', import.meta.url).pathname.slice(import.meta.url.match(/^file:\\/\\/\\/\\w:/) ? 1 : 0, -1)":"__dirname"} + ${JSON.stringify(o+"/"+assetBase(t))};`}))}}e.exports=async function(e,t){if(this.cacheable)this.cacheable();this.async();const r=this.resourcePath;const A=s.dirname(r);const T=F(this);injectPathHook(this._compilation,T.outputAssetBase);if(r.endsWith(".node")){const t=getAssetState(T,this._compilation);const s=D(this.resourcePath)||A;await x(s,t,assetBase(T.outputAssetBase),this.emitFile);let a;if(!(a=t.assets[r]))a=t.assets[r]=E(r.substr(s.length+1).replace(/\\/g,"/"),r,t.assetNames);const o=await new Promise(((e,t)=>u(r,((r,s)=>r?t(r):e(s.mode)))));t.assetMeta[a]={path:r,permissions:o};this.emitFile(assetBase(T.outputAssetBase)+a,e);this.callback(null,"module.exports = __non_webpack_require__(__webpack_require__.ab + "+JSON.stringify(a)+")");return}if(r.endsWith(".json"))return this.callback(null,I,t);let I=e.toString();if(typeof T.production==="boolean"&&H.env.NODE_ENV===L){H.env.NODE_ENV=T.production?"production":"dev"}if(!se){if(typeof T.cwd==="string")se=s.resolve(T.cwd);else se=process.cwd()}const q=getAssetState(T,this._compilation);const U=q.entryIds;const $=D(r);const emitAsset=e=>{let t=s.basename(e);if(e.endsWith(".node")){if($)t=e.substr($.length+1).replace(/\\/g,"/");const r=x($,q,assetBase(T.outputAssetBase),this.emitFile);Z=Z.then((()=>r))}let a;if(!(a=q.assets[e])){a=q.assets[e]=E(t,e,q.assetNames);if(T.debugLog)console.log("Emitting "+e+" for static use in module "+r)}Z=Z.then((async()=>{const[t,r]=await Promise.all([new Promise(((t,r)=>o(e,((e,s)=>e?r(e):t(s))))),await new Promise(((t,r)=>c(e,((e,s)=>e?r(e):t(s)))))]);if(r.isSymbolicLink()){const t=await new Promise(((t,r)=>{h(e,((e,s)=>e?r(e):t(s)))}));const r=s.dirname(e);q.assetSymlinks[assetBase(T.outputAssetBase)+a]=s.relative(r,s.resolve(r,t))}else{q.assetMeta[assetBase(T.outputAssetBase)+a]={path:e,permissions:r.mode};this.addDependency(e);this.emitFile(assetBase(T.outputAssetBase)+a,t)}}));return"__webpack_require__.ab + "+JSON.stringify(a).replace(/\\/g,"/")};const emitAssetDirectory=(e,t)=>{const a=e.indexOf(M);const u=a===-1?e.length:e.lastIndexOf(s.sep,a);const p=e.substr(0,u);const d=e.substr(u);const v=d.replace(V,((e,t)=>d[t-1]===s.sep?"**/*":"*/**/*"))||"/**/*";if(T.debugLog)console.log("Emitting directory "+p+v+" for static use in module "+r);const m=s.basename(p);const g=q.assets[p]||(q.assets[p]=E(m,p,q.assetNames,true));q.assets[p]=g;const y=w.sync(p+v,{mark:true,ignore:"node_modules/**/*"}).filter((e=>!ie.has(s.extname(e))&&!ne.has(s.basename(e))&&!e.endsWith("/")));if(!y.length)return;Z=Z.then((async()=>{await Promise.all(y.map((async e=>{const[t,r]=await Promise.all([new Promise(((t,r)=>o(e,((e,s)=>e?r(e):t(s))))),await new Promise(((t,r)=>c(e,((e,s)=>e?r(e):t(s)))))]);if(r.isSymbolicLink()){const t=await new Promise(((t,r)=>{h(e,((e,s)=>e?r(e):t(s)))}));const r=s.dirname(e);q.assetSymlinks[assetBase(T.outputAssetBase)+g+e.substr(p.length)]=s.relative(r,s.resolve(r,t)).replace(/\\/g,"/")}else{q.assetMeta[assetBase(T.outputAssetBase)+g+e.substr(p.length)]={path:e,permissions:r.mode};this.addDependency(e);this.emitFile(assetBase(T.outputAssetBase)+g+e.substr(p.length),t)}})))}));let _="";let x="";if(t){let e=d;let r=true;for(const s of t){const t=e.indexOf(M);const a=e.substr(0,t);e=e.substr(t+1);if(r){x=a;r=false}else{_+=" + '"+JSON.stringify(a).slice(1,-1)+"'"}if(s.type==="SpreadElement")_+=" + "+I.substring(s.argument.start,s.argument.end)+".join('/')";else _+=" + "+I.substring(s.start,s.end)}if(e.length){_+=" + '"+JSON.stringify(e).replace(/\\/g,"/").slice(1,-1)+"'"}}return"__webpack_require__.ab + "+JSON.stringify((g+x).replace(/\\/g,"/"))+_};let Z=Promise.resolve();const re=new v(I);let ue,le;try{ue=y.parse(I,{allowReturnOutsideFunction:true,ecmaVersion:2020});le=false}catch(e){}if(!ue){try{ue=y.parse(I,{sourceType:"module",ecmaVersion:2020,allowAwaitOutsideFunction:true});le=true}catch(e){return this.callback(null,I,t)}}let ce=m(ue,"scope");let fe=false;const he=N(r).href;const pe=Object.assign(Object.create(null),{__dirname:{shadowDepth:0,value:s.resolve(r,"..")},__filename:{shadowDepth:0,value:r},process:{shadowDepth:0,value:H}});if(!le){pe.require={shadowDepth:0,value:{[j](e){const t=Y[e];return t.default},resolve(e){return R.sync(e,{basedir:A,extensions:P})}}};pe.require.value.resolve[te]=true}let de=[];function setKnownBinding(e,t){if(e==="require")return;pe[e]={shadowDepth:0,value:t}}function getKnownBinding(e){const t=pe[e];if(t){if(t.shadowDepth===0){return t.value}}}if(le){for(const e of ue.body){if(e.type==="ImportDeclaration"){const t=e.source.value;const r=Y[t];if(r){for(const t of e.specifiers){if(t.type==="ImportNamespaceSpecifier")setKnownBinding(t.local.name,r);else if(t.type==="ImportDefaultSpecifier"&&"default"in r)setKnownBinding(t.local.name,r.default);else if(t.type==="ImportSpecifier"&&t.imported.name in r)setKnownBinding(t.local.name,r[t.imported.name])}}}}}function computePureStaticValue(e,t=true){const r=Object.create(null);Object.keys(pe).forEach((e=>{r[e]=getKnownBinding(e)}));Object.keys(ee).forEach((e=>{r[e]=ee[e]}));r["import.meta"]={url:he};const s=g(e,r,t);return s}let ve,me;let ge=false;let be;function isStaticRequire(e){return e&&e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="require"&&pe.require.shadowDepth===0&&e.arguments.length===1&&e.arguments[0].type==="Literal"}({ast:ue=ue,scope:ce=ce,transformed:fe=fe}=k({id:r,ast:ue,scope:ce,pkgBase:$,magicString:re,options:T,emitAsset:emitAsset,emitAssetDirectory:emitAssetDirectory})||{});d(ue,{enter(e,t){if(e.scope){ce=e.scope;for(const t in e.scope.declarations){if(t in pe)pe[t].shadowDepth++}}if(ve)return backtrack(this,t);if(e.type==="Identifier"){if(isIdentifierRead(e,t)){let r;if(typeof(r=getKnownBinding(e.name))==="string"&&r.match(ae)||r&&(typeof r==="function"||typeof r==="object")&&r[te]){me={value:typeof r==="string"?r:undefined};ve=e;return this.skip()}else if(!le&&e.name==="require"&&pe.require.shadowDepth===0&&t.type!=="UnaryExpression"){re.overwrite(e.start,e.end,"__non_webpack_require__");fe=true;return this.skip()}else if(!le&&e.name==="__non_webpack_require__"&&t.type!=="UnaryExpression"){re.overwrite(e.start,e.end,'eval("require")');fe=true;return this.skip()}}}else if(e.type==="MemberExpression"&&e.object.type==="MetaProperty"&&e.object.meta.name==="import"&&e.object.property.name==="meta"&&(e.property.computed?e.property.value:e.property.name)==="url"){me={value:he};ve=e;return this.skip()}else if(!le&&e.type==="CallExpression"&&e.callee.type==="Identifier"&&e.callee.name==="require"&&pe.require.shadowDepth===0&&e.arguments.length){const c=e.arguments[0];const{result:h,sawIdentifier:p}=computePureStaticValue(c,true);if(!h){if(c.type==="LogicalExpression"&&c.operator==="||"&&c.left.type==="Identifier"){fe=true;re.overwrite(c.start,c.end,I.substring(c.right.start,c.right.end));return this.skip()}fe=true;re.overwrite(e.callee.start,e.callee.end,"__non_webpack_require__");return this.skip()}else if(typeof h.value==="string"&&p){if(h.wildcards){const t=s.resolve(A,h.value);if(h.wildcards.length===1&&validAssetEmission(t)){const r=generateWildcardRequire(A,t,I.substring(h.wildcards[0].start,h.wildcards[0].end),de,T.debugLog);if(r){re.overwrite(e.start,e.end,r);fe=true;return this.skip()}}}else if(h.value){let e;if(T.customEmit)e=T.customEmit(h.value,true);if(e===undefined)e=JSON.stringify(h.value);if(e!==false){re.overwrite(c.start,c.end,e);fe=true;return this.skip()}}}else if(h&&typeof h.then==="string"&&typeof h.else==="string"&&p){const e=computePureStaticValue(h.test,true).result;if(e&&"value"in e){if(e){fe=true;re.overwrite(c.start,c.end,JSON.stringify(h.then));return this.skip()}else{fe=true;re.overwrite(c.start,c.end,JSON.stringify(h.else));return this.skip()}}else{const e=I.substring(h.test.start,h.test.end);fe=true;re.overwrite(c.start,c.end,`${e} ? ${JSON.stringify(h.then)} : ${JSON.stringify(h.else)}`);return this.skip()}}else if(t.type==="CallExpression"&&t.callee===e){if(h.value==="pkginfo"&&t.arguments.length&&t.arguments[0].type==="Identifier"&&t.arguments[0].name==="module"){let e=new Set;for(let r=1;r<t.arguments.length;r++){if(t.arguments[r].type==="Literal")e.add(t.arguments[r].value)}const s=C(r);if(s){try{var o=JSON.parse(a(s+"/package.json"));if(e.size){for(var u in o){if(!e.has(u))delete o[u]}}}catch(e){}if(o){fe=true;re.overwrite(t.start,t.end,`Object.assign(module.exports, ${JSON.stringify(o)})`);return this.skip()}}}return this.skip()}else{if(typeof h.value==="string"&&T.customEmit){const t=T.customEmit(h.value,{id:r,isRequire:true});if(t){re.overwrite(e.start,e.end,"__non_webpack_require__("+t+")");fe=true;return this.skip()}}return this.skip()}}else if(!le&&e.type==="MemberExpression"&&e.object.type==="Identifier"&&e.object.name==="require"&&pe.require.shadowDepth===0&&e.property.type==="Identifier"&&!e.computed){if(e.property.name==="main"&&t&&t.type==="BinaryExpression"&&(t.operator==="=="||t.operator==="===")){let s;s=t.right===e?t.left:t.right;if(s.type==="Identifier"&&s.name==="module"){if(U&&U.indexOf(r)!==-1){re.overwrite(s.start,s.end,"__non_webpack_require__.cache[eval('__filename')]")}else{re.overwrite(t.start,t.end,"false");fe=true;return this.skip()}}}if(e.property.name==="ensure"){return this.skip()}}else if(!le&&e.type==="MemberExpression"&&e.object.type==="Identifier"&&e.object.name==="module"&&"module"in pe===false&&e.property.type==="Identifier"&&!e.computed&&e.property.name==="require"){re.overwrite(e.start,e.end,"require");e.type="Identifier";e.name="require";fe=true}else if(e.type==="CallExpression"){const r=computePureStaticValue(e.callee,false).result;if(r&&typeof r.value==="function"&&r.value[te]){me=computePureStaticValue(e,true).result;if(me){ve=e;return backtrack(this,t)}}else if(r&&typeof r.value==="symbol"){switch(r.value){case oe:if(e.arguments.length===1&&e.arguments[0].type==="Literal"&&e.callee.type==="Identifier"&&pe.require.shadowDepth===0){fe=true;re.overwrite(e.callee.start,e.callee.end,"require");re.appendRight(e.start,be+"(");re.appendLeft(e.end,", "+I.substring(e.arguments[0].start,e.arguments[0].end)+")");return this.skip()}break;case X:if(e.arguments.length){const r=computePureStaticValue(e.arguments[0],false).result;if(r&&r.value){let s=false;let a;if(typeof r.value==="object")a=r.value;else if(typeof r.value==="string")a={bindings:r.value};if(!a.path){s=true;a.path=true}a.module_root=$;let o;try{o=_(a)}catch(e){}if(o){me={value:o};ve=e;emitStaticChildAsset(s);return backtrack(this,t)}}}break;case J:if(e.arguments.length===1&&e.arguments[0].type==="Identifier"&&e.arguments[0].name==="__dirname"&&pe.__dirname.shadowDepth===0){fe=true;let r;try{r=B.path(A)}catch(e){}if(r){me={value:r};ve=e;emitStaticChildAsset(s);return backtrack(this,t)}}break;case Q:if(e.arguments.length===2&&e.arguments[0].type==="Identifier"&&e.arguments[0].name==="__dirname"&&pe.__dirname.shadowDepth===0){fe=true;re.overwrite(e.start,e.arguments[0].end+1,"require.resolve(");return this.skip()}break;case z:if(e.arguments.length){const t=computePureStaticValue(e.arguments[0],false).result;if(t&&t.value){const r=S(t.value);if(r){r.path=s.relative(A,r.path);fe=true;const t=JSON.stringify(r.path.replace(/\\/g,"/"));re.overwrite(e.start,e.end,`({ bind: require(${t}).NBind.bind_value, lib: require(${t}) })`);return this.skip()}}}break;case G:if(e.arguments.length===2&&e.arguments[0].type==="Literal"&&e.arguments[0].value==="view engine"&&!ge){fe=true;const t=I.substring(e.arguments[1].start,e.arguments[1].end);re.appendRight(e.callee.object.end,`.engine(${t}, require(${t}).__express)`);return this.skip()}break;case W:ge=true;break;case K:if(e.arguments[0]){me=computePureStaticValue(e.arguments[0],true).result;if(me){ve=e.arguments[0];return backtrack(this,t)}}break}}}else if(e.type==="VariableDeclaration"&&!isVarLoop(t)){for(const r of e.declarations){if(!r.init)continue;const e=computePureStaticValue(r.init,false).result;if(e&&"value"in e){if(r.id.type==="Identifier"){setKnownBinding(r.id.name,e.value)}else if(r.id.type==="ObjectPattern"){for(const t of r.id.properties){if(t.type!=="Property"||t.key.type!=="Identifier"||t.value.type!=="Identifier"||typeof e.value!=="object"||e.value===null||!(t.key.name in e.value))continue;setKnownBinding(t.value.name,e.value[t.key.name])}}if(isAbsolutePathOrUrl(e.value)){me=e;ve=r.init;emitStaticChildAsset();return backtrack(this,t)}}}}else if(e.type==="AssignmentExpression"&&!isLoop(t)){const r=computePureStaticValue(e.right,false).result;if(r&&"value"in r){if(e.left.type==="Identifier"){setKnownBinding(e.left.name,r.value)}else if(e.left.type==="ObjectPattern"){for(const t of e.left.properties){if(t.type!=="Property"||t.key.type!=="Identifier"||t.value.type!=="Identifier"||typeof r.value!=="object"||r.value===null||!(t.key.name in r.value))continue;setKnownBinding(t.value.name,r.value[t.key.name])}}if(isAbsolutePathOrUrl(r.value)){me=r;ve=e.right;emitStaticChildAsset();return backtrack(this,t)}}if(!le&&e.right.type==="CallExpression"&&isStaticRequire(e.right.callee)&&e.right.callee.arguments[0].value==="esm"&&e.left.type==="Identifier"&&e.left.name==="require"){fe=true;re.overwrite(e.start,e.end,"");return this.skip()}}else if(!le&&e.type==="ConditionalExpression"&&isStaticRequire(e.consequent)&&isStaticRequire(e.alternate)){const t=computePureStaticValue(e.test,false).result;if(t&&"value"in t){fe=true;if(t.value){re.overwrite(e.start,e.end,I.substring(e.consequent.start,e.consequent.end))}else{re.overwrite(e.start,e.end,I.substring(e.alternate.start,e.alternate.end))}return this.skip()}}else if(!le&&(e.type==="FunctionDeclaration"||e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression")&&(e.arguments||e.params)[0]&&(e.arguments||e.params)[0].type==="Identifier"){let r,s;if((e.type==="ArrowFunctionExpression"||e.type==="FunctionExpression")&&t.type==="VariableDeclarator"&&t.id.type==="Identifier"){r=t.id;s=e.arguments||e.params}else if(e.id){r=e.id;s=e.arguments||e.params}if(r&&e.body.body){let a,o,u=false;for(let t=0;t<e.body.body.length;t++){if(e.body.body[t].type==="VariableDeclaration"&&!a){a=e.body.body[t].declarations.find((e=>e.id.type==="Identifier"&&e.init&&e.init.type==="CallExpression"&&e.init.callee.type==="Identifier"&&e.init.callee.name==="require"&&pe.require.shadowDepth===0&&e.init.arguments[0]&&e.init.arguments[0].type==="Identifier"&&e.init.arguments[0].name===s[0].name));if(a)o=e.body.body[t]}if(a&&e.body.body[t].type==="ReturnStatement"&&e.body.body[t].argument&&e.body.body[t].argument.type==="Identifier"&&e.body.body[t].argument.name===a.id.name){u=true;break}}if(u){let u=";";const c=e.type==="ArrowFunctionExpression"&&e.params[0].start===e.start;if(e.type==="FunctionExpression"||e.type==="ArrowFunctionExpression"){e=t;u=","}be=r.name+"$$mod";setKnownBinding(r.name,oe);const h=u+I.substring(e.start,r.start)+be+I.substring(r.end,s[0].start+!c)+(c?"(":"")+a.id.name+", "+I.substring(s[0].start,s[s.length-1].end+!c)+(c?")":"")+I.substring(s[0].end+!c,o.start)+I.substring(o.end,e.end);re.appendRight(e.end,h)}}}},leave(e,t){if(e.scope){ce=ce.parent;for(const t in e.scope.declarations){if(t in pe){if(pe[t].shadowDepth>0)pe[t].shadowDepth--;else delete pe[t]}}}if(ve){const t=computePureStaticValue(e,true).result;if(t){if("value"in t&&typeof t.value!=="symbol"||typeof t.then!=="symbol"&&typeof t.else!=="symbol"){me=t;ve=e;return}}emitStaticChildAsset()}}});if(!fe)return this.callback(null,I,t);Z.then((()=>{if(de.length)re.appendLeft(ue.body[0].start,de.join("\n")+"\n");I=re.toString();t=t||re.generateMap();if(t){t.sources=[r]}this.callback(null,I,t)}));function validAssetEmission(e){if(!e)return;if(e===r)return;let t="";if(e.endsWith(s.sep))t=s.sep;else if(e.endsWith(s.sep+M))t=s.sep+M;else if(e.endsWith(M))t=M;if(!T.emitDirnameAll&&e===A+t)return;if(!T.emitFilterAssetBaseAll&&e===(T.filterAssetBase||se)+t)return;if(e.endsWith(s.sep+"node_modules"+t))return;if(A.startsWith(e.substr(0,e.length-t.length)+s.sep))return;if($){const t=r.substr(0,r.indexOf(s.sep+"node_modules"))+s.sep+"node_modules"+s.sep;if(!e.startsWith(t)){if(T.debugLog){if(assetEmission(e))console.log("Skipping asset emission of "+e.replace(V,"*")+" for "+r+" as it is outside the package base "+$)}return}}else if(!e.startsWith(T.filterAssetBase||se)){if(T.debugLog){if(assetEmission(e))console.log("Skipping asset emission of "+e.replace(V,"*")+" for "+r+" as it is outside the filterAssetBase directory "+(T.filterAssetBase||se))}return}if(T.customEmit){const t=T.customEmit(e,{id:r,isRequire:false});if(t===false)return;if(typeof t==="string")return()=>t}return assetEmission(e)}function assetEmission(e){const t=e.indexOf(M);const r=t===-1?e.length:e.lastIndexOf(s.sep,t);const a=e.substr(0,r);try{const e=p(a);if(t!==-1&&e.isFile())return;if(e.isFile())return emitAsset;if(e.isDirectory())return emitAssetDirectory}catch(e){return}}function resolveAbsolutePathOrUrl(e){return e instanceof URL?O(e):e.startsWith("file:")?O(new URL(e)):s.resolve(e)}function emitStaticChildAsset(e=false){if(isAbsolutePathOrUrl(me.value)){let t;try{t=resolveAbsolutePathOrUrl(me.value)}catch(e){}let r;if(r=validAssetEmission(t)){let s=r(t,me.wildcards);if(s){if(e)s="__non_webpack_require__("+s+")";re.overwrite(ve.start,ve.end,s);fe=true}}}else if(isAbsolutePathOrUrl(me.then)&&isAbsolutePathOrUrl(me.else)){let t;try{t=resolveAbsolutePathOrUrl(me.then)}catch(e){}let r;try{r=resolveAbsolutePathOrUrl(me.else)}catch(e){}let s;if(!e&&(s=validAssetEmission(t))&&s===validAssetEmission(r)){const e=s(t);const a=s(r);if(e&&a){re.overwrite(ve.start,ve.end,`${I.substring(me.test.start,me.test.end)} ? ${e} : ${a}`);fe=true}}}else if(ve.type==="ArrayExpression"&&me.value instanceof Array){for(let t=0;t<me.value.length;t++){const r=me.value[t];const s=ve.elements[t];if(isAbsolutePathOrUrl(r)){let t;try{t=resolveAbsolutePathOrUrl(r)}catch(e){}let a;if(a=validAssetEmission(t)){let r=a(t);if(r){if(e)r="__non_webpack_require__("+r+")";re.overwrite(s.start,s.end,r);fe=true}}}}}ve=me=undefined}};e.exports.raw=true;e.exports.getAssetMeta=function(e,t){const r=t?q.get(t):U;if(r)return r.assetMeta[e]};e.exports.getSymlinks=function(e){const t=e?q.get(e):U;if(t)return U.assetSymlinks};e.exports.initAssetCache=e.exports.initAssetMetaCache=function(e,t){injectPathHook(e,t);const r=getEntryIds(e);if(!r)return;const s=U={entryIds:r,assets:Object.create(null),assetNames:Object.create(null),assetMeta:Object.create(null),assetSymlinks:Object.create(null),hadOptions:false};q.set(e,s);const a=e.getCache?e.getCache():e.cache;if(a)a.get("/RelocateLoader/AssetState/"+JSON.stringify(r),null,((e,t)=>{if(e)console.error(e);if(t){const e=JSON.parse(t);if(e.assetMeta)s.assetMeta=e.assetMeta;if(e.assetSymlinks)s.assetSymlinks=e.assetSymlinks}}));e.compiler.hooks.afterCompile.tap("relocate-loader",(e=>{const t=e.getCache?e.getCache():e.cache;if(t)t.store("/RelocateLoader/AssetState/"+JSON.stringify(r),null,JSON.stringify({assetMeta:s.assetMeta,assetSymlinks:s.assetSymlinks}),(e=>{if(e)console.error(e)}))}))}},5277:(__unused_webpack_module,exports,__nested_webpack_require_892507__)=>{const path=__nested_webpack_require_892507__(5622);const fs=__nested_webpack_require_892507__(5747);const versioning=__nested_webpack_require_892507__(887);const napi=__nested_webpack_require_892507__(480);const pregypFind=(e,t)=>{const r=JSON.parse(fs.readFileSync(e).toString());versioning.validate_config(r,t);var s;if(napi.get_napi_build_versions(r,t)){s=napi.get_best_napi_build_version(r,t)}t=t||{};if(!t.module_root)t.module_root=path.dirname(e);var a=versioning.evaluate(r,t,s);return a.module};exports.pregyp={default:{find:pregypFind},find:pregypFind};function makeModulePathList(e,t){return[[e,t],[e,"build",t],[e,"build","Debug",t],[e,"build","Release",t],[e,"out","Debug",t],[e,"Debug",t],[e,"out","Release",t],[e,"Release",t],[e,"build","default",t],[e,process.env["NODE_BINDINGS_COMPILED_DIR"]||"compiled",process.versions.node,process.platform,process.arch,t]]}function findCompiledModule(basePath,specList){var resolvedList=[];var ext=path.extname(basePath);for(var _i=0,specList_1=specList;_i<specList_1.length;_i++){var spec=specList_1[_i];if(ext==spec.ext){try{spec.path=eval("require.resolve(basePath)");return spec}catch(e){resolvedList.push(basePath)}}}for(var _a=0,specList_2=specList;_a<specList_2.length;_a++){var spec=specList_2[_a];for(var _b=0,_c=makeModulePathList(basePath,spec.name);_b<_c.length;_b++){var pathParts=_c[_b];var resolvedPath=path.resolve.apply(path,pathParts);try{spec.path=eval("require.resolve(resolvedPath)")}catch(e){resolvedList.push(resolvedPath);continue}return spec}}return null}function find(e=process.cwd()){const t=findCompiledModule(e,[{ext:".node",name:"nbind.node",type:"node"},{ext:".js",name:"nbind.js",type:"emcc"}]);return t}exports.nbind=find},8357:(e,t,r)=>{const s=r(5622);e.exports=getUniqueAssetName;function getUniqueAssetName(e,t,r,a){const o=s.extname(e);let u=e,c=0;while((u in r||a&&Object.keys(r).some((e=>e.startsWith(u+s.sep))))&&r[u]!==t){u=e.substr(0,e.length-o.length)+ ++c+o}r[u]=t;return u}},8011:e=>{const t=/^(@[^\\\/]+[\\\/])?[^\\\/]+/;e.exports=function(e){const r=e.lastIndexOf("node_modules");if(r!==-1&&(e[r-1]==="/"||e[r-1]==="\\")&&(e[r+12]==="/"||e[r+12]==="\\")){const s=e.substr(r+13).match(t);if(s)return e.substr(0,r+13+s[0].length)}};e.exports.pkgNameRegEx=t},3953:(e,t,r)=>{const{existsSync:s}=r(5747);const{dirname:a}=r(5622);e.exports=function getPackageScope(e){let t=a(e);do{e=t;t=a(e);if(s(e+"/package.json"))return e}while(e!==t)}},4723:(e,t,r)=>{const{encode:s,decode:a}=r(4957);function traceSegment(e,t,r,s,a){const o=t[r];if(!o)return null;let u=0;let c=o.length-1;while(u<=c){const t=u+c>>1;const r=o[t];if(r[0]===s){return{source:r[1],line:r[2],column:r[3],name:e.names[r[4]]||a}}if(r[0]>s)c=t-1;else u=t+1}return null}e.exports=function(e,t){const r=[];const o=[];const u=[];const c=[];const h=a(e.mappings);for(const s of a(t.mappings)){const a=[];for(const c of s){const s=traceSegment(e,h,c[2],c[3],t.names[c[4]]);if(s){const t=e.sources[s.source];let h=r.lastIndexOf(t);if(h===-1){h=r.length;r.push(t);o[h]=e.sourcesContent[s.source]}else if(o[h]==null){o[h]=e.sourcesContent[s.source]}const p=[c[0],h,s.line,s.column];if(s.name){let e=u.indexOf(s.name);if(e===-1){e=u.length;u.push(s.name)}p[4]=e}a.push(p)}}c.push(a)}return{version:3,file:null,sources:r,mappings:s(c),names:u,sourcesContent:o}}},1331:(e,t,r)=>{const s=r(2087);const a=r(7758);const o=r(1957);const u=r(5622);let c;switch(s.platform()){case"darwin":c="/**/*.@(dylib|so?(.*))";break;case"win32":c="/**/*.dll";break;default:c="/**/*.so?(.*)"}e.exports=async function(e,t,r,s,h){const p=await new Promise(((t,r)=>o(e+c,{ignore:"node_modules/**/*"},((e,s)=>e?r(e):t(s)))));await Promise.all(p.map((async o=>{const[c,p]=await Promise.all([new Promise(((e,t)=>a.readFile(o,((r,s)=>r?t(r):e(s))))),await new Promise(((e,t)=>a.lstat(o,((r,s)=>r?t(r):e(s)))))]);if(p.isSymbolicLink()){const s=await new Promise(((e,t)=>{a.readlink(o,((r,s)=>r?t(r):e(s)))}));const c=u.dirname(o);t.assetSymlinks[r+o.substr(e.length+1)]=u.relative(c,u.resolve(c,s))}else{t.assetMeta[o.substr(e.length)]={path:o,permissions:p.mode};if(h)console.log("Emitting "+o+" for shared library support in "+e);s(r+o.substr(e.length+1),c)}})))}},9646:(e,t,r)=>{const s=r(5622);const a=r(9283);const o=r(5747);const u=r(5094);e.exports=function({id:e,code:t,pkgBase:r,ast:c,scope:h,magicString:p,emitAssetDirectory:d}){let v;({transformed:v,ast:c,scope:h}=u(c,h,p));if(v)return{transformed:v,ast:c,scope:h};if(e.endsWith("google-gax/build/src/grpc.js")||global._unit&&e.includes("google-gax")){for(const t of c.body){if(t.type==="VariableDeclaration"&&t.declarations[0].id.type==="Identifier"&&t.declarations[0].id.name==="googleProtoFilesDir"){const r=d(s.resolve(s.dirname(e),global._unit?"./":"../../../google-proto-files"));if(r){p.overwrite(t.declarations[0].init.start,t.declarations[0].init.end,r);t.declarations[0].init=null;return{transformed:true}}}}}else if(e.endsWith("socket.io/lib/index.js")||global._unit&&e.includes("socket.io")){function replaceResolvePathStatement(t){if(t.type==="ExpressionStatement"&&t.expression.type==="AssignmentExpression"&&t.expression.operator==="="&&t.expression.right.type==="CallExpression"&&t.expression.right.callee.type==="Identifier"&&t.expression.right.callee.name==="read"&&t.expression.right.arguments.length>=1&&t.expression.right.arguments[0].type==="CallExpression"&&t.expression.right.arguments[0].callee.type==="Identifier"&&t.expression.right.arguments[0].callee.name==="resolvePath"&&t.expression.right.arguments[0].arguments.length===1&&t.expression.right.arguments[0].arguments[0].type==="Literal"){const o=t.expression.right.arguments[0].arguments[0].value;try{var r=a.sync(o,{basedir:s.dirname(e)})}catch(e){return{transformed:false}}const u="/"+s.relative(s.dirname(e),r);t.expression.right.arguments[0]={type:"BinaryExpression",start:t.expression.right.arguments[0].start,end:t.expression.right.arguments[0].end,operator:"+",left:{type:"Identifier",name:"__dirname"},right:{type:"Literal",value:u,raw:JSON.stringify(u)}};return{transformed:true}}return{transformed:false}}for(const e of c.body){if(e.type==="ExpressionStatement"&&e.expression.type==="AssignmentExpression"&&e.expression.operator==="="&&e.expression.left.type==="MemberExpression"&&e.expression.left.object.type==="MemberExpression"&&e.expression.left.object.object.type==="Identifier"&&e.expression.left.object.object.name==="Server"&&e.expression.left.object.property.type==="Identifier"&&e.expression.left.object.property.name==="prototype"&&e.expression.left.property.type==="Identifier"&&e.expression.left.property.name==="serveClient"&&e.expression.right.type==="FunctionExpression"){let t;for(const r of e.expression.right.body.body)if(r.type==="IfStatement")t=r;const r=t&&t.consequent.body;let s=false;if(r&&r[0]&&r[0].type==="ExpressionStatement")s=replaceResolvePathStatement(r[0]);const a=r&&r[1]&&r[1].type==="TryStatement"&&r[1].block.body;if(a&&a[0])s=replaceResolvePathStatement(a[0])||s;return{transformed:s}}}}else if(e.endsWith("oracledb/lib/oracledb.js")||global._unit&&e.includes("oracledb")){for(const t of c.body){if(t.type==="ForStatement"&&t.body.body&&t.body.body[0]&&t.body.body[0].type==="TryStatement"&&t.body.body[0].block.body[0]&&t.body.body[0].block.body[0].type==="ExpressionStatement"&&t.body.body[0].block.body[0].expression.type==="AssignmentExpression"&&t.body.body[0].block.body[0].expression.operator==="="&&t.body.body[0].block.body[0].expression.left.type==="Identifier"&&t.body.body[0].block.body[0].expression.left.name==="oracledbCLib"&&t.body.body[0].block.body[0].expression.right.type==="CallExpression"&&t.body.body[0].block.body[0].expression.right.callee.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.callee.name==="require"&&t.body.body[0].block.body[0].expression.right.arguments.length===1&&t.body.body[0].block.body[0].expression.right.arguments[0].type==="MemberExpression"&&t.body.body[0].block.body[0].expression.right.arguments[0].computed===true&&t.body.body[0].block.body[0].expression.right.arguments[0].object.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.arguments[0].object.name==="binaryLocations"&&t.body.body[0].block.body[0].expression.right.arguments[0].property.type==="Identifier"&&t.body.body[0].block.body[0].expression.right.arguments[0].property.name==="i"){const r=t.body.body[0].block.body[0].expression.right.arguments[0];t.body.body[0].block.body[0].expression.right.arguments=[{type:"Literal",value:"_"}];const s=global._unit?"3.0.0":JSON.parse(o.readFileSync(e.slice(0,-15)+"package.json")).version;const a=Number(s.slice(0,s.indexOf(".")))>=4;const u="oracledb-"+(a?s:"abi"+process.versions.modules)+"-"+process.platform+"-"+process.arch+".node";p.overwrite(r.start,r.end,global._unit?"'./oracledb.js'":"'../build/Release/"+u+"'");return{transformed:true}}}}return{transformed:false}}},798:e=>{e.exports=function(e,t={},r=true){const s={computeBranches:r,sawIdentifier:false,vars:t};const a=walk(e);return{result:a,sawIdentifier:s.sawIdentifier};function walk(e){const t=o[e.type];if(t)return t.call(s,e,walk)}};const t=e.exports.UNKNOWN=Symbol();const r=e.exports.FUNCTION=Symbol();const s=e.exports.WILDCARD="";const a=e.exports.wildcardRegEx=/\x1a/g;function countWildcards(e){a.lastIndex=0;let t=0;while(a.exec(e))t++;return t}const o={ArrayExpression(e,t){const r=[];for(let s=0,a=e.elements.length;s<a;s++){if(e.elements[s]===null){r.push(null);continue}const a=t(e.elements[s]);if(!a)return;if("value"in a===false)return;r.push(a.value)}return{value:r}},BinaryExpression(e,t){const r=e.operator;let a=t(e.left);if(!a&&r!=="+")return;let o=t(e.right);if(!a&&!o)return;if(!a){if(this.computeBranches&&typeof o.value==="string")return{value:s+o.value,wildcards:[e.left,...o.wildcards||[]]};return}if(!o){if(this.computeBranches&&r==="+"){if(typeof a.value==="string")return{value:a.value+s,wildcards:[...a.wildcards||[],e.right]}}if(!("test"in a)&&r==="||"&&a.value)return e.right;return}if("test"in a&&"test"in o)return;if("test"in a){o=o.value;if(r==="==")return{test:a.test,then:a.then==o,else:a.else==o};if(r==="===")return{test:a.test,then:a.then===o,else:a.else===o};if(r==="!=")return{test:a.test,then:a.then!=o,else:a.else!=o};if(r==="!==")return{test:a.test,then:a.then!==o,else:a.else!==o};if(r==="+")return{test:a.test,then:a.then+o,else:a.else+o};if(r==="-")return{test:a.test,then:a.then-o,else:a.else-o};if(r==="*")return{test:a.test,then:a.then*o,else:a.else*o};if(r==="/")return{test:a.test,then:a.then/o,else:a.else/o};if(r==="%")return{test:a.test,then:a.then%o,else:a.else%o};if(r==="<")return{test:a.test,then:a.then<o,else:a.else<o};if(r==="<=")return{test:a.test,then:a.then<=o,else:a.else<=o};if(r===">")return{test:a.test,then:a.then>o,else:a.else>o};if(r===">=")return{test:a.test,then:a.then>=o,else:a.else>=o};if(r==="|")return{test:a.test,then:a.then|o,else:a.else|o};if(r==="&")return{test:a.test,then:a.then&o,else:a.else&o};if(r==="^")return{test:a.test,then:a.then^o,else:a.else^o};if(r==="&&")return{test:a.test,then:a.then&&o,else:a.else&&o};if(r==="||")return{test:a.test,then:a.then||o,else:a.else||o}}else if("test"in o){a=a.value;if(r==="==")return{test:o.test,then:a==o.then,else:a==o.else};if(r==="===")return{test:o.test,then:a===o.then,else:a===o.else};if(r==="!=")return{test:o.test,then:a!=o.then,else:a!=o.else};if(r==="!==")return{test:o.test,then:a!==o.then,else:a!==o.else};if(r==="+")return{test:o.test,then:a+o.then,else:a+o.else};if(r==="-")return{test:o.test,then:a-o.then,else:a-o.else};if(r==="*")return{test:o.test,then:a*o.then,else:a*o.else};if(r==="/")return{test:o.test,then:a/o.then,else:a/o.else};if(r==="%")return{test:o.test,then:a%o.then,else:a%o.else};if(r==="<")return{test:o.test,then:a<o.then,else:a<o.else};if(r==="<=")return{test:o.test,then:a<=o.then,else:a<=o.else};if(r===">")return{test:o.test,then:a>o.then,else:a>o.else};if(r===">=")return{test:o.test,then:a>=o.then,else:a>=o.else};if(r==="|")return{test:o.test,then:a|o.then,else:a|o.else};if(r==="&")return{test:o.test,then:a&o.then,else:a&o.else};if(r==="^")return{test:o.test,then:a^o.then,else:a^o.else};if(r==="&&")return{test:o.test,then:a&&o.then,else:a&&o.else};if(r==="||")return{test:o.test,then:a||o.then,else:a||o.else}}else{if(r==="==")return{value:a.value==o.value};if(r==="===")return{value:a.value===o.value};if(r==="!=")return{value:a.value!=o.value};if(r==="!==")return{value:a.value!==o.value};if(r==="+"){const e={value:a.value+o.value};if(a.wildcards||o.wildcards)e.wildcards=[...a.wildcards||[],...o.wildcards||[]];return e}if(r==="-")return{value:a.value-o.value};if(r==="*")return{value:a.value*o.value};if(r==="/")return{value:a.value/o.value};if(r==="%")return{value:a.value%o.value};if(r==="<")return{value:a.value<o.value};if(r==="<=")return{value:a.value<=o.value};if(r===">")return{value:a.value>o.value};if(r===">=")return{value:a.value>=o.value};if(r==="|")return{value:a.value|o.value};if(r==="&")return{value:a.value&o.value};if(r==="^")return{value:a.value^o.value};if(r==="&&")return{value:a.value&&o.value};if(r==="||")return{value:a.value||o.value}}return},CallExpression(e,a){const o=a(e.callee);if(!o||"test"in o)return;let u=o.value;if(typeof u==="object"&&u!==null)u=u[r];if(typeof u!=="function")return;const c=e.callee.object&&a(e.callee.object).value||null;let h;let p=[];let d;let v=e.arguments.length>0;const m=[];for(let t=0,r=e.arguments.length;t<r;t++){let r=a(e.arguments[t]);if(r){v=false;if(typeof r.value==="string"&&r.wildcards)r.wildcards.forEach((e=>m.push(e)))}else{if(!this.computeBranches)return;r={value:s};m.push(e.arguments[t])}if("test"in r){if(m.length)return;if(h)return;h=r.test;d=p.concat([]);p.push(r.then);d.push(r.else)}else{p.push(r.value);if(d)d.push(r.value)}}if(v)return;try{const e=u.apply(c,p);if(e===t)return;if(!h){if(m.length){if(typeof e!=="string"||countWildcards(e)!==m.length)return;return{value:e,wildcards:m}}return{value:e}}const r=u.apply(c,d);if(e===t)return;return{test:h,then:e,else:r}}catch(e){return}},ConditionalExpression(e,t){const r=t(e.test);if(r&&"value"in r)return r.value?t(e.consequent):t(e.alternate);if(!this.computeBranches)return;const s=t(e.consequent);if(!s||"wildcards"in s||"test"in s)return;const a=t(e.alternate);if(!a||"wildcards"in a||"test"in a)return;return{test:e.test,then:s.value,else:a.value}},ExpressionStatement(e,t){return t(e.expression)},Identifier(e){this.sawIdentifier=true;if(Object.hasOwnProperty.call(this.vars,e.name)){const r=this.vars[e.name];if(r===t)return;return{value:r}}return},Literal(e){return{value:e.value}},MemberExpression(e,r){const s=r(e.object);if(!s||"test"in s||typeof s.value==="function")return;if(e.property.type==="Identifier"){if(typeof s.value==="object"&&s.value!==null){if(e.property.name in s.value){const r=s.value[e.property.name];if(r===t)return;return{value:r}}else if(s.value[t])return}else{return{value:undefined}}}const a=r(e.property);if(!a||"test"in a)return;if(typeof s.value==="object"&&s.value!==null){if(a.value in s.value){const e=s.value[a.value];if(e===t)return;return{value:e}}else if(s.value[t]){return}}else{return{value:undefined}}},MetaProperty:function MetaProperty(e){if(e.meta.name==="import"&&e.property.name==="meta"){return{value:this.vars["import.meta"]}}return undefined},NewExpression:function NewExpression(e,t){const r=t(e.callee);if(r&&"value"in r&&r.value===URL&&e.arguments.length){const r=t(e.arguments[0]);if(!r)return undefined;let s=null;if(e.arguments[1]){s=t(e.arguments[1]);if(!s||!("value"in s))return undefined}if("value"in r){if(s){try{return{value:new URL(r.value,s.value)}}catch{return undefined}}try{return{value:new URL(r.value)}}catch{return undefined}}else{const e=r.test;if(s){try{return{test:e,then:new URL(r.then,s.value),else:new URL(r.else,s.value)}}catch{return undefined}}try{return{test:e,then:new URL(r.then),else:new URL(r.else)}}catch{return undefined}}}return undefined},ObjectExpression(e,r){const s={};for(let a=0;a<e.properties.length;a++){const o=e.properties[a];const u=o.computed?r(o.key):o.key&&{value:o.key.name||o.key.value};if(!u||"test"in u)return;const c=r(o.value);if(!c||"test"in c)return;if(c.value===t)return;s[u.value]=c.value}return s},TemplateLiteral(e,t){let r={value:""};for(var a=0;a<e.expressions.length;a++){if("value"in r){r.value+=e.quasis[a].value.cooked}else{r.then+=e.quasis[a].value.cooked;r.else+=e.quasis[a].value.cooked}let o=t(e.expressions[a]);if(!o){if(!this.computeBranches)return;o={value:s,wildcards:[e.expressions[a]]}}if("value"in o){if("value"in r){r.value+=o.value;if(o.wildcards)r.wildcards=[...r.wildcards||[],...o.wildcards]}else{if(o.wildcards)return;r.then+=o.value;r.else+=o.value}}else{if("value"in r===false||r.wildcards)return;r={test:o.test,then:r.value+o.then,else:r.value+o.else}}}if("value"in r){r.value+=e.quasis[a].value.cooked}else{r.then+=e.quasis[a].value.cooked;r.else+=e.quasis[a].value.cooked}return r},ThisExpression(){if(Object.hasOwnProperty.call(this.vars,"this"))return{value:this.vars["this"]}},UnaryExpression(e,t){const r=t(e.argument);if(!r)return;if("value"in r&&"wildcards"in r===false){if(e.operator==="+")return{value:+r.value};if(e.operator==="-")return{value:-r.value};if(e.operator==="~")return{value:~r.value};if(e.operator==="!")return{value:!r.value}}else if("test"in r&&"wildcards"in r===false){if(e.operator==="+")return{test:r.test,then:+r.then,else:+r.else};if(e.operator==="-")return{test:r.test,then:-r.then,else:-r.else};if(e.operator==="~")return{test:r.test,then:~r.then,else:~r.else};if(e.operator==="!")return{test:r.test,then:!r.then,else:!r.else}}return}};o.LogicalExpression=o.BinaryExpression},5094:(e,t,r)=>{const{walk:s}=r(6465);function isUndefinedOrVoid(e){return e.type==="Identifier"&&e.name==="undefined"||e.type==="UnaryExpression"&&e.operator==="void"&&e.argument.type==="Literal"&&e.argument.value===0}function handleWrappers(e,t,r){let a=false;let o;if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="UnaryExpression"&&e.body[0].expression.operator==="!"&&e.body[0].expression.argument.type==="CallExpression"&&e.body[0].expression.argument.callee.type==="FunctionExpression"&&e.body[0].expression.argument.arguments.length===1)o=e.body[0].expression.argument;else if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="CallExpression"&&e.body[0].expression.callee.type==="FunctionExpression"&&e.body[0].expression.arguments.length===1)o=e.body[0].expression;else if(e.body.length===1&&e.body[0].type==="ExpressionStatement"&&e.body[0].expression.type==="AssgnmentExpression"&&e.body[0].expression.left.type==="MemberExpression"&&e.body[0].expression.left.object.type==="Identifier"&&e.body[0].expression.left.object.name==="module"&&e.body[0].expression.left.property.type==="Identifier"&&e.body[0].expression.left.property.name==="exports"&&e.body[0].expression.right.type==="CallExpression"&&e.body[0].expression.right.callee.type==="FunctionExpression"&&e.body[0].expression.right.arguments.length===1)o=e.body[0].expression.right;if(o){if(o.arguments[0].type==="ConditionalExpression"&&o.arguments[0].test.type==="LogicalExpression"&&o.arguments[0].test.operator==="&&"&&o.arguments[0].test.left.type==="BinaryExpression"&&o.arguments[0].test.left.operator==="==="&&o.arguments[0].test.left.left.type==="UnaryExpression"&&o.arguments[0].test.left.left.operator==="typeof"&&o.arguments[0].test.left.left.argument.name==="define"&&o.arguments[0].test.left.right.type==="Literal"&&o.arguments[0].test.left.right.value==="function"&&o.arguments[0].test.right.type==="MemberExpression"&&o.arguments[0].test.right.object.type==="Identifier"&&o.arguments[0].test.right.property.type==="Identifier"&&o.arguments[0].test.right.property.name==="amd"&&o.arguments[0].test.right.computed===false&&o.arguments[0].alternate.type==="FunctionExpression"&&o.arguments[0].alternate.params.length===1&&o.arguments[0].alternate.params[0].type==="Identifier"&&o.arguments[0].alternate.body.body.length===1&&o.arguments[0].alternate.body.body[0].type==="ExpressionStatement"&&o.arguments[0].alternate.body.body[0].expression.type==="AssignmentExpression"&&o.arguments[0].alternate.body.body[0].expression.left.type==="MemberExpression"&&o.arguments[0].alternate.body.body[0].expression.left.object.type==="Identifier"&&o.arguments[0].alternate.body.body[0].expression.left.object.name==="module"&&o.arguments[0].alternate.body.body[0].expression.left.property.type==="Identifier"&&o.arguments[0].alternate.body.body[0].expression.left.property.name==="exports"&&o.arguments[0].alternate.body.body[0].expression.left.computed===false&&o.arguments[0].alternate.body.body[0].expression.right.type==="CallExpression"&&o.arguments[0].alternate.body.body[0].expression.right.callee.type==="Identifier"&&o.arguments[0].alternate.body.body[0].expression.right.callee.name===o.arguments[0].alternate.params[0].name&&o.arguments[0].alternate.body.body[0].expression.right.arguments.length===1&&o.arguments[0].alternate.body.body[0].expression.right.arguments[0].type==="Identifier"&&o.arguments[0].alternate.body.body[0].expression.right.arguments[0].name==="require"){let e=o.callee.body.body;if(e[0].type==="ExpressionStatement"&&e[0].expression.type==="Literal"&&e[0].expression.value==="use strict"){e=e.slice(1)}if(e.length===1&&e[0].type==="ExpressionStatement"&&e[0].expression.type==="CallExpression"&&e[0].expression.callee.type==="Identifier"&&e[0].expression.callee.name===o.arguments[0].test.right.object.name&&e[0].expression.arguments.length===1&&e[0].expression.arguments[0].type==="FunctionExpression"&&e[0].expression.arguments[0].params.length===1&&e[0].expression.arguments[0].params[0].type==="Identifier"&&e[0].expression.arguments[0].params[0].name==="require"){r.remove(e[0].expression.arguments[0].params[0].start,e[0].expression.arguments[0].params[0].end);a=true}}else if(o.arguments[0].type==="FunctionExpression"&&o.arguments[0].params.length===0&&(o.arguments[0].body.body.length===1||o.arguments[0].body.body.length===2&&o.arguments[0].body.body[0].type==="VariableDeclaration"&&o.arguments[0].body.body[0].declarations.length===3&&o.arguments[0].body.body[0].declarations.every((e=>e.init===null&&e.id.type==="Identifier")))&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].type==="ReturnStatement"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.type==="CallExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.type==="CallExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.arguments.length&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.arguments.every((e=>e.type==="Literal"&&typeof e.value==="number"))&&(o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.type==="FunctionExpression"||o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.type==="CallExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.callee.type==="FunctionExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.arguments.length===0)&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.arguments.length===3&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.arguments[0].type==="ObjectExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.arguments[1].type==="ObjectExpression"&&o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.arguments[2].type==="ArrayExpression"){const e=o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.arguments[0].properties;const t=o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.type==="FunctionExpression"?o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee:o.arguments[0].body.body[o.arguments[0].body.body.length-1].argument.callee.callee.callee.body.body[0];let s;if(t.type==="FunctionDeclaration")s=t.body;else if(t.type==="ReturnStatement")s=t.argument.body;if(s){const e=s.body[0].body.body[0].consequent.body[0].consequent.body[0].declarations[0].init;const t=s.body[1].init.declarations[0].init;e.right.name="_";t.right.name="_";r.overwrite(e.start,e.end,"__non_webpack_require__");r.overwrite(t.start,t.end,"__non_webpack_require__");a=true}const u={};if(e.every((e=>{if(e.type!=="Property"||e.computed!==false||e.key.type!=="Literal"||typeof e.key.value!=="number"||e.value.type!=="ArrayExpression"||e.value.elements.length!==2||e.value.elements[0].type!=="FunctionExpression"||e.value.elements[1].type!=="ObjectExpression")return false;const t=e.value.elements[1].properties;for(const e of t){if(e.type!=="Property"||e.value.type!=="Identifier"&&e.value.type!=="Literal"&&!isUndefinedOrVoid(e.value)||!(e.key.type==="Literal"&&typeof e.key.value==="string"||e.key.type==="Identifier")||e.computed)return false;if(isUndefinedOrVoid(e.value))u[e.key.value||e.key.name]=true}return true}))){const e=Object.keys(u);if(e.length){const t=(o.arguments[0].body.body[1]||o.arguments[0].body.body[0]).argument.callee.arguments[1];const s=e.map((e=>`"${e}": { exports: require("${e}") }`)).join(",\n  ");r.appendRight(t.end-1,s);a=true}}}else if(o.arguments[0].type==="FunctionExpression"&&o.arguments[0].params.length===2&&o.arguments[0].params[0].type==="Identifier"&&o.arguments[0].params[1].type==="Identifier"&&o.callee.body.body.length===1){const e=o.callee.body.body[0];if(e.type==="IfStatement"&&e.test.type==="LogicalExpression"&&e.test.operator==="&&"&&e.test.left.type==="BinaryExpression"&&e.test.left.left.type==="UnaryExpression"&&e.test.left.left.operator==="typeof"&&e.test.left.left.argument.type==="Identifier"&&e.test.left.left.argument.name==="module"&&e.test.left.right.type==="Literal"&&e.test.left.right.value==="object"&&e.test.right.type==="BinaryExpression"&&e.test.right.left.type==="UnaryExpression"&&e.test.right.left.operator==="typeof"&&e.test.right.left.argument.type==="MemberExpression"&&e.test.right.left.argument.object.type==="Identifier"&&e.test.right.left.argument.object.name==="module"&&e.test.right.left.argument.property.type==="Identifier"&&e.test.right.left.argument.property.name==="exports"&&e.test.right.right.type==="Literal"&&e.test.right.right.value==="object"&&e.consequent.type==="BlockStatement"&&e.consequent.body.length>0){let t;if(e.consequent.body[0].type==="VariableDeclaration"&&e.consequent.body[0].declarations[0].init&&e.consequent.body[0].declarations[0].init.type==="CallExpression")t=e.consequent.body[0].declarations[0].init;else if(e.consequent.body[0].type==="ExpressionStatement"&&e.consequent.body[0].expression.type==="CallExpression")t=e.consequent.body[0].expression;else if(e.consequent.body[0].type==="ExpressionStatement"&&e.consequent.body[0].expression.type==="AssignmentExpression"&&e.consequent.body[0].expression.right.type==="CallExpression")t=e.consequent.body[0].expression.right;if(t&&t.callee.type==="Identifier"&&t.callee.name===o.callee.params[0].name&&t.arguments.length===2&&t.arguments[0].type==="Identifier"&&t.arguments[0].name==="require"&&t.arguments[1].type==="Identifier"&&t.arguments[1].name==="exports"){r.remove(o.arguments[0].params[0].start,o.arguments[0].params[o.arguments[0].params.length-1].end);a=true}}}else if(o.callee.type==="FunctionExpression"&&o.callee.params.length===1&&o.callee.body.body.length>2&&o.callee.body.body[0].type==="VariableDeclaration"&&o.callee.body.body[0].declarations.length===1&&o.callee.body.body[0].declarations[0].type==="VariableDeclarator"&&o.callee.body.body[0].declarations[0].id.type==="Identifier"&&o.callee.body.body[0].declarations[0].init.type==="ObjectExpression"&&o.callee.body.body[0].declarations[0].init.properties.length===0&&o.callee.body.body[1].type==="FunctionDeclaration"&&o.callee.body.body[1].params.length===1&&o.callee.body.body[1].body.body.length===3&&o.arguments[0].type==="ArrayExpression"&&o.arguments[0].elements.length>0&&o.arguments[0].elements.every((e=>e.type==="FunctionExpression"))){const e=new Map;for(let t=0;t<o.arguments[0].elements.length;t++){const r=o.arguments[0].elements[t];if(r.body.body.length===1&&r.body.body[0].type==="ExpressionStatement"&&r.body.body[0].expression.type==="AssignmentExpression"&&r.body.body[0].expression.operator==="="&&r.body.body[0].expression.left.type==="MemberExpression"&&r.body.body[0].expression.left.object.type==="Identifier"&&r.body.body[0].expression.left.object.name===r.params[0].name&&r.body.body[0].expression.left.property.type==="Identifier"&&r.body.body[0].expression.left.property.name==="exports"&&r.body.body[0].expression.right.type==="CallExpression"&&r.body.body[0].expression.right.callee.type==="Identifier"&&r.body.body[0].expression.right.callee.name==="require"&&r.body.body[0].expression.right.arguments.length===1&&r.body.body[0].expression.right.arguments[0].type==="Literal"){e.set(t,r.body.body[0].expression.right.arguments[0].value)}}for(let t=0;t<o.arguments[0].elements.length;t++){const u=o.arguments[0].elements[t];if(u.params.length===3&&u.params[2].type==="Identifier"){s(u.body.body,{enter(t,s){if(t.type==="FunctionExpression"||t.type==="FunctionDeclaration"||t.type==="ArrowFunctionExpression"||t.type==="BlockStatement"||t.type==="TryStatement"){if(s)return this.skip()}if(t.type==="CallExpression"&&t.callee.type==="Identifier"&&t.callee.name===u.params[2].name&&t.arguments.length===1&&t.arguments[0].type==="Literal"){const o=e.get(t.arguments[0].value);if(o){const e={type:"CallExpression",callee:{type:"Identifier",name:"require"},arguments:[{type:"Literal",value:o}]};r.overwrite(t.start,t.end,`require(${JSON.stringify(o)})`);a=true;if(s.right===t)s.right=e;else if(s.left===t)s.left=e;else if(s.object===t)s.object=e;else if(s.callee===t)s.callee=e;else if(s.arguments&&s.arguments.some((e=>e===t)))s.arguments=s.arguments.map((r=>r===t?e:r));else if(s.init===t)s.init=e}}}})}}}}return{ast:e,scope:t,transformed:a}}e.exports=handleWrappers},282:e=>{"use strict";e.exports=JSON.parse('{"0.1.14":{"node_abi":null,"v8":"1.3"},"0.1.15":{"node_abi":null,"v8":"1.3"},"0.1.16":{"node_abi":null,"v8":"1.3"},"0.1.17":{"node_abi":null,"v8":"1.3"},"0.1.18":{"node_abi":null,"v8":"1.3"},"0.1.19":{"node_abi":null,"v8":"2.0"},"0.1.20":{"node_abi":null,"v8":"2.0"},"0.1.21":{"node_abi":null,"v8":"2.0"},"0.1.22":{"node_abi":null,"v8":"2.0"},"0.1.23":{"node_abi":null,"v8":"2.0"},"0.1.24":{"node_abi":null,"v8":"2.0"},"0.1.25":{"node_abi":null,"v8":"2.0"},"0.1.26":{"node_abi":null,"v8":"2.0"},"0.1.27":{"node_abi":null,"v8":"2.1"},"0.1.28":{"node_abi":null,"v8":"2.1"},"0.1.29":{"node_abi":null,"v8":"2.1"},"0.1.30":{"node_abi":null,"v8":"2.1"},"0.1.31":{"node_abi":null,"v8":"2.1"},"0.1.32":{"node_abi":null,"v8":"2.1"},"0.1.33":{"node_abi":null,"v8":"2.1"},"0.1.90":{"node_abi":null,"v8":"2.2"},"0.1.91":{"node_abi":null,"v8":"2.2"},"0.1.92":{"node_abi":null,"v8":"2.2"},"0.1.93":{"node_abi":null,"v8":"2.2"},"0.1.94":{"node_abi":null,"v8":"2.2"},"0.1.95":{"node_abi":null,"v8":"2.2"},"0.1.96":{"node_abi":null,"v8":"2.2"},"0.1.97":{"node_abi":null,"v8":"2.2"},"0.1.98":{"node_abi":null,"v8":"2.2"},"0.1.99":{"node_abi":null,"v8":"2.2"},"0.1.100":{"node_abi":null,"v8":"2.2"},"0.1.101":{"node_abi":null,"v8":"2.3"},"0.1.102":{"node_abi":null,"v8":"2.3"},"0.1.103":{"node_abi":null,"v8":"2.3"},"0.1.104":{"node_abi":null,"v8":"2.3"},"0.2.0":{"node_abi":1,"v8":"2.3"},"0.2.1":{"node_abi":1,"v8":"2.3"},"0.2.2":{"node_abi":1,"v8":"2.3"},"0.2.3":{"node_abi":1,"v8":"2.3"},"0.2.4":{"node_abi":1,"v8":"2.3"},"0.2.5":{"node_abi":1,"v8":"2.3"},"0.2.6":{"node_abi":1,"v8":"2.3"},"0.3.0":{"node_abi":1,"v8":"2.5"},"0.3.1":{"node_abi":1,"v8":"2.5"},"0.3.2":{"node_abi":1,"v8":"3.0"},"0.3.3":{"node_abi":1,"v8":"3.0"},"0.3.4":{"node_abi":1,"v8":"3.0"},"0.3.5":{"node_abi":1,"v8":"3.0"},"0.3.6":{"node_abi":1,"v8":"3.0"},"0.3.7":{"node_abi":1,"v8":"3.0"},"0.3.8":{"node_abi":1,"v8":"3.1"},"0.4.0":{"node_abi":1,"v8":"3.1"},"0.4.1":{"node_abi":1,"v8":"3.1"},"0.4.2":{"node_abi":1,"v8":"3.1"},"0.4.3":{"node_abi":1,"v8":"3.1"},"0.4.4":{"node_abi":1,"v8":"3.1"},"0.4.5":{"node_abi":1,"v8":"3.1"},"0.4.6":{"node_abi":1,"v8":"3.1"},"0.4.7":{"node_abi":1,"v8":"3.1"},"0.4.8":{"node_abi":1,"v8":"3.1"},"0.4.9":{"node_abi":1,"v8":"3.1"},"0.4.10":{"node_abi":1,"v8":"3.1"},"0.4.11":{"node_abi":1,"v8":"3.1"},"0.4.12":{"node_abi":1,"v8":"3.1"},"0.5.0":{"node_abi":1,"v8":"3.1"},"0.5.1":{"node_abi":1,"v8":"3.4"},"0.5.2":{"node_abi":1,"v8":"3.4"},"0.5.3":{"node_abi":1,"v8":"3.4"},"0.5.4":{"node_abi":1,"v8":"3.5"},"0.5.5":{"node_abi":1,"v8":"3.5"},"0.5.6":{"node_abi":1,"v8":"3.6"},"0.5.7":{"node_abi":1,"v8":"3.6"},"0.5.8":{"node_abi":1,"v8":"3.6"},"0.5.9":{"node_abi":1,"v8":"3.6"},"0.5.10":{"node_abi":1,"v8":"3.7"},"0.6.0":{"node_abi":1,"v8":"3.6"},"0.6.1":{"node_abi":1,"v8":"3.6"},"0.6.2":{"node_abi":1,"v8":"3.6"},"0.6.3":{"node_abi":1,"v8":"3.6"},"0.6.4":{"node_abi":1,"v8":"3.6"},"0.6.5":{"node_abi":1,"v8":"3.6"},"0.6.6":{"node_abi":1,"v8":"3.6"},"0.6.7":{"node_abi":1,"v8":"3.6"},"0.6.8":{"node_abi":1,"v8":"3.6"},"0.6.9":{"node_abi":1,"v8":"3.6"},"0.6.10":{"node_abi":1,"v8":"3.6"},"0.6.11":{"node_abi":1,"v8":"3.6"},"0.6.12":{"node_abi":1,"v8":"3.6"},"0.6.13":{"node_abi":1,"v8":"3.6"},"0.6.14":{"node_abi":1,"v8":"3.6"},"0.6.15":{"node_abi":1,"v8":"3.6"},"0.6.16":{"node_abi":1,"v8":"3.6"},"0.6.17":{"node_abi":1,"v8":"3.6"},"0.6.18":{"node_abi":1,"v8":"3.6"},"0.6.19":{"node_abi":1,"v8":"3.6"},"0.6.20":{"node_abi":1,"v8":"3.6"},"0.6.21":{"node_abi":1,"v8":"3.6"},"0.7.0":{"node_abi":1,"v8":"3.8"},"0.7.1":{"node_abi":1,"v8":"3.8"},"0.7.2":{"node_abi":1,"v8":"3.8"},"0.7.3":{"node_abi":1,"v8":"3.9"},"0.7.4":{"node_abi":1,"v8":"3.9"},"0.7.5":{"node_abi":1,"v8":"3.9"},"0.7.6":{"node_abi":1,"v8":"3.9"},"0.7.7":{"node_abi":1,"v8":"3.9"},"0.7.8":{"node_abi":1,"v8":"3.9"},"0.7.9":{"node_abi":1,"v8":"3.11"},"0.7.10":{"node_abi":1,"v8":"3.9"},"0.7.11":{"node_abi":1,"v8":"3.11"},"0.7.12":{"node_abi":1,"v8":"3.11"},"0.8.0":{"node_abi":1,"v8":"3.11"},"0.8.1":{"node_abi":1,"v8":"3.11"},"0.8.2":{"node_abi":1,"v8":"3.11"},"0.8.3":{"node_abi":1,"v8":"3.11"},"0.8.4":{"node_abi":1,"v8":"3.11"},"0.8.5":{"node_abi":1,"v8":"3.11"},"0.8.6":{"node_abi":1,"v8":"3.11"},"0.8.7":{"node_abi":1,"v8":"3.11"},"0.8.8":{"node_abi":1,"v8":"3.11"},"0.8.9":{"node_abi":1,"v8":"3.11"},"0.8.10":{"node_abi":1,"v8":"3.11"},"0.8.11":{"node_abi":1,"v8":"3.11"},"0.8.12":{"node_abi":1,"v8":"3.11"},"0.8.13":{"node_abi":1,"v8":"3.11"},"0.8.14":{"node_abi":1,"v8":"3.11"},"0.8.15":{"node_abi":1,"v8":"3.11"},"0.8.16":{"node_abi":1,"v8":"3.11"},"0.8.17":{"node_abi":1,"v8":"3.11"},"0.8.18":{"node_abi":1,"v8":"3.11"},"0.8.19":{"node_abi":1,"v8":"3.11"},"0.8.20":{"node_abi":1,"v8":"3.11"},"0.8.21":{"node_abi":1,"v8":"3.11"},"0.8.22":{"node_abi":1,"v8":"3.11"},"0.8.23":{"node_abi":1,"v8":"3.11"},"0.8.24":{"node_abi":1,"v8":"3.11"},"0.8.25":{"node_abi":1,"v8":"3.11"},"0.8.26":{"node_abi":1,"v8":"3.11"},"0.8.27":{"node_abi":1,"v8":"3.11"},"0.8.28":{"node_abi":1,"v8":"3.11"},"0.9.0":{"node_abi":1,"v8":"3.11"},"0.9.1":{"node_abi":10,"v8":"3.11"},"0.9.2":{"node_abi":10,"v8":"3.11"},"0.9.3":{"node_abi":10,"v8":"3.13"},"0.9.4":{"node_abi":10,"v8":"3.13"},"0.9.5":{"node_abi":10,"v8":"3.13"},"0.9.6":{"node_abi":10,"v8":"3.15"},"0.9.7":{"node_abi":10,"v8":"3.15"},"0.9.8":{"node_abi":10,"v8":"3.15"},"0.9.9":{"node_abi":11,"v8":"3.15"},"0.9.10":{"node_abi":11,"v8":"3.15"},"0.9.11":{"node_abi":11,"v8":"3.14"},"0.9.12":{"node_abi":11,"v8":"3.14"},"0.10.0":{"node_abi":11,"v8":"3.14"},"0.10.1":{"node_abi":11,"v8":"3.14"},"0.10.2":{"node_abi":11,"v8":"3.14"},"0.10.3":{"node_abi":11,"v8":"3.14"},"0.10.4":{"node_abi":11,"v8":"3.14"},"0.10.5":{"node_abi":11,"v8":"3.14"},"0.10.6":{"node_abi":11,"v8":"3.14"},"0.10.7":{"node_abi":11,"v8":"3.14"},"0.10.8":{"node_abi":11,"v8":"3.14"},"0.10.9":{"node_abi":11,"v8":"3.14"},"0.10.10":{"node_abi":11,"v8":"3.14"},"0.10.11":{"node_abi":11,"v8":"3.14"},"0.10.12":{"node_abi":11,"v8":"3.14"},"0.10.13":{"node_abi":11,"v8":"3.14"},"0.10.14":{"node_abi":11,"v8":"3.14"},"0.10.15":{"node_abi":11,"v8":"3.14"},"0.10.16":{"node_abi":11,"v8":"3.14"},"0.10.17":{"node_abi":11,"v8":"3.14"},"0.10.18":{"node_abi":11,"v8":"3.14"},"0.10.19":{"node_abi":11,"v8":"3.14"},"0.10.20":{"node_abi":11,"v8":"3.14"},"0.10.21":{"node_abi":11,"v8":"3.14"},"0.10.22":{"node_abi":11,"v8":"3.14"},"0.10.23":{"node_abi":11,"v8":"3.14"},"0.10.24":{"node_abi":11,"v8":"3.14"},"0.10.25":{"node_abi":11,"v8":"3.14"},"0.10.26":{"node_abi":11,"v8":"3.14"},"0.10.27":{"node_abi":11,"v8":"3.14"},"0.10.28":{"node_abi":11,"v8":"3.14"},"0.10.29":{"node_abi":11,"v8":"3.14"},"0.10.30":{"node_abi":11,"v8":"3.14"},"0.10.31":{"node_abi":11,"v8":"3.14"},"0.10.32":{"node_abi":11,"v8":"3.14"},"0.10.33":{"node_abi":11,"v8":"3.14"},"0.10.34":{"node_abi":11,"v8":"3.14"},"0.10.35":{"node_abi":11,"v8":"3.14"},"0.10.36":{"node_abi":11,"v8":"3.14"},"0.10.37":{"node_abi":11,"v8":"3.14"},"0.10.38":{"node_abi":11,"v8":"3.14"},"0.10.39":{"node_abi":11,"v8":"3.14"},"0.10.40":{"node_abi":11,"v8":"3.14"},"0.10.41":{"node_abi":11,"v8":"3.14"},"0.10.42":{"node_abi":11,"v8":"3.14"},"0.10.43":{"node_abi":11,"v8":"3.14"},"0.10.44":{"node_abi":11,"v8":"3.14"},"0.10.45":{"node_abi":11,"v8":"3.14"},"0.10.46":{"node_abi":11,"v8":"3.14"},"0.10.47":{"node_abi":11,"v8":"3.14"},"0.10.48":{"node_abi":11,"v8":"3.14"},"0.11.0":{"node_abi":12,"v8":"3.17"},"0.11.1":{"node_abi":12,"v8":"3.18"},"0.11.2":{"node_abi":12,"v8":"3.19"},"0.11.3":{"node_abi":12,"v8":"3.19"},"0.11.4":{"node_abi":12,"v8":"3.20"},"0.11.5":{"node_abi":12,"v8":"3.20"},"0.11.6":{"node_abi":12,"v8":"3.20"},"0.11.7":{"node_abi":12,"v8":"3.20"},"0.11.8":{"node_abi":13,"v8":"3.21"},"0.11.9":{"node_abi":13,"v8":"3.22"},"0.11.10":{"node_abi":13,"v8":"3.22"},"0.11.11":{"node_abi":14,"v8":"3.22"},"0.11.12":{"node_abi":14,"v8":"3.22"},"0.11.13":{"node_abi":14,"v8":"3.25"},"0.11.14":{"node_abi":14,"v8":"3.26"},"0.11.15":{"node_abi":14,"v8":"3.28"},"0.11.16":{"node_abi":14,"v8":"3.28"},"0.12.0":{"node_abi":14,"v8":"3.28"},"0.12.1":{"node_abi":14,"v8":"3.28"},"0.12.2":{"node_abi":14,"v8":"3.28"},"0.12.3":{"node_abi":14,"v8":"3.28"},"0.12.4":{"node_abi":14,"v8":"3.28"},"0.12.5":{"node_abi":14,"v8":"3.28"},"0.12.6":{"node_abi":14,"v8":"3.28"},"0.12.7":{"node_abi":14,"v8":"3.28"},"0.12.8":{"node_abi":14,"v8":"3.28"},"0.12.9":{"node_abi":14,"v8":"3.28"},"0.12.10":{"node_abi":14,"v8":"3.28"},"0.12.11":{"node_abi":14,"v8":"3.28"},"0.12.12":{"node_abi":14,"v8":"3.28"},"0.12.13":{"node_abi":14,"v8":"3.28"},"0.12.14":{"node_abi":14,"v8":"3.28"},"0.12.15":{"node_abi":14,"v8":"3.28"},"0.12.16":{"node_abi":14,"v8":"3.28"},"0.12.17":{"node_abi":14,"v8":"3.28"},"0.12.18":{"node_abi":14,"v8":"3.28"},"1.0.0":{"node_abi":42,"v8":"3.31"},"1.0.1":{"node_abi":42,"v8":"3.31"},"1.0.2":{"node_abi":42,"v8":"3.31"},"1.0.3":{"node_abi":42,"v8":"4.1"},"1.0.4":{"node_abi":42,"v8":"4.1"},"1.1.0":{"node_abi":43,"v8":"4.1"},"1.2.0":{"node_abi":43,"v8":"4.1"},"1.3.0":{"node_abi":43,"v8":"4.1"},"1.4.1":{"node_abi":43,"v8":"4.1"},"1.4.2":{"node_abi":43,"v8":"4.1"},"1.4.3":{"node_abi":43,"v8":"4.1"},"1.5.0":{"node_abi":43,"v8":"4.1"},"1.5.1":{"node_abi":43,"v8":"4.1"},"1.6.0":{"node_abi":43,"v8":"4.1"},"1.6.1":{"node_abi":43,"v8":"4.1"},"1.6.2":{"node_abi":43,"v8":"4.1"},"1.6.3":{"node_abi":43,"v8":"4.1"},"1.6.4":{"node_abi":43,"v8":"4.1"},"1.7.1":{"node_abi":43,"v8":"4.1"},"1.8.1":{"node_abi":43,"v8":"4.1"},"1.8.2":{"node_abi":43,"v8":"4.1"},"1.8.3":{"node_abi":43,"v8":"4.1"},"1.8.4":{"node_abi":43,"v8":"4.1"},"2.0.0":{"node_abi":44,"v8":"4.2"},"2.0.1":{"node_abi":44,"v8":"4.2"},"2.0.2":{"node_abi":44,"v8":"4.2"},"2.1.0":{"node_abi":44,"v8":"4.2"},"2.2.0":{"node_abi":44,"v8":"4.2"},"2.2.1":{"node_abi":44,"v8":"4.2"},"2.3.0":{"node_abi":44,"v8":"4.2"},"2.3.1":{"node_abi":44,"v8":"4.2"},"2.3.2":{"node_abi":44,"v8":"4.2"},"2.3.3":{"node_abi":44,"v8":"4.2"},"2.3.4":{"node_abi":44,"v8":"4.2"},"2.4.0":{"node_abi":44,"v8":"4.2"},"2.5.0":{"node_abi":44,"v8":"4.2"},"3.0.0":{"node_abi":45,"v8":"4.4"},"3.1.0":{"node_abi":45,"v8":"4.4"},"3.2.0":{"node_abi":45,"v8":"4.4"},"3.3.0":{"node_abi":45,"v8":"4.4"},"3.3.1":{"node_abi":45,"v8":"4.4"},"4.0.0":{"node_abi":46,"v8":"4.5"},"4.1.0":{"node_abi":46,"v8":"4.5"},"4.1.1":{"node_abi":46,"v8":"4.5"},"4.1.2":{"node_abi":46,"v8":"4.5"},"4.2.0":{"node_abi":46,"v8":"4.5"},"4.2.1":{"node_abi":46,"v8":"4.5"},"4.2.2":{"node_abi":46,"v8":"4.5"},"4.2.3":{"node_abi":46,"v8":"4.5"},"4.2.4":{"node_abi":46,"v8":"4.5"},"4.2.5":{"node_abi":46,"v8":"4.5"},"4.2.6":{"node_abi":46,"v8":"4.5"},"4.3.0":{"node_abi":46,"v8":"4.5"},"4.3.1":{"node_abi":46,"v8":"4.5"},"4.3.2":{"node_abi":46,"v8":"4.5"},"4.4.0":{"node_abi":46,"v8":"4.5"},"4.4.1":{"node_abi":46,"v8":"4.5"},"4.4.2":{"node_abi":46,"v8":"4.5"},"4.4.3":{"node_abi":46,"v8":"4.5"},"4.4.4":{"node_abi":46,"v8":"4.5"},"4.4.5":{"node_abi":46,"v8":"4.5"},"4.4.6":{"node_abi":46,"v8":"4.5"},"4.4.7":{"node_abi":46,"v8":"4.5"},"4.5.0":{"node_abi":46,"v8":"4.5"},"4.6.0":{"node_abi":46,"v8":"4.5"},"4.6.1":{"node_abi":46,"v8":"4.5"},"4.6.2":{"node_abi":46,"v8":"4.5"},"4.7.0":{"node_abi":46,"v8":"4.5"},"4.7.1":{"node_abi":46,"v8":"4.5"},"4.7.2":{"node_abi":46,"v8":"4.5"},"4.7.3":{"node_abi":46,"v8":"4.5"},"4.8.0":{"node_abi":46,"v8":"4.5"},"4.8.1":{"node_abi":46,"v8":"4.5"},"4.8.2":{"node_abi":46,"v8":"4.5"},"4.8.3":{"node_abi":46,"v8":"4.5"},"4.8.4":{"node_abi":46,"v8":"4.5"},"4.8.5":{"node_abi":46,"v8":"4.5"},"4.8.6":{"node_abi":46,"v8":"4.5"},"4.8.7":{"node_abi":46,"v8":"4.5"},"4.9.0":{"node_abi":46,"v8":"4.5"},"4.9.1":{"node_abi":46,"v8":"4.5"},"5.0.0":{"node_abi":47,"v8":"4.6"},"5.1.0":{"node_abi":47,"v8":"4.6"},"5.1.1":{"node_abi":47,"v8":"4.6"},"5.2.0":{"node_abi":47,"v8":"4.6"},"5.3.0":{"node_abi":47,"v8":"4.6"},"5.4.0":{"node_abi":47,"v8":"4.6"},"5.4.1":{"node_abi":47,"v8":"4.6"},"5.5.0":{"node_abi":47,"v8":"4.6"},"5.6.0":{"node_abi":47,"v8":"4.6"},"5.7.0":{"node_abi":47,"v8":"4.6"},"5.7.1":{"node_abi":47,"v8":"4.6"},"5.8.0":{"node_abi":47,"v8":"4.6"},"5.9.0":{"node_abi":47,"v8":"4.6"},"5.9.1":{"node_abi":47,"v8":"4.6"},"5.10.0":{"node_abi":47,"v8":"4.6"},"5.10.1":{"node_abi":47,"v8":"4.6"},"5.11.0":{"node_abi":47,"v8":"4.6"},"5.11.1":{"node_abi":47,"v8":"4.6"},"5.12.0":{"node_abi":47,"v8":"4.6"},"6.0.0":{"node_abi":48,"v8":"5.0"},"6.1.0":{"node_abi":48,"v8":"5.0"},"6.2.0":{"node_abi":48,"v8":"5.0"},"6.2.1":{"node_abi":48,"v8":"5.0"},"6.2.2":{"node_abi":48,"v8":"5.0"},"6.3.0":{"node_abi":48,"v8":"5.0"},"6.3.1":{"node_abi":48,"v8":"5.0"},"6.4.0":{"node_abi":48,"v8":"5.0"},"6.5.0":{"node_abi":48,"v8":"5.1"},"6.6.0":{"node_abi":48,"v8":"5.1"},"6.7.0":{"node_abi":48,"v8":"5.1"},"6.8.0":{"node_abi":48,"v8":"5.1"},"6.8.1":{"node_abi":48,"v8":"5.1"},"6.9.0":{"node_abi":48,"v8":"5.1"},"6.9.1":{"node_abi":48,"v8":"5.1"},"6.9.2":{"node_abi":48,"v8":"5.1"},"6.9.3":{"node_abi":48,"v8":"5.1"},"6.9.4":{"node_abi":48,"v8":"5.1"},"6.9.5":{"node_abi":48,"v8":"5.1"},"6.10.0":{"node_abi":48,"v8":"5.1"},"6.10.1":{"node_abi":48,"v8":"5.1"},"6.10.2":{"node_abi":48,"v8":"5.1"},"6.10.3":{"node_abi":48,"v8":"5.1"},"6.11.0":{"node_abi":48,"v8":"5.1"},"6.11.1":{"node_abi":48,"v8":"5.1"},"6.11.2":{"node_abi":48,"v8":"5.1"},"6.11.3":{"node_abi":48,"v8":"5.1"},"6.11.4":{"node_abi":48,"v8":"5.1"},"6.11.5":{"node_abi":48,"v8":"5.1"},"6.12.0":{"node_abi":48,"v8":"5.1"},"6.12.1":{"node_abi":48,"v8":"5.1"},"6.12.2":{"node_abi":48,"v8":"5.1"},"6.12.3":{"node_abi":48,"v8":"5.1"},"6.13.0":{"node_abi":48,"v8":"5.1"},"6.13.1":{"node_abi":48,"v8":"5.1"},"6.14.0":{"node_abi":48,"v8":"5.1"},"6.14.1":{"node_abi":48,"v8":"5.1"},"6.14.2":{"node_abi":48,"v8":"5.1"},"6.14.3":{"node_abi":48,"v8":"5.1"},"6.14.4":{"node_abi":48,"v8":"5.1"},"7.0.0":{"node_abi":51,"v8":"5.4"},"7.1.0":{"node_abi":51,"v8":"5.4"},"7.2.0":{"node_abi":51,"v8":"5.4"},"7.2.1":{"node_abi":51,"v8":"5.4"},"7.3.0":{"node_abi":51,"v8":"5.4"},"7.4.0":{"node_abi":51,"v8":"5.4"},"7.5.0":{"node_abi":51,"v8":"5.4"},"7.6.0":{"node_abi":51,"v8":"5.5"},"7.7.0":{"node_abi":51,"v8":"5.5"},"7.7.1":{"node_abi":51,"v8":"5.5"},"7.7.2":{"node_abi":51,"v8":"5.5"},"7.7.3":{"node_abi":51,"v8":"5.5"},"7.7.4":{"node_abi":51,"v8":"5.5"},"7.8.0":{"node_abi":51,"v8":"5.5"},"7.9.0":{"node_abi":51,"v8":"5.5"},"7.10.0":{"node_abi":51,"v8":"5.5"},"7.10.1":{"node_abi":51,"v8":"5.5"},"8.0.0":{"node_abi":57,"v8":"5.8"},"8.1.0":{"node_abi":57,"v8":"5.8"},"8.1.1":{"node_abi":57,"v8":"5.8"},"8.1.2":{"node_abi":57,"v8":"5.8"},"8.1.3":{"node_abi":57,"v8":"5.8"},"8.1.4":{"node_abi":57,"v8":"5.8"},"8.2.0":{"node_abi":57,"v8":"5.8"},"8.2.1":{"node_abi":57,"v8":"5.8"},"8.3.0":{"node_abi":57,"v8":"6.0"},"8.4.0":{"node_abi":57,"v8":"6.0"},"8.5.0":{"node_abi":57,"v8":"6.0"},"8.6.0":{"node_abi":57,"v8":"6.0"},"8.7.0":{"node_abi":57,"v8":"6.1"},"8.8.0":{"node_abi":57,"v8":"6.1"},"8.8.1":{"node_abi":57,"v8":"6.1"},"8.9.0":{"node_abi":57,"v8":"6.1"},"8.9.1":{"node_abi":57,"v8":"6.1"},"8.9.2":{"node_abi":57,"v8":"6.1"},"8.9.3":{"node_abi":57,"v8":"6.1"},"8.9.4":{"node_abi":57,"v8":"6.1"},"8.10.0":{"node_abi":57,"v8":"6.2"},"8.11.0":{"node_abi":57,"v8":"6.2"},"8.11.1":{"node_abi":57,"v8":"6.2"},"8.11.2":{"node_abi":57,"v8":"6.2"},"8.11.3":{"node_abi":57,"v8":"6.2"},"8.11.4":{"node_abi":57,"v8":"6.2"},"8.12.0":{"node_abi":57,"v8":"6.2"},"9.0.0":{"node_abi":59,"v8":"6.2"},"9.1.0":{"node_abi":59,"v8":"6.2"},"9.2.0":{"node_abi":59,"v8":"6.2"},"9.2.1":{"node_abi":59,"v8":"6.2"},"9.3.0":{"node_abi":59,"v8":"6.2"},"9.4.0":{"node_abi":59,"v8":"6.2"},"9.5.0":{"node_abi":59,"v8":"6.2"},"9.6.0":{"node_abi":59,"v8":"6.2"},"9.6.1":{"node_abi":59,"v8":"6.2"},"9.7.0":{"node_abi":59,"v8":"6.2"},"9.7.1":{"node_abi":59,"v8":"6.2"},"9.8.0":{"node_abi":59,"v8":"6.2"},"9.9.0":{"node_abi":59,"v8":"6.2"},"9.10.0":{"node_abi":59,"v8":"6.2"},"9.10.1":{"node_abi":59,"v8":"6.2"},"9.11.0":{"node_abi":59,"v8":"6.2"},"9.11.1":{"node_abi":59,"v8":"6.2"},"9.11.2":{"node_abi":59,"v8":"6.2"},"10.0.0":{"node_abi":64,"v8":"6.6"},"10.1.0":{"node_abi":64,"v8":"6.6"},"10.2.0":{"node_abi":64,"v8":"6.6"},"10.2.1":{"node_abi":64,"v8":"6.6"},"10.3.0":{"node_abi":64,"v8":"6.6"},"10.4.0":{"node_abi":64,"v8":"6.7"},"10.4.1":{"node_abi":64,"v8":"6.7"},"10.5.0":{"node_abi":64,"v8":"6.7"},"10.6.0":{"node_abi":64,"v8":"6.7"},"10.7.0":{"node_abi":64,"v8":"6.7"},"10.8.0":{"node_abi":64,"v8":"6.7"},"10.9.0":{"node_abi":64,"v8":"6.8"},"10.10.0":{"node_abi":64,"v8":"6.8"},"10.11.0":{"node_abi":64,"v8":"6.8"},"10.12.0":{"node_abi":64,"v8":"6.8"},"10.13.0":{"node_abi":64,"v8":"6.8"},"11.0.0":{"node_abi":67,"v8":"7.0"},"11.1.0":{"node_abi":67,"v8":"7.0"}}')},5537:e=>{"use strict";e.exports=JSON.parse('{"assert":true,"async_hooks":">= 8","buffer_ieee754":"< 0.9.7","buffer":true,"child_process":true,"cluster":true,"console":true,"constants":true,"crypto":true,"_debugger":"< 8","dgram":true,"dns":true,"domain":true,"events":true,"freelist":"< 6","fs":true,"fs/promises":">= 10 && < 10.1","_http_agent":">= 0.11.1","_http_client":">= 0.11.1","_http_common":">= 0.11.1","_http_incoming":">= 0.11.1","_http_outgoing":">= 0.11.1","_http_server":">= 0.11.1","http":true,"http2":">= 8.8","https":true,"inspector":">= 8.0.0","_linklist":"< 8","module":true,"net":true,"node-inspect/lib/_inspect":">= 7.6.0","node-inspect/lib/internal/inspect_client":">= 7.6.0","node-inspect/lib/internal/inspect_repl":">= 7.6.0","os":true,"path":true,"perf_hooks":">= 8.5","process":">= 1","punycode":true,"querystring":true,"readline":true,"repl":true,"smalloc":">= 0.11.5 && < 3","_stream_duplex":">= 0.9.4","_stream_transform":">= 0.9.4","_stream_wrap":">= 1.4.1","_stream_passthrough":">= 0.9.4","_stream_readable":">= 0.9.4","_stream_writable":">= 0.9.4","stream":true,"string_decoder":true,"sys":true,"timers":true,"_tls_common":">= 0.11.13","_tls_legacy":">= 0.11.3 && < 10","_tls_wrap":">= 0.11.3","tls":true,"trace_events":">= 10","tty":true,"url":true,"util":true,"v8/tools/arguments":">= 10","v8/tools/codemap":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/consarray":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/csvparser":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/logreader":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/profile_view":[">= 4.4.0 && < 5",">= 5.2.0"],"v8/tools/splaytree":[">= 4.4.0 && < 5",">= 5.2.0"],"v8":">= 1","vm":true,"worker_threads":">= 11.7","zlib":true}')},2357:e=>{"use strict";e.exports=__webpack_require__(357)},4293:e=>{"use strict";e.exports=__webpack_require__(293)},3129:e=>{"use strict";e.exports=__webpack_require__(129)},7619:e=>{"use strict";e.exports=__webpack_require__(619)},6417:e=>{"use strict";e.exports=__webpack_require__(417)},8614:e=>{"use strict";e.exports=__webpack_require__(614)},5747:e=>{"use strict";e.exports=__webpack_require__(747)},2087:e=>{"use strict";e.exports=__webpack_require__(87)},5622:e=>{"use strict";e.exports=__webpack_require__(622)},2413:e=>{"use strict";e.exports=__webpack_require__(413)},8835:e=>{"use strict";e.exports=__webpack_require__(835)},1669:e=>{"use strict";e.exports=__webpack_require__(669)}};var __webpack_module_cache__={};function __nested_webpack_require_970479__(e){if(__webpack_module_cache__[e]){return __webpack_module_cache__[e].exports}var t=__webpack_module_cache__[e]={exports:{}};var r=true;try{__webpack_modules__[e].call(t.exports,t,t.exports,__nested_webpack_require_970479__);r=false}finally{if(r)delete __webpack_module_cache__[e]}return t.exports}__nested_webpack_require_970479__.ab=__dirname+"/";return __nested_webpack_require_970479__(6265)})()},300:(e,t,r)=>{e.exports=r(901)},357:e=>{"use strict";e.exports=require("assert")},293:e=>{"use strict";e.exports=require("buffer")},129:e=>{"use strict";e.exports=require("child_process")},619:e=>{"use strict";e.exports=require("constants")},417:e=>{"use strict";e.exports=require("crypto")},614:e=>{"use strict";e.exports=require("events")},747:e=>{"use strict";e.exports=require("fs")},87:e=>{"use strict";e.exports=require("os")},622:e=>{"use strict";e.exports=require("path")},413:e=>{"use strict";e.exports=require("stream")},835:e=>{"use strict";e.exports=require("url")},669:e=>{"use strict";e.exports=require("util")}};var __webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(t!==undefined){return t.exports}var r=__webpack_module_cache__[e]={exports:{}};var s=true;try{__webpack_modules__[e](r,r.exports,__webpack_require__);s=false}finally{if(s)delete __webpack_module_cache__[e]}return r.exports}if(typeof __webpack_require__!=="undefined")__webpack_require__.ab=__dirname+"/";var __webpack_exports__=__webpack_require__(300);module.exports=__webpack_exports__})();