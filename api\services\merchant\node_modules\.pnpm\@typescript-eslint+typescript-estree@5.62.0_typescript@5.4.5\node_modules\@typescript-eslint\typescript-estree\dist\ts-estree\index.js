"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TSESTree = exports.AST_TOKEN_TYPES = exports.AST_NODE_TYPES = void 0;
// for simplicity and backwards-compatibility
var types_1 = require("@typescript-eslint/types");
Object.defineProperty(exports, "AST_NODE_TYPES", { enumerable: true, get: function () { return types_1.AST_NODE_TYPES; } });
Object.defineProperty(exports, "AST_TOKEN_TYPES", { enumerable: true, get: function () { return types_1.AST_TOKEN_TYPES; } });
Object.defineProperty(exports, "TSESTree", { enumerable: true, get: function () { return types_1.TSESTree; } });
__exportStar(require("./ts-nodes"), exports);
__exportStar(require("./estree-to-ts-node-types"), exports);
//# sourceMappingURL=index.js.map