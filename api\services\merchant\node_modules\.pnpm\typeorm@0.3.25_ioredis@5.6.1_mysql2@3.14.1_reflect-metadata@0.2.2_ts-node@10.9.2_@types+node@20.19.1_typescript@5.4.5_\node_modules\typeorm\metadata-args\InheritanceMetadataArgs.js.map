{"version": 3, "sources": ["../../src/metadata-args/InheritanceMetadataArgs.ts"], "names": [], "mappings": "", "file": "InheritanceMetadataArgs.js", "sourcesContent": ["import { ColumnOptions } from \"../decorator/options/ColumnOptions\"\n\n/**\n * Arguments for InheritanceMetadata class.\n */\nexport interface InheritanceMetadataArgs {\n    /**\n     * Class to which inheritance is applied.\n     */\n    readonly target?: Function | string\n\n    /**\n     * Inheritance pattern.\n     */\n    readonly pattern: \"STI\" /*|\"CTI\"*/\n\n    /**\n     * Column used as inheritance discriminator column.\n     */\n    readonly column?: ColumnOptions\n}\n"], "sourceRoot": ".."}