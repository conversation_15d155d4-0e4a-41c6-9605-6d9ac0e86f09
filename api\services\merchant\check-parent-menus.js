const mysql = require('mysql2/promise');

async function checkParentMenus() {
  try {
    const connection = await mysql.createConnection({
      host: '127.0.0.1',
      port: 3306,
      user: 'root',
      password: 'wap.336101',
      database: 'merchant_service_db'
    });

    console.log('🏠 顶级菜单 (parentId=0):');
    const [topMenus] = await connection.execute(`
      SELECT id, name, router, type, orderNum 
      FROM merchant_sys_menu 
      WHERE parentId = 0 
      ORDER BY orderNum, id
    `);
    console.table(topMenus);
    
    console.log('\n📊 菜单层级统计:');
    const [stats] = await connection.execute(`
      SELECT 
        parentId,
        COUNT(*) as count,
        GROUP_CONCAT(name ORDER BY orderNum, id) as menu_names
      FROM merchant_sys_menu 
      GROUP BY parentId 
      ORDER BY parentId
    `);
    console.table(stats);
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

checkParentMenus();
