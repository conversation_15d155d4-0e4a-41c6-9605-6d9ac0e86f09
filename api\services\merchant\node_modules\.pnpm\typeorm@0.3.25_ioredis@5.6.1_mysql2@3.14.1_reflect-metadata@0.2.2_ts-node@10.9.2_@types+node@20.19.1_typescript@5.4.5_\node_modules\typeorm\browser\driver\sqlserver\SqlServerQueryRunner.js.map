{"version": 3, "sources": ["../browser/src/driver/sqlserver/SqlServerQueryRunner.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,8BAA8B,CAAA;AAC/D,OAAO,EAAE,+BAA+B,EAAE,MAAM,6CAA6C,CAAA;AAC7F,OAAO,EAAE,0BAA0B,EAAE,MAAM,wCAAwC,CAAA;AAEnF,OAAO,EAAE,eAAe,EAAE,MAAM,oCAAoC,CAAA;AACpE,OAAO,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAA;AACxD,OAAO,EAAE,WAAW,EAAE,MAAM,gCAAgC,CAAA;AAG5D,OAAO,EAAE,KAAK,EAAE,MAAM,kCAAkC,CAAA;AACxD,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AAEpE,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAC5E,OAAO,EAAE,UAAU,EAAE,MAAM,uCAAuC,CAAA;AAClE,OAAO,EAAE,WAAW,EAAE,MAAM,wCAAwC,CAAA;AACpE,OAAO,EAAE,IAAI,EAAE,MAAM,gCAAgC,CAAA;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,oCAAoC,CAAA;AACtE,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAA;AAC5D,OAAO,EAAE,QAAQ,EAAE,MAAM,qBAAqB,CAAA;AAC9C,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAA;AAGhC,OAAO,EAAE,iBAAiB,EAAE,MAAM,4BAA4B,CAAA;AAK9D;;GAEG;AACH,MAAM,OAAO,oBACT,SAAQ,eAAe;IAkBvB,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAY,MAAuB,EAAE,IAAqB;QACtD,KAAK,EAAE,CAAA;QAXX,4EAA4E;QAC5E,qBAAqB;QACrB,4EAA4E;QAEpE,SAAI,GAAc,IAAI,SAAS,EAAE,CAAA;QAQrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAA;QACnC,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;QACxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;;OAGG;IACH,OAAO;QACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;;OAGG;IACH,OAAO;QACH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAA;QACtB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAAC,cAA+B;QAClD,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;QAC/B,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;QAC9D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;YAChC,MAAM,GAAG,CAAA;QACb,CAAC;QACD,MAAM,IAAI,OAAO,CAAO,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE;YACvC,MAAM,mBAAmB,GAAG,CAAC,GAAQ,EAAE,EAAE;gBACrC,IAAI,GAAG,EAAE,CAAC;oBACN,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;oBAChC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBACpB,CAAC;gBACD,EAAE,EAAE,CAAA;YACR,CAAC,CAAA;YAED,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO;oBACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;oBACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAA;gBAC3C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;gBAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAA;gBACpD,IAAI,cAAc,EAAE,CAAC;oBACjB,IAAI,CAAC,kBAAkB,CAAC,KAAK,CACzB,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,EAC1C,mBAAmB,CACtB,CAAA;oBACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAC3B,kCAAkC,GAAG,cAAc,CACtD,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;gBACtD,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,MAAM,IAAI,CAAC,KAAK,CACZ,4BAA4B,IAAI,CAAC,gBAAgB,EAAE,CACtD,CAAA;gBACD,EAAE,EAAE,CAAA;YACR,CAAC;YACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;QAEF,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,iBAAiB;QACnB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,yBAAyB,CAAC,CAAA;QAE3D,IAAI,IAAI,CAAC,gBAAgB,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,GAAQ,EAAE,EAAE;oBAC9C,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;oBAChC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;oBAE9B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,wBAAwB,CAAC,CAAA;oBAE1D,EAAE,EAAE,CAAA;oBACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAA;oBACzC,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;gBAC9B,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;QACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;IAC9B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,mBAAmB;QACrB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,IAAI,CAAC,IAAI,CAAC,mBAAmB;YAAE,MAAM,IAAI,0BAA0B,EAAE,CAAA;QAErE,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;QAE7D,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,KAAK,CACZ,gCAAgC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAC9D,CAAA;YACD,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;QAC9B,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,OAAO,CAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAClC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAQ,EAAE,EAAE;oBAChD,IAAI,GAAG;wBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,mBAAmB,GAAG,KAAK,CAAA;oBAChC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAA;oBAE9B,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,0BAA0B,CAAC,CAAA;oBAE5D,EAAE,EAAE,CAAA;oBACJ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;oBAC3C,IAAI,CAAC,gBAAgB,IAAI,CAAC,CAAA;gBAC9B,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK,CACP,KAAa,EACb,UAAkB,EAClB,mBAAmB,GAAG,KAAK;QAE3B,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QAEzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,CAAC,CAAA;QAElE,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAA;QAEjD,IAAI,CAAC;YACD,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO;gBACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;gBACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAA;YAC3C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CACzC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAC5D,CAAA;YACD,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;gBAClC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;oBACpC,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;oBACtC,IAAI,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC9C,MAAM,cAAc,GAChB,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,CAAA;wBACnD,IAAI,cAAc,EAAE,CAAC;4BACjB,OAAO,CAAC,KAAK,CACT,aAAa,EACb,cAAc,EACd,SAAS,CAAC,KAAK,CAClB,CAAA;wBACL,CAAC;6BAAM,CAAC;4BACJ,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,KAAK,CAAC,CAAA;wBACjD,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;oBAC3C,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;YAEjC,MAAM,GAAG,GAAG,MAAM,IAAI,OAAO,CAAM,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;gBAC5C,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,GAAQ,EAAE,GAAQ,EAAE,EAAE;oBACxC,oDAAoD;oBACpD,MAAM,qBAAqB,GACvB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,qBAAqB,CAAA;oBAC7C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAA;oBAC/B,MAAM,kBAAkB,GAAG,YAAY,GAAG,cAAc,CAAA;oBAExD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,IAAI,EACJ,kBAAkB,EAClB,GAAG,EACH,SAAS,CACZ,CAAA;oBAED,IACI,qBAAqB;wBACrB,kBAAkB,GAAG,qBAAqB,EAC5C,CAAC;wBACC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CACtC,kBAAkB,EAClB,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;oBACL,CAAC;oBAED,IAAI,GAAG,EAAE,CAAC;wBACN,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,CAAA;oBACtD,CAAC;oBAED,EAAE,CAAC,GAAG,CAAC,CAAA;gBACX,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,MAAM,MAAM,GAAG,IAAI,WAAW,EAAE,CAAA;YAEhC,IAAI,GAAG,EAAE,cAAc,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC,SAAS,CAAA;YAClC,CAAC;YAED,IAAI,GAAG,EAAE,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;gBACtC,MAAM,CAAC,QAAQ,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAA;YACzC,CAAC;YAED,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAA;YACpD,QAAQ,SAAS,EAAE,CAAC;gBAChB,KAAK,QAAQ;oBACT,+DAA+D;oBAC/D,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAA;oBACjD,MAAK;gBACT;oBACI,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAA;YAClC,CAAC;YAED,IAAI,mBAAmB,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAA;YACjB,CAAC;iBAAM,CAAC;gBACJ,OAAO,MAAM,CAAC,GAAG,CAAA;YACrB,CAAC;QACL,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACD,IAAI,CAAC,WAAW,CAAC,wBAAwB,CACrC,iBAAiB,EACjB,KAAK,EACL,UAAU,EACV,KAAK,EACL,SAAS,EACT,SAAS,EACT,GAAG,CACN,CAAA;YAED,MAAM,GAAG,CAAA;QACb,CAAC;gBAAS,CAAC;YACP,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;YAE9B,OAAO,EAAE,CAAA;QACb,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,MAAM,CACR,KAAa,EACb,UAAkB,EAClB,KAAgB,EAChB,OAAkB;QAElB,IAAI,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,+BAA+B,EAAE,CAAA;QAEhE,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;QAEzC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;QAC/D,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO;YACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,EAAE;YACrC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,CAAC,CAAA;QAC3C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CACzC,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAC5D,CAAA;QACD,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;YAClC,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;gBACpC,MAAM,aAAa,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAA;gBACtC,IAAI,eAAe,CAAC,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC9C,OAAO,CAAC,KAAK,CACT,aAAa,EACb,IAAI,CAAC,+BAA+B,CAAC,SAAS,CAAC,EAC/C,SAAS,CAAC,KAAK,CAClB,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,CAAC,CAAA;gBAC3C,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QAEpB,MAAM,aAAa,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAA;QAEhD,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAQ,EAAE,EAAE;YACnC,OAAO,EAAE,CAAA;YACT,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CACvC,GAAG,EACH,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;QACL,CAAC,CAAC,CAAA;QAEF,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YACzB,OAAO,EAAE,CAAA;QACb,CAAC,CAAC,CAAA;QAEF,IAAI,KAAK,EAAE,CAAC;YACR,aAAa,CAAC,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAClC,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACV,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACtC,CAAC;QAED,OAAO,aAAa,CAAA;IACxB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QACd,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACtE,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,QAAiB;QAC9B,MAAM,KAAK,GAAG,QAAQ;YAClB,CAAC,CAAC,kBAAkB,QAAQ,kBAAkB;YAC9C,CAAC,CAAC,+BAA+B,CAAA;QACrC,MAAM,OAAO,GAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACxD,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,QAAgB;QAC9B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,iBAAiB,QAAQ,eAAe,CAC3C,CAAA;QACD,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAA;QAC/B,OAAO,CAAC,CAAC,IAAI,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB;QACpB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAA;QACxE,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,MAAc;QAC1B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC3B,qBAAqB,MAAM,mBAAmB,CACjD,CAAA;QACD,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAA;QACvC,OAAO,CAAC,CAAC,QAAQ,CAAA;IACrB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB;QAClB,MAAM,kBAAkB,GAAG,MAAM,IAAI,CAAC,KAAK,CACvC,uCAAuC,CAC1C,CAAA;QACD,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,WAA2B;QACtC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC5B,eAAe,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC9D,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,kBAAkB,eAAe,CAAC,QAAQ,yDAAyD,eAAe,CAAC,SAAS,2BAA2B,eAAe,CAAC,MAAM,GAAG,CAAA;QAC5L,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,UAAkB;QAElB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE/D,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC5B,eAAe,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC9D,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,GAAG,GAAG,kBAAkB,eAAe,CAAC,QAAQ,0DAA0D,eAAe,CAAC,SAAS,2BAA2B,eAAe,CAAC,MAAM,0BAA0B,UAAU,GAAG,CAAA;QACjO,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QACpC,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAA;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,QAAgB,EAChB,UAAoB;QAEpB,MAAM,EAAE,GAAG,UAAU;YACjB,CAAC,CAAC,aAAa,QAAQ,+BAA+B,QAAQ,GAAG;YACjE,CAAC,CAAC,oBAAoB,QAAQ,GAAG,CAAA;QACrC,MAAM,IAAI,GAAG,kBAAkB,QAAQ,GAAG,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAiB;QAClD,MAAM,EAAE,GAAG,OAAO;YACd,CAAC,CAAC,aAAa,QAAQ,iCAAiC,QAAQ,GAAG;YACnE,CAAC,CAAC,kBAAkB,QAAQ,GAAG,CAAA;QACnC,MAAM,IAAI,GAAG,oBAAoB,QAAQ,GAAG,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAA;IAC7D,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,YAAY,CACd,UAAkB,EAClB,UAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,UAAU;gBACtB,CAAC,CAAC,iBAAiB,UAAU,0CAA0C,UAAU,SAAS;gBAC1F,CAAC,CAAC,kBAAkB,UAAU,GAAG,CAAA;YACrC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,gBAAgB,UAAU,GAAG,CAAC,CAAC,CAAA;QAC9D,CAAC;aAAM,CAAC;YACJ,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACvC,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YACjD,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;YAC5C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;YAEjD,MAAM,OAAO,GAAG,UAAU;gBACtB,CAAC,CAAC,iBAAiB,MAAM,0CAA0C,MAAM,SAAS;gBAClF,CAAC,CAAC,kBAAkB,MAAM,GAAG,CAAA;YACjC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,gBAAgB,MAAM,GAAG,CAAC,CAAC,CAAA;YAEtD,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;YAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,UAAkB,EAAE,OAAiB;QAClD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,OAAO;gBACnB,CAAC,CAAC,iBAAiB,UAAU,wCAAwC,UAAU,SAAS;gBACxF,CAAC,CAAC,gBAAgB,UAAU,GAAG,CAAA;YACnC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,kBAAkB,UAAU,GAAG,CAAC,CAAC,CAAA;QAChE,CAAC;aAAM,CAAC;YACJ,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACvC,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;YACvC,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;YACjD,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;YAC5C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;YAEjD,MAAM,OAAO,GAAG,OAAO;gBACnB,CAAC,CAAC,iBAAiB,MAAM,wCAAwC,MAAM,SAAS;gBAChF,CAAC,CAAC,gBAAgB,MAAM,GAAG,CAAA;YAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,CAAA;YAClC,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,kBAAkB,MAAM,GAAG,CAAC,CAAC,CAAA;YAExD,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;YAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,KAAY,EACZ,aAAsB,KAAK,EAC3B,oBAA6B,IAAI,EACjC,gBAAyB,IAAI;QAE7B,IAAI,UAAU,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC9C,CAAC;QACD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAC7D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QAE1C,iFAAiF;QACjF,kIAAkI;QAClI,IAAI,iBAAiB;YACjB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC9D,CAAA;QAEL,IAAI,aAAa,EAAE,CAAC;YAChB,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,sFAAsF;gBACtF,IAAI,CAAC,KAAK,CAAC,IAAI;oBACX,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACjD,KAAK,EACL,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;gBACL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBACjD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACrD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,6FAA6F;QAC7F,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAC1D,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,OAAiB,EACjB,kBAA2B,IAAI,EAC/B,cAAuB,IAAI;QAE3B,IAAI,OAAO,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;YACrD,IAAI,CAAC,YAAY;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAC/C,CAAC;QAED,8FAA8F;QAC9F,MAAM,iBAAiB,GAAY,eAAe,CAAA;QAClD,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,qGAAqG;QACrG,wDAAwD;QAExD,IAAI,WAAW,EAAE,CAAC;YACd,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBAC5B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;gBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;QACN,CAAC;QAED,iGAAiG;QACjG,kIAAkI;QAClI,IAAI,eAAe;YACf,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE,CACrC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAC5D,CAAA;QAEL,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAA;QACxC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,iBAAiB,CAAC,CAAC,CAAA;QAE/D,kGAAkG;QAClG,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACzC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAC1D,CAAA;QAED,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACpC,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAC1D,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,IAAU,EACV,mBAA4B,KAAK;QAEjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACxC,IAAI,gBAAgB;YAChB,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC9D,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAqB;QAChC,MAAM,QAAQ,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAA;QACtE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAA;QAE/C,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QACxD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAA;QAC1C,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;IACrD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,cAA8B,EAC9B,YAAoB;QAEpB,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAC/B,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,cAAc,CAAC;YACpD,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAA;QAEjC,iEAAiE;QACjE,IAAI,MAAM,GAAuB,SAAS,CAAA;QAC1C,IAAI,UAAU,GAAuB,SAAS,CAAA;QAC9C,IAAI,YAAY,GAAW,QAAQ,CAAC,IAAI,CAAA;QACxC,MAAM,YAAY,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7C,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC5B,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;YACxB,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;YAC9B,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE;gBAAE,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QAC5D,CAAC;aAAM,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACnC,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;YAC5B,YAAY,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;QAClC,CAAC;QAED,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACtC,YAAY,EACZ,UAAU,EACV,MAAM,CACT,CAAA;QAED,2HAA2H;QAC3H,qEAAqE;QACrE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACjD,IAAI,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;YAC5C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;QACrD,CAAC;QAED,eAAe;QACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,OAAO,YAAY,GAAG,CAC1B,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,OAAO,YAAY,GAAG,CAC1B,CACJ,CAAA;QAED,gCAAgC;QAChC,IACI,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YAClC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,EACtD,CAAC;YACC,MAAM,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,GAAG,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,QAAQ,EACR,WAAW,CACd,CAAA;YAED,4BAA4B;YAC5B,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,IAAI,SAAS,OAAO,SAAS,GAAG,CACpC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,IAAI,SAAS,OAAO,SAAS,GAAG,CACpC,CACJ,CAAA;QACL,CAAC;QAED,4BAA4B;QAC5B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;gBAAE,OAAM;YAEzC,4BAA4B;YAC5B,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,QAAQ,EACR,MAAM,CAAC,WAAW,CACrB,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAC1C,MAAM,CAAC,IACX,OAAO,aAAa,GAAG,CAC1B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,IAAI,aAAa,OAAO,MAAM,CAAC,IAAI,GAAG,CAC1C,CACJ,CAAA;YAED,0BAA0B;YAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;QAC/B,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,0DAA0D;YAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;gBAAE,OAAM;YAEvC,4BAA4B;YAC5B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACzD,QAAQ,EACR,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;YAED,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAC1C,KAAK,CAAC,IACV,OAAO,YAAY,YAAY,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,QAAQ,CACX,IAAI,YAAY,OAAO,KAAK,CAAC,IAAI,YAAY,CACjD,CACJ,CAAA;YAED,0BAA0B;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,iCAAiC;QACjC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;YACxC,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gEAAgE;YAChE,IAAI,UAAU,CAAC,IAAI,KAAK,iBAAiB;gBAAE,OAAM;YAEjD,4BAA4B;YAC5B,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,QAAQ,EACR,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;YAEL,gBAAgB;YAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,mBAAmB,CACvC,UAAU,CAAC,IAAK,EAChB,UAAU,EACV,MAAM,CACT,OAAO,iBAAiB,GAAG,CAC/B,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,mBAAmB,CACvC,iBAAiB,EACjB,UAAU,EACV,MAAM,CACT,OAAO,UAAU,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;YAED,0BAA0B;YAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;QACvC,CAAC,CAAC,CAAA;QAEF,qDAAqD;QACrD,IAAI,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YACjC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;YAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;QAClD,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,oDAAoD;QACpD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,MAAmB;QAEnB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAC9B,KAAK,EACL,MAAM,EACN,KAAK,EACL,IAAI,CACP,EAAE,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;YACjD,wEAAwE;YACxE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;gBAEP,MAAM,WAAW,GAAG,cAAc;qBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;qBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACL,CAAC;YAED,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YAC3B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QACL,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACvD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC3D,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;YAClB,MAAM,gBAAgB,GAAG,IAAI,WAAW,CAAC;gBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;gBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;aAC7B,CAAC,CAAA;YACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;YAC1C,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,cAAc,MAAM,CAAC,IAAI,IAAI,CAChC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,qBACjC,gBAAgB,CAAC,IACrB,GAAG,CACN,CACJ,CAAA;QACL,CAAC;QAED,4BAA4B;QAC5B,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1D,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,MAAM,CAAC,IAAI,CACd,CAAA;YACL,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAC1D,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;QAC7B,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,OAAsB;QAEtB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,oBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,oBAAoB,CAAC,CAAA;QAChE,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IAAI,SAAS,GAA4B,SAAS,CAAA;QAClD,IAAI,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACtD,SAAS,GAAG,oBAAoB,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,SAAS,GAAG,SAAS,CAAC,KAAK,EAAE,CAAA;YAC7B,SAAS,CAAC,IAAI,GAAG,oBAAoB,CAAA;QACzC,CAAC;QAED,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;IACxD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CACd,WAA2B,EAC3B,oBAA0C,EAC1C,SAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACjE,CAAC,CAAC,oBAAoB;YACtB,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,CACnD,CAAA;QACP,IAAI,CAAC,SAAS;YACV,MAAM,IAAI,YAAY,CAClB,WAAW,oBAAoB,2BAA2B,KAAK,CAAC,IAAI,UAAU,CACjF,CAAA;QAEL,IACI,CAAC,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW;YAC5C,SAAS,CAAC,kBAAkB,KAAK,MAAM,CAAC;YAC5C,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI;YACjC,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;YACrC,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,YAAY;YACjD,SAAS,CAAC,aAAa,KAAK,SAAS,CAAC,aAAa,EACrD,CAAC;YACC,yGAAyG;YACzG,kDAAkD;YAClD,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YACvC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;YAEtC,sBAAsB;YACtB,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,IAAI,SAAS,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACpC,iEAAiE;gBACjE,IAAI,MAAM,GAAuB,SAAS,CAAA;gBAC1C,IAAI,UAAU,GAAuB,SAAS,CAAA;gBAC9C,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;gBAC1C,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC5B,MAAM,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;oBACxB,IAAI,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE;wBAAE,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;gBAC5D,CAAC;qBAAM,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACnC,UAAU,GAAG,YAAY,CAAC,CAAC,CAAC,CAAA;gBAChC,CAAC;gBAED,2HAA2H;gBAC3H,qEAAqE;gBACrE,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;gBACjD,IAAI,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACjC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;oBAC5C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;gBACrD,CAAC;gBAED,oBAAoB;gBACpB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IACvC,SAAS,CAAC,IACd,OAAO,SAAS,CAAC,IAAI,GAAG,CAC3B,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IACvC,SAAS,CAAC,IACd,OAAO,SAAS,CAAC,IAAI,GAAG,CAC3B,CACJ,CAAA;gBAED,uCAAuC;gBACvC,IACI,SAAS,CAAC,SAAS,KAAK,IAAI;oBAC5B,CAAC,SAAS,CAAC,wBAAwB,EACrC,CAAC;oBACC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;oBAEjD,oCAAoC;oBACpC,MAAM,WAAW,GAAG,cAAc,CAAC,GAAG,CAClC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CAAA;oBACD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,+CAA+C;oBAC/C,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAA;oBAC1D,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAEhC,oCAAoC;oBACpC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;oBAEL,4BAA4B;oBAC5B,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,SAAS,OAAO,SAAS,GAAG,CACpC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,SAAS,OAAO,SAAS,GAAG,CACpC,CACJ,CAAA;gBACL,CAAC;gBAED,2BAA2B;gBAC3B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvD,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,0DAA0D;oBAC1D,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY;wBAAE,OAAM;oBAEvC,4BAA4B;oBAC5B,KAAK,CAAC,WAAW,CAAC,MAAM,CACpB,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EACzC,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACtC,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,SAAS,CACpC,WAAW,EACX,KAAK,CAAC,WAAW,EACjB,KAAK,CAAC,KAAK,CACd,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,KAAK,CAAC,IAAI,OAAO,YAAY,YAAY,CACjD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,YAAY,OAAO,KAAK,CAAC,IAAI,YAAY,CACjD,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,iCAAiC;gBACjC,WAAW;qBACN,qBAAqB,CAAC,SAAS,CAAC;qBAChC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;oBACpB,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gEAAgE;oBAChE,IAAI,UAAU,CAAC,IAAI,KAAK,cAAc;wBAAE,OAAM;oBAE9C,4BAA4B;oBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM,CACzB,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBAC3C,MAAM,iBAAiB,GACnB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,mBAAmB,CACvC,UAAU,CAAC,IAAK,EAChB,UAAU,EACV,MAAM,CACT,OAAO,iBAAiB,GAAG,CAC/B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,mBAAmB,CACvC,iBAAiB,EACjB,UAAU,EACV,MAAM,CACT,OAAO,UAAU,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,UAAU,CAAC,IAAI,GAAG,iBAAiB,CAAA;gBACvC,CAAC,CAAC,CAAA;gBAEN,2BAA2B;gBAC3B,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;oBACtD,4BAA4B;oBAC5B,KAAK,CAAC,WAAY,CAAC,MAAM,CACrB,KAAK,CAAC,WAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,KAAK,CAAC,WAAY,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,YAAY,GACd,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,WAAW,EACX,KAAK,CAAC,UAAW,CACpB,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,KAAK,CAAC,IAAI,OAAO,YAAY,GAAG,CACxC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,YAAY,OAAO,KAAK,CAAC,IAAI,GAAG,CACxC,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA;gBAC7B,CAAC,CAAC,CAAA;gBAEF,4BAA4B;gBAC5B,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxD,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,2DAA2D;oBAC3D,IAAI,MAAM,CAAC,IAAI,KAAK,aAAa;wBAAE,OAAM;oBAEzC,4BAA4B;oBAC5B,MAAM,CAAC,WAAW,CAAC,MAAM,CACrB,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAC1C,CAAC,CACJ,CAAA;oBACD,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;oBACvC,MAAM,aAAa,GACf,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,WAAW,EACX,MAAM,CAAC,WAAW,CACrB,CAAA;oBAEL,gBAAgB;oBAChB,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,MAAM,CAAC,IAAI,OAAO,aAAa,GAAG,CAC1C,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,mBAAmB,IAAI,CAAC,YAAY,CAChC,WAAW,CACd,IAAI,aAAa,OAAO,MAAM,CAAC,IAAI,GAAG,CAC1C,CACJ,CAAA;oBAED,0BAA0B;oBAC1B,MAAM,CAAC,IAAI,GAAG,aAAa,CAAA;gBAC/B,CAAC,CAAC,CAAA;gBAEF,6BAA6B;gBAC7B,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBACL,MAAM,cAAc,GAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBAEL,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,aAC/B,SAAS,CAAC,OACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;oBAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,aAC/B,SAAS,CAAC,OACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CACJ,CAAA;gBACL,CAAC;gBAED,qDAAqD;gBACrD,IAAI,MAAM,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;oBACjC,SAAS,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,SAAS,GAAG,CAAC,CAAC,CAAA;oBAC/C,WAAW,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,MAAM,GAAG,CAAC,CAAC,CAAA;gBAClD,CAAC;gBAED,wCAAwC;gBACxC,MAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;gBACD,WAAW,CAAC,OAAO,CACf,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,cAAe,CAAC,CAC/C,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;gBACvB,SAAS,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAA;YACnC,CAAC;YAED,IACI,IAAI,CAAC,eAAe,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EACjE,CAAC;gBACC,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,IAAI,CAAC,oBAAoB,CACvC,KAAK,EACL,SAAS,EACT,IAAI,EACJ,KAAK,EACL,IAAI,CACP,EAAE,CACN,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,iBAAiB,IAAI,CAAC,oBAAoB,CACvC,KAAK,EACL,SAAS,EACT,IAAI,EACJ,KAAK,EACL,IAAI,CACP,EAAE,CACN,CACJ,CAAA;YACL,CAAC;YAED,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC3C,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;gBACvD,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC;oBAC5B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CACpD,KAAK,EACL,aAAa,EACb,IAAI,CACP;oBACD,UAAU,EAAE,aAAa;iBAC5B,CAAC,CAAA;gBAEF,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA;gBACvD,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC;oBAC5B,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CACpD,KAAK,EACL,aAAa,EACb,IAAI,CACP;oBACD,UAAU,EAAE,aAAa;iBAC5B,CAAC,CAAA;gBAEF,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAC5D,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAE9D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAA;gBAC9D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAA;YACpE,CAAC;YAED,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,CAAC,SAAS,EAAE,CAAC;gBAC9C,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;gBAEjD,2EAA2E;gBAC3E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACL,CAAC;gBAED,IAAI,SAAS,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;oBAC/B,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC9B,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,IAAI,CAAA;oBACxB,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;wBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;oBAEP,MAAM,WAAW,GAAG,cAAc;yBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;yBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;oBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,aAAa,GAAG,cAAc,CAAC,IAAI,CACrC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CACnC,CAAA;oBACD,cAAc,CAAC,MAAM,CACjB,cAAc,CAAC,OAAO,CAAC,aAAc,CAAC,EACtC,CAAC,CACJ,CAAA;oBAED,yBAAyB;oBACzB,MAAM,MAAM,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACnC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,SAAS,CAAC,IAAI,CAC7C,CAAA;oBACD,MAAO,CAAC,SAAS,GAAG,KAAK,CAAA;oBAEzB,gEAAgE;oBAChE,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;6BAC3B,wBAAwB;4BACzB,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;4BAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;wBAEP,MAAM,WAAW,GAAG,cAAc;6BAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;6BACnC,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;wBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;oBACL,CAAC;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;gBAC5C,IAAI,SAAS,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;oBAC9B,MAAM,gBAAgB,GAAG,IAAI,WAAW,CAAC;wBACrC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,SAAS,CAAC,IAAI,CAAC,CACnB;wBACD,WAAW,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC;qBAChC,CAAC,CAAA;oBACF,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;oBAC1C,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAgB,CAAC,IACrB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAgB,CAAC,IAAI,GAAG,CACjD,CACJ,CAAA;gBACL,CAAC;qBAAM,CAAC;oBACJ,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,MAAM,EAAE,EAAE;wBACP,OAAO,CACH,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;4BAC/B,CAAC,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CACrB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,KAAK,SAAS,CAAC,IAAI,CACpC,CACJ,CAAA;oBACL,CAAC,CACJ,CAAA;oBACD,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAiB,CAAC,EAC9C,CAAC,CACJ,CAAA;oBACD,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,gBAAiB,CAAC,IAAI,GAAG,CAClD,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBACG,gBAAiB,CAAC,IACtB,cAAc,SAAS,CAAC,IAAI,IAAI,CACnC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC;gBAC1C,8EAA8E;gBAC9E,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBACL,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,WAAW,aAC5B,SAAS,CAAC,OACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;gBACL,CAAC;gBAED,IACI,SAAS,CAAC,OAAO,KAAK,IAAI;oBAC1B,SAAS,CAAC,OAAO,KAAK,SAAS,EACjC,CAAC;oBACC,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,SAAS,CAAC,IAAI,CACjB,CAAA;oBACL,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,WAAW,aAC5B,SAAS,CAAC,OACd,SAAS,SAAS,CAAC,IAAI,GAAG,CAC7B,CACJ,CAAA;oBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;YACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAC/C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,cAAoE;QAEpE,KAAK,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,cAAc,EAAE,CAAC;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC,CAAA;QAC9D,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CACZ,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YACtD,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC1C,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,YAAY,CAClB,WAAW,YAAY,6BAA6B,KAAK,CAAC,IAAI,GAAG,CACpE,CAAA;QAEL,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,8BAA8B;QAC9B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,MAAM,GAAG,MAAM,CAAC,wBAAwB;gBAC1C,CAAC,CAAC,MAAM,CAAC,wBAAwB;gBACjC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC1D,CAAA;YAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;iBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;YAED,yBAAyB;YACzB,MAAM,WAAW,GAAG,WAAW,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;YAC7D,WAAY,CAAC,SAAS,GAAG,KAAK,CAAA;YAE9B,mFAAmF;YACnF,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC;qBACvC,wBAAwB;oBACzB,CAAC,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;oBACxD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CAAC,cAAc,CAAC,GAAG,CAC1B,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAC1B,CACJ,CAAA;gBAEP,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc;qBACzC,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,IAAI,aAAa,CAAC,IAAI,GAAG,CAAC;qBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,oBAAoB,MAAM,kBAAkB,WAAW,GAAG,CAC9D,CACJ,CAAA;gBACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,WAAW,CACd,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACL,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACxC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,EACxC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YACrD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QAC7D,CAAC;QAED,oBAAoB;QACpB,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,CAAC,CAAC,KAAK,CAAC,WAAW;YACnB,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC9B,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC3C,CAAA;QACD,IAAI,WAAW,EAAE,CAAC;YACd,WAAW,CAAC,MAAM,CAAC,MAAM,CACrB,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EACvC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC/D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;QACvE,CAAC;QAED,qBAAqB;QACrB,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,IAAI,CACzC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;YAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;QACD,IAAI,YAAY,EAAE,CAAC;YACf,WAAW,CAAC,OAAO,CAAC,MAAM,CACtB,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,EACzC,CAAC,CACJ,CAAA;YACD,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAA;YACjE,WAAW,CAAC,IAAI,CACZ,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,CACtD,CAAA;QACL,CAAC;QAED,0BAA0B;QAC1B,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1D,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,MAAM,CAAC,IAAI,CACd,CAAA;YACL,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,WAAW,GAAG,CACvC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,WAAW,aAC5B,MAAM,CAAC,OACX,SAAS,MAAM,CAAC,IAAI,GAAG,CAC1B,CACJ,CAAA;QACL,CAAC;QAED,IAAI,MAAM,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAA;YAEzD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;YAC1D,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;aACpB,CAAC,CAAA;YACF,MAAM,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC;gBAC9C,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,MAAM,EAAE,eAAe,CAAC,MAAM;gBAC9B,KAAK,EAAE,eAAe,CAAC,SAAS;gBAChC,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;gBACxC,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,KAAK,EAAE,MAAM,CAAC,YAAY;aAC7B,CAAC,CAAA;YAEF,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3B,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACjC,CAAC;QAED,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,iBACjC,MAAM,CAAC,IACX,GAAG,CACN,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,QAAQ,IAAI,CAAC,oBAAoB,CAC9B,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,CACR,EAAE,CACN,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QAEjD,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAiC;QAEjC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;QAC9C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,WAAqB,EACrB,cAAuB;QAEvB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,WAAW,EAAE,cAAc,CAAC,CAAA;QAEvE,4GAA4G;QAC5G,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACnC,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,KAAK,MAAM,CAAC,IAAI,CAAC;gBAC5D,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;QAC/B,CAAC,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAA;QAEhD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,OAAsB;QAEtB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;QACjC,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACxD,MAAM,SAAS,GAAY,EAAE,CAAA;QAC7B,MAAM,WAAW,GAAY,EAAE,CAAA;QAE/B,4DAA4D;QAC5D,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAA;QACjD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,iBAAiB,GAAG,cAAc;iBACnC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;YACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACL,CAAC;QAED,2BAA2B;QAC3B,WAAW,CAAC,OAAO;aACd,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;aAC3D,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAA;QAEnD,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YACrD,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;YAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,WAAW,EACX,WAAW,CACd,CAAA;QAEP,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QAEf,SAAS,CAAC,IAAI,CACV,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,MAAM,kBAAkB,iBAAiB,GAAG,CACpE,CACJ,CAAA;QACD,WAAW,CAAC,IAAI,CACZ,IAAI,KAAK,CACL,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,MAAM,GAAG,CAClC,CACJ,CAAA;QAED,MAAM,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,WAAW,CAAC,CAAA;QACjD,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,cAAuB;QAEvB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;QACxC,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CACjC,KAAK,EACL,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EACjD,cAAc,CACjB,CAAA;QACD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YACpC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAA;QAC5B,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA6B;QAE7B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,gBAAgB,CAAC,IAAI;YACtB,gBAAgB,CAAC,IAAI;gBACjB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,gBAAgB,CAAC,WAAW,CAC/B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE,CACxD,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC7D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,YAAkC;QAElC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,gBAAgB,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAChE,CAAC,CAAC,YAAY;YACd,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC,CAAA;QACxD,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,YAAY,CAClB,qDAAqD,KAAK,CAAC,IAAI,EAAE,CACpE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAA;QACpE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAA;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,iBAAgC;QAEhC,MAAM,QAAQ,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC,gBAAgB,EAAE,EAAE,CACxD,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CACvB,WAA2B,EAC3B,eAA2B;QAE3B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,mGAAmG;QACnG,IAAI,CAAC,eAAe,CAAC,IAAI;YACrB,eAAe,CAAC,IAAI;gBAChB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,eAAe,CAAC,UAAW,CAC9B,CAAA;QAET,MAAM,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAChE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,kBAAkB,CAAC,eAAe,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,sBAAsB,CACxB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,qBAAqB,CAAC,WAAW,EAAE,eAAe,CAAC,CAC3D,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,eAAe,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YAC7D,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACtD,IAAI,CAAC,eAAe;YAChB,MAAM,IAAI,YAAY,CAClB,oDAAoD,KAAK,CAAC,IAAI,EAAE,CACnE,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC9D,MAAM,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,qBAAqB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,oBAAoB,CACtB,WAA2B,EAC3B,gBAA8B;QAE9B,MAAM,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,EAAE,CACtD,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CACzD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAC3B,WAA2B,EAC3B,mBAAmC;QAEnC,MAAM,IAAI,YAAY,CAClB,mDAAmD,CACtD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,0BAA0B,CAC5B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,mDAAmD,CACtD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CACzB,WAA2B,EAC3B,eAAwC;QAExC,MAAM,IAAI,YAAY,CAClB,mDAAmD,CACtD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB,CAC1B,WAA2B,EAC3B,oBAAsC;QAEtC,MAAM,IAAI,YAAY,CAClB,mDAAmD,CACtD,CAAA;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,gBAAgB,CAClB,WAA2B,EAC3B,UAA2B;QAE3B,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;YACpD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC;YACzC,CAAC,CAAC,SAAS,CAAA;QAEf,IACI,QAAQ;YACR,QAAQ,CAAC,kBAAkB;YAC3B,QAAQ,CAAC,kBAAmB,CAAC,YAAY;YACzC,QAAQ,CAAC,WAAW,CAAC,IAAI,CACrB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,QAAQ,KAAK,WAAW,CACtD;YAED,MAAM,IAAI,YAAY,CAClB,mDAAmD,CACtD,CAAA;QAEL,gFAAgF;QAChF,IAAI,CAAC,UAAU,CAAC,IAAI;YAChB,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAC3D,KAAK,EACL,UAAU,CAAC,WAAW,EACtB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,EAC7B,UAAU,CAAC,qBAAqB,CACnC,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,CACjD,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAChB,WAA2B,EAC3B,gBAA0C;QAE1C,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,UAAU,GAAG,eAAe,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;YAClE,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAA;QAClE,IAAI,CAAC,UAAU;YACX,MAAM,IAAI,YAAY,CAClB,+CAA+C,KAAK,CAAC,IAAI,EAAE,CAC9D,CAAA;QAEL,MAAM,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACpD,MAAM,IAAI,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QACxD,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAA;IACtC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,eAAe,CACjB,WAA2B,EAC3B,WAA8B;QAE9B,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAC5C,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,UAAU,CAAC,CAC/C,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,KAAiB;QAEjB,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAE5C,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC5C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CACf,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CACvC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CACX,WAA2B,EAC3B,WAAgC;QAEhC,MAAM,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC;YAC9C,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;QAC5C,MAAM,KAAK,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACnD,CAAC,CAAC,WAAW;YACb,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAA;QACvD,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,YAAY,CAClB,yCAAyC,KAAK,CAAC,IAAI,EAAE,CACxD,CAAA;QAEL,sFAAsF;QACtF,IAAI,CAAC,KAAK,CAAC,IAAI;YAAE,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAElE,MAAM,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC9C,MAAM,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA;QACnC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CACb,WAA2B,EAC3B,OAAqB;QAErB,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CACnC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,KAAK,CAAC,CACrC,CAAA;QACD,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,UAAU,CAAC,SAAiB;QAC9B,MAAM,IAAI,CAAC,KAAK,CAAC,kBAAkB,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IACpE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,QAAiB;QACjC,IAAI,QAAQ,EAAE,CAAC;YACX,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAA;YACxD,IAAI,CAAC,eAAe;gBAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QAClD,CAAC;QAED,MAAM,0BAA0B,GAAG,IAAI,CAAC,mBAAmB,CAAA;QAC3D,IAAI,CAAC,0BAA0B;YAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC9D,IAAI,CAAC;YACD,MAAM,WAAW,GAAG,QAAQ;gBACxB,CAAC,CAAC,kBAAkB,QAAQ,gCAAgC;gBAC5D,CAAC,CAAC,4CAA4C,CAAA;YAClD,MAAM,eAAe,GAAoB,MAAM,IAAI,CAAC,KAAK,CACrD,WAAW,CACd,CAAA;YAED,MAAM,OAAO,CAAC,GAAG,CACb,eAAe,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAC/B,0FAA0F;gBAC1F,MAAM,YAAY,GAAG,cAAc,UAAU,CAAC,cAAc,CAAC,MAAM,UAAU,CAAC,YAAY,CAAC,GAAG,CAAA;gBAC9F,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;YACnC,CAAC,CAAC,CACL,CAAA;YAED,MAAM,YAAY,GAAG,QAAQ;gBACzB,CAAC,CAAC,kBAAkB,QAAQ,mEAAmE;gBAC/F,CAAC,CAAC,+EAA+E,CAAA;YACrF,MAAM,gBAAgB,GAAoB,MAAM,IAAI,CAAC,KAAK,CACtD,YAAY,CACf,CAAA;YAED,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9B,MAAM,eAAe,GAKjB,gBAAgB,CAAC,MAAM,CACvB,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;oBAC/C,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA;oBACzC,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC,CAAA;oBACnD,OAAO,CAAC,CAAA;gBACZ,CAAC,EACD,EAAE,CACL,CAAA;gBAED,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC;qBACjD,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;oBAC7B,MAAM,UAAU,GAAG,MAAM;yBACpB,GAAG,CAAC,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE;wBAClC,OAAO,8CAA8C,aAAa,MAAM,YAAY,MAAM,UAAU,MAAM,CAAA;oBAC9G,CAAC,CAAC;yBACD,IAAI,CAAC,MAAM,CAAC,CAAA;oBAEjB,OAAO;2CACY,aAAa;;oEAEY,aAAa;sFACK,aAAa;;gCAEnE,aAAa;iCACZ,UAAU;qBACtB,CAAA;gBACD,CAAC,CAAC;qBACD,IAAI,CAAC,aAAa,CAAC,CAAA;gBAExB,MAAM,WAAW,GAKX,MAAM,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;gBAEtC,MAAM,OAAO,CAAC,GAAG,CACb,WAAW,CAAC,GAAG,CACX,KAAK,EAAE,EACH,aAAa,EACb,YAAY,EACZ,UAAU,EACV,eAAe,GAClB,EAAE,EAAE;oBACD,gCAAgC;oBAChC,MAAM,IAAI,CAAC,KAAK,CACZ,gBAAgB,aAAa,MAAM,YAAY,MAAM,UAAU,IAAI;wBAC/D,uBAAuB,eAAe,GAAG,CAChD,CAAA;oBAED,MAAM,IAAI,CAAC,KAAK,CACZ,gBAAgB,aAAa,MAAM,YAAY,MAAM,UAAU,IAAI;wBAC/D,oBAAoB,eAAe,iBAAiB,CAC3D,CAAA;gBACL,CAAC,CACJ,CACJ,CAAA;gBAED,MAAM,OAAO,CAAC,GAAG,CACb,gBAAgB,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;oBAClC,IAAI,YAAY,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC7C,qCAAqC;wBACrC,OAAM;oBACV,CAAC;oBAED,MAAM,YAAY,GAAG,eAAe,YAAY,CAAC,eAAe,CAAC,MAAM,YAAY,CAAC,cAAc,CAAC,MAAM,YAAY,CAAC,YAAY,CAAC,GAAG,CAAA;oBACtI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;gBACnC,CAAC,CAAC,CACL,CAAA;YACL,CAAC;YAED,IAAI,CAAC,0BAA0B;gBAAE,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,CAAC,0BAA0B;oBAC3B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;YACxC,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAElE,KAAK,CAAC,SAAS,CAAC,SAAoB;QAC1C,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,2BAA2B,EAAE,CAAC,CAAA;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACZ,OAAO,EAAE,CAAA;QACb,CAAC;QAED,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,SAAS,GAAG,EAAE,CAAA;QAClB,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,MAAM,OAAO,GAAG,SAAS;aACpB,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC;aAChE,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,CAAA;QAEnC,IACI,IAAI,CAAC,MAAM,CAAC,QAAQ;YACpB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAE1D,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;QAEtC,MAAM,cAAc,GAAG,SAAS;aAC3B,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;YACd,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;YAExC,IAAI,CAAC,MAAM,EAAE,CAAC;gBACV,MAAM,GAAG,aAAa,CAAA;YAC1B,CAAC;YACD,OAAO,oBAAoB,MAAM,uBAAuB,IAAI,IAAI,CAAA;QACpE,CAAC,CAAC;aACD,IAAI,CAAC,MAAM,CAAC,CAAA;QAEjB,MAAM,KAAK,GAAG,OAAO;aAChB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;YACZ,OAAO,CACH,yCAAyC,IAAI,CAAC,UAAU,CACpD,IAAI,CAAC,2BAA2B,EAAE,CACrC,OAAO;gBACR,eAAe,MAAM,iIACjB,iBAAiB,CAAC,IACtB,KAAK,cAAc,CAAC,CAAC,CAAC,QAAQ,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACzD,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACvC,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,MAAW,EAAE,EAAE;YAC/B,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAA;YACvB,MAAM,EAAE,GACJ,MAAM,CAAC,eAAe,CAAC,KAAK,eAAe;gBACvC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,CAAA;YACjC,MAAM,MAAM,GACR,MAAM,CAAC,QAAQ,CAAC,KAAK,aAAa;gBAClC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;gBACvB,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,eAAe,CAAC,CAAA;YACvC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,EAAE,CAAC,CAAA;YAClE,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAA;YACjC,OAAO,IAAI,CAAA;QACf,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,UAAU,CAAC,UAAqB;QAC5C,6CAA6C;QAC7C,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxC,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QACnD,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEvD,MAAM,QAAQ,GAIR,EAAE,CAAA;QAER,IAAI,CAAC,UAAU,EAAE,CAAC;YACd,MAAM,YAAY,GACd,yBAAyB;gBACzB,qCAAqC;gBACrC,iDAAiD,CAAA;YACrD,MAAM,WAAW,GAAuB,MAAM,IAAI,CAAC,KAAK,CACpD,YAAY,CACf,CAAA;YAED,MAAM,SAAS,GAAG,WAAW;iBACxB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE;gBACd,OAAO;;;4BAGC,IAAI;;;;2CAIW,IAAI;;;iBAG9B,CAAA;YACD,CAAC,CAAC;iBACD,IAAI,CAAC,aAAa,CAAC,CAAA;YAExB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;aAAM,CAAC;YACJ,MAAM,mBAAmB,GAAG,UAAU;iBACjC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;iBACzD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE;gBAClC,QAAQ,GAAG,QAAQ,IAAI,eAAe,CAAA;gBACtC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAA;gBAC/B,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;oBACb,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,aAAa;oBACrC,SAAS,EAAE,KAAK,CAAC,SAAS;iBAC7B,CAAC,CAAA;gBACF,OAAO,CAAC,CAAA;YACZ,CAAC,EAAE,EAAgE,CAAC,CAAA;YAExE,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,CAAC;iBAChD,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;gBACxB,MAAM,eAAe,GAAG,MAAM;qBACzB,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,SAAS,EAAE,EAAE,EAAE;oBAC3B,OAAO,sBAAsB,MAAM,yBAAyB,SAAS,IAAI,CAAA;gBAC7E,CAAC,CAAC;qBACD,IAAI,CAAC,MAAM,CAAC,CAAA;gBAEjB,OAAO;;;4BAGC,QAAQ;;;+CAGW,QAAQ;4BAC3B,eAAe;iBAC1B,CAAA;YACD,CAAC,CAAC;iBACD,IAAI,CAAC,aAAa,CAAC,CAAA;YAExB,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAA;QACnD,CAAC;QAED,yDAAyD;QACzD,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,OAAO,EAAE,CAAA;QACb,CAAC;QAED,MAAM,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CACrC,CAAC,CAAC,EAAE,EAAE,aAAa,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE;YAC/B,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,aAAa,CAAC,IAAI,EAAE,CAAA;YACzC,CAAC,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;YAC5B,OAAO,CAAC,CAAA;QACZ,CAAC,EACD,EAEC,CACJ,CAAA;QAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC;aAC/C,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7B,MAAM,SAAS,GAAG,MAAM;iBACnB,GAAG,CACA,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,EAAE,CAC7B,sBAAsB,YAAY,yBAAyB,UAAU,IAAI,CAChF;iBACA,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,OAAO,CACH,6DAA6D;gBAC7D,SAAS,aAAa,mCAAmC;gBACzD,0GAA0G;gBAC1G,UAAU,SAAS,GAAG,CACzB,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC;aACnD,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,MAAM;iBACpB,GAAG,CACA,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,CAC7B,qCAAqC,YAAY,wCAAwC,UAAU,IAAI,CAC9G;iBACA,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,OAAO,CACH,oFAAoF;gBACpF,SAAS,aAAa,kEAAkE;gBACxF,eAAe,aAAa,gEAAgE;gBAC5F,KAAK;gBACL,8EAA8E;gBAC9E,wEAAwE;gBACxE,gEAAgE;gBAChE,cAAc,aAAa,oCAAoC;gBAC/D,KAAK;gBACL,+IAA+I;gBAC/I,QAAQ;gBACR,IAAI,UAAU,QAAQ;gBACtB,4EAA4E,CAC/E,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC;aACnD,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,MAAM;iBACpB,GAAG,CACA,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,CAC7B,mBAAmB,YAAY,wBAAwB,UAAU,IAAI,CAC5E;iBACA,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,OAAO,CACH,qCAAqC,aAAa,oFAAoF;gBACtI,0HAA0H;gBAC1H,6GAA6G;gBAC7G,SAAS,aAAa,8BAA8B;gBACpD,eAAe,aAAa,yFAAyF;gBACrH,eAAe,aAAa,sEAAsE;gBAClG,eAAe,aAAa,gEAAgE;gBAC5F,eAAe,aAAa,0EAA0E;gBACtG,eAAe,aAAa,gEAAgE;gBAC5F,eAAe,aAAa,6HAA6H;gBACzJ,eAAe,aAAa,qIAAqI;gBACjK,UAAU,UAAU,GAAG,CAC1B,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,kBAAkB,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC;aACvD,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,MAAM;iBACpB,GAAG,CACA,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,CAC7B,sBAAsB,YAAY,yBAAyB,UAAU,IAAI,CAChF;iBACA,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,OAAO,CACH,sEAAsE;gBACtE,SAAS,aAAa,mCAAmC;gBACzD,QAAQ;gBACR,yBAAyB,aAAa,qLAAqL;gBAC3N,IAAI,UAAU,GAAG,CACpB,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,eAAe,GAAG,wDAAwD,CAAA;QAEhF,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,iBAAiB,CAAC;aAC/C,GAAG,CAAC,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,EAAE,EAAE;YAC7B,MAAM,UAAU,GAAG,MAAM;iBACpB,GAAG,CACA,CAAC,EAAE,UAAU,EAAE,YAAY,EAAE,EAAE,EAAE,CAC7B,kBAAkB,YAAY,uBAAuB,UAAU,IAAI,CAC1E;iBACA,IAAI,CAAC,MAAM,CAAC,CAAA;YAEjB,OAAO,CACH,WAAW,aAAa,kFAAkF;gBAC1G,0IAA0I;gBAC1I,SAAS,aAAa,0BAA0B;gBAChD,eAAe,aAAa,8GAA8G;gBAC1I,eAAe,aAAa,2GAA2G;gBACvI,eAAe,aAAa,8DAA8D;gBAC1F,eAAe,aAAa,6DAA6D;gBACzF,QAAQ;gBACR,kGAAkG;gBAClG,IAAI,UAAU,GAAG,CACpB,CAAA;QACL,CAAC,CAAC;aACD,IAAI,CAAC,aAAa,CAAC,CAAA;QAExB,MAAM,CACF,SAAS,EACT,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,YAAY,EACZ,SAAS,EACZ,GAAsB,MAAM,OAAO,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;YAC1B,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;SACzB,CAAC,CAAA;QAEF,yCAAyC;QACzC,OAAO,MAAM,OAAO,CAAC,GAAG,CACpB,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YAC3B,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAA;YAEzB,MAAM,gBAAgB,GAAG,CAAC,QAAa,EAAE,GAAW,EAAE,EAAE;gBACpD,OAAO,QAAQ,CAAC,GAAG,CAAC,KAAK,aAAa;oBAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM;wBACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,KAAK,aAAa,CAAC;oBACjD,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAA;YACvB,CAAC,CAAA;YAED,qFAAqF;YACrF,MAAM,EAAE,GACJ,OAAO,CAAC,eAAe,CAAC,KAAK,eAAe;gBACxC,CAAC,CAAC,SAAS;gBACX,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,CAAA;YAClC,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;YACxD,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC,eAAe,CAAC,CAAA;YACzC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;YACtC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CACnC,OAAO,CAAC,YAAY,CAAC,EACrB,MAAM,EACN,EAAE,CACL,CAAA;YAED,MAAM,gBAAgB,GAAG,YAAY,CAAC,IAAI,CACtC,CAAC,WAAW,EAAE,EAAE,CACZ,WAAW,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC,eAAe,CAAC,CACtD,CAAA;YAEF,yCAAyC;YACzC,KAAK,CAAC,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC7B,SAAS;iBACJ,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,YAAY,CAAC;gBAClB,OAAO,CAAC,YAAY,CAAC;gBACzB,QAAQ,CAAC,cAAc,CAAC;oBACpB,OAAO,CAAC,cAAc,CAAC;gBAC3B,QAAQ,CAAC,eAAe,CAAC;oBACrB,OAAO,CAAC,eAAe,CAAC,CACnC;iBACA,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;gBACpB,MAAM,iBAAiB,GAAG,aAAa,CAAC,MAAM,CAC1C,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;oBACtB,QAAQ,CAAC,YAAY,CAAC;oBAC1B,YAAY,CAAC,cAAc,CAAC;wBACxB,QAAQ,CAAC,cAAc,CAAC;oBAC5B,YAAY,CAAC,eAAe,CAAC;wBACzB,QAAQ,CAAC,eAAe,CAAC;oBAC7B,YAAY,CAAC,aAAa,CAAC;wBACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,MAAM,CAC9C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CACjD,CAAA;gBACD,MAAM,qBAAqB,GACvB,iBAAiB,CAAC,KAAK,CAAC,CAAC,gBAAgB,EAAE,EAAE;oBACzC,OAAO,aAAa,CAAC,IAAI,CACrB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,iBAAiB,CAAC;wBAC3B,QAAQ;wBACZ,YAAY,CAAC,iBAAiB,CAAC;4BAC3B,gBAAgB,CACZ,iBAAiB,CACpB;wBACL,YAAY,CAAC,cAAc,CAAC;4BACxB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,YAAY,CAAC,eAAe,CAAC;4BACzB,QAAQ,CAAC,eAAe,CAAC;wBAC7B,YAAY,CAAC,aAAa,CAAC;4BACvB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,WAAW,GAAG,CAAC,CAAC,iBAAiB,CAAC,IAAI,CACxC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY,CAAC;oBAChB,QAAQ,CAAC,YAAY,CAAC;oBAC1B,MAAM,CAAC,cAAc,CAAC;wBAClB,QAAQ,CAAC,cAAc,CAAC;oBAC5B,MAAM,CAAC,eAAe,CAAC;wBACnB,QAAQ,CAAC,eAAe,CAAC;oBAC7B,MAAM,CAAC,aAAa,CAAC;wBACjB,QAAQ,CAAC,aAAa,CAAC,CAClC,CAAA;gBAED,MAAM,WAAW,GAAG,IAAI,WAAW,EAAE,CAAA;gBACrC,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAA;gBAC1C,WAAW,CAAC,IAAI;oBACZ,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAA;gBAEvC,+CAA+C;gBAC/C,IACI,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CACrC,WAAW,CAAC,IAAkB,CACjC,KAAK,CAAC,CAAC;oBACR,QAAQ,CAAC,0BAA0B,CAAC,EACtC,CAAC;oBACC,MAAM,MAAM,GACR,QAAQ,CACJ,0BAA0B,CAC7B,CAAC,QAAQ,EAAE,CAAA;oBAChB,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBAClB,WAAW,CAAC,MAAM,GAAG,KAAK,CAAA;oBAC9B,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,MAAM;4BACd,CAAC,IAAI,CAAC,qBAAqB,CACvB,KAAK,EACL,WAAW,EACX,MAAM,CACT;gCACG,CAAC,CAAC,MAAM;gCACR,CAAC,CAAC,EAAE,CAAA;oBAChB,CAAC;gBACL,CAAC;gBAED,IACI,WAAW,CAAC,IAAI,KAAK,SAAS;oBAC9B,WAAW,CAAC,IAAI,KAAK,SAAS,EAChC,CAAC;oBACC,IACI,QAAQ,CAAC,mBAAmB,CAAC,KAAK,IAAI;wBACtC,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,mBAAmB,CAAC,CAChC;wBAED,WAAW,CAAC,SAAS;4BACjB,QAAQ,CAAC,mBAAmB,CAAC,CAAA;oBACrC,IACI,QAAQ,CAAC,eAAe,CAAC,KAAK,IAAI;wBAClC,CAAC,IAAI,CAAC,oBAAoB,CACtB,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,eAAe,CAAC,CAC5B;wBAED,WAAW,CAAC,KAAK;4BACb,QAAQ,CAAC,eAAe,CAAC,CAAA;gBACrC,CAAC;gBAED,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAClC,2BAA2B;oBAC3B,MAAM,sBAAsB,GACxB,iBAAiB,CAAC,MAAM,CACpB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC;wBAC7B,OAAO,CACd,CAAA;oBACL,IAAI,sBAAsB,CAAC,MAAM,EAAE,CAAC;wBAChC,oIAAoI;wBACpI,KAAK,MAAM,eAAe,IAAI,sBAAsB,EAAE,CAAC;4BACnD,IACI,IAAI,CAAC,qBAAqB,CACtB,eAAe,CACX,iBAAiB,CACpB,CACJ,EACH,CAAC;gCACC,uDAAuD;gCACvD,WAAW,CAAC,IAAI,GAAG,EAAE,CAAA;gCACrB,MAAM,eAAe,GAAG,IAAI,MAAM,CAC9B,KAAK;oCACD,WAAW,CAAC,IAAI;oCAChB,eAAe,EACnB,GAAG,CACN,CAAA;gCACD,IAAI,MAAM,CAAA;gCACV,OACI,CAAC,MAAM,GAAG,eAAe,CAAC,IAAI,CAC1B,eAAe,CACX,YAAY,CACf,CACJ,CAAC,KAAK,IAAI,EACb,CAAC;oCACC,WAAW,CAAC,IAAI,CAAC,OAAO,CACpB,MAAM,CAAC,CAAC,CAAC,CACZ,CAAA;gCACL,CAAC;gCACD,gCAAgC;gCAChC,MAAK;4BACT,CAAC;wBACL,CAAC;oBACL,CAAC;gBACL,CAAC;gBAED,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,IAAI,CAC5C,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,iBAAiB,CAAC;oBAC7B,aAAa,CACpB,CAAA;gBACD,IAAI,iBAAiB,EAAE,CAAC;oBACpB,WAAW,CAAC,SAAS,GAAG,IAAI,CAAA;oBAC5B,0DAA0D;oBAC1D,MAAM,yBAAyB,GAC3B,aAAa,CAAC,MAAM,CAChB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,YAAY,CAAC;wBACpB,QAAQ,CAAC,YAAY,CAAC;wBAC1B,UAAU,CAAC,cAAc,CAAC;4BACtB,QAAQ,CAAC,cAAc,CAAC;wBAC5B,UAAU,CAAC,eAAe,CAAC;4BACvB,QAAQ,CAAC,eAAe,CAAC;wBAC7B,UAAU,CAAC,aAAa,CAAC;4BACrB,QAAQ,CAAC,aAAa,CAAC;wBAC3B,UAAU,CAAC,iBAAiB,CAAC;4BACzB,aAAa,CACxB,CAAA;oBAEL,2BAA2B;oBAC3B,MAAM,WAAW,GACb,yBAAyB,CAAC,GAAG,CACzB,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,aAAa,CAAC,CAChC,CAAA;oBACL,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAA;oBAEzC,4CAA4C;oBAC5C,MAAM,MAAM,GACR,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,WAAW,CACd,CAAA;oBAEL,4EAA4E;oBAC5E,IACI,iBAAiB,CAAC,iBAAiB,CAAC;wBACpC,MAAM,EACR,CAAC;wBACC,WAAW,CAAC,wBAAwB;4BAChC,iBAAiB,CAAC,iBAAiB,CAAC,CAAA;oBAC5C,CAAC;gBACL,CAAC;gBAED,WAAW,CAAC,OAAO;oBACf,QAAQ,CAAC,gBAAgB,CAAC,KAAK,IAAI;wBACnC,QAAQ,CAAC,gBAAgB,CAAC,KAAK,SAAS;wBACpC,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAC7B,QAAQ,CAAC,gBAAgB,CAAC,CAC7B;wBACH,CAAC,CAAC,SAAS,CAAA;gBACnB,WAAW,CAAC,UAAU;oBAClB,QAAQ,CAAC,aAAa,CAAC,KAAK,KAAK,CAAA;gBACrC,WAAW,CAAC,QAAQ;oBAChB,iBAAiB,CAAC,MAAM,GAAG,CAAC;wBAC5B,CAAC,qBAAqB,CAAA;gBAC1B,WAAW,CAAC,WAAW,GAAG,WAAW,CAAA;gBACrC,IAAI,WAAW;oBACX,WAAW,CAAC,kBAAkB,GAAG,WAAW,CAAA;gBAChD,IAAI,WAAW,CAAC,OAAO,KAAK,mBAAmB,EAAE,CAAC;oBAC9C,WAAW,CAAC,WAAW,GAAG,IAAI,CAAA;oBAC9B,WAAW,CAAC,kBAAkB,GAAG,MAAM,CAAA;oBACvC,WAAW,CAAC,OAAO,GAAG,SAAS,CAAA;gBACnC,CAAC;gBAED,sCAAsC;gBACtC,wDAAwD;gBACxD,IAAI,QAAQ,CAAC,gBAAgB,CAAC;oBAC1B,WAAW,CAAC,SAAS;wBACjB,QAAQ,CAAC,gBAAgB,CAAC;4BAC1B,gBAAgB,CAAC,gBAAgB,CAAC;4BAC9B,CAAC,CAAC,SAAS;4BACX,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;gBAExC,IACI,WAAW,CAAC,IAAI,KAAK,WAAW;oBAChC,WAAW,CAAC,IAAI,KAAK,MAAM;oBAC3B,WAAW,CAAC,IAAI,KAAK,gBAAgB,EACvC,CAAC;oBACC,WAAW,CAAC,SAAS;wBACjB,CAAC,IAAI,CAAC,wBAAwB,CAC1B,KAAK,EACL,WAAW,EACX,QAAQ,CAAC,oBAAoB,CAAC,CACjC;4BACG,CAAC,CAAC,QAAQ,CAAC,oBAAoB,CAAC;4BAChC,CAAC,CAAC,SAAS,CAAA;gBACvB,CAAC;gBAED,IACI,QAAQ,CAAC,cAAc,CAAC,KAAK,IAAI;oBACjC,QAAQ,CAAC,cAAc,CAAC,KAAK,SAAS;oBACtC,QAAQ,CAAC,YAAY,CAAC,EACxB,CAAC;oBACC,WAAW,CAAC,aAAa;wBACrB,QAAQ,CAAC,cAAc,CAAC,KAAK,IAAI;4BAC7B,CAAC,CAAC,QAAQ;4BACV,CAAC,CAAC,SAAS,CAAA;oBACnB,0GAA0G;oBAC1G,MAAM,iBAAiB,GACnB,IAAI,CAAC,wBAAwB,CAAC;wBAC1B,QAAQ,EAAE,OAAO,CAAC,eAAe,CAAC;wBAClC,MAAM,EAAE,OAAO,CAAC,cAAc,CAAC;wBAC/B,KAAK,EAAE,OAAO,CAAC,YAAY,CAAC;wBAC5B,IAAI,EAAE,iBAAiB,CAAC,gBAAgB;wBACxC,IAAI,EAAE,WAAW,CAAC,IAAI;qBACzB,CAAC,CAAA;oBAEN,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,CAC5B,iBAAiB,CAAC,KAAK,EACvB,iBAAiB,CAAC,UAAU,CAC/B,CAAA;oBACD,IAAI,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;wBACjC,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAA;oBAC/C,CAAC;yBAAM,CAAC;wBACJ,WAAW,CAAC,YAAY,GAAG,EAAE,CAAA;oBACjC,CAAC;gBACL,CAAC;gBAED,OAAO,WAAW,CAAA;YACtB,CAAC,CAAC,CACT,CAAA;YAED,yFAAyF;YACzF,MAAM,sBAAsB,GAAG,QAAQ,CAAC,IAAI,CACxC,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC;gBACzB,YAAY,CAAC,cAAc,CAAC;oBACxB,OAAO,CAAC,cAAc,CAAC;gBAC3B,YAAY,CAAC,eAAe,CAAC;oBACzB,OAAO,CAAC,eAAe,CAAC;gBAC5B,YAAY,CAAC,iBAAiB,CAAC,KAAK,QAAQ,CACnD,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACtD,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAChC,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,WAAW,CAAC;oBACnB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;iBACpD,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CACvC,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC;gBACzB,YAAY,CAAC,cAAc,CAAC;oBACxB,OAAO,CAAC,cAAc,CAAC;gBAC3B,YAAY,CAAC,eAAe,CAAC;oBACzB,OAAO,CAAC,eAAe,CAAC;gBAC5B,YAAY,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAClD,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,iBAAiB,CAAC,CACpD,CAAA;YAED,KAAK,CAAC,MAAM,GAAG,qBAAqB;iBAC/B,MAAM,CACH,CAAC,UAAU,EAAE,EAAE,CACX,CAAC,IAAI,CAAC,qBAAqB,CACvB,UAAU,CAAC,iBAAiB,CAAC,CAChC,CACR;iBACA,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChB,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAC/B,CAAC,GAAG,EAAE,EAAE,CACJ,GAAG,CAAC,iBAAiB,CAAC;oBACtB,UAAU,CAAC,iBAAiB,CAAC,CACpC,CAAA;gBACD,OAAO,IAAI,UAAU,CAAC;oBAClB,IAAI,EAAE,UAAU,CAAC,iBAAiB,CAAC;oBACnC,WAAW,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBAChD,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC;iBACvC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEN,kGAAkG;YAClG,MAAM,0BAA0B,GAAG,QAAQ,CAAC,IAAI,CAC5C,aAAa,CAAC,MAAM,CAChB,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,YAAY,CAAC;gBACtB,OAAO,CAAC,YAAY,CAAC;gBACzB,YAAY,CAAC,cAAc,CAAC;oBACxB,OAAO,CAAC,cAAc,CAAC;gBAC3B,YAAY,CAAC,eAAe,CAAC;oBACzB,OAAO,CAAC,eAAe,CAAC,CACnC,EACD,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAC5C,CAAA;YAED,KAAK,CAAC,WAAW,GAAG,0BAA0B,CAAC,GAAG,CAC9C,CAAC,YAAY,EAAE,EAAE;gBACb,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CACpC,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,SAAS,CAAC,KAAK,YAAY,CAAC,SAAS,CAAC,CAClD,CAAA;gBAED,0HAA0H;gBAC1H,MAAM,EAAE,GACJ,YAAY,CAAC,eAAe,CAAC,KAAK,eAAe;oBAC7C,CAAC,CAAC,SAAS;oBACX,CAAC,CAAC,YAAY,CAAC,eAAe,CAAC,CAAA;gBACvC,MAAM,MAAM,GAAG,gBAAgB,CAC3B,YAAY,EACZ,YAAY,CACf,CAAA;gBACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAClD,YAAY,CAAC,WAAW,CAAC,EACzB,MAAM,EACN,EAAE,CACL,CAAA;gBAED,OAAO,IAAI,eAAe,CAAC;oBACvB,IAAI,EAAE,YAAY,CAAC,SAAS,CAAC;oBAC7B,WAAW,EAAE,WAAW,CAAC,GAAG,CACxB,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAChC;oBACD,kBAAkB,EAAE,YAAY,CAAC,eAAe,CAAC;oBACjD,gBAAgB,EAAE,YAAY,CAAC,YAAY,CAAC;oBAC5C,mBAAmB,EAAE,mBAAmB;oBACxC,qBAAqB,EAAE,WAAW,CAAC,GAAG,CAClC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAC/B;oBACD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,CACvC,GAAG,EACH,GAAG,CACN,EAAE,oDAAoD;oBACvD,QAAQ,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,OAAO,CACvC,GAAG,EACH,GAAG,CACN,EAAE,oDAAoD;iBAC1D,CAAC,CAAA;YACN,CAAC,CACJ,CAAA;YAED,uFAAuF;YACvF,MAAM,qBAAqB,GAAG,QAAQ,CAAC,IAAI,CACvC,SAAS,CAAC,MAAM,CACZ,CAAC,OAAO,EAAE,EAAE,CACR,OAAO,CAAC,YAAY,CAAC,KAAK,OAAO,CAAC,YAAY,CAAC;gBAC/C,OAAO,CAAC,cAAc,CAAC;oBACnB,OAAO,CAAC,cAAc,CAAC;gBAC3B,OAAO,CAAC,eAAe,CAAC;oBACpB,OAAO,CAAC,eAAe,CAAC,CACnC,EACD,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,YAAY,CAAC,CACrC,CAAA;YAED,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBACrD,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;oBACvC,OAAO,CACH,KAAK,CAAC,eAAe,CAAC;wBAClB,UAAU,CAAC,eAAe,CAAC;wBAC/B,KAAK,CAAC,cAAc,CAAC;4BACjB,UAAU,CAAC,cAAc,CAAC;wBAC9B,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC;wBAChD,KAAK,CAAC,YAAY,CAAC,KAAK,UAAU,CAAC,YAAY,CAAC,CACnD,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,OAAO,IAAI,UAAU,CAAoB;oBACrC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,UAAU,CAAC,YAAY,CAAC;oBAC9B,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;oBACjD,QAAQ,EAAE,UAAU,CAAC,WAAW,CAAC;oBACjC,KAAK,EAAE,UAAU,CAAC,WAAW,CAAC;iBACjC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,OAAO,KAAK,CAAA;QAChB,CAAC,CAAC,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,iBAA2B;QAC9D,MAAM,iBAAiB,GAAG,KAAK,CAAC,OAAO;aAClC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CACZ,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,CACxD;aACA,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,IAAI,GAAG,GAAG,gBAAgB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE,CAAA;QAExE,KAAK,CAAC,OAAO;aACR,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC;aACnC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;YAChB,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACpC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC;gBAC/B,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,IAAI,CAC5C,CAAA;YACD,IAAI,CAAC,aAAa;gBACd,KAAK,CAAC,OAAO,CAAC,IAAI,CACd,IAAI,WAAW,CAAC;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CACrD,KAAK,EACL,CAAC,MAAM,CAAC,IAAI,CAAC,CAChB;oBACD,WAAW,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;iBAC7B,CAAC,CACL,CAAA;QACT,CAAC,CAAC,CAAA;QAEN,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO;iBAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,MAAM,UAAU,GAAG,MAAM,CAAC,IAAI;oBAC1B,CAAC,CAAC,MAAM,CAAC,IAAI;oBACb,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAC/C,KAAK,EACL,MAAM,CAAC,WAAW,CACrB,CAAA;gBACP,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW;qBACjC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,OAAO,eAAe,UAAU,aAAa,WAAW,GAAG,CAAA;YAC/D,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,UAAU,EAAE,CAAA;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM;iBACzB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACX,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI;oBACxB,CAAC,CAAC,KAAK,CAAC,IAAI;oBACZ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,KAAK,CAAC,UAAW,CACpB,CAAA;gBACP,OAAO,eAAe,SAAS,YAAY,KAAK,CAAC,UAAU,GAAG,CAAA;YAClE,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,SAAS,EAAE,CAAA;QAC3B,CAAC;QAED,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,EAAE,CAAC;YACpD,MAAM,cAAc,GAAG,KAAK,CAAC,WAAW;iBACnC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;gBACR,MAAM,WAAW,GAAG,EAAE,CAAC,WAAW;qBAC7B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBACf,IAAI,CAAC,EAAE,CAAC,IAAI;oBACR,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACnD,KAAK,EACL,EAAE,CAAC,WAAW,EACd,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,EACrB,EAAE,CAAC,qBAAqB,CAC3B,CAAA;gBACL,MAAM,qBAAqB,GAAG,EAAE,CAAC,qBAAqB;qBACjD,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;qBACtC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAEf,IAAI,UAAU,GAAG,eACb,EAAE,CAAC,IACP,kBAAkB,WAAW,gBAAgB,IAAI,CAAC,UAAU,CACxD,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CACxB,KAAK,qBAAqB,GAAG,CAAA;gBAC9B,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAC1D,IAAI,EAAE,CAAC,QAAQ;oBAAE,UAAU,IAAI,cAAc,EAAE,CAAC,QAAQ,EAAE,CAAA;gBAE1D,OAAO,UAAU,CAAA;YACrB,CAAC,CAAC;iBACD,IAAI,CAAC,IAAI,CAAC,CAAA;YAEf,GAAG,IAAI,KAAK,cAAc,EAAE,CAAA;QAChC,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC7D,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB;gBAC5C,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CACzC,KAAK,EACL,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAC9C,CAAA;YAEP,MAAM,WAAW,GAAG,cAAc;iBAC7B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAA;YACf,GAAG,IAAI,iBAAiB,cAAc,kBAAkB,WAAW,GAAG,CAAA;QAC1E,CAAC;QAED,GAAG,IAAI,GAAG,CAAA;QAEV,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,WAA2B,EAC3B,OAAiB;QAEjB,MAAM,KAAK,GAAG,OAAO;YACjB,CAAC,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;YACxD,CAAC,CAAC,cAAc,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAA;QAClD,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,CAAA;IAC3B,CAAC;IAES,aAAa,CAAC,IAAU;QAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAEnD,oFAAoF;QACpF,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM;YACpC,CAAC,CAAC,IAAI,UAAU,CAAC,MAAM,MAAM,UAAU,CAAC,SAAS,GAAG;YACpD,CAAC,CAAC,IAAI,UAAU,CAAC,SAAS,GAAG,CAAA;QAEjC,IAAI,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ,EAAE,CAAC;YACtC,OAAO,IAAI,KAAK,CACZ,eAAe,cAAc,OAAO,IAAI,CAAC,UAAU,EAAE,CACxD,CAAA;QACL,CAAC;aAAM,CAAC;YACJ,OAAO,IAAI,KAAK,CACZ,eAAe,cAAc,OAAO,IAAI;iBACnC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC3B,QAAQ,EAAE,EAAE,CACpB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,uBAAuB,CAAC,IAAU;QAC9C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAExD,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,MAAM,UAAU,GACZ,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;YAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;YACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;QACrD,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,IAAI,EAAE,eAAe,CAAC,SAAS;YAC/B,KAAK,EAAE,UAAU;SACpB,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,WAAW,CAAC,UAAyB;QAC3C,OAAO,IAAI,KAAK,CAAC,aAAa,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,uBAAuB,CACnC,UAAyB;QAEzB,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;QAE9D,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,eAAe,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC1D,CAAC;QAED,OAAO,IAAI,CAAC,wBAAwB,CAAC;YACjC,IAAI,EAAE,iBAAiB,CAAC,IAAI;YAC5B,QAAQ,EAAE,eAAe,CAAC,QAAQ;YAClC,MAAM,EAAE,eAAe,CAAC,MAAM;YAC9B,IAAI,EAAE,eAAe,CAAC,SAAS;SAClC,CAAC,CAAA;IACN,CAAC;IAED;;OAEG;IACO,cAAc,CAAC,KAAY,EAAE,KAAiB;QACpD,MAAM,OAAO,GAAG,KAAK,CAAC,WAAW;aAC5B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,UAAU,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UACrC,KAAK,CAAC,IACV,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,OAAO,KACtC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAC3C,EAAE,CACL,CAAA;IACL,CAAC;IAED;;OAEG;IACO,YAAY,CAClB,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,KAAK,CACZ,eAAe,SAAS,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAC3D,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,WAAqB,EACrB,cAAuB;QAEvB,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,MAAM,iBAAiB,GAAG,WAAW;aAChC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,UAAU,GAAG,CAAC;aACtC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,oBAAoB,cAAc,kBAAkB,iBAAiB,GAAG,CAC5E,CAAA;IACL,CAAC;IAED;;OAEG;IACO,iBAAiB,CAAC,KAAY;QACpC,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAAA;QACvE,MAAM,cAAc,GAAG,cAAc;YACjC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,KAAK,EAAE,WAAW,CAAC,CAAA;QAEvE,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,yBAAyB,CAC/B,KAAY,EACZ,gBAA6B;QAE7B,MAAM,WAAW,GAAG,gBAAgB,CAAC,WAAW;aAC3C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,gBAAgB,CAAC,IACrB,aAAa,WAAW,GAAG,CAC9B,CAAA;IACL,CAAC;IAED;;OAEG;IACO,uBAAuB,CAC7B,KAAY,EACZ,YAAkC;QAElC,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAC,YAAY,CAAC;YAC1D,CAAC,CAAC,YAAY,CAAC,IAAI;YACnB,CAAC,CAAC,YAAY,CAAA;QAClB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,UAAU,GAAG,CACtC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,wBAAwB,CAC9B,KAAY,EACZ,eAA2B;QAE3B,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,eAAe,CAAC,IACpB,YAAY,eAAe,CAAC,UAAU,GAAG,CAC5C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAC5B,KAAY,EACZ,WAAgC;QAEhC,MAAM,SAAS,GAAG,eAAe,CAAC,YAAY,CAAC,WAAW,CAAC;YACvD,CAAC,CAAC,WAAW,CAAC,IAAI;YAClB,CAAC,CAAC,WAAW,CAAA;QACjB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,SAAS,GAAG,CACrC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB,CACzB,KAAY,EACZ,UAA2B;QAE3B,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW;aACrC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,IAAI,CAAC,CAAA;QACf,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB;aACzD,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,GAAG,GAAG,CAAC;aACnC,IAAI,CAAC,GAAG,CAAC,CAAA;QACd,IAAI,GAAG,GACH,eAAe,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,oBACjC,UAAU,CAAC,IACf,kBAAkB,WAAW,IAAI;YACjC,cAAc,IAAI,CAAC,UAAU,CACzB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAChC,IAAI,qBAAqB,GAAG,CAAA;QACjC,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QACnE,IAAI,UAAU,CAAC,QAAQ;YAAE,GAAG,IAAI,cAAc,UAAU,CAAC,QAAQ,EAAE,CAAA;QAEnE,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,CAAA;IACzB,CAAC;IAED;;OAEG;IACO,iBAAiB,CACvB,KAAY,EACZ,gBAA0C;QAE1C,MAAM,cAAc,GAAG,eAAe,CAAC,iBAAiB,CACpD,gBAAgB,CACnB;YACG,CAAC,CAAC,gBAAgB,CAAC,IAAI;YACvB,CAAC,CAAC,gBAAgB,CAAA;QACtB,OAAO,IAAI,KAAK,CACZ,eAAe,IAAI,CAAC,UAAU,CAC1B,KAAK,CACR,qBAAqB,cAAc,GAAG,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,UAAU,CAAC,MAA6B;QAC9C,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,GACjC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAEtC,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;YAChD,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;gBAChD,OAAO,IAAI,QAAQ,MAAM,MAAM,MAAM,SAAS,GAAG,CAAA;YACrD,CAAC;YAED,OAAO,IAAI,QAAQ,OAAO,SAAS,GAAG,CAAA;QAC1C,CAAC;QAED,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAChD,OAAO,IAAI,MAAM,MAAM,SAAS,GAAG,CAAA;QACvC,CAAC;QAED,OAAO,IAAI,SAAS,GAAG,CAAA;IAC3B,CAAC;IAED;;;OAGG;IACO,mBAAmB,CACzB,MAAc,EACd,UAA8B,EAC9B,MAA0B;QAE1B,IAAI,YAAY,GAAG,MAAM,CAAA;QACzB,IAAI,UAAU,IAAI,UAAU,KAAK,IAAI,CAAC,MAAM,CAAC,YAAY;YACrD,YAAY,GAAG,UAAU,GAAG,GAAG,GAAG,YAAY,CAAA;QAClD,IAAI,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ;YACzC,YAAY,GAAG,MAAM,GAAG,GAAG,GAAG,YAAY,CAAA;QAE9C,OAAO,YAAY,CAAA;IACvB,CAAC;IAED;;;;;;OAMG;IACO,4BAA4B,CAAC,YAAoB;QACvD,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,GAAG;YAAE,OAAO,YAAY,CAAA;QAC1D,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CACzC,CAAC,EACD,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CACpC,CAAA;QACD,OAAO,IAAI,CAAC,4BAA4B,CAAC,iBAAiB,CAAC,CAAA;IAC/D,CAAC;IAED;;OAEG;IACO,oBAAoB,CAC1B,KAAY,EACZ,MAAmB,EACnB,YAAqB,EACrB,aAAsB,EACtB,QAAkB;QAElB,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAC7D,MAAM,CACT,EAAE,CAAA;QAEH,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;YACjD,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,mBAAmB,CAC9C,KAAK,EACL,UAAU,EACV,IAAI,CACP,CAAA;YACL,CAAC,IAAI,eAAe,SAAS,UAAU,UAAU,GAAG,CAAA;QACxD,CAAC;QAED,IAAI,MAAM,CAAC,SAAS;YAAE,CAAC,IAAI,WAAW,GAAG,MAAM,CAAC,SAAS,CAAA;QAEzD,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;YACtB,CAAC,IAAI,QAAQ,MAAM,CAAC,YAAY,GAAG,CAAA;YACnC,IAAI,MAAM,CAAC,aAAa,KAAK,QAAQ,EAAE,CAAC;gBACpC,CAAC,IAAI,YAAY,CAAA;gBAEjB,qFAAqF;gBACrF,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI;oBAAE,CAAC,IAAI,WAAW,CAAA;YACpD,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IAAI,MAAM,CAAC,UAAU,KAAK,IAAI;gBAAE,CAAC,IAAI,WAAW,CAAA;QACpD,CAAC;QAED,IACI,MAAM,CAAC,WAAW,KAAK,IAAI;YAC3B,MAAM,CAAC,kBAAkB,KAAK,WAAW;YACzC,CAAC,YAAY;YAEb,8FAA8F;YAC9F,CAAC,IAAI,gBAAgB,CAAA;QAEzB,IACI,MAAM,CAAC,OAAO,KAAK,SAAS;YAC5B,MAAM,CAAC,OAAO,KAAK,IAAI;YACvB,aAAa,EACf,CAAC;YACC,2FAA2F;YAC3F,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,MAAM,CAAC,IAAI,CACd,CAAA;YACL,CAAC,IAAI,gBAAgB,WAAW,aAAa,MAAM,CAAC,OAAO,EAAE,CAAA;QACjE,CAAC;QAED,IACI,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,MAAM;YACpC,CAAC,MAAM,CAAC,OAAO,EACjB,CAAC;YACC,2FAA2F;YAC3F,MAAM,WAAW,GACb,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,qBAAqB,CAChD,KAAK,EACL,MAAM,CAAC,IAAI,CACd,CAAA;YACL,CAAC,IAAI,gBAAgB,WAAW,6BAA6B,CAAA;QACjE,CAAC;QACD,OAAO,CAAC,CAAA;IACZ,CAAC;IAEO,iBAAiB,CAAC,MAAmB;QACzC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAA;QACnE,CAAC;QACD,OAAO,CACH,MAAM,CAAC,IAAI;YACX,OAAO;YACP,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACnD,GAAG,CACN,CAAA;IACL,CAAC;IAES,qBAAqB,CAAC,IAAY;QACxC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;IACtE,CAAC;IAED;;OAEG;IACO,+BAA+B,CAAC,SAAyB;QAC/D,QAAQ,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,IAAW,EAAE,CAAC,EAAE,CAAC;YACjE,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAA;YAChC,KAAK,QAAQ;gBACT,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAA;YACnC,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YACzD,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAA;YAClC,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAA;YAChC,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAA;YAClC,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YACzD,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAA;YACrC,KAAK,YAAY;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAA;YACvC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAA;YACjC,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAA;YACpC,KAAK,MAAM;gBACP,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;oBACvB,EAAE,oCAAoC,EAC5C,CAAC;oBACC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;gBACtD,CAAC;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YACvD,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YACvD,KAAK,MAAM;gBACP,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;oBACvB,EAAE,oCAAoC,EAC5C,CAAC;oBACC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAA;gBACjC,CAAC;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAA;YAClC,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAA;YAClC,KAAK,SAAS;gBACV,IACI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;oBACvB,EAAE,oCAAoC,EAC5C,CAAC;oBACC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;gBACzD,CAAC;gBACD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YAC1D,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YAC1D,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAA;YAChC,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YACtD,KAAK,MAAM;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAA;YACjC,KAAK,UAAU;gBACX,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAA;YACrC,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YAC3D,KAAK,gBAAgB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YAChE,KAAK,eAAe;gBAChB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,aAAa,CAAA;YAC1C,KAAK,kBAAkB;gBACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAA;YAC7C,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAA;YACpC,KAAK,QAAQ;gBACT,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAA;YACnC,KAAK,WAAW;gBACZ,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC,CAAA;YAC3D,KAAK,OAAO;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAA;YAClC,KAAK,KAAK;gBACN,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAA;YAChC,KAAK,YAAY;gBACb,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAA;QAC3C,CAAC;IACL,CAAC;IAED;;;OAGG;IACH,qBAAqB,CAAC,SAAyB;QAC3C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,eAAe,CAAA;QACzD,QAAQ,SAAS,EAAE,CAAC;YAChB,KAAK,kBAAkB;gBACnB,OAAO,eAAe,CAAC,gBAAgB,CAAA;YAC3C,KAAK,iBAAiB;gBAClB,OAAO,eAAe,CAAC,eAAe,CAAA;YAC1C,KAAK,cAAc;gBACf,OAAO,eAAe,CAAC,YAAY,CAAA;YAEvC,KAAK,gBAAgB,CAAC;YACtB;gBACI,OAAO,eAAe,CAAC,cAAc,CAAA;QAC7C,CAAC;IACL,CAAC;IAED;;OAEG;IACH,kBAAkB,CACd,WAA2B,EAC3B,OAAgB;QAEhB,MAAM,IAAI,YAAY,CAClB,yDAAyD,CAC5D,CAAA;IACL,CAAC;CACJ", "file": "SqlServerQueryRunner.js", "sourcesContent": ["import { ObjectLiteral } from \"../../common/ObjectLiteral\"\nimport { TypeORMError } from \"../../error\"\nimport { QueryFailedError } from \"../../error/QueryFailedError\"\nimport { QueryRunnerAlreadyReleasedError } from \"../../error/QueryRunnerAlreadyReleasedError\"\nimport { TransactionNotStartedError } from \"../../error/TransactionNotStartedError\"\nimport { ReadStream } from \"../../platform/PlatformTools\"\nimport { BaseQueryRunner } from \"../../query-runner/BaseQueryRunner\"\nimport { QueryLock } from \"../../query-runner/QueryLock\"\nimport { QueryResult } from \"../../query-runner/QueryResult\"\nimport { QueryRunner } from \"../../query-runner/QueryRunner\"\nimport { TableIndexOptions } from \"../../schema-builder/options/TableIndexOptions\"\nimport { Table } from \"../../schema-builder/table/Table\"\nimport { TableCheck } from \"../../schema-builder/table/TableCheck\"\nimport { TableColumn } from \"../../schema-builder/table/TableColumn\"\nimport { TableExclusion } from \"../../schema-builder/table/TableExclusion\"\nimport { TableForeignKey } from \"../../schema-builder/table/TableForeignKey\"\nimport { TableIndex } from \"../../schema-builder/table/TableIndex\"\nimport { TableUnique } from \"../../schema-builder/table/TableUnique\"\nimport { View } from \"../../schema-builder/view/View\"\nimport { Broadcaster } from \"../../subscriber/Broadcaster\"\nimport { BroadcasterResult } from \"../../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../../util/InstanceChecker\"\nimport { OrmUtils } from \"../../util/OrmUtils\"\nimport { Query } from \"../Query\"\nimport { ColumnType } from \"../types/ColumnTypes\"\nimport { IsolationLevel } from \"../types/IsolationLevel\"\nimport { MetadataTableType } from \"../types/MetadataTableType\"\nimport { ReplicationMode } from \"../types/ReplicationMode\"\nimport { MssqlParameter } from \"./MssqlParameter\"\nimport { SqlServerDriver } from \"./SqlServerDriver\"\n\n/**\n * Runs queries on a single SQL Server database connection.\n */\nexport class SqlServerQueryRunner\n    extends BaseQueryRunner\n    implements QueryRunner\n{\n    // -------------------------------------------------------------------------\n    // Public Implemented Properties\n    // -------------------------------------------------------------------------\n\n    /**\n     * Database driver used by connection.\n     */\n    driver: SqlServerDriver\n\n    // -------------------------------------------------------------------------\n    // Private Properties\n    // -------------------------------------------------------------------------\n\n    private lock: QueryLock = new QueryLock()\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(driver: SqlServerDriver, mode: ReplicationMode) {\n        super()\n        this.driver = driver\n        this.connection = driver.connection\n        this.broadcaster = new Broadcaster(this)\n        this.mode = mode\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates/uses database connection from the connection pool to perform further operations.\n     * Returns obtained database connection.\n     */\n    connect(): Promise<void> {\n        return Promise.resolve()\n    }\n\n    /**\n     * Releases used database connection.\n     * You cannot use query runner methods once its released.\n     */\n    release(): Promise<void> {\n        this.isReleased = true\n        return Promise.resolve()\n    }\n\n    /**\n     * Starts transaction.\n     */\n    async startTransaction(isolationLevel?: IsolationLevel): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        this.isTransactionActive = true\n        try {\n            await this.broadcaster.broadcast(\"BeforeTransactionStart\")\n        } catch (err) {\n            this.isTransactionActive = false\n            throw err\n        }\n        await new Promise<void>(async (ok, fail) => {\n            const transactionCallback = (err: any) => {\n                if (err) {\n                    this.isTransactionActive = false\n                    return fail(err)\n                }\n                ok()\n            }\n\n            if (this.transactionDepth === 0) {\n                const pool = await (this.mode === \"slave\"\n                    ? this.driver.obtainSlaveConnection()\n                    : this.driver.obtainMasterConnection())\n                this.databaseConnection = pool.transaction()\n                this.connection.logger.logQuery(\"BEGIN TRANSACTION\")\n                if (isolationLevel) {\n                    this.databaseConnection.begin(\n                        this.convertIsolationLevel(isolationLevel),\n                        transactionCallback,\n                    )\n                    this.connection.logger.logQuery(\n                        \"SET TRANSACTION ISOLATION LEVEL \" + isolationLevel,\n                    )\n                } else {\n                    this.databaseConnection.begin(transactionCallback)\n                }\n            } else {\n                await this.query(\n                    `SAVE TRANSACTION typeorm_${this.transactionDepth}`,\n                )\n                ok()\n            }\n            this.transactionDepth += 1\n        })\n\n        await this.broadcaster.broadcast(\"AfterTransactionStart\")\n    }\n\n    /**\n     * Commits transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async commitTransaction(): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionCommit\")\n\n        if (this.transactionDepth === 1) {\n            return new Promise<void>((ok, fail) => {\n                this.databaseConnection.commit(async (err: any) => {\n                    if (err) return fail(err)\n                    this.isTransactionActive = false\n                    this.databaseConnection = null\n\n                    await this.broadcaster.broadcast(\"AfterTransactionCommit\")\n\n                    ok()\n                    this.connection.logger.logQuery(\"COMMIT\")\n                    this.transactionDepth -= 1\n                })\n            })\n        }\n        this.transactionDepth -= 1\n    }\n\n    /**\n     * Rollbacks transaction.\n     * Error will be thrown if transaction was not started.\n     */\n    async rollbackTransaction(): Promise<void> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        if (!this.isTransactionActive) throw new TransactionNotStartedError()\n\n        await this.broadcaster.broadcast(\"BeforeTransactionRollback\")\n\n        if (this.transactionDepth > 1) {\n            await this.query(\n                `ROLLBACK TRANSACTION typeorm_${this.transactionDepth - 1}`,\n            )\n            this.transactionDepth -= 1\n        } else {\n            return new Promise<void>((ok, fail) => {\n                this.databaseConnection.rollback(async (err: any) => {\n                    if (err) return fail(err)\n                    this.isTransactionActive = false\n                    this.databaseConnection = null\n\n                    await this.broadcaster.broadcast(\"AfterTransactionRollback\")\n\n                    ok()\n                    this.connection.logger.logQuery(\"ROLLBACK\")\n                    this.transactionDepth -= 1\n                })\n            })\n        }\n    }\n\n    /**\n     * Executes a given SQL query.\n     */\n    async query(\n        query: string,\n        parameters?: any[],\n        useStructuredResult = false,\n    ): Promise<any> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const release = await this.lock.acquire()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        await this.broadcaster.broadcast(\"BeforeQuery\", query, parameters)\n\n        const broadcasterResult = new BroadcasterResult()\n\n        try {\n            const pool = await (this.mode === \"slave\"\n                ? this.driver.obtainSlaveConnection()\n                : this.driver.obtainMasterConnection())\n            const request = new this.driver.mssql.Request(\n                this.isTransactionActive ? this.databaseConnection : pool,\n            )\n            if (parameters && parameters.length) {\n                parameters.forEach((parameter, index) => {\n                    const parameterName = index.toString()\n                    if (InstanceChecker.isMssqlParameter(parameter)) {\n                        const mssqlParameter =\n                            this.mssqlParameterToNativeParameter(parameter)\n                        if (mssqlParameter) {\n                            request.input(\n                                parameterName,\n                                mssqlParameter,\n                                parameter.value,\n                            )\n                        } else {\n                            request.input(parameterName, parameter.value)\n                        }\n                    } else {\n                        request.input(parameterName, parameter)\n                    }\n                })\n            }\n            const queryStartTime = Date.now()\n\n            const raw = await new Promise<any>((ok, fail) => {\n                request.query(query, (err: any, raw: any) => {\n                    // log slow queries if maxQueryExecution time is set\n                    const maxQueryExecutionTime =\n                        this.driver.options.maxQueryExecutionTime\n                    const queryEndTime = Date.now()\n                    const queryExecutionTime = queryEndTime - queryStartTime\n\n                    this.broadcaster.broadcastAfterQueryEvent(\n                        broadcasterResult,\n                        query,\n                        parameters,\n                        true,\n                        queryExecutionTime,\n                        raw,\n                        undefined,\n                    )\n\n                    if (\n                        maxQueryExecutionTime &&\n                        queryExecutionTime > maxQueryExecutionTime\n                    ) {\n                        this.driver.connection.logger.logQuerySlow(\n                            queryExecutionTime,\n                            query,\n                            parameters,\n                            this,\n                        )\n                    }\n\n                    if (err) {\n                        fail(new QueryFailedError(query, parameters, err))\n                    }\n\n                    ok(raw)\n                })\n            })\n\n            const result = new QueryResult()\n\n            if (raw?.hasOwnProperty(\"recordset\")) {\n                result.records = raw.recordset\n            }\n\n            if (raw?.hasOwnProperty(\"rowsAffected\")) {\n                result.affected = raw.rowsAffected[0]\n            }\n\n            const queryType = query.slice(0, query.indexOf(\" \"))\n            switch (queryType) {\n                case \"DELETE\":\n                    // for DELETE query additionally return number of affected rows\n                    result.raw = [raw.recordset, raw.rowsAffected[0]]\n                    break\n                default:\n                    result.raw = raw.recordset\n            }\n\n            if (useStructuredResult) {\n                return result\n            } else {\n                return result.raw\n            }\n        } catch (err) {\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n            this.broadcaster.broadcastAfterQueryEvent(\n                broadcasterResult,\n                query,\n                parameters,\n                false,\n                undefined,\n                undefined,\n                err,\n            )\n\n            throw err\n        } finally {\n            await broadcasterResult.wait()\n\n            release()\n        }\n    }\n\n    /**\n     * Returns raw data stream.\n     */\n    async stream(\n        query: string,\n        parameters?: any[],\n        onEnd?: Function,\n        onError?: Function,\n    ): Promise<ReadStream> {\n        if (this.isReleased) throw new QueryRunnerAlreadyReleasedError()\n\n        const release = await this.lock.acquire()\n\n        this.driver.connection.logger.logQuery(query, parameters, this)\n        const pool = await (this.mode === \"slave\"\n            ? this.driver.obtainSlaveConnection()\n            : this.driver.obtainMasterConnection())\n        const request = new this.driver.mssql.Request(\n            this.isTransactionActive ? this.databaseConnection : pool,\n        )\n        if (parameters && parameters.length) {\n            parameters.forEach((parameter, index) => {\n                const parameterName = index.toString()\n                if (InstanceChecker.isMssqlParameter(parameter)) {\n                    request.input(\n                        parameterName,\n                        this.mssqlParameterToNativeParameter(parameter),\n                        parameter.value,\n                    )\n                } else {\n                    request.input(parameterName, parameter)\n                }\n            })\n        }\n\n        request.query(query)\n\n        const streamRequest = request.toReadableStream()\n\n        streamRequest.on(\"error\", (err: any) => {\n            release()\n            this.driver.connection.logger.logQueryError(\n                err,\n                query,\n                parameters,\n                this,\n            )\n        })\n\n        streamRequest.on(\"end\", () => {\n            release()\n        })\n\n        if (onEnd) {\n            streamRequest.on(\"end\", onEnd)\n        }\n\n        if (onError) {\n            streamRequest.on(\"error\", onError)\n        }\n\n        return streamRequest\n    }\n\n    /**\n     * Returns all available database names including system databases.\n     */\n    async getDatabases(): Promise<string[]> {\n        const results: ObjectLiteral[] = await this.query(`EXEC sp_databases`)\n        return results.map((result) => result[\"DATABASE_NAME\"])\n    }\n\n    /**\n     * Returns all available schema names including system schemas.\n     * If database parameter specified, returns schemas of that database.\n     */\n    async getSchemas(database?: string): Promise<string[]> {\n        const query = database\n            ? `SELECT * FROM \"${database}\".\"sys\".\"schema\"`\n            : `SELECT * FROM \"sys\".\"schemas\"`\n        const results: ObjectLiteral[] = await this.query(query)\n        return results.map((result) => result[\"name\"])\n    }\n\n    /**\n     * Checks if database with the given name exist.\n     */\n    async hasDatabase(database: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT DB_ID('${database}') as \"db_id\"`,\n        )\n        const dbId = result[0][\"db_id\"]\n        return !!dbId\n    }\n\n    /**\n     * Loads currently using database\n     */\n    async getCurrentDatabase(): Promise<string> {\n        const currentDBQuery = await this.query(`SELECT DB_NAME() AS \"db_name\"`)\n        return currentDBQuery[0][\"db_name\"]\n    }\n\n    /**\n     * Checks if schema with the given name exist.\n     */\n    async hasSchema(schema: string): Promise<boolean> {\n        const result = await this.query(\n            `SELECT SCHEMA_ID('${schema}') as \"schema_id\"`,\n        )\n        const schemaId = result[0][\"schema_id\"]\n        return !!schemaId\n    }\n\n    /**\n     * Loads currently using database schema\n     */\n    async getCurrentSchema(): Promise<string> {\n        const currentSchemaQuery = await this.query(\n            `SELECT SCHEMA_NAME() AS \"schema_name\"`,\n        )\n        return currentSchemaQuery[0][\"schema_name\"]\n    }\n\n    /**\n     * Checks if table with the given name exist in the database.\n     */\n    async hasTable(tableOrName: Table | string): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.database) {\n            parsedTableName.database = await this.getCurrentDatabase()\n        }\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"${parsedTableName.database}\".\"INFORMATION_SCHEMA\".\"TABLES\" WHERE \"TABLE_NAME\" = '${parsedTableName.tableName}' AND \"TABLE_SCHEMA\" = '${parsedTableName.schema}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Checks if column exist in the table.\n     */\n    async hasColumn(\n        tableOrName: Table | string,\n        columnName: string,\n    ): Promise<boolean> {\n        const parsedTableName = this.driver.parseTableName(tableOrName)\n\n        if (!parsedTableName.database) {\n            parsedTableName.database = await this.getCurrentDatabase()\n        }\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const sql = `SELECT * FROM \"${parsedTableName.database}\".\"INFORMATION_SCHEMA\".\"COLUMNS\" WHERE \"TABLE_NAME\" = '${parsedTableName.tableName}' AND \"TABLE_SCHEMA\" = '${parsedTableName.schema}' AND \"COLUMN_NAME\" = '${columnName}'`\n        const result = await this.query(sql)\n        return result.length ? true : false\n    }\n\n    /**\n     * Creates a new database.\n     */\n    async createDatabase(\n        database: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const up = ifNotExist\n            ? `IF DB_ID('${database}') IS NULL CREATE DATABASE \"${database}\"`\n            : `CREATE DATABASE \"${database}\"`\n        const down = `DROP DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Drops database.\n     */\n    async dropDatabase(database: string, ifExist?: boolean): Promise<void> {\n        const up = ifExist\n            ? `IF DB_ID('${database}') IS NOT NULL DROP DATABASE \"${database}\"`\n            : `DROP DATABASE \"${database}\"`\n        const down = `CREATE DATABASE \"${database}\"`\n        await this.executeQueries(new Query(up), new Query(down))\n    }\n\n    /**\n     * Creates table schema.\n     * If database name also specified (e.g. 'dbName.schemaName') schema will be created in specified database.\n     */\n    async createSchema(\n        schemaPath: string,\n        ifNotExist?: boolean,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (schemaPath.indexOf(\".\") === -1) {\n            const upQuery = ifNotExist\n                ? `IF SCHEMA_ID('${schemaPath}') IS NULL BEGIN EXEC ('CREATE SCHEMA \"${schemaPath}\"') END`\n                : `CREATE SCHEMA \"${schemaPath}\"`\n            upQueries.push(new Query(upQuery))\n            downQueries.push(new Query(`DROP SCHEMA \"${schemaPath}\"`))\n        } else {\n            const dbName = schemaPath.split(\".\")[0]\n            const schema = schemaPath.split(\".\")[1]\n            const currentDB = await this.getCurrentDatabase()\n            upQueries.push(new Query(`USE \"${dbName}\"`))\n            downQueries.push(new Query(`USE \"${currentDB}\"`))\n\n            const upQuery = ifNotExist\n                ? `IF SCHEMA_ID('${schema}') IS NULL BEGIN EXEC ('CREATE SCHEMA \"${schema}\"') END`\n                : `CREATE SCHEMA \"${schema}\"`\n            upQueries.push(new Query(upQuery))\n            downQueries.push(new Query(`DROP SCHEMA \"${schema}\"`))\n\n            upQueries.push(new Query(`USE \"${currentDB}\"`))\n            downQueries.push(new Query(`USE \"${dbName}\"`))\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops table schema.\n     * If database name also specified (e.g. 'dbName.schemaName') schema will be dropped in specified database.\n     */\n    async dropSchema(schemaPath: string, ifExist?: boolean): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        if (schemaPath.indexOf(\".\") === -1) {\n            const upQuery = ifExist\n                ? `IF SCHEMA_ID('${schemaPath}') IS NULL BEGIN EXEC ('DROP SCHEMA \"${schemaPath}\"') END`\n                : `DROP SCHEMA \"${schemaPath}\"`\n            upQueries.push(new Query(upQuery))\n            downQueries.push(new Query(`CREATE SCHEMA \"${schemaPath}\"`))\n        } else {\n            const dbName = schemaPath.split(\".\")[0]\n            const schema = schemaPath.split(\".\")[1]\n            const currentDB = await this.getCurrentDatabase()\n            upQueries.push(new Query(`USE \"${dbName}\"`))\n            downQueries.push(new Query(`USE \"${currentDB}\"`))\n\n            const upQuery = ifExist\n                ? `IF SCHEMA_ID('${schema}') IS NULL BEGIN EXEC ('DROP SCHEMA \"${schema}\"') END`\n                : `DROP SCHEMA \"${schema}\"`\n            upQueries.push(new Query(upQuery))\n            downQueries.push(new Query(`CREATE SCHEMA \"${schema}\"`))\n\n            upQueries.push(new Query(`USE \"${currentDB}\"`))\n            downQueries.push(new Query(`USE \"${dbName}\"`))\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new table.\n     */\n    async createTable(\n        table: Table,\n        ifNotExist: boolean = false,\n        createForeignKeys: boolean = true,\n        createIndices: boolean = true,\n    ): Promise<void> {\n        if (ifNotExist) {\n            const isTableExist = await this.hasTable(table)\n            if (isTableExist) return Promise.resolve()\n        }\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(this.createTableSql(table, createForeignKeys))\n        downQueries.push(this.dropTableSql(table))\n\n        // if createForeignKeys is true, we must drop created foreign keys in down query.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (createForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                downQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        if (createIndices) {\n            table.indices.forEach((index) => {\n                // new index may be passed without name. In this case we generate index name manually.\n                if (!index.name)\n                    index.name = this.connection.namingStrategy.indexName(\n                        table,\n                        index.columnNames,\n                        index.where,\n                    )\n                upQueries.push(this.createIndexSql(table, index))\n                downQueries.push(this.dropIndexSql(table, index))\n            })\n        }\n\n        // if table have column with generated type, we must add the expression to the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const parsedTableName = this.driver.parseTableName(table)\n\n            if (!parsedTableName.schema) {\n                parsedTableName.schema = await this.getCurrentSchema()\n            }\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the table.\n     */\n    async dropTable(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n        dropForeignKeys: boolean = true,\n        dropIndices: boolean = true,\n    ): Promise<void> {\n        if (ifExist) {\n            const isTableExist = await this.hasTable(tableOrName)\n            if (!isTableExist) return Promise.resolve()\n        }\n\n        // if dropTable called with dropForeignKeys = true, we must create foreign keys in down query.\n        const createForeignKeys: boolean = dropForeignKeys\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // It needs because if table does not exist and dropForeignKeys or dropIndices is true, we don't need\n        // to perform drop queries for foreign keys and indices.\n\n        if (dropIndices) {\n            table.indices.forEach((index) => {\n                upQueries.push(this.dropIndexSql(table, index))\n                downQueries.push(this.createIndexSql(table, index))\n            })\n        }\n\n        // if dropForeignKeys is true, we just drop the table, otherwise we also drop table foreign keys.\n        // createTable does not need separate method to create foreign keys, because it create fk's in the same query with table creation.\n        if (dropForeignKeys)\n            table.foreignKeys.forEach((foreignKey) =>\n                upQueries.push(this.dropForeignKeySql(table, foreignKey)),\n            )\n\n        upQueries.push(this.dropTableSql(table))\n        downQueries.push(this.createTableSql(table, createForeignKeys))\n\n        // if table had columns with generated type, we must remove the expression from the metadata table\n        const generatedColumns = table.columns.filter(\n            (column) => column.generatedType && column.asExpression,\n        )\n\n        for (const column of generatedColumns) {\n            const parsedTableName = this.driver.parseTableName(table)\n\n            if (!parsedTableName.schema) {\n                parsedTableName.schema = await this.getCurrentSchema()\n            }\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Creates a new view.\n     */\n    async createView(\n        view: View,\n        syncWithMetadata: boolean = false,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(this.createViewSql(view))\n        if (syncWithMetadata)\n            upQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.dropViewSql(view))\n        if (syncWithMetadata)\n            downQueries.push(await this.deleteViewDefinitionSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Drops the view.\n     */\n    async dropView(target: View | string): Promise<void> {\n        const viewName = InstanceChecker.isView(target) ? target.name : target\n        const view = await this.getCachedView(viewName)\n\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        upQueries.push(await this.deleteViewDefinitionSql(view))\n        upQueries.push(this.dropViewSql(view))\n        downQueries.push(await this.insertViewDefinitionSql(view))\n        downQueries.push(this.createViewSql(view))\n        await this.executeQueries(upQueries, downQueries)\n    }\n\n    /**\n     * Renames a table.\n     */\n    async renameTable(\n        oldTableOrName: Table | string,\n        newTableName: string,\n    ): Promise<void> {\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n        const oldTable = InstanceChecker.isTable(oldTableOrName)\n            ? oldTableOrName\n            : await this.getCachedTable(oldTableOrName)\n        const newTable = oldTable.clone()\n\n        // we need database name and schema name to rename FK constraints\n        let dbName: string | undefined = undefined\n        let schemaName: string | undefined = undefined\n        let oldTableName: string = oldTable.name\n        const splittedName = oldTable.name.split(\".\")\n        if (splittedName.length === 3) {\n            dbName = splittedName[0]\n            oldTableName = splittedName[2]\n            if (splittedName[1] !== \"\") schemaName = splittedName[1]\n        } else if (splittedName.length === 2) {\n            schemaName = splittedName[0]\n            oldTableName = splittedName[1]\n        }\n\n        newTable.name = this.driver.buildTableName(\n            newTableName,\n            schemaName,\n            dbName,\n        )\n\n        // if we have tables with database which differs from database specified in config, we must change currently used database.\n        // This need because we can not rename objects from another database.\n        const currentDB = await this.getCurrentDatabase()\n        if (dbName && dbName !== currentDB) {\n            upQueries.push(new Query(`USE \"${dbName}\"`))\n            downQueries.push(new Query(`USE \"${currentDB}\"`))\n        }\n\n        // rename table\n        upQueries.push(\n            new Query(\n                `EXEC sp_rename \"${this.getTablePath(\n                    oldTable,\n                )}\", \"${newTableName}\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `EXEC sp_rename \"${this.getTablePath(\n                    newTable,\n                )}\", \"${oldTableName}\"`,\n            ),\n        )\n\n        // rename primary key constraint\n        if (\n            newTable.primaryColumns.length > 0 &&\n            !newTable.primaryColumns[0].primaryKeyConstraintName\n        ) {\n            const columnNames = newTable.primaryColumns.map(\n                (column) => column.name,\n            )\n\n            const oldPkName = this.connection.namingStrategy.primaryKeyName(\n                oldTable,\n                columnNames,\n            )\n            const newPkName = this.connection.namingStrategy.primaryKeyName(\n                newTable,\n                columnNames,\n            )\n\n            // rename primary constraint\n            upQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(\n                        newTable,\n                    )}.${oldPkName}\", \"${newPkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(\n                        newTable,\n                    )}.${newPkName}\", \"${oldPkName}\"`,\n                ),\n            )\n        }\n\n        // rename unique constraints\n        newTable.uniques.forEach((unique) => {\n            const oldUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    oldTable,\n                    unique.columnNames,\n                )\n\n            // Skip renaming if Unique has user defined constraint name\n            if (unique.name !== oldUniqueName) return\n\n            // build new constraint name\n            const newUniqueName =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    newTable,\n                    unique.columnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(newTable)}.${\n                        unique.name\n                    }\", \"${newUniqueName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(\n                        newTable,\n                    )}.${newUniqueName}\", \"${unique.name}\"`,\n                ),\n            )\n\n            // replace constraint name\n            unique.name = newUniqueName\n        })\n\n        // rename index constraints\n        newTable.indices.forEach((index) => {\n            const oldIndexName = this.connection.namingStrategy.indexName(\n                oldTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // Skip renaming if Index has user defined constraint name\n            if (index.name !== oldIndexName) return\n\n            // build new constraint name\n            const newIndexName = this.connection.namingStrategy.indexName(\n                newTable,\n                index.columnNames,\n                index.where,\n            )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(newTable)}.${\n                        index.name\n                    }\", \"${newIndexName}\", \"INDEX\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.getTablePath(\n                        newTable,\n                    )}.${newIndexName}\", \"${index.name}\", \"INDEX\"`,\n                ),\n            )\n\n            // replace constraint name\n            index.name = newIndexName\n        })\n\n        // rename foreign key constraints\n        newTable.foreignKeys.forEach((foreignKey) => {\n            const oldForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    oldTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // Skip renaming if foreign key has user defined constraint name\n            if (foreignKey.name !== oldForeignKeyName) return\n\n            // build new constraint name\n            const newForeignKeyName =\n                this.connection.namingStrategy.foreignKeyName(\n                    newTable,\n                    foreignKey.columnNames,\n                    this.getTablePath(foreignKey),\n                    foreignKey.referencedColumnNames,\n                )\n\n            // build queries\n            upQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.buildForeignKeyName(\n                        foreignKey.name!,\n                        schemaName,\n                        dbName,\n                    )}\", \"${newForeignKeyName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `EXEC sp_rename \"${this.buildForeignKeyName(\n                        newForeignKeyName,\n                        schemaName,\n                        dbName,\n                    )}\", \"${foreignKey.name}\"`,\n                ),\n            )\n\n            // replace constraint name\n            foreignKey.name = newForeignKeyName\n        })\n\n        // change currently used database back to default db.\n        if (dbName && dbName !== currentDB) {\n            upQueries.push(new Query(`USE \"${currentDB}\"`))\n            downQueries.push(new Query(`USE \"${dbName}\"`))\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        // rename old table and replace it in cached tabled;\n        oldTable.name = newTable.name\n        this.replaceCachedTable(oldTable, newTable)\n    }\n\n    /**\n     * Creates a new column from the column in the table.\n     */\n    async addColumn(\n        tableOrName: Table | string,\n        column: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(\n                    table,\n                    column,\n                    false,\n                    true,\n                )}`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n\n        // create or update primary key constraint\n        if (column.isPrimary) {\n            const primaryColumns = clonedTable.primaryColumns\n            // if table already have primary key, me must drop it and recreate again\n            if (primaryColumns.length > 0) {\n                const pkName = primaryColumns[0].primaryKeyConstraintName\n                    ? primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          primaryColumns.map((column) => column.name),\n                      )\n\n                const columnNames = primaryColumns\n                    .map((column) => `\"${column.name}\"`)\n                    .join(\", \")\n\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n            }\n\n            primaryColumns.push(column)\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n        }\n\n        // create column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            upQueries.push(this.createIndexSql(table, columnIndex))\n            downQueries.push(this.dropIndexSql(table, columnIndex))\n        }\n\n        // create unique constraint\n        if (column.isUnique) {\n            const uniqueConstraint = new TableUnique({\n                name: this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    [column.name],\n                ),\n                columnNames: [column.name],\n            })\n            clonedTable.uniques.push(uniqueConstraint)\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\" UNIQUE (\"${column.name}\")`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(table)} DROP CONSTRAINT \"${\n                        uniqueConstraint.name\n                    }\"`,\n                ),\n            )\n        }\n\n        // remove default constraint\n        if (column.default !== null && column.default !== undefined) {\n            const defaultName =\n                this.connection.namingStrategy.defaultConstraintName(\n                    table,\n                    column.name,\n                )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${defaultName}\"`,\n                ),\n            )\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const parsedTableName = this.driver.parseTableName(table)\n\n            if (!parsedTableName.schema) {\n                parsedTableName.schema = await this.getCurrentSchema()\n            }\n\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n\n            upQueries.push(insertQuery)\n            downQueries.push(deleteQuery)\n        }\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.addColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Creates a new columns from the column in the table.\n     */\n    async addColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.addColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Renames column in the given table.\n     */\n    async renameColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newTableColumnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find((c) => c.name === oldTableColumnOrName)\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        let newColumn: TableColumn | undefined = undefined\n        if (InstanceChecker.isTableColumn(newTableColumnOrName)) {\n            newColumn = newTableColumnOrName\n        } else {\n            newColumn = oldColumn.clone()\n            newColumn.name = newTableColumnOrName\n        }\n\n        await this.changeColumn(table, oldColumn, newColumn)\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumn(\n        tableOrName: Table | string,\n        oldTableColumnOrName: TableColumn | string,\n        newColumn: TableColumn,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        let clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        const oldColumn = InstanceChecker.isTableColumn(oldTableColumnOrName)\n            ? oldTableColumnOrName\n            : table.columns.find(\n                  (column) => column.name === oldTableColumnOrName,\n              )\n        if (!oldColumn)\n            throw new TypeORMError(\n                `Column \"${oldTableColumnOrName}\" was not found in the \"${table.name}\" table.`,\n            )\n\n        if (\n            (newColumn.isGenerated !== oldColumn.isGenerated &&\n                newColumn.generationStrategy !== \"uuid\") ||\n            newColumn.type !== oldColumn.type ||\n            newColumn.length !== oldColumn.length ||\n            newColumn.asExpression !== oldColumn.asExpression ||\n            newColumn.generatedType !== oldColumn.generatedType\n        ) {\n            // SQL Server does not support changing of IDENTITY column, so we must drop column and recreate it again.\n            // Also, we recreate column if column type changed\n            await this.dropColumn(table, oldColumn)\n            await this.addColumn(table, newColumn)\n\n            // update cloned table\n            clonedTable = table.clone()\n        } else {\n            if (newColumn.name !== oldColumn.name) {\n                // we need database name and schema name to rename FK constraints\n                let dbName: string | undefined = undefined\n                let schemaName: string | undefined = undefined\n                const splittedName = table.name.split(\".\")\n                if (splittedName.length === 3) {\n                    dbName = splittedName[0]\n                    if (splittedName[1] !== \"\") schemaName = splittedName[1]\n                } else if (splittedName.length === 2) {\n                    schemaName = splittedName[0]\n                }\n\n                // if we have tables with database which differs from database specified in config, we must change currently used database.\n                // This need because we can not rename objects from another database.\n                const currentDB = await this.getCurrentDatabase()\n                if (dbName && dbName !== currentDB) {\n                    upQueries.push(new Query(`USE \"${dbName}\"`))\n                    downQueries.push(new Query(`USE \"${currentDB}\"`))\n                }\n\n                // rename the column\n                upQueries.push(\n                    new Query(\n                        `EXEC sp_rename \"${this.getTablePath(table)}.${\n                            oldColumn.name\n                        }\", \"${newColumn.name}\"`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `EXEC sp_rename \"${this.getTablePath(table)}.${\n                            newColumn.name\n                        }\", \"${oldColumn.name}\"`,\n                    ),\n                )\n\n                // rename column primary key constraint\n                if (\n                    oldColumn.isPrimary === true &&\n                    !oldColumn.primaryKeyConstraintName\n                ) {\n                    const primaryColumns = clonedTable.primaryColumns\n\n                    // build old primary constraint name\n                    const columnNames = primaryColumns.map(\n                        (column) => column.name,\n                    )\n                    const oldPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // replace old column name with new column name\n                    columnNames.splice(columnNames.indexOf(oldColumn.name), 1)\n                    columnNames.push(newColumn.name)\n\n                    // build new primary constraint name\n                    const newPkName =\n                        this.connection.namingStrategy.primaryKeyName(\n                            clonedTable,\n                            columnNames,\n                        )\n\n                    // rename primary constraint\n                    upQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${oldPkName}\", \"${newPkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${newPkName}\", \"${oldPkName}\"`,\n                        ),\n                    )\n                }\n\n                // rename index constraints\n                clonedTable.findColumnIndices(oldColumn).forEach((index) => {\n                    const oldIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // Skip renaming if Index has user defined constraint name\n                    if (index.name !== oldIndexName) return\n\n                    // build new constraint name\n                    index.columnNames.splice(\n                        index.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    index.columnNames.push(newColumn.name)\n                    const newIndexName =\n                        this.connection.namingStrategy.indexName(\n                            clonedTable,\n                            index.columnNames,\n                            index.where,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${index.name}\", \"${newIndexName}\", \"INDEX\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${newIndexName}\", \"${index.name}\", \"INDEX\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    index.name = newIndexName\n                })\n\n                // rename foreign key constraints\n                clonedTable\n                    .findColumnForeignKeys(oldColumn)\n                    .forEach((foreignKey) => {\n                        const foreignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // Skip renaming if foreign key has user defined constraint name\n                        if (foreignKey.name !== foreignKeyName) return\n\n                        // build new constraint name\n                        foreignKey.columnNames.splice(\n                            foreignKey.columnNames.indexOf(oldColumn.name),\n                            1,\n                        )\n                        foreignKey.columnNames.push(newColumn.name)\n                        const newForeignKeyName =\n                            this.connection.namingStrategy.foreignKeyName(\n                                clonedTable,\n                                foreignKey.columnNames,\n                                this.getTablePath(foreignKey),\n                                foreignKey.referencedColumnNames,\n                            )\n\n                        // build queries\n                        upQueries.push(\n                            new Query(\n                                `EXEC sp_rename \"${this.buildForeignKeyName(\n                                    foreignKey.name!,\n                                    schemaName,\n                                    dbName,\n                                )}\", \"${newForeignKeyName}\"`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `EXEC sp_rename \"${this.buildForeignKeyName(\n                                    newForeignKeyName,\n                                    schemaName,\n                                    dbName,\n                                )}\", \"${foreignKey.name}\"`,\n                            ),\n                        )\n\n                        // replace constraint name\n                        foreignKey.name = newForeignKeyName\n                    })\n\n                // rename check constraints\n                clonedTable.findColumnChecks(oldColumn).forEach((check) => {\n                    // build new constraint name\n                    check.columnNames!.splice(\n                        check.columnNames!.indexOf(oldColumn.name),\n                        1,\n                    )\n                    check.columnNames!.push(newColumn.name)\n                    const newCheckName =\n                        this.connection.namingStrategy.checkConstraintName(\n                            clonedTable,\n                            check.expression!,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${check.name}\", \"${newCheckName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${newCheckName}\", \"${check.name}\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    check.name = newCheckName\n                })\n\n                // rename unique constraints\n                clonedTable.findColumnUniques(oldColumn).forEach((unique) => {\n                    const oldUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // Skip renaming if Unique has user defined constraint name\n                    if (unique.name !== oldUniqueName) return\n\n                    // build new constraint name\n                    unique.columnNames.splice(\n                        unique.columnNames.indexOf(oldColumn.name),\n                        1,\n                    )\n                    unique.columnNames.push(newColumn.name)\n                    const newUniqueName =\n                        this.connection.namingStrategy.uniqueConstraintName(\n                            clonedTable,\n                            unique.columnNames,\n                        )\n\n                    // build queries\n                    upQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${unique.name}\", \"${newUniqueName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `EXEC sp_rename \"${this.getTablePath(\n                                clonedTable,\n                            )}.${newUniqueName}\", \"${unique.name}\"`,\n                        ),\n                    )\n\n                    // replace constraint name\n                    unique.name = newUniqueName\n                })\n\n                // rename default constraints\n                if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    const oldDefaultName =\n                        this.connection.namingStrategy.defaultConstraintName(\n                            table,\n                            oldColumn.name,\n                        )\n                    const newDefaultName =\n                        this.connection.namingStrategy.defaultConstraintName(\n                            table,\n                            newColumn.name,\n                        )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${oldDefaultName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${oldDefaultName}\" DEFAULT ${\n                                oldColumn.default\n                            } FOR \"${newColumn.name}\"`,\n                        ),\n                    )\n\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${newDefaultName}\" DEFAULT ${\n                                oldColumn.default\n                            } FOR \"${newColumn.name}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${newDefaultName}\"`,\n                        ),\n                    )\n                }\n\n                // change currently used database back to default db.\n                if (dbName && dbName !== currentDB) {\n                    upQueries.push(new Query(`USE \"${currentDB}\"`))\n                    downQueries.push(new Query(`USE \"${dbName}\"`))\n                }\n\n                // rename old column in the Table object\n                const oldTableColumn = clonedTable.columns.find(\n                    (column) => column.name === oldColumn.name,\n                )\n                clonedTable.columns[\n                    clonedTable.columns.indexOf(oldTableColumn!)\n                ].name = newColumn.name\n                oldColumn.name = newColumn.name\n            }\n\n            if (\n                this.isColumnChanged(oldColumn, newColumn, false, false, false)\n            ) {\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ALTER COLUMN ${this.buildCreateColumnSql(\n                            table,\n                            newColumn,\n                            true,\n                            false,\n                            true,\n                        )}`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            table,\n                        )} ALTER COLUMN ${this.buildCreateColumnSql(\n                            table,\n                            oldColumn,\n                            true,\n                            false,\n                            true,\n                        )}`,\n                    ),\n                )\n            }\n\n            if (this.isEnumChanged(oldColumn, newColumn)) {\n                const oldExpression = this.getEnumExpression(oldColumn)\n                const oldCheck = new TableCheck({\n                    name: this.connection.namingStrategy.checkConstraintName(\n                        table,\n                        oldExpression,\n                        true,\n                    ),\n                    expression: oldExpression,\n                })\n\n                const newExpression = this.getEnumExpression(newColumn)\n                const newCheck = new TableCheck({\n                    name: this.connection.namingStrategy.checkConstraintName(\n                        table,\n                        newExpression,\n                        true,\n                    ),\n                    expression: newExpression,\n                })\n\n                upQueries.push(this.dropCheckConstraintSql(table, oldCheck))\n                upQueries.push(this.createCheckConstraintSql(table, newCheck))\n\n                downQueries.push(this.dropCheckConstraintSql(table, newCheck))\n                downQueries.push(this.createCheckConstraintSql(table, oldCheck))\n            }\n\n            if (newColumn.isPrimary !== oldColumn.isPrimary) {\n                const primaryColumns = clonedTable.primaryColumns\n\n                // if primary column state changed, we must always drop existed constraint.\n                if (primaryColumns.length > 0) {\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                }\n\n                if (newColumn.isPrimary === true) {\n                    primaryColumns.push(newColumn)\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = true\n                    const pkName = primaryColumns[0].primaryKeyConstraintName\n                        ? primaryColumns[0].primaryKeyConstraintName\n                        : this.connection.namingStrategy.primaryKeyName(\n                              clonedTable,\n                              primaryColumns.map((column) => column.name),\n                          )\n\n                    const columnNames = primaryColumns\n                        .map((column) => `\"${column.name}\"`)\n                        .join(\", \")\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${pkName}\"`,\n                        ),\n                    )\n                } else {\n                    const primaryColumn = primaryColumns.find(\n                        (c) => c.name === newColumn.name,\n                    )\n                    primaryColumns.splice(\n                        primaryColumns.indexOf(primaryColumn!),\n                        1,\n                    )\n\n                    // update column in table\n                    const column = clonedTable.columns.find(\n                        (column) => column.name === newColumn.name,\n                    )\n                    column!.isPrimary = false\n\n                    // if we have another primary keys, we must recreate constraint.\n                    if (primaryColumns.length > 0) {\n                        const pkName = primaryColumns[0]\n                            .primaryKeyConstraintName\n                            ? primaryColumns[0].primaryKeyConstraintName\n                            : this.connection.namingStrategy.primaryKeyName(\n                                  clonedTable,\n                                  primaryColumns.map((column) => column.name),\n                              )\n\n                        const columnNames = primaryColumns\n                            .map((column) => `\"${column.name}\"`)\n                            .join(\", \")\n                        upQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                            ),\n                        )\n                        downQueries.push(\n                            new Query(\n                                `ALTER TABLE ${this.escapePath(\n                                    table,\n                                )} DROP CONSTRAINT \"${pkName}\"`,\n                            ),\n                        )\n                    }\n                }\n            }\n\n            if (newColumn.isUnique !== oldColumn.isUnique) {\n                if (newColumn.isUnique === true) {\n                    const uniqueConstraint = new TableUnique({\n                        name: this.connection.namingStrategy.uniqueConstraintName(\n                            table,\n                            [newColumn.name],\n                        ),\n                        columnNames: [newColumn.name],\n                    })\n                    clonedTable.uniques.push(uniqueConstraint)\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint.name}\"`,\n                        ),\n                    )\n                } else {\n                    const uniqueConstraint = clonedTable.uniques.find(\n                        (unique) => {\n                            return (\n                                unique.columnNames.length === 1 &&\n                                !!unique.columnNames.find(\n                                    (columnName) =>\n                                        columnName === newColumn.name,\n                                )\n                            )\n                        },\n                    )\n                    clonedTable.uniques.splice(\n                        clonedTable.uniques.indexOf(uniqueConstraint!),\n                        1,\n                    )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${uniqueConstraint!.name}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${\n                                uniqueConstraint!.name\n                            }\" UNIQUE (\"${newColumn.name}\")`,\n                        ),\n                    )\n                }\n            }\n\n            if (newColumn.default !== oldColumn.default) {\n                // (note) if there is a previous default, we need to drop its constraint first\n                if (\n                    oldColumn.default !== null &&\n                    oldColumn.default !== undefined\n                ) {\n                    const defaultName =\n                        this.connection.namingStrategy.defaultConstraintName(\n                            table,\n                            oldColumn.name,\n                        )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${defaultName}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${defaultName}\" DEFAULT ${\n                                oldColumn.default\n                            } FOR \"${oldColumn.name}\"`,\n                        ),\n                    )\n                }\n\n                if (\n                    newColumn.default !== null &&\n                    newColumn.default !== undefined\n                ) {\n                    const defaultName =\n                        this.connection.namingStrategy.defaultConstraintName(\n                            table,\n                            newColumn.name,\n                        )\n                    upQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} ADD CONSTRAINT \"${defaultName}\" DEFAULT ${\n                                newColumn.default\n                            } FOR \"${newColumn.name}\"`,\n                        ),\n                    )\n                    downQueries.push(\n                        new Query(\n                            `ALTER TABLE ${this.escapePath(\n                                table,\n                            )} DROP CONSTRAINT \"${defaultName}\"`,\n                        ),\n                    )\n                }\n            }\n\n            await this.executeQueries(upQueries, downQueries)\n            this.replaceCachedTable(table, clonedTable)\n        }\n    }\n\n    /**\n     * Changes a column in the table.\n     */\n    async changeColumns(\n        tableOrName: Table | string,\n        changedColumns: { newColumn: TableColumn; oldColumn: TableColumn }[],\n    ): Promise<void> {\n        for (const { oldColumn, newColumn } of changedColumns) {\n            await this.changeColumn(tableOrName, oldColumn, newColumn)\n        }\n    }\n\n    /**\n     * Drops column in the table.\n     */\n    async dropColumn(\n        tableOrName: Table | string,\n        columnOrName: TableColumn | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const column = InstanceChecker.isTableColumn(columnOrName)\n            ? columnOrName\n            : table.findColumnByName(columnOrName)\n        if (!column)\n            throw new TypeORMError(\n                `Column \"${columnOrName}\" was not found in table \"${table.name}\"`,\n            )\n\n        const clonedTable = table.clone()\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // drop primary key constraint\n        if (column.isPrimary) {\n            const pkName = column.primaryKeyConstraintName\n                ? column.primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      clonedTable.primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = clonedTable.primaryColumns\n                .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        clonedTable,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                ),\n            )\n\n            // update column in table\n            const tableColumn = clonedTable.findColumnByName(column.name)\n            tableColumn!.isPrimary = false\n\n            // if primary key have multiple columns, we must recreate it without dropped column\n            if (clonedTable.primaryColumns.length > 0) {\n                const pkName = clonedTable.primaryColumns[0]\n                    .primaryKeyConstraintName\n                    ? clonedTable.primaryColumns[0].primaryKeyConstraintName\n                    : this.connection.namingStrategy.primaryKeyName(\n                          clonedTable,\n                          clonedTable.primaryColumns.map(\n                              (column) => column.name,\n                          ),\n                      )\n\n                const columnNames = clonedTable.primaryColumns\n                    .map((primaryColumn) => `\"${primaryColumn.name}\"`)\n                    .join(\", \")\n                upQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNames})`,\n                    ),\n                )\n                downQueries.push(\n                    new Query(\n                        `ALTER TABLE ${this.escapePath(\n                            clonedTable,\n                        )} DROP CONSTRAINT \"${pkName}\"`,\n                    ),\n                )\n            }\n        }\n\n        // drop column index\n        const columnIndex = clonedTable.indices.find(\n            (index) =>\n                index.columnNames.length === 1 &&\n                index.columnNames[0] === column.name,\n        )\n        if (columnIndex) {\n            clonedTable.indices.splice(\n                clonedTable.indices.indexOf(columnIndex),\n                1,\n            )\n            upQueries.push(this.dropIndexSql(table, columnIndex))\n            downQueries.push(this.createIndexSql(table, columnIndex))\n        }\n\n        // drop column check\n        const columnCheck = clonedTable.checks.find(\n            (check) =>\n                !!check.columnNames &&\n                check.columnNames.length === 1 &&\n                check.columnNames[0] === column.name,\n        )\n        if (columnCheck) {\n            clonedTable.checks.splice(\n                clonedTable.checks.indexOf(columnCheck),\n                1,\n            )\n            upQueries.push(this.dropCheckConstraintSql(table, columnCheck))\n            downQueries.push(this.createCheckConstraintSql(table, columnCheck))\n        }\n\n        // drop column unique\n        const columnUnique = clonedTable.uniques.find(\n            (unique) =>\n                unique.columnNames.length === 1 &&\n                unique.columnNames[0] === column.name,\n        )\n        if (columnUnique) {\n            clonedTable.uniques.splice(\n                clonedTable.uniques.indexOf(columnUnique),\n                1,\n            )\n            upQueries.push(this.dropUniqueConstraintSql(table, columnUnique))\n            downQueries.push(\n                this.createUniqueConstraintSql(table, columnUnique),\n            )\n        }\n\n        // drop default constraint\n        if (column.default !== null && column.default !== undefined) {\n            const defaultName =\n                this.connection.namingStrategy.defaultConstraintName(\n                    table,\n                    column.name,\n                )\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${defaultName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${defaultName}\" DEFAULT ${\n                        column.default\n                    } FOR \"${column.name}\"`,\n                ),\n            )\n        }\n\n        if (column.generatedType && column.asExpression) {\n            const parsedTableName = this.driver.parseTableName(table)\n\n            if (!parsedTableName.schema) {\n                parsedTableName.schema = await this.getCurrentSchema()\n            }\n\n            const deleteQuery = this.deleteTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n            })\n            const insertQuery = this.insertTypeormMetadataSql({\n                database: parsedTableName.database,\n                schema: parsedTableName.schema,\n                table: parsedTableName.tableName,\n                type: MetadataTableType.GENERATED_COLUMN,\n                name: column.name,\n                value: column.asExpression,\n            })\n\n            upQueries.push(deleteQuery)\n            downQueries.push(insertQuery)\n        }\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(table)} DROP COLUMN \"${\n                    column.name\n                }\"`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD ${this.buildCreateColumnSql(\n                    table,\n                    column,\n                    false,\n                    false,\n                )}`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n\n        clonedTable.removeColumn(column)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops the columns in the table.\n     */\n    async dropColumns(\n        tableOrName: Table | string,\n        columns: TableColumn[] | string[],\n    ): Promise<void> {\n        for (const column of columns) {\n            await this.dropColumn(tableOrName, column)\n        }\n    }\n\n    /**\n     * Creates a new primary key.\n     */\n    async createPrimaryKey(\n        tableOrName: Table | string,\n        columnNames: string[],\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n\n        const up = this.createPrimaryKeySql(table, columnNames, constraintName)\n\n        // mark columns as primary, because dropPrimaryKeySql build constraint name from table primary column names.\n        clonedTable.columns.forEach((column) => {\n            if (columnNames.find((columnName) => columnName === column.name))\n                column.isPrimary = true\n        })\n        const down = this.dropPrimaryKeySql(clonedTable)\n\n        await this.executeQueries(up, down)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    async updatePrimaryKeys(\n        tableOrName: Table | string,\n        columns: TableColumn[],\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const clonedTable = table.clone()\n        const columnNames = columns.map((column) => column.name)\n        const upQueries: Query[] = []\n        const downQueries: Query[] = []\n\n        // if table already have primary columns, we must drop them.\n        const primaryColumns = clonedTable.primaryColumns\n        if (primaryColumns.length > 0) {\n            const pkName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      clonedTable,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNamesString = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n\n            upQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} DROP CONSTRAINT \"${pkName}\"`,\n                ),\n            )\n            downQueries.push(\n                new Query(\n                    `ALTER TABLE ${this.escapePath(\n                        table,\n                    )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n                ),\n            )\n        }\n\n        // update columns in table.\n        clonedTable.columns\n            .filter((column) => columnNames.indexOf(column.name) !== -1)\n            .forEach((column) => (column.isPrimary = true))\n\n        const pkName = primaryColumns[0].primaryKeyConstraintName\n            ? primaryColumns[0].primaryKeyConstraintName\n            : this.connection.namingStrategy.primaryKeyName(\n                  clonedTable,\n                  columnNames,\n              )\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n\n        upQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} ADD CONSTRAINT \"${pkName}\" PRIMARY KEY (${columnNamesString})`,\n            ),\n        )\n        downQueries.push(\n            new Query(\n                `ALTER TABLE ${this.escapePath(\n                    table,\n                )} DROP CONSTRAINT \"${pkName}\"`,\n            ),\n        )\n\n        await this.executeQueries(upQueries, downQueries)\n        this.replaceCachedTable(table, clonedTable)\n    }\n\n    /**\n     * Drops a primary key.\n     */\n    async dropPrimaryKey(\n        tableOrName: Table | string,\n        constraintName?: string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const up = this.dropPrimaryKeySql(table)\n        const down = this.createPrimaryKeySql(\n            table,\n            table.primaryColumns.map((column) => column.name),\n            constraintName,\n        )\n        await this.executeQueries(up, down)\n        table.primaryColumns.forEach((column) => {\n            column.isPrimary = false\n        })\n    }\n\n    /**\n     * Creates a new unique constraint.\n     */\n    async createUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueConstraint: TableUnique,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!uniqueConstraint.name)\n            uniqueConstraint.name =\n                this.connection.namingStrategy.uniqueConstraintName(\n                    table,\n                    uniqueConstraint.columnNames,\n                )\n\n        const up = this.createUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.addUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Creates a new unique constraints.\n     */\n    async createUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const promises = uniqueConstraints.map((uniqueConstraint) =>\n            this.createUniqueConstraint(tableOrName, uniqueConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops unique constraint.\n     */\n    async dropUniqueConstraint(\n        tableOrName: Table | string,\n        uniqueOrName: TableUnique | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const uniqueConstraint = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName\n            : table.uniques.find((u) => u.name === uniqueOrName)\n        if (!uniqueConstraint)\n            throw new TypeORMError(\n                `Supplied unique constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropUniqueConstraintSql(table, uniqueConstraint)\n        const down = this.createUniqueConstraintSql(table, uniqueConstraint)\n        await this.executeQueries(up, down)\n        table.removeUniqueConstraint(uniqueConstraint)\n    }\n\n    /**\n     * Drops an unique constraints.\n     */\n    async dropUniqueConstraints(\n        tableOrName: Table | string,\n        uniqueConstraints: TableUnique[],\n    ): Promise<void> {\n        const promises = uniqueConstraints.map((uniqueConstraint) =>\n            this.dropUniqueConstraint(tableOrName, uniqueConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new check constraint.\n     */\n    async createCheckConstraint(\n        tableOrName: Table | string,\n        checkConstraint: TableCheck,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new unique constraint may be passed without name. In this case we generate unique name manually.\n        if (!checkConstraint.name)\n            checkConstraint.name =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    checkConstraint.expression!,\n                )\n\n        const up = this.createCheckConstraintSql(table, checkConstraint)\n        const down = this.dropCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.addCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Creates a new check constraints.\n     */\n    async createCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.createCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops check constraint.\n     */\n    async dropCheckConstraint(\n        tableOrName: Table | string,\n        checkOrName: TableCheck | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const checkConstraint = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName\n            : table.checks.find((c) => c.name === checkOrName)\n        if (!checkConstraint)\n            throw new TypeORMError(\n                `Supplied check constraint was not found in table ${table.name}`,\n            )\n\n        const up = this.dropCheckConstraintSql(table, checkConstraint)\n        const down = this.createCheckConstraintSql(table, checkConstraint)\n        await this.executeQueries(up, down)\n        table.removeCheckConstraint(checkConstraint)\n    }\n\n    /**\n     * Drops check constraints.\n     */\n    async dropCheckConstraints(\n        tableOrName: Table | string,\n        checkConstraints: TableCheck[],\n    ): Promise<void> {\n        const promises = checkConstraints.map((checkConstraint) =>\n            this.dropCheckConstraint(tableOrName, checkConstraint),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new exclusion constraint.\n     */\n    async createExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionConstraint: TableExclusion,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SqlServer does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new exclusion constraints.\n     */\n    async createExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SqlServer does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraint.\n     */\n    async dropExclusionConstraint(\n        tableOrName: Table | string,\n        exclusionOrName: TableExclusion | string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SqlServer does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Drops exclusion constraints.\n     */\n    async dropExclusionConstraints(\n        tableOrName: Table | string,\n        exclusionConstraints: TableExclusion[],\n    ): Promise<void> {\n        throw new TypeORMError(\n            `SqlServer does not support exclusion constraints.`,\n        )\n    }\n\n    /**\n     * Creates a new foreign key.\n     */\n    async createForeignKey(\n        tableOrName: Table | string,\n        foreignKey: TableForeignKey,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const metadata = this.connection.hasMetadata(table.name)\n            ? this.connection.getMetadata(table.name)\n            : undefined\n\n        if (\n            metadata &&\n            metadata.treeParentRelation &&\n            metadata.treeParentRelation!.isTreeParent &&\n            metadata.foreignKeys.find(\n                (foreignKey) => foreignKey.onDelete !== \"NO ACTION\",\n            )\n        )\n            throw new TypeORMError(\n                \"SqlServer does not support options in TreeParent.\",\n            )\n\n        // new FK may be passed without name. In this case we generate FK name manually.\n        if (!foreignKey.name)\n            foreignKey.name = this.connection.namingStrategy.foreignKeyName(\n                table,\n                foreignKey.columnNames,\n                this.getTablePath(foreignKey),\n                foreignKey.referencedColumnNames,\n            )\n\n        const up = this.createForeignKeySql(table, foreignKey)\n        const down = this.dropForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.addForeignKey(foreignKey)\n    }\n\n    /**\n     * Creates a new foreign keys.\n     */\n    async createForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.createForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops a foreign key from the table.\n     */\n    async dropForeignKey(\n        tableOrName: Table | string,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const foreignKey = InstanceChecker.isTableForeignKey(foreignKeyOrName)\n            ? foreignKeyOrName\n            : table.foreignKeys.find((fk) => fk.name === foreignKeyOrName)\n        if (!foreignKey)\n            throw new TypeORMError(\n                `Supplied foreign key was not found in table ${table.name}`,\n            )\n\n        const up = this.dropForeignKeySql(table, foreignKey)\n        const down = this.createForeignKeySql(table, foreignKey)\n        await this.executeQueries(up, down)\n        table.removeForeignKey(foreignKey)\n    }\n\n    /**\n     * Drops a foreign keys from the table.\n     */\n    async dropForeignKeys(\n        tableOrName: Table | string,\n        foreignKeys: TableForeignKey[],\n    ): Promise<void> {\n        const promises = foreignKeys.map((foreignKey) =>\n            this.dropForeignKey(tableOrName, foreignKey),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Creates a new index.\n     */\n    async createIndex(\n        tableOrName: Table | string,\n        index: TableIndex,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n\n        // new index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.createIndexSql(table, index)\n        const down = this.dropIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.addIndex(index)\n    }\n\n    /**\n     * Creates a new indices\n     */\n    async createIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.createIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Drops an index.\n     */\n    async dropIndex(\n        tableOrName: Table | string,\n        indexOrName: TableIndex | string,\n    ): Promise<void> {\n        const table = InstanceChecker.isTable(tableOrName)\n            ? tableOrName\n            : await this.getCachedTable(tableOrName)\n        const index = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName\n            : table.indices.find((i) => i.name === indexOrName)\n        if (!index)\n            throw new TypeORMError(\n                `Supplied index was not found in table ${table.name}`,\n            )\n\n        // old index may be passed without name. In this case we generate index name manually.\n        if (!index.name) index.name = this.generateIndexName(table, index)\n\n        const up = this.dropIndexSql(table, index)\n        const down = this.createIndexSql(table, index)\n        await this.executeQueries(up, down)\n        table.removeIndex(index)\n    }\n\n    /**\n     * Drops an indices from the table.\n     */\n    async dropIndices(\n        tableOrName: Table | string,\n        indices: TableIndex[],\n    ): Promise<void> {\n        const promises = indices.map((index) =>\n            this.dropIndex(tableOrName, index),\n        )\n        await Promise.all(promises)\n    }\n\n    /**\n     * Clears all table contents.\n     * Note: this operation uses SQL's TRUNCATE query which cannot be reverted in transactions.\n     */\n    async clearTable(tablePath: string): Promise<void> {\n        await this.query(`TRUNCATE TABLE ${this.escapePath(tablePath)}`)\n    }\n\n    /**\n     * Removes all tables from the currently connected database.\n     */\n    async clearDatabase(database?: string): Promise<void> {\n        if (database) {\n            const isDatabaseExist = await this.hasDatabase(database)\n            if (!isDatabaseExist) return Promise.resolve()\n        }\n\n        const isAnotherTransactionActive = this.isTransactionActive\n        if (!isAnotherTransactionActive) await this.startTransaction()\n        try {\n            const allViewsSql = database\n                ? `SELECT * FROM \"${database}\".\"INFORMATION_SCHEMA\".\"VIEWS\"`\n                : `SELECT * FROM \"INFORMATION_SCHEMA\".\"VIEWS\"`\n            const allViewsResults: ObjectLiteral[] = await this.query(\n                allViewsSql,\n            )\n\n            await Promise.all(\n                allViewsResults.map((viewResult) => {\n                    // 'DROP VIEW' does not allow specifying the database name as a prefix to the object name.\n                    const dropTableSql = `DROP VIEW \"${viewResult[\"TABLE_SCHEMA\"]}\".\"${viewResult[\"TABLE_NAME\"]}\"`\n                    return this.query(dropTableSql)\n                }),\n            )\n\n            const allTablesSql = database\n                ? `SELECT * FROM \"${database}\".\"INFORMATION_SCHEMA\".\"TABLES\" WHERE \"TABLE_TYPE\" = 'BASE TABLE'`\n                : `SELECT * FROM \"INFORMATION_SCHEMA\".\"TABLES\" WHERE \"TABLE_TYPE\" = 'BASE TABLE'`\n            const allTablesResults: ObjectLiteral[] = await this.query(\n                allTablesSql,\n            )\n\n            if (allTablesResults.length > 0) {\n                const tablesByCatalog: {\n                    [key: string]: {\n                        TABLE_NAME: string\n                        TABLE_SCHEMA: string\n                    }[]\n                } = allTablesResults.reduce(\n                    (c, { TABLE_CATALOG, TABLE_SCHEMA, TABLE_NAME }) => {\n                        c[TABLE_CATALOG] = c[TABLE_CATALOG] || []\n                        c[TABLE_CATALOG].push({ TABLE_SCHEMA, TABLE_NAME })\n                        return c\n                    },\n                    {},\n                )\n\n                const foreignKeysSql = Object.entries(tablesByCatalog)\n                    .map(([TABLE_CATALOG, tables]) => {\n                        const conditions = tables\n                            .map(({ TABLE_SCHEMA, TABLE_NAME }) => {\n                                return `(\"fk\".\"referenced_object_id\" = OBJECT_ID('\"${TABLE_CATALOG}\".\"${TABLE_SCHEMA}\".\"${TABLE_NAME}\"'))`\n                            })\n                            .join(\" OR \")\n\n                        return `\n                        SELECT DISTINCT '${TABLE_CATALOG}' AS                                              \"TABLE_CATALOG\",\n                                        OBJECT_SCHEMA_NAME(\"fk\".\"parent_object_id\",\n                                                           DB_ID('${TABLE_CATALOG}')) AS                   \"TABLE_SCHEMA\",\n                                        OBJECT_NAME(\"fk\".\"parent_object_id\", DB_ID('${TABLE_CATALOG}')) AS \"TABLE_NAME\",\n                                        \"fk\".\"name\" AS                                                     \"CONSTRAINT_NAME\"\n                        FROM \"${TABLE_CATALOG}\".\"sys\".\"foreign_keys\" AS \"fk\"\n                        WHERE (${conditions})\n                    `\n                    })\n                    .join(\" UNION ALL \")\n\n                const foreignKeys: {\n                    TABLE_CATALOG: string\n                    TABLE_SCHEMA: string\n                    TABLE_NAME: string\n                    CONSTRAINT_NAME: string\n                }[] = await this.query(foreignKeysSql)\n\n                await Promise.all(\n                    foreignKeys.map(\n                        async ({\n                            TABLE_CATALOG,\n                            TABLE_SCHEMA,\n                            TABLE_NAME,\n                            CONSTRAINT_NAME,\n                        }) => {\n                            // Disable the constraint first.\n                            await this.query(\n                                `ALTER TABLE \"${TABLE_CATALOG}\".\"${TABLE_SCHEMA}\".\"${TABLE_NAME}\" ` +\n                                    `NOCHECK CONSTRAINT \"${CONSTRAINT_NAME}\"`,\n                            )\n\n                            await this.query(\n                                `ALTER TABLE \"${TABLE_CATALOG}\".\"${TABLE_SCHEMA}\".\"${TABLE_NAME}\" ` +\n                                    `DROP CONSTRAINT \"${CONSTRAINT_NAME}\" -- FROM CLEAR`,\n                            )\n                        },\n                    ),\n                )\n\n                await Promise.all(\n                    allTablesResults.map((tablesResult) => {\n                        if (tablesResult[\"TABLE_NAME\"].startsWith(\"#\")) {\n                            // don't try to drop temporary tables\n                            return\n                        }\n\n                        const dropTableSql = `DROP TABLE \"${tablesResult[\"TABLE_CATALOG\"]}\".\"${tablesResult[\"TABLE_SCHEMA\"]}\".\"${tablesResult[\"TABLE_NAME\"]}\"`\n                        return this.query(dropTableSql)\n                    }),\n                )\n            }\n\n            if (!isAnotherTransactionActive) await this.commitTransaction()\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (!isAnotherTransactionActive)\n                    await this.rollbackTransaction()\n            } catch (rollbackError) {}\n            throw error\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    protected async loadViews(viewPaths?: string[]): Promise<View[]> {\n        const hasTable = await this.hasTable(this.getTypeormMetadataTableName())\n        if (!hasTable) {\n            return []\n        }\n\n        if (!viewPaths) {\n            viewPaths = []\n        }\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        const dbNames = viewPaths\n            .map((viewPath) => this.driver.parseTableName(viewPath).database)\n            .filter((database) => database)\n\n        if (\n            this.driver.database &&\n            !dbNames.find((dbName) => dbName === this.driver.database)\n        )\n            dbNames.push(this.driver.database)\n\n        const viewsCondition = viewPaths\n            .map((viewPath) => {\n                let { schema, tableName: name } =\n                    this.driver.parseTableName(viewPath)\n\n                if (!schema) {\n                    schema = currentSchema\n                }\n                return `(\"T\".\"SCHEMA\" = '${schema}' AND \"T\".\"NAME\" = '${name}')`\n            })\n            .join(\" OR \")\n\n        const query = dbNames\n            .map((dbName) => {\n                return (\n                    `SELECT \"T\".*, \"V\".\"CHECK_OPTION\" FROM ${this.escapePath(\n                        this.getTypeormMetadataTableName(),\n                    )} \"t\" ` +\n                    `INNER JOIN \"${dbName}\".\"INFORMATION_SCHEMA\".\"VIEWS\" \"V\" ON \"V\".\"TABLE_SCHEMA\" = \"T\".\"SCHEMA\" AND \"v\".\"TABLE_NAME\" = \"T\".\"NAME\" WHERE \"T\".\"TYPE\" = '${\n                        MetadataTableType.VIEW\n                    }' ${viewsCondition ? `AND (${viewsCondition})` : \"\"}`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const dbViews = await this.query(query)\n        return dbViews.map((dbView: any) => {\n            const view = new View()\n            const db =\n                dbView[\"TABLE_CATALOG\"] === currentDatabase\n                    ? undefined\n                    : dbView[\"TABLE_CATALOG\"]\n            const schema =\n                dbView[\"schema\"] === currentSchema &&\n                !this.driver.options.schema\n                    ? undefined\n                    : dbView[\"schema\"]\n            view.database = dbView[\"TABLE_CATALOG\"]\n            view.schema = dbView[\"schema\"]\n            view.name = this.driver.buildTableName(dbView[\"name\"], schema, db)\n            view.expression = dbView[\"value\"]\n            return view\n        })\n    }\n\n    /**\n     * Loads all tables (with given names) from the database and creates a Table from them.\n     */\n    protected async loadTables(tableNames?: string[]): Promise<Table[]> {\n        // if no tables given then no need to proceed\n        if (tableNames && tableNames.length === 0) {\n            return []\n        }\n\n        const currentSchema = await this.getCurrentSchema()\n        const currentDatabase = await this.getCurrentDatabase()\n\n        const dbTables: {\n            TABLE_CATALOG: string\n            TABLE_SCHEMA: string\n            TABLE_NAME: string\n        }[] = []\n\n        if (!tableNames) {\n            const databasesSql =\n                `SELECT DISTINCT \"name\" ` +\n                `FROM \"master\".\"dbo\".\"sysdatabases\" ` +\n                `WHERE \"name\" NOT IN ('master', 'model', 'msdb')`\n            const dbDatabases: { name: string }[] = await this.query(\n                databasesSql,\n            )\n\n            const tablesSql = dbDatabases\n                .map(({ name }) => {\n                    return `\n                    SELECT DISTINCT\n                        \"TABLE_CATALOG\", \"TABLE_SCHEMA\", \"TABLE_NAME\"\n                    FROM \"${name}\".\"INFORMATION_SCHEMA\".\"TABLES\"\n                    WHERE\n                      \"TABLE_TYPE\" = 'BASE TABLE'\n                      AND\n                      \"TABLE_CATALOG\" = '${name}'\n                      AND\n                      ISNULL(Objectproperty(Object_id(\"TABLE_CATALOG\" + '.' + \"TABLE_SCHEMA\" + '.' + \"TABLE_NAME\"), 'IsMSShipped'), 0) = 0\n                `\n                })\n                .join(\" UNION ALL \")\n\n            dbTables.push(...(await this.query(tablesSql)))\n        } else {\n            const tableNamesByCatalog = tableNames\n                .map((tableName) => this.driver.parseTableName(tableName))\n                .reduce((c, { database, ...other }) => {\n                    database = database || currentDatabase\n                    c[database] = c[database] || []\n                    c[database].push({\n                        schema: other.schema || currentSchema,\n                        tableName: other.tableName,\n                    })\n                    return c\n                }, {} as { [key: string]: { schema: string; tableName: string }[] })\n\n            const tablesSql = Object.entries(tableNamesByCatalog)\n                .map(([database, tables]) => {\n                    const tablesCondition = tables\n                        .map(({ schema, tableName }) => {\n                            return `(\"TABLE_SCHEMA\" = '${schema}' AND \"TABLE_NAME\" = '${tableName}')`\n                        })\n                        .join(\" OR \")\n\n                    return `\n                    SELECT DISTINCT\n                        \"TABLE_CATALOG\", \"TABLE_SCHEMA\", \"TABLE_NAME\"\n                    FROM \"${database}\".\"INFORMATION_SCHEMA\".\"TABLES\"\n                    WHERE\n                          \"TABLE_TYPE\" = 'BASE TABLE' AND\n                          \"TABLE_CATALOG\" = '${database}' AND\n                          ${tablesCondition}\n                `\n                })\n                .join(\" UNION ALL \")\n\n            dbTables.push(...(await this.query(tablesSql)))\n        }\n\n        // if tables were not found in the db, no need to proceed\n        if (dbTables.length === 0) {\n            return []\n        }\n\n        const dbTablesByCatalog = dbTables.reduce(\n            (c, { TABLE_CATALOG, ...other }) => {\n                c[TABLE_CATALOG] = c[TABLE_CATALOG] || []\n                c[TABLE_CATALOG].push(other)\n                return c\n            },\n            {} as {\n                [key: string]: { TABLE_NAME: string; TABLE_SCHEMA: string }[]\n            },\n        )\n\n        const columnsSql = Object.entries(dbTablesByCatalog)\n            .map(([TABLE_CATALOG, tables]) => {\n                const condition = tables\n                    .map(\n                        ({ TABLE_SCHEMA, TABLE_NAME }) =>\n                            `(\"TABLE_SCHEMA\" = '${TABLE_SCHEMA}' AND \"TABLE_NAME\" = '${TABLE_NAME}')`,\n                    )\n                    .join(\"OR\")\n\n                return (\n                    `SELECT \"COLUMNS\".*, \"cc\".\"is_persisted\", \"cc\".\"definition\" ` +\n                    `FROM \"${TABLE_CATALOG}\".\"INFORMATION_SCHEMA\".\"COLUMNS\" ` +\n                    `LEFT JOIN \"sys\".\"computed_columns\" \"cc\" ON COL_NAME(\"cc\".\"object_id\", \"cc\".\"column_id\") = \"column_name\" ` +\n                    `WHERE (${condition})`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const constraintsSql = Object.entries(dbTablesByCatalog)\n            .map(([TABLE_CATALOG, tables]) => {\n                const conditions = tables\n                    .map(\n                        ({ TABLE_NAME, TABLE_SCHEMA }) =>\n                            `(\"columnUsages\".\"TABLE_SCHEMA\" = '${TABLE_SCHEMA}' AND \"columnUsages\".\"TABLE_NAME\" = '${TABLE_NAME}')`,\n                    )\n                    .join(\" OR \")\n\n                return (\n                    `SELECT \"columnUsages\".*, \"tableConstraints\".\"CONSTRAINT_TYPE\", \"chk\".\"definition\" ` +\n                    `FROM \"${TABLE_CATALOG}\".\"INFORMATION_SCHEMA\".\"CONSTRAINT_COLUMN_USAGE\" \"columnUsages\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"INFORMATION_SCHEMA\".\"TABLE_CONSTRAINTS\" \"tableConstraints\" ` +\n                    `ON ` +\n                    `\"tableConstraints\".\"CONSTRAINT_NAME\" = \"columnUsages\".\"CONSTRAINT_NAME\" AND ` +\n                    `\"tableConstraints\".\"TABLE_SCHEMA\" = \"columnUsages\".\"TABLE_SCHEMA\" AND ` +\n                    `\"tableConstraints\".\"TABLE_NAME\" = \"columnUsages\".\"TABLE_NAME\" ` +\n                    `LEFT JOIN \"${TABLE_CATALOG}\".\"sys\".\"check_constraints\" \"chk\" ` +\n                    `ON ` +\n                    `\"chk\".\"object_id\" = OBJECT_ID(\"columnUsages\".\"TABLE_CATALOG\" + '.' + \"columnUsages\".\"TABLE_SCHEMA\" + '.' + \"columnUsages\".\"CONSTRAINT_NAME\") ` +\n                    `WHERE ` +\n                    `(${conditions}) AND ` +\n                    `\"tableConstraints\".\"CONSTRAINT_TYPE\" IN ('PRIMARY KEY', 'UNIQUE', 'CHECK')`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const foreignKeysSql = Object.entries(dbTablesByCatalog)\n            .map(([TABLE_CATALOG, tables]) => {\n                const conditions = tables\n                    .map(\n                        ({ TABLE_NAME, TABLE_SCHEMA }) =>\n                            `(\"s1\".\"name\" = '${TABLE_SCHEMA}' AND \"t1\".\"name\" = '${TABLE_NAME}')`,\n                    )\n                    .join(\" OR \")\n\n                return (\n                    `SELECT \"fk\".\"name\" AS \"FK_NAME\", '${TABLE_CATALOG}' AS \"TABLE_CATALOG\", \"s1\".\"name\" AS \"TABLE_SCHEMA\", \"t1\".\"name\" AS \"TABLE_NAME\", ` +\n                    `\"col1\".\"name\" AS \"COLUMN_NAME\", \"s2\".\"name\" AS \"REF_SCHEMA\", \"t2\".\"name\" AS \"REF_TABLE\", \"col2\".\"name\" AS \"REF_COLUMN\", ` +\n                    `\"fk\".\"delete_referential_action_desc\" AS \"ON_DELETE\", \"fk\".\"update_referential_action_desc\" AS \"ON_UPDATE\" ` +\n                    `FROM \"${TABLE_CATALOG}\".\"sys\".\"foreign_keys\" \"fk\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"foreign_key_columns\" \"fkc\" ON \"fkc\".\"constraint_object_id\" = \"fk\".\"object_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"tables\" \"t1\" ON \"t1\".\"object_id\" = \"fk\".\"parent_object_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"schemas\" \"s1\" ON \"s1\".\"schema_id\" = \"t1\".\"schema_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"tables\" \"t2\" ON \"t2\".\"object_id\" = \"fk\".\"referenced_object_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"schemas\" \"s2\" ON \"s2\".\"schema_id\" = \"t2\".\"schema_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"columns\" \"col1\" ON \"col1\".\"column_id\" = \"fkc\".\"parent_column_id\" AND \"col1\".\"object_id\" = \"fk\".\"parent_object_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"columns\" \"col2\" ON \"col2\".\"column_id\" = \"fkc\".\"referenced_column_id\" AND \"col2\".\"object_id\" = \"fk\".\"referenced_object_id\" ` +\n                    `WHERE (${conditions})`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const identityColumnsSql = Object.entries(dbTablesByCatalog)\n            .map(([TABLE_CATALOG, tables]) => {\n                const conditions = tables\n                    .map(\n                        ({ TABLE_NAME, TABLE_SCHEMA }) =>\n                            `(\"TABLE_SCHEMA\" = '${TABLE_SCHEMA}' AND \"TABLE_NAME\" = '${TABLE_NAME}')`,\n                    )\n                    .join(\" OR \")\n\n                return (\n                    `SELECT \"TABLE_CATALOG\", \"TABLE_SCHEMA\", \"COLUMN_NAME\", \"TABLE_NAME\" ` +\n                    `FROM \"${TABLE_CATALOG}\".\"INFORMATION_SCHEMA\".\"COLUMNS\" ` +\n                    `WHERE ` +\n                    `EXISTS(SELECT 1 FROM \"${TABLE_CATALOG}\".\"sys\".\"columns\" \"S\" WHERE OBJECT_ID(\"TABLE_CATALOG\" + '.' + \"TABLE_SCHEMA\" + '.' + \"TABLE_NAME\") = \"S\".\"OBJECT_ID\" AND \"COLUMN_NAME\" = \"S\".\"NAME\" AND \"S\".\"is_identity\" = 1) AND ` +\n                    `(${conditions})`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const dbCollationsSql = `SELECT \"NAME\", \"COLLATION_NAME\" FROM \"sys\".\"databases\"`\n\n        const indicesSql = Object.entries(dbTablesByCatalog)\n            .map(([TABLE_CATALOG, tables]) => {\n                const conditions = tables\n                    .map(\n                        ({ TABLE_NAME, TABLE_SCHEMA }) =>\n                            `(\"s\".\"name\" = '${TABLE_SCHEMA}' AND \"t\".\"name\" = '${TABLE_NAME}')`,\n                    )\n                    .join(\" OR \")\n\n                return (\n                    `SELECT '${TABLE_CATALOG}' AS \"TABLE_CATALOG\", \"s\".\"name\" AS \"TABLE_SCHEMA\", \"t\".\"name\" AS \"TABLE_NAME\", ` +\n                    `\"ind\".\"name\" AS \"INDEX_NAME\", \"col\".\"name\" AS \"COLUMN_NAME\", \"ind\".\"is_unique\" AS \"IS_UNIQUE\", \"ind\".\"filter_definition\" as \"CONDITION\" ` +\n                    `FROM \"${TABLE_CATALOG}\".\"sys\".\"indexes\" \"ind\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"index_columns\" \"ic\" ON \"ic\".\"object_id\" = \"ind\".\"object_id\" AND \"ic\".\"index_id\" = \"ind\".\"index_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"columns\" \"col\" ON \"col\".\"object_id\" = \"ic\".\"object_id\" AND \"col\".\"column_id\" = \"ic\".\"column_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"tables\" \"t\" ON \"t\".\"object_id\" = \"ind\".\"object_id\" ` +\n                    `INNER JOIN \"${TABLE_CATALOG}\".\"sys\".\"schemas\" \"s\" ON \"s\".\"schema_id\" = \"t\".\"schema_id\" ` +\n                    `WHERE ` +\n                    `\"ind\".\"is_primary_key\" = 0 AND \"ind\".\"is_unique_constraint\" = 0 AND \"t\".\"is_ms_shipped\" = 0 AND ` +\n                    `(${conditions})`\n                )\n            })\n            .join(\" UNION ALL \")\n\n        const [\n            dbColumns,\n            dbConstraints,\n            dbForeignKeys,\n            dbIdentityColumns,\n            dbCollations,\n            dbIndices,\n        ]: ObjectLiteral[][] = await Promise.all([\n            this.query(columnsSql),\n            this.query(constraintsSql),\n            this.query(foreignKeysSql),\n            this.query(identityColumnsSql),\n            this.query(dbCollationsSql),\n            this.query(indicesSql),\n        ])\n\n        // create table schemas for loaded tables\n        return await Promise.all(\n            dbTables.map(async (dbTable) => {\n                const table = new Table()\n\n                const getSchemaFromKey = (dbObject: any, key: string) => {\n                    return dbObject[key] === currentSchema &&\n                        (!this.driver.options.schema ||\n                            this.driver.options.schema === currentSchema)\n                        ? undefined\n                        : dbObject[key]\n                }\n\n                // We do not need to join schema and database names, when db or schema is by default.\n                const db =\n                    dbTable[\"TABLE_CATALOG\"] === currentDatabase\n                        ? undefined\n                        : dbTable[\"TABLE_CATALOG\"]\n                const schema = getSchemaFromKey(dbTable, \"TABLE_SCHEMA\")\n                table.database = dbTable[\"TABLE_CATALOG\"]\n                table.schema = dbTable[\"TABLE_SCHEMA\"]\n                table.name = this.driver.buildTableName(\n                    dbTable[\"TABLE_NAME\"],\n                    schema,\n                    db,\n                )\n\n                const defaultCollation = dbCollations.find(\n                    (dbCollation) =>\n                        dbCollation[\"NAME\"] === dbTable[\"TABLE_CATALOG\"],\n                )!\n\n                // create columns from the loaded columns\n                table.columns = await Promise.all(\n                    dbColumns\n                        .filter(\n                            (dbColumn) =>\n                                dbColumn[\"TABLE_NAME\"] ===\n                                    dbTable[\"TABLE_NAME\"] &&\n                                dbColumn[\"TABLE_SCHEMA\"] ===\n                                    dbTable[\"TABLE_SCHEMA\"] &&\n                                dbColumn[\"TABLE_CATALOG\"] ===\n                                    dbTable[\"TABLE_CATALOG\"],\n                        )\n                        .map(async (dbColumn) => {\n                            const columnConstraints = dbConstraints.filter(\n                                (dbConstraint) =>\n                                    dbConstraint[\"TABLE_NAME\"] ===\n                                        dbColumn[\"TABLE_NAME\"] &&\n                                    dbConstraint[\"TABLE_SCHEMA\"] ===\n                                        dbColumn[\"TABLE_SCHEMA\"] &&\n                                    dbConstraint[\"TABLE_CATALOG\"] ===\n                                        dbColumn[\"TABLE_CATALOG\"] &&\n                                    dbConstraint[\"COLUMN_NAME\"] ===\n                                        dbColumn[\"COLUMN_NAME\"],\n                            )\n\n                            const uniqueConstraints = columnConstraints.filter(\n                                (constraint) =>\n                                    constraint[\"CONSTRAINT_TYPE\"] === \"UNIQUE\",\n                            )\n                            const isConstraintComposite =\n                                uniqueConstraints.every((uniqueConstraint) => {\n                                    return dbConstraints.some(\n                                        (dbConstraint) =>\n                                            dbConstraint[\"CONSTRAINT_TYPE\"] ===\n                                                \"UNIQUE\" &&\n                                            dbConstraint[\"CONSTRAINT_NAME\"] ===\n                                                uniqueConstraint[\n                                                    \"CONSTRAINT_NAME\"\n                                                ] &&\n                                            dbConstraint[\"TABLE_SCHEMA\"] ===\n                                                dbColumn[\"TABLE_SCHEMA\"] &&\n                                            dbConstraint[\"TABLE_CATALOG\"] ===\n                                                dbColumn[\"TABLE_CATALOG\"] &&\n                                            dbConstraint[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"],\n                                    )\n                                })\n\n                            const isGenerated = !!dbIdentityColumns.find(\n                                (column) =>\n                                    column[\"TABLE_NAME\"] ===\n                                        dbColumn[\"TABLE_NAME\"] &&\n                                    column[\"TABLE_SCHEMA\"] ===\n                                        dbColumn[\"TABLE_SCHEMA\"] &&\n                                    column[\"TABLE_CATALOG\"] ===\n                                        dbColumn[\"TABLE_CATALOG\"] &&\n                                    column[\"COLUMN_NAME\"] ===\n                                        dbColumn[\"COLUMN_NAME\"],\n                            )\n\n                            const tableColumn = new TableColumn()\n                            tableColumn.name = dbColumn[\"COLUMN_NAME\"]\n                            tableColumn.type =\n                                dbColumn[\"DATA_TYPE\"].toLowerCase()\n\n                            // check only columns that have length property\n                            if (\n                                this.driver.withLengthColumnTypes.indexOf(\n                                    tableColumn.type as ColumnType,\n                                ) !== -1 &&\n                                dbColumn[\"CHARACTER_MAXIMUM_LENGTH\"]\n                            ) {\n                                const length =\n                                    dbColumn[\n                                        \"CHARACTER_MAXIMUM_LENGTH\"\n                                    ].toString()\n                                if (length === \"-1\") {\n                                    tableColumn.length = \"MAX\"\n                                } else {\n                                    tableColumn.length =\n                                        !this.isDefaultColumnLength(\n                                            table,\n                                            tableColumn,\n                                            length,\n                                        )\n                                            ? length\n                                            : \"\"\n                                }\n                            }\n\n                            if (\n                                tableColumn.type === \"decimal\" ||\n                                tableColumn.type === \"numeric\"\n                            ) {\n                                if (\n                                    dbColumn[\"NUMERIC_PRECISION\"] !== null &&\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"NUMERIC_PRECISION\"],\n                                    )\n                                )\n                                    tableColumn.precision =\n                                        dbColumn[\"NUMERIC_PRECISION\"]\n                                if (\n                                    dbColumn[\"NUMERIC_SCALE\"] !== null &&\n                                    !this.isDefaultColumnScale(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"NUMERIC_SCALE\"],\n                                    )\n                                )\n                                    tableColumn.scale =\n                                        dbColumn[\"NUMERIC_SCALE\"]\n                            }\n\n                            if (tableColumn.type === \"nvarchar\") {\n                                // Check if this is an enum\n                                const columnCheckConstraints =\n                                    columnConstraints.filter(\n                                        (constraint) =>\n                                            constraint[\"CONSTRAINT_TYPE\"] ===\n                                            \"CHECK\",\n                                    )\n                                if (columnCheckConstraints.length) {\n                                    // const isEnumRegexp = new RegExp(\"^\\\\(\\\\[\" + tableColumn.name + \"\\\\]='[^']+'(?: OR \\\\[\" + tableColumn.name + \"\\\\]='[^']+')*\\\\)$\");\n                                    for (const checkConstraint of columnCheckConstraints) {\n                                        if (\n                                            this.isEnumCheckConstraint(\n                                                checkConstraint[\n                                                    \"CONSTRAINT_NAME\"\n                                                ],\n                                            )\n                                        ) {\n                                            // This is an enum constraint, make column into an enum\n                                            tableColumn.enum = []\n                                            const enumValueRegexp = new RegExp(\n                                                \"\\\\[\" +\n                                                    tableColumn.name +\n                                                    \"\\\\]='([^']+)'\",\n                                                \"g\",\n                                            )\n                                            let result\n                                            while (\n                                                (result = enumValueRegexp.exec(\n                                                    checkConstraint[\n                                                        \"definition\"\n                                                    ],\n                                                )) !== null\n                                            ) {\n                                                tableColumn.enum.unshift(\n                                                    result[1],\n                                                )\n                                            }\n                                            // Skip other column constraints\n                                            break\n                                        }\n                                    }\n                                }\n                            }\n\n                            const primaryConstraint = columnConstraints.find(\n                                (constraint) =>\n                                    constraint[\"CONSTRAINT_TYPE\"] ===\n                                    \"PRIMARY KEY\",\n                            )\n                            if (primaryConstraint) {\n                                tableColumn.isPrimary = true\n                                // find another columns involved in primary key constraint\n                                const anotherPrimaryConstraints =\n                                    dbConstraints.filter(\n                                        (constraint) =>\n                                            constraint[\"TABLE_NAME\"] ===\n                                                dbColumn[\"TABLE_NAME\"] &&\n                                            constraint[\"TABLE_SCHEMA\"] ===\n                                                dbColumn[\"TABLE_SCHEMA\"] &&\n                                            constraint[\"TABLE_CATALOG\"] ===\n                                                dbColumn[\"TABLE_CATALOG\"] &&\n                                            constraint[\"COLUMN_NAME\"] !==\n                                                dbColumn[\"COLUMN_NAME\"] &&\n                                            constraint[\"CONSTRAINT_TYPE\"] ===\n                                                \"PRIMARY KEY\",\n                                    )\n\n                                // collect all column names\n                                const columnNames =\n                                    anotherPrimaryConstraints.map(\n                                        (constraint) =>\n                                            constraint[\"COLUMN_NAME\"],\n                                    )\n                                columnNames.push(dbColumn[\"COLUMN_NAME\"])\n\n                                // build default primary key constraint name\n                                const pkName =\n                                    this.connection.namingStrategy.primaryKeyName(\n                                        table,\n                                        columnNames,\n                                    )\n\n                                // if primary key has user-defined constraint name, write it in table column\n                                if (\n                                    primaryConstraint[\"CONSTRAINT_NAME\"] !==\n                                    pkName\n                                ) {\n                                    tableColumn.primaryKeyConstraintName =\n                                        primaryConstraint[\"CONSTRAINT_NAME\"]\n                                }\n                            }\n\n                            tableColumn.default =\n                                dbColumn[\"COLUMN_DEFAULT\"] !== null &&\n                                dbColumn[\"COLUMN_DEFAULT\"] !== undefined\n                                    ? this.removeParenthesisFromDefault(\n                                          dbColumn[\"COLUMN_DEFAULT\"],\n                                      )\n                                    : undefined\n                            tableColumn.isNullable =\n                                dbColumn[\"IS_NULLABLE\"] === \"YES\"\n                            tableColumn.isUnique =\n                                uniqueConstraints.length > 0 &&\n                                !isConstraintComposite\n                            tableColumn.isGenerated = isGenerated\n                            if (isGenerated)\n                                tableColumn.generationStrategy = \"increment\"\n                            if (tableColumn.default === \"newsequentialid()\") {\n                                tableColumn.isGenerated = true\n                                tableColumn.generationStrategy = \"uuid\"\n                                tableColumn.default = undefined\n                            }\n\n                            // todo: unable to get default charset\n                            // tableColumn.charset = dbColumn[\"CHARACTER_SET_NAME\"];\n                            if (dbColumn[\"COLLATION_NAME\"])\n                                tableColumn.collation =\n                                    dbColumn[\"COLLATION_NAME\"] ===\n                                    defaultCollation[\"COLLATION_NAME\"]\n                                        ? undefined\n                                        : dbColumn[\"COLLATION_NAME\"]\n\n                            if (\n                                tableColumn.type === \"datetime2\" ||\n                                tableColumn.type === \"time\" ||\n                                tableColumn.type === \"datetimeoffset\"\n                            ) {\n                                tableColumn.precision =\n                                    !this.isDefaultColumnPrecision(\n                                        table,\n                                        tableColumn,\n                                        dbColumn[\"DATETIME_PRECISION\"],\n                                    )\n                                        ? dbColumn[\"DATETIME_PRECISION\"]\n                                        : undefined\n                            }\n\n                            if (\n                                dbColumn[\"is_persisted\"] !== null &&\n                                dbColumn[\"is_persisted\"] !== undefined &&\n                                dbColumn[\"definition\"]\n                            ) {\n                                tableColumn.generatedType =\n                                    dbColumn[\"is_persisted\"] === true\n                                        ? \"STORED\"\n                                        : \"VIRTUAL\"\n                                // We cannot relay on information_schema.columns.generation_expression, because it is formatted different.\n                                const asExpressionQuery =\n                                    this.selectTypeormMetadataSql({\n                                        database: dbTable[\"TABLE_CATALOG\"],\n                                        schema: dbTable[\"TABLE_SCHEMA\"],\n                                        table: dbTable[\"TABLE_NAME\"],\n                                        type: MetadataTableType.GENERATED_COLUMN,\n                                        name: tableColumn.name,\n                                    })\n\n                                const results = await this.query(\n                                    asExpressionQuery.query,\n                                    asExpressionQuery.parameters,\n                                )\n                                if (results[0] && results[0].value) {\n                                    tableColumn.asExpression = results[0].value\n                                } else {\n                                    tableColumn.asExpression = \"\"\n                                }\n                            }\n\n                            return tableColumn\n                        }),\n                )\n\n                // find unique constraints of table, group them by constraint name and build TableUnique.\n                const tableUniqueConstraints = OrmUtils.uniq(\n                    dbConstraints.filter(\n                        (dbConstraint) =>\n                            dbConstraint[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbConstraint[\"TABLE_SCHEMA\"] ===\n                                dbTable[\"TABLE_SCHEMA\"] &&\n                            dbConstraint[\"TABLE_CATALOG\"] ===\n                                dbTable[\"TABLE_CATALOG\"] &&\n                            dbConstraint[\"CONSTRAINT_TYPE\"] === \"UNIQUE\",\n                    ),\n                    (dbConstraint) => dbConstraint[\"CONSTRAINT_NAME\"],\n                )\n\n                table.uniques = tableUniqueConstraints.map((constraint) => {\n                    const uniques = dbConstraints.filter(\n                        (dbC) =>\n                            dbC[\"CONSTRAINT_NAME\"] ===\n                            constraint[\"CONSTRAINT_NAME\"],\n                    )\n                    return new TableUnique({\n                        name: constraint[\"CONSTRAINT_NAME\"],\n                        columnNames: uniques.map((u) => u[\"COLUMN_NAME\"]),\n                    })\n                })\n\n                // find check constraints of table, group them by constraint name and build TableCheck.\n                const tableCheckConstraints = OrmUtils.uniq(\n                    dbConstraints.filter(\n                        (dbConstraint) =>\n                            dbConstraint[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbConstraint[\"TABLE_SCHEMA\"] ===\n                                dbTable[\"TABLE_SCHEMA\"] &&\n                            dbConstraint[\"TABLE_CATALOG\"] ===\n                                dbTable[\"TABLE_CATALOG\"] &&\n                            dbConstraint[\"CONSTRAINT_TYPE\"] === \"CHECK\",\n                    ),\n                    (dbConstraint) => dbConstraint[\"CONSTRAINT_NAME\"],\n                )\n\n                table.checks = tableCheckConstraints\n                    .filter(\n                        (constraint) =>\n                            !this.isEnumCheckConstraint(\n                                constraint[\"CONSTRAINT_NAME\"],\n                            ),\n                    )\n                    .map((constraint) => {\n                        const checks = dbConstraints.filter(\n                            (dbC) =>\n                                dbC[\"CONSTRAINT_NAME\"] ===\n                                constraint[\"CONSTRAINT_NAME\"],\n                        )\n                        return new TableCheck({\n                            name: constraint[\"CONSTRAINT_NAME\"],\n                            columnNames: checks.map((c) => c[\"COLUMN_NAME\"]),\n                            expression: constraint[\"definition\"],\n                        })\n                    })\n\n                // find foreign key constraints of table, group them by constraint name and build TableForeignKey.\n                const tableForeignKeyConstraints = OrmUtils.uniq(\n                    dbForeignKeys.filter(\n                        (dbForeignKey) =>\n                            dbForeignKey[\"TABLE_NAME\"] ===\n                                dbTable[\"TABLE_NAME\"] &&\n                            dbForeignKey[\"TABLE_SCHEMA\"] ===\n                                dbTable[\"TABLE_SCHEMA\"] &&\n                            dbForeignKey[\"TABLE_CATALOG\"] ===\n                                dbTable[\"TABLE_CATALOG\"],\n                    ),\n                    (dbForeignKey) => dbForeignKey[\"FK_NAME\"],\n                )\n\n                table.foreignKeys = tableForeignKeyConstraints.map(\n                    (dbForeignKey) => {\n                        const foreignKeys = dbForeignKeys.filter(\n                            (dbFk) =>\n                                dbFk[\"FK_NAME\"] === dbForeignKey[\"FK_NAME\"],\n                        )\n\n                        // if referenced table located in currently used db and schema, we don't need to concat db and schema names to table name.\n                        const db =\n                            dbForeignKey[\"TABLE_CATALOG\"] === currentDatabase\n                                ? undefined\n                                : dbForeignKey[\"TABLE_CATALOG\"]\n                        const schema = getSchemaFromKey(\n                            dbForeignKey,\n                            \"REF_SCHEMA\",\n                        )\n                        const referencedTableName = this.driver.buildTableName(\n                            dbForeignKey[\"REF_TABLE\"],\n                            schema,\n                            db,\n                        )\n\n                        return new TableForeignKey({\n                            name: dbForeignKey[\"FK_NAME\"],\n                            columnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"COLUMN_NAME\"],\n                            ),\n                            referencedDatabase: dbForeignKey[\"TABLE_CATALOG\"],\n                            referencedSchema: dbForeignKey[\"REF_SCHEMA\"],\n                            referencedTableName: referencedTableName,\n                            referencedColumnNames: foreignKeys.map(\n                                (dbFk) => dbFk[\"REF_COLUMN\"],\n                            ),\n                            onDelete: dbForeignKey[\"ON_DELETE\"].replace(\n                                \"_\",\n                                \" \",\n                            ), // SqlServer returns NO_ACTION, instead of NO ACTION\n                            onUpdate: dbForeignKey[\"ON_UPDATE\"].replace(\n                                \"_\",\n                                \" \",\n                            ), // SqlServer returns NO_ACTION, instead of NO ACTION\n                        })\n                    },\n                )\n\n                // find index constraints of table, group them by constraint name and build TableIndex.\n                const tableIndexConstraints = OrmUtils.uniq(\n                    dbIndices.filter(\n                        (dbIndex) =>\n                            dbIndex[\"TABLE_NAME\"] === dbTable[\"TABLE_NAME\"] &&\n                            dbIndex[\"TABLE_SCHEMA\"] ===\n                                dbTable[\"TABLE_SCHEMA\"] &&\n                            dbIndex[\"TABLE_CATALOG\"] ===\n                                dbTable[\"TABLE_CATALOG\"],\n                    ),\n                    (dbIndex) => dbIndex[\"INDEX_NAME\"],\n                )\n\n                table.indices = tableIndexConstraints.map((constraint) => {\n                    const indices = dbIndices.filter((index) => {\n                        return (\n                            index[\"TABLE_CATALOG\"] ===\n                                constraint[\"TABLE_CATALOG\"] &&\n                            index[\"TABLE_SCHEMA\"] ===\n                                constraint[\"TABLE_SCHEMA\"] &&\n                            index[\"TABLE_NAME\"] === constraint[\"TABLE_NAME\"] &&\n                            index[\"INDEX_NAME\"] === constraint[\"INDEX_NAME\"]\n                        )\n                    })\n                    return new TableIndex(<TableIndexOptions>{\n                        table: table,\n                        name: constraint[\"INDEX_NAME\"],\n                        columnNames: indices.map((i) => i[\"COLUMN_NAME\"]),\n                        isUnique: constraint[\"IS_UNIQUE\"],\n                        where: constraint[\"CONDITION\"],\n                    })\n                })\n\n                return table\n            }),\n        )\n    }\n\n    /**\n     * Builds and returns SQL for create table.\n     */\n    protected createTableSql(table: Table, createForeignKeys?: boolean): Query {\n        const columnDefinitions = table.columns\n            .map((column) =>\n                this.buildCreateColumnSql(table, column, false, true),\n            )\n            .join(\", \")\n        let sql = `CREATE TABLE ${this.escapePath(table)} (${columnDefinitions}`\n\n        table.columns\n            .filter((column) => column.isUnique)\n            .forEach((column) => {\n                const isUniqueExist = table.uniques.some(\n                    (unique) =>\n                        unique.columnNames.length === 1 &&\n                        unique.columnNames[0] === column.name,\n                )\n                if (!isUniqueExist)\n                    table.uniques.push(\n                        new TableUnique({\n                            name: this.connection.namingStrategy.uniqueConstraintName(\n                                table,\n                                [column.name],\n                            ),\n                            columnNames: [column.name],\n                        }),\n                    )\n            })\n\n        if (table.uniques.length > 0) {\n            const uniquesSql = table.uniques\n                .map((unique) => {\n                    const uniqueName = unique.name\n                        ? unique.name\n                        : this.connection.namingStrategy.uniqueConstraintName(\n                              table,\n                              unique.columnNames,\n                          )\n                    const columnNames = unique.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    return `CONSTRAINT \"${uniqueName}\" UNIQUE (${columnNames})`\n                })\n                .join(\", \")\n\n            sql += `, ${uniquesSql}`\n        }\n\n        if (table.checks.length > 0) {\n            const checksSql = table.checks\n                .map((check) => {\n                    const checkName = check.name\n                        ? check.name\n                        : this.connection.namingStrategy.checkConstraintName(\n                              table,\n                              check.expression!,\n                          )\n                    return `CONSTRAINT \"${checkName}\" CHECK (${check.expression})`\n                })\n                .join(\", \")\n\n            sql += `, ${checksSql}`\n        }\n\n        if (table.foreignKeys.length > 0 && createForeignKeys) {\n            const foreignKeysSql = table.foreignKeys\n                .map((fk) => {\n                    const columnNames = fk.columnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n                    if (!fk.name)\n                        fk.name = this.connection.namingStrategy.foreignKeyName(\n                            table,\n                            fk.columnNames,\n                            this.getTablePath(fk),\n                            fk.referencedColumnNames,\n                        )\n                    const referencedColumnNames = fk.referencedColumnNames\n                        .map((columnName) => `\"${columnName}\"`)\n                        .join(\", \")\n\n                    let constraint = `CONSTRAINT \"${\n                        fk.name\n                    }\" FOREIGN KEY (${columnNames}) REFERENCES ${this.escapePath(\n                        this.getTablePath(fk),\n                    )} (${referencedColumnNames})`\n                    if (fk.onDelete) constraint += ` ON DELETE ${fk.onDelete}`\n                    if (fk.onUpdate) constraint += ` ON UPDATE ${fk.onUpdate}`\n\n                    return constraint\n                })\n                .join(\", \")\n\n            sql += `, ${foreignKeysSql}`\n        }\n\n        const primaryColumns = table.columns.filter(\n            (column) => column.isPrimary,\n        )\n        if (primaryColumns.length > 0) {\n            const primaryKeyName = primaryColumns[0].primaryKeyConstraintName\n                ? primaryColumns[0].primaryKeyConstraintName\n                : this.connection.namingStrategy.primaryKeyName(\n                      table,\n                      primaryColumns.map((column) => column.name),\n                  )\n\n            const columnNames = primaryColumns\n                .map((column) => `\"${column.name}\"`)\n                .join(\", \")\n            sql += `, CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNames})`\n        }\n\n        sql += `)`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop table sql.\n     */\n    protected dropTableSql(\n        tableOrName: Table | string,\n        ifExist?: boolean,\n    ): Query {\n        const query = ifExist\n            ? `DROP TABLE IF EXISTS ${this.escapePath(tableOrName)}`\n            : `DROP TABLE ${this.escapePath(tableOrName)}`\n        return new Query(query)\n    }\n\n    protected createViewSql(view: View): Query {\n        const parsedName = this.driver.parseTableName(view)\n\n        // Can't use `escapePath` here because `CREATE VIEW` does not accept database names.\n        const viewIdentifier = parsedName.schema\n            ? `\"${parsedName.schema}\".\"${parsedName.tableName}\"`\n            : `\"${parsedName.tableName}\"`\n\n        if (typeof view.expression === \"string\") {\n            return new Query(\n                `CREATE VIEW ${viewIdentifier} AS ${view.expression}`,\n            )\n        } else {\n            return new Query(\n                `CREATE VIEW ${viewIdentifier} AS ${view\n                    .expression(this.connection)\n                    .getQuery()}`,\n            )\n        }\n    }\n\n    protected async insertViewDefinitionSql(view: View): Promise<Query> {\n        const parsedTableName = this.driver.parseTableName(view)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        const expression =\n            typeof view.expression === \"string\"\n                ? view.expression.trim()\n                : view.expression(this.connection).getQuery()\n        return this.insertTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            database: parsedTableName.database,\n            schema: parsedTableName.schema,\n            name: parsedTableName.tableName,\n            value: expression,\n        })\n    }\n\n    /**\n     * Builds drop view sql.\n     */\n    protected dropViewSql(viewOrPath: View | string): Query {\n        return new Query(`DROP VIEW ${this.escapePath(viewOrPath)}`)\n    }\n\n    /**\n     * Builds remove view sql.\n     */\n    protected async deleteViewDefinitionSql(\n        viewOrPath: View | string,\n    ): Promise<Query> {\n        const parsedTableName = this.driver.parseTableName(viewOrPath)\n\n        if (!parsedTableName.schema) {\n            parsedTableName.schema = await this.getCurrentSchema()\n        }\n\n        return this.deleteTypeormMetadataSql({\n            type: MetadataTableType.VIEW,\n            database: parsedTableName.database,\n            schema: parsedTableName.schema,\n            name: parsedTableName.tableName,\n        })\n    }\n\n    /**\n     * Builds create index sql.\n     */\n    protected createIndexSql(table: Table, index: TableIndex): Query {\n        const columns = index.columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `CREATE ${index.isUnique ? \"UNIQUE \" : \"\"}INDEX \"${\n                index.name\n            }\" ON ${this.escapePath(table)} (${columns}) ${\n                index.where ? \"WHERE \" + index.where : \"\"\n            }`,\n        )\n    }\n\n    /**\n     * Builds drop index sql.\n     */\n    protected dropIndexSql(\n        table: Table,\n        indexOrName: TableIndex | string,\n    ): Query {\n        const indexName = InstanceChecker.isTableIndex(indexOrName)\n            ? indexOrName.name\n            : indexOrName\n        return new Query(\n            `DROP INDEX \"${indexName}\" ON ${this.escapePath(table)}`,\n        )\n    }\n\n    /**\n     * Builds create primary key sql.\n     */\n    protected createPrimaryKeySql(\n        table: Table,\n        columnNames: string[],\n        constraintName?: string,\n    ): Query {\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        const columnNamesString = columnNames\n            .map((columnName) => `\"${columnName}\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} ADD CONSTRAINT \"${primaryKeyName}\" PRIMARY KEY (${columnNamesString})`,\n        )\n    }\n\n    /**\n     * Builds drop primary key sql.\n     */\n    protected dropPrimaryKeySql(table: Table): Query {\n        const columnNames = table.primaryColumns.map((column) => column.name)\n        const constraintName = table.primaryColumns[0].primaryKeyConstraintName\n        const primaryKeyName = constraintName\n            ? constraintName\n            : this.connection.namingStrategy.primaryKeyName(table, columnNames)\n\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${primaryKeyName}\"`,\n        )\n    }\n\n    /**\n     * Builds create unique constraint sql.\n     */\n    protected createUniqueConstraintSql(\n        table: Table,\n        uniqueConstraint: TableUnique,\n    ): Query {\n        const columnNames = uniqueConstraint.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                uniqueConstraint.name\n            }\" UNIQUE (${columnNames})`,\n        )\n    }\n\n    /**\n     * Builds drop unique constraint sql.\n     */\n    protected dropUniqueConstraintSql(\n        table: Table,\n        uniqueOrName: TableUnique | string,\n    ): Query {\n        const uniqueName = InstanceChecker.isTableUnique(uniqueOrName)\n            ? uniqueOrName.name\n            : uniqueOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${uniqueName}\"`,\n        )\n    }\n\n    /**\n     * Builds create check constraint sql.\n     */\n    protected createCheckConstraintSql(\n        table: Table,\n        checkConstraint: TableCheck,\n    ): Query {\n        return new Query(\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                checkConstraint.name\n            }\" CHECK (${checkConstraint.expression})`,\n        )\n    }\n\n    /**\n     * Builds drop check constraint sql.\n     */\n    protected dropCheckConstraintSql(\n        table: Table,\n        checkOrName: TableCheck | string,\n    ): Query {\n        const checkName = InstanceChecker.isTableCheck(checkOrName)\n            ? checkOrName.name\n            : checkOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${checkName}\"`,\n        )\n    }\n\n    /**\n     * Builds create foreign key sql.\n     */\n    protected createForeignKeySql(\n        table: Table,\n        foreignKey: TableForeignKey,\n    ): Query {\n        const columnNames = foreignKey.columnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\", \")\n        const referencedColumnNames = foreignKey.referencedColumnNames\n            .map((column) => `\"` + column + `\"`)\n            .join(\",\")\n        let sql =\n            `ALTER TABLE ${this.escapePath(table)} ADD CONSTRAINT \"${\n                foreignKey.name\n            }\" FOREIGN KEY (${columnNames}) ` +\n            `REFERENCES ${this.escapePath(\n                this.getTablePath(foreignKey),\n            )}(${referencedColumnNames})`\n        if (foreignKey.onDelete) sql += ` ON DELETE ${foreignKey.onDelete}`\n        if (foreignKey.onUpdate) sql += ` ON UPDATE ${foreignKey.onUpdate}`\n\n        return new Query(sql)\n    }\n\n    /**\n     * Builds drop foreign key sql.\n     */\n    protected dropForeignKeySql(\n        table: Table,\n        foreignKeyOrName: TableForeignKey | string,\n    ): Query {\n        const foreignKeyName = InstanceChecker.isTableForeignKey(\n            foreignKeyOrName,\n        )\n            ? foreignKeyOrName.name\n            : foreignKeyOrName\n        return new Query(\n            `ALTER TABLE ${this.escapePath(\n                table,\n            )} DROP CONSTRAINT \"${foreignKeyName}\"`,\n        )\n    }\n\n    /**\n     * Escapes given table or View path.\n     */\n    protected escapePath(target: Table | View | string): string {\n        const { database, schema, tableName } =\n            this.driver.parseTableName(target)\n\n        if (database && database !== this.driver.database) {\n            if (schema && schema !== this.driver.searchSchema) {\n                return `\"${database}\".\"${schema}\".\"${tableName}\"`\n            }\n\n            return `\"${database}\"..\"${tableName}\"`\n        }\n\n        if (schema && schema !== this.driver.searchSchema) {\n            return `\"${schema}\".\"${tableName}\"`\n        }\n\n        return `\"${tableName}\"`\n    }\n\n    /**\n     * Concat database name and schema name to the foreign key name.\n     * Needs because FK name is relevant to the schema and database.\n     */\n    protected buildForeignKeyName(\n        fkName: string,\n        schemaName: string | undefined,\n        dbName: string | undefined,\n    ): string {\n        let joinedFkName = fkName\n        if (schemaName && schemaName !== this.driver.searchSchema)\n            joinedFkName = schemaName + \".\" + joinedFkName\n        if (dbName && dbName !== this.driver.database)\n            joinedFkName = dbName + \".\" + joinedFkName\n\n        return joinedFkName\n    }\n\n    /**\n     * Removes parenthesis around default value.\n     * Sql server returns default value with parenthesis around, e.g.\n     *  ('My text') - for string\n     *  ((1)) - for number\n     *  (newsequentialId()) - for function\n     */\n    protected removeParenthesisFromDefault(defaultValue: string): any {\n        if (defaultValue.substr(0, 1) !== \"(\") return defaultValue\n        const normalizedDefault = defaultValue.substr(\n            1,\n            defaultValue.lastIndexOf(\")\") - 1,\n        )\n        return this.removeParenthesisFromDefault(normalizedDefault)\n    }\n\n    /**\n     * Builds a query for create column.\n     */\n    protected buildCreateColumnSql(\n        table: Table,\n        column: TableColumn,\n        skipIdentity: boolean,\n        createDefault: boolean,\n        skipEnum?: boolean,\n    ) {\n        let c = `\"${column.name}\" ${this.connection.driver.createFullType(\n            column,\n        )}`\n\n        if (!skipEnum && column.enum) {\n            const expression = this.getEnumExpression(column)\n            const checkName =\n                this.connection.namingStrategy.checkConstraintName(\n                    table,\n                    expression,\n                    true,\n                )\n            c += ` CONSTRAINT ${checkName} CHECK(${expression})`\n        }\n\n        if (column.collation) c += \" COLLATE \" + column.collation\n\n        if (column.asExpression) {\n            c += ` AS (${column.asExpression})`\n            if (column.generatedType === \"STORED\") {\n                c += ` PERSISTED`\n\n                // NOT NULL can be specified for computed columns only if PERSISTED is also specified\n                if (column.isNullable !== true) c += \" NOT NULL\"\n            }\n        } else {\n            if (column.isNullable !== true) c += \" NOT NULL\"\n        }\n\n        if (\n            column.isGenerated === true &&\n            column.generationStrategy === \"increment\" &&\n            !skipIdentity\n        )\n            // don't use skipPrimary here since updates can update already exist primary without auto inc.\n            c += \" IDENTITY(1,1)\"\n\n        if (\n            column.default !== undefined &&\n            column.default !== null &&\n            createDefault\n        ) {\n            // we create named constraint to be able to delete this constraint when column been dropped\n            const defaultName =\n                this.connection.namingStrategy.defaultConstraintName(\n                    table,\n                    column.name,\n                )\n            c += ` CONSTRAINT \"${defaultName}\" DEFAULT ${column.default}`\n        }\n\n        if (\n            column.isGenerated &&\n            column.generationStrategy === \"uuid\" &&\n            !column.default\n        ) {\n            // we create named constraint to be able to delete this constraint when column been dropped\n            const defaultName =\n                this.connection.namingStrategy.defaultConstraintName(\n                    table,\n                    column.name,\n                )\n            c += ` CONSTRAINT \"${defaultName}\" DEFAULT NEWSEQUENTIALID()`\n        }\n        return c\n    }\n\n    private getEnumExpression(column: TableColumn) {\n        if (!column.enum) {\n            throw new Error(`Enum is not defined in column ${column.name}`)\n        }\n        return (\n            column.name +\n            \" IN (\" +\n            column.enum.map((val) => \"'\" + val + \"'\").join(\",\") +\n            \")\"\n        )\n    }\n\n    protected isEnumCheckConstraint(name: string): boolean {\n        return name.indexOf(\"CHK_\") !== -1 && name.indexOf(\"_ENUM\") !== -1\n    }\n\n    /**\n     * Converts MssqlParameter into real mssql parameter type.\n     */\n    protected mssqlParameterToNativeParameter(parameter: MssqlParameter): any {\n        switch (this.driver.normalizeType({ type: parameter.type as any })) {\n            case \"bit\":\n                return this.driver.mssql.Bit\n            case \"bigint\":\n                return this.driver.mssql.BigInt\n            case \"decimal\":\n                return this.driver.mssql.Decimal(...parameter.params)\n            case \"float\":\n                return this.driver.mssql.Float\n            case \"int\":\n                return this.driver.mssql.Int\n            case \"money\":\n                return this.driver.mssql.Money\n            case \"numeric\":\n                return this.driver.mssql.Numeric(...parameter.params)\n            case \"smallint\":\n                return this.driver.mssql.SmallInt\n            case \"smallmoney\":\n                return this.driver.mssql.SmallMoney\n            case \"real\":\n                return this.driver.mssql.Real\n            case \"tinyint\":\n                return this.driver.mssql.TinyInt\n            case \"char\":\n                if (\n                    this.driver.options.options\n                        ?.disableAsciiToUnicodeParamConversion\n                ) {\n                    return this.driver.mssql.Char(...parameter.params)\n                }\n                return this.driver.mssql.NChar(...parameter.params)\n            case \"nchar\":\n                return this.driver.mssql.NChar(...parameter.params)\n            case \"text\":\n                if (\n                    this.driver.options.options\n                        ?.disableAsciiToUnicodeParamConversion\n                ) {\n                    return this.driver.mssql.Text\n                }\n                return this.driver.mssql.Ntext\n            case \"ntext\":\n                return this.driver.mssql.Ntext\n            case \"varchar\":\n                if (\n                    this.driver.options.options\n                        ?.disableAsciiToUnicodeParamConversion\n                ) {\n                    return this.driver.mssql.VarChar(...parameter.params)\n                }\n                return this.driver.mssql.NVarChar(...parameter.params)\n            case \"nvarchar\":\n                return this.driver.mssql.NVarChar(...parameter.params)\n            case \"xml\":\n                return this.driver.mssql.Xml\n            case \"time\":\n                return this.driver.mssql.Time(...parameter.params)\n            case \"date\":\n                return this.driver.mssql.Date\n            case \"datetime\":\n                return this.driver.mssql.DateTime\n            case \"datetime2\":\n                return this.driver.mssql.DateTime2(...parameter.params)\n            case \"datetimeoffset\":\n                return this.driver.mssql.DateTimeOffset(...parameter.params)\n            case \"smalldatetime\":\n                return this.driver.mssql.SmallDateTime\n            case \"uniqueidentifier\":\n                return this.driver.mssql.UniqueIdentifier\n            case \"variant\":\n                return this.driver.mssql.Variant\n            case \"binary\":\n                return this.driver.mssql.Binary\n            case \"varbinary\":\n                return this.driver.mssql.VarBinary(...parameter.params)\n            case \"image\":\n                return this.driver.mssql.Image\n            case \"udt\":\n                return this.driver.mssql.UDT\n            case \"rowversion\":\n                return this.driver.mssql.RowVersion\n        }\n    }\n\n    /**\n     * Converts string literal of isolation level to enum.\n     * The underlying mssql driver requires an enum for the isolation level.\n     */\n    convertIsolationLevel(isolation: IsolationLevel) {\n        const ISOLATION_LEVEL = this.driver.mssql.ISOLATION_LEVEL\n        switch (isolation) {\n            case \"READ UNCOMMITTED\":\n                return ISOLATION_LEVEL.READ_UNCOMMITTED\n            case \"REPEATABLE READ\":\n                return ISOLATION_LEVEL.REPEATABLE_READ\n            case \"SERIALIZABLE\":\n                return ISOLATION_LEVEL.SERIALIZABLE\n\n            case \"READ COMMITTED\":\n            default:\n                return ISOLATION_LEVEL.READ_COMMITTED\n        }\n    }\n\n    /**\n     * Change table comment.\n     */\n    changeTableComment(\n        tableOrName: Table | string,\n        comment?: string,\n    ): Promise<void> {\n        throw new TypeORMError(\n            `sqlserver driver does not support change table comment.`,\n        )\n    }\n}\n"], "sourceRoot": "../.."}