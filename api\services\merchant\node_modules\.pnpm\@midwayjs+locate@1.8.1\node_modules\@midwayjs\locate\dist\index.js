"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Locator = exports.ProjectType = void 0;
const fs_1 = require("fs");
const globby = require("globby");
const path_1 = require("path");
const util_1 = require("./util");
const whiltelist_1 = require("./whiltelist");
var ProjectType;
(function (ProjectType) {
    ProjectType["UNKNOWN"] = "unknown";
    ProjectType["MIDWAY"] = "midway";
    ProjectType["MIDWAY_FRONT_MONOREPO"] = "midway_front_monorepo";
    ProjectType["MIDWAY_FRONT_integration"] = "midway_front_integration";
    ProjectType["MIDWAY_FAAS"] = "midway_faas";
    ProjectType["MIDWAY_FAAS_FRONT_MONOREPO"] = "midway_faas_front_monorepo";
    ProjectType["MIDWAY_FAAS_FRONT_integration"] = "midway_faas_front_integration";
})(ProjectType = exports.ProjectType || (exports.ProjectType = {}));
const globOptions = {
    followSymbolicLinks: false,
    ignore: [
        '**/node_modules/**',
        '**/test/**',
        '**/run/**',
        '**/public/**',
        '**/build/**',
        '**/dist/**',
        '**/.serverless/**',
        '**/.faas_debug_tmp/**',
        'midway.config.ts',
        'vite.config.ts',
    ],
};
class Locator {
    constructor(cwd) {
        this.integrationProject = false;
        this.projectType = ProjectType.UNKNOWN;
        this.isMidwayProject = false;
        this.isMidwayFaaSProject = false;
        this.cwd = cwd || this.analyzeCWD();
    }
    async run(options = {}) {
        await this.formatOptions(options);
        this.tsCodeRoot = options.tsCodeRoot;
        this.tsBuildRoot = options.tsBuildRoot;
        await this.analyzeRoot();
        await this.analyzeTSCodeRoot();
        await this.analyzeTSBuildRoot();
        await this.analyzeIntegrationProject();
        await this.analyzeUsingDependencies();
        await this.analyzeUsingDependenciesVersion();
        return {
            cwd: this.cwd,
            midwayRoot: this.root,
            tsCodeRoot: this.tsCodeRoot,
            tsConfigFilePath: this.tsConfigFilePath,
            tsBuildRoot: this.tsBuildRoot,
            integrationProject: this.integrationProject,
            projectType: this.projectType,
            usingDependencies: this.usingDependencies,
            usingDependenciesVersion: this.usingDependenciesVersion,
        };
    }
    analyzeCWD() {
        return process.cwd();
    }
    async formatOptions(options) {
        const json = await util_1.safeReadJSON(path_1.join(this.cwd, 'package.json'));
        const integrationOptions = util_1.safeGetProperty(json, 'midway-integration') || {};
        if (!options.tsCodeRoot) {
            options.tsCodeRoot = integrationOptions.tsCodeRoot;
        }
        if (!options.tsBuildRoot) {
            options.tsBuildRoot = integrationOptions.tsBuildRoot;
        }
    }
    /**
     * 分析 midway 系列项目根目录
     */
    async analyzeRoot() {
        const tsConfig = await util_1.safeReadJSON(path_1.join(this.cwd, 'tsconfig.json'));
        if (tsConfig === null || tsConfig === void 0 ? void 0 : tsConfig.exclude) {
            globOptions.ignore.push(...tsConfig.exclude);
        }
        const paths = await globby(['**/package.json'], {
            ...globOptions,
            cwd: this.cwd,
            deep: 2,
        });
        // find midway root
        for (let pkgPath of paths) {
            const json = await util_1.safeReadJSON(path_1.join(this.cwd, pkgPath));
            let result = util_1.propertyExists(json, [
                'dependencies.midway',
                'dependencies.@ali/midway',
            ]);
            if (result) {
                this.isMidwayProject = true;
                this.root = path_1.dirname(path_1.join(this.cwd, pkgPath));
                break;
            }
            result = util_1.propertyExists(json, [
                'dependencies.@midwayjs/faas',
                'dependencies.@ali/midway-faas',
            ]);
            if (result) {
                this.isMidwayFaaSProject = true;
                this.root = path_1.dirname(path_1.join(this.cwd, pkgPath));
                break;
            }
        }
    }
    /**
     * 分析 ts 代码的根目录，比如 src，或者其他
     */
    async analyzeTSCodeRoot() {
        if (!this.root) {
            return;
        }
        if (this.tsCodeRoot) {
            this.tsCodeRoot = this.formatAbsolutePath(this.tsCodeRoot);
            return;
        }
        const paths = await globby(['**/*.ts', '!**/*.d.ts'], {
            ...globOptions,
            cwd: this.root,
        });
        const common = util_1.findCommonDir(paths);
        this.tsCodeRoot = path_1.join(this.root, common);
    }
    /**
     * 分析构建后的根目录
     */
    async analyzeTSBuildRoot() {
        if (!this.root || !this.tsCodeRoot) {
            return;
        }
        if (this.tsBuildRoot) {
            this.tsBuildRoot = this.formatAbsolutePath(this.tsBuildRoot);
        }
        this.tsConfigFilePath = await util_1.findFile([
            path_1.join(this.tsCodeRoot, 'tsconfig.json'),
            path_1.join(this.root, 'tsconfig.json'),
            path_1.join(this.cwd, 'tsconfig.json'),
        ]);
        if (this.tsConfigFilePath && !this.tsBuildRoot) {
            const tsConfig = await util_1.safeReadJSON(this.tsConfigFilePath);
            const distDir = util_1.safeGetProperty(tsConfig, 'compilerOptions.outDir');
            if (distDir) {
                this.tsBuildRoot = path_1.join(path_1.dirname(this.tsConfigFilePath), distDir);
            }
        }
    }
    /**
     * 分析用到的依赖
     */
    async analyzeUsingDependencies() {
        if (!this.root || !this.tsCodeRoot)
            return;
        if (this.integrationProject) {
            // 一体化项目，需要分析函数用到的依赖
            const dependencies = new Set();
            const paths = await globby(['**/*.ts', '**/*.js', '!**/*.d.ts'], {
                ...globOptions,
                cwd: this.tsCodeRoot,
            });
            for (const p of paths) {
                try {
                    const file = path_1.join(this.tsCodeRoot, p);
                    const ext = path_1.extname(file);
                    const isJSX = ext === '.tsx' || ext === '.jsx' || ext === '.js';
                    const result = util_1.findDependenciesByAST(fs_1.readFileSync(file, 'utf-8'), isJSX);
                    result.forEach((module) => {
                        util_1.filterModule(module, dependencies);
                    });
                }
                catch (err) {
                    console.error(`"${p}" find dependencies and parse error, err="${err.message}"`);
                }
            }
            this.usingDependencies = Array.from(dependencies.values());
            const json = await util_1.safeReadJSON(path_1.join(this.root, 'package.json'));
            const pkgDeps = json['dependencies'] || [];
            this.usingDependencies = whiltelist_1.includeDependencies(this.usingDependencies, pkgDeps);
        }
        else {
            const json = await util_1.safeReadJSON(path_1.join(this.root, 'package.json'));
            const dependencies = json['dependencies'] || [];
            this.usingDependencies = Object.keys(dependencies);
        }
    }
    async analyzeUsingDependenciesVersion() {
        if (!this.root || !this.tsCodeRoot || !this.usingDependencies)
            return;
        const json = await util_1.safeReadJSON(path_1.join(this.root, 'package.json'));
        const dependencies = json['dependencies'] || [];
        const dependenciesVersion = {
            valid: {},
            unValid: [],
        };
        this.usingDependencies.forEach((depName) => {
            if (dependencies[depName]) {
                dependenciesVersion.valid[depName] = dependencies[depName];
            }
            else {
                dependenciesVersion.unValid.push(depName);
            }
        });
        this.usingDependenciesVersion = dependenciesVersion;
    }
    async analyzeIntegrationProject() {
        if (!this.root)
            return;
        // 当前目录不等于 midway 根目录，对等视图
        if (this.cwd !== this.root) {
            if (this.isMidwayProject) {
                this.projectType = ProjectType.MIDWAY_FRONT_MONOREPO;
            }
            else {
                this.projectType = ProjectType.MIDWAY_FAAS_FRONT_MONOREPO;
            }
            return;
        }
        // 全 ts 版本，前后端代码可能在一起，前端视图的情况
        // rax/ice 等
        let isIntegration = [
            'src/pages',
            'src/index.tsx',
            'src/index.scss',
            'src/index.less',
        ].find((name) => {
            return fs_1.existsSync(path_1.join(this.root, name));
        });
        if (!isIntegration && fs_1.existsSync(path_1.join(this.root, 'midway.config.ts'))) {
            const pkgJson = await util_1.safeReadJSON(path_1.join(this.root, 'package.json'));
            isIntegration = ['react', 'vue', 'rax'].find(depName => {
                var _a, _b;
                return ((_a = pkgJson === null || pkgJson === void 0 ? void 0 : pkgJson.dependencies) === null || _a === void 0 ? void 0 : _a[depName]) || ((_b = pkgJson === null || pkgJson === void 0 ? void 0 : pkgJson.devDependencies) === null || _b === void 0 ? void 0 : _b[depName]);
            });
        }
        if (isIntegration) {
            this.integrationProject = true;
            if (this.isMidwayProject) {
                this.projectType = ProjectType.MIDWAY_FRONT_integration;
            }
            else {
                this.projectType = ProjectType.MIDWAY_FAAS_FRONT_integration;
            }
            return;
        }
        // 剩下可能就是纯应用
        if (this.isMidwayProject) {
            this.projectType = ProjectType.MIDWAY;
        }
        else if (this.isMidwayFaaSProject) {
            this.projectType = ProjectType.MIDWAY_FAAS;
        }
    }
    formatAbsolutePath(p) {
        if (!path_1.isAbsolute(p)) {
            return path_1.join(this.root, p);
        }
        return p;
    }
}
exports.Locator = Locator;
//# sourceMappingURL=data:application/json;base64,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