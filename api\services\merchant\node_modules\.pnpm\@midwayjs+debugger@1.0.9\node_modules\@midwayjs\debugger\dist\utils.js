"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.vscodeSupport = exports.checkPort = exports.getType = exports.getFun = exports.waitDebug = exports.getWssUrl = exports.getDebugPath = exports.getRandomId = exports.getData = exports.onMessage = exports.sendData = void 0;
const fs_1 = require("fs");
const os_1 = require("os");
const path_1 = require("path");
const net_1 = require("net");
const child_process_1 = require("child_process");
// 进程间传递数据
exports.sendData = (proc, result) => {
    const id = result && result.id || exports.getRandomId();
    const tmpData = path_1.join(os_1.tmpdir(), 'data' + id);
    fs_1.writeFileSync(tmpData, JSON.stringify(result));
    proc.send({ type: 'bigData', id, exitCode: result.exitCode });
};
// 处理消息
exports.onMessage = (proc, cb) => {
    proc.on('message', async (msg) => {
        if (msg && msg.type === 'bigData') {
            msg = exports.getData(msg.id);
        }
        cb(msg);
    });
};
// 进程间获取大数据
exports.getData = (id) => {
    const tmpData = path_1.join(os_1.tmpdir(), 'data' + id);
    return JSON.parse(fs_1.readFileSync(tmpData).toString());
};
// 获取随机Id
exports.getRandomId = (key) => {
    return Date.now() + Math.random() + (key || '');
};
exports.getDebugPath = () => {
    if (require.extensions['.ts']) {
        return {
            path: path_1.resolve(__dirname, './debug.ts'),
            extensions: ['-r', 'ts-node/register']
        };
    }
    return { path: path_1.resolve(__dirname, './debug.js') };
};
function getWssUrl(port, type, count) {
    return new Promise((resolve, reject) => {
        count = count || 0;
        if (count > 100) {
            return reject('timeout');
        }
        setTimeout(() => {
            const fetch = require('node-fetch');
            fetch('http://127.0.0.1:' + port + '/json/list')
                .then(res => res.json())
                .then(debugInfo => {
                const url = debugInfo[0][type || 'webSocketDebuggerUrl'] || '';
                const ret = url.replace('js_app.html?experiments=true&', 'inspector.html?');
                resolve(ret);
            })
                .catch(() => {
                getWssUrl(port, type, count + 1).then(resolve).catch(reject);
            });
        }, 100);
    });
}
exports.getWssUrl = getWssUrl;
function debugWs(addr) {
    return new Promise(resolve => {
        const WebSocket = require('ws');
        const ws = new WebSocket(addr);
        let currentId = 0;
        const cbMap = {};
        ws.on('open', () => {
            ws.on('message', message => {
                try {
                    message = JSON.parse(message);
                }
                catch (e) { }
                if (message.params) {
                    const id = message.params.scriptId;
                    if (id) {
                        if (id > currentId) {
                            currentId = id - 0;
                        }
                        if (cbMap[id]) {
                            cbMap[id](message.params);
                        }
                    }
                }
            });
            const send = (method, params) => {
                return new Promise(resolve2 => {
                    const curId = currentId + 1;
                    currentId = curId;
                    cbMap[curId] = data => {
                        resolve2(data);
                    };
                    const param = { id: curId, method };
                    if (params) {
                        param.params = params;
                    }
                    ws.send(JSON.stringify(param));
                });
            };
            send('Profiler.enable');
            send('Runtime.enable');
            send('Debugger.enable', { maxScriptsCacheSize: 10000000 });
            send('Debugger.setBlackboxPatterns', { patterns: ['internal'] });
            resolve(send);
        });
    });
}
async function waitDebug(port) {
    const wssUrl = await getWssUrl(port);
    return debugWs(wssUrl);
}
exports.waitDebug = waitDebug;
exports.getFun = (options) => {
    let fun = require(options.file);
    if (options.export) {
        fun = fun[options.export];
    }
    return (...args) => {
        return Promise.resolve(true).then(() => {
            return fun(...args);
        });
    };
};
exports.getType = (data) => {
    return ({}).toString.call(data).slice(8, -1).toLowerCase();
};
exports.checkPort = async (port) => {
    return new Promise(resolve => {
        const plat = os_1.platform();
        if (plat != 'win32') {
            try {
                const portUse = child_process_1.execSync(`lsof -i:${port}`).toString().replace(/\n$/, '').split('\n');
                if (portUse.length <= 1) {
                    return resolve(false);
                }
                portUse.shift();
                const findUse = portUse.find(proc => {
                    const procList = proc.split(/\s+/);
                    const last = procList.pop();
                    if (last === '(LISTEN)') {
                        return true;
                    }
                });
                if (findUse) {
                    return resolve(true);
                }
            }
            catch (_a) { }
        }
        const server = net_1.createServer(socket => {
            socket.write('check port\r\n');
            socket.pipe(socket);
        });
        setTimeout(() => {
            server.listen(port, '127.0.0.1');
        }, 100);
        server.on('error', () => {
            resolve(true);
        });
        server.on('listening', () => {
            server.close();
            resolve(false);
        });
    });
};
exports.vscodeSupport = (options) => {
    const isInVscode = process.env.TERM_PROGRAM === 'vscode';
    if (!isInVscode) {
        return;
    }
    let vscodeVersion = [];
    try {
        vscodeVersion = child_process_1.execSync('code -v').toString().split('\n')[0].split('.');
    }
    catch (_a) { }
    if (!vscodeVersion || !Array.isArray(vscodeVersion) || vscodeVersion.length < 3) {
        return;
    }
    const versionCount = vscodeVersion[0] * 100 + vscodeVersion[1];
    const cwd = (options === null || options === void 0 ? void 0 : options.cwd) || process.cwd();
    const vscodeSettingDir = path_1.join(cwd, '.vscode');
    if (!fs_1.existsSync(vscodeSettingDir)) {
        fs_1.mkdirSync(vscodeSettingDir);
    }
    let vscodeSettingFile = path_1.join(vscodeSettingDir, 'settings.json');
    let vscodeSettingJson = {};
    try {
        vscodeSettingJson = JSON.parse(fs_1.readFileSync(vscodeSettingFile).toString());
    }
    catch (_b) { }
    // 自动打开 autoAttach
    vscodeSettingJson['debug.node.autoAttach'] = 'on';
    // version 1.49.0 + 需要设置 usePreviewAutoAttach
    if (versionCount >= 149) {
        vscodeSettingJson['debug.javascript.usePreviewAutoAttach'] = false;
    }
    fs_1.writeFileSync(vscodeSettingFile, JSON.stringify(vscodeSettingJson, null, 2));
};
//# sourceMappingURL=data:application/json;base64,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