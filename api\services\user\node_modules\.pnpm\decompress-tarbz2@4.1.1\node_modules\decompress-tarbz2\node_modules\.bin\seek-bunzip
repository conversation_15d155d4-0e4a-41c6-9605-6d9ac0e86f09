#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules/seek-bzip/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules/seek-bzip/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules/seek-bzip/bin/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules/seek-bzip/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/seek-bzip@1.0.6/node_modules:/mnt/d/jiangrenjie/api/services/user/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../seek-bzip@1.0.6/node_modules/seek-bzip/bin/seek-bunzip" "$@"
else
  exec node  "$basedir/../../../../../seek-bzip@1.0.6/node_modules/seek-bzip/bin/seek-bunzip" "$@"
fi
