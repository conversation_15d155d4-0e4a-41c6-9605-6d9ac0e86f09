"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayPipelineService = exports.PipelineContext = void 0;
const decorator_1 = require("../decorator");
////////////// implements ///////////////////////
class PipelineContext {
    constructor(args) {
        this.info = { current: null, currentName: null };
        this.data = new Map();
        this.args = args;
    }
    get(key) {
        return this.data.get(key);
    }
    set(key, val) {
        this.data.set(key, val);
    }
    keys() {
        const keys = [];
        const iter = this.data.keys();
        for (const k of iter) {
            keys.push(k);
        }
        return keys;
    }
}
exports.PipelineContext = PipelineContext;
class MidwayPipelineService {
    // 默认的 valves (@Pipeline(['test1', 'test2']))
    constructor(applicationContext, valves) {
        this.applicationContext = applicationContext;
        this.valves = valves;
    }
    /**
     * 并行执行，使用 Promise.all
     * @param opts 执行参数
     */
    async parallel(opts) {
        const valves = this.prepareParallelValves(opts);
        const res = await Promise.all(valves);
        return this.packResult(res, false);
    }
    /**
     * 并行执行，最终 result 为数组
     * @param opts 执行参数
     */
    async concat(opts) {
        const valves = this.prepareParallelValves(opts);
        const res = await Promise.all(valves);
        return this.packResult(res, true);
    }
    /**
     * 串行执行，使用 foreach await
     * @param opts 执行参数
     */
    async series(opts) {
        const valves = this.mergeValves(opts.valves);
        const ctx = new PipelineContext(opts.args);
        const result = { success: true, result: null };
        const data = {};
        const info = {
            prevValue: null,
            current: null,
            currentName: null,
            prev: null,
            prevName: null,
            next: null,
            nextName: null,
        };
        let nextIdx = 1;
        for (const v of valves) {
            info.prev = info.current;
            info.prevName = getName(info.prev);
            info.current = v;
            info.currentName = getName(info.current);
            if (nextIdx < valves.length) {
                info.next = valves[nextIdx];
                info.nextName = getName(info.next);
            }
            else {
                info.next = undefined;
                info.nextName = undefined;
            }
            nextIdx += 1;
            ctx.info = info;
            try {
                const inst = await this.applicationContext.getAsync(v);
                const tmpValue = await inst.invoke(ctx);
                let key = v;
                if (inst.alias) {
                    key = inst.alias;
                }
                data[key] = tmpValue;
                info.prevValue = tmpValue;
            }
            catch (e) {
                result.success = false;
                result.error = {
                    valveName: typeof v === 'string' ? v : v.name,
                    message: e.message,
                    error: e,
                };
                return result;
            }
        }
        result.result = data;
        return result;
    }
    /**
     * 串行执行，使用 foreach await，最终 result 为数组
     * @param opts 执行参数
     */
    async concatSeries(opts) {
        const valves = this.mergeValves(opts.valves);
        const ctx = new PipelineContext(opts.args);
        const result = { success: true, result: null };
        const data = [];
        const info = {
            prevValue: null,
            current: null,
            currentName: null,
            prev: null,
            prevName: null,
            next: null,
            nextName: null,
        };
        let nextIdx = 1;
        for (const v of valves) {
            info.prev = info.current;
            info.prevName = getName(info.prev);
            info.current = v;
            info.currentName = getName(info.current);
            if (nextIdx < valves.length) {
                info.next = valves[nextIdx];
                info.nextName = getName(info.next);
            }
            else {
                info.next = undefined;
                info.nextName = undefined;
            }
            nextIdx += 1;
            ctx.info = info;
            try {
                const inst = await this.applicationContext.getAsync(v);
                const tmpValue = await inst.invoke(ctx);
                data.push(tmpValue);
                info.prevValue = tmpValue;
            }
            catch (e) {
                result.success = false;
                result.error = {
                    valveName: typeof v === 'string' ? v : v.name,
                    message: e.message,
                    error: e,
                };
                return result;
            }
        }
        result.result = data;
        return result;
    }
    /**
     * 串行执行，但是会把前者执行结果当成入参，传入到下一个执行中去，最后一个执行的 valve 结果会被返回
     * @param opts 执行参数
     */
    async waterfall(opts) {
        const result = await this.concatSeries(opts);
        if (result.success) {
            const data = result.result;
            result.result = data[data.length - 1];
        }
        return result;
    }
    mergeValves(valves) {
        let items = [];
        if (this.valves && this.valves.length > 0) {
            items = this.valves;
        }
        let newItems = [];
        if (valves) {
            for (const v of valves) {
                if (items.includes(v)) {
                    newItems.push(v);
                }
            }
        }
        else {
            newItems = items;
        }
        return newItems;
    }
    prepareParallelValves(opts) {
        const valves = this.mergeValves(opts.valves);
        const ctx = new PipelineContext(opts.args);
        return valves.map(async (v) => {
            const rt = { valveName: v, dataKey: v, data: null };
            try {
                const inst = await this.applicationContext.getAsync(v);
                if (inst.alias) {
                    rt.dataKey = inst.alias;
                }
                rt.data = await inst.invoke(ctx);
            }
            catch (e) {
                rt.error = e;
            }
            return rt;
        });
    }
    packResult(res, resultIsArray = false) {
        const result = { success: true, result: null };
        let data;
        if (resultIsArray) {
            data = [];
        }
        else {
            data = {};
        }
        for (const r of res) {
            if (r.error) {
                result.success = false;
                result.error = {
                    valveName: typeof r.valveName === 'string' ? r.valveName : r.valveName.name,
                    message: r.error.message,
                    error: r.error,
                };
                return result;
            }
            else {
                if (resultIsArray) {
                    data.push(r.data);
                }
                else {
                    data[r.dataKey] = r.data;
                }
            }
        }
        result.result = data;
        return result;
    }
}
exports.MidwayPipelineService = MidwayPipelineService;
function getName(target) {
    if (target) {
        if (typeof target === 'string') {
            return target;
        }
        else {
            return (0, decorator_1.getProviderName)(target);
        }
    }
    return null;
}
//# sourceMappingURL=pipelineService.js.map