"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.check = check;
const fs_1 = require("fs");
const uuid_1 = require("uuid");
const path_1 = require("path");
/**
 * 检查并替换单个配置文件
 */
async function checkAndReplaceFile(config) {
    const filePath = (0, path_1.join)(process.cwd(), config.path);
    if (!(0, fs_1.existsSync)(filePath)) {
        return;
    }
    let content = (0, fs_1.readFileSync)(filePath, 'utf-8');
    if (content.includes(config.pattern)) {
        console.log(`${config.path}，key is default, auto replace it`);
        content = content.replace(config.pattern, (0, uuid_1.v4)());
        (0, fs_1.writeFileSync)(filePath, content, 'utf-8');
    }
}
/**
 * 检查配置文件
 */
async function check() {
    const configs = [
        {
            path: 'src/config/config.default.ts',
            pattern: 'cool-admin-keys-xxxxxx',
        },
        {
            path: 'src/modules/base/config.ts',
            pattern: 'cool-admin-xxxxxx',
        },
        {
            path: 'src/modules/user/config.ts',
            pattern: 'cool-app-xxxxx',
        },
    ];
    await Promise.all(configs.map(config => checkAndReplaceFile(config)));
}
