{"version": 3, "sources": ["../../src/schema-builder/RdbmsSchemaBuilder.ts"], "names": [], "mappings": ";;;AAAA,yCAAqC;AACrC,qDAAiD;AACjD,6DAAyD;AACzD,mDAA+C;AAO/C,kDAA8C;AAE9C,qDAAiD;AACjD,mDAA+C;AAC/C,2DAAuD;AACvD,sCAAkC;AAClC,gDAA4C;AAC5C,uDAAmD;AAGnD;;;;;;;;;;;;;GAaG;AACH,MAAa,kBAAkB;IAY3B,4EAA4E;IAC5E,cAAc;IACd,4EAA4E;IAE5E,YAAsB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;QAfnC,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;IAeV,CAAC;IAEhD,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,KAAK;QACP,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QAEtD,2DAA2D;QAC3D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAA;QACtD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAA;QAElD,yGAAyG;QACzG,8FAA8F;QAC9F,4FAA4F;QAC5F,uCAAuC;QACvC,MAAM,mBAAmB,GACrB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa,CAAC;YACxD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;YACpD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,yBAAyB,KAAK,MAAM,CAAA;QAEhE,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;QAExC,IAAI,mBAAmB,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAA;QAC7C,CAAC;QAED,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC3D,2CAA2C;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC3D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC9B,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC9D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC9B,CAAA;YAED,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAE1C,MAAM,IAAI,CAAC,wCAAwC,EAAE,CAAA;YAErD,iEAAiE;YACjE,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAChC,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAC9C,IAAI,CAAC,WAAW,CACnB,CAAA;YAEL,IAAI,mBAAmB,EAAE,CAAC;gBACtB,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAA;YAC9C,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,IAAI,CAAC;gBACD,2DAA2D;gBAC3D,IAAI,mBAAmB,EAAE,CAAC;oBACtB,MAAM,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAChD,CAAC;YACL,CAAC;YAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC1B,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,MAAM,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE,CAAA;YAEvC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAA;QACpC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,8BAA8B,CAChC,WAAwB;QAExB,IACI,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC;YACzC,IAAI,CAAC,mBAAmB,EAAE,EAC5B,CAAC;YACC,MAAM,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAA;QACtD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,GAAG;QACL,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE,CAAA;QACtD,IAAI,CAAC;YACD,2CAA2C;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC3D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC9B,CAAA;YACD,MAAM,SAAS,GAAG,IAAI,CAAC,yBAAyB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE,CAC9D,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC9B,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,CAAA;YAC5C,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YAE1C,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE,CAAA;YAClC,MAAM,IAAI,CAAC,wCAAwC,EAAE,CAAA;YAErD,iEAAiE;YACjE,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBAChC,iCAAiC;gBACjC,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,WAAW,CAC9C,IAAI,CAAC,WAAW,CACnB,CAAA;YAEL,OAAO,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAA;QAC1C,CAAC;gBAAS,CAAC;YACP,mFAAmF;YACnF,2FAA2F;YAC3F,sFAAsF;YACtF,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAA;YACnC,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAA;QACpC,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACH,IAAc,qBAAqB;QAC/B,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,MAAM,CACzC,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,WAAW;YACpB,QAAQ,CAAC,SAAS,KAAK,cAAc;YACrC,QAAQ,CAAC,SAAS,KAAK,MAAM,CACpC,CAAA;IACL,CAAC;IAED;;OAEG;IACH,IAAc,yBAAyB;QACnC,OAAO,CACH,IAAI,CAAC,UAAU,CAAC,eAAe;aAC1B,MAAM,CACH,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,SAAS,KAAK,MAAM,IAAI,QAAQ,CAAC,WAAW,CAC5D;YACD,+CAA+C;aAC9C,IAAI,CAAC,qBAAS,CAAC,eAAe,CAAC,CACvC,CAAA;IACL,CAAC;IAED;;OAEG;IACO,mBAAmB;QACzB,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,cAAc,EAAE,EAAE;YAC3D,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAA;QACxE,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,wCAAwC;QACpD,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QACzB,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC/B,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;QAC3B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;QAC1B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9B,MAAM,IAAI,CAAC,8BAA8B,EAAE,CAAA;QAC3C,6BAA6B;QAC7B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;QAC1B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC/B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;QAC5B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC/B,MAAM,IAAI,CAAC,aAAa,EAAE,CAAA;QAC1B,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9B,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAC/B,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;QAC7B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAA;QAC5B,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChC,MAAM,IAAI,CAAC,gCAAgC,EAAE,CAAA;QAC7C,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC9B,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACxB,MAAM,IAAI,CAAC,oBAAoB,EAAE,CAAA;IACrC,CAAC;IAEO,YAAY,CAChB,MAAgE;QAEhE,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAE5D,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACxC,MAAM,CAAC,SAAS,EAChB,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EACnC,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,eAAe,CAC1C,CAAA;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,kBAAkB;QAC9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,wFAAwF;YACxF,MAAM,sBAAsB,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CACnD,CAAC,eAAe,EAAE,EAAE;gBAChB,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CACxC,CAAC,kBAAkB,EAAE,EAAE,CACnB,eAAe,CAAC,IAAI,KAAK,kBAAkB,CAAC,IAAI;oBAChD,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;wBAC9B,IAAI,CAAC,YAAY,CACb,kBAAkB,CAAC,wBAAwB,CAC9C,CACZ,CAAA;gBACD,OAAO,CACH,CAAC,UAAU;oBACX,CAAC,UAAU,CAAC,QAAQ;wBAChB,UAAU,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,CAAC;oBACrD,CAAC,UAAU,CAAC,QAAQ;wBAChB,UAAU,CAAC,QAAQ,KAAK,eAAe,CAAC,QAAQ,CAAC,CACxD,CAAA;YACL,CAAC,CACJ,CAAA;YACD,IAAI,sBAAsB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,gCACI,KAAK,CAAC,IACV,KAAK,sBAAsB;iBACtB,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;iBACxC,IAAI,CAAC,IAAI,CAAC,EAAE,CACpB,CAAA;YAED,sCAAsC;YACtC,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAClC,KAAK,EACL,sBAAsB,CACzB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,YAAY;QACxB,uDAAuD;QACvD,2HAA2H;QAC3H,IAAI;IACR,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,aAAa;QACzB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM;gBAAE,SAAQ;YAE9D,MAAM,sBAAsB,GAAG,QAAQ,CAAC,OAAO;iBAC1C,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC;iBACnC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE;gBACf,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;oBACvC,OAAO,CACH,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,YAAY;wBACxC,WAAW,CAAC,IAAI;4BACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;wBAChD,WAAW,CAAC,UAAU,KAAK,MAAM,CAAC,UAAU;wBAC5C,WAAW,CAAC,QAAQ;4BAChB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC,CACvD,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEN,IACI,sBAAsB,CAAC,MAAM,KAAK,CAAC;gBACnC,sBAAsB,CAAC,MAAM,GAAG,CAAC;gBAEjC,SAAQ;YAEZ,MAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC7D,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;oBACrC,OAAO,CACH,CAAC,MAAM,CAAC,iBAAiB;wBACzB,MAAM,CAAC,YAAY,KAAK,WAAW,CAAC,IAAI;wBACxC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC;4BACxC,WAAW,CAAC,IAAI;wBACpB,MAAM,CAAC,UAAU,KAAK,WAAW,CAAC,UAAU;wBAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;4BAC5C,WAAW,CAAC,QAAQ,CAC3B,CAAA;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YAEF,IACI,mBAAmB,CAAC,MAAM,KAAK,CAAC;gBAChC,mBAAmB,CAAC,MAAM,GAAG,CAAC;gBAE9B,SAAQ;YAEZ,MAAM,aAAa,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAA;YACpD,aAAa,CAAC,IAAI,GAAG,sBAAsB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAA;YAE3D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,oBAAoB,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS,KAAK,CAAC,IAAI,SAAS,aAAa,CAAC,IAAI,GAAG,CACnG,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,YAAY,CAC/B,KAAK,EACL,mBAAmB,CAAC,CAAC,CAAC,EACtB,aAAa,CAChB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,cAAc;QAC1B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO;iBAC5B,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;gBACnB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAC5C,CAAA;gBACD,IAAI,aAAa,EAAE,CAAC;oBAChB,IAAI,aAAa,CAAC,WAAW,KAAK,KAAK;wBAAE,OAAO,KAAK,CAAA;oBAErD,IAAI,aAAa,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ;wBAC9C,OAAO,IAAI,CAAA;oBAEf,IAAI,aAAa,CAAC,SAAS,KAAK,UAAU,CAAC,SAAS;wBAChD,OAAO,IAAI,CAAA;oBAEf,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,EAAE;wBACtD,aAAa,CAAC,UAAU,KAAK,UAAU,CAAC,UAAU;wBAElD,OAAO,IAAI,CAAA;oBAEf,IACI,aAAa,CAAC,OAAO,CAAC,MAAM;wBAC5B,UAAU,CAAC,WAAW,CAAC,MAAM;wBAE7B,OAAO,IAAI,CAAA;oBAEf,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAC/B,CAAC,MAAM,EAAE,EAAE,CACP,UAAU,CAAC,WAAW,CAAC,OAAO,CAC1B,MAAM,CAAC,YAAY,CACtB,KAAK,CAAC,CAAC,CACf,CAAA;gBACL,CAAC;gBAED,OAAO,IAAI,CAAA;YACf,CAAC,CAAC;iBACD,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;gBACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,uBAAuB,UAAU,CAAC,IAAI,gBAAgB,KAAK,CAAC,IAAI,EAAE,CACrE,CAAA;gBACD,MAAM,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YACvD,CAAC,CAAC,CAAA;YAEN,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;QAClC,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YAC9C,MAAM,mBAAmB,GAExB,IAAI,CAAC,WAAW,CAAA;YACjB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACpD,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAC1C,CAAC,IAAI,EAAE,EAAE,CACL,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC9D,CAAA;gBACD,IAAI,CAAC,IAAI;oBAAE,SAAQ;gBAEnB,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO;qBAC3B,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;oBACnB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CACvC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAC5C,CAAA;oBACD,IAAI,aAAa,EAAE,CAAC;wBAChB,IAAI,aAAa,CAAC,WAAW,KAAK,KAAK;4BACnC,OAAO,KAAK,CAAA;wBAEhB,IAAI,aAAa,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ;4BAC9C,OAAO,IAAI,CAAA;wBAEf,IACI,aAAa,CAAC,SAAS,KAAK,UAAU,CAAC,SAAS;4BAEhD,OAAO,IAAI,CAAA;wBAEf,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,6BAA6B,EAAE;4BACtD,aAAa,CAAC,UAAU;gCACpB,UAAU,CAAC,UAAU;4BAEzB,OAAO,IAAI,CAAA;wBAEf,IACI,aAAa,CAAC,OAAO,CAAC,MAAM;4BAC5B,UAAU,CAAC,WAAW,CAAC,MAAM;4BAE7B,OAAO,IAAI,CAAA;wBAEf,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,KAAK,CAC/B,CAAC,MAAM,EAAE,EAAE,CACP,UAAU,CAAC,WAAW,CAAC,OAAO,CAC1B,MAAM,CAAC,YAAY,CACtB,KAAK,CAAC,CAAC,CACf,CAAA;oBACL,CAAC;oBAED,OAAO,IAAI,CAAA;gBACf,CAAC,CAAC;qBACD,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,EAAE;oBACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,uBAAuB,UAAU,CAAC,IAAI,eAAe,IAAI,CAAC,IAAI,EAAE,CACnE,CAAA;oBACD,MAAM,mBAAmB,CAAC,aAAa,CACnC,IAAI,EACJ,UAAU,CACb,CAAA;gBACL,CAAC,CAAC,CAAA;gBAEN,MAAM,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;YAClC,CAAC;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,aAAa;QACzB,2CAA2C;QAC3C,IACI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;YAEtD,OAAM;QAEV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjD,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CACxB,CAAC,aAAa,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,CAC5D,CAAA;YACL,CAAC,CAAC,CAAA;YAEF,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,kCAAkC,SAAS;iBACtC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC;iBACjC,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAC/C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACjE,CAAC;IACL,CAAC;IAES,KAAK,CAAC,8BAA8B;QAC1C,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,gBAAgB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC1D,OAAO,CACH,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;oBAClC,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAClB,CAAC,cAAc,EAAE,EAAE,CACf,cAAc,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,CAC/C,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAE3C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,mCAAmC,gBAAgB;iBAC9C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAC/C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,CACxC,KAAK,EACL,gBAAgB,CACnB,CAAA;QACL,CAAC;IACL,CAAC;IAES,KAAK,CAAC,iBAAiB;QAC7B,iDAAiD;QACjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC;YAAE,OAAM;QAEjE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,EAAE;gBAC7D,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAC5B,CAAC,iBAAiB,EAAE,EAAE,CAClB,iBAAiB,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CACrD,CAAA;YACL,CAAC,CAAC,CAAA;YAEF,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAExC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,sCAAsC,aAAa;iBAC9C,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,CAAC,IAAI,GAAG,CAC/C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,wBAAwB,CAC3C,KAAK,EACL,aAAa,CAChB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,kBAAkB;QAC9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,IACI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EACpD,CAAC;gBACC,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAA;gBACnC,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;YAChE,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,KAAK,CAAC,eAAe;QAC3B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,oCAAoC;YACpC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CACjD,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,UAAU;gBAAE,SAAQ;YAExB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,yBAAyB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CACzD,CAAA;YAED,iDAAiD;YACjD,MAAM,KAAK,GAAG,aAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YAC5D,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;YACvD,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC7C,CAAC;IACL,CAAC;IAES,KAAK,CAAC,WAAW;QACvB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACpD,mCAAmC;YACnC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACzD,MAAM,cAAc,GAChB,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;oBAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;oBACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;gBACrD,MAAM,kBAAkB,GACpB,OAAO,QAAQ,CAAC,UAAU,KAAK,QAAQ;oBACnC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE;oBAC5B,CAAC,CAAC,QAAQ,CAAC,UAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;gBAC1D,OAAO,CACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACvD,cAAc,KAAK,kBAAkB,CACxC,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,SAAS;gBAAE,SAAQ;YAEvB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,wBAAwB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,CACxD,CAAA;YAED,gDAAgD;YAChD,MAAM,IAAI,GAAG,WAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAA;YAC1D,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;YAC7C,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3C,CAAC;IACL,CAAC;IAES,KAAK,CAAC,YAAY;QACxB,MAAM,YAAY,GAAgB,EAAE,CAAA;QACpC,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAA;QAChE,gDAAgD;QAChD,MAAM,cAAc,GAAG,IAAI,GAAG,EAAwB,CAAA;QACtD,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC7D,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAClE,CAAC,CAAC,CAAA;YACF,IAAI,YAAY,EAAE,CAAC;gBACf,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,YAAY,CAAC,CAAA;YAC1C,CAAC;QACL,CAAC;QACD,4CAA4C;QAC5C,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC9C,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC7C,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,SAAQ;YACZ,CAAC;YACD,MAAM,cAAc,GAChB,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;gBAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;gBACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;YACrD,MAAM,kBAAkB,GACpB,OAAO,YAAY,CAAC,UAAU,KAAK,QAAQ;gBACvC,CAAC,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,EAAE;gBAChC,CAAC,CAAC,YAAY,CAAC,UAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;YAE9D,IAAI,cAAc,KAAK,kBAAkB;gBAAE,SAAQ;YAEnD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,yBAAyB,IAAI,CAAC,IAAI,EAAE,CACvC,CAAA;YAED,6BAA6B;YAC7B,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC3B,CAAC;QAED,kHAAkH;QAClH,MAAM,mBAAmB,GAAG,CAAC,IAAU,EAAU,EAAE;YAC/C,wBAAwB;YACxB,MAAM,YAAY,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;YAC7C,IAAI,oBAAoB,GAAG,CAAC,IAAI,CAAC,CAAA;YACjC,sEAAsE;YACtE,IAAI,CAAC,YAAY,EAAE,CAAC;gBAChB,OAAO,oBAAoB,CAAA;YAC/B,CAAC;YACD,+BAA+B;YAC/B,KAAK,MAAM,CACP,WAAW,EACX,eAAe,EAClB,IAAI,cAAc,CAAC,OAAO,EAAE,EAAE,CAAC;gBAC5B,wBAAwB;gBACxB,IAAI,WAAW,KAAK,IAAI,EAAE,CAAC;oBACvB,SAAQ;gBACZ,CAAC;gBACD,+DAA+D;gBAC/D,IACI,eAAe,CAAC,SAAS;oBACzB,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;wBAC/C,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,EACvD,CAAC;oBACC,4DAA4D;oBAC5D,oBAAoB,GAAG,oBAAoB,CAAC,MAAM,CAC9C,mBAAmB,CAAC,WAAW,CAAC,CACnC,CAAA;gBACL,CAAC;YACL,CAAC;YACD,6BAA6B;YAC7B,OAAO,oBAAoB,CAAA;QAC/B,CAAC,CAAA;QAED,gFAAgF;QAChF,MAAM,4BAA4B,GAAc,IAAI,GAAG;QACnD,oDAAoD;QACpD,YAAY;aACP,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACzC,4EAA4E;aAC3E,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YACrB,OAAO,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC,EAAE,EAAE,CAAC;YACN,iDAAiD;aAChD,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YACX,OAAO,qBAAS,CAAC,eAAe,CAC5B,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,EACrB,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CACxB,CAAA;QACL,CAAC,CAAC;YACF,kCAAkC;aACjC,OAAO,EAAE,CACjB,CAAA;QAED,8BAA8B;QAC9B,KAAK,MAAM,IAAI,IAAI,4BAA4B,EAAE,CAAC;YAC9C,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QACzC,CAAC;QACD,IAAI,CAAC,WAAW,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAC9D,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,4BAA4B,CAAC,GAAG,CAAC,IAAI,CAAC,CACpD,CAAA;IACL,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,kBAAkB;QAC9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,6EAA6E;YAC7E,MAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC7D,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CACzB,CAAC,cAAc,EAAE,EAAE,CACf,cAAc,CAAC,iBAAiB;oBAChC,cAAc,CAAC,YAAY,KAAK,WAAW,CAAC,IAAI,CACvD,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,mBAAmB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAE9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,sBAAsB,KAAK,CAAC,IAAI,IAAI;gBAChC,mBAAmB,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAClE,CAAA;YAED,iCAAiC;YACjC,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,mBAAmB,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,aAAa;QACzB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,6BAA6B;YAC7B,MAAM,kBAAkB,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAC9C,CAAC,cAAc,EAAE,EAAE;gBACf,OAAO,CACH,CAAC,cAAc,CAAC,iBAAiB;oBACjC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACf,CAAC,WAAW,EAAE,EAAE,CACZ,WAAW,CAAC,IAAI;wBAChB,cAAc,CAAC,YAAY,CAClC,CACJ,CAAA;YACL,CAAC,CACJ,CAAA;YACD,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAE7C,iCAAiC;YACjC,MAAM,qBAAqB,GACvB,IAAI,CAAC,mCAAmC,CAAC,kBAAkB,CAAC,CAAA;YAChE,MAAM,eAAe,GAAG,qBAAqB,CAAC,GAAG,CAC7C,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,yBAAW,CAAC,MAAM,CAAC,CACtC,CAAA;YAED,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAE1C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,qBAAqB;gBACjB,kBAAkB;qBACb,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC;qBACpC,IAAI,CAAC,IAAI,CAAC,CACtB,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,EAAE,eAAe,CAAC,CAAA;QAC7D,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,sBAAsB,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAClD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;YACD,MAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAC5C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAC/B,CAAA;YACD,IACI,mBAAmB,CAAC,MAAM,KAAK,sBAAsB,CAAC,MAAM;gBAC5D,sBAAsB,CAAC,MAAM,GAAG,CAAC,EACnC,CAAC;gBACC,MAAM,qBAAqB,GAAG,sBAAsB,CAAC,GAAG,CACpD,CAAC,qBAAqB,EAAE,EAAE;oBACtB,OAAO,IAAI,yBAAW,CAClB,uBAAU,CAAC,wBAAwB,CAC/B,qBAAqB,EACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB,CACJ,CAAA;gBACL,CAAC,CACJ,CAAA;gBACD,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CACpC,KAAK,EACL,qBAAqB,CACxB,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;;OAGG;IACO,KAAK,CAAC,kBAAkB;QAC9B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,kBAAkB,CAC5D,KAAK,CAAC,OAAO,EACb,QAAQ,CAAC,OAAO,CACnB,CAAA;YACD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEzC,kDAAkD;YAClD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,+BAA+B,CACtC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAC3B,aAAa,CAAC,YAAY,CAC7B,CAAA;YACL,CAAC;YAED,oDAAoD;YACpD,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;gBACzC,MAAM,IAAI,CAAC,0BAA0B,CACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAC3B,aAAa,CAAC,YAAY,CAC7B,CAAA;YACL,CAAC;YAED,oDAAoD;YACpD,6CAA6C;YAC7C,IACI,CAAC,CACG,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;gBACtD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CACpD,EACH,CAAC;gBACC,KAAK,MAAM,aAAa,IAAI,cAAc,EAAE,CAAC;oBACzC,MAAM,IAAI,CAAC,0BAA0B,CACjC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,EAC3B,aAAa,CAAC,YAAY,CAC7B,CAAA;gBACL,CAAC;YACL,CAAC;YAED,oCAAoC;YACpC,MAAM,qBAAqB,GAAG,cAAc,CAAC,GAAG,CAC5C,CAAC,aAAa,EAAE,EAAE;gBACd,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,IAAI,CACrC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,aAAa,CAAC,YAAY,CACxD,CAAA;gBACF,MAAM,qBAAqB,GACvB,uBAAU,CAAC,wBAAwB,CAC/B,aAAa,EACb,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB,CAAA;gBACL,MAAM,cAAc,GAAG,IAAI,yBAAW,CAClC,qBAAqB,CACxB,CAAA;gBAED,OAAO;oBACH,SAAS,EAAE,cAAc;oBACzB,SAAS,EAAE,cAAc;iBAC5B,CAAA;YACL,CAAC,CACJ,CAAA;YAED,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEhD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,uBAAuB,KAAK,CAAC,IAAI,eAAe;gBAC5C,cAAc;qBACT,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC;qBACpC,IAAI,CAAC,IAAI,CAAC,CACtB,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAA;QACtE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,gBAAgB;QAC5B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO;iBAC9B,MAAM,CACH,CAAC,aAAa,EAAE,EAAE,CACd,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACf,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAC7C,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAC9C;iBACA,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,uBAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;YAE7D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAErC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,sBAAsB,UAAU;iBAC3B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC;iBACjC,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAC7C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU,CAAC,CAAA;QAC3D,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,oBAAoB;QAChC,2DAA2D;QAC3D,IACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU;YAC3C,CAAC,yBAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EACvD,CAAC;YACC,OAAM;QACV,CAAC;QACD,MAAM,mBAAmB,GAA6C,CAClE,IAAI,CAAC,WAAW,CACnB,CAAA;QACD,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;YACpD,mCAAmC;YACnC,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;gBACpD,MAAM,cAAc,GAChB,OAAO,IAAI,CAAC,UAAU,KAAK,QAAQ;oBAC/B,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;oBACxB,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;gBACrD,MAAM,kBAAkB,GACpB,OAAO,QAAQ,CAAC,UAAU,KAAK,QAAQ;oBACnC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE;oBAC5B,CAAC,CAAC,QAAQ,CAAC,UAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,CAAA;gBAC1D,OAAO,CACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;oBACvD,cAAc,KAAK,kBAAkB,CACxC,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY;gBAAE,SAAQ;YAEzC,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO;iBAC9B,MAAM,CACH,CAAC,aAAa,EAAE,EAAE,CACd,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CACd,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAC7C,IAAI,aAAa,CAAC,WAAW,KAAK,IAAI,CAC9C;iBACA,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,uBAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;YAE7D,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAErC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,sBAAsB,UAAU;iBAC3B,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC;iBACjC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG,CAC3C,CAAA;YACD,MAAM,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAA;QACjE,CAAC;IACL,CAAC;IAES,KAAK,CAAC,eAAe;QAC3B,2CAA2C;QAC3C,IACI,yBAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc;YAEtD,OAAM;QAEV,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM;iBAC5B,MAAM,CACH,CAAC,aAAa,EAAE,EAAE,CACd,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CACd,CAAC,UAAU,EAAE,EAAE,CACX,UAAU,CAAC,IAAI,KAAK,aAAa,CAAC,IAAI,CAC7C,CACR;iBACA,GAAG,CAAC,CAAC,aAAa,EAAE,EAAE,CAAC,uBAAU,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAA;YAE7D,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAEpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,iCAAiC,SAAS;iBACrC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC;iBACjC,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAC7C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,sBAAsB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;QACnE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,gCAAgC;QAC5C,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO;iBACpC,MAAM,CACH,CAAC,cAAc,EAAE,EAAE,CACf,cAAc,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;gBACjC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CACf,CAAC,WAAW,EAAE,EAAE,CACZ,WAAW,CAAC,IAAI,KAAK,cAAc,CAAC,IAAI,CAC/C,CACR;iBACA,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,yBAAW,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAA;YAEhE,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAE3C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,kCAAkC,gBAAgB;iBAC7C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,GAAG,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAC7C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,uBAAuB,CAC1C,KAAK,EACL,gBAAgB,CACnB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,mBAAmB;QAC/B,iDAAiD;QACjD,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,CAAC;YAAE,OAAM;QAEjE,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,aAAa,GAAG,QAAQ,CAAC,UAAU;iBACpC,MAAM,CACH,CAAC,iBAAiB,EAAE,EAAE,CAClB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAClB,CAAC,cAAc,EAAE,EAAE,CACf,cAAc,CAAC,IAAI,KAAK,iBAAiB,CAAC,IAAI,CACrD,CACR;iBACA,GAAG,CAAC,CAAC,iBAAiB,EAAE,EAAE,CACvB,+BAAc,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAC3C,CAAA;YAEL,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAExC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,qCAAqC,aAAa;iBAC7C,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC;iBACzC,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAC7C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,0BAA0B,CAC7C,KAAK,EACL,aAAa,CAChB,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,iBAAiB;QAC7B,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CACN,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC/D,CAAA;YACD,IAAI,CAAC,KAAK;gBAAE,SAAQ;YAEpB,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,EAAE;gBACvD,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAC1B,CAAC,YAAY,EAAE,EAAE,CACb,YAAY,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI;oBACrC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;wBAC3B,IAAI,CAAC,YAAY,CACb,UAAU,CAAC,wBAAwB,CACtC,CACZ,CAAA;YACL,CAAC,CAAC,CAAA;YACF,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;gBAAE,SAAQ;YAElC,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,EAAE,CACrD,iCAAe,CAAC,MAAM,CAClB,kBAAkB,EAClB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB,CACJ,CAAA;YACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,4BAA4B,OAAO;iBAC9B,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC;iBACtB,IAAI,CAAC,IAAI,CAAC,cAAc,KAAK,CAAC,IAAI,GAAG,CAC7C,CAAA;YACD,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,aAAa,CAAC,CAAA;QAClE,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,+BAA+B,CAC3C,SAAiB,EACjB,UAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,CACpD,CAAA;QACD,IAAI,CAAC,KAAK;YAAE,OAAM;QAElB,MAAM,YAAY,GAAY,EAAE,CAAA;QAChC,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAC3C,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CACpE,CAAA;QACD,IAAI,gBAAgB,EAAE,CAAC;YACnB,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;YACjC,WAAW,CAAC,WAAW,GAAG,CAAC,gBAAgB,CAAC,CAAA;YAC5C,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAC9B,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAA;QAC5C,CAAC;QAED,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACtD,MAAM,iBAAiB,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,CACpD,CAAC,UAAU,EAAE,EAAE;gBACX,OAAO,CACH,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,SAAS;oBAC3C,UAAU,CAAC,qBAAqB,CAAC,OAAO,CAAC,UAAU,CAAC;wBAChD,CAAC,CAAC,CACT,CAAA;YACL,CAAC,CACJ,CAAA;YAED,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,MAAM,WAAW,GAAG,WAAW,CAAC,KAAK,EAAE,CAAA;gBACvC,WAAW,CAAC,WAAW,GAAG,iBAAiB,CAAA;gBAC3C,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;gBAC9B,iBAAiB,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,EAAE,CAC3C,WAAW,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CACjD,CAAA;YACL,CAAC;QACL,CAAC;QAED,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;gBACrC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,oCACI,WAAW,CAAC,IAChB,KAAK,WAAW,CAAC,WAAW;qBACvB,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;qBACpC,IAAI,CAAC,IAAI,CAAC,EAAE,CACpB,CAAA;gBACD,MAAM,IAAI,CAAC,WAAW,CAAC,eAAe,CAClC,WAAW,EACX,WAAW,CAAC,WAAW,CAC1B,CAAA;YACL,CAAC;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,0BAA0B,CACtC,SAAiB,EACjB,UAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,CACpD,CAAA;QACD,IAAI,CAAC,KAAK;YAAE,OAAM;QAElB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,KAAK,EAAE,EAAE,CACN,KAAK,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;YAC5B,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CACnD,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAEvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,gCAAgC,SAAS,MAAM,UAAU,MAAM,cAAc;aACxE,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;aAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,CACpB,CAAA;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,0BAA0B,CACtC,SAAiB,EACjB,UAAkB;QAElB,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAC5C,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,SAAS,CACpD,CAAA;QACD,IAAI,CAAC,KAAK;YAAE,OAAM;QAElB,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CACvC,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC;YAC7B,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CACpD,CAAA;QACD,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC;YAAE,OAAM;QAEvC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CACjC,2CAA2C,SAAS,MAAM,UAAU,MAAM,cAAc;aACnF,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;aAC5B,IAAI,CAAC,IAAI,CAAC,EAAE,CACpB,CAAA;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,qBAAqB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAA;IACvE,CAAC;IAED;;OAEG;IACO,mCAAmC,CACzC,OAAyB;QAEzB,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,EAAE,CAClC,uBAAU,CAAC,wBAAwB,CAC/B,cAAc,EACd,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB,CACJ,CAAA;IACL,CAAC;IAED;;OAEG;IACO,KAAK,CAAC,0BAA0B,CAAC,WAAwB;QAC/D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAA;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAA;QACrC,MAAM,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAC9D,IAAI,CAAC,UAAU,CAAC,iBAAiB,EACjC,MAAM,EACN,QAAQ,CACX,CAAA;QAED,wDAAwD;QACxD,gEAAgE;QAChE,2FAA2F;QAC3F,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAA;QACnE,MAAM,WAAW,CAAC,WAAW,CACzB,IAAI,aAAK,CAAC;YACN,QAAQ,EAAE,QAAQ;YAClB,MAAM,EAAE,MAAM;YACd,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE;gBACL;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,YAAY;qBACpB,CAAC;oBACF,UAAU,EAAE,KAAK;oBACjB,SAAS;iBACZ;gBACD;oBACI,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,gBAAgB;qBACxB,CAAC;oBACF,UAAU,EAAE,IAAI;oBAChB,SAAS;iBACZ;gBACD;oBACI,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,cAAc;qBACtB,CAAC;oBACF,UAAU,EAAE,IAAI;oBAChB,SAAS;iBACZ;gBACD;oBACI,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,aAAa;qBACrB,CAAC;oBACF,UAAU,EAAE,IAAI;oBAChB,SAAS;iBACZ;gBACD;oBACI,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,YAAY;qBACpB,CAAC;oBACF,UAAU,EAAE,IAAI;oBAChB,SAAS;iBACZ;gBACD;oBACI,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC;wBACvC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,eAAe;6BACvC,aAAa;qBACrB,CAAC;oBACF,UAAU,EAAE,IAAI;oBAChB,SAAS;iBACZ;aACJ;SACJ,CAAC,EACF,IAAI,CACP,CAAA;IACL,CAAC;CACJ;AAv2CD,gDAu2CC", "file": "RdbmsSchemaBuilder.js", "sourcesContent": ["import { Table } from \"./table/Table\"\nimport { TableColumn } from \"./table/TableColumn\"\nimport { TableForeignKey } from \"./table/TableForeignKey\"\nimport { TableIndex } from \"./table/TableIndex\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { SchemaBuilder } from \"./SchemaBuilder\"\nimport { SqlInMemory } from \"../driver/SqlInMemory\"\nimport { TableUtils } from \"./util/TableUtils\"\nimport { TableColumnOptions } from \"./options/TableColumnOptions\"\nimport { TableUnique } from \"./table/TableUnique\"\nimport { TableCheck } from \"./table/TableCheck\"\nimport { TableExclusion } from \"./table/TableExclusion\"\nimport { View } from \"./view/View\"\nimport { ViewUtils } from \"./util/ViewUtils\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { PostgresQueryRunner } from \"../driver/postgres/PostgresQueryRunner\"\n\n/**\n * Creates complete tables schemas in the database based on the entity metadatas.\n *\n * Steps how schema is being built:\n * 1. load list of all tables with complete column and keys information from the db\n * 2. drop all (old) foreign keys that exist in the table, but does not exist in the metadata\n * 3. create new tables that does not exist in the db, but exist in the metadata\n * 4. drop all columns exist (left old) in the db table, but does not exist in the metadata\n * 5. add columns from metadata which does not exist in the table\n * 6. update all exist columns which metadata has changed\n * 7. update primary keys - update old and create new primary key from changed columns\n * 8. create foreign keys which does not exist in the table yet\n * 9. create indices which are missing in db yet, and drops indices which exist in the db, but does not exist in the metadata anymore\n */\nexport class RdbmsSchemaBuilder implements SchemaBuilder {\n    readonly \"@instanceof\" = Symbol.for(\"RdbmsSchemaBuilder\")\n\n    /**\n     * Used to execute schema creation queries in a single connection.\n     */\n    protected queryRunner: QueryRunner\n\n    private currentDatabase?: string\n\n    private currentSchema?: string\n\n    // -------------------------------------------------------------------------\n    // Constructor\n    // -------------------------------------------------------------------------\n\n    constructor(protected connection: DataSource) {}\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates complete schemas for the given entity metadatas.\n     */\n    async build(): Promise<void> {\n        this.queryRunner = this.connection.createQueryRunner()\n\n        // this.connection.driver.database || this.currentDatabase;\n        this.currentDatabase = this.connection.driver.database\n        this.currentSchema = this.connection.driver.schema\n\n        // CockroachDB implements asynchronous schema sync operations which can not been executed in transaction.\n        // E.g. if you try to DROP column and ADD it again in the same transaction, crdb throws error.\n        // In Spanner queries against the INFORMATION_SCHEMA can be used in a read-only transaction,\n        // but not in a read-write transaction.\n        const isUsingTransactions =\n            !(this.connection.driver.options.type === \"cockroachdb\") &&\n            !(this.connection.driver.options.type === \"spanner\") &&\n            this.connection.options.migrationsTransactionMode !== \"none\"\n\n        await this.queryRunner.beforeMigration()\n\n        if (isUsingTransactions) {\n            await this.queryRunner.startTransaction()\n        }\n\n        try {\n            await this.createMetadataTableIfNecessary(this.queryRunner)\n            // Flush the queryrunner table & view cache\n            const tablePaths = this.entityToSyncMetadatas.map((metadata) =>\n                this.getTablePath(metadata),\n            )\n            const viewPaths = this.viewEntityToSyncMetadatas.map((metadata) =>\n                this.getTablePath(metadata),\n            )\n\n            await this.queryRunner.getTables(tablePaths)\n            await this.queryRunner.getViews(viewPaths)\n\n            await this.executeSchemaSyncOperationsInProperOrder()\n\n            // if cache is enabled then perform cache-synchronization as well\n            if (this.connection.queryResultCache)\n                await this.connection.queryResultCache.synchronize(\n                    this.queryRunner,\n                )\n\n            if (isUsingTransactions) {\n                await this.queryRunner.commitTransaction()\n            }\n        } catch (error) {\n            try {\n                // we throw original error even if rollback thrown an error\n                if (isUsingTransactions) {\n                    await this.queryRunner.rollbackTransaction()\n                }\n            } catch (rollbackError) {}\n            throw error\n        } finally {\n            await this.queryRunner.afterMigration()\n\n            await this.queryRunner.release()\n        }\n    }\n\n    /**\n     * Create the typeorm_metadata table if necessary.\n     */\n    async createMetadataTableIfNecessary(\n        queryRunner: QueryRunner,\n    ): Promise<void> {\n        if (\n            this.viewEntityToSyncMetadatas.length > 0 ||\n            this.hasGeneratedColumns()\n        ) {\n            await this.createTypeormMetadataTable(queryRunner)\n        }\n    }\n\n    /**\n     * Returns sql queries to be executed by schema builder.\n     */\n    async log(): Promise<SqlInMemory> {\n        this.queryRunner = this.connection.createQueryRunner()\n        try {\n            // Flush the queryrunner table & view cache\n            const tablePaths = this.entityToSyncMetadatas.map((metadata) =>\n                this.getTablePath(metadata),\n            )\n            const viewPaths = this.viewEntityToSyncMetadatas.map((metadata) =>\n                this.getTablePath(metadata),\n            )\n            await this.queryRunner.getTables(tablePaths)\n            await this.queryRunner.getViews(viewPaths)\n\n            this.queryRunner.enableSqlMemory()\n            await this.executeSchemaSyncOperationsInProperOrder()\n\n            // if cache is enabled then perform cache-synchronization as well\n            if (this.connection.queryResultCache)\n                // todo: check this functionality\n                await this.connection.queryResultCache.synchronize(\n                    this.queryRunner,\n                )\n\n            return this.queryRunner.getMemorySql()\n        } finally {\n            // its important to disable this mode despite the fact we are release query builder\n            // because there exist drivers which reuse same query runner. Also its important to disable\n            // sql memory after call of getMemorySql() method because last one flushes sql memory.\n            this.queryRunner.disableSqlMemory()\n            await this.queryRunner.release()\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Returns only entities that should be synced in the database.\n     */\n    protected get entityToSyncMetadatas(): EntityMetadata[] {\n        return this.connection.entityMetadatas.filter(\n            (metadata) =>\n                metadata.synchronize &&\n                metadata.tableType !== \"entity-child\" &&\n                metadata.tableType !== \"view\",\n        )\n    }\n\n    /**\n     * Returns only entities that should be synced in the database.\n     */\n    protected get viewEntityToSyncMetadatas(): EntityMetadata[] {\n        return (\n            this.connection.entityMetadatas\n                .filter(\n                    (metadata) =>\n                        metadata.tableType === \"view\" && metadata.synchronize,\n                )\n                // sort views in creation order by dependencies\n                .sort(ViewUtils.viewMetadataCmp)\n        )\n    }\n\n    /**\n     * Checks if there are at least one generated column.\n     */\n    protected hasGeneratedColumns(): boolean {\n        return this.connection.entityMetadatas.some((entityMetadata) => {\n            return entityMetadata.columns.some((column) => column.generatedType)\n        })\n    }\n\n    /**\n     * Executes schema sync operations in a proper order.\n     * Order of operations matter here.\n     */\n    protected async executeSchemaSyncOperationsInProperOrder(): Promise<void> {\n        await this.dropOldViews()\n        await this.dropOldForeignKeys()\n        await this.dropOldIndices()\n        await this.dropOldChecks()\n        await this.dropOldExclusions()\n        await this.dropCompositeUniqueConstraints()\n        // await this.renameTables();\n        await this.renameColumns()\n        await this.changeTableComment()\n        await this.createNewTables()\n        await this.dropRemovedColumns()\n        await this.addNewColumns()\n        await this.updatePrimaryKeys()\n        await this.updateExistColumns()\n        await this.createNewIndices()\n        await this.createNewChecks()\n        await this.createNewExclusions()\n        await this.createCompositeUniqueConstraints()\n        await this.createForeignKeys()\n        await this.createViews()\n        await this.createNewViewIndices()\n    }\n\n    private getTablePath(\n        target: EntityMetadata | Table | View | TableForeignKey | string,\n    ): string {\n        const parsed = this.connection.driver.parseTableName(target)\n\n        return this.connection.driver.buildTableName(\n            parsed.tableName,\n            parsed.schema || this.currentSchema,\n            parsed.database || this.currentDatabase,\n        )\n    }\n\n    /**\n     * Drops all (old) foreign keys that exist in the tables, but do not exist in the entity metadata.\n     */\n    protected async dropOldForeignKeys(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            // find foreign keys that exist in the schemas but does not exist in the entity metadata\n            const tableForeignKeysToDrop = table.foreignKeys.filter(\n                (tableForeignKey) => {\n                    const metadataFK = metadata.foreignKeys.find(\n                        (metadataForeignKey) =>\n                            tableForeignKey.name === metadataForeignKey.name &&\n                            this.getTablePath(tableForeignKey) ===\n                                this.getTablePath(\n                                    metadataForeignKey.referencedEntityMetadata,\n                                ),\n                    )\n                    return (\n                        !metadataFK ||\n                        (metadataFK.onDelete &&\n                            metadataFK.onDelete !== tableForeignKey.onDelete) ||\n                        (metadataFK.onUpdate &&\n                            metadataFK.onUpdate !== tableForeignKey.onUpdate)\n                    )\n                },\n            )\n            if (tableForeignKeysToDrop.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `dropping old foreign keys of ${\n                    table.name\n                }: ${tableForeignKeysToDrop\n                    .map((dbForeignKey) => dbForeignKey.name)\n                    .join(\", \")}`,\n            )\n\n            // drop foreign keys from the database\n            await this.queryRunner.dropForeignKeys(\n                table,\n                tableForeignKeysToDrop,\n            )\n        }\n    }\n\n    /**\n     * Rename tables\n     */\n    protected async renameTables(): Promise<void> {\n        // for (const metadata of this.entityToSyncMetadatas) {\n        //     const table = this.queryRunner.loadedTables.find(table => this.getTablePath(table) === this.getTablePath(metadata));\n        // }\n    }\n\n    /**\n     * Renames columns.\n     * Works if only one column per table was changed.\n     * Changes only column name. If something besides name was changed, these changes will be ignored.\n     */\n    protected async renameColumns(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            if (metadata.columns.length !== table.columns.length) continue\n\n            const renamedMetadataColumns = metadata.columns\n                .filter((c) => !c.isVirtualProperty)\n                .filter((column) => {\n                    return !table.columns.find((tableColumn) => {\n                        return (\n                            tableColumn.name === column.databaseName &&\n                            tableColumn.type ===\n                                this.connection.driver.normalizeType(column) &&\n                            tableColumn.isNullable === column.isNullable &&\n                            tableColumn.isUnique ===\n                                this.connection.driver.normalizeIsUnique(column)\n                        )\n                    })\n                })\n\n            if (\n                renamedMetadataColumns.length === 0 ||\n                renamedMetadataColumns.length > 1\n            )\n                continue\n\n            const renamedTableColumns = table.columns.filter((tableColumn) => {\n                return !metadata.columns.find((column) => {\n                    return (\n                        !column.isVirtualProperty &&\n                        column.databaseName === tableColumn.name &&\n                        this.connection.driver.normalizeType(column) ===\n                            tableColumn.type &&\n                        column.isNullable === tableColumn.isNullable &&\n                        this.connection.driver.normalizeIsUnique(column) ===\n                            tableColumn.isUnique\n                    )\n                })\n            })\n\n            if (\n                renamedTableColumns.length === 0 ||\n                renamedTableColumns.length > 1\n            )\n                continue\n\n            const renamedColumn = renamedTableColumns[0].clone()\n            renamedColumn.name = renamedMetadataColumns[0].databaseName\n\n            this.connection.logger.logSchemaBuild(\n                `renaming column \"${renamedTableColumns[0].name}\" in \"${table.name}\" to \"${renamedColumn.name}\"`,\n            )\n            await this.queryRunner.renameColumn(\n                table,\n                renamedTableColumns[0],\n                renamedColumn,\n            )\n        }\n    }\n\n    protected async dropOldIndices(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const dropQueries = table.indices\n                .filter((tableIndex) => {\n                    const indexMetadata = metadata.indices.find(\n                        (index) => index.name === tableIndex.name,\n                    )\n                    if (indexMetadata) {\n                        if (indexMetadata.synchronize === false) return false\n\n                        if (indexMetadata.isUnique !== tableIndex.isUnique)\n                            return true\n\n                        if (indexMetadata.isSpatial !== tableIndex.isSpatial)\n                            return true\n\n                        if (\n                            this.connection.driver.isFullTextColumnTypeSupported() &&\n                            indexMetadata.isFulltext !== tableIndex.isFulltext\n                        )\n                            return true\n\n                        if (\n                            indexMetadata.columns.length !==\n                            tableIndex.columnNames.length\n                        )\n                            return true\n\n                        return !indexMetadata.columns.every(\n                            (column) =>\n                                tableIndex.columnNames.indexOf(\n                                    column.databaseName,\n                                ) !== -1,\n                        )\n                    }\n\n                    return true\n                })\n                .map(async (tableIndex) => {\n                    this.connection.logger.logSchemaBuild(\n                        `dropping an index: \"${tableIndex.name}\" from table ${table.name}`,\n                    )\n                    await this.queryRunner.dropIndex(table, tableIndex)\n                })\n\n            await Promise.all(dropQueries)\n        }\n        if (this.connection.options.type === \"postgres\") {\n            const postgresQueryRunner: PostgresQueryRunner = <\n                PostgresQueryRunner\n            >this.queryRunner\n            for (const metadata of this.viewEntityToSyncMetadatas) {\n                const view = this.queryRunner.loadedViews.find(\n                    (view) =>\n                        this.getTablePath(view) === this.getTablePath(metadata),\n                )\n                if (!view) continue\n\n                const dropQueries = view.indices\n                    .filter((tableIndex) => {\n                        const indexMetadata = metadata.indices.find(\n                            (index) => index.name === tableIndex.name,\n                        )\n                        if (indexMetadata) {\n                            if (indexMetadata.synchronize === false)\n                                return false\n\n                            if (indexMetadata.isUnique !== tableIndex.isUnique)\n                                return true\n\n                            if (\n                                indexMetadata.isSpatial !== tableIndex.isSpatial\n                            )\n                                return true\n\n                            if (\n                                this.connection.driver.isFullTextColumnTypeSupported() &&\n                                indexMetadata.isFulltext !==\n                                    tableIndex.isFulltext\n                            )\n                                return true\n\n                            if (\n                                indexMetadata.columns.length !==\n                                tableIndex.columnNames.length\n                            )\n                                return true\n\n                            return !indexMetadata.columns.every(\n                                (column) =>\n                                    tableIndex.columnNames.indexOf(\n                                        column.databaseName,\n                                    ) !== -1,\n                            )\n                        }\n\n                        return true\n                    })\n                    .map(async (tableIndex) => {\n                        this.connection.logger.logSchemaBuild(\n                            `dropping an index: \"${tableIndex.name}\" from view ${view.name}`,\n                        )\n                        await postgresQueryRunner.dropViewIndex(\n                            view,\n                            tableIndex,\n                        )\n                    })\n\n                await Promise.all(dropQueries)\n            }\n        }\n    }\n\n    protected async dropOldChecks(): Promise<void> {\n        // Mysql does not support check constraints\n        if (\n            DriverUtils.isMySQLFamily(this.connection.driver) ||\n            this.connection.driver.options.type === \"aurora-mysql\"\n        )\n            return\n\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const oldChecks = table.checks.filter((tableCheck) => {\n                return !metadata.checks.find(\n                    (checkMetadata) => checkMetadata.name === tableCheck.name,\n                )\n            })\n\n            if (oldChecks.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `dropping old check constraint: ${oldChecks\n                    .map((check) => `\"${check.name}\"`)\n                    .join(\", \")} from table \"${table.name}\"`,\n            )\n            await this.queryRunner.dropCheckConstraints(table, oldChecks)\n        }\n    }\n\n    protected async dropCompositeUniqueConstraints(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const compositeUniques = table.uniques.filter((tableUnique) => {\n                return (\n                    tableUnique.columnNames.length > 1 &&\n                    !metadata.uniques.find(\n                        (uniqueMetadata) =>\n                            uniqueMetadata.name === tableUnique.name,\n                    )\n                )\n            })\n\n            if (compositeUniques.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `dropping old unique constraint: ${compositeUniques\n                    .map((unique) => `\"${unique.name}\"`)\n                    .join(\", \")} from table \"${table.name}\"`,\n            )\n            await this.queryRunner.dropUniqueConstraints(\n                table,\n                compositeUniques,\n            )\n        }\n    }\n\n    protected async dropOldExclusions(): Promise<void> {\n        // Only PostgreSQL supports exclusion constraints\n        if (!(this.connection.driver.options.type === \"postgres\")) return\n\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const oldExclusions = table.exclusions.filter((tableExclusion) => {\n                return !metadata.exclusions.find(\n                    (exclusionMetadata) =>\n                        exclusionMetadata.name === tableExclusion.name,\n                )\n            })\n\n            if (oldExclusions.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `dropping old exclusion constraint: ${oldExclusions\n                    .map((exclusion) => `\"${exclusion.name}\"`)\n                    .join(\", \")} from table \"${table.name}\"`,\n            )\n            await this.queryRunner.dropExclusionConstraints(\n                table,\n                oldExclusions,\n            )\n        }\n    }\n\n    /**\n     * change table comment\n     */\n    protected async changeTableComment(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            if (\n                DriverUtils.isMySQLFamily(this.connection.driver) ||\n                this.connection.driver.options.type === \"postgres\"\n            ) {\n                const newComment = metadata.comment\n                await this.queryRunner.changeTableComment(table, newComment)\n            }\n        }\n    }\n\n    /**\n     * Creates tables that do not exist in the database yet.\n     * New tables are created without foreign and primary keys.\n     * Primary key only can be created in conclusion with auto generated column.\n     */\n    protected async createNewTables(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            // check if table does not exist yet\n            const existTable = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (existTable) continue\n\n            this.connection.logger.logSchemaBuild(\n                `creating a new table: ${this.getTablePath(metadata)}`,\n            )\n\n            // create a new table and sync it in the database\n            const table = Table.create(metadata, this.connection.driver)\n            await this.queryRunner.createTable(table, false, false)\n            this.queryRunner.loadedTables.push(table)\n        }\n    }\n\n    protected async createViews(): Promise<void> {\n        for (const metadata of this.viewEntityToSyncMetadatas) {\n            // check if view does not exist yet\n            const existView = this.queryRunner.loadedViews.find((view) => {\n                const viewExpression =\n                    typeof view.expression === \"string\"\n                        ? view.expression.trim()\n                        : view.expression(this.connection).getQuery()\n                const metadataExpression =\n                    typeof metadata.expression === \"string\"\n                        ? metadata.expression.trim()\n                        : metadata.expression!(this.connection).getQuery()\n                return (\n                    this.getTablePath(view) === this.getTablePath(metadata) &&\n                    viewExpression === metadataExpression\n                )\n            })\n            if (existView) continue\n\n            this.connection.logger.logSchemaBuild(\n                `creating a new view: ${this.getTablePath(metadata)}`,\n            )\n\n            // create a new view and sync it in the database\n            const view = View.create(metadata, this.connection.driver)\n            await this.queryRunner.createView(view, true)\n            this.queryRunner.loadedViews.push(view)\n        }\n    }\n\n    protected async dropOldViews(): Promise<void> {\n        const droppedViews: Array<View> = []\n        const viewEntityToSyncMetadatas = this.viewEntityToSyncMetadatas\n        // BuIld lookup cache for finding views metadata\n        const viewToMetadata = new Map<View, EntityMetadata>()\n        for (const view of this.queryRunner.loadedViews) {\n            const viewMetadata = viewEntityToSyncMetadatas.find((metadata) => {\n                return this.getTablePath(view) === this.getTablePath(metadata)\n            })\n            if (viewMetadata) {\n                viewToMetadata.set(view, viewMetadata)\n            }\n        }\n        // Gather all changed view, that need a drop\n        for (const view of this.queryRunner.loadedViews) {\n            const viewMetadata = viewToMetadata.get(view)\n            if (!viewMetadata) {\n                continue\n            }\n            const viewExpression =\n                typeof view.expression === \"string\"\n                    ? view.expression.trim()\n                    : view.expression(this.connection).getQuery()\n            const metadataExpression =\n                typeof viewMetadata.expression === \"string\"\n                    ? viewMetadata.expression.trim()\n                    : viewMetadata.expression!(this.connection).getQuery()\n\n            if (viewExpression === metadataExpression) continue\n\n            this.connection.logger.logSchemaBuild(\n                `dropping an old view: ${view.name}`,\n            )\n\n            // Collect view to be dropped\n            droppedViews.push(view)\n        }\n\n        // Helper function that for a given view, will recursively return list of the view and all views that depend on it\n        const viewDependencyChain = (view: View): View[] => {\n            // Get the view metadata\n            const viewMetadata = viewToMetadata.get(view)\n            let viewWithDependencies = [view]\n            // If no metadata is known for the view, simply return the view itself\n            if (!viewMetadata) {\n                return viewWithDependencies\n            }\n            // Iterate over all known views\n            for (const [\n                currentView,\n                currentMetadata,\n            ] of viewToMetadata.entries()) {\n                // Ignore self reference\n                if (currentView === view) {\n                    continue\n                }\n                // If the currently iterated view depends on the passed in view\n                if (\n                    currentMetadata.dependsOn &&\n                    (currentMetadata.dependsOn.has(viewMetadata.target) ||\n                        currentMetadata.dependsOn.has(viewMetadata.name))\n                ) {\n                    // Recursively add currently iterate view and its dependents\n                    viewWithDependencies = viewWithDependencies.concat(\n                        viewDependencyChain(currentView),\n                    )\n                }\n            }\n            // Return all collected views\n            return viewWithDependencies\n        }\n\n        // Collect final list of views to be dropped in a Set so there are no duplicates\n        const droppedViewsWithDependencies: Set<View> = new Set(\n            // Collect all dropped views, and their dependencies\n            droppedViews\n                .map((view) => viewDependencyChain(view))\n                // Flattened to single Array ( can be replaced with flatMap, once supported)\n                .reduce((all, segment) => {\n                    return all.concat(segment)\n                }, [])\n                // Sort the views to be dropped in creation order\n                .sort((a, b) => {\n                    return ViewUtils.viewMetadataCmp(\n                        viewToMetadata.get(a),\n                        viewToMetadata.get(b),\n                    )\n                })\n                // reverse order to get drop order\n                .reverse(),\n        )\n\n        // Finally emit all drop views\n        for (const view of droppedViewsWithDependencies) {\n            await this.queryRunner.dropView(view)\n        }\n        this.queryRunner.loadedViews = this.queryRunner.loadedViews.filter(\n            (view) => !droppedViewsWithDependencies.has(view),\n        )\n    }\n\n    /**\n     * Drops all columns that exist in the table, but does not exist in the metadata (left old).\n     * We drop their keys too, since it should be safe.\n     */\n    protected async dropRemovedColumns(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            // find columns that exist in the database but does not exist in the metadata\n            const droppedTableColumns = table.columns.filter((tableColumn) => {\n                return !metadata.columns.find(\n                    (columnMetadata) =>\n                        columnMetadata.isVirtualProperty ||\n                        columnMetadata.databaseName === tableColumn.name,\n                )\n            })\n            if (droppedTableColumns.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `columns dropped in ${table.name}: ` +\n                    droppedTableColumns.map((column) => column.name).join(\", \"),\n            )\n\n            // drop columns from the database\n            await this.queryRunner.dropColumns(table, droppedTableColumns)\n        }\n    }\n\n    /**\n     * Adds columns from metadata which does not exist in the table.\n     * Columns are created without keys.\n     */\n    protected async addNewColumns(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            // find which columns are new\n            const newColumnMetadatas = metadata.columns.filter(\n                (columnMetadata) => {\n                    return (\n                        !columnMetadata.isVirtualProperty &&\n                        !table.columns.find(\n                            (tableColumn) =>\n                                tableColumn.name ===\n                                columnMetadata.databaseName,\n                        )\n                    )\n                },\n            )\n            if (newColumnMetadatas.length === 0) continue\n\n            // create columns in the database\n            const newTableColumnOptions =\n                this.metadataColumnsToTableColumnOptions(newColumnMetadatas)\n            const newTableColumns = newTableColumnOptions.map(\n                (option) => new TableColumn(option),\n            )\n\n            if (newTableColumns.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `new columns added: ` +\n                    newColumnMetadatas\n                        .map((column) => column.databaseName)\n                        .join(\", \"),\n            )\n            await this.queryRunner.addColumns(table, newTableColumns)\n        }\n    }\n\n    /**\n     * Updates composite primary keys.\n     */\n    protected async updatePrimaryKeys(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const primaryMetadataColumns = metadata.columns.filter(\n                (column) => column.isPrimary,\n            )\n            const primaryTableColumns = table.columns.filter(\n                (column) => column.isPrimary,\n            )\n            if (\n                primaryTableColumns.length !== primaryMetadataColumns.length &&\n                primaryMetadataColumns.length > 1\n            ) {\n                const changedPrimaryColumns = primaryMetadataColumns.map(\n                    (primaryMetadataColumn) => {\n                        return new TableColumn(\n                            TableUtils.createTableColumnOptions(\n                                primaryMetadataColumn,\n                                this.connection.driver,\n                            ),\n                        )\n                    },\n                )\n                await this.queryRunner.updatePrimaryKeys(\n                    table,\n                    changedPrimaryColumns,\n                )\n            }\n        }\n    }\n\n    /**\n     * Update all exist columns which metadata has changed.\n     * Still don't create keys. Also we don't touch foreign keys of the changed columns.\n     */\n    protected async updateExistColumns(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const changedColumns = this.connection.driver.findChangedColumns(\n                table.columns,\n                metadata.columns,\n            )\n            if (changedColumns.length === 0) continue\n\n            // drop all foreign keys that point to this column\n            for (const changedColumn of changedColumns) {\n                await this.dropColumnReferencedForeignKeys(\n                    this.getTablePath(metadata),\n                    changedColumn.databaseName,\n                )\n            }\n\n            // drop all composite indices related to this column\n            for (const changedColumn of changedColumns) {\n                await this.dropColumnCompositeIndices(\n                    this.getTablePath(metadata),\n                    changedColumn.databaseName,\n                )\n            }\n\n            // drop all composite uniques related to this column\n            // Mysql does not support unique constraints.\n            if (\n                !(\n                    DriverUtils.isMySQLFamily(this.connection.driver) ||\n                    this.connection.driver.options.type === \"aurora-mysql\" ||\n                    this.connection.driver.options.type === \"spanner\"\n                )\n            ) {\n                for (const changedColumn of changedColumns) {\n                    await this.dropColumnCompositeUniques(\n                        this.getTablePath(metadata),\n                        changedColumn.databaseName,\n                    )\n                }\n            }\n\n            // generate a map of new/old columns\n            const newAndOldTableColumns = changedColumns.map(\n                (changedColumn) => {\n                    const oldTableColumn = table.columns.find(\n                        (column) => column.name === changedColumn.databaseName,\n                    )!\n                    const newTableColumnOptions =\n                        TableUtils.createTableColumnOptions(\n                            changedColumn,\n                            this.connection.driver,\n                        )\n                    const newTableColumn = new TableColumn(\n                        newTableColumnOptions,\n                    )\n\n                    return {\n                        oldColumn: oldTableColumn,\n                        newColumn: newTableColumn,\n                    }\n                },\n            )\n\n            if (newAndOldTableColumns.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `columns changed in \"${table.name}\". updating: ` +\n                    changedColumns\n                        .map((column) => column.databaseName)\n                        .join(\", \"),\n            )\n            await this.queryRunner.changeColumns(table, newAndOldTableColumns)\n        }\n    }\n\n    /**\n     * Creates composite indices which are missing in db yet.\n     */\n    protected async createNewIndices(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const newIndices = metadata.indices\n                .filter(\n                    (indexMetadata) =>\n                        !table.indices.find(\n                            (tableIndex) =>\n                                tableIndex.name === indexMetadata.name,\n                        ) && indexMetadata.synchronize === true,\n                )\n                .map((indexMetadata) => TableIndex.create(indexMetadata))\n\n            if (newIndices.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `adding new indices ${newIndices\n                    .map((index) => `\"${index.name}\"`)\n                    .join(\", \")} in table \"${table.name}\"`,\n            )\n            await this.queryRunner.createIndices(table, newIndices)\n        }\n    }\n\n    /**\n     * Creates indices for materialized views.\n     */\n    protected async createNewViewIndices(): Promise<void> {\n        // Only PostgreSQL supports indices for materialized views.\n        if (\n            this.connection.options.type !== \"postgres\" ||\n            !DriverUtils.isPostgresFamily(this.connection.driver)\n        ) {\n            return\n        }\n        const postgresQueryRunner: PostgresQueryRunner = <PostgresQueryRunner>(\n            this.queryRunner\n        )\n        for (const metadata of this.viewEntityToSyncMetadatas) {\n            // check if view does not exist yet\n            const view = this.queryRunner.loadedViews.find((view) => {\n                const viewExpression =\n                    typeof view.expression === \"string\"\n                        ? view.expression.trim()\n                        : view.expression(this.connection).getQuery()\n                const metadataExpression =\n                    typeof metadata.expression === \"string\"\n                        ? metadata.expression.trim()\n                        : metadata.expression!(this.connection).getQuery()\n                return (\n                    this.getTablePath(view) === this.getTablePath(metadata) &&\n                    viewExpression === metadataExpression\n                )\n            })\n            if (!view || !view.materialized) continue\n\n            const newIndices = metadata.indices\n                .filter(\n                    (indexMetadata) =>\n                        !view.indices.find(\n                            (tableIndex) =>\n                                tableIndex.name === indexMetadata.name,\n                        ) && indexMetadata.synchronize === true,\n                )\n                .map((indexMetadata) => TableIndex.create(indexMetadata))\n\n            if (newIndices.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `adding new indices ${newIndices\n                    .map((index) => `\"${index.name}\"`)\n                    .join(\", \")} in view \"${view.name}\"`,\n            )\n            await postgresQueryRunner.createViewIndices(view, newIndices)\n        }\n    }\n\n    protected async createNewChecks(): Promise<void> {\n        // Mysql does not support check constraints\n        if (\n            DriverUtils.isMySQLFamily(this.connection.driver) ||\n            this.connection.driver.options.type === \"aurora-mysql\"\n        )\n            return\n\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const newChecks = metadata.checks\n                .filter(\n                    (checkMetadata) =>\n                        !table.checks.find(\n                            (tableCheck) =>\n                                tableCheck.name === checkMetadata.name,\n                        ),\n                )\n                .map((checkMetadata) => TableCheck.create(checkMetadata))\n\n            if (newChecks.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `adding new check constraints: ${newChecks\n                    .map((index) => `\"${index.name}\"`)\n                    .join(\", \")} in table \"${table.name}\"`,\n            )\n            await this.queryRunner.createCheckConstraints(table, newChecks)\n        }\n    }\n\n    /**\n     * Creates composite uniques which are missing in db yet.\n     */\n    protected async createCompositeUniqueConstraints(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const compositeUniques = metadata.uniques\n                .filter(\n                    (uniqueMetadata) =>\n                        uniqueMetadata.columns.length > 1 &&\n                        !table.uniques.find(\n                            (tableUnique) =>\n                                tableUnique.name === uniqueMetadata.name,\n                        ),\n                )\n                .map((uniqueMetadata) => TableUnique.create(uniqueMetadata))\n\n            if (compositeUniques.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `adding new unique constraints: ${compositeUniques\n                    .map((unique) => `\"${unique.name}\"`)\n                    .join(\", \")} in table \"${table.name}\"`,\n            )\n            await this.queryRunner.createUniqueConstraints(\n                table,\n                compositeUniques,\n            )\n        }\n    }\n\n    /**\n     * Creates exclusions which are missing in db yet.\n     */\n    protected async createNewExclusions(): Promise<void> {\n        // Only PostgreSQL supports exclusion constraints\n        if (!(this.connection.driver.options.type === \"postgres\")) return\n\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const newExclusions = metadata.exclusions\n                .filter(\n                    (exclusionMetadata) =>\n                        !table.exclusions.find(\n                            (tableExclusion) =>\n                                tableExclusion.name === exclusionMetadata.name,\n                        ),\n                )\n                .map((exclusionMetadata) =>\n                    TableExclusion.create(exclusionMetadata),\n                )\n\n            if (newExclusions.length === 0) continue\n\n            this.connection.logger.logSchemaBuild(\n                `adding new exclusion constraints: ${newExclusions\n                    .map((exclusion) => `\"${exclusion.name}\"`)\n                    .join(\", \")} in table \"${table.name}\"`,\n            )\n            await this.queryRunner.createExclusionConstraints(\n                table,\n                newExclusions,\n            )\n        }\n    }\n\n    /**\n     * Creates foreign keys which does not exist in the table yet.\n     */\n    protected async createForeignKeys(): Promise<void> {\n        for (const metadata of this.entityToSyncMetadatas) {\n            const table = this.queryRunner.loadedTables.find(\n                (table) =>\n                    this.getTablePath(table) === this.getTablePath(metadata),\n            )\n            if (!table) continue\n\n            const newKeys = metadata.foreignKeys.filter((foreignKey) => {\n                return !table.foreignKeys.find(\n                    (dbForeignKey) =>\n                        dbForeignKey.name === foreignKey.name &&\n                        this.getTablePath(dbForeignKey) ===\n                            this.getTablePath(\n                                foreignKey.referencedEntityMetadata,\n                            ),\n                )\n            })\n            if (newKeys.length === 0) continue\n\n            const dbForeignKeys = newKeys.map((foreignKeyMetadata) =>\n                TableForeignKey.create(\n                    foreignKeyMetadata,\n                    this.connection.driver,\n                ),\n            )\n            this.connection.logger.logSchemaBuild(\n                `creating a foreign keys: ${newKeys\n                    .map((key) => key.name)\n                    .join(\", \")} on table \"${table.name}\"`,\n            )\n            await this.queryRunner.createForeignKeys(table, dbForeignKeys)\n        }\n    }\n\n    /**\n     * Drops all foreign keys where given column of the given table is being used.\n     */\n    protected async dropColumnReferencedForeignKeys(\n        tablePath: string,\n        columnName: string,\n    ): Promise<void> {\n        const table = this.queryRunner.loadedTables.find(\n            (table) => this.getTablePath(table) === tablePath,\n        )\n        if (!table) return\n\n        const tablesWithFK: Table[] = []\n        const columnForeignKey = table.foreignKeys.find(\n            (foreignKey) => foreignKey.columnNames.indexOf(columnName) !== -1,\n        )\n        if (columnForeignKey) {\n            const clonedTable = table.clone()\n            clonedTable.foreignKeys = [columnForeignKey]\n            tablesWithFK.push(clonedTable)\n            table.removeForeignKey(columnForeignKey)\n        }\n\n        for (const loadedTable of this.queryRunner.loadedTables) {\n            const dependForeignKeys = loadedTable.foreignKeys.filter(\n                (foreignKey) => {\n                    return (\n                        this.getTablePath(foreignKey) === tablePath &&\n                        foreignKey.referencedColumnNames.indexOf(columnName) !==\n                            -1\n                    )\n                },\n            )\n\n            if (dependForeignKeys.length > 0) {\n                const clonedTable = loadedTable.clone()\n                clonedTable.foreignKeys = dependForeignKeys\n                tablesWithFK.push(clonedTable)\n                dependForeignKeys.forEach((dependForeignKey) =>\n                    loadedTable.removeForeignKey(dependForeignKey),\n                )\n            }\n        }\n\n        if (tablesWithFK.length > 0) {\n            for (const tableWithFK of tablesWithFK) {\n                this.connection.logger.logSchemaBuild(\n                    `dropping related foreign keys of ${\n                        tableWithFK.name\n                    }: ${tableWithFK.foreignKeys\n                        .map((foreignKey) => foreignKey.name)\n                        .join(\", \")}`,\n                )\n                await this.queryRunner.dropForeignKeys(\n                    tableWithFK,\n                    tableWithFK.foreignKeys,\n                )\n            }\n        }\n    }\n\n    /**\n     * Drops all composite indices, related to given column.\n     */\n    protected async dropColumnCompositeIndices(\n        tablePath: string,\n        columnName: string,\n    ): Promise<void> {\n        const table = this.queryRunner.loadedTables.find(\n            (table) => this.getTablePath(table) === tablePath,\n        )\n        if (!table) return\n\n        const relatedIndices = table.indices.filter(\n            (index) =>\n                index.columnNames.length > 1 &&\n                index.columnNames.indexOf(columnName) !== -1,\n        )\n        if (relatedIndices.length === 0) return\n\n        this.connection.logger.logSchemaBuild(\n            `dropping related indices of \"${tablePath}\".\"${columnName}\": ${relatedIndices\n                .map((index) => index.name)\n                .join(\", \")}`,\n        )\n        await this.queryRunner.dropIndices(table, relatedIndices)\n    }\n\n    /**\n     * Drops all composite uniques, related to given column.\n     */\n    protected async dropColumnCompositeUniques(\n        tablePath: string,\n        columnName: string,\n    ): Promise<void> {\n        const table = this.queryRunner.loadedTables.find(\n            (table) => this.getTablePath(table) === tablePath,\n        )\n        if (!table) return\n\n        const relatedUniques = table.uniques.filter(\n            (unique) =>\n                unique.columnNames.length > 1 &&\n                unique.columnNames.indexOf(columnName) !== -1,\n        )\n        if (relatedUniques.length === 0) return\n\n        this.connection.logger.logSchemaBuild(\n            `dropping related unique constraints of \"${tablePath}\".\"${columnName}\": ${relatedUniques\n                .map((unique) => unique.name)\n                .join(\", \")}`,\n        )\n        await this.queryRunner.dropUniqueConstraints(table, relatedUniques)\n    }\n\n    /**\n     * Creates new columns from the given column metadatas.\n     */\n    protected metadataColumnsToTableColumnOptions(\n        columns: ColumnMetadata[],\n    ): TableColumnOptions[] {\n        return columns.map((columnMetadata) =>\n            TableUtils.createTableColumnOptions(\n                columnMetadata,\n                this.connection.driver,\n            ),\n        )\n    }\n\n    /**\n     * Creates typeorm service table for storing user defined Views and generate columns.\n     */\n    protected async createTypeormMetadataTable(queryRunner: QueryRunner) {\n        const schema = this.currentSchema\n        const database = this.currentDatabase\n        const typeormMetadataTable = this.connection.driver.buildTableName(\n            this.connection.metadataTableName,\n            schema,\n            database,\n        )\n\n        // Spanner requires at least one primary key in a table.\n        // Since we don't have unique column in \"typeorm_metadata\" table\n        // and we should avoid breaking changes, we mark all columns as primary for Spanner driver.\n        const isPrimary = this.connection.driver.options.type === \"spanner\"\n        await queryRunner.createTable(\n            new Table({\n                database: database,\n                schema: schema,\n                name: typeormMetadataTable,\n                columns: [\n                    {\n                        name: \"type\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataType,\n                        }),\n                        isNullable: false,\n                        isPrimary,\n                    },\n                    {\n                        name: \"database\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataDatabase,\n                        }),\n                        isNullable: true,\n                        isPrimary,\n                    },\n                    {\n                        name: \"schema\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataSchema,\n                        }),\n                        isNullable: true,\n                        isPrimary,\n                    },\n                    {\n                        name: \"table\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataTable,\n                        }),\n                        isNullable: true,\n                        isPrimary,\n                    },\n                    {\n                        name: \"name\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataName,\n                        }),\n                        isNullable: true,\n                        isPrimary,\n                    },\n                    {\n                        name: \"value\",\n                        type: this.connection.driver.normalizeType({\n                            type: this.connection.driver.mappedDataTypes\n                                .metadataValue,\n                        }),\n                        isNullable: true,\n                        isPrimary,\n                    },\n                ],\n            }),\n            true,\n        )\n    }\n}\n"], "sourceRoot": ".."}