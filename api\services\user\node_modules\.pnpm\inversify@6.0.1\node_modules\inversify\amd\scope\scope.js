var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
define(["require", "exports", "../inversify", "../utils/async"], function (require, exports, inversify_1, async_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.saveToScope = exports.tryGetFromScope = void 0;
    var tryGetFromScope = function (requestScope, binding) {
        if ((binding.scope === inversify_1.BindingScopeEnum.Singleton) && binding.activated) {
            return binding.cache;
        }
        if (binding.scope === inversify_1.BindingScopeEnum.Request &&
            requestScope.has(binding.id)) {
            return requestScope.get(binding.id);
        }
        return null;
    };
    exports.tryGetFromScope = tryGetFromScope;
    var saveToScope = function (requestScope, binding, result) {
        if (binding.scope === inversify_1.BindingScopeEnum.Singleton) {
            _saveToSingletonScope(binding, result);
        }
        if (binding.scope === inversify_1.BindingScopeEnum.Request) {
            _saveToRequestScope(requestScope, binding, result);
        }
    };
    exports.saveToScope = saveToScope;
    var _saveToRequestScope = function (requestScope, binding, result) {
        if (!requestScope.has(binding.id)) {
            requestScope.set(binding.id, result);
        }
    };
    var _saveToSingletonScope = function (binding, result) {
        binding.cache = result;
        binding.activated = true;
        if ((0, async_1.isPromise)(result)) {
            void _saveAsyncResultToSingletonScope(binding, result);
        }
    };
    var _saveAsyncResultToSingletonScope = function (binding, asyncResult) { return __awaiter(void 0, void 0, void 0, function () {
        var result, ex_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    return [4, asyncResult];
                case 1:
                    result = _a.sent();
                    binding.cache = result;
                    return [3, 3];
                case 2:
                    ex_1 = _a.sent();
                    binding.cache = null;
                    binding.activated = false;
                    throw ex_1;
                case 3: return [2];
            }
        });
    }); };
});
//# sourceMappingURL=scope.js.map