import { BaseService } from '@cool-midway/core';
import { CoolRpcService } from '@cool-midway/rpc';
import { Provide, Inject } from '@midwayjs/decorator';

/**
 * 支付服务（微服务版）
 * 通过插件机制调用微信、支付宝等第三方SDK
 */
@Provide()
@CoolRpcService()
export class PaymentPayService extends BaseService {
  @Inject()
  rpc;

  @Inject()
  pluginService;

  /**
   * 支付成功
   * @param orderNum 订单号
   * @param payType 支付方式 0-待支付 1-微信 2-支付宝
   */
  async paySuccess(orderNum: string, payType: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getByOrderNum', [orderNum]);
    if (order && order.status === 0) {
      // 2. 更新订单状态为已支付
      await this.rpc.call('order.updateOrderStatus', [order.id, 1, payType]);
      // 3. 扣减库存
      await this.rpc.call('goods.updateStock', [order.goodsList]);
      // 4. 拆分订单
      await this.splitOrder(orderNum);
      // 5. 商品售罄处理
      for (const e of order.goodsList) {
        await this.rpc.call('goods.updateStatus', [e.goodsId, 3]);
      }
    }
    return 'success';
  }

  /**
   * 拆分订单
   * @param orderNum 订单号
   */
  async splitOrder(orderNum: string) {
    const order = await this.rpc.call('order.getByOrderNum', [orderNum]);
    const subOrders = [];
    if (order.status === 1) {
      for (let i = 0; i < order.goodsList.length; i++) {
        const item = order.goodsList[i];
        const data = {
          ...order,
          isMain: 0,
          mainOrderNum: order.orderNum,
          sellerId: item.goodsInfo.userId,
          price: item.price,
          orderNum: `${order.orderNum}${i}`,
          goodsList: [item],
        };
        // 插入子订单
        const subOrderId = await this.rpc.call('order.insertSubOrder', [data]);
        subOrders.push(subOrderId);
        // 插入订单商品
        await this.rpc.call('order.insertOrderGoods', [{ ...item, orderId: subOrderId }]);
        if (order.sellerId) {
          // 卖家加冻结金额
          await this.rpc.call('wallet.changeFreeze', [data.sellerId, item.price]);
          // 卖家加一条记录
          await this.rpc.call('wallet.addRecord', [{
            userId: data.sellerId,
            title: '订单收入',
            amount: item.price,
            type: 0,
            objectId: subOrderId,
            objectType: 0,
          }]);
          // 卖家发送消息
          await this.rpc.call('msg.sendMsg', [data.sellerId, '订单提醒', '您有一个新的订单，请及时发货', { type: 0, id: subOrderId }]);
        }
        // 自动确认收货队列
        const orderConfirm = await this.rpc.call('sysParam.dataByKey', ['orderConfirm']);
        await this.rpc.call('order.addOrderQueue', [{ orderId: order.id, action: 'CONFIRM' }, { delay: orderConfirm * 60 * 60 * 1000 }]);
      }
      // 更新通过次数
      await this.rpc.call('sysParam.updateCount', ['passed', order.goodsList.length]);
      // 修改原订单，插入子订单
      await this.rpc.call('order.updateSubOrders', [order.id, subOrders]);
    }
  }

  /**
   * 微信小程序支付
   * @param orderId
   * @param userId
   */
  async wxMiniPay(orderId: number, userId: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getOrder', [orderId, userId]);
    if (!order) throw new Error('订单不存在');
    // 2. 获取微信支付插件实例
    const plugin = await this.pluginService.getInstance('pay-wx');
    const instance = await plugin.getInstance();
    // 3. 组装支付参数
    const params = {
      description: '商品采购',
      out_trade_no: order.orderNum,
      notify_url: plugin.config.notify_url,
      amount: {
        total: order.price * 100,
      },
      payer: {
        openid: order.openid, // 需通过用户服务获取
      },
      scene_info: {
        payer_client_ip: '127.0.0.1',
      },
    };
    // 4. 调用微信SDK发起支付
    const result = await instance.transactions_jsapi(params);
    return result;
  }

  /**
   * 钱包余额支付
   * @param orderId
   * @param userId
   */
  async walletPay(orderId: number, userId: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getOrder', [orderId, userId]);
    // 2. 校验订单状态
    if (!order || order.status !== 0) {
      throw new Error('订单状态异常');
    }
    // 3. 校验用户钱包余额
    const wallet = await this.rpc.call('wallet.getUserWalletInfo', [userId]);
    if (!wallet || wallet.balance < order.price) {
      throw new Error('余额不足');
    }
    // 4. 扣减余额
    await this.rpc.call('wallet.changeBalance', [userId, -order.price]);
    // 5. 调用支付成功逻辑
    return await this.paySuccess(order.orderNum, 3); // 3-钱包支付
  }

  /**
   * 微信退款
   * @param orderNum
   * @param amount
   */
  async wxRefund(orderNum: string, amount: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getByOrderNum', [orderNum]);
    if (!order) {
      throw new Error('订单不存在');
    }
    // 2. 调用微信退款服务（假设有 payment.wxRefund）
    const result = await this.rpc.call('payment.wxRefund', [order, amount]);
    // 3. 退款后处理（如解冻金额、生成退款流水、消息通知等）
    await this.rpc.call('wallet.unfreeze', [order.sellerId, amount]);
    await this.rpc.call('wallet.addRecord', [{
      userId: order.sellerId,
      title: '订单退款',
      amount: -amount,
      type: 1,
      objectId: order.id,
      objectType: 1,
    }]);
    await this.rpc.call('msg.sendMsg', [order.sellerId, '退款提醒', '您的订单已退款', { type: 1, id: order.id }]);
    return result;
  }

  /**
   * 支付宝支付
   * @param orderId
   * @param userId
   */
  async aliPay(orderId: number, userId: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getOrder', [orderId, userId]);
    if (!order) throw new Error('订单不存在');
    // 2. 获取支付宝支付插件实例
    const plugin = await this.pluginService.getInstance('pay-ali');
    const instance = await plugin.getInstance();
    // 3. 组装支付参数
    const params = {
      out_trade_no: order.orderNum,
      total_amount: order.price,
      subject: '商品采购',
      product_code: 'QUICK_MSECURITY_PAY',
      notify_url: plugin.config.notify_url,
    };
    // 4. 调用支付宝SDK发起支付
    const result = await instance.pagePay(params);
    return result;
  }

  /**
   * 通用退款（自动判断支付方式）
   * @param orderNum
   * @param amount
   */
  async refund(orderNum: string, amount: number) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getByOrderNum', [orderNum]);
    if (!order) {
      throw new Error('订单不存在');
    }
    // 2. 根据支付方式调用不同退款逻辑
    if (order.payType === 1) {
      // 微信退款
      return await this.wxRefund(orderNum, amount);
    } else if (order.payType === 2) {
      // 支付宝退款
      const result = await this.rpc.call('payment.aliRefund', [order, amount]);
      // 退款后处理
      await this.rpc.call('wallet.unfreeze', [order.sellerId, amount]);
      await this.rpc.call('wallet.addRecord', [{
        userId: order.sellerId,
        title: '订单退款',
        amount: -amount,
        type: 1,
        objectId: order.id,
        objectType: 1,
      }]);
      await this.rpc.call('msg.sendMsg', [order.sellerId, '退款提醒', '您的订单已退款', { type: 1, id: order.id }]);
      return result;
    } else if (order.payType === 3) {
      // 钱包退款
      await this.rpc.call('wallet.changeBalance', [order.userId, amount]);
      await this.rpc.call('wallet.unfreeze', [order.sellerId, amount]);
      await this.rpc.call('wallet.addRecord', [{
        userId: order.sellerId,
        title: '订单退款',
        amount: -amount,
        type: 1,
        objectId: order.id,
        objectType: 1,
      }]);
      await this.rpc.call('msg.sendMsg', [order.sellerId, '退款提醒', '您的订单已退款', { type: 1, id: order.id }]);
      return 'wallet refund success';
    } else {
      throw new Error('不支持的支付方式');
    }
  }

  /**
   * 支付回调处理（如微信/支付宝异步通知）
   * @param payType 支付方式
   * @param callbackData 回调数据
   */
  async handlePayCallback(payType: number, callbackData: any) {
    // 1. 验签、解析回调数据（可通过 RPC 调用第三方支付服务）
    const verifyResult = await this.rpc.call('payment.verifyCallback', [payType, callbackData]);
    if (!verifyResult.success) {
      throw new Error('支付回调验签失败');
    }
    // 2. 获取订单号、支付金额等
    const { orderNum, amount } = verifyResult;
    // 3. 调用支付成功主流程
    await this.paySuccess(orderNum, payType);
    // 4. 记录回调日志
    await this.rpc.call('payment.saveCallbackLog', [payType, callbackData]);
    return { success: true };
  }

  /**
   * 查询支付状态
   * @param orderNum 订单号
   */
  async getPayStatus(orderNum: string) {
    // 1. 获取订单
    const order = await this.rpc.call('order.getByOrderNum', [orderNum]);
    if (!order) {
      throw new Error('订单不存在');
    }
    // 2. 返回支付状态
    return {
      orderNum: order.orderNum,
      status: order.status, // 0-待支付 1-已支付
      payType: order.payType,
      payTime: order.payTime,
    };
  }

  /**
   * 对账（可定时任务调用）
   * @param date 对账日期
   */
  async reconcile(date: string) {
    // 1. 获取本地支付流水
    const localRecords = await this.rpc.call('payment.getLocalRecords', [date]);
    // 2. 获取第三方支付平台流水
    const thirdRecords = await this.rpc.call('payment.getThirdRecords', [date]);
    // 3. 对比并生成对账报告
    const report = await this.rpc.call('payment.generateReconcileReport', [localRecords, thirdRecords]);
    // 4. 记录对账结果
    await this.rpc.call('payment.saveReconcileReport', [date, report]);
    return report;
  }
} 