import { ModuleLoadType, DataSourceManagerConfigOption } from '../interface';
import { MidwayEnvironmentService } from '../service/environmentService';
import { MidwayPriorityManager } from './priorityManager';
export declare abstract class DataSourceManager<T, ConnectionOpts extends Record<string, any> = Record<string, any>> {
    protected dataSource: Map<string, T>;
    protected options: DataSourceManagerConfigOption<ConnectionOpts>;
    protected modelMapping: WeakMap<object, any>;
    private innerDefaultDataSourceName;
    protected dataSourcePriority: Record<string, string>;
    protected appDir: string;
    protected environmentService: MidwayEnvironmentService;
    protected priorityManager: MidwayPriorityManager;
    protected initDataSource(dataSourceConfig: DataSourceManagerConfigOption<ConnectionOpts>, baseDirOrOptions: {
        baseDir: string;
        entitiesConfigKey?: string;
    } | string): Promise<void>;
    /**
     * get a data source instance
     * @param dataSourceName
     */
    getDataSource(dataSourceName: string): T;
    /**
     * check data source has exists
     * @param dataSourceName
     */
    hasDataSource(dataSourceName: string): boolean;
    getDataSourceNames(): string[];
    getAllDataSources(): Map<string, T>;
    /**
     * check the data source is connected
     * @param dataSourceName
     */
    isConnected(dataSourceName: string): Promise<boolean>;
    createInstance(config: any, clientName: any, options?: {
        validateConnection?: boolean;
        cacheInstance?: boolean | undefined;
    }): Promise<T | void>;
    /**
     * get data source name by model or repository
     * @param modelOrRepository
     */
    getDataSourceNameByModel(modelOrRepository: any): string | undefined;
    abstract getName(): string;
    protected abstract createDataSource(config: any, dataSourceName: string): Promise<T | void> | (T | void);
    protected abstract checkConnected(dataSource: T): Promise<boolean>;
    protected abstract destroyDataSource(dataSource: T): Promise<void>;
    stop(): Promise<void>;
    getDefaultDataSourceName(): string;
    getDataSourcePriority(name: string): string;
    isHighPriority(name: string): boolean;
    isMediumPriority(name: string): boolean;
    isLowPriority(name: string): boolean;
}
export declare function formatGlobString(globString: string): string[];
export declare function globModels(globString: string, appDir: string, loadMode?: ModuleLoadType): Promise<any[]>;
//# sourceMappingURL=dataSourceManager.d.ts.map