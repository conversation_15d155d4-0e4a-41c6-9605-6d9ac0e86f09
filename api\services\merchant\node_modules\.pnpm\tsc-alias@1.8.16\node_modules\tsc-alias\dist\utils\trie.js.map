{"version": 3, "file": "trie.js", "sourceRoot": "", "sources": ["../../src/utils/trie.ts"], "names": [], "mappings": ";;;AAcA,+BAAgE;AAChE,wCAA6E;AAG7E,MAAa,QAAQ;IAInB;QACE,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;IAQM,GAAG,CAAC,IAAY,EAAE,IAAO;QAC9B,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACrC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,CAAC,IAAI,QAAQ,EAAK,CAAC;QACtB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SACnC;QACD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAOM,MAAM,CAAC,IAAY;;QACxB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC;QAElC,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,OAAO,IAAI;YACT,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC;gBAChB,CAAC,CAAC,IAAI,CAAC,IAAI;gBACX,CAAC,CAAC,MAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,mCAAI,IAAI,CAAC,IAAI;YAC/C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;IAChB,CAAC;IAQD,MAAM,CAAC,cAAc,CACnB,MAAsB,EACtB,KAAgB;QAEhB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAS,CAAC;QACpC,IAAI,KAAK,EAAE;YACT,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;iBACf,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;gBACb,OAAO;oBACL,uBAAuB,EAAE,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC;oBAC5C,MAAM,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;oBAEhC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;wBAC/B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;wBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;wBACvC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;wBAC1C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;wBAItC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;4BACvD,MAAM,SAAS,GAAG,QAAQ,CAAC;4BAC3B,IAAI,mBAAmB,GAAG,QAAQ,CAAC;4BACnC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;gCACrB,mBAAmB,GAAG,SAAS,CAAC,OAAO,CACrC,kBAAkB,EAClB,SAAS,CACV,CAAC;6BACH;4BAED,IAAI,GAAG,SAAS,GAAG,mBAAmB,CAAC;yBACxC;wBAED,IAAI,IAAA,iBAAU,EAAC,IAAI,CAAC,EAAE;4BACpB,IAAI,GAAG,IAAA,eAAQ,EACb,IAAA,cAAO,EAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,EACzC,IAAI,CACL,CAAC;yBACH;wBAED,IACE,IAAA,gBAAS,EAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC;4BAC9B,CAAC,MAAM,CAAC,kBAAkB,EAC1B;4BACA,IAAA,oCAA0B,EAAC,MAAM,CAAC,CAAC;yBACpC;wBAED,OAAO,IAAI,CAAC;oBACd,CAAC,CAAC;iBACH,CAAC;YACJ,CAAC,CAAC;iBACD,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;gBACjB,IAAI,KAAK,CAAC,MAAM,EAAE;oBAEhB,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,kCACrB,KAAK,KAER,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAA,6BAAmB,EAAC,MAAM,CAAC,CAAC,IACnD,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;SACN;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAnHD,4BAmHC;AAED,SAAS,KAAK,CAAC,SAAiB;IAC9B,OAAO,0BAA0B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACpD,CAAC", "sourcesContent": ["/**\n * @file\n *\n * The TrieNode class is a prefix tree.\n * [Trie](https://en.wikipedia.org/wiki/Trie)\n *\n * This is a tree data structure used for locating specific keys\n * from within a set. The links between nodes defined by individual characters.\n * A node's position in the trie defines the key with which it is associated.\n * This distributes the value of each key across the data structure,\n * and means that not every node necessarily has an associated value.\n */\n\n/** */\nimport { isAbsolute, normalize, relative, resolve } from 'path';\nimport { findBasePathOfAlias, relativeOutPathToConfigDir } from '../helpers';\nimport { Alias, IProjectConfig, PathLike } from '../interfaces';\n\nexport class TrieNode<T> {\n  private children: Map<string, TrieNode<T>>;\n  public data: T | null;\n\n  constructor() {\n    this.children = new Map();\n    this.data = null;\n  }\n\n  /**\n   * add adds an alias to the prefix tree.\n   * @param {string} name the prefix of the alias.\n   * @param {T} data the alias data.\n   * @returns {void}.\n   */\n  public add(name: string, data: T): void {\n    if (name.length <= 0) return;\n    const node = this.children.has(name[0])\n      ? this.children.get(name[0])\n      : new TrieNode<T>();\n    if (name.length == 1) {\n      node.data = data;\n    } else {\n      node.add(name.substring(1), data);\n    }\n    this.children.set(name[0], node);\n  }\n\n  /**\n   * search searches the prefix tree for the most correct alias data for a given prefix.\n   * @param {string} name the prefix to search for.\n   * @returns {T | null} the alias data or null.\n   */\n  public search(name: string): T | null {\n    if (name.length <= 0) return null;\n\n    const node = this.children.get(name[0]);\n    return node\n      ? name.length == 1\n        ? node.data\n        : node.search(name.substring(1)) ?? node.data\n      : this.data;\n  }\n\n  /**\n   * buildAliasTrie builds an alias trie\n   * @param {IProjectConfig} config projectConfig is an object with config details\n   * @param {PathLike} paths optional the paths to put into the trie\n   * @returns {TrieNode<Alias>} a TrieNode with the paths/aliases inside\n   */\n  static buildAliasTrie(\n    config: IProjectConfig,\n    paths?: PathLike\n  ): TrieNode<Alias> {\n    const aliasTrie = new this<Alias>();\n    if (paths) {\n      Object.keys(paths)\n        .map((alias) => {\n          return {\n            shouldPrefixMatchWildly: alias.endsWith('*'),\n            prefix: alias.replace(/\\*$/, ''),\n            // Normalize paths.\n            paths: paths[alias].map((path) => {\n              path = path.replace(/\\*$/, '');\n\n              const dotIndex = path.lastIndexOf('.');\n              const beforeDot = path.slice(0, dotIndex);\n              const afterDot = path.slice(dotIndex);\n\n              // Refuse to normalize extensions for paths that look like \"a.b/c\" or \"a.b\\c\".\n              // Even if the current system is Linux the original author could've written a Windows path.\n              if (!afterDot.includes('/') && !afterDot.includes('\\\\')) {\n                const extension = afterDot;\n                let normalizedExtension = afterDot;\n                if (!isDTS(extension)) {\n                  normalizedExtension = extension.replace(\n                    /\\.([mc])?ts(x)?$/,\n                    '.$1js$2'\n                  );\n                }\n\n                path = beforeDot + normalizedExtension;\n              }\n\n              if (isAbsolute(path)) {\n                path = relative(\n                  resolve(config.configDir, config.baseUrl),\n                  path\n                );\n              }\n\n              if (\n                normalize(path).includes('..') &&\n                !config.configDirInOutPath\n              ) {\n                relativeOutPathToConfigDir(config);\n              }\n\n              return path;\n            })\n          };\n        })\n        .forEach((alias) => {\n          if (alias.prefix) {\n            // Add all aliases to AliasTrie.\n            aliasTrie.add(alias.prefix, {\n              ...alias,\n              // Find basepath of aliases.\n              paths: alias.paths.map(findBasePathOfAlias(config))\n            });\n          }\n        });\n    }\n    return aliasTrie;\n  }\n}\n\nfunction isDTS(extension: string): boolean {\n  return /\\.d(\\..*)?\\.[mc]?ts(x)?$/.test(extension);\n}\n"]}