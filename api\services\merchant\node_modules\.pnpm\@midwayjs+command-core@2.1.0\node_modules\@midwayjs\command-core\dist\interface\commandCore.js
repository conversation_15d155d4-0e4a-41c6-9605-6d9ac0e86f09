"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLIOutputLevel = void 0;
var CLIOutputLevel;
(function (CLIOutputLevel) {
    CLIOutputLevel[CLIOutputLevel["None"] = 0] = "None";
    CLIOutputLevel[CLIOutputLevel["Error"] = 1] = "Error";
    CLIOutputLevel[CLIOutputLevel["Warn"] = 2] = "Warn";
    CLIOutputLevel[CLIOutputLevel["Info"] = 3] = "Info";
    CLIOutputLevel[CLIOutputLevel["All"] = 4] = "All";
})(CLIOutputLevel = exports.CLIOutputLevel || (exports.CLIOutputLevel = {}));
//# sourceMappingURL=commandCore.js.map