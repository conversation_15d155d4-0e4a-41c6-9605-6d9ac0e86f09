{"version": 3, "file": "IsUUID.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsUUID.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,eAAe,MAAM,sBAAsB,CAAC;AAGnD,MAAM,CAAC,IAAM,OAAO,GAAG,QAAQ,CAAC;AAEhC;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,KAAc,EAAE,OAAiC;IACtE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,MAAM,CAAC,OAAiC,EAAE,iBAAqC;IAC7F,OAAO,UAAU,CACf;QACE,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,CAAC,OAAO,CAAC;QACtB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,MAAM,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAAnC,CAAmC;YACvE,cAAc,EAAE,YAAY,CAAC,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,0BAA0B,EAAvC,CAAuC,EAAE,iBAAiB,CAAC;SACvG;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isUuidValidator from 'validator/lib/isUUID';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_UUID = 'isUuid';\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function isUUID(value: unknown, version?: ValidatorJS.UUIDVersion): boolean {\n  return typeof value === 'string' && isUuidValidator(value, version);\n}\n\n/**\n * Checks if the string is a UUID (version 3, 4 or 5).\n * If given value is not a string, then it returns false.\n */\nexport function IsUUID(version?: ValidatorJS.UUIDVersion, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_UUID,\n      constraints: [version],\n      validator: {\n        validate: (value, args): boolean => isUUID(value, args?.constraints[0]),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be a UUID', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}