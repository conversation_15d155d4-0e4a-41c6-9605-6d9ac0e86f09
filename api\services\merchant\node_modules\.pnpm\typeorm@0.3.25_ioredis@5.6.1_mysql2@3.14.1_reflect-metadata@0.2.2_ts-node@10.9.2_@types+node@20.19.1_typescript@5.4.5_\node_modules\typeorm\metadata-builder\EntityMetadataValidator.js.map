{"version": 3, "sources": ["../../src/metadata-builder/EntityMetadataValidator.ts"], "names": [], "mappings": ";;;AACA,kFAA8E;AAC9E,4EAAwE;AACxE,+CAA2C;AAE3C,kFAA8E;AAE9E,8EAA0E;AAC1E,gFAA4E;AAC5E,oCAAuC;AACvC,uDAAmD;AAEnD,mEAAmE;AACnE,6FAA6F;AAC7F,sEAAsE;AACtE,mGAAmG;AACnG,2GAA2G;AAC3G,6DAA6D;AAC7D,uEAAuE;AACvE,iFAAiF;AAEjF,2FAA2F;AAC3F,wHAAwH;AACxH,gIAAgI;AAChI,mGAAmG;AACnG,8EAA8E;AAC9E,8CAA8C;AAC9C,yDAAyD;AAEzD;;GAEG;AACH,MAAa,uBAAuB;IAChC,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,YAAY,CAAC,eAAiC,EAAE,MAAc;QAC1D,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE,CACvC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,eAAe,EAAE,MAAM,CAAC,CACzD,CAAA;QACD,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,CAAA;QAC1C,IAAI,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAA;IAChD,CAAC;IAED;;OAEG;IACH,QAAQ,CACJ,cAA8B,EAC9B,kBAAoC,EACpC,MAAc;QAEd,oCAAoC;QACpC,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU;YACnE,MAAM,IAAI,qDAAyB,CAAC,cAAc,CAAC,CAAA;QAEvD,uEAAuE;QACvE,6DAA6D;QAC7D,IAAI,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3C,MAAM,uBAAuB,GAAG,cAAc,CAAC,cAAc,CAAC,KAAK,CAC/D,CAAC,cAAc,EAAE,CAAC,EAAE,eAAe,EAAE,EAAE,CACnC,cAAc,CAAC,wBAAwB;gBACvC,eAAe,CAAC,CAAC,CAAC,CAAC,wBAAwB,CAClD,CAAA;YACD,IAAI,CAAC,uBAAuB,EAAE,CAAC;gBAC3B,MAAM,IAAI,oBAAY,CAClB,UAAU,cAAc,CAAC,IAAI,sGAAsG,CACtI,CAAA;YACL,CAAC;QACL,CAAC;QAED,gEAAgE;QAChE,uEAAuE;QACvE,IACI,cAAc,CAAC,kBAAkB,KAAK,KAAK;YAC3C,cAAc,CAAC,SAAS,KAAK,cAAc,EAC7C,CAAC;YACC,IAAI,CAAC,cAAc,CAAC,mBAAmB;gBACnC,MAAM,IAAI,oBAAY,CAClB,UAAU,cAAc,CAAC,IAAI,kIAAkI,CAClK,CAAA;YAEL,IAAI,OAAO,cAAc,CAAC,kBAAkB,KAAK,WAAW;gBACxD,MAAM,IAAI,oBAAY,CAClB,UAAU,cAAc,CAAC,IAAI,+EAA+E,CAC/G,CAAA;YAEL,MAAM,oCAAoC,GACtC,kBAAkB,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACjC,OAAO,CACH,QAAQ,KAAK,cAAc;oBAC3B,CAAC,QAAQ,CAAC,kBAAkB,KAAK,KAAK;wBAClC,QAAQ,CAAC,SAAS,KAAK,cAAc,CAAC;oBAC1C,QAAQ,CAAC,SAAS,KAAK,cAAc,CAAC,SAAS;oBAC/C,QAAQ,CAAC,kBAAkB;wBACvB,cAAc,CAAC,kBAAkB;oBACrC,QAAQ,CAAC,eAAe,CAAC,IAAI,CACzB,CAAC,MAAM,EAAE,EAAE,CACP,cAAc,CAAC,eAAe,CAAC,OAAO,CAClC,MAAM,CACT,KAAK,CAAC,CAAC,CACf,CACJ,CAAA;YACL,CAAC,CAAC,CAAA;YACN,IAAI,oCAAoC;gBACpC,MAAM,IAAI,oBAAY,CAClB,YAAY,cAAc,CAAC,IAAI,QAAQ,oCAAoC,CAAC,IAAI,2GAA2G,CAC9L,CAAA;QACT,CAAC;QAED,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;YACpD,IACI,aAAa,CAAC,QAAQ,CAAC,WAAW;gBAClC,aAAa,CAAC,QAAQ,CAAC,UAAU;gBAEjC,MAAM,IAAI,oBAAY,CAClB,2EAA2E,CAC9E,CAAA;QACT,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC,EAAE,CAAC;YACvC,cAAc,CAAC,OAAO;iBACjB,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC;iBAC7C,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;gBAChB,MAAM,gBAAgB,GAAG,MAAM,CAAC,aAAa,CACzC,MAAM,CACK,CAAA;gBACf,IACI,MAAM,CAAC,kBAAkB,CAAC,OAAO,CAAC,gBAAgB,CAAC;oBACnD,CAAC,CAAC;oBAEF,MAAM,IAAI,qDAAyB,CAC/B,MAAM,EACN,gBAAgB,EAChB,MAAM,CAAC,OAAO,CAAC,IAAI,CACtB,CAAA;gBACL,IACI,MAAM,CAAC,MAAM;oBACb,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAChC,gBAAgB,CACnB,KAAK,CAAC,CAAC;oBAER,MAAM,IAAI,oBAAY,CAClB,UAAU,MAAM,CAAC,YAAY,cAAc,cAAc,CAAC,IAAI,oCAAoC,CACrG,CAAA;gBACL,IACI,MAAM,CAAC,IAAI,KAAK,MAAM;oBACtB,CAAC,MAAM,CAAC,IAAI;oBACZ,CAAC,MAAM,CAAC,QAAQ;oBAEhB,MAAM,IAAI,oBAAY,CAClB,WAAW,MAAM,CAAC,YAAY,gBAAgB,cAAc,CAAC,IAAI,oEAAoE,CACxI,CAAA;YACT,CAAC,CAAC,CAAA;QACV,CAAC;QAED,IACI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC;YACjC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxC,CAAC;YACC,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAClD,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM,CACjE,CAAA;YACD,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBAC3B,MAAM,IAAI,oBAAY,CAClB,YAAY,cAAc,CAAC,IAAI,sEAAsE,CACxG,CAAA;QACT,CAAC;QAED,gHAAgH;QAChH,4GAA4G;QAC5G,iHAAiH;QACjH,IAAI,yBAAW,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;YACpC,MAAM,qBAAqB,GAAG,kBAAkB,CAAC,MAAM,CACnD,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAClC,CAAA;YACD,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ;gBACtD,MAAM,IAAI,iDAAuB,CAAC,UAAU,CAAC,CAAA;QACrD,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClC,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,CAChD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,OAAO,CAC7B,CAAA;YACD,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC;gBACzB,MAAM,IAAI,oBAAY,CAClB,yDAAyD,CAC5D,CAAA;QACT,CAAC;QAED,mDAAmD;QACnD,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACrC,MAAM,aAAa,GAAG,cAAc,CAAC,OAAO,CAAC,IAAI,CAC7C,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY;gBACnB,CAAC,CAAC,MAAM,CAAC,aAAa;oBAClB,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,CAC9C,CAAA;YACD,IAAI,aAAa;gBACb,MAAM,IAAI,oBAAY,CAClB,WAAW,aAAa,CAAC,YAAY,gBAAgB,cAAc,CAAC,IAAI,+EAA+E,CAC1J,CAAA;QACT,CAAC;QAED,4DAA4D;QAC5D,MAAM,cAAc,GAAG,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE;YACpD,gBAAgB,EAAE,IAAI;SACzB,CAAC,CAAA;QACF,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,IAAI,QAAQ,CAAC,YAAY,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAChD,uGAAuG;gBACvG,IAAI,QAAQ,CAAC,kBAAkB,KAAK,KAAK;oBAAE,OAAM;gBAEjD,sDAAsD;gBACtD,MAAM,wBAAwB,GAC1B,QAAQ,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;gBAC3C,IAAI,KAAK,CAAC,OAAO,CAAC,wBAAwB,CAAC;oBACvC,MAAM,IAAI,mDAAwB,CAAC,QAAQ,CAAC,CAAA;YACpD,CAAC;QACL,CAAC,CAAC,CAAA;QAEF,qBAAqB;QACrB,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,sBAAsB;YACtB,IACI,MAAM,CAAC,sBAAsB;gBAC7B,QAAQ,CAAC,QAAQ;gBACjB,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAC5D,CAAC;gBACC,MAAM,IAAI,oBAAY,CAClB,iBAAiB,QAAQ,CAAC,QAAQ,0BAA0B,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CACrF,CAAA;YACL,CAAC;YAED,sBAAsB;YACtB,IACI,MAAM,CAAC,sBAAsB;gBAC7B,QAAQ,CAAC,QAAQ;gBACjB,CAAC,MAAM,CAAC,sBAAsB,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAC5D,CAAC;gBACC,MAAM,IAAI,oBAAY,CAClB,iBAAiB,QAAQ,CAAC,QAAQ,sBAAsB,MAAM,CAAC,OAAO,CAAC,IAAI,GAAG,CACjF,CAAA;YACL,CAAC;YAED,qBAAqB;YACrB,4EAA4E;YAC5E,kBAAkB;YAClB,4BAA4B;YAC5B,kCAAkC;YAClC,+EAA+E;YAC/E,iGAAiG;YACjG,yEAAyE;YACzE,uFAAuF;YACvF,IAAI;YACJ,sBAAsB;YACtB,8GAA8G;YAC9G,uDAAuD;YACvD,kBAAkB;YAClB;;;;;;;;;;;;;;;;;;;6EAmBiE;YACjE,wFAAwF;YACxF,qEAAqE;YACrE,qBAAqB;YACrB,yHAAyH;YACzH,iEAAiE;YACjE,mGAAmG;YACnG,oGAAoG;YACpG,oGAAoG;YACpG,6IAA6I;YAC7I,sLAAsL;YACtL,wEAAwE;YACxE,0IAA0I;YAC1I,8FAA8F;YAC9F,qJAAqJ;YACrJ,oHAAoH;YACpH,oEAAoE;QACxE,CAAC,CAAC,CAAA;QAEF,0GAA0G;QAC1G,cAAc,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;YAC1C,MAAM,uBAAuB,GACzB,QAAQ,CAAC,eAAe;gBACxB,QAAQ,CAAC,eAAe;gBACxB,QAAQ,CAAC,eAAgB,CAAC,eAAe,CAAA;YAC7C,IAAI,uBAAuB;gBACvB,MAAM,IAAI,oBAAY,CAClB,YAAY,cAAc,CAAC,IAAI,IAC3B,QAAQ,CAAC,YACb,QAAQ,QAAQ,CAAC,eAAgB,CAAC,cAAc,CAAC,IAAI,IACjD,QAAQ,CAAC,eAAgB,CAAC,YAC9B,gCAAgC;oBAC5B,8GAA8G,CACrH,CAAA;QACT,CAAC,CAAC,CAAA,CAAC,qFAAqF;QAExF,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,GAAE,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED;;OAEG;IACO,oBAAoB,CAAC,eAAiC;QAC5D,MAAM,KAAK,GAAG,IAAI,mBAAQ,EAAE,CAAA;QAC5B,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QACtC,CAAC,CAAC,CAAA;QACF,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,wBAAwB;iBAClC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC;iBAC1C,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAClB,KAAK,CAAC,aAAa,CACf,cAAc,CAAC,IAAI,EACnB,QAAQ,CAAC,qBAAqB,CAAC,IAAI,CACtC,CAAA;YACL,CAAC,CAAC,CAAA;QACV,CAAC,CAAC,CAAA;QACF,IAAI,CAAC;YACD,KAAK,CAAC,YAAY,EAAE,CAAA;QACxB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,MAAM,IAAI,+CAAsB,CAC5B,GAAG,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,iCAAiC,EAAE,EAAE,CAAC,CAChE,CAAA;QACL,CAAC;IACL,CAAC;IAED;;OAEG;IACO,sBAAsB,CAAC,eAAiC;QAC9D,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,EAAE;YACvC,cAAc,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC/C,IACI,QAAQ,CAAC,eAAe;oBACxB,QAAQ,CAAC,eAAe,CAAC,OAAO;oBAEhC,MAAM,IAAI,oBAAY,CAClB,2CAA2C;wBACvC,GAAG,cAAc,CAAC,UAAU,IAAI,QAAQ,CAAC,YAAY,gDAAgD;wBACrG,GAAG,QAAQ,CAAC,qBAAqB,CAAC,UAAU,IAAI,QAAQ,CAAC,eAAe,CAAC,YAAY,kCAAkC;wBACvH,sDAAsD,CAC7D,CAAA;YACT,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AAhVD,0DAgVC", "file": "EntityMetadataValidator.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { MissingPrimaryColumnError } from \"../error/MissingPrimaryColumnError\"\nimport { CircularRelationsError } from \"../error/CircularRelationsError\"\nimport { DepGraph } from \"../util/DepGraph\"\nimport { Driver } from \"../driver/Driver\"\nimport { DataTypeNotSupportedError } from \"../error/DataTypeNotSupportedError\"\nimport { ColumnType } from \"../driver/types/ColumnTypes\"\nimport { NoConnectionOptionError } from \"../error/NoConnectionOptionError\"\nimport { InitializedRelationError } from \"../error/InitializedRelationError\"\nimport { TypeORMError } from \"../error\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\n\n/// todo: add check if there are multiple tables with the same name\n/// todo: add checks when generated column / table names are too long for the specific driver\n// todo: type in function validation, inverse side function validation\n// todo: check on build for duplicate names, since naming checking was removed from MetadataStorage\n// todo: duplicate name checking for: table, relation, column, index, naming strategy, join tables/columns?\n// todo: check if multiple tree parent metadatas in validator\n// todo: tree decorators can be used only on closure table (validation)\n// todo: throw error if parent tree metadata was not specified in a closure table\n\n// todo: MetadataArgsStorage: type in function validation, inverse side function validation\n// todo: MetadataArgsStorage: check on build for duplicate names, since naming checking was removed from MetadataStorage\n// todo: MetadataArgsStorage: duplicate name checking for: table, relation, column, index, naming strategy, join tables/columns?\n// todo: MetadataArgsStorage: check for duplicate targets too since this check has been removed too\n// todo: check if relation decorator contains primary: true and nullable: true\n// todo: check column length, precision. scale\n// todo: MySQL index can be unique or spatial or fulltext\n\n/**\n * Validates built entity metadatas.\n */\nexport class EntityMetadataValidator {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Validates all given entity metadatas.\n     */\n    validateMany(entityMetadatas: EntityMetadata[], driver: Driver) {\n        entityMetadatas.forEach((entityMetadata) =>\n            this.validate(entityMetadata, entityMetadatas, driver),\n        )\n        this.validateDependencies(entityMetadatas)\n        this.validateEagerRelations(entityMetadatas)\n    }\n\n    /**\n     * Validates given entity metadata.\n     */\n    validate(\n        entityMetadata: EntityMetadata,\n        allEntityMetadatas: EntityMetadata[],\n        driver: Driver,\n    ) {\n        // check if table metadata has an id\n        if (!entityMetadata.primaryColumns.length && !entityMetadata.isJunction)\n            throw new MissingPrimaryColumnError(entityMetadata)\n\n        // if entity has multiple primary keys and uses custom constraint name,\n        // then all primary keys should have the same constraint name\n        if (entityMetadata.primaryColumns.length > 1) {\n            const areConstraintNamesEqual = entityMetadata.primaryColumns.every(\n                (columnMetadata, i, columnMetadatas) =>\n                    columnMetadata.primaryKeyConstraintName ===\n                    columnMetadatas[0].primaryKeyConstraintName,\n            )\n            if (!areConstraintNamesEqual) {\n                throw new TypeORMError(\n                    `Entity ${entityMetadata.name} has multiple primary columns with different constraint names. Constraint names should be the equal.`,\n                )\n            }\n        }\n\n        // validate if table is using inheritance it has a discriminator\n        // also validate if discriminator values are not empty and not repeated\n        if (\n            entityMetadata.inheritancePattern === \"STI\" ||\n            entityMetadata.tableType === \"entity-child\"\n        ) {\n            if (!entityMetadata.discriminatorColumn)\n                throw new TypeORMError(\n                    `Entity ${entityMetadata.name} using single-table inheritance, it should also have a discriminator column. Did you forget to put discriminator column options?`,\n                )\n\n            if (typeof entityMetadata.discriminatorValue === \"undefined\")\n                throw new TypeORMError(\n                    `Entity ${entityMetadata.name} has an undefined discriminator value. Discriminator value should be defined.`,\n                )\n\n            const sameDiscriminatorValueEntityMetadata =\n                allEntityMetadatas.find((metadata) => {\n                    return (\n                        metadata !== entityMetadata &&\n                        (metadata.inheritancePattern === \"STI\" ||\n                            metadata.tableType === \"entity-child\") &&\n                        metadata.tableName === entityMetadata.tableName &&\n                        metadata.discriminatorValue ===\n                            entityMetadata.discriminatorValue &&\n                        metadata.inheritanceTree.some(\n                            (parent) =>\n                                entityMetadata.inheritanceTree.indexOf(\n                                    parent,\n                                ) !== -1,\n                        )\n                    )\n                })\n            if (sameDiscriminatorValueEntityMetadata)\n                throw new TypeORMError(\n                    `Entities ${entityMetadata.name} and ${sameDiscriminatorValueEntityMetadata.name} have the same discriminator values. Make sure they are different while using the @ChildEntity decorator.`,\n                )\n        }\n\n        entityMetadata.relationCounts.forEach((relationCount) => {\n            if (\n                relationCount.relation.isManyToOne ||\n                relationCount.relation.isOneToOne\n            )\n                throw new TypeORMError(\n                    `Relation count can not be implemented on ManyToOne or OneToOne relations.`,\n                )\n        })\n\n        if (!(driver.options.type === \"mongodb\")) {\n            entityMetadata.columns\n                .filter((column) => !column.isVirtualProperty)\n                .forEach((column) => {\n                    const normalizedColumn = driver.normalizeType(\n                        column,\n                    ) as ColumnType\n                    if (\n                        driver.supportedDataTypes.indexOf(normalizedColumn) ===\n                        -1\n                    )\n                        throw new DataTypeNotSupportedError(\n                            column,\n                            normalizedColumn,\n                            driver.options.type,\n                        )\n                    if (\n                        column.length &&\n                        driver.withLengthColumnTypes.indexOf(\n                            normalizedColumn,\n                        ) === -1\n                    )\n                        throw new TypeORMError(\n                            `Column ${column.propertyName} of Entity ${entityMetadata.name} does not support length property.`,\n                        )\n                    if (\n                        column.type === \"enum\" &&\n                        !column.enum &&\n                        !column.enumName\n                    )\n                        throw new TypeORMError(\n                            `Column \"${column.propertyName}\" of Entity \"${entityMetadata.name}\" is defined as enum, but missing \"enum\" or \"enumName\" properties.`,\n                        )\n                })\n        }\n\n        if (\n            DriverUtils.isMySQLFamily(driver) ||\n            driver.options.type === \"aurora-mysql\"\n        ) {\n            const generatedColumns = entityMetadata.columns.filter(\n                (column) =>\n                    column.isGenerated && column.generationStrategy !== \"uuid\",\n            )\n            if (generatedColumns.length > 1)\n                throw new TypeORMError(\n                    `Error in ${entityMetadata.name} entity. There can be only one auto-increment column in MySql table.`,\n                )\n        }\n\n        // for mysql we are able to not define a default selected database, instead all entities can have their database\n        // defined in their decorators. To make everything work either all entities must have database define and we\n        // can live without database set in the connection options, either database in the connection options must be set\n        if (DriverUtils.isMySQLFamily(driver)) {\n            const metadatasWithDatabase = allEntityMetadatas.filter(\n                (metadata) => metadata.database,\n            )\n            if (metadatasWithDatabase.length === 0 && !driver.database)\n                throw new NoConnectionOptionError(\"database\")\n        }\n\n        if (driver.options.type === \"mssql\") {\n            const charsetColumns = entityMetadata.columns.filter(\n                (column) => column.charset,\n            )\n            if (charsetColumns.length > 1)\n                throw new TypeORMError(\n                    `Character set specifying is not supported in Sql Server`,\n                )\n        }\n\n        // Postgres supports only STORED generated columns.\n        if (driver.options.type === \"postgres\") {\n            const virtualColumn = entityMetadata.columns.find(\n                (column) =>\n                    column.asExpression &&\n                    (!column.generatedType ||\n                        column.generatedType === \"VIRTUAL\"),\n            )\n            if (virtualColumn)\n                throw new TypeORMError(\n                    `Column \"${virtualColumn.propertyName}\" of Entity \"${entityMetadata.name}\" is defined as VIRTUAL, but Postgres supports only STORED generated columns.`,\n                )\n        }\n\n        // check if relations are all without initialized properties\n        const entityInstance = entityMetadata.create(undefined, {\n            fromDeserializer: true,\n        })\n        entityMetadata.relations.forEach((relation) => {\n            if (relation.isManyToMany || relation.isOneToMany) {\n                // we skip relations for which persistence is disabled since initialization in them cannot harm somehow\n                if (relation.persistenceEnabled === false) return\n\n                // get entity relation value and check if its an array\n                const relationInitializedValue =\n                    relation.getEntityValue(entityInstance)\n                if (Array.isArray(relationInitializedValue))\n                    throw new InitializedRelationError(relation)\n            }\n        })\n\n        // validate relations\n        entityMetadata.relations.forEach((relation) => {\n            // check OnDeleteTypes\n            if (\n                driver.supportedOnDeleteTypes &&\n                relation.onDelete &&\n                !driver.supportedOnDeleteTypes.includes(relation.onDelete)\n            ) {\n                throw new TypeORMError(\n                    `OnDeleteType \"${relation.onDelete}\" is not supported for ${driver.options.type}!`,\n                )\n            }\n\n            // check OnUpdateTypes\n            if (\n                driver.supportedOnUpdateTypes &&\n                relation.onUpdate &&\n                !driver.supportedOnUpdateTypes.includes(relation.onUpdate)\n            ) {\n                throw new TypeORMError(\n                    `OnUpdateType \"${relation.onUpdate}\" is not valid for ${driver.options.type}!`,\n                )\n            }\n\n            // check join tables:\n            // using JoinTable is possible only on one side of the many-to-many relation\n            // todo(dima): fix\n            // if (relation.joinTable) {\n            //     if (!relation.isManyToMany)\n            //         throw new UsingJoinTableIsNotAllowedError(entityMetadata, relation);\n            //     // if there is inverse side of the relation, then check if it does not have join table too\n            //     if (relation.hasInverseSide && relation.inverseRelation.joinTable)\n            //         throw new UsingJoinTableOnlyOnOneSideAllowedError(entityMetadata, relation);\n            // }\n            // check join columns:\n            // using JoinColumn is possible only on one side of the relation and on one-to-one, many-to-one relation types\n            // first check if relation is one-to-one or many-to-one\n            // todo(dima): fix\n            /*if (relation.joinColumn) {\n\n                // join column can be applied only on one-to-one and many-to-one relations\n                if (!relation.isOneToOne && !relation.isManyToOne)\n                    throw new UsingJoinColumnIsNotAllowedError(entityMetadata, relation);\n\n                // if there is inverse side of the relation, then check if it does not have join table too\n                if (relation.hasInverseSide && relation.inverseRelation.joinColumn && relation.isOneToOne)\n                    throw new UsingJoinColumnOnlyOnOneSideAllowedError(entityMetadata, relation);\n\n                // check if join column really has referenced column\n                if (relation.joinColumn && !relation.joinColumn.referencedColumn)\n                    throw new TypeORMError(`Join column does not have referenced column set`);\n\n            }\n\n            // if its a one-to-one relation and JoinColumn is missing on both sides of the relation\n            // or its one-side relation without JoinColumn we should give an error\n            if (!relation.joinColumn && relation.isOneToOne && (!relation.hasInverseSide || !relation.inverseRelation.joinColumn))\n                throw new MissingJoinColumnError(entityMetadata, relation);*/\n            // if its a many-to-many relation and JoinTable is missing on both sides of the relation\n            // or its one-side relation without JoinTable we should give an error\n            // todo(dima): fix it\n            // if (!relation.joinTable && relation.isManyToMany && (!relation.hasInverseSide || !relation.inverseRelation.joinTable))\n            //     throw new MissingJoinTableError(entityMetadata, relation);\n            // todo: validate if its one-to-one and side which does not have join column MUST have inverse side\n            // todo: validate if its many-to-many and side which does not have join table MUST have inverse side\n            // todo: if there is a relation, and inverse side is specified only on one side, shall we give error\n            // todo: with message like: \"Inverse side is specified only on one side of the relationship. Specify on other side too to prevent confusion\".\n            // todo: add validation if there two entities with the same target, and show error message with description of the problem (maybe file was renamed/moved but left in output directory)\n            // todo: check if there are multiple columns on the same column applied.\n            // todo: check column type if is missing in relational databases (throw new TypeORMError(`Column type of ${type} cannot be determined.`);)\n            // todo: include driver-specific checks. for example in mongodb empty prefixes are not allowed\n            // todo: if multiple columns with same name - throw exception, including cases when columns are in embeds with same prefixes or without prefix at all\n            // todo: if multiple primary key used, at least one of them must be unique or @Index decorator must be set on entity\n            // todo: check if entity with duplicate names, some decorators exist\n        })\n\n        // make sure cascade remove is not set for both sides of relationships (can be set in OneToOne decorators)\n        entityMetadata.relations.forEach((relation) => {\n            const isCircularCascadeRemove =\n                relation.isCascadeRemove &&\n                relation.inverseRelation &&\n                relation.inverseRelation!.isCascadeRemove\n            if (isCircularCascadeRemove)\n                throw new TypeORMError(\n                    `Relation ${entityMetadata.name}#${\n                        relation.propertyName\n                    } and ${relation.inverseRelation!.entityMetadata.name}#${\n                        relation.inverseRelation!.propertyName\n                    } both has cascade remove set. ` +\n                        `This may lead to unexpected circular removals. Please set cascade remove only from one side of relationship.`,\n                )\n        }) // todo: maybe better just deny removal from one to one relation without join column?\n\n        entityMetadata.eagerRelations.forEach((relation) => {})\n    }\n\n    /**\n     * Validates dependencies of the entity metadatas.\n     */\n    protected validateDependencies(entityMetadatas: EntityMetadata[]) {\n        const graph = new DepGraph()\n        entityMetadatas.forEach((entityMetadata) => {\n            graph.addNode(entityMetadata.name)\n        })\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.relationsWithJoinColumns\n                .filter((relation) => !relation.isNullable)\n                .forEach((relation) => {\n                    graph.addDependency(\n                        entityMetadata.name,\n                        relation.inverseEntityMetadata.name,\n                    )\n                })\n        })\n        try {\n            graph.overallOrder()\n        } catch (err) {\n            throw new CircularRelationsError(\n                err.toString().replace(\"Error: Dependency Cycle Found: \", \"\"),\n            )\n        }\n    }\n\n    /**\n     * Validates eager relations to prevent circular dependency in them.\n     */\n    protected validateEagerRelations(entityMetadatas: EntityMetadata[]) {\n        entityMetadatas.forEach((entityMetadata) => {\n            entityMetadata.eagerRelations.forEach((relation) => {\n                if (\n                    relation.inverseRelation &&\n                    relation.inverseRelation.isEager\n                )\n                    throw new TypeORMError(\n                        `Circular eager relations are disallowed. ` +\n                            `${entityMetadata.targetName}#${relation.propertyPath} contains \"eager: true\", and its inverse side ` +\n                            `${relation.inverseEntityMetadata.targetName}#${relation.inverseRelation.propertyPath} contains \"eager: true\" as well.` +\n                            ` Remove \"eager: true\" from one side of the relation.`,\n                    )\n            })\n        })\n    }\n}\n"], "sourceRoot": ".."}