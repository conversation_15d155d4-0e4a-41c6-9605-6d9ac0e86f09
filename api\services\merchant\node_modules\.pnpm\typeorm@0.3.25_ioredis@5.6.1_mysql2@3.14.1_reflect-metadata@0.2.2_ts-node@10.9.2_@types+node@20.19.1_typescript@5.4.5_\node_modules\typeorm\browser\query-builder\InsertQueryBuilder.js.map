{"version": 3, "sources": ["../browser/src/query-builder/InsertQueryBuilder.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,EAAE,IAAI,MAAM,EAAE,MAAM,MAAM,CAAA;AAInC,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AAGnD,OAAO,EAAE,YAAY,EAAE,MAAM,UAAU,CAAA;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,mCAAmC,CAAA;AAC5E,OAAO,EAAE,mCAAmC,EAAE,MAAM,8CAA8C,CAAA;AAElG,OAAO,EAAE,iBAAiB,EAAE,MAAM,iCAAiC,CAAA;AACnE,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAA;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AAEjD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AACpD,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAA;AAG/E;;GAEG;AACH,MAAM,OAAO,kBAEX,SAAQ,YAAoB;IAF9B;;QAGa,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAA;IA68C7D,CAAC;IA38CG,4EAA4E;IAC5E,6BAA6B;IAC7B,4EAA4E;IAE5E;;OAEG;IACH,QAAQ;QACJ,IAAI,GAAG,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;QAC9B,GAAG,IAAI,IAAI,CAAC,mBAAmB,EAAE,CAAA;QACjC,GAAG,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAA;QACpC,OAAO,IAAI,CAAC,oCAAoC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAA;IAChE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACT,+BAA+B;QAC/B,MAAM,SAAS,GAAoB,IAAI,CAAC,YAAY,EAAE,CAAA;QACtD,kCAAkC;QAElC,kEAAkE;QAClE,YAAY;QACZ,EAAE;QACF,sEAAsE;QACtE,kEAAkE;QAClE,oEAAoE;QACpE,uCAAuC;QACvC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,IAAI,YAAY,EAAE,CAAA;QAErD,wCAAwC;QACxC,mCAAmC;QACnC,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAA;QAC5C,IAAI,sBAAsB,GAAY,KAAK,CAAA;QAE3C,IAAI,CAAC;YACD,sCAAsC;YACtC,IACI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,IAAI;gBAC1C,WAAW,CAAC,mBAAmB,KAAK,KAAK,EAC3C,CAAC;gBACC,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAA;gBACpC,sBAAsB,GAAG,IAAI,CAAA;YACjC,CAAC;YAED,sCAAsC;YAEtC,6DAA6D;YAC7D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,eAAe,GAAG,IAAI,iBAAiB,EAAE,CAAA;gBAC/C,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBAC3B,WAAW,CAAC,WAAW,CAAC,0BAA0B,CAC9C,eAAe,EACf,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,QAAQ,CACX,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,MAAM,eAAe,CAAC,IAAI,EAAE,CAAA;YAChC,CAAC;YAED,IAAI,UAAU,GAAkB,IAAI,CAAA;YACpC,IAAI,eAAe,GAAkB,IAAI,CAAA;YAEzC,yFAAyF;YACzF,gDAAgD;YAChD,MAAM,6BAA6B,GAC/B,IAAI,6BAA6B,CAC7B,WAAW,EACX,IAAI,CAAC,aAAa,CACrB,CAAA;YAEL,MAAM,gBAAgB,GAAqB,EAAE,CAAA;YAE7C,IACI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;oBACpD,gBAAgB,CAAC,IAAI,CACjB,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,2BAA2B,CACjE,UAAU,CACb,CACJ,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,IACI,CAAC,CACG,SAAS,CAAC,MAAM,GAAG,CAAC;oBACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CACnD,EACH,CAAC;oBACC,IAAI,CAAC,aAAa,CAAC,qBAAqB;wBACpC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,4BAA4B,EAAE,CAAA;gBAC7E,CAAC;gBAED,gBAAgB,CAAC,IAAI,CACjB,GAAG,IAAI,CAAC,aAAa,CAAC,qBAAqB,CAAC,MAAM,CAC9C,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,CACvC,CACJ,CAAA;YACL,CAAC;YAED,IACI,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;gBACC,UAAU,GACN,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,6BAA6B,CAC3B,cAAc,EACd,gBAAgB,CACnB,CAAA;gBACD,eAAe,GAAG,4BAA4B,CAAA;YAClD,CAAC;YACD,mDAAmD;YAEnD,gBAAgB;YAChB,iDAAiD;YACjD,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAA;YAC5D,oDAAoD;YAEpD,gDAAgD;YAChD,MAAM,UAAU,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC,CAAA;YAC3D,MAAM,GAAG,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAE7D,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;YAElE,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YAEnD,mDAAmD;YAEnD,kFAAkF;YAClF,IACI,IAAI,CAAC,aAAa,CAAC,YAAY,KAAK,IAAI;gBACxC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,oCAAoC;gBACpC,MAAM,6BAA6B,CAAC,MAAM,CACtC,YAAY,EACZ,SAAS,CACZ,CAAA;gBACD,uCAAuC;YAC3C,CAAC;YAED,4DAA4D;YAC5D,IACI,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,IAAI;gBACzC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAC3C,CAAC;gBACC,MAAM,eAAe,GAAG,IAAI,iBAAiB,EAAE,CAAA;gBAC/C,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;oBAC3B,WAAW,CAAC,WAAW,CAAC,yBAAyB,CAC7C,eAAe,EACf,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,EACtC,QAAQ,CACX,CAAA;gBACL,CAAC,CAAC,CAAA;gBACF,MAAM,eAAe,CAAC,IAAI,EAAE,CAAA;YAChC,CAAC;YAED,qCAAqC;YACrC,2BAA2B;YAC3B,IAAI,sBAAsB,EAAE,CAAC;gBACzB,MAAM,WAAW,CAAC,iBAAiB,EAAE,CAAA;YACzC,CAAC;YACD,8BAA8B;YAE9B,OAAO,YAAY,CAAA;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,wCAAwC;YACxC,IAAI,sBAAsB,EAAE,CAAC;gBACzB,IAAI,CAAC;oBACD,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAA;gBAC3C,CAAC;gBAAC,OAAO,aAAa,EAAE,CAAC,CAAA,CAAC;YAC9B,CAAC;YACD,MAAM,KAAK,CAAA;QACf,CAAC;gBAAS,CAAC;YACP,yCAAyC;YACzC,IAAI,WAAW,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnC,wCAAwC;gBACxC,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC/B,CAAC;YACD,4CAA4C;YAC5C,2CAA2C;QAC/C,CAAC;IACL,CAAC;IAED,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,IAAI,CACA,YAA6B,EAC7B,OAAkB;QAElB,YAAY,GAAG,eAAe,CAAC,cAAc,CAAC,YAAY,CAAC;YACvD,CAAC,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI;YAC3B,CAAC,CAAC,YAAY,CAAA;QAClB,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;QAC1C,IAAI,CAAC,aAAa,CAAC,aAAa,GAAG,OAAO,IAAI,EAAE,CAAA;QAChD,OAAO,IAAoC,CAAA;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM,CACF,MAEsC;QAEtC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,MAAM,CAAA;QACrC,OAAO,IAAI,CAAA;IACf,CAAC;IAmBD;;OAEG;IACH,MAAM,CAAC,MAAyB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAA;IACjC,CAAC;IAmBD;;OAEG;IACH,SAAS,CAAC,SAA4B;QAClC,mDAAmD;QACnD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5D,MAAM,IAAI,mCAAmC,EAAE,CAAA;QACnD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,SAAS,CAAA;QACxC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,YAAY,CAAC,OAAgB;QACzB,IAAI,CAAC,aAAa,CAAC,YAAY,GAAG,OAAO,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,SAAiB;QACxB,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,SAAS,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IAED;;OAEG;IACH,QAAQ,CAAC,YAA8B,IAAI;QACvC,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,CAAC,SAAS,CAAA;QACzC,OAAO,IAAI,CAAA;IACf,CAAC;IA0BD;;OAEG;IACH,QAAQ,CACJ,oBAMc,EACd,cAAkC,EAClC,eAAuC;QAEvC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,eAAe,EAAE,kBAAkB,IAAI,EAAE,CAAA;QACvE,IAAI,MAAiC,CAAA;QACrC,IAAI,KAAK,EAAE,CAAC;YACR,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAA;YAC/C,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS;gBAC7D,MAAM,GAAG,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,CAAC,CAAA;QAC3D,CAAC;QACD,IAAI,UAAU;YAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAA;QAE9C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,oBAAoB,CAAC,EAAE,CAAC;YACvC,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG;gBAC1B,QAAQ,EAAE,oBAAoB,EAAE,eAAe;gBAC/C,OAAO,EAAE,oBAAoB,EAAE,OAAO;gBACtC,SAAS,EAAE,oBAAoB,EAAE,SAAS;gBAC1C,2BAA2B,EACvB,eAAe,EAAE,2BAA2B;gBAChD,UAAU,EAAE,eAAe,EAAE,UAAU;gBACvC,kBAAkB,EAAE,MAAM;aAC7B,CAAA;YACD,OAAO,IAAI,CAAA;QACf,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG;YAC1B,SAAS,EAAE,oBAAoB;YAC/B,QAAQ,EAAE,cAAc;YACxB,2BAA2B,EACvB,eAAe,EAAE,2BAA2B;YAChD,cAAc,EAAE,eAAe,EAAE,cAAc;YAC/C,UAAU,EAAE,eAAe,EAAE,UAAU;YACvC,kBAAkB,EAAE,MAAM;SAC7B,CAAA;QACD,OAAO,IAAI,CAAA;IACf,CAAC;IAED,4EAA4E;IAC5E,oBAAoB;IACpB,4EAA4E;IAE5E;;OAEG;IACO,sBAAsB;QAC5B,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC7D,IACI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,IAAI,YAAY,CAAC;gBACrD,YAAY;gBAChB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAChD,YAAY,CACf;gBAED,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAA;QAC3C,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAC5D,MAAM,gBAAgB,GAClB,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAClC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;YACzB,CAAC,CAAC,SAAS,CAAA;QACnB,MAAM,gBAAgB,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAA,CAAC,qIAAqI;QAC5L,MAAM,mBAAmB,GACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;YAChD,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC;YAC1B,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAA,CAAC,wDAAwD;QAC3G,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAC5D,IAAI,KAAK,GAAG,SAAS,CAAA;QAErB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,KAAK,aAAa,EAAE,CAAC;YAC5D,KAAK,GAAG,SAAS,CAAA;QACrB,CAAC;QAED,IACI,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxD,CAAC;YACC,KAAK,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;QAC/D,CAAC;QAED,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAA;QAE5B,IACI,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,gBAAgB,EAAE;YACtC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EACtD,CAAC;YACC,KAAK,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAA;QAClC,CAAC;QAED,yBAAyB;QACzB,IAAI,iBAAiB,EAAE,CAAC;YACpB,KAAK,IAAI,IAAI,iBAAiB,GAAG,CAAA;QACrC,CAAC;aAAM,CAAC;YACJ,IACI,CAAC,gBAAgB;gBACjB,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;oBAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC;gBAE3D,oDAAoD;gBACpD,KAAK,IAAI,IAAI,CAAA;QACrB,CAAC;QAED,wBAAwB;QACxB,IACI,mBAAmB;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;YACC,KAAK,IAAI,WAAW,mBAAmB,EAAE,CAAA;QAC7C,CAAC;QAED,wBAAwB;QACxB,IAAI,gBAAgB,EAAE,CAAC;YACnB,IACI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;gBAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC;gBAClD,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM,GAAG,CAAC,EAChC,CAAC;gBACC,KAAK,IAAI,IAAI,gBAAgB,EAAE,CAAA;YACnC,CAAC;iBAAM,CAAC;gBACJ,KAAK,IAAI,WAAW,gBAAgB,EAAE,CAAA;YAC1C,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,IACI,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,EACxD,CAAC;gBACC,oDAAoD;gBACpD,KAAK,IAAI,YAAY,CAAA;YACzB,CAAC;iBAAM,CAAC;gBACJ,KAAK,IAAI,iBAAiB,CAAA;YAC9B,CAAC;QACL,CAAC;QACD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,KAAK,aAAa,EAAE,CAAC;YAC5D,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAChD,uBAAuB,CAC1B,EACH,CAAC;gBACC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9B,KAAK,IAAI,0BAA0B,CAAA;gBACvC,CAAC;qBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;oBACvC,KAAK,IAAI,gBAAgB,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,CAAA;gBAC7D,CAAC;qBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBACrC,MAAM,EACF,SAAS,EACT,OAAO,EACP,QAAQ,EACR,2BAA2B,EAC3B,cAAc,GACjB,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;oBAE/B,IAAI,cAAc,GAAG,aAAa,CAAA;oBAElC,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC1B,cAAc,IAAI,MAAM,QAAQ;6BAC3B,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;6BACpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAA;wBACnB,IACI,cAAc;4BACd,CAAC,WAAW,CAAC,gBAAgB,CACzB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB,EACH,CAAC;4BACC,MAAM,IAAI,YAAY,CAClB,uEAAuE,CAC1E,CAAA;wBACL,CAAC;wBACD,IACI,cAAc;4BACd,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EACtD,CAAC;4BACC,cAAc,IAAI,YAAY,cAAc,IAAI,CAAA;wBACpD,CAAC;oBACL,CAAC;yBAAM,IAAI,QAAQ,EAAE,CAAC;wBAClB,cAAc,IAAI,kBAAkB,IAAI,CAAC,MAAM,CAC3C,QAAQ,CACX,EAAE,CAAA;oBACP,CAAC;oBAED,MAAM,UAAU,GAAa,EAAE,CAAA;oBAE/B,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC3B,UAAU,CAAC,IAAI,CACX,GAAG,SAAS,CAAC,GAAG,CACZ,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,IAAI,CAAC,MAAM,CACV,MAAM,CACT,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAC5C,CACJ,CAAA;oBACL,CAAC;yBAAM,IAAI,OAAO,EAAE,CAAC;wBACjB,UAAU,CAAC,IAAI,CACX,GAAG,OAAO,CAAC,GAAG,CACV,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAC5C,CACJ,CAAA;oBACL,CAAC;oBAED,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACxB,KAAK,IAAI,IAAI,cAAc,iBAAiB,CAAA;wBAE5C,UAAU,CAAC,IAAI,CACX,GAAG,IAAI,CAAC,aAAa;6BAChB,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAC/B,CAAC,MAAM,EAAE,EAAE,CACP,MAAM,CAAC,YAAY;4BACnB,CAAC,SAAS,EAAE,QAAQ,CAChB,MAAM,CAAC,YAAY,CACtB;4BACD,CAAC,CACG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;iCAC1B,IAAI,KAAK,QAAQ;gCAClB,IAAI,CAAC,YAAY,EAAE,CAAC,MAAM;oCACtB,CAAC,CAAC;gCACV,WAAW,CAAC,cAAc,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;gCACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;qCACzB,IAAI,KAAK,KAAK;gCACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;qCACzB,IAAI,KAAK,SAAS,CAC1B,CACR;6BACA,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,IAAI,CAAC,MAAM,CACV,MAAM,CAAC,YAAY,CACtB,YAAY,CACpB,CACR,CAAA;wBAED,KAAK,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAClC,CAAC;oBAED,IACI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;wBACxB,2BAA2B,EAC7B,CAAC;wBACC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,KAAK,EAAE,CAAA;wBACrD,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAc,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;4BACnD,IAAI,EAAE,IAAI;4BACV,SAAS,EAAE,GAAG,gBAAgB,IAAI,IAAI,CAAC,MAAM,CACzC,MAAM,CACT,8BAA8B,IAAI,CAAC,MAAM,CACtC,MAAM,CACT,EAAE;yBACN,CAAC,CAAC,CAAA;wBACH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC;4BAChD,IAAI,EAAE,KAAK;4BACX,SAAS,EAAE,MAAM;yBACpB,CAAC,CAAA;oBACN,CAAC;oBACD,IACI,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;wBACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB;wBAC9C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,CAAC,MAAM;4BACjD,CAAC,EACP,CAAC;wBACC,KAAK,IAAI,UAAU,IAAI,CAAC,+BAA+B,EAAE,EAAE,CAAA;oBAC/D,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAChD,yBAAyB,CAC5B,EACH,CAAC;gBACC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9B,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;oBAE1D,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC3B,KAAK,IAAI,2BAA2B,CAAA;wBACpC,KAAK,IAAI,SAAS;6BACb,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,IAAI,CAAC,MAAM,CACV,MAAM,CACT,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAC3C;6BACA,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,KAAK,IAAI,GAAG,CAAA;oBAChB,CAAC;yBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;wBAChC,KAAK,IAAI,2BAA2B,CAAA;wBACpC,KAAK,IAAI,OAAO;6BACX,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,MAAM,EAAE,CAC5C;6BACA,IAAI,CAAC,IAAI,CAAC,CAAA;wBACf,KAAK,IAAI,GAAG,CAAA;oBAChB,CAAC;gBACL,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBAC9B,MAAM,IAAI,YAAY,CAClB,0DAA0D,CAC7D,CAAA;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,IACI,mBAAmB;YACnB,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACjD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;gBAChD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,aAAa;gBACrD,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EACxD,CAAC;YACC,KAAK,IAAI,cAAc,mBAAmB,EAAE,CAAA;QAChD,CAAC;QAED,IACI,mBAAmB;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;YACC,KAAK,IAAI,gBAAgB,mBAAmB,EAAE,CAAA;QAClD,CAAC;QAED,0GAA0G;QAC1G,oMAAoM;QACpM,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;YAC/C,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YACzC,IAAI,CAAC,aAAa;iBACb,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAC3C,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC;gBACvC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CACpC,MAAM,CAAC,YAAY,CACtB,KAAK,CAAC,CAAC;gBACV,CAAC,CAAC,MAAM,CAAC,QAAQ,CACxB;iBACA,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CACb,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,CACjD,EACP,CAAC;YACC,KAAK,GAAG,uBAAuB,SAAS,QAAQ,KAAK,yBAAyB,SAAS,MAAM,CAAA;QACjG,CAAC;QAED,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,kBAAkB;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YAAE,OAAO,EAAE,CAAA;QAEzD,OAAO,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CACxD,CAAC,MAAM,EAAE,EAAE;YACP,oFAAoF;YACpF,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM;gBACvC,OAAO,CACH,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CACpC,MAAM,CAAC,YAAY,CACtB,KAAK,CAAC,CAAC,CACX,CAAA;YAEL,yDAAyD;YACzD,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACnB,OAAO,KAAK,CAAA;YAChB,CAAC;YAED,wFAAwF;YACxF,+GAA+G;YAC/G,IACI,MAAM,CAAC,WAAW;gBAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW;gBACzC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;gBACpD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC;gBACnD,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBAClD,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC;gBACzD,CAAC,CACG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC/C,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC,CACjD;gBAED,OAAO,KAAK,CAAA;YAEhB,OAAO,IAAI,CAAA;QACf,CAAC,CACJ,CAAA;IACL,CAAC;IAED;;OAEG;IACO,2BAA2B;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzC,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;YAClB,OAAO,OAAO;iBACT,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,CAAA;QAEnB,uFAAuF;QACvF,gGAAgG;QAChG,IACI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW;YAC1C,CAAC,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,EAC1C,CAAC;YACC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;YACrC,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;gBACtB,OAAO,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;qBAC3B,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;qBAC5C,IAAI,CAAC,IAAI,CAAC,CAAA;QACvB,CAAC;QAED,iDAAiD;QACjD,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa;aAClC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;aAC5C,IAAI,CAAC,IAAI,CAAC,CAAA;IACnB,CAAC;IAED;;OAEG;IACO,sBAAsB;QAC5B,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEzC,gFAAgF;QAChF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,UAAU,GAAG,EAAE,CAAA;YACnB,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAC1C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACpC,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;wBACpB,IACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;4BAChD,SAAS,CAAC,MAAM,GAAG,CAAC,EACtB,CAAC;4BACC,UAAU,IAAI,UAAU,CAAA;wBAC5B,CAAC;6BAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;4BAC7C,SAAS,CAAC,MAAM,GAAG,CAAC,EACtB,CAAC;4BACC,UAAU,IAAI,UAAU,CAAA;wBAC5B,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,GAAG,CAAA;wBACrB,CAAC;oBACL,CAAC;oBAED,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAC1C,SAAS,EACT,aAAa,EACb,MAAM,CACT,CAAA;oBAED,IAAI,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrC,IAAI,aAAa,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACzC,IACI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtC;gCACD,SAAS,CAAC,MAAM,GAAG,CAAC,EACtB,CAAC;gCACC,UAAU;oCACN,QAAQ;wCACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAA;4BAC7C,CAAC;iCAAM,CAAC;gCACJ,UAAU,IAAI,GAAG,CAAA;4BACrB,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,IACI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtC;gCACD,SAAS,CAAC,MAAM,GAAG,CAAC,EACtB,CAAC;gCACC,UAAU;oCACN,QAAQ;wCACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc;wCACrC,aAAa,CAAA;4BACrB,CAAC;iCAAM,CAAC;gCACJ,UAAU,IAAI,KAAK,CAAA;4BACvB,CAAC;wBACL,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,UAAU,IAAI,IAAI,CAAA;oBACtB,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACF,IAAI,UAAU,KAAK,IAAI;gBAAE,OAAO,EAAE,CAAA;YAElC,OAAO,UAAU,CAAA;QACrB,CAAC;aAAM,CAAC;YACJ,8BAA8B;YAC9B,kCAAkC;YAClC,IAAI,UAAU,GAAG,EAAE,CAAA;YAEnB,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,cAAc,EAAE,EAAE;gBAC3C,MAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;gBACrC,OAAO,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,WAAW,EAAE,EAAE;oBACxC,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;wBACpB,UAAU,IAAI,GAAG,CAAA;oBACrB,CAAC;oBAED,MAAM,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAA;oBAElC,yCAAyC;oBACzC,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;wBAC9B,UAAU,IAAI,KAAK,EAAE,CAAA;wBAErB,sEAAsE;oBAC1E,CAAC;yBAAM,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;wBAC7B,IACI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;4BAC7C,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;4BACzB,WAAW,CAAC,cAAc,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CACzB;4BACD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;4BAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;4BACC,UAAU,IAAI,MAAM,CAAA;wBACxB,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,SAAS,CAAA;wBAC3B,CAAC;oBACL,CAAC;yBAAM,IACH,KAAK,KAAK,IAAI;wBACd,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;wBACC,+BAA+B;oBACnC,CAAC;yBAAM,CAAC;wBACJ,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;oBAC7C,CAAC;oBAED,IAAI,WAAW,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACnD,IAAI,cAAc,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BAC1C,UAAU,IAAI,GAAG,CAAA;wBACrB,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,KAAK,CAAA;wBACvB,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,UAAU,IAAI,IAAI,CAAA;oBACtB,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;YACF,IAAI,UAAU,KAAK,IAAI;gBAAE,OAAO,EAAE,CAAA;YAClC,OAAO,UAAU,CAAA;QACrB,CAAC;IACL,CAAC;IAED;;OAEG;IACO,YAAY;QAClB,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YAC3C,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA;QAEvC,IAAI,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;YAClD,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;QAEzC,MAAM,IAAI,wBAAwB,EAAE,CAAA;IACxC,CAAC;IAED;;;;OAIG;IACO,iCAAiC,CACvC,MAAsB;QAEtB,OAAO,CACH,MAAM,CAAC,SAAS;YAChB,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,WAAW;YACzC,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CACpB,CAAC,QAAQ,EAAE,EAAE,CACT,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,SAAS;gBAC7C,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,IAAI,CAC/C,CACJ,CAAA;IACL,CAAC;IAED;;OAEG;IACO,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC;YACnE,MAAM,IAAI,YAAY,CAClB,sEAAsE,CACzE,CAAA;QAEL,IACI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU;YACvC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,KAAK,YAAY,EACzD,CAAC;YACC,MAAM,IAAI,YAAY,CAClB,gBAAgB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,+CAA+C,CACxG,CAAA;QACL,CAAC;QACD,kDAAkD;QAClD,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC,CAAA;QAC5D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1C,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QACzC,MAAM,iBAAiB,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAA;QAE5D,IAAI,KAAK,GAAG,cAAc,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAA;QAEhE,MAAM,gBAAgB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAA;QAEvD,MAAM,qBAAqB,GACvB,IAAI,CAAC,+BAA+B,CAAC,gBAAgB,CAAC,CAAA;QAE1D,KAAK,IAAI,IAAI,qBAAqB,EAAE,CAAA;QAEpC,qBAAqB;QACrB,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAA;YAC7D,IAAI,UAAU,EAAE,CAAC;gBACb,KAAK,IAAI,QAAQ,UAAU,IAAI,IAAI,CAAC,MAAM,CACtC,UAAU,CAAC,YAAY,CAC1B,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAClC,UAAU,CAAC,YAAY,CAC1B,GAAG,CAAA;YACR,CAAC;iBAAM,CAAC;gBACJ,KAAK,IAAI,OAAO,IAAI,CAAC,aAAa;qBAC7B,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxC,OAAO,IAAI,MAAM,CAAC,OAAO;yBACpB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;wBACZ,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CAC/B,MAAM,CAAC,YAAY,CACtB,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAClC,MAAM,CAAC,YAAY,CACtB,EAAE,CAAA;oBACP,CAAC,CAAC;yBACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;gBACzB,CAAC,CAAC;qBACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA;YACxB,CAAC;QACL,CAAC;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YACrC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;YAEhE,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,IAAI,YAAY,CAClB,oEAAoE,CACvE,CAAA;YACL,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1B,KAAK,IAAI,QAAQ,QAAQ;qBACpB,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CACxB,MAAM,CACT,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD;qBACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;YACzB,CAAC;iBAAM,IAAI,QAAQ,EAAE,CAAC;gBAClB,KAAK,IAAI,QAAQ,UAAU,IAAI,IAAI,CAAC,MAAM,CACtC,QAAQ,CACX,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAA;YACvD,CAAC;iBAAM,CAAC;gBACJ,KAAK,IAAI,OAAO,IAAI,CAAC,aAAa;qBAC7B,SAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;oBACxC,OAAO,IAAI,MAAM,CAAC,OAAO;yBACpB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;wBACZ,OAAO,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CAC/B,MAAM,CAAC,YAAY,CACtB,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAClC,MAAM,CAAC,YAAY,CACtB,EAAE,CAAA;oBACP,CAAC,CAAC;yBACD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA;gBACzB,CAAC,CAAC;qBACD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAA;YACxB,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;YAC9B,MAAM,EACF,SAAS,EACT,OAAO,EACP,QAAQ,EACR,2BAA2B,GAC9B,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA;YAC/B,IAAI,gBAAgB,GAAG,EAAE,CAAA;YAEzB,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC3B,gBAAgB,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC;oBACtC,EAAE,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;qBAChD,GAAG,CACA,CAAC,MAAM,EAAE,EAAE,CACP,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CACxB,MAAM,CACT,MAAM,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CACvD;qBACA,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;YAED,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,2BAA2B,EAAE,CAAC;gBAC1D,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,KAAK,EAAE,CAAA;gBACrD,MAAM,MAAM,GAAG,SAAS,CAAC,GAAG,CAAc,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;oBACnD,IAAI,EAAE,IAAI;oBACV,SAAS,EAAE;wBACP,QAAQ,EAAE,UAAU;wBACpB,UAAU,EAAE;4BACR,GAAG,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;4BACtC,GAAG,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;yBAC/C;qBACJ;iBACJ,CAAC,CAAC,CAAA;gBACH,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC;oBAChD,IAAI,EAAE,KAAK;oBACX,SAAS,EAAE,MAAM;iBACpB,CAAC,CAAA;YACN,CAAC;YACD,MAAM,cAAc,GAAG,IAAI,CAAC,+BAA+B,EAAE,CAAA;YAC7D,IAAI,gBAAgB,CAAC,IAAI,EAAE,EAAE,CAAC;gBAC1B,IACI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;oBAC5C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC;oBAClD,cAAc,IAAI,EAAE,EACtB,CAAC;oBACC,KAAK,IAAI,qBAAqB,cAAc,oBAAoB,gBAAgB,EAAE,CAAA;gBACtF,CAAC;qBAAM,CAAC;oBACJ,KAAK,IAAI,iCAAiC,gBAAgB,EAAE,CAAA;oBAC5D,IAAI,cAAc,IAAI,EAAE,EAAE,CAAC;wBACvB,KAAK,IAAI,UAAU,cAAc,EAAE,CAAA;oBACvC,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAClB,IAAI,CAAC,qCAAqC,CAAC,gBAAgB,CAAC,CAAA;QAChE,MAAM,mBAAmB,GACrB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;YAC3C,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC;YAC1C,CAAC,CAAC,IAAI,CAAA;QAEd,KAAK,IAAI,+BAA+B,CAAA;QAExC,yBAAyB;QACzB,IAAI,iBAAiB,EAAE,CAAC;YACpB,KAAK,IAAI,IAAI,iBAAiB,GAAG,CAAA;QACrC,CAAC;QAED,wBAAwB;QACxB,IAAI,gBAAgB,EAAE,CAAC;YACnB,KAAK,IAAI,WAAW,gBAAgB,EAAE,CAAA;QAC1C,CAAC;QAED,wBAAwB;QACxB,IACI,mBAAmB;YACnB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;YACC,KAAK,IAAI,WAAW,mBAAmB,EAAE,CAAA;QAC7C,CAAC;QACD,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YAClD,KAAK,IAAI,GAAG,CAAA;QAChB,CAAC;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACO,+BAA+B,CACrC,gBAAwB;QAExB,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAA;QACrC,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEzC,IAAI,UAAU,GAAG,SAAS,CAAA;QAC1B,gFAAgF;QAChF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;gBAClD,UAAU,IAAI,SAAS,CAAA;YAC3B,CAAC;YACD,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAC1C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;oBACpC,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;wBACpB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;4BAClD,UAAU,IAAI,GAAG,CAAA;wBACrB,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,SAAS,CAAA;wBAC3B,CAAC;oBACL,CAAC;oBAED,MAAM,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;oBAE7C,IACI,KAAK,KAAK,SAAS;wBACnB,CAAC,CACG,MAAM,CAAC,WAAW;4BAClB,MAAM,CAAC,kBAAkB,KAAK,MAAM;4BACpC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,yBAAyB,EAAE,CACtD,EACH,CAAC;wBACC,IACI,MAAM,CAAC,OAAO,KAAK,SAAS;4BAC5B,MAAM,CAAC,OAAO,KAAK,IAAI,EACzB,CAAC;4BACC,2CAA2C;4BAC3C,UAAU;gCACN,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;wBACvD,CAAC;6BAAM,CAAC;4BACJ,UAAU,IAAI,MAAM,CAAA,CAAC,2DAA2D;wBACpF,CAAC;oBACL,CAAC;yBAAM,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;wBACxB,UAAU,IAAI,MAAM,CAAA;oBACxB,CAAC;yBAAM,CAAC;wBACJ,UAAU,IAAI,IAAI,CAAC,2BAA2B,CAC1C,SAAS,EACT,aAAa,EACb,MAAM,CACT,CAAA;oBACL,CAAC;oBAED,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;wBAC/C,UAAU,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAA;oBAE3D,IAAI,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACrC,IAAI,aAAa,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACzC,IACI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtC,EACH,CAAC;gCACC,UAAU;oCACN,QAAQ;wCACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc,CAAA;4BAC7C,CAAC;iCAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;gCACC,UAAU,IAAI,GAAG,CAAA;4BACrB,CAAC;wBACL,CAAC;6BAAM,CAAC;4BACJ,IACI,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CACtB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CACtC;gCACD,SAAS,CAAC,MAAM,GAAG,CAAC,EACtB,CAAC;gCACC,UAAU;oCACN,QAAQ;wCACR,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,cAAc;wCACrC,aAAa,CAAA;4BACrB,CAAC;iCAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO,EACjD,CAAC;gCACC,UAAU,IAAI,KAAK,CAAA;4BACvB,CAAC;iCAAM,CAAC;gCACJ,UAAU,IAAI,aAAa,CAAA;4BAC/B,CAAC;wBACL,CAAC;oBACL,CAAC;yBAAM,CAAC;wBACJ,UAAU,IAAI,IAAI,CAAA;oBACtB,CAAC;gBACL,CAAC,CAAC,CAAA;YACN,CAAC,CAAC,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,8BAA8B;YAC9B,MAAM,IAAI,YAAY,CAClB,mEAAmE,CACtE,CAAA;QACL,CAAC;QACD,UAAU,IAAI,KAAK,gBAAgB,EAAE,CAAA;QACrC,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;YAC/C,UAAU,IAAI,KAAK,OAAO;iBACrB,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;iBACjD,IAAI,CAAC,IAAI,CAAC,GAAG,CAAA;QACtB,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,qCAAqC,CAC3C,gBAAwB;QAExB,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAA;QAEzC,IAAI,UAAU,GAAG,EAAE,CAAA;QACnB,gFAAgF;QAChF,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;gBACpC,IAAI,WAAW,KAAK,CAAC,EAAE,CAAC;oBACpB,UAAU,IAAI,GAAG,CAAA;gBACrB,CAAC;gBAED,IACI,CAAC,MAAM,CAAC,WAAW;oBACf,MAAM,CAAC,kBAAkB,KAAK,MAAM;oBACpC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAC;oBACvD,CAAC,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,kBAAkB,KAAK,MAAM,CAAC,EAC9D,CAAC;oBACC,UAAU,IAAI,SAAS,CAAA;gBAC3B,CAAC;qBAAM,CAAC;oBACJ,UAAU,IAAI,GAAG,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAC5C,MAAM,CAAC,YAAY,CACtB,EAAE,CAAA;gBACP,CAAC;gBAED,IAAI,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACrC,UAAU,IAAI,GAAG,CAAA;gBACrB,CAAC;qBAAM,CAAC;oBACJ,UAAU,IAAI,IAAI,CAAA;gBACtB,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;aAAM,CAAC;YACJ,8BAA8B;YAC9B,MAAM,IAAI,YAAY,CAClB,mEAAmE,CACtE,CAAA;QACL,CAAC;QACD,IAAI,UAAU,KAAK,IAAI;YAAE,OAAO,EAAE,CAAA;QAClC,OAAO,UAAU,CAAA;IACrB,CAAC;IAED;;OAEG;IACO,+BAA+B;QACrC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB;YAAE,OAAO,EAAE,CAAA;QAC9D,MAAM,eAAe,GAAG,EAAE,CAAA;QAE1B,MAAM,eAAe,GAAG,IAAI,CAAC,4BAA4B,CACrD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,kBAAkB,CACjD,CAAA;QAED,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,eAAe,KAAK,KAAK,EAAE,CAAC;YAC1D,eAAe,CAAC,IAAI,CAAC,eAAe,CAAC,CAAA;QACzC,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,WAAW,EAAE,CAAC;YAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAA;YACvD,sGAAsG;YACtG,IACI,IAAI,CAAC,aAAa,CAAC,SAAS,KAAK,QAAQ;gBACzC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW;gBAC/B,QAAQ,CAAC,gBAAgB,EAC3B,CAAC;gBACC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB;oBACvD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;wBAClC,GAAG;wBACH,QAAQ,CAAC,gBAAgB,CAAC,YAAY;oBACxC,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,YAAY,CAAA;gBAE5C,MAAM,SAAS,GAAG,GAAG,MAAM,UAAU,CAAA;gBACrC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnC,CAAC;YAED,IAAI,QAAQ,CAAC,mBAAmB,IAAI,QAAQ,CAAC,oBAAoB,EAAE,CAAC;gBAChE,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,yBAAyB;oBACvD,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,IAAI;wBAClC,GAAG;wBACH,QAAQ,CAAC,mBAAmB,CAAC,YAAY;oBAC3C,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,YAAY,CAAA;gBAE/C,MAAM,SAAS,GAAG,GAAG,MAAM,qCAAqC,CAAA;gBAChE,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACnC,CAAC;QACL,CAAC;QAED,IAAI,IAAI,CAAC,aAAa,CAAC,8BAA8B,EAAE,CAAC;YACpD,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,8BAA8B,CAAA;YACnE,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACnC,CAAC;QAED,IAAI,SAAS,GAAG,EAAE,CAAA;QAElB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC1B,SAAS,IAAI,EAAE,CAAA;QACnB,CAAC;aAAM,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACtC,SAAS,IAAI,GAAG,eAAe,CAAC,CAAC,CAAC,EAAE,CAAA;QACxC,CAAC;aAAM,CAAC;YACJ,SAAS,IAAI,KAAK,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAA;QAC3D,CAAC;QAED,OAAO,SAAS,CAAA;IACpB,CAAC;IAES,2BAA2B,CACjC,SAA0B,EAC1B,aAAqB,EACrB,MAAsB;QAEtB,MAAM,QAAQ,GAAG,SAAS,CAAC,aAAa,CAAC,CAAA;QACzC,IAAI,UAAU,GAAG,EAAE,CAAA;QAEnB,qCAAqC;QACrC,IAAI,KAAK,GAAG,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;QAE3C,wGAAwG;QACxG,wFAAwF;QACxF,sCAAsC;QACtC;;WAEG;QAEH,IAAI,CAAC,CAAC,OAAO,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC;YACjC,gDAAgD;YAChD,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;QACxE,CAAC;QAED,2EAA2E;QAC3E,6CAA6C;QAC7C,IAAI,MAAM,CAAC,SAAS,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC1C,UAAU,IAAI,GAAG,CAAA;YAEjB,uCAAuC;YACvC,wFAAwF;YACxF,uHAAuH;YACvH,8GAA8G;YAC9G,8BAA8B;YAC9B,EAAE;YACF,wCAAwC;YACxC,wFAAwF;YACxF,uHAAuH;YACvH,8GAA8G;YAC9G,8BAA8B;QAClC,CAAC;aAAM,IAAI,MAAM,CAAC,eAAe,EAAE,CAAC;YAChC,UAAU,IAAI,IAAI,CAAC,eAAe,CAC9B,IAAI,CAAC,aAAa,CAAC,SAAU,CAAC,QAAQ,CAAC,kBAAkB,CAC5D,CAAA;YACD,cAAc;YAEd,qDAAqD;YACrD,uFAAuF;YACvF,+EAA+E;YAC/E,2DAA2D;YAC3D,kCAAkC;YAElC,8KAA8K;QAClL,CAAC;aAAM,IACH,MAAM,CAAC,WAAW;YAClB,MAAM,CAAC,kBAAkB,KAAK,MAAM;YACpC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,yBAAyB,EAAE;YACnD,KAAK,KAAK,SAAS,EACrB,CAAC;YACC,KAAK,GAAG,MAAM,EAAE,CAAA;YAChB,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YAEzC,IAAI,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1D,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,GAAG,EAAE,CAAA;YAC3D,CAAC;YACD,MAAM,CAAC,cAAc,CACjB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,aAAa,CAAC,EAClD,KAAK,CACR,CAAA;YAED,sEAAsE;QAC1E,CAAC;aAAM,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YAC7B,IACI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ;gBAC7C,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;gBACzB,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,KAAK;gBAC7C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,EACnD,CAAC;gBACC,6EAA6E;gBAC7E,IAAI,MAAM,CAAC,OAAO,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;oBAC1D,2CAA2C;oBAC3C,UAAU;wBACN,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAA;gBACvD,CAAC;qBAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;oBACjD,MAAM,CAAC,WAAW;oBAClB,MAAM,CAAC,kBAAkB,KAAK,MAAM,EACtC,CAAC;oBACC,UAAU,IAAI,iBAAiB,CAAA,CAAC,4EAA4E;gBAChH,CAAC;qBAAM,CAAC;oBACJ,UAAU,IAAI,MAAM,CAAA,CAAC,2DAA2D;gBACpF,CAAC;YACL,CAAC;iBAAM,CAAC;gBACJ,UAAU,IAAI,SAAS,CAAA;YAC3B,CAAC;QACL,CAAC;aAAM,IACH,KAAK,KAAK,IAAI;YACd,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS;gBAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,QAAQ,CAAC,EACvD,CAAC;YACC,UAAU,IAAI,MAAM,CAAA;YAEpB,yCAAyC;QAC7C,CAAC;aAAM,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE,CAAC;YACrC,UAAU,IAAI,KAAK,EAAE,CAAA;YAErB,+BAA+B;QACnC,CAAC;aAAM,CAAC;YACJ,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;gBAC/C,KAAK,GACD,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;YAErC,0GAA0G;YAC1G,8BAA8B;YAC9B,yCAAyC;YAEzC,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAA;YAE7C,IACI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBAC9C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,cAAc,CAAC;gBAC3D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAC3D,CAAC;gBACC,MAAM,SAAS,GACX,IAAI,CAAC,UAAU,CAAC,MACnB,CAAC,OAAO,CAAC,oBAAoB,CAAA;gBAC9B,MAAM,YAAY,GAAG,SAAS;oBAC1B,CAAC,CAAC,cAAc;oBAChB,CAAC,CAAC,iBAAiB,CAAA;gBACvB,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;oBACtB,UAAU,IAAI,GAAG,YAAY,IAAI,SAAS,KAAK,MAAM,CAAC,IAAI,GAAG,CAAA;gBACjE,CAAC;qBAAM,CAAC;oBACJ,UAAU,IAAI,GAAG,YAAY,IAAI,SAAS,GAAG,CAAA;gBACjD,CAAC;YACL,CAAC;iBAAM,IACH,WAAW,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;gBACpD,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAC3D,CAAC;gBACC,IAAI,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;oBACtB,UAAU,IAAI,iCAAiC,SAAS,MAAM,MAAM,CAAC,IAAI,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;gBAChG,CAAC;qBAAM,CAAC;oBACJ,UAAU,IAAI,sBAAsB,SAAS,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;gBACpE,CAAC;YACL,CAAC;iBAAM,IACH,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,KAAK,OAAO;gBAC/C,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EAC3D,CAAC;gBACC,UAAU;oBACN,MAAM,CAAC,IAAI;wBACX,mBAAmB;wBACnB,SAAS;wBACT,IAAI;wBACJ,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,CAAC;wBACpB,GAAG,CAAA;YACX,CAAC;iBAAM,CAAC;gBACJ,UAAU,IAAI,SAAS,CAAA;YAC3B,CAAC;QACL,CAAC;QACD,OAAO,UAAU,CAAA;IACrB,CAAC;CACJ", "file": "InsertQueryBuilder.js", "sourcesContent": ["import { v4 as uuidv4 } from \"uuid\"\nimport { EntityTarget } from \"../common/EntityTarget\"\nimport { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { AuroraMysqlDriver } from \"../driver/aurora-mysql/AuroraMysqlDriver\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { MysqlDriver } from \"../driver/mysql/MysqlDriver\"\nimport { SqlServerDriver } from \"../driver/sqlserver/SqlServerDriver\"\nimport { TypeORMError } from \"../error\"\nimport { InsertValuesMissingError } from \"../error/InsertValuesMissingError\"\nimport { ReturningStatementNotSupportedError } from \"../error/ReturningStatementNotSupportedError\"\nimport { ColumnMetadata } from \"../metadata/ColumnMetadata\"\nimport { BroadcasterResult } from \"../subscriber/BroadcasterResult\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InsertOrUpdateOptions } from \"./InsertOrUpdateOptions\"\nimport { QueryBuilder } from \"./QueryBuilder\"\nimport { QueryDeepPartialEntity } from \"./QueryPartialEntity\"\nimport { InsertResult } from \"./result/InsertResult\"\nimport { ReturningResultsEntityUpdator } from \"./ReturningResultsEntityUpdator\"\nimport { WhereClause } from \"./WhereClause\"\n\n/**\n * Allows to build complex sql queries in a fashion way and execute those queries.\n */\nexport class InsertQueryBuilder<\n    Entity extends ObjectLiteral,\n> extends QueryBuilder<Entity> {\n    readonly \"@instanceof\" = Symbol.for(\"InsertQueryBuilder\")\n\n    // -------------------------------------------------------------------------\n    // Public Implemented Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets generated SQL query without parameters being replaced.\n     */\n    getQuery(): string {\n        let sql = this.createComment()\n        sql += this.createCteExpression()\n        sql += this.createInsertExpression()\n        return this.replacePropertyNamesForTheWholeQuery(sql.trim())\n    }\n\n    /**\n     * Executes sql generated by query builder and returns raw database results.\n     */\n    async execute(): Promise<InsertResult> {\n        // console.time(\".value sets\");\n        const valueSets: ObjectLiteral[] = this.getValueSets()\n        // console.timeEnd(\".value sets\");\n\n        // If user passed empty array of entities then we don't need to do\n        // anything.\n        //\n        // Fixes GitHub issues #3111 and #5734. If we were to let this through\n        // we would run into problems downstream, like subscribers getting\n        // invoked with the empty array where they expect an entity, and SQL\n        // queries with an empty VALUES clause.\n        if (valueSets.length === 0) return new InsertResult()\n\n        // console.time(\"QueryBuilder.execute\");\n        // console.time(\".database stuff\");\n        const queryRunner = this.obtainQueryRunner()\n        let transactionStartedByUs: boolean = false\n\n        try {\n            // start transaction if it was enabled\n            if (\n                this.expressionMap.useTransaction === true &&\n                queryRunner.isTransactionActive === false\n            ) {\n                await queryRunner.startTransaction()\n                transactionStartedByUs = true\n            }\n\n            // console.timeEnd(\".database stuff\");\n\n            // call before insertion methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                const broadcastResult = new BroadcasterResult()\n                valueSets.forEach((valueSet) => {\n                    queryRunner.broadcaster.broadcastBeforeInsertEvent(\n                        broadcastResult,\n                        this.expressionMap.mainAlias!.metadata,\n                        valueSet,\n                    )\n                })\n                await broadcastResult.wait()\n            }\n\n            let declareSql: string | null = null\n            let selectOutputSql: string | null = null\n\n            // if update entity mode is enabled we may need extra columns for the returning statement\n            // console.time(\".prepare returning statement\");\n            const returningResultsEntityUpdator =\n                new ReturningResultsEntityUpdator(\n                    queryRunner,\n                    this.expressionMap,\n                )\n\n            const returningColumns: ColumnMetadata[] = []\n\n            if (\n                Array.isArray(this.expressionMap.returning) &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                for (const columnPath of this.expressionMap.returning) {\n                    returningColumns.push(\n                        ...this.expressionMap.mainAlias!.metadata.findColumnsWithPropertyPath(\n                            columnPath,\n                        ),\n                    )\n                }\n            }\n\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                if (\n                    !(\n                        valueSets.length > 1 &&\n                        this.connection.driver.options.type === \"oracle\"\n                    )\n                ) {\n                    this.expressionMap.extraReturningColumns =\n                        this.expressionMap.mainAlias!.metadata.getInsertionReturningColumns()\n                }\n\n                returningColumns.push(\n                    ...this.expressionMap.extraReturningColumns.filter(\n                        (c) => !returningColumns.includes(c),\n                    ),\n                )\n            }\n\n            if (\n                returningColumns.length > 0 &&\n                this.connection.driver.options.type === \"mssql\"\n            ) {\n                declareSql = (\n                    this.connection.driver as SqlServerDriver\n                ).buildTableVariableDeclaration(\n                    \"@OutputTable\",\n                    returningColumns,\n                )\n                selectOutputSql = `SELECT * FROM @OutputTable`\n            }\n            // console.timeEnd(\".prepare returning statement\");\n\n            // execute query\n            // console.time(\".getting query and parameters\");\n            const [insertSql, parameters] = this.getQueryAndParameters()\n            // console.timeEnd(\".getting query and parameters\");\n\n            // console.time(\".query execution by database\");\n            const statements = [declareSql, insertSql, selectOutputSql]\n            const sql = statements.filter((s) => s != null).join(\";\\n\\n\")\n\n            const queryResult = await queryRunner.query(sql, parameters, true)\n\n            const insertResult = InsertResult.from(queryResult)\n\n            // console.timeEnd(\".query execution by database\");\n\n            // load returning results and set them to the entity if entity updation is enabled\n            if (\n                this.expressionMap.updateEntity === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                // console.time(\".updating entity\");\n                await returningResultsEntityUpdator.insert(\n                    insertResult,\n                    valueSets,\n                )\n                // console.timeEnd(\".updating entity\");\n            }\n\n            // call after insertion methods in listeners and subscribers\n            if (\n                this.expressionMap.callListeners === true &&\n                this.expressionMap.mainAlias!.hasMetadata\n            ) {\n                const broadcastResult = new BroadcasterResult()\n                valueSets.forEach((valueSet) => {\n                    queryRunner.broadcaster.broadcastAfterInsertEvent(\n                        broadcastResult,\n                        this.expressionMap.mainAlias!.metadata,\n                        valueSet,\n                    )\n                })\n                await broadcastResult.wait()\n            }\n\n            // close transaction if we started it\n            // console.time(\".commit\");\n            if (transactionStartedByUs) {\n                await queryRunner.commitTransaction()\n            }\n            // console.timeEnd(\".commit\");\n\n            return insertResult\n        } catch (error) {\n            // rollback transaction if we started it\n            if (transactionStartedByUs) {\n                try {\n                    await queryRunner.rollbackTransaction()\n                } catch (rollbackError) {}\n            }\n            throw error\n        } finally {\n            // console.time(\".releasing connection\");\n            if (queryRunner !== this.queryRunner) {\n                // means we created our own query runner\n                await queryRunner.release()\n            }\n            // console.timeEnd(\".releasing connection\");\n            // console.timeEnd(\"QueryBuilder.execute\");\n        }\n    }\n\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Specifies INTO which entity's table insertion will be executed.\n     */\n    into<T extends ObjectLiteral>(\n        entityTarget: EntityTarget<T>,\n        columns?: string[],\n    ): InsertQueryBuilder<T> {\n        entityTarget = InstanceChecker.isEntitySchema(entityTarget)\n            ? entityTarget.options.name\n            : entityTarget\n        const mainAlias = this.createFromAlias(entityTarget)\n        this.expressionMap.setMainAlias(mainAlias)\n        this.expressionMap.insertColumns = columns || []\n        return this as any as InsertQueryBuilder<T>\n    }\n\n    /**\n     * Values needs to be inserted into table.\n     */\n    values(\n        values:\n            | QueryDeepPartialEntity<Entity>\n            | QueryDeepPartialEntity<Entity>[],\n    ): this {\n        this.expressionMap.valuesSet = values\n        return this\n    }\n\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    output(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    output(output: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    output(output: string | string[]): this {\n        return this.returning(output)\n    }\n\n    /**\n     * Optional returning/output clause.\n     * This will return given column values.\n     */\n    returning(columns: string[]): this\n\n    /**\n     * Optional returning/output clause.\n     * Returning is a SQL string containing returning statement.\n     */\n    returning(returning: string): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this\n\n    /**\n     * Optional returning/output clause.\n     */\n    returning(returning: string | string[]): this {\n        // not all databases support returning/output cause\n        if (!this.connection.driver.isReturningSqlSupported(\"insert\")) {\n            throw new ReturningStatementNotSupportedError()\n        }\n\n        this.expressionMap.returning = returning\n        return this\n    }\n\n    /**\n     * Indicates if entity must be updated after insertion operations.\n     * This may produce extra query or use RETURNING / OUTPUT statement (depend on database).\n     * Enabled by default.\n     */\n    updateEntity(enabled: boolean): this {\n        this.expressionMap.updateEntity = enabled\n        return this\n    }\n\n    /**\n     * Adds additional ON CONFLICT statement supported in postgres and cockroach.\n     *\n     * @deprecated Use `orIgnore` or `orUpdate`\n     */\n    onConflict(statement: string): this {\n        this.expressionMap.onConflict = statement\n        return this\n    }\n\n    /**\n     * Adds additional ignore statement supported in databases.\n     */\n    orIgnore(statement: string | boolean = true): this {\n        this.expressionMap.onIgnore = !!statement\n        return this\n    }\n\n    /**\n     * @deprecated\n     *\n     * `.orUpdate({ columns: [ \"is_updated\" ] }).setParameter(\"is_updated\", value)`\n     *\n     * is now `.orUpdate([\"is_updated\"])`\n     *\n     * `.orUpdate({ conflict_target: ['date'], overwrite: ['title'] })`\n     *\n     * is now `.orUpdate(['title'], ['date'])`\n     *\n     */\n    orUpdate(statement?: {\n        columns?: string[]\n        overwrite?: string[]\n        conflict_target?: string | string[]\n    }): this\n\n    orUpdate(\n        overwrite: string[],\n        conflictTarget?: string | string[],\n        orUpdateOptions?: InsertOrUpdateOptions,\n    ): this\n\n    /**\n     * Adds additional update statement supported in databases.\n     */\n    orUpdate(\n        statementOrOverwrite?:\n            | {\n                  columns?: string[]\n                  overwrite?: string[]\n                  conflict_target?: string | string[]\n              }\n            | string[],\n        conflictTarget?: string | string[],\n        orUpdateOptions?: InsertOrUpdateOptions,\n    ): this {\n        const { where, parameters } = orUpdateOptions?.overwriteCondition ?? {}\n        let wheres: WhereClause[] | undefined\n        if (where) {\n            const condition = this.getWhereCondition(where)\n            if (Array.isArray(condition) ? condition.length !== 0 : condition)\n                wheres = [{ type: \"simple\", condition: condition }]\n        }\n        if (parameters) this.setParameters(parameters)\n\n        if (!Array.isArray(statementOrOverwrite)) {\n            this.expressionMap.onUpdate = {\n                conflict: statementOrOverwrite?.conflict_target,\n                columns: statementOrOverwrite?.columns,\n                overwrite: statementOrOverwrite?.overwrite,\n                skipUpdateIfNoValuesChanged:\n                    orUpdateOptions?.skipUpdateIfNoValuesChanged,\n                upsertType: orUpdateOptions?.upsertType,\n                overwriteCondition: wheres,\n            }\n            return this\n        }\n\n        this.expressionMap.onUpdate = {\n            overwrite: statementOrOverwrite,\n            conflict: conflictTarget,\n            skipUpdateIfNoValuesChanged:\n                orUpdateOptions?.skipUpdateIfNoValuesChanged,\n            indexPredicate: orUpdateOptions?.indexPredicate,\n            upsertType: orUpdateOptions?.upsertType,\n            overwriteCondition: wheres,\n        }\n        return this\n    }\n\n    // -------------------------------------------------------------------------\n    // Protected Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Creates INSERT express used to perform insert query.\n     */\n    protected createInsertExpression() {\n        if (this.expressionMap.onUpdate || this.expressionMap.onIgnore) {\n            if (\n                (this.expressionMap.onUpdate?.upsertType ?? \"merge-into\") ===\n                    \"merge-into\" &&\n                this.connection.driver.supportedUpsertTypes.includes(\n                    \"merge-into\",\n                )\n            )\n                return this.createMergeExpression()\n        }\n        const tableName = this.getTableName(this.getMainTableName())\n        const tableOrAliasName =\n            this.alias !== this.getMainTableName()\n                ? this.escape(this.alias)\n                : tableName\n        const valuesExpression = this.createValuesExpression() // its important to get values before returning expression because oracle rely on native parameters and ordering of them is important\n        const returningExpression =\n            this.connection.driver.options.type === \"oracle\" &&\n            this.getValueSets().length > 1\n                ? null\n                : this.createReturningExpression(\"insert\") // oracle doesnt support returning with multi-row insert\n        const columnsExpression = this.createColumnNamesExpression()\n        let query = \"INSERT \"\n\n        if (this.expressionMap.onUpdate?.upsertType === \"primary-key\") {\n            query = \"UPSERT \"\n        }\n\n        if (\n            DriverUtils.isMySQLFamily(this.connection.driver) ||\n            this.connection.driver.options.type === \"aurora-mysql\"\n        ) {\n            query += `${this.expressionMap.onIgnore ? \" IGNORE \" : \"\"}`\n        }\n\n        query += `INTO ${tableName}`\n\n        if (\n            this.alias !== this.getMainTableName() &&\n            DriverUtils.isPostgresFamily(this.connection.driver)\n        ) {\n            query += ` AS \"${this.alias}\"`\n        }\n\n        // add columns expression\n        if (columnsExpression) {\n            query += `(${columnsExpression})`\n        } else {\n            if (\n                !valuesExpression &&\n                (DriverUtils.isMySQLFamily(this.connection.driver) ||\n                    this.connection.driver.options.type === \"aurora-mysql\")\n            )\n                // special syntax for mysql DEFAULT VALUES insertion\n                query += \"()\"\n        }\n\n        // add OUTPUT expression\n        if (\n            returningExpression &&\n            this.connection.driver.options.type === \"mssql\"\n        ) {\n            query += ` OUTPUT ${returningExpression}`\n        }\n\n        // add VALUES expression\n        if (valuesExpression) {\n            if (\n                (this.connection.driver.options.type === \"oracle\" ||\n                    this.connection.driver.options.type === \"sap\") &&\n                this.getValueSets().length > 1\n            ) {\n                query += ` ${valuesExpression}`\n            } else {\n                query += ` VALUES ${valuesExpression}`\n            }\n        } else {\n            if (\n                DriverUtils.isMySQLFamily(this.connection.driver) ||\n                this.connection.driver.options.type === \"aurora-mysql\"\n            ) {\n                // special syntax for mysql DEFAULT VALUES insertion\n                query += \" VALUES ()\"\n            } else {\n                query += ` DEFAULT VALUES`\n            }\n        }\n        if (this.expressionMap.onUpdate?.upsertType !== \"primary-key\") {\n            if (\n                this.connection.driver.supportedUpsertTypes.includes(\n                    \"on-conflict-do-update\",\n                )\n            ) {\n                if (this.expressionMap.onIgnore) {\n                    query += \" ON CONFLICT DO NOTHING \"\n                } else if (this.expressionMap.onConflict) {\n                    query += ` ON CONFLICT ${this.expressionMap.onConflict} `\n                } else if (this.expressionMap.onUpdate) {\n                    const {\n                        overwrite,\n                        columns,\n                        conflict,\n                        skipUpdateIfNoValuesChanged,\n                        indexPredicate,\n                    } = this.expressionMap.onUpdate\n\n                    let conflictTarget = \"ON CONFLICT\"\n\n                    if (Array.isArray(conflict)) {\n                        conflictTarget += ` ( ${conflict\n                            .map((column) => this.escape(column))\n                            .join(\", \")} )`\n                        if (\n                            indexPredicate &&\n                            !DriverUtils.isPostgresFamily(\n                                this.connection.driver,\n                            )\n                        ) {\n                            throw new TypeORMError(\n                                `indexPredicate option is not supported by the current database driver`,\n                            )\n                        }\n                        if (\n                            indexPredicate &&\n                            DriverUtils.isPostgresFamily(this.connection.driver)\n                        ) {\n                            conflictTarget += ` WHERE ( ${indexPredicate} )`\n                        }\n                    } else if (conflict) {\n                        conflictTarget += ` ON CONSTRAINT ${this.escape(\n                            conflict,\n                        )}`\n                    }\n\n                    const updatePart: string[] = []\n\n                    if (Array.isArray(overwrite)) {\n                        updatePart.push(\n                            ...overwrite.map(\n                                (column) =>\n                                    `${this.escape(\n                                        column,\n                                    )} = EXCLUDED.${this.escape(column)}`,\n                            ),\n                        )\n                    } else if (columns) {\n                        updatePart.push(\n                            ...columns.map(\n                                (column) =>\n                                    `${this.escape(column)} = :${column}`,\n                            ),\n                        )\n                    }\n\n                    if (updatePart.length > 0) {\n                        query += ` ${conflictTarget} DO UPDATE SET `\n\n                        updatePart.push(\n                            ...this.expressionMap\n                                .mainAlias!.metadata.columns.filter(\n                                    (column) =>\n                                        column.isUpdateDate &&\n                                        !overwrite?.includes(\n                                            column.databaseName,\n                                        ) &&\n                                        !(\n                                            (this.connection.driver.options\n                                                .type === \"oracle\" &&\n                                                this.getValueSets().length >\n                                                    1) ||\n                                            DriverUtils.isSQLiteFamily(\n                                                this.connection.driver,\n                                            ) ||\n                                            this.connection.driver.options\n                                                .type === \"sap\" ||\n                                            this.connection.driver.options\n                                                .type === \"spanner\"\n                                        ),\n                                )\n                                .map(\n                                    (column) =>\n                                        `${this.escape(\n                                            column.databaseName,\n                                        )} = DEFAULT`,\n                                ),\n                        )\n\n                        query += updatePart.join(\", \")\n                    }\n\n                    if (\n                        Array.isArray(overwrite) &&\n                        skipUpdateIfNoValuesChanged\n                    ) {\n                        this.expressionMap.onUpdate.overwriteCondition ??= []\n                        const wheres = overwrite.map<WhereClause>((column) => ({\n                            type: \"or\",\n                            condition: `${tableOrAliasName}.${this.escape(\n                                column,\n                            )} IS DISTINCT FROM EXCLUDED.${this.escape(\n                                column,\n                            )}`,\n                        }))\n                        this.expressionMap.onUpdate.overwriteCondition.push({\n                            type: \"and\",\n                            condition: wheres,\n                        })\n                    }\n                    if (\n                        DriverUtils.isPostgresFamily(this.connection.driver) &&\n                        this.expressionMap.onUpdate.overwriteCondition &&\n                        this.expressionMap.onUpdate.overwriteCondition.length >\n                            0\n                    ) {\n                        query += ` WHERE ${this.createUpsertConditionExpression()}`\n                    }\n                }\n            } else if (\n                this.connection.driver.supportedUpsertTypes.includes(\n                    \"on-duplicate-key-update\",\n                )\n            ) {\n                if (this.expressionMap.onUpdate) {\n                    const { overwrite, columns } = this.expressionMap.onUpdate\n\n                    if (Array.isArray(overwrite)) {\n                        query += \" ON DUPLICATE KEY UPDATE \"\n                        query += overwrite\n                            .map(\n                                (column) =>\n                                    `${this.escape(\n                                        column,\n                                    )} = VALUES(${this.escape(column)})`,\n                            )\n                            .join(\", \")\n                        query += \" \"\n                    } else if (Array.isArray(columns)) {\n                        query += \" ON DUPLICATE KEY UPDATE \"\n                        query += columns\n                            .map(\n                                (column) =>\n                                    `${this.escape(column)} = :${column}`,\n                            )\n                            .join(\", \")\n                        query += \" \"\n                    }\n                }\n            } else {\n                if (this.expressionMap.onUpdate) {\n                    throw new TypeORMError(\n                        `onUpdate is not supported by the current database driver`,\n                    )\n                }\n            }\n        }\n\n        // add RETURNING expression\n        if (\n            returningExpression &&\n            (DriverUtils.isPostgresFamily(this.connection.driver) ||\n                this.connection.driver.options.type === \"oracle\" ||\n                this.connection.driver.options.type === \"cockroachdb\" ||\n                DriverUtils.isMySQLFamily(this.connection.driver))\n        ) {\n            query += ` RETURNING ${returningExpression}`\n        }\n\n        if (\n            returningExpression &&\n            this.connection.driver.options.type === \"spanner\"\n        ) {\n            query += ` THEN RETURN ${returningExpression}`\n        }\n\n        // Inserting a specific value for an auto-increment primary key in mssql requires enabling IDENTITY_INSERT\n        // IDENTITY_INSERT can only be enabled for tables where there is an IDENTITY column and only if there is a value to be inserted (i.e. supplying DEFAULT is prohibited if IDENTITY_INSERT is enabled)\n        if (\n            this.connection.driver.options.type === \"mssql\" &&\n            this.expressionMap.mainAlias!.hasMetadata &&\n            this.expressionMap\n                .mainAlias!.metadata.columns.filter((column) =>\n                    this.expressionMap.insertColumns.length > 0\n                        ? this.expressionMap.insertColumns.indexOf(\n                              column.propertyPath,\n                          ) !== -1\n                        : column.isInsert,\n                )\n                .some((column) =>\n                    this.isOverridingAutoIncrementBehavior(column),\n                )\n        ) {\n            query = `SET IDENTITY_INSERT ${tableName} ON; ${query}; SET IDENTITY_INSERT ${tableName} OFF`\n        }\n\n        return query\n    }\n\n    /**\n     * Gets list of columns where values must be inserted to.\n     */\n    protected getInsertedColumns(): ColumnMetadata[] {\n        if (!this.expressionMap.mainAlias!.hasMetadata) return []\n\n        return this.expressionMap.mainAlias!.metadata.columns.filter(\n            (column) => {\n                // if user specified list of columns he wants to insert to, then we filter only them\n                if (this.expressionMap.insertColumns.length)\n                    return (\n                        this.expressionMap.insertColumns.indexOf(\n                            column.propertyPath,\n                        ) !== -1\n                    )\n\n                // skip columns the user doesn't want included by default\n                if (!column.isInsert) {\n                    return false\n                }\n\n                // if user did not specified such list then return all columns except auto-increment one\n                // for Oracle we return auto-increment column as well because Oracle does not support DEFAULT VALUES expression\n                if (\n                    column.isGenerated &&\n                    column.generationStrategy === \"increment\" &&\n                    !(this.connection.driver.options.type === \"spanner\") &&\n                    !(this.connection.driver.options.type === \"oracle\") &&\n                    !DriverUtils.isSQLiteFamily(this.connection.driver) &&\n                    !DriverUtils.isMySQLFamily(this.connection.driver) &&\n                    !(this.connection.driver.options.type === \"aurora-mysql\") &&\n                    !(\n                        this.connection.driver.options.type === \"mssql\" &&\n                        this.isOverridingAutoIncrementBehavior(column)\n                    )\n                )\n                    return false\n\n                return true\n            },\n        )\n    }\n\n    /**\n     * Creates a columns string where values must be inserted to for INSERT INTO expression.\n     */\n    protected createColumnNamesExpression(): string {\n        const columns = this.getInsertedColumns()\n        if (columns.length > 0)\n            return columns\n                .map((column) => this.escape(column.databaseName))\n                .join(\", \")\n\n        // in the case if there are no insert columns specified and table without metadata used\n        // we get columns from the inserted value map, in the case if only one inserted map is specified\n        if (\n            !this.expressionMap.mainAlias!.hasMetadata &&\n            !this.expressionMap.insertColumns.length\n        ) {\n            const valueSets = this.getValueSets()\n            if (valueSets.length === 1)\n                return Object.keys(valueSets[0])\n                    .map((columnName) => this.escape(columnName))\n                    .join(\", \")\n        }\n\n        // get a table name and all column database names\n        return this.expressionMap.insertColumns\n            .map((columnName) => this.escape(columnName))\n            .join(\", \")\n    }\n\n    /**\n     * Creates list of values needs to be inserted in the VALUES expression.\n     */\n    protected createValuesExpression(): string {\n        const valueSets = this.getValueSets()\n        const columns = this.getInsertedColumns()\n\n        // if column metadatas are given then apply all necessary operations with values\n        if (columns.length > 0) {\n            let expression = \"\"\n            valueSets.forEach((valueSet, valueSetIndex) => {\n                columns.forEach((column, columnIndex) => {\n                    if (columnIndex === 0) {\n                        if (\n                            this.connection.driver.options.type === \"oracle\" &&\n                            valueSets.length > 1\n                        ) {\n                            expression += \" SELECT \"\n                        } else if (\n                            this.connection.driver.options.type === \"sap\" &&\n                            valueSets.length > 1\n                        ) {\n                            expression += \" SELECT \"\n                        } else {\n                            expression += \"(\"\n                        }\n                    }\n\n                    expression += this.createColumnValueExpression(\n                        valueSets,\n                        valueSetIndex,\n                        column,\n                    )\n\n                    if (columnIndex === columns.length - 1) {\n                        if (valueSetIndex === valueSets.length - 1) {\n                            if (\n                                [\"oracle\", \"sap\"].includes(\n                                    this.connection.driver.options.type,\n                                ) &&\n                                valueSets.length > 1\n                            ) {\n                                expression +=\n                                    \" FROM \" +\n                                    this.connection.driver.dummyTableName\n                            } else {\n                                expression += \")\"\n                            }\n                        } else {\n                            if (\n                                [\"oracle\", \"sap\"].includes(\n                                    this.connection.driver.options.type,\n                                ) &&\n                                valueSets.length > 1\n                            ) {\n                                expression +=\n                                    \" FROM \" +\n                                    this.connection.driver.dummyTableName +\n                                    \" UNION ALL \"\n                            } else {\n                                expression += \"), \"\n                            }\n                        }\n                    } else {\n                        expression += \", \"\n                    }\n                })\n            })\n            if (expression === \"()\") return \"\"\n\n            return expression\n        } else {\n            // for tables without metadata\n            // get values needs to be inserted\n            let expression = \"\"\n\n            valueSets.forEach((valueSet, insertionIndex) => {\n                const columns = Object.keys(valueSet)\n                columns.forEach((columnName, columnIndex) => {\n                    if (columnIndex === 0) {\n                        expression += \"(\"\n                    }\n\n                    const value = valueSet[columnName]\n\n                    // support for SQL expressions in queries\n                    if (typeof value === \"function\") {\n                        expression += value()\n\n                        // if value for this column was not provided then insert default value\n                    } else if (value === undefined) {\n                        if (\n                            (this.connection.driver.options.type === \"oracle\" &&\n                                valueSets.length > 1) ||\n                            DriverUtils.isSQLiteFamily(\n                                this.connection.driver,\n                            ) ||\n                            this.connection.driver.options.type === \"sap\" ||\n                            this.connection.driver.options.type === \"spanner\"\n                        ) {\n                            expression += \"NULL\"\n                        } else {\n                            expression += \"DEFAULT\"\n                        }\n                    } else if (\n                        value === null &&\n                        this.connection.driver.options.type === \"spanner\"\n                    ) {\n                        // just any other regular value\n                    } else {\n                        expression += this.createParameter(value)\n                    }\n\n                    if (columnIndex === Object.keys(valueSet).length - 1) {\n                        if (insertionIndex === valueSets.length - 1) {\n                            expression += \")\"\n                        } else {\n                            expression += \"), \"\n                        }\n                    } else {\n                        expression += \", \"\n                    }\n                })\n            })\n            if (expression === \"()\") return \"\"\n            return expression\n        }\n    }\n\n    /**\n     * Gets array of values need to be inserted into the target table.\n     */\n    protected getValueSets(): ObjectLiteral[] {\n        if (Array.isArray(this.expressionMap.valuesSet))\n            return this.expressionMap.valuesSet\n\n        if (ObjectUtils.isObject(this.expressionMap.valuesSet))\n            return [this.expressionMap.valuesSet]\n\n        throw new InsertValuesMissingError()\n    }\n\n    /**\n     * Checks if column is an auto-generated primary key, but the current insertion specifies a value for it.\n     *\n     * @param column\n     */\n    protected isOverridingAutoIncrementBehavior(\n        column: ColumnMetadata,\n    ): boolean {\n        return (\n            column.isPrimary &&\n            column.isGenerated &&\n            column.generationStrategy === \"increment\" &&\n            this.getValueSets().some(\n                (valueSet) =>\n                    column.getEntityValue(valueSet) !== undefined &&\n                    column.getEntityValue(valueSet) !== null,\n            )\n        )\n    }\n\n    /**\n     * Creates MERGE express used to perform insert query.\n     */\n    protected createMergeExpression() {\n        if (!this.connection.driver.supportedUpsertTypes.includes(\"merge-into\"))\n            throw new TypeORMError(\n                `Upsert type \"merge-into\" is not supported by current database driver`,\n            )\n\n        if (\n            this.expressionMap.onUpdate?.upsertType &&\n            this.expressionMap.onUpdate.upsertType !== \"merge-into\"\n        ) {\n            throw new TypeORMError(\n                `Upsert type \"${this.expressionMap.onUpdate.upsertType}\" is not supported by current database driver`,\n            )\n        }\n        // const mainAlias = this.expressionMap.mainAlias!\n        const tableName = this.getTableName(this.getMainTableName())\n        const tableAlias = this.escape(this.alias)\n        const columns = this.getInsertedColumns()\n        const columnsExpression = this.createColumnNamesExpression()\n\n        let query = `MERGE INTO ${tableName} ${this.escape(this.alias)}`\n\n        const mergeSourceAlias = this.escape(\"mergeIntoSource\")\n\n        const mergeSourceExpression =\n            this.createMergeIntoSourceExpression(mergeSourceAlias)\n\n        query += ` ${mergeSourceExpression}`\n\n        // build on condition\n        if (this.expressionMap.onIgnore) {\n            const primaryKey = columns.find((column) => column.isPrimary)\n            if (primaryKey) {\n                query += ` ON (${tableAlias}.${this.escape(\n                    primaryKey.databaseName,\n                )} = ${mergeSourceAlias}.${this.escape(\n                    primaryKey.databaseName,\n                )})`\n            } else {\n                query += `ON (${this.expressionMap\n                    .mainAlias!.metadata.uniques.map((unique) => {\n                        return `(${unique.columns\n                            .map((column) => {\n                                return `${tableAlias}.${this.escape(\n                                    column.databaseName,\n                                )} = ${mergeSourceAlias}.${this.escape(\n                                    column.databaseName,\n                                )}`\n                            })\n                            .join(\" AND \")})`\n                    })\n                    .join(\" OR \")})`\n            }\n        } else if (this.expressionMap.onUpdate) {\n            const { conflict, indexPredicate } = this.expressionMap.onUpdate\n\n            if (indexPredicate) {\n                throw new TypeORMError(\n                    `indexPredicate option is not supported by upsert type \"merge-into\"`,\n                )\n            }\n\n            if (Array.isArray(conflict)) {\n                query += ` ON (${conflict\n                    .map(\n                        (column) =>\n                            `${tableAlias}.${this.escape(\n                                column,\n                            )} = ${mergeSourceAlias}.${this.escape(column)}`,\n                    )\n                    .join(\" AND \")})`\n            } else if (conflict) {\n                query += ` ON (${tableAlias}.${this.escape(\n                    conflict,\n                )} = ${mergeSourceAlias}.${this.escape(conflict)})`\n            } else {\n                query += `ON (${this.expressionMap\n                    .mainAlias!.metadata.uniques.map((unique) => {\n                        return `(${unique.columns\n                            .map((column) => {\n                                return `${tableAlias}.${this.escape(\n                                    column.databaseName,\n                                )} = ${mergeSourceAlias}.${this.escape(\n                                    column.databaseName,\n                                )}`\n                            })\n                            .join(\" AND \")})`\n                    })\n                    .join(\" OR \")})`\n            }\n        }\n\n        if (this.expressionMap.onUpdate) {\n            const {\n                overwrite,\n                columns,\n                conflict,\n                skipUpdateIfNoValuesChanged,\n            } = this.expressionMap.onUpdate\n            let updateExpression = \"\"\n\n            if (Array.isArray(overwrite)) {\n                updateExpression += (overwrite || columns)\n                    ?.filter((column) => !conflict?.includes(column))\n                    .map(\n                        (column) =>\n                            `${tableAlias}.${this.escape(\n                                column,\n                            )} = ${mergeSourceAlias}.${this.escape(column)}`,\n                    )\n                    .join(\", \")\n            }\n\n            if (Array.isArray(overwrite) && skipUpdateIfNoValuesChanged) {\n                this.expressionMap.onUpdate.overwriteCondition ??= []\n                const wheres = overwrite.map<WhereClause>((column) => ({\n                    type: \"or\",\n                    condition: {\n                        operator: \"notEqual\",\n                        parameters: [\n                            `${tableAlias}.${this.escape(column)}`,\n                            `${mergeSourceAlias}.${this.escape(column)}`,\n                        ],\n                    },\n                }))\n                this.expressionMap.onUpdate.overwriteCondition.push({\n                    type: \"and\",\n                    condition: wheres,\n                })\n            }\n            const mergeCondition = this.createUpsertConditionExpression()\n            if (updateExpression.trim()) {\n                if (\n                    (this.connection.driver.options.type === \"mssql\" ||\n                        this.connection.driver.options.type === \"sap\") &&\n                    mergeCondition != \"\"\n                ) {\n                    query += ` WHEN MATCHED AND ${mergeCondition} THEN UPDATE SET ${updateExpression}`\n                } else {\n                    query += ` WHEN MATCHED THEN UPDATE SET ${updateExpression}`\n                    if (mergeCondition != \"\") {\n                        query += ` WHERE ${mergeCondition}`\n                    }\n                }\n            }\n        }\n\n        const valuesExpression =\n            this.createMergeIntoInsertValuesExpression(mergeSourceAlias)\n        const returningExpression =\n            this.connection.driver.options.type === \"mssql\"\n                ? this.createReturningExpression(\"insert\")\n                : null\n\n        query += \" WHEN NOT MATCHED THEN INSERT\"\n\n        // add columns expression\n        if (columnsExpression) {\n            query += `(${columnsExpression})`\n        }\n\n        // add VALUES expression\n        if (valuesExpression) {\n            query += ` VALUES ${valuesExpression}`\n        }\n\n        // add OUTPUT expression\n        if (\n            returningExpression &&\n            this.connection.driver.options.type === \"mssql\"\n        ) {\n            query += ` OUTPUT ${returningExpression}`\n        }\n        if (this.connection.driver.options.type === \"mssql\") {\n            query += `;`\n        }\n        return query\n    }\n\n    /**\n     * Creates list of values needs to be inserted in the VALUES expression.\n     */\n    protected createMergeIntoSourceExpression(\n        mergeSourceAlias: string,\n    ): string {\n        const valueSets = this.getValueSets()\n        const columns = this.getInsertedColumns()\n\n        let expression = \"USING (\"\n        // if column metadatas are given then apply all necessary operations with values\n        if (columns.length > 0) {\n            if (this.connection.driver.options.type === \"mssql\") {\n                expression += \"VALUES \"\n            }\n            valueSets.forEach((valueSet, valueSetIndex) => {\n                columns.forEach((column, columnIndex) => {\n                    if (columnIndex === 0) {\n                        if (this.connection.driver.options.type === \"mssql\") {\n                            expression += \"(\"\n                        } else {\n                            expression += \"SELECT \"\n                        }\n                    }\n\n                    const value = column.getEntityValue(valueSet)\n\n                    if (\n                        value === undefined &&\n                        !(\n                            column.isGenerated &&\n                            column.generationStrategy === \"uuid\" &&\n                            !this.connection.driver.isUUIDGenerationSupported()\n                        )\n                    ) {\n                        if (\n                            column.default !== undefined &&\n                            column.default !== null\n                        ) {\n                            // try to use default defined in the column\n                            expression +=\n                                this.connection.driver.normalizeDefault(column)\n                        } else {\n                            expression += \"NULL\" // otherwise simply use NULL and pray if column is nullable\n                        }\n                    } else if (value === null) {\n                        expression += \"NULL\"\n                    } else {\n                        expression += this.createColumnValueExpression(\n                            valueSets,\n                            valueSetIndex,\n                            column,\n                        )\n                    }\n\n                    if (this.connection.driver.options.type !== \"mssql\")\n                        expression += ` AS ${this.escape(column.databaseName)}`\n\n                    if (columnIndex === columns.length - 1) {\n                        if (valueSetIndex === valueSets.length - 1) {\n                            if (\n                                [\"oracle\", \"sap\"].includes(\n                                    this.connection.driver.options.type,\n                                )\n                            ) {\n                                expression +=\n                                    \" FROM \" +\n                                    this.connection.driver.dummyTableName\n                            } else if (\n                                this.connection.driver.options.type === \"mssql\"\n                            ) {\n                                expression += \")\"\n                            }\n                        } else {\n                            if (\n                                [\"oracle\", \"sap\"].includes(\n                                    this.connection.driver.options.type,\n                                ) &&\n                                valueSets.length > 1\n                            ) {\n                                expression +=\n                                    \" FROM \" +\n                                    this.connection.driver.dummyTableName +\n                                    \" UNION ALL \"\n                            } else if (\n                                this.connection.driver.options.type === \"mssql\"\n                            ) {\n                                expression += \"), \"\n                            } else {\n                                expression += \" UNION ALL \"\n                            }\n                        }\n                    } else {\n                        expression += \", \"\n                    }\n                })\n            })\n        } else {\n            // for tables without metadata\n            throw new TypeORMError(\n                'Upsert type \"merge-into\" is not supported without metadata tables',\n            )\n        }\n        expression += `) ${mergeSourceAlias}`\n        if (this.connection.driver.options.type === \"mssql\")\n            expression += ` (${columns\n                .map((column) => this.escape(column.databaseName))\n                .join(\", \")})`\n        return expression\n    }\n\n    /**\n     * Creates list of values needs to be inserted in the VALUES expression.\n     */\n    protected createMergeIntoInsertValuesExpression(\n        mergeSourceAlias: string,\n    ): string {\n        const columns = this.getInsertedColumns()\n\n        let expression = \"\"\n        // if column metadatas are given then apply all necessary operations with values\n        if (columns.length > 0) {\n            columns.forEach((column, columnIndex) => {\n                if (columnIndex === 0) {\n                    expression += \"(\"\n                }\n\n                if (\n                    (column.isGenerated &&\n                        column.generationStrategy === \"uuid\" &&\n                        this.connection.driver.isUUIDGenerationSupported()) ||\n                    (column.isGenerated && column.generationStrategy !== \"uuid\")\n                ) {\n                    expression += `DEFAULT`\n                } else {\n                    expression += `${mergeSourceAlias}.${this.escape(\n                        column.databaseName,\n                    )}`\n                }\n\n                if (columnIndex === columns.length - 1) {\n                    expression += \")\"\n                } else {\n                    expression += \", \"\n                }\n            })\n        } else {\n            // for tables without metadata\n            throw new TypeORMError(\n                'Upsert type \"merge-into\" is not supported without metadata tables',\n            )\n        }\n        if (expression === \"()\") return \"\"\n        return expression\n    }\n\n    /**\n     * Create upsert search condition expression.\n     */\n    protected createUpsertConditionExpression() {\n        if (!this.expressionMap.onUpdate.overwriteCondition) return \"\"\n        const conditionsArray = []\n\n        const whereExpression = this.createWhereClausesExpression(\n            this.expressionMap.onUpdate.overwriteCondition,\n        )\n\n        if (whereExpression.length > 0 && whereExpression !== \"1=1\") {\n            conditionsArray.push(whereExpression)\n        }\n\n        if (this.expressionMap.mainAlias!.hasMetadata) {\n            const metadata = this.expressionMap.mainAlias!.metadata\n            // Adds the global condition of \"non-deleted\" for the entity with delete date columns in select query.\n            if (\n                this.expressionMap.queryType === \"select\" &&\n                !this.expressionMap.withDeleted &&\n                metadata.deleteDateColumn\n            ) {\n                const column = this.expressionMap.aliasNamePrefixingEnabled\n                    ? this.expressionMap.mainAlias!.name +\n                      \".\" +\n                      metadata.deleteDateColumn.propertyName\n                    : metadata.deleteDateColumn.propertyName\n\n                const condition = `${column} IS NULL`\n                conditionsArray.push(condition)\n            }\n\n            if (metadata.discriminatorColumn && metadata.parentEntityMetadata) {\n                const column = this.expressionMap.aliasNamePrefixingEnabled\n                    ? this.expressionMap.mainAlias!.name +\n                      \".\" +\n                      metadata.discriminatorColumn.databaseName\n                    : metadata.discriminatorColumn.databaseName\n\n                const condition = `${column} IN (:...discriminatorColumnValues)`\n                conditionsArray.push(condition)\n            }\n        }\n\n        if (this.expressionMap.extraAppendedAndWhereCondition) {\n            const condition = this.expressionMap.extraAppendedAndWhereCondition\n            conditionsArray.push(condition)\n        }\n\n        let condition = \"\"\n\n        if (!conditionsArray.length) {\n            condition += \"\"\n        } else if (conditionsArray.length === 1) {\n            condition += `${conditionsArray[0]}`\n        } else {\n            condition += `( ${conditionsArray.join(\" ) AND ( \")} )`\n        }\n\n        return condition\n    }\n\n    protected createColumnValueExpression(\n        valueSets: ObjectLiteral[],\n        valueSetIndex: number,\n        column: ColumnMetadata,\n    ): string {\n        const valueSet = valueSets[valueSetIndex]\n        let expression = \"\"\n\n        // extract real value from the entity\n        let value = column.getEntityValue(valueSet)\n\n        // if column is relational and value is an object then get real referenced column value from this object\n        // for example column value is { question: { id: 1 } }, value will be equal to { id: 1 }\n        // and we extract \"1\" from this object\n        /*if (column.referencedColumn && value instanceof Object && !(typeof value === \"function\")) { // todo: check if we still need it since getEntityValue already has similar code\n            value = column.referencedColumn.getEntityValue(value);\n        }*/\n\n        if (!(typeof value === \"function\")) {\n            // make sure our value is normalized by a driver\n            value = this.connection.driver.preparePersistentValue(value, column)\n        }\n\n        // newly inserted entities always have a version equal to 1 (first version)\n        // also, user-specified version must be empty\n        if (column.isVersion && value === undefined) {\n            expression += \"1\"\n\n            // } else if (column.isNestedSetLeft) {\n            //     const tableName = this.connection.driver.escape(column.entityMetadata.tablePath);\n            //     const rightColumnName = this.connection.driver.escape(column.entityMetadata.nestedSetRightColumn!.databaseName);\n            //     const subQuery = `(SELECT c.max + 1 FROM (SELECT MAX(${rightColumnName}) as max from ${tableName}) c)`;\n            //     expression += subQuery;\n            //\n            // } else if (column.isNestedSetRight) {\n            //     const tableName = this.connection.driver.escape(column.entityMetadata.tablePath);\n            //     const rightColumnName = this.connection.driver.escape(column.entityMetadata.nestedSetRightColumn!.databaseName);\n            //     const subQuery = `(SELECT c.max + 2 FROM (SELECT MAX(${rightColumnName}) as max from ${tableName}) c)`;\n            //     expression += subQuery;\n        } else if (column.isDiscriminator) {\n            expression += this.createParameter(\n                this.expressionMap.mainAlias!.metadata.discriminatorValue,\n            )\n            // return \"1\";\n\n            // for create and update dates we insert current date\n            // no, we don't do it because this constant is already in \"default\" value of the column\n            // with extended timestamp functionality, like CURRENT_TIMESTAMP(6) for example\n            // } else if (column.isCreateDate || column.isUpdateDate) {\n            //     return \"CURRENT_TIMESTAMP\";\n\n            // if column is generated uuid and database does not support its generation and custom generated value was not provided by a user - we generate a new uuid value for insertion\n        } else if (\n            column.isGenerated &&\n            column.generationStrategy === \"uuid\" &&\n            !this.connection.driver.isUUIDGenerationSupported() &&\n            value === undefined\n        ) {\n            value = uuidv4()\n            expression += this.createParameter(value)\n\n            if (!(valueSetIndex in this.expressionMap.locallyGenerated)) {\n                this.expressionMap.locallyGenerated[valueSetIndex] = {}\n            }\n            column.setEntityValue(\n                this.expressionMap.locallyGenerated[valueSetIndex],\n                value,\n            )\n\n            // if value for this column was not provided then insert default value\n        } else if (value === undefined) {\n            if (\n                (this.connection.driver.options.type === \"oracle\" &&\n                    valueSets.length > 1) ||\n                DriverUtils.isSQLiteFamily(this.connection.driver) ||\n                this.connection.driver.options.type === \"sap\" ||\n                this.connection.driver.options.type === \"spanner\"\n            ) {\n                // unfortunately sqlite does not support DEFAULT expression in INSERT queries\n                if (column.default !== undefined && column.default !== null) {\n                    // try to use default defined in the column\n                    expression +=\n                        this.connection.driver.normalizeDefault(column)\n                } else if (\n                    this.connection.driver.options.type === \"spanner\" &&\n                    column.isGenerated &&\n                    column.generationStrategy === \"uuid\"\n                ) {\n                    expression += \"GENERATE_UUID()\" // Produces a random universally unique identifier (UUID) as a STRING value.\n                } else {\n                    expression += \"NULL\" // otherwise simply use NULL and pray if column is nullable\n                }\n            } else {\n                expression += \"DEFAULT\"\n            }\n        } else if (\n            value === null &&\n            (this.connection.driver.options.type === \"spanner\" ||\n                this.connection.driver.options.type === \"oracle\")\n        ) {\n            expression += \"NULL\"\n\n            // support for SQL expressions in queries\n        } else if (typeof value === \"function\") {\n            expression += value()\n\n            // just any other regular value\n        } else {\n            if (this.connection.driver.options.type === \"mssql\")\n                value = (\n                    this.connection.driver as SqlServerDriver\n                ).parametrizeValue(column, value)\n\n            // we need to store array values in a special class to make sure parameter replacement will work correctly\n            // if (value instanceof Array)\n            //     value = new ArrayParameter(value);\n\n            const paramName = this.createParameter(value)\n\n            if (\n                (DriverUtils.isMySQLFamily(this.connection.driver) ||\n                    this.connection.driver.options.type === \"aurora-mysql\") &&\n                this.connection.driver.spatialTypes.includes(column.type)\n            ) {\n                const useLegacy = (\n                    this.connection.driver as MysqlDriver | AuroraMysqlDriver\n                ).options.legacySpatialSupport\n                const geomFromText = useLegacy\n                    ? \"GeomFromText\"\n                    : \"ST_GeomFromText\"\n                if (column.srid != null) {\n                    expression += `${geomFromText}(${paramName}, ${column.srid})`\n                } else {\n                    expression += `${geomFromText}(${paramName})`\n                }\n            } else if (\n                DriverUtils.isPostgresFamily(this.connection.driver) &&\n                this.connection.driver.spatialTypes.includes(column.type)\n            ) {\n                if (column.srid != null) {\n                    expression += `ST_SetSRID(ST_GeomFromGeoJSON(${paramName}), ${column.srid})::${column.type}`\n                } else {\n                    expression += `ST_GeomFromGeoJSON(${paramName})::${column.type}`\n                }\n            } else if (\n                this.connection.driver.options.type === \"mssql\" &&\n                this.connection.driver.spatialTypes.includes(column.type)\n            ) {\n                expression +=\n                    column.type +\n                    \"::STGeomFromText(\" +\n                    paramName +\n                    \", \" +\n                    (column.srid || \"0\") +\n                    \")\"\n            } else {\n                expression += paramName\n            }\n        }\n        return expression\n    }\n}\n"], "sourceRoot": ".."}