import { FrameworkType } from '../../interface';
export declare function Framework(): ClassDecorator;
export declare function Plugin(identifier?: string): PropertyDecorator;
export declare function Config(identifier?: string): PropertyDecorator;
export declare function App(typeOrNamespace?: FrameworkType | string): PropertyDecorator;
export declare function Logger(identifier?: string): PropertyDecorator;
export declare function ApplicationContext(): PropertyDecorator;
//# sourceMappingURL=framework.d.ts.map