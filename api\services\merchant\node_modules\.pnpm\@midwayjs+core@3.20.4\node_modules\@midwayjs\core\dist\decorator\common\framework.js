"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationContext = exports.Logger = exports.App = exports.Config = exports.Plugin = exports.Framework = void 0;
const __1 = require("../");
const interface_1 = require("../../interface");
function Framework() {
    return (target) => {
        (0, __1.saveModule)(__1.FRAMEWORK_KEY, target);
        (0, __1.Scope)(interface_1.ScopeEnum.Singleton)(target);
        (0, __1.Provide)()(target);
    };
}
exports.Framework = Framework;
function Plugin(identifier) {
    return (0, __1.createCustomPropertyDecorator)(__1.PLUGIN_KEY, {
        identifier,
    });
}
exports.Plugin = Plugin;
function Config(identifier) {
    return (0, __1.createCustomPropertyDecorator)(__1.CONFIG_KEY, {
        identifier,
    });
}
exports.Config = Config;
function App(typeOrNamespace) {
    return (0, __1.createCustomPropertyDecorator)(__1.APPLICATION_KEY, {
        type: typeOrNamespace,
    });
}
exports.App = App;
function Logger(identifier) {
    return (0, __1.createCustomPropertyDecorator)(__1.LOGGER_KEY, {
        identifier,
    });
}
exports.Logger = Logger;
function ApplicationContext() {
    return (0, __1.createCustomPropertyDecorator)(__1.APPLICATION_CONTEXT_KEY, {});
}
exports.ApplicationContext = ApplicationContext;
//# sourceMappingURL=framework.js.map