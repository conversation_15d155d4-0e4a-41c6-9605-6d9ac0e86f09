"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RpcTestController = void 0;
const decorator_1 = require("@midwayjs/decorator");
const core_1 = require("@cool-midway/core");
const rpc_1 = require("./rpc");
/**
 * 本地开发调试
 */
let RpcTestController = class RpcTestController extends core_1.BaseController {
    /**
     * 测试
     */
    async test() {
        const { name, service, method, params } = this.ctx.request.body;
        return this.rpc.call(name, service, method, params);
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", rpc_1.CoolRpc)
], RpcTestController.prototype, "rpc", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", Object)
], RpcTestController.prototype, "ctx", void 0);
__decorate([
    (0, decorator_1.Post)('/test'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RpcTestController.prototype, "test", null);
RpcTestController = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Controller)('/rpc')
], RpcTestController);
exports.RpcTestController = RpcTestController;
