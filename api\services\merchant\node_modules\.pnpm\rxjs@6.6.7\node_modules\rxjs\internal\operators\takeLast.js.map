{"version": 3, "file": "takeLast.js", "sources": ["../../src/internal/operators/takeLast.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,4CAA2C;AAC3C,2EAA0E;AAC1E,6CAA4C;AA8C5C,SAAgB,QAAQ,CAAI,KAAa;IACvC,OAAO,SAAS,wBAAwB,CAAC,MAAqB;QAC5D,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,aAAK,EAAE,CAAC;SAChB;aAAM;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC;SACjD;IACH,CAAC,CAAC;AACJ,CAAC;AARD,4BAQC;AAED;IACE,0BAAoB,KAAa;QAAb,UAAK,GAAL,KAAK,CAAQ;QAC/B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YAClB,MAAM,IAAI,iDAAuB,CAAC;SACnC;IACH,CAAC;IAED,+BAAI,GAAJ,UAAK,UAAyB,EAAE,MAAW;QACzC,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1E,CAAC;IACH,uBAAC;AAAD,CAAC,AAVD,IAUC;AAOD;IAAoC,sCAAa;IAI/C,4BAAY,WAA0B,EAAU,KAAa;QAA7D,YACE,kBAAM,WAAW,CAAC,SACnB;QAF+C,WAAK,GAAL,KAAK,CAAQ;QAHrD,UAAI,GAAa,IAAI,KAAK,EAAE,CAAC;QAC7B,WAAK,GAAW,CAAC,CAAC;;IAI1B,CAAC;IAES,kCAAK,GAAf,UAAgB,KAAQ;QACtB,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC;QAE3B,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,EAAE;YACvB,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAClB;aAAM;YACL,IAAM,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACrB;IACH,CAAC;IAES,sCAAS,GAAnB;QACE,IAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QAEvB,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,IAAM,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACjE,IAAM,IAAI,GAAI,IAAI,CAAC,IAAI,CAAC;YAExB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;gBAC9B,IAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC;gBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;aAC7B;SACF;QAED,WAAW,CAAC,QAAQ,EAAE,CAAC;IACzB,CAAC;IACH,yBAAC;AAAD,CAAC,AArCD,CAAoC,uBAAU,GAqC7C"}