{"version": 3, "sources": ["../browser/src/driver/better-sqlite3/BetterSqlite3ConnectionOptions.ts"], "names": [], "mappings": "", "file": "BetterSqlite3ConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Sqlite-specific connection options.\n */\nexport interface BetterSqlite3ConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"better-sqlite3\"\n\n    /**\n     * Storage type or path to the storage.\n     */\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"better-sqlite3\")\n     */\n    readonly driver?: any\n\n    /**\n     * Encryption key for for SQLCipher.\n     */\n    readonly key?: string\n\n    /**\n     * Cache size of sqlite statement to speed up queries.\n     * Default: 100.\n     */\n    readonly statementCacheSize?: number\n\n    /**\n     * Function to run before a database is used in typeorm.\n     * You can set pragmas, register plugins or register\n     * functions or aggregates in this function.\n     */\n    readonly prepareDatabase?: (db: any) => void | Promise<void>\n\n    /**\n     * Open the database connection in readonly mode.\n     * Default: false.\n     */\n    readonly readonly?: boolean\n\n    /**\n     * If the database does not exist, an Error will be thrown instead of creating a new file.\n     * This option does not affect in-memory or readonly database connections.\n     * Default: false.\n     */\n    readonly fileMustExist?: boolean\n\n    /**\n     * The number of milliseconds to wait when executing queries\n     * on a locked database, before throwing a SQLITE_BUSY error.\n     * Default: 5000.\n     */\n    readonly timeout?: number\n\n    /**\n     * Provide a function that gets called with every SQL string executed by the database connection.\n     */\n    readonly verbose?: Function\n\n    /**\n     * Relative or absolute path to the native addon (better_sqlite3.node).\n     */\n    readonly nativeBinding?: string\n\n    readonly poolSize?: never\n\n    /**\n     * Enables WAL mode. By default its disabled.\n     *\n     * @see https://www.sqlite.org/wal.html\n     */\n    readonly enableWAL?: boolean\n}\n"], "sourceRoot": "../.."}