import { IProviderInstance } from './provider';
import { ICommandInstance } from './plugin';
export interface ILog {
    log: (...any: any[]) => void;
    error?: (...any: any[]) => void;
    debug?: (...any: any[]) => void;
    warn?: (...any: any[]) => void;
}
export interface IOptions {
    provider?: string;
    options?: any;
    commands?: string[];
    service?: any;
    config?: any;
    log?: ILog;
    extensions?: {
        [extensionName: string]: any;
    };
    displayUsage?: any;
    point?: any;
    npm?: string;
    stopLifecycle?: string;
    disableAutoLoad?: boolean;
    cwd?: string;
    outputLevel?: CLIOutputLevel;
}
export declare enum CLIOutputLevel {
    None = 0,
    Error = 1,
    Warn = 2,
    Info = 3,
    All = 4
}
export interface ICommandCore {
    addPlugin(plugin: IPlugin): void;
    store: IStore<any>;
    options: any;
}
interface IStore<T> {
    [index: string]: T;
    [index: number]: T;
}
export interface ICoreInstance {
    classes: any;
    cli: ILog;
    config: any;
    getProvider(providerName: string): IProviderInstance;
    invoke(commandsArray?: string[], allowEntryPoints?: boolean, options?: any): any;
    pluginManager: ICommandCore;
    store: IStore<any>;
    debug: any;
    processedInput: {
        options: any;
        commands: string[];
    };
    cwd?: string;
    coreOptions?: IOptions;
    setProvider(providerName: string, providerInstance: IProviderInstance): any;
    spawn(commandsArray: string[], options?: any): any;
    [otherProp: string]: any;
}
export declare class IPlugin {
    constructor(coreInstance: ICoreInstance, options: any);
}
export interface ICommands {
    [command: string]: {
        usage: string;
        type: string;
        lifecycleEvents: string[];
        rank: number;
        options: any;
        origin: ICommandInstance[];
        alias?: string;
        commands: ICommands;
    };
}
export type IHookFun = () => Promise<void> | void;
export interface IHooks {
    [lifestyle: string]: IHookFun[];
}
export {};
//# sourceMappingURL=commandCore.d.ts.map