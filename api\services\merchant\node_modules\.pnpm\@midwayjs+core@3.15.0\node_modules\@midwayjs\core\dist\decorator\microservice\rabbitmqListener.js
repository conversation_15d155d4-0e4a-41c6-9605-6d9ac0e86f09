"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RabbitMQListener = void 0;
const __1 = require("../");
function RabbitMQListener(queueName, options = {}) {
    return (target, propertyKey) => {
        options.queueName = queueName;
        options.propertyKey = propertyKey;
        (0, __1.attachPropertyDataToClass)(__1.MS_CONSUMER_KEY, options, target, propertyKey);
    };
}
exports.RabbitMQListener = RabbitMQListener;
//# sourceMappingURL=rabbitmqListener.js.map