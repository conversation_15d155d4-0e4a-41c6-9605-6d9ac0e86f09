import { IEnvironmentService, ModuleLoadType } from '../interface';
export declare class MidwayEnvironmentService implements IEnvironmentService {
    protected environment: string;
    protected moduleLoadType: ModuleLoadType;
    getCurrentEnvironment(): string;
    setCurrentEnvironment(environment: string): void;
    isDevelopmentEnvironment(): boolean;
    setModuleLoadType(moduleLoadType: ModuleLoadType): void;
    getModuleLoadType(): ModuleLoadType;
    isPkgEnvironment(): boolean;
}
//# sourceMappingURL=environmentService.d.ts.map