import { BaseEntity } from '@cool-midway/core';
import { Column, Entity } from 'typeorm';

/**
 * 创作者认证
 */
@Entity('heritage_auth')
export class HeritageAuthEntity extends BaseEntity {
  @Column({ comment: '类型：0-手工艺人，1-非遗传承人' })
  type: number;

  @Column({ comment: '个人姓名（手工艺人）', nullable: true })
  name?: string;

  @Column({ comment: '公司名称（非遗传承人）', nullable: true })
  companyName?: string;

  @Column({ comment: '法人代表（非遗传承人）', nullable: true })
  legalPerson?: string;

  @Column({ comment: '联系电话' })
  phone: string;

  @Column({ comment: '邮箱', nullable: true })
  email?: string;

  @Column({ comment: '身份证号', nullable: true })
  idCard?: string;

  @Column({ comment: '统一社会信用代码（企业）', nullable: true })
  creditCode?: string;

  @Column({ comment: '技艺分类' })
  category: string;

  @Column({ comment: '审核状态：0-待审核，1-已通过，2-已拒绝', default: 0 })
  status: number;

  @Column({ comment: '个人/企业简介', type: 'text', nullable: true })
  intro?: string;

  @Column({ comment: '技艺描述', type: 'text', nullable: true })
  skillDescription?: string;

  @Column({ comment: '身份证正面', nullable: true })
  idCardFront?: string;

  @Column({ comment: '身份证反面', nullable: true })
  idCardBack?: string;

  @Column({ comment: '技能证书', nullable: true })
  skillCert?: string;

  @Column({ comment: '非遗传承证书', nullable: true })
  heritageCert?: string;

  @Column({ comment: '作品样例（JSON数组）', type: 'json', nullable: true })
  workSamples?: string[];

  @Column({ comment: '审核备注', type: 'text', nullable: true })
  remark?: string;
}
