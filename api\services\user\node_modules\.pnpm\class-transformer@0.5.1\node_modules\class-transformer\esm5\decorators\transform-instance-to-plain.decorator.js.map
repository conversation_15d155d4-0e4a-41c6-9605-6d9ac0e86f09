{"version": 3, "file": "transform-instance-to-plain.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/transform-instance-to-plain.decorator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAC;AAGvD;;;;GAIG;AACH,MAAM,UAAU,wBAAwB,CAAC,MAA8B;IACrE,OAAO,UAAU,MAA2B,EAAE,WAA4B,EAAE,UAA8B;QACxG,IAAM,gBAAgB,GAAqB,IAAI,gBAAgB,EAAE,CAAC;QAClE,IAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG;YAAU,cAAc;iBAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;gBAAd,yBAAc;;YACzC,IAAM,MAAM,GAAQ,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,IAAM,SAAS,GACb,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;YAChH,OAAO,SAAS;gBACd,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAC,IAAS,IAAK,OAAA,gBAAgB,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,EAA9C,CAA8C,CAAC;gBAC5E,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["import { ClassTransformer } from '../ClassTransformer';\nimport { ClassTransformOptions } from '../interfaces';\n\n/**\n * Transform the object from class to plain object and return only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformInstanceToPlain(params?: ClassTransformOptions): MethodDecorator {\n  return function (target: Record<string, any>, propertyKey: string | Symbol, descriptor: PropertyDescriptor): void {\n    const classTransformer: ClassTransformer = new ClassTransformer();\n    const originalMethod = descriptor.value;\n\n    descriptor.value = function (...args: any[]): Record<string, any> {\n      const result: any = originalMethod.apply(this, args);\n      const isPromise =\n        !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n      return isPromise\n        ? result.then((data: any) => classTransformer.instanceToPlain(data, params))\n        : classTransformer.instanceToPlain(result, params);\n    };\n  };\n}\n"]}