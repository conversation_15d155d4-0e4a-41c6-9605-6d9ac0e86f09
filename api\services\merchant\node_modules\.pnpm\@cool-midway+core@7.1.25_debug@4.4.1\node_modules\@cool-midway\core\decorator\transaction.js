"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolTransaction = exports.COOL_TRANSACTION = void 0;
const decorator_1 = require("@midwayjs/decorator");
// 装饰器内部的唯一 id
exports.COOL_TRANSACTION = "decorator:cool_transaction";
function CoolTransaction(option) {
    return (0, decorator_1.createCustomMethodDecorator)(exports.COOL_TRANSACTION, option);
}
exports.CoolTransaction = CoolTransaction;
