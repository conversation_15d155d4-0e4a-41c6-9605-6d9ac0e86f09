"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolEps = void 0;
const core_1 = require("@midwayjs/core");
const _ = require("lodash");
const core_2 = require("@midwayjs/core");
const typeorm_1 = require("@midwayjs/typeorm");
const data_1 = require("../tag/data");
const tag_1 = require("../decorator/tag");
/**
 * 实体路径
 */
let CoolEps = class CoolEps {
    constructor() {
        this.admin = {};
        this.app = {};
        this.module = {};
    }
    // @Init()
    async init() {
        var _a, _b, _c, _d;
        if (!this.epsConfig)
            return;
        const entitys = await this.entity();
        const controllers = await this.controller();
        const routers = await this.router();
        await this.modules();
        const adminArr = [];
        const appArr = [];
        for (const controller of controllers) {
            const { prefix, module, curdOption, routerOptions } = controller;
            const pageQueryOp = await this.getPageOp(curdOption);
            const name = (_a = curdOption === null || curdOption === void 0 ? void 0 : curdOption.entity) === null || _a === void 0 ? void 0 : _a.name;
            (_.startsWith(prefix, '/admin/') ? adminArr : appArr).push({
                module,
                info: {
                    type: {
                        name: prefix.split('/').pop(),
                        description: (routerOptions === null || routerOptions === void 0 ? void 0 : routerOptions.description) || '',
                    },
                },
                api: routers[prefix],
                name,
                columns: entitys[name] || [],
                pageQueryOp: {
                    keyWordLikeFields: ((_b = pageQueryOp === null || pageQueryOp === void 0 ? void 0 : pageQueryOp.keyWordLikeFields) === null || _b === void 0 ? void 0 : _b.map(field => field.includes('.') ? field : `a.${field}`)) || [],
                    fieldEq: ((_c = pageQueryOp === null || pageQueryOp === void 0 ? void 0 : pageQueryOp.fieldEq) === null || _c === void 0 ? void 0 : _c.map(field => typeof field === 'string'
                        ? field.includes('.')
                            ? field
                            : `a.${field}`
                        : field)) || [],
                    fieldLike: ((_d = pageQueryOp === null || pageQueryOp === void 0 ? void 0 : pageQueryOp.fieldLike) === null || _d === void 0 ? void 0 : _d.map(field => typeof field === 'string'
                        ? field.includes('.')
                            ? field
                            : `a.${field}`
                        : field)) || [],
                },
                pageColumns: await this.pageColumns(entitys, curdOption),
                prefix,
            });
        }
        this.admin = _.groupBy(adminArr, 'module');
        this.app = _.groupBy(appArr, 'module');
    }
    /**
     * 获取分页查询配置
     * @param curdOption
     * @returns
     */
    async getPageOp(curdOption) {
        let pageQueryOp = curdOption === null || curdOption === void 0 ? void 0 : curdOption.pageQueryOp;
        if (typeof pageQueryOp === 'function') {
            pageQueryOp = await pageQueryOp();
        }
        return pageQueryOp;
    }
    /**
     * 处理列
     * @param entitys
     * @param entityColumns
     * @param curdOption
     */
    async pageColumns(entitys, curdOption) {
        var _a;
        const pageQueryOp = await this.getPageOp(curdOption);
        // 检查 pageQueryOp 是否为对象且具有 select 属性
        if (pageQueryOp &&
            typeof pageQueryOp === 'object' &&
            'select' in pageQueryOp &&
            ((_a = curdOption === null || curdOption === void 0 ? void 0 : curdOption.entity) === null || _a === void 0 ? void 0 : _a.name)) {
            const select = pageQueryOp.select;
            const join = pageQueryOp.join || [];
            // 所有的关联entitys
            const joinEntitys = [{ name: curdOption.entity.name, alias: 'a' }];
            if (join.length > 0) {
                joinEntitys.push(...join.map(item => {
                    return { name: item.entity.name, alias: item.alias };
                }));
            }
            // 处理 select
            const result = [];
            for (const selectItem of select) {
                // 处理 'a.*' 这种情况
                if (selectItem.endsWith('.*')) {
                    const alias = selectItem.split('.')[0];
                    const entity = joinEntitys.find(e => e.alias === alias);
                    if (entity) {
                        const entityColumns = entitys[entity.name] || [];
                        result.push(...entityColumns.map(e => {
                            return {
                                ...e,
                                source: `${alias}.${e.propertyName}`,
                            };
                        }));
                    }
                    continue;
                }
                // 处理单个字段，如 'b.name' 或 'b.name as userName'
                const asRegex = /\s+as\s+/i;
                const [field, asName] = selectItem.split(asRegex).map(s => s.trim());
                const [alias, fieldName] = field.split('.');
                const entity = joinEntitys.find(e => e.alias === alias);
                if (entity) {
                    const entityColumns = entitys[entity.name] || [];
                    const column = entityColumns.find(col => col.propertyName === fieldName);
                    if (column) {
                        result.push({
                            ...column,
                            propertyName: asName || column.propertyName,
                            source: `${alias}.${column.propertyName}`,
                        });
                    }
                }
            }
            // 将 createTime 和 updateTime 移到末尾
            const finalResult = [...result];
            const timeFields = ['createTime', 'updateTime'];
            const timeColumns = [];
            // 先找出并删除所有时间字段
            for (let i = finalResult.length - 1; i >= 0; i--) {
                if (timeFields.includes(finalResult[i].propertyName)) {
                    timeColumns.unshift(finalResult.splice(i, 1)[0]);
                }
            }
            // 将时间字段添加到末尾
            finalResult.push(...timeColumns);
            return finalResult;
        }
        return [];
    }
    /**
     * 模块信息
     * @param module
     */
    async modules(module) {
        for (const key in this.moduleConfig) {
            const config = this.moduleConfig[key];
            this.module[key] = {
                name: config.name,
                description: config.description,
            };
        }
        return module ? this.module[module] : this.module;
    }
    /**
     * 所有controller
     * @returns
     */
    async controller() {
        const result = [];
        const controllers = (0, core_1.listModule)(core_1.CONTROLLER_KEY);
        for (const controller of controllers) {
            result.push((0, core_1.getClassMetadata)(core_1.CONTROLLER_KEY, controller));
        }
        return result;
    }
    /**
     * 所有路由
     * @returns
     */
    async router() {
        let ignoreUrls = this.coolUrlTagData.byKey(tag_1.TagTypes.IGNORE_TOKEN);
        if (_.isEmpty(ignoreUrls)) {
            ignoreUrls = [];
        }
        return _.groupBy((await this.midwayWebRouterService.getFlattenRouterTable()).map(item => {
            return {
                method: item.requestMethod,
                path: item.url,
                summary: item.summary,
                dts: {},
                tag: '',
                prefix: item.prefix,
                ignoreToken: ignoreUrls.includes(item.prefix + item.url),
            };
        }), 'prefix');
    }
    /**
     * 所有实体
     * @returns
     */
    async entity() {
        const result = {};
        const dataSourceNames = this.typeORMDataSourceManager.getDataSourceNames();
        for (const dataSourceName of dataSourceNames) {
            const entityMetadatas = await this.typeORMDataSourceManager.getDataSource(dataSourceName).entityMetadatas;
            for (const entityMetadata of entityMetadatas) {
                const commColums = [];
                let columns = entityMetadata.columns;
                if (entityMetadata.tableType != 'regular')
                    continue;
                columns = _.filter(columns.map(e => {
                    return {
                        propertyName: e.propertyName,
                        type: typeof e.type === 'string' ? e.type : e.type.name.toLowerCase(),
                        length: e.length,
                        comment: e.comment,
                        nullable: e.isNullable,
                        defaultValue: e.default,
                        dict: e['dict'],
                        source: `a.${e.propertyName}`,
                    };
                }), o => {
                    if (['createTime', 'updateTime'].includes(o.propertyName)) {
                        commColums.push(o);
                    }
                    return (o &&
                        !['createTime', 'updateTime', 'tenantId'].includes(o.propertyName));
                }).concat(commColums);
                result[entityMetadata.name] = columns;
            }
        }
        return result;
    }
};
exports.CoolEps = CoolEps;
__decorate([
    (0, core_2.Inject)(),
    __metadata("design:type", core_2.MidwayWebRouterService)
], CoolEps.prototype, "midwayWebRouterService", void 0);
__decorate([
    (0, core_2.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolEps.prototype, "typeORMDataSourceManager", void 0);
__decorate([
    (0, core_2.Config)('cool.eps'),
    __metadata("design:type", Boolean)
], CoolEps.prototype, "epsConfig", void 0);
__decorate([
    (0, core_2.Config)('module'),
    __metadata("design:type", Object)
], CoolEps.prototype, "moduleConfig", void 0);
__decorate([
    (0, core_2.Inject)(),
    __metadata("design:type", data_1.CoolUrlTagData)
], CoolEps.prototype, "coolUrlTagData", void 0);
exports.CoolEps = CoolEps = __decorate([
    (0, core_1.Provide)(),
    (0, core_2.Scope)(core_2.ScopeEnum.Singleton)
], CoolEps);
