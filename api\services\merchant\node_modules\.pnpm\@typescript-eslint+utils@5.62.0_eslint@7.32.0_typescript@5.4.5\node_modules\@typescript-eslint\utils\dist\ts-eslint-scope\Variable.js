"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Variable = void 0;
const variable_1 = __importDefault(require("eslint-scope/lib/variable"));
const Variable = variable_1.default;
exports.Variable = Variable;
//# sourceMappingURL=Variable.js.map