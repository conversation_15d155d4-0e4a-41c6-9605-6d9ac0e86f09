{"version": 3, "sources": ["../../src/commands/MigrationRevertCommand.ts"], "names": [], "mappings": ";;;;AAEA,6DAAyD;AACzD,wDAAuB;AACvB,8DAA6B;AAC7B,iDAA6C;AAE7C;;GAEG;AACH,MAAa,sBAAsB;IAAnC;QACI,YAAO,GAAG,kBAAkB,CAAA;QAC5B,aAAQ,GAAG,kCAAkC,CAAA;IAwEjD,CAAC;IAtEG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,MAAM,CAAC,YAAY,EAAE;YAClB,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC;aACD,MAAM,CAAC,aAAa,EAAE;YACnB,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,SAAS;YAClB,QAAQ,EACJ,0FAA0F;SACjG,CAAC;aACD,MAAM,CAAC,MAAM,EAAE;YACZ,KAAK,EAAE,GAAG;YACV,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,+BAA+B;SAC5C,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,EAAE;gBACf,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,CAAC;aACxC,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,OAAO,GAAG;gBACZ,WAAW,EACP,UAAU,CAAC,OAAO,CAAC,yBAAyB;oBAC3C,KAAiC;gBACtC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;aACjB,CAAA;YAED,QAAQ,IAAI,CAAC,CAAC,EAAE,CAAC;gBACb,KAAK,KAAK;oBACN,OAAO,CAAC,WAAW,GAAG,KAAK,CAAA;oBAC3B,MAAK;gBACT,KAAK,MAAM,CAAC;gBACZ,KAAK,OAAO;oBACR,OAAO,CAAC,WAAW,GAAG,MAAM,CAAA;oBAC5B,MAAK;gBACT,KAAK,MAAM;oBACP,OAAO,CAAC,WAAW,GAAG,MAAM,CAAA;oBAC5B,MAAK;gBACT,QAAQ;gBACR,OAAO;YACX,CAAC;YAED,MAAM,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAA;YAC3C,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAA;YAE9D,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AA1ED,wDA0EC", "file": "MigrationRevertCommand.js", "sourcesContent": ["import { DataSource } from \"../data-source/DataSource\"\nimport * as yargs from \"yargs\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport path from \"path\"\nimport process from \"process\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Reverts last migration command.\n */\nexport class MigrationRevertCommand implements yargs.CommandModule {\n    command = \"migration:revert\"\n    describe = \"Reverts last executed migration.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .option(\"dataSource\", {\n                alias: \"d\",\n                describe:\n                    \"Path to the file where your DataSource instance is defined.\",\n                demandOption: true,\n            })\n            .option(\"transaction\", {\n                alias: \"t\",\n                default: \"default\",\n                describe:\n                    \"Indicates if transaction should be used or not for migration revert. Enabled by default.\",\n            })\n            .option(\"fake\", {\n                alias: \"f\",\n                type: \"boolean\",\n                default: false,\n                describe: \"Fakes reverting the migration\",\n            })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                subscribers: [],\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: [\"query\", \"error\", \"schema\"],\n            })\n            await dataSource.initialize()\n\n            const options = {\n                transaction:\n                    dataSource.options.migrationsTransactionMode ??\n                    (\"all\" as \"all\" | \"none\" | \"each\"),\n                fake: !!args.f,\n            }\n\n            switch (args.t) {\n                case \"all\":\n                    options.transaction = \"all\"\n                    break\n                case \"none\":\n                case \"false\":\n                    options.transaction = \"none\"\n                    break\n                case \"each\":\n                    options.transaction = \"each\"\n                    break\n                default:\n                // noop\n            }\n\n            await dataSource.undoLastMigration(options)\n            await dataSource.destroy()\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during migration revert:\", err)\n\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}