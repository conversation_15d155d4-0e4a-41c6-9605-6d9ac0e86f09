{"version": 3, "file": "IsBase58.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBase58.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,iBAAiB,MAAM,wBAAwB,CAAC;AAEvD,MAAM,CAAC,MAAM,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,SAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,kCAAkC,EAAE,iBAAiB,CAAC;SAC/G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBase58Validator from 'validator/lib/isBase58';\n\nexport const IS_BASE58 = 'isBase58';\n\n/**\n * Checks if a string is base58 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function isBase58(value: unknown): boolean {\n  return typeof value === 'string' && isBase58Validator(value);\n}\n\n/**\n * Checks if a string is base58 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function IsBase58(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BASE58,\n      validator: {\n        validate: (value, args): boolean => isBase58(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be base58 encoded', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}