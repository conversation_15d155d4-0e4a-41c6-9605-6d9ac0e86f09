import { MidwayCache } from '@midwayjs/cache-manager';
import { MidwayDecoratorService } from '@midwayjs/core';
import { TypeORMDataSourceManager } from '@midwayjs/typeorm';
import { CoolUrlTagData } from '../tag/data';
/**
 * 装饰器
 */
export declare class CoolDecorator {
    typeORMDataSourceManager: TypeORMDataSourceManager;
    decoratorService: MidwayDecoratorService;
    midwayCache: MidwayCache;
    coolUrlTagData: CoolUrlTagData;
    init(): Promise<void>;
    /**
     * 缓存
     */
    cache(): Promise<void>;
    /**
     * 事务
     */
    transaction(): Promise<void>;
}
