{"name": "@midwayjs/locate", "version": "1.8.1", "description": "Locating midway application locations", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"test": "midway-bin test --ts", "cov": "midway-bin cov --ts", "build": "midway-bin build -c"}, "keywords": ["midway", "locate"], "files": ["dist", "src"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@types/mocha": "^5.2.7", "@types/node": "^13.1.2", "midway-bin": "1", "typescript": "^3.7.4"}, "dependencies": {"fs-extra": "^8.1.0", "globby": "^10.0.1"}}