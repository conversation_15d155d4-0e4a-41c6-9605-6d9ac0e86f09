"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockContext = exports.restoreMocks = exports.restoreAllMocks = exports.mockProperty = exports.mockClassProperty = exports.mockHeader = exports.mockSession = void 0;
const core_1 = require("@midwayjs/core");
function getMockService(app) {
    if (!app) {
        app = (0, core_1.getCurrentMainApp)();
    }
    if (!app) {
        return null;
    }
    if (!app.getApplicationContext) {
        throw new Error('[mock]: app.getApplicationContext is undefined.');
    }
    const applicationContext = app.getApplicationContext();
    const mockService = applicationContext.get(core_1.MidwayMockService);
    if (!mockService) {
        throw new Error('[mock]: MidwayMockService is undefined.');
    }
    return mockService;
}
function mockSession(app, key, value, group = 'default') {
    const mockService = getMockService(app);
    mockService.mockContext(app, (ctx) => {
        if (!ctx.session) {
            ctx.session = {};
        }
        ctx.session[key] = value;
    }, undefined, group);
}
exports.mockSession = mockSession;
function mockHeader(app, headerKey, headerValue, group = 'default') {
    const mockService = getMockService(app);
    mockService.mockContext(app, (ctx) => {
        ctx.headers[headerKey] = headerValue;
    }, undefined, group);
}
exports.mockHeader = mockHeader;
function mockClassProperty(clzz, propertyName, value, group = 'default') {
    const mockService = getMockService();
    if (!mockService) {
        return core_1.MidwayMockService.mockClassProperty(clzz, propertyName, value, group);
    }
    else {
        return mockService.mockClassProperty(clzz, propertyName, value, group);
    }
}
exports.mockClassProperty = mockClassProperty;
function mockProperty(obj, key, value, group = 'default') {
    const mockService = getMockService();
    if (!mockService) {
        return core_1.MidwayMockService.mockProperty(obj, key, value, group);
    }
    else {
        return mockService.mockProperty(obj, key, value, group);
    }
}
exports.mockProperty = mockProperty;
function restoreAllMocks() {
    const mockService = getMockService();
    if (mockService) {
        mockService.restoreAll();
    }
    else {
        core_1.MidwayMockService.prepareMocks = [];
    }
}
exports.restoreAllMocks = restoreAllMocks;
function restoreMocks(group = 'default') {
    const mockService = getMockService();
    if (mockService) {
        mockService.restore(group);
    }
}
exports.restoreMocks = restoreMocks;
function mockContext(app, key, value, group = 'default') {
    const mockService = getMockService(app);
    mockService.mockContext(app, key, value, group);
}
exports.mockContext = mockContext;
//# sourceMappingURL=mock.js.map