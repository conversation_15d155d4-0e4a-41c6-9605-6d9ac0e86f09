"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const core_2 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const filter_1 = require("./exception/filter");
const func_1 = require("./util/func");
const koa = require("@midwayjs/koa");
const config_1 = require("./module/config");
const import_1 = require("./module/import");
const event_1 = require("./event");
const eps_1 = require("./rest/eps");
const decorator_1 = require("./decorator");
const cache = require("@midwayjs/cache-manager");
const location_1 = require("./util/location");
let CoolConfiguration = class CoolConfiguration {
    async onReady(container) {
        this.coolEventManager.emit('onReady');
        this.coolEventManager.globalEmit('onReadyOnce', true);
        // 处理模块配置
        await container.getAsync(config_1.CoolModuleConfig);
        // 常用函数处理
        await container.getAsync(func_1.FuncUtil);
        // 异常处理
        this.app.useFilter([filter_1.CoolExceptionFilter]);
        // 装饰器
        await container.getAsync(decorator_1.CoolDecorator);
        // 注册一个路由，用于处理静态资源
        this.webRouterService.addRouter(async (ctx) => {
            ctx.redirect('/index.html');
        }, {
            url: '/',
            requestMethod: 'GET',
        });
    }
    async onConfigLoad(container, app) {
        await container.getAsync(location_1.LocationUtil);
        // 替换app的getBaseDir
        app.getBaseDir = () => {
            return container.get(location_1.LocationUtil).getDistPath();
        };
    }
    async onServerReady(container) {
        // 事件
        await (await container.getAsync(event_1.CoolEventManager)).init();
        // 导入模块数据
        (await container.getAsync(import_1.CoolModuleImport)).init();
        // 实体与路径
        const eps = await container.getAsync(eps_1.CoolEps);
        eps.init();
        this.coolEventManager.emit('onServerReady');
        this.coolEventManager.globalEmit('onServerReadyOnce', true);
        // location.clean();
    }
};
exports.CoolConfiguration = CoolConfiguration;
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], CoolConfiguration.prototype, "coreLogger", void 0);
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], CoolConfiguration.prototype, "app", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", event_1.CoolEventManager)
], CoolConfiguration.prototype, "coolEventManager", void 0);
__decorate([
    (0, core_1.Config)(core_1.ALL),
    __metadata("design:type", Object)
], CoolConfiguration.prototype, "allConfig", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayWebRouterService)
], CoolConfiguration.prototype, "webRouterService", void 0);
exports.CoolConfiguration = CoolConfiguration = __decorate([
    (0, core_2.Configuration)({
        namespace: 'cool',
        imports: [cache],
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], CoolConfiguration);
