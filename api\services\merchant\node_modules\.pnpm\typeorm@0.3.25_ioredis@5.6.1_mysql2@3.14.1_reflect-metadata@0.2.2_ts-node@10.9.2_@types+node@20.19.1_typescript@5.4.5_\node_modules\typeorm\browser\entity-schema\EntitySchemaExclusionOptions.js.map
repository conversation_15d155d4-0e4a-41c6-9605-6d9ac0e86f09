{"version": 3, "sources": ["../browser/src/entity-schema/EntitySchemaExclusionOptions.ts"], "names": [], "mappings": "", "file": "EntitySchemaExclusionOptions.js", "sourcesContent": ["export interface EntitySchemaExclusionOptions {\n    /**\n     * Exclusion constraint name.\n     */\n    name?: string\n\n    /**\n     * Exclusion expression.\n     */\n    expression: string\n}\n"], "sourceRoot": ".."}