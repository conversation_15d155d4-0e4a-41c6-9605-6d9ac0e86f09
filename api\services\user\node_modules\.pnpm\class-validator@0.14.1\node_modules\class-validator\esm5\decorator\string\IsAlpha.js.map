{"version": 3, "file": "IsAlpha.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAlpha.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,gBAAgB,MAAM,uBAAuB,CAAC;AAGrD,MAAM,CAAC,IAAM,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,KAAc,EAAE,MAAgC;IACtE,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtE,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,OAAO,CAAC,MAAgC,EAAE,iBAAqC;IAC7F,OAAO,UAAU,CACf;QACE,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,CAAC,MAAM,CAAC;QACrB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,OAAO,CAAC,KAAK,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,WAAW,CAAC,CAAC,CAAC,CAAC,EAApC,CAAoC;YACxE,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,8CAA8C,EAA3D,CAA2D,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAlphaValidator from 'validator/lib/isAlpha';\nimport * as ValidatorJS from 'validator';\n\nexport const IS_ALPHA = 'isAlpha';\n\n/**\n * Checks if the string contains only letters (a-zA-Z).\n * If given value is not a string, then it returns false.\n */\nexport function isAlpha(value: unknown, locale?: ValidatorJS.AlphaLocale): boolean {\n  return typeof value === 'string' && isAlphaValidator(value, locale);\n}\n\n/**\n * Checks if the string contains only letters (a-zA-Z).\n * If given value is not a string, then it returns false.\n */\nexport function IsAlpha(locale?: ValidatorJS.AlphaLocale, validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ALPHA,\n      constraints: [locale],\n      validator: {\n        validate: (value, args): boolean => isAlpha(value, args?.constraints[0]),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only letters (a-zA-Z)',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}