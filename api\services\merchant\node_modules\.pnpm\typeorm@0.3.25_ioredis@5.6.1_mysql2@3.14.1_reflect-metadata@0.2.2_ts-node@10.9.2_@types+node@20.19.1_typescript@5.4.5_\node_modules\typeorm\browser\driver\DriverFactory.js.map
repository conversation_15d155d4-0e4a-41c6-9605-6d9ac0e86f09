{"version": 3, "sources": ["../browser/src/driver/DriverFactory.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,6BAA6B,CAAA;AAChE,OAAO,EAAE,eAAe,EAAE,MAAM,+BAA+B,CAAA;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AACnD,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAA;AAC7D,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AACpD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AACpD,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAA;AACvD,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAA;AACpE,OAAO,EAAE,kBAAkB,EAAE,MAAM,mCAAmC,CAAA;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAA;AACjD,OAAO,EAAE,cAAc,EAAE,MAAM,2BAA2B,CAAA;AAC1D,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAA;AAC5D,OAAO,EAAE,iBAAiB,EAAE,MAAM,kCAAkC,CAAA;AACpE,OAAO,EAAE,oBAAoB,EAAE,MAAM,wCAAwC,CAAA;AAG7E,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAC3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,sCAAsC,CAAA;AAC1E,OAAO,EAAE,eAAe,EAAE,MAAM,6BAA6B,CAAA;AAC7D,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAA;AAEvD;;GAEG;AACH,MAAM,OAAO,aAAa;IACtB;;OAEG;IACH,MAAM,CAAC,UAAsB;QACzB,MAAM,EAAE,IAAI,EAAE,GAAG,UAAU,CAAC,OAAO,CAAA;QACnC,QAAQ,IAAI,EAAE,CAAC;YACX,KAAK,OAAO;gBACR,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,UAAU;gBACX,OAAO,IAAI,cAAc,CAAC,UAAU,CAAC,CAAA;YACzC,KAAK,aAAa;gBACd,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,KAAK;gBACN,OAAO,IAAI,SAAS,CAAC,UAAU,CAAC,CAAA;YACpC,KAAK,SAAS;gBACV,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,QAAQ;gBACT,OAAO,IAAI,YAAY,CAAC,UAAU,CAAC,CAAA;YACvC,KAAK,gBAAgB;gBACjB,OAAO,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAA;YAC9C,KAAK,SAAS;gBACV,OAAO,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;YACxC,KAAK,cAAc;gBACf,OAAO,IAAI,kBAAkB,CAAC,UAAU,CAAC,CAAA;YAC7C,KAAK,cAAc;gBACf,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAA;YAC5C,KAAK,OAAO;gBACR,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,QAAQ;gBACT,OAAO,IAAI,YAAY,CAAC,UAAU,CAAC,CAAA;YACvC,KAAK,OAAO;gBACR,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,SAAS;gBACV,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,CAAA;YACtC,KAAK,MAAM;gBACP,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC,MAAM,EAAE,CAAA;YACrD,KAAK,cAAc;gBACf,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAA;YAC5C,KAAK,iBAAiB;gBAClB,OAAO,IAAI,oBAAoB,CAAC,UAAU,CAAC,CAAA;YAC/C,KAAK,WAAW;gBACZ,OAAO,IAAI,eAAe,CAAC,UAAU,CAAC,CAAA;YAC1C,KAAK,SAAS;gBACV,OAAO,IAAI,aAAa,CAAC,UAAU,CAAC,CAAA;YACxC;gBACI,MAAM,IAAI,kBAAkB,CAAC,IAAI,EAAE;oBAC/B,cAAc;oBACd,iBAAiB;oBACjB,gBAAgB;oBAChB,WAAW;oBACX,aAAa;oBACb,SAAS;oBACT,MAAM;oBACN,SAAS;oBACT,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,cAAc;oBACd,QAAQ;oBACR,UAAU;oBACV,cAAc;oBACd,KAAK;oBACL,QAAQ;oBACR,OAAO;oBACP,SAAS;iBACZ,CAAC,CAAA;QACV,CAAC;IACL,CAAC;CACJ", "file": "DriverFactory.js", "sourcesContent": ["import { MissingDriverError } from \"../error/MissingDriverError\"\nimport { CockroachDriver } from \"./cockroachdb/CockroachDriver\"\nimport { MongoDriver } from \"./mongodb/MongoDriver\"\nimport { SqlServerDriver } from \"./sqlserver/SqlServerDriver\"\nimport { OracleDriver } from \"./oracle/OracleDriver\"\nimport { SqliteDriver } from \"./sqlite/SqliteDriver\"\nimport { CordovaDriver } from \"./cordova/CordovaDriver\"\nimport { ReactNativeDriver } from \"./react-native/ReactNativeDriver\"\nimport { NativescriptDriver } from \"./nativescript/NativescriptDriver\"\nimport { SqljsDriver } from \"./sqljs/SqljsDriver\"\nimport { MysqlDriver } from \"./mysql/MysqlDriver\"\nimport { PostgresDriver } from \"./postgres/PostgresDriver\"\nimport { ExpoDriverFactory } from \"./expo/ExpoDriverFactory\"\nimport { AuroraMysqlDriver } from \"./aurora-mysql/AuroraMysqlDriver\"\nimport { AuroraPostgresDriver } from \"./aurora-postgres/AuroraPostgresDriver\"\nimport { Driver } from \"./Driver\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { SapDriver } from \"./sap/SapDriver\"\nimport { BetterSqlite3Driver } from \"./better-sqlite3/BetterSqlite3Driver\"\nimport { CapacitorDriver } from \"./capacitor/CapacitorDriver\"\nimport { SpannerDriver } from \"./spanner/SpannerDriver\"\n\n/**\n * Helps to create drivers.\n */\nexport class DriverFactory {\n    /**\n     * Creates a new driver depend on a given connection's driver type.\n     */\n    create(connection: DataSource): Driver {\n        const { type } = connection.options\n        switch (type) {\n            case \"mysql\":\n                return new MysqlDriver(connection)\n            case \"postgres\":\n                return new PostgresDriver(connection)\n            case \"cockroachdb\":\n                return new CockroachDriver(connection)\n            case \"sap\":\n                return new SapDriver(connection)\n            case \"mariadb\":\n                return new MysqlDriver(connection)\n            case \"sqlite\":\n                return new SqliteDriver(connection)\n            case \"better-sqlite3\":\n                return new BetterSqlite3Driver(connection)\n            case \"cordova\":\n                return new CordovaDriver(connection)\n            case \"nativescript\":\n                return new NativescriptDriver(connection)\n            case \"react-native\":\n                return new ReactNativeDriver(connection)\n            case \"sqljs\":\n                return new SqljsDriver(connection)\n            case \"oracle\":\n                return new OracleDriver(connection)\n            case \"mssql\":\n                return new SqlServerDriver(connection)\n            case \"mongodb\":\n                return new MongoDriver(connection)\n            case \"expo\":\n                return new ExpoDriverFactory(connection).create()\n            case \"aurora-mysql\":\n                return new AuroraMysqlDriver(connection)\n            case \"aurora-postgres\":\n                return new AuroraPostgresDriver(connection)\n            case \"capacitor\":\n                return new CapacitorDriver(connection)\n            case \"spanner\":\n                return new SpannerDriver(connection)\n            default:\n                throw new MissingDriverError(type, [\n                    \"aurora-mysql\",\n                    \"aurora-postgres\",\n                    \"better-sqlite3\",\n                    \"capacitor\",\n                    \"cockroachdb\",\n                    \"cordova\",\n                    \"expo\",\n                    \"mariadb\",\n                    \"mongodb\",\n                    \"mssql\",\n                    \"mysql\",\n                    \"nativescript\",\n                    \"oracle\",\n                    \"postgres\",\n                    \"react-native\",\n                    \"sap\",\n                    \"sqlite\",\n                    \"sqljs\",\n                    \"spanner\",\n                ])\n        }\n    }\n}\n"], "sourceRoot": ".."}