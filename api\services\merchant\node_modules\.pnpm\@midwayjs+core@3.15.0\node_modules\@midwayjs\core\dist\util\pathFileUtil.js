"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getModuleRequirePathList = exports.PathFileUtil = exports.getFileContentSync = exports.isPathEqual = exports.isPath = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
function isPath(p) {
    // eslint-disable-next-line no-useless-escape
    if (/(^[\.\/])|:|\\/.test(p)) {
        return true;
    }
    return false;
}
exports.isPath = isPath;
function isPathEqual(one, two) {
    if (!one || !two) {
        return false;
    }
    const ext = (0, path_1.extname)(one);
    return one.replace(ext, '') === two;
}
exports.isPathEqual = isPathEqual;
function getFileContentSync(filePath, encoding) {
    return typeof filePath === 'string'
        ? (0, fs_1.readFileSync)(filePath, {
            encoding,
        })
        : filePath;
}
exports.getFileContentSync = getFileContentSync;
exports.PathFileUtil = {
    isPath,
    isPathEqual,
    getFileContentSync,
};
function getModuleRequirePathList(moduleName) {
    const moduleNameList = [moduleName, moduleName.replace(/\//g, '_')];
    let moduleNameMap = {};
    const modulePathList = [];
    Object.keys(require.cache || {}).forEach(moduleName => {
        let moduleIndex = -1;
        for (const moduleName of moduleNameList) {
            const index = moduleName.indexOf(moduleName);
            if (index !== -1) {
                moduleIndex = index;
                break;
            }
        }
        if (moduleIndex === -1) {
            return;
        }
        const modulePath = moduleName.slice(0, moduleIndex);
        if (moduleNameMap[modulePath]) {
            return;
        }
        moduleNameMap[modulePath] = true;
        modulePathList.push(modulePath);
    });
    moduleNameMap = undefined;
    return modulePathList;
}
exports.getModuleRequirePathList = getModuleRequirePathList;
//# sourceMappingURL=pathFileUtil.js.map