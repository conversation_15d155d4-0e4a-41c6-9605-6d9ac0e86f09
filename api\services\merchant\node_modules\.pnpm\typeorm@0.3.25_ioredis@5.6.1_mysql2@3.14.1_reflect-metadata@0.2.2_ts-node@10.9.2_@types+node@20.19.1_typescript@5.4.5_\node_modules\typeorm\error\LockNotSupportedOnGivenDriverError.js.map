{"version": 3, "sources": ["../../src/error/LockNotSupportedOnGivenDriverError.ts"], "names": [], "mappings": ";;;AAAA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,kCAAmC,SAAQ,2BAAY;IAChE;QACI,KAAK,CAAC,wCAAwC,CAAC,CAAA;IACnD,CAAC;CACJ;AAJD,gFAIC", "file": "LockNotSupportedOnGivenDriverError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when selected sql driver does not supports locking.\n */\nexport class LockNotSupportedOnGivenDriverError extends TypeORMError {\n    constructor() {\n        super(`Locking not supported on given driver.`)\n    }\n}\n"], "sourceRoot": ".."}