{"version": 3, "sources": ["../browser/src/decorator/options/ForeignKeyOptions.ts"], "names": [], "mappings": "", "file": "ForeignKeyOptions.js", "sourcesContent": ["import { DeferrableType } from \"../../metadata/types/DeferrableType\"\nimport { OnDeleteType } from \"../../metadata/types/OnDeleteType\"\nimport { OnUpdateType } from \"../../metadata/types/OnUpdateType\"\n\n/**\n * Describes all foreign key options.\n */\nexport interface ForeignKeyOptions {\n    /**\n     * Name of the foreign key constraint.\n     */\n    name?: string\n\n    /**\n     * Database cascade action on delete.\n     */\n    onDelete?: OnDeleteType\n\n    /**\n     * Database cascade action on update.\n     */\n    onUpdate?: OnUpdateType\n\n    /**\n     * Indicate if foreign key constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n}\n"], "sourceRoot": "../.."}