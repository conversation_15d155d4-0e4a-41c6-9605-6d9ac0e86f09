"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DataListener = void 0;
const decorator_1 = require("../decorator");
class DataListener {
    async init() {
        this.innerData = await this.initData();
        await this.onData(this.setData.bind(this));
    }
    setData(data) {
        this.innerData = data;
    }
    getData() {
        return this.innerData;
    }
    async stop() {
        await this.destroyListener();
    }
    async destroyListener() { }
}
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DataListener.prototype, "init", null);
__decorate([
    (0, decorator_1.Destroy)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], DataListener.prototype, "stop", null);
exports.DataListener = DataListener;
//# sourceMappingURL=dataListener.js.map