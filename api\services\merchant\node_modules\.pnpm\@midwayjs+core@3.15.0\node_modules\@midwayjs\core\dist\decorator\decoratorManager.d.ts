import 'reflect-metadata';
import { GroupModeType, IModuleStore, MethodDecoratorOptions, ObjectDefinitionOptions, ObjectIdentifier, ParamDecoratorOptions, TagPropsMetadata, TSDesignType } from '../interface';
export declare const PRELOAD_MODULE_KEY = "INJECTION_PRELOAD_MODULE_KEY";
export declare const INJECT_CLASS_KEY_PREFIX = "INJECTION_CLASS_META_DATA";
export declare class DecoratorManager extends Map implements IModuleStore {
    /**
     * the key for meta data store in class
     */
    injectClassKeyPrefix: string;
    /**
     * the key for method meta data store in class
     */
    injectClassMethodKeyPrefix: string;
    /**
     * the key for method meta data store in method
     */
    injectMethodKeyPrefix: string;
    container: IModuleStore;
    saveModule(key: any, module: any): any;
    listModule(key: any): any;
    resetModule(key: any): void;
    bindContainer(container: IModuleStore): void;
    static getDecoratorClassKey(decoratorNameKey: ObjectIdentifier): string;
    static removeDecoratorClassKeySuffix(decoratorNameKey: ObjectIdentifier): string;
    static getDecoratorMethodKey(decoratorNameKey: ObjectIdentifier): string;
    static getDecoratorClsExtendedKey(decoratorNameKey: ObjectIdentifier): string;
    static getDecoratorClsMethodPrefix(decoratorNameKey: ObjectIdentifier): string;
    static getDecoratorClsMethodKey(decoratorNameKey: ObjectIdentifier, methodKey: ObjectIdentifier): string;
    static getDecoratorMethod(decoratorNameKey: ObjectIdentifier, methodKey: ObjectIdentifier): string;
    static saveMetadata(metaKey: string, target: any, dataKey: string, data: any): void;
    static attachMetadata(metaKey: string, target: any, dataKey: string, data: any, groupBy?: string, groupMode?: GroupModeType): void;
    static getMetadata(metaKey: string, target: any, dataKey?: string): any;
    /**
     * save meta data to class or property
     * @param decoratorNameKey the alias name for decorator
     * @param data the data you want to store
     * @param target target class
     * @param propertyName
     */
    saveMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName?: any): void;
    /**
     * attach data to class or property
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     * @param groupBy
     */
    attachMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName?: string, groupBy?: string, groupMode?: GroupModeType): void;
    /**
     * get single data from class or property
     * @param decoratorNameKey
     * @param target
     * @param propertyName
     */
    getMetadata(decoratorNameKey: ObjectIdentifier, target: any, propertyName?: any): any;
    /**
     * save property data to class
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     */
    savePropertyDataToClass(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any): void;
    /**
     * attach property data to class
     * @param decoratorNameKey
     * @param data
     * @param target
     * @param propertyName
     * @param groupBy
     */
    attachPropertyDataToClass(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any, groupBy?: string): void;
    /**
     * get property data from class
     * @param decoratorNameKey
     * @param target
     * @param propertyName
     */
    getPropertyDataFromClass(decoratorNameKey: ObjectIdentifier, target: any, propertyName: any): any;
    /**
     * list property data from class
     * @param decoratorNameKey
     * @param target
     */
    listPropertyDataFromClass(decoratorNameKey: ObjectIdentifier, target: any): any[];
}
/**
 * save data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param mergeIfExist
 */
export declare function saveClassMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, mergeIfExist?: boolean): void;
/**
 * attach data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param groupBy
 */
export declare function attachClassMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, groupBy?: string, groupMode?: GroupModeType): void;
/**
 * get data from class and proto
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 * @param useCache
 */
export declare function getClassExtendedMetadata<T = any>(decoratorNameKey: ObjectIdentifier, target: any, propertyName?: string, useCache?: boolean): T;
/**
 * get data from class
 * @param decoratorNameKey
 * @param target
 */
export declare function getClassMetadata<T = any>(decoratorNameKey: ObjectIdentifier, target: any): T;
/**
 * save property data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
export declare function savePropertyDataToClass(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any): void;
/**
 * attach property data to class
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 * @param groupBy
 */
export declare function attachPropertyDataToClass(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any, groupBy?: string): void;
/**
 * get property data from class
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 */
export declare function getPropertyDataFromClass<T = any>(decoratorNameKey: ObjectIdentifier, target: any, propertyName: any): T;
/**
 * list property data from class
 * @param decoratorNameKey
 * @param target
 */
export declare function listPropertyDataFromClass(decoratorNameKey: ObjectIdentifier, target: any): any[];
/**
 * save property data
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
export declare function savePropertyMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any): void;
/**
 * attach property data
 * @param decoratorNameKey
 * @param data
 * @param target
 * @param propertyName
 */
export declare function attachPropertyMetadata(decoratorNameKey: ObjectIdentifier, data: any, target: any, propertyName: any): void;
/**
 * get property data
 * @param decoratorNameKey
 * @param target
 * @param propertyName
 */
export declare function getPropertyMetadata<T = any>(decoratorNameKey: ObjectIdentifier, target: any, propertyName: any): T;
/**
 * save preload module by target
 * @param target
 */
export declare function savePreloadModule(target: any): any;
/**
 * list preload module
 */
export declare function listPreloadModule(): any[];
/**
 * save module to inner map
 * @param decoratorNameKey
 * @param target
 */
export declare function saveModule(decoratorNameKey: ObjectIdentifier, target: any): any;
export declare function bindContainer(container: any): void;
export declare function clearBindContainer(): any;
/**
 * list module from decorator key
 * @param decoratorNameKey
 * @param filter
 */
export declare function listModule(decoratorNameKey: ObjectIdentifier, filter?: (module: any) => boolean): any[];
/**
 * reset module
 * @param decoratorNameKey
 */
export declare function resetModule(decoratorNameKey: ObjectIdentifier): void;
/**
 * clear all module
 */
export declare function clearAllModule(): void;
export declare function transformTypeFromTSDesign(designFn: any): TSDesignType;
/**
 * save property inject args
 * @param opts 参数
 */
export declare function savePropertyInject(opts: {
    identifier: ObjectIdentifier;
    target: any;
    targetKey: string;
    args?: any;
}): void;
/**
 * get property inject args
 * @param target
 * @param useCache
 */
export declare function getPropertyInject(target: any, useCache?: boolean): {
    [methodName: string]: TagPropsMetadata;
};
/**
 * save class object definition
 * @param target class
 * @param props property data
 */
export declare function saveObjectDefinition(target: any, props?: {}): any;
/**
 * get class object definition from metadata
 * @param target
 */
export declare function getObjectDefinition(target: any): ObjectDefinitionOptions;
/**
 * class provider id
 * @param identifier id
 * @param target class
 */
export declare function saveProviderId(identifier: ObjectIdentifier, target: any): any;
/**
 * get provider id from module
 * @param module
 */
export declare function getProviderId(module: any): string;
export declare function getProviderName(module: any): string;
/**
 * get provider uuid from module
 * @param module
 */
export declare function getProviderUUId(module: any): string;
/**
 * use @Provide decorator or not
 * @param target class
 */
export declare function isProvide(target: any): boolean;
export declare enum BaseType {
    Boolean = "boolean",
    Number = "number",
    String = "string"
}
/**
 * get parameters type by reflect-metadata
 */
export declare function getMethodParamTypes(target: any, methodName: string | symbol): any;
/**
 * get property(method) type from metadata
 * @param target
 * @param methodName
 */
export declare function getPropertyType(target: any, methodName: string | symbol): TSDesignType<unknown>;
/**
 * get method return type from metadata
 * @param target
 * @param methodName
 */
export declare function getMethodReturnTypes(target: any, methodName: string | symbol): any;
/**
 * create a custom property inject
 * @param decoratorKey
 * @param metadata
 * @param impl default true, configuration need decoratorService.registerMethodHandler
 */
export declare function createCustomPropertyDecorator(decoratorKey: string, metadata: any, impl?: boolean): PropertyDecorator;
/**
 *
 * @param decoratorKey
 * @param metadata
 * @param impl default true, configuration need decoratorService.registerMethodHandler
 */
export declare function createCustomMethodDecorator(decoratorKey: string, metadata: any, implOrOptions?: boolean | MethodDecoratorOptions): MethodDecorator;
/**
 *
 * @param decoratorKey
 * @param metadata
 * @param options
 */
export declare function createCustomParamDecorator(decoratorKey: string, metadata: any, implOrOptions?: boolean | ParamDecoratorOptions): ParameterDecorator;
//# sourceMappingURL=decoratorManager.d.ts.map