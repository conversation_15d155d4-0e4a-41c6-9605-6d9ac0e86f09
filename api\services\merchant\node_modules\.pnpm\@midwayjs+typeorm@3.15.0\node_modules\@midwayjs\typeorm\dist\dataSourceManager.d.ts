import { DataSourceManager, IMidwayContainer, MidwayLoggerService } from '@midwayjs/core';
import { DataSource } from 'typeorm';
export declare class TypeORMDataSourceManager extends DataSourceManager<DataSource> {
    typeormConfig: any;
    applicationContext: IMidwayContainer;
    baseDir: string;
    loggerService: MidwayLoggerService;
    init(): Promise<void>;
    getName(): string;
    protected createDataSource(config: any, dataSourceName: string): Promise<DataSource>;
    protected checkConnected(dataSource: DataSource): Promise<boolean>;
    protected destroyDataSource(dataSource: DataSource): Promise<void>;
}
//# sourceMappingURL=dataSourceManager.d.ts.map