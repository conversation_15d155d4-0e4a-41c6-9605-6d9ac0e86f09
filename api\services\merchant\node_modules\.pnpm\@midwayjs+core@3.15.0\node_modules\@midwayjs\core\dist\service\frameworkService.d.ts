import { IMidwayContainer, IMidwayFramework, MidwayFrameworkType } from '../interface';
import { MidwayConfigService } from './configService';
import { MidwayLoggerService } from './loggerService';
import { MidwayDecoratorService } from './decoratorService';
import { MidwayAspectService } from './aspectService';
import { MidwayApplicationManager } from '../common/applicationManager';
export declare class MidwayFrameworkService {
    readonly applicationContext: IMidwayContainer;
    readonly globalOptions: any;
    configService: MidwayConfigService;
    loggerService: MidwayLoggerService;
    aspectService: MidwayAspectService;
    decoratorService: MidwayDecoratorService;
    applicationManager: MidwayApplicationManager;
    constructor(applicationContext: IMidwayContainer, globalOptions: any);
    private mainFramework;
    private globalFrameworkList;
    protected init(): Promise<void>;
    getMainApp(): any;
    getMainFramework(): IMidwayFramework<any, any, any, unknown, unknown>;
    getFramework(namespaceOrFrameworkType: string | MidwayFrameworkType): IMidwayFramework<any, any, any, unknown, unknown>;
    runFramework(): Promise<void>;
    stopFramework(): Promise<void>;
}
//# sourceMappingURL=frameworkService.d.ts.map