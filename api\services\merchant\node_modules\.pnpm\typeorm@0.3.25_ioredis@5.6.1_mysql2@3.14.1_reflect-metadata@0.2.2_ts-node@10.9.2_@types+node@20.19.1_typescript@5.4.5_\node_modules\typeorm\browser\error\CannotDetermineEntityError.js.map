{"version": 3, "sources": ["../browser/src/error/CannotDetermineEntityError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,0BAA2B,SAAQ,YAAY;IACxD,YAAY,SAAiB;QACzB,KAAK,CACD,UAAU,SAAS,kDAAkD;YACjE,uFAAuF,CAC9F,CAAA;IACL,CAAC;CACJ", "file": "CannotDetermineEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when user tries to save/remove/etc. constructor-less object (object literal) instead of entity.\n */\nexport class CannotDetermineEntityError extends TypeORMError {\n    constructor(operation: string) {\n        super(\n            `Cannot ${operation}, given value must be instance of entity class, ` +\n                `instead object literal is given. Or you must specify an entity target to method call.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}