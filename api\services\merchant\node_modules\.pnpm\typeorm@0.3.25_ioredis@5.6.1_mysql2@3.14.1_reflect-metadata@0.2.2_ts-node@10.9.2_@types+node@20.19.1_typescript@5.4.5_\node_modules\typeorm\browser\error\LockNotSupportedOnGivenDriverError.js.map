{"version": 3, "sources": ["../browser/src/error/LockNotSupportedOnGivenDriverError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,kCAAmC,SAAQ,YAAY;IAChE;QACI,KAAK,CAAC,wCAAwC,CAAC,CAAA;IACnD,CAAC;CACJ", "file": "LockNotSupportedOnGivenDriverError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when selected sql driver does not supports locking.\n */\nexport class LockNotSupportedOnGivenDriverError extends TypeORMError {\n    constructor() {\n        super(`Locking not supported on given driver.`)\n    }\n}\n"], "sourceRoot": ".."}