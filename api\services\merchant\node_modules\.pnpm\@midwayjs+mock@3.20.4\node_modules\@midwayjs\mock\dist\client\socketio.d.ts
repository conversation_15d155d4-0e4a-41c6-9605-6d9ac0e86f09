/// <reference types="node" />
import type { ManagerOptions, SocketOptions, Socket } from 'socket.io-client';
export interface MidwaySocketIOClientOptions extends Partial<ManagerOptions & SocketOptions> {
    url?: string;
    protocol?: string;
    host?: string;
    namespace?: string;
    port?: any;
}
export declare class SocketIOWrapperClient {
    private readonly socket;
    constructor(socket: any);
    connect(): Promise<unknown>;
    getSocket(): Socket<import("@socket.io/component-emitter").DefaultEventsMap, import("@socket.io/component-emitter").DefaultEventsMap>;
    send(eventName: string, ...args: any[]): void;
    on(eventName: string, handler: any): void;
    once(eventName: string, handler: any): Socket<import("@socket.io/component-emitter").DefaultEventsMap, import("@socket.io/component-emitter").DefaultEventsMap>;
    removeListener(event: string, fn?: any): Socket<import("@socket.io/component-emitter").DefaultEventsMap, import("@socket.io/component-emitter").DefaultEventsMap>;
    emit(eventName: string, ...args: any[]): Socket<import("@socket.io/component-emitter").DefaultEventsMap, import("@socket.io/component-emitter").DefaultEventsMap>;
    sendWithAck(eventName: string, ...args: any[]): Promise<unknown>;
    close(): void;
}
export declare function createSocketIOClient(opts: MidwaySocketIOClientOptions): Promise<SocketIOWrapperClient & NodeJS.EventEmitter>;
//# sourceMappingURL=socketio.d.ts.map