{"version": 3, "file": "binding_when_on_syntax.js", "sourceRoot": "", "sources": ["../../src/syntax/binding_when_on_syntax.ts"], "names": [], "mappings": ";;;;IAIA;QAME,6BAAmB,OAA8B;YAC/C,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;YACxB,IAAI,CAAC,kBAAkB,GAAG,IAAI,uCAAiB,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClE,IAAI,CAAC,gBAAgB,GAAG,IAAI,mCAAe,CAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;QAChE,CAAC;QAEM,kCAAI,GAAX,UAAY,UAAoD;YAC9D,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC;QAEM,6CAAe,GAAtB,UAAuB,IAAY;YACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAEM,iDAAmB,GAA1B;YACE,OAAO,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,EAAE,CAAC;QACvD,CAAC;QAEM,8CAAgB,GAAvB,UAAwB,GAAW,EAAE,KAAc;YACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QAEM,8CAAgB,GAAvB,UAAwB,MAAkC;YACxD,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC1D,CAAC;QAEM,6CAAe,GAAtB,UAAuB,IAAY;YACjC,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;QAEM,8CAAgB,GAAvB,UAAwB,GAAW,EAAE,KAAc;YACjD,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC;QAEM,+CAAiB,GAAxB,UAAyB,QAAoC;YAC3D,OAAO,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC7D,CAAC;QAEM,8CAAgB,GAAvB,UAAwB,QAAoC;YAC1D,OAAO,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAC5D,CAAC;QAEM,kDAAoB,GAA3B,UAA4B,IAAY;YACtC,OAAO,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC5D,CAAC;QAEM,mDAAqB,GAA5B,UAA6B,GAAW,EAAE,KAAc;YACtD,OAAO,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACnE,CAAC;QAEM,iDAAmB,GAA1B,UAA2B,IAAY;YACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC3D,CAAC;QAEM,kDAAoB,GAA3B,UAA4B,GAAW,EAAE,KAAc;YACrD,OAAO,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;QAEM,oDAAsB,GAA7B,UAA8B,UAAoD;YAChF,OAAO,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QACpE,CAAC;QAEM,mDAAqB,GAA5B,UAA6B,UAAoD;YAC/E,OAAO,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QACnE,CAAC;QAEM,0CAAY,GAAnB,UAAoB,OAA0D;YAC5E,OAAO,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACrD,CAAC;QAEM,4CAAc,GAArB,UAAsB,OAAgD;YACpE,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;QACvD,CAAC;QAEH,0BAAC;IAAD,CAAC,AAhFD,IAgFC;IAEQ,kDAAmB"}