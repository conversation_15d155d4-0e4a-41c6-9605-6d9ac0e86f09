{"version": 3, "file": "typeCheck.js", "sourceRoot": "", "sources": ["../src/typeCheck.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,+BAAgC;AAEzB,MAAM,SAAS,GAAG,GAAG,CAAC,EAAE;;IAC7B,MAAM,YAAY,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC/D,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpE,MAAM,iBAAiB,GAAG,EAAE,CAAC,0BAA0B,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;IAC7E,MAAM,eAAe,GAAG;QACtB,GAAG,iBAAiB,CAAC,OAAO;KAC7B,CAAC;IACF,MAAM,IAAI,GAAG,EAAE,CAAC,kBAAkB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAC1D,MAAM,OAAO,GAAG,EAAE,CAAC,aAAa,CAC9B,iBAAiB,CAAC,SAAS,EAC3B,eAAe,EACf,IAAI,CACL,CAAC;IACF,MAAM,cAAc,GAAG,EAAE,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;IACzD,MAAM,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE;QAChD,OAAO,UAAU,CAAC,QAAQ,KAAK,EAAE,CAAC,kBAAkB,CAAC,KAAK,CAAC;IAC7D,CAAC,CAAC,CAAC;IACH,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;QAC5C,OAAO;KACR;IACD,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC1B,MAAM,SAAS,GACb,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,QAAQ;YAC/B,CAAC,CAAC,IAAI,IAAA,eAAQ,EAAC,GAAG,EAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;YAC3C,CAAC,CAAC,EAAE,CAAC;QACT,MAAM,OAAO,GACX,CAAA,MAAC,KAAK,CAAC,WAAmB,0CAAE,WAAW,KAAI,KAAK,CAAC,WAAW,CAAC;QAC/D,MAAM,IAAI,KAAK,CAAC,aAAa,OAAO,IAAI,eAAe,GAAG,SAAS,EAAE,CAAC,CAAC;KACxE;AACH,CAAC,CAAC;AA7BW,QAAA,SAAS,aA6BpB"}