{"version": 3, "sources": ["../browser/src/error/CannotConnectAlreadyConnectedError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,kCAAmC,SAAQ,YAAY;IAChE,YAAY,cAAsB;QAC9B,KAAK,CACD,oBAAoB,cAAc,sEAAsE,CAC3G,CAAA;IACL,CAAC;CACJ", "file": "CannotConnectAlreadyConnectedError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to connect when he already connected.\n */\nexport class CannotConnectAlreadyConnectedError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot create a \"${connectionName}\" connection because connection to the database already established.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}