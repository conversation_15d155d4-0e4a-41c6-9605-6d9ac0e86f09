{"name": "@midwayjs/cli-plugin-check", "version": "2.1.0", "main": "dist/index", "typings": "dist/index.d.ts", "dependencies": {"@midwayjs/command-core": "^2.1.0", "@midwayjs/locate": "^1.0.3", "@midwayjs/luckyeye": "^1.1.0", "chalk": "^4.1.1", "fs-extra": "^8.1.0", "globby": "^10.0.1", "js-yaml": "^4.1.0", "midway-version": "^1.1.1"}, "devDependencies": {"typescript": "^4.1.0"}, "engines": {"node": ">= 10"}, "files": ["plugin.json", "dist", "src"], "scripts": {"build": "tsc --build", "lint": "../../node_modules/.bin/eslint .", "test": "../../node_modules/.bin/jest", "cov": "../../node_modules/.bin/jest --coverage", "ci": "npm run lint && npm run pkgfiles -- --check && npm run ci-test-only && npm run cov"}, "repository": {"type": "git", "url": "**************:midwayjs/cli.git"}, "publishConfig": {"access": "public"}, "license": "MIT", "gitHead": "8b6ce5b6bebd4d31140af0e9a51871ab12692b14"}