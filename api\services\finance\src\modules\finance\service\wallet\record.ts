import { Init, Provide } from '@midwayjs/decorator';
import { BaseService } from '@cool-midway/core';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Equal, Repository } from 'typeorm';
import { FinanceWalletRecordEntity } from '../../entity/wallet/record';

/**
 * 钱包记录
 */
@Provide()
export class FinanceWalletRecordService extends BaseService {
  @InjectEntityModel(FinanceWalletRecordEntity)
  financeWalletRecordEntity: Repository<FinanceWalletRecordEntity>;

  @Init()
  async init() {
    await super.init();
    this.setEntity(this.financeWalletRecordEntity);
  }

  /**
   * 保存记录
   * @param record
   */
  async save(record) {
    await this.financeWalletRecordEntity.save(record);
  }

  /**
   * 通过对象获取
   * @param userId
   * @param objectId
   * @param objectType
   * @returns
   */
  async getByObject(userId: number, objectId: number, objectType: number) {
    return this.financeWalletRecordEntity.findOneBy({
      userId: Equal(userId),
      objectId: Equal(objectId),
      objectType: Equal(objectType),
    });
  }

  /**
   * 更新对象信息
   * @param id
   * @param object
   */
  async updateObject(id: number, object: any) {
    await this.financeWalletRecordEntity.update(id, { objectInfo: object });
  }
}
