"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayHealthService = void 0;
const decorator_1 = require("../decorator");
const interface_1 = require("../interface");
const configService_1 = require("./configService");
const util_1 = require("../util");
let MidwayHealthService = class MidwayHealthService {
    constructor() {
        this.healthCheckTimeout = 1000;
        this.healthCheckMethods = [];
    }
    async init(lifeCycleInstanceList) {
        const healthCheckTimeout = this.configService.getConfiguration('core.healthCheckTimeout') || 1000;
        this.setCheckTimeout(healthCheckTimeout);
        for (const lifecycleInstance of lifeCycleInstanceList) {
            if (lifecycleInstance.instance &&
                lifecycleInstance.instance['onHealthCheck']) {
                this.healthCheckMethods.push({
                    item: lifecycleInstance.instance['onHealthCheck'].bind(lifecycleInstance.instance),
                    meta: {
                        namespace: lifecycleInstance.namespace,
                    },
                });
            }
        }
    }
    async getStatus() {
        const checkResult = await (0, util_1.createPromiseTimeoutInvokeChain)({
            promiseItems: this.healthCheckMethods.map(item => {
                return {
                    item: item.item(this.applicationContext),
                    meta: item.meta,
                };
            }),
            timeout: this.healthCheckTimeout,
            methodName: 'configuration.onHealthCheck',
            onSuccess: (result, meta) => {
                if (result['status'] !== undefined) {
                    return {
                        namespace: meta.namespace,
                        ...result,
                    };
                }
                else {
                    return {
                        status: false,
                        namespace: meta.namespace,
                        reason: 'configuration.onHealthCheck return value must be object and contain status field',
                    };
                }
            },
            onFail: (err, meta) => {
                return {
                    status: false,
                    namespace: meta.namespace,
                    reason: err.message,
                };
            },
        });
        const failedResult = checkResult.find(item => !item.status);
        return {
            status: !failedResult,
            namespace: failedResult === null || failedResult === void 0 ? void 0 : failedResult.namespace,
            reason: failedResult === null || failedResult === void 0 ? void 0 : failedResult.reason,
            results: checkResult,
        };
    }
    setCheckTimeout(timeout) {
        this.healthCheckTimeout = timeout;
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", configService_1.MidwayConfigService)
], MidwayHealthService.prototype, "configService", void 0);
__decorate([
    (0, decorator_1.ApplicationContext)(),
    __metadata("design:type", Object)
], MidwayHealthService.prototype, "applicationContext", void 0);
MidwayHealthService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton)
], MidwayHealthService);
exports.MidwayHealthService = MidwayHealthService;
//# sourceMappingURL=healthService.js.map