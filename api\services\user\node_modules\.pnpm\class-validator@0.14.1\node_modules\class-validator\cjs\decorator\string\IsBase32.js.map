{"version": 3, "file": "IsBase32.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsBase32.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,sEAAuD;AAE1C,QAAA,SAAS,GAAG,UAAU,CAAC;AAEpC;;;GAGG;AACH,SAAgB,QAAQ,CAAC,KAAc;IACrC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,kBAAiB,EAAC,KAAK,CAAC,CAAC;AAC/D,CAAC;AAFD,4BAEC;AAED;;;GAGG;AACH,SAAgB,QAAQ,CAAC,iBAAqC;IAC5D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,iBAAS;QACf,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC;YACnD,cAAc,EAAE,IAAA,yBAAY,EAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,kCAAkC,EAAE,iBAAiB,CAAC;SAC/G;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAXD,4BAWC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isBase32Validator from 'validator/lib/isBase32';\n\nexport const IS_BASE32 = 'isBase32';\n\n/**\n * Checks if a string is base32 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function isBase32(value: unknown): boolean {\n  return typeof value === 'string' && isBase32Validator(value);\n}\n\n/**\n * Check if a string is base32 encoded.\n * If given value is not a string, then it returns false.\n */\nexport function IsBase32(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_BASE32,\n      validator: {\n        validate: (value, args): boolean => isBase32(value),\n        defaultMessage: buildMessage(eachPrefix => eachPrefix + '$property must be base32 encoded', validationOptions),\n      },\n    },\n    validationOptions\n  );\n}\n"]}