"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypedResourceManager = void 0;
class TypedResourceManager {
    constructor(typedResourceInitializerOptions) {
        this.typedResourceInitializerOptions = typedResourceInitializerOptions;
        this.resourceMap = new Map();
        this.resourceBindingMap = new Map();
    }
    async createResource(resourceName, resourceInitializeConfig) {
        const resource = await this.typedResourceInitializerOptions.resourceInitialize(resourceInitializeConfig, resourceName);
        this.resourceMap.set(resourceName, resource);
        return resource;
    }
    async init() {
        for (const resourceName of Object.keys(this.typedResourceInitializerOptions.initializeValue)) {
            const resourceInitializeConfig = this.typedResourceInitializerOptions.initializeValue[resourceName];
            const ClzProvider = this.typedResourceInitializerOptions.initializeClzProvider[resourceName];
            const resource = await this.createResource(resourceName, resourceInitializeConfig);
            const bindingResult = await this.typedResourceInitializerOptions.resourceBinding(ClzProvider, resourceInitializeConfig, resource, resourceName);
            if (bindingResult) {
                this.resourceBindingMap.set(resourceName, bindingResult);
            }
        }
    }
    async startParallel() {
        const startPromises = [];
        for (const [resourceName, resource] of this.resourceMap.entries()) {
            startPromises.push(this.typedResourceInitializerOptions.resourceStart(resource, this.typedResourceInitializerOptions.initializeValue[resourceName], this.resourceBindingMap.get(resourceName)));
        }
        await Promise.all(startPromises);
    }
    async start() {
        for (const [resourceName, resource] of this.resourceMap.entries()) {
            await this.typedResourceInitializerOptions.resourceStart(resource, this.typedResourceInitializerOptions.initializeValue[resourceName], this.resourceBindingMap.get(resourceName));
        }
    }
    async destroyParallel() {
        const destroyPromises = [];
        for (const [resourceName, resource] of this.resourceMap.entries()) {
            destroyPromises.push(this.typedResourceInitializerOptions.resourceDestroy(resource, this.typedResourceInitializerOptions.initializeValue[resourceName]));
        }
        await Promise.all(destroyPromises);
    }
    async destroy() {
        for (const [resourceName, resource] of this.resourceMap.entries()) {
            await this.typedResourceInitializerOptions.resourceDestroy(resource, this.typedResourceInitializerOptions.initializeValue[resourceName]);
        }
        this.resourceMap.clear();
        this.resourceBindingMap.clear();
    }
    getResource(resourceName) {
        return this.resourceMap.get(resourceName);
    }
}
exports.TypedResourceManager = TypedResourceManager;
//# sourceMappingURL=typedResourceManager.js.map