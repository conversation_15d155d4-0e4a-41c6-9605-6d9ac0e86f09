import { BaseMysqlService } from "./mysql";
import { BasePgService } from "./postgres";
import { Application, Context } from "@midwayjs/koa";
import { TypeORMDataSourceManager } from "@midwayjs/typeorm";
import { Repository, SelectQueryBuilder } from "typeorm";
import { QueryOp } from "../decorator/controller";
import { CoolEventManager } from "../event";
import { BaseSqliteService } from "./sqlite";
/**
 * 服务基类
 */
export declare abstract class BaseService {
    baseMysqlService: BaseMysqlService;
    basePgService: BasePgService;
    baseSqliteService: BaseSqliteService;
    ormType: any;
    service: BaseMysqlService | BasePgService | BaseSqliteService;
    protected entity: Repository<any>;
    protected sqlParams: any;
    typeORMDataSourceManager: TypeORMDataSourceManager;
    coolEventManager: CoolEventManager;
    baseCtx: Context;
    baseApp: Application;
    init(): Promise<void>;
    setEntity(entity: any): void;
    setCtx(ctx: Context): void;
    setApp(app: Application): void;
    /**
     * 设置sql
     * @param condition 条件是否成立
     * @param sql sql语句
     * @param params 参数
     */
    setSql(condition: any, sql: any, params: any): any;
    /**
     * 获得查询个数的SQL
     * @param sql
     */
    getCountSql(sql: any): string;
    /**
     * 参数安全性检查
     * @param params
     */
    paramSafetyCheck(params: any): Promise<boolean>;
    /**
     * 原生查询
     * @param sql
     * @param params
     * @param connectionName
     */
    nativeQuery(sql: any, params?: any, connectionName?: any): Promise<any>;
    /**
     * 获得ORM管理
     *  @param connectionName 连接名称
     */
    getOrmManager(connectionName?: string): import("typeorm").DataSource;
    /**
     * 操作entity获得分页数据，不用写sql
     * @param find QueryBuilder
     * @param query
     * @param autoSort
     * @param connectionName
     */
    entityRenderPage(find: SelectQueryBuilder<any>, query: any, autoSort?: boolean): Promise<{
        list: any[];
        pagination: {
            page: number;
            size: number;
            total: number; /**
             * 删除
             * @param ids 删除的ID集合 如：[1,2,3] 或者 1,2,3
             */
        };
    }>;
    /**
     * 执行SQL并获得分页数据
     * @param sql 执行的sql语句
     * @param query 分页查询条件
     * @param autoSort 是否自动排序
     * @param connectionName 连接名称
     */
    sqlRenderPage(sql: any, query: any, autoSort?: boolean, connectionName?: any): Promise<{
        list: any;
        pagination: {
            page: number; /**
             * 新增|修改
             * @param param 数据
             */
            size: number;
            total: number;
        };
    }>;
    /**
     * 获得单个ID
     * @param id ID
     * @param infoIgnoreProperty 忽略返回属性
     */
    info(id: any, infoIgnoreProperty?: string[]): Promise<any>;
    /**
     * 删除
     * @param ids 删除的ID集合 如：[1,2,3] 或者 1,2,3
     */
    delete(ids: any): Promise<void>;
    /**
     * 软删除
     * @param ids 删除的ID数组
     * @param entity 实体
     */
    softDelete(ids: number[], entity?: Repository<any>): Promise<void>;
    /**
     * 修改
     * @param param 数据
     */
    update(param: any): Promise<void>;
    /**
     * 新增
     * @param param 数据
     */
    add(param: any | any[]): Promise<Object>;
    /**
     * 新增|修改
     * @param param 数据
     */
    addOrUpdate(param: any | any[], type?: "add" | "update"): Promise<void>;
    /**
     * 非分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    list(query: any, option: any, connectionName?: any): Promise<any>;
    /**
     * 分页查询
     * @param query 查询条件
     * @param option 查询配置
     * @param connectionName 连接名
     */
    page(query: any, option: any, connectionName?: any): Promise<{
        list: any;
        pagination: {
            page: number; /**
             * 新增|修改
             * @param param 数据
             */
            size: number;
            total: number;
        };
    }>;
    /**
     * 构建查询配置
     * @param query 前端查询
     * @param option
     */
    getOptionFind(query: any, option: QueryOp): Promise<string>;
    /**
     * 新增|修改|删除 之后的操作
     * @param data 对应数据
     */
    modifyAfter(data: any, type: "delete" | "update" | "add"): Promise<void>;
    /**
     * 新增|修改|删除 之前的操作
     * @param data 对应数据
     */
    modifyBefore(data: any, type: "delete" | "update" | "add"): Promise<void>;
}
