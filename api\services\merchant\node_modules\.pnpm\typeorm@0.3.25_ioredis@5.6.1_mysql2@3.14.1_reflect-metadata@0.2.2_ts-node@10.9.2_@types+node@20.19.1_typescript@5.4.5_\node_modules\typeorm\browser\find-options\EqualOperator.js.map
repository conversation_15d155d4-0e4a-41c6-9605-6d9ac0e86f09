{"version": 3, "sources": ["../browser/src/find-options/EqualOperator.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C,MAAM,OAAO,aAAiB,SAAQ,YAAe;IAGjD,YAAY,KAA0B;QAClC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAA;QAHhB,mBAAa,GAAG,MAAM,CAAC,GAAG,CAAC,eAAe,CAAC,CAAA;IAIpD,CAAC;CACJ", "file": "EqualOperator.js", "sourcesContent": ["import { FindOperator } from \"./FindOperator\"\n\nexport class EqualOperator<T> extends FindOperator<T> {\n    readonly \"@instanceof\" = Symbol.for(\"EqualOperator\")\n\n    constructor(value: T | FindOperator<T>) {\n        super(\"equal\", value)\n    }\n}\n"], "sourceRoot": ".."}