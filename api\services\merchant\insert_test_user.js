// 插入测试用户数据的Node.js脚本
const mysql = require('mysql2/promise');

async function insertTestUser() {
  const connection = await mysql.createConnection({
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'merchant_service_db'
  });

  try {
    // 插入测试用户 (admin/admin)
    const [result] = await connection.execute(`
      INSERT IGNORE INTO merchant_sys_user 
      (userId, userName, password, passwordV, roles, buttons, avatar, email, phone, status) 
      VALUES 
      (1, 'admin', '21232f297a57a5a743894a0e4a801fc3', 1, '["admin","super"]', '["add","edit","delete","view"]', NULL, '<EMAIL>', '13800138000', 1)
    `);
    
    console.log('✅ 测试用户插入成功:', result);
    
    // 验证插入的数据
    const [rows] = await connection.execute(
      'SELECT userId, userName, roles, buttons FROM merchant_sys_user WHERE userName = ?',
      ['admin']
    );
    
    console.log('📋 用户数据:', rows);
    
  } catch (error) {
    console.error('❌ 插入失败:', error.message);
  } finally {
    await connection.end();
  }
}

insertTestUser(); 