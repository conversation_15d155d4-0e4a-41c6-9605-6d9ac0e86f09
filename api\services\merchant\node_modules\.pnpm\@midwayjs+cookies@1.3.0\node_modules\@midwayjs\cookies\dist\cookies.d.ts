/// <reference types="node" />
import { CookieGetOptions, CookieSetOptions } from './interface';
import { IncomingMessage, ServerResponse } from 'http';
/**
 * cookies extend pillarjs/cookies, add encrypt and decrypt
 */
export declare class Cookies {
    private readonly _defaultCookieOptions;
    private readonly _defaultGetCookieOptions;
    private uaParseResult;
    ctx: any;
    secure: any;
    app: any;
    request: IncomingMessage;
    response: ServerResponse;
    constructor(ctx: any, keys: any, defaultCookieOptions?: CookieSetOptions, defaultGetCookieOptions?: CookieGetOptions);
    get keys(): any;
    /**
     * This extracts the cookie with the given name from the
     * Cookie header in the request. If such a cookie exists,
     * its value is returned. Otherwise, nothing is returned.
     * @param name The cookie's unique name.
     * @param opts Optional. The options for cookie's getting.
     * @returns The cookie's value according to the specific name.
     */
    get(name: string, opts?: CookieGetOptions): string | undefined;
    /**
     * This sets the given cookie in the response and returns
     * the current context to allow chaining.If the value is omitted,
     * an outbound header with an expired date is used to delete the cookie.
     * @param name The cookie's unique name.
     * @param value Optional. The cookie's real value.
     * @param opts Optional. The options for cookie's setting.
     * @returns The current 'Cookie' instance.
     */
    set(name: string, value: string | null, opts?: CookieSetOptions): this;
    set(name: string, opts?: CookieSetOptions): this;
    protected isSameSiteNoneCompatible(userAgent: string): boolean;
    protected isPartitionedCompatible(userAgent: string): boolean;
    protected parseChromiumAndMajorVersion(userAgent: any): {
        chromium: boolean;
        majorVersion: number;
    };
    protected isPriorityCompatible(userAgent: string): boolean;
}
export declare function urlSafeEncode(encode: string): string;
export declare function urlSafeDecode(encodeStr: any): any;
//# sourceMappingURL=cookies.d.ts.map