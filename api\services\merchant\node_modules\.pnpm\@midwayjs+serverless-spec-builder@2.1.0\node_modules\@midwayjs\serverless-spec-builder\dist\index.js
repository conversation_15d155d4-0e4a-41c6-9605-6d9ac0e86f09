"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.writeToSpec = exports.loadSpec = exports.getSpecFile = exports.generate = exports.parse = exports.saveYaml = exports.transform = exports.getFaaSPackageVersion = exports.filterUserDefinedEnv = void 0;
const parse_1 = require("./parse");
const fs_1 = require("fs");
const path_1 = require("path");
const builder_1 = require("./builder");
__exportStar(require("./interface"), exports);
__exportStar(require("./builder"), exports);
__exportStar(require("./wrapper"), exports);
var utils_1 = require("./utils");
Object.defineProperty(exports, "filterUserDefinedEnv", { enumerable: true, get: function () { return utils_1.filterUserDefinedEnv; } });
Object.defineProperty(exports, "getFaaSPackageVersion", { enumerable: true, get: function () { return utils_1.getFaaSPackageVersion; } });
const pattern = /\$\{\s*(\w+\.\w+)\s*\}/g;
const transform = (sourcefilePathOrJson, builderCls) => {
    let result = sourcefilePathOrJson;
    if (typeof sourcefilePathOrJson === 'string') {
        if ((0, fs_1.existsSync)(sourcefilePathOrJson)) {
            const content = (0, fs_1.readFileSync)(sourcefilePathOrJson, 'utf8');
            const yamlContent = content.replace(pattern, (match, key) => {
                if (key.startsWith('env.')) {
                    return process.env[key.replace('env.', '')] || match;
                }
            });
            // replace
            result = (0, parse_1.parse)(sourcefilePathOrJson, yamlContent);
        }
    }
    if (!result) {
        return;
    }
    if (builderCls) {
        return new builderCls(result).toJSON();
    }
    else {
        return new builder_1.SpecBuilder(result).toJSON();
    }
};
exports.transform = transform;
var parse_2 = require("./parse");
Object.defineProperty(exports, "saveYaml", { enumerable: true, get: function () { return parse_2.saveYaml; } });
Object.defineProperty(exports, "parse", { enumerable: true, get: function () { return parse_2.parse; } });
const generate = (sourceFilePathOrJson, targetFilePath, builderCls) => {
    let baseDir = process.cwd();
    let transformResultJSON = {};
    if (typeof sourceFilePathOrJson === 'string') {
        if (!(0, path_1.isAbsolute)(sourceFilePathOrJson)) {
            sourceFilePathOrJson = (0, path_1.join)(baseDir, sourceFilePathOrJson);
        }
        else {
            baseDir = (0, path_1.dirname)(sourceFilePathOrJson);
        }
    }
    transformResultJSON = (0, exports.transform)(sourceFilePathOrJson, builderCls);
    if (!(0, path_1.isAbsolute)(targetFilePath)) {
        targetFilePath = (0, path_1.join)(baseDir, targetFilePath);
    }
    return (0, parse_1.saveYaml)(targetFilePath, transformResultJSON);
};
exports.generate = generate;
const getSpecFile = baseDir => {
    baseDir = baseDir || process.cwd();
    const specPath = [
        'f.yml',
        'f.yaml',
        'serverless.yml',
        'serverless.yaml',
    ].find(spec => (0, fs_1.existsSync)((0, path_1.resolve)(baseDir, spec)));
    if (specPath) {
        return {
            type: 'yaml',
            path: (0, path_1.resolve)(baseDir, specPath),
        };
    }
    return {};
};
exports.getSpecFile = getSpecFile;
const loadSpec = (baseDir, specFileInfo) => {
    const specFile = specFileInfo || (0, exports.getSpecFile)(baseDir);
    if (!specFile || !specFile.type) {
        return {};
    }
    if (specFile.type === 'yaml') {
        return (0, exports.transform)(specFile.path);
    }
};
exports.loadSpec = loadSpec;
const writeToSpec = (baseDir, specResult, specFileInfo) => {
    const specFile = specFileInfo || (0, exports.getSpecFile)(baseDir);
    if (!specFile || !specFile.type) {
        return {};
    }
    if (specFile.type === 'yaml') {
        return (0, parse_1.saveYaml)(specFile.path, specResult);
    }
};
exports.writeToSpec = writeToSpec;
//# sourceMappingURL=index.js.map