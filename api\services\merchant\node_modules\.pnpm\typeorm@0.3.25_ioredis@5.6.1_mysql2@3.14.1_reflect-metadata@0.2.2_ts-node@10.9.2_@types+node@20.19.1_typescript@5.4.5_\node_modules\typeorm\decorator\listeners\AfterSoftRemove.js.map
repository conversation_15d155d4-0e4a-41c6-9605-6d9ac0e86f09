{"version": 3, "sources": ["../../src/decorator/listeners/AfterSoftRemove.ts"], "names": [], "mappings": ";;AAOA,0CAQC;AAfD,2CAAsD;AACtD,gFAA4E;AAG5E;;GAEG;AACH,SAAgB,eAAe;IAC3B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1C,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,uCAAkB,CAAC,iBAAiB;SACf,CAAC,CAAA;IACpC,CAAC,CAAA;AACL,CAAC", "file": "AfterSoftRemove.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { EventListenerTypes } from \"../../metadata/types/EventListenerTypes\"\nimport { EntityListenerMetadataArgs } from \"../../metadata-args/EntityListenerMetadataArgs\"\n\n/**\n * Calls a method on which this decorator is applied before this entity soft removal.\n */\nexport function AfterSoftRemove(): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().entityListeners.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            type: EventListenerTypes.AFTER_SOFT_REMOVE,\n        } as EntityListenerMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}