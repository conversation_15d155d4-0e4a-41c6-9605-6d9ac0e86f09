{"name": "url-parse-lax", "version": "3.0.0", "description": "Lax url.parse() with support for protocol-less URLs & IPs", "license": "MIT", "repository": "sindresorhus/url-parse-lax", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["url", "uri", "parse", "parser", "loose", "lax", "protocol", "less", "protocol-less", "ip", "ipv4", "ipv6"], "dependencies": {"prepend-http": "^2.0.0"}, "devDependencies": {"ava": "*", "xo": "*"}}