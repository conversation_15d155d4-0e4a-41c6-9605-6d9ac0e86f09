{"version": 3, "sources": ["../browser/src/find-options/FindOneOptions.ts"], "names": [], "mappings": "", "file": "FindOneOptions.js", "sourcesContent": ["import { JoinOptions } from \"./JoinOptions\"\nimport { FindOptionsWhere } from \"./FindOptionsWhere\"\nimport {\n    FindOptionsSelect,\n    FindOptionsSelectByString,\n} from \"./FindOptionsSelect\"\nimport {\n    FindOptionsRelationByString,\n    FindOptionsRelations,\n} from \"./FindOptionsRelations\"\nimport { FindOptionsOrder } from \"./FindOptionsOrder\"\n\n/**\n * Defines a special criteria to find specific entity.\n */\nexport interface FindOneOptions<Entity = any> {\n    /**\n     * Adds a comment with the supplied string in the generated query.  This is\n     * helpful for debugging purposes, such as finding a specific query in the\n     * database server's logs, or for categorization using an APM product.\n     */\n    comment?: string\n\n    /**\n     * Specifies what columns should be retrieved.\n     */\n    select?: FindOptionsSelect<Entity> | FindOptionsSelectByString<Entity>\n\n    /**\n     * Simple condition that should be applied to match entities.\n     */\n    where?: FindOptionsWhere<Entity>[] | FindOptionsWhere<Entity>\n\n    /**\n     * Indicates what relations of entity should be loaded (simplified left join form).\n     */\n    relations?: FindOptionsRelations<Entity> | FindOptionsRelationByString\n\n    /**\n     * Specifies how relations must be loaded - using \"joins\" or separate queries.\n     * If you are loading too much data with nested joins it's better to load relations\n     * using separate queries.\n     *\n     * Default strategy is \"join\", but default can be customized in connection options.\n     */\n    relationLoadStrategy?: \"join\" | \"query\"\n\n    /**\n     * Specifies what relations should be loaded.\n     *\n     * @deprecated\n     */\n    join?: JoinOptions\n\n    /**\n     * Order, in which entities should be ordered.\n     */\n    order?: FindOptionsOrder<Entity>\n\n    /**\n     * Enables or disables query result caching.\n     */\n    cache?: boolean | number | { id: any; milliseconds: number }\n\n    /**\n     * Indicates what locking mode should be used.\n     *\n     * Note: For lock tables, you must specify the table names and not the relation names\n     */\n    lock?:\n        | { mode: \"optimistic\"; version: number | Date }\n        | {\n              mode:\n                  | \"pessimistic_read\"\n                  | \"pessimistic_write\"\n                  | \"dirty_read\"\n                  /*\n                    \"pessimistic_partial_write\" and \"pessimistic_write_or_fail\" are deprecated and\n                    will be removed in a future version.\n\n                    Use onLocked instead.\n                 */\n                  | \"pessimistic_partial_write\"\n                  | \"pessimistic_write_or_fail\"\n                  | \"for_no_key_update\"\n                  | \"for_key_share\"\n              tables?: string[]\n              onLocked?: \"nowait\" | \"skip_locked\"\n          }\n\n    /**\n     * Indicates if soft-deleted rows should be included in entity result.\n     */\n    withDeleted?: boolean\n\n    /**\n     * If sets to true then loads all relation ids of the entity and maps them into relation values (not relation objects).\n     * If array of strings is given then loads only relation ids of the given properties.\n     */\n    loadRelationIds?:\n        | boolean\n        | { relations?: string[]; disableMixedMap?: boolean } // todo: extract options into separate interface, reuse\n\n    /**\n     * Indicates if eager relations should be loaded or not.\n     * By default, they are loaded when find methods are used.\n     */\n    loadEagerRelations?: boolean\n\n    /**\n     * If this is set to true, SELECT query in a `find` method will be executed in a transaction.\n     */\n    transaction?: boolean\n}\n"], "sourceRoot": ".."}