"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CoolModuleMenu = void 0;
const core_1 = require("@midwayjs/core");
const typeorm_1 = require("@midwayjs/typeorm");
const fs = require("fs");
const _ = require("lodash");
const path = require("path");
const typeorm_2 = require("typeorm");
const event_1 = require("../event");
const config_1 = require("./config");
/**
 * 菜单
 */
let CoolModuleMenu = class CoolModuleMenu {
    constructor() {
        this.datas = {};
    }
    async init() {
        this.initJudge = this.coolConfig.initJudge;
        if (!this.initJudge) {
            this.initJudge = "file";
        }
        // 是否需要导入
        if (this.coolConfig.initMenu) {
            const modules = this.coolModuleConfig.modules;
            const metadatas = await this.getDbMetadatas();
            for (const module of modules) {
                if (this.initJudge == "file") {
                    const { exist, lockPath } = this.checkFileExist(module);
                    if (!exist) {
                        await this.importMenu(module, metadatas, lockPath);
                    }
                }
                if (this.initJudge == "db") {
                    const exist = await this.checkDbExist(module, metadatas);
                    if (!exist) {
                        await this.importMenu(module, metadatas);
                    }
                }
            }
            this.coolEventManager.emit("onMenuImport", this.datas);
        }
    }
    /**
     * 导入菜单
     * @param module
     * @param lockPath
     */
    async importMenu(module, metadatas, lockPath) {
        // 模块路径
        const modulePath = `${this.app.getBaseDir()}/modules/${module}`;
        // json 路径
        const menuPath = `${modulePath}/menu.json`;
        // 导入
        if (fs.existsSync(menuPath)) {
            const data = fs.readFileSync(menuPath);
            try {
                // this.coolEventManager.emit("onMenuImport", module, JSON.parse(data.toString()));
                this.datas[module] = JSON.parse(data.toString());
                await this.lockImportData(module, metadatas, lockPath);
            }
            catch (error) {
                this.coreLogger.error(error);
                this.coreLogger.error(`自动初始化模块[${module}]菜单失败，请检查对应的数据结构是否正确`);
            }
        }
    }
    /**
     * 获取数据库元数据
     */
    async getDbMetadatas() {
        // 获得所有的实体
        const entityMetadatas = this.defaultDataSource.entityMetadatas;
        const metadatas = _.mapValues(_.keyBy(entityMetadatas, "tableName"), "target");
        return metadatas;
    }
    /**
     * 检查数据是否存在
     * @param module
     * @param metadatas
     */
    async checkDbExist(module, metadatas) {
        const cKey = `init_menu_${module}`;
        const repository = this.defaultDataSource.getRepository(metadatas["base_sys_conf"]);
        const data = await repository.findOneBy({ cKey: (0, typeorm_2.Equal)(cKey) });
        return !!data;
    }
    /**
     * 检查文件是否存在
     * @param module
     */
    checkFileExist(module) {
        const importLockPath = path.join(`${this.app.getBaseDir()}`, "..", "lock", "menu");
        if (!fs.existsSync(importLockPath)) {
            fs.mkdirSync(importLockPath, { recursive: true });
        }
        const lockPath = path.join(importLockPath, module + ".menu.lock");
        return {
            exist: fs.existsSync(lockPath),
            lockPath,
        };
    }
    /**
     * 锁定导入
     * @param module
     * @param metadatas
     * @param lockPath
     * @param time
     */
    async lockImportData(module, metadatas, lockPath) {
        if (this.initJudge == "file") {
            fs.writeFileSync(lockPath, `success`);
        }
        if (this.initJudge == "db") {
            const repository = this.defaultDataSource.getRepository(metadatas["base_sys_conf"]);
            if (this.ormConfig.default.type == "postgres") {
                await repository.save(repository.create({
                    cKey: `init_menu_${module}`,
                    cValue: `success`,
                }));
            }
            else {
                await repository.insert({
                    cKey: `init_menu_${module}`,
                    cValue: `success`,
                });
            }
        }
    }
};
exports.CoolModuleMenu = CoolModuleMenu;
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", config_1.CoolModuleConfig)
], CoolModuleMenu.prototype, "coolModuleConfig", void 0);
__decorate([
    (0, core_1.Config)("cool"),
    __metadata("design:type", Object)
], CoolModuleMenu.prototype, "coolConfig", void 0);
__decorate([
    (0, core_1.App)(),
    __metadata("design:type", Object)
], CoolModuleMenu.prototype, "app", void 0);
__decorate([
    (0, core_1.Logger)(),
    __metadata("design:type", Object)
], CoolModuleMenu.prototype, "coreLogger", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", event_1.CoolEventManager)
], CoolModuleMenu.prototype, "coolEventManager", void 0);
__decorate([
    (0, core_1.Config)("typeorm.dataSource"),
    __metadata("design:type", Object)
], CoolModuleMenu.prototype, "ormConfig", void 0);
__decorate([
    (0, typeorm_1.InjectDataSource)("default"),
    __metadata("design:type", typeorm_2.DataSource)
], CoolModuleMenu.prototype, "defaultDataSource", void 0);
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", typeorm_1.TypeORMDataSourceManager)
], CoolModuleMenu.prototype, "typeORMDataSourceManager", void 0);
exports.CoolModuleMenu = CoolModuleMenu = __decorate([
    (0, core_1.Provide)(),
    (0, core_1.Scope)(core_1.ScopeEnum.Singleton)
], CoolModuleMenu);
