"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatAggregationHandlers = exports.writeWrapper = void 0;
const path_1 = require("path");
const fs_1 = require("fs");
const ejs_1 = require("ejs");
const utils_1 = require("./utils");
// 写入口
function writeWrapper(options) {
    var _a, _b, _c;
    const { service, distDir, starter, baseDir, cover, faasModName = '@midwayjs/faas', initializeName, advancePreventMultiInit, loadDirectory = [], preloadModules = [], faasStarterName, middleware, clearCache = true, templatePath, moreArgs, preloadFile, moreTemplateVariables = {}, aggregationBeforeExecScript = '', specificStarterName = '', initializeInHandler = false, isDefaultFunc = false, } = options;
    const files = {};
    // for function programing，function
    let functionMap;
    const functions = service.functions || {};
    for (const func in functions) {
        const handlerConf = functions[func];
        // for fp
        functionMap = assignToFunctionMap(functionMap, handlerConf);
        // for aggregation fp
        if (handlerConf._handlers) {
            handlerConf._handlers.forEach(innerHandlerConf => {
                functionMap = assignToFunctionMap(functionMap, innerHandlerConf);
            });
        }
        const handlerSplitInfo = handlerConf.handler.split('.');
        let handlerFileName = handlerConf.handlerFileName || handlerSplitInfo[0];
        const name = handlerSplitInfo[1];
        if (isDefaultFunc) {
            handlerFileName = func;
        }
        if (!cover && (0, fs_1.existsSync)((0, path_1.join)(baseDir, handlerFileName + '.js'))) {
            // 如果入口文件名存在，则跳过
            continue;
        }
        if (!files[handlerFileName]) {
            files[handlerFileName] = {
                handlers: [],
                originLayers: [],
            };
        }
        if (isDefaultFunc) {
            files[handlerFileName].defaultFunctionHandlerName = handlerConf.handler;
        }
        if (handlerConf.layers && handlerConf.layers.length) {
            files[handlerFileName].originLayers.push(handlerConf.layers);
        }
        // 高密度部署
        if (handlerConf._isAggregation) {
            files[handlerFileName].aggregationHandlerName = handlerConf.handler;
            files[handlerFileName].handlers.push({
                name,
                handlers: formatAggregationHandlers(handlerConf._handlers),
            });
        }
        else {
            files[handlerFileName].handlers.push({
                name,
                handler: handlerConf.handler,
            });
        }
    }
    let tplPath = templatePath;
    if (!tplPath) {
        let entryWrapper = '../wrapper_v1.ejs';
        const isCustomAppType = !!(service === null || service === void 0 ? void 0 : service.deployType);
        // 指定了 deployType
        if (isCustomAppType) {
            entryWrapper = '../wrapper_app.ejs';
        }
        else if (specificStarterName) {
            entryWrapper = '../wrapper_v3_specific.ejs';
        }
        else {
            const faasVersion = (0, utils_1.getFaaSPackageVersion)(distDir, baseDir);
            if (faasVersion === 2) {
                entryWrapper = '../wrapper_v2.ejs';
            }
            else if (faasVersion === 3) {
                entryWrapper = '../wrapper_v3.ejs';
            }
        }
        tplPath = (0, path_1.resolve)(__dirname, entryWrapper);
    }
    const tpl = (0, fs_1.readFileSync)(tplPath).toString();
    if ((_a = functionMap === null || functionMap === void 0 ? void 0 : functionMap.functionList) === null || _a === void 0 ? void 0 : _a.length) {
        const target = (0, path_1.join)(distDir, 'registerFunction.js');
        const source = (0, fs_1.readFileSync)((0, path_1.join)(__dirname, '../hooks_runtime.ejs'), 'utf-8');
        const runtime = (0, ejs_1.render)(source, {
            runtime: (_c = (_b = service === null || service === void 0 ? void 0 : service.hooks) === null || _b === void 0 ? void 0 : _b.runtime) !== null && _c !== void 0 ? _c : 'compiler',
        });
        if (!(0, fs_1.existsSync)(target)) {
            (0, fs_1.writeFileSync)(target, runtime, { encoding: 'utf-8' });
        }
    }
    for (const file in files) {
        const fileName = (0, path_1.join)(distDir, `${file}.js`);
        const fileInfo = files[file];
        const layers = (0, utils_1.getLayers)(service.layers, ...fileInfo.originLayers);
        let variables = {
            starter,
            runtimeConfig: service,
            faasModName: faasModName || '@midwayjs/faas',
            loadDirectory,
            // Todo: future need remove middleware, use egg
            middleware: middleware || [],
            faasStarterName: faasStarterName || 'FaaSStarter',
            advancePreventMultiInit: advancePreventMultiInit || false,
            initializer: initializeName || 'initializer',
            handlers: fileInfo.handlers,
            functionMap,
            preloadModules,
            clearCache,
            moreArgs: moreArgs || false,
            preloadFile,
            aggregationBeforeExecScript,
            specificStarterName,
            initializeInHandler,
            defaultFunctionHandlerName: files[file].defaultFunctionHandlerName,
            aggregationHandlerName: files[file].aggregationHandlerName,
            ...layers,
        };
        // 更多变量
        if (typeof moreTemplateVariables === 'object') {
            Object.assign(variables, moreTemplateVariables);
        }
        else if (typeof moreTemplateVariables === 'function') {
            variables = moreTemplateVariables({
                file,
                fileInfo,
                variables,
            });
        }
        const content = (0, ejs_1.render)(tpl, variables);
        if ((0, fs_1.existsSync)(fileName)) {
            const oldContent = (0, fs_1.readFileSync)(fileName).toString();
            if (oldContent === content) {
                continue;
            }
        }
        (0, fs_1.writeFileSync)(fileName, content);
    }
}
exports.writeWrapper = writeWrapper;
const assignToFunctionMap = (functionMap, handlerConf) => {
    if (handlerConf.isFunctional) {
        if (!(functionMap === null || functionMap === void 0 ? void 0 : functionMap.functionList)) {
            functionMap = { functionList: [] };
        }
        functionMap.functionList.push({
            functionName: handlerConf.exportFunction,
            functionHandler: handlerConf.handler,
            functionFilePath: handlerConf.sourceFilePath,
            argsPath: handlerConf.argsPath,
        });
    }
    return functionMap;
};
function formatAggregationHandlers(handlers) {
    if (!handlers || !handlers.length) {
        return [];
    }
    return handlers
        .map(handler => {
        const { path = '', eventType } = handler;
        if (eventType !== 'http') {
            return {
                ...handler,
                level: -1,
            };
        }
        return {
            ...handler,
            method: (handler.method ? [].concat(handler.method) : []).map(method => {
                return method.toLowerCase();
            }),
            handler: handler.handler,
            router: path.replace(/\*/g, '**'),
            pureRouter: path.replace(/\**$/, ''),
            regRouter: path.replace(/\/\*$/, '/(.*)?') || '/(.*)?',
            level: path.split('/').length - 1,
            paramsMatchLevel: path.indexOf('/:') !== -1 ? 1 : 0,
        };
    })
        .sort((handlerA, handlerB) => {
        if (handlerA.level === handlerB.level) {
            if (handlerA.level < 0) {
                return -1;
            }
            if (handlerB.pureRouter === handlerA.pureRouter) {
                return handlerA.router.length - handlerB.router.length;
            }
            if (handlerA.paramsMatchLevel === handlerB.paramsMatchLevel) {
                return handlerB.pureRouter.length - handlerA.pureRouter.length;
            }
            return handlerA.paramsMatchLevel - handlerB.paramsMatchLevel;
        }
        return handlerB.level - handlerA.level;
    });
}
exports.formatAggregationHandlers = formatAggregationHandlers;
//# sourceMappingURL=wrapper.js.map