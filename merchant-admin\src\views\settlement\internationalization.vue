<template>
  <div class="intl-page">
    <div class="page-header">
      <div class="header-stats">
        <span class="stat">{{ activeRegions }}个地区</span>
        <span class="stat">{{ activeCurrencies }}种货币</span>
        <span class="stat">{{ todayTransactions }}笔交易</span>
        <span class="stat">{{ totalVolume }}交易额</span>
      </div>
      <div class="header-actions">
        <el-button @click="toggleAutoSync" :type="autoSync ? 'primary' : 'default'" size="small">
          {{ autoSync ? '自动同步' : '手动同步' }}
        </el-button>
        <el-button @click="addCurrency" size="small">添加货币</el-button>
        <el-button @click="exportData" size="small">导出数据</el-button>
      </div>
    </div>

    <div class="main-content">
      <div class="content-left">
        <div class="currency-table">
          <el-table :data="currencyEcosystem" style="width: 100%">
            <el-table-column prop="flag" label="货币" width="80">
              <template #default="{ row }">
                <span class="currency-flag">{{ row.flag }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="代码" width="80" />
            <el-table-column prop="name" label="名称" min-width="120" />
            <el-table-column prop="rate" label="汇率" width="100" />
            <el-table-column prop="change" label="24h变化" width="100">
              <template #default="{ row }">
                <span :class="row.change >= 0 ? 'positive' : 'negative'">
                  {{ row.change >= 0 ? '+' : '' }}{{ row.change }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 'online' ? 'success' : 'info'" size="small">
                  {{ row.status === 'online' ? '在线' : '离线' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template #default="{ row }">
                <el-button link size="small" @click="configureCurrency(row)">配置</el-button>
                <el-button link size="small" @click="viewHistory(row)">历史</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="market-table">
          <el-table :data="globalMarkets" style="width: 100%">
            <el-table-column prop="flag" label="市场" width="80">
              <template #default="{ row }">
                <span class="market-flag">{{ row.flag }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="name" label="名称" min-width="100" />
            <el-table-column prop="localTime" label="当地时间" width="120" />
            <el-table-column prop="timezone" label="时区" width="100" />
            <el-table-column prop="statusText" label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isOpen ? 'success' : 'info'" size="small">
                  {{ row.statusText }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="activity" label="活跃度" width="100">
              <template #default="{ row }">
                {{ row.activity }}%
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80">
              <template #default="{ row }">
                <el-button link size="small" @click="configureMarket(row)">配置</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div class="content-right">
        <div class="events-panel">
          <div class="panel-header">
            <span class="panel-title">实时事件</span>
            <el-button link size="small" @click="viewAllEvents">
              查看全部
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </div>
          <div class="events-list">
            <div v-for="event in liveEvents.slice(0, 5)" :key="event.id"
                 class="event-item"
                 :class="event.level === 'warning' ? 'event-warning' : 'event-normal'">
              <div class="event-header">
                <span class="event-time" :class="event.level === 'warning' ? 'time-warning' : 'time-normal'">
                  {{ event.time }}
                </span>
                <el-tag :type="event.level === 'warning' ? 'warning' : 'info'" size="small">
                  {{ event.level === 'warning' ? '警告' : '普通' }}
                </el-tag>
              </div>
              <div class="event-description">{{ event.description }}</div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'

// 响应式数据
const autoSync = ref(true)
const activeRegions = ref(2)
const activeCurrencies = ref(4)
const todayTransactions = ref('12,847')
const totalVolume = ref('¥156.8M')

// 货币生态系统数据
const currencyEcosystem = ref([
  {
    code: 'CNY',
    name: '人民币',
    flag: '🇨🇳',
    rate: '1.0000',
    change: 0,
    status: 'online',
    isPrimary: true,
    isActive: true
  },
  {
    code: 'USD',
    name: '美元',
    flag: '🇺🇸',
    rate: '7.2456',
    change: -0.013,
    status: 'online',
    isPrimary: false,
    isActive: true
  },
  {
    code: 'EUR',
    name: '欧元',
    flag: '🇪🇺',
    rate: '7.8234',
    change: -0.035,
    status: 'online',
    isPrimary: false,
    isActive: true
  },
  {
    code: 'JPY',
    name: '日元',
    flag: '🇯🇵',
    rate: '0.0485',
    change: 0.049,
    status: 'online',
    isPrimary: false,
    isActive: true
  },
  {
    code: 'GBP',
    name: '英镑',
    flag: '🇬🇧',
    rate: '8.9567',
    change: -0.12,
    status: 'offline',
    isPrimary: false,
    isActive: false
  }
])

// 全球市场数据
const globalMarkets = ref([
  {
    code: 'CN',
    name: '中国',
    flag: '🇨🇳',
    localTime: '14:30:25',
    timezone: 'UTC+8',
    isOpen: true,
    statusText: '交易中',
    activity: 89,
    isPrimary: true
  },
  {
    code: 'US',
    name: '美国',
    flag: '🇺🇸',
    localTime: '01:30:25',
    timezone: 'UTC-5',
    isOpen: false,
    statusText: '休市',
    activity: 12
  },
  {
    code: 'EU',
    name: '欧盟',
    flag: '🇪🇺',
    localTime: '07:30:25',
    timezone: 'UTC+1',
    isOpen: false,
    statusText: '休市',
    activity: 8
  },
  {
    code: 'JP',
    name: '日本',
    flag: '🇯🇵',
    localTime: '15:30:25',
    timezone: 'UTC+9',
    isOpen: true,
    statusText: '交易中',
    activity: 67
  }
])

// 实时事件数据
const liveEvents = ref([
  {
    id: 1,
    time: '14:30:25',
    level: 'warning',
    description: 'EUR/CNY 汇率波动超过阈值，当前波动率 0.5%'
  },
  {
    id: 2,
    time: '14:28:10',
    level: 'normal',
    description: '系统成功同步最新市场数据'
  },
  {
    id: 3,
    time: '14:25:00',
    level: 'normal',
    description: '美国市场交易时段结束'
  },
  {
    id: 4,
    time: '14:22:15',
    level: 'warning',
    description: 'USD/CNY 交易量异常增长，需要关注'
  },
  {
    id: 5,
    time: '14:20:30',
    level: 'normal',
    description: '日本市场开盘，汇率数据已更新'
  }
])

// 方法
const toggleAutoSync = () => {
  autoSync.value = !autoSync.value
  ElMessage.success(autoSync.value ? '已开启自动同步' : '已关闭自动同步')
}

const selectCurrency = (currency: any) => {
  ElMessage.info(`已选择 ${currency.name} (${currency.code})`)
}

const addCurrency = () => {
  ElMessage.info('添加货币功能开发中...')
}

const configureCurrency = (currency: any) => {
  ElMessage.info(`配置 ${currency.name} 货币`)
}

const viewHistory = (currency: any) => {
  ElMessage.info(`查看 ${currency.name} 历史数据`)
}

const configureMarket = (market: any) => {
  ElMessage.info(`配置 ${market.name} 市场`)
}

const exportData = () => {
  ElMessage.success('数据导出中...')
}

const viewAllEvents = () => {
  ElMessage.info('查看全部事件')
}

// 生命周期
onMounted(() => {
  // 初始化逻辑
})
</script>

<style scoped lang="scss">
.intl-page {
  padding: 20px;
  background: #fafafa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  .header-stats {
    display: flex;
    gap: 24px;
    
    .stat {
      font-size: 14px;
      color: #606266;
    }
  }
  
  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.main-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.content-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-right {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.currency-table,
.market-table {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.events-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .panel-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.event-item {
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;

  &.event-warning {
    background: #fef7e6;
    border: 1px solid #fde2a7;
  }

  &.event-normal {
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .event-time {
      font-size: 12px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-weight: 500;

      &.time-warning {
        color: #e6a23c;
      }

      &.time-normal {
        color: #909399;
      }
    }
  }

  .event-description {
    font-size: 13px;
    color: #303133;
    line-height: 1.5;
    margin: 0;
  }
}



.currency-flag,
.market-flag {
  font-size: 18px;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

.neutral {
  color: #909399;
}
</style>
