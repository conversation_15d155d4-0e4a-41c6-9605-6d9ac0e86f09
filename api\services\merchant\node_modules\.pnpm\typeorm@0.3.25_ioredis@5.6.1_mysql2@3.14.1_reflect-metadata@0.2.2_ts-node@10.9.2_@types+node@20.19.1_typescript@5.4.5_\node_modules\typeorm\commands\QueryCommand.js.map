{"version": 3, "sources": ["../../src/commands/QueryCommand.ts"], "names": [], "mappings": ";;;;AAAA,0DAAwB;AACxB,wDAAuB;AACvB,8DAA6B;AAG7B,6DAAyD;AAEzD,iDAA6C;AAE7C;;GAEG;AACH,MAAa,YAAY;IAAzB;QACI,YAAO,GAAG,eAAe,CAAA;QACzB,aAAQ,GACJ,kHAAkH,CAAA;IAgE1H,CAAC;IA9DG,OAAO,CAAC,IAAgB;QACpB,OAAO,IAAI;aACN,UAAU,CAAC,OAAO,EAAE;YACjB,QAAQ,EAAE,sBAAsB;YAChC,IAAI,EAAE,QAAQ;SACjB,CAAC;aACD,MAAM,CAAC,YAAY,EAAE;YAClB,KAAK,EAAE,GAAG;YACV,QAAQ,EACJ,6DAA6D;YACjE,YAAY,EAAE,IAAI;SACrB,CAAC,CAAA;IACV,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAqB;QAC/B,IAAI,WAAW,GAA4B,SAAS,CAAA;QACpD,IAAI,UAAU,GAA2B,SAAS,CAAA;QAClD,IAAI,CAAC;YACD,UAAU,GAAG,MAAM,2BAAY,CAAC,cAAc,CAC1C,cAAI,CAAC,OAAO,CAAC,iBAAO,CAAC,GAAG,EAAE,EAAE,IAAI,CAAC,UAAoB,CAAC,CACzD,CAAA;YACD,UAAU,CAAC,UAAU,CAAC;gBAClB,WAAW,EAAE,KAAK;gBAClB,aAAa,EAAE,KAAK;gBACpB,UAAU,EAAE,KAAK;gBACjB,OAAO,EAAE,KAAK;aACjB,CAAC,CAAA;YACF,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,mDAAmD;YACnD,WAAW,GAAG,UAAU,CAAC,iBAAiB,EAAE,CAAA;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAe,CAAA;YAClC,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,iBAAiB,GAAG,6BAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAClE,CAAA;YACD,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAElD,IAAI,OAAO,WAAW,KAAK,WAAW,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CACP,eAAI,CAAC,KAAK,CAAA,kDAAkD,CAC/D,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,OAAO,CAAC,GAAG,CAAC,eAAI,CAAC,KAAK,CAAA,mCAAmC,CAAC,CAAA;gBAC1D,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE;oBACrB,WAAW,EAAE,QAAQ;oBACrB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,IAAI;iBACd,CAAC,CAAA;YACN,CAAC;YAED,MAAM,WAAW,CAAC,OAAO,EAAE,CAAA;YAC3B,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,6BAAa,CAAC,SAAS,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAA;YAE7D,IAAI,WAAW;gBAAE,MAAO,WAA2B,CAAC,OAAO,EAAE,CAAA;YAC7D,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa;gBACtC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAA;YAE9B,iBAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACnB,CAAC;IACL,CAAC;CACJ;AAnED,oCAmEC", "file": "QueryCommand.js", "sourcesContent": ["import ansi from \"ansis\"\nimport path from \"path\"\nimport process from \"process\"\nimport yargs from \"yargs\"\nimport { DataSource } from \"../data-source/DataSource\"\nimport { PlatformTools } from \"../platform/PlatformTools\"\nimport { QueryRunner } from \"../query-runner/QueryRunner\"\nimport { CommandUtils } from \"./CommandUtils\"\n\n/**\n * Executes an SQL query on the given dataSource.\n */\nexport class QueryCommand implements yargs.CommandModule {\n    command = \"query [query]\"\n    describe =\n        \"Executes given SQL query on a default dataSource. Specify connection name to run query on a specific dataSource.\"\n\n    builder(args: yargs.Argv) {\n        return args\n            .positional(\"query\", {\n                describe: \"The SQL Query to run\",\n                type: \"string\",\n            })\n            .option(\"dataSource\", {\n                alias: \"d\",\n                describe:\n                    \"Path to the file where your DataSource instance is defined.\",\n                demandOption: true,\n            })\n    }\n\n    async handler(args: yargs.Arguments) {\n        let queryRunner: QueryRunner | undefined = undefined\n        let dataSource: DataSource | undefined = undefined\n        try {\n            dataSource = await CommandUtils.loadDataSource(\n                path.resolve(process.cwd(), args.dataSource as string),\n            )\n            dataSource.setOptions({\n                synchronize: false,\n                migrationsRun: false,\n                dropSchema: false,\n                logging: false,\n            })\n            await dataSource.initialize()\n\n            // create a query runner and execute query using it\n            queryRunner = dataSource.createQueryRunner()\n            const query = args.query as string\n            console.log(\n                ansi.green`Running query: ` + PlatformTools.highlightSql(query),\n            )\n            const queryResult = await queryRunner.query(query)\n\n            if (typeof queryResult === \"undefined\") {\n                console.log(\n                    ansi.green`Query has been executed. No result was returned.`,\n                )\n            } else {\n                console.log(ansi.green`Query has been executed. Result: `)\n                console.dir(queryResult, {\n                    breakLength: Infinity,\n                    compact: false,\n                    depth: null,\n                })\n            }\n\n            await queryRunner.release()\n            await dataSource.destroy()\n        } catch (err) {\n            PlatformTools.logCmdErr(\"Error during query execution:\", err)\n\n            if (queryRunner) await (queryRunner as QueryRunner).release()\n            if (dataSource && dataSource.isInitialized)\n                await dataSource.destroy()\n\n            process.exit(1)\n        }\n    }\n}\n"], "sourceRoot": ".."}