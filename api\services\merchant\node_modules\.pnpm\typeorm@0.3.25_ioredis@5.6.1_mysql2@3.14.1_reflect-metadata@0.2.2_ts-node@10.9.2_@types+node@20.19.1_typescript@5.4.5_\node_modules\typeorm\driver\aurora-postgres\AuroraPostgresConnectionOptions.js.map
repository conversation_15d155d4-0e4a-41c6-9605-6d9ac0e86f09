{"version": 3, "sources": ["../../src/driver/aurora-postgres/AuroraPostgresConnectionOptions.ts"], "names": [], "mappings": "", "file": "AuroraPostgresConnectionOptions.js", "sourcesContent": ["import { BaseDataSourceOptions } from \"../../data-source/BaseDataSourceOptions\"\n\n/**\n * Postgres-specific connection options.\n */\nexport interface AuroraPostgresConnectionOptions extends BaseDataSourceOptions {\n    /**\n     * Database type.\n     */\n    readonly type: \"aurora-postgres\"\n\n    readonly region: string\n\n    readonly secretArn: string\n\n    readonly resourceArn: string\n\n    readonly database: string\n\n    /**\n     * The driver object\n     * This defaults to require(\"typeorm-aurora-data-api-driver\")\n     */\n    readonly driver?: any\n\n    /**\n     * The Postgres extension to use to generate UUID columns. Defaults to uuid-ossp.\n     * If pgcrypto is selected, TypeORM will use the gen_random_uuid() function from this extension.\n     * If uuid-ossp is selected, TypeORM will use the uuid_generate_v4() function from this extension.\n     */\n    readonly uuidExtension?: \"pgcrypto\" | \"uuid-ossp\"\n\n    readonly transformParameters?: boolean\n\n    /*\n     * Function handling errors thrown by drivers pool.\n     * Defaults to logging error with `warn` level.\n     */\n    readonly poolErrorHandler?: (err: any) => any\n\n    readonly serviceConfigOptions?: { [key: string]: any }\n\n    readonly formatOptions?: { [key: string]: any; castParameters: boolean }\n\n    readonly poolSize?: never\n}\n"], "sourceRoot": "../.."}