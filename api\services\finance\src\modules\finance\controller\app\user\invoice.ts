import { Cool<PERSON>ontroller, BaseController } from '@cool-midway/core';
import { FinanceUserInvoiceEntity } from '../../../entity/user/invoice';
import { FinanceUserInvoiceService } from '../../../service/user/invoice';

/**
 * 描述
 */
@CoolController({
  api: ['add', 'delete', 'update', 'info', 'page'],
  entity: FinanceUserInvoiceEntity,
  service: FinanceUserInvoiceService,
  insertParam: ctx => {
    return {
      userId: ctx.user.id,
    };
  },
  pageQueryOp: {
    where: ctx => {
      return [['a.userId =:userId', { userId: ctx.user.id }]];
    },
  },
})
export class AppFinanceUserInvoiceController extends BaseController {}
