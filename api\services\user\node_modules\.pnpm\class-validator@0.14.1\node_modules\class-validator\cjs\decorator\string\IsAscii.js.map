{"version": 3, "file": "IsAscii.js", "sourceRoot": "", "sources": ["../../../../src/decorator/string/IsAscii.ts"], "names": [], "mappings": ";;;;;;AACA,qDAAgE;AAChE,oEAAqD;AAExC,QAAA,QAAQ,GAAG,SAAS,CAAC;AAElC;;;GAGG;AACH,SAAgB,OAAO,CAAC,KAAc;IACpC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,IAAA,iBAAgB,EAAC,KAAK,CAAC,CAAC;AAC9D,CAAC;AAFD,0BAEC;AAED;;;GAGG;AACH,SAAgB,OAAO,CAAC,iBAAqC;IAC3D,OAAO,IAAA,uBAAU,EACf;QACE,IAAI,EAAE,gBAAQ;QACd,SAAS,EAAE;YACT,QAAQ,EAAE,CAAC,KAAK,EAAE,IAAI,EAAW,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;YAClD,cAAc,EAAE,IAAA,yBAAY,EAC1B,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,8CAA8C,EACzE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAdD,0BAcC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\nimport isAsciiValidator from 'validator/lib/isAscii';\n\nexport const IS_ASCII = 'isAscii';\n\n/**\n * Checks if the string contains ASCII chars only.\n * If given value is not a string, then it returns false.\n */\nexport function isAscii(value: unknown): boolean {\n  return typeof value === 'string' && isAsciiValidator(value);\n}\n\n/**\n * Checks if the string contains ASCII chars only.\n * If given value is not a string, then it returns false.\n */\nexport function IsAscii(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_ASCII,\n      validator: {\n        validate: (value, args): boolean => isAscii(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must contain only ASCII characters',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}