{"name": "@midwayjs/async-hooks-context-manager", "version": "3.20.4", "description": "midway async hooks context manager", "main": "dist/index.js", "typings": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand", "cov": "node --require=ts-node/register ../../node_modules/.bin/jest --runInBand --coverage --forceExit"}, "keywords": ["midway", "async", "context manager"], "files": ["dist/**/*.js", "dist/**/*.d.ts"], "license": "MIT", "devDependencies": {"@midwayjs/core": "^3.20.4"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/midwayjs/midway.git"}, "engines": {"node": ">=12.17.0"}, "gitHead": "c3fb65a7ada8829635f3c6af5ef83c65c3a43d79"}