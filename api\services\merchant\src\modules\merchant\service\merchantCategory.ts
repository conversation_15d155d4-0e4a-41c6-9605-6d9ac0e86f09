import { Provide } from '@midwayjs/core';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { MerchantCategoryEntity } from '../entity/merchantCategory';

@Provide()
@CoolRpcService({
  entity: MerchantCategoryEntity,
  method: ["add", "delete", "update", "info", "list", "page"]
})
export class MerchantCategoryService extends BaseRpcService {
  @InjectEntityModel(MerchantCategoryEntity)
  merchantCategoryEntity!: Repository<MerchantCategoryEntity>;

  /**
   * 重写分页查询方法
   */
  async page(query: any, option?: any) {
    console.log('🔍 [MerchantCategoryService] page 被调用，原始参数:', { query, option });

    // ✅ 修复：正确处理参数格式 - query可能是数组格式
    let actualQuery = query;
    if (Array.isArray(query) && query.length > 0) {
      actualQuery = query[0]; // 取第一个元素作为查询参数
      console.log('🔧 [MerchantCategoryService] 检测到数组格式，提取查询参数:', actualQuery);
    }

    // ✅ 修复：正确解构参数，增加详细日志
    const {
      page = 1,
      size = 10,  // 修改默认值为10，与前端保持一致
      status,
      merchantType,
      name,
      searchKey  // 兼容可能的searchKey字段
    } = actualQuery;

    // 优先使用name字段，如果没有则使用searchKey
    const searchName = name || searchKey;

    console.log('📋 [MerchantCategoryService] 解构后的参数:', {
      page,
      size,
      status,
      merchantType,
      searchName,
      originalQuery: query
    });

    const queryBuilder = this.merchantCategoryEntity.createQueryBuilder('category');

    // 状态筛选 - 修复：0是有效值，不应该被过滤掉
    if (status !== undefined && status !== null) {
      console.log('🔍 [MerchantCategoryService] 添加状态筛选:', status, typeof status);
      queryBuilder.andWhere('category.status = :status', { status: Number(status) });
    }

    // 商户类型筛选 - 修复：0是有效值（个人商户），不应该被过滤掉
    if (merchantType !== undefined && merchantType !== null) {
      console.log('🔍 [MerchantCategoryService] 添加商户类型筛选:', merchantType, typeof merchantType);
      queryBuilder.andWhere('category.merchantType = :merchantType', { merchantType: Number(merchantType) });
    }

    // 名称搜索
    if (searchName && searchName.trim()) {
      console.log('🔍 [MerchantCategoryService] 添加名称搜索:', searchName);
      queryBuilder.andWhere('category.name LIKE :name', { name: `%${searchName.trim()}%` });
    }

    // 排序：先按排序号降序，再按ID降序（新记录在前）
    queryBuilder.orderBy('category.sortNum', 'DESC')
                .addOrderBy('category.id', 'DESC');

    // 分页计算
    const skip = (Number(page) - 1) * Number(size);
    const take = Number(size);
    
    console.log('📄 [MerchantCategoryService] 分页计算:', { 
      page: Number(page), 
      size: Number(size), 
      skip, 
      take 
    });

    // 分页查询
    const [list, total] = await queryBuilder
      .skip(skip)
      .take(take)
      .getManyAndCount();

    const result = {
      list,
      pagination: {
        page: Number(page),
        size: Number(size),
        total
      }
    };

    console.log('✅ [MerchantCategoryService] 查询完成:', { 
      listCount: list.length, 
      total, 
      currentPage: result.pagination.page,
      pageSize: result.pagination.size,
      sql: queryBuilder.getQuery()
    });
    
    return result;
  }

  /**
   * 获取启用的分类列表
   * @param merchantType 商户类型：0-个人 1-企业 2-通用
   */
  async getActiveCategories(merchantType?: number) {
    console.log('🔍 [MerchantCategoryService] getActiveCategories 被调用，参数:', { merchantType });

    const queryBuilder = this.merchantCategoryEntity.createQueryBuilder('category');

    // 只获取启用的分类
    queryBuilder.where('category.status = :status', { status: 1 });

    if (merchantType !== undefined) {
      // 获取指定类型和通用类型的分类
      queryBuilder.andWhere('(category.merchantType = :merchantType OR category.merchantType = 2)', {
        merchantType
      });
    }

    // 排序：先按排序号降序，再按ID降序（新记录在前）
    queryBuilder.orderBy('category.sortNum', 'DESC')
                .addOrderBy('category.id', 'DESC');

    const result = await queryBuilder.getMany();
    console.log('✅ [MerchantCategoryService] getActiveCategories 查询结果数量:', result.length);
    console.log('✅ [MerchantCategoryService] getActiveCategories 分类名称:', result.map(r => `${r.name}(${r.merchantType})`));

    return result;
  }

  /**
   * 获取分类统计信息
   */
  async getCategoryStats() {
    const sql = `
      SELECT
        mc.id,
        mc.name,
        mc.merchant_type,
        COUNT(m.id) as merchant_count,
        COUNT(CASE WHEN m.status = 1 THEN 1 END) as active_merchant_count
      FROM merchant_category mc
      LEFT JOIN merchant m ON mc.name = m.category
      WHERE mc.status = 1
      GROUP BY mc.id, mc.name, mc.merchant_type
      ORDER BY mc.sort_num DESC
    `;

    return await this.nativeQuery(sql);
  }

  /**
   * 重写删除方法，正确处理参数
   */
  async delete(ids: any) {
    console.log('🔍 [MerchantCategoryService] delete 被调用，参数:', ids);

    let idArr: number[] = [];

    // 处理不同格式的参数
    if (Array.isArray(ids)) {
      idArr = ids;
    } else if (typeof ids === 'string') {
      // 如果是字符串，尝试按逗号分割
      idArr = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    } else if (typeof ids === 'number') {
      idArr = [ids];
    } else if (ids && typeof ids === 'object' && ids.id) {
      idArr = [ids.id];
    } else {
      throw new Error('无效的删除参数');
    }

    console.log('🔍 [MerchantCategoryService] 处理后的ID数组:', idArr);

    if (idArr.length === 0) {
      throw new Error('请选择要删除的分类');
    }

    // 检查分类是否存在并执行删除
    let deletedCount = 0;
    for (const id of idArr) {
      const category = await this.merchantCategoryEntity.findOneBy({ id });
      if (!category) {
        throw new Error(`分类ID ${id} 不存在`);
      }

      // 逐个删除
      const result = await this.merchantCategoryEntity.delete({ id });
      if (result.affected && result.affected > 0) {
        deletedCount++;
      }
    }

    console.log('✅ [MerchantCategoryService] 删除完成，删除数量:', deletedCount);

    return { affected: deletedCount };
  }
}
