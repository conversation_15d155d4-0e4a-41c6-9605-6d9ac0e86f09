#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/ncc/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/ncc/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/ncc/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules/@vercel/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/@vercel+ncc@0.30.0/node_modules:/mnt/d/jiangrenjie_demo/api/services/merchant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/ncc/cli.js" "$@"
else
  exec node  "$basedir/../../../../../../@vercel+ncc@0.30.0/node_modules/@vercel/ncc/dist/ncc/cli.js" "$@"
fi
