{"version": 3, "sources": ["../../src/error/EntityMetadataNotFoundError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAC7C,qDAAiD;AACjD,6DAAyD;AAEzD,MAAa,2BAA4B,SAAQ,2BAAY;IACzD,YAAY,MAAyB;QACjC,KAAK,EAAE,CAAA;QAEP,IAAI,CAAC,OAAO,GAAG,oBAAoB,IAAI,CAAC,eAAe,CACnD,MAAM,CACT,cAAc,CAAA;IACnB,CAAC;IAEO,eAAe,CAAC,MAAyB;QAC7C,IAAI,iCAAe,CAAC,cAAc,CAAC,MAAM,CAAC,EAAE,CAAC;YACzC,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAA;QAC9B,CAAC;aAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE,CAAC;YACtC,OAAO,MAAM,CAAC,IAAI,CAAA;QACtB,CAAC;aAAM,IAAI,yBAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,MAAM,IAAK,MAAc,EAAE,CAAC;YACnE,OAAQ,MAAc,CAAC,IAAI,CAAA;QAC/B,CAAC;aAAM,CAAC;YACJ,OAAO,MAAa,CAAA;QACxB,CAAC;IACL,CAAC;CACJ;AApBD,kEAoBC", "file": "EntityMetadataNotFoundError.js", "sourcesContent": ["import { EntityTarget } from \"../common/EntityTarget\"\nimport { TypeORMError } from \"./TypeORMError\"\nimport { ObjectUtils } from \"../util/ObjectUtils\"\nimport { InstanceChecker } from \"../util/InstanceChecker\"\n\nexport class EntityMetadataNotFoundError extends TypeORMError {\n    constructor(target: EntityTarget<any>) {\n        super()\n\n        this.message = `No metadata for \"${this.stringifyTarget(\n            target,\n        )}\" was found.`\n    }\n\n    private stringifyTarget(target: EntityTarget<any>): string {\n        if (InstanceChecker.isEntitySchema(target)) {\n            return target.options.name\n        } else if (typeof target === \"function\") {\n            return target.name\n        } else if (ObjectUtils.isObject(target) && \"name\" in (target as any)) {\n            return (target as any).name\n        } else {\n            return target as any\n        }\n    }\n}\n"], "sourceRoot": ".."}