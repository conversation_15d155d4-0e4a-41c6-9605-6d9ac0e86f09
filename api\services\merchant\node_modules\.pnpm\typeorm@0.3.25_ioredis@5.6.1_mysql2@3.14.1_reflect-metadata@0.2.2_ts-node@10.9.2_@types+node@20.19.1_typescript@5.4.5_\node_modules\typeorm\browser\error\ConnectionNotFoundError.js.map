{"version": 3, "sources": ["../browser/src/error/ConnectionNotFoundError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,uBAAwB,SAAQ,YAAY;IACrD,YAAY,IAAY;QACpB,KAAK,CAAC,eAAe,IAAI,kBAAkB,CAAC,CAAA;IAChD,CAAC;CACJ", "file": "ConnectionNotFoundError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to get connection that does not exist.\n */\nexport class ConnectionNotFoundError extends TypeORMError {\n    constructor(name: string) {\n        super(`Connection \"${name}\" was not found.`)\n    }\n}\n"], "sourceRoot": ".."}