-- 初始化角色数据
USE merchant_service_db;

-- 清空现有角色数据（如果有的话）
DELETE FROM merchant_sys_role WHERE id > 0;

-- 重置自增ID
ALTER TABLE merchant_sys_role AUTO_INCREMENT = 1;

-- 添加基础角色数据
INSERT INTO merchant_sys_role (
    id,
    name,
    label,
    remark,
    status,
    relevance,
    menuIdList,
    departmentIdList,
    createTime,
    updateTime
) VALUES
(1, '超级管理员', 'superAdmin', '拥有系统所有权限的超级管理员角色', 1, 1, '[]', '[]', NOW(), NOW()),
(2, '系统管理员', 'admin', '系统管理员，负责用户和角色管理', 1, 1, '[]', '[]', NOW(), NOW()),
(3, '商户管理员', 'merchantAdmin', '商户管理相关权限', 1, 1, '[]', '[]', NOW(), NOW()),
(4, '普通用户', 'user', '普通用户角色，基础查看权限', 1, 1, '[]', '[]', NOW(), NOW()),
(5, '审核员', 'auditor', '负责商户入驻审核', 1, 1, '[]', '[]', NOW(), NOW());

-- 查询插入结果
SELECT * FROM merchant_sys_role ORDER BY id; 