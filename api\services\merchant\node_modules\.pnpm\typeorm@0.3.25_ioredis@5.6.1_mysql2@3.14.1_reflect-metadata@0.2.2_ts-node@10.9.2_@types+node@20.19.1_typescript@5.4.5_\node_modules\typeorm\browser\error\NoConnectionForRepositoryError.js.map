{"version": 3, "sources": ["../browser/src/error/NoConnectionForRepositoryError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,8BAA+B,SAAQ,YAAY;IAC5D,YAAY,cAAsB;QAC9B,KAAK,CACD,gCAAgC,cAAc,oDAAoD;YAC9F,iFAAiF,CACxF,CAAA;IACL,CAAC;CACJ", "file": "NoConnectionForRepositoryError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when consumer tries to access repository before connection is established.\n */\nexport class NoConnectionForRepositoryError extends TypeORMError {\n    constructor(connectionName: string) {\n        super(\n            `Cannot get a Repository for \"${connectionName} connection, because connection with the database ` +\n                `is not established yet. Call connection#connect method to establish connection.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}