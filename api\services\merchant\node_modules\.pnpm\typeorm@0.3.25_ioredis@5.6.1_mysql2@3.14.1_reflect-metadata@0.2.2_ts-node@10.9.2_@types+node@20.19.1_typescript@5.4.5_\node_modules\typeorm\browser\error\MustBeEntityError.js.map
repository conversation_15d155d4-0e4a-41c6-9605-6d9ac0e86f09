{"version": 3, "sources": ["../browser/src/error/MustBeEntityError.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAA;AAE7C;;GAEG;AACH,MAAM,OAAO,iBAAkB,SAAQ,YAAY;IAC/C,YAAY,SAAiB,EAAE,UAAe;QAC1C,KAAK,CACD,UAAU,SAAS,6CAA6C,UAAU,aAAa,CAC1F,CAAA;IACL,CAAC;CACJ", "file": "MustBeEntityError.js", "sourcesContent": ["import { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when method expects entity but instead something else is given.\n */\nexport class MustBeEntityError extends TypeORMError {\n    constructor(operation: string, wrongValue: any) {\n        super(\n            `Cannot ${operation}, given value must be an entity, instead \"${wrongValue}\" is given.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}