"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.I18nConfiguration = void 0;
const core_1 = require("@midwayjs/core");
const DefaultConfig = require("./config/config.default");
const middleware_1 = require("./middleware");
let I18nConfiguration = class I18nConfiguration {
    async onReady() {
        this.applicationManager
            .getApplications(['koa', 'egg', 'faas', 'express'])
            .forEach(app => {
            app.useMiddleware(middleware_1.I18nMiddleware);
        });
    }
};
__decorate([
    (0, core_1.Inject)(),
    __metadata("design:type", core_1.MidwayApplicationManager)
], I18nConfiguration.prototype, "applicationManager", void 0);
I18nConfiguration = __decorate([
    (0, core_1.Configuration)({
        namespace: 'i18n',
        importConfigs: [
            {
                default: DefaultConfig,
            },
        ],
    })
], I18nConfiguration);
exports.I18nConfiguration = I18nConfiguration;
//# sourceMappingURL=configuration.js.map