{"version": 3, "file": "index.js", "sources": ["../esm/utils.js", "../esm/compareVersions.js", "../esm/compare.js", "../esm/satisfies.js", "../esm/validate.js"], "sourcesContent": ["export const semver = /^[v^~<>=]*?(\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+)(?:\\.([x*]|\\d+))?(?:-([\\da-z\\-]+(?:\\.[\\da-z\\-]+)*))?(?:\\+[\\da-z\\-]+(?:\\.[\\da-z\\-]+)*)?)?)?$/i;\nexport const validateAndParse = (version) => {\n    if (typeof version !== 'string') {\n        throw new TypeError('Invalid argument expected string');\n    }\n    const match = version.match(semver);\n    if (!match) {\n        throw new Error(`Invalid argument not valid semver ('${version}' received)`);\n    }\n    match.shift();\n    return match;\n};\nconst isWildcard = (s) => s === '*' || s === 'x' || s === 'X';\nconst tryParse = (v) => {\n    const n = parseInt(v, 10);\n    return isNaN(n) ? v : n;\n};\nconst forceType = (a, b) => typeof a !== typeof b ? [String(a), String(b)] : [a, b];\nconst compareStrings = (a, b) => {\n    if (isWildcard(a) || isWildcard(b))\n        return 0;\n    const [ap, bp] = forceType(tryParse(a), tryParse(b));\n    if (ap > bp)\n        return 1;\n    if (ap < bp)\n        return -1;\n    return 0;\n};\nexport const compareSegments = (a, b) => {\n    for (let i = 0; i < Math.max(a.length, b.length); i++) {\n        const r = compareStrings(a[i] || '0', b[i] || '0');\n        if (r !== 0)\n            return r;\n    }\n    return 0;\n};\n//# sourceMappingURL=utils.js.map", "import { compareSegments, validateAndParse } from './utils.js';\n/**\n * Compare [semver](https://semver.org/) version strings to find greater, equal or lesser.\n * This library supports the full semver specification, including comparing versions with different number of digits like `1.0.0`, `1.0`, `1`, and pre-release versions like `1.0.0-alpha`.\n * @param v1 - First version to compare\n * @param v2 - Second version to compare\n * @returns Numeric value compatible with the [Array.sort(fn) interface](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#Parameters).\n */\nexport const compareVersions = (v1, v2) => {\n    // validate input and split into segments\n    const n1 = validateAndParse(v1);\n    const n2 = validateAndParse(v2);\n    // pop off the patch\n    const p1 = n1.pop();\n    const p2 = n2.pop();\n    // validate numbers\n    const r = compareSegments(n1, n2);\n    if (r !== 0)\n        return r;\n    // validate pre-release\n    if (p1 && p2) {\n        return compareSegments(p1.split('.'), p2.split('.'));\n    }\n    else if (p1 || p2) {\n        return p1 ? -1 : 1;\n    }\n    return 0;\n};\n//# sourceMappingURL=compareVersions.js.map", "import { compareVersions } from './compareVersions.js';\n/**\n * Compare [semver](https://semver.org/) version strings using the specified operator.\n *\n * @param v1 First version to compare\n * @param v2 Second version to compare\n * @param operator Allowed arithmetic operator to use\n * @returns `true` if the comparison between the firstVersion and the secondVersion satisfies the operator, `false` otherwise.\n *\n * @example\n * ```\n * compare('10.1.8', '10.0.4', '>'); // return true\n * compare('10.0.1', '10.0.1', '='); // return true\n * compare('10.1.1', '10.2.2', '<'); // return true\n * compare('10.1.1', '10.2.2', '<='); // return true\n * compare('10.1.1', '10.2.2', '>='); // return false\n * ```\n */\nexport const compare = (v1, v2, operator) => {\n    // validate input operator\n    assertValidOperator(operator);\n    // since result of compareVersions can only be -1 or 0 or 1\n    // a simple map can be used to replace switch\n    const res = compareVersions(v1, v2);\n    return operatorResMap[operator].includes(res);\n};\nconst operatorResMap = {\n    '>': [1],\n    '>=': [0, 1],\n    '=': [0],\n    '<=': [-1, 0],\n    '<': [-1],\n    '!=': [-1, 1],\n};\nconst allowedOperators = Object.keys(operatorResMap);\nconst assertValidOperator = (op) => {\n    if (typeof op !== 'string') {\n        throw new TypeError(`Invalid operator type, expected string but got ${typeof op}`);\n    }\n    if (allowedOperators.indexOf(op) === -1) {\n        throw new Error(`Invalid operator, expected one of ${allowedOperators.join('|')}`);\n    }\n};\n//# sourceMappingURL=compare.js.map", "import { compare } from './compare.js';\nimport { compareSegments, validateAndParse } from './utils.js';\n/**\n * Match [npm semver](https://docs.npmjs.com/cli/v6/using-npm/semver) version range.\n *\n * @param version Version number to match\n * @param range Range pattern for version\n * @returns `true` if the version number is within the range, `false` otherwise.\n *\n * @example\n * ```\n * satisfies('1.1.0', '^1.0.0'); // return true\n * satisfies('1.1.0', '~1.0.0'); // return false\n * ```\n */\nexport const satisfies = (version, range) => {\n    // clean input\n    range = range.replace(/([><=]+)\\s+/g, '$1');\n    // handle multiple comparators\n    if (range.includes('||')) {\n        return range.split('||').some((r) => satisfies(version, r));\n    }\n    else if (range.includes(' - ')) {\n        const [a, b] = range.split(' - ', 2);\n        return satisfies(version, `>=${a} <=${b}`);\n    }\n    else if (range.includes(' ')) {\n        return range\n            .trim()\n            .replace(/\\s{2,}/g, ' ')\n            .split(' ')\n            .every((r) => satisfies(version, r));\n    }\n    // if no range operator then \"=\"\n    const m = range.match(/^([<>=~^]+)/);\n    const op = m ? m[1] : '=';\n    // if gt/lt/eq then operator compare\n    if (op !== '^' && op !== '~')\n        return compare(version, range, op);\n    // else range of either \"~\" or \"^\" is assumed\n    const [v1, v2, v3, , vp] = validateAndParse(version);\n    const [r1, r2, r3, , rp] = validateAndParse(range);\n    const v = [v1, v2, v3];\n    const r = [r1, r2 !== null && r2 !== void 0 ? r2 : 'x', r3 !== null && r3 !== void 0 ? r3 : 'x'];\n    // validate pre-release\n    if (rp) {\n        if (!vp)\n            return false;\n        if (compareSegments(v, r) !== 0)\n            return false;\n        if (compareSegments(vp.split('.'), rp.split('.')) === -1)\n            return false;\n    }\n    // first non-zero number\n    const nonZero = r.findIndex((v) => v !== '0') + 1;\n    // pointer to where segments can be >=\n    const i = op === '~' ? 2 : nonZero > 1 ? nonZero : 1;\n    // before pointer must be equal\n    if (compareSegments(v.slice(0, i), r.slice(0, i)) !== 0)\n        return false;\n    // after pointer must be >=\n    if (compareSegments(v.slice(i), r.slice(i)) === -1)\n        return false;\n    return true;\n};\n//# sourceMappingURL=satisfies.js.map", "import { semver } from './utils.js';\n/**\n * Validate [semver](https://semver.org/) version strings.\n *\n * @param version Version number to validate\n * @returns `true` if the version number is a valid semver version number, `false` otherwise.\n *\n * @example\n * ```\n * validate('1.0.0-rc.1'); // return true\n * validate('1.0-rc.1'); // return false\n * validate('foo'); // return false\n * ```\n */\nexport const validate = (version) => typeof version === 'string' && /^[v\\d]/.test(version) && semver.test(version);\n/**\n * Validate [semver](https://semver.org/) version strings strictly. Will not accept wildcards and version ranges.\n *\n * @param version Version number to validate\n * @returns `true` if the version number is a valid semver version number `false` otherwise\n *\n * @example\n * ```\n * validate('1.0.0-rc.1'); // return true\n * validate('1.0-rc.1'); // return false\n * validate('foo'); // return false\n * ```\n */\nexport const validateStrict = (version) => typeof version === 'string' &&\n    /^(0|[1-9]\\d*)\\.(0|[1-9]\\d*)\\.(0|[1-9]\\d*)(?:-((?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*)(?:\\.(?:0|[1-9]\\d*|\\d*[a-zA-Z-][0-9a-zA-Z-]*))*))?(?:\\+([0-9a-zA-Z-]+(?:\\.[0-9a-zA-Z-]+)*))?$/.test(version);\n//# sourceMappingURL=validate.js.map"], "names": [], "mappings": ";;;;;;IAAO,MAAM,MAAM,GAAG,4IAA4I,CAAC;IAC5J,MAAM,gBAAgB,GAAG,CAAC,OAAO,KAAK;IAC7C,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;IACrC,QAAQ,MAAM,IAAI,SAAS,CAAC,kCAAkC,CAAC,CAAC;IAChE,KAAK;IACL,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;IACxC,IAAI,IAAI,CAAC,KAAK,EAAE;IAChB,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC;IACrF,KAAK;IACL,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;IAClB,IAAI,OAAO,KAAK,CAAC;IACjB,CAAC,CAAC;IACF,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;IAC9D,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK;IACxB,IAAI,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IAC9B,IAAI,OAAO,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,MAAM,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACpF,MAAM,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;IACjC,IAAI,IAAI,UAAU,CAAC,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC;IACtC,QAAQ,OAAO,CAAC,CAAC;IACjB,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACzD,IAAI,IAAI,EAAE,GAAG,EAAE;IACf,QAAQ,OAAO,CAAC,CAAC;IACjB,IAAI,IAAI,EAAE,GAAG,EAAE;IACf,QAAQ,OAAO,CAAC,CAAC,CAAC;IAClB,IAAI,OAAO,CAAC,CAAC;IACb,CAAC,CAAC;IACK,MAAM,eAAe,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;IACzC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE;IAC3D,QAAQ,MAAM,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;IAC3D,QAAQ,IAAI,CAAC,KAAK,CAAC;IACnB,YAAY,OAAO,CAAC,CAAC;IACrB,KAAK;IACL,IAAI,OAAO,CAAC,CAAC;IACb,CAAC;;IClCD;IACA;IACA;IACA;IACA;IACA;IACA;AACY,UAAC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK;IAC3C;IACA,IAAI,MAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACpC,IAAI,MAAM,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACpC;IACA,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACxB,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACxB;IACA,IAAI,MAAM,CAAC,GAAG,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACtC,IAAI,IAAI,CAAC,KAAK,CAAC;IACf,QAAQ,OAAO,CAAC,CAAC;IACjB;IACA,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE;IAClB,QAAQ,OAAO,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7D,KAAK;IACL,SAAS,IAAI,EAAE,IAAI,EAAE,EAAE;IACvB,QAAQ,OAAO,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC3B,KAAK;IACL,IAAI,OAAO,CAAC,CAAC;IACb;;IC1BA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACY,UAAC,OAAO,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,KAAK;IAC7C;IACA,IAAI,mBAAmB,CAAC,QAAQ,CAAC,CAAC;IAClC;IACA;IACA,IAAI,MAAM,GAAG,GAAG,eAAe,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;IACxC,IAAI,OAAO,cAAc,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;IAClD,EAAE;IACF,MAAM,cAAc,GAAG;IACvB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IACZ,IAAI,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IACZ,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;IACb,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACjB,CAAC,CAAC;IACF,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACrD,MAAM,mBAAmB,GAAG,CAAC,EAAE,KAAK;IACpC,IAAI,IAAI,OAAO,EAAE,KAAK,QAAQ,EAAE;IAChC,QAAQ,MAAM,IAAI,SAAS,CAAC,CAAC,+CAA+C,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3F,KAAK;IACL,IAAI,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;IAC7C,QAAQ,MAAM,IAAI,KAAK,CAAC,CAAC,kCAAkC,EAAE,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3F,KAAK;IACL,CAAC;;ICxCD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACY,UAAC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,KAAK;IAC7C;IACA,IAAI,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC9B,QAAQ,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACpE,KAAK;IACL,SAAS,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;IACpC,QAAQ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IAC7C,QAAQ,OAAO,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,KAAK;IACL,SAAS,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IAClC,QAAQ,OAAO,KAAK;IACpB,aAAa,IAAI,EAAE;IACnB,aAAa,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IACpC,aAAa,KAAK,CAAC,GAAG,CAAC;IACvB,aAAa,KAAK,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;IACjD,KAAK;IACL;IACA,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;IACzC,IAAI,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;IAC9B;IACA,IAAI,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG;IAChC,QAAQ,OAAO,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;IAC3C;IACA,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;IACzD,IAAI,MAAM,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;IACvD,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAC3B,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CAAC;IACrG;IACA,IAAI,IAAI,EAAE,EAAE;IACZ,QAAQ,IAAI,CAAC,EAAE;IACf,YAAY,OAAO,KAAK,CAAC;IACzB,QAAQ,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC;IACvC,YAAY,OAAO,KAAK,CAAC;IACzB,QAAQ,IAAI,eAAe,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAChE,YAAY,OAAO,KAAK,CAAC;IACzB,KAAK;IACL;IACA,IAAI,MAAM,OAAO,GAAG,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IACtD;IACA,IAAI,MAAM,CAAC,GAAG,EAAE,KAAK,GAAG,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,OAAO,GAAG,CAAC,CAAC;IACzD;IACA,IAAI,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;IAC3D,QAAQ,OAAO,KAAK,CAAC;IACrB;IACA,IAAI,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;IACtD,QAAQ,OAAO,KAAK,CAAC;IACrB,IAAI,OAAO,IAAI,CAAC;IAChB;;IC/DA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACY,UAAC,QAAQ,GAAG,CAAC,OAAO,KAAK,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;IACnH;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACY,UAAC,cAAc,GAAG,CAAC,OAAO,KAAK,OAAO,OAAO,KAAK,QAAQ;IACtE,IAAI,qLAAqL,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;"}