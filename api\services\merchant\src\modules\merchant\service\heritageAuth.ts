import { Provide } from '@midwayjs/core';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { HeritageAuthEntity } from '../entity/heritageAuth';

/**
 * 创作者认证RPC服务
 */
@Provide()
@CoolRpcService({
  entity: HeritageAuthEntity,
  method: ["add", "delete", "update", "info", "list", "page"]
})
export class HeritageAuthService extends BaseRpcService {
  @InjectEntityModel(HeritageAuthEntity)
  heritageAuthEntity!: Repository<HeritageAuthEntity>;

  /**
   * 重写分页方法，保证健壮性
   */
  async page(query: any, option?: any) {
    // 当RPC调用传递参数为数组[query, options]时，需要解构第一个元素
    const actualQuery = Array.isArray(query) ? query[0] : query;
    const actualOption = Array.isArray(query) ? query[1] : option;

    console.log('🔍 [微服务] 接收到的查询参数:', actualQuery);

    const page = actualQuery.page || actualQuery.current || 1;
    const size = actualQuery.size || actualQuery.pageSize || 10;
    const qb = this.heritageAuthEntity.createQueryBuilder('heritage');

    // 可加筛选条件，如类型、状态、关键词等
    if (actualQuery.type !== undefined && actualQuery.type !== "") {
      console.log('🔍 [微服务] 添加类型筛选条件:', actualQuery.type);
      qb.andWhere('heritage.type = :type', { type: actualQuery.type });
    }
    if (actualQuery.status !== undefined && actualQuery.status !== "") {
      console.log('🔍 [微服务] 添加状态筛选条件:', actualQuery.status);
      qb.andWhere('heritage.status = :status', { status: actualQuery.status });
    }
    if (actualQuery.searchKey) {
      console.log('🔍 [微服务] 添加关键词筛选条件:', actualQuery.searchKey);
      qb.andWhere(
        '(heritage.name LIKE :kw OR heritage.companyName LIKE :kw OR heritage.legalPerson LIKE :kw OR heritage.category LIKE :kw)',
        { kw: `%${actualQuery.searchKey}%` }
      );
    }

    // 健壮排序，默认按id倒序
    qb.orderBy('heritage.id', 'DESC');

    // 分页
    const [list, total] = await qb.skip((page - 1) * size).take(size).getManyAndCount();

    return {
      list,
      pagination: {
        page: Number(page),
        size: Number(size),
        total
      }
    };
  }

  /**
   * 审核创作者认证
   */
  async audit(params: { id: number; status: number; remark?: string }) {
    const { id, status, remark } = params;
    
    console.log('🔍 [微服务] 审核创作者认证:', { id, status, remark });
    
    // 验证状态值
    if (![0, 1, 2].includes(status)) {
      throw new Error('无效的审核状态');
    }
    
    // 查找记录
    const heritage = await this.heritageAuthEntity.findOne({ where: { id } });
    if (!heritage) {
      throw new Error('创作者认证记录不存在');
    }
    
    // 更新状态
    await this.heritageAuthEntity.update(id, { 
      status,
      remark: remark || null,
      updateTime: new Date()
    });
    
    console.log('✅ [微服务] 创作者认证审核完成:', { id, status });
    return true;
  }



  /**
   * 批量审核创作者认证
   */
  async batchAudit(params: { ids: number[]; status: number; remark?: string }) {
    const { ids, status, remark } = params;
    
    console.log('🔍 [微服务] 批量审核创作者认证:', { ids, status, remark });
    
    // 验证状态值
    if (![0, 1, 2].includes(status)) {
      throw new Error('无效的审核状态');
    }
    
    // 批量更新
    await this.heritageAuthEntity.update(ids, { 
      status,
      remark: remark || null,
      updateTime: new Date()
    });
    
    console.log('✅ [微服务] 批量审核完成:', { count: ids.length, status });
    return true;
  }
}