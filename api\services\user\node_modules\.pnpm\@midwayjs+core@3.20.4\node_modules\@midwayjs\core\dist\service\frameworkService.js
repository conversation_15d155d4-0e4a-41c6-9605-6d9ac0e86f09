"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayFrameworkService = void 0;
const decorator_1 = require("../decorator");
const interface_1 = require("../interface");
const configService_1 = require("./configService");
const loggerService_1 = require("./loggerService");
const baseFramework_1 = require("../baseFramework");
const pipelineService_1 = require("./pipelineService");
const decoratorService_1 = require("./decoratorService");
const aspectService_1 = require("./aspectService");
const applicationManager_1 = require("../common/applicationManager");
const util = require("util");
const error_1 = require("../error");
const constants_1 = require("../constants");
const performanceManager_1 = require("../common/performanceManager");
const debug = util.debuglog('midway:debug');
let MidwayFrameworkService = class MidwayFrameworkService {
    constructor(applicationContext, globalOptions) {
        this.applicationContext = applicationContext;
        this.globalOptions = globalOptions;
        this.globalFrameworkList = [];
    }
    async init() {
        var _a, _b;
        // register base config hook
        this.decoratorService.registerPropertyHandler(decorator_1.CONFIG_KEY, (propertyName, meta) => {
            var _a;
            if (meta.identifier === decorator_1.ALL) {
                return this.configService.getConfiguration();
            }
            else {
                return this.configService.getConfiguration((_a = meta.identifier) !== null && _a !== void 0 ? _a : propertyName);
            }
        });
        // register @Logger decorator handler
        this.decoratorService.registerPropertyHandler(decorator_1.LOGGER_KEY, (propertyName, meta) => {
            var _a;
            return this.loggerService.getLogger((_a = meta.identifier) !== null && _a !== void 0 ? _a : propertyName);
        });
        this.decoratorService.registerPropertyHandler(decorator_1.PIPELINE_IDENTIFIER, (key, meta, instance) => {
            var _a, _b;
            return new pipelineService_1.MidwayPipelineService((_b = (_a = instance[constants_1.REQUEST_OBJ_CTX_KEY]) === null || _a === void 0 ? void 0 : _a.requestContext) !== null && _b !== void 0 ? _b : this.applicationContext, meta.valves);
        });
        // register @App decorator handler
        this.decoratorService.registerPropertyHandler(decorator_1.APPLICATION_KEY, (propertyName, meta) => {
            if (meta.type) {
                const framework = this.applicationManager.getApplication(meta.type);
                if (!framework) {
                    throw new error_1.MidwayCommonError(`Framework ${meta.type} not Found`);
                }
                return framework;
            }
            else {
                return this.getMainApp();
            }
        });
        this.decoratorService.registerPropertyHandler(decorator_1.PLUGIN_KEY, (propertyName, meta) => {
            var _a;
            return this.getMainApp()[(_a = meta.identifier) !== null && _a !== void 0 ? _a : propertyName];
        });
        this.decoratorService.registerPropertyHandler(decorator_1.FACTORY_SERVICE_CLIENT_KEY, (propertyName, meta) => {
            const factory = this.applicationContext.get(meta.serviceFactoryClz);
            const clientName = meta.clientName || factory.getDefaultClientName();
            if (clientName && factory.has(clientName)) {
                return factory.get(clientName);
            }
            else {
                if (!clientName) {
                    throw new error_1.MidwayParameterError(`Please set clientName or options.defaultClientName for ${meta.serviceFactoryClz.name}).`);
                }
                else {
                    throw new error_1.MidwayParameterError(`ClientName(${clientName} not found in ${meta.serviceFactoryClz.name}).`);
                }
            }
        });
        let frameworks = (0, decorator_1.listModule)(decorator_1.FRAMEWORK_KEY);
        // filter proto
        frameworks = filterProtoFramework(frameworks);
        debug(`[core]: Found Framework length = ${frameworks.length}`);
        if (frameworks.length) {
            for (const frameworkClz of frameworks) {
                if (!this.applicationContext.hasDefinition((0, decorator_1.getProviderUUId)(frameworkClz))) {
                    debug(`[core]: Found Framework "${frameworkClz.name}" but missing definition, skip initialize.`);
                    continue;
                }
                const frameworkInstance = await this.applicationContext.getAsync(frameworkClz, [this.applicationContext]);
                // if enable, just init framework
                if (frameworkInstance.isEnable()) {
                    performanceManager_1.MidwayInitializerPerformanceManager.frameworkInitializeStart(frameworkInstance.getFrameworkName());
                    // app init
                    await frameworkInstance.initialize({
                        applicationContext: this.applicationContext,
                        namespace: frameworkInstance.getNamespace(),
                        ...this.globalOptions,
                    });
                    performanceManager_1.MidwayInitializerPerformanceManager.frameworkInitializeEnd(frameworkInstance.getFrameworkName());
                    debug(`[core]: Found Framework "${frameworkInstance.getFrameworkName()}" and initialize.`);
                }
                else {
                    debug(`[core]: Found Framework "${frameworkInstance.getFrameworkName()}" and delay initialize.`);
                }
                // app init
                const definition = this.applicationContext.registry.getDefinition((0, decorator_1.getProviderUUId)(frameworkClz));
                // set framework namespace here
                frameworkInstance.setNamespace(definition === null || definition === void 0 ? void 0 : definition.namespace);
                // link framework to application manager
                this.applicationManager.addFramework((_a = definition === null || definition === void 0 ? void 0 : definition.namespace) !== null && _a !== void 0 ? _a : frameworkInstance.getFrameworkName(), frameworkInstance);
                this.globalFrameworkList.push(frameworkInstance);
            }
            let mainNs;
            /**
             * 这里处理引入组件的顺序，在主框架之前是否包含其他的 framework
             * 1、装饰器的顺序和 import 的写的顺序有关
             * 2、主框架和 configuration 中的配置加载顺序有关
             * 3、两者不符合的话，App 装饰器获取的 app 会不一致，导致中间件等无法正常使用
             */
            const namespaceList = this.applicationContext.getNamespaceList();
            for (const namespace of namespaceList) {
                const framework = this.applicationManager.getApplication(namespace);
                if (framework) {
                    mainNs = namespace;
                    break;
                }
            }
            global['MIDWAY_MAIN_FRAMEWORK'] = this.mainFramework =
                (_b = this.applicationManager.getFramework(mainNs)) !== null && _b !== void 0 ? _b : this.globalFrameworkList[0];
            debug(`[core]: Current main Framework is "${mainNs}".`);
        }
        // init aspect module
        await this.aspectService.loadAspect();
    }
    getMainApp() {
        var _a;
        return (_a = this.mainFramework) === null || _a === void 0 ? void 0 : _a.getApplication();
    }
    getMainFramework() {
        return this.mainFramework;
    }
    getFramework(namespaceOrFrameworkType) {
        return this.applicationManager.getFramework(namespaceOrFrameworkType);
    }
    async runFramework() {
        const namespaceList = this.applicationContext.getNamespaceList();
        // globalFrameworkList 需要基于 namespaceList 进行排序，不然会出现顺序问题
        this.globalFrameworkList = this.globalFrameworkList.sort((a, b) => {
            return (namespaceList.indexOf(a.getNamespace()) -
                namespaceList.indexOf(b.getNamespace()));
        });
        for (const frameworkInstance of this.globalFrameworkList) {
            // if enable, just init framework
            if (frameworkInstance.isEnable()) {
                performanceManager_1.MidwayInitializerPerformanceManager.frameworkRunStart(frameworkInstance.getFrameworkName());
                // app init
                await frameworkInstance.run();
                debug(`[core]: Found Framework "${frameworkInstance.getFrameworkName()}" and run.`);
                performanceManager_1.MidwayInitializerPerformanceManager.frameworkRunEnd(frameworkInstance.getFrameworkName());
            }
        }
    }
    async stopFramework() {
        await Promise.all(Array.from(this.globalFrameworkList).map(frameworkInstance => {
            return frameworkInstance.stop();
        }));
    }
};
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", configService_1.MidwayConfigService)
], MidwayFrameworkService.prototype, "configService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", loggerService_1.MidwayLoggerService)
], MidwayFrameworkService.prototype, "loggerService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", aspectService_1.MidwayAspectService)
], MidwayFrameworkService.prototype, "aspectService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", decoratorService_1.MidwayDecoratorService)
], MidwayFrameworkService.prototype, "decoratorService", void 0);
__decorate([
    (0, decorator_1.Inject)(),
    __metadata("design:type", applicationManager_1.MidwayApplicationManager)
], MidwayFrameworkService.prototype, "applicationManager", void 0);
__decorate([
    (0, decorator_1.Init)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MidwayFrameworkService.prototype, "init", null);
MidwayFrameworkService = __decorate([
    (0, decorator_1.Provide)(),
    (0, decorator_1.Scope)(interface_1.ScopeEnum.Singleton),
    __metadata("design:paramtypes", [Object, Object])
], MidwayFrameworkService);
exports.MidwayFrameworkService = MidwayFrameworkService;
function filterProtoFramework(frameworks) {
    const frameworkProtoArr = [];
    // 这里把继承的框架父类都找出来，然后排除掉，只取第一层
    for (const framework of frameworks) {
        let proto = Object.getPrototypeOf(framework);
        while (proto.name && proto.name !== baseFramework_1.BaseFramework.name) {
            frameworkProtoArr.push(proto);
            proto = Object.getPrototypeOf(proto);
        }
    }
    return frameworks.filter(framework => {
        return !frameworkProtoArr.includes(framework);
    });
}
//# sourceMappingURL=frameworkService.js.map