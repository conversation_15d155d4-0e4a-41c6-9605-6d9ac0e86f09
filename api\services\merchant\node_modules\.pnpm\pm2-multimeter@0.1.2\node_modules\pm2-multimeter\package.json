{"name": "pm2-multimeter", "version": "0.1.2", "description": "render multiple progress bars at once on the terminal with eventlimit maxed", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "dependencies": {"charm": "~0.1.1"}, "repository": {"type": "git", "url": "https://github.com/Unitech/node-multimeter.git"}, "keywords": ["progress", "bar", "status", "meter", "terminal", "console", "ansi"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT/X11", "engine": {"node": ">=0.4"}}