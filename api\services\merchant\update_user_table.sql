-- 更新商户系统用户表结构
USE merchant_service_db;

-- 添加 nickName 字段（如果不存在）
ALTER TABLE merchant_sys_user 
ADD COLUMN IF NOT EXISTS nickName VARCHAR(100) NULL COMMENT '昵称' AFTER userName;

-- 添加 remark 字段（如果不存在）  
ALTER TABLE merchant_sys_user 
ADD COLUMN IF NOT EXISTS remark VARCHAR(500) NULL COMMENT '备注' AFTER email;

-- 查看更新后的表结构
DESCRIBE merchant_sys_user;

-- 查看现有数据
SELECT userId, userName, nickName, email, remark, status FROM merchant_sys_user LIMIT 5; 