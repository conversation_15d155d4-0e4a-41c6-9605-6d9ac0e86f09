"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MidwayInitializerPerformanceManager = exports.MidwayPerformanceManager = void 0;
const perf_hooks_1 = require("perf_hooks");
class MidwayPerformanceManager {
    constructor(group) {
        this.group = group;
        this.observer = null;
        this.marks = new Set();
        this.measures = new Set();
    }
    static getInstance(group) {
        if (!this.instances.has(group)) {
            this.instances.set(group, new MidwayPerformanceManager(group));
        }
        return this.instances.get(group);
    }
    formatKey(key, step) {
        return `${this.group}:${key}-${step}`;
    }
    markStart(key) {
        const markKey = this.formatKey(key, 'start');
        perf_hooks_1.performance.mark(markKey);
        this.marks.add(markKey);
    }
    markEnd(key) {
        const startKey = this.formatKey(key, 'start');
        const endKey = this.formatKey(key, 'end');
        const measureKey = `${this.group}:${key}`;
        perf_hooks_1.performance.mark(endKey);
        this.marks.add(endKey);
        perf_hooks_1.performance.measure(measureKey, startKey, endKey);
        this.measures.add(measureKey);
    }
    observeMeasure(callback) {
        if (this.observer) {
            return;
        }
        this.observer = new perf_hooks_1.PerformanceObserver(list => {
            const filteredEntries = list
                .getEntries()
                .filter(entry => entry.name.startsWith(`${this.group}:`));
            if (filteredEntries.length > 0) {
                callback({
                    getEntries: () => filteredEntries,
                });
            }
        });
        this.observer.observe({ entryTypes: ['measure'] });
        return this.observer;
    }
    disconnect() {
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
    }
    clean() {
        this.marks.forEach(mark => {
            try {
                perf_hooks_1.performance.clearMarks(mark);
            }
            catch (error) {
                console.warn(`Failed to clear mark ${mark}: ${error}`);
            }
        });
        this.marks.clear();
        this.measures.forEach(measure => {
            try {
                perf_hooks_1.performance.clearMeasures(measure);
            }
            catch (error) {
                console.warn(`Failed to clear measure ${measure}: ${error}`);
            }
        });
        this.measures.clear();
        this.disconnect();
    }
    static cleanAll() {
        this.instances.forEach(instance => instance.clean());
        this.instances.clear();
    }
    static getInitialPerformanceEntries() {
        const entries = [];
        perf_hooks_1.performance === null || perf_hooks_1.performance === void 0 ? void 0 : perf_hooks_1.performance.getEntries().forEach(entry => {
            if (entry.name.startsWith(this.DEFAULT_GROUP.INITIALIZE)) {
                entries.push(entry.toJSON());
            }
        });
        return entries;
    }
}
exports.MidwayPerformanceManager = MidwayPerformanceManager;
MidwayPerformanceManager.instances = new Map();
MidwayPerformanceManager.DEFAULT_GROUP = {
    INITIALIZE: 'MidwayInitialize',
};
class MidwayInitializerPerformanceManager {
    static markStart(key) {
        const manager = MidwayPerformanceManager.getInstance(MidwayPerformanceManager.DEFAULT_GROUP.INITIALIZE);
        manager.markStart(key);
    }
    static markEnd(key) {
        const manager = MidwayPerformanceManager.getInstance(MidwayPerformanceManager.DEFAULT_GROUP.INITIALIZE);
        manager.markEnd(key);
    }
    static frameworkInitializeStart(frameworkName) {
        this.markStart(`${this.MEASURE_KEYS.FRAMEWORK_INITIALIZE}:${frameworkName}`);
    }
    static frameworkInitializeEnd(frameworkName) {
        this.markEnd(`${this.MEASURE_KEYS.FRAMEWORK_INITIALIZE}:${frameworkName}`);
    }
    static frameworkRunStart(frameworkName) {
        this.markStart(`${this.MEASURE_KEYS.FRAMEWORK_RUN}:${frameworkName}`);
    }
    static frameworkRunEnd(frameworkName) {
        this.markEnd(`${this.MEASURE_KEYS.FRAMEWORK_RUN}:${frameworkName}`);
    }
    static lifecycleStart(namespace, lifecycleName) {
        this.markStart(`${this.MEASURE_KEYS.LIFECYCLE_PREPARE}:${namespace}:${lifecycleName}`);
    }
    static lifecycleEnd(namespace, lifecycleName) {
        this.markEnd(`${this.MEASURE_KEYS.LIFECYCLE_PREPARE}:${namespace}:${lifecycleName}`);
    }
}
exports.MidwayInitializerPerformanceManager = MidwayInitializerPerformanceManager;
MidwayInitializerPerformanceManager.MEASURE_KEYS = {
    INITIALIZE: 'Initialize',
    METADATA_PREPARE: 'MetadataPrepare',
    DETECTOR_PREPARE: 'DetectorPrepare',
    DEFINITION_PREPARE: 'DefinitionPrepare',
    CONFIG_LOAD: 'ConfigLoad',
    LOGGER_PREPARE: 'LoggerPrepare',
    FRAMEWORK_PREPARE: 'FrameworkPrepare',
    FRAMEWORK_INITIALIZE: 'FrameworkInitialize',
    FRAMEWORK_RUN: 'FrameworkRun',
    LIFECYCLE_PREPARE: 'LifecyclePrepare',
    PRELOAD_MODULE_PREPARE: 'PreloadModulePrepare',
};
//# sourceMappingURL=performanceManager.js.map