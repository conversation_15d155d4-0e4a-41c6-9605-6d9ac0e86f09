{"version": 3, "file": "IsNegative.js", "sourceRoot": "", "sources": ["../../../../src/decorator/number/IsNegative.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAEhE,MAAM,CAAC,IAAM,WAAW,GAAG,YAAY,CAAC;AAExC;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,KAAc;IACvC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AAChD,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,UAAU,CAAC,iBAAqC;IAC9D,OAAO,UAAU,CACf;QACE,IAAI,EAAE,WAAW;QACjB,SAAS,EAAE;YACT,QAAQ,EAAE,UAAC,KAAK,EAAE,IAAI,IAAc,OAAA,UAAU,CAAC,KAAK,CAAC,EAAjB,CAAiB;YACrD,cAAc,EAAE,YAAY,CAC1B,UAAA,UAAU,IAAI,OAAA,UAAU,GAAG,qCAAqC,EAAlD,CAAkD,EAChE,iBAAiB,CAClB;SACF;KACF,EACD,iBAAiB,CAClB,CAAC;AACJ,CAAC", "sourcesContent": ["import { ValidationOptions } from '../ValidationOptions';\nimport { buildMessage, ValidateBy } from '../common/ValidateBy';\n\nexport const IS_NEGATIVE = 'isNegative';\n\n/**\n * Checks if the value is a negative number smaller than zero.\n */\nexport function isNegative(value: unknown): boolean {\n  return typeof value === 'number' && value < 0;\n}\n\n/**\n * Checks if the value is a negative number smaller than zero.\n */\nexport function IsNegative(validationOptions?: ValidationOptions): PropertyDecorator {\n  return ValidateBy(\n    {\n      name: IS_NEGATIVE,\n      validator: {\n        validate: (value, args): boolean => isNegative(value),\n        defaultMessage: buildMessage(\n          eachPrefix => eachPrefix + '$property must be a negative number',\n          validationOptions\n        ),\n      },\n    },\n    validationOptions\n  );\n}\n"]}