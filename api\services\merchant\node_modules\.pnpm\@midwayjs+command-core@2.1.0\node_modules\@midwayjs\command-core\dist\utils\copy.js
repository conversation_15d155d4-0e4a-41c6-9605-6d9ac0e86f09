"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exists = exports.copyStaticFiles = exports.copyFiles = void 0;
const plimit = require("p-limit");
const globby = require("globby");
const path_1 = require("path");
const fs_extra_1 = require("fs-extra");
const copyFiles = async (options) => {
    const { defaultInclude, include, exclude, sourceDir, targetDir, log } = options;
    const paths = await globby((defaultInclude || ['*.yml', '*.js', '*.ts', '*.json', 'app', 'config']).concat(include || []), {
        cwd: sourceDir,
        followSymbolicLinks: false,
        ignore: [
            '**/node_modules/**',
            '**/test/**',
            '**/run/**',
            '**/.serverless/**',
            '**/.faas_debug_tmp/**', // faas 调试临时目录
        ].concat(exclude || []),
    });
    await docopy(sourceDir, targetDir, paths, log);
};
exports.copyFiles = copyFiles;
const copyStaticFiles = async ({ sourceDir, targetDir, log }) => {
    if (!sourceDir || !targetDir) {
        return;
    }
    const paths = globby.sync(['**/*.*'], {
        cwd: sourceDir,
        followSymbolicLinks: false,
        ignore: [
            '**/*.ts',
            '**/node_modules/**', // 模块依赖目录
        ],
    });
    return docopy(sourceDir, targetDir, paths, log);
};
exports.copyStaticFiles = copyStaticFiles;
const docopy = async (sourceDir, targetDir, paths, log) => {
    const limit = plimit(20);
    await Promise.all(paths.map((path) => {
        return limit(async () => {
            const source = (0, path_1.join)(sourceDir, path);
            const target = (0, path_1.join)(targetDir, path);
            if (await (0, exports.exists)(target)) {
                const sourceStat = await (0, fs_extra_1.stat)(source);
                const targetStat = await (0, fs_extra_1.stat)(target);
                // source 修改时间小于目标文件 修改时间，则不拷贝
                if (sourceStat.mtimeMs <= targetStat.mtimeMs) {
                    return;
                }
            }
            if (log) {
                log(path);
            }
            return (0, fs_extra_1.copy)(source, target).catch(e => {
                if (log) {
                    log(`Error!!! From '${source}' to '${target}'`, e);
                }
            });
        });
    }));
};
const exists = async (path) => {
    try {
        await (0, fs_extra_1.access)(path);
        return true;
    }
    catch (_a) {
        return false;
    }
};
exports.exists = exists;
//# sourceMappingURL=copy.js.map