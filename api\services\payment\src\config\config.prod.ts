export default {
  typeorm: {
    type: 'mysql',
    host: 'localhost',
    port: 3306,
    username: 'root',
    password: 'password',
    database: 'payment_service_db',
    synchronize: false,
    logging: false,
    entities: ['src/modules/payment/entity/*.ts'],
  },
  redis: {
    cluster: true,
    nodes: [
      { host: '*************', port: 6379 },
      { host: '*************', port: 6379 },
      { host: '*************', port: 6379 },
      // 更多节点可按需添加
    ],
    password: 'your_password', // 如有密码
  },
  rpc: {
    namespace: 'payment',
    port: 9805,
  },
}; 