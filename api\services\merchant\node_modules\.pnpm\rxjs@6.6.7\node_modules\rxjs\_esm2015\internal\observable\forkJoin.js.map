{"version": 3, "file": "forkJoin.js", "sources": ["../../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAE3C,OAAO,EAAE,OAAO,EAAE,MAAM,iBAAiB,CAAC;AAC1C,OAAO,EAAE,GAAG,EAAE,MAAM,kBAAkB,CAAC;AACvC,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAE5C,OAAO,EAAE,IAAI,EAAE,MAAM,QAAQ,CAAC;AAsI9B,MAAM,UAAU,QAAQ,CACtB,GAAG,OAAc;IAEjB,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;QACxB,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QACzB,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;YAClB,OAAO,gBAAgB,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACtC;QAED,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,EAAE;YACxE,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;SAC5D;KACF;IAGD,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;QACrD,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,EAAc,CAAC;QACjD,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;QAC/E,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,IAAI,CACzC,GAAG,CAAC,CAAC,IAAW,EAAE,EAAE,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC,CAAC,CAC9C,CAAC;KACH;IAED,OAAO,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACzC,CAAC;AAED,SAAS,gBAAgB,CAAC,OAA+B,EAAE,IAAqB;IAC9E,OAAO,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE;QACjC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC;QAC3B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,MAAM,MAAM,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,IAAI,SAAS,GAAG,CAAC,CAAC;QAClB,IAAI,OAAO,GAAG,CAAC,CAAC;QAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;gBAC9B,IAAI,EAAE,KAAK,CAAC,EAAE;oBACZ,IAAI,CAAC,QAAQ,EAAE;wBACb,QAAQ,GAAG,IAAI,CAAC;wBAChB,OAAO,EAAE,CAAC;qBACX;oBACD,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBACpB,CAAC;gBACD,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC;gBACnC,QAAQ,EAAE,GAAG,EAAE;oBACb,SAAS,EAAE,CAAC;oBACZ,IAAI,SAAS,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE;wBAClC,IAAI,OAAO,KAAK,GAAG,EAAE;4BACnB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gCACpB,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;gCACxE,MAAM,CAAC,CAAC;yBACX;wBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;qBACvB;gBACH,CAAC;aACF,CAAC,CAAC,CAAC;SACL;IACH,CAAC,CAAC,CAAC;AACL,CAAC"}