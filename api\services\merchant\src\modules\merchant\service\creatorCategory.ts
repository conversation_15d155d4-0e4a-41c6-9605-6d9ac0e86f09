import { Provide } from '@midwayjs/core';
import { BaseRpcService, CoolRpcService } from '@cool-midway/rpc';
import { InjectEntityModel } from '@midwayjs/typeorm';
import { Repository } from 'typeorm';
import { CreatorCategoryEntity } from '../entity/creatorCategory';

/**
 * 创作者认证分类RPC服务
 */
@Provide()
@CoolRpcService({
  entity: CreatorCategoryEntity,
  method: ["add", "delete", "update", "info", "list", "page"]
})
export class CreatorCategoryService extends BaseRpcService {
  @InjectEntityModel(CreatorCategoryEntity)
  creatorCategoryEntity!: Repository<CreatorCategoryEntity>;

  /**
   * 重写分页查询方法
   */
  async page(query: any, option?: any) {
    console.log('🔍 [CreatorCategoryService] page 被调用，原始参数:', { query, option });

    // 处理参数格式 - query可能是数组格式
    let actualQuery = query;
    if (Array.isArray(query) && query.length > 0) {
      actualQuery = query[0];
      console.log('🔧 [CreatorCategoryService] 检测到数组格式，提取查询参数:', actualQuery);
    }

    // 解构参数
    const {
      page = 1,
      size = 20, // 修改默认值为20，与前端保持一致
      status,
      authType,
      name,
      searchKey
    } = actualQuery;

    // 优先使用name字段，如果没有则使用searchKey
    const searchName = name || searchKey;

    console.log('📋 [CreatorCategoryService] 解构后的参数:', {
      page,
      size,
      status,
      authType,
      searchName,
      originalQuery: query
    });

    const queryBuilder = this.creatorCategoryEntity.createQueryBuilder('category');

    // 状态筛选
    if (status !== undefined && status !== null) {
      console.log('🔍 [CreatorCategoryService] 添加状态筛选:', status, typeof status);
      queryBuilder.andWhere('category.status = :status', { status: Number(status) });
    }

    // 认证类型筛选
    if (authType !== undefined && authType !== null) {
      console.log('🔍 [CreatorCategoryService] 添加认证类型筛选:', authType, typeof authType);
      queryBuilder.andWhere('category.authType = :authType', { authType: Number(authType) });
    }

    // 名称搜索
    if (searchName && searchName.trim()) {
      console.log('🔍 [CreatorCategoryService] 添加名称搜索:', searchName);
      queryBuilder.andWhere('(category.name LIKE :name OR category.description LIKE :description)', { 
        name: `%${searchName.trim()}%`,
        description: `%${searchName.trim()}%`
      });
    }

    // 排序：按ID降序（新记录在前）
    queryBuilder.orderBy('category.id', 'DESC');

    // 分页计算
    const skip = (Number(page) - 1) * Number(size);
    const take = Number(size);
    
    console.log('📄 [CreatorCategoryService] 分页计算:', { 
      page: Number(page), 
      size: Number(size), 
      skip, 
      take 
    });

    // 分页查询
    const [list, total] = await queryBuilder
      .skip(skip)
      .take(take)
      .getManyAndCount();

    const result = {
      list,
      pagination: {
        page: Number(page),
        size: Number(size),
        total
      }
    };

    console.log('✅ [CreatorCategoryService] page 查询结果:', {
      listCount: result.list.length,
      total: result.pagination.total,
      page: result.pagination.page,
      size: result.pagination.size
    });

    return result;
  }

  /**
   * 获取启用的分类列表
   * @param authType 认证类型：0-手工艺人 1-非遗传承人 2-通用
   */
  async getActiveCategories(authType?: number) {
    console.log('🔍 [CreatorCategoryService] getActiveCategories 被调用，参数:', { authType });

    const queryBuilder = this.creatorCategoryEntity.createQueryBuilder('category');

    // 只获取启用的分类
    queryBuilder.where('category.status = :status', { status: 1 });

    if (authType !== undefined) {
      // 获取指定类型和通用类型的分类
      queryBuilder.andWhere('(category.authType = :authType OR category.authType = 2)', {
        authType
      });
    }

    // 排序：按ID降序（新记录在前）
    queryBuilder.orderBy('category.id', 'DESC');

    const result = await queryBuilder.getMany();
    console.log('✅ [CreatorCategoryService] getActiveCategories 查询结果数量:', result.length);
    console.log('✅ [CreatorCategoryService] getActiveCategories 分类名称:', result.map(r => `${r.name}(${r.authType})`));

    return result;
  }

  /**
   * 重写删除方法，正确处理参数
   */
  async delete(ids: any) {
    console.log('🔍 [CreatorCategoryService] delete 被调用，参数:', ids);

    let idArr: number[] = [];

    // 处理不同格式的参数
    if (Array.isArray(ids)) {
      idArr = ids;
    } else if (typeof ids === 'string') {
      // 如果是字符串，尝试按逗号分割
      idArr = ids.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    } else if (typeof ids === 'number') {
      idArr = [ids];
    } else if (ids && typeof ids === 'object' && ids.id) {
      idArr = [ids.id];
    } else {
      throw new Error('无效的删除参数');
    }

    console.log('🔍 [CreatorCategoryService] 处理后的ID数组:', idArr);

    if (idArr.length === 0) {
      throw new Error('请选择要删除的分类');
    }

    // 执行删除
    const result = await this.creatorCategoryEntity.delete(idArr);
    console.log('✅ [CreatorCategoryService] 删除结果:', result);

    return result;
  }
}
