{"version": 3, "sources": ["../../src/find-options/operator/ArrayContainedBy.ts"], "names": [], "mappings": ";;AAMA,4CAIC;AAVD,kDAA8C;AAE9C;;;GAGG;AACH,SAAgB,gBAAgB,CAC5B,KAAqC;IAErC,OAAO,IAAI,2BAAY,CAAC,kBAAkB,EAAE,KAAY,CAAC,CAAA;AAC7D,CAAC", "file": "ArrayContainedBy.js", "sourcesContent": ["import { FindOperator } from \"../FindOperator\"\n\n/**\n * FindOptions Operator.\n * Example: { someField: ArrayContainedBy([...]) }\n */\nexport function ArrayContainedBy<T>(\n    value: readonly T[] | FindOperator<T>,\n): FindOperator<any> {\n    return new FindOperator(\"arrayContainedBy\", value as any)\n}\n"], "sourceRoot": "../.."}