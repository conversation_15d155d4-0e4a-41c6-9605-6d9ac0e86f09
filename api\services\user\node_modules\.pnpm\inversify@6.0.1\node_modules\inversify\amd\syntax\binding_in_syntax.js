define(["require", "exports", "../constants/literal_types", "./binding_when_on_syntax"], function (require, exports, literal_types_1, binding_when_on_syntax_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BindingInSyntax = void 0;
    var BindingInSyntax = (function () {
        function BindingInSyntax(binding) {
            this._binding = binding;
        }
        BindingInSyntax.prototype.inRequestScope = function () {
            this._binding.scope = literal_types_1.BindingScopeEnum.Request;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingInSyntax.prototype.inSingletonScope = function () {
            this._binding.scope = literal_types_1.BindingScopeEnum.Singleton;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        BindingInSyntax.prototype.inTransientScope = function () {
            this._binding.scope = literal_types_1.BindingScopeEnum.Transient;
            return new binding_when_on_syntax_1.BindingWhenOnSyntax(this._binding);
        };
        return BindingInSyntax;
    }());
    exports.BindingInSyntax = BindingInSyntax;
});
//# sourceMappingURL=binding_in_syntax.js.map