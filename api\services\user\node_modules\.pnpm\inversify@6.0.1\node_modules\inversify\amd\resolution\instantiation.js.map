{"version": 3, "file": "instantiation.js", "sourceRoot": "", "sources": ["../../src/resolution/instantiation.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqBA,SAAS,gBAAgB,CACvB,aAAmC,EACnC,cAAgD;QAEhD,OAAO,aAAa,CAAC,MAAM,CAAmB,UAAC,gBAAgB,EAAE,YAAY;YAC3E,IAAM,SAAS,GAAG,cAAc,CAAC,YAAY,CAAC,CAAA;YAC9C,IAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAA;YAC3C,IAAI,UAAU,KAAK,8BAAc,CAAC,mBAAmB,EAAE;gBACrD,gBAAgB,CAAC,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACvD;iBAAM;gBACL,gBAAgB,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;gBACpD,gBAAgB,CAAC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACpD;YACD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;gBAC7B,gBAAgB,CAAC,OAAO,GAAG,IAAA,kCAA0B,EAAC,SAAS,CAAC,CAAC;aAClE;YACD,OAAO,gBAAgB,CAAA;QACzB,CAAC,EAAE,EAAE,qBAAqB,EAAE,EAAE,EAAE,kBAAkB,EAAE,EAAE,EAAE,gBAAgB,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAA;IACjG,CAAC;IAED,SAAS,eAAe,CACtB,MAA6B,EAC7B,aAAmC,EACnC,cAAgD;QAEhD,IAAI,MAAsB,CAAC;QAE3B,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC5B,IAAM,QAAQ,GAAG,gBAAgB,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;YAChE,IAAM,+BAA+B,yBAA2C,QAAQ,KAAE,MAAM,QAAA,GAAE,CAAA;YAClG,IAAI,QAAQ,CAAC,OAAO,EAAE;gBACpB,MAAM,GAAG,iCAAiC,CAAC,+BAA+B,CAAC,CAAA;aAC5E;iBAAM;gBACL,MAAM,GAAG,4BAA4B,CAAC,+BAA+B,CAAC,CAAA;aACvE;SACF;aAAM;YACL,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;SACvB;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,SAAS,4BAA4B,CACnC,IAAuC;;QAEvC,IAAM,QAAQ,QAAO,CAAA,KAAA,IAAI,CAAC,MAAM,CAAA,wCAAI,IAAI,CAAC,qBAAgC,YAAC,CAAC;QAC3E,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAC,CAAqB,EAAE,KAAa;YACjE,IAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;YACrC,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YAChD,QAA6C,CAAC,QAAQ,CAAC,GAAG,SAAS,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED,SAAe,iCAAiC,CAC9C,IAAuC;;;;;4BAET,WAAM,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,EAAA;;wBAAhF,qBAAqB,GAAG,SAAwD;wBAC3D,WAAM,sBAAsB,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAA;;wBAA1E,kBAAkB,GAAG,SAAqD;wBAChF,WAAO,4BAA4B,uBAAS,IAAI,KAAE,qBAAqB,uBAAA,EAAE,kBAAkB,oBAAA,IAAG,EAAA;;;;KAC/F;IAED,SAAe,sBAAsB,CAAC,yBAAoC;;;;gBAClE,UAAU,GAAc,EAAE,CAAC;gBACjC,WAAiD,EAAzB,uDAAyB,EAAzB,uCAAyB,EAAzB,IAAyB,EAAE;oBAAxC,SAAS;oBAClB,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;wBAC5B,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAA;qBACxC;yBAAM;wBACL,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;qBAC3B;iBACF;gBACD,WAAO,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAA;;;KAC/B;IAED,SAAS,8BAA8B,CAAI,MAA6B,EAAE,MAAS;QAEjF,IAAM,mBAAmB,GAAG,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE3D,IAAI,IAAA,iBAAS,EAAC,mBAAmB,CAAC,EAAE;YAClC,OAAO,mBAAmB,CAAC,IAAI,CAAC,cAAM,OAAA,MAAM,EAAN,CAAM,CAAC,CAAC;SAC/C;aAAM;YACL,OAAO,MAAM,CAAC;SACf;IACH,CAAC;IAED,SAAS,cAAc,CAAI,MAA6B,EAAE,QAAW;;QACnE,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,EAAE;YAC5D,IAAM,IAAI,GAAa,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;YAChF,IAAI;gBACF,OAAO,MAAA,MAAC,QAA2C,EAAE,IAAI,CAAC,KAAgB,CAAC,kDAAI,CAAC;aACjF;YAAC,OAAO,CAAC,EAAE;gBACV,MAAM,IAAI,KAAK,CAAC,IAAA,iCAAoB,EAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;aAC/D;SACF;IACH,CAAC;IAED,SAAS,2BAA2B,CAAc,OAA8B,EAAE,MAA6B;QAC7G,IAAI,OAAO,CAAC,KAAK,KAAK,gCAAgB,CAAC,SAAS,EAAE;YAChD,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,SAAS,4BAA4B,CAAc,OAA8B,EAAE,MAA6B;QAC9G,IAAM,iBAAiB,GAAG,sCAAmC,OAAO,CAAC,KAAK,KAAK,gCAAgB,CAAC,OAAO,CAAC,CAAC;YACvG,SAAS,CAAC,CAAC;YACX,WAAW,aAAS,CAAC;QACvB,IAAI,OAAO,OAAO,CAAC,cAAc,KAAK,UAAU,EAAE;YAChD,MAAM,IAAI,KAAK,CAAC,IAAA,kCAAqB,EAAC,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACxE;QAED,IAAI,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC,EAAE;YACzD,MAAM,IAAI,KAAK,CAAC,IAAA,8BAAiB,EAAC,MAAM,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC;SACpE;IACH,CAAC;IAED,SAAS,eAAe,CACtB,OAA8B,EAC9B,MAA6B,EAC7B,aAAmC,EACnC,cAAgD;QAEhD,2BAA2B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAE7C,IAAM,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAEtE,IAAI,IAAA,iBAAS,EAAC,MAAM,CAAC,EAAE;YACrB,OAAO,MAAM,CAAC,IAAI,CAAC,UAAC,cAAc,IAAK,OAAA,8BAA8B,CAAC,MAAM,EAAE,cAAc,CAAC,EAAtD,CAAsD,CAAC,CAAC;SAChG;aAAM;YACL,OAAO,8BAA8B,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;SACvD;IACH,CAAC;IAEQ,0CAAe"}