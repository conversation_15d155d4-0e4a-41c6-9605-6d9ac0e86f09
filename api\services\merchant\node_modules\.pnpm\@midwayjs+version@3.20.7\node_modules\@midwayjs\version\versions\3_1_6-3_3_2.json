{"@midwayjs/egg-layer": ["3.3.2", "3.3.3"], "@midwayjs/express-layer": "3.3.2", "@midwayjs/faas-typings": "3.1.6", "@midwayjs/koa-layer": "3.3.2", "@midwayjs/runtime-engine": "3.0.4", "@midwayjs/runtime-mock": "3.0.4", "@midwayjs/serverless-app": ["3.3.2", "3.3.3"], "@midwayjs/serverless-aws-starter": "3.1.6", "@midwayjs/serverless-fc-starter": "3.3.2", "@midwayjs/serverless-fc-trigger": "3.3.2", "@midwayjs/serverless-http-parser": "3.1.6", "@midwayjs/serverless-scf-starter": "3.1.6", "@midwayjs/serverless-scf-trigger": "3.3.2", "@midwayjs/serverless-vercel-starter": "3.3.2", "@midwayjs/serverless-vercel-trigger": "3.0.4", "@midwayjs/serverless-worker-starter": "3.3.2", "@midwayjs/static-layer": "3.3.2", "@midwayjs/axios": "3.3.2", "@midwayjs/bootstrap": ["3.3.2", "3.3.3"], "@midwayjs/cache": "3.3.2", "@midwayjs/consul": "3.3.2", "@midwayjs/core": "3.3.2", "@midwayjs/cos": "3.3.2", "@midwayjs/cross-domain": ["3.3.2", "3.3.3"], "@midwayjs/decorator": "3.1.6", "@midwayjs/express-session": "3.3.2", "@midwayjs/faas": "3.3.2", "@midwayjs/grpc": "3.3.2", "@midwayjs/http-proxy": ["3.3.2", "3.3.3"], "@midwayjs/i18n": "3.3.2", "@midwayjs/info": "3.3.2", "@midwayjs/jwt": ["3.3.2", "3.3.3"], "@midwayjs/mock": "3.3.2", "@midwayjs/mongoose": "3.3.2", "@midwayjs/orm": "3.3.2", "@midwayjs/oss": "3.3.2", "@midwayjs/otel": "3.3.2", "@midwayjs/passport": ["3.3.2", "3.3.3"], "@midwayjs/process-agent": "3.3.2", "@midwayjs/prometheus-socket-io": ["3.3.2", "3.3.3"], "@midwayjs/prometheus": "3.3.2", "@midwayjs/rabbitmq": "3.3.2", "@midwayjs/redis": "3.3.2", "@midwayjs/security": ["3.3.2", "3.3.3"], "@midwayjs/sequelize": "3.3.2", "@midwayjs/session": "3.3.2", "@midwayjs/socketio": "3.3.2", "@midwayjs/static-file": ["3.3.2", "3.3.3"], "@midwayjs/swagger": "3.3.2", "@midwayjs/tablestore": "3.3.2", "@midwayjs/task": "3.3.2", "@midwayjs/typegoose": "3.3.2", "@midwayjs/upload": ["3.3.2", "3.3.3"], "@midwayjs/validate": "3.3.2", "@midwayjs/version": ["3.3.2", "3.3.3"], "@midwayjs/view-ejs": "3.3.2", "@midwayjs/view-nunjucks": "3.3.2", "@midwayjs/view": "3.3.2", "@midwayjs/express": "3.3.2", "@midwayjs/koa": "3.3.2", "@midwayjs/web": ["3.3.2", "3.3.3"], "@midwayjs/ws": "3.3.2"}